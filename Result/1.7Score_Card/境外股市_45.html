<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="3f24458e99eb43b9aca4c31eceb07286" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_3f24458e99eb43b9aca4c31eceb07286 = echarts.init(
            document.getElementById('3f24458e99eb43b9aca4c31eceb07286'), 'white', {renderer: 'canvas'});
        var option_3f24458e99eb43b9aca4c31eceb07286 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "gauge",
            "title": {
                "show": true,
                "position": [
                    "0%",
                    "0%"
                ],
                "color": "blue",
                "margin": 8,
                "fontSize": 30,
                "fontFamily": "Microsoft YaHei",
                "valueAnimation": false
            },
            "detail": {
                "show": true,
                "backgroundColor": "transparent",
                "borderWidth": 0,
                "borderColor": "transparent",
                "offsetCenter": [
                    0,
                    "-40%"
                ],
                "color": "auto",
                "fontStyle": "normal",
                "fontWeight": "normal",
                "fontFamily": "sans-serif",
                "fontSize": 30,
                "borderRadius": 0,
                "padding": 0,
                "shadowColor": "transparent",
                "shadowBlur": 0,
                "shadowOffsetX": 0,
                "shadowOffsetY": 0,
                "overflow": "none",
                "valueAnimation": true
            },
            "name": "\u5883\u5916\u80a1\u5e02",
            "min": 0,
            "max": 100,
            "splitNumber": 10,
            "center": [
                "50%",
                "50%"
            ],
            "radius": "75%",
            "startAngle": 225,
            "endAngle": -45,
            "clockwise": true,
            "data": [
                {
                    "name": "\u5883\u5916\u80a1\u5e02\n(\u603b\u8bc4\u5206)",
                    "value": 45
                }
            ],
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 10,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": [
                        [
                            0.33,
                            "#70AD47"
                        ],
                        [
                            0.67,
                            "#A5A5A5"
                        ],
                        [
                            1,
                            "#C00000"
                        ]
                    ]
                }
            },
            "progress": {
                "show": false,
                "overlap": true,
                "width": 10,
                "roundCap": false,
                "clip": false
            },
            "anchor": {
                "show": true,
                "showAbove": false,
                "size": 6,
                "icon": "circle",
                "offsetCenter": [
                    0,
                    0
                ],
                "keepAspect": false
            },
            "pointer": {
                "show": true,
                "length": "80%",
                "width": 8,
                "itemStyle": {
                    "color": "#A5A5A5"
                }
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5883\u5916\u80a1\u5e02"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_3f24458e99eb43b9aca4c31eceb07286.setOption(option_3f24458e99eb43b9aca4c31eceb07286);
    </script>
</body>
</html>
