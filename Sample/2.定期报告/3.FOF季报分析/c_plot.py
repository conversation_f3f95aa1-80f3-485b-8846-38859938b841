# %%
# -*- coding: utf-8 -*-
'''
Created on 20221014
@author: gyrx-linyy
本函数用pyechart来画图，专用于季报分析和基金分析
'''

# %%
import numpy as np
import pandas as pd
import datetime as dt
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# %%
from pyecharts.globals import CurrentConfig
CurrentConfig.ONLINE_HOST = "//vmfs/FOF投资部/基金精选/4.0 定期报告/pyecharts-assets/assets/"
from pyecharts import options as opts
from pyecharts.charts import Bar, Line, Pie, Tab, Timeline, Scatter, Page, Grid,Boxplot
from pyecharts.components import Table
from plottable import Table as Tab

# %% 柱状、折线、堆积
def multi_chart(df,title="多功能图片",area=0.5,id='0',label=1,sort=1)->Line:
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    if sort==1:
        df=df.sort_values(by=df.index[-1],axis=1,ascending=False)
    line=Line()
    line.add_xaxis(xaxis_data=df.index.tolist())
    for i in range(len(df.columns)):
        if i < 5:
            coord_y={0.5:int(df.iloc[-1,:i+1].sum()),0:df.iloc[:,i].tolist()[-1]}
            line.add_yaxis(
                series_name=df.columns[i],
                stack="总量",
                y_axis=df.iloc[:,i].tolist(),
                areastyle_opts=opts.AreaStyleOpts(opacity=area),
                markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,
                    data=[opts.MarkPointItem(name=df.columns[i], coord=[df.index.tolist()[-1], coord_y[area]], value=df.iloc[:,i].tolist()[-1])],
                    label_opts=opts.LabelOpts(position='right')
                )
            )
        else:
            line.add_yaxis(
                series_name=df.columns[i],
                stack="总量",
                y_axis=df.iloc[:,i].tolist(),
                areastyle_opts=opts.AreaStyleOpts(opacity=area),
            )

    line.set_series_opts(label_opts=opts.LabelOpts(is_show=False))#不显示label
    if len(df.columns)<3:
        pos_left_flag='center'
    else:
        pos_left_flag='30%'
    
    line.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),#鼠标放上去会显示X\Y对应数据
        yaxis_opts=opts.AxisOpts(
            type_="value",
            axistick_opts=opts.AxisTickOpts(is_show=True),
            splitline_opts=opts.SplitLineOpts(is_show=True),
        ),
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False,axislabel_opts=opts.LabelOpts(rotate=50, interval=0)),
        datazoom_opts=[
            #opts.DataZoomOpts(range_start=0,range_end=100,orient='vertical'),
            opts.DataZoomOpts(range_start=0,range_end=100,pos_bottom='bottom',orient='horizontal')],
        toolbox_opts=opts.ToolboxOpts(
            orient='vertical',pos_left='90%',pos_top='10%',
            feature=opts.ToolBoxFeatureOpts(
                    restore=opts.ToolBoxFeatureRestoreOpts(),
                    magic_type=opts.ToolBoxFeatureMagicTypeOpts(),
                    save_as_image= None,
                    data_zoom =None,
                    brush= None,
                    data_view = None
                )
            ),#可以选折线图、堆积图、柱状图、数据视图
        legend_opts = opts.LegendOpts(pos_left=pos_left_flag),#type_='scroll',
    )
    #line.render("导出图片示例/柱状折现或堆积.html")
    if id !='0':
        line.chart_id=id
    line.set_colors(color_list)
    return line

# %% 柱状、折线、堆积
def multi_chart_bar(df,title="多功能图片",area=0.5,id='0',label=1,sort=1)->Bar:
    color_list=['rgb(6,6,185,0.5)','rgb(167,9,19,0.5)','rgb(16,154,14,0.5)','rgb(151,151,151,0.5)','rgb(225,119,38,0.5)','rgb(95,161,197,0.5)','rgb(244,159,172,0.5)','rgb(127,204,126,0.5)','rgb(219,143,82,0.5)']
    if sort==1:
        df=df.sort_values(by=df.index[-1],axis=1,ascending=False)
    bar=Bar()
    bar.add_xaxis(xaxis_data=df.index.tolist())
    #label_i={1:{i:True if i < 5 else False for i in range(50)},0:{i:False for i in range(50)}}
    for i in range(len(df.columns)):
        if i < 5:
            coord_y={0.5:int(df.iloc[-1,:i+1].sum()),0:df.iloc[:,i].tolist()[-1]}
            bar.add_yaxis(
                series_name=df.columns[i],
                stack="总量",
                y_axis=df.iloc[:,i].tolist(),
                markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,
                    data=[opts.MarkPointItem(name=df.columns[i], coord=[df.index.tolist()[-1], coord_y[area]], value=df.iloc[:,i].tolist()[-1])],
                    label_opts=opts.LabelOpts(position='right')
                )
            )
        else:
            bar.add_yaxis(
                series_name=df.columns[i],
                stack="总量",
                y_axis=df.iloc[:,i].tolist(),
            )

    bar.set_series_opts(label_opts=opts.LabelOpts(is_show=False))#不显示label
    if len(df.columns)<3:
        pos_left_flag='center'
    else:
        pos_left_flag='30%'
    bar.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),#鼠标放上去会显示X\Y对应数据
        yaxis_opts=opts.AxisOpts(
            type_="value",
            axistick_opts=opts.AxisTickOpts(is_show=True),
            splitline_opts=opts.SplitLineOpts(is_show=True),
        ),
        xaxis_opts=opts.AxisOpts(axislabel_opts=(opts.LabelOpts(rotate=50) if len(df.index.tolist())<20 else None)),
        datazoom_opts=[
            opts.DataZoomOpts(range_start=0,range_end=100,pos_bottom='bottom',orient='horizontal')],
        toolbox_opts=opts.ToolboxOpts(
            orient='vertical',pos_left='90%',pos_top='10%',
            feature=opts.ToolBoxFeatureOpts(
                    restore=opts.ToolBoxFeatureRestoreOpts(),
                    magic_type=opts.ToolBoxFeatureMagicTypeOpts(),
                    save_as_image= None,
                    data_zoom =None,
                    brush= None,
                    data_view = None
                )
            ),#可以选折线图、堆积图、柱状图、数据视图
        legend_opts = opts.LegendOpts(pos_left=pos_left_flag),
    )
    if id !='0':
        bar.chart_id=id
    bar.set_colors(color_list)
    return bar

# %% 带中位数的折线图
def line_chart(df,title="带中位数的折线图",percentile=1,id='0')->Line:
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    df=df.sort_values(by=df.index[-1],axis=1,ascending=False)
    line=Line()
    line.add_xaxis(xaxis_data=df.index.tolist())
    is_selected_flag={i:True if i < 3 else False for i in range(50)}
    label_i={i:True if i < 4 else False for i in range(50)}
    for i in range(len(df.columns)):
        line.add_yaxis(df.columns[i],df.iloc[:,i].tolist(),is_connect_nones=True,
            markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,
                    data=[opts.MarkPointItem(name=df.columns[i], coord=[df.index.tolist()[-1], df.iloc[:,i].tolist()[-1]], value=df.iloc[:,i].tolist()[-1])],
                    label_opts=opts.LabelOpts(position='right')
                )
        )
    line.set_series_opts(label_opts=opts.LabelOpts(is_show=False))
    if len(df.columns)<3:
        pos_left_flag='center'
    else:
        pos_left_flag='30%'
    line.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        yaxis_opts=opts.AxisOpts(
            type_="value",
            axistick_opts=opts.AxisTickOpts(is_show=True),
            splitline_opts=opts.SplitLineOpts(is_show=True),
        ),
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False,axislabel_opts=opts.LabelOpts(rotate=50) if len(df.index.tolist())<20 else None),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),#鼠标放上去会显示X\Y对应数据
        datazoom_opts=[
            opts.DataZoomOpts(range_start=0,range_end=100,pos_bottom='bottom',orient='horizontal')],#可以选日期
        legend_opts = opts.LegendOpts(pos_left=pos_left_flag),
    )
    if percentile==1:
        line.set_series_opts(
                markline_opts=opts.MarkLineOpts(
                    data=[
                        opts.MarkLineItem(type_="median", name="中位数"),
                    ]
                ),
            )
    if id !='0':
        line.chart_id=id
    line.set_colors(color_list)
    return line

# %% 组合图：柱状图+线图
def bar_line(df,title="组合图",id='0')->Bar:#df中柱状图放第一列，折线图放第二列
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    bar = (
        Bar()
        .add_xaxis(df.index.tolist())
        .add_yaxis(df.columns[0], df[df.columns[0]].tolist(),yaxis_index=0,color='rgb(6,6,185,0.5)',
            markpoint_opts=opts.MarkPointOpts(
                symbol_size=0.1,label_opts=opts.LabelOpts(position='top',background_color='#fff'),
                data=[opts.MarkPointItem(name=df.columns[0], coord=[df.index.tolist()[-1], df.iloc[:,0].tolist()[-1]], value=df.iloc[:,0].tolist()[-1])]
            )
        )
        .extend_axis(
            yaxis=opts.AxisOpts(
                name=df.columns[1],
                axislabel_opts=opts.LabelOpts(formatter="{value}",color=color_list[1]),
            )
        )
        .set_series_opts(label_opts=opts.LabelOpts(is_show=False))
        .set_global_opts(
            title_opts=opts.TitleOpts(title=title),
            yaxis_opts=opts.AxisOpts(
                axislabel_opts=opts.LabelOpts(formatter="{value}",color=color_list[0]),
                splitline_opts=opts.SplitLineOpts(is_show=True),
                ),
            xaxis_opts=opts.AxisOpts(axislabel_opts=(opts.LabelOpts(rotate=50) if len(df.index.tolist())<20 else None)),
            datazoom_opts=[
            #opts.DataZoomOpts(range_start=0,range_end=100,orient='vertical'),
            opts.DataZoomOpts(range_start=0,range_end=100,pos_bottom='bottom',orient='horizontal')],
        )
    )
    line = Line() \
        .add_xaxis(df.index.tolist()) \
        .add_yaxis(df.columns[1], df[df.columns[1]].tolist(),yaxis_index=1,is_connect_nones=True,
            itemstyle_opts=opts.ItemStyleOpts(border_color=color_list[1]),
            markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,label_opts=opts.LabelOpts(position='right',background_color='#fff'),
            data=[opts.MarkPointItem(name=df.columns[1], coord=[df.index.tolist()[-1], df.iloc[:,1].tolist()[-1]], value=df.iloc[:,1].tolist()[-1])]
            )) \
        .set_series_opts(label_opts=opts.LabelOpts(is_show=False))
    bar.overlap(line)
    # bar.render("导出图片示例\\柱状折现或堆积.html")
    if id !='0':
        bar.chart_id=id
    #bar.set_colors(color_list)
    return bar

# %% 组合图：线图+线图，双Y轴
def line_line(df1,df2,title="组合图",id='0')->Bar:#左轴显示的放在df1，右轴显示的放在df2
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    line1=Line()
    line1.add_xaxis(df1.index.tolist())
    for i in range(len(df1.columns)):
        line1.add_yaxis(
            df1.columns[i], df1.iloc[:,i].tolist(), yaxis_index=0,
            markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,label_opts=opts.LabelOpts(position='right',background_color='#fff'),
                data=[opts.MarkPointItem(name=df1.columns[i], coord=[df1.index.tolist()[-1], df1.iloc[:,i].tolist()[-1]], value=df1.iloc[:,i].tolist()[-1])]
            )
        )
    line1.extend_axis(
        yaxis=opts.AxisOpts(
            name=df2.columns[-1],
            axislabel_opts=opts.LabelOpts(formatter="{value}",color=color_list[len(df1.columns)])
        )
    )
    line1.set_series_opts(label_opts=opts.LabelOpts(is_show=False))
    line1.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False,axislabel_opts=opts.LabelOpts(rotate=50) if len(df1.index.tolist())<20 else None),
        yaxis_opts=opts.AxisOpts(
            axislabel_opts=opts.LabelOpts(formatter="{value}",color=color_list[0] if len(df1.columns)<2 else 'blake'),
            splitline_opts=opts.SplitLineOpts(is_show=True),
            ),
        datazoom_opts=[
            #opts.DataZoomOpts(range_start=0,range_end=100,orient='vertical'),
            opts.DataZoomOpts(range_start=0,range_end=100,pos_bottom='bottom',orient='horizontal')],
    )
    
    line2 = Line().add_xaxis(df2.index.tolist()).add_yaxis(df2.columns[-1], df2[df2.columns[-1]].tolist(),yaxis_index=1,is_connect_nones=True,
        markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,label_opts=opts.LabelOpts(position='right',background_color='#fff'),
            data=[opts.MarkPointItem(name=df2.columns[-1], coord=[df2.index.tolist()[-1], df2.iloc[:,-1].tolist()[-1]], value=df2.iloc[:,-1].tolist()[-1])]
        )
    ).set_series_opts(label_opts=opts.LabelOpts(is_show=False))
    line1.overlap(line2)
    # bar.render("导出图片示例\\柱状折现或堆积.html")
    if id !='0':
        line1.chart_id=id
    line1.set_colors(color_list)
    return line1

# %% 饼图
def pie_chart(df,title="板块配置情况",id='0',data=100,area=0.5)->Timeline:
    color_list=['rgb(6,6,185,0.5)','rgb(167,9,19,0.5)','rgb(16,154,14,0.5)','rgb(151,151,151,0.5)','rgb(225,119,38,0.5)','rgb(95,161,197,0.5)','rgb(244,159,172,0.5)','rgb(127,204,126,0.5)','rgb(219,143,82,0.5)']
    df=df.sort_values(by=df.index[-1],axis=1,ascending=False)
    attr = df.columns.tolist()
    tl = Timeline()
    for i in reversed(df.index):
        if data != 100:
            dfi=df.loc[[i]].T.sort_values(ascending=False,by=i).head(data)
            dfi.loc['其他',i]=100-dfi.sum().values[0]
            data_pie=[list(z) for z in zip(dfi.index, dfi[i].tolist())]
        else:
            data_pie=[list(z) for z in zip(attr, df.loc[i].tolist())]
        pie = (
            Pie()
            .add(
                "title",
                data_pie,
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=title,subtitle=i),
                legend_opts = opts.LegendOpts(is_show=False),
                )
            .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {c}"))
        )
        pie.set_colors(color_list)
        tl.add(pie, "{}".format(i))
    if id !='0':
        tl.chart_id=id
    return tl


# %% 差分柱状图
def bar_diff(df,title="环比变化",id='0',area=0.5)->Timeline:
    color_list=['rgb(6,6,185,0.5)','rgb(167,9,19,0.5)','rgb(16,154,14,0.5)','rgb(151,151,151,0.5)','rgb(225,119,38,0.5)','rgb(95,161,197,0.5)','rgb(244,159,172,0.5)','rgb(127,204,126,0.5)','rgb(219,143,82,0.5)']
    df=df.fillna(0).diff(periods=1).dropna().round(2)
    tl = Timeline()
    for i in reversed(df.index):
        df_i=df.loc[i].sort_values(ascending = False)
        bar = Bar()
        bar.add_xaxis(df_i.index.tolist())
        bar.add_yaxis(i,df_i.tolist())
        bar.set_series_opts(label_opts=opts.LabelOpts(is_show=False))
        if len(df_i.index)<6:
            bar.set_global_opts(
                title_opts=opts.TitleOpts(title=title,subtitle=i),
                legend_opts = opts.LegendOpts(is_show=False),
                yaxis_opts=opts.AxisOpts(splitline_opts=opts.SplitLineOpts(is_show=True)),
                )
        else:
            bar.set_global_opts(
                xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=90)),
                title_opts=opts.TitleOpts(title=title,subtitle=i),
                legend_opts = opts.LegendOpts(is_show=False),
                yaxis_opts=opts.AxisOpts(splitline_opts=opts.SplitLineOpts(is_show=True)),
                )
        bar.set_colors(color_list)
        tl.add(bar, "{}".format(i))
    if id !='0':
        tl.chart_id=id
    return tl

# %% 散点图
def scatter_chart(df,title="散点图，仅用于风格市值",id='0')->Scatter:
    # style_dict={0:'价值',1:'平衡',2:'成长'}
    # marketvalue_dict={0:'小盘',1:'中盘',2:'大盘'}
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    scatter=Scatter()
    scatter.add_xaxis(df[df.columns[0]].tolist())
    scatter.add_yaxis(
        df.columns[1], df[df.columns[1]].tolist(),
        markpoint_opts=opts.MarkPointOpts(
            data=[opts.MarkPointItem(name="最新位置", coord=[df[df.columns[0]].tolist()[-1], df[df.columns[1]].tolist()[-1]], value=df.index.tolist()[-1])]
            )
            )
    scatter.set_series_opts(label_opts=opts.LabelOpts(is_show=False))
    scatter.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        xaxis_opts=opts.AxisOpts(
            name=df.columns[0],type_="value",interval=1,
            splitline_opts=opts.SplitLineOpts(is_show=True)
            ),
        yaxis_opts=opts.AxisOpts(
            name=df.columns[1],type_="value",interval=1,
            splitline_opts=opts.SplitLineOpts(is_show=True),
            ),
        legend_opts = opts.LegendOpts(is_show=False)
    )
    if id !='0':
        scatter.chart_id=id
    scatter.set_colors(color_list)
    return scatter


# %%表格图片
def table_pic(df,name):
    fig, ax = plt.subplots()
    Tab(df, odd_row_color='#f8f9fa', even_row_color='white',
          textprops={
              'fontsize': 4,
              'ha': 'center'
          })

    plt.title(name)
    # 显示图形并保存为图片（这里以png格式为例）
    plt.savefig('报告/{0}.png'.format(name), dpi=300)
    return


# %%表格网页
def table_chart(df,title='表格展示',id='0'):#df不展示index
    table = Table()
    headers = df.columns.tolist()
    rows = df.values
    table.add(headers, rows)
    table.set_global_opts(
        title_opts=opts.ComponentTitleOpts(title=title),
    )
    #table.render("导出图片示例\page_simple_layout.html")
    if id !='0':
        table.chart_id=id
    return table

# %% 箱体图
def boxplot_chart(title,df,label,id='0')->Boxplot:
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    box = Boxplot()
    box.add_xaxis(df.columns.tolist())
    for i in np.unique(df.reset_index()[label].tolist()):
        box.add_yaxis(i, box.prepare_data([df.loc[i,col].dropna().tolist() for col in df.columns.tolist()]))
    box.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        yaxis_opts=opts.AxisOpts(
                type_="value",
                axistick_opts=opts.AxisTickOpts(is_show=True),
                splitline_opts=opts.SplitLineOpts(is_show=True),
            ),
            xaxis_opts=opts.AxisOpts(
                type_="category",
                axistick_opts=opts.AxisTickOpts(is_show=True),
                splitline_opts=opts.SplitLineOpts(is_show=True),
            )
        )
        #box.render("boxplot_base.html")
    if id !='0':
        box.chart_id=id
    box.set_colors(color_list)
    return box

# %% box-scatter
def box_scatter(title,box_data,scatter_data,scatter_data2,series1_name,series2_name,series3_name,x_sorted,id='0')->Timeline:
    color_list=['rgb(6,6,185)','rgb(167,9,19)','rgb(16,154,14)','rgb(151,151,151)','rgb(225,119,38)','rgb(95,161,197)','rgb(244,159,172)','rgb(127,204,126)','rgb(219,143,82)']
    tl = Timeline()
    for i in scatter_data.keys():

        boxplot = Boxplot()

        boxplot.add_xaxis(x_sorted[i])
        boxplot.add_yaxis(series1_name, boxplot.prepare_data(box_data[i]))
        boxplot.set_global_opts(
                title_opts=opts.TitleOpts(title=title,subtitle=i),
                yaxis_opts=opts.AxisOpts(
                        type_="value",
                        axistick_opts=opts.AxisTickOpts(is_show=True),
                        splitline_opts=opts.SplitLineOpts(is_show=True),
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                    axislabel_opts=opts.LabelOpts(rotate=50, interval=0),
                ),
                datazoom_opts=[opts.DataZoomOpts(range_start=0,range_end=100,orient='vertical')],
                legend_opts = opts.LegendOpts(pos_left='30%'),
            )

        scatter = Scatter()
        scatter.add_xaxis(x_sorted[i])
        for j in range(max(len(x) for x in scatter_data[i])):
            scatter.add_yaxis(series2_name, [data_i[j] if len(data_i) > j else None for data_i in scatter_data[i]], symbol_size=10,label_opts=opts.LabelOpts(is_show=False),itemstyle_opts=opts.ItemStyleOpts(color='rgb(151,151,151)'))
        
        scatter2 = Scatter()
        scatter2.add_xaxis(x_sorted[i])
        for j in range(max(len(x) for x in scatter_data2[i])):
            scatter2.add_yaxis(series3_name, [data_i[j] if len(data_i) > j else None for data_i in scatter_data2[i]], symbol_size=10,label_opts=opts.LabelOpts(is_show=False),itemstyle_opts=opts.ItemStyleOpts(color='rgb(167,9,19)'))
        # 结合图
        boxplot.overlap(scatter).overlap(scatter2)
        boxplot.set_colors(color_list)
        tl.add(boxplot, "{}".format(i))
        #boxplot.render("boxplot_base.html")
    if id !='0':
        tl.chart_id=id
    return tl
