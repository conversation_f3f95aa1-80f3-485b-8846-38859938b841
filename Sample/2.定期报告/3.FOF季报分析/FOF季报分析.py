# %%
# -*- coding: utf-8 -*-
'''
Created on 20231101

@author: gyrx-linyx

本函数用于按类进行季报分析
'''

# %% 基础包导入
import os
from c_plot import multi_chart,line_chart,bar_line,line_line,pie_chart,bar_diff,scatter_chart,table_chart,boxplot_chart,table_pic
from pyecharts.charts import Page
import warnings
warnings.filterwarnings('ignore')
from pandas.errors import SettingWithCopyWarning
from WindPy import w
import numpy as np
import pandas as pd
import datetime as dt
from dateutil.relativedelta import relativedelta
import pickle
import copy
import matplotlib.pyplot as plt
w.start()
t0=dt.datetime.now()
t=t0

#%%运行时间
def printtime(t):
    if t!=1:
        print('-----' * 5+'花费%s秒'%(dt.datetime.now()-t))
        t=dt.datetime.now()
    else:
        print('—' * 25+'共费%s秒'%(dt.datetime.now()-t0))
    return t

# %% 数据的定义
#定义起始日期
start_day='20190331'
end_day='20240630'
with open('基金池\FOF数据.pkl', "rb") as fp:
    fund_all = pickle.load(fp)
# wind_type_list = ['养老目标日期FOF(2035)']
#定义FOF类型
wind_type_list=['债券型FOF','养老目标日期FOF(2025)','养老目标日期FOF(2030)','养老目标日期FOF(2035)','养老目标日期FOF(2040)','养老目标日期FOF(2045)','养老目标日期FOF(2050)','养老目标日期FOF(2055)','养老目标日期FOF(2060)','养老目标风险FOF(权益资产0-30%)','养老目标风险FOF(权益资产30%-60%)','养老目标风险FOF(权益资产60%-80%)','混合型FOF(权益资产0-30%)','混合型FOF(权益资产30%-60%)','混合型FOF(权益资产60%-95%)','全部']
#定义加权方式
cal_type_list = ['加权平均']  # '中位数'，'加权平均'
#定义公司名单
# company_list = ['兴证全球基金', '交银施罗德基金', '南方基金', '易方达基金', '汇添富基金', '广发基金', '华夏基金', '民生加银基金', '中欧基金', '平安基金',
#                 '工银瑞信基金'] #目前的公司标签有'全部'、'FOF总规模前十大'，公司的名单和公司标签只能二选一
company_list=['全部']

wind_type_list_yield=['债券型FOF','养老目标日期FOF(2025)','养老目标日期FOF(2030)','养老目标日期FOF(2035)','养老目标日期FOF(2040)','养老目标日期FOF(2045)','养老目标日期FOF(2050)','养老目标日期FOF(2055)',
    '养老目标日期FOF(2060)','养老目标风险FOF(权益资产0-30%)','养老目标风险FOF(权益资产30%-60%)','养老目标风险FOF(权益资产60%-80%)','混合型FOF(权益资产0-30%)',
    '混合型FOF(权益资产30%-60%)','混合型FOF(权益资产60%-95%)','股票型FOF']
dates=list(pd.date_range(start=start_day,end=end_day, freq='Q').strftime('%Y%m%d').values)
#自定义基金公司维度个性标签
if company_list[0]=='全部':
    company_label='全部公司'
    company_list = fund_all[dates[-1]]['基金信息'].groupby(['基金管理人']).sum(numeric_only=True)[['基金规模']].sort_values(by='基金规模',ascending=False).index.tolist()
elif company_list[0] == 'FOF总规模前十大':
    company_label = 'FOF总规模前十大'
    company_list=fund_all[dates[-1]]['基金信息'].groupby(['基金管理人']).sum(numeric_only=True)[['基金规模']].sort_values(by='基金规模', ascending=False)[:10].index.tolist()
else:
    company_label = '选定公司'

t=printtime(t)

# %% 进入主程序
for wind_type in wind_type_list:
    for cal_type in cal_type_list:
        # %% 数据的提取和整合、计算
        fund_company, fund_info, fund_fund,fund_scale = pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
        cal_fund_median,cal_fund_weight,cal_fund_holding_value=pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        for date_i in dates:
            fund = fund_all[date_i]
            fund_info1 = fund['基金信息']
            fund_fund1 = fund['基金持基明细']
            temp = fund_fund1[['基金代码','资产类别(I)','管理方式(VI)', '是否内部', '持基占比']]
            fund_info1 = fund_info1.join(
                (temp[(temp['是否内部'] == '内部')&(temp['资产类别(I)'] == '股票')].groupby(['基金代码'])[['持基占比']].sum(numeric_only=True)).div(temp[temp['资产类别(I)'] == '股票'].groupby(['基金代码'])['持基占比'].sum(numeric_only=True),
                                                                                   axis='index').rename(
                    columns={'持基占比': '内部持股基规模占比'}).fillna(0) * 100)
            fund_info1 = fund_info1.join(
                (temp[(temp['是否内部'] == '内部') & (temp['资产类别(I)'] == '债券')].groupby(['基金代码'])[['持基占比']].sum(numeric_only=True)).div(
                    temp[temp['资产类别(I)'] == '债券'].groupby(['基金代码'])['持基占比'].sum(numeric_only=True),
                    axis='index').rename(
                    columns={'持基占比': '内部持债基规模占比'}).fillna(0) * 100)
            fund_info1 = fund_info1.join(
                (temp[(temp['管理方式(VI)'] == '主动') & (temp['资产类别(I)'] == '股票')].groupby(['基金代码'])[['持基占比']].sum(numeric_only=True)).div(
                    temp[temp['资产类别(I)'] == '股票'].groupby(['基金代码'])['持基占比'].sum(numeric_only=True),
                    axis='index').rename(
                    columns={'持基占比': '主动股基规模占比'}).fillna(0) * 100)
            fund_info1 = fund_info1.join(
                (temp[(temp['管理方式(VI)'] == '主动') & (temp['资产类别(I)'] == '债券')].groupby(['基金代码'])[['持基占比']].sum(numeric_only=True)).div(
                    temp[temp['资产类别(I)'] == '债券'].groupby(['基金代码'])['持基占比'].sum(numeric_only=True),
                    axis='index').rename(
                    columns={'持基占比': '主动债基规模占比'}).fillna(0) * 100)
            fund_info = pd.concat([fund_info, fund_info1], axis=0)
            fund_fund = pd.concat([fund_fund, fund_fund1], axis=0)

            fund_scale1 = (fund['基金信息'].reset_index().groupby(['类型']).sum(numeric_only=True)[['基金资产净值']]/100000000).rename(columns={'基金资产净值':date_i})
            fund_scale = pd.concat([fund_scale, fund_scale1], axis=1)
            if date_i==dates[-1]:
                fund_yield=fund['基金信息'].reset_index().set_index(['类型','基金代码']).loc[wind_type_list_yield,['YTD','季度收益率']]
            cal_fund_info = fund['基金信息'].reset_index().set_index(['基金代码'])
            fund_stock = fund['基金持股明细']
            fund_direct_stock = fund_stock.loc[~fund_stock['持基代码'].isnull(), :].reset_index().set_index(['基金代码'])
            for type_i in fund_direct_stock['申万一级行业'].unique():
                cal_fund_info = cal_fund_info.join(
                    fund_direct_stock[['持基个股占比']][fund_direct_stock['申万一级行业'] == type_i].groupby(['基金代码']).sum(numeric_only=True).rename(
                        columns={'持基个股占比': '个股行业_{0}占比'.format(type_i)}))
            cal_fund_info = cal_fund_info.fillna(0)
            cal_fund_info.index.name = '基金代码'
            cal_fund_info['类型'] = cal_fund_info['类型'].astype(str)
            #基金类型选择全部
            if wind_type=='全部':
                # 根据选择的基金公司处理数据
                cal_fund_info['是否关注'] = cal_fund_info['基金管理人'].apply(lambda x: "是" if x in company_list else "否")
            else:
                cal_fund_info['是否关注'] = np.where((cal_fund_info['基金管理人'].isin(company_list)) & (cal_fund_info['类型'] == wind_type), '是', '否')
            cal_fund_info = cal_fund_info.reset_index().set_index(['是否关注', '基金代码'])
            cal_fund_median1=cal_fund_info.groupby(['是否关注']).median(numeric_only=True)
            cal_fund_median1['日期']=date_i
            cal_fund_median = pd.concat([cal_fund_median,cal_fund_median1],axis=0)
            cal_fund_median.index.name = '类型'
            # %%加权平均cal_fund_weight
            weight = (cal_fund_info[['基金规模']]).div((cal_fund_info[['基金规模']].groupby(['是否关注']).sum(numeric_only=True)), axis='index')
            cal_fund_weight2 = pd.DataFrame()
            # 设置各项指标的加权平均对象
            weight_choose = {
                '基金规模': ['基金规模', '机构投资者占比', '基金占比', '股票占比', '港股占比', '可转债占比', '合计股票占比', '合计转债占比', '下季持股收益率', '下季持基收益率', '调仓收益']
            }
            weight_choose['基金规模'].extend(list(
                filter(lambda x: any(y in x for y in ['风格指数','申万一级行业','申万二级行业','板块','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)']),
                       cal_fund_info.columns.to_list())))
            for weight_i in weight.columns:
                cal_fund_weight_i = (
                    cal_fund_info[weight_choose[weight_i]].mul(weight[weight_i], axis='index').groupby(['是否关注']).sum(numeric_only=True))
                cal_fund_weight2 = pd.concat([cal_fund_weight2, cal_fund_weight_i], axis=1)
            cal_fund_weight2[['基金规模']] = cal_fund_info[['基金规模']].groupby(['是否关注']).sum(numeric_only=True)
            cal_fund_median[['基金规模']] = cal_fund_info[['基金规模']].groupby(['是否关注']).sum(numeric_only=True)
            cal_fund_weight2[['基金数量']] = cal_fund_info[['证券全称']].drop_duplicates().groupby(['是否关注']).count()
            cal_fund_median[['基金数量']] = cal_fund_info[['证券全称']].drop_duplicates().groupby(['是否关注']).count()
            cal_fund_weight2['日期']=date_i
            cal_fund_weight = pd.concat([cal_fund_weight,cal_fund_weight2],axis=0)
            cal_fund_weight.index.name = '类型'
            # %%计算各类型基金的持基明细（用于重仓持基分析）cal_fund_holding_value
            cal_fund_holding2 = fund['基金持基明细'].set_index(['基金代码'])
            cal_fund_holding2=cal_fund_holding2.join(fund_info1[['类型']])
            if wind_type=='全部':
                cal_fund_holding2['是否关注'] = cal_fund_holding2['基金管理人'].apply(
                lambda x: "是" if x in company_list else "否")
            else:
                cal_fund_holding2['是否关注'] = np.where(
                    (cal_fund_holding2['基金管理人'].isin(company_list)) & (cal_fund_holding2['类型'] == wind_type), '是', '否')
            cal_fund_holding_value2 = cal_fund_holding2.groupby(['是否关注', '持基代码']).sum(numeric_only=True)[['持基市值']]
            cal_fund_holding_value2 = cal_fund_holding_value2.join(
                fund['基金持基明细'][['持基代码','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)', '下季持基收益率', '当季持基收益率']].drop_duplicates(subset=['持基代码'], keep='first').set_index(
                    ['持基代码']))
            cal_fund_holding_value2 = cal_fund_holding_value2.reset_index()
            fund_fund_name = pd.DataFrame({'持基代码': cal_fund_holding_value2['持基代码'].unique().tolist(),
                                           '持基名称': w.wss(cal_fund_holding_value2['持基代码'].unique().tolist(), "sec_name").Data[
                                               0]}).set_index('持基代码')
            cal_fund_holding_value2 = cal_fund_holding_value2.set_index(['是否关注', '持基代码']).join(fund_fund_name)
            cal_fund_info = cal_fund_info.reset_index()
            cal_fund_holding_value2 = cal_fund_holding_value2.join(
                pd.DataFrame((cal_fund_holding_value2['持基市值'].div(cal_fund_info.groupby('是否关注')['基金投资市值'].sum(numeric_only=True), axis='index')*100),
                             columns=['持仓市值占比']))
            cal_fund_holding_value2['日期']=date_i
            cal_fund_holding_value = pd.concat([cal_fund_holding_value, cal_fund_holding_value2], axis=0)

        # %% 画图底层数据的处理

        cal_fund_median.index.name='是否关注'
        cal_fund_median=cal_fund_median.reset_index().set_index(['是否关注','日期'])
        cal_fund_weight.index.name='是否关注'
        cal_fund_weight=cal_fund_weight.reset_index().set_index(['是否关注','日期'])
        cal_type_dict={'加权平均':cal_fund_weight,'中位数':cal_fund_median}
        cal_fund=cal_type_dict[cal_type]
        cal_fund=cal_fund.reset_index(['是否关注'])
        cal_fund = cal_fund.reset_index().set_index(['是否关注','日期'])
        cal_fund = cal_fund.loc[:, ((100*cal_fund.fillna(0)).round(0) != 0).any()]

        #指数涨跌幅
        code_fund={'885001.WI':'偏股基金','885008.WI':'长期纯债基金指数','885007.WI':'混合二级债基','885072.WI':'混合型FOF'}
        for i,code in enumerate([code_fund]):
            x=w.wsd(','.join(code.keys()), "close", end_day[:4]+'0101', end_day[:4]+'0101', "Days=Alldays;Fill=Previous").Data[0]
            y=w.wsd(','.join(code.keys()), "close", end_day, end_day, "Days=Alldays;Fill=Previous").Data[0]
            z=w.wsd(','.join(code.keys()), "pct_chg", end_day, end_day, "Period=Q;Days=Alldays").Data[0]
            chg=pd.DataFrame(index=['YTD','季度收益'],columns=list(code.values()))
            chg.loc['YTD']=[(yi/xi-1)*100 for xi,yi in zip(x,y)]
            chg.loc['季度收益']=z
            chg_fund=chg

        # %% 基金规模、收益画图
        page_all = Page(layout=Page.DraggablePageLayout)

        page_all.add(
            multi_chart(title='基金规模情况(亿)', df=fund_scale.T.astype('float').round(2).drop(columns=[col for col in fund_scale.index.tolist() if '银河证券三级分类' in col]).sort_values(by=end_day,axis=1,ascending=False), id=1),
            bar_line(
                title='{0}基金规模(亿)&基金数量'.format(wind_type),
                df=pd.concat(((cal_fund.loc['是', ['基金规模']] / 100000000, cal_fund.loc['是', ['基金数量']])), axis=1).round(2),
                id=2
            ),
            multi_chart(title='基金指数涨跌幅情况',df=chg_fund.astype('float').round(2),area=0,sort=0,id=3),
            boxplot_chart(title='基金收益情况',df=fund_yield,label='类型',id=4),
            line_chart(title='未来一个季度收益分拆', percentile=0,
                       df=pd.DataFrame(
                           cal_fund.loc['是', ['下季持股收益率', '下季持基收益率', '调仓收益']].replace(0, None).astype('float').round(2),
                           columns=['下季持股收益率', '下季持基收益率', '调仓收益']).rename(
                           columns={'下季持股收益率': '下季持股收益', '下季持基收益率': '下季持基收益', '调仓收益': '下季调仓收益'}),
                       id=5
                       ),

        )


        # %%  基金持仓画图
        try:
            temp=(cal_fund.loc['是',list(filter(lambda x: '穿透申万一级' in x ,cal_fund.columns.to_list()))].rename(lambda x:x[7:-2],axis='columns').fillna(0)).round(2).astype(float)
            page_all.add(
                multi_chart(title='资产配置情况',
                        df=(cal_fund.loc['是',['基金占比','股票占比','港股占比','可转债占比']].fillna(0)).round(2).astype(float),
                        id=6
                    ),
                multi_chart(title='资产类别(I)分布变化(基金股票)',
                            df=(cal_fund.loc['是', list(filter(lambda x: ('资产类别(I)' in x) and ('穿透' not in x), cal_fund.columns.to_list()))].dropna(how='all',axis=1).rename(
                                lambda x: x[8:-2], axis='columns').fillna(0)*100).astype(float).round(2).drop(columns=['nan'], errors='ignore'),
                            id=7
                            ),
                multi_chart(title='资产地区(II)分布变化(基金股票)',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('资产地区(II)' in x) and ('穿透' not in x), cal_fund.columns.to_list()))].dropna(how='all',axis=1).rename(
                                lambda x: x[9:-2], axis='columns').fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=8
                            ),
                multi_chart(title='资产类属(III)分布变化(基金股票)',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('资产类属(III)' in x) and ('穿透' not in x), cal_fund.columns.to_list()))].dropna(how='all',axis=1).rename(
                                lambda x: x[10:-2], axis='columns').fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=9
                            ),
                multi_chart(title='资产板块(IV)分布变化(基金股票)',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('资产板块(IV)' in x) and ('穿透' not in x), cal_fund.columns.to_list()))].dropna(how='all',axis=1).rename(
                                lambda x: x[9:-2], axis='columns').fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=10
                            ),
                multi_chart(title='资产细分(V)分布变化(基金股票)',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('资产细分(V)' in x) and ('穿透' not in x), cal_fund.columns.to_list()))].dropna(how='all',axis=1).rename(
                                lambda x: x[8:-2], axis='columns').fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=11
                            ),
                multi_chart(title='管理方式(VI)分布变化(基金股票)',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('管理方式(VI)' in x) and ('穿透' not in x), cal_fund.columns.to_list()))].dropna(how='all',axis=1).rename(
                                lambda x: x[9:-2], axis='columns').fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=12
                            ),)
        except:
            pass
        # %%  基金穿透后持仓画图
        try:
            page_all.add(
                line_chart(title='穿透仓位水平变化',
                            df=(cal_fund.loc['是',['合计股票占比','合计转债占比']].dropna(how='all',axis=1).fillna(0)).round(2).astype(float),
                            id=13
                        ),
                multi_chart(title='穿透后资产类别(I)分布变化',
                            df=(cal_fund.loc['是', list(filter(lambda x: ('穿透资产类别(I)' in x) , cal_fund.columns.to_list()))].rename(
                                lambda x: x[10:-2], axis='columns').dropna(how='all',axis=1).fillna(0)*100).astype(float).round(2).drop(columns=['nan'], errors='ignore'),
                            id=14
                            ),
                multi_chart(title='穿透后资产地区(II)分布变化',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('穿透资产地区(II)' in x) , cal_fund.columns.to_list()))].rename(
                                lambda x: x[11:-2], axis='columns').dropna(how='all',axis=1).fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=15
                            ),
                multi_chart(title='穿透后资产类属(III)分布变化',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('穿透资产类属(III)' in x) , cal_fund.columns.to_list()))].rename(
                                lambda x: x[12:-2], axis='columns').dropna(how='all',axis=1).fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=16
                            ),
                multi_chart(title='穿透后资产板块(IV)分布变化',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('穿透资产板块(IV)' in x) , cal_fund.columns.to_list()))].rename(
                                lambda x: x[11:-2], axis='columns').dropna(how='all',axis=1).fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=17
                            ),
                multi_chart(title='穿透后资产细分(V)分布变化',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('穿透资产细分(V)' in x) , cal_fund.columns.to_list()))].rename(
                                lambda x: x[10:-2], axis='columns').dropna(how='all',axis=1).fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=18
                            ),
                multi_chart(title='穿透后管理方式(VI)分布变化',
                            df=pd.DataFrame((cal_fund.loc['是', list(filter(lambda x: ('穿透管理方式(VI)' in x) , cal_fund.columns.to_list()))].rename(
                                lambda x: x[11:-2], axis='columns').dropna(how='all',axis=1).fillna(0)*100).astype(float).round(2)).drop(columns=['nan'], errors='ignore'),
                            id=19
                            ),
                multi_chart(title='穿透后风格分布变化',
                            df=(cal_fund.loc['是',list(filter(lambda x: '穿透风格指数' in x ,cal_fund.columns.to_list()))].rename(lambda x:x[7:-2],axis='columns').dropna(how='all',axis=1).fillna(0)*100).round(2).astype(float),
                            id=20
                            ),
                multi_chart(title='穿透后申万一级行业分布变化',
                        df=(100*cal_fund.loc['是',list(filter(lambda x: '穿透申万一级' in x ,cal_fund.columns.to_list()))] \
                            .rename(lambda x:x[9:-2],axis='columns') \
                            .sort_values(by=end_day,axis=1,ascending=False) \
                            .dropna(how='all',axis=1).fillna(0)).round(2).astype(float),
                        sort=0,
                        id=21
                    ),
                bar_diff(title='穿透后申万一级行业分布环比变化',
                        df=(cal_fund.loc['是',list(filter(lambda x: '穿透申万一级' in x ,cal_fund.columns.to_list()))].rename(lambda x:x[9:-2],axis='columns').dropna(how='all',axis=1).fillna(0)*100).round(2).astype(float),
                        id=22
                    ),
                )
        except:
            pass

        id_old=23
        # %%   个基层面画图，持仓前20大，加减仓画图
        cal_fund_holding_value_i=cal_fund_holding_value.loc['是']
        cal_fund_holding_value_i['持仓市值占比']=cal_fund_holding_value_i['持仓市值占比'].fillna(0)
        df=cal_fund_holding_value_i.reset_index().pivot(index='日期',columns='持基代码',values='持仓市值占比').fillna(0)
        df_diff=df.diff(periods=1).dropna()
        df_diff_datei=(
        pd.DataFrame(df_diff.loc[end_day].sort_values(ascending=False))
                .rename({end_day:'持仓变动比例'},axis=1)
                .join(cal_fund_holding_value_i[cal_fund_holding_value_i['日期'] == end_day][['持基名称','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)','当季持基收益率']])
                .sort_values(by='持仓变动比例',ascending=False)
                .reset_index()
                [['持基代码','持基名称','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)','持仓变动比例','当季持基收益率']]
                )
        df_diff_datei['持仓变动比例']=df_diff_datei['持仓变动比例'].apply(lambda x: format(x, '.2%'))
        df_diff_datei['当季持基收益率'] = df_diff_datei['当季持基收益率'].apply(lambda x: format(x/100, '.2%'))
        cal_fund_holding_value_i=cal_fund_holding_value_i.reset_index().set_index('日期')

        fund_lagest=dict()
        fund_type=cal_fund_holding_value_i['资产类别(I)'].dropna().unique()
        for type_i in fund_type:
            fund_lagest[type_i]=pd.DataFrame()
            for i in range(1,4):
                temp=cal_fund_holding_value_i[cal_fund_holding_value_i['资产类别(I)'] == type_i]
                try:
                    cal_fund_holding_value_datei=(
                    temp.loc[dates[-i],['持基名称','资产类别(I)','持仓市值占比','当季持基收益率']]
                    .sort_values(by='持仓市值占比',ascending=False).iloc[:20]
                    .applymap(lambda x: format(x/100, '.2%') if isinstance(x, float) else x)
                    .rename(lambda x:'{0}_{1}'.format(dates[-i][2:6],x),axis='columns')
                    .reset_index().drop(columns='日期'))
                    fund_lagest[type_i] = pd.concat([fund_lagest[type_i], cal_fund_holding_value_datei], axis=1)
                except:
                    pass
        for type_i in fund_type:
            try:
                page_all.add(
                    table_chart(fund_lagest[type_i].fillna(''),title='持仓前20{0}基金'.format(type_i),id=id_old),
                    table_chart(df_diff_datei[df_diff_datei['资产类别(I)'] == type_i].iloc[:20].fillna(''),title='加仓前20{0}基金_{1}'.format(type_i,dates[-1]),id=id_old+1),
                    table_chart(df_diff_datei[df_diff_datei['资产类别(I)'] == type_i].iloc[-20:].fillna('').sort_values(by='持仓变动比例',ascending=False),title='减仓前20{0}基金_{1}'.format(type_i,dates[-1]),id=id_old+2),
                    )
                id_old = id_old + 3
            except:
                pass

        if wind_type in wind_type_list_yield:
            fund_info=fund_info[fund_info['类型']==wind_type]

        # %% 同业横向比较部分(基金管理人维度)数据处理
        fund_company = fund_info.groupby(['日期', '基金管理人']).median(numeric_only=True)
        # 补充基金管理人维度的基础数据
        # 持基数量，分散度
        fund_company['FOF基金数量'] = fund_info.groupby(['日期', '基金管理人'])['证券全称'].count()
        # 内外部基金配置
        fund_company['内部数量'] = fund_info[fund_info['内外部'] == '内部'].groupby(['日期', '基金管理人'])['内外部'].count()
        fund_company['内部占比'] = fund_company['内部数量'] / fund_company['FOF基金数量'] * 100
        fund_company = fund_company.reset_index().set_index('日期')
        fund_info = fund_info.reset_index().set_index('日期')
        # 筛选年报和半年报的数据
        fund_company = fund_company[fund_company.index.str.contains('0630|1231')]
        fund_info = fund_info[fund_info.index.str.contains('0630|1231')]
        half_year = fund_company.index.unique().tolist()

        # %% FOF基金持基数量
        fund_num = pd.DataFrame(index=company_list)
        temp = fund_company[['基金管理人', '持基数量']]
        for i in half_year:
            try:
                df1 = temp.loc[i].reset_index().set_index('基金管理人').rename(columns={'持基数量': i}).drop(
                    ['日期'], axis=1)
                fund_num = fund_num.join(df1)
            except:
                continue

        fund_num['持基数量 50"'] = fund_num.median(axis=1)
        fund_num.loc['25"'] = fund_num[fund_num.columns].quantile(0.75)
        fund_num.loc['50"'] = fund_num[fund_num.columns].quantile(0.5)
        fund_num.loc['75"'] = fund_num[fund_num.columns].quantile(0.25)

        # %% FOF基金股票占比
        fund_asset = pd.DataFrame(index=company_list)
        temp = fund_company[['基金管理人', '基金占比', '股票占比']]
        for i in half_year:
            try:
                df3 = temp.loc[i].reset_index().set_index('基金管理人').rename(
                    columns={'基金占比': i + '基金占比', '股票占比': i + '股票占比'}).drop(['日期'], axis=1)
                fund_asset = fund_asset.join(df3)
            except:
                continue

        fund_asset['基金占比 50"'] = fund_asset[list(filter(lambda x: '基金占比' in x, fund_asset.columns.to_list()))].median(
            axis=1)
        fund_asset['股票占比 50"'] = fund_asset[list(filter(lambda x: '股票占比' in x, fund_asset.columns.to_list()))].median(
            axis=1)
        fund_asset.loc['25"'] = fund_asset[fund_asset.columns].quantile(0.75)
        fund_asset.loc['50"'] = fund_asset[fund_asset.columns].quantile(0.5)
        fund_asset.loc['75"'] = fund_asset[fund_asset.columns].quantile(0.25)

        page_all.add(
            table_chart(
                fund_num.dropna(how='all', axis=0).applymap(lambda x: round(x, 2)).astype(str).fillna('').replace('nan',
                                                                                                                  '').reset_index().rename(
                    columns={'index': '基金公司'}), title='持基数量', id=id_old),
            table_chart(
                fund_asset.dropna(how='all', axis=0).applymap(lambda x: format(x / 100, '.2%')).astype(str).fillna(
                    '').replace('nan%',
                                '').reset_index().rename(
                    columns={'index': '基金公司'}), title='资产配置', id=id_old + 1), )
        id_old=id_old+2

        # FOF内部基金占比（区分权益和债券）
        temp = pd.DataFrame(fund_info.groupby(['日期', '基金管理人'])[['内部持股基规模占比', '内部持债基规模占比']].median(numeric_only=True)).reset_index().set_index(['日期'])
        fund_self_num = dict()
        fund_type=['内部股基','内部债基']
        fund_self_num['内部股基'] = pd.DataFrame(index=company_list)
        fund_self_num['内部债基'] = pd.DataFrame(index=company_list)
        for i in half_year:
            try:
                df2 = temp.loc[i].set_index('基金管理人').rename(
                    columns={'内部持股基规模占比': i }).drop(columns=['内部持债基规模占比'])
                fund_self_num['内部股基'] = fund_self_num['内部股基'].join(df2)
                df2 = temp.loc[i].set_index('基金管理人').rename(
                    columns={'内部持债基规模占比': i }).drop(columns=['内部持股基规模占比'])
                fund_self_num['内部债基'] = fund_self_num['内部债基'].join(df2)
            except:
                continue

        fund_self_num['内部股基']['50"'] = fund_self_num['内部股基'].median(axis=1)
        fund_self_num['内部债基']['50"'] = fund_self_num['内部债基'].median(axis=1)

        fund_self_num['内部股基'].loc['25"'] = fund_self_num['内部股基'][fund_self_num['内部股基'].columns].quantile(0.75)
        fund_self_num['内部股基'].loc['50"'] = fund_self_num['内部股基'][fund_self_num['内部股基'].columns].quantile(0.5)
        fund_self_num['内部股基'].loc['75"'] = fund_self_num['内部股基'][fund_self_num['内部股基'].columns].quantile(0.25)

        fund_self_num['内部债基'].loc['25"'] = fund_self_num['内部债基'][fund_self_num['内部债基'].columns].quantile(0.75)
        fund_self_num['内部债基'].loc['50"'] = fund_self_num['内部债基'][fund_self_num['内部债基'].columns].quantile(0.5)
        fund_self_num['内部债基'].loc['75"'] = fund_self_num['内部债基'][fund_self_num['内部债基'].columns].quantile(0.25)

        for type_i in fund_type:
            try:
                page_all.add(
                    table_chart(fund_self_num[type_i].dropna(how='all',axis=0).applymap(lambda x: format(x/100, '.2%')).astype(str).fillna('').replace('nan%','').reset_index().rename(columns={'index': '基金公司'}),title='持有{0}占比'.format(type_i),id=id_old),
                    )
                id_old = id_old + 1
            except:
                pass


        # %% FOF基金主动股基/主动债基占比
        # 剔除只能投资于ETF的FOF-LOF基金
        temp = fund_info[~fund_info['证券全称'].isin(
            ['汇添富积极投资指数优选一年定期开放股票型基金中基金(FOF-LOF)', '民生加银优享进取一年封闭运作股票型基金中基金(FOF-LOF)', '工银瑞信睿智进取股票型基金中基金(FOF-LOF)',
             '华夏行业配置股票型基金中基金(FOF-LOF)', '富国智鑫行业精选股票型基金中基金(FOF-LOF)', '华夏优选配置股票型基金中基金(FOF-LOF)'])]
        temp = temp.groupby(['日期', '基金管理人'])[['主动股基规模占比', '主动债基规模占比']].median(numeric_only=True)
        fund_type=['主动股基','主动债基']
        fund_active=dict()
        fund_active['主动债基'] = pd.DataFrame(index=company_list)
        fund_active['主动股基'] = pd.DataFrame(index=company_list)
        for i in half_year:
            try:
                df4 = temp.loc[i, :].reset_index().set_index('基金管理人').rename(
                    columns={'主动股基规模占比': i }).drop(columns=['主动债基规模占比'])
                fund_active['主动股基'] = fund_active['主动股基'].join(df4)
                df4 = temp.loc[i, :].reset_index().set_index('基金管理人').rename(
                    columns={ '主动债基规模占比': i }).drop(columns=['主动股基规模占比'])
                fund_active['主动债基'] = fund_active['主动债基'].join(df4)
            except:
                continue

        fund_active['主动股基']['50"'] = fund_active['主动股基'].median(axis=1)
        fund_active['主动股基'].loc['25"'] = fund_active['主动股基'][fund_active['主动股基'].columns].quantile(0.75)
        fund_active['主动股基'].loc['50"'] = fund_active['主动股基'][fund_active['主动股基'].columns].quantile(0.5)
        fund_active['主动股基'].loc['75"'] = fund_active['主动股基'][fund_active['主动股基'].columns].quantile(0.25)

        fund_active['主动债基']['50"'] = fund_active['主动债基'].median(axis=1)
        fund_active['主动股基'].loc['25"'] = fund_active['主动股基'][fund_active['主动股基'].columns].quantile(0.75)
        fund_active['主动股基'].loc['50"'] = fund_active['主动股基'][fund_active['主动股基'].columns].quantile(0.5)
        fund_active['主动股基'].loc['75"'] = fund_active['主动股基'][fund_active['主动股基'].columns].quantile(0.25)

        for type_i in fund_type:
            try:
                page_all.add(
                    table_chart(fund_active[type_i].dropna(how='all',axis=0).applymap(lambda x: format(x/100, '.2%')).astype(str).fillna('').replace('nan%','').reset_index().rename(columns={'index': '基金公司'}),title='持有{0}占比'.format(type_i),id=id_old),
                    )
                id_old = id_old + 1
            except:
                pass


        # %% FOF基金权益基金和股票中资产类属(III)占比
        fund_type = list(fund_fund[fund_fund['资产类别(I)']=='股票']['资产类属(III)'].unique())
        temp = fund_company[
            list(filter(lambda x: ('资产类属(III)' in x)and ('穿透' not in x), fund_company.columns.to_list())) + ['基金管理人']].reset_index().set_index(
            ['日期', '基金管理人'])
        temp = temp[list(filter(lambda x: '资产类属(III)' in x, temp.columns.to_list()))].rename(lambda x: x[10:-2],
                                                                                             axis='columns')
        temp=temp[fund_type]
        temp[list(filter(lambda x: '资产类属(III)' in x, temp.columns.to_list()))] = temp[
            list(filter(lambda x: '资产类属(III)' in x, temp.columns.to_list()))].div(temp.sum(axis=1), axis='index')


        for type_i in fund_type:
            fund_stocklabel = pd.DataFrame(index=company_list)
            for i in half_year:
                try:
                    df8 = temp[[type_i]].loc[i].reset_index().set_index('基金管理人').rename(
                        columns={type_i: i})
                    fund_stocklabel = fund_stocklabel.join(df8)
                except:
                    continue
            fund_stocklabel['50"'] = fund_stocklabel.median(axis=1)
            fund_stocklabel.loc['25"'] = fund_stocklabel[fund_stocklabel.columns].quantile(0.75)
            fund_stocklabel.loc['50"'] = fund_stocklabel[fund_stocklabel.columns].quantile(0.5)
            fund_stocklabel.loc['75"'] = fund_stocklabel[fund_stocklabel.columns].quantile(0.25)
            if (fund_stocklabel.isna() | fund_stocklabel.round(2).eq(0)).all().all():
                continue
            else:
                try:
                    page_all.add(
                        table_chart(
                            fund_stocklabel.dropna(how='all', axis=0).applymap(lambda x: format(x, '.2%')).astype(str).fillna(
                                '').replace('nan%', '').reset_index().rename(columns={'index': '基金公司'}),
                            title='权益(基金股票)资产类属(III)-{0}占比'.format(type_i), id=id_old),
                    )
                    id_old = id_old + 1
                except:
                    pass


        # %% FOF基金债基中资产细分(V)占比
        fund_type = list(fund_fund[fund_fund['资产类别(I)'] == '债券']['资产细分(V)'].unique())
        temp = fund_company[
            list(filter(lambda x: ('资产细分(V)' in x) and ('穿透' not in x), fund_company.columns.to_list())) + [
                '基金管理人']].reset_index().set_index(
            ['日期', '基金管理人'])
        temp = temp[list(filter(lambda x: '资产细分(V)' in x, temp.columns.to_list()))].rename(lambda x: x[8:-2],
                                                                                           axis='columns')
        temp = temp[fund_type]
        temp[list(filter(lambda x: '资产细分(V)' in x, temp.columns.to_list()))] = temp[
            list(filter(lambda x: '资产细分(V)' in x, temp.columns.to_list()))].div(temp.sum(axis=1), axis='index')

        for type_i in fund_type:
            fund_bondlabel = pd.DataFrame(index=company_list)
            for i in half_year:
                try:
                    df8 = temp[[type_i]].loc[i].reset_index().set_index('基金管理人').rename(
                        columns={type_i: i})
                    fund_bondlabel = fund_bondlabel.join(df8)
                except:
                    continue
            fund_bondlabel['50"'] = fund_bondlabel.median(axis=1)
            fund_bondlabel.loc['25"'] = fund_bondlabel[fund_bondlabel.columns].quantile(0.75)
            fund_bondlabel.loc['50"'] = fund_bondlabel[fund_bondlabel.columns].quantile(0.5)
            fund_bondlabel.loc['75"'] = fund_bondlabel[fund_bondlabel.columns].quantile(0.25)
            if (fund_bondlabel.isna() | fund_bondlabel.round(2).eq(0)).all().all():
                continue
            else:
                try:
                    page_all.add(
                        table_chart(
                            fund_bondlabel.dropna(how='all', axis=0).applymap(lambda x: format(x, '.2%')).astype(str).fillna(
                                '').replace('nan%', '').reset_index().rename(columns={'index': '基金公司'}),
                            title='债券基金资产细分(V)-{0}占比'.format(type_i), id=id_old),
                    )
                    id_old = id_old + 1
                except:
                    pass



        # %% FOF基金穿透后风格占比
        fund_type = [x[7:-2] for x in list(filter(lambda x: '穿透风格指数' in x, fund_company.columns.to_list()))]
        temp = fund_company[
            list(filter(lambda x: '穿透风格指数' in x, fund_company.columns.to_list())) + ['基金管理人']].reset_index().set_index(['日期', '基金管理人'])
        temp=temp[list(filter(lambda x: '穿透风格指数' in x, temp.columns.to_list()))].rename(lambda x: x[7:-2],axis='columns')

        for type_i in fund_type:
            fund_style = pd.DataFrame(index=company_list)
            for i in half_year:
                try:
                    df8 = temp[[type_i]].loc[i].reset_index().set_index('基金管理人').rename(
                    columns={type_i: i })
                    fund_style = fund_style.join(df8)
                except:
                    continue
            fund_style['50"'] = fund_style.median(axis=1)
            fund_style.loc['25"'] = fund_style[fund_style.columns].quantile(0.75)
            fund_style.loc['50"'] = fund_style[fund_style.columns].quantile(0.5)
            fund_style.loc['75"'] = fund_style[fund_style.columns].quantile(0.25)
            try:
                page_all.add(
                    table_chart(fund_style.dropna(how='all',axis=0).applymap(lambda x: format(x, '.2%')).astype(str).fillna('').replace('nan%','').reset_index().rename(columns={'index': '基金公司'}),title='穿透后风格指数-{0}占比'.format(type_i),id=id_old),
                    )
                id_old = id_old + 1
            except:
                pass


        page_all.render("季报分析结果导出\{0}_{1}_{2}_{3}.html".format(wind_type,company_label,cal_type,end_day))

        t=printtime(t)

printtime(1)
