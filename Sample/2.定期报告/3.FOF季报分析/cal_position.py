# -*- coding: utf-8 -*-
"""
Created on Wed Jan  6 13:25:56 2021

@author: gyr<PERSON>-<PERSON><PERSON><PERSON><PERSON>
"""

import pandas as pd
import numpy as np
import datetime as dt
from dateutil.relativedelta import relativedelta
from scipy.optimize import lsq_linear

from WindPy import *
w.start()

def exposure_cal(fund_list, cal_day, windows):
    zxfg = w.wsd("CI005917.WI,CI005918.WI,CI005919.WI,CI005920.WI,CI005921.WI", "close", "ED-" + str(windows) + "TD", cal_day, "")
    zxfg = pd.DataFrame({'Time': pd.to_datetime(zxfg.Times),'金融': zxfg.Data[0],'周期': zxfg.Data[1],
                         '消费': zxfg.Data[2],'成长': zxfg.Data[3],'稳定': zxfg.Data[4]})
    zxfg = zxfg.set_index('Time')
    zxfg = (zxfg / zxfg.shift(1) - 1) * 100
    zxfg['cons'] = 1
    zxfg = zxfg.dropna()
    fund_nav = w.wsd(fund_list, "NAV_adj", "ED-" + str(windows) + "TD", cal_day, "")
    fund_nav = pd.DataFrame(np.array(fund_nav.Data).T, index = fund_nav.Times, columns = fund_nav.Codes)
    fund_rtn = (fund_nav/fund_nav.shift(1) - 1) * 100 
    fund_rtn = fund_rtn.dropna(how = 'all')
    
    lb = [0,0,0,0,0,-np.inf]
    ub = [1,1,1,1,1,np.inf]
    # lb = [0 for i in zxfg.columns]
    # ub = [1 for i in zxfg.columns]
    position = pd.DataFrame(index = fund_list, columns = zxfg.columns)
    for i in fund_list:
        res = lsq_linear(zxfg.loc[fund_rtn[i].dropna().index], fund_rtn[i].dropna(), bounds=(lb, ub), lsmr_tol='auto')
        position.loc[i] = res.x
    position = position.astype(float)
    position[position < 1e-8] = 0
    position['position'] = position['金融'] + position['周期'] + position['消费'] + position['成长']
    return position['position'] *100
