# %%
# -*- coding: utf-8 -*-
'''
Created on 20230517

@author: gyrx-liyx

本函数用来导出特定季报日期的基金信息fund_info、基金持基明细、基金持股明细、基金持债明细
'''

# %%基础包导入和函数定义
import os
from WindPy import w
import numpy as np
import pandas as pd
import datetime as dt
import pickle
import cx_Oracle
cx_Oracle.init_oracle_client(os.path.join('.', 'tools', 'clt64', 'instantclient_11_2'))
import warnings
import cal_position
from pandas.errors import SettingWithCopyWarning
from dateutil.relativedelta import relativedelta
import urllib3
from json import dumps,loads
warnings.simplefilter(action='ignore',category=FutureWarning)
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.simplefilter(action="ignore", category=UserWarning)
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')
w.start()

oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@192.168.105.38:1521/wind', encoding="UTF-8")
# 定义函数用于提取基金基本信息
def fund_type_df(date,code):
    fund_list = w.wset('sectorconstituent',date=date,sectorid=code)
    if len(fund_list.Data[1]) < 500:
        fund = pd.DataFrame({'证券代码':fund_list.Data[1],'证券简称':fund_list.Data[2],'投资类型(一级分类)':w.wss(fund_list.Data[1], "fund_firstinvesttype").Data[0],'投资类型(二级分类)':w.wss(fund_list.Data[1], "fund_investtype").Data[0],'证券全称':w.wss(fund_list.Data[1], "fund_fullname").Data[0],'是否初始基金':w.wss(fund_list.Data[1], "fund_initial").Data[0],'是否披露季报':w.wss(fund_list.Data[1], "qanal_income",unit=1,rptDate=date).Data[0]}).set_index('证券代码')
    else:
        fund=pd.DataFrame()
        for i in range(0,len(fund_list.Data[1])//500):
            fund0 = pd.DataFrame({'证券代码':fund_list.Data[1][500*i:500*(1+i)],'证券简称':fund_list.Data[2][500*i:500*(1+i)],'投资类型(一级分类)':w.wss(fund_list.Data[1][500*i:500*(1+i)], "fund_firstinvesttype").Data[0],'投资类型(二级分类)':w.wss(fund_list.Data[1][500*i:500*(1+i)], "fund_investtype").Data[0],'证券全称':w.wss(fund_list.Data[1][500*i:500*(1+i)], "fund_fullname").Data[0],'是否初始基金':w.wss(fund_list.Data[1][500*i:500*(1+i)], "fund_initial").Data[0],'是否披露季报':w.wss(fund_list.Data[1][500*i:500*(1+i)], "qanal_income",unit=1,rptDate=date).Data[0]}).set_index('证券代码')
            fund=pd.concat([fund,fund0],axis=0)
        if len(fund_list.Data[1])%500 > 0:
            fund0 = pd.DataFrame({'证券代码':fund_list.Data[1][500*(1+i):],'证券简称':fund_list.Data[2][500*(1+i):],'投资类型(一级分类)':w.wss(fund_list.Data[1][500*(1+i):], "fund_firstinvesttype").Data[0],'投资类型(二级分类)':w.wss(fund_list.Data[1][500*(1+i):], "fund_investtype").Data[0],'证券全称':w.wss(fund_list.Data[1][500*(1+i):], "fund_fullname").Data[0],'是否初始基金':w.wss(fund_list.Data[1][500*(1+i):], "fund_initial").Data[0],'是否披露季报':w.wss(fund_list.Data[1][500*(1+i):], "qanal_income",unit=1,rptDate=date).Data[0]}).set_index('证券代码')
            fund=pd.concat([fund,fund0],axis=0)
    fund = fund[(fund['是否初始基金'] == '是')&(fund['是否披露季报'].isna()==False)]
    return fund

dates = ['20240630']#,'20240331',
         # '20231231', '20230930', '20230630', '20230331',
         # '20221231', '20220930', '20220630',   '20220331',
         # '20211231',  '20210930', '20210630',   '20210331',
         # '20201231',  '20200930',  '20200630',  '20200331',
         # '20191231',  '20190930', '20190630','20190331']
# %%进入主程序
for date_i in dates:
    try:
        url = 'http://192.168.105.63/GetFndLevelClassify'
        kw = {'username': 'gyrx', 'passwd': '4E4AB38D17E274B0D2D6A846AE4393E7'}
        http = urllib3.PoolManager()
        r = http.request('post', url, body=dumps(kw))
        data_pd = pd.DataFrame(loads(r.data.decode())['data'])
        data_pd.index = [x + '.OF' for x in data_pd['FUNDCODE']]
        data_pd['FUNDCLOSEDATE'] = [dt.datetime.strptime(x[0:10], "%Y-%m-%d") for x in data_pd['FUNDCLOSEDATE']]
        data_pd['ESTABDATE'] = [dt.datetime.strptime(x[0:10], "%Y-%m-%d") for x in data_pd['ESTABDATE']]
        data_pd = data_pd.loc[:, ['FUNDSNAME', 'F_FNAME', 'ESTABDATE', 'LEVEL1NAME', 'LEVEL2NAME', 'LEVEL3NAME']]
        data_pd.columns = ['基金简称', '基金管理人全称', '成立日', '银河证券一级分类', '银河证券二级分类', '银河证券三级分类']
        data_pd.to_excel('基金池/银河基金数据.xlsx')
    except:
        data_pd = pd.read_excel('基金池/银河基金数据.xlsx', index_col=0)
    # %% 提取基金的基础数据fund_info
    trade_date=w.tdaysoffset(0, date_i, "").Data[0][0]
    trade_date=trade_date.strftime('%Y-%m-%d')
    rpt_date=dt.datetime.strptime(date_i,'%Y%m%d')
    rpt_date=rpt_date.strftime('%Y-%m-%d')
    # 基金标签需要根据最新基金池结果更新
    fund_label = pd.read_excel('基金池/2.8.1data_label.xlsx',sheet_name='all',index_col=0)

    #提取基金池
    fund_type = {'FOF基金':'1000041489000000'}
    fund_df_wind = pd.DataFrame()
    for code_i in fund_type:
        fund_df_wind = pd.concat([fund_df_wind,fund_type_df(date_i,fund_type[code_i])],axis=0,sort=False)
    fund_df=fund_df_wind[['证券简称','证券全称','投资类型(二级分类)']].rename({'投资类型(二级分类)':'WIND分类'},axis=1).reset_index()
    fund_df['类型']=fund_df['证券代码'].apply(lambda x: data_pd.loc[x[:6] + '.OF', '银河证券三级分类'][:-4]).astype(str)



    #把基金代码统一成数据库的代码，.SZ .SH
    sql="""
    SELECT F_INFO_WINDCODE AS WIND代码,F_INFO_FRONT_CODE AS 基金代码
    FROM winddf.ChinaMutualFundDescription
    """
    fund_code_T=pd.read_sql_query(sql, oracle_conn)

    for i in range(len(fund_df)):
        if fund_df.loc[i,'证券代码'] not in fund_code_T['WIND代码'].tolist():
            if fund_df.loc[i,'证券代码'][:-3]+'.SZ' in fund_code_T['WIND代码'].tolist():
                fund_df.loc[i,'证券代码']=fund_df.loc[i,'证券代码'][:-3]+'.SZ'
            elif fund_df.loc[i,'证券代码'][:-3]+'.SH' in fund_code_T['WIND代码'].tolist():
                fund_df.loc[i,'证券代码']=fund_df.loc[i,'证券代码'][:-3]+'.SH'
            else:
                print(fund_df.loc[i,'证券代码']+'在数据库找不到对应基金代码')

    #导出基金业绩信息
    sql="""
    SELECT
    S_INFO_WINDCODE AS 基金代码,F_AVGRETURN_THISQUARTER AS 季度收益率,F_AVGRETURN_THISYEAR AS YTD
    FROM winddf.CHINAMFPERFORMANCE
    WHERE TRADE_DT = {0}
    """.format(w.tdaysoffset(0, date_i, "").Data[0][0].strftime('%Y%m%d'))
    fund_info0=pd.read_sql_query(sql, oracle_conn)
    fund_info0=fund_info0.set_index('基金代码').loc[list(set(fund_info0['基金代码'].tolist())&set(fund_df['证券代码'].tolist()))]
    #导出基金发行信息
    sql = """
        SELECT
        S_INFO_WINDCODE AS 基金代码,F_INFO_MGRCOMP AS 基金管理人,F_INFO_SETUPDATE AS 基金成立日
        FROM winddf.ChinaMutualFundIssue
        """
    fund_info4 = pd.read_sql_query(sql, oracle_conn)
    fund_info4 = fund_info4.set_index('基金代码').loc[
        list(set(fund_info4['基金代码'].tolist()) & set(fund_df['证券代码'].tolist()))]

    #导出基金基本信息，当前时点规模、各类型资产投资市值等
    sql="""
    SELECT
    S_INFO_WINDCODE AS 基金代码,F_PRT_ENDDATE AS 日期,F_PRT_TOTALASSET AS 基金资产总值,F_PRT_NETASSET AS 基金资产净值,
    F_PRT_STOCKVALUE AS 股票投资市值,F_PRT_BONDVALUE AS 债券投资市值,F_PRT_HKSTOCKVALUE AS 港股投资市值,F_PRT_FUNDVALUE AS 基金投资市值,
    F_PRT_GOVBOND AS 国债投资市值,F_PRT_FINANBOND AS 金融债投资市值,F_PRT_COVERTBOND AS 可转债投资市值,F_PRT_CORPBOND AS 企业债投资市值,F_PRT_CTRBANKBILL AS 央行票据投资市值,F_PRT_CDS AS 同业存单投资市值, F_PRT_POLIFINANBDVALUE AS 政策性金融债投资市值,F_PRT_CPVALUE AS 短融投资市值,F_PRT_MTNVALUE AS 中票投资市值
    FROM winddf.CHINAMUTUALFUNDASSETPORTFOLIO
    WHERE F_PRT_ENDDATE = {0}
    """.format(date_i)
    fund_info1=pd.read_sql_query(sql, oracle_conn).fillna(0)
    fund_info1=fund_info1.set_index('基金代码').loc[list(set(fund_info1['基金代码'].tolist())&set(fund_df['证券代码'].tolist()))]
    pre_date = pd.to_datetime(date_i) - pd.tseries.offsets.QuarterBegin(startingMonth=1) - dt.timedelta(days=1)

    # 导出基金基本信息，上一期规模
    sql = """
       SELECT
       S_INFO_WINDCODE AS 基金代码,F_PRT_TOTALASSET AS 上期基金资产总值,F_PRT_NETASSET AS 上期基金资产净值
       FROM winddf.CHINAMUTUALFUNDASSETPORTFOLIO
       WHERE F_PRT_ENDDATE = {0}
       """.format(pre_date.strftime('%Y%m%d'))
    fund_info1_pre = pd.read_sql_query(sql, oracle_conn).fillna(0)
    fund_info1_pre = fund_info1_pre.set_index('基金代码').loc[
        list(set(fund_info1_pre['基金代码'].tolist()) & set(fund_df['证券代码'].tolist()))]

    # 导出基金基本信息，上一期份额
    sql = """
        SELECT
        F_INFO_WINDCODE AS 基金代码, FUNDSHARE_TOTAL/10000 AS 上期基金份额
        FROM winddf.ChinaMutualFundShare
        WHERE 	CHANGE_DATE={0}
        """.format(pre_date.strftime('%Y%m%d'))
    fund_info5_pre = pd.read_sql_query(sql, oracle_conn).fillna(0)
    fund_info5_pre = fund_info5_pre.set_index('基金代码').loc[
        list(set(fund_info5_pre['基金代码'].tolist()) & set(fund_df['证券代码'].tolist()))]

    # 导出基金基本信息，当期份额
    sql = """
            SELECT
            F_INFO_WINDCODE AS 基金代码, FUNDSHARE_TOTAL/10000 AS 基金份额
            FROM winddf.ChinaMutualFundShare
            WHERE 	CHANGE_DATE={0}
            """.format(date_i)
    fund_info5 = pd.read_sql_query(sql, oracle_conn).fillna(0)
    fund_info5 = fund_info5.set_index('基金代码').loc[
        list(set(fund_info5['基金代码'].tolist()) & set(fund_df['证券代码'].tolist()))]

    # 导出基金基本信息，机构投资者占比
    sql="""
    SELECT S_INFO_WINDCODE AS 基金代码,HOLDER_INSTITUTION_HOLDINGPCT AS 机构投资者占比
    FROM winddf.CMFHolderStructure
    WHERE END_DT = {0}
    """.format(date_i)
    fund_info2=pd.read_sql_query(sql, oracle_conn).fillna(0)
    fund_info2=fund_info2.set_index('基金代码').loc[list(set(fund_info2['基金代码'].tolist())&set(fund_df['证券代码'].tolist()))]
    fund_info1[['股票占比','债券占比','港股占比','可转债占比']]=fund_info1[['股票投资市值','债券投资市值','港股投资市值','可转债投资市值']].div(fund_info1['基金资产净值'],axis='index')*100
    fund_info1['企业债投资市值']=fund_info1['企业债投资市值']-fund_info1['短融投资市值']-fund_info1['中票投资市值']
    fund_info1[['国债','金融债','可转债','企业债','央行票据','同业存单','短融','中票','政金债']]=fund_info1[['国债投资市值','金融债投资市值','可转债投资市值','企业债投资市值','央行票据投资市值','同业存单投资市值','短融投资市值','中票投资市值','政策性金融债投资市值']].div(fund_info1['债券投资市值'],axis='index')*100
    fund_info1['信用债']=fund_info1['企业债']+fund_info1['短融']+fund_info1['中票']+fund_info1['金融债']-fund_info1['政金债']
    fund_info1['利率债']=fund_info1['国债']+fund_info1['政金债']+fund_info1['央行票据']
    fund_info1['其他债']=100-fund_info1['信用债']-fund_info1['利率债']-fund_info1['可转债']
    fund_info1.fillna({'国债': 0,'金融债':0,'可转债':0,'企业债':0,'央行票据':0,'同业存单':0,'短融':0,'中票':0,'政金债':0,'信用债':0,'利率债':0,'其他债':0}, inplace=True)
    fund_info1['杠杆率']=fund_info1['基金资产总值'].div(fund_info1['基金资产净值'],axis='index')
    # 导出基金基本信息，基金规模、前十个股集中度等
    field={'netasset_total':'基金规模','style_topnproportiontoallshares':'前十个股集中度','style_averagepositiontime':'平均持仓时间','style_avgpositiontimeranking':'平均持仓时间同类排名','style_styleattribute':'风格属性','style_marketvalueattribute':'市值属性'}
    try:
        data=w.wss(','.join(fund_df['证券代码']), ','.join(field.keys()),year=rpt_date[:4], fundType=3, tradeDate=trade_date, rptDate=rpt_date, unit=1, topN=10)
        fund_info3=pd.DataFrame(np.array(data.Data).T,index=data.Codes, columns=[field[i.lower()] for i in data.Fields]).fillna(0)
    except:
        fund_info3=pd.DataFrame()
        for i in range(0,len(fund_df['证券代码'])//500):
            data=w.wss(','.join(fund_df['证券代码'][500*i:500*(1+i)]), ','.join(field.keys()),year=rpt_date[:4], fundType=3, tradeDate=trade_date, rptDate=rpt_date, unit=1, topN=10)
            fund_info3i=pd.DataFrame(np.array(data.Data).T,index=data.Codes, columns=[field[i.lower()] for i in data.Fields]).fillna(0)
            fund_info3=pd.concat([fund_info3,fund_info3i],axis=0)
        if len(fund_df['证券代码'])%500 > 0:
            data=w.wss(','.join(fund_df['证券代码'][500*(1+i):]), ','.join(field.keys()),year=rpt_date[:4], fundType=3, tradeDate=trade_date, rptDate=rpt_date, unit=1, topN=10)
            fund_info3i=pd.DataFrame(np.array(data.Data).T,index=data.Codes, columns=[field[i.lower()] for i in data.Fields]).fillna(0)
            fund_info3=pd.concat([fund_info3,fund_info3i],axis=0)
    # 数据处理
    style_dict={'价值风格型基金':0,'平衡风格型基金':1,'成长风格型基金':2,0:None}
    marketvalue_dict={'小盘风格型基金':0,'中盘风格型基金':1,'大盘风格型基金':2,0:None}
    fund_info3['风格属性']=[style_dict[i] for i in fund_info3['风格属性']]
    fund_info3['市值属性']=[marketvalue_dict[i] for i in fund_info3['市值属性']]
    fund_info3.index.name='基金代码'
    fund_info=pd.concat([fund_info0,fund_info4,fund_info1,fund_info1_pre,fund_info5,fund_info5_pre,fund_info2,fund_info3],axis=1,join='outer')

    fund_info=fund_df.rename({'证券代码':'基金代码'},axis=1).set_index('基金代码').join(fund_info).dropna(axis=0,how='all')
    fund_list = fund_info.index.to_list()
    fund_info['权益仓位']=fund_info['股票占比']+0.5*fund_info['可转债占比']#仓位
    fund_info['估算仓位'] = cal_position.exposure_cal(fund_list, date_i, windows=60)
    fund_info['股票占比(港股除外)']=fund_info['股票占比']-fund_info['港股占比']
    fund_info['纯债占比']=fund_info['债券占比']-fund_info['可转债占比']
    # fund_info['其他资产占比']=1-fund_info['股票占比']-fund_info['债券占比']
    fund_info['纯债投资市值']=fund_info['债券投资市值']-fund_info['可转债投资市值']





    # %% 导出基金持基明细fund_fund

    sql = """
        SELECT S_INFO_WINDCODE AS 基金代码,END_DT AS 日期,S_INFO_HOLDWINDCODE AS 持基代码,VALUE AS 持基市值,VALUETONAV AS 持基占比
        FROM winddf.CMFOtherPortfolio
        WHERE END_DT = {0}
        """.format(date_i)
    fund_fund = pd.read_sql_query(sql, oracle_conn)
    fund_fund = fund_fund.set_index('基金代码').loc[list(set(fund_fund['基金代码'].tolist()) & set(fund_list))].reset_index()

    for i in range(len(fund_fund)):
        if fund_fund.loc[i,'持基代码'] not in fund_code_T['WIND代码'].tolist():
            if fund_fund.loc[i,'持基代码'][:-3]+'.SZ' in fund_code_T['WIND代码'].tolist():
                fund_fund.loc[i,'持基代码']=fund_fund.loc[i,'持基代码'][:-3]+'.SZ'
            elif fund_fund.loc[i,'持基代码'][:-3]+'.SH' in fund_code_T['WIND代码'].tolist():
                fund_fund.loc[i,'持基代码']=fund_fund.loc[i,'持基代码'][:-3]+'.SH'
            else:
                print(fund_fund.loc[i,'持基代码']+'在数据库找不到对应基金代码')

    fund_initialcode=pd.DataFrame({'持基代码':fund_fund['持基代码'].unique().tolist(),'持基初始代码':w.wss(fund_fund['持基代码'].unique().tolist(), "fund_initialcode").Data[0]})
    fund_initialcode['持基初始代码'].fillna(fund_initialcode['持基代码'], inplace=True)
    def update_code(code):
        if any(code[:6] + '.SZ' in x for x in fund_initialcode['持基代码']):
            return code[:6] + '.SZ'
        elif any(code[:6] + '.SH' in x for x in fund_initialcode['持基代码']):
            return code[:6] + '.SH'
        else:
            return code[:6] + '.OF'
    fund_initialcode['持基初始代码'] = fund_initialcode['持基初始代码'].apply(update_code)

    fund_fund=pd.merge(fund_fund,fund_initialcode, on='持基代码', how='left')
    fund_fund.rename(columns={'持基代码': '持基原代码'}, inplace=True)
    fund_fund.rename(columns={'持基初始代码': '持基代码'}, inplace=True)

    fund_fund_list = fund_fund['持基代码'].unique().tolist()
    #导出基金管理人信息
    sql = """
            SELECT
            S_INFO_WINDCODE AS 基金代码,F_INFO_MGRCOMP AS 基金管理人
            FROM winddf.ChinaMutualFundIssue
            """
    fund_fund1 = pd.read_sql_query(sql, oracle_conn)
    fund_fund1 = fund_fund1.set_index('基金代码').loc[
        list(set(fund_fund1['基金代码'].tolist()) & set(fund_fund['基金代码'].unique().tolist()))]
    fund_fund = pd.merge(fund_fund, fund_fund1, on='基金代码', how='left')
    # 导出持基管理人信息
    sql = """
                SELECT
                S_INFO_WINDCODE AS 持基代码,F_INFO_MGRCOMP AS 持基基金管理人
                FROM winddf.ChinaMutualFundIssue
                """
    fund_fund2 = pd.read_sql_query(sql, oracle_conn)
    fund_fund2 = fund_fund2.set_index('持基代码').loc[
        list(set(fund_fund2['持基代码'].tolist()) & set(fund_fund_list))]
    #对于数据库中提取不到的信息用API补齐
    left_code=list(set(fund_fund_list)-set(fund_fund2.index.tolist()))
    if left_code:
        fund_fund2plus = pd.DataFrame({'持基代码':left_code,'持基基金管理人':w.wss(left_code, "fund_corp_fundmanagementcompany").Data[0]}).set_index('持基代码')
        fund_fund2=pd.concat([fund_fund2,fund_fund2plus],axis=0).reset_index()
    else:
        fund_fund2=fund_fund2.reset_index()
    fund_fund = pd.merge(fund_fund, fund_fund2, on='持基代码', how='left')


    # 根据基金管理人和持基基金管理人判断是否为内部基金
    fund_fund['是否内部'] = np.where(fund_fund['基金管理人'] == fund_fund['持基基金管理人'], '内部', '外部')
    # # 导出当季持基收益和持基YTD
    date_str=w.tdaysoffset(0, date_i, "").Data[0][0].strftime('%Y%m%d')
    last3m = (dt.datetime.strptime(date_i,'%Y%m%d')+ relativedelta(months=-3) + relativedelta(days=1)).strftime('%Y%m%d')
    ytd = str(dt.datetime.strptime(date_i,'%Y%m%d').year) +'-01-01'
    sql = """
        SELECT
        S_INFO_WINDCODE AS 持基代码,F_AVGRETURN_THISQUARTER AS 当季持基收益率,F_AVGRETURN_THISYEAR AS 持基YTD
        FROM winddf.CHINAMFPERFORMANCE
        WHERE TRADE_DT = {0}
        """.format(date_str)
    fund_fund0 = pd.read_sql_query(sql, oracle_conn)
    fund_fund0 = fund_fund0.set_index('持基代码').loc[list(set(fund_fund0['持基代码'].tolist()) & set(fund_fund_list))]
    #对于数据库中提取不到的信息用API补齐
    left_code=list(set(fund_fund_list)-set(fund_fund0.index.tolist()))
    if left_code:
        fund_fund0plus = pd.DataFrame({'持基代码':left_code,'当季持基收益率':w.wss(left_code, "NAV_adj_return", "startDate=" + last3m + ";endDate=" + date_str).Data[0],'持基YTD':w.wss(left_code, "NAV_adj_return", "startDate=" + ytd + ";endDate=" + date_str).Data[0]}).set_index('持基代码')
        fund_fund0=pd.concat([fund_fund0,fund_fund0plus],axis=0).reset_index()
    else:
        fund_fund0=fund_fund0.reset_index()
    fund_fund = pd.merge(fund_fund, fund_fund0, on='持基代码', how='left')
    # 根据当前日期，导出FOF下季持基收益率
    today = dt.datetime.today() + relativedelta(days=-1)
    next_date = pd.Timestamp(date_i) + pd.tseries.offsets.QuarterEnd(startingMonth=3)
    next_month1 = (next_date - pd.tseries.offsets.MonthEnd()).strftime('%Y%m%d')
    next_month2 = (pd.to_datetime(next_month1) - pd.tseries.offsets.MonthEnd()).strftime('%Y%m%d')

    if today>next_date:
        next_date = next_date.strftime('%Y%m%d')
        sql = """
            SELECT
            S_INFO_WINDCODE AS 持基代码,F_AVGRETURN_THISQUARTER AS 下季持基收益率
            FROM winddf.CHINAMFPERFORMANCE
            WHERE TRADE_DT = {0}
            """.format(w.tdaysoffset(0, next_date, "").Data[0][0].strftime('%Y%m%d'))
        fund_fund3 = pd.read_sql_query(sql, oracle_conn)
        fund_fund3 = fund_fund3.set_index('持基代码').loc[
        list(set(fund_fund3['持基代码'].tolist()) & set(fund_fund_list))]
        # 对于数据库中提取不到的信息用API补齐
        left_code = list(set(fund_fund_list) - set(fund_fund3.index.tolist()))
        if left_code:
            fund_fund3plus = pd.DataFrame({'持基代码': left_code, '下季持基收益率':
                w.wss(left_code, "NAV_adj_return", "startDate=" + w.tdaysoffset(1, date_i, "").Data[0][0].strftime('%Y%m%d') + ";endDate=" + next_date).Data[0]}).set_index('持基代码')
            fund_fund3 = pd.concat([fund_fund3, fund_fund3plus], axis=0).reset_index()
        else:
            fund_fund3=fund_fund3.reset_index()
        fund_fund = pd.merge(fund_fund, fund_fund3, on='持基代码', how='left')

        sql = """
                            SELECT
                            S_INFO_WINDCODE AS 基金代码,F_AVGRETURN_THISQUARTER AS 下季收益率
                            FROM winddf.CHINAMFPERFORMANCE
                            WHERE TRADE_DT = {0}
                            """.format(w.tdaysoffset(0, next_date, "").Data[0][0].strftime('%Y%m%d'))
        fund_fund4 = pd.read_sql_query(sql, oracle_conn)
        fund_fund4 = fund_fund4.set_index('基金代码').loc[
            list(set(fund_fund4['基金代码'].tolist()) & set(fund_list))]
        # 对于数据库中提取不到的信息用API补齐
        left_code = list(set(fund_list) - set(fund_fund4.index.tolist()))
        if left_code:
            fund_fund4plus = pd.DataFrame({'基金代码': left_code, '下季收益率':
                w.wss(left_code, "NAV_adj_return",
                      "startDate=" + w.tdaysoffset(1, date_i, "").Data[0][0].strftime('%Y%m%d') + ";endDate=" + next_date).Data[
                    0]}).set_index('基金代码')
            fund_fund4 = pd.concat([fund_fund4, fund_fund4plus], axis=0)
        fund_info=fund_info.join(fund_fund4)
    else:
        fund_fund['下季持基收益率']=0
        fund_info['下季收益率']=0

    # 补充持有基金的基金标签
    fund_fund_type=pd.DataFrame({'持基代码': fund_fund_list}).set_index('持基代码')
    fund_label.rename(columns={'简称':'持基简称','成立日期':'持基成立日期','Wind二级分类':'持基Wind二级分类'},inplace=True)
    fund_fund_type=fund_fund_type.join(fund_label).reset_index()
    fund_fund=pd.merge(fund_fund,fund_fund_type, on='持基代码', how='left')
    if fund_fund.empty:
        print("本期没有基金持仓")
    # 获取股票占比和转债占比
    sql = """
        SELECT
        S_INFO_WINDCODE AS 持基代码,F_PRT_STOCKTONAV AS 持基股票占比,F_PRT_COVERTBONDTONAV AS 持基转债占比
        FROM winddf.CHINAMUTUALFUNDASSETPORTFOLIO
        WHERE F_PRT_ENDDATE = {0}
        """.format(date_i)
    fund_fund5 = pd.read_sql_query(sql, oracle_conn).fillna(0)
    fund_fund5 = fund_fund5.set_index('持基代码').loc[
        list(set(fund_fund5['持基代码'].tolist()) & set(fund_fund_list))].reset_index()
    fund_fund = pd.merge(fund_fund, fund_fund5, on='持基代码', how='left')


    # %% 导出基金持债明细fund_bond、bond_info
    #导出持债明细
    sql="""
    SELECT S_INFO_WINDCODE AS 基金代码,S_INFO_BONDWINDCODE AS 债券代码,F_PRT_BDVALUE AS 持仓市值,
    F_PRT_BDVALUETONAV AS 债券占比
    FROM winddf.ChinaMutualFundBondPortfolio
    WHERE F_PRT_ENDDATE = {0}
    """.format(date_i)
    fund_bond=pd.read_sql_query(sql, oracle_conn)
    fund_fund_bond=fund_bond.set_index('基金代码').loc[list(set(fund_bond['基金代码'].tolist())&set(fund_fund_list))].reset_index()
    fund_fund_bond.rename(columns={'基金代码': '持基代码'}, inplace=True)
    fund_fund_bond=pd.merge(fund_fund[['基金代码','持基代码','持基占比']], fund_fund_bond, on=['持基代码'],how='left')
    # 调整fund_bond的债券市值
    fund_fund_bond['持仓市值'] = fund_fund_bond['持仓市值'] * fund_fund_bond['持基占比'] / 100
    fund_bond=fund_bond.set_index('基金代码').loc[list(set(fund_bond['基金代码'].tolist())&set(fund_list))].reset_index()

    if fund_bond.empty:
        if fund_fund_bond.empty:
            print("FOF和FOF持有基金没有债券持仓")
        else:
            fund_bond=fund_fund_bond
            print("FOF没有债券持仓")
    else:
        if fund_fund_bond.empty:
            print("FOF持有基金没有债券持仓")
        else:
            fund_bond=pd.concat([fund_fund_bond,fund_bond])

    #导出债券简称、债券类型、债券久期、转股溢价率、纯债溢价率
    if not fund_bond.empty:
        sql="""
        SELECT S_INFO_WINDCODE AS 债券代码, S_INFO_NAME AS 债券简称
        FROM winddf.CBondDescription
         """
        bond_info1=pd.read_sql_query(sql, oracle_conn)
        bond_info1=bond_info1.set_index('债券代码').loc[list(set(bond_info1['债券代码'].tolist())&set(fund_bond['债券代码'].unique()))]
        sql="""
        SELECT S_INFO_WINDCODE AS 债券代码, S_INFO_INDUSTRYNAME AS 债券类型
        FROM winddf.CBondIndustryWind
        """
        bond_info2=pd.read_sql_query(sql, oracle_conn)
        bond_info2=bond_info2.set_index('债券代码').loc[list(set(bond_info2['债券代码'].tolist())&set(fund_bond['债券代码'].unique()))]
        sql="""
        SELECT S_INFO_WINDCODE AS 债券代码, B_ANAL_DURATION AS 债券久期
        FROM winddf.CBondValuation
        WHERE TRADE_DT = {0}
        """.format(w.tdaysoffset(0, date_i, "").Data[0][0].strftime('%Y%m%d'))
        bond_info3=pd.read_sql_query(sql, oracle_conn)
        bond_info3=bond_info3.set_index('债券代码').loc[list(set(bond_info3['债券代码'].tolist())&set(fund_bond['债券代码'].unique()))]

        sql="""
        SELECT S_INFO_WINDCODE AS 债券代码, CB_ANAL_CONVPREMIUMRATIO AS 转股溢价率, CB_ANAL_STRBPREMIUMRATIO AS 纯债溢价率
        FROM winddf.CCBondValuation
        WHERE TRADE_DT = {0}
        """.format(w.tdaysoffset(0, date_i, "").Data[0][0].strftime('%Y%m%d'))
        bond_info4=pd.read_sql_query(sql, oracle_conn)
        bond_info4=bond_info4.set_index('债券代码').loc[list(set(bond_info4['债券代码'].tolist())&set(fund_bond['债券代码'].unique()))]

        bond_info=pd.concat([bond_info1,bond_info2,bond_info3,bond_info4],axis=1,join='outer')
        fund_bond=fund_bond.set_index(['债券代码']).join(bond_info).reset_index().set_index(['基金代码','债券代码']).sort_index()
    else:
        bond_info=pd.DataFrame()
    # %% 导出持股明细fund_stock、stock_info

    #导出持有股票的基本信息
    sql="""
    SELECT S_INFO_WINDCODE AS 基金代码,S_INFO_STOCKWINDCODE AS 股票代码,F_PRT_STKVALUE AS 持仓市值,
    F_PRT_STKVALUETONAV AS 持基个股占比
    FROM winddf.ChinaMutualFundStockPortfolio
    WHERE F_PRT_ENDDATE = {0}
    """.format(date_i)
    fund_stock=pd.read_sql_query(sql, oracle_conn)
    fund_fund_stock=fund_stock.set_index('基金代码').loc[list(set(fund_stock['基金代码'].tolist())&set(fund_fund_list))].reset_index()
    fund_fund_stock.rename(columns={'基金代码': '持基代码'}, inplace=True)
    fund_fund_stock = pd.merge(fund_fund[['基金代码','持基代码','持基占比']], fund_fund_stock, on=['持基代码'], how='left')
    fund_fund_stock['持仓市值'] = fund_fund_stock['持仓市值'] * fund_fund_stock['持基占比'] / 100
    fund_stock=fund_stock.set_index('基金代码').loc[list(set(fund_stock['基金代码'].tolist())&set(fund_list))].reset_index()
    # fund_stock = pd.merge(fund_stock, fund_fund[['基金代码','持基代码','持基占比']], on='基金代码', how='left')
    if fund_stock.empty:
        if fund_fund_stock.empty:
            print("FOF和FOF持有的基金没有股票持仓")
        else:
            fund_stock = fund_fund_stock
            print("FOF没有股票持仓")
    else:
        if fund_fund_stock.empty:
            print("FOF持有的基金没有股票持仓")
        else:
            fund_stock = pd.concat([fund_fund_stock, fund_stock])
    fund_stock=fund_stock[~fund_stock['股票代码'].isnull()]
    #导出股票简称
    if not fund_stock.empty:
        sql="""
        SELECT S_INFO_WINDCODE AS 股票代码, S_INFO_NAME AS 股票简称
        FROM winddf.AShareDescription
        """
        stock_info1=pd.read_sql_query(sql, oracle_conn)
        stock_info1=stock_info1.set_index('股票代码').loc[list(set(stock_info1['股票代码'].tolist())&set(fund_stock['股票代码'].unique()))]#A股

        sql="""
        SELECT S_INFO_WINDCODE AS 股票代码, S_INFO_NAME AS 股票简称
        FROM winddf.HKShareDescription
        """
        stock_info2=pd.read_sql_query(sql, oracle_conn)
        stock_info2=stock_info2.set_index('股票代码').loc[list(set(stock_info2['股票代码'].tolist())&set(fund_stock['股票代码'].unique()))]#港股
        stock_info3=pd.concat((stock_info1,stock_info2))
        fund_stock=fund_stock.set_index(['股票代码']).join(stock_info3).reset_index()

    #建立板块和申万一级行业对应关系
        ind_grow = ['电子','通信','计算机','传媒','国防军工','电力设备','环保']
        ind_bio = ['医药生物']
        ind_fin = ['银行','房地产','非银金融']
        ind_consume = ['轻工制造','纺织服饰','社会服务','商贸零售','美容护理','家用电器','食品饮料','农林牧渔']
        ind_up = ['煤炭','石油石化','基础化工','钢铁','有色金属','建筑材料']
        ind_mid = ['交通运输','机械设备','汽车']
        others = ['建筑装饰','公用事业','综合']
        new = ['未披露']#尚未上市，新股，未披露
        indAll = [ind_grow,ind_bio,ind_fin,ind_consume,ind_up,ind_mid,others,new]
        indSecName = ['成长','医药','金融','消费','上游','中游','稳定','未披露']
        sector=dict()
        for i in range(len(indAll)):
            sector.update(dict(zip(indAll[i],[indSecName[i]]*len(indAll[i]))))
    #导出股票季度收益率
        stock_info=pd.DataFrame(fund_stock['股票代码'].unique(),columns=['股票代码']).dropna().set_index('股票代码')
        fund_label.rename(columns={'持基简称': '持股简称', '持基成立日期': '持股成立日期', '持基Wind二级分类': '持股Wind二级分类'}, inplace=True)
        stock_info=stock_info.join(fund_label)
        next_date = pd.Timestamp(date_i) + pd.tseries.offsets.QuarterEnd(startingMonth=3)
        if today > next_date:
            next_date = next_date.strftime('%Y%m%d')
            stock_info1 = pd.DataFrame({'股票代码': fund_stock['股票代码'].unique(), '下季持股收益率':
                w.wss(list(fund_stock['股票代码'].unique()), "pct_chg_per", "startDate=" + w.tdaysoffset(1, date_i, "").Data[0][0].strftime(
                    '%Y%m%d') + ";endDate=" + next_date).Data[0]}).set_index('股票代码')
            stock_info = stock_info.join(stock_info1)
        else:
            stock_info['下季持股收益率']=0

        #导出个股风格
        data=w.wss(','.join(fund_stock['股票代码'].unique().astype(str)), "compindex2", tradeDate=trade_date, index=3)#index=3是沪深300，6是中证500，7是中证1000。
        stock_info=stock_info.join(pd.DataFrame(np.array(data.Data).T,index=data.Codes,columns=['沪深300']))
        data=w.wss(','.join(fund_stock['股票代码'].unique().astype(str)), "compindex2", tradeDate=trade_date, index=6)
        stock_info=stock_info.join(pd.DataFrame(np.array(data.Data).T,index=data.Codes,columns=['中证500']))
        data=w.wss(','.join(fund_stock['股票代码'].unique().astype(str)), "compindex2", tradeDate=trade_date, index=7)
        stock_info=stock_info.join(pd.DataFrame(np.array(data.Data).T,index=data.Codes,columns=['中证1000']))
        data = w.wss(','.join(fund_stock['股票代码'].unique().astype(str)), "compindex3", windCode3="932000.CSI", tradeDate=trade_date)
        stock_info = stock_info.join(pd.DataFrame(np.array(data.Data).T, index=data.Codes, columns=['中证2000']))
        stock_info['风格指数']=""
        stock_info['风格指数'][stock_info['沪深300']=='是']='沪深300'
        stock_info['风格指数'][stock_info['中证500']=='是']='中证500'
        stock_info['风格指数'][stock_info['中证1000']=='是']='中证1000'
        stock_info['风格指数'][stock_info['中证2000'] == '是'] = '中证2000'
        stock_info['风格指数'][stock_info.index.str.contains('.HK')] = '港股'
        stock_info['风格指数'][stock_info.index.str.contains('.BJ')] = '北交所'
        stock_info['风格指数'][stock_info['风格指数']==""]='其他'
        stock_info.drop(['沪深300','中证500','中证1000','中证2000'],axis=1,inplace=True)
    #导出个股行业
        data=w.wss(','.join(fund_stock['股票代码'].unique().astype(str)), "industry_sw_2021", tradeDate=trade_date, industryType=1)
        stock_info=stock_info.join(pd.DataFrame(np.array(data.Data).T,index=data.Codes,columns=['申万一级行业']).fillna('未披露'))
        data=w.wss(','.join(fund_stock['股票代码'].unique().astype(str)), "industry_sw_2021", tradeDate=trade_date, industryType=2)
        stock_info=stock_info.join(pd.DataFrame(np.array(data.Data).T,index=data.Codes,columns=['申万二级行业']).fillna('未披露'))
    #个股板块
        stock_info['板块'] = [sector[i] if pd.notnull(i) else '未知' for i in stock_info['申万一级行业']]
    #导出个股市值、估值
        data=w.wss(','.join(fund_stock['股票代码'].unique().astype(str)),"mkt_freeshares,val_pe_deducted_ttm,estpeg_FTM",tradeDate=trade_date,unit=1,Currency='CNY')
        stock_info=stock_info.join(pd.DataFrame(np.array(data.Data).T,index=data.Codes,columns=['自由流通市值','PE_TTM','PEG']))
        stock_info[['自由流通市值','PE_TTM','PEG']]=stock_info[['自由流通市值','PE_TTM','PEG']].astype('float')
        stock_info.index.name='股票代码'
        fund_stock=fund_stock.set_index(['股票代码']).join(stock_info).reset_index().set_index(['基金代码','股票代码'])
    #根据fund_stock得到各个风格或行业的穿透持仓占比，更新fund_info
        column=['风格指数','申万一级行业','申万二级行业','板块','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)']
        for column_i in column:
            for type_i in fund_stock[column_i].unique():
                fund_info=fund_info.join(
                (fund_stock[['持仓市值']][fund_stock[column_i]==type_i].groupby(['基金代码']).sum()).div(
                    fund_stock['持仓市值'].groupby(['基金代码']).sum(),axis='index'
                ).rename(columns={'持仓市值':'穿透{0}_{1}占比'.format(column_i,type_i)}).fillna(0)
                )
    #不穿透的持仓占比
        fund_direct_stock = fund_stock[fund_stock['持基代码'].isnull()].reset_index()
        fund_temp=pd.concat([fund_fund[['基金代码','持基代码','持基市值','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)']].rename(columns={'持基市值':'持仓市值'}),fund_direct_stock[['基金代码','股票代码','持仓市值','资产类别(I)','资产地区(II)','资产类属(III)','资产板块(IV)','资产细分(V)','管理方式(VI)']]],axis=0).set_index('基金代码')
        column = ['资产类别(I)', '资产地区(II)', '资产类属(III)', '资产板块(IV)', '资产细分(V)', '管理方式(VI)']
        for column_i in column:
            for type_i in fund_temp[column_i].unique():
                fund_info = fund_info.join(
                    (fund_temp[['持仓市值']][fund_temp[column_i] == type_i].groupby(['基金代码']).sum()).div(
                        fund_temp['持仓市值'].groupby(['基金代码']).sum(), axis='index'
                    ).rename(columns={'持仓市值': '{0}_{1}占比'.format(column_i, type_i)}).fillna(0)
                )

    #根据fund_stock得到市值和估值中位数，更新fund_info
        fund_info=fund_info.join(
        fund_stock[['自由流通市值', 'PE_TTM','PEG']].groupby(['基金代码']).median()
        )
    else:
        stock_info=pd.DataFrame()

    #增加持基数量
    fund_info = fund_info.join(pd.DataFrame(fund_fund.groupby(['基金代码'])['持基代码'].nunique()).rename(columns={'持基代码':'持基数量'}))


    fund_info['基金占比'] = fund_info['基金投资市值'] / fund_info['基金资产净值'] * 100
    fund_direct_stock['下季持股收益率']=fund_direct_stock['下季持股收益率']*fund_direct_stock['持基个股占比']/100
    fund_info = fund_info.join(fund_direct_stock[['基金代码','下季持股收益率']].fillna(0).groupby(['基金代码']).sum())
    temp=fund_fund[['基金代码', '下季持基收益率','持基占比', '持基股票占比','持基转债占比']]
    temp['下季持基收益率']=temp['下季持基收益率']*temp['持基占比']/100
    # 根据fund_fund得到穿透后的股票、转债占比等，更新fund_info
    temp['持基股票占比'] = temp['持基股票占比'] * temp['持基占比'] / 100
    temp['持基转债占比'] = temp['持基转债占比'] * temp['持基占比'] / 100
    fund_info = fund_info.join(temp.groupby(['基金代码']).sum())
    fund_info['合计股票占比'] = fund_info['股票占比'] + fund_info['持基股票占比']
    fund_info['合计转债占比'] = fund_info['可转债占比'] + fund_info['持基转债占比']
    fund_info['调仓收益']=fund_info['下季收益率']-fund_info['下季持基收益率']-fund_info['下季持基收益率']

    # 根据fund_fund得到内部持基占比等，更新fund_info
    temp=fund_fund[['基金代码', '是否内部','持基占比']]
    fund_info=fund_info.join((temp[temp['是否内部']=='内部'].groupby(['基金代码'])[['持基占比']].sum()).div(temp.groupby(['基金代码'])['持基占比'].sum(), axis='index').rename(columns={'持基占比': '内部持基规模占比'}).fillna(0)*100)
    fund_info=fund_info.join((temp[temp['是否内部']=='内部'].groupby(['基金代码'])[['持基占比']].count()).div(temp.groupby(['基金代码'])['持基占比'].count(), axis='index').rename(columns={'持基占比': '内部持基数量占比'}).fillna(0)*100)
    fund_info['内外部'] = fund_info['内部持基规模占比'].apply(lambda x: '内部' if x > 60 else '外部')

    # %%数据的写入
    #写入pickle
    fund={
        '基金列表':fund_df,
        '基金信息':fund_info,
        '基金持股明细':fund_stock,
        '基金持债明细':fund_bond,
        '基金持基明细': fund_fund
        }

    try:
        with open('基金池/FOF数据.pkl', "rb") as fp:
            fund_all = pickle.load(fp)
        fund_all.update({date_i:fund})
    except:#第一次跑没这个文件
        fund_all=dict()
        fund_all.update({date_i:fund})

    with open('基金池/FOF数据.pkl', "wb") as fp:
        pickle.dump(fund_all, fp)

    print('{0}数据导出已完成'.format(date_i))
