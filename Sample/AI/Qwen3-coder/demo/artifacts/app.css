.left_header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.right_panel {
  margin-top: 66px;
  border: 1px solid #BFBFC4;
  border-radius: 8px;
  overflow: hidden;;
}

.render_header {
  height: 30px;
  width: 100%;
  padding: 5px 16px;
  background-color: #f5f5f5;
}

.header_btn {
  display: inline-block;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  margin-right: 4px;
}

.render_header > .header_btn:nth-child(1) {
  background-color: #f5222d;
}

.render_header > .header_btn:nth-child(2) {
  background-color: #faad14;
}
.render_header > .header_btn:nth-child(3) {
  background-color: #52c41a;
}

.right_content {
  height: 920px; 
  display: flex; 
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.history_chatbot button {
  background: none;
  border: none;
}

.html_content {
  width: 100%;
  height: 920px;
}