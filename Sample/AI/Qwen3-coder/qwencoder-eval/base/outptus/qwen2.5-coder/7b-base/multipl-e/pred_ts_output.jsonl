{"task_id": "HumanEval_23_strlen", "generation": "//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string: string): number {\n\n  return string.length;\n}\n", "prompt": "//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string: string): number {\n", "wholecode": "//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string: string): number {\n  return string.length;\n}\n\n//Return a string with all characters in reverse order\n// >>> reverse(\"abc\")\n// \"cba\"\n// >>> reverse(\"a\")\n// \"a\"\n// >>> reverse(\"\")\n// \"\"\nfunction reverse(string: string): string {\n  return string.split(\"\").reverse().join(\"\");\n}\n\n//Return a string with all characters in upper case\n// >>> upper(\"abc\")\n// \"ABC\"\n// >>> upper(\"a\")\n// \"A\"\n// >>> upper(\"\")\n// \"\"\nfunction upper(string: string): string {\n  return string.toUpperCase();\n}\n\n//Return a string with all characters in lower case\n// >>> lower(\"ABC\")\n// \"abc\"\n// >>> lower(\"A\")\n// \"a\"\n// >>> lower(\"\")\n// \"\"\nfunction lower(string: string): string {\n  return string.toLowerCase();\n}\n\n//Return a string with all characters in title case\n// >>> title(\"abc\")\n// \"Abc\"\n// >>> title(\"a\")\n// \"A\"\n// >>> title(\"\")\n// \"\"\nfunction title(string: string): string {\n  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();\n}\n\n//Return a string with all characters in camel case\n// >>> camel(\"abc\")\n// \"abc\"\n// >>> camel(\"a\")\n// \"a\"\n// >>> camel(\"\")\n// \"\"\nfunction camel(string: string): string {\n  return string.charAt(0).toLowerCase() + string.slice(1).toLowerCase();\n}\n\n//Return a string with all characters in snake case\n// >>> snake(\"abc\")\n// \"abc\"\n// >>> snake(\"a\")\n// \"a\"\n// >>> snake(\"\")\n// \"\"\nfunction snake(string: string): string {\n  return string.replace(/ /g, \"_\");\n}\n\n//Return a string with all characters in kebab case\n// >>> kebab(\"abc\")\n// \"abc\"\n// >>> kebab(\"a\")\n// \"a\"\n// >>> kebab(\"\")\n// \"\"\nfunction kebab(string: string): string {\n  return string.replace(/ /g, \"-\");\n}\n\n//Return a string with all characters in pascal case\n// >>> pascal(\"abc\")\n// \"Abc\"\n// >>> pascal(\"a\")\n// \"A\"\n// >>> pascal(\"\")\n// \"\"\nfunction pascal(string: string): string {\n  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();\n}\n\n//Return a string with all characters in sentence case\n// >>> sentence(\"abc\")\n// \"Abc\"\n// >>>"}
{"task_id": "HumanEval_89_encrypt", "generation": "//Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunction encrypt(s: string): string {\n\n    let alphabet: string = \"abcdefghijklmnopqrstuvwxyz\";\n    let encrypted: string = \"\";\n    for (let i: number = 0; i < s.length; i++) {\n        let index: number = alphabet.indexOf(s[i]);\n        encrypted += alphabet[(index + 4) % 26];\n    }\n    return encrypted;\n}", "prompt": "//Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunction encrypt(s: string): string {\n", "wholecode": "//Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunction encrypt(s: string): string {\n    let alphabet: string = \"abcdefghijklmnopqrstuvwxyz\";\n    let encrypted: string = \"\";\n    for (let i: number = 0; i < s.length; i++) {\n        let index: number = alphabet.indexOf(s[i]);\n        encrypted += alphabet[(index + 4) % 26];\n    }\n    return encrypted;\n}\nconsole.log(encrypt(\"hi\"));\nconsole.log(encrypt(\"asdfghjkl\"));\nconsole.log(encrypt(\"gf\"));\nconsole.log(encrypt(\"et\"));"}
{"task_id": "HumanEval_95_check_dict_case", "generation": "//Given an object, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given object is empty.\n// Examples:\n// >>> check_dict_case({\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case({\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case({\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case({\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case({\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunction check_dict_case(dict: {[key: string]: string}): boolean {\n\n    if (Object.keys(dict).length === 0) {\n        return false;\n    }\n    const keys = Object.keys(dict);\n    const allLower = keys.every(key => key === key.toLowerCase());\n    const allUpper = keys.every(key => key === key.toUpperCase());\n    return allLower || allUpper;\n}", "prompt": "//Given an object, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given object is empty.\n// Examples:\n// >>> check_dict_case({\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case({\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case({\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case({\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case({\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunction check_dict_case(dict: {[key: string]: string}): boolean {\n", "wholecode": "//Given an object, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given object is empty.\n// Examples:\n// >>> check_dict_case({\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case({\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case({\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case({\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case({\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunction check_dict_case(dict: {[key: string]: string}): boolean {\n    if (Object.keys(dict).length === 0) {\n        return false;\n    }\n    const keys = Object.keys(dict);\n    const allLower = keys.every(key => key === key.toLowerCase());\n    const allUpper = keys.every(key => key === key.toUpperCase());\n    return allLower || allUpper;\n}"}
{"task_id": "HumanEval_85_add", "generation": "//Given a non-empty array of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([4, 2, 6, 7])\n// 2\nfunction add(lst: number[]): number {\n\n  let sum = 0;\n  for (let i = 0; i < lst.length; i++) {\n    if (i % 2 !== 0 && lst[i] % 2 === 0) {\n      sum += lst[i];\n    }\n  }\n  return sum;\n}\n", "prompt": "//Given a non-empty array of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([4, 2, 6, 7])\n// 2\nfunction add(lst: number[]): number {\n", "wholecode": "//Given a non-empty array of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([4, 2, 6, 7])\n// 2\nfunction add(lst: number[]): number {\n  let sum = 0;\n  for (let i = 0; i < lst.length; i++) {\n    if (i % 2 !== 0 && lst[i] % 2 === 0) {\n      sum += lst[i];\n    }\n  }\n  return sum;\n}\n\nconsole.log(add([4, 2, 6, 7]));"}
{"task_id": "HumanEval_140_fix_spaces", "generation": "//Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunction fix_spaces(text: string): string {\n\n    let result = text.replace(/ /g, \"_\");\n    result = result.replace(/_{2,}/g, \"-\");\n    return result;\n}\n", "prompt": "//Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunction fix_spaces(text: string): string {\n", "wholecode": "//Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunction fix_spaces(text: string): string {\n    let result = text.replace(/ /g, \"_\");\n    result = result.replace(/_{2,}/g, \"-\");\n    return result;\n}\n\nconsole.log(fix_spaces(\" Example\"));\nconsole.log(fix_spaces(\" Example 1\"));\nconsole.log(fix_spaces(\" Example 2\"));\nconsole.log(fix_spaces(\" Example 3\"));"}
{"task_id": "HumanEval_63_fibfib", "generation": "//The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunction fibfib(n: number): number {\n\n  if (n === 0 || n === 1) return 0;\n  if (n === 2) return 1;\n  return fibfib(n - 1) + fibfib(n - 2) + fibfib(n - 3);\n}\n", "prompt": "//The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunction fibfib(n: number): number {\n", "wholecode": "//The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunction fibfib(n: number): number {\n  if (n === 0 || n === 1) return 0;\n  if (n === 2) return 1;\n  return fibfib(n - 1) + fibfib(n - 2) + fibfib(n - 3);\n}\n\nconsole.log(fibfib(1));\nconsole.log(fibfib(5));\nconsole.log(fibfib(8));"}
{"task_id": "HumanEval_151_double_the_difference", "generation": "//Given an array of numbers, return the sum of squares of the numbers\n// in the array that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([1, 3, 2, 0])\n// 10\n// >>> double_the_difference([-1, -2, 0])\n// 0\n// >>> double_the_difference([9, -2])\n// 81\n// >>> double_the_difference([0])\n// 0\n// If the input array is empty, return 0.\nfunction double_the_difference(lst: number[]): number {\n\n  let sum = 0;\n  for (let i = 0; i < lst.length; i++) {\n    if (lst[i] > 0 && lst[i] % 2 !== 0) {\n      sum += lst[i] * lst[i];\n    }\n  }\n  return sum;\n}\n", "prompt": "//Given an array of numbers, return the sum of squares of the numbers\n// in the array that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([1, 3, 2, 0])\n// 10\n// >>> double_the_difference([-1, -2, 0])\n// 0\n// >>> double_the_difference([9, -2])\n// 81\n// >>> double_the_difference([0])\n// 0\n// If the input array is empty, return 0.\nfunction double_the_difference(lst: number[]): number {\n", "wholecode": "//Given an array of numbers, return the sum of squares of the numbers\n// in the array that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([1, 3, 2, 0])\n// 10\n// >>> double_the_difference([-1, -2, 0])\n// 0\n// >>> double_the_difference([9, -2])\n// 81\n// >>> double_the_difference([0])\n// 0\n// If the input array is empty, return 0.\nfunction double_the_difference(lst: number[]): number {\n  let sum = 0;\n  for (let i = 0; i < lst.length; i++) {\n    if (lst[i] > 0 && lst[i] % 2 !== 0) {\n      sum += lst[i] * lst[i];\n    }\n  }\n  return sum;\n}\n\nconsole.log(double_the_difference([1, 3, 2, 0]));\nconsole.log(double_the_difference([-1, -2, 0]));\nconsole.log(double_the_difference([9, -2]));\nconsole.log(double_the_difference([0]));"}
{"task_id": "HumanEval_22_filter_integers", "generation": "//Filter given array of any tsthon values only for integers\n// >>> filter_integers([\"a\", 3.14, 5])\n// [5]\n// >>> filter_integers([1, 2, 3, \"abc\", {}, []])\n// [1, 2, 3]\nfunction filter_integers(values: any[]): number[] {\n\n    return values.filter((value) => typeof value === \"number\" && Number.isInteger(value));\n}\n", "prompt": "//Filter given array of any tsthon values only for integers\n// >>> filter_integers([\"a\", 3.14, 5])\n// [5]\n// >>> filter_integers([1, 2, 3, \"abc\", {}, []])\n// [1, 2, 3]\nfunction filter_integers(values: any[]): number[] {\n", "wholecode": "//Filter given array of any tsthon values only for integers\n// >>> filter_integers([\"a\", 3.14, 5])\n// [5]\n// >>> filter_integers([1, 2, 3, \"abc\", {}, []])\n// [1, 2, 3]\nfunction filter_integers(values: any[]): number[] {\n    return values.filter((value) => typeof value === \"number\" && Number.isInteger(value));\n}\n\n//Filter given array of any tsthon values only for strings\n// >>> filter_strings([\"a\", 3.14, 5])\n// [\"a\"]\n// >>> filter_strings([1, 2, 3, \"abc\", {}, []])\n// [\"abc\"]\nfunction filter_strings(values: any[]): string[] {\n    return values.filter((value) => typeof value === \"string\");\n}\n\n//Filter given array of any tsthon values only for objects\n// >>> filter_objects([\"a\", 3.14, 5])\n// []\n// >>> filter_objects([1, 2, 3, \"abc\", {}, []])\n// [{}]\nfunction filter_objects(values: any[]): object[] {\n    return values.filter((value) => typeof value === \"object\" && value !== null && !Array.isArray(value));\n}\n\n//Filter given array of any tsthon values only for arrays\n// >>> filter_arrays([\"a\", 3.14, 5])\n// []\n// >>> filter_arrays([1, 2, 3, \"abc\", {}, []])\n// [[]]\nfunction filter_arrays(values: any[]): any[] {\n    return values.filter((value) => Array.isArray(value));\n}\n\n//Filter given array of any tsthon values only for booleans\n// >>> filter_booleans([\"a\", 3.14, 5])\n// []\n// >>> filter_booleans([1, 2, 3, \"abc\", {}, []])\n// []\nfunction filter_booleans(values: any[]): boolean[] {\n    return values.filter((value) => typeof value === \"boolean\");\n}\n\n//Filter given array of any tsthon values only for nulls\n// >>> filter_nulls([\"a\", 3.14, 5])\n// []\n// >>> filter_nulls([1, 2, 3, \"abc\", {}, []])\n// []\nfunction filter_nulls(values: any[]): null[] {\n    return values.filter((value) => value === null);\n}\n\n//Filter given array of any tsthon values only for undefineds\n// >>> filter_undefineds([\"a\", 3.14, 5])\n// []\n// >>> filter_undefineds([1, 2, 3, \"abc\", {}, []])\n// []\nfunction filter_undefineds(values: any[]): undefined"}
{"task_id": "HumanEval_41_car_race_collision", "generation": "//Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunction car_race_collision(n: number): number {\n\n  return n * n;\n}", "prompt": "//Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunction car_race_collision(n: number): number {\n", "wholecode": "//Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunction car_race_collision(n: number): number {\n  return n * n;\n}"}
{"task_id": "HumanEval_17_parse_music", "generation": "//Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return array of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunction parse_music(music_string: string): number[] {\n\n    const notes: string[] = music_string.split(\" \");\n    const beats: number[] = [];\n    for (const note of notes) {\n        if (note === \"o\") {\n            beats.push(4);\n        } else if (note === \"o|\") {\n            beats.push(2);\n        } else if (note === \".|\") {\n            beats.push(1);\n        }\n    }\n    return beats;\n}", "prompt": "//Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return array of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunction parse_music(music_string: string): number[] {\n", "wholecode": "//Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return array of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunction parse_music(music_string: string): number[] {\n    const notes: string[] = music_string.split(\" \");\n    const beats: number[] = [];\n    for (const note of notes) {\n        if (note === \"o\") {\n            beats.push(4);\n        } else if (note === \"o|\") {\n            beats.push(2);\n        } else if (note === \".|\") {\n            beats.push(1);\n        }\n    }\n    return beats;\n}"}
{"task_id": "HumanEval_79_decimal_to_binary", "generation": "//You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunction decimal_to_binary(decimal: number): string {\n\n    let binary: string = \"db\";\n    while (decimal > 0) {\n        binary = (decimal % 2) + binary;\n        decimal = Math.floor(decimal / 2);\n    }\n    return binary + \"db\";\n}", "prompt": "//You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunction decimal_to_binary(decimal: number): string {\n", "wholecode": "//You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunction decimal_to_binary(decimal: number): string {\n    let binary: string = \"db\";\n    while (decimal > 0) {\n        binary = (decimal % 2) + binary;\n        decimal = Math.floor(decimal / 2);\n    }\n    return binary + \"db\";\n}"}
{"task_id": "HumanEval_14_all_prefixes", "generation": "//Return array of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// [\"a\", \"ab\", \"abc\"]\nfunction all_prefixes(string: string): string[] {\n\n  let prefixes: string[] = [];\n  for (let i = 0; i < string.length; i++) {\n    prefixes.push(string.slice(0, i + 1));\n  }\n  return prefixes;\n}\n", "prompt": "//Return array of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// [\"a\", \"ab\", \"abc\"]\nfunction all_prefixes(string: string): string[] {\n", "wholecode": "//Return array of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// [\"a\", \"ab\", \"abc\"]\nfunction all_prefixes(string: string): string[] {\n  let prefixes: string[] = [];\n  for (let i = 0; i < string.length; i++) {\n    prefixes.push(string.slice(0, i + 1));\n  }\n  return prefixes;\n}\n\n//Return array of all suffixes from longest to shortest of the input string\n// >>> all_suffixes(\"abc\")\n// [\"abc\", \"bc\", \"c\"]\nfunction all_suffixes(string: string): string[] {\n  let suffixes: string[] = [];\n  for (let i = string.length; i > 0; i--) {\n    suffixes.push(string.slice(i - 1));\n  }\n  return suffixes;\n}\n\n//Return array of all substrings of the input string\n// >>> all_substrings(\"abc\")\n// [\"a\", \"ab\", \"abc\", \"b\", \"bc\", \"c\"]\nfunction all_substrings(string: string): string[] {\n  let substrings: string[] = [];\n  for (let i = 0; i < string.length; i++) {\n    for (let j = i + 1; j <= string.length; j++) {\n      substrings.push(string.slice(i, j));\n    }\n  }\n  return substrings;\n}\n\n//Return array of all substrings of the input string that are palindromes\n// >>> all_palindromes(\"abc\")\n// [\"a\", \"b\", \"c\"]\nfunction all_palindromes(string: string): string[] {\n  let palindromes: string[] = [];\n  for (let i = 0; i < string.length; i++) {\n    for (let j = i + 1; j <= string.length; j++) {\n      let substring = string.slice(i, j);\n      if (substring === substring.split(\"\").reverse().join(\"\")) {\n        palindromes.push(substring);\n      }\n    }\n  }\n  return palindromes;\n}\n\n//Return array of all substrings of the input string that are palindromes\n// >>> all_palindromes(\"abc\")\n// [\"a\", \"b\", \"c\"]\nfunction all_palindromes(string: string): string[] {\n  let palindromes: string[] = [];\n  for (let i = 0; i < string.length; i++) {\n    for (let j = i + 1; j <= string.length; j++) {\n      let substring = string.slice(i,"}
{"task_id": "HumanEval_53_add", "generation": "//Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunction add(x: number, y: number): number {\n\n    return x + y;\n}\n", "prompt": "//Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunction add(x: number, y: number): number {\n", "wholecode": "//Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunction add(x: number, y: number): number {\n    return x + y;\n}\n\n//Multiply two numbers x and y\n// >>> multiply(2, 3)\n// 6\n// >>> multiply(5, 7)\n// 35\nfunction multiply(x: number, y: number): number {\n    return x * y;\n}\n\n//Divide two numbers x and y\n// >>> divide(10, 2)\n// 5\n// >>> divide(10, 0)\n// Error: Cannot divide by zero\nfunction divide(x: number, y: number): number {\n    if (y === 0) {\n        throw new Error(\"Cannot divide by zero\");\n    }\n    return x / y;\n}\n\n//Subtract two numbers x and y\n// >>> subtract(10, 2)\n// 8\n// >>> subtract(5, 7)\n// -2\nfunction subtract(x: number, y: number): number {\n    return x - y;\n}\n\n//Calculate the power of a number x raised to the power of y\n// >>> power(2, 3)\n// 8\n// >>> power(5, 2)\n// 25\nfunction power(x: number, y: number): number {\n    return Math.pow(x, y);\n}\n\n//Calculate the square root of a number x\n// >>> squareRoot(9)\n// 3\n// >>> squareRoot(16)\n// 4\nfunction squareRoot(x: number): number {\n    return Math.sqrt(x);\n}\n\n//Calculate the factorial of a number x\n// >>> factorial(5)\n// 120\n// >>> factorial(0)\n// 1\nfunction factorial(x: number): number {\n    if (x === 0) {\n        return 1;\n    }\n    return x * factorial(x - 1);\n}\n\n//Calculate the absolute value of a number x\n// >>> absoluteValue(5)\n// 5\n// >>> absoluteValue(-5)\n// 5\nfunction absoluteValue(x: number): number {\n    return Math.abs(x);\n}\n\n//Calculate the logarithm of a number x with base y\n// >>> logarithm(10, 10)\n// 1\n// >>> logarithm(100, 10)\n// 2\nfunction logarithm(x: number, y: number): number {\n    return Math.log(x) / Math.log(y);\n}\n\n//Calculate the sine of an"}
{"task_id": "HumanEval_159_eat", "generation": "//You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return an array of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// [11, 4]\n// >>> eat(4, 8, 9)\n// [12, 1]\n// >>> eat(1, 10, 10)\n// [11, 0]\n// >>> eat(2, 11, 5)\n// [7, 0]\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunction eat(number: number, need: number, remaining: number): number[] {\n\n  const totalEaten = number + need;\n  const carrotsLeft = remaining - need;\n\n  if (carrotsLeft < 0) {\n    return [totalEaten, 0];\n  } else {\n    return [totalEaten, carrotsLeft];\n  }\n}", "prompt": "//You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return an array of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// [11, 4]\n// >>> eat(4, 8, 9)\n// [12, 1]\n// >>> eat(1, 10, 10)\n// [11, 0]\n// >>> eat(2, 11, 5)\n// [7, 0]\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunction eat(number: number, need: number, remaining: number): number[] {\n", "wholecode": "//You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return an array of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// [11, 4]\n// >>> eat(4, 8, 9)\n// [12, 1]\n// >>> eat(1, 10, 10)\n// [11, 0]\n// >>> eat(2, 11, 5)\n// [7, 0]\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunction eat(number: number, need: number, remaining: number): number[] {\n  const totalEaten = number + need;\n  const carrotsLeft = remaining - need;\n\n  if (carrotsLeft < 0) {\n    return [totalEaten, 0];\n  } else {\n    return [totalEaten, carrotsLeft];\n  }\n}"}
{"task_id": "HumanEval_115_max_fill", "generation": "//You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n// 6\n// Example 2:\n// >>> max_fill([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n// 5\n// Example 3:\n// >>> max_fill([[0, 0, 0], [0, 0, 0]], 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunction max_fill(grid: number[][], capacity: number): number {\n\n    let count = 0;\n    for (let i = 0; i < grid.length; i++) {\n        for (let j = 0; j < grid[i].length; j++) {\n            if (grid[i][j] === 1) {\n                count++;\n            }\n        }\n    }\n    return Math.ceil(count / capacity);\n}", "prompt": "//You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n// 6\n// Example 2:\n// >>> max_fill([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n// 5\n// Example 3:\n// >>> max_fill([[0, 0, 0], [0, 0, 0]], 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunction max_fill(grid: number[][], capacity: number): number {\n", "wholecode": "//You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n// 6\n// Example 2:\n// >>> max_fill([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n// 5\n// Example 3:\n// >>> max_fill([[0, 0, 0], [0, 0, 0]], 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunction max_fill(grid: number[][], capacity: number): number {\n    let count = 0;\n    for (let i = 0; i < grid.length; i++) {\n        for (let j = 0; j < grid[i].length; j++) {\n            if (grid[i][j] === 1) {\n                count++;\n            }\n        }\n    }\n    return Math.ceil(count / capacity);\n}"}
{"task_id": "HumanEval_160_do_algebra", "generation": "//Given two arrays operator, and operand. The first array has basic algebra operations, and \n// the second array is an array of integers. Use the two given arrays to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// array = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator array is equal to the length of operand array minus one.\n// Operand is an array of of non-negative integers.\n// Operator array has at least one operator, and operand array has at least two operands.\nfunction do_algebra(operator: string[], operand: number[]): number {\n\n    let result = operand[0];\n    for (let i = 0; i < operator.length; i++) {\n        switch (operator[i]) {\n            case '+':\n                result += operand[i + 1];\n                break;\n            case '-':\n                result -= operand[i + 1];\n                break;\n            case '*':\n                result *= operand[i + 1];\n                break;\n            case '/':\n                result /= operand[i + 1];\n                break;\n            case '**':\n                result **= operand[i + 1];\n                break;\n        }\n    }\n    return result;\n}", "prompt": "//Given two arrays operator, and operand. The first array has basic algebra operations, and \n// the second array is an array of integers. Use the two given arrays to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// array = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator array is equal to the length of operand array minus one.\n// Operand is an array of of non-negative integers.\n// Operator array has at least one operator, and operand array has at least two operands.\nfunction do_algebra(operator: string[], operand: number[]): number {\n", "wholecode": "//Given two arrays operator, and operand. The first array has basic algebra operations, and \n// the second array is an array of integers. Use the two given arrays to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// array = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator array is equal to the length of operand array minus one.\n// Operand is an array of of non-negative integers.\n// Operator array has at least one operator, and operand array has at least two operands.\nfunction do_algebra(operator: string[], operand: number[]): number {\n    let result = operand[0];\n    for (let i = 0; i < operator.length; i++) {\n        switch (operator[i]) {\n            case '+':\n                result += operand[i + 1];\n                break;\n            case '-':\n                result -= operand[i + 1];\n                break;\n            case '*':\n                result *= operand[i + 1];\n                break;\n            case '/':\n                result /= operand[i + 1];\n                break;\n            case '**':\n                result **= operand[i + 1];\n                break;\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_27_flip_case", "generation": "//For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunction flip_case(string: string): string {\n\n    let newString = \"\";\n    for (let i = 0; i < string.length; i++) {\n        if (string[i] === string[i].toUpperCase()) {\n            newString += string[i].toLowerCase();\n        } else {\n            newString += string[i].toUpperCase();\n        }\n    }\n    return newString;\n}\n", "prompt": "//For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunction flip_case(string: string): string {\n", "wholecode": "//For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunction flip_case(string: string): string {\n    let newString = \"\";\n    for (let i = 0; i < string.length; i++) {\n        if (string[i] === string[i].toUpperCase()) {\n            newString += string[i].toLowerCase();\n        } else {\n            newString += string[i].toUpperCase();\n        }\n    }\n    return newString;\n}\n\nconsole.log(flip_case(\"Hello\"));"}
{"task_id": "HumanEval_105_by_length", "generation": "//Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting array, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([2, 1, 1, 4, 5, 8, 2, 3])\n// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n// If the array is empty, return an empty array:\n// >>> by_length([])\n// []\n// If the array has any strange number ignore it:\n// >>> by_length([1, -1, 55])\n// [\"One\"]\nfunction by_length(arr: number[]): string[] {\n\n    const names = [\"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\"];\n    const result: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        if (arr[i] >= 1 && arr[i] <= 9) {\n            result.push(names[arr[i] - 1]);\n        }\n    }\n    return result.reverse();\n}\n", "prompt": "//Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting array, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([2, 1, 1, 4, 5, 8, 2, 3])\n// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n// If the array is empty, return an empty array:\n// >>> by_length([])\n// []\n// If the array has any strange number ignore it:\n// >>> by_length([1, -1, 55])\n// [\"One\"]\nfunction by_length(arr: number[]): string[] {\n", "wholecode": "//Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting array, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([2, 1, 1, 4, 5, 8, 2, 3])\n// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n// If the array is empty, return an empty array:\n// >>> by_length([])\n// []\n// If the array has any strange number ignore it:\n// >>> by_length([1, -1, 55])\n// [\"One\"]\nfunction by_length(arr: number[]): string[] {\n    const names = [\"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\"];\n    const result: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        if (arr[i] >= 1 && arr[i] <= 9) {\n            result.push(names[arr[i] - 1]);\n        }\n    }\n    return result.reverse();\n}\n\nconsole.log(by_length([2, 1, 1, 4, 5, 8, 2, 3]));\nconsole.log(by_length([]));\nconsole.log(by_length([1, -1, 55]));"}
{"task_id": "HumanEval_25_factorize", "generation": "//Return array of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// [2, 2, 2]\n// >>> factorize(25)\n// [5, 5]\n// >>> factorize(70)\n// [2, 5, 7]\nfunction factorize(n: number): number[] {\n\n  let factors: number[] = [];\n  let i = 2;\n  while (n > 1) {\n    if (n % i === 0) {\n      factors.push(i);\n      n /= i;\n    } else {\n      i++;\n    }\n  }\n  return factors;\n}\n", "prompt": "//Return array of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// [2, 2, 2]\n// >>> factorize(25)\n// [5, 5]\n// >>> factorize(70)\n// [2, 5, 7]\nfunction factorize(n: number): number[] {\n", "wholecode": "//Return array of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// [2, 2, 2]\n// >>> factorize(25)\n// [5, 5]\n// >>> factorize(70)\n// [2, 5, 7]\nfunction factorize(n: number): number[] {\n  let factors: number[] = [];\n  let i = 2;\n  while (n > 1) {\n    if (n % i === 0) {\n      factors.push(i);\n      n /= i;\n    } else {\n      i++;\n    }\n  }\n  return factors;\n}\n\nconsole.log(factorize(8));\nconsole.log(factorize(25));\nconsole.log(factorize(70));"}
{"task_id": "HumanEval_96_count_up_to", "generation": "//Implement a function that takes an non-negative integer and returns an array of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// [2, 3]\n// >>> count_up_to(11)\n// [2, 3, 5, 7]\n// >>> count_up_to(0)\n// []\n// >>> count_up_to(20)\n// [2, 3, 5, 7, 11, 13, 17, 19]\n// >>> count_up_to(1)\n// []\n// >>> count_up_to(18)\n// [2, 3, 5, 7, 11, 13, 17]\nfunction count_up_to(n: number): number[] {\n\n  let primes: number[] = [];\n  for (let i = 2; i < n; i++) {\n    if (isPrime(i)) {\n      primes.push(i);\n    }\n  }\n  return primes;\n}\n", "prompt": "//Implement a function that takes an non-negative integer and returns an array of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// [2, 3]\n// >>> count_up_to(11)\n// [2, 3, 5, 7]\n// >>> count_up_to(0)\n// []\n// >>> count_up_to(20)\n// [2, 3, 5, 7, 11, 13, 17, 19]\n// >>> count_up_to(1)\n// []\n// >>> count_up_to(18)\n// [2, 3, 5, 7, 11, 13, 17]\nfunction count_up_to(n: number): number[] {\n", "wholecode": "//Implement a function that takes an non-negative integer and returns an array of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// [2, 3]\n// >>> count_up_to(11)\n// [2, 3, 5, 7]\n// >>> count_up_to(0)\n// []\n// >>> count_up_to(20)\n// [2, 3, 5, 7, 11, 13, 17, 19]\n// >>> count_up_to(1)\n// []\n// >>> count_up_to(18)\n// [2, 3, 5, 7, 11, 13, 17]\nfunction count_up_to(n: number): number[] {\n  let primes: number[] = [];\n  for (let i = 2; i < n; i++) {\n    if (isPrime(i)) {\n      primes.push(i);\n    }\n  }\n  return primes;\n}\n\nfunction isPrime(n: number): boolean {\n  for (let i = 2; i < n; i++) {\n    if (n % i === 0) {\n      return false;\n    }\n  }\n  return true;\n}\n\nconsole.log(count_up_to(5));\nconsole.log(count_up_to(11));\nconsole.log(count_up_to(0));\nconsole.log(count_up_to(20));\nconsole.log(count_up_to(1));\nconsole.log(count_up_to(18));"}
{"task_id": "HumanEval_34_unique", "generation": "//Return sorted unique elements in an array\n// >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [0, 2, 3, 5, 9, 123]\nfunction unique(l: number[]): number[] {\n\n  return [...new Set(l)].sort((a, b) => a - b);\n}\n", "prompt": "//Return sorted unique elements in an array\n// >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [0, 2, 3, 5, 9, 123]\nfunction unique(l: number[]): number[] {\n", "wholecode": "//Return sorted unique elements in an array\n// >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [0, 2, 3, 5, 9, 123]\nfunction unique(l: number[]): number[] {\n  return [...new Set(l)].sort((a, b) => a - b);\n}\n\n//Return the first element of an array\n// >>> first([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// 5\nfunction first(l: number[]): number {\n  return l[0];\n}\n\n//Return the last element of an array\n// >>> last([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// 123\nfunction last(l: number[]): number {\n  return l[l.length - 1];\n}\n\n//Return the nth element of an array\n// >>> nth([5, 3, 5, 2, 3, 3, 9, 0, 123], 3)\n// 2\nfunction nth(l: number[], n: number): number {\n  return l[n];\n}\n\n//Return the sum of all elements in an array\n// >>> sum([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// 180\nfunction sum(l: number[]): number {\n  return l.reduce((a, b) => a + b, 0);\n}\n\n//Return the product of all elements in an array\n// >>> product([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// 0\nfunction product(l: number[]): number {\n  return l.reduce((a, b) => a * b, 1);\n}\n\n//Return the average of all elements in an array\n// >>> average([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// 18\nfunction average(l: number[]): number {\n  return sum(l) / l.length;\n}\n\n//Return the median of all elements in an array\n// >>> median([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// 3\nfunction median(l: number[]): number {\n  const sorted = l.sort((a, b) => a - b);\n  const middle = Math"}
{"task_id": "HumanEval_74_total_match", "generation": "//Write a function that accepts two arrays of strings and returns the array that has \n// total number of chars in the all strings of the array less than the other array.\n// if the two arrays have the same number of chars, return the first array.\n// Examples\n// >>> total_match([], [])\n// []\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n// [\"hI\", \"Hi\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n// [\"hi\", \"admin\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n// [\"hI\", \"hi\", \"hi\"]\n// >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n// [\"4\"]\nfunction total_match(lst1: string[], lst2: string[]): string[] {\n\n    let total1: number = 0;\n    let total2: number = 0;\n    for (let i = 0; i < lst1.length; i++) {\n        total1 += lst1[i].length;\n    }\n    for (let i = 0; i < lst2.length; i++) {\n        total2 += lst2[i].length;\n    }\n    if (total1 < total2) {\n        return lst1;\n    } else {\n        return lst2;\n    }\n}", "prompt": "//Write a function that accepts two arrays of strings and returns the array that has \n// total number of chars in the all strings of the array less than the other array.\n// if the two arrays have the same number of chars, return the first array.\n// Examples\n// >>> total_match([], [])\n// []\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n// [\"hI\", \"Hi\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n// [\"hi\", \"admin\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n// [\"hI\", \"hi\", \"hi\"]\n// >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n// [\"4\"]\nfunction total_match(lst1: string[], lst2: string[]): string[] {\n", "wholecode": "//Write a function that accepts two arrays of strings and returns the array that has \n// total number of chars in the all strings of the array less than the other array.\n// if the two arrays have the same number of chars, return the first array.\n// Examples\n// >>> total_match([], [])\n// []\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n// [\"hI\", \"Hi\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n// [\"hi\", \"admin\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n// [\"hI\", \"hi\", \"hi\"]\n// >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n// [\"4\"]\nfunction total_match(lst1: string[], lst2: string[]): string[] {\n    let total1: number = 0;\n    let total2: number = 0;\n    for (let i = 0; i < lst1.length; i++) {\n        total1 += lst1[i].length;\n    }\n    for (let i = 0; i < lst2.length; i++) {\n        total2 += lst2[i].length;\n    }\n    if (total1 < total2) {\n        return lst1;\n    } else {\n        return lst2;\n    }\n}"}
{"task_id": "HumanEval_35_max_element", "generation": "//Return maximum element in the array.\n// >>> max_element([1, 2, 3])\n// 3\n// >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 123\nfunction max_element(l: number[]): number {\n\n    let max = l[0];\n    for (let i = 1; i < l.length; i++) {\n        if (l[i] > max) {\n            max = l[i];\n        }\n    }\n    return max;\n}\n", "prompt": "//Return maximum element in the array.\n// >>> max_element([1, 2, 3])\n// 3\n// >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 123\nfunction max_element(l: number[]): number {\n", "wholecode": "//Return maximum element in the array.\n// >>> max_element([1, 2, 3])\n// 3\n// >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 123\nfunction max_element(l: number[]): number {\n    let max = l[0];\n    for (let i = 1; i < l.length; i++) {\n        if (l[i] > max) {\n            max = l[i];\n        }\n    }\n    return max;\n}\n\n//Return minimum element in the array.\n// >>> min_element([1, 2, 3])\n// 1\n// >>> min_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// -10\nfunction min_element(l: number[]): number {\n    let min = l[0];\n    for (let i = 1; i < l.length; i++) {\n        if (l[i] < min) {\n            min = l[i];\n        }\n    }\n    return min;\n}\n\n//Return the sum of all elements in the array.\n// >>> sum([1, 2, 3])\n// 6\n// >>> sum([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 130\nfunction sum(l: number[]): number {\n    let sum = 0;\n    for (let i = 0; i < l.length; i++) {\n        sum += l[i];\n    }\n    return sum;\n}\n\n//Return the product of all elements in the array.\n// >>> product([1, 2, 3])\n// 6\n// >>> product([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 0\nfunction product(l: number[]): number {\n    let product = 1;\n    for (let i = 0; i < l.length; i++) {\n        product *= l[i];\n    }\n    return product;\n}\n\n//Return the average of all elements in the array.\n// >>> average([1, 2, 3])\n// 2\n// >>> average([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 7.2727272727272725\nfunction average"}
{"task_id": "HumanEval_132_is_nested", "generation": "//Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunction is_nested(string: string): boolean {\n\n    let count = 0;\n    for (let i = 0; i < string.length; i++) {\n        if (string[i] === '[') {\n            count++;\n        } else if (string[i] === ']') {\n            count--;\n        }\n        if (count < 0) {\n            return false;\n        }\n    }\n    return count === 0;\n}", "prompt": "//Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunction is_nested(string: string): boolean {\n", "wholecode": "//Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunction is_nested(string: string): boolean {\n    let count = 0;\n    for (let i = 0; i < string.length; i++) {\n        if (string[i] === '[') {\n            count++;\n        } else if (string[i] === ']') {\n            count--;\n        }\n        if (count < 0) {\n            return false;\n        }\n    }\n    return count === 0;\n}"}
{"task_id": "HumanEval_103_rounded_avg", "generation": "//You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg(1, 5)\n// \"0b11\"\n// >>> rounded_avg(7, 5)\n// -1\n// >>> rounded_avg(10, 20)\n// \"0b1111\"\n// >>> rounded_avg(20, 33)\n// \"0b11010\"\nfunction rounded_avg(n: number, m: number): string| number {\n\n    if (n > m) {\n        return -1;\n    }\n    let sum = 0;\n    for (let i = n; i <= m; i++) {\n        sum += i;\n    }\n    let avg = Math.round(sum / (m - n + 1));\n    return \"0b\" + avg.toString(2);\n}", "prompt": "//You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg(1, 5)\n// \"0b11\"\n// >>> rounded_avg(7, 5)\n// -1\n// >>> rounded_avg(10, 20)\n// \"0b1111\"\n// >>> rounded_avg(20, 33)\n// \"0b11010\"\nfunction rounded_avg(n: number, m: number): string| number {\n", "wholecode": "//You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg(1, 5)\n// \"0b11\"\n// >>> rounded_avg(7, 5)\n// -1\n// >>> rounded_avg(10, 20)\n// \"0b1111\"\n// >>> rounded_avg(20, 33)\n// \"0b11010\"\nfunction rounded_avg(n: number, m: number): string| number {\n    if (n > m) {\n        return -1;\n    }\n    let sum = 0;\n    for (let i = n; i <= m; i++) {\n        sum += i;\n    }\n    let avg = Math.round(sum / (m - n + 1));\n    return \"0b\" + avg.toString(2);\n}"}
{"task_id": "HumanEval_113_odd_count", "generation": "//Given an array of strings, where each string consists of only digits, return an array.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([\"1234567\"])\n// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n// >>> odd_count([\"3\", \"11111111\"])\n// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunction odd_count(lst: string[]): string[] {\n\n    let result: string[] = [];\n    for (let i = 0; i < lst.length; i++) {\n        let count = 0;\n        for (let j = 0; j < lst[i].length; j++) {\n            if (lst[i][j] % 2 == 1) {\n                count++;\n            }\n        }\n        result.push(\"the number of odd elements \" + count + \"n the str\" + count + \"ng \" + count + \" of the \" + count + \"nput.\");\n    }\n    return result;\n}", "prompt": "//Given an array of strings, where each string consists of only digits, return an array.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([\"1234567\"])\n// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n// >>> odd_count([\"3\", \"11111111\"])\n// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunction odd_count(lst: string[]): string[] {\n", "wholecode": "//Given an array of strings, where each string consists of only digits, return an array.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([\"1234567\"])\n// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n// >>> odd_count([\"3\", \"11111111\"])\n// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunction odd_count(lst: string[]): string[] {\n    let result: string[] = [];\n    for (let i = 0; i < lst.length; i++) {\n        let count = 0;\n        for (let j = 0; j < lst[i].length; j++) {\n            if (lst[i][j] % 2 == 1) {\n                count++;\n            }\n        }\n        result.push(\"the number of odd elements \" + count + \"n the str\" + count + \"ng \" + count + \" of the \" + count + \"nput.\");\n    }\n    return result;\n}\nconsole.log(odd_count([\"1234567\"]));\nconsole.log(odd_count([\"3\", \"11111111\"]));"}
{"task_id": "HumanEval_109_move_one_ball", "generation": "//We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the array will be randomly ordered. Your task is to determine if\n// it is possible to get an array sorted in non-decreasing order by performing \n// the following operation on the given array:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the array by one\n// position in the right direction. The last element of the array will be moved to\n// the starting position in the array i.e. 0th index. \n// If it is possible to obtain the sorted array by performing the above operation\n// then return true else return false.\n// If the given array is empty then return true.\n// Note: The given array is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([3, 4, 5, 1, 2])\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given array.\n// >>> move_one_ball([3, 5, 4, 1, 2])\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// array by performing any number of right shift operations.\nfunction move_one_ball(arr: number[]): boolean {\n\n    if (arr.length === 0) {\n        return true;\n    }\n    let count = 0;\n    for (let i = 1; i < arr.length; i++) {\n        if (arr[i] < arr[i - 1]) {\n            count++;\n        }\n    }\n    if (arr[arr.length - 1] > arr[0]) {\n        count++;\n    }\n    return count <= 1;\n}", "prompt": "//We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the array will be randomly ordered. Your task is to determine if\n// it is possible to get an array sorted in non-decreasing order by performing \n// the following operation on the given array:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the array by one\n// position in the right direction. The last element of the array will be moved to\n// the starting position in the array i.e. 0th index. \n// If it is possible to obtain the sorted array by performing the above operation\n// then return true else return false.\n// If the given array is empty then return true.\n// Note: The given array is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([3, 4, 5, 1, 2])\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given array.\n// >>> move_one_ball([3, 5, 4, 1, 2])\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// array by performing any number of right shift operations.\nfunction move_one_ball(arr: number[]): boolean {\n", "wholecode": "//We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the array will be randomly ordered. Your task is to determine if\n// it is possible to get an array sorted in non-decreasing order by performing \n// the following operation on the given array:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the array by one\n// position in the right direction. The last element of the array will be moved to\n// the starting position in the array i.e. 0th index. \n// If it is possible to obtain the sorted array by performing the above operation\n// then return true else return false.\n// If the given array is empty then return true.\n// Note: The given array is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([3, 4, 5, 1, 2])\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given array.\n// >>> move_one_ball([3, 5, 4, 1, 2])\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// array by performing any number of right shift operations.\nfunction move_one_ball(arr: number[]): boolean {\n    if (arr.length === 0) {\n        return true;\n    }\n    let count = 0;\n    for (let i = 1; i < arr.length; i++) {\n        if (arr[i] < arr[i - 1]) {\n            count++;\n        }\n    }\n    if (arr[arr.length - 1] > arr[0]) {\n        count++;\n    }\n    return count <= 1;\n}"}
{"task_id": "HumanEval_107_even_odd_palindrome", "generation": "//Given a positive integer n, return an array that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// [1, 2]\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// [4, 6]\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned array has the number of even and odd integer palindromes respectively.\nfunction even_odd_palindrome(n: number): [number, number] {\n\n  let even = 0;\n  let odd = 0;\n  for (let i = 1; i <= n; i++) {\n    if (isPalindrome(i)) {\n      if (i % 2 === 0) {\n        even++;\n      } else {\n        odd++;\n      }\n    }\n  }\n  return [even, odd];\n}\n", "prompt": "//Given a positive integer n, return an array that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// [1, 2]\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// [4, 6]\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned array has the number of even and odd integer palindromes respectively.\nfunction even_odd_palindrome(n: number): [number, number] {\n", "wholecode": "//Given a positive integer n, return an array that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// [1, 2]\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// [4, 6]\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned array has the number of even and odd integer palindromes respectively.\nfunction even_odd_palindrome(n: number): [number, number] {\n  let even = 0;\n  let odd = 0;\n  for (let i = 1; i <= n; i++) {\n    if (isPalindrome(i)) {\n      if (i % 2 === 0) {\n        even++;\n      } else {\n        odd++;\n      }\n    }\n  }\n  return [even, odd];\n}\n\nfunction isPalindrome(n: number): boolean {\n  const str = n.toString();\n  let left = 0;\n  let right = str.length - 1;\n  while (left < right) {\n    if (str[left] !== str[right]) {\n      return false;\n    }\n    left++;\n    right--;\n  }\n  return true;\n}"}
{"task_id": "HumanEval_138_is_equal_to_sum_even", "generation": "//Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunction is_equal_to_sum_even(n: number): boolean {\n\n    if (n % 2 !== 0) {\n        return false;\n    }\n    if (n < 8) {\n        return false;\n    }\n    return true;\n}\n", "prompt": "//Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunction is_equal_to_sum_even(n: number): boolean {\n", "wholecode": "//Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunction is_equal_to_sum_even(n: number): boolean {\n    if (n % 2 !== 0) {\n        return false;\n    }\n    if (n < 8) {\n        return false;\n    }\n    return true;\n}\n\nconsole.log(is_equal_to_sum_even(4));\nconsole.log(is_equal_to_sum_even(6));\nconsole.log(is_equal_to_sum_even(8));"}
{"task_id": "HumanEval_62_derivative", "generation": "//xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([3, 1, 2, 4, 5])\n// [1, 4, 12, 20]\n// >>> derivative([1, 2, 3])\n// [2, 6]\nfunction derivative(xs: number[]): number[] {\n\n  return xs.map((x, i) => x * i).slice(1);\n}", "prompt": "//xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([3, 1, 2, 4, 5])\n// [1, 4, 12, 20]\n// >>> derivative([1, 2, 3])\n// [2, 6]\nfunction derivative(xs: number[]): number[] {\n", "wholecode": "//xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([3, 1, 2, 4, 5])\n// [1, 4, 12, 20]\n// >>> derivative([1, 2, 3])\n// [2, 6]\nfunction derivative(xs: number[]): number[] {\n  return xs.map((x, i) => x * i).slice(1);\n}"}
{"task_id": "HumanEval_126_is_sorted", "generation": "//Given an array of numbers, return whether or not they are sorted\n// in ascending order. If array has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([5])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5])\n// false\n// >>> is_sorted([1, 2, 3, 4, 5, 6])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5, 6, 7])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5, 6, 7])\n// false\n// >>> is_sorted([1, 2, 2, 3, 3, 4])\n// true\n// >>> is_sorted([1, 2, 2, 2, 3, 4])\n// false\nfunction is_sorted(lst: number[]): boolean {\n\n  for (let i = 0; i < lst.length - 1; i++) {\n    if (lst[i] > lst[i + 1]) {\n      return false;\n    }\n  }\n  return true;\n}\n", "prompt": "//Given an array of numbers, return whether or not they are sorted\n// in ascending order. If array has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([5])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5])\n// false\n// >>> is_sorted([1, 2, 3, 4, 5, 6])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5, 6, 7])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5, 6, 7])\n// false\n// >>> is_sorted([1, 2, 2, 3, 3, 4])\n// true\n// >>> is_sorted([1, 2, 2, 2, 3, 4])\n// false\nfunction is_sorted(lst: number[]): boolean {\n", "wholecode": "//Given an array of numbers, return whether or not they are sorted\n// in ascending order. If array has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([5])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5])\n// false\n// >>> is_sorted([1, 2, 3, 4, 5, 6])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5, 6, 7])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5, 6, 7])\n// false\n// >>> is_sorted([1, 2, 2, 3, 3, 4])\n// true\n// >>> is_sorted([1, 2, 2, 2, 3, 4])\n// false\nfunction is_sorted(lst: number[]): boolean {\n  for (let i = 0; i < lst.length - 1; i++) {\n    if (lst[i] > lst[i + 1]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nconsole.log(is_sorted([5]));\nconsole.log(is_sorted([1, 2, 3, 4, 5]));\nconsole.log(is_sorted([1, 3, 2, 4, 5]));\nconsole.log(is_sorted([1, 2, 3, 4, 5, 6]));\nconsole.log(is_sorted([1, 2, 3, 4, 5, 6, 7]));\nconsole.log(is_sorted([1, 3, 2, 4, 5, 6, 7]));\nconsole.log(is_sorted([1, 2, 2, 3, 3, 4]));\nconsole.log(is_sorted([1, 2, 2, 2, 3, 4]));"}
{"task_id": "HumanEval_161_solve", "generation": "//You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunction solve(s: string): string {\n\n    let result = '';\n    let hasLetters = false;\n    for (let i = 0; i < s.length; i++) {\n      const char = s[i];\n      if (/[a-zA-Z]/.test(char)) {\n        hasLetters = true;\n        result += char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase();\n      } else {\n        result += char;\n      }\n    }\n    return hasLetters ? result : result.split('').reverse().join('');\n  }", "prompt": "//You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunction solve(s: string): string {\n", "wholecode": "//You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunction solve(s: string): string {\n    let result = '';\n    let hasLetters = false;\n    for (let i = 0; i < s.length; i++) {\n      const char = s[i];\n      if (/[a-zA-Z]/.test(char)) {\n        hasLetters = true;\n        result += char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase();\n      } else {\n        result += char;\n      }\n    }\n    return hasLetters ? result : result.split('').reverse().join('');\n  }"}
{"task_id": "HumanEval_130_tri", "generation": "//Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return an array of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// [1, 3, 2, 8]\nfunction tri(n: number): number[] {\n\n    let arr: number[] = [1, 3];\n    for (let i = 2; i <= n; i++) {\n        if (i % 2 === 0) {\n            arr.push(1 + i / 2);\n        } else {\n            arr.push(arr[i - 1] + arr[i - 2] + arr[i + 1]);\n        }\n    }\n    return arr;\n}", "prompt": "//Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return an array of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// [1, 3, 2, 8]\nfunction tri(n: number): number[] {\n", "wholecode": "//Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return an array of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// [1, 3, 2, 8]\nfunction tri(n: number): number[] {\n    let arr: number[] = [1, 3];\n    for (let i = 2; i <= n; i++) {\n        if (i % 2 === 0) {\n            arr.push(1 + i / 2);\n        } else {\n            arr.push(arr[i - 1] + arr[i - 2] + arr[i + 1]);\n        }\n    }\n    return arr;\n}"}
{"task_id": "HumanEval_36_fizz_buzz", "generation": "//Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunction fizz_buzz(n: number): number {\n\n    let count = 0;\n    for (let i = 1; i < n; i++) {\n        if (i % 11 === 0 || i % 13 === 0) {\n            count += i.toString().split('').filter(digit => digit === '7').length;\n        }\n    }\n    return count;\n}\n", "prompt": "//Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunction fizz_buzz(n: number): number {\n", "wholecode": "//Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunction fizz_buzz(n: number): number {\n    let count = 0;\n    for (let i = 1; i < n; i++) {\n        if (i % 11 === 0 || i % 13 === 0) {\n            count += i.toString().split('').filter(digit => digit === '7').length;\n        }\n    }\n    return count;\n}\n\nconsole.log(fizz_buzz(50));\nconsole.log(fizz_buzz(78));\nconsole.log(fizz_buzz(79));"}
{"task_id": "HumanEval_29_filter_by_prefix", "generation": "//Filter an input array of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([], \"a\")\n// []\n// >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"array\"]\nfunction filter_by_prefix(strings: string[], prefix: string): string[] {\n\n    return strings.filter((string) => string.startsWith(prefix));\n}\n", "prompt": "//Filter an input array of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([], \"a\")\n// []\n// >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"array\"]\nfunction filter_by_prefix(strings: string[], prefix: string): string[] {\n", "wholecode": "//Filter an input array of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([], \"a\")\n// []\n// >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"array\"]\nfunction filter_by_prefix(strings: string[], prefix: string): string[] {\n    return strings.filter((string) => string.startsWith(prefix));\n}\n\n//Find the maximum value in an array of numbers.\n// >>> find_max([1, 2, 3, 4, 5])\n// 5\n// >>> find_max([-10, -20, -30, -40, -50])\n// -10\nfunction find_max(numbers: number[]): number {\n    return Math.max(...numbers);\n}\n\n//Find the minimum value in an array of numbers.\n// >>> find_min([1, 2, 3, 4, 5])\n// 1\n// >>> find_min([-10, -20, -30, -40, -50])\n// -50\nfunction find_min(numbers: number[]): number {\n    return Math.min(...numbers);\n}\n\n//Find the sum of all numbers in an array.\n// >>> sum_array([1, 2, 3, 4, 5])\n// 15\n// >>> sum_array([-1, -2, -3, -4, -5])\n// -15\nfunction sum_array(numbers: number[]): number {\n    return numbers.reduce((acc, curr) => acc + curr, 0);\n}\n\n//Find the product of all numbers in an array.\n// >>> product_array([1, 2, 3, 4, 5])\n// 120\n// >>> product_array([-1, -2, -3, -4, -5])\n// -120\nfunction product_array(numbers: number[]): number {\n    return numbers.reduce((acc, curr) => acc * curr, 1);\n}\n\n//Find the average of all numbers in an array.\n// >>> average_array([1, 2, 3, 4, 5])\n// 3\n// >>> average_array([-1, -2, -3, -4, -5])\n// -3\nfunction average_array(numbers: number[]): number {\n    return sum_array(numbers) / numbers.length;\n}\n\n//Find the median of all numbers in an array.\n// >>> median_array([1, 2, 3, 4, 5])\n// 3\n// >>> median_array([-1, -2, -3, -4, -5])\n// -3\nfunction median_array(numbers: number[]): number {\n    const sorted = numbers.sort((a, b"}
{"task_id": "HumanEval_84_solve", "generation": "//Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 ≤ N ≤ 10000.\n// Output:\n// a string of binary number\nfunction solve(N: number): string {\n\n  let sum = 0;\n  while (N > 0) {\n    sum += N % 2;\n    N = Math.floor(N / 2);\n  }\n  return sum.toString(2);\n}", "prompt": "//Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 ≤ N ≤ 10000.\n// Output:\n// a string of binary number\nfunction solve(N: number): string {\n", "wholecode": "//Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 ≤ N ≤ 10000.\n// Output:\n// a string of binary number\nfunction solve(N: number): string {\n  let sum = 0;\n  while (N > 0) {\n    sum += N % 2;\n    N = Math.floor(N / 2);\n  }\n  return sum.toString(2);\n}"}
{"task_id": "HumanEval_129_minPath", "generation": "//Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered arrays of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered array of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n// [1, 2, 1]\n// >>> minPath([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n// [1]\nfunction minPath(grid: number[][], k: number): number[] {\n\n    const n = grid.length;\n    const dp: number[][][] = Array.from({ length: n }, () => Array.from({ length: n }, () => Array(k + 1).fill(Infinity)));\n    const directions = [[0, 1], [1, 0], [0, -1], [-1, 0]];\n    \n    for (let i = 0; i < n; i++) {\n        for (let j = 0; j < n; j++) {\n            dp[i][j][1] = grid[i][j];\n        }\n    }\n    \n    for (let len = 2; len <= k; len++) {\n        for (let i = 0; i < n; i++) {\n            for (let j = 0; j < n; j++) {\n                for (const [dx, dy] of directions) {\n                    const x = i + dx;\n                    const y = j + dy;\n                    if (x >= 0 && x < n && y >= 0 && y < n) {\n                        dp[i][j][len] = Math.min(dp[i][j][len], dp[x][y][len - 1] + grid[i][j]);\n                    }\n                }\n            }\n        }\n    }\n    \n    let minPathValue = Infinity;\n    let minPathIndex = -1;\n    for (let i = 0; i < n; i++) {\n        for (let j = 0; j < n; j++) {\n            if (dp[i][j][k] < minPathValue) {\n                minPathValue = dp[i][j][k];\n                minPathIndex = i * n + j;\n            }\n        }\n    }\n    \n    const minPath = [];\n    for (let len = k; len > 0; len--) {\n        minPath.push(grid[Math.floor(minPathIndex / n)][minPathIndex % n]);\n        for (const [dx, dy] of directions) {\n            const x = Math.floor(minPathIndex / n) + dx;\n            const y = minPathIndex % n + dy;\n            if (x >= 0 && x < n && y >= 0 && y < n && dp[x][y][len - 1] + grid[x][y] === dp[Math.floor(minPathIndex / n)][minPathIndex % n][len]) {\n               ", "prompt": "//Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered arrays of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered array of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n// [1, 2, 1]\n// >>> minPath([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n// [1]\nfunction minPath(grid: number[][], k: number): number[] {\n", "wholecode": "//Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered arrays of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered array of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n// [1, 2, 1]\n// >>> minPath([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n// [1]\nfunction minPath(grid: number[][], k: number): number[] {\n    const n = grid.length;\n    const dp: number[][][] = Array.from({ length: n }, () => Array.from({ length: n }, () => Array(k + 1).fill(Infinity)));\n    const directions = [[0, 1], [1, 0], [0, -1], [-1, 0]];\n    \n    for (let i = 0; i < n; i++) {\n        for (let j = 0; j < n; j++) {\n            dp[i][j][1] = grid[i][j];\n        }\n    }\n    \n    for (let len = 2; len <= k; len++) {\n        for (let i = 0; i < n; i++) {\n            for (let j = 0; j < n; j++) {\n                for (const [dx, dy] of directions) {\n                    const x = i + dx;\n                    const y = j + dy;\n                    if (x >= 0 && x < n && y >= 0 && y < n) {\n                        dp[i][j][len] = Math.min(dp[i][j][len], dp[x][y][len - 1] + grid[i][j]);\n                    }\n                }\n            }\n        }\n    }\n    \n    let minPathValue = Infinity;\n    let minPathIndex = -1;\n    for (let i = 0; i < n; i++) {\n        for (let j = 0; j < n; j++) {\n            if (dp[i][j][k] < minPathValue) {\n                minPathValue = dp[i][j][k];\n                minPathIndex = i * n + j;\n            }\n        }\n    }\n    \n    const minPath = [];\n    for (let len = k; len > 0; len--) {\n        minPath.push(grid[Math.floor(minPathIndex / n)][minPathIndex % n]);\n        for (const [dx, dy] of directions) {\n            const x = Math.floor(minPathIndex / n) + dx;\n            const y = minPathIndex % n + dy;\n            if (x >= 0 && x < n && y >= 0 && y < n && dp[x][y][len - 1] + grid[x][y] === dp[Math.floor(minPathIndex / n)][minPathIndex % n][len]) {\n               "}
{"task_id": "HumanEval_98_count_upper", "generation": "//Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunction count_upper(s: string): number {\n\n    let count = 0;\n    for (let i = 0; i < s.length; i += 2) {\n        if (s[i] === s[i].toUpperCase() && \"AEIOU\".includes(s[i])) {\n            count++;\n        }\n    }\n    return count;\n}", "prompt": "//Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunction count_upper(s: string): number {\n", "wholecode": "//Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunction count_upper(s: string): number {\n    let count = 0;\n    for (let i = 0; i < s.length; i += 2) {\n        if (s[i] === s[i].toUpperCase() && \"AEIOU\".includes(s[i])) {\n            count++;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_120_maximum", "generation": "//Given an array arr of integers and a positive integer k, return a sorted array \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([-3, -4, 5], 3)\n// [-4, -3, 5]\n// Example 2:\n// >>> maximum([4, -4, 4], 2)\n// [4, 4]\n// Example 3:\n// >>> maximum([-3, 2, 1, 2, -1, -2, 1], 1)\n// [2]\n// Note:\n// 1. The length of the array will be in the range of [1, 1000].\n// 2. The elements in the array will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunction maximum(arr: number[], k: number): number[] {\n\n    let sortedArr = arr.sort((a, b) => b - a);\n    return sortedArr.slice(0, k);\n}", "prompt": "//Given an array arr of integers and a positive integer k, return a sorted array \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([-3, -4, 5], 3)\n// [-4, -3, 5]\n// Example 2:\n// >>> maximum([4, -4, 4], 2)\n// [4, 4]\n// Example 3:\n// >>> maximum([-3, 2, 1, 2, -1, -2, 1], 1)\n// [2]\n// Note:\n// 1. The length of the array will be in the range of [1, 1000].\n// 2. The elements in the array will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunction maximum(arr: number[], k: number): number[] {\n", "wholecode": "//Given an array arr of integers and a positive integer k, return a sorted array \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([-3, -4, 5], 3)\n// [-4, -3, 5]\n// Example 2:\n// >>> maximum([4, -4, 4], 2)\n// [4, 4]\n// Example 3:\n// >>> maximum([-3, 2, 1, 2, -1, -2, 1], 1)\n// [2]\n// Note:\n// 1. The length of the array will be in the range of [1, 1000].\n// 2. The elements in the array will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunction maximum(arr: number[], k: number): number[] {\n    let sortedArr = arr.sort((a, b) => b - a);\n    return sortedArr.slice(0, k);\n}"}
{"task_id": "HumanEval_24_largest_divisor", "generation": "//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n: number): number {\n\n    for (let i = n - 1; i > 0; i--) {\n        if (n % i === 0) {\n            return i;\n        }\n    }\n    return 1;\n}\n", "prompt": "//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n: number): number {\n", "wholecode": "//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n: number): number {\n    for (let i = n - 1; i > 0; i--) {\n        if (n % i === 0) {\n            return i;\n        }\n    }\n    return 1;\n}\n\n// >>> largest_divisor(17)\n// 1\nconsole.log(largest_divisor(15));\nconsole.log(largest_divisor(17));"}
{"task_id": "HumanEval_88_sort_array", "generation": "//Given an array of non-negative integers, return a cots of the given array after sorting,\n// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given array.\n// Examples:\n// >>> sort_array([])\n// []\n// >>> sort_array([5])\n// [5]\n// >>> sort_array([2, 4, 3, 0, 1, 5])\n// [0, 1, 2, 3, 4, 5]\n// >>> sort_array([2, 4, 3, 0, 1, 5, 6])\n// [6, 5, 4, 3, 2, 1, 0]\nfunction sort_array(array: number[]): number[] {\n\n    if (array.length <= 1) {\n        return array;\n    }\n    const sum = array[0] + array[array.length - 1];\n    if (sum % 2 === 0) {\n        return array.sort((a, b) => b - a);\n    } else {\n        return array.sort((a, b) => a - b);\n    }\n}", "prompt": "//Given an array of non-negative integers, return a cots of the given array after sorting,\n// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given array.\n// Examples:\n// >>> sort_array([])\n// []\n// >>> sort_array([5])\n// [5]\n// >>> sort_array([2, 4, 3, 0, 1, 5])\n// [0, 1, 2, 3, 4, 5]\n// >>> sort_array([2, 4, 3, 0, 1, 5, 6])\n// [6, 5, 4, 3, 2, 1, 0]\nfunction sort_array(array: number[]): number[] {\n", "wholecode": "//Given an array of non-negative integers, return a cots of the given array after sorting,\n// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given array.\n// Examples:\n// >>> sort_array([])\n// []\n// >>> sort_array([5])\n// [5]\n// >>> sort_array([2, 4, 3, 0, 1, 5])\n// [0, 1, 2, 3, 4, 5]\n// >>> sort_array([2, 4, 3, 0, 1, 5, 6])\n// [6, 5, 4, 3, 2, 1, 0]\nfunction sort_array(array: number[]): number[] {\n    if (array.length <= 1) {\n        return array;\n    }\n    const sum = array[0] + array[array.length - 1];\n    if (sum % 2 === 0) {\n        return array.sort((a, b) => b - a);\n    } else {\n        return array.sort((a, b) => a - b);\n    }\n}"}
{"task_id": "HumanEval_106_f", "generation": "//Implement the function f that takes n as a parameter,\n// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// [1, 2, 6, 24, 15]\nfunction f(n: number): number[] {\n\n    let arr: number[] = [];\n    for (let i = 1; i <= n; i++) {\n        if (i % 2 === 0) {\n            arr.push(factorial(i));\n        } else {\n            arr.push(sum(i));\n        }\n    }\n    return arr;\n}\n", "prompt": "//Implement the function f that takes n as a parameter,\n// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// [1, 2, 6, 24, 15]\nfunction f(n: number): number[] {\n", "wholecode": "//Implement the function f that takes n as a parameter,\n// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// [1, 2, 6, 24, 15]\nfunction f(n: number): number[] {\n    let arr: number[] = [];\n    for (let i = 1; i <= n; i++) {\n        if (i % 2 === 0) {\n            arr.push(factorial(i));\n        } else {\n            arr.push(sum(i));\n        }\n    }\n    return arr;\n}\n\nfunction factorial(n: number): number {\n    let result = 1;\n    for (let i = 1; i <= n; i++) {\n        result *= i;\n    }\n    return result;\n}\n\nfunction sum(n: number): number {\n    let result = 0;\n    for (let i = 1; i <= n; i++) {\n        result += i;\n    }\n    return result;\n}\n\nconsole.log(f(5));"}
{"task_id": "HumanEval_77_iscube", "generation": "//Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunction iscube(a: number): boolean {\n\n    return Math.cbrt(a) % 1 === 0;\n}", "prompt": "//Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunction iscube(a: number): boolean {\n", "wholecode": "//Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunction iscube(a: number): boolean {\n    return Math.cbrt(a) % 1 === 0;\n}"}
{"task_id": "HumanEval_93_encode", "generation": "//Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunction encode(message: string): string {\n\n    let encodedMessage: string = \"\";\n    for (let i = 0; i < message.length; i++) {\n        let char: string = message[i];\n        if (char === char.toUpperCase()) {\n            char = char.toLowerCase();\n        } else {\n            char = char.toUpperCase();\n        }\n        if (char === \"a\" || char === \"e\" || char === \"i\" || char === \"o\" || char === \"u\") {\n            if (char === \"a\") {\n                char = \"c\";\n            } else if (char === \"e\") {\n                char = \"g\";\n            } else if (char === \"i\") {\n                char = \"k\";\n            } else if (char === \"o\") {\n                char = \"q\";\n            } else if (char === \"u\") {\n                char = \"w\";\n            }\n        }\n        encodedMessage += char;\n    }\n    return encodedMessage;\n}", "prompt": "//Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunction encode(message: string): string {\n", "wholecode": "//Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunction encode(message: string): string {\n    let encodedMessage: string = \"\";\n    for (let i = 0; i < message.length; i++) {\n        let char: string = message[i];\n        if (char === char.toUpperCase()) {\n            char = char.toLowerCase();\n        } else {\n            char = char.toUpperCase();\n        }\n        if (char === \"a\" || char === \"e\" || char === \"i\" || char === \"o\" || char === \"u\") {\n            if (char === \"a\") {\n                char = \"c\";\n            } else if (char === \"e\") {\n                char = \"g\";\n            } else if (char === \"i\") {\n                char = \"k\";\n            } else if (char === \"o\") {\n                char = \"q\";\n            } else if (char === \"u\") {\n                char = \"w\";\n            }\n        }\n        encodedMessage += char;\n    }\n    return encodedMessage;\n}"}
{"task_id": "HumanEval_91_is_bored", "generation": "//You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunction is_bored(S: string): number {\n\n  let count = 0;\n  const sentences = S.split(/[.?!]/);\n  for (const sentence of sentences) {\n    const trimmedSentence = sentence.trim();\n    if (trimmedSentence.startsWith(\"I\")) {\n      count++;\n    }\n  }\n  return count;\n}", "prompt": "//You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunction is_bored(S: string): number {\n", "wholecode": "//You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunction is_bored(S: string): number {\n  let count = 0;\n  const sentences = S.split(/[.?!]/);\n  for (const sentence of sentences) {\n    const trimmedSentence = sentence.trim();\n    if (trimmedSentence.startsWith(\"I\")) {\n      count++;\n    }\n  }\n  return count;\n}"}
{"task_id": "HumanEval_43_pairs_sum_to_zero", "generation": "//pairs_sum_to_zero takes an array of integers as an input.\n// it returns true if there are two distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> pairs_sum_to_zero([1, 3, -2, 1])\n// false\n// >>> pairs_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n// true\n// >>> pairs_sum_to_zero([1])\n// false\nfunction pairs_sum_to_zero(l: number[]): boolean {\n\n    for (let i = 0; i < l.length; i++) {\n        for (let j = i + 1; j < l.length; j++) {\n            if (l[i] + l[j] === 0) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n", "prompt": "//pairs_sum_to_zero takes an array of integers as an input.\n// it returns true if there are two distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> pairs_sum_to_zero([1, 3, -2, 1])\n// false\n// >>> pairs_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n// true\n// >>> pairs_sum_to_zero([1])\n// false\nfunction pairs_sum_to_zero(l: number[]): boolean {\n", "wholecode": "//pairs_sum_to_zero takes an array of integers as an input.\n// it returns true if there are two distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> pairs_sum_to_zero([1, 3, -2, 1])\n// false\n// >>> pairs_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n// true\n// >>> pairs_sum_to_zero([1])\n// false\nfunction pairs_sum_to_zero(l: number[]): boolean {\n    for (let i = 0; i < l.length; i++) {\n        for (let j = i + 1; j < l.length; j++) {\n            if (l[i] + l[j] === 0) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n//pairs_sum_to_k takes an array of integers and an integer k as inputs.\n// it returns true if there are two distinct elements in the array that\n// sum to k, and false otherwise.\n// >>> pairs_sum_to_k([1, 3, 5, 0], 0)\n// true\n// >>> pairs_sum_to_k([1, 3, -2, 1], 8)\n// false\n// >>> pairs_sum_to_k([1, 9, 5, 0], 10)\n// true\n// >>> pairs_sum_to_k([2, 4, -5, 3, 5, 7], 10)\n// true\n// >>> pairs_sum_to_k([1], 2)\n// false\nfunction pairs_sum_to_k(l: number[], k: number): boolean {\n    for (let i = 0; i < l.length; i++) {\n        for (let j = i + 1; j < l.length; j++) {\n            if (l[i] + l[j] === k) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n//pairs_sum_to_k2 takes an array of integers and an integer k as inputs.\n// it returns true if there are two distinct elements in the array that\n// sum to k, and false otherwise.\n// >>> pairs_sum_to_k2([1, 3, 5, 0], 0)\n// true\n// >>> pairs_sum_to_k2([1, 3, -2, 1], 8)\n// false\n// >>> pairs_sum_to_k2([1, 9, 5, 0], 10)\n// true\n// >>> pairs_sum_to_k2([2, 4, -5, 3, 5, 7], 10)\n// true\n// >>> pairs_sum_to_k2([1], 2)\n// false\nfunction pairs_sum_to_k2(l: number[], k: number): boolean {\n    let s = new Set<number>();\n    for (let"}
{"task_id": "HumanEval_71_triangle_area", "generation": "//Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunction triangle_area(a: number, b: number, c: number): number {\n\n    if (a + b > c && a + c > b && b + c > a) {\n        const s = (a + b + c) / 2;\n        const area = Math.sqrt(s * (s - a) * (s - b) * (s - c));\n        return parseFloat(area.toFixed(2));\n    } else {\n        return -1;\n    }\n}", "prompt": "//Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunction triangle_area(a: number, b: number, c: number): number {\n", "wholecode": "//Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunction triangle_area(a: number, b: number, c: number): number {\n    if (a + b > c && a + c > b && b + c > a) {\n        const s = (a + b + c) / 2;\n        const area = Math.sqrt(s * (s - a) * (s - b) * (s - c));\n        return parseFloat(area.toFixed(2));\n    } else {\n        return -1;\n    }\n}"}
{"task_id": "HumanEval_131_digits", "generation": "//Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunction digits(n: number): number {\n\n    let result = 1;\n    let str = n.toString();\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] % 2 !== 0) {\n            result *= Number(str[i]);\n        }\n    }\n    return result;\n}", "prompt": "//Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunction digits(n: number): number {\n", "wholecode": "//Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunction digits(n: number): number {\n    let result = 1;\n    let str = n.toString();\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] % 2 !== 0) {\n            result *= Number(str[i]);\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_101_words_string", "generation": "//You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return an array of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n// >>> words_string(\"One, two, three, four, five, six\")\n// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunction words_string(s: string): string[] {\n\n    return s.split(/[\\s,]+/);\n}", "prompt": "//You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return an array of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n// >>> words_string(\"One, two, three, four, five, six\")\n// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunction words_string(s: string): string[] {\n", "wholecode": "//You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return an array of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n// >>> words_string(\"One, two, three, four, five, six\")\n// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunction words_string(s: string): string[] {\n    return s.split(/[\\s,]+/);\n}"}
{"task_id": "HumanEval_18_how_many_times", "generation": "//Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunction how_many_times(string: string, substring: string): number {\n\n  let count = 0;\n  let index = 0;\n  while (index !== -1) {\n    index = string.indexOf(substring, index);\n    if (index !== -1) {\n      count++;\n      index += substring.length;\n    }\n  }\n  return count;\n}\n", "prompt": "//Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunction how_many_times(string: string, substring: string): number {\n", "wholecode": "//Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunction how_many_times(string: string, substring: string): number {\n  let count = 0;\n  let index = 0;\n  while (index !== -1) {\n    index = string.indexOf(substring, index);\n    if (index !== -1) {\n      count++;\n      index += substring.length;\n    }\n  }\n  return count;\n}\n\nconsole.log(how_many_times(\"aaa\", \"a\"));"}
{"task_id": "HumanEval_51_remove_vowels", "generation": "//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text: string): string {\n\n    let vowels = ['a', 'e', 'i', 'o', 'u', 'A', 'E', 'I', 'O', 'U'];\n    let result = '';\n    for (let i = 0; i < text.length; i++) {\n        if (!vowels.includes(text[i])) {\n            result += text[i];\n        }\n    }\n    return result;\n}\n", "prompt": "//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text: string): string {\n", "wholecode": "//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text: string): string {\n    let vowels = ['a', 'e', 'i', 'o', 'u', 'A', 'E', 'I', 'O', 'U'];\n    let result = '';\n    for (let i = 0; i < text.length; i++) {\n        if (!vowels.includes(text[i])) {\n            result += text[i];\n        }\n    }\n    return result;\n}\n\n//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text: string): string {\n    let vowels = ['a', 'e', 'i', 'o', 'u', 'A', 'E', 'I', 'O', 'U'];\n    let result = '';\n    for (let i = 0; i < text.length; i++) {\n        if (!vowels.includes(text[i])) {\n            result += text[i];\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_70_strange_sort_list", "generation": "//Given array of integers, return array in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([1, 2, 3, 4])\n// [1, 4, 2, 3]\n// >>> strange_sort_list([5, 5, 5, 5])\n// [5, 5, 5, 5]\n// >>> strange_sort_list([])\n// []\nfunction strange_sort_list(lst: number[]): number[] {\n\n    let result: number[] = [];\n    let min: number = Math.min(...lst);\n    let max: number = Math.max(...lst);\n    while (lst.length > 0) {\n        result.push(min);\n        lst.splice(lst.indexOf(min), 1);\n        if (lst.length > 0) {\n            result.push(max);\n            lst.splice(lst.indexOf(max), 1);\n        }\n        min = Math.min(...lst);\n        max = Math.max(...lst);\n    }\n    return result;\n}", "prompt": "//Given array of integers, return array in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([1, 2, 3, 4])\n// [1, 4, 2, 3]\n// >>> strange_sort_list([5, 5, 5, 5])\n// [5, 5, 5, 5]\n// >>> strange_sort_list([])\n// []\nfunction strange_sort_list(lst: number[]): number[] {\n", "wholecode": "//Given array of integers, return array in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([1, 2, 3, 4])\n// [1, 4, 2, 3]\n// >>> strange_sort_list([5, 5, 5, 5])\n// [5, 5, 5, 5]\n// >>> strange_sort_list([])\n// []\nfunction strange_sort_list(lst: number[]): number[] {\n    let result: number[] = [];\n    let min: number = Math.min(...lst);\n    let max: number = Math.max(...lst);\n    while (lst.length > 0) {\n        result.push(min);\n        lst.splice(lst.indexOf(min), 1);\n        if (lst.length > 0) {\n            result.push(max);\n            lst.splice(lst.indexOf(max), 1);\n        }\n        min = Math.min(...lst);\n        max = Math.max(...lst);\n    }\n    return result;\n}"}
{"task_id": "HumanEval_20_find_closest_elements", "generation": "//From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n// [2.0, 2.2]\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n// [2.0, 2.0]\nfunction find_closest_elements(numbers: number[]): [number, number] {\n\n    let closestPair: [number, number] = [numbers[0], numbers[1]];\n    let minDistance: number = Math.abs(numbers[1] - numbers[0]);\n\n    for (let i = 0; i < numbers.length - 1; i++) {\n        for (let j = i + 1; j < numbers.length; j++) {\n            const distance = Math.abs(numbers[j] - numbers[i]);\n            if (distance < minDistance) {\n                minDistance = distance;\n                closestPair = [numbers[i], numbers[j]];\n            }\n        }\n    }\n\n    return closestPair;\n}\n", "prompt": "//From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n// [2.0, 2.2]\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n// [2.0, 2.0]\nfunction find_closest_elements(numbers: number[]): [number, number] {\n", "wholecode": "//From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n// [2.0, 2.2]\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n// [2.0, 2.0]\nfunction find_closest_elements(numbers: number[]): [number, number] {\n    let closestPair: [number, number] = [numbers[0], numbers[1]];\n    let minDistance: number = Math.abs(numbers[1] - numbers[0]);\n\n    for (let i = 0; i < numbers.length - 1; i++) {\n        for (let j = i + 1; j < numbers.length; j++) {\n            const distance = Math.abs(numbers[j] - numbers[i]);\n            if (distance < minDistance) {\n                minDistance = distance;\n                closestPair = [numbers[i], numbers[j]];\n            }\n        }\n    }\n\n    return closestPair;\n}\n\nconsole.log(find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2]));\nconsole.log(find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0]));"}
{"task_id": "HumanEval_76_is_simple_power", "generation": "//Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunction is_simple_power(x: number, n: number): boolean {\n\n  if (x === 1) return true;\n  if (n === 1) return false;\n  let i = 1;\n  while (i < x) {\n    i = i * n;\n    if (i === x) return true;\n  }\n  return false;\n}", "prompt": "//Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunction is_simple_power(x: number, n: number): boolean {\n", "wholecode": "//Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunction is_simple_power(x: number, n: number): boolean {\n  if (x === 1) return true;\n  if (n === 1) return false;\n  let i = 1;\n  while (i < x) {\n    i = i * n;\n    if (i === x) return true;\n  }\n  return false;\n}"}
{"task_id": "HumanEval_39_prime_fib", "generation": "//prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunction prime_fib(n: number): number {\n\n    let fib: number[] = [0, 1];\n    let prime: number[] = [];\n    let i: number = 2;\n    while (prime.length < n) {\n        fib[i] = fib[i - 1] + fib[i - 2];\n        if (isPrime(fib[i])) {\n            prime.push(fib[i]);\n        }\n        i++;\n    }\n    return prime[n - 1];\n}\n", "prompt": "//prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunction prime_fib(n: number): number {\n", "wholecode": "//prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunction prime_fib(n: number): number {\n    let fib: number[] = [0, 1];\n    let prime: number[] = [];\n    let i: number = 2;\n    while (prime.length < n) {\n        fib[i] = fib[i - 1] + fib[i - 2];\n        if (isPrime(fib[i])) {\n            prime.push(fib[i]);\n        }\n        i++;\n    }\n    return prime[n - 1];\n}\n\nfunction isPrime(n: number): boolean {\n    if (n < 2) {\n        return false;\n    }\n    for (let i = 2; i <= Math.sqrt(n); i++) {\n        if (n % i === 0) {\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_145_order_by_points", "generation": "//Write a function which sorts the given array of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original array.\n// For example:\n// >>> order_by_points([1, 11, -1, -11, -12])\n// [-1, -11, 1, -12, 11]\n// >>> order_by_points([])\n// []\nfunction order_by_points(nums: number[]): number[] {\n\n  return nums.sort((a, b) => {\n    const sumA = a.toString().split(\"\").reduce((acc, curr) => acc + Number(curr), 0);\n    const sumB = b.toString().split(\"\").reduce((acc, curr) => acc + Number(curr), 0);\n    if (sumA === sumB) {\n      return nums.indexOf(a) - nums.indexOf(b);\n    }\n    return sumA - sumB;\n  });\n}\n", "prompt": "//Write a function which sorts the given array of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original array.\n// For example:\n// >>> order_by_points([1, 11, -1, -11, -12])\n// [-1, -11, 1, -12, 11]\n// >>> order_by_points([])\n// []\nfunction order_by_points(nums: number[]): number[] {\n", "wholecode": "//Write a function which sorts the given array of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original array.\n// For example:\n// >>> order_by_points([1, 11, -1, -11, -12])\n// [-1, -11, 1, -12, 11]\n// >>> order_by_points([])\n// []\nfunction order_by_points(nums: number[]): number[] {\n  return nums.sort((a, b) => {\n    const sumA = a.toString().split(\"\").reduce((acc, curr) => acc + Number(curr), 0);\n    const sumB = b.toString().split(\"\").reduce((acc, curr) => acc + Number(curr), 0);\n    if (sumA === sumB) {\n      return nums.indexOf(a) - nums.indexOf(b);\n    }\n    return sumA - sumB;\n  });\n}\n\nconsole.log(order_by_points([1, 11, -1, -11, -12]));"}
{"task_id": "HumanEval_0_has_close_elements", "generation": "//Check if in given array of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n// false\n// >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n// true\nfunction has_close_elements(numbers: number[], threshold: number): boolean {\n\n    for (let i = 0; i < numbers.length; i++) {\n        for (let j = i + 1; j < numbers.length; j++) {\n            if (Math.abs(numbers[i] - numbers[j]) < threshold) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n", "prompt": "//Check if in given array of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n// false\n// >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n// true\nfunction has_close_elements(numbers: number[], threshold: number): boolean {\n", "wholecode": "//Check if in given array of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n// false\n// >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n// true\nfunction has_close_elements(numbers: number[], threshold: number): boolean {\n    for (let i = 0; i < numbers.length; i++) {\n        for (let j = i + 1; j < numbers.length; j++) {\n            if (Math.abs(numbers[i] - numbers[j]) < threshold) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\nconsole.log(has_close_elements([1.0, 2.0, 3.0], 0.5));\nconsole.log(has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3));"}
{"task_id": "HumanEval_10_make_palindrome", "generation": "//Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction make_palindrome(string: string): string {\n\n  const n = string.length;\n  const is_palindrome = (i: number, j: number): boolean => {\n    while (i < j) {\n      if (string[i] !== string[j]) {\n        return false;\n      }\n      i++;\n      j--;\n    }\n    return true;\n  };\n  let i = n - 1;\n  while (i >= 0) {\n    if (is_palindrome(0, i)) {\n      break;\n    }\n    i--;\n  }\n  return string + string.slice(0, i).split(\"\").reverse().join(\"\");\n}\n", "prompt": "//Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction make_palindrome(string: string): string {\n", "wholecode": "//Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction make_palindrome(string: string): string {\n  const n = string.length;\n  const is_palindrome = (i: number, j: number): boolean => {\n    while (i < j) {\n      if (string[i] !== string[j]) {\n        return false;\n      }\n      i++;\n      j--;\n    }\n    return true;\n  };\n  let i = n - 1;\n  while (i >= 0) {\n    if (is_palindrome(0, i)) {\n      break;\n    }\n    i--;\n  }\n  return string + string.slice(0, i).split(\"\").reverse().join(\"\");\n}\n\n// Find the longest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction longest_palindrome(string: string): string {\n  const n = string.length;\n  const is_palindrome = (i: number, j: number): boolean => {\n    while (i < j) {\n      if (string[i] !== string[j]) {\n        return false;\n      }\n      i++;\n      j--;\n    }\n    return true;\n  };\n  let i = n - 1;\n  while (i >= 0) {\n    if (is_palindrome(0, i)) {\n      break;\n    }\n    i--;\n  }\n  return string.slice(0, i + 1);\n}\n\nexport { make_palindrome, longest_palindrome };"}
{"task_id": "HumanEval_11_string_xor", "generation": "//Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunction string_xor(a: string, b: string): string {\n\n    let result = \"\";\n    for (let i = 0; i < a.length; i++) {\n        result += a[i] === b[i] ? \"0\" : \"1\";\n    }\n    return result;\n}", "prompt": "//Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunction string_xor(a: string, b: string): string {\n", "wholecode": "//Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunction string_xor(a: string, b: string): string {\n    let result = \"\";\n    for (let i = 0; i < a.length; i++) {\n        result += a[i] === b[i] ? \"0\" : \"1\";\n    }\n    return result;\n}"}
{"task_id": "HumanEval_139_special_factorial", "generation": "//The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunction special_factorial(n: number): number {\n\n  let result = 1;\n  for (let i = 1; i <= n; i++) {\n    result *= factorial(i);\n  }\n  return result;\n}\n", "prompt": "//The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunction special_factorial(n: number): number {\n", "wholecode": "//The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunction special_factorial(n: number): number {\n  let result = 1;\n  for (let i = 1; i <= n; i++) {\n    result *= factorial(i);\n  }\n  return result;\n}\n\nfunction factorial(n: number): number {\n  if (n === 0 || n === 1) {\n    return 1;\n  } else {\n    return n * factorial(n - 1);\n  }\n}\n\nconsole.log(special_factorial(4));"}
{"task_id": "HumanEval_122_add_elements", "generation": "//Given a non-empty array of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunction add_elements(arr: number[], k: number): number {\n\n  let sum = 0;\n  for (let i = 0; i < k; i++) {\n    if (arr[i] < 100 && arr[i] > -100) {\n      sum += arr[i];\n    }\n  }\n  return sum;\n}", "prompt": "//Given a non-empty array of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunction add_elements(arr: number[], k: number): number {\n", "wholecode": "//Given a non-empty array of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunction add_elements(arr: number[], k: number): number {\n  let sum = 0;\n  for (let i = 0; i < k; i++) {\n    if (arr[i] < 100 && arr[i] > -100) {\n      sum += arr[i];\n    }\n  }\n  return sum;\n}"}
{"task_id": "HumanEval_46_fib4", "generation": "//The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunction fib4(n: number): number {\n\n  if (n === 0 || n === 1) return 0;\n  if (n === 2) return 2;\n  if (n === 3) return 0;\n\n  let fib4_0 = 0;\n  let fib4_1 = 0;\n  let fib4_2 = 2;\n  let fib4_3 = 0;\n  let fib4_n = 0;\n\n  for (let i = 4; i <= n; i++) {\n    fib4_n = fib4_0 + fib4_1 + fib4_2 + fib4_3;\n    fib4_0 = fib4_1;\n    fib4_1 = fib4_2;\n    fib4_2 = fib4_3;\n    fib4_3 = fib4_n;\n  }\n\n  return fib4_n;\n}\n", "prompt": "//The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunction fib4(n: number): number {\n", "wholecode": "//The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunction fib4(n: number): number {\n  if (n === 0 || n === 1) return 0;\n  if (n === 2) return 2;\n  if (n === 3) return 0;\n\n  let fib4_0 = 0;\n  let fib4_1 = 0;\n  let fib4_2 = 2;\n  let fib4_3 = 0;\n  let fib4_n = 0;\n\n  for (let i = 4; i <= n; i++) {\n    fib4_n = fib4_0 + fib4_1 + fib4_2 + fib4_3;\n    fib4_0 = fib4_1;\n    fib4_1 = fib4_2;\n    fib4_2 = fib4_3;\n    fib4_3 = fib4_n;\n  }\n\n  return fib4_n;\n}\n\nconsole.log(fib4(5));\nconsole.log(fib4(6));\nconsole.log(fib4(7));"}
{"task_id": "HumanEval_104_unique_digits", "generation": "//Given an array of positive integers x. return a sorted array of all \n// elements that hasn't any even digit.\n// Note: Returned array should be sorted in increasing order.\n// For example:\n// >>> unique_digits([15, 33, 1422, 1])\n// [1, 15, 33]\n// >>> unique_digits([152, 323, 1422, 10])\n// []\nfunction unique_digits(x: number[]): number[] {\n\n    let result: number[] = [];\n    for (let i = 0; i < x.length; i++) {\n        if (x[i] % 2 !== 0) {\n            result.push(x[i]);\n        }\n    }\n    return result.sort((a, b) => a - b);\n}", "prompt": "//Given an array of positive integers x. return a sorted array of all \n// elements that hasn't any even digit.\n// Note: Returned array should be sorted in increasing order.\n// For example:\n// >>> unique_digits([15, 33, 1422, 1])\n// [1, 15, 33]\n// >>> unique_digits([152, 323, 1422, 10])\n// []\nfunction unique_digits(x: number[]): number[] {\n", "wholecode": "//Given an array of positive integers x. return a sorted array of all \n// elements that hasn't any even digit.\n// Note: Returned array should be sorted in increasing order.\n// For example:\n// >>> unique_digits([15, 33, 1422, 1])\n// [1, 15, 33]\n// >>> unique_digits([152, 323, 1422, 10])\n// []\nfunction unique_digits(x: number[]): number[] {\n    let result: number[] = [];\n    for (let i = 0; i < x.length; i++) {\n        if (x[i] % 2 !== 0) {\n            result.push(x[i]);\n        }\n    }\n    return result.sort((a, b) => a - b);\n}"}
{"task_id": "HumanEval_117_select_words", "generation": "//Given a string s and a natural number n, you have been tasked to implement \n// a function that returns an array of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty array.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// [\"little\"]\n// >>> select_words(\"Mary had a little lamb\", 3)\n// [\"Mary\", \"lamb\"]\n// >>> select_words(\"simple white space\", 2)\n// []\n// >>> select_words(\"Hello world\", 4)\n// [\"world\"]\n// >>> select_words(\"Uncle sam\", 3)\n// [\"Uncle\"]\nfunction select_words(s: string, n: number): string[] {\n\n    const words = s.split(' ');\n    const result: string[] = [];\n    const vowels = ['a', 'e', 'i', 'o', 'u'];\n    for (const word of words) {\n        let consonantCount = 0;\n        for (const char of word) {\n            if (!vowels.includes(char.toLowerCase())) {\n                consonantCount++;\n            }\n        }\n        if (consonantCount === n) {\n            result.push(word);\n        }\n    }\n    return result;\n}", "prompt": "//Given a string s and a natural number n, you have been tasked to implement \n// a function that returns an array of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty array.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// [\"little\"]\n// >>> select_words(\"Mary had a little lamb\", 3)\n// [\"Mary\", \"lamb\"]\n// >>> select_words(\"simple white space\", 2)\n// []\n// >>> select_words(\"Hello world\", 4)\n// [\"world\"]\n// >>> select_words(\"Uncle sam\", 3)\n// [\"Uncle\"]\nfunction select_words(s: string, n: number): string[] {\n", "wholecode": "//Given a string s and a natural number n, you have been tasked to implement \n// a function that returns an array of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty array.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// [\"little\"]\n// >>> select_words(\"Mary had a little lamb\", 3)\n// [\"Mary\", \"lamb\"]\n// >>> select_words(\"simple white space\", 2)\n// []\n// >>> select_words(\"Hello world\", 4)\n// [\"world\"]\n// >>> select_words(\"Uncle sam\", 3)\n// [\"Uncle\"]\nfunction select_words(s: string, n: number): string[] {\n    const words = s.split(' ');\n    const result: string[] = [];\n    const vowels = ['a', 'e', 'i', 'o', 'u'];\n    for (const word of words) {\n        let consonantCount = 0;\n        for (const char of word) {\n            if (!vowels.includes(char.toLowerCase())) {\n                consonantCount++;\n            }\n        }\n        if (consonantCount === n) {\n            result.push(word);\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_72_will_it_fly", "generation": "//Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([1, 2], 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([3, 2, 3], 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([3, 2, 3], 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([3], 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunction will_it_fly(q: number[], w: number): boolean {\n\n  let sum = 0;\n  for (let i = 0; i < q.length; i++) {\n    sum += q[i];\n  }\n  if (sum > w) {\n    return false;\n  }\n  for (let i = 0; i < q.length / 2; i++) {\n    if (q[i] !== q[q.length - 1 - i]) {\n      return false;\n    }\n  }\n  return true;\n}", "prompt": "//Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([1, 2], 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([3, 2, 3], 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([3, 2, 3], 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([3], 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunction will_it_fly(q: number[], w: number): boolean {\n", "wholecode": "//Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([1, 2], 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([3, 2, 3], 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([3, 2, 3], 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([3], 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunction will_it_fly(q: number[], w: number): boolean {\n  let sum = 0;\n  for (let i = 0; i < q.length; i++) {\n    sum += q[i];\n  }\n  if (sum > w) {\n    return false;\n  }\n  for (let i = 0; i < q.length / 2; i++) {\n    if (q[i] !== q[q.length - 1 - i]) {\n      return false;\n    }\n  }\n  return true;\n}"}
{"task_id": "HumanEval_55_fib", "generation": "//Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunction fib(n: number): number {\n\n  if (n <= 1) {\n    return n;\n  }\n  return fib(n - 1) + fib(n - 2);\n}\n", "prompt": "//Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunction fib(n: number): number {\n", "wholecode": "//Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunction fib(n: number): number {\n  if (n <= 1) {\n    return n;\n  }\n  return fib(n - 1) + fib(n - 2);\n}\n\n//Return the factorial of n.\n// >>> fact(5)\n// 120\n// >>> fact(3)\n// 6\n// >>> fact(2)\n// 2\nfunction fact(n: number): number {\n  if (n <= 1) {\n    return 1;\n  }\n  return n * fact(n - 1);\n}\n\n//Return the sum of all numbers from 1 to n.\n// >>> sum(4)\n// 10\n// >>> sum(1)\n// 1\n// >>> sum(10)\n// 55\nfunction sum(n: number): number {\n  if (n <= 1) {\n    return n;\n  }\n  return n + sum(n - 1);\n}\n\n//Return the sum of all even numbers from 1 to n.\n// >>> sumEven(5)\n// 6\n// >>> sumEven(8)\n// 20\n// >>> sumEven(3)\n// 2\nfunction sumEven(n: number): number {\n  if (n <= 1) {\n    return 0;\n  }\n  if (n % 2 === 0) {\n    return n + sumEven(n - 2);\n  }\n  return sumEven(n - 1);\n}\n\n//Return the sum of all odd numbers from 1 to n.\n// >>> sumOdd(5)\n// 9\n// >>> sumOdd(8)\n// 16\n// >>> sumOdd(3)\n// 4\nfunction sumOdd(n: number): number {\n  if (n <= 1) {\n    return 0;\n  }\n  if (n % 2 !== 0) {\n    return n + sumOdd(n - 2);\n  }\n  return sumOdd(n - 1);\n}\n\n//Return the sum of all numbers from 1 to n that are divisible by 3.\n// >>> sumDivisibleBy3(5)\n// 3\n// >>> sumDivisibleBy3(8)\n// 18\n// >>> sumDivisibleBy3(3)\n// 3\nfunction sumDivisibleBy3(n: number): number {\n  if (n <= 1) {\n    return 0;\n  }\n  if (n % 3 === "}
{"task_id": "HumanEval_153_Strongest_Extension", "generation": "//You will be given the name of a class (a string) and an array of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the array.\n// For example, if you are given \"Slices\" as the class and an array of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n// \"my_class.AA\"\nfunction Strongest_Extension(class_name: string, extensions: string[]): string {\n\n    let strongestExtension = '';\n    let strongestStrength = -Infinity;\n\n    for (const extension of extensions) {\n        const cap = extension.match(/[A-Z]/g)?.length || 0;\n        const sm = extension.match(/[a-z]/g)?.length || 0;\n        const strength = cap - sm;\n\n        if (strength > strongestStrength) {\n            strongestExtension = extension;\n            strongestStrength = strength;\n        }\n    }\n\n    return `${class_name}.${strongestExtension}`;\n}", "prompt": "//You will be given the name of a class (a string) and an array of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the array.\n// For example, if you are given \"Slices\" as the class and an array of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n// \"my_class.AA\"\nfunction Strongest_Extension(class_name: string, extensions: string[]): string {\n", "wholecode": "//You will be given the name of a class (a string) and an array of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the array.\n// For example, if you are given \"Slices\" as the class and an array of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n// \"my_class.AA\"\nfunction Strongest_Extension(class_name: string, extensions: string[]): string {\n    let strongestExtension = '';\n    let strongestStrength = -Infinity;\n\n    for (const extension of extensions) {\n        const cap = extension.match(/[A-Z]/g)?.length || 0;\n        const sm = extension.match(/[a-z]/g)?.length || 0;\n        const strength = cap - sm;\n\n        if (strength > strongestStrength) {\n            strongestExtension = extension;\n            strongestStrength = strength;\n        }\n    }\n\n    return `${class_name}.${strongestExtension}`;\n}"}
{"task_id": "HumanEval_119_match_parens", "generation": "//You are given an array of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([\"()(\", \")\"])\n// \"Yes\"\n// >>> match_parens([\")\", \")\"])\n// \"No\"\nfunction match_parens(lst: string[]): string {\n\n  const str = lst.join(\"\");\n  let open = 0;\n  let close = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === \"(\") {\n      open++;\n    } else {\n      close++;\n    }\n  }\n  return open === close ? \"Yes\" : \"No\";\n}\n", "prompt": "//You are given an array of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([\"()(\", \")\"])\n// \"Yes\"\n// >>> match_parens([\")\", \")\"])\n// \"No\"\nfunction match_parens(lst: string[]): string {\n", "wholecode": "//You are given an array of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([\"()(\", \")\"])\n// \"Yes\"\n// >>> match_parens([\")\", \")\"])\n// \"No\"\nfunction match_parens(lst: string[]): string {\n  const str = lst.join(\"\");\n  let open = 0;\n  let close = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === \"(\") {\n      open++;\n    } else {\n      close++;\n    }\n  }\n  return open === close ? \"Yes\" : \"No\";\n}\n\nconsole.log(match_parens([\"()(\", \")\"]));\nconsole.log(match_parens([\")\", \")\"]));"}
{"task_id": "HumanEval_90_next_smallest", "generation": "//You are given an array of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the array.\n// Return undefined if there is no such element.\n// >>> next_smallest([1, 2, 3, 4, 5])\n// 2\n// >>> next_smallest([5, 1, 4, 3, 2])\n// 2\n// >>> next_smallest([])\n// undefined\n// >>> next_smallest([1, 1])\n// undefined\nfunction next_smallest(lst: number[]): number | undefined {\n\n  if (lst.length < 2) {\n    return undefined;\n  }\n  let smallest = Infinity;\n  let secondSmallest = Infinity;\n  for (let i = 0; i < lst.length; i++) {\n    if (lst[i] < smallest) {\n      secondSmallest = smallest;\n      smallest = lst[i];\n    } else if (lst[i] < secondSmallest && lst[i] !== smallest) {\n      secondSmallest = lst[i];\n    }\n  }\n  return secondSmallest === Infinity ? undefined : secondSmallest;\n}\n", "prompt": "//You are given an array of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the array.\n// Return undefined if there is no such element.\n// >>> next_smallest([1, 2, 3, 4, 5])\n// 2\n// >>> next_smallest([5, 1, 4, 3, 2])\n// 2\n// >>> next_smallest([])\n// undefined\n// >>> next_smallest([1, 1])\n// undefined\nfunction next_smallest(lst: number[]): number | undefined {\n", "wholecode": "//You are given an array of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the array.\n// Return undefined if there is no such element.\n// >>> next_smallest([1, 2, 3, 4, 5])\n// 2\n// >>> next_smallest([5, 1, 4, 3, 2])\n// 2\n// >>> next_smallest([])\n// undefined\n// >>> next_smallest([1, 1])\n// undefined\nfunction next_smallest(lst: number[]): number | undefined {\n  if (lst.length < 2) {\n    return undefined;\n  }\n  let smallest = Infinity;\n  let secondSmallest = Infinity;\n  for (let i = 0; i < lst.length; i++) {\n    if (lst[i] < smallest) {\n      secondSmallest = smallest;\n      smallest = lst[i];\n    } else if (lst[i] < secondSmallest && lst[i] !== smallest) {\n      secondSmallest = lst[i];\n    }\n  }\n  return secondSmallest === Infinity ? undefined : secondSmallest;\n}\n\nconsole.log(next_smallest([1, 2, 3, 4, 5]));\nconsole.log(next_smallest([5, 1, 4, 3, 2]));\nconsole.log(next_smallest([]));\nconsole.log(next_smallest([1, 1]));"}
{"task_id": "HumanEval_92_any_int", "generation": "//Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunction any_int(x: number, y: number, z: number): boolean {\n\n  if (Number.isInteger(x) && Number.isInteger(y) && Number.isInteger(z)) {\n    if (x === y + z || y === x + z || z === x + y) {\n      return true;\n    } else {\n      return false;\n    }\n  } else {\n    return false;\n  }\n}", "prompt": "//Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunction any_int(x: number, y: number, z: number): boolean {\n", "wholecode": "//Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunction any_int(x: number, y: number, z: number): boolean {\n  if (Number.isInteger(x) && Number.isInteger(y) && Number.isInteger(z)) {\n    if (x === y + z || y === x + z || z === x + y) {\n      return true;\n    } else {\n      return false;\n    }\n  } else {\n    return false;\n  }\n}\nconsole.log(any_int(5, 2, 7));\nconsole.log(any_int(3, 2, 2));\nconsole.log(any_int(3, -2, 1));\nconsole.log(any_int(3.6, -2.2, 2));"}
{"task_id": "HumanEval_2_truncate_number", "generation": "//Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunction truncate_number(number: number): number {\n\n    return number - Math.floor(number);\n}\n", "prompt": "//Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunction truncate_number(number: number): number {\n", "wholecode": "//Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunction truncate_number(number: number): number {\n    return number - Math.floor(number);\n}\n\nconsole.log(truncate_number(3.5));"}
{"task_id": "HumanEval_42_incr_list", "generation": "//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l: number[]): number[] {\n\n    return l.map((x) => x + 1);\n}\n", "prompt": "//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l: number[]): number[] {\n", "wholecode": "//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l: number[]): number[] {\n    return l.map((x) => x + 1);\n}\n\n//Return array with elements multiplied by 2.\n// >>> double_list([1, 2, 3])\n// [2, 4, 6]\n// >>> double_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [10, 6, 10, 4, 6, 6, 18, 0, 246]\nfunction double_list(l: number[]): number[] {\n    return l.map((x) => x * 2);\n}\n\n//Return array with elements multiplied by n.\n// >>> multiply_list([1, 2, 3], 5)\n// [5, 10, 15]\n// >>> multiply_list([5, 3, 5, 2, 3, 3, 9, 0, 123], 10)\n// [50, 30, 50, 20, 30, 30, 90, 0, 1230]\nfunction multiply_list(l: number[], n: number): number[] {\n    return l.map((x) => x * n);\n}\n\n//Return array with elements divided by n.\n// >>> divide_list([1, 2, 3], 4)\n// [0.25, 0.5, 0.75]\n// >>> divide_list([5, 3, 5, 2, 3, 3, 9, 0, 123], 10)\n// [0.5, 0.3, 0.5, 0.2, 0.3, 0.3, 0.9, 0, 12.3]\nfunction divide_list(l: number[], n: number): number[] {\n    return l.map((x) => x / n);\n}\n\n//Return array with elements raised to the power of n.\n// >>> power_list([1, 2, 3], 2)\n// [1, 4, 9]\n// >>> power_list([5, 3, 5, 2, 3, 3, 9, 0, 123], "}
{"task_id": "HumanEval_150_x_or_y", "generation": "//A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunction x_or_y(n: number, x: number, y: number): number {\n\n    if (isPrime(n)) {\n        return x;\n    } else {\n        return y;\n    }\n}\n", "prompt": "//A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunction x_or_y(n: number, x: number, y: number): number {\n", "wholecode": "//A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunction x_or_y(n: number, x: number, y: number): number {\n    if (isPrime(n)) {\n        return x;\n    } else {\n        return y;\n    }\n}\n\nfunction isPrime(n: number): boolean {\n    if (n <= 1) {\n        return false;\n    }\n    for (let i = 2; i <= Math.sqrt(n); i++) {\n        if (n % i === 0) {\n            return false;\n        }\n    }\n    return true;\n}\n\nconsole.log(x_or_y(7, 34, 12));\nconsole.log(x_or_y(15, 8, 5));"}
{"task_id": "HumanEval_49_modp", "generation": "//Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunction modp(n: number, p: number): number {\n\n  if (n === 0) return 1;\n  if (n % 2 === 0) return modp(n / 2, p) ** 2 % p;\n  return (modp((n - 1) / 2, p) ** 2 * 2) % p;\n}\n", "prompt": "//Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunction modp(n: number, p: number): number {\n", "wholecode": "//Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunction modp(n: number, p: number): number {\n  if (n === 0) return 1;\n  if (n % 2 === 0) return modp(n / 2, p) ** 2 % p;\n  return (modp((n - 1) / 2, p) ** 2 * 2) % p;\n}\n\n//Return n! modulo p (be aware of numerics).\n// >>> modfact(3, 5)\n// 2\n// >>> modfact(0, 101)\n// 1\n// >>> modfact(3, 11)\n// 6\n// >>> modfact(100, 101)\n// 1\nfunction modfact(n: number, p: number): number {\n  if (n === 0) return 1;\n  return (modfact(n - 1, p) * n) % p;\n}\n\n//Return n! modulo p (be aware of numerics).\n// >>> modfact(3, 5)\n// 2\n// >>> modfact(0, 101)\n// 1\n// >>> modfact(3, 11)\n// 6\n// >>> modfact(100, 101)\n// 1\nfunction modfact(n: number, p: number): number {\n  if (n === 0) return 1;\n  return (modfact(n - 1, p) * n) % p;\n}\n\n//Return n! modulo p (be aware of numerics).\n// >>> modfact(3, 5)\n// 2\n// >>> modfact(0, 101)\n// 1\n// >>> modfact(3, 11)\n// 6\n// >>> modfact(100, 101)\n// 1\nfunction modfact(n: number, p: number): number {\n  if (n === 0) return 1;\n  return (modfact(n - 1, p) * n) % p;\n}\n\n//Return n! modulo p (be aware of numerics).\n// >>> modfact(3, 5)\n// 2\n// >>> modfact(0, 101)\n// 1\n// >>> modfact(3, 11)\n// 6\n// >>> modfact(100, 101)\n//"}
{"task_id": "HumanEval_155_even_odd_count", "generation": "//Given an integer. return an array that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// [1, 1]\n// >>> even_odd_count(123)\n// [1, 2]\nfunction even_odd_count(num: number): [number, number] {\n\n  const numStr = Math.abs(num).toString();\n  let evenCount = 0;\n  let oddCount = 0;\n\n  for (const digit of numStr) {\n    if (parseInt(digit) % 2 === 0) {\n      evenCount++;\n    } else {\n      oddCount++;\n    }\n  }\n\n  return [evenCount, oddCount];\n}\n", "prompt": "//Given an integer. return an array that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// [1, 1]\n// >>> even_odd_count(123)\n// [1, 2]\nfunction even_odd_count(num: number): [number, number] {\n", "wholecode": "//Given an integer. return an array that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// [1, 1]\n// >>> even_odd_count(123)\n// [1, 2]\nfunction even_odd_count(num: number): [number, number] {\n  const numStr = Math.abs(num).toString();\n  let evenCount = 0;\n  let oddCount = 0;\n\n  for (const digit of numStr) {\n    if (parseInt(digit) % 2 === 0) {\n      evenCount++;\n    } else {\n      oddCount++;\n    }\n  }\n\n  return [evenCount, oddCount];\n}\n\nconsole.log(even_odd_count(-12));\nconsole.log(even_odd_count(123));"}
{"task_id": "HumanEval_80_is_happy", "generation": "//You are given a string s.\n// Your task is to check if the string is hapts or not.\n// A string is hapts if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunction is_happy(s: string): boolean {\n\n    if (s.length < 3) {\n        return false;\n    }\n    for (let i = 0; i < s.length - 2; i++) {\n        if (s[i] === s[i + 1] || s[i] === s[i + 2] || s[i + 1] === s[i + 2]) {\n            return false;\n        }\n    }\n    return true;\n}", "prompt": "//You are given a string s.\n// Your task is to check if the string is hapts or not.\n// A string is hapts if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunction is_happy(s: string): boolean {\n", "wholecode": "//You are given a string s.\n// Your task is to check if the string is hapts or not.\n// A string is hapts if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunction is_happy(s: string): boolean {\n    if (s.length < 3) {\n        return false;\n    }\n    for (let i = 0; i < s.length - 2; i++) {\n        if (s[i] === s[i + 1] || s[i] === s[i + 2] || s[i + 1] === s[i + 2]) {\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_59_largest_prime_factor", "generation": "//Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunction largest_prime_factor(n: number): number {\n\n    let largestPrimeFactor = 1;\n    let i = 2;\n    while (i <= n) {\n        if (n % i === 0) {\n            largestPrimeFactor = i;\n            n /= i;\n        } else {\n            i++;\n        }\n    }\n    return largestPrimeFactor;\n}", "prompt": "//Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunction largest_prime_factor(n: number): number {\n", "wholecode": "//Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunction largest_prime_factor(n: number): number {\n    let largestPrimeFactor = 1;\n    let i = 2;\n    while (i <= n) {\n        if (n % i === 0) {\n            largestPrimeFactor = i;\n            n /= i;\n        } else {\n            i++;\n        }\n    }\n    return largestPrimeFactor;\n}"}
{"task_id": "HumanEval_66_digitSum", "generation": "//Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunction digitSum(s: string): number {\n\n  let sum = 0;\n  for (let i = 0; i < s.length; i++) {\n    if (s[i] === s[i].toUpperCase()) {\n      sum += s.charCodeAt(i);\n    }\n  }\n  return sum;\n}", "prompt": "//Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunction digitSum(s: string): number {\n", "wholecode": "//Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunction digitSum(s: string): number {\n  let sum = 0;\n  for (let i = 0; i < s.length; i++) {\n    if (s[i] === s[i].toUpperCase()) {\n      sum += s.charCodeAt(i);\n    }\n  }\n  return sum;\n}"}
{"task_id": "HumanEval_21_rescale_to_unit", "generation": "//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit(numbers: number[]): number[] {\n\n    const min = Math.min(...numbers);\n    const max = Math.max(...numbers);\n    const range = max - min;\n    return numbers.map((num) => (num - min) / range);\n}\n", "prompt": "//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit(numbers: number[]): number[] {\n", "wholecode": "//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit(numbers: number[]): number[] {\n    const min = Math.min(...numbers);\n    const max = Math.max(...numbers);\n    const range = max - min;\n    return numbers.map((num) => (num - min) / range);\n}\n\n//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit2(numbers: number[]): number[] {\n    const min = Math.min(...numbers);\n    const max = Math.max(...numbers);\n    const range = max - min;\n    return numbers.map((num) => (num - min) / range);\n}\n\n//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit3(numbers: number[]): number[] {\n    const min = Math.min(...numbers);\n    const max = Math.max(...numbers);\n    const range = max - min;\n    return numbers.map((num) => (num - min) / range);\n}\n\n//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit4(numbers: number[]): number[] {\n    const min = Math.min(...numbers);\n    const max = Math.max(...numbers);\n    const range = max - min;\n    return numbers.map((num) => (num - min) / range"}
{"task_id": "HumanEval_121_solution", "generation": "//Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([5, 8, 7, 1])\n// 12\n// >>> solution([3, 3, 3, 3, 3])\n// 9\n// >>> solution([30, 13, 24, 321])\n// 0\nfunction solution(lst: number[]): number {\n\n    let sum = 0;\n    for (let i = 0; i < lst.length; i += 2) {\n        if (lst[i] % 2 !== 0) {\n            sum += lst[i];\n        }\n    }\n    return sum;\n}", "prompt": "//Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([5, 8, 7, 1])\n// 12\n// >>> solution([3, 3, 3, 3, 3])\n// 9\n// >>> solution([30, 13, 24, 321])\n// 0\nfunction solution(lst: number[]): number {\n", "wholecode": "//Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([5, 8, 7, 1])\n// 12\n// >>> solution([3, 3, 3, 3, 3])\n// 9\n// >>> solution([30, 13, 24, 321])\n// 0\nfunction solution(lst: number[]): number {\n    let sum = 0;\n    for (let i = 0; i < lst.length; i += 2) {\n        if (lst[i] % 2 !== 0) {\n            sum += lst[i];\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_68_pluck", "generation": "//\"Given an array representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in an array, [ smalest_value, its index ],\n// If there are no even values or the given array is empty, return [].\n// Example 1:\n// >>> pluck([4, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([1, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([])\n// []\n// Example 4:\n// >>> pluck([5, 0, 3, 0, 4, 2])\n// [0, 1]\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunction pluck(arr: number[]): number[] {\n\n  let min = Infinity;\n  let index = -1;\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] % 2 === 0 && arr[i] < min) {\n      min = arr[i];\n      index = i;\n    }\n  }\n  return index === -1 ? [] : [min, index];\n}", "prompt": "//\"Given an array representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in an array, [ smalest_value, its index ],\n// If there are no even values or the given array is empty, return [].\n// Example 1:\n// >>> pluck([4, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([1, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([])\n// []\n// Example 4:\n// >>> pluck([5, 0, 3, 0, 4, 2])\n// [0, 1]\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunction pluck(arr: number[]): number[] {\n", "wholecode": "//\"Given an array representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in an array, [ smalest_value, its index ],\n// If there are no even values or the given array is empty, return [].\n// Example 1:\n// >>> pluck([4, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([1, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([])\n// []\n// Example 4:\n// >>> pluck([5, 0, 3, 0, 4, 2])\n// [0, 1]\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunction pluck(arr: number[]): number[] {\n  let min = Infinity;\n  let index = -1;\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] % 2 === 0 && arr[i] < min) {\n      min = arr[i];\n      index = i;\n    }\n  }\n  return index === -1 ? [] : [min, index];\n}"}
{"task_id": "HumanEval_147_get_max_triples", "generation": "//You are given a positive integer n. You have to create an integer array a of length n.\n// For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunction get_max_triples(n: number): number {\n\n    let a: number[] = [];\n    for (let i = 1; i <= n; i++) {\n        a.push(i * i - i + 1);\n    }\n    let count: number = 0;\n    for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n            for (let k = j + 1; k < n; k++) {\n                if ((a[i] + a[j] + a[k]) % 3 === 0) {\n                    count++;\n                }\n            }\n        }\n    }\n    return count;\n}", "prompt": "//You are given a positive integer n. You have to create an integer array a of length n.\n// For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunction get_max_triples(n: number): number {\n", "wholecode": "//You are given a positive integer n. You have to create an integer array a of length n.\n// For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunction get_max_triples(n: number): number {\n    let a: number[] = [];\n    for (let i = 1; i <= n; i++) {\n        a.push(i * i - i + 1);\n    }\n    let count: number = 0;\n    for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n            for (let k = j + 1; k < n; k++) {\n                if ((a[i] + a[j] + a[k]) % 3 === 0) {\n                    count++;\n                }\n            }\n        }\n    }\n    return count;\n}\nconsole.log(get_max_triples(5));"}
{"task_id": "HumanEval_110_exchange", "generation": "//In this problem, you will implement a function that takes two arrays of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 an array of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([1, 2, 3, 4], [1, 2, 3, 4])\n// \"YES\"\n// >>> exchange([1, 2, 3, 4], [1, 5, 3, 4])\n// \"NO\"\n// It is assumed that the input arrays will be non-empty.\nfunction exchange(lst1: number[], lst2: number[]): string {\n\n  let oddCount = 0;\n  let evenCount = 0;\n  for (let i = 0; i < lst1.length; i++) {\n    if (lst1[i] % 2 !== 0) {\n      oddCount++;\n    } else {\n      evenCount++;\n    }\n  }\n  for (let i = 0; i < lst2.length; i++) {\n    if (lst2[i] % 2 !== 0) {\n      oddCount++;\n    } else {\n      evenCount++;\n    }\n  }\n  if (oddCount === 0) {\n    return \"YES\";\n  } else if (oddCount > evenCount) {\n    return \"NO\";\n  } else {\n    return \"YES\";\n  }\n}", "prompt": "//In this problem, you will implement a function that takes two arrays of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 an array of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([1, 2, 3, 4], [1, 2, 3, 4])\n// \"YES\"\n// >>> exchange([1, 2, 3, 4], [1, 5, 3, 4])\n// \"NO\"\n// It is assumed that the input arrays will be non-empty.\nfunction exchange(lst1: number[], lst2: number[]): string {\n", "wholecode": "//In this problem, you will implement a function that takes two arrays of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 an array of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([1, 2, 3, 4], [1, 2, 3, 4])\n// \"YES\"\n// >>> exchange([1, 2, 3, 4], [1, 5, 3, 4])\n// \"NO\"\n// It is assumed that the input arrays will be non-empty.\nfunction exchange(lst1: number[], lst2: number[]): string {\n  let oddCount = 0;\n  let evenCount = 0;\n  for (let i = 0; i < lst1.length; i++) {\n    if (lst1[i] % 2 !== 0) {\n      oddCount++;\n    } else {\n      evenCount++;\n    }\n  }\n  for (let i = 0; i < lst2.length; i++) {\n    if (lst2[i] % 2 !== 0) {\n      oddCount++;\n    } else {\n      evenCount++;\n    }\n  }\n  if (oddCount === 0) {\n    return \"YES\";\n  } else if (oddCount > evenCount) {\n    return \"NO\";\n  } else {\n    return \"YES\";\n  }\n}"}
{"task_id": "HumanEval_47_median", "generation": "//Return median of elements in the array l.\n// >>> median([3, 1, 2, 4, 5])\n// 3\n// >>> median([-10, 4, 6, 1000, 10, 20])\n// 15.0\nfunction median(l: number[]): number {\n\n  const sorted = l.sort((a, b) => a - b);\n  const mid = Math.floor(sorted.length / 2);\n  if (sorted.length % 2 === 0) {\n    return (sorted[mid - 1] + sorted[mid]) / 2;\n  } else {\n    return sorted[mid];\n  }\n}\n", "prompt": "//Return median of elements in the array l.\n// >>> median([3, 1, 2, 4, 5])\n// 3\n// >>> median([-10, 4, 6, 1000, 10, 20])\n// 15.0\nfunction median(l: number[]): number {\n", "wholecode": "//Return median of elements in the array l.\n// >>> median([3, 1, 2, 4, 5])\n// 3\n// >>> median([-10, 4, 6, 1000, 10, 20])\n// 15.0\nfunction median(l: number[]): number {\n  const sorted = l.sort((a, b) => a - b);\n  const mid = Math.floor(sorted.length / 2);\n  if (sorted.length % 2 === 0) {\n    return (sorted[mid - 1] + sorted[mid]) / 2;\n  } else {\n    return sorted[mid];\n  }\n}\n\n//Return the mode of elements in the array l.\n// >>> mode([1, 2, 3, 4, 4])\n// 4\n// >>> mode([1, 1, 2, 3, 4])\n// 1\nfunction mode(l: number[]): number {\n  const counts: { [key: number]: number } = {};\n  let maxCount = 0;\n  let mode = 0;\n  for (const num of l) {\n    counts[num] = (counts[num] || 0) + 1;\n    if (counts[num] > maxCount) {\n      maxCount = counts[num];\n      mode = num;\n    }\n  }\n  return mode;\n}\n\n//Return the variance of elements in the array l.\n// >>> variance([10, 4, 12, 15, 20, 5])\n// 15.75\nfunction variance(l: number[]): number {\n  const mean = l.reduce((a, b) => a + b) / l.length;\n  const squaredDiffs = l.map((x) => (x - mean) ** 2);\n  const variance = squaredDiffs.reduce((a, b) => a + b) / l.length;\n  return variance;\n}\n\n//Return the standard deviation of elements in the array l.\n// >>> standardDeviation([10, 4, 12, 15, 20, 5])\n// 3.9686269665968855\nfunction standardDeviation(l: number[]): number {\n  return Math.sqrt(variance(l));\n}\n\n//Return the range of elements in the array l.\n// >>> range([1, 2, 3, 4, 5])\n// 4\n// >>> range([1, 10, 2, 20, 3, 30, 4, 40, 5, 50])\n// 49\nfunction range(l: number"}
{"task_id": "HumanEval_82_prime_length", "generation": "//Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunction prime_length(string: string): boolean {\n\n  let length = string.length;\n  if (length <= 1) {\n    return false;\n  }\n  for (let i = 2; i < length; i++) {\n    if (length % i === 0) {\n      return false;\n    }\n  }\n  return true;\n}\n", "prompt": "//Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunction prime_length(string: string): boolean {\n", "wholecode": "//Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunction prime_length(string: string): boolean {\n  let length = string.length;\n  if (length <= 1) {\n    return false;\n  }\n  for (let i = 2; i < length; i++) {\n    if (length % i === 0) {\n      return false;\n    }\n  }\n  return true;\n}\n\nconsole.log(prime_length(\"Hello\"));\nconsole.log(prime_length(\"abcdcba\"));\nconsole.log(prime_length(\"kittens\"));\nconsole.log(prime_length(\"orange\"));"}
{"task_id": "HumanEval_73_smallest_change", "generation": "//Given an array arr of integers, find the minimum number of elements that\n// need to be changed to make the array palindromic. A palindromic array is an array that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([1, 2, 3, 5, 4, 7, 9, 6])\n// 4\n// >>> smallest_change([1, 2, 3, 4, 3, 2, 2])\n// 1\n// >>> smallest_change([1, 2, 3, 2, 1])\n// 0\nfunction smallest_change(arr: number[]): number {\n\n  let count = 0;\n  let left = 0;\n  let right = arr.length - 1;\n  while (left < right) {\n    if (arr[left] !== arr[right]) {\n      count++;\n    }\n    left++;\n    right--;\n  }\n  return count;\n}", "prompt": "//Given an array arr of integers, find the minimum number of elements that\n// need to be changed to make the array palindromic. A palindromic array is an array that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([1, 2, 3, 5, 4, 7, 9, 6])\n// 4\n// >>> smallest_change([1, 2, 3, 4, 3, 2, 2])\n// 1\n// >>> smallest_change([1, 2, 3, 2, 1])\n// 0\nfunction smallest_change(arr: number[]): number {\n", "wholecode": "//Given an array arr of integers, find the minimum number of elements that\n// need to be changed to make the array palindromic. A palindromic array is an array that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([1, 2, 3, 5, 4, 7, 9, 6])\n// 4\n// >>> smallest_change([1, 2, 3, 4, 3, 2, 2])\n// 1\n// >>> smallest_change([1, 2, 3, 2, 1])\n// 0\nfunction smallest_change(arr: number[]): number {\n  let count = 0;\n  let left = 0;\n  let right = arr.length - 1;\n  while (left < right) {\n    if (arr[left] !== arr[right]) {\n      count++;\n    }\n    left++;\n    right--;\n  }\n  return count;\n}"}
{"task_id": "HumanEval_133_sum_squares", "generation": "//You are given an array of numbers.\n// You need to return the sum of squared numbers in the given array,\n// round each element in the array to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([1.0, 2.0, 3.0])\n// 14\n// >>> lst([1.0, 4.0, 9.0])\n// 98\n// >>> lst([1.0, 3.0, 5.0, 7.0])\n// 84\n// >>> lst([1.4, 4.2, 0.0])\n// 29\n// >>> lst([-2.4, 1.0, 1.0])\n// 6\nfunction sum_squares(lst: number[]): number {\n\n  return lst.map((x) => Math.ceil(x) ** 2).reduce((a, b) => a + b, 0);\n}", "prompt": "//You are given an array of numbers.\n// You need to return the sum of squared numbers in the given array,\n// round each element in the array to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([1.0, 2.0, 3.0])\n// 14\n// >>> lst([1.0, 4.0, 9.0])\n// 98\n// >>> lst([1.0, 3.0, 5.0, 7.0])\n// 84\n// >>> lst([1.4, 4.2, 0.0])\n// 29\n// >>> lst([-2.4, 1.0, 1.0])\n// 6\nfunction sum_squares(lst: number[]): number {\n", "wholecode": "//You are given an array of numbers.\n// You need to return the sum of squared numbers in the given array,\n// round each element in the array to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([1.0, 2.0, 3.0])\n// 14\n// >>> lst([1.0, 4.0, 9.0])\n// 98\n// >>> lst([1.0, 3.0, 5.0, 7.0])\n// 84\n// >>> lst([1.4, 4.2, 0.0])\n// 29\n// >>> lst([-2.4, 1.0, 1.0])\n// 6\nfunction sum_squares(lst: number[]): number {\n  return lst.map((x) => Math.ceil(x) ** 2).reduce((a, b) => a + b, 0);\n}"}
{"task_id": "HumanEval_141_file_name_check", "generation": "//Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunction file_name_check(file_name: string): string {\n\n    let digits: number = 0;\n    let dot: number = 0;\n    let substring_before_dot: string = \"\";\n    let substring_after_dot: string = \"\";\n    for (let i: number = 0; i < file_name.length; i++) {\n        if (file_name[i] >= \"0\" && file_name[i] <= \"9\") {\n            digits++;\n        }\n        if (file_name[i] === \".\") {\n            dot++;\n            substring_before_dot = file_name.slice(0, i);\n            substring_after_dot = file_name.slice(i + 1);\n        }\n    }\n    if (digits > 3 || dot !== 1 || substring_before_dot === \"\" || substring_before_dot[0] < \"a\" || substring_before_dot[0] > \"z\" || substring_before_dot[0] < \"A\" || substring_before_dot[0] > \"Z\" || substring_after_dot !== \"txt\" && substring_after_dot !== \"exe\" && substring_after_dot !== \"dll\") {\n        return \"No\";\n    }\n    return \"Yes\";\n}", "prompt": "//Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunction file_name_check(file_name: string): string {\n", "wholecode": "//Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunction file_name_check(file_name: string): string {\n    let digits: number = 0;\n    let dot: number = 0;\n    let substring_before_dot: string = \"\";\n    let substring_after_dot: string = \"\";\n    for (let i: number = 0; i < file_name.length; i++) {\n        if (file_name[i] >= \"0\" && file_name[i] <= \"9\") {\n            digits++;\n        }\n        if (file_name[i] === \".\") {\n            dot++;\n            substring_before_dot = file_name.slice(0, i);\n            substring_after_dot = file_name.slice(i + 1);\n        }\n    }\n    if (digits > 3 || dot !== 1 || substring_before_dot === \"\" || substring_before_dot[0] < \"a\" || substring_before_dot[0] > \"z\" || substring_before_dot[0] < \"A\" || substring_before_dot[0] > \"Z\" || substring_after_dot !== \"txt\" && substring_after_dot !== \"exe\" && substring_after_dot !== \"dll\") {\n        return \"No\";\n    }\n    return \"Yes\";\n}"}
{"task_id": "HumanEval_40_triples_sum_to_zero", "generation": "//triples_sum_to_zero takes an array of integers as an input.\n// it returns true if there are three distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> triples_sum_to_zero([1, 3, -2, 1])\n// true\n// >>> triples_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n// true\n// >>> triples_sum_to_zero([1])\n// false\nfunction triples_sum_to_zero(l: number[]): boolean {\n\n    for (let i = 0; i < l.length; i++) {\n        for (let j = i + 1; j < l.length; j++) {\n            for (let k = j + 1; k < l.length; k++) {\n                if (l[i] + l[j] + l[k] === 0) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}", "prompt": "//triples_sum_to_zero takes an array of integers as an input.\n// it returns true if there are three distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> triples_sum_to_zero([1, 3, -2, 1])\n// true\n// >>> triples_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n// true\n// >>> triples_sum_to_zero([1])\n// false\nfunction triples_sum_to_zero(l: number[]): boolean {\n", "wholecode": "//triples_sum_to_zero takes an array of integers as an input.\n// it returns true if there are three distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> triples_sum_to_zero([1, 3, -2, 1])\n// true\n// >>> triples_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n// true\n// >>> triples_sum_to_zero([1])\n// false\nfunction triples_sum_to_zero(l: number[]): boolean {\n    for (let i = 0; i < l.length; i++) {\n        for (let j = i + 1; j < l.length; j++) {\n            for (let k = j + 1; k < l.length; k++) {\n                if (l[i] + l[j] + l[k] === 0) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}"}
{"task_id": "HumanEval_127_intersection", "generation": "//You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([1, 2], [2, 3])\n// \"NO\"\n// >>> intersection([-1, 1], [0, 4])\n// \"NO\"\n// >>> intersection([-3, -1], [-5, 5])\n// \"YES\"\nfunction intersection(interval1: [number, number], interval2: [number, number]): string {\n\n    let start = Math.max(interval1[0], interval2[0]);\n    let end = Math.min(interval1[1], interval2[1]);\n    if (start > end) {\n        return \"NO\";\n    }\n    let length = end - start + 1;\n    if (length <= 1) {\n        return \"NO\";\n    }\n    for (let i = 2; i <= Math.sqrt(length); i++) {\n        if (length % i === 0) {\n            return \"NO\";\n        }\n    }\n    return \"YES\";\n}", "prompt": "//You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([1, 2], [2, 3])\n// \"NO\"\n// >>> intersection([-1, 1], [0, 4])\n// \"NO\"\n// >>> intersection([-3, -1], [-5, 5])\n// \"YES\"\nfunction intersection(interval1: [number, number], interval2: [number, number]): string {\n", "wholecode": "//You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([1, 2], [2, 3])\n// \"NO\"\n// >>> intersection([-1, 1], [0, 4])\n// \"NO\"\n// >>> intersection([-3, -1], [-5, 5])\n// \"YES\"\nfunction intersection(interval1: [number, number], interval2: [number, number]): string {\n    let start = Math.max(interval1[0], interval2[0]);\n    let end = Math.min(interval1[1], interval2[1]);\n    if (start > end) {\n        return \"NO\";\n    }\n    let length = end - start + 1;\n    if (length <= 1) {\n        return \"NO\";\n    }\n    for (let i = 2; i <= Math.sqrt(length); i++) {\n        if (length % i === 0) {\n            return \"NO\";\n        }\n    }\n    return \"YES\";\n}"}
{"task_id": "HumanEval_1_separate_paren_groups", "generation": "//Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the array of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// [\"()\", \"(())\", \"(()())\"]\nfunction separate_paren_groups(paren_string: string): string[] {\n\n    let result: string[] = [];\n    let current_group: string = \"\";\n    let open_braces: number = 0;\n    for (let i = 0; i < paren_string.length; i++) {\n        if (paren_string[i] === \" \") {\n            continue;\n        }\n        if (paren_string[i] === \"(\") {\n            open_braces++;\n        } else if (paren_string[i] === \")\") {\n            open_braces--;\n        }\n        current_group += paren_string[i];\n        if (open_braces === 0) {\n            result.push(current_group);\n            current_group = \"\";\n        }\n    }\n    return result;\n}\n", "prompt": "//Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the array of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// [\"()\", \"(())\", \"(()())\"]\nfunction separate_paren_groups(paren_string: string): string[] {\n", "wholecode": "//Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the array of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// [\"()\", \"(())\", \"(()())\"]\nfunction separate_paren_groups(paren_string: string): string[] {\n    let result: string[] = [];\n    let current_group: string = \"\";\n    let open_braces: number = 0;\n    for (let i = 0; i < paren_string.length; i++) {\n        if (paren_string[i] === \" \") {\n            continue;\n        }\n        if (paren_string[i] === \"(\") {\n            open_braces++;\n        } else if (paren_string[i] === \")\") {\n            open_braces--;\n        }\n        current_group += paren_string[i];\n        if (open_braces === 0) {\n            result.push(current_group);\n            current_group = \"\";\n        }\n    }\n    return result;\n}\n\nconsole.log(separate_paren_groups(\"( ) (( )) (( )( ))\"));"}
{"task_id": "HumanEval_152_compare", "generation": "//I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n// [0, 0, 0, 0, 3, 3]\n// >>> compare([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n// [4, 4, 1, 0, 0, 6]\nfunction compare(game: number[], guess: number[]): number[] {\n\n    let result: number[] = [];\n    for (let i = 0; i < game.length; i++) {\n        if (game[i] === guess[i]) {\n            result.push(0);\n        } else {\n            result.push(Math.abs(game[i] - guess[i]));\n        }\n    }\n    return result;\n}", "prompt": "//I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n// [0, 0, 0, 0, 3, 3]\n// >>> compare([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n// [4, 4, 1, 0, 0, 6]\nfunction compare(game: number[], guess: number[]): number[] {\n", "wholecode": "//I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n// [0, 0, 0, 0, 3, 3]\n// >>> compare([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n// [4, 4, 1, 0, 0, 6]\nfunction compare(game: number[], guess: number[]): number[] {\n    let result: number[] = [];\n    for (let i = 0; i < game.length; i++) {\n        if (game[i] === guess[i]) {\n            result.push(0);\n        } else {\n            result.push(Math.abs(game[i] - guess[i]));\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_83_starts_one_ends", "generation": "//Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunction starts_one_ends(n: number): number {\n\n  if (n === 1) return 1;\n  return 9 * 10 ** (n - 2) * 2;\n}\n", "prompt": "//Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunction starts_one_ends(n: number): number {\n", "wholecode": "//Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunction starts_one_ends(n: number): number {\n  if (n === 1) return 1;\n  return 9 * 10 ** (n - 2) * 2;\n}\n\nconsole.log(starts_one_ends(1));\nconsole.log(starts_one_ends(2));\nconsole.log(starts_one_ends(3));"}
{"task_id": "HumanEval_134_check_if_last_char_is_a_letter", "generation": "//Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunction check_if_last_char_is_a_letter(txt: string): boolean {\n\n  return /[a-zA-Z]$/.test(txt) && !/[a-zA-Z]\\s$/.test(txt);\n}\n", "prompt": "//Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunction check_if_last_char_is_a_letter(txt: string): boolean {\n", "wholecode": "//Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunction check_if_last_char_is_a_letter(txt: string): boolean {\n  return /[a-zA-Z]$/.test(txt) && !/[a-zA-Z]\\s$/.test(txt);\n}\n\nconsole.log(check_if_last_char_is_a_letter(\"apple pie\"));\nconsole.log(check_if_last_char_is_a_letter(\"apple pi e\"));\nconsole.log(check_if_last_char_is_a_letter(\"apple pi e \"));\nconsole.log(check_if_last_char_is_a_letter(\"\"));"}
{"task_id": "HumanEval_124_valid_date", "generation": "//You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunction valid_date(date: string): boolean {\n\n  const dateArr = date.split(\"-\");\n  const month = parseInt(dateArr[0]);\n  const day = parseInt(dateArr[1]);\n  const year = parseInt(dateArr[2]);\n  if (month < 1 || month > 12) {\n    return false;\n  }\n  if (day < 1 || day > 31) {\n    return false;\n  }\n  if (month === 2) {\n    if (day > 29) {\n      return false;\n    }\n  } else if (month === 4 || month === 6 || month === 9 || month === 11) {\n    if (day > 30) {\n      return false;\n    }\n  }\n  return true;\n}", "prompt": "//You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunction valid_date(date: string): boolean {\n", "wholecode": "//You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunction valid_date(date: string): boolean {\n  const dateArr = date.split(\"-\");\n  const month = parseInt(dateArr[0]);\n  const day = parseInt(dateArr[1]);\n  const year = parseInt(dateArr[2]);\n  if (month < 1 || month > 12) {\n    return false;\n  }\n  if (day < 1 || day > 31) {\n    return false;\n  }\n  if (month === 2) {\n    if (day > 29) {\n      return false;\n    }\n  } else if (month === 4 || month === 6 || month === 9 || month === 11) {\n    if (day > 30) {\n      return false;\n    }\n  }\n  return true;\n}"}
{"task_id": "HumanEval_108_count_nums", "generation": "//Write a function count_nums which takes an array of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([])\n// 0\n// >>> count_nums([-1, 11, -11])\n// 1\n// >>> count_nums([1, 1, 2])\n// 3\nfunction count_nums(arr: number[]): number {\n\n  let count = 0;\n  for (let i = 0; i < arr.length; i++) {\n    let sum = 0;\n    let num = Math.abs(arr[i]);\n    while (num > 0) {\n      sum += num % 10;\n      num = Math.floor(num / 10);\n    }\n    if (sum > 0) {\n      count++;\n    }\n  }\n  return count;\n}\n", "prompt": "//Write a function count_nums which takes an array of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([])\n// 0\n// >>> count_nums([-1, 11, -11])\n// 1\n// >>> count_nums([1, 1, 2])\n// 3\nfunction count_nums(arr: number[]): number {\n", "wholecode": "//Write a function count_nums which takes an array of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([])\n// 0\n// >>> count_nums([-1, 11, -11])\n// 1\n// >>> count_nums([1, 1, 2])\n// 3\nfunction count_nums(arr: number[]): number {\n  let count = 0;\n  for (let i = 0; i < arr.length; i++) {\n    let sum = 0;\n    let num = Math.abs(arr[i]);\n    while (num > 0) {\n      sum += num % 10;\n      num = Math.floor(num / 10);\n    }\n    if (sum > 0) {\n      count++;\n    }\n  }\n  return count;\n}\n\nconsole.log(count_nums([]));\nconsole.log(count_nums([-1, 11, -11]));\nconsole.log(count_nums([1, 1, 2]));"}
{"task_id": "HumanEval_86_anti_shuffle", "generation": "//Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunction anti_shuffle(s: string): string {\n\n  return s\n    .split(\" \")\n    .map((word) => word.split(\"\").sort().join(\"\"))\n    .join(\" \");\n}", "prompt": "//Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunction anti_shuffle(s: string): string {\n", "wholecode": "//Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunction anti_shuffle(s: string): string {\n  return s\n    .split(\" \")\n    .map((word) => word.split(\"\").sort().join(\"\"))\n    .join(\" \");\n}"}
{"task_id": "HumanEval_48_is_palindrome", "generation": "//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n", "prompt": "//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n", "wholecode": "//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n\n//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n\n//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n\n//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n\n//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n\n//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let reversedText = text.split(\"\").reverse().join(\"\");\n    return text === reversedText;\n}\n\n//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text: string): boolean {\n    let"}
{"task_id": "HumanEval_118_get_closest_vowel", "generation": "//You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunction get_closest_vowel(word: string): string {\n\n    const vowels = 'aeiouAEIOU';\n    let closestVowel = '';\n    let closestDistance = Infinity;\n  \n    for (let i = word.length - 1; i >= 0; i--) {\n      if (vowels.includes(word[i])) {\n        for (let j = i - 1; j >= 0; j--) {\n          if (!vowels.includes(word[j])) {\n            const distance = i - j;\n            if (distance < closestDistance) {\n              closestDistance = distance;\n              closestVowel = word[i];\n            }\n            break;\n          }\n        }\n      }\n    }\n  \n    return closestVowel;\n  }", "prompt": "//You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunction get_closest_vowel(word: string): string {\n", "wholecode": "//You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunction get_closest_vowel(word: string): string {\n    const vowels = 'aeiouAEIOU';\n    let closestVowel = '';\n    let closestDistance = Infinity;\n  \n    for (let i = word.length - 1; i >= 0; i--) {\n      if (vowels.includes(word[i])) {\n        for (let j = i - 1; j >= 0; j--) {\n          if (!vowels.includes(word[j])) {\n            const distance = i - j;\n            if (distance < closestDistance) {\n              closestDistance = distance;\n              closestVowel = word[i];\n            }\n            break;\n          }\n        }\n      }\n    }\n  \n    return closestVowel;\n  }"}
{"task_id": "HumanEval_31_is_prime", "generation": "//Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunction is_prime(n: number): boolean {\n\n    if (n <= 1) {\n        return false;\n    }\n    for (let i = 2; i <= Math.sqrt(n); i++) {\n        if (n % i === 0) {\n            return false;\n        }\n    }\n    return true;\n}\n", "prompt": "//Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunction is_prime(n: number): boolean {\n", "wholecode": "//Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunction is_prime(n: number): boolean {\n    if (n <= 1) {\n        return false;\n    }\n    for (let i = 2; i <= Math.sqrt(n); i++) {\n        if (n % i === 0) {\n            return false;\n        }\n    }\n    return true;\n}\n\nconsole.log(is_prime(6)); // false\nconsole.log(is_prime(101)); // true\nconsole.log(is_prime(11)); // true\nconsole.log(is_prime(13441)); // true\nconsole.log(is_prime(61)); // true\nconsole.log(is_prime(4)); // false\nconsole.log(is_prime(1)); // false"}
{"task_id": "HumanEval_144_simplify", "generation": "//Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunction simplify(x: string, n: string): boolean {\n\n  const xArr = x.split(\"/\");\n  const nArr = n.split(\"/\");\n  const xNum = Number(xArr[0]);\n  const xDen = Number(xArr[1]);\n  const nNum = Number(nArr[0]);\n  const nDen = Number(nArr[1]);\n  const result = (xNum * nNum) / (xDen * nDen);\n  return result % 1 === 0;\n}", "prompt": "//Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunction simplify(x: string, n: string): boolean {\n", "wholecode": "//Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunction simplify(x: string, n: string): boolean {\n  const xArr = x.split(\"/\");\n  const nArr = n.split(\"/\");\n  const xNum = Number(xArr[0]);\n  const xDen = Number(xArr[1]);\n  const nNum = Number(nArr[0]);\n  const nDen = Number(nArr[1]);\n  const result = (xNum * nNum) / (xDen * nDen);\n  return result % 1 === 0;\n}"}
{"task_id": "HumanEval_78_hex_key", "generation": "//You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunction hex_key(num: string): number {\n\n    let count = 0;\n    for (let i = 0; i < num.length; i++) {\n        if (num[i] === \"2\" || num[i] === \"3\" || num[i] === \"5\" || num[i] === \"7\" || num[i] === \"B\" || num[i] === \"D\") {\n            count++;\n        }\n    }\n    return count;\n}", "prompt": "//You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunction hex_key(num: string): number {\n", "wholecode": "//You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunction hex_key(num: string): number {\n    let count = 0;\n    for (let i = 0; i < num.length; i++) {\n        if (num[i] === \"2\" || num[i] === \"3\" || num[i] === \"5\" || num[i] === \"7\" || num[i] === \"B\" || num[i] === \"D\") {\n            count++;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_143_words_in_sentence", "generation": "//You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunction words_in_sentence(sentence: string): string {\n\n    const words = sentence.split(\" \");\n    const primeWords = words.filter(word => isPrime(word.length));\n    return primeWords.join(\" \");\n}\n", "prompt": "//You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunction words_in_sentence(sentence: string): string {\n", "wholecode": "//You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunction words_in_sentence(sentence: string): string {\n    const words = sentence.split(\" \");\n    const primeWords = words.filter(word => isPrime(word.length));\n    return primeWords.join(\" \");\n}\n\nfunction isPrime(num: number): boolean {\n    if (num <= 1) return false;\n    for (let i = 2; i <= Math.sqrt(num); i++) {\n        if (num % i === 0) return false;\n    }\n    return true;\n}"}
{"task_id": "HumanEval_111_histogram", "generation": "//Given a string representing a space separated lowercase letters, return an object\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// {\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// {\"b\": 4}\n// >>> histogram(\"\")\n// {}\nfunction histogram(test: string): {[key: string]: number} {\n\n    let obj: {[key: string]: number} = {};\n    let arr: string[] = test.split(\" \");\n    for (let i = 0; i < arr.length; i++) {\n        if (obj[arr[i]] === undefined) {\n            obj[arr[i]] = 1;\n        } else {\n            obj[arr[i]]++;\n        }\n    }\n    return obj;\n}", "prompt": "//Given a string representing a space separated lowercase letters, return an object\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// {\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// {\"b\": 4}\n// >>> histogram(\"\")\n// {}\nfunction histogram(test: string): {[key: string]: number} {\n", "wholecode": "//Given a string representing a space separated lowercase letters, return an object\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// {\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// {\"b\": 4}\n// >>> histogram(\"\")\n// {}\nfunction histogram(test: string): {[key: string]: number} {\n    let obj: {[key: string]: number} = {};\n    let arr: string[] = test.split(\" \");\n    for (let i = 0; i < arr.length; i++) {\n        if (obj[arr[i]] === undefined) {\n            obj[arr[i]] = 1;\n        } else {\n            obj[arr[i]]++;\n        }\n    }\n    return obj;\n}"}
{"task_id": "HumanEval_87_get_row", "generation": "//You are given a 2 dimensional data, as a nested arrays,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the array,\n// and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n// each array is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n// [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n// >>> get_row([], 1)\n// []\n// >>> get_row([[], [1], [1, 2, 3]], 3)\n// [[2, 2]]\nfunction get_row(lst: number[][], x: number): [number, number][] {\n\n  const result: [number, number][] = [];\n  for (let i = 0; i < lst.length; i++) {\n    for (let j = 0; j < lst[i].length; j++) {\n      if (lst[i][j] === x) {\n        result.push([i, j]);\n      }\n    }\n  }\n  result.sort((a, b) => {\n    if (a[0] === b[0]) {\n      return b[1] - a[1];\n    } else {\n      return a[0] - b[0];\n    }\n  });\n  return result;\n}\n", "prompt": "//You are given a 2 dimensional data, as a nested arrays,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the array,\n// and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n// each array is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n// [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n// >>> get_row([], 1)\n// []\n// >>> get_row([[], [1], [1, 2, 3]], 3)\n// [[2, 2]]\nfunction get_row(lst: number[][], x: number): [number, number][] {\n", "wholecode": "//You are given a 2 dimensional data, as a nested arrays,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the array,\n// and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n// each array is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n// [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n// >>> get_row([], 1)\n// []\n// >>> get_row([[], [1], [1, 2, 3]], 3)\n// [[2, 2]]\nfunction get_row(lst: number[][], x: number): [number, number][] {\n  const result: [number, number][] = [];\n  for (let i = 0; i < lst.length; i++) {\n    for (let j = 0; j < lst[i].length; j++) {\n      if (lst[i][j] === x) {\n        result.push([i, j]);\n      }\n    }\n  }\n  result.sort((a, b) => {\n    if (a[0] === b[0]) {\n      return b[1] - a[1];\n    } else {\n      return a[0] - b[0];\n    }\n  });\n  return result;\n}\n\nconsole.log(get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1));\nconsole.log(get_row([], 1));\nconsole.log(get_row([[], [1], [1, 2, 3]], 3));"}
{"task_id": "HumanEval_123_get_odd_collatz", "generation": "//Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned array sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// [1, 5]\nfunction get_odd_collatz(n: number): number[] {\n\n    let arr: number[] = [n];\n    while (n !== 1) {\n        if (n % 2 === 0) {\n            n = n / 2;\n        } else {\n            n = 3 * n + 1;\n        }\n        arr.push(n);\n    }\n    return arr.filter((num) => num % 2 !== 0).sort((a, b) => a - b);\n}", "prompt": "//Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned array sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// [1, 5]\nfunction get_odd_collatz(n: number): number[] {\n", "wholecode": "//Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned array sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// [1, 5]\nfunction get_odd_collatz(n: number): number[] {\n    let arr: number[] = [n];\n    while (n !== 1) {\n        if (n % 2 === 0) {\n            n = n / 2;\n        } else {\n            n = 3 * n + 1;\n        }\n        arr.push(n);\n    }\n    return arr.filter((num) => num % 2 !== 0).sort((a, b) => a - b);\n}"}
{"task_id": "HumanEval_135_can_arrange", "generation": "//Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given array will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([1, 2, 4, 3, 5])\n// 3\n// >>> can_arrange([1, 2, 3])\n// -1\nfunction can_arrange(arr: number[]): number {\n\n  for (let i = 1; i < arr.length; i++) {\n    if (arr[i] < arr[i - 1]) {\n      return i;\n    }\n  }\n  return -1;\n}", "prompt": "//Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given array will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([1, 2, 4, 3, 5])\n// 3\n// >>> can_arrange([1, 2, 3])\n// -1\nfunction can_arrange(arr: number[]): number {\n", "wholecode": "//Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given array will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([1, 2, 4, 3, 5])\n// 3\n// >>> can_arrange([1, 2, 3])\n// -1\nfunction can_arrange(arr: number[]): number {\n  for (let i = 1; i < arr.length; i++) {\n    if (arr[i] < arr[i - 1]) {\n      return i;\n    }\n  }\n  return -1;\n}"}
{"task_id": "HumanEval_19_sort_numbers", "generation": "//Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunction sort_numbers(numbers: string): string {\n\n    const numberMap: { [key: string]: number } = {\n        zero: 0,\n        one: 1,\n        two: 2,\n        three: 3,\n        four: 4,\n        five: 5,\n        six: 6,\n        seven: 7,\n        eight: 8,\n        nine: 9\n    };\n\n    const numberArray: string[] = numbers.split(' ');\n    const sortedArray: string[] = numberArray.sort((a, b) => numberMap[a] - numberMap[b]);\n\n    return sortedArray.join(' ');\n}", "prompt": "//Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunction sort_numbers(numbers: string): string {\n", "wholecode": "//Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunction sort_numbers(numbers: string): string {\n    const numberMap: { [key: string]: number } = {\n        zero: 0,\n        one: 1,\n        two: 2,\n        three: 3,\n        four: 4,\n        five: 5,\n        six: 6,\n        seven: 7,\n        eight: 8,\n        nine: 9\n    };\n\n    const numberArray: string[] = numbers.split(' ');\n    const sortedArray: string[] = numberArray.sort((a, b) => numberMap[a] - numberMap[b]);\n\n    return sortedArray.join(' ');\n}"}
{"task_id": "HumanEval_65_circular_shift", "generation": "//Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunction circular_shift(x: number, shift: number): string {\n\n    let str = x.toString();\n    let len = str.length;\n    if (shift > len) {\n        return str.split('').reverse().join('');\n    }\n    let result = str.slice(-shift) + str.slice(0, len - shift);\n    return result;\n}\n", "prompt": "//Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunction circular_shift(x: number, shift: number): string {\n", "wholecode": "//Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunction circular_shift(x: number, shift: number): string {\n    let str = x.toString();\n    let len = str.length;\n    if (shift > len) {\n        return str.split('').reverse().join('');\n    }\n    let result = str.slice(-shift) + str.slice(0, len - shift);\n    return result;\n}\n\nconsole.log(circular_shift(12, 1));\nconsole.log(circular_shift(12, 2));"}
{"task_id": "HumanEval_142_sum_squares", "generation": "//\"\n// This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// [1, 2, 3]\n// >>> lst\n// []\n// >>> lst\n// [-1, -5, 2, -1, -5]\nfunction sum_squares(lst: number[]): number {\n\n    let sum: number = 0;\n    for (let i = 0; i < lst.length; i++) {\n        if (i % 3 === 0) {\n            sum += lst[i] ** 2;\n        } else if (i % 4 === 0) {\n            sum += lst[i] ** 3;\n        } else {\n            sum += lst[i];\n        }\n    }\n    return sum;\n}", "prompt": "//\"\n// This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// [1, 2, 3]\n// >>> lst\n// []\n// >>> lst\n// [-1, -5, 2, -1, -5]\nfunction sum_squares(lst: number[]): number {\n", "wholecode": "//\"\n// This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// [1, 2, 3]\n// >>> lst\n// []\n// >>> lst\n// [-1, -5, 2, -1, -5]\nfunction sum_squares(lst: number[]): number {\n    let sum: number = 0;\n    for (let i = 0; i < lst.length; i++) {\n        if (i % 3 === 0) {\n            sum += lst[i] ** 2;\n        } else if (i % 4 === 0) {\n            sum += lst[i] ** 3;\n        } else {\n            sum += lst[i];\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_94_skjkasdkd", "generation": "//You are given an array of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n// 10\n// >>> skjkasdkd([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n// 25\n// >>> skjkasdkd([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n// 13\n// >>> skjkasdkd([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n// 11\n// >>> skjkasdkd([0, 81, 12, 3, 1, 21])\n// 3\n// >>> skjkasdkd([0, 8, 1, 2, 1, 7])\n// 7\nfunction skjkasdkd(lst: number[]): number {\n\n  let maxPrime = 0;\n  for (let i = 0; i < lst.length; i++) {\n    if (isPrime(lst[i]) && lst[i] > maxPrime) {\n      maxPrime = lst[i];\n    }\n  }\n  return sumDigits(maxPrime);\n}", "prompt": "//You are given an array of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n// 10\n// >>> skjkasdkd([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n// 25\n// >>> skjkasdkd([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n// 13\n// >>> skjkasdkd([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n// 11\n// >>> skjkasdkd([0, 81, 12, 3, 1, 21])\n// 3\n// >>> skjkasdkd([0, 8, 1, 2, 1, 7])\n// 7\nfunction skjkasdkd(lst: number[]): number {\n", "wholecode": "//You are given an array of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n// 10\n// >>> skjkasdkd([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n// 25\n// >>> skjkasdkd([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n// 13\n// >>> skjkasdkd([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n// 11\n// >>> skjkasdkd([0, 81, 12, 3, 1, 21])\n// 3\n// >>> skjkasdkd([0, 8, 1, 2, 1, 7])\n// 7\nfunction skjkasdkd(lst: number[]): number {\n  let maxPrime = 0;\n  for (let i = 0; i < lst.length; i++) {\n    if (isPrime(lst[i]) && lst[i] > maxPrime) {\n      maxPrime = lst[i];\n    }\n  }\n  return sumDigits(maxPrime);\n}\nfunction isPrime(num: number): boolean {\n  if (num < 2) {\n    return false;\n  }\n  for (let i = 2; i <= Math.sqrt(num); i++) {\n    if (num % i === 0) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction sumDigits(num: number): number {\n  let sum = 0;\n  while (num > 0) {\n    sum += num % 10;\n    num = Math.floor(num / 10);\n  }\n  return sum;\n}"}
{"task_id": "HumanEval_8_sum_product", "generation": "//For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([])\n// [0, 1]\n// >>> sum_product([1, 2, 3, 4])\n// [10, 24]\nfunction sum_product(numbers: number[]): [number, number] {\n\n    let sum = 0;\n    let product = 1;\n    for (let i = 0; i < numbers.length; i++) {\n        sum += numbers[i];\n        product *= numbers[i];\n    }\n    return [sum, product];\n}", "prompt": "//For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([])\n// [0, 1]\n// >>> sum_product([1, 2, 3, 4])\n// [10, 24]\nfunction sum_product(numbers: number[]): [number, number] {\n", "wholecode": "//For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([])\n// [0, 1]\n// >>> sum_product([1, 2, 3, 4])\n// [10, 24]\nfunction sum_product(numbers: number[]): [number, number] {\n    let sum = 0;\n    let product = 1;\n    for (let i = 0; i < numbers.length; i++) {\n        sum += numbers[i];\n        product *= numbers[i];\n    }\n    return [sum, product];\n}"}
{"task_id": "HumanEval_102_choose_num", "generation": "//This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunction choose_num(x: number, y: number): number {\n\n    if (x > y) {\n        return -1;\n    }\n    for (let i = y; i >= x; i--) {\n        if (i % 2 === 0) {\n            return i;\n        }\n    }\n    return -1;\n}\n", "prompt": "//This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunction choose_num(x: number, y: number): number {\n", "wholecode": "//This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunction choose_num(x: number, y: number): number {\n    if (x > y) {\n        return -1;\n    }\n    for (let i = y; i >= x; i--) {\n        if (i % 2 === 0) {\n            return i;\n        }\n    }\n    return -1;\n}\n\nconsole.log(choose_num(12, 15));\nconsole.log(choose_num(13, 12));"}
{"task_id": "HumanEval_136_largest_smallest_integers", "generation": "//Create a function that returns an array (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in an array.\n// If there is no negative or positive integers, return them as undefined.\n// Examples:\n// >>> largest_smallest_integers([2, 4, 1, 3, 5, 7])\n// [undefined, 1]\n// >>> largest_smallest_integers([])\n// [undefined, undefined]\n// >>> largest_smallest_integers([0])\n// [undefined, undefined]\nfunction largest_smallest_integers(lst: number[]): [number | undefined, number | undefined] {\n\n    let largestNegative: number | undefined = undefined;\n    let smallestPositive: number | undefined = undefined;\n\n    for (const num of lst) {\n        if (num < 0) {\n            if (largestNegative === undefined || num > largestNegative) {\n                largestNegative = num;\n            }\n        } else if (num > 0) {\n            if (smallestPositive === undefined || num < smallestPositive) {\n                smallestPositive = num;\n            }\n        }\n    }\n\n    return [largestNegative, smallestPositive];\n}\n", "prompt": "//Create a function that returns an array (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in an array.\n// If there is no negative or positive integers, return them as undefined.\n// Examples:\n// >>> largest_smallest_integers([2, 4, 1, 3, 5, 7])\n// [undefined, 1]\n// >>> largest_smallest_integers([])\n// [undefined, undefined]\n// >>> largest_smallest_integers([0])\n// [undefined, undefined]\nfunction largest_smallest_integers(lst: number[]): [number | undefined, number | undefined] {\n", "wholecode": "//Create a function that returns an array (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in an array.\n// If there is no negative or positive integers, return them as undefined.\n// Examples:\n// >>> largest_smallest_integers([2, 4, 1, 3, 5, 7])\n// [undefined, 1]\n// >>> largest_smallest_integers([])\n// [undefined, undefined]\n// >>> largest_smallest_integers([0])\n// [undefined, undefined]\nfunction largest_smallest_integers(lst: number[]): [number | undefined, number | undefined] {\n    let largestNegative: number | undefined = undefined;\n    let smallestPositive: number | undefined = undefined;\n\n    for (const num of lst) {\n        if (num < 0) {\n            if (largestNegative === undefined || num > largestNegative) {\n                largestNegative = num;\n            }\n        } else if (num > 0) {\n            if (smallestPositive === undefined || num < smallestPositive) {\n                smallestPositive = num;\n            }\n        }\n    }\n\n    return [largestNegative, smallestPositive];\n}\n\nconsole.log(largest_smallest_integers([2, 4, 1, 3, 5, 7])); // [undefined, 1]\nconsole.log(largest_smallest_integers([])); // [undefined, undefined]\nconsole.log(largest_smallest_integers([0])); // [undefined, undefined]"}
{"task_id": "HumanEval_16_count_distinct_characters", "generation": "//Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunction count_distinct_characters(string: string): number {\n\n    let count = 0;\n    let charSet = new Set();\n    for (let i = 0; i < string.length; i++) {\n        let char = string[i].toLowerCase();\n        if (!charSet.has(char)) {\n            charSet.add(char);\n            count++;\n        }\n    }\n    return count;\n}\n", "prompt": "//Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunction count_distinct_characters(string: string): number {\n", "wholecode": "//Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunction count_distinct_characters(string: string): number {\n    let count = 0;\n    let charSet = new Set();\n    for (let i = 0; i < string.length; i++) {\n        let char = string[i].toLowerCase();\n        if (!charSet.has(char)) {\n            charSet.add(char);\n            count++;\n        }\n    }\n    return count;\n}\n\nconsole.log(count_distinct_characters(\"xyzXYZ\"));\nconsole.log(count_distinct_characters(\"Jerry\"));"}
{"task_id": "HumanEval_100_make_a_pile", "generation": "//Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in an array, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// [3, 5, 7]\nfunction make_a_pile(n: number): number[] {\n\n    let result: number[] = [];\n    let current: number = n;\n    while (current > 0) {\n        result.push(current);\n        if (current % 2 === 0) {\n            current += 2;\n        } else {\n            current += 1;\n        }\n    }\n    return result;\n}\n", "prompt": "//Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in an array, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// [3, 5, 7]\nfunction make_a_pile(n: number): number[] {\n", "wholecode": "//Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in an array, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// [3, 5, 7]\nfunction make_a_pile(n: number): number[] {\n    let result: number[] = [];\n    let current: number = n;\n    while (current > 0) {\n        result.push(current);\n        if (current % 2 === 0) {\n            current += 2;\n        } else {\n            current += 1;\n        }\n    }\n    return result;\n}\n\nconsole.log(make_a_pile(3));"}
{"task_id": "HumanEval_128_prod_signs", "generation": "//You are given an array arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the array, represented by 1, -1 or 0.\n// Note: return undefined for empty arr.\n// Example:\n// >>> prod_signs([1, 2, 2, -4])\n// 9\n// >>> prod_signs([0, 1])\n// 0\n// >>> prod_signs([])\n// undefined\nfunction prod_signs(arr: number[]): number | undefined {\n\n  if (arr.length === 0) return undefined;\n  let sum = 0;\n  let product = 1;\n  for (let i = 0; i < arr.length; i++) {\n    sum += Math.abs(arr[i]);\n    if (arr[i] > 0) product *= 1;\n    else if (arr[i] < 0) product *= -1;\n    else product *= 0;\n  }\n  return sum * product;\n}", "prompt": "//You are given an array arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the array, represented by 1, -1 or 0.\n// Note: return undefined for empty arr.\n// Example:\n// >>> prod_signs([1, 2, 2, -4])\n// 9\n// >>> prod_signs([0, 1])\n// 0\n// >>> prod_signs([])\n// undefined\nfunction prod_signs(arr: number[]): number | undefined {\n", "wholecode": "//You are given an array arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the array, represented by 1, -1 or 0.\n// Note: return undefined for empty arr.\n// Example:\n// >>> prod_signs([1, 2, 2, -4])\n// 9\n// >>> prod_signs([0, 1])\n// 0\n// >>> prod_signs([])\n// undefined\nfunction prod_signs(arr: number[]): number | undefined {\n  if (arr.length === 0) return undefined;\n  let sum = 0;\n  let product = 1;\n  for (let i = 0; i < arr.length; i++) {\n    sum += Math.abs(arr[i]);\n    if (arr[i] > 0) product *= 1;\n    else if (arr[i] < 0) product *= -1;\n    else product *= 0;\n  }\n  return sum * product;\n}"}
{"task_id": "HumanEval_114_minSubArraySum", "generation": "//Given an array of integers nums, find the minimum sum of any non-empty sub-array\n// of nums.\n// Example\n// >>> minSubArraySum([2, 3, 4, 1, 2, 4])\n// 1\n// >>> minSubArraySum([-1, -2, -3])\n// -6\nfunction minSubArraySum(nums: number[]): number {\n\n  let minSum = Infinity;\n  let currentSum = 0;\n\n  for (let i = 0; i < nums.length; i++) {\n    currentSum += nums[i];\n    minSum = Math.min(minSum, currentSum);\n    if (currentSum > 0) {\n      currentSum = 0;\n    }\n  }\n\n  return minSum;\n}", "prompt": "//Given an array of integers nums, find the minimum sum of any non-empty sub-array\n// of nums.\n// Example\n// >>> minSubArraySum([2, 3, 4, 1, 2, 4])\n// 1\n// >>> minSubArraySum([-1, -2, -3])\n// -6\nfunction minSubArraySum(nums: number[]): number {\n", "wholecode": "//Given an array of integers nums, find the minimum sum of any non-empty sub-array\n// of nums.\n// Example\n// >>> minSubArraySum([2, 3, 4, 1, 2, 4])\n// 1\n// >>> minSubArraySum([-1, -2, -3])\n// -6\nfunction minSubArraySum(nums: number[]): number {\n  let minSum = Infinity;\n  let currentSum = 0;\n\n  for (let i = 0; i < nums.length; i++) {\n    currentSum += nums[i];\n    minSum = Math.min(minSum, currentSum);\n    if (currentSum > 0) {\n      currentSum = 0;\n    }\n  }\n\n  return minSum;\n}"}
{"task_id": "HumanEval_15_string_sequence", "generation": "//Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunction string_sequence(n: number): string {\n\n    let result: string = \"\";\n    for (let i = 0; i <= n; i++) {\n        result += i + \" \";\n    }\n    return result.trim();\n}\n", "prompt": "//Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunction string_sequence(n: number): string {\n", "wholecode": "//Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunction string_sequence(n: number): string {\n    let result: string = \"\";\n    for (let i = 0; i <= n; i++) {\n        result += i + \" \";\n    }\n    return result.trim();\n}\n\nconsole.log(string_sequence(0));\nconsole.log(string_sequence(5));"}
{"task_id": "HumanEval_154_cycpattern_check", "generation": "//You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunction cycpattern_check(a: string, b: string): boolean {\n\n    let b1 = b + b;\n    return a.includes(b1.substring(1, b1.length - 1));\n}\n", "prompt": "//You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunction cycpattern_check(a: string, b: string): boolean {\n", "wholecode": "//You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunction cycpattern_check(a: string, b: string): boolean {\n    let b1 = b + b;\n    return a.includes(b1.substring(1, b1.length - 1));\n}\n\nconsole.log(cycpattern_check(\"abcd\", \"abd\"));\nconsole.log(cycpattern_check(\"hello\", \"ell\"));\nconsole.log(cycpattern_check(\"whassup\", \"psus\"));\nconsole.log(cycpattern_check(\"abab\", \"baa\"));\nconsole.log(cycpattern_check(\"efef\", \"eeff\"));\nconsole.log(cycpattern_check(\"himenss\", \"simen\"));"}
{"task_id": "HumanEval_57_monotonic", "generation": "//Return true is array elements are monotonically increasing or decreasing.\n// >>> monotonic([1, 2, 4, 20])\n// true\n// >>> monotonic([1, 20, 4, 10])\n// false\n// >>> monotonic([4, 1, 0, -10])\n// true\nfunction monotonic(l: number[]): boolean {\n\n  let isIncreasing = true;\n  let isDecreasing = true;\n\n  for (let i = 1; i < l.length; i++) {\n    if (l[i] > l[i - 1]) {\n      isDecreasing = false;\n    } else if (l[i] < l[i - 1]) {\n      isIncreasing = false;\n    }\n  }\n\n  return isIncreasing || isDecreasing;\n}\n", "prompt": "//Return true is array elements are monotonically increasing or decreasing.\n// >>> monotonic([1, 2, 4, 20])\n// true\n// >>> monotonic([1, 20, 4, 10])\n// false\n// >>> monotonic([4, 1, 0, -10])\n// true\nfunction monotonic(l: number[]): boolean {\n", "wholecode": "//Return true is array elements are monotonically increasing or decreasing.\n// >>> monotonic([1, 2, 4, 20])\n// true\n// >>> monotonic([1, 20, 4, 10])\n// false\n// >>> monotonic([4, 1, 0, -10])\n// true\nfunction monotonic(l: number[]): boolean {\n  let isIncreasing = true;\n  let isDecreasing = true;\n\n  for (let i = 1; i < l.length; i++) {\n    if (l[i] > l[i - 1]) {\n      isDecreasing = false;\n    } else if (l[i] < l[i - 1]) {\n      isIncreasing = false;\n    }\n  }\n\n  return isIncreasing || isDecreasing;\n}\n\nconsole.log(monotonic([1, 2, 4, 20]));\nconsole.log(monotonic([1, 20, 4, 10]));\nconsole.log(monotonic([4, 1, 0, -10]));"}
{"task_id": "HumanEval_12_longest", "generation": "//Out of array of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return undefined in case the input array is empty.\n// >>> longest([])\n// undefined\n// >>> longest([\"a\", \"b\", \"c\"])\n// \"a\"\n// >>> longest([\"a\", \"bb\", \"ccc\"])\n// \"ccc\"\nfunction longest(strings: string[]): string | undefined {\n\n    if (strings.length === 0) {\n        return undefined;\n    }\n    let longestString = strings[0];\n    for (let i = 1; i < strings.length; i++) {\n        if (strings[i].length > longestString.length) {\n            longestString = strings[i];\n        }\n    }\n    return longestString;\n}\n", "prompt": "//Out of array of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return undefined in case the input array is empty.\n// >>> longest([])\n// undefined\n// >>> longest([\"a\", \"b\", \"c\"])\n// \"a\"\n// >>> longest([\"a\", \"bb\", \"ccc\"])\n// \"ccc\"\nfunction longest(strings: string[]): string | undefined {\n", "wholecode": "//Out of array of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return undefined in case the input array is empty.\n// >>> longest([])\n// undefined\n// >>> longest([\"a\", \"b\", \"c\"])\n// \"a\"\n// >>> longest([\"a\", \"bb\", \"ccc\"])\n// \"ccc\"\nfunction longest(strings: string[]): string | undefined {\n    if (strings.length === 0) {\n        return undefined;\n    }\n    let longestString = strings[0];\n    for (let i = 1; i < strings.length; i++) {\n        if (strings[i].length > longestString.length) {\n            longestString = strings[i];\n        }\n    }\n    return longestString;\n}\n\nconsole.log(longest([]));\nconsole.log(longest([\"a\", \"b\", \"c\"]));\nconsole.log(longest([\"a\", \"bb\", \"ccc\"]));"}
{"task_id": "HumanEval_52_below_threshold", "generation": "//Return true if all numbers in the array l are below threshold t.\n// >>> below_threshold([1, 2, 4, 10], 100)\n// true\n// >>> below_threshold([1, 20, 4, 10], 5)\n// false\nfunction below_threshold(l: number[], t: number): boolean {\n\n    return l.every((num) => num < t);\n}\n", "prompt": "//Return true if all numbers in the array l are below threshold t.\n// >>> below_threshold([1, 2, 4, 10], 100)\n// true\n// >>> below_threshold([1, 20, 4, 10], 5)\n// false\nfunction below_threshold(l: number[], t: number): boolean {\n", "wholecode": "//Return true if all numbers in the array l are below threshold t.\n// >>> below_threshold([1, 2, 4, 10], 100)\n// true\n// >>> below_threshold([1, 20, 4, 10], 5)\n// false\nfunction below_threshold(l: number[], t: number): boolean {\n    return l.every((num) => num < t);\n}\n\nconsole.log(below_threshold([1, 2, 4, 10], 100));\nconsole.log(below_threshold([1, 20, 4, 10], 5));"}
{"task_id": "HumanEval_75_is_multiply_prime", "generation": "//Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunction is_multiply_prime(a: number): boolean {\n\n    let count = 0;\n    for (let i = 2; i <= a; i++) {\n        if (a % i === 0) {\n            count++;\n            a /= i;\n            i--;\n        }\n    }\n    return count === 3;\n}", "prompt": "//Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunction is_multiply_prime(a: number): boolean {\n", "wholecode": "//Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunction is_multiply_prime(a: number): boolean {\n    let count = 0;\n    for (let i = 2; i <= a; i++) {\n        if (a % i === 0) {\n            count++;\n            a /= i;\n            i--;\n        }\n    }\n    return count === 3;\n}\nconsole.log(is_multiply_prime(30));"}
{"task_id": "HumanEval_30_get_positive", "generation": "//Return only positive numbers in the array.\n// >>> get_positive([-1, 2, -4, 5, 6])\n// [2, 5, 6]\n// >>> get_positive([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, 2, 3, 9, 123, 1]\nfunction get_positive(l: number[]): number[] {\n\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (l[i] > 0) {\n            result.push(l[i]);\n        }\n    }\n    return result;\n}\n", "prompt": "//Return only positive numbers in the array.\n// >>> get_positive([-1, 2, -4, 5, 6])\n// [2, 5, 6]\n// >>> get_positive([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, 2, 3, 9, 123, 1]\nfunction get_positive(l: number[]): number[] {\n", "wholecode": "//Return only positive numbers in the array.\n// >>> get_positive([-1, 2, -4, 5, 6])\n// [2, 5, 6]\n// >>> get_positive([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, 2, 3, 9, 123, 1]\nfunction get_positive(l: number[]): number[] {\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (l[i] > 0) {\n            result.push(l[i]);\n        }\n    }\n    return result;\n}\n\n//Return only negative numbers in the array.\n// >>> get_negative([-1, 2, -4, 5, 6])\n// [-1, -4]\n// >>> get_negative([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [-5, -3, -10]\nfunction get_negative(l: number[]): number[] {\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (l[i] < 0) {\n            result.push(l[i]);\n        }\n    }\n    return result;\n}\n\n//Return only even numbers in the array.\n// >>> get_even([1, 2, 3, 4, 5, 6])\n// [2, 4, 6]\n// >>> get_even([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [2, 0, -10]\nfunction get_even(l: number[]): number[] {\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (l[i] % 2 == 0) {\n            result.push(l[i]);\n        }\n    }\n    return result;\n}\n\n//Return only odd numbers in the array.\n// >>> get_odd([1, 2, 3, 4, 5, 6])\n// [1, 3, 5]\n// >>> get_odd([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, -5, -3, 3, 9, 123, 1]\nfunction get_odd(l: number[]): number[] {\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if ("}
{"task_id": "HumanEval_33_sort_third", "generation": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n// [2, 6, 3, 4, 8, 9, 5]\nfunction sort_third(l: number[]): number[] {\n\n    let l1: number[] = [];\n    let l2: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 3 == 0) {\n            l1.push(l[i]);\n        } else {\n            l2.push(l[i]);\n        }\n    }\n    l1.sort((a, b) => a - b);\n    let l3: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 3 == 0) {\n            l3.push(l1.shift());\n        } else {\n            l3.push(l2.shift());\n        }\n    }\n    return l3;\n}", "prompt": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n// [2, 6, 3, 4, 8, 9, 5]\nfunction sort_third(l: number[]): number[] {\n", "wholecode": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n// [2, 6, 3, 4, 8, 9, 5]\nfunction sort_third(l: number[]): number[] {\n    let l1: number[] = [];\n    let l2: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 3 == 0) {\n            l1.push(l[i]);\n        } else {\n            l2.push(l[i]);\n        }\n    }\n    l1.sort((a, b) => a - b);\n    let l3: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 3 == 0) {\n            l3.push(l1.shift());\n        } else {\n            l3.push(l2.shift());\n        }\n    }\n    return l3;\n}"}
{"task_id": "HumanEval_6_parse_nested_parens", "generation": "//Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// [2, 3, 1, 3]\nfunction parse_nested_parens(paren_string: string): number[] {\n\n  const groups = paren_string.split(\" \");\n  const depths = groups.map((group) => {\n    let maxDepth = 0;\n    let currentDepth = 0;\n    for (let i = 0; i < group.length; i++) {\n      if (group[i] === \"(\") {\n        currentDepth++;\n        maxDepth = Math.max(maxDepth, currentDepth);\n      } else if (group[i] === \")\") {\n        currentDepth--;\n      }\n    }\n    return maxDepth;\n  });\n  return depths;\n}\n", "prompt": "//Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// [2, 3, 1, 3]\nfunction parse_nested_parens(paren_string: string): number[] {\n", "wholecode": "//Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// [2, 3, 1, 3]\nfunction parse_nested_parens(paren_string: string): number[] {\n  const groups = paren_string.split(\" \");\n  const depths = groups.map((group) => {\n    let maxDepth = 0;\n    let currentDepth = 0;\n    for (let i = 0; i < group.length; i++) {\n      if (group[i] === \"(\") {\n        currentDepth++;\n        maxDepth = Math.max(maxDepth, currentDepth);\n      } else if (group[i] === \")\") {\n        currentDepth--;\n      }\n    }\n    return maxDepth;\n  });\n  return depths;\n}\n\nconsole.log(parse_nested_parens(\"(()()) ((())) () ((())()())\"));"}
{"task_id": "HumanEval_45_triangle_area", "generation": "//Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunction triangle_area(a: number, h: number): number {\n\n    return (a * h) / 2;\n}\n", "prompt": "//Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunction triangle_area(a: number, h: number): number {\n", "wholecode": "//Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunction triangle_area(a: number, h: number): number {\n    return (a * h) / 2;\n}\n\nconsole.log(triangle_area(5, 3));"}
{"task_id": "HumanEval_97_multiply", "generation": "//Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunction multiply(a: number, b: number): number {\n\n    return (a % 10) * (b % 10);\n  }", "prompt": "//Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunction multiply(a: number, b: number): number {\n", "wholecode": "//Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunction multiply(a: number, b: number): number {\n    return (a % 10) * (b % 10);\n  }"}
{"task_id": "HumanEval_4_mean_absolute_deviation", "generation": "//For a given array of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n// 1.0\nfunction mean_absolute_deviation(numbers: number[]): number {\n\n  const mean = numbers.reduce((a, b) => a + b) / numbers.length;\n  const absoluteDifferences = numbers.map((num) => Math.abs(num - mean));\n  const mad = absoluteDifferences.reduce((a, b) => a + b) / numbers.length;\n  return mad;\n}\n", "prompt": "//For a given array of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n// 1.0\nfunction mean_absolute_deviation(numbers: number[]): number {\n", "wholecode": "//For a given array of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n// 1.0\nfunction mean_absolute_deviation(numbers: number[]): number {\n  const mean = numbers.reduce((a, b) => a + b) / numbers.length;\n  const absoluteDifferences = numbers.map((num) => Math.abs(num - mean));\n  const mad = absoluteDifferences.reduce((a, b) => a + b) / numbers.length;\n  return mad;\n}\n\nconsole.log(mean_absolute_deviation([1.0, 2.0, 3.0, 4.0]));"}
{"task_id": "HumanEval_58_common", "generation": "//Return sorted unique common elements for two arrays.\n// >>> common([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n// [1, 5, 653]\n// >>> common([5, 3, 2, 8], [3, 2])\n// [2, 3]\nfunction common(l1: number[], l2: number[]): number[] {\n\n  const set1 = new Set(l1);\n  const set2 = new Set(l2);\n  const result = new Set<number>();\n  for (const item of set1) {\n    if (set2.has(item)) {\n      result.add(item);\n    }\n  }\n  return Array.from(result).sort((a, b) => a - b);\n}", "prompt": "//Return sorted unique common elements for two arrays.\n// >>> common([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n// [1, 5, 653]\n// >>> common([5, 3, 2, 8], [3, 2])\n// [2, 3]\nfunction common(l1: number[], l2: number[]): number[] {\n", "wholecode": "//Return sorted unique common elements for two arrays.\n// >>> common([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n// [1, 5, 653]\n// >>> common([5, 3, 2, 8], [3, 2])\n// [2, 3]\nfunction common(l1: number[], l2: number[]): number[] {\n  const set1 = new Set(l1);\n  const set2 = new Set(l2);\n  const result = new Set<number>();\n  for (const item of set1) {\n    if (set2.has(item)) {\n      result.add(item);\n    }\n  }\n  return Array.from(result).sort((a, b) => a - b);\n}"}
{"task_id": "HumanEval_156_int_to_mini_roman", "generation": "//Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunction int_to_mini_roman(number: number): string {\n\n    const romanNumerals: { [key: number]: string } = {\n        1: \"i\",\n        4: \"iv\",\n        5: \"v\",\n        9: \"ix\",\n        10: \"x\",\n        40: \"xl\",\n        50: \"l\",\n        90: \"xc\",\n        100: \"c\",\n        400: \"cd\",\n        500: \"d\",\n        900: \"cm\",\n        1000: \"m\",\n    };\n\n    let result = \"\";\n    let num = number;\n\n    for (const value of Object.keys(romanNumerals).reverse()) {\n        const numeral = romanNumerals[parseInt(value)];\n        while (num >= parseInt(value)) {\n            result += numeral;\n            num -= parseInt(value);\n        }\n    }\n\n    return result;\n}\n", "prompt": "//Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunction int_to_mini_roman(number: number): string {\n", "wholecode": "//Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunction int_to_mini_roman(number: number): string {\n    const romanNumerals: { [key: number]: string } = {\n        1: \"i\",\n        4: \"iv\",\n        5: \"v\",\n        9: \"ix\",\n        10: \"x\",\n        40: \"xl\",\n        50: \"l\",\n        90: \"xc\",\n        100: \"c\",\n        400: \"cd\",\n        500: \"d\",\n        900: \"cm\",\n        1000: \"m\",\n    };\n\n    let result = \"\";\n    let num = number;\n\n    for (const value of Object.keys(romanNumerals).reverse()) {\n        const numeral = romanNumerals[parseInt(value)];\n        while (num >= parseInt(value)) {\n            result += numeral;\n            num -= parseInt(value);\n        }\n    }\n\n    return result;\n}\n\nconsole.log(int_to_mini_roman(19));\nconsole.log(int_to_mini_roman(152));\nconsole.log(int_to_mini_roman(426));"}
{"task_id": "HumanEval_67_fruit_distribution", "generation": "//In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunction fruit_distribution(s: string, n: number): number {\n\n    const [apples, oranges] = s.match(/\\d+/g).map(Number);\n    return n - apples - oranges;\n  }\n  \n  console.log(fruit_distribution(\"5 apples and 6 oranges\", 19)); // 8\n  console.log(fruit_distribution(\"0 apples and 1 oranges\", 3)); // 2\n  console.log(fruit_distribution(\"2 apples and 3 oranges\", 100)); // 95\n  console.log(fruit_distribution(\"100 apples and 1 oranges\", 120)); // 19", "prompt": "//In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunction fruit_distribution(s: string, n: number): number {\n", "wholecode": "//In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunction fruit_distribution(s: string, n: number): number {\n    const [apples, oranges] = s.match(/\\d+/g).map(Number);\n    return n - apples - oranges;\n  }\n  \n  console.log(fruit_distribution(\"5 apples and 6 oranges\", 19)); // 8\n  console.log(fruit_distribution(\"0 apples and 1 oranges\", 3)); // 2\n  console.log(fruit_distribution(\"2 apples and 3 oranges\", 100)); // 95\n  console.log(fruit_distribution(\"100 apples and 1 oranges\", 120)); // 19"}
{"task_id": "HumanEval_112_reverse_delete", "generation": "//Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return an array containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// [\"bcd\", false]\n// >>> reverse_delete(\"abcdef\", \"b\")\n// [\"acdef\", false]\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// [\"cdedc\", true]\nfunction reverse_delete(s: string, c: string): [string, boolean] {\n\n  let result = s.split(\"\").filter((char) => !c.includes(char)).join(\"\");\n  return [result, result === result.split(\"\").reverse().join(\"\")];\n}", "prompt": "//Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return an array containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// [\"bcd\", false]\n// >>> reverse_delete(\"abcdef\", \"b\")\n// [\"acdef\", false]\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// [\"cdedc\", true]\nfunction reverse_delete(s: string, c: string): [string, boolean] {\n", "wholecode": "//Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return an array containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// [\"bcd\", false]\n// >>> reverse_delete(\"abcdef\", \"b\")\n// [\"acdef\", false]\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// [\"cdedc\", true]\nfunction reverse_delete(s: string, c: string): [string, boolean] {\n  let result = s.split(\"\").filter((char) => !c.includes(char)).join(\"\");\n  return [result, result === result.split(\"\").reverse().join(\"\")];\n}"}
{"task_id": "HumanEval_13_greatest_common_divisor", "generation": "//Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunction greatest_common_divisor(a: number, b: number): number {\n\n  if (b === 0) {\n    return a;\n  } else {\n    return greatest_common_divisor(b, a % b);\n  }\n}\n", "prompt": "//Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunction greatest_common_divisor(a: number, b: number): number {\n", "wholecode": "//Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunction greatest_common_divisor(a: number, b: number): number {\n  if (b === 0) {\n    return a;\n  } else {\n    return greatest_common_divisor(b, a % b);\n  }\n}\n\nconsole.log(greatest_common_divisor(3, 5));\nconsole.log(greatest_common_divisor(25, 15));"}
{"task_id": "HumanEval_125_split_words", "generation": "//Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words(\"Hello world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"Hello,world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"abcdef\")\n// 3\nfunction split_words(txt: string): string[]| number {\n\n    if (txt.includes(' ')) {\n        return txt.split(' ');\n    } else if (txt.includes(',')) {\n        return txt.split(',');\n    } else {\n        let count = 0;\n        for (let i = 0; i < txt.length; i++) {\n            if (txt[i] === txt[i].toLowerCase() && i % 2 === 1) {\n                count++;\n            }\n        }\n        return count;\n    }\n}\n", "prompt": "//Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words(\"Hello world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"Hello,world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"abcdef\")\n// 3\nfunction split_words(txt: string): string[]| number {\n", "wholecode": "//Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words(\"Hello world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"Hello,world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"abcdef\")\n// 3\nfunction split_words(txt: string): string[]| number {\n    if (txt.includes(' ')) {\n        return txt.split(' ');\n    } else if (txt.includes(',')) {\n        return txt.split(',');\n    } else {\n        let count = 0;\n        for (let i = 0; i < txt.length; i++) {\n            if (txt[i] === txt[i].toLowerCase() && i % 2 === 1) {\n                count++;\n            }\n        }\n        return count;\n    }\n}\n\nconsole.log(split_words(\"Hello world!\"));\nconsole.log(split_words(\"Hello,world!\"));\nconsole.log(split_words(\"abcdef\"));"}
{"task_id": "HumanEval_116_sort_array", "generation": "//In this Kata, you have to sort an array of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([1, 5, 2, 3, 4])\n// [1, 2, 3, 4, 5]\n// >>> sort_array([-2, -3, -4, -5, -6])\n// [-6, -5, -4, -3, -2]\n// >>> sort_array([1, 0, 2, 3, 4])\n// [0, 1, 2, 3, 4]\nfunction sort_array(arr: number[]): number[] {\n\n  return arr.sort((a, b) => {\n    const aBinary = a.toString(2);\n    const bBinary = b.toString(2);\n    const aOnes = aBinary.split(\"\").filter((x) => x === \"1\").length;\n    const bOnes = bBinary.split(\"\").filter((x) => x === \"1\").length;\n    if (aOnes === bOnes) {\n      return a - b;\n    } else {\n      return aOnes - bOnes;\n    }\n  });\n}", "prompt": "//In this Kata, you have to sort an array of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([1, 5, 2, 3, 4])\n// [1, 2, 3, 4, 5]\n// >>> sort_array([-2, -3, -4, -5, -6])\n// [-6, -5, -4, -3, -2]\n// >>> sort_array([1, 0, 2, 3, 4])\n// [0, 1, 2, 3, 4]\nfunction sort_array(arr: number[]): number[] {\n", "wholecode": "//In this Kata, you have to sort an array of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([1, 5, 2, 3, 4])\n// [1, 2, 3, 4, 5]\n// >>> sort_array([-2, -3, -4, -5, -6])\n// [-6, -5, -4, -3, -2]\n// >>> sort_array([1, 0, 2, 3, 4])\n// [0, 1, 2, 3, 4]\nfunction sort_array(arr: number[]): number[] {\n  return arr.sort((a, b) => {\n    const aBinary = a.toString(2);\n    const bBinary = b.toString(2);\n    const aOnes = aBinary.split(\"\").filter((x) => x === \"1\").length;\n    const bOnes = bBinary.split(\"\").filter((x) => x === \"1\").length;\n    if (aOnes === bOnes) {\n      return a - b;\n    } else {\n      return aOnes - bOnes;\n    }\n  });\n}"}
{"task_id": "HumanEval_28_concatenate", "generation": "//Concatenate array of strings into a single string\n// >>> concatenate([])\n// \"\"\n// >>> concatenate([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction concatenate(strings: string[]): string {\n\n  return strings.join(\"\");\n}\n", "prompt": "//Concatenate array of strings into a single string\n// >>> concatenate([])\n// \"\"\n// >>> concatenate([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction concatenate(strings: string[]): string {\n", "wholecode": "//Concatenate array of strings into a single string\n// >>> concatenate([])\n// \"\"\n// >>> concatenate([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction concatenate(strings: string[]): string {\n  return strings.join(\"\");\n}\n\n//Convert a string to an array of characters\n// >>> toArray(\"abc\")\n// [\"a\", \"b\", \"c\"]\nfunction toArray(string: string): string[] {\n  return string.split(\"\");\n}\n\n//Convert an array of characters to a string\n// >>> toStr([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction toStr(chars: string[]): string {\n  return chars.join(\"\");\n}\n\n//Reverse a string\n// >>> reverse(\"abc\")\n// \"cba\"\nfunction reverse(string: string): string {\n  return string.split(\"\").reverse().join(\"\");\n}\n\n//Check if a string is a palindrome\n// >>> isPalindrome(\"racecar\")\n// true\n// >>> isPalindrome(\"hello\")\n// false\nfunction isPalindrome(string: string): boolean {\n  return string === reverse(string);\n}\n\n//Count the number of vowels in a string\n// >>> countVowels(\"hello\")\n// 2\nfunction countVowels(string: string): number {\n  const vowels = \"aeiouAEIOU\";\n  let count = 0;\n  for (let i = 0; i < string.length; i++) {\n    if (vowels.includes(string[i])) {\n      count++;\n    }\n  }\n  return count;\n}\n\n//Count the number of consonants in a string\n// >>> countConsonants(\"hello\")\n// 3\nfunction countConsonants(string: string): number {\n  const consonants = \"bcdfghjklmnpqrstvwxyzBCDFGHJKLMNPQRSTVWXYZ\";\n  let count = 0;\n  for (let i = 0; i < string.length; i++) {\n    if (consonants.includes(string[i])) {\n      count++;\n    }\n  }\n  return count;\n}\n\n//Count the number of words in a string\n// >>> countWords(\"hello world\")\n// 2\nfunction countWords(string: string): number {\n  return string.split(\" \").length;\n}\n\n//Count the number of occurrences of a substring in a string\n// >>> countOccurrences(\"hello world\", \"l\")\n// 3\nfunction countOccurrences(string: string, substring: string): number {\n  let count = 0;\n  for (let i = 0; i < string.length; i++) {\n    if (string.slice(i, i + substring.length) === substring) {\n      count++;\n    }\n  }\n  return count;\n"}
{"task_id": "HumanEval_149_sorted_list_sum", "generation": "//Write a function that accepts an array of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted array with a sorted order,\n// The array is always an array of strings and never an array of numbers,\n// and it may contain duplicates.\n// The order of the array should be ascending by length of each word, and you\n// should return the array sorted by that rule.\n// If two words have the same length, sort the array alphabetically.\n// The function should return an array of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([\"aa\", \"a\", \"aaa\"])\n// [\"aa\"]\n// >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n// [\"ab\", \"cd\"]\nfunction sorted_list_sum(lst: string[]): string[] {\n\n  const sortedList = lst.sort((a, b) => a.length - b.length);\n  const filteredList = sortedList.filter((word) => word.length % 2 === 0);\n  return filteredList;\n}", "prompt": "//Write a function that accepts an array of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted array with a sorted order,\n// The array is always an array of strings and never an array of numbers,\n// and it may contain duplicates.\n// The order of the array should be ascending by length of each word, and you\n// should return the array sorted by that rule.\n// If two words have the same length, sort the array alphabetically.\n// The function should return an array of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([\"aa\", \"a\", \"aaa\"])\n// [\"aa\"]\n// >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n// [\"ab\", \"cd\"]\nfunction sorted_list_sum(lst: string[]): string[] {\n", "wholecode": "//Write a function that accepts an array of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted array with a sorted order,\n// The array is always an array of strings and never an array of numbers,\n// and it may contain duplicates.\n// The order of the array should be ascending by length of each word, and you\n// should return the array sorted by that rule.\n// If two words have the same length, sort the array alphabetically.\n// The function should return an array of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([\"aa\", \"a\", \"aaa\"])\n// [\"aa\"]\n// >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n// [\"ab\", \"cd\"]\nfunction sorted_list_sum(lst: string[]): string[] {\n  const sortedList = lst.sort((a, b) => a.length - b.length);\n  const filteredList = sortedList.filter((word) => word.length % 2 === 0);\n  return filteredList;\n}\nconsole.log(sorted_list_sum([\"aa\", \"a\", \"aaa\"]));\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\"]));"}
{"task_id": "HumanEval_7_filter_by_substring", "generation": "//Filter an input array of strings only for ones that contain given substring\n// >>> filter_by_substring([], \"a\")\n// []\n// >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"bacd\", \"array\"]\nfunction filter_by_substring(strings: string[], substring: string): string[] {\n\n    return strings.filter((string) => string.includes(substring));\n}\n", "prompt": "//Filter an input array of strings only for ones that contain given substring\n// >>> filter_by_substring([], \"a\")\n// []\n// >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"bacd\", \"array\"]\nfunction filter_by_substring(strings: string[], substring: string): string[] {\n", "wholecode": "//Filter an input array of strings only for ones that contain given substring\n// >>> filter_by_substring([], \"a\")\n// []\n// >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"bacd\", \"array\"]\nfunction filter_by_substring(strings: string[], substring: string): string[] {\n    return strings.filter((string) => string.includes(substring));\n}\n\n//Find the first element in an array that satisfies a given predicate\n// >>> find_first([1, 2, 3, 4, 5], (n) => n % 2 === 0)\n// 2\n// >>> find_first([1, 3, 5], (n) => n % 2 === 0)\n// undefined\nfunction find_first<T>(array: T[], predicate: (element: T) => boolean): T | undefined {\n    return array.find(predicate);\n}\n\n//Find the last element in an array that satisfies a given predicate\n// >>> find_last([1, 2, 3, 4, 5], (n) => n % 2 === 0)\n// 4\n// >>> find_last([1, 3, 5], (n) => n % 2 === 0)\n// undefined\nfunction find_last<T>(array: T[], predicate: (element: T) => boolean): T | undefined {\n    return array.reverse().find(predicate);\n}\n\n//Find the first index of an element in an array that satisfies a given predicate\n// >>> find_index([1, 2, 3, 4, 5], (n) => n % 2 === 0)\n// 1\n// >>> find_index([1, 3, 5], (n) => n % 2 === 0)\n// -1\nfunction find_index<T>(array: T[], predicate: (element: T) => boolean): number {\n    return array.findIndex(predicate);\n}\n\n//Find the last index of an element in an array that satisfies a given predicate\n// >>> find_last_index([1, 2, 3, 4, 5], (n) => n % 2 === 0)\n// 3\n// >>> find_last_index([1, 3, 5], (n) => n % 2 === 0)\n// -1\nfunction find_last_index<T>(array: T[], predicate: (element: T) => boolean): number {\n    return array.reverse().findIndex(predicate);\n}\n\n//Find the first element in an array that satisfies a given predicate\n// >>> find_first([1, 2, 3, 4, 5], (n) => n % 2 === 0)\n//"}
{"task_id": "HumanEval_99_closest_integer", "generation": "//Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunction closest_integer(value: string): number {\n\n  const num = parseFloat(value);\n  const rounded = Math.round(num);\n  if (num - rounded === 0.5) {\n    return num > 0 ? rounded + 1 : rounded - 1;\n  }\n  return rounded;\n}", "prompt": "//Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunction closest_integer(value: string): number {\n", "wholecode": "//Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunction closest_integer(value: string): number {\n  const num = parseFloat(value);\n  const rounded = Math.round(num);\n  if (num - rounded === 0.5) {\n    return num > 0 ? rounded + 1 : rounded - 1;\n  }\n  return rounded;\n}"}
{"task_id": "HumanEval_64_vowels_count", "generation": "//Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunction vowels_count(s: string): number {\n\n  let count = 0;\n  for (let i = 0; i < s.length; i++) {\n    if (s[i] === \"a\" || s[i] === \"e\" || s[i] === \"i\" || s[i] === \"o\" || s[i] === \"u\") {\n      count++;\n    }\n  }\n  if (s[s.length - 1] === \"y\") {\n    count++;\n  }\n  return count;\n}", "prompt": "//Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunction vowels_count(s: string): number {\n", "wholecode": "//Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunction vowels_count(s: string): number {\n  let count = 0;\n  for (let i = 0; i < s.length; i++) {\n    if (s[i] === \"a\" || s[i] === \"e\" || s[i] === \"i\" || s[i] === \"o\" || s[i] === \"u\") {\n      count++;\n    }\n  }\n  if (s[s.length - 1] === \"y\") {\n    count++;\n  }\n  return count;\n}"}
{"task_id": "HumanEval_158_find_max", "generation": "//Write a function that accepts an array of strings.\n// The array contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([\"name\", \"of\", \"string\"])\n// \"string\"\n// >>> find_max([\"name\", \"enam\", \"game\"])\n// \"enam\"\n// >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n// \"aaaaaaa\"\nfunction find_max(words: string[]): string {\n\n  let maxUniqueChars = 0;\n  let maxWord = \"\";\n\n  for (const word of words) {\n    const uniqueChars = new Set(word);\n    if (uniqueChars.size > maxUniqueChars) {\n      maxUniqueChars = uniqueChars.size;\n      maxWord = word;\n    } else if (uniqueChars.size === maxUniqueChars) {\n      if (word < maxWord) {\n        maxWord = word;\n      }\n    }\n  }\n\n  return maxWord;\n}\n", "prompt": "//Write a function that accepts an array of strings.\n// The array contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([\"name\", \"of\", \"string\"])\n// \"string\"\n// >>> find_max([\"name\", \"enam\", \"game\"])\n// \"enam\"\n// >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n// \"aaaaaaa\"\nfunction find_max(words: string[]): string {\n", "wholecode": "//Write a function that accepts an array of strings.\n// The array contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([\"name\", \"of\", \"string\"])\n// \"string\"\n// >>> find_max([\"name\", \"enam\", \"game\"])\n// \"enam\"\n// >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n// \"aaaaaaa\"\nfunction find_max(words: string[]): string {\n  let maxUniqueChars = 0;\n  let maxWord = \"\";\n\n  for (const word of words) {\n    const uniqueChars = new Set(word);\n    if (uniqueChars.size > maxUniqueChars) {\n      maxUniqueChars = uniqueChars.size;\n      maxWord = word;\n    } else if (uniqueChars.size === maxUniqueChars) {\n      if (word < maxWord) {\n        maxWord = word;\n      }\n    }\n  }\n\n  return maxWord;\n}\n\nconsole.log(find_max([\"name\", \"of\", \"string\"]));\nconsole.log(find_max([\"name\", \"enam\", \"game\"]));\nconsole.log(find_max([\"aaaaaaa\", \"bb\", \"cc\"]));"}
{"task_id": "HumanEval_162_string_to_md5", "generation": "//Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_md5(\"Hello world\")\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunction string_to_md5(text: string): string | undefined {\n\n    if (text === \"\") {\n        return undefined;\n    }\n    return md5(text);\n}\n", "prompt": "//Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_md5(\"Hello world\")\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunction string_to_md5(text: string): string | undefined {\n", "wholecode": "//Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_md5(\"Hello world\")\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunction string_to_md5(text: string): string | undefined {\n    if (text === \"\") {\n        return undefined;\n    }\n    return md5(text);\n}\n\n//Given a string 'text', return its sha256 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_sha256(\"Hello world\")\n// \"7509e5bda0c762d2bac7f90d758b5b2263fa01ccbc542ab5e3df163be08e6ca9\"\nfunction string_to_sha256(text: string): string | undefined {\n    if (text === \"\") {\n        return undefined;\n    }\n    return sha256(text);\n}\n\n//Given a string 'text', return its sha512 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_sha512(\"Hello world\")\n// \"2c74fd17edafd80e8447b0d46741ee243b7eb74dd2149a0ab1b9246fb30382f27e853d8585719e0e67cbda0daa8f51671064615d645ae27acb15bfb1447f459b\"\nfunction string_to_sha512(text: string): string | undefined {\n    if (text === \"\") {\n        return undefined;\n    }\n    return sha512(text);\n}\n\n//Given a string 'text', return its sha384 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_sha384(\"Hello world\")\n// \"8567b14b536c449a8d07d24a237b4f2a63e32922fcD0b35f751a75848287649845819f7b709b383a077cfefc5096292\"\nfunction string_to_sha384(text: string): string |"}
{"task_id": "HumanEval_44_change_base", "generation": "//Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunction change_base(x: number, base: number): string {\n\n  let result = \"\";\n  while (x > 0) {\n    result = (x % base) + result;\n    x = Math.floor(x / base);\n  }\n  return result;\n}\n", "prompt": "//Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunction change_base(x: number, base: number): string {\n", "wholecode": "//Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunction change_base(x: number, base: number): string {\n  let result = \"\";\n  while (x > 0) {\n    result = (x % base) + result;\n    x = Math.floor(x / base);\n  }\n  return result;\n}\n\nconsole.log(change_base(8, 3));\nconsole.log(change_base(8, 2));\nconsole.log(change_base(7, 2));"}
{"task_id": "HumanEval_157_right_angle_triangle", "generation": "//Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunction right_angle_triangle(a: number, b: number, c: number): boolean {\n\n    if (a * a + b * b === c * c) {\n        return true;\n    } else if (a * a + c * c === b * b) {\n        return true;\n    } else if (b * b + c * c === a * a) {\n        return true;\n    } else {\n        return false;\n    }\n}", "prompt": "//Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunction right_angle_triangle(a: number, b: number, c: number): boolean {\n", "wholecode": "//Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunction right_angle_triangle(a: number, b: number, c: number): boolean {\n    if (a * a + b * b === c * c) {\n        return true;\n    } else if (a * a + c * c === b * b) {\n        return true;\n    } else if (b * b + c * c === a * a) {\n        return true;\n    } else {\n        return false;\n    }\n}\nconsole.log(right_angle_triangle(3, 4, 5));\nconsole.log(right_angle_triangle(1, 2, 3));"}
{"task_id": "HumanEval_81_numerical_letter_grade", "generation": "//It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you an array of GPAs for some students and you have to write \n// a function that can output an array of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([4.0, 3, 1.7, 2, 3.5])\n// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunction numerical_letter_grade(grades: number[]): string[] {\n\n    let letter_grades: string[] = [];\n    for (let i = 0; i < grades.length; i++) {\n        if (grades[i] === 4.0) {\n            letter_grades.push(\"A+\");\n        } else if (grades[i] > 3.7) {\n            letter_grades.push(\"A\");\n        } else if (grades[i] > 3.3) {\n            letter_grades.push(\"A-\");\n        } else if (grades[i] > 3.0) {\n            letter_grades.push(\"B+\");\n        } else if (grades[i] > 2.7) {\n            letter_grades.push(\"B\");\n        } else if (grades[i] > 2.3) {\n            letter_grades.push(\"B-\");\n        } else if (grades[i] > 2.0) {\n            letter_grades.push(\"C+\");\n        } else if (grades[i] > 1.7) {\n            letter_grades.push(\"C\");\n        } else if (grades[i] > 1.3) {\n            letter_grades.push(\"C-\");\n        } else if (grades[i] > 1.0) {\n            letter_grades.push(\"D+\");\n        } else if (grades[i] > 0.7) {\n            letter_grades.push(\"D\");\n        } else if (grades[i] > 0.0) {\n            letter_grades.push(\"D-\");\n        } else {\n            letter_grades.push(\"E\");\n        }\n    }\n    return letter_grades;\n}", "prompt": "//It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you an array of GPAs for some students and you have to write \n// a function that can output an array of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([4.0, 3, 1.7, 2, 3.5])\n// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunction numerical_letter_grade(grades: number[]): string[] {\n", "wholecode": "//It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you an array of GPAs for some students and you have to write \n// a function that can output an array of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([4.0, 3, 1.7, 2, 3.5])\n// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunction numerical_letter_grade(grades: number[]): string[] {\n    let letter_grades: string[] = [];\n    for (let i = 0; i < grades.length; i++) {\n        if (grades[i] === 4.0) {\n            letter_grades.push(\"A+\");\n        } else if (grades[i] > 3.7) {\n            letter_grades.push(\"A\");\n        } else if (grades[i] > 3.3) {\n            letter_grades.push(\"A-\");\n        } else if (grades[i] > 3.0) {\n            letter_grades.push(\"B+\");\n        } else if (grades[i] > 2.7) {\n            letter_grades.push(\"B\");\n        } else if (grades[i] > 2.3) {\n            letter_grades.push(\"B-\");\n        } else if (grades[i] > 2.0) {\n            letter_grades.push(\"C+\");\n        } else if (grades[i] > 1.7) {\n            letter_grades.push(\"C\");\n        } else if (grades[i] > 1.3) {\n            letter_grades.push(\"C-\");\n        } else if (grades[i] > 1.0) {\n            letter_grades.push(\"D+\");\n        } else if (grades[i] > 0.7) {\n            letter_grades.push(\"D\");\n        } else if (grades[i] > 0.0) {\n            letter_grades.push(\"D-\");\n        } else {\n            letter_grades.push(\"E\");\n        }\n    }\n    return letter_grades;\n}\nconsole.log(numerical_letter_grade([4.0, 3, 1.7, 2, 3.5]));"}
{"task_id": "HumanEval_5_intersperse", "generation": "//Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n// >>> intersperse([], 4)\n// []\n// >>> intersperse([1, 2, 3], 4)\n// [1, 4, 2, 4, 3]\nfunction intersperse(numbers: number[], delimeter: number): number[] {\n\n  const result: number[] = [];\n  for (let i = 0; i < numbers.length; i++) {\n    result.push(numbers[i]);\n    if (i < numbers.length - 1) {\n      result.push(delimeter);\n    }\n  }\n  return result;\n}\n", "prompt": "//Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n// >>> intersperse([], 4)\n// []\n// >>> intersperse([1, 2, 3], 4)\n// [1, 4, 2, 4, 3]\nfunction intersperse(numbers: number[], delimeter: number): number[] {\n", "wholecode": "//Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n// >>> intersperse([], 4)\n// []\n// >>> intersperse([1, 2, 3], 4)\n// [1, 4, 2, 4, 3]\nfunction intersperse(numbers: number[], delimeter: number): number[] {\n  const result: number[] = [];\n  for (let i = 0; i < numbers.length; i++) {\n    result.push(numbers[i]);\n    if (i < numbers.length - 1) {\n      result.push(delimeter);\n    }\n  }\n  return result;\n}\n\n//Given an array of numbers, return the sum of all the numbers in the array\n// >>> sum([1, 2, 3])\n// 6\n// >>> sum([10, 20, 30])\n// 60\nfunction sum(numbers: number[]): number {\n  let result = 0;\n  for (let i = 0; i < numbers.length; i++) {\n    result += numbers[i];\n  }\n  return result;\n}\n\n//Given an array of numbers, return the product of all the numbers in the array\n// >>> product([1, 2, 3])\n// 6\n// >>> product([10, 20, 30])\n// 6000\nfunction product(numbers: number[]): number {\n  let result = 1;\n  for (let i = 0; i < numbers.length; i++) {\n    result *= numbers[i];\n  }\n  return result;\n}\n\n//Given an array of numbers, return the maximum number in the array\n// >>> max([1, 2, 3])\n// 3\n// >>> max([10, 20, 30])\n// 30\nfunction max(numbers: number[]): number {\n  let result = numbers[0];\n  for (let i = 1; i < numbers.length; i++) {\n    if (numbers[i] > result) {\n      result = numbers[i];\n    }\n  }\n  return result;\n}\n\n//Given an array of numbers, return the minimum number in the array\n// >>> min([1, 2, 3])\n// 1\n// >>> min([10, 20, 30])\n// 10\nfunction min(numbers: number[]): number {\n  let result = numbers[0];\n  for (let i = 1; i < numbers.length; i++) {\n    if (numbers[i] < result) {\n      result = numbers[i];\n    }\n  }\n  return result;\n}\n\n//Given an array of numbers, return the average of all the numbers in the array\n// >>> average([1,"}
{"task_id": "HumanEval_146_specialFilter", "generation": "//Write a function that takes an array of numbers as input and returns \n// the number of elements in the array that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([15, -73, 14, -15])\n// 1\n// >>> specialFilter([33, -2, -3, 45, 21, 109])\n// 2\nfunction specialFilter(nums: number[]): number {\n\n    let count = 0;\n    for (let i = 0; i < nums.length; i++) {\n        if (nums[i] > 10 && isOddDigit(nums[i])) {\n            count++;\n        }\n    }\n    return count;\n}\n", "prompt": "//Write a function that takes an array of numbers as input and returns \n// the number of elements in the array that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([15, -73, 14, -15])\n// 1\n// >>> specialFilter([33, -2, -3, 45, 21, 109])\n// 2\nfunction specialFilter(nums: number[]): number {\n", "wholecode": "//Write a function that takes an array of numbers as input and returns \n// the number of elements in the array that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([15, -73, 14, -15])\n// 1\n// >>> specialFilter([33, -2, -3, 45, 21, 109])\n// 2\nfunction specialFilter(nums: number[]): number {\n    let count = 0;\n    for (let i = 0; i < nums.length; i++) {\n        if (nums[i] > 10 && isOddDigit(nums[i])) {\n            count++;\n        }\n    }\n    return count;\n}\n\nfunction isOddDigit(num: number): boolean {\n    const firstDigit = Math.abs(num);\n    const lastDigit = Math.abs(num) % 10;\n    return firstDigit % 2 !== 0 && lastDigit % 2 !== 0;\n}\n\nconsole.log(specialFilter([15, -73, 14, -15])); // Output: 1\nconsole.log(specialFilter([33, -2, -3, 45, 21, 109])); // Output: 2"}
{"task_id": "HumanEval_60_sum_to_n", "generation": "//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n: number): number {\n\n    let sum: number = 0;\n    for (let i: number = 1; i <= n; i++) {\n        sum += i;\n    }\n    return sum;\n}\n", "prompt": "//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n: number): number {\n", "wholecode": "//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n: number): number {\n    let sum: number = 0;\n    for (let i: number = 1; i <= n; i++) {\n        sum += i;\n    }\n    return sum;\n}\n\nconsole.log(sum_to_n(30));\nconsole.log(sum_to_n(100));\nconsole.log(sum_to_n(5));\nconsole.log(sum_to_n(10));\nconsole.log(sum_to_n(1));"}
{"task_id": "HumanEval_26_remove_duplicates", "generation": "//From an array of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([1, 2, 3, 2, 4])\n// [1, 3, 4]\nfunction remove_duplicates(numbers: number[]): number[] {\n\n    let result: number[] = [];\n    let count: number = 0;\n    for (let i = 0; i < numbers.length; i++) {\n        for (let j = 0; j < numbers.length; j++) {\n            if (numbers[i] === numbers[j]) {\n                count++;\n            }\n        }\n        if (count === 1) {\n            result.push(numbers[i]);\n        }\n        count = 0;\n    }\n    return result;\n}\n", "prompt": "//From an array of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([1, 2, 3, 2, 4])\n// [1, 3, 4]\nfunction remove_duplicates(numbers: number[]): number[] {\n", "wholecode": "//From an array of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([1, 2, 3, 2, 4])\n// [1, 3, 4]\nfunction remove_duplicates(numbers: number[]): number[] {\n    let result: number[] = [];\n    let count: number = 0;\n    for (let i = 0; i < numbers.length; i++) {\n        for (let j = 0; j < numbers.length; j++) {\n            if (numbers[i] === numbers[j]) {\n                count++;\n            }\n        }\n        if (count === 1) {\n            result.push(numbers[i]);\n        }\n        count = 0;\n    }\n    return result;\n}\n\nconsole.log(remove_duplicates([1, 2, 3, 2, 4]));"}
{"task_id": "HumanEval_163_generate_integers", "generation": "//Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// [2, 4, 6, 8]\n// >>> generate_integers(8, 2)\n// [2, 4, 6, 8]\n// >>> generate_integers(10, 14)\n// []\nfunction generate_integers(a: number, b: number): number[] {\n\n  const result: number[] = [];\n  const start = Math.min(a, b);\n  const end = Math.max(a, b);\n\n  for (let i = start; i <= end; i++) {\n    if (i % 2 === 0) {\n      result.push(i);\n    }\n  }\n\n  return result;\n}\n", "prompt": "//Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// [2, 4, 6, 8]\n// >>> generate_integers(8, 2)\n// [2, 4, 6, 8]\n// >>> generate_integers(10, 14)\n// []\nfunction generate_integers(a: number, b: number): number[] {\n", "wholecode": "//Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// [2, 4, 6, 8]\n// >>> generate_integers(8, 2)\n// [2, 4, 6, 8]\n// >>> generate_integers(10, 14)\n// []\nfunction generate_integers(a: number, b: number): number[] {\n  const result: number[] = [];\n  const start = Math.min(a, b);\n  const end = Math.max(a, b);\n\n  for (let i = start; i <= end; i++) {\n    if (i % 2 === 0) {\n      result.push(i);\n    }\n  }\n\n  return result;\n}\n\nconsole.log(generate_integers(2, 8));\nconsole.log(generate_integers(8, 2));\nconsole.log(generate_integers(10, 14));"}
{"task_id": "HumanEval_9_rolling_max", "generation": "//From a given array of integers, generate an array of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([1, 2, 3, 2, 3, 4, 2])\n// [1, 2, 3, 3, 3, 4, 4]\nfunction rolling_max(numbers: number[]): number[] {\n\n    let max = numbers[0];\n    let result: number[] = [];\n    for (let i = 0; i < numbers.length; i++) {\n        if (numbers[i] > max) {\n            max = numbers[i];\n        }\n        result.push(max);\n    }\n    return result;\n}\n", "prompt": "//From a given array of integers, generate an array of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([1, 2, 3, 2, 3, 4, 2])\n// [1, 2, 3, 3, 3, 4, 4]\nfunction rolling_max(numbers: number[]): number[] {\n", "wholecode": "//From a given array of integers, generate an array of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([1, 2, 3, 2, 3, 4, 2])\n// [1, 2, 3, 3, 3, 4, 4]\nfunction rolling_max(numbers: number[]): number[] {\n    let max = numbers[0];\n    let result: number[] = [];\n    for (let i = 0; i < numbers.length; i++) {\n        if (numbers[i] > max) {\n            max = numbers[i];\n        }\n        result.push(max);\n    }\n    return result;\n}\n\nconsole.log(rolling_max([1, 2, 3, 2, 3, 4, 2]));"}
{"task_id": "HumanEval_3_below_zero", "generation": "//You're given an array of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([1, 2, 3])\n// false\n// >>> below_zero([1, 2, -4, 5])\n// true\nfunction below_zero(operations: number[]): boolean {\n\n  let balance = 0;\n  for (let i = 0; i < operations.length; i++) {\n    balance += operations[i];\n    if (balance < 0) {\n      return true;\n    }\n  }\n  return false;\n}\n", "prompt": "//You're given an array of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([1, 2, 3])\n// false\n// >>> below_zero([1, 2, -4, 5])\n// true\nfunction below_zero(operations: number[]): boolean {\n", "wholecode": "//You're given an array of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([1, 2, 3])\n// false\n// >>> below_zero([1, 2, -4, 5])\n// true\nfunction below_zero(operations: number[]): boolean {\n  let balance = 0;\n  for (let i = 0; i < operations.length; i++) {\n    balance += operations[i];\n    if (balance < 0) {\n      return true;\n    }\n  }\n  return false;\n}\n\nconsole.log(below_zero([1, 2, 3]));\nconsole.log(below_zero([1, 2, -4, 5]));"}
{"task_id": "HumanEval_69_search", "generation": "//You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the array.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([4, 1, 2, 2, 3, 1])\n// 2\n// >>> search([1, 2, 2, 3, 3, 3, 4, 4, 4])\n// 3\n// >>> search([5, 5, 4, 4, 4])\n// -1\nfunction search(lst: number[]): number {\n\n    let freq: { [key: number]: number } = {};\n    for (let i = 0; i < lst.length; i++) {\n        if (freq[lst[i]]) {\n            freq[lst[i]]++;\n        } else {\n            freq[lst[i]] = 1;\n        }\n    }\n    let max = -1;\n    for (let i = 0; i < lst.length; i++) {\n        if (freq[lst[i]] >= lst[i]) {\n            max = Math.max(max, lst[i]);\n        }\n    }\n    return max;\n}", "prompt": "//You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the array.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([4, 1, 2, 2, 3, 1])\n// 2\n// >>> search([1, 2, 2, 3, 3, 3, 4, 4, 4])\n// 3\n// >>> search([5, 5, 4, 4, 4])\n// -1\nfunction search(lst: number[]): number {\n", "wholecode": "//You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the array.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([4, 1, 2, 2, 3, 1])\n// 2\n// >>> search([1, 2, 2, 3, 3, 3, 4, 4, 4])\n// 3\n// >>> search([5, 5, 4, 4, 4])\n// -1\nfunction search(lst: number[]): number {\n    let freq: { [key: number]: number } = {};\n    for (let i = 0; i < lst.length; i++) {\n        if (freq[lst[i]]) {\n            freq[lst[i]]++;\n        } else {\n            freq[lst[i]] = 1;\n        }\n    }\n    let max = -1;\n    for (let i = 0; i < lst.length; i++) {\n        if (freq[lst[i]] >= lst[i]) {\n            max = Math.max(max, lst[i]);\n        }\n    }\n    return max;\n}"}
{"task_id": "HumanEval_61_correct_bracketing", "generation": "//brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunction correct_bracketing(brackets: string): boolean {\n\n    let count = 0;\n    for (let i = 0; i < brackets.length; i++) {\n        if (brackets[i] === \"(\") {\n            count++;\n        } else if (brackets[i] === \")\") {\n            count--;\n        }\n        if (count < 0) {\n            return false;\n        }\n    }\n    return count === 0;\n}", "prompt": "//brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunction correct_bracketing(brackets: string): boolean {\n", "wholecode": "//brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunction correct_bracketing(brackets: string): boolean {\n    let count = 0;\n    for (let i = 0; i < brackets.length; i++) {\n        if (brackets[i] === \"(\") {\n            count++;\n        } else if (brackets[i] === \")\") {\n            count--;\n        }\n        if (count < 0) {\n            return false;\n        }\n    }\n    return count === 0;\n}"}
{"task_id": "HumanEval_37_sort_even", "generation": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_even([5, 6, 3, 4])\n// [3, 6, 5, 4]\nfunction sort_even(l: number[]): number[] {\n\n    let even: number[] = [];\n    let odd: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 2 == 0) {\n            even.push(l[i]);\n        } else {\n            odd.push(l[i]);\n        }\n    }\n    even.sort((a, b) => a - b);\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 2 == 0) {\n            result.push(even.shift()!);\n        } else {\n            result.push(odd.shift()!);\n        }\n    }\n    return result;\n}\n", "prompt": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_even([5, 6, 3, 4])\n// [3, 6, 5, 4]\nfunction sort_even(l: number[]): number[] {\n", "wholecode": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_even([5, 6, 3, 4])\n// [3, 6, 5, 4]\nfunction sort_even(l: number[]): number[] {\n    let even: number[] = [];\n    let odd: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 2 == 0) {\n            even.push(l[i]);\n        } else {\n            odd.push(l[i]);\n        }\n    }\n    even.sort((a, b) => a - b);\n    let result: number[] = [];\n    for (let i = 0; i < l.length; i++) {\n        if (i % 2 == 0) {\n            result.push(even.shift()!);\n        } else {\n            result.push(odd.shift()!);\n        }\n    }\n    return result;\n}\n\nconsole.log(sort_even([1, 2, 3]));\nconsole.log(sort_even([5, 6, 3, 4]));"}
{"task_id": "HumanEval_54_same_chars", "generation": "//Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunction same_chars(s0: string, s1: string): boolean {\n\n    let s0Arr = s0.split(\"\");\n    let s1Arr = s1.split(\"\");\n    let s0Set = new Set(s0Arr);\n    let s1Set = new Set(s1Arr);\n    return s0Set.size === s1Set.size;\n}\n", "prompt": "//Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunction same_chars(s0: string, s1: string): boolean {\n", "wholecode": "//Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunction same_chars(s0: string, s1: string): boolean {\n    let s0Arr = s0.split(\"\");\n    let s1Arr = s1.split(\"\");\n    let s0Set = new Set(s0Arr);\n    let s1Set = new Set(s1Arr);\n    return s0Set.size === s1Set.size;\n}\n\nconsole.log(same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\"));\nconsole.log(same_chars(\"abcd\", \"dddddddabc\"));\nconsole.log(same_chars(\"dddddddabc\", \"abcd\"));\nconsole.log(same_chars(\"eabcd\", \"dddddddabc\"));\nconsole.log(same_chars(\"abcd\", \"dddddddabce\"));\nconsole.log(same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\"));"}
{"task_id": "HumanEval_56_correct_bracketing", "generation": "//brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunction correct_bracketing(brackets: string): boolean {\n\n  let count = 0;\n  for (let i = 0; i < brackets.length; i++) {\n    if (brackets[i] === \"<\") {\n      count++;\n    } else if (brackets[i] === \">\") {\n      count--;\n    }\n    if (count < 0) {\n      return false;\n    }\n  }\n  return count === 0;\n}\n", "prompt": "//brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunction correct_bracketing(brackets: string): boolean {\n", "wholecode": "//brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunction correct_bracketing(brackets: string): boolean {\n  let count = 0;\n  for (let i = 0; i < brackets.length; i++) {\n    if (brackets[i] === \"<\") {\n      count++;\n    } else if (brackets[i] === \">\") {\n      count--;\n    }\n    if (count < 0) {\n      return false;\n    }\n  }\n  return count === 0;\n}\n\nconsole.log(correct_bracketing(\"<\"));\nconsole.log(correct_bracketing(\"<>\"));\nconsole.log(correct_bracketing(\"<<><>>\"));\nconsole.log(correct_bracketing(\"><<>\"));"}
