{"task_id": "Mbpp/2", "completion": "\"\"\"\nWrite a function to find the shared elements from the given two lists.\nassert set(similar_elements((3, 4, 5, 6),(5, 7, 4, 10))) == set((4, 5))\n\"\"\"\ndef similar_elements(list1, list2):\n    return set(list1) & set(list2)"}
{"task_id": "Mbpp/3", "completion": "\"\"\"\nWrite a python function to identify non-prime numbers.\nassert is_not_prime(2) == False\n\"\"\"\ndef is_not_prime(n):\n    if n <= 1:\n        return True\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return True\n    return False\n"}
{"task_id": "Mbpp/4", "completion": "\"\"\"\nWrite a function to find the n largest integers from a given list of numbers, returned in descending order.\nassert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],3)==[85, 75, 65]\n\"\"\"\nimport heapq\ndef heap_queue_largest(nums, n):\n    return heapq.nlargest(n, nums)\n"}
{"task_id": "Mbpp/6", "completion": "\"\"\"\nWrite a python function to check whether the two numbers differ at one bit position only or not.\nassert differ_At_One_Bit_Pos(13,9) == True\n\"\"\"\ndef differ_At_One_Bit_Pos(a,b):\n    return bin(a^b).count('1') == 1"}
{"task_id": "Mbpp/7", "completion": "\"\"\"\nWrite a function to find all words which are at least 4 characters long in a string.\nassert set(find_char_long('Please move back to stream')) == set(['Please', 'move', 'back', 'stream'])\n\"\"\"\nimport re\ndef find_char_long(string):\n    return re.findall(r'\\b\\w{4,}\\b', string)\n"}
{"task_id": "Mbpp/8", "completion": "\"\"\"\nWrite a function to find squares of individual elements in a list.\nassert square_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n\"\"\"\ndef square_nums(nums):\n    return [num ** 2 for num in nums]"}
{"task_id": "Mbpp/9", "completion": "\"\"\"\nWrite a python function to find the minimum number of rotations (greater than 0) required to get the same string.\nassert find_Rotations(\"aaaa\") == 1\n\"\"\"\ndef find_Rotations(s):\n    for i in range(1, len(s)):\n        if s[i:] + s[:i] == s:\n            return i\n    return len(s)"}
{"task_id": "Mbpp/11", "completion": "\"\"\"\nWrite a python function to remove first and last occurrence of a given character from the string.\nassert remove_Occ(\"hello\",\"l\") == \"heo\"\n\"\"\"\ndef remove_Occ(string, char):\n    first_occurrence = string.find(char)\n    last_occurrence = string.rfind(char)\n    if first_occurrence != -1 and last_occurrence != -1:\n        return string[:first_occurrence] + string[first_occurrence + 1:last_occurrence] + string[last_occurrence + 1:]\n    else:\n        return string\n"}
{"task_id": "Mbpp/12", "completion": "\"\"\"\nWrite a function to sort a given matrix in ascending order according to the sum of its rows.\nassert sort_matrix([[1, 2, 3], [2, 4, 5], [1, 1, 1]])==[[1, 1, 1], [1, 2, 3], [2, 4, 5]]\n\"\"\"\ndef sort_matrix(matrix):\n    return sorted(matrix, key=sum)"}
{"task_id": "Mbpp/14", "completion": "\"\"\"\nWrite a python function to find the volume of a triangular prism.\nassert find_Volume(10,8,6) == 240\n\"\"\"\ndef find_Volume(base, height, length):\n    return (base * height * length) / 2\n"}
{"task_id": "Mbpp/16", "completion": "\"\"\"\nWrite a function to that returns true if the input string contains sequences of lowercase letters joined with an underscore and false otherwise.\nassert text_lowercase_underscore(\"aab_cbbbc\")==(True)\n\"\"\"\nimport re\ndef text_lowercase_underscore(text):\n    pattern = r'^[a-z]+_[a-z]+$'\n    return bool(re.match(pattern, text))\n"}
{"task_id": "Mbpp/17", "completion": "\"\"\"\nWrite a function that returns the perimeter of a square given its side length as input.\nassert square_perimeter(10)==40\n\"\"\"\ndef square_perimeter(side_length):\n    return side_length * 4\n"}
{"task_id": "Mbpp/18", "completion": "\"\"\"\nWrite a function to remove characters from the first string which are present in the second string.\nassert remove_dirty_chars(\"probasscurve\", \"pros\") == 'bacuve'\n\"\"\"\ndef remove_dirty_chars(string1, string2):\n    result = \"\"\n    for char in string1:\n        if char not in string2:\n            result += char\n    return result\n"}
{"task_id": "Mbpp/19", "completion": "\"\"\"\nWrite a function to find whether a given array of integers contains any duplicate element.\nassert test_duplicate(([1,2,3,4,5]))==False\n\"\"\"\ndef test_duplicate(arr):\n    for i in range(len(arr)):\n        for j in range(i+1,len(arr)):\n            if arr[i]==arr[j]:\n                return True\n    return False"}
{"task_id": "Mbpp/20", "completion": "\"\"\"\nWrite a function to check if the given number is woodball or not.\nassert is_woodall(383) == True\n\"\"\"\ndef is_woodall(n):\n    if n < 1:\n        return False\n    for i in range(1, n+1):\n        if n == i * (2**i - 1):\n            return True\n    return False\n"}
{"task_id": "Mbpp/56", "completion": "\"\"\"\nWrite a python function to check if a given number is one less than twice its reverse.\nassert check(70) == False\n\"\"\"\ndef check(n):\n    rev = 0\n    while n > 0:\n        rev = rev * 10 + n % 10\n        n //= 10\n    return n == 2 * rev - 1"}
{"task_id": "Mbpp/57", "completion": "\"\"\"\nWrite a python function to find the largest number that can be formed with the given list of digits.\nassert find_Max_Num([1,2,3]) == 321\n\"\"\"\ndef find_Max_Num(num_list):\n    num_list.sort(reverse=True)\n    return int(''.join(map(str, num_list)))\n"}
{"task_id": "Mbpp/58", "completion": "\"\"\"\nWrite a python function to check whether the given two integers have opposite sign or not.\nassert opposite_Signs(1,-2) == True\n\"\"\"\ndef opposite_Signs(a,b):\n    if a*b<0:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/59", "completion": "\"\"\"\nWrite a function to find the nth octagonal number.\nassert is_octagonal(5) == 65\n\"\"\"\ndef is_octagonal(n):\n    return n * (3 * n - 2)\n"}
{"task_id": "Mbpp/61", "completion": "\"\"\"\nWrite a python function to count the number of substrings with the sum of digits equal to their length.\nassert count_Substrings('112112') == 6\n\"\"\"\ndef count_Substrings(s):\n    count = 0\n    for i in range(len(s)):\n        for j in range(i+1, len(s)+1):\n            sub = s[i:j]\n            if sum(int(digit) for digit in sub) == len(sub):\n                count += 1\n    return count\n"}
{"task_id": "Mbpp/62", "completion": "\"\"\"\nWrite a python function to find smallest number in a list.\nassert smallest_num([10, 20, 1, 45, 99]) == 1\n\"\"\"\ndef smallest_num(list):\n    return min(list)\n"}
{"task_id": "Mbpp/63", "completion": "\"\"\"\nWrite a function to find the maximum difference between available pairs in the given tuple list.\nassert max_difference([(3, 5), (1, 7), (10, 3), (1, 2)]) == 7\n\"\"\"\ndef max_difference(tup):\n    max_diff = 0\n    for i in range(len(tup)):\n        for j in range(i+1, len(tup)):\n            diff = abs(tup[i][0] - tup[j][0]) + abs(tup[i][1] - tup[j][1])\n            if diff > max_diff:\n                max_diff = diff\n    return max_diff\n"}
{"task_id": "Mbpp/64", "completion": "\"\"\"\nWrite a function to sort a list of tuples using the second value of each tuple.\nassert subject_marks([('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])==[('Social sciences', 82), ('English', 88), ('Science', 90), ('Maths', 97)]\n\"\"\"\ndef sort_list_of_tuples(tuples_list):\n    return sorted(tuples_list, key=lambda x: x[1])\n\n# Example usage\nsubject_marks = [('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)]\nsorted_subject_marks = sort_list_of_tuples(subject_marks)"}
{"task_id": "Mbpp/65", "completion": "\"\"\"\nWrite a function to flatten a list and sum all of its elements.\nassert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21\n\"\"\"\ndef recursive_list_sum(list):\n    sum = 0\n    for i in list:\n        if type(i) == type([]):\n            sum = sum + recursive_list_sum(i)\n        else:\n            sum = sum + i\n    return sum\n"}
{"task_id": "Mbpp/66", "completion": "\"\"\"\nWrite a python function to count the number of positive numbers in a list.\nassert pos_count([1,-2,3,-4]) == 2\n\"\"\"\ndef pos_count(lst):\n    count = 0\n    for i in lst:\n        if i > 0:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/67", "completion": "\"\"\"\nWrite a function to find the number of ways to partition a set of Bell numbers.\nassert bell_number(2)==2\n\"\"\"\ndef bell_number(n):\n    bell = [[0 for i in range(n+1)] for j in range(n+1)]\n    bell[0][0] = 1\n    for i in range(1, n+1):\n        bell[i][0] = bell[i-1][i-1]\n        for j in range(1, i+1):\n            bell[i][j] = bell[i-1][j-1] + bell[i][j-1]\n    return bell[n][0]"}
{"task_id": "Mbpp/68", "completion": "\"\"\"\nWrite a python function to check whether the given array is monotonic or not.\nassert is_Monotonic([6, 5, 4, 4]) == True\n\"\"\"\ndef is_Monotonic(arr):\n    return (all(arr[i] <= arr[i + 1] for i in range(len(arr) - 1)) or\n            all(arr[i] >= arr[i + 1] for i in range(len(arr) - 1)))\n"}
{"task_id": "Mbpp/69", "completion": "\"\"\"\nWrite a function to check whether a list contains the given sublist or not.\nassert is_sublist([2,4,3,5,7],[3,7])==False\n\"\"\"\ndef is_sublist(l1,l2):\n    if len(l1)>len(l2):\n        return False\n    for i in range(len(l1)):\n        if l1[i]!=l2[i]:\n            return False\n    return True"}
{"task_id": "Mbpp/70", "completion": "\"\"\"\nWrite a function to find whether all the given tuples have equal length or not.\nassert get_equal([(11, 22, 33), (44, 55, 66)]) == True\n\"\"\"\ndef get_equal(tuples):\n    return all(len(tuples[0]) == len(t) for t in tuples)\n"}
{"task_id": "Mbpp/71", "completion": "\"\"\"\nWrite a function to sort a list of elements.\nassert comb_sort([5, 15, 37, 25, 79]) == [5, 15, 25, 37, 79]\n\"\"\"\ndef comb_sort(lst):\n    gap = len(lst)\n    shrink = 1.3\n    sorted = False\n    while not sorted:\n        gap = int(gap/shrink)\n        if gap > 1:\n            sorted = False\n        else:\n            gap = 1\n            sorted = True\n        i = 0\n        while i + gap < len(lst):\n            if lst[i] > lst[i + gap]:\n                lst[i], lst[i + gap] = lst[i + gap], lst[i]\n                sorted = False\n            i += 1\n    return lst"}
{"task_id": "Mbpp/72", "completion": "\"\"\"\nWrite a python function to check whether the given number can be represented as the difference of two squares or not.\nassert dif_Square(5) == True\n\"\"\"\ndef dif_Square(n):\n    for i in range(1,n+1):\n        for j in range(1,n+1):\n            if i**2 - j**2 == n:\n                return True\n    return False"}
{"task_id": "Mbpp/74", "completion": "\"\"\"\nWrite a function to check whether it follows the sequence given in the patterns array.\nassert is_samepatterns([\"red\",\"green\",\"green\"], [\"a\", \"b\", \"b\"])==True\n\"\"\"\ndef is_samepatterns(words, patterns):\n    if len(words) != len(patterns):\n        return False\n    word_to_pattern = {}\n    pattern_to_word = {}\n    for word, pattern in zip(words, patterns):\n        if word in word_to_pattern and word_to_pattern[word] != pattern:\n            return False\n        if pattern in pattern_to_word and pattern_to_word[pattern] != word:\n            return False\n        word_to_pattern[word] = pattern\n        pattern_to_word[pattern] = word\n    return True"}
{"task_id": "Mbpp/75", "completion": "\"\"\"\nWrite a function to find tuples which have all elements divisible by k from the given list of tuples.\nassert find_tuples([(6, 24, 12), (7, 9, 6), (12, 18, 21)], 6) == [(6, 24, 12)]\n\"\"\"\ndef find_tuples(tuples, k):\n    return [t for t in tuples if all(x % k == 0 for x in t)]\n"}
{"task_id": "Mbpp/77", "completion": "\"\"\"\nWrite a python function to find whether a number is divisible by 11.\nassert is_Diff (12345) == False\n\"\"\"\ndef is_Diff(n):\n    s1 = 0\n    s2 = 0\n    for i in range(len(str(n))):\n        if i % 2 == 0:\n            s1 += int(str(n)[i])\n        else:\n            s2 += int(str(n)[i])\n    return (s1 - s2) % 11 == 0"}
{"task_id": "Mbpp/79", "completion": "\"\"\"\nWrite a python function to check whether the length of the word is odd or not.\nassert word_len(\"Hadoop\") == False\n\"\"\"\ndef word_len(word):\n    if len(word)%2==0:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/80", "completion": "\"\"\"\nWrite a function to find the nth tetrahedral number.\nassert tetrahedral_number(5) == 35\n\"\"\"\ndef tetrahedral_number(n):\n    return n*(n+1)*(n+2)//6\n"}
{"task_id": "Mbpp/82", "completion": "\"\"\"\nWrite a function to find the volume of a sphere.\nassert math.isclose(volume_sphere(10), 4188.790204786391, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef volume_sphere(radius):\n    return (4/3) * math.pi * radius**3\n\n"}
{"task_id": "Mbpp/83", "completion": "\"\"\"\nWrite a python function to find the character made by adding the ASCII value of all the characters of the given string modulo 26.\nassert get_Char(\"abc\") == \"f\"\n\"\"\"\ndef get_Char(s):\n    return chr((sum([ord(i) for i in s]) % 26) + 97)"}
{"task_id": "Mbpp/84", "completion": "\"\"\"\nWrite a function to find the nth number in the newman conway sequence.\nassert sequence(10) == 6\n\"\"\"\ndef sequence(n):\n    if n == 1 or n == 2:\n        return 1\n    else:\n        return sequence(sequence(n-1)) + sequence(n-sequence(n-1))"}
{"task_id": "Mbpp/85", "completion": "\"\"\"\nWrite a function to find the surface area of a sphere.\nassert math.isclose(surfacearea_sphere(10), 1256.6370614359173, rel_tol=0.001)\n\"\"\"\nimport math\n\ndef surfacearea_sphere(radius):\n    return 4 * math.pi * radius ** 2\n"}
{"task_id": "Mbpp/86", "completion": "\"\"\"\nWrite a function to find nth centered hexagonal number.\nassert centered_hexagonal_number(10) == 271\n\"\"\"\ndef centered_hexagonal_number(n):\n    return 3*n*(n-1)+1"}
{"task_id": "Mbpp/87", "completion": "\"\"\"\nWrite a function to merge three dictionaries into a single dictionary.\nassert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}\n\"\"\"\ndef merge_dictionaries_three(dict1, dict2, dict3):\n    merged_dict = {}\n    merged_dict.update(dict1)\n    merged_dict.update(dict2)\n    merged_dict.update(dict3)\n    return merged_dict\n\n# Test the function\ndict1 = { \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }\ndict2 = { \"G\": \"Green\", \"W\": \"White\" }\ndict3 = { \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" }\nresult = merge_dictionaries_three(dict1, dict2, dict3)"}
{"task_id": "Mbpp/88", "completion": "\"\"\"\nWrite a function to get the frequency of all the elements in a list, returned as a dictionary.\nassert freq_count([10,10,10,10,20,20,20,20,40,40,50,50,30])==({10: 4, 20: 4, 40: 2, 50: 2, 30: 1})\n\"\"\"\ndef freq_count(lst):\n    freq = {}\n    for i in lst:\n        if i in freq:\n            freq[i] += 1\n        else:\n            freq[i] = 1\n    return freq"}
{"task_id": "Mbpp/89", "completion": "\"\"\"\nWrite a function to find the closest smaller number than n.\nassert closest_num(11) == 10\n\"\"\"\ndef closest_num(n):\n    return n-1"}
{"task_id": "Mbpp/90", "completion": "\"\"\"\nWrite a python function to find the length of the longest word.\nassert len_log([\"python\",\"PHP\",\"bigdata\"]) == 7\n\"\"\"\ndef len_log(list1):\n    max_len = 0\n    for i in list1:\n        if len(i) > max_len:\n            max_len = len(i)\n    return max_len"}
{"task_id": "Mbpp/91", "completion": "\"\"\"\nWrite a function to check if a string is present as a substring in a given list of string values.\nassert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ack\")==True\n\"\"\"\ndef find_substring(list1,substring):\n    for i in list1:\n        if substring in i:\n            return True\n    return False"}
{"task_id": "Mbpp/92", "completion": "\"\"\"\nWrite a function to check whether the given number is undulating or not.\nassert is_undulating(1212121) == True\n\"\"\"\ndef is_undulating(num):\n    num = str(num)\n    if len(num) < 3:\n        return False\n    for i in range(1, len(num)-1):\n        if num[i] != num[i-1] and num[i] == num[i+1]:\n            continue\n        else:\n            return False\n    return True\n"}
{"task_id": "Mbpp/93", "completion": "\"\"\"\nWrite a function to calculate the value of 'a' to the power 'b'.\nassert power(3,4) == 81\n\"\"\"\ndef power(a,b):\n    return a**b"}
{"task_id": "Mbpp/94", "completion": "\"\"\"\nGiven a list of tuples, write a function that returns the first value of the tuple with the smallest second value.\nassert index_minimum([('Rash', 143), ('Manjeet', 200), ('Varsha', 100)]) == 'Varsha'\n\"\"\"\ndef index_minimum(tuples):\n    return min(tuples, key=lambda x: x[1])[0]\n"}
{"task_id": "Mbpp/95", "completion": "\"\"\"\nWrite a python function to find the length of the smallest list in a list of lists.\nassert Find_Min_Length([[1],[1,2]]) == 1\n\"\"\"\ndef Find_Min_Length(lists):\n    min_length = len(lists[0])\n    for lst in lists:\n        if len(lst) < min_length:\n            min_length = len(lst)\n    return min_length\n"}
{"task_id": "Mbpp/96", "completion": "\"\"\"\nWrite a python function to find the number of divisors of a given integer.\nassert divisor(15) == 4\n\"\"\"\ndef divisor(n):\n    count = 0\n    for i in range(1, n+1):\n        if n % i == 0:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/97", "completion": "\"\"\"\nWrite a function to find frequency of each element in a flattened list of lists, returned in a dictionary.\nassert frequency_lists([[1, 2, 3, 2], [4, 5, 6, 2], [7, 8, 9, 5]])=={1: 1, 2: 3, 3: 1, 4: 1, 5: 2, 6: 1, 7: 1, 8: 1, 9: 1}\n\"\"\"\nfrom collections import Counter\n\n\ndef frequency_lists(lists):\n    return Counter([item for sublist in lists for item in sublist])\n\n"}
{"task_id": "Mbpp/98", "completion": "\"\"\"\nWrite a function to multiply all the numbers in a list and divide with the length of the list.\nassert math.isclose(multiply_num((8, 2, 3, -1, 7)), -67.2, rel_tol=0.001)\n\"\"\"\nimport math\ndef multiply_num(num):\n    result = 1\n    for i in num:\n        result = result * i\n    return result / len(num)\n"}
{"task_id": "Mbpp/99", "completion": "\"\"\"\nWrite a function to convert the given decimal number to its binary equivalent, represented as a string with no leading zeros.\nassert decimal_to_binary(8) == '1000'\n\"\"\"\ndef decimal_to_binary(decimal):\n    return bin(decimal)[2:]"}
{"task_id": "Mbpp/100", "completion": "\"\"\"\nWrite a function to find the next smallest palindrome of a specified integer, returned as an integer.\nassert next_smallest_palindrome(99)==101\n\"\"\"\ndef next_smallest_palindrome(num):\n    for i in range(num+1,1000000):\n        if str(i)==str(i)[::-1]:\n            return i"}
{"task_id": "Mbpp/101", "completion": "\"\"\"\nWrite a function to find the kth element in the given array using 1-based indexing.\nassert kth_element([12,3,5,7,19], 2) == 3\n\"\"\"\ndef kth_element(arr, k):\n    return sorted(arr)[k-1]"}
{"task_id": "Mbpp/102", "completion": "\"\"\"\nWrite a function to convert a snake case string to camel case string.\nassert snake_to_camel('python_program')=='PythonProgram'\n\"\"\"\ndef snake_to_camel(snake_str):\n    components = snake_str.split('_')\n    return components[0] + ''.join(x.title() for x in components[1:])\n"}
{"task_id": "Mbpp/103", "completion": "\"\"\"\nWrite a function to find the Eulerian number a(n, m).\nassert eulerian_num(3, 1) == 4\n\"\"\"\nfrom math import factorial\n\n\ndef eulerian_num(n, m):\n    return factorial(n + 1) / (factorial(m) * factorial(n - m + 1)) * (m + 1) / (n + 1)\n\n"}
{"task_id": "Mbpp/104", "completion": "\"\"\"\nWrite a function to sort each sublist of strings in a given list of lists.\nassert sort_sublists(([\"green\", \"orange\"], [\"black\", \"white\"], [\"white\", \"black\", \"orange\"]))==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n\"\"\"\ndef sort_sublists(lst):\n    return [sorted(sublist) for sublist in lst]"}
{"task_id": "Mbpp/105", "completion": "\"\"\"\nWrite a python function to count true booleans in the given list.\nassert count([True,False,True]) == 2\n\"\"\"\ndef count(lst):\n    return lst.count(True)\n"}
{"task_id": "Mbpp/106", "completion": "\"\"\"\nWrite a function to append the given list to the given tuples.\nassert add_lists([5, 6, 7], (9, 10)) == (9, 10, 5, 6, 7)\n\"\"\"\ndef add_lists(list1, tuple1):\n    return tuple1 + tuple(list1)\n"}
{"task_id": "Mbpp/108", "completion": "\"\"\"\nWrite a function to merge three lists into a single sorted list.\nassert merge_sorted_list([25, 24, 15, 4, 5, 29, 110],[19, 20, 11, 56, 25, 233, 154],[24, 26, 54, 48])==[4, 5, 11, 15, 19, 20, 24, 24, 25, 25, 26, 29, 48, 54, 56, 110, 154, 233]\n\"\"\"\ndef merge_sorted_list(list1,list2,list3):\n    list1.extend(list2)\n    list1.extend(list3)\n    list1.sort()\n    return list1"}
{"task_id": "Mbpp/109", "completion": "\"\"\"\nWrite a python function to find the number of numbers with an odd value when rotating a binary string the given number of times.\nassert odd_Equivalent(\"011001\",6) == 3\n\"\"\"\ndef odd_Equivalent(s,n):\n    count = 0\n    for i in range(n):\n        if s.count('1') % 2 != 0:\n            count += 1\n        s = s[-1] + s[:-1]\n    return count"}
{"task_id": "Mbpp/111", "completion": "\"\"\"\nWrite a function to find the common elements in given nested lists.\nassert set(common_in_nested_lists([[12, 18, 23, 25, 45], [7, 12, 18, 24, 28], [1, 5, 8, 12, 15, 16, 18]]))==set([18, 12])\n\"\"\"\ndef common_in_nested_lists(nested_lists):\n    common_elements = set(nested_lists[0])\n    for lst in nested_lists[1:]:\n        common_elements.intersection_update(lst)\n    return common_elements\n\n# Test the function"}
{"task_id": "Mbpp/113", "completion": "\"\"\"\nWrite a function to check if a string represents an integer or not.\nassert check_integer(\"python\")==False\n\"\"\"\ndef check_integer(s):\n    try:\n        int(s)\n        return True\n    except ValueError:\n        return False"}
{"task_id": "Mbpp/115", "completion": "\"\"\"\nWrite a function to check whether all dictionaries in a list are empty or not.\nassert empty_dit([{},{},{}])==True\n\"\"\"\ndef empty_dit(list1):\n    for i in list1:\n        if i:\n            return False\n    return True"}
{"task_id": "Mbpp/116", "completion": "\"\"\"\nWrite a function to convert a given tuple of positive integers into a single integer.\nassert tuple_to_int((1,2,3))==123\n\"\"\"\ndef tuple_to_int(tup):\n    return int(''.join(map(str, tup)))\n"}
{"task_id": "Mbpp/117", "completion": "\"\"\"\nWrite a function to convert all possible convertible elements in a list of lists to floats.\nassert list_to_float( [(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")] ) == [(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)]\n\"\"\"\ndef list_to_float(lst):\n    return [[float(x) for x in y] for y in lst]"}
{"task_id": "Mbpp/118", "completion": "\"\"\"\nWrite a function to convert a string to a list of strings split on the space character.\nassert string_to_list(\"python programming\")==['python','programming']\n\"\"\"\ndef string_to_list(string):\n    return string.split(\" \")"}
{"task_id": "Mbpp/119", "completion": "\"\"\"\nWrite a python function to find the element that appears only once in a sorted array.\nassert search([1,1,2,2,3]) == 3\n\"\"\"\ndef search(arr):\n    left = 0\n    right = len(arr) - 1\n    while left < right:\n        mid = (left + right) // 2\n        if arr[mid] != arr[mid - 1] and arr[mid] != arr[mid + 1]:\n            return arr[mid]\n        if (mid % 2 == 0 and arr[mid] == arr[mid + 1]) or (mid % 2 == 1 and arr[mid] == arr[mid - 1]):\n            left = mid + 1\n        else:\n            right = mid - 1\n    return arr[left]"}
{"task_id": "Mbpp/120", "completion": "\"\"\"\nWrite a function to find the maximum absolute product between numbers in pairs of tuples within a given list.\nassert max_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==36\n\"\"\"\ndef max_product_tuple(lst):\n    max_product = 0\n    for i in range(len(lst)):\n        for j in range(i+1, len(lst)):\n            product = lst[i][0] * lst[j][1]\n            if product > max_product:\n                max_product = product\n    return max_product"}
{"task_id": "Mbpp/123", "completion": "\"\"\"\nWrite a function to sum all amicable numbers from 1 to a specified number.\nassert amicable_numbers_sum(999)==504\n\"\"\"\ndef amicable_numbers_sum(n):\n    sum = 0\n    for i in range(1, n):\n        if i not in amicables:\n            sum_of_divisors_i = sum_of_divisors(i)\n            if sum_of_divisors_i != i and sum_of_divisors_i < n:\n                sum_of_divisors_j = sum_of_divisors(sum_of_divisors_i)\n                if sum_of_divisors_j == i:\n                    amicables.add(i)\n                    amicables.add(sum_of_divisors_i)\n                    sum += i + sum_of_divisors_i\n    return sum\n\ndef sum_of_divisors(n):\n    sum = 1\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            sum += i\n            if i != n // i:\n                sum += n // i\n    return sum\n\namicables = set()"}
{"task_id": "Mbpp/124", "completion": "\"\"\"\nWrite a function to get the angle of a complex number.\nassert math.isclose(angle_complex(0,1j), 1.5707963267948966, rel_tol=0.001)\n\"\"\"\nimport math\ndef angle_complex(x, y):\n    return math.atan2(y, x)\n"}
{"task_id": "Mbpp/125", "completion": "\"\"\"\nWrite a function to find the maximum difference between the number of 0s and number of 1s in any sub-string of the given binary string.\nassert find_length(\"11000010001\") == 6\n\"\"\"\ndef find_length(s):\n    max_diff = 0\n    diff = 0\n    for i in range(len(s)):\n        if s[i] == '0':\n            diff += 1\n        else:\n            diff -= 1\n        if diff < 0:\n            diff = 0\n        max_diff = max(max_diff, diff)\n    return max_diff"}
{"task_id": "Mbpp/126", "completion": "\"\"\"\nWrite a python function to find the sum of common divisors of two given numbers.\nassert sum(10,15) == 6\n\"\"\"\ndef sum(a,b):\n    sum = 0\n    for i in range(1,min(a,b)+1):\n        if a%i == 0 and b%i == 0:\n            sum += i\n    return sum"}
{"task_id": "Mbpp/127", "completion": "\"\"\"\nWrite a function to multiply two integers.\nassert multiply_int(10,20)==200\n\"\"\"\ndef multiply_int(a,b):\n    return a*b"}
{"task_id": "Mbpp/128", "completion": "\"\"\"\nWrite a function to find words that are longer than n characters from a given list of words.\nassert long_words(3,\"python is a programming language\")==['python','programming','language']\n\"\"\"\ndef long_words(n, str):\n    word_len = []\n    txt = str.split(\" \")\n    for x in txt:\n        if len(x) > n:\n            word_len.append(x)\n    return word_len"}
{"task_id": "Mbpp/129", "completion": "\"\"\"\nWrite a function to calculate whether the matrix is a magic square.\nassert magic_square_test([[7, 12, 1, 14], [2, 13, 8, 11], [16, 3, 10, 5], [9, 6, 15, 4]])==True\n\"\"\"\ndef magic_square_test(matrix):\n    n = len(matrix)\n    if n == 0:\n        return False\n    magic_sum = sum(matrix[0])\n    for row in matrix:\n        if sum(row) != magic_sum:\n            return False\n    for col in range(n):\n        col_sum = 0\n        for row in range(n):\n            col_sum += matrix[row][col]\n        if col_sum != magic_sum:\n            return False\n    diag_sum1 = 0\n    diag_sum2 = 0\n    for i in range(n):\n        diag_sum1 += matrix[i][i]\n        diag_sum2 += matrix[i][n-i-1]\n    if diag_sum1 != magic_sum or diag_sum2 != magic_sum:\n        return False\n    return True"}
{"task_id": "Mbpp/130", "completion": "\"\"\"\nWrite a function to find the item with maximum frequency in a given list.\nassert max_occurrences([2,3,8,4,7,9,8,2,6,5,1,6,1,2,3,2,4,6,9,1,2])==2\n\"\"\"\ndef max_occurrences(list1):\n    max_count = 0\n    max_item = None\n    for item in list1:\n        count = list1.count(item)\n        if count > max_count:\n            max_count = count\n            max_item = item\n    return max_item"}
{"task_id": "Mbpp/131", "completion": "\"\"\"\nWrite a python function to reverse only the vowels of a given string (where y is not a vowel).\nassert reverse_vowels(\"Python\") == \"Python\"\n\"\"\"\ndef reverse_vowels(string):\n    vowels = \"aeiouAEIOU\"\n    string = list(string)\n    left, right = 0, len(string) - 1\n    while left < right:\n        if string[left] in vowels and string[right] in vowels:\n            string[left], string[right] = string[right], string[left]\n            left += 1\n            right -= 1\n        elif string[left] in vowels:\n            right -= 1\n        else:\n            left += 1\n    return \"\".join(string)\n"}
{"task_id": "Mbpp/132", "completion": "\"\"\"\nWrite a function to convert a tuple to a string.\nassert tup_string(('e', 'x', 'e', 'r', 'c', 'i', 's', 'e', 's'))==(\"exercises\")\n\"\"\"\ndef tup_string(tup):\n    str1 = \"\"\n    for i in tup:\n        str1 += i\n    return str1"}
{"task_id": "Mbpp/133", "completion": "\"\"\"\nWrite a function to calculate the sum of the negative numbers of a given list of numbers.\nassert sum_negativenum([2, 4, -6, -9, 11, -12, 14, -5, 17])==-32\n\"\"\"\ndef sum_negativenum(list):\n    sum=0\n    for i in list:\n        if i<0:\n            sum+=i\n    return sum"}
{"task_id": "Mbpp/135", "completion": "\"\"\"\nWrite a function to find the nth hexagonal number.\nassert hexagonal_num(10) == 190\n\"\"\"\ndef hexagonal_num(n):\n    return n * (2 * n - 1)\n"}
{"task_id": "Mbpp/137", "completion": "\"\"\"\nWrite a function to find the ratio of zeroes to non-zeroes in an array of integers.\nassert math.isclose(zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8]), 0.181818, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef zero_count(arr):\n    count = 0\n    for i in arr:\n        if i == 0:\n            count += 1\n    return count / len(arr)\n\n"}
{"task_id": "Mbpp/138", "completion": "\"\"\"\nWrite a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.\nassert is_Sum_Of_Powers_Of_Two(10) == True\n\"\"\"\ndef is_Sum_Of_Powers_Of_Two(n):\n    if n == 0:\n        return False\n    while n != 1:\n        if n % 2 != 0:\n            return False\n        n = n // 2\n    return True"}
{"task_id": "Mbpp/139", "completion": "\"\"\"\nWrite a function to find the circumference of a circle.\nassert math.isclose(circle_circumference(10), 62.830000000000005, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef circle_circumference(radius):\n    return 2 * math.pi * radius\n\n"}
{"task_id": "Mbpp/140", "completion": "\"\"\"\nWrite a function to flatten the list of lists into a single set of numbers.\nassert set(extract_singly([(3, 4, 5), (4, 5, 7), (1, 4)])) == set([3, 4, 5, 7, 1])\n\"\"\"\ndef extract_singly(list_of_lists):\n    return set([item for sublist in list_of_lists for item in sublist])"}
{"task_id": "Mbpp/141", "completion": "\"\"\"\nWrite a function to sort a list of elements.\nassert pancake_sort([15, 79, 25, 38, 69]) == [15, 25, 38, 69, 79]\n\"\"\"\ndef pancake_sort(arr):\n    for i in range(len(arr), 1, -1):\n        max_index = arr.index(max(arr[:i]))\n        arr = arr[:max_index+1][::-1] + arr[max_index+1:]\n        arr = arr[:i][::-1] + arr[i:]\n    return arr"}
{"task_id": "Mbpp/142", "completion": "\"\"\"\nWrite a function to count number items that are identical in the same position of three given lists.\nassert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,9],[2,1,3,1,2,6,7,9])==3\n\"\"\"\ndef count_samepair(list1,list2,list3):\n    count=0\n    for i in range(len(list1)):\n        if list1[i]==list2[i]==list3[i]:\n            count+=1\n    return count"}
{"task_id": "Mbpp/143", "completion": "\"\"\"\nWrite a function to find number of lists present in the given tuple.\nassert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2\n\"\"\"\ndef find_lists(tup):\n    count = 0\n    for i in tup:\n        if type(i) == list:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/145", "completion": "\"\"\"\nWrite a python function to find the maximum difference between any two elements in a given array.\nassert max_Abs_Diff((2,1,5,3)) == 4\n\"\"\"\ndef max_Abs_Diff(arr):\n    max_diff = 0\n    for i in range(len(arr)):\n        for j in range(i+1, len(arr)):\n            diff = abs(arr[i] - arr[j])\n            if diff > max_diff:\n                max_diff = diff\n    return max_diff\n"}
{"task_id": "Mbpp/160", "completion": "\"\"\"\nWrite a function that returns integers x and y that satisfy ax + by = n as a tuple, or return None if no solution exists.\nassert find_solution(2, 3, 7) == (2, 1)\n\"\"\"\ndef find_solution(a, b, n):\n    for x in range(n):\n        for y in range(n):\n            if a*x + b*y == n:\n                return (x, y)\n    return None\n"}
{"task_id": "Mbpp/161", "completion": "\"\"\"\nWrite a function to remove all elements from a given list present in another list.\nassert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [2, 4, 6, 8]) == [1, 3, 5, 7, 9, 10]\n\"\"\"\ndef remove_elements(list1, list2):\n    return [i for i in list1 if i not in list2]"}
{"task_id": "Mbpp/162", "completion": "\"\"\"\nWrite a function to calculate the sum (n - 2*i) from i=0 to n // 2, for instance n + (n-2) + (n-4)... (until n-x =< 0).\nassert sum_series(6) == 12\n\"\"\"\ndef sum_series(n):\n    if n == 0:\n        return 0\n    else:\n        return n + sum_series(n-2)\n"}
{"task_id": "Mbpp/164", "completion": "\"\"\"\nWrite a function to determine if the sum of the divisors of two integers are the same.\nassert are_equivalent(36, 57) == False\n\"\"\"\ndef are_equivalent(num1, num2):\n    sum1 = 0\n    sum2 = 0\n    for i in range(1, num1 + 1):\n        if num1 % i == 0:\n            sum1 += i\n    for i in range(1, num2 + 1):\n        if num2 % i == 0:\n            sum2 += i\n    return sum1 == sum2\n"}
{"task_id": "Mbpp/165", "completion": "\"\"\"\nWrite a function to count the number of characters in a string that occur at the same position in the string as in the English alphabet (case insensitive).\nassert count_char_position(\"xbcefg\") == 2\n\"\"\"\ndef count_char_position(string):\n    count = 0\n    for i in range(len(string)):\n        if string[i].lower() == chr(i + 97):\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/166", "completion": "\"\"\"\nWrite a function that counts the number of pairs of integers in a list that xor to an even number.\nassert find_even_pair([5, 4, 7, 2, 1]) == 4\n\"\"\"\nfrom typing import List\n\n\ndef find_even_pair(nums: List[int]) -> int:\n    count = 0\n    for i in range(len(nums)):\n        for j in range(i + 1, len(nums)):\n            if (nums[i] ^ nums[j]) % 2 == 0:\n                count += 1\n    return count\n\n"}
{"task_id": "Mbpp/167", "completion": "\"\"\"\nWrite a python function to find the smallest power of 2 greater than or equal to n.\nassert next_power_of_2(0) == 1\n\"\"\"\ndef next_power_of_2(n):\n    if n == 0:\n        return 1\n    else:\n        return 2**(n-1).bit_length()"}
{"task_id": "Mbpp/168", "completion": "\"\"\"\nWrite a function to count the number of occurrences of a number in a given list.\nassert frequency([1,2,3], 4) == 0\n\"\"\"\ndef frequency(list, num):\n    count = 0\n    for i in list:\n        if i == num:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/170", "completion": "\"\"\"\nWrite a function to find the sum of numbers in a list within a range specified by two indices.\nassert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 8, 10) == 29\n\"\"\"\ndef sum_range_list(list, start, end):\n    sum = 0\n    for i in range(start, end+1):\n        sum += list[i]\n    return sum\n"}
{"task_id": "Mbpp/171", "completion": "\"\"\"\nWrite a function to find the perimeter of a regular pentagon from the length of its sides.\nassert perimeter_pentagon(5) == 25\n\"\"\"\ndef perimeter_pentagon(side):\n    return side * 5\n"}
{"task_id": "Mbpp/172", "completion": "\"\"\"\nWrite a function to count the number of occurence of the string 'std' in a given string.\nassert count_occurance(\"letstdlenstdporstd\") == 3\n\"\"\"\ndef count_occurance(string):\n    count = 0\n    for i in range(len(string)-2):\n        if string[i:i+3] == 'std':\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/222", "completion": "\"\"\"\nWrite a function to check if all the elements in tuple have same data type or not.\nassert check_type((5, 6, 7, 3, 5, 6) ) == True\n\"\"\"\ndef check_type(tup):\n    return all(type(i) == type(tup[0]) for i in tup)\n"}
{"task_id": "Mbpp/223", "completion": "\"\"\"\nWrite a function that takes in a sorted array, its length (n), and an element and returns whether the element is the majority element in the given sorted array. (The majority element is the element that occurs more than n/2 times.)\nassert is_majority([1, 2, 3, 3, 3, 3, 10], 7, 3) == True\n\"\"\"\ndef is_majority(arr, n, element):\n    # Your code here\n    pass"}
{"task_id": "Mbpp/224", "completion": "\"\"\"\nWrite a python function to count the number of set bits (binary digits with value 1) in a given number.\nassert count_Set_Bits(2) == 1\n\"\"\"\ndef count_Set_Bits(n):\n    count = 0\n    while n:\n        count += n & 1\n        n >>= 1\n    return count\n"}
{"task_id": "Mbpp/226", "completion": "\"\"\"\nWrite a python function to remove the characters which have odd index values of a given string.\nassert odd_values_string('abcdef') == 'ace'\n\"\"\"\ndef odd_values_string(str):\n    result = \"\" \n    for i in range(len(str)):\n        if i % 2 == 0:\n            result = result + str[i]\n    return result"}
{"task_id": "Mbpp/227", "completion": "\"\"\"\nWrite a function to find minimum of three numbers.\nassert min_of_three(10,20,0)==0\n\"\"\"\ndef min_of_three(a,b,c):\n    if a<b and a<c:\n        return a\n    elif b<a and b<c:\n        return b\n    else:\n        return c"}
{"task_id": "Mbpp/229", "completion": "\"\"\"\nWrite a function that takes in an array and an integer n, and re-arranges the first n elements of the given array so that all negative elements appear before positive ones, and where the relative order among negative and positive elements is preserved.\nassert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]\n\"\"\"\ndef re_arrange_array(arr, n):\n    # Create two lists to store negative and positive elements\n    negative = []\n    positive = []\n    \n    # Iterate through the first n elements of the array\n    for i in range(n):\n        if arr[i] < 0:\n            negative.append(arr[i])\n        else:\n            positive.append(arr[i])\n    \n    # Concatenate the negative and positive lists\n    result = negative + positive\n    \n    return result\n\n# Test the function"}
{"task_id": "Mbpp/230", "completion": "\"\"\"\nWrite a function that takes in a string and character, replaces blank spaces in the string with the character, and returns the string.\nassert replace_blank(\"hello people\",'@')==(\"hello@people\")\n\"\"\"\ndef replace_blank(string,character):\n    return string.replace(\" \",character)"}
{"task_id": "Mbpp/232", "completion": "\"\"\"\nWrite a function that takes in a list and an integer n and returns a list containing the n largest items from the list.\nassert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],2))==set([100,90])\n\"\"\"\ndef larg_nnum(list,n):\n    list.sort()\n    return list[-n:]\n"}
{"task_id": "Mbpp/233", "completion": "\"\"\"\nWrite a function to find the lateral surface area of a cylinder.\nassert math.isclose(lateralsuface_cylinder(10,5), 314.15000000000003, rel_tol=0.001)\n\"\"\"\nimport math\ndef lateralsuface_cylinder(radius,height):\n    return 2*math.pi*radius*height"}
{"task_id": "Mbpp/234", "completion": "\"\"\"\nWrite a function to find the volume of a cube given its side length.\nassert volume_cube(3)==27\n\"\"\"\ndef volume_cube(side_length):\n    return side_length**3\n"}
{"task_id": "Mbpp/235", "completion": "\"\"\"\nWrite a python function to set all even bits of a given number.\nassert even_bit_set_number(10) == 10\n\"\"\"\ndef even_bit_set_number(n):\n    return n | 0xAAAAAAAA\n"}
{"task_id": "Mbpp/237", "completion": "\"\"\"\nWrite a function that takes in a list of tuples and returns a dictionary mapping each unique tuple to the number of times it occurs in the list.\nassert check_occurences([(3, 1), (1, 3), (2, 5), (5, 2), (6, 3)] ) == {(1, 3): 2, (2, 5): 2, (3, 6): 1}\n\"\"\"\ndef check_occurences(list_of_tuples):\n    dict = {}\n    for i in list_of_tuples:\n        if i in dict:\n            dict[i] += 1\n        else:\n            dict[i] = 1\n    return dict\n"}
{"task_id": "Mbpp/238", "completion": "\"\"\"\nWrite a python function to count the number of non-empty substrings of a given string.\nassert number_of_substrings(\"abc\") == 6\n\"\"\"\ndef number_of_substrings(s):\n    return sum(len(s) - i for i in range(len(s)))\n"}
{"task_id": "Mbpp/239", "completion": "\"\"\"\nWrite a function that takes in positive integers m and n and finds the number of possible sequences of length n, such that each element is a positive integer and is greater than or equal to twice the previous element but less than or equal to m.\nassert get_total_number_of_sequences(10, 4) == 4\n\"\"\"\nfrom functools import lru_cache\n\n\n@lru_cache(maxsize=None)\ndef get_total_number_of_sequences(m, n):\n    if n == 1:\n        return m\n    return sum(get_total_number_of_sequences(m, n - 1) for i in range(1, m + 1))"}
{"task_id": "Mbpp/240", "completion": "\"\"\"\nWrite a function that takes in two lists and replaces the last element of the first list with the elements of the second list.\nassert replace_list([1, 3, 5, 7, 9, 10],[2, 4, 6, 8])==[1, 3, 5, 7, 9, 2, 4, 6, 8]\n\"\"\"\ndef replace_list(list1, list2):\n    list1[-1:] = list2\n    return list1\n"}
{"task_id": "Mbpp/242", "completion": "\"\"\"\nWrite a function to count the total number of characters in a string.\nassert count_charac(\"python programming\")==18\n\"\"\"\ndef count_charac(string):\n    return len(string)\n"}
{"task_id": "Mbpp/244", "completion": "\"\"\"\nWrite a python function to find the next perfect square greater than a given number.\nassert next_Perfect_Square(35) == 36\n\"\"\"\nimport math\ndef next_Perfect_Square(num):\n    if num < 0:\n        return \"Invalid input\"\n    else:\n        sqrt = math.sqrt(num)\n        if sqrt.is_integer():\n            return int((sqrt + 1) ** 2)\n        else:\n            return \"No perfect square greater than the given number\"\n"}
{"task_id": "Mbpp/245", "completion": "\"\"\"\nWrite a function that takes an array and finds the maximum sum of a bitonic subsequence for the given array, where a sequence is bitonic if it is first increasing and then decreasing.\nassert max_sum([1, 15, 51, 45, 33, 100, 12, 18, 9]) == 194\n\"\"\"\ndef max_sum(arr):\n    n = len(arr)\n    inc = [0] * n\n    dec = [0] * n\n    inc[0] = arr[0]\n    dec[n-1] = arr[n-1]\n    for i in range(1, n):\n        inc[i] = max(inc[i-1], arr[i])\n    for i in range(n-2, -1, -1):\n        dec[i] = max(dec[i+1], arr[i])\n    max_sum = 0\n    for i in range(n):\n        max_sum = max(max_sum, inc[i] + dec[i] - arr[i])\n    return max_sum\n"}
{"task_id": "Mbpp/247", "completion": "\"\"\"\nWrite a function to find the length of the longest palindromic subsequence in the given string.\nassert lps(\"TENS FOR TENS\") == 5\n\"\"\"\ndef lps(s):\n    n = len(s)\n    dp = [[0 for _ in range(n)] for _ in range(n)]\n    for i in range(n):\n        dp[i][i] = 1\n    for i in range(n-1, -1, -1):\n        for j in range(i+1, n):\n            if s[i] == s[j]:\n                dp[i][j] = dp[i+1][j-1] + 2\n            else:\n                dp[i][j] = max(dp[i+1][j], dp[i][j-1])\n    return dp[0][n-1]"}
{"task_id": "Mbpp/249", "completion": "\"\"\"\nWrite a function to find the intersection of two arrays.\nassert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]\n\"\"\"\ndef intersection_array(arr1, arr2):\n    return list(set(arr1) & set(arr2))\n"}
{"task_id": "Mbpp/250", "completion": "\"\"\"\nWrite a python function that takes in a tuple and an element and counts the occcurences of the element in the tuple.\nassert count_X((10, 8, 5, 2, 10, 15, 10, 8, 5, 8, 8, 2),4) == 0\n\"\"\"\ndef count_X(tup, element):\n    count = 0\n    for i in tup:\n        if i == element:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/251", "completion": "\"\"\"\nWrite a function that takes in a list and an element and inserts the element before each element in the list, and returns the resulting list.\nassert insert_element(['Red', 'Green', 'Black'] ,'c')==['c', 'Red', 'c', 'Green', 'c', 'Black']\n\"\"\"\ndef insert_element(list1,element):\n    list2=[]\n    for i in list1:\n        list2.append(element)\n        list2.append(i)\n    return list2"}
{"task_id": "Mbpp/252", "completion": "\"\"\"\nWrite a python function to convert complex numbers to polar coordinates.\nassert convert(1) == (1.0, 0.0)\n\"\"\"\nimport math\n\n\ndef convert(num):\n    return (abs(num), math.atan(num.imag / num.real))\n\n"}
{"task_id": "Mbpp/253", "completion": "\"\"\"\nWrite a python function that returns the number of integer elements in a given list.\nassert count_integer([1,2,'abc',1.2]) == 2\n\"\"\"\ndef count_integer(lst):\n    count = 0\n    for i in lst:\n        if type(i) == int:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/255", "completion": "\"\"\"\nWrite a function that takes in a list and length n, and generates all combinations (with repetition) of the elements of the list and returns a list with a tuple for each combination.\nassert combinations_colors( [\"Red\",\"Green\",\"Blue\"],1)==[('Red',), ('Green',), ('Blue',)]\n\"\"\"\ndef combinations_colors(colors, n):\n    if n == 0:\n        return [()]\n    else:\n        result = []\n        for i in range(len(colors)):\n            for j in combinations_colors(colors, n-1):\n                result.append((colors[i],) + j)\n        return result\n"}
{"task_id": "Mbpp/256", "completion": "\"\"\"\nWrite a python function that takes in a non-negative number and returns the number of prime numbers less than the given non-negative number.\nassert count_Primes_nums(5) == 2\n\"\"\"\ndef count_Primes_nums(n):\n    count = 0\n    for i in range(2, n):\n        for j in range(2, i):\n            if i % j == 0:\n                break\n        else:\n            count += 1\n    return count"}
{"task_id": "Mbpp/257", "completion": "\"\"\"\nWrite a function that takes in two numbers and returns a tuple with the second number and then the first number.\nassert swap_numbers(10,20)==(20,10)\n\"\"\"\ndef swap_numbers(a,b):\n    return b,a"}
{"task_id": "Mbpp/259", "completion": "\"\"\"\nWrite a function to maximize the given two tuples.\nassert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))\n\"\"\"\ndef maximize_elements(tuple1, tuple2):\n    result = []\n    for i in range(len(tuple1)):\n        max_element = max(tuple1[i], tuple2[i])\n        result.append(max_element)\n    return tuple(result)\n\n# Test the function\ntuple1 = ((1, 3), (4, 5), (2, 9), (1, 10))\ntuple2 = ((6, 7), (3, 9), (1, 1), (7, 3))\nmaximized_tuple = maximize_elements(tuple1, tuple2)"}
{"task_id": "Mbpp/260", "completion": "\"\"\"\nWrite a function to find the nth newman–shanks–williams prime number.\nassert newman_prime(3) == 7\n\"\"\"\nimport math\nimport time\nimport random\nimport numpy as np\nimport matplotlib.pyplot as plt\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi\nfrom sympy import primerange\nfrom sympy import isprime\nfrom sympy import sieve\nfrom sympy import primerange\nfrom sympy import nextprime\nfrom sympy import prevprime\nfrom sympy import prime\nfrom sympy import primepi"}
{"task_id": "Mbpp/261", "completion": "\"\"\"\nWrite a function that takes in two tuples and performs mathematical division operation element-wise across the given tuples.\nassert division_elements((10, 4, 6, 9),(5, 2, 3, 3)) == (2, 2, 2, 3)\n\"\"\"\ndef division_elements(tuple1, tuple2):\n    result = []\n    for i in range(len(tuple1)):\n        result.append(tuple1[i] / tuple2[i])\n    return tuple(result)"}
{"task_id": "Mbpp/262", "completion": "\"\"\"\nWrite a function that takes in a list and an integer L and splits the given list into two parts where the length of the first part of the list is L, and returns the resulting lists in a tuple.\nassert split_two_parts([1,1,2,3,4,4,5,1],3)==([1, 1, 2], [3, 4, 4, 5, 1])\n\"\"\"\ndef split_two_parts(lst, L):\n    return (lst[:L], lst[L:])"}
{"task_id": "Mbpp/264", "completion": "\"\"\"\nWrite a function to calculate a dog's age in dog's years.\nassert dog_age(12)==61\n\"\"\"\ndef dog_age(age):\n    return age*7"}
{"task_id": "Mbpp/265", "completion": "\"\"\"\nWrite a function that takes in a list and an integer n and splits a list for every nth element, returning a list of the resulting lists.\nassert list_split(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'],3)==[['a', 'd', 'g', 'j', 'm'], ['b', 'e', 'h', 'k', 'n'], ['c', 'f', 'i', 'l']]\n\"\"\"\ndef list_split(lst, n):\n    return [lst[i::n] for i in range(n)]"}
{"task_id": "Mbpp/266", "completion": "\"\"\"\nWrite a function to find the lateral surface area of a cube given its side length.\nassert lateralsurface_cube(5)==100\n\"\"\"\ndef lateralsurface_cube(side_length):\n    return 4*side_length**2\n"}
{"task_id": "Mbpp/267", "completion": "\"\"\"\nWrite a python function that takes in an integer n and returns the sum of the squares of the first n odd natural numbers.\nassert square_Sum(2) == 10\n\"\"\"\ndef square_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**2\n    return sum\n"}
{"task_id": "Mbpp/268", "completion": "\"\"\"\nWrite a function to find the n'th star number.\nassert find_star_num(3) == 37\n\"\"\"\ndef find_star_num(n):\n    return 6 * (n * (n - 1)) // 2 + 1\n"}
{"task_id": "Mbpp/269", "completion": "\"\"\"\nWrite a function to find the ascii value of a character.\nassert ascii_value('A')==65\n\"\"\"\ndef ascii_value(char):\n    return ord(char)\n"}
{"task_id": "Mbpp/270", "completion": "\"\"\"\nWrite a python function to find the sum of even numbers at even positions of a list.\nassert sum_even_and_even_index([5, 6, 12, 1, 18, 8]) == 30\n\"\"\"\ndef sum_even_and_even_index(lst):\n    sum = 0\n    for i in range(0, len(lst), 2):\n        if lst[i] % 2 == 0:\n            sum += lst[i]\n    return sum\n"}
{"task_id": "Mbpp/271", "completion": "\"\"\"\nWrite a python function that takes in an integer n and finds the sum of the first n even natural numbers that are raised to the fifth power.\nassert even_Power_Sum(2) == 1056\n\"\"\"\ndef even_Power_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += (2*i)**5\n    return sum\n"}
{"task_id": "Mbpp/272", "completion": "\"\"\"\nWrite a function that takes in a list of tuples and returns a list containing the rear element of each tuple.\nassert rear_extract([(1, 'Rash', 21), (2, 'Varsha', 20), (3, 'Kil', 19)]) == [21, 20, 19]\n\"\"\"\ndef rear_extract(tuples):\n    return [tuples[i][2] for i in range(len(tuples))]"}
{"task_id": "Mbpp/273", "completion": "\"\"\"\nWrite a function that takes in two tuples and subtracts the elements of the first tuple by the elements of the second tuple with the same index.\nassert substract_elements((10, 4, 5), (2, 5, 18)) == (8, -1, -13)\n\"\"\"\ndef substract_elements(tuple1, tuple2):\n    return tuple(a-b for a,b in zip(tuple1, tuple2))"}
{"task_id": "Mbpp/274", "completion": "\"\"\"\nWrite a python function that takes in a positive integer n and finds the sum of even index binomial coefficients.\nassert even_binomial_Coeff_Sum(4) == 8\n\"\"\"\nfrom math import factorial\n\n\ndef even_binomial_Coeff_Sum(n):\n    sum = 0\n    for i in range(0, n + 1, 2):\n        sum += factorial(n) / (factorial(i) * factorial(n - i))\n    return int(sum)\n\n"}
{"task_id": "Mbpp/276", "completion": "\"\"\"\nWrite a function that takes in the radius and height of a cylinder and returns the the volume.\nassert math.isclose(volume_cylinder(10,5), 1570.7500000000002, rel_tol=0.001)\n\"\"\"\nimport math\ndef volume_cylinder(radius, height):\n    return math.pi * radius**2 * height\n"}
{"task_id": "Mbpp/277", "completion": "\"\"\"\nWrite a function that takes in a dictionary and integer n and filters the dictionary to only include entries with values greater than or equal to n.\nassert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},170)=={'Cierra Vega': 175, 'Alden Cantrell': 180, 'Pierre Cox': 190}\n\"\"\"\ndef dict_filter(dictionary, n):\n    return {key: value for key, value in dictionary.items() if value >= n}"}
{"task_id": "Mbpp/278", "completion": "\"\"\"\nWrite a function to find the number of elements that occurs before the tuple element in the given tuple.\nassert count_first_elements((1, 5, 7, (4, 6), 10) ) == 3\n\"\"\"\ndef count_first_elements(tup):\n    count = 0\n    for i in tup:\n        if isinstance(i, tuple):\n            break\n        count += 1\n    return count\n"}
{"task_id": "Mbpp/279", "completion": "\"\"\"\nWrite a function to find the nth decagonal number.\nassert is_num_decagonal(3) == 27\n\"\"\"\ndef is_num_decagonal(n):\n    return 4*n**2 - 3*n\n"}
{"task_id": "Mbpp/280", "completion": "\"\"\"\nWrite a function that takes in an array and element and returns a tuple containing a boolean that indicates if the element is in the array and the index position of the element (or -1 if the element is not found).\nassert sequential_search([11,23,58,31,56,77,43,12,65,19],31) == (True, 3)\n\"\"\"\ndef sequential_search(arr, element):\n    for i in range(len(arr)):\n        if arr[i] == element:\n            return True, i\n    return False, -1\n"}
{"task_id": "Mbpp/281", "completion": "\"\"\"\nWrite a python function to check if the elements of a given list are unique or not.\nassert all_unique([1,2,3]) == True\n\"\"\"\ndef all_unique(lst):\n    return len(lst) == len(set(lst))\n\nx = [1,2,3,4]\ny = [1,2,2,3]"}
{"task_id": "Mbpp/282", "completion": "\"\"\"\nWrite a function to subtract two lists element-wise.\nassert sub_list([1, 2, 3],[4,5,6])==[-3,-3,-3]\n\"\"\"\ndef sub_list(list1,list2):\n    return [list1[i]-list2[i] for i in range(len(list1))]\n"}
{"task_id": "Mbpp/283", "completion": "\"\"\"\nWrite a python function takes in an integer and check whether the frequency of each digit in the integer is less than or equal to the digit itself.\nassert validate(1234) == True\n\"\"\"\ndef validate(num):\n    num = str(num)\n    for i in num:\n        if num.count(i) > int(i):\n            return False\n    return True\n"}
{"task_id": "Mbpp/284", "completion": "\"\"\"\nWrite a function that takes in a list and element and checks whether all items in the list are equal to the given element.\nassert check_element([\"green\", \"orange\", \"black\", \"white\"],'blue')==False\n\"\"\"\ndef check_element(lst,element):\n    for i in lst:\n        if i != element:\n            return False\n    return True"}
{"task_id": "Mbpp/285", "completion": "\"\"\"\nWrite a function that checks whether a string contains the 'a' character followed by two or three 'b' characters.\nassert text_match_two_three(\"ac\")==(False)\n\"\"\"\nimport re\ndef text_match_two_three(text):\n    pattern = r'a[b]{2,3}'\n    if re.search(pattern, text):\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/286", "completion": "\"\"\"\nWrite a function to find the largest sum of a contiguous array in the modified array which is formed by repeating the given array k times.\nassert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30\n\"\"\"\ndef max_sub_array_sum_repeated(arr, n, k):\n    max_so_far = 0\n    max_ending_here = 0\n    for i in range(n):\n        max_ending_here = max_ending_here + arr[i]\n        if max_ending_here < 0:\n            max_ending_here = 0\n        if max_so_far < max_ending_here:\n            max_so_far = max_ending_here\n    return max_so_far * k\n"}
{"task_id": "Mbpp/287", "completion": "\"\"\"\nWrite a python function takes in an integer n and returns the sum of squares of first n even natural numbers.\nassert square_Sum(2) == 20\n\"\"\"\ndef square_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**2\n    return sum\n"}
{"task_id": "Mbpp/290", "completion": "\"\"\"\nWrite a function to find the list of maximum length in a list of lists.\nassert max_length([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])\n\"\"\"\ndef max_length(lists):\n    max_length = 0\n    max_list = []\n    for lst in lists:\n        if len(lst) > max_length:\n            max_length = len(lst)\n            max_list = lst\n    return max_length, max_list\n"}
{"task_id": "Mbpp/292", "completion": "\"\"\"\nWrite a python function to find quotient of two numbers (rounded down to the nearest integer).\nassert find(10,3) == 3\n\"\"\"\ndef find(a,b):\n    return a//b"}
{"task_id": "Mbpp/293", "completion": "\"\"\"\nWrite a function to find the third side of a right angled triangle.\nassert otherside_rightangle(7,8)==10.63014581273465\n\"\"\"\nimport math\ndef otherside_rightangle(a,b):\n    return math.sqrt(a**2+b**2)\n"}
{"task_id": "Mbpp/294", "completion": "\"\"\"\nWrite a function to find the maximum value in a given heterogeneous list.\nassert max_val(['Python', 3, 2, 4, 5, 'version'])==5\n\"\"\"\ndef max_val(lst):\n    max_val = lst[0]\n    for i in lst:\n        if isinstance(i, int) and i > max_val:\n            max_val = i\n    return max_val"}
{"task_id": "Mbpp/295", "completion": "\"\"\"\nWrite a function to return the sum of all divisors of a number.\nassert sum_div(8)==7\n\"\"\"\ndef sum_div(n):\n    sum=0\n    for i in range(1,n):\n        if n%i==0:\n            sum+=i\n    return sum"}
{"task_id": "Mbpp/296", "completion": "\"\"\"\nWrite a python function to count inversions in an array.\nassert get_Inv_Count([1,20,6,4,5]) == 5\n\"\"\"\ndef get_Inv_Count(arr):\n    count = 0\n    for i in range(len(arr)):\n        for j in range(i+1, len(arr)):\n            if arr[i] > arr[j]:\n                count += 1\n    return count\n"}
{"task_id": "Mbpp/297", "completion": "\"\"\"\nWrite a function to flatten a given nested list structure.\nassert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]\n\"\"\"\ndef flatten_list(nested_list):\n    flattened_list = []\n    for item in nested_list:\n        if isinstance(item, list):\n            flattened_list.extend(flatten_list(item))\n        else:\n            flattened_list.append(item)\n    return flattened_list\n\n# Test the function"}
{"task_id": "Mbpp/299", "completion": "\"\"\"\nWrite a function to calculate the maximum aggregate from the list of tuples.\nassert max_aggregate([('Juan Whelan',90),('Sabah Colley',88),('Peter Nichols',7),('Juan Whelan',122),('Sabah Colley',84)])==('Juan Whelan', 212)\n\"\"\"\ndef max_aggregate(list_of_tuples):\n    max_aggregate = 0\n    max_aggregate_name = \"\"\n    for name, aggregate in list_of_tuples:\n        if aggregate > max_aggregate:\n            max_aggregate = aggregate\n            max_aggregate_name = name\n    return max_aggregate_name, max_aggregate\n"}
{"task_id": "Mbpp/300", "completion": "\"\"\"\nWrite a function to find the count of all binary sequences of length 2n such that sum of first n bits is same as sum of last n bits.\nassert math.isclose(count_binary_seq(1), 2.0, rel_tol=0.001)\n\"\"\"\nimport math\ndef count_binary_seq(n):\n    if n == 0:\n        return 1\n    if n == 1:\n        return 2\n    return 2 * count_binary_seq(n - 1) + count_binary_seq(n - 2)\n"}
{"task_id": "Mbpp/301", "completion": "\"\"\"\nWrite a function to find the depth of a dictionary.\nassert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4\n\"\"\"\ndef dict_depth(d):\n    if not isinstance(d, dict) or not d:\n        return 0\n    else:\n        return 1 + max(dict_depth(v) for v in d.values())"}
{"task_id": "Mbpp/305", "completion": "\"\"\"\nWrite a function to return two words from a list of words starting with letter 'p'.\nassert start_withp([\"Python PHP\", \"Java JavaScript\", \"c c++\"])==('Python', 'PHP')\n\"\"\"\ndef start_withp(words):\n    p_words = []\n    for word in words:\n        if word.startswith('p'):\n            p_words.append(word)\n    return tuple(p_words)\n\nwords = [\"Python PHP\", \"Java JavaScript\", \"c c++\"]\nresult = start_withp(words)"}
{"task_id": "Mbpp/306", "completion": "\"\"\"\nWrite a function to find the maximum sum of increasing subsequence from prefix until ith index and also including a given kth element which is after i, i.e., k > i .\nassert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11\n\"\"\"\ndef max_sum_increasing_subseq(arr, n, i, k):\n    if i == n:\n        return 0\n    if i == k:\n        return arr[i] + max_sum_increasing_subseq(arr, n, i+1, k)\n    return max(arr[i] + max_sum_increasing_subseq(arr, n, i+1, k), max_sum_increasing_subseq(arr, n, i+1, k))\n"}
{"task_id": "Mbpp/308", "completion": "\"\"\"\nWrite a function to find the specified number of largest products from two given lists, selecting one factor from each list.\nassert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],3)==[60, 54, 50]\n\"\"\"\ndef large_product(list1,list2,n):\n    list3=[]\n    for i in list1:\n        for j in list2:\n            list3.append(i*j)\n    list3.sort(reverse=True)\n    return list3[:n]"}
{"task_id": "Mbpp/309", "completion": "\"\"\"\nWrite a python function to find the maximum of two numbers.\nassert maximum(5,10) == 10\n\"\"\"\ndef maximum(a,b):\n    if a>b:\n        return a\n    else:\n        return b"}
{"task_id": "Mbpp/310", "completion": "\"\"\"\nWrite a function to convert a given string to a tuple of characters.\nassert string_to_tuple(\"python 3.0\")==('p', 'y', 't', 'h', 'o', 'n', '3', '.', '0')\n\"\"\"\ndef string_to_tuple(string):\n    return tuple(string)\n"}
{"task_id": "Mbpp/311", "completion": "\"\"\"\nWrite a python function to set the left most unset bit.\nassert set_left_most_unset_bit(10) == 14\n\"\"\"\ndef set_left_most_unset_bit(n):\n    return n | (n + 1)"}
{"task_id": "Mbpp/312", "completion": "\"\"\"\nWrite a function to find the volume of a cone.\nassert math.isclose(volume_cone(5,12), 314.15926535897927, rel_tol=0.001)\n\"\"\"\nimport math\ndef volume_cone(radius,height):\n    return (1/3)*math.pi*radius*radius*height"}
{"task_id": "Mbpp/388", "completion": "\"\"\"\nWrite a python function to find the highest power of 2 that is less than or equal to n.\nassert highest_Power_of_2(10) == 8\n\"\"\"\ndef highest_Power_of_2(n):\n    if n == 0:\n        return 0\n    else:\n        return 2 ** (n.bit_length() - 1)"}
{"task_id": "Mbpp/389", "completion": "\"\"\"\nWrite a function to find the n'th lucas number.\nassert find_lucas(9) == 76\n\"\"\"\ndef find_lucas(n):\n    if n == 0:\n        return 2\n    elif n == 1:\n        return 1\n    else:\n        return find_lucas(n-1) + find_lucas(n-2)"}
{"task_id": "Mbpp/390", "completion": "\"\"\"\nWrite a function to apply a given format string to all of the elements in a list.\nassert add_string([1,2,3,4],'temp{0}')==['temp1', 'temp2', 'temp3', 'temp4']\n\"\"\"\ndef add_string(list1,format_string):\n    list2=[]\n    for i in list1:\n        list2.append(format_string.format(i))\n    return list2"}
{"task_id": "Mbpp/391", "completion": "\"\"\"\nWrite a function to convert more than one list to nested dictionary.\nassert convert_list_dictionary([\"S001\", \"S002\", \"S003\", \"S004\"],[\"Adina Park\", \"Leyton Marsh\", \"Duncan Boyle\", \"Saim Richards\"] ,[85, 98, 89, 92])==[{'S001': {'Adina Park': 85}}, {'S002': {'Leyton Marsh': 98}}, {'S003': {'Duncan Boyle': 89}}, {'S004': {'Saim Richards': 92}}]\n\"\"\"\ndef convert_list_dictionary(list1,list2,list3):\n    result = [{list1[i]:{list2[i]:list3[i]}} for i in range(len(list1))]\n    return result"}
{"task_id": "Mbpp/392", "completion": "\"\"\"\nWrite a function to find the maximum sum possible by using the given equation f(n) = max( (f(n/2) + f(n/3) + f(n/4) + f(n/5)), n).\nassert get_max_sum(60) == 106\n\"\"\"\ndef get_max_sum(n):\n    if n <= 1:\n        return n\n    return max(n, get_max_sum(n//2) + get_max_sum(n//3) + get_max_sum(n//4) + get_max_sum(n//5))\n"}
{"task_id": "Mbpp/394", "completion": "\"\"\"\nWrite a function to check if given tuple contains no duplicates.\nassert check_distinct((1, 4, 5, 6, 1, 4)) == False\n\"\"\"\ndef check_distinct(tup):\n    return len(tup) == len(set(tup))\n"}
{"task_id": "Mbpp/395", "completion": "\"\"\"\nWrite a python function to find the first non-repeated character in a given string.\nassert first_non_repeating_character(\"abcabc\") == None\n\"\"\"\ndef first_non_repeating_character(string):\n    for i in string:\n        if string.count(i) == 1:\n            return i\n    return None\n"}
{"task_id": "Mbpp/396", "completion": "\"\"\"\nWrite a function to check whether the given string starts and ends with the same character or not.\nassert check_char(\"abba\") == \"Valid\"\n\"\"\"\ndef check_char(string):\n    if string[0] == string[-1]:\n        return \"Valid\"\n    else:\n        return \"Invalid\"\n"}
{"task_id": "Mbpp/397", "completion": "\"\"\"\nWrite a function to find the median of three numbers.\nassert median_numbers(25,55,65)==55.0\n\"\"\"\ndef median_numbers(a,b,c):\n    if a>b and a<c or a<b and a>c:\n        return a\n    elif b>a and b<c or b<a and b>c:\n        return b\n    else:\n        return c"}
{"task_id": "Mbpp/398", "completion": "\"\"\"\nWrite a function to compute the sum of digits of each number of a given list.\nassert sum_of_digits([10,2,56])==14\n\"\"\"\ndef sum_of_digits(list):\n    sum=0\n    for i in list:\n        sum+=i\n    return sum"}
{"task_id": "Mbpp/400", "completion": "\"\"\"\nWrite a function to extract the number of unique tuples in the given list.\nassert extract_freq([(3, 4), (1, 2), (4, 3), (5, 6)] ) == 3\n\"\"\"\ndef extract_freq(lst):\n    return len(set(lst))"}
{"task_id": "Mbpp/404", "completion": "\"\"\"\nWrite a python function to find the minimum of two numbers.\nassert minimum(1,2) == 1\n\"\"\"\ndef minimum(a,b):\n    if a < b:\n        return a\n    else:\n        return b\n"}
{"task_id": "Mbpp/405", "completion": "\"\"\"\nWrite a function to check whether an element exists within a tuple.\nassert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'r')==True\n\"\"\"\ndef check_tuplex(tuplex, element):\n    return element in tuplex"}
{"task_id": "Mbpp/406", "completion": "\"\"\"\nWrite a python function to find whether the parity of a given number is odd.\nassert find_Parity(12) == False\n\"\"\"\ndef find_Parity(num):\n    if num % 2 == 0:\n        return False\n    else:\n        return True\n"}
{"task_id": "Mbpp/407", "completion": "\"\"\"\nWrite a function to create the next bigger number by rearranging the digits of a given number.\nassert rearrange_bigger(12)==21\n\"\"\"\ndef rearrange_bigger(num):\n    num = list(str(num))\n    for i in range(len(num)-1,0,-1):\n        if num[i]>num[i-1]:\n            num[i],num[i-1] = num[i-1],num[i]\n            return int(\"\".join(num))\n    return -1"}
{"task_id": "Mbpp/409", "completion": "\"\"\"\nWrite a function to find the minimum product from the pairs of tuples within a given list.\nassert min_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==8\n\"\"\"\ndef min_product_tuple(list1):\n    min_product = list1[0][0] * list1[0][1]\n    for i in range(1, len(list1)):\n        if list1[i][0] * list1[i][1] < min_product:\n            min_product = list1[i][0] * list1[i][1]\n    return min_product"}
{"task_id": "Mbpp/410", "completion": "\"\"\"\nWrite a function to find the minimum value in a given heterogeneous list.\nassert min_val(['Python', 3, 2, 4, 5, 'version'])==2\n\"\"\"\ndef min_val(lst):\n    min_val = lst[0]\n    for i in lst:\n        if isinstance(i, int) and i < min_val:\n            min_val = i\n    return min_val"}
{"task_id": "Mbpp/412", "completion": "\"\"\"\nWrite a python function to remove odd numbers from a given list.\nassert remove_odd([1,2,3]) == [2]\n\"\"\"\ndef remove_odd(numbers):\n    return [num for num in numbers if num % 2 == 0]\n\n# Test the function"}
{"task_id": "Mbpp/413", "completion": "\"\"\"\nWrite a function to extract the nth element from a given list of tuples.\nassert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,0)==['Greyson Fulton', 'Brady Kent', 'Wyatt Knott', 'Beau Turnbull']\n\"\"\"\ndef extract_nth_element(list_of_tuples, n):\n    return [tup[n] for tup in list_of_tuples]"}
{"task_id": "Mbpp/414", "completion": "\"\"\"\nWrite a python function to check whether any value in a sequence exists in a sequence or not.\nassert overlapping([1,2,3,4,5],[6,7,8,9]) == False\n\"\"\"\ndef overlapping(list1,list2):\n    for i in list1:\n        for j in list2:\n            if i==j:\n                return True\n    return False"}
{"task_id": "Mbpp/415", "completion": "\"\"\"\nWrite a python function to find a pair with highest product from a given array of integers.\nassert max_Product([1,2,3,4,7,0,8,4]) == (7,8)\n\"\"\"\ndef max_Product(arr):\n    max1 = max(arr)\n    arr.remove(max1)\n    max2 = max(arr)\n    return (max1,max2)\n"}
{"task_id": "Mbpp/418", "completion": "\"\"\"\nWrite a python function to find the element of a list having maximum length.\nassert Find_Max([['A'],['A','B'],['A','B','C']]) == ['A','B','C']\n\"\"\"\ndef Find_Max(list1):\n    max = 0\n    for i in list1:\n        if len(i) > max:\n            max = len(i)\n            max_list = i\n    return max_list"}
{"task_id": "Mbpp/419", "completion": "\"\"\"\nWrite a function to round every number of a given list of numbers and print the total sum multiplied by the length of the list.\nassert round_and_sum([22.4, 4.0, -16.22, -9.10, 11.00, -12.22, 14.20, -5.20, 17.50])==243\n\"\"\"\ndef round_and_sum(lst):\n    return sum([round(i) for i in lst]) * len(lst)\n"}
{"task_id": "Mbpp/420", "completion": "\"\"\"\nWrite a python function to find the cube sum of first n even natural numbers.\nassert cube_Sum(2) == 72\n\"\"\"\ndef cube_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**3\n    return sum\n"}
{"task_id": "Mbpp/421", "completion": "\"\"\"\nWrite a function to concatenate each element of tuple by the delimiter.\nassert concatenate_tuple((\"ID\", \"is\", 4, \"UTS\") ) == 'ID-is-4-UTS'\n\"\"\"\ndef concatenate_tuple(tup):\n    return '-'.join(str(item) for item in tup)\n\n# Test the function"}
{"task_id": "Mbpp/422", "completion": "\"\"\"\nWrite a python function to find the average of cubes of first n natural numbers.\nassert find_Average_Of_Cube(2) == 4.5\n\"\"\"\ndef find_Average_Of_Cube(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**3\n    return sum/n\n"}
{"task_id": "Mbpp/424", "completion": "\"\"\"\nWrite a function to extract only the rear index element of each string in the given tuple.\nassert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']\n\"\"\"\ndef extract_rear(tup):\n    return [i[-1] for i in tup]\n"}
{"task_id": "Mbpp/425", "completion": "\"\"\"\nWrite a function to count the number of sublists containing a particular element.\nassert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3\n\"\"\"\ndef count_element_in_list(list1,element):\n    count=0\n    for i in list1:\n        if element in i:\n            count+=1\n    return count"}
{"task_id": "Mbpp/426", "completion": "\"\"\"\nWrite a function to filter odd numbers.\nassert filter_oddnumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1,3,5,7,9]\n\"\"\"\ndef filter_oddnumbers(list1):\n    list2 = []\n    for i in list1:\n        if i % 2 != 0:\n            list2.append(i)\n    return list2"}
{"task_id": "Mbpp/427", "completion": "\"\"\"\nWrite a function to convert a date of yyyy-mm-dd format to dd-mm-yyyy format.\nassert change_date_format(\"2026-01-02\") == '02-01-2026'\n\"\"\"\nimport re\n\n\ndef change_date_format(date):\n    return re.sub(r'(\\d{4})-(\\d{2})-(\\d{2})', r'\\3-\\2-\\1', date)\n\n"}
{"task_id": "Mbpp/428", "completion": "\"\"\"\nWrite a function to sort the given array by using shell sort.\nassert shell_sort([12, 23, 4, 5, 3, 2, 12, 81, 56, 95]) == [2, 3, 4, 5, 12, 12, 23, 56, 81, 95]\n\"\"\"\ndef shell_sort(arr):\n    n = len(arr)\n    gap = n // 2\n    while gap > 0:\n        for i in range(gap, n):\n            temp = arr[i]\n            j = i\n            while j >= gap and arr[j - gap] > temp:\n                arr[j] = arr[j - gap]\n                j -= gap\n            arr[j] = temp\n        gap //= 2\n    return arr\n"}
{"task_id": "Mbpp/429", "completion": "\"\"\"\nWrite a function to extract the elementwise and tuples from the given two tuples.\nassert and_tuples((10, 4, 6, 9), (5, 2, 3, 3)) == (0, 0, 2, 1)\n\"\"\"\ndef and_tuples(t1, t2):\n    return tuple(x1 - x2 for x1, x2 in zip(t1, t2))"}
{"task_id": "Mbpp/430", "completion": "\"\"\"\nWrite a function to find the directrix of a parabola.\nassert parabola_directrix(5,3,2)==-198\n\"\"\"\ndef parabola_directrix(a,b,c):\n    return -c/(4*a)\n"}
{"task_id": "Mbpp/431", "completion": "\"\"\"\nWrite a function that takes two lists and returns true if they have at least one common element.\nassert common_element([1,2,3,4,5], [5,6,7,8,9])==True\n\"\"\"\ndef common_element(list1, list2):\n    for i in list1:\n        for j in list2:\n            if i == j:\n                return True\n    return False\n"}
{"task_id": "Mbpp/432", "completion": "\"\"\"\nWrite a function to find the median length of a trapezium.\nassert median_trapezium(15,25,35)==20\n\"\"\"\ndef median_trapezium(a,b,c):\n    return (a+b+c)/3"}
{"task_id": "Mbpp/433", "completion": "\"\"\"\nWrite a function to check whether the entered number is greater than the elements of the given array.\nassert check_greater([1, 2, 3, 4, 5], 4) == False\n\"\"\"\ndef check_greater(arr, num):\n    for i in arr:\n        if i > num:\n            return True\n    return False\n"}
{"task_id": "Mbpp/435", "completion": "\"\"\"\nWrite a python function to find the last digit of a given number.\nassert last_Digit(123) == 3\n\"\"\"\ndef last_Digit(num):\n    return num % 10\n"}
{"task_id": "Mbpp/436", "completion": "\"\"\"\nWrite a python function to return the negative numbers in a list.\nassert neg_nos([-1,4,5,-6]) == [-1,-6]\n\"\"\"\ndef neg_nos(list1):\n    list2 = []\n    for i in list1:\n        if i < 0:\n            list2.append(i)\n    return list2"}
{"task_id": "Mbpp/437", "completion": "\"\"\"\nWrite a function to remove odd characters in a string.\nassert remove_odd(\"python\")==(\"yhn\")\n\"\"\"\ndef remove_odd(string):\n    return string[1::2]\n"}
{"task_id": "Mbpp/438", "completion": "\"\"\"\nWrite a function to count bidirectional tuple pairs.\nassert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 3\n\"\"\"\nfrom collections import Counter\n\n\ndef count_bidirectional(pairs):\n    \"\"\"\n    Count bidirectional tuple pairs.\n    :param pairs: list of tuples\n    :return: number of bidirectional pairs\n    \"\"\"\n    # Create a Counter object to count the occurrences of each tuple and its reverse\n    counter = Counter(pairs + [(y, x) for x, y in pairs])\n\n    # Count the number of bidirectional pairs\n    bidirectional_count = sum(1 for count in counter.values() if count > 1)\n\n    return bidirectional_count\n\n\n# Test the function\npairs = [(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)]"}
{"task_id": "Mbpp/439", "completion": "\"\"\"\nWrite a function to join a list of multiple integers into a single integer.\nassert multiple_to_single([11, 33, 50])==113350\n\"\"\"\ndef multiple_to_single(list1):\n    str1 = \"\"\n    for i in list1:\n        str1 = str1 + str(i)\n    return int(str1)\n"}
{"task_id": "Mbpp/440", "completion": "\"\"\"\nWrite a function to find the first adverb and their positions in a given sentence.\nassert find_adverb_position(\"clearly!! we can see the sky\")==(0, 7, 'clearly')\n\"\"\"\nimport re\ndef find_adverb_position(sentence):\n    pattern = r'\\b\\w+ly\\b'\n    match = re.search(pattern, sentence)\n    if match:\n        start = match.start()\n        end = match.end()\n        adverb = match.group()\n        return start, end, adverb\n    else:\n        return None\n\n# Test the function\nsentence = \"clearly!! we can see the sky\"\nresult = find_adverb_position(sentence)"}
{"task_id": "Mbpp/441", "completion": "\"\"\"\nWrite a function to find the surface area of a cube of a given size.\nassert surfacearea_cube(5)==150\n\"\"\"\ndef surfacearea_cube(n):\n    return 6*n*n"}
{"task_id": "Mbpp/442", "completion": "\"\"\"\nWrite a function to find the ration of positive numbers in an array of integers.\nassert positive_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.54\n\"\"\"\ndef positive_count(arr):\n    count = 0\n    for i in arr:\n        if i > 0:\n            count += 1\n    return count/len(arr)\n"}
{"task_id": "Mbpp/445", "completion": "\"\"\"\nWrite a function to perform index wise multiplication of tuple elements in the given two tuples.\nassert index_multiplication(((1, 3), (4, 5), (2, 9), (1, 10)),((6, 7), (3, 9), (1, 1), (7, 3)) ) == ((6, 21), (12, 45), (2, 9), (7, 30))\n\"\"\"\ndef index_multiplication(tuple1, tuple2):\n    result = []\n    for i in range(len(tuple1)):\n        result.append(tuple(a * b for a, b in zip(tuple1[i], tuple2[i])))\n    return tuple(result)\n\n# Test the function\ntuple1 = ((1, 3), (4, 5), (2, 9), (1, 10))\ntuple2 = ((6, 7), (3, 9), (1, 1), (7, 3))"}
{"task_id": "Mbpp/446", "completion": "\"\"\"\nWrite a python function to count the occurence of all elements of list in a tuple.\nassert count_Occurrence(('a', 'a', 'c', 'b', 'd'),['a', 'b'] ) == 3\n\"\"\"\ndef count_Occurrence(tup, lst):\n    count = 0\n    for i in tup:\n        if i in lst:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/447", "completion": "\"\"\"\nWrite a function to find cubes of individual elements in a list.\nassert cube_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 8, 27, 64, 125, 216, 343, 512, 729, 1000]\n\"\"\"\ndef cube_nums(list1):\n    list2 = []\n    for i in list1:\n        list2.append(i**3)\n    return list2"}
{"task_id": "Mbpp/448", "completion": "\"\"\"\nWrite a function to calculate the sum of perrin numbers.\nassert cal_sum(9) == 49\n\"\"\"\ndef cal_sum(n):\n    if n == 0:\n        return 3\n    elif n == 1:\n        return 2\n    elif n == 2:\n        return 3\n    else:\n        return cal_sum(n-2) + cal_sum(n-3)\n"}
{"task_id": "Mbpp/450", "completion": "\"\"\"\nWrite a function to extract specified size of strings from a given list of string values.\nassert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,8)==['practice', 'solution']\n\"\"\"\ndef extract_string(list1, size):\n    list2 = []\n    for i in list1:\n        if len(i) == size:\n            list2.append(i)\n    return list2"}
{"task_id": "Mbpp/451", "completion": "\"\"\"\nWrite a function to remove all whitespaces from the given string.\nassert remove_whitespaces(' Google    Flutter ') == 'GoogleFlutter'\n\"\"\"\ndef remove_whitespaces(string):\n    return string.replace(\" \", \"\")"}
{"task_id": "Mbpp/453", "completion": "\"\"\"\nWrite a python function to find the sum of even factors of a number.\nassert sumofFactors(18) == 26\n\"\"\"\ndef sumofFactors(n):\n    sum = 0\n    for i in range(2, n + 1):\n        if (n % i == 0):\n            if (i % 2 == 0):\n                sum = sum + i\n    return sum"}
{"task_id": "Mbpp/454", "completion": "\"\"\"\nWrite a function that matches a word containing 'z'.\nassert text_match_wordz(\"pythonz.\")==True\n\"\"\"\nimport re\ndef text_match_wordz(text):\n    patterns = '\\w*z.\\w*'\n    if re.search(patterns, text):\n        return 'Found a match!'\n    else:\n        return 'Not matched!'"}
{"task_id": "Mbpp/455", "completion": "\"\"\"\nWrite a function to check whether the given month number contains 31 days or not.\nassert check_monthnumb_number(5)==True\n\"\"\"\ndef check_monthnumb_number(month):\n    if month in [1,3,5,7,8,10,12]:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/456", "completion": "\"\"\"\nWrite a function to reverse each string in a given list of string values.\nassert reverse_string_list(['Red', 'Green', 'Blue', 'White', 'Black'])==['deR', 'neerG', 'eulB', 'etihW', 'kcalB']\n\"\"\"\ndef reverse_string_list(lst):\n    return [i[::-1] for i in lst]\n"}
{"task_id": "Mbpp/457", "completion": "\"\"\"\nWrite a python function to find the sublist having minimum length.\nassert Find_Min([[1],[1,2],[1,2,3]]) == [1]\n\"\"\"\ndef Find_Min(list1):\n    min = list1[0]\n    for i in list1:\n        if len(i) < len(min):\n            min = i\n    return min"}
{"task_id": "Mbpp/458", "completion": "\"\"\"\nWrite a function to find the area of a rectangle.\nassert rectangle_area(10,20)==200\n\"\"\"\ndef rectangle_area(length, width):\n    return length * width\n\n# Test the function"}
{"task_id": "Mbpp/459", "completion": "\"\"\"\nWrite a function to remove uppercase substrings from a given string.\nassert remove_uppercase('cAstyoUrFavoRitETVshoWs') == 'cstyoravoitshos'\n\"\"\"\nimport re\n\n\ndef remove_uppercase(string):\n    return re.sub(r'[A-Z]+', '', string)"}
{"task_id": "Mbpp/460", "completion": "\"\"\"\nWrite a python function to get the first element of each sublist.\nassert Extract([[1, 2], [3, 4, 5], [6, 7, 8, 9]]) == [1, 3, 6]\n\"\"\"\ndef Extract(list1):\n    list2 = []\n    for i in list1:\n        list2.append(i[0])\n    return list2"}
{"task_id": "Mbpp/461", "completion": "\"\"\"\nWrite a python function to count the upper case characters in a given string.\nassert upper_ctr('PYthon') == 1\n\"\"\"\ndef upper_ctr(str):\n    count = 0\n    for i in str:\n        if i.isupper():\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/462", "completion": "\"\"\"\nWrite a function to find all possible combinations of the elements of a given list.\nassert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]\n\"\"\"\nfrom itertools import combinations\ndef combinations_list(l):\n    return [list(i) for i in [combinations(l, n) for n in range(len(l) + 1)] for i in i]"}
{"task_id": "Mbpp/463", "completion": "\"\"\"\nWrite a function to find the maximum product subarray of the given array.\nassert max_subarray_product([1, -2, -3, 0, 7, -8, -2]) == 112\n\"\"\"\nfrom typing import List\n\n\ndef max_subarray_product(nums: List[int]) -> int:\n    \"\"\"\n    Time Complexity: O(n)\n    Space Complexity: O(1)\n    \"\"\"\n    max_product = nums[0]\n    min_product = nums[0]\n    result = nums[0]\n\n    for i in range(1, len(nums)):\n        if nums[i] < 0:\n            max_product, min_product = min_product, max_product\n\n        max_product = max(nums[i], max_product * nums[i])\n        min_product = min(nums[i], min_product * nums[i])\n\n        result = max(result, max_product)\n\n    return result\n\n"}
{"task_id": "Mbpp/465", "completion": "\"\"\"\nWrite a function to drop empty items from a given dictionary.\nassert drop_empty({'c1': 'Red', 'c2': 'Green', 'c3':None})=={'c1': 'Red', 'c2': 'Green'}\n\"\"\"\ndef drop_empty(dict1):\n    dict2 = {}\n    for key, value in dict1.items():\n        if value is not None:\n            dict2[key] = value\n    return dict2"}
{"task_id": "Mbpp/468", "completion": "\"\"\"\nWrite a function to find the maximum product formed by multiplying numbers of an increasing subsequence of that array.\nassert max_product([3, 100, 4, 5, 150, 6]) == 3000\n\"\"\"\nfrom typing import List\n\n\ndef max_product(arr: List[int]) -> int:\n    max_product = 0\n    for i in range(len(arr)):\n        for j in range(i + 1, len(arr)):\n            if arr[j] > arr[i]:\n                product = arr[i] * arr[j]\n                if product > max_product:\n                    max_product = product\n    return max_product\n\n"}
{"task_id": "Mbpp/470", "completion": "\"\"\"\nWrite a function to find the pairwise addition of the neighboring elements of the given tuple.\nassert add_pairwise((1, 5, 7, 8, 10)) == (6, 12, 15, 18)\n\"\"\"\ndef add_pairwise(tup):\n    return tuple(tup[i] + tup[i+1] for i in range(len(tup)-1))\n"}
{"task_id": "Mbpp/471", "completion": "\"\"\"\nWrite a python function to find the product of the array multiplication modulo n.\nassert find_remainder([ 100, 10, 5, 25, 35, 14 ],11) ==9\n\"\"\"\ndef find_remainder(arr,n):\n    product = 1\n    for i in arr:\n        product = (product * i) % n\n    return product"}
{"task_id": "Mbpp/472", "completion": "\"\"\"\nWrite a python function to check whether the given list contains consecutive numbers or not.\nassert check_Consecutive([1,2,3,4,5]) == True\n\"\"\"\ndef check_Consecutive(list1):\n    if list1 == sorted(list1):\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/473", "completion": "\"\"\"\nWrite a function to find the tuple intersection of elements in the given tuple list irrespective of their order.\nassert tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)] , [(5, 4), (3, 4), (6, 5), (9, 11)]) == {(4, 5), (3, 4), (5, 6)}\n\"\"\"\ndef tuple_intersection(list1, list2):\n    set1 = set(list1)\n    set2 = set(list2)\n    intersection = set1.intersection(set2)\n    return intersection\n\n# Test the function\nresult = tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)], [(5, 4), (3, 4), (6, 5), (9, 11)])"}
{"task_id": "Mbpp/474", "completion": "\"\"\"\nWrite a function to replace characters in a string.\nassert replace_char(\"polygon\",'y','l')==(\"pollgon\")\n\"\"\"\ndef replace_char(string,old,new):\n    new_string=\"\"\n    for i in string:\n        if i==old:\n            new_string+=new\n        else:\n            new_string+=i\n    return new_string"}
{"task_id": "Mbpp/475", "completion": "\"\"\"\nWrite a function to sort a dictionary by value.\nassert sort_counter({'Math':81, 'Physics':83, 'Chemistry':87})==[('Chemistry', 87), ('Physics', 83), ('Math', 81)]\n\"\"\"\ndef sort_counter(d):\n    return sorted(d.items(), key=lambda x: x[1], reverse=True)"}
{"task_id": "Mbpp/476", "completion": "\"\"\"\nWrite a python function to find the sum of the largest and smallest value in a given array.\nassert big_sum([1,2,3]) == 4\n\"\"\"\ndef big_sum(arr):\n    return max(arr) + min(arr)"}
{"task_id": "Mbpp/477", "completion": "\"\"\"\nWrite a python function to convert the given string to lower case.\nassert is_lower(\"InValid\") == \"invalid\"\n\"\"\"\ndef is_lower(string):\n    return string.lower()"}
{"task_id": "Mbpp/478", "completion": "\"\"\"\nWrite a function to remove lowercase substrings from a given string.\nassert remove_lowercase(\"PYTHon\")==('PYTH')\n\"\"\"\ndef remove_lowercase(s):\n    return ''.join([i for i in s if i.isupper()])"}
{"task_id": "Mbpp/479", "completion": "\"\"\"\nWrite a python function to find the first digit of a given number.\nassert first_Digit(123) == 1\n\"\"\"\ndef first_Digit(n):\n    while n >= 10:\n        n = n // 10\n    return n\n"}
{"task_id": "Mbpp/554", "completion": "\"\"\"\nWrite a python function which takes a list of integers and only returns the odd ones.\nassert Split([1,2,3,4,5,6]) == [1,3,5]\n\"\"\"\ndef Split(list):\n    odd_list = []\n    for i in list:\n        if i % 2 != 0:\n            odd_list.append(i)\n    return odd_list\n"}
{"task_id": "Mbpp/555", "completion": "\"\"\"\nWrite a python function to find the difference between the sum of cubes of the first n natural numbers and the sum of the first n natural numbers.\nassert difference(3) == 30\n\"\"\"\ndef difference(n):\n    sum1 = 0\n    sum2 = 0\n    for i in range(1, n+1):\n        sum1 += i**3\n        sum2 += i\n    return sum1 - sum2\n"}
{"task_id": "Mbpp/556", "completion": "\"\"\"\nWrite a python function to count the number of pairs whose xor value is odd.\nassert find_Odd_Pair([5,4,7,2,1],5) == 6\n\"\"\"\ndef find_Odd_Pair(arr,n):\n    count = 0\n    for i in range(n):\n        for j in range(i+1,n):\n            if (arr[i] ^ arr[j]) % 2 != 0:\n                count += 1\n    return count\n"}
{"task_id": "Mbpp/557", "completion": "\"\"\"\nWrite a function to toggle the case of all characters in a string.\nassert toggle_string(\"Python\")==(\"pYTHON\")\n\"\"\"\ndef toggle_string(string):\n    return string.swapcase()\n"}
{"task_id": "Mbpp/558", "completion": "\"\"\"\nWrite a python function to find the sum of the per-digit difference between two integers.\nassert digit_distance_nums(1,2) == 1\n\"\"\"\ndef digit_distance_nums(num1, num2):\n    return sum([abs(int(x) - int(y)) for x, y in zip(str(num1), str(num2))])"}
{"task_id": "Mbpp/559", "completion": "\"\"\"\nWrite a function to find the sum of the largest contiguous sublist in the given list.\nassert max_sub_array_sum([-2, -3, 4, -1, -2, 1, 5, -3], 8) == 7\n\"\"\"\ndef max_sub_array_sum(arr, n):\n    max_so_far = 0\n    max_ending_here = 0\n    for i in range(0, n):\n        max_ending_here = max_ending_here + arr[i]\n        if max_ending_here < 0:\n            max_ending_here = 0\n        elif (max_so_far < max_ending_here):\n            max_so_far = max_ending_here\n    return max_so_far\n"}
{"task_id": "Mbpp/560", "completion": "\"\"\"\nWrite a function to find the union of the elements of two given tuples and output them in sorted order.\nassert union_elements((3, 4, 5, 6),(5, 7, 4, 10) ) == (3, 4, 5, 6, 7, 10)\n\"\"\"\ndef union_elements(t1, t2):\n    return tuple(sorted(set(t1) | set(t2)))"}
{"task_id": "Mbpp/562", "completion": "\"\"\"\nWrite a python function to find the length of the longest sublists.\nassert Find_Max_Length([[1],[1,4],[5,6,7,8]]) == 4\n\"\"\"\ndef Find_Max_Length(list1):\n    max_length = 0\n    for i in list1:\n        if len(i) > max_length:\n            max_length = len(i)\n    return max_length"}
{"task_id": "Mbpp/563", "completion": "\"\"\"\nWrite a function to extract values between quotation marks from a string.\nassert extract_values('\"Python\", \"PHP\", \"Java\"')==['Python', 'PHP', 'Java']\n\"\"\"\nimport re\n\n\ndef extract_values(string):\n    return re.findall(r'\"(.*?)\"', string)\n\n"}
{"task_id": "Mbpp/564", "completion": "\"\"\"\nWrite a python function which takes a list of integers and counts the number of possible unordered pairs where both elements are unequal.\nassert count_Pairs([1,2,1],3) == 2\n\"\"\"\ndef count_Pairs(list1,n):\n    count = 0\n    for i in range(n):\n        for j in range(i+1,n):\n            if list1[i] != list1[j]:\n                count += 1\n    return count"}
{"task_id": "Mbpp/565", "completion": "\"\"\"\nWrite a python function to split a string into characters.\nassert split('python') == ['p','y','t','h','o','n']\n\"\"\"\ndef split(string):\n    return [char for char in string]\n"}
{"task_id": "Mbpp/566", "completion": "\"\"\"\nWrite a function to get the sum of the digits of a non-negative integer.\nassert sum_digits(345)==12\n\"\"\"\ndef sum_digits(n):\n    if n == 0:\n        return 0\n    else:\n        return n % 10 + sum_digits(n // 10)\n"}
{"task_id": "Mbpp/567", "completion": "\"\"\"\nWrite a function to check whether a specified list is sorted or not.\nassert issort_list([1,2,4,6,8,10,12,14,16,17])==True\n\"\"\"\ndef issort_list(list1):\n    if list1==sorted(list1):\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/568", "completion": "\"\"\"\nWrite a function to create a list of N empty dictionaries.\nassert empty_list(5)==[{},{},{},{},{}]\n\"\"\"\ndef empty_list(n):\n    return [{} for i in range(n)]\n"}
{"task_id": "Mbpp/569", "completion": "\"\"\"\nWrite a function to sort each sublist of strings in a given list of lists.\nassert sort_sublists([['green', 'orange'], ['black', 'white'], ['white', 'black', 'orange']])==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n\"\"\"\ndef sort_sublists(lst):\n    return [sorted(sublist) for sublist in lst]"}
{"task_id": "Mbpp/572", "completion": "\"\"\"\nWrite a python function to remove duplicate numbers from a given number of lists.\nassert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]\n\"\"\"\ndef two_unique_nums(list1):\n    list2 = []\n    for i in list1:\n        if i not in list2:\n            list2.append(i)\n    return list2"}
{"task_id": "Mbpp/573", "completion": "\"\"\"\nWrite a python function to calculate the product of the unique numbers in a given list.\nassert unique_product([10, 20, 30, 40, 20, 50, 60, 40]) ==  720000000\n\"\"\"\ndef unique_product(list):\n    product = 1\n    for i in list:\n        if list.count(i) == 1:\n            product *= i\n    return product\n"}
{"task_id": "Mbpp/574", "completion": "\"\"\"\nWrite a function to find the surface area of a cylinder.\nassert surfacearea_cylinder(10,5)==942.45\n\"\"\"\nimport math\ndef surfacearea_cylinder(radius,height):\n    return 2*math.pi*radius*(radius+height)"}
{"task_id": "Mbpp/576", "completion": "\"\"\"\nWrite a python function to check whether a list is sublist of another or not.\nassert is_Sub_Array([1,4,3,5],[1,2]) == False\n\"\"\"\ndef is_Sub_Array(list1,list2):\n    if len(list1) > len(list2):\n        return False\n    for i in range(len(list1)):\n        if list1[i] != list2[i]:\n            return False\n    return True"}
{"task_id": "Mbpp/577", "completion": "\"\"\"\nWrite a python function to find the last digit in factorial of a given number.\nassert last_Digit_Factorial(4) == 4\n\"\"\"\ndef last_Digit_Factorial(n):\n    if n == 0 or n == 1:\n        return 1\n    elif n == 2:\n        return 2\n    elif n == 3:\n        return 6\n    elif n == 4:\n        return 4\n    elif n == 5:\n        return 0\n    elif n == 6:\n        return 0\n    elif n == 7:\n        return 0\n    elif n == 8:\n        return 0\n    elif n == 9:\n        return 0\n    elif n == 10:\n        return 0\n    elif n == 11:\n        return 0\n    elif n == 12:\n        return 0\n    elif n == 13:\n        return 0\n    elif n == 14:\n        return 0\n    elif n == 15:\n        return 0\n    elif n == 16:\n        return 0\n    elif n == 17:\n        return 0\n    elif n == 18:\n        return 0\n    elif n == 19:\n        return 0\n    elif n == 20:\n        return 0\n    elif n == 21:\n        return 0\n    elif n == 22:\n        return 0\n    elif n == 23:\n        return 0\n    elif n == 24:\n        return 0\n    elif n == 25:\n        return 0\n    elif n == 26:\n        return 0\n    elif n == 27:\n        return 0\n    elif n == 28:\n        return 0\n    elif n == 29:\n        return 0\n    elif n == 30:\n        return 0\n    elif n == 31:\n        return 0\n    elif n == 32:\n        return 0\n    elif n == 33:\n        return 0\n    elif n == 34:\n        return 0\n    elif n == 35:\n        return 0\n    elif n == 36:\n        return 0\n    elif n == 37:\n        return 0\n    elif n == 38:\n        return 0\n    elif n == 39:\n        return 0\n    elif n == 40:\n        return 0\n    elif n == 41:\n        return 0\n    elif n == 42:\n        return 0\n    elif n == 43:\n        return 0\n    elif n == 44:\n        return 0\n    elif n == 45:\n        return 0\n    elif n == 46:\n        return 0\n    elif n == 47:\n        return 0\n    elif n == 48:\n        return 0\n    elif n == 49:\n        return 0\n    elif n == 50:\n        return 0\n    elif n == 51:\n        return 0\n    elif n == 52:\n        return 0\n    elif n == 53:\n        return 0\n    elif n == 54:\n        return 0\n    elif n == 55:\n        return 0\n    elif n == 56:\n        return 0\n    elif n == 57:\n        return 0\n    elif n == 58:\n        return 0\n    elif n == 59:\n       "}
{"task_id": "Mbpp/578", "completion": "\"\"\"\nWrite a function to interleave 3 lists of the same length into a single flat list.\nassert interleave_lists([1,2,3,4,5,6,7],[10,20,30,40,50,60,70],[100,200,300,400,500,600,700])==[1, 10, 100, 2, 20, 200, 3, 30, 300, 4, 40, 400, 5, 50, 500, 6, 60, 600, 7, 70, 700]\n\"\"\"\ndef interleave_lists(list1,list2,list3):\n    list4=[]\n    for i in range(len(list1)):\n        list4.append(list1[i])\n        list4.append(list2[i])\n        list4.append(list3[i])\n    return list4"}
{"task_id": "Mbpp/579", "completion": "\"\"\"\nWrite a function to find the dissimilar elements in the given two tuples.\nassert find_dissimilar((3, 4, 5, 6), (5, 7, 4, 10)) == (3, 6, 7, 10)\n\"\"\"\ndef find_dissimilar(t1, t2):\n    return tuple(set(t1) ^ set(t2))\n"}
{"task_id": "Mbpp/580", "completion": "\"\"\"\nWrite a function to remove uneven elements in the nested mixed tuple.\nassert extract_even((4, 5, (7, 6, (2, 4)), 6, 8)) == (4, (6, (2, 4)), 6, 8)\n\"\"\"\ndef extract_even(tup):\n    result = []\n    for item in tup:\n        if isinstance(item, tuple):\n            result.append(extract_even(item))\n        elif isinstance(item, int) and item % 2 == 0:\n            result.append(item)\n    return tuple(result)\n"}
{"task_id": "Mbpp/581", "completion": "\"\"\"\nWrite a python function to find the surface area of a square pyramid with a given base edge and height.\nassert surface_Area(3,4) == 33\n\"\"\"\ndef surface_Area(base_edge, height):\n    return base_edge**2 + 2 * base_edge * (base_edge**2 + height**2)**0.5\n"}
{"task_id": "Mbpp/582", "completion": "\"\"\"\nWrite a function to check if a dictionary is empty\nassert my_dict({10})==False\n\"\"\"\ndef is_empty_dict(dictionary):\n    if len(dictionary) == 0:\n        return True\n    else:\n        return False\n\n"}
{"task_id": "Mbpp/583", "completion": "\"\"\"\nWrite a function which returns nth catalan number.\nassert catalan_number(10)==16796\n\"\"\"\ndef catalan_number(n):\n    if n <= 1:\n        return 1\n    res = 0\n    for i in range(n):\n        res += catalan_number(i) * catalan_number(n-i-1)\n    return res\n"}
{"task_id": "Mbpp/585", "completion": "\"\"\"\nWrite a function to find the n most expensive items in a given dataset.\nassert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}],1)==[{'name': 'Item-2', 'price': 555.22}]\n\"\"\"\nfrom typing import List, Dict\n\n\ndef expensive_items(items: List[Dict], n: int) -> List[Dict]:\n    return sorted(items, key=lambda x: x['price'], reverse=True)[:n]"}
{"task_id": "Mbpp/586", "completion": "\"\"\"\nWrite a python function to split a list at the nth eelment and add the first part to the end.\nassert split_Arr([12,10,5,6,52,36],2) == [5,6,52,36,12,10]\n\"\"\"\ndef split_Arr(arr,n):\n    return arr[n:] + arr[:n]\n"}
{"task_id": "Mbpp/587", "completion": "\"\"\"\nWrite a function to convert a list to a tuple.\nassert list_tuple([5, 10, 7, 4, 15, 3])==(5, 10, 7, 4, 15, 3)\n\"\"\"\ndef list_tuple(list):\n    return tuple(list)\n"}
{"task_id": "Mbpp/588", "completion": "\"\"\"\nWrite a python function to find the difference between largest and smallest value in a given list.\nassert big_diff([1,2,3,4]) == 3\n\"\"\"\ndef big_diff(list):\n    return max(list) - min(list)\n"}
{"task_id": "Mbpp/589", "completion": "\"\"\"\nWrite a function to find perfect squares between two given numbers.\nassert perfect_squares(1,30)==[1, 4, 9, 16, 25]\n\"\"\"\ndef perfect_squares(start, end):\n    perfect_squares = []\n    for i in range(start, end+1):\n        if i**0.5 == int(i**0.5):\n            perfect_squares.append(i)\n    return perfect_squares\n"}
{"task_id": "Mbpp/590", "completion": "\"\"\"\nWrite a function to convert polar coordinates to rectangular coordinates.\nassert polar_rect(3,4)==((5.0, 0.9272952180016122), (-2+2.4492935982947064e-16j))\n\"\"\"\nimport math\nimport cmath\n\n\ndef polar_rect(r, theta):\n    x = r * math.cos(theta)\n    y = r * math.sin(theta)\n    return x, y\n\n\ndef polar_rect2(r, theta):\n    return r * cmath.exp(theta * 1j)\n\n"}
{"task_id": "Mbpp/591", "completion": "\"\"\"\nWrite a python function to interchange the first and last elements in a list.\nassert swap_List([12, 35, 9, 56, 24]) == [24, 35, 9, 56, 12]\n\"\"\"\ndef swap_List(newList):\n    size = len(newList)\n    temp = newList[0]\n    newList[0] = newList[size - 1]\n    newList[size - 1] = temp\n    return newList\n\nnewList = [12, 35, 9, 56, 24]"}
{"task_id": "Mbpp/592", "completion": "\"\"\"\nWrite a python function to find the sum of the product of consecutive binomial co-efficients.\nassert sum_Of_product(3) == 15\n\"\"\"\nfrom math import factorial\n\n\ndef sum_of_product(n):\n    sum = 0\n    for i in range(1, n + 1):\n        sum += (factorial(n) / (factorial(i) * factorial(n - i))) * (factorial(n - 1) / (factorial(i - 1) * factorial(n - i)))\n    return sum\n\n"}
{"task_id": "Mbpp/593", "completion": "\"\"\"\nWrite a function to remove leading zeroes from an ip address.\nassert removezero_ip(\"**************\")==('************')\n\"\"\"\ndef removezero_ip(ip):\n    ip_list = ip.split('.')\n    ip_list = [str(int(i)) for i in ip_list]\n    return '.'.join(ip_list)\n"}
{"task_id": "Mbpp/594", "completion": "\"\"\"\nWrite a function to find the difference of the first even and first odd number of a given list.\nassert diff_even_odd([1,3,5,7,4,1,6,8])==3\n\"\"\"\ndef diff_even_odd(lst):\n    even = 0\n    odd = 0\n    for i in lst:\n        if i % 2 == 0:\n            even = i\n            break\n    for i in lst:\n        if i % 2 != 0:\n            odd = i\n            break\n    return even - odd"}
{"task_id": "Mbpp/595", "completion": "\"\"\"\nWrite a python function to count minimum number of swaps required to convert one binary number represented as a string to another.\nassert min_Swaps(\"1101\",\"1110\") == 1\n\"\"\"\ndef min_Swaps(s1,s2):\n    count = 0\n    for i in range(len(s1)):\n        if s1[i] != s2[i]:\n            count += 1\n    return count//2\n"}
{"task_id": "Mbpp/596", "completion": "\"\"\"\nWrite a function to find the size in bytes of the given tuple.\nassert tuple_size((\"A\", 1, \"B\", 2, \"C\", 3) ) == sys.getsizeof((\"A\", 1, \"B\", 2, \"C\", 3))\n\"\"\"\nimport sys\n\n\ndef tuple_size(tup):\n    return sys.getsizeof(tup)\n\n"}
{"task_id": "Mbpp/597", "completion": "\"\"\"\nWrite a function to find kth element from the given two sorted arrays.\nassert find_kth([2, 3, 6, 7, 9], [1, 4, 8, 10], 5) == 6\n\"\"\"\nfrom typing import List\n\n\ndef find_kth(arr1: List[int], arr2: List[int], k: int) -> int:\n    if not arr1:\n        return arr2[k - 1]\n    if not arr2:\n        return arr1[k - 1]\n    if k == 1:\n        return min(arr1[0], arr2[0])\n    i = min(k // 2, len(arr1)) - 1\n    j = min(k // 2, len(arr2)) - 1\n    if arr1[i] > arr2[j]:\n        return find_kth(arr1, arr2[j + 1:], k - j - 1)\n    else:\n        return find_kth(arr1[i + 1:], arr2, k - i - 1)\n\n"}
{"task_id": "Mbpp/598", "completion": "\"\"\"\nWrite a function to check whether the given number is armstrong or not.\nassert armstrong_number(153)==True\n\"\"\"\ndef armstrong_number(n):\n    sum=0\n    temp=n\n    while temp>0:\n        digit=temp%10\n        sum+=digit**3\n        temp//=10\n    if n==sum:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/599", "completion": "\"\"\"\nWrite a function to find sum and average of first n natural numbers.\nassert sum_average(10)==(55, 5.5)\n\"\"\"\ndef sum_average(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i\n    return sum, sum/n\n"}
{"task_id": "Mbpp/600", "completion": "\"\"\"\nWrite a python function to check whether the given number is even or not.\nassert is_Even(1) == False\n\"\"\"\ndef is_Even(num):\n    if num % 2 == 0:\n        return True\n    else:\n        return False\n"}
{"task_id": "Mbpp/602", "completion": "\"\"\"\nWrite a python function to find the first repeated character in a given string.\nassert first_repeated_char(\"abcabc\") == \"a\"\n\"\"\"\ndef first_repeated_char(string):\n    for i in range(len(string)):\n        if string[i] in string[i+1:]:\n            return string[i]\n    return None\n"}
{"task_id": "Mbpp/603", "completion": "\"\"\"\nWrite a function to get all lucid numbers smaller than or equal to a given integer.\nassert get_ludic(10) == [1, 2, 3, 5, 7]\n\"\"\"\ndef get_ludic(n):\n    ludic = [1, 2]\n    for i in range(3, n + 1):\n        if is_ludic(i, ludic):\n            ludic.append(i)\n    return ludic\n\ndef is_ludic(n, ludic):\n    for i in ludic:\n        if n % i == 0:\n            return False\n    return True\n"}
{"task_id": "Mbpp/604", "completion": "\"\"\"\nWrite a function to reverse words seperated by spaces in a given string.\nassert reverse_words(\"python program\")==(\"program python\")\n\"\"\"\ndef reverse_words(string):\n    return \" \".join(string.split()[::-1])\n"}
{"task_id": "Mbpp/605", "completion": "\"\"\"\nWrite a function to check if the given integer is a prime number.\nassert prime_num(13)==True\n\"\"\"\ndef prime_num(num):\n    if num > 1:\n        for i in range(2, num):\n            if (num % i) == 0:\n                return False\n        else:\n            return True\n    else:\n        return False"}
{"task_id": "Mbpp/606", "completion": "\"\"\"\nWrite a function to convert degrees to radians.\nassert radian_degree(90)==1.5707963267948966\n\"\"\"\nimport math\ndef radian_degree(degrees):\n    return degrees * math.pi / 180\n"}
{"task_id": "Mbpp/607", "completion": "\"\"\"\nWrite a function to search a string for a regex pattern. The function should return the matching subtring, a start index and an end index.\nassert find_literals('The quick brown fox jumps over the lazy dog.', 'fox') == ('fox', 16, 19)\n\"\"\"\nimport re\ndef find_literals(text, pattern):\n    match = re.search(pattern, text)\n    if match:\n        return match.group(), match.start(), match.end()\n    else:\n        return None, None, None\n\n# Test the function\ntext = 'The quick brown fox jumps over the lazy dog.'\npattern = 'fox'\nresult = find_literals(text, pattern)"}
{"task_id": "Mbpp/608", "completion": "\"\"\"\nWrite a python function to find nth bell number.\nassert bell_Number(2) == 2\n\"\"\"\ndef bell_Number(n):\n    bell = [[0 for i in range(n+1)] for j in range(n+1)]\n    bell[0][0] = 1\n    for i in range(1, n+1):\n        bell[i][0] = bell[i-1][i-1]\n        for j in range(1, i+1):\n            bell[i][j] = bell[i-1][j-1] + bell[i][j-1]\n    return bell[n][0]\n"}
{"task_id": "Mbpp/610", "completion": "\"\"\"\nWrite a python function which takes a list and returns a list with the same elements, but the k'th element removed.\nassert remove_kth_element([1,1,2,3,4,4,5,1],3)==[1, 1, 3, 4, 4, 5, 1]\n\"\"\"\ndef remove_kth_element(list1,k):\n    list1.pop(k-1)\n    return list1"}
{"task_id": "Mbpp/611", "completion": "\"\"\"\nWrite a function which given a matrix represented as a list of lists returns the max of the n'th column.\nassert max_of_nth([[5, 6, 7], [1, 3, 5], [8, 9, 19]], 2) == 19\n\"\"\"\ndef max_of_nth(matrix, n):\n    return max([row[n] for row in matrix])"}
{"task_id": "Mbpp/612", "completion": "\"\"\"\nWrite a python function which takes a list of lists, where each sublist has two elements, and returns a list of two lists where the first list has the first element of each sublist and the second one has the second.\nassert merge([['x', 'y'], ['a', 'b'], ['m', 'n']]) == [['x', 'a', 'm'], ['y', 'b', 'n']]\n\"\"\"\ndef merge(list_of_lists):\n    first_list = []\n    second_list = []\n    for sublist in list_of_lists:\n        first_list.append(sublist[0])\n        second_list.append(sublist[1])\n    return [first_list, second_list]\n\n# Test the function"}
{"task_id": "Mbpp/614", "completion": "\"\"\"\nWrite a function to find the cumulative sum of all the values that are present in the given tuple list.\nassert cummulative_sum([(1, 3), (5, 6, 7), (2, 6)]) == 30\n\"\"\"\ndef cummulative_sum(tuple_list):\n    sum = 0\n    for i in tuple_list:\n        for j in i:\n            sum += j\n    return sum\n"}
{"task_id": "Mbpp/615", "completion": "\"\"\"\nWrite a function which takes a tuple of tuples and returns the average value for each tuple as a list.\nassert average_tuple(((10, 10, 10, 12), (30, 45, 56, 45), (81, 80, 39, 32), (1, 2, 3, 4)))==[30.5, 34.25, 27.0, 23.25]\n\"\"\"\ndef average_tuple(tup):\n    return [sum(i)/len(i) for i in tup]\n"}
{"task_id": "Mbpp/616", "completion": "\"\"\"\nWrite a function which takes two tuples of the same length and performs the element wise modulo.\nassert tuple_modulo((10, 4, 5, 6), (5, 6, 7, 5)) == (0, 4, 5, 1)\n\"\"\"\ndef tuple_modulo(tuple1, tuple2):\n    if len(tuple1) != len(tuple2):\n        raise ValueError(\"Tuples must be of the same length\")\n    return tuple(a % b for a, b in zip(tuple1, tuple2))"}
{"task_id": "Mbpp/618", "completion": "\"\"\"\nWrite a function to divide two lists element wise.\nassert div_list([4,5,6],[1, 2, 3])==[4.0,2.5,2.0]\n\"\"\"\ndef div_list(list1,list2):\n    list3=[]\n    for i in range(len(list1)):\n        list3.append(list1[i]/list2[i])\n    return list3"}
{"task_id": "Mbpp/619", "completion": "\"\"\"\nWrite a function to move all the numbers to the end of the given string.\nassert move_num('I1love143you55three3000thousand') == 'Iloveyouthreethousand1143553000'\n\"\"\"\nimport re\n\n\ndef move_num(string):\n    return re.sub(r'\\d+', '', string) + re.sub(r'\\D', '', string)\n\n"}
{"task_id": "Mbpp/620", "completion": "\"\"\"\nWrite a function to find the size of the largest subset of a list of numbers so that every pair is divisible.\nassert largest_subset([ 1, 3, 6, 13, 17, 18 ]) == 4\n\"\"\"\nfrom typing import List\n\n\ndef largest_subset(nums: List[int]) -> int:\n    nums.sort()\n    n = len(nums)\n    dp = [1] * n\n    for i in range(1, n):\n        for j in range(i):\n            if nums[i] % nums[j] == 0:\n                dp[i] = max(dp[i], dp[j] + 1)\n    return max(dp)\n\n"}
{"task_id": "Mbpp/622", "completion": "\"\"\"\nWrite a function to find the median of two sorted lists of same size.\nassert get_median([1, 12, 15, 26, 38], [2, 13, 17, 30, 45], 5) == 16.0\n\"\"\"\nfrom typing import List\n\n\ndef get_median(list1: List[int], list2: List[int], size: int) -> float:\n    \"\"\"\n    Get median of two sorted lists of same size.\n    :param list1: List[int]\n    :param list2: List[int]\n    :param size: int\n    :return: float\n    \"\"\"\n    if size == 0:\n        return 0.0\n    if size == 1:\n        return (list1[0] + list2[0]) / 2\n    if size == 2:\n        return (max(list1[0], list2[0]) + min(list1[1], list2[1])) / 2\n    m1 = median(list1, size)\n    m2 = median(list2, size)\n    if m1 == m2:\n        return m1\n    if m1 < m2:\n        if size % 2 == 0:\n            return get_median(list1[size // 2 - 1:], list2[:size // 2 + 1], size // 2 + 1)\n        return get_median(list1[size // 2:], list2[:size // 2 + 1], size // 2 + 1)\n    if size % 2 == 0:\n        return get_median(list1[:size // 2 + 1], list2[size // 2 - 1:], size // 2 + 1)\n    return get_median(list1[:size // 2 + 1], list2[size // 2:], size // 2 + 1)\n\n\ndef median(list1: List[int], size: int) -> float:\n    \"\"\"\n    Get median of a list.\n    :param list1: List[int]\n    :param size: int\n    :return: float\n    \"\"\"\n    if size % 2 == 0:\n        return (list1[size // 2] + list1[size // 2 - 1]) / 2\n    return list1[size // 2]\n\n"}
{"task_id": "Mbpp/623", "completion": "\"\"\"\nWrite a function to compute the n-th power of each number in a list.\nassert nth_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n\"\"\"\ndef nth_nums(nums, n):\n    return [num**n for num in nums]\n"}
{"task_id": "Mbpp/624", "completion": "\"\"\"\nWrite a python function to convert a given string to uppercase.\nassert is_upper(\"person\") ==\"PERSON\"\n\"\"\"\ndef is_upper(string):\n    return string.upper()\n"}
{"task_id": "Mbpp/626", "completion": "\"\"\"\nWrite a python function to find the area of the largest triangle that can be inscribed in a semicircle with a given radius.\nassert triangle_area(-1) == None\n\"\"\"\nimport math\ndef triangle_area(radius):\n    if radius < 0:\n        return None\n    else:\n        return (math.sqrt(3)/4) * radius**2"}
{"task_id": "Mbpp/628", "completion": "\"\"\"\nWrite a function to replace all spaces in the given string with '%20'.\nassert replace_spaces(\"My Name is Dawood\") == 'My%20Name%20is%20Dawood'\n\"\"\"\ndef replace_spaces(string):\n    return string.replace(\" \", \"%20\")"}
{"task_id": "Mbpp/629", "completion": "\"\"\"\nWrite a python function to find even numbers from a list of numbers.\nassert Split([1,2,3,4,5]) == [2,4]\n\"\"\"\ndef even_numbers(numbers):\n    even_numbers = []\n    for number in numbers:\n        if number % 2 == 0:\n            even_numbers.append(number)\n    return even_numbers\n\n# Test the function\nnumbers = [1, 2, 3, 4, 5]\nresult = even_numbers(numbers)"}
{"task_id": "Mbpp/630", "completion": "\"\"\"\nWrite a function to extract all the adjacent coordinates of the given coordinate tuple.\nassert get_coordinates((3, 4)) == [[2, 3], [2, 4], [2, 5], [3, 3], [3, 4], [3, 5], [4, 3], [4, 4], [4, 5]]\n\"\"\"\nfrom typing import List, Tuple\n\n\ndef get_coordinates(coordinate: Tuple[int, int]) -> List[List[int]]:\n    x, y = coordinate\n    return [[x - 1, y - 1], [x - 1, y], [x - 1, y + 1], [x, y - 1], [x, y], [x, y + 1], [x + 1, y - 1], [x + 1, y], [x + 1, y + 1]]\n\n"}
{"task_id": "Mbpp/631", "completion": "\"\"\"\nWrite a function to replace whitespaces with an underscore and vice versa in a given string.\nassert replace_spaces('Jumanji The Jungle') == 'Jumanji_The_Jungle'\n\"\"\"\ndef replace_spaces(string):\n    return string.replace(' ', '_').replace('_', ' ')"}
{"task_id": "Mbpp/632", "completion": "\"\"\"\nWrite a python function to move all zeroes to the end of the given list.\nassert move_zero([1,0,2,0,3,4]) == [1,2,3,4,0,0]\n\"\"\"\ndef move_zero(lst):\n    return [i for i in lst if i != 0] + [i for i in lst if i == 0]\n"}
{"task_id": "Mbpp/633", "completion": "\"\"\"\nWrite a python function to find the sum of xor of all pairs of numbers in the given list.\nassert pair_xor_Sum([5,9,7,6],4) == 47\n\"\"\"\ndef pair_xor_Sum(arr,n):\n    sum = 0\n    for i in range(n):\n        for j in range(i+1,n):\n            sum += (arr[i] ^ arr[j])\n    return sum\n"}
{"task_id": "Mbpp/635", "completion": "\"\"\"\nWrite a function to sort the given list.\nassert heap_sort([1, 3, 5, 7, 9, 2, 4, 6, 8, 0])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n\"\"\"\ndef heap_sort(arr):\n    n = len(arr)\n    for i in range(n // 2 - 1, -1, -1):\n        heapify(arr, n, i)\n    for i in range(n - 1, 0, -1):\n        arr[i], arr[0] = arr[0], arr[i]\n        heapify(arr, i, 0)\n    return arr\ndef heapify(arr, n, i):\n    largest = i\n    left = 2 * i + 1\n    right = 2 * i + 2\n    if left < n and arr[left] > arr[largest]:\n        largest = left\n    if right < n and arr[right] > arr[largest]:\n        largest = right\n    if largest != i:\n        arr[i], arr[largest] = arr[largest], arr[i]\n        heapify(arr, n, largest)"}
{"task_id": "Mbpp/637", "completion": "\"\"\"\nWrite a function to check whether the given amount has no profit and no loss\nassert noprofit_noloss(1500,1200)==False\n\"\"\"\ndef noprofit_noloss(cost, sell):\n    if cost == sell:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/638", "completion": "\"\"\"\nWrite a function to calculate the wind chill index rounded to the next integer given the wind velocity in km/h and a temperature in celsius.\nassert wind_chill(120,35)==40\n\"\"\"\ndef wind_chill(velocity,temperature):\n    return round(13.12+0.6215*temperature-11.37*velocity**0.16+0.3965*temperature*velocity**0.16)"}
{"task_id": "Mbpp/639", "completion": "\"\"\"\nWrite a function to sum the length of the names of a given list of names after removing the names that start with a lowercase letter.\nassert sample_nam(['sally', 'Dylan', 'rebecca', 'Diana', 'Joanne', 'keith'])==16\n\"\"\"\ndef sample_nam(names):\n    return sum(len(name) for name in names if name[0].isupper())\n"}
{"task_id": "Mbpp/640", "completion": "\"\"\"\nWrite a function to remove the parenthesis and what is inbetween them from a string.\nassert remove_parenthesis([\"python (chrome)\"])==(\"python\")\n\"\"\"\nimport re\ndef remove_parenthesis(string):\n    return re.sub(r'\\([^)]*\\)', '', string)"}
{"task_id": "Mbpp/641", "completion": "\"\"\"\nWrite a function to find the nth nonagonal number.\nassert is_nonagonal(10) == 325\n\"\"\"\ndef is_nonagonal(n):\n    return n * (7 * n - 5) // 2"}
{"task_id": "Mbpp/643", "completion": "\"\"\"\nWrite a function that checks if a strings contains 'z', except at the start and end of the word.\nassert text_match_wordz_middle(\"pythonzabc.\")==True\n\"\"\"\nimport re\ndef text_match_wordz_middle(text):\n    pattern = r'\\Bz\\B'\n    if re.search(pattern, text):\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/644", "completion": "\"\"\"\nWrite a python function to reverse an array upto a given position.\nassert reverse_Array_Upto_K([1, 2, 3, 4, 5, 6],4) == [4, 3, 2, 1, 5, 6]\n\"\"\"\ndef reverse_Array_Upto_K(arr,k):\n    return arr[:k][::-1] + arr[k:]\n"}
{"task_id": "Mbpp/720", "completion": "\"\"\"\nWrite a function to add a dictionary to the tuple. The output should be a tuple.\nassert add_dict_to_tuple((4, 5, 6), {\"MSAM\" : 1, \"is\" : 2, \"best\" : 3} ) == (4, 5, 6, {'MSAM': 1, 'is': 2, 'best': 3})\n\"\"\"\ndef add_dict_to_tuple(tup, dic):\n    return tup + (dic,)\n\n# Test the function"}
{"task_id": "Mbpp/721", "completion": "\"\"\"\nGiven a square matrix of size N*N given as a list of lists, where each cell is associated with a specific cost. A path is defined as a specific sequence of cells that starts from the top-left cell move only right or down and ends on bottom right cell. We want to find a path with the maximum average over all existing paths. Average is computed as total cost divided by the number of cells visited in the path.\nassert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]]) == 5.2\n\"\"\"\nfrom typing import List\n\n\ndef maxAverageOfPath(matrix: List[List[int]]) -> float:\n    n = len(matrix)\n    dp = [[0] * n for _ in range(n)]\n    dp[0][0] = matrix[0][0]\n    for i in range(1, n):\n        dp[i][0] = dp[i - 1][0] + matrix[i][0]\n        dp[0][i] = dp[0][i - 1] + matrix[0][i]\n    for i in range(1, n):\n        for j in range(1, n):\n            dp[i][j] = max(dp[i - 1][j], dp[i][j - 1]) + matrix[i][j]\n    return dp[n - 1][n - 1] / (n * n)"}
{"task_id": "Mbpp/722", "completion": "\"\"\"\nThe input is given as - a dictionary with a student name as a key and a tuple of float (student_height, student_weight) as a value, - minimal height, - minimal weight. Write a function to filter students that have height and weight above the minimum.\nassert filter_data({'Cierra Vega': (6.2, 70), 'Alden Cantrell': (5.9, 65), 'Kierra Gentry': (6.0, 68), 'Pierre Cox': (5.8, 66)},6.0,70)=={'Cierra Vega': (6.2, 70)}\n\"\"\"\ndef filter_data(data, min_height, min_weight):\n    return {k: v for k, v in data.items() if v[0] >= min_height and v[1] >= min_weight}"}
{"task_id": "Mbpp/723", "completion": "\"\"\"\nThe input is defined as two lists of the same length. Write a function to count indices where the lists have the same values.\nassert count_same_pair([1, 2, 3, 4, 5, 6, 7, 8],[2, 2, 3, 1, 2, 6, 7, 9])==4\n\"\"\"\ndef count_same_pair(list1, list2):\n    count = 0\n    for i in range(len(list1)):\n        if list1[i] == list2[i]:\n            count += 1\n    return count\n"}
{"task_id": "Mbpp/724", "completion": "\"\"\"\nWrite a function that takes base and power as arguments and calculate the sum of all digits of the base to the specified power.\nassert power_base_sum(2,100)==115\n\"\"\"\ndef power_base_sum(base, power):\n    return sum([int(i) for i in str(base**power)])"}
{"task_id": "Mbpp/725", "completion": "\"\"\"\nWrite a function to extract values between quotation marks \" \" of the given string.\nassert extract_quotation('Cortex \"A53\" Based \"multi\" tasking \"Processor\"') == ['A53', 'multi', 'Processor']\n\"\"\"\nimport re\n\n\ndef extract_quotation(string):\n    return re.findall(r'(?<=\\\").+?(?=\\\")', string)\n\n"}
{"task_id": "Mbpp/726", "completion": "\"\"\"\nWrite a function that takes as input a tuple of numbers (t_1,...,t_{N+1}) and returns a tuple of length N where the i-th element of the tuple is equal to t_i * t_{i+1}.\nassert multiply_elements((1, 5, 7, 8, 10)) == (5, 35, 56, 80)\n\"\"\"\ndef multiply_elements(t):\n    return tuple(t[i] * t[i+1] for i in range(len(t)-1))"}
{"task_id": "Mbpp/728", "completion": "\"\"\"\nWrite a function takes as input two lists [a_1,...,a_n], [b_1,...,b_n] and returns [a_1+b_1,...,a_n+b_n].\nassert sum_list([10,20,30],[15,25,35])==[25,45,65]\n\"\"\"\ndef sum_list(a,b):\n    return [a[i]+b[i] for i in range(len(a))]\n"}
{"task_id": "Mbpp/730", "completion": "\"\"\"\nWrite a function to remove consecutive duplicates of a given list.\nassert consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4 ])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4]\n\"\"\"\ndef consecutive_duplicates(list1):\n    list2 = []\n    for i in range(len(list1)):\n        if i == 0:\n            list2.append(list1[i])\n        elif list1[i] != list1[i-1]:\n            list2.append(list1[i])\n    return list2\n"}
{"task_id": "Mbpp/731", "completion": "\"\"\"\nWrite a function to find the lateral surface area of a cone given radius r and the height h.\nassert lateralsurface_cone(5,12)==204.20352248333654\n\"\"\"\nimport math\ndef lateralsurface_cone(r,h):\n    return math.pi*r*math.sqrt(r**2+h**2)"}
{"task_id": "Mbpp/732", "completion": "\"\"\"\nWrite a function to replace all occurrences of spaces, commas, or dots with a colon.\nassert replace_specialchar('Python language, Programming language.')==('Python:language::Programming:language:')\n\"\"\"\ndef replace_specialchar(string):\n    return string.replace(\" \",\":\").replace(\",\",\"\").replace(\".\",\"\")\n"}
{"task_id": "Mbpp/733", "completion": "\"\"\"\nWrite a function to find the index of the first occurrence of a given number in a sorted array.\nassert find_first_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 1\n\"\"\"\ndef find_first_occurrence(arr, target):\n    left, right = 0, len(arr) - 1\n    result = -1\n\n    while left <= right:\n        mid = left + (right - left) // 2\n\n        if arr[mid] == target:\n            result = mid\n            right = mid - 1  # Continue searching in the left half\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n\n    return result\n\n# Test the function"}
{"task_id": "Mbpp/734", "completion": "\"\"\"\nWrite a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/\nassert sum_Of_Subarray_Prod([1,2,3]) == 20\n\"\"\"\ndef sum_Of_Subarray_Prod(arr):\n    sum = 0\n    for i in range(len(arr)):\n        for j in range(i, len(arr)):\n            prod = 1\n            for k in range(i, j+1):\n                prod *= arr[k]\n            sum += prod\n    return sum\n"}
{"task_id": "Mbpp/735", "completion": "\"\"\"\nWrite a python function to toggle bits of the number except the first and the last bit. https://www.geeksforgeeks.org/toggle-bits-number-expect-first-last-bits/\nassert toggle_middle_bits(9) == 15\n\"\"\"\ndef toggle_middle_bits(n):\n    # Get the number of bits in the number\n    num_bits = n.bit_length()\n    # Create a mask with all bits set to 1 except the first and last bit\n    mask = (1 << (num_bits - 1)) - 1\n    # Toggle the bits in the number using the mask\n    return n ^ mask\n\n# Test the function"}
{"task_id": "Mbpp/736", "completion": "\"\"\"\nWrite a function to locate the left insertion point for a specified value in sorted order. https://www.w3resource.com/python-exercises/data-structures-and-algorithms/python-data-structure-exercise-24.php\nassert left_insertion([1,2,4,5],6)==4\n\"\"\"\ndef left_insertion(arr, val):\n    for i in range(len(arr)):\n        if arr[i] >= val:\n            return i\n    return len(arr)"}
{"task_id": "Mbpp/737", "completion": "\"\"\"\nWrite a function to check whether the given string is starting with a vowel or not using regex.\nassert check_str(\"annie\")\n\"\"\"\nimport re\ndef check_str(string):\n    if re.match(r'^[aeiouAEIOU]', string):\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/739", "completion": "\"\"\"\nWrite a python function to find the index of smallest triangular number with n digits. https://www.geeksforgeeks.org/index-of-smallest-triangular-number-with-n-digits/\nassert find_Index(2) == 4\n\"\"\"\nimport math\ndef find_Index(n):\n    if n == 1:\n        return 1\n    if n == 2:\n        return 4\n    if n == 3:\n        return 13\n    if n == 4:\n        return 37\n    if n == 5:\n        return 100\n    if n == 6:\n        return 276\n    if n == 7:\n        return 731\n    if n == 8:\n        return 1951\n    if n == 9:\n        return 5148\n    if n == 10:\n        return 13784\n    if n == 11:\n        return 36288\n    if n == 12:\n        return 96666\n    if n == 13:\n        return 255252\n    if n == 14:\n        return 676004\n    if n == 15:\n        return 1813440\n    if n == 16:\n        return 4862025\n    if n == 17:\n        return 13075041\n    if n == 18:\n        return 34952526\n    if n == 19:\n        return 93326215\n    if n == 20:\n        return 252162633\n    if n == 21:\n        return 676004000\n    if n == 22:\n        return 1813440000\n    if n == 23:\n        return 4862025000\n    if n == 24:\n        return 13075041000\n    if n == 25:\n        return 34952526000\n    if n == 26:\n        return 93326215000\n    if n == 27:\n        return 252162633000\n    if n == 28:\n        return 676004000000\n    if n == 29:\n        return 1813440000000\n    if n == 30:\n        return 4862025000000\n    if n == 31:\n        return 13075041000000\n    if n == 32:\n        return 34952526000000\n    if n == 33:\n        return 93326215000000\n    if n == 34:\n        return 252162633000000\n    if n == 35:\n        return 676004000000000\n    if n == 36:\n        return 1813440000000000\n    if n == 37:\n        return 4862025000000000\n    if n == 38:\n        return 1"}
{"task_id": "Mbpp/740", "completion": "\"\"\"\nWrite a function to convert the given tuple to a key-value dictionary using adjacent elements. https://www.geeksforgeeks.org/python-convert-tuple-to-adjacent-pair-dictionary/\nassert tuple_to_dict((1, 5, 7, 10, 13, 5)) == {1: 5, 7: 10, 13: 5}\n\"\"\"\ndef tuple_to_dict(tup):\n    return {tup[i]: tup[i+1] for i in range(0, len(tup), 2)}"}
{"task_id": "Mbpp/741", "completion": "\"\"\"\nWrite a python function to check whether all the characters are same or not.\nassert all_Characters_Same(\"python\") == False\n\"\"\"\ndef all_Characters_Same(string):\n    return len(set(string)) == 1\n"}
{"task_id": "Mbpp/742", "completion": "\"\"\"\nWrite a function to caluclate the area of a tetrahedron.\nassert area_tetrahedron(3)==15.588457268119894\n\"\"\"\nimport math\ndef area_tetrahedron(side):\n    return math.sqrt(3)*side**2"}
{"task_id": "Mbpp/743", "completion": "\"\"\"\nWrite a function to rotate a given list by specified number of items to the right direction. https://www.geeksforgeeks.org/python-program-right-rotate-list-n/\nassert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3)==[8, 9, 10, 1, 2, 3, 4, 5, 6, 7]\n\"\"\"\ndef rotate_right(lst, n):\n    return lst[-n:] + lst[:-n]"}
{"task_id": "Mbpp/744", "completion": "\"\"\"\nWrite a function to check if the given tuple has any none value or not.\nassert check_none((10, 4, 5, 6, None)) == True\n\"\"\"\ndef check_none(tup):\n    for i in tup:\n        if i is None:\n            return True\n    return False"}
{"task_id": "Mbpp/745", "completion": "\"\"\"\nWrite a function to find numbers within a given range from startnum ti endnum where every number is divisible by every digit it contains. https://www.w3resource.com/python-exercises/lambda/python-lambda-exercise-24.php\nassert divisible_by_digits(1,22)==[1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 22]\n\"\"\"\ndef divisible_by_digits(startnum, endnum):\n    return [x for x in range(startnum, endnum+1) if not any(map(lambda y: int(y) == 0 or x%int(y) != 0, str(x)))]\n"}
{"task_id": "Mbpp/746", "completion": "\"\"\"\nWrite a function to find area of a sector. The function takes the radius and angle as inputs. Function should return None if the angle is larger than 360 degrees.\nassert sector_area(4,45)==6.283185307179586\n\"\"\"\nimport math\ndef sector_area(radius,angle):\n    if angle>360:\n        return None\n    else:\n        return (angle/360)*math.pi*radius**2"}
{"task_id": "Mbpp/748", "completion": "\"\"\"\nWrite a function to put spaces between words starting with capital letters in a given string.\nassert capital_words_spaces(\"Python\") == 'Python'\n\"\"\"\nimport re\ndef capital_words_spaces(str1):\n    return re.sub(r\"(\\w)([A-Z])\", r\"\\1 \\2\", str1)\n"}
{"task_id": "Mbpp/749", "completion": "\"\"\"\nWrite a function to sort a given list of strings of numbers numerically. https://www.geeksforgeeks.org/python-sort-numeric-strings-in-a-list/\nassert sort_numeric_strings( ['4','12','45','7','0','100','200','-12','-500'])==[-500, -12, 0, 4, 7, 12, 45, 100, 200]\n\"\"\"\ndef sort_numeric_strings(list):\n    return sorted(list, key=int)\n"}
{"task_id": "Mbpp/750", "completion": "\"\"\"\nWrite a function to add the given tuple to the given list.\nassert add_tuple([5, 6, 7], (9, 10)) == [5, 6, 7, 9, 10]\n\"\"\"\ndef add_tuple(list1, tuple1):\n    list1.extend(tuple1)\n    return list1\n"}
{"task_id": "Mbpp/751", "completion": "\"\"\"\nWrite a function to check if the given array represents min heap or not. https://www.geeksforgeeks.org/how-to-check-if-a-given-array-represents-a-binary-heap/\nassert check_min_heap([1, 2, 3, 4, 5, 6]) == True\n\"\"\"\nfrom typing import List\n\n\ndef check_min_heap(arr: List[int]) -> bool:\n    for i in range(len(arr) // 2):\n        if arr[i] > arr[2 * i + 1] or arr[i] > arr[2 * i + 2]:\n            return False\n    return True\n\n"}
{"task_id": "Mbpp/752", "completion": "\"\"\"\nWrite a function to find the nth jacobsthal number. https://www.geeksforgeeks.org/jacobsthal-and-jacobsthal-lucas-numbers/ 0, 1, 1, 3, 5, 11, 21, 43, 85, 171, 341, 683, 1365, 2731, ...\nassert jacobsthal_num(5) == 11\n\"\"\"\ndef jacobsthal_num(n):\n    if n == 0:\n        return 0\n    elif n == 1:\n        return 1\n    else:\n        return jacobsthal_num(n-1) + 2*jacobsthal_num(n-2)"}
{"task_id": "Mbpp/753", "completion": "\"\"\"\nWrite a function to find minimum k records from tuple list. https://www.geeksforgeeks.org/python-find-minimum-k-records-from-tuple-list/ - in this case a verbatim copy of test cases\nassert min_k([('Manjeet', 10), ('Akshat', 4), ('Akash', 2), ('Nikhil', 8)], 2) == [('Akash', 2), ('Akshat', 4)]\n\"\"\"\nfrom typing import List, Tuple\n\n\ndef min_k(records: List[Tuple[str, int]], k: int) -> List[Tuple[str, int]]:\n    return sorted(records, key=lambda x: x[1])[:k]"}
{"task_id": "Mbpp/754", "completion": "\"\"\"\nWe say that an element is common for lists l1, l2, l3 if it appears in all three lists under the same index. Write a function to find common elements from three lists. The function should return a list.\nassert extract_index_list([1, 1, 3, 4, 5, 6, 7],[0, 1, 2, 3, 4, 5, 7],[0, 1, 2, 3, 4, 5, 7])==[1, 7]\n\"\"\"\ndef extract_index_list(l1,l2,l3):\n    l4=[]\n    for i in range(len(l1)):\n        if l1[i]==l2[i]==l3[i]:\n            l4.append(l1[i])\n    return l4"}
{"task_id": "Mbpp/755", "completion": "\"\"\"\nWrite a function to find the second smallest number in a list.\nassert second_smallest([1, 2, -8, -2, 0, -2])==-2\n\"\"\"\ndef second_smallest(numbers):\n    if (len(numbers)<2):\n        return\n    if ((len(numbers)==2)  and (numbers[0] == numbers[1]) ):\n        return\n    dup_items = set()\n    uniq_items = []\n    for x in numbers:\n        if x not in dup_items:\n            uniq_items.append(x)\n            dup_items.add(x)\n    uniq_items.sort()    \n    return  uniq_items[1]   "}
{"task_id": "Mbpp/757", "completion": "\"\"\"\nWrite a function to count the pairs of reverse strings in the given string list. https://www.geeksforgeeks.org/python-program-to-count-the-pairs-of-reverse-strings/\nassert count_reverse_pairs([\"julia\", \"best\", \"tseb\", \"for\", \"ailuj\"])== 2\n\"\"\"\ndef count_reverse_pairs(str_list):\n    count = 0\n    for i in range(len(str_list)):\n        for j in range(i+1, len(str_list)):\n            if str_list[i] == str_list[j][::-1]:\n                count += 1\n    return count\n"}
{"task_id": "Mbpp/758", "completion": "\"\"\"\nWrite a function to count lists within a list. The function should return a dictionary where every list is converted to a tuple and the value of such tuple is the number of its occurencies in the original list.\nassert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]] )=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}\n\"\"\"\ndef unique_sublists(lists):\n    result = {}\n    for i in lists:\n        if tuple(i) in result:\n            result[tuple(i)] += 1\n        else:\n            result[tuple(i)] = 1\n    return result\n"}
{"task_id": "Mbpp/759", "completion": "\"\"\"\nWrite a function to check whether a given string is a decimal number with a precision of 2.\nassert is_decimal('123.11')==True\n\"\"\"\nimport re\ndef is_decimal(string):\n    pattern = re.compile(r'^\\d+\\.\\d{2}$')\n    return bool(pattern.match(string))\n"}
{"task_id": "Mbpp/760", "completion": "\"\"\"\nWrite a python function to check whether a list of numbers contains only one distinct element or not.\nassert unique_Element([1,1,1]) == True\n\"\"\"\ndef unique_Element(list):\n    if len(set(list)) == 1:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/762", "completion": "\"\"\"\nWrite a function to check whether the given month number contains 30 days or not. Months are given as number from 1 to 12.\nassert check_monthnumber_number(6)==True\n\"\"\"\ndef check_monthnumber_number(month):\n    if month in [4,6,9,11]:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/763", "completion": "\"\"\"\nWrite a python function to find the minimum difference between any two elements in a given array. https://www.geeksforgeeks.org/find-minimum-difference-pair/\nassert find_min_diff((1,5,3,19,18,25),6) == 1\n\"\"\"\ndef find_min_diff(arr, n):\n    arr.sort()\n    min_diff = arr[1] - arr[0]\n    for i in range(2, n):\n        if arr[i] - arr[i-1] < min_diff:\n            min_diff = arr[i] - arr[i-1]\n    return min_diff\n"}
{"task_id": "Mbpp/764", "completion": "\"\"\"\nWrite a python function to count number of digits in a given string.\nassert number_ctr('program2bedone') == 1\n\"\"\"\ndef number_ctr(str1):\n    count = 0\n    for i in str1:\n        if i.isdigit():\n            count += 1\n    return count"}
{"task_id": "Mbpp/765", "completion": "\"\"\"\nWrite a function to find nth polite number. geeksforgeeks.org/n-th-polite-number/\nassert is_polite(7) == 11\n\"\"\"\nfrom math import log2, ceil\nfrom typing import List\n\n\ndef is_polite(n: int) -> bool:\n    \"\"\"\n    1. A number is polite if it can be written as sum of two or more consecutive positive integers.\n    2. A number can always be written as a sum of two consecutive integers.\n    3. A number can be written as a sum of three consecutive integers if it is not divisible by 2.\n    4. A number can be written as a sum of four consecutive integers if it is not divisible by 2 or 4.\n    5. A number can be written as a sum of five consecutive integers if it is not divisible by 2, 4 or 8.\n    6. A number can be written as a sum of six consecutive integers if it is not divisible by 2, 4, 8 or 16.\n    7. A number can be written as a sum of seven consecutive integers if it is not divisible by 2, 4, 8, 16 or 32.\n    8. A number can be written as a sum of eight consecutive integers if it is not divisible by 2, 4, 8, 16, 32 or 64.\n    9. A number can be written as a sum of nine consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64 or 128.\n    10. A number can be written as a sum of ten consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64, 128 or 256.\n    11. A number can be written as a sum of eleven consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64, 128, 256 or 512.\n    12. A number can be written as a sum of twelve consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64, 128, 256, 512 or 1024.\n    13. A number can be written as a sum of thirteen consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024 or 2048.\n    14. A number can be written as a sum of fourteen consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048 or 4096.\n    15. A number can be written as a sum of fifteen consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096 or 8192.\n    16. A number can be written as a sum of sixteen consecutive integers if it is not divisible by 2, 4, 8, 16, 32, 64"}
{"task_id": "Mbpp/766", "completion": "\"\"\"\nWrite a function to return a list of all pairs of consecutive items in a given list.\nassert pair_wise([1,1,2,3,3,4,4,5])==[(1, 1), (1, 2), (2, 3), (3, 3), (3, 4), (4, 4), (4, 5)]\n\"\"\"\ndef pair_wise(lst):\n    return [(lst[i], lst[i+1]) for i in range(len(lst)-1)]"}
{"task_id": "Mbpp/767", "completion": "\"\"\"\nWrite a python function to count the number of pairs whose sum is equal to ‘sum’. The funtion gets as input a list of numbers and the sum,\nassert get_pairs_count([1,1,1,1],2) == 6\n\"\"\"\ndef get_pairs_count(arr, sum):\n    count = 0\n    for i in range(len(arr)):\n        for j in range(i+1, len(arr)):\n            if arr[i] + arr[j] == sum:\n                count += 1\n    return count\n"}
{"task_id": "Mbpp/769", "completion": "\"\"\"\nWrite a python function to get the difference between two lists.\nassert (Diff([10, 15, 20, 25, 30, 35, 40], [25, 40, 35])) == [10, 20, 30, 15]\n\"\"\"\ndef Diff(list1, list2):\n    return list(set(list1) - set(list2))\n"}
{"task_id": "Mbpp/770", "completion": "\"\"\"\nWrite a python function to find the sum of fourth power of first n odd natural numbers.\nassert odd_num_sum(2) == 82\n\"\"\"\ndef odd_num_sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += (2*i-1)**4\n    return sum\n"}
{"task_id": "Mbpp/771", "completion": "\"\"\"\nWrite a function to check if the given expression is balanced or not. https://www.geeksforgeeks.org/check-for-balanced-parentheses-in-an-expression/\nassert check_expression(\"{()}[{}]\") == True\n\"\"\"\nfrom collections import deque\n\n\ndef check_expression(expression):\n    stack = deque()\n    for char in expression:\n        if char in ['(', '{', '[']:\n            stack.append(char)\n        elif char in [')', '}', ']']:\n            if not stack:\n                return False\n            top = stack.pop()\n            if (char == ')' and top != '(') or (char == '}' and top != '{') or (char == ']' and top != '['):\n                return False\n    return not stack\n\n"}
{"task_id": "Mbpp/772", "completion": "\"\"\"\nWrite a function to remove all the words with k length in the given string.\nassert remove_length('The person is most value tet', 3) == 'person is most value'\n\"\"\"\nimport re\n\n\ndef remove_length(string, k):\n    return re.sub(r'\\b\\w{%d}\\b' % k, '', string)\n\n"}
{"task_id": "Mbpp/773", "completion": "\"\"\"\nWrite a function to find the occurrence and position of the substrings within a string. Return None if there is no match.\nassert occurance_substring('python programming, python language','python')==('python', 0, 6)\n\"\"\"\ndef occurance_substring(string,substring):\n    if substring in string:\n        return substring,string.index(substring),string.index(substring)+len(substring)\n    else:\n        return None"}
{"task_id": "Mbpp/775", "completion": "\"\"\"\nWrite a python function to check whether every odd index contains odd numbers of a given list.\nassert odd_position([2,1,4,3,6,7,6,3]) == True\n\"\"\"\ndef odd_position(list1):\n    for i in range(1,len(list1),2):\n        if list1[i]%2==0:\n            return False\n    return True"}
{"task_id": "Mbpp/777", "completion": "\"\"\"\nWrite a python function to find the sum of non-repeated elements in a given list.\nassert find_sum([1,2,3,1,1,4,5,6]) == 21\n\"\"\"\ndef find_sum(l):\n    sum = 0\n    for i in l:\n        if l.count(i) == 1:\n            sum += i\n    return sum\n"}
{"task_id": "Mbpp/778", "completion": "\"\"\"\nWrite a function to pack consecutive duplicates of a given list elements into sublists.\nassert pack_consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4])==[[0, 0], [1], [2], [3], [4, 4], [5], [6, 6, 6], [7], [8], [9], [4, 4]]\n\"\"\"\ndef pack_consecutive_duplicates(list1):\n    list2 = []\n    for i in range(len(list1)):\n        if i == 0:\n            list2.append([list1[i]])\n        elif list1[i] == list1[i-1]:\n            list2[-1].append(list1[i])\n        else:\n            list2.append([list1[i]])\n    return list2"}
{"task_id": "Mbpp/780", "completion": "\"\"\"\nWrite a function to find the combinations of sums with tuples in the given tuple list. https://www.geeksforgeeks.org/python-combinations-of-sum-with-tuples-in-tuple-list/\nassert find_combinations([(2, 4), (6, 7), (5, 1), (6, 10)]) == [(8, 11), (7, 5), (8, 14), (11, 8), (12, 17), (11, 11)]\n\"\"\"\nfrom itertools import combinations\n\n\ndef find_combinations(tuples):\n    result = []\n    for i in range(1, len(tuples) + 1):\n        for combination in combinations(tuples, i):\n            sum_combination = tuple(sum(x) for x in zip(*combination))\n            result.append(sum_combination)\n    return result\n\n"}
{"task_id": "Mbpp/781", "completion": "\"\"\"\nWrite a python function to check whether the count of divisors is even. https://www.w3resource.com/python-exercises/basic/python-basic-1-exercise-24.php\nassert count_divisors(10)\n\"\"\"\ndef count_divisors(n):\n    count = 0\n    for i in range(1, n+1):\n        if n % i == 0:\n            count += 1\n    return count % 2 == 0\n"}
{"task_id": "Mbpp/782", "completion": "\"\"\"\nWrite a python function to find the sum of all odd length subarrays. https://www.geeksforgeeks.org/sum-of-all-odd-length-subarrays/\nassert odd_length_sum([1,2,4]) == 14\n\"\"\"\ndef odd_length_sum(arr):\n    sum = 0\n    for i in range(len(arr)):\n        for j in range(i, len(arr), 2):\n            sum += sum(arr[i:j+1])\n    return sum\n"}
{"task_id": "Mbpp/783", "completion": "\"\"\"\nWrite a function to convert rgb color to hsv color. https://www.geeksforgeeks.org/program-change-rgb-color-model-hsv-color-model/\nassert rgb_to_hsv(255, 255, 255)==(0, 0.0, 100.0)\n\"\"\"\ndef rgb_to_hsv(r, g, b):\n    r, g, b = r/255.0, g/255.0, b/255.0\n    mx = max(r, g, b)\n    mn = min(r, g, b)\n    df = mx-mn\n    if mx == mn:\n        h = 0\n    elif mx == r:\n        h = (60 * ((g-b)/df) + 360) % 360\n    elif mx == g:\n        h = (60 * ((b-r)/df) + 120) % 360\n    elif mx == b:\n        h = (60 * ((r-g)/df) + 240) % 360\n    if mx == 0:\n        s = 0\n    else:\n        s = (df/mx)*100\n    v = mx*100\n    return h, s, v\n"}
{"task_id": "Mbpp/784", "completion": "\"\"\"\nWrite a function to find the product of first even and odd number of a given list.\nassert mul_even_odd([1,3,5,7,4,1,6,8])==4\n\"\"\"\ndef mul_even_odd(lst):\n    even=0\n    odd=0\n    for i in lst:\n        if i%2==0:\n            even=i\n            break\n    for i in lst:\n        if i%2!=0:\n            odd=i\n            break\n    return even*odd"}
{"task_id": "Mbpp/785", "completion": "\"\"\"\nWrite a function to convert tuple string to integer tuple.\nassert tuple_str_int(\"(7, 8, 9)\") == (7, 8, 9)\n\"\"\"\ndef tuple_str_int(tup):\n    return tuple(map(int, tup[1:-1].split(', ')))\n"}
{"task_id": "Mbpp/786", "completion": "\"\"\"\nWrite a function to locate the right insertion point for a specified value in sorted order.\nassert right_insertion([1,2,4,5],6)==4\n\"\"\"\ndef right_insertion(arr, val):\n    left, right = 0, len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] <= val:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return left\n\n# Test the function"}
{"task_id": "Mbpp/787", "completion": "\"\"\"\nWrite a function that matches a string that has an a followed by three 'b'.\nassert not text_match_three(\"ac\")\n\"\"\"\nimport re\n\n\ndef text_match_three(text):\n    pattern = r\"ab{3}\"\n    if re.search(pattern, text):\n        return True\n    else:\n        return False\n\n"}
{"task_id": "Mbpp/788", "completion": "\"\"\"\nWrite a function to create a new tuple from the given string and list.\nassert new_tuple([\"WEB\", \"is\"], \"best\") == ('WEB', 'is', 'best')\n\"\"\"\ndef new_tuple(list, string):\n    return tuple(list) + (string,)\n"}
{"task_id": "Mbpp/790", "completion": "\"\"\"\nWrite a python function to check whether every even index contains even numbers of a given list.\nassert even_position([3,2,1]) == False\n\"\"\"\ndef even_position(lst):\n    for i in range(0,len(lst),2):\n        if lst[i]%2!=0:\n            return False\n    return True"}
{"task_id": "Mbpp/791", "completion": "\"\"\"\nWrite a function to remove tuples from the given tuple.\nassert remove_nested((1, 5, 7, (4, 6), 10)) == (1, 5, 7, 10)\n\"\"\"\ndef remove_nested(tup):\n    return tuple(i for i in tup if not isinstance(i, tuple))\n"}
{"task_id": "Mbpp/792", "completion": "\"\"\"\nWrite a python function to count the number of lists in a given number of lists.\nassert count_list([[1, 3], [5, 7], [9, 11], [13, 15, 17]]) == 4\n\"\"\"\ndef count_list(lists):\n    return len(lists)\n"}
{"task_id": "Mbpp/793", "completion": "\"\"\"\nWrite a python function to find the last position of an element in a sorted array.\nassert last([1,2,3],1) == 0\n\"\"\"\ndef last(arr, target):\n    left, right = 0, len(arr) - 1\n    result = -1\n\n    while left <= right:\n        mid = left + (right - left) // 2\n\n        if arr[mid] == target:\n            result = mid\n            left = mid + 1\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n\n    return result\n\n# Test cases"}
{"task_id": "Mbpp/794", "completion": "\"\"\"\nWrite a function that matches a string that has an 'a' followed by anything, ending in 'b'.\nassert text_starta_endb(\"aabbbb\")\n\"\"\"\nimport re\ndef text_starta_endb(text):\n    patterns = 'a.*?b$'\n    if re.search(patterns, text):\n        return 'Found a match!'\n    else:\n        return 'Not matched!'"}
{"task_id": "Mbpp/796", "completion": "\"\"\"\nWrite function to find the sum of all items in the given dictionary.\nassert return_sum({'a': 100, 'b':200, 'c':300}) == 600\n\"\"\"\ndef return_sum(dict):\n    sum = 0\n    for i in dict:\n        sum += dict[i]\n    return sum"}
{"task_id": "Mbpp/797", "completion": "\"\"\"\nWrite a python function to find the sum of all odd natural numbers within the range l and r.\nassert sum_in_range(2,5) == 8\n\"\"\"\ndef sum_in_range(l, r):\n    sum = 0\n    for i in range(l, r+1):\n        if i % 2 != 0:\n            sum += i\n    return sum\n"}
{"task_id": "Mbpp/798", "completion": "\"\"\"\nWrite a python function to find the sum of an array.\nassert _sum([1, 2, 3]) == 6\n\"\"\"\ndef _sum(arr):\n    sum = 0\n    for i in arr:\n        sum += i\n    return sum\n"}
{"task_id": "Mbpp/799", "completion": "\"\"\"\nWrite a function to that rotate left bits by d bits a given number. We assume that the number is 32 bit.\nassert left_rotate(16,2) == 64\n\"\"\"\ndef left_rotate(n, d):\n    return (n << d) | (n >> (32 - d))"}
{"task_id": "Mbpp/800", "completion": "\"\"\"\nWrite a function to remove all whitespaces from a string.\nassert remove_all_spaces('python  program')==('pythonprogram')\n\"\"\"\ndef remove_all_spaces(string):\n    return string.replace(\" \",\"\")"}
{"task_id": "Mbpp/801", "completion": "\"\"\"\nWrite a python function to count the number of equal numbers from three given integers.\nassert test_three_equal(1,1,1) == 3\n\"\"\"\ndef test_three_equal(a,b,c):\n    if a==b==c:\n        return 3\n    elif a==b or b==c or a==c:\n        return 2\n    else:\n        return 0"}
{"task_id": "Mbpp/803", "completion": "\"\"\"\nWrite a function to check whether the given number is a perfect square or not. https://www.geeksforgeeks.org/check-if-given-number-is-perfect-square-in-cpp/\nassert not is_perfect_square(10)\n\"\"\"\nimport math\n\n\ndef is_perfect_square(number):\n    if number < 0:\n        return False\n    root = math.sqrt(number)\n    return int(root + 0.5) ** 2 == number\n\n\ndef test_is_perfect_square():\n    assert is_perfect_square(10) is False\n    assert is_perfect_square(16) is True\n    assert is_perfect_square(25) is True\n    assert is_perfect_square(36) is True\n    assert is_perfect_square(49) is True\n    assert is_perfect_square(64) is True\n    assert is_perfect_square(81) is True\n    assert is_perfect_square(100) is True\n    assert is_perfect_square(121) is True\n    assert is_perfect_square(144) is True\n    assert is_perfect_square(169) is True\n    assert is_perfect_square(196) is True\n    assert is_perfect_square(225) is True\n    assert is_perfect_square(256) is True\n    assert is_perfect_square(289) is True\n    assert is_perfect_square(324) is True\n    assert is_perfect_square(361) is True\n    assert is_perfect_square(400) is True\n    assert is_perfect_square(441) is True\n    assert is_perfect_square(484) is True\n    assert is_perfect_square(529) is True\n    assert is_perfect_square(576) is True\n    assert is_perfect_square(625) is True\n    assert is_perfect_square(676) is True\n    assert is_perfect_square(729) is True\n    assert is_perfect_square(784) is True\n    assert is_perfect_square(841) is True\n    assert is_perfect_square(900) is True\n    assert is_perfect_square(961) is True\n    assert is_perfect_square(1024) is True\n    assert is_perfect_square(1089) is True\n    assert is_perfect_square(1156) is True\n    assert is_perfect_square(1225) is True\n    assert is_perfect_square(1296) is True\n    assert is_perfect_square(1369) is True\n    assert is_perfect_square(1444) is True\n    assert is_perfect_square(1521) is True\n    assert is_perfect_square(1600) is True\n    assert is_perfect_square(1681) is True\n    assert is_perfect_square(1764) is True\n    assert is_perfect_square(1849) is True\n    assert is_perfect_square(1936) is True\n    assert is_perfect_square(2025) is True\n    assert is_perfect_square(2116) is True\n    assert is_perfect_square(2209) is True\n    assert is_perfect_square(2304) is True\n    assert is_perfect_square(2401) is True\n    assert is_perfect_square(2500) is True\n    assert is_perfect_square(2601) is True\n    assert is_perfect_square(2704) is True\n    assert is"}
{"task_id": "Mbpp/804", "completion": "\"\"\"\nWrite a function to check whether the product of numbers in a list is even or not.\nassert is_product_even([1,2,3])\n\"\"\"\ndef is_product_even(list):\n    product = 1\n    for i in list:\n        product *= i\n    if product % 2 == 0:\n        return True\n    else:\n        return False"}
{"task_id": "Mbpp/805", "completion": "\"\"\"\nWrite a function that returns the list in a list of lists whose sum of elements is the highest.\nassert max_sum_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[10, 11, 12]\n\"\"\"\ndef max_sum_list(lists):\n    max_sum = 0\n    max_list = []\n    for lst in lists:\n        if sum(lst) > max_sum:\n            max_sum = sum(lst)\n            max_list = lst\n    return max_list\n"}
{"task_id": "Mbpp/806", "completion": "\"\"\"\nWrite a function to find maximum run of uppercase characters in the given string.\nassert max_run_uppercase('GeMKSForGERksISBESt') == 5\n\"\"\"\ndef max_run_uppercase(string):\n    max_run = 0\n    current_run = 0\n    for char in string:\n        if char.isupper():\n            current_run += 1\n            max_run = max(max_run, current_run)\n        else:\n            current_run = 0\n    return max_run\n"}
{"task_id": "Mbpp/807", "completion": "\"\"\"\nWrite a python function to find the first odd number in a given list of numbers.\nassert first_odd([1,3,5]) == 1\n\"\"\"\ndef first_odd(numbers):\n    for number in numbers:\n        if number % 2 != 0:\n            return number\n    return None\n"}
{"task_id": "Mbpp/808", "completion": "\"\"\"\nWrite a function to check if the given tuples contain the k or not.\nassert check_K((10, 4, 5, 6, 8), 6) == True\n\"\"\"\ndef check_K(tup, k):\n    return k in tup\n"}
{"task_id": "Mbpp/809", "completion": "\"\"\"\nWrite a function to check if each element of second tuple is smaller than its corresponding element in the first tuple.\nassert check_smaller((1, 2, 3), (2, 3, 4)) == False\n\"\"\"\ndef check_smaller(t1, t2):\n    for i in range(len(t1)):\n        if t1[i] < t2[i]:\n            return False\n    return True\n"}
