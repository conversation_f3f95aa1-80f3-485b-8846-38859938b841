{"task_id": "HumanEval_23_strlen", "generation": "//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string){\n\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        count++;\n    }\n    return count;\n}\n", "prompt": "//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string){\n", "wholecode": "//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string){\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        count++;\n    }\n    return count;\n}\n\n//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string){\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        count++;\n    }\n    return count;\n}\n\n//Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunction strlen(string){\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        count++;\n    }\n    return count;\n}"}
{"task_id": "HumanEval_89_encrypt", "generation": "//Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunction encrypt(s){\n\n    let alphabet = \"abcdefghijklmnopqrstuvwxyz\";\n    let newString = \"\";\n    for(let i = 0; i < s.length; i++){\n        let index = alphabet.indexOf(s[i]);\n        if(index === -1){\n            newString += s[i];\n        }else{\n            newString += alphabet[index + 2 * 2];\n        }\n    }\n    return newString;\n}", "prompt": "//Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunction encrypt(s){\n", "wholecode": "//Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunction encrypt(s){\n    let alphabet = \"abcdefghijklmnopqrstuvwxyz\";\n    let newString = \"\";\n    for(let i = 0; i < s.length; i++){\n        let index = alphabet.indexOf(s[i]);\n        if(index === -1){\n            newString += s[i];\n        }else{\n            newString += alphabet[index + 2 * 2];\n        }\n    }\n    return newString;\n}"}
{"task_id": "HumanEval_95_check_dict_case", "generation": "//Given an object, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given object is empty.\n// Examples:\n// >>> check_dict_case({\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case({\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case({\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case({\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case({\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunction check_dict_case(dict){\n\n    let lower = 0;\n    let upper = 0;\n    for (let key in dict){\n        if (typeof dict[key] === \"string\"){\n            if (dict[key].toLowerCase() === dict[key]){\n                lower++;\n            } else {\n                upper++;\n            }\n        }\n    }\n    if (lower === 0 && upper === 0){\n        return false;\n    } else if (lower === Object.keys(dict).length){\n        return true;\n    } else if (upper === Object.keys(dict).length){\n        return true;\n    } else {\n        return false;\n    }\n}", "prompt": "//Given an object, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given object is empty.\n// Examples:\n// >>> check_dict_case({\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case({\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case({\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case({\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case({\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunction check_dict_case(dict){\n", "wholecode": "//Given an object, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given object is empty.\n// Examples:\n// >>> check_dict_case({\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case({\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case({\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case({\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case({\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunction check_dict_case(dict){\n    let lower = 0;\n    let upper = 0;\n    for (let key in dict){\n        if (typeof dict[key] === \"string\"){\n            if (dict[key].toLowerCase() === dict[key]){\n                lower++;\n            } else {\n                upper++;\n            }\n        }\n    }\n    if (lower === 0 && upper === 0){\n        return false;\n    } else if (lower === Object.keys(dict).length){\n        return true;\n    } else if (upper === Object.keys(dict).length){\n        return true;\n    } else {\n        return false;\n    }\n}"}
{"task_id": "HumanEval_85_add", "generation": "//Given a non-empty array of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([4, 2, 6, 7])\n// 2\nfunction add(lst){\n\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        if(i % 2 === 0 && lst[i] % 2 === 0){\n            sum += lst[i];\n        }\n    }\n    return sum;\n}\n", "prompt": "//Given a non-empty array of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([4, 2, 6, 7])\n// 2\nfunction add(lst){\n", "wholecode": "//Given a non-empty array of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([4, 2, 6, 7])\n// 2\nfunction add(lst){\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        if(i % 2 === 0 && lst[i] % 2 === 0){\n            sum += lst[i];\n        }\n    }\n    return sum;\n}\n\n//Given a non-empty array of integers lst, return the result of multiplying the values together in order. Examples:\n// >>> multiply([1, 2, 3, 4])\n// 24\nfunction multiply(lst){\n    let product = 1;\n    for(let i = 0; i < lst.length; i++){\n        product *= lst[i];\n    }\n    return product;\n}\n\n//Given a non-empty array of integers lst, return the result of multiplying the values together in order. Examples:\n// >>> multiply([1, 2, 3, 4])\n// 24\nfunction multiply(lst){\n    let product = 1;\n    for(let i = 0; i < lst.length; i++){\n        product *= lst[i];\n    }\n    return product;\n}\n\n//Given a non-empty array of integers lst, return the result of multiplying the values together in order. Examples:\n// >>> multiply([1, 2, 3, 4])\n// 24\nfunction multiply(lst){\n    let product = 1;\n    for(let i = 0; i < lst.length; i++){\n        product *= lst[i];\n    }\n    return product;\n}\n\n//Given a non-empty array of integers lst, return the result of multiplying the values together in order. Examples:\n// >>> multiply([1, 2, 3, 4])\n// 24\nfunction multiply(lst){\n    let product = 1;\n    for(let i = 0; i < lst.length; i++){\n        product *= lst[i];\n    }\n    return product;\n}\n\n//Given a non-empty array of integers lst, return the result of multiplying the values together in order. Examples:\n// >>> multiply([1, 2, 3, 4])\n// 24\nfunction multiply(lst){\n    let product = 1;\n    for(let i = 0; i < lst.length; i++){\n        product *= lst[i];\n    }\n    return product;\n}\n\n//Given a non-empty array of integers lst, return the result of multiplying the values together in order. Examples:\n// >>> multiply([1, 2, "}
{"task_id": "HumanEval_140_fix_spaces", "generation": "//Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunction fix_spaces(text){\n\n    let result = text.replace(/\\s+/g, (match, offset) => {\n        if (match.length > 2) {\n            return match.replace(/\\s/g, \"-\");\n        } else {\n            return match.replace(/\\s/g, \"_\");\n        }\n    });\n    return result;\n}", "prompt": "//Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunction fix_spaces(text){\n", "wholecode": "//Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunction fix_spaces(text){\n    let result = text.replace(/\\s+/g, (match, offset) => {\n        if (match.length > 2) {\n            return match.replace(/\\s/g, \"-\");\n        } else {\n            return match.replace(/\\s/g, \"_\");\n        }\n    });\n    return result;\n}\nconsole.log(fix_spaces(\" Example 1\"));\nconsole.log(fix_spaces(\" Example 2\"));\nconsole.log(fix_spaces(\" Example 3\"));"}
{"task_id": "HumanEval_63_fibfib", "generation": "//The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunction fibfib(n){\n\n    if(n === 0 || n === 1){\n        return 0;\n    }else if(n === 2){\n        return 1;\n    }else{\n        return fibfib(n-1) + fibfib(n-2) + fibfib(n-3);\n    }\n}", "prompt": "//The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunction fibfib(n){\n", "wholecode": "//The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunction fibfib(n){\n    if(n === 0 || n === 1){\n        return 0;\n    }else if(n === 2){\n        return 1;\n    }else{\n        return fibfib(n-1) + fibfib(n-2) + fibfib(n-3);\n    }\n}"}
{"task_id": "HumanEval_151_double_the_difference", "generation": "//Given an array of numbers, return the sum of squares of the numbers\n// in the array that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([1, 3, 2, 0])\n// 10\n// >>> double_the_difference([-1, -2, 0])\n// 0\n// >>> double_the_difference([9, -2])\n// 81\n// >>> double_the_difference([0])\n// 0\n// If the input array is empty, return 0.\nfunction double_the_difference(lst){\n\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        if(lst[i] % 2 !== 0 && lst[i] > 0){\n            sum += lst[i] * lst[i];\n        }\n    }\n    return sum;\n}", "prompt": "//Given an array of numbers, return the sum of squares of the numbers\n// in the array that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([1, 3, 2, 0])\n// 10\n// >>> double_the_difference([-1, -2, 0])\n// 0\n// >>> double_the_difference([9, -2])\n// 81\n// >>> double_the_difference([0])\n// 0\n// If the input array is empty, return 0.\nfunction double_the_difference(lst){\n", "wholecode": "//Given an array of numbers, return the sum of squares of the numbers\n// in the array that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([1, 3, 2, 0])\n// 10\n// >>> double_the_difference([-1, -2, 0])\n// 0\n// >>> double_the_difference([9, -2])\n// 81\n// >>> double_the_difference([0])\n// 0\n// If the input array is empty, return 0.\nfunction double_the_difference(lst){\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        if(lst[i] % 2 !== 0 && lst[i] > 0){\n            sum += lst[i] * lst[i];\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_22_filter_integers", "generation": "//Filter given array of any jsthon values only for integers\n// >>> filter_integers([\"a\", 3.14, 5])\n// [5]\n// >>> filter_integers([1, 2, 3, \"abc\", {}, []])\n// [1, 2, 3]\nfunction filter_integers(values){\n\n    return values.filter((value) => {\n        return typeof value === \"number\" && Number.isInteger(value);\n    });\n}\n", "prompt": "//Filter given array of any jsthon values only for integers\n// >>> filter_integers([\"a\", 3.14, 5])\n// [5]\n// >>> filter_integers([1, 2, 3, \"abc\", {}, []])\n// [1, 2, 3]\nfunction filter_integers(values){\n", "wholecode": "//Filter given array of any jsthon values only for integers\n// >>> filter_integers([\"a\", 3.14, 5])\n// [5]\n// >>> filter_integers([1, 2, 3, \"abc\", {}, []])\n// [1, 2, 3]\nfunction filter_integers(values){\n    return values.filter((value) => {\n        return typeof value === \"number\" && Number.isInteger(value);\n    });\n}\n\n//Filter given array of any jsthon values only for strings\n// >>> filter_strings([\"a\", 3.14, 5])\n// [\"a\"]\n// >>> filter_strings([1, 2, 3, \"abc\", {}, []])\n// [\"abc\"]\nfunction filter_strings(values){\n    return values.filter((value) => {\n        return typeof value === \"string\";\n    });\n}\n\n//Filter given array of any jsthon values only for objects\n// >>> filter_objects([\"a\", 3.14, 5])\n// []\n// >>> filter_objects([1, 2, 3, \"abc\", {}, []])\n// [{}]\nfunction filter_objects(values){\n    return values.filter((value) => {\n        return typeof value === \"object\" && value !== null;\n    });\n}\n\n//Filter given array of any jsthon values only for arrays\n// >>> filter_arrays([\"a\", 3.14, 5])\n// []\n// >>> filter_arrays([1, 2, 3, \"abc\", {}, []])\n// [[], [], []]\nfunction filter_arrays(values){\n    return values.filter((value) => {\n        return Array.isArray(value);\n    });\n}\n\n//Filter given array of any jsthon values only for booleans\n// >>> filter_booleans([\"a\", 3.14, 5])\n// []\n// >>> filter_booleans([true, false, 3.14, 5])\n// [true, false]\nfunction filter_booleans(values){\n    return values.filter((value) => {\n        return typeof value === \"boolean\";\n    });\n}\n\n//Filter given array of any jsthon values only for undefined\n// >>> filter_undefined([\"a\", 3.14, 5])\n// []\n// >>> filter_undefined([undefined, 3.14, 5])\n// [undefined]\nfunction filter_undefined(values){\n    return values.filter((value) => {\n        return value === undefined;\n    });\n}\n\n//Filter given array of any jsthon values only for null\n// >>> filter_null([\"a\", 3.14, 5])\n// []\n// >>> filter_null([null, 3.14, 5])\n// [null]\nfunction filter_null(values){\n    return values.filter((value)"}
{"task_id": "HumanEval_41_car_race_collision", "generation": "//Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunction car_race_collision(n){\n\n    return n * n;\n}", "prompt": "//Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunction car_race_collision(n){\n", "wholecode": "//Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunction car_race_collision(n){\n    return n * n;\n}"}
{"task_id": "HumanEval_17_parse_music", "generation": "//Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return array of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunction parse_music(music_string){\n\n    let result = [];\n    for(let i = 0; i < music_string.length; i++){\n        if(music_string[i] === 'o'){\n            result.push(4);\n        }else if(music_string[i] === 'o|'){\n            result.push(2);\n        }else if(music_string[i] === '.|'){\n            result.push(1);\n        }\n    }\n    return result;\n}", "prompt": "//Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return array of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunction parse_music(music_string){\n", "wholecode": "//Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return array of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunction parse_music(music_string){\n    let result = [];\n    for(let i = 0; i < music_string.length; i++){\n        if(music_string[i] === 'o'){\n            result.push(4);\n        }else if(music_string[i] === 'o|'){\n            result.push(2);\n        }else if(music_string[i] === '.|'){\n            result.push(1);\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_79_decimal_to_binary", "generation": "//You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunction decimal_to_binary(decimal){\n\n    let binary = decimal.toString(2);\n    return \"db\" + binary + \"db\";\n}", "prompt": "//You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunction decimal_to_binary(decimal){\n", "wholecode": "//You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunction decimal_to_binary(decimal){\n    let binary = decimal.toString(2);\n    return \"db\" + binary + \"db\";\n}"}
{"task_id": "HumanEval_14_all_prefixes", "generation": "//Return array of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// [\"a\", \"ab\", \"abc\"]\nfunction all_prefixes(string){\n\n    let arr = []\n    for(let i = 0; i < string.length; i++){\n        arr.push(string.slice(0, i+1))\n    }\n    return arr\n}\n", "prompt": "//Return array of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// [\"a\", \"ab\", \"abc\"]\nfunction all_prefixes(string){\n", "wholecode": "//Return array of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// [\"a\", \"ab\", \"abc\"]\nfunction all_prefixes(string){\n    let arr = []\n    for(let i = 0; i < string.length; i++){\n        arr.push(string.slice(0, i+1))\n    }\n    return arr\n}\n\n//Return array of all suffixes from shortest to longest of the input string\n// >>> all_suffixes(\"abc\")\n// [\"c\", \"bc\", \"abc\"]\nfunction all_suffixes(string){\n    let arr = []\n    for(let i = 0; i < string.length; i++){\n        arr.push(string.slice(i))\n    }\n    return arr\n}\n\n//Return array of all substrings from shortest to longest of the input string\n// >>> all_substrings(\"abc\")\n// [\"a\", \"ab\", \"abc\", \"b\", \"bc\", \"c\"]\nfunction all_substrings(string){\n    let arr = []\n    for(let i = 0; i < string.length; i++){\n        for(let j = i; j < string.length; j++){\n            arr.push(string.slice(i, j+1))\n        }\n    }\n    return arr\n}\n\n//Return array of all permutations of the input string\n// >>> all_permutations(\"abc\")\n// [\"abc\", \"acb\", \"bac\", \"bca\", \"cab\", \"cba\"]\nfunction all_permutations(string){\n    let arr = []\n    for(let i = 0; i < string.length; i++){\n        for(let j = i; j < string.length; j++){\n            let temp = string.slice(i, j+1)\n            let temp2 = string.slice(0, i) + string.slice(j+1)\n            arr.push(temp2 + temp)\n        }\n    }\n    return arr\n}\n\n//Return array of all combinations of the input string\n// >>> all_combinations(\"abc\")\n// [\"a\", \"ab\", \"abc\", \"b\", \"bc\", \"c\", \"ac\", \"abc\", \"c\"]\nfunction all_combinations(string){\n    let arr = []\n    for(let i = 0; i < string.length; i++){\n        for(let j = i; j < string.length; j++){\n            let temp = string.slice(i, j+1)\n            let temp2 = string.slice(0, i) + string.slice(j+1)\n            arr.push(temp2 + temp)\n        }\n    }\n    return arr\n}\n\n//Return array of all combinations of the input string with duplicates\n// >>> all_combinations_with_duplicates(\"abc"}
{"task_id": "HumanEval_53_add", "generation": "//Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunction add(x, y){\n\n    return x + y;\n}\n", "prompt": "//Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunction add(x, y){\n", "wholecode": "//Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunction add(x, y){\n    return x + y;\n}\n\n//Subtract two numbers x and y\n// >>> subtract(2, 3)\n// -1\n// >>> subtract(5, 7)\n// -2\nfunction subtract(x, y){\n    return x - y;\n}\n\n//Multiply two numbers x and y\n// >>> multiply(2, 3)\n// 6\n// >>> multiply(5, 7)\n// 35\nfunction multiply(x, y){\n    return x * y;\n}\n\n//Divide two numbers x and y\n// >>> divide(6, 3)\n// 2\n// >>> divide(5, 7)\n// 0.7142857142857143\nfunction divide(x, y){\n    return x / y;\n}\n\n//Calculate the remainder of two numbers x and y\n// >>> remainder(2, 3)\n// 2\n// >>> remainder(5, 7)\n// 5\nfunction remainder(x, y){\n    return x % y;\n}\n\n//Calculate the power of two numbers x and y\n// >>> power(2, 3)\n// 8\n// >>> power(5, 7)\n// 78125\nfunction power(x, y){\n    return Math.pow(x, y);\n}\n\n//Calculate the square root of a number x\n// >>> squareRoot(4)\n// 2\n// >>> squareRoot(16)\n// 4\nfunction squareRoot(x){\n    return Math.sqrt(x);\n}\n\n//Calculate the absolute value of a number x\n// >>> absoluteValue(4)\n// 4\n// >>> absoluteValue(-4)\n// 4\nfunction absoluteValue(x){\n    return Math.abs(x);\n}\n\n//Calculate the sine of a number x\n// >>> sine(45)\n// 0.850918\n// >>> sine(90)\n// 1\nfunction sine(x){\n    return Math.sin(x);\n}\n\n//Calculate the cosine of a number x\n// >>> cosine(45)\n// 0.707107\n// >>> cosine(90)\n// 0\nfunction cosine(x){\n    return Math.cos(x);\n}\n\n//Calculate the tangent of a number x\n// >>> tangent(45)\n// 1\n// >>> tangent(90)\n// Infinity\nfunction tangent(x){\n    return Math.tan"}
{"task_id": "HumanEval_159_eat", "generation": "//You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return an array of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// [11, 4]\n// >>> eat(4, 8, 9)\n// [12, 1]\n// >>> eat(1, 10, 10)\n// [11, 0]\n// >>> eat(2, 11, 5)\n// [7, 0]\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunction eat(number, need, remaining){\n\n    let eaten = number + need;\n    let left = remaining - need;\n    if (eaten > remaining) {\n        return [eaten, 0];\n    } else {\n        return [eaten, left];\n    }\n}", "prompt": "//You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return an array of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// [11, 4]\n// >>> eat(4, 8, 9)\n// [12, 1]\n// >>> eat(1, 10, 10)\n// [11, 0]\n// >>> eat(2, 11, 5)\n// [7, 0]\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunction eat(number, need, remaining){\n", "wholecode": "//You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return an array of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// [11, 4]\n// >>> eat(4, 8, 9)\n// [12, 1]\n// >>> eat(1, 10, 10)\n// [11, 0]\n// >>> eat(2, 11, 5)\n// [7, 0]\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunction eat(number, need, remaining){\n    let eaten = number + need;\n    let left = remaining - need;\n    if (eaten > remaining) {\n        return [eaten, 0];\n    } else {\n        return [eaten, left];\n    }\n}"}
{"task_id": "HumanEval_115_max_fill", "generation": "//You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n// 6\n// Example 2:\n// >>> max_fill([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n// 5\n// Example 3:\n// >>> max_fill([[0, 0, 0], [0, 0, 0]], 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunction max_fill(grid, capacity){\n\n    let count = 0;\n    for(let i = 0; i < grid.length; i++){\n        for(let j = 0; j < grid[i].length; j++){\n            if(grid[i][j] === 1){\n                count += Math.ceil(grid[i][j] / capacity);\n            }\n        }\n    }\n    return count;\n}", "prompt": "//You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n// 6\n// Example 2:\n// >>> max_fill([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n// 5\n// Example 3:\n// >>> max_fill([[0, 0, 0], [0, 0, 0]], 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunction max_fill(grid, capacity){\n", "wholecode": "//You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n// 6\n// Example 2:\n// >>> max_fill([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n// 5\n// Example 3:\n// >>> max_fill([[0, 0, 0], [0, 0, 0]], 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunction max_fill(grid, capacity){\n    let count = 0;\n    for(let i = 0; i < grid.length; i++){\n        for(let j = 0; j < grid[i].length; j++){\n            if(grid[i][j] === 1){\n                count += Math.ceil(grid[i][j] / capacity);\n            }\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_160_do_algebra", "generation": "//Given two arrays operator, and operand. The first array has basic algebra operations, and \n// the second array is an array of integers. Use the two given arrays to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// array = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator array is equal to the length of operand array minus one.\n// Operand is an array of of non-negative integers.\n// Operator array has at least one operator, and operand array has at least two operands.\nfunction do_algebra(operator, operand){\n\n    let result = operand[0];\n    for(let i = 0; i < operator.length; i++){\n        if(operator[i] === '+'){\n            result += operand[i+1];\n        }else if(operator[i] === '-'){\n            result -= operand[i+1];\n        }else if(operator[i] === '*'){\n            result *= operand[i+1];\n        }else if(operator[i] === '//'){\n            result = Math.floor(result / operand[i+1]);\n        }else if(operator[i] === '**'){\n            result = Math.pow(result, operand[i+1]);\n        }\n    }\n    return result;\n}", "prompt": "//Given two arrays operator, and operand. The first array has basic algebra operations, and \n// the second array is an array of integers. Use the two given arrays to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// array = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator array is equal to the length of operand array minus one.\n// Operand is an array of of non-negative integers.\n// Operator array has at least one operator, and operand array has at least two operands.\nfunction do_algebra(operator, operand){\n", "wholecode": "//Given two arrays operator, and operand. The first array has basic algebra operations, and \n// the second array is an array of integers. Use the two given arrays to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// array = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator array is equal to the length of operand array minus one.\n// Operand is an array of of non-negative integers.\n// Operator array has at least one operator, and operand array has at least two operands.\nfunction do_algebra(operator, operand){\n    let result = operand[0];\n    for(let i = 0; i < operator.length; i++){\n        if(operator[i] === '+'){\n            result += operand[i+1];\n        }else if(operator[i] === '-'){\n            result -= operand[i+1];\n        }else if(operator[i] === '*'){\n            result *= operand[i+1];\n        }else if(operator[i] === '//'){\n            result = Math.floor(result / operand[i+1]);\n        }else if(operator[i] === '**'){\n            result = Math.pow(result, operand[i+1]);\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_27_flip_case", "generation": "//For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunction flip_case(string){\n\n    let result = \"\";\n    for(let i = 0; i < string.length; i++){\n        if(string[i] === string[i].toLowerCase()){\n            result += string[i].toUpperCase();\n        }else{\n            result += string[i].toLowerCase();\n        }\n    }\n    return result;\n}", "prompt": "//For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunction flip_case(string){\n", "wholecode": "//For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunction flip_case(string){\n    let result = \"\";\n    for(let i = 0; i < string.length; i++){\n        if(string[i] === string[i].toLowerCase()){\n            result += string[i].toUpperCase();\n        }else{\n            result += string[i].toLowerCase();\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_105_by_length", "generation": "//Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting array, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([2, 1, 1, 4, 5, 8, 2, 3])\n// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n// If the array is empty, return an empty array:\n// >>> by_length([])\n// []\n// If the array has any strange number ignore it:\n// >>> by_length([1, -1, 55])\n// [\"One\"]\nfunction by_length(arr){\n\n    if(arr.length === 0){\n        return [];\n    }\n    let newArr = arr.filter(num => num >= 1 && num <= 9).sort((a,b) => b - a);\n    let result = [];\n    for(let i = 0; i < newArr.length; i++){\n        if(newArr[i] === 1){\n            result.push(\"One\");\n        }else if(newArr[i] === 2){\n            result.push(\"Two\");\n        }else if(newArr[i] === 3){\n            result.push(\"Three\");\n        }else if(newArr[i] === 4){\n            result.push(\"Four\");\n        }else if(newArr[i] === 5){\n            result.push(\"Five\");\n        }else if(newArr[i] === 6){\n            result.push(\"Six\");\n        }else if(newArr[i] === 7){\n            result.push(\"Seven\");\n        }else if(newArr[i] === 8){\n            result.push(\"Eight\");\n        }else if(newArr[i] === 9){\n            result.push(\"Nine\");\n        }\n    }\n    return result.reverse();\n}", "prompt": "//Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting array, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([2, 1, 1, 4, 5, 8, 2, 3])\n// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n// If the array is empty, return an empty array:\n// >>> by_length([])\n// []\n// If the array has any strange number ignore it:\n// >>> by_length([1, -1, 55])\n// [\"One\"]\nfunction by_length(arr){\n", "wholecode": "//Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting array, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([2, 1, 1, 4, 5, 8, 2, 3])\n// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n// If the array is empty, return an empty array:\n// >>> by_length([])\n// []\n// If the array has any strange number ignore it:\n// >>> by_length([1, -1, 55])\n// [\"One\"]\nfunction by_length(arr){\n    if(arr.length === 0){\n        return [];\n    }\n    let newArr = arr.filter(num => num >= 1 && num <= 9).sort((a,b) => b - a);\n    let result = [];\n    for(let i = 0; i < newArr.length; i++){\n        if(newArr[i] === 1){\n            result.push(\"One\");\n        }else if(newArr[i] === 2){\n            result.push(\"Two\");\n        }else if(newArr[i] === 3){\n            result.push(\"Three\");\n        }else if(newArr[i] === 4){\n            result.push(\"Four\");\n        }else if(newArr[i] === 5){\n            result.push(\"Five\");\n        }else if(newArr[i] === 6){\n            result.push(\"Six\");\n        }else if(newArr[i] === 7){\n            result.push(\"Seven\");\n        }else if(newArr[i] === 8){\n            result.push(\"Eight\");\n        }else if(newArr[i] === 9){\n            result.push(\"Nine\");\n        }\n    }\n    return result.reverse();\n}"}
{"task_id": "HumanEval_25_factorize", "generation": "//Return array of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// [2, 2, 2]\n// >>> factorize(25)\n// [5, 5]\n// >>> factorize(70)\n// [2, 5, 7]\nfunction factorize(n){\n\n    let arr = [];\n    let i = 2;\n    while(n > 1){\n        if(n % i === 0){\n            arr.push(i);\n            n = n / i;\n        } else {\n            i++;\n        }\n    }\n    return arr;\n}", "prompt": "//Return array of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// [2, 2, 2]\n// >>> factorize(25)\n// [5, 5]\n// >>> factorize(70)\n// [2, 5, 7]\nfunction factorize(n){\n", "wholecode": "//Return array of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// [2, 2, 2]\n// >>> factorize(25)\n// [5, 5]\n// >>> factorize(70)\n// [2, 5, 7]\nfunction factorize(n){\n    let arr = [];\n    let i = 2;\n    while(n > 1){\n        if(n % i === 0){\n            arr.push(i);\n            n = n / i;\n        } else {\n            i++;\n        }\n    }\n    return arr;\n}"}
{"task_id": "HumanEval_96_count_up_to", "generation": "//Implement a function that takes an non-negative integer and returns an array of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// [2, 3]\n// >>> count_up_to(11)\n// [2, 3, 5, 7]\n// >>> count_up_to(0)\n// []\n// >>> count_up_to(20)\n// [2, 3, 5, 7, 11, 13, 17, 19]\n// >>> count_up_to(1)\n// []\n// >>> count_up_to(18)\n// [2, 3, 5, 7, 11, 13, 17]\nfunction count_up_to(n){\n\n    let arr = [];\n    for(let i = 2; i < n; i++){\n        if(isPrime(i)){\n            arr.push(i);\n        }\n    }\n    return arr;\n}\n", "prompt": "//Implement a function that takes an non-negative integer and returns an array of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// [2, 3]\n// >>> count_up_to(11)\n// [2, 3, 5, 7]\n// >>> count_up_to(0)\n// []\n// >>> count_up_to(20)\n// [2, 3, 5, 7, 11, 13, 17, 19]\n// >>> count_up_to(1)\n// []\n// >>> count_up_to(18)\n// [2, 3, 5, 7, 11, 13, 17]\nfunction count_up_to(n){\n", "wholecode": "//Implement a function that takes an non-negative integer and returns an array of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// [2, 3]\n// >>> count_up_to(11)\n// [2, 3, 5, 7]\n// >>> count_up_to(0)\n// []\n// >>> count_up_to(20)\n// [2, 3, 5, 7, 11, 13, 17, 19]\n// >>> count_up_to(1)\n// []\n// >>> count_up_to(18)\n// [2, 3, 5, 7, 11, 13, 17]\nfunction count_up_to(n){\n    let arr = [];\n    for(let i = 2; i < n; i++){\n        if(isPrime(i)){\n            arr.push(i);\n        }\n    }\n    return arr;\n}\n\nfunction isPrime(n){\n    for(let i = 2; i < n; i++){\n        if(n % i === 0){\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_34_unique", "generation": "//Return sorted unique elements in an array\n// >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [0, 2, 3, 5, 9, 123]\nfunction unique(l){\n\n    return l.filter((v,i,a)=>a.indexOf(v)===i)\n}", "prompt": "//Return sorted unique elements in an array\n// >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [0, 2, 3, 5, 9, 123]\nfunction unique(l){\n", "wholecode": "//Return sorted unique elements in an array\n// >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [0, 2, 3, 5, 9, 123]\nfunction unique(l){\n    return l.filter((v,i,a)=>a.indexOf(v)===i)\n}\nconsole.log(unique([5, 3, 5, 2, 3, 3, 9, 0, 123]))"}
{"task_id": "HumanEval_74_total_match", "generation": "//Write a function that accepts two arrays of strings and returns the array that has \n// total number of chars in the all strings of the array less than the other array.\n// if the two arrays have the same number of chars, return the first array.\n// Examples\n// >>> total_match([], [])\n// []\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n// [\"hI\", \"Hi\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n// [\"hi\", \"admin\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n// [\"hI\", \"hi\", \"hi\"]\n// >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n// [\"4\"]\nfunction total_match(lst1, lst2){\n\n    let count1 = 0;\n    let count2 = 0;\n    for(let i = 0; i < lst1.length; i++){\n        count1 += lst1[i].length;\n    }\n    for(let i = 0; i < lst2.length; i++){\n        count2 += lst2[i].length;\n    }\n    if(count1 < count2){\n        return lst1;\n    }else if(count1 > count2){\n        return lst2;\n    }else{\n        return lst1;\n    }\n}", "prompt": "//Write a function that accepts two arrays of strings and returns the array that has \n// total number of chars in the all strings of the array less than the other array.\n// if the two arrays have the same number of chars, return the first array.\n// Examples\n// >>> total_match([], [])\n// []\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n// [\"hI\", \"Hi\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n// [\"hi\", \"admin\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n// [\"hI\", \"hi\", \"hi\"]\n// >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n// [\"4\"]\nfunction total_match(lst1, lst2){\n", "wholecode": "//Write a function that accepts two arrays of strings and returns the array that has \n// total number of chars in the all strings of the array less than the other array.\n// if the two arrays have the same number of chars, return the first array.\n// Examples\n// >>> total_match([], [])\n// []\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n// [\"hI\", \"Hi\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n// [\"hi\", \"admin\"]\n// >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n// [\"hI\", \"hi\", \"hi\"]\n// >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n// [\"4\"]\nfunction total_match(lst1, lst2){\n    let count1 = 0;\n    let count2 = 0;\n    for(let i = 0; i < lst1.length; i++){\n        count1 += lst1[i].length;\n    }\n    for(let i = 0; i < lst2.length; i++){\n        count2 += lst2[i].length;\n    }\n    if(count1 < count2){\n        return lst1;\n    }else if(count1 > count2){\n        return lst2;\n    }else{\n        return lst1;\n    }\n}"}
{"task_id": "HumanEval_35_max_element", "generation": "//Return maximum element in the array.\n// >>> max_element([1, 2, 3])\n// 3\n// >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 123\nfunction max_element(l){\n\n    let max = l[0];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] > max){\n            max = l[i];\n        }\n    }\n    return max;\n}\n", "prompt": "//Return maximum element in the array.\n// >>> max_element([1, 2, 3])\n// 3\n// >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 123\nfunction max_element(l){\n", "wholecode": "//Return maximum element in the array.\n// >>> max_element([1, 2, 3])\n// 3\n// >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 123\nfunction max_element(l){\n    let max = l[0];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] > max){\n            max = l[i];\n        }\n    }\n    return max;\n}\n\n//Return minimum element in the array.\n// >>> min_element([1, 2, 3])\n// 1\n// >>> min_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// -10\nfunction min_element(l){\n    let min = l[0];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] < min){\n            min = l[i];\n        }\n    }\n    return min;\n}\n\n//Return the sum of all elements in the array.\n// >>> sum([1, 2, 3])\n// 6\n// >>> sum([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 131\nfunction sum(l){\n    let sum = 0;\n    for(let i = 0; i < l.length; i++){\n        sum += l[i];\n    }\n    return sum;\n}\n\n//Return the product of all elements in the array.\n// >>> product([1, 2, 3])\n// 6\n// >>> product([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 0\nfunction product(l){\n    let product = 1;\n    for(let i = 0; i < l.length; i++){\n        product *= l[i];\n    }\n    return product;\n}\n\n//Return the average of all elements in the array.\n// >>> average([1, 2, 3])\n// 2\n// >>> average([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// 17.6\nfunction average(l){\n    let sum = 0;\n    for(let i = 0; i < l.length; i++){\n        sum += l[i];\n    }\n    return sum / l.length"}
{"task_id": "HumanEval_132_is_nested", "generation": "//Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunction is_nested(string){\n\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        if(string[i] === '['){\n            count++;\n        }else if(string[i] === ']'){\n            count--;\n        }\n        if(count < 0){\n            return false;\n        }\n    }\n    return count === 0;\n}", "prompt": "//Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunction is_nested(string){\n", "wholecode": "//Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunction is_nested(string){\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        if(string[i] === '['){\n            count++;\n        }else if(string[i] === ']'){\n            count--;\n        }\n        if(count < 0){\n            return false;\n        }\n    }\n    return count === 0;\n}"}
{"task_id": "HumanEval_103_rounded_avg", "generation": "//You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg(1, 5)\n// \"0b11\"\n// >>> rounded_avg(7, 5)\n// -1\n// >>> rounded_avg(10, 20)\n// \"0b1111\"\n// >>> rounded_avg(20, 33)\n// \"0b11010\"\nfunction rounded_avg(n, m){\n\n    if (n > m) return -1\n    let sum = 0\n    for (let i = n; i <= m; i++) {\n        sum += i\n    }\n    let avg = Math.round(sum / (m - n + 1))\n    return avg.toString(2)\n}", "prompt": "//You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg(1, 5)\n// \"0b11\"\n// >>> rounded_avg(7, 5)\n// -1\n// >>> rounded_avg(10, 20)\n// \"0b1111\"\n// >>> rounded_avg(20, 33)\n// \"0b11010\"\nfunction rounded_avg(n, m){\n", "wholecode": "//You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg(1, 5)\n// \"0b11\"\n// >>> rounded_avg(7, 5)\n// -1\n// >>> rounded_avg(10, 20)\n// \"0b1111\"\n// >>> rounded_avg(20, 33)\n// \"0b11010\"\nfunction rounded_avg(n, m){\n    if (n > m) return -1\n    let sum = 0\n    for (let i = n; i <= m; i++) {\n        sum += i\n    }\n    let avg = Math.round(sum / (m - n + 1))\n    return avg.toString(2)\n}"}
{"task_id": "HumanEval_113_odd_count", "generation": "//Given an array of strings, where each string consists of only digits, return an array.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([\"1234567\"])\n// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n// >>> odd_count([\"3\", \"11111111\"])\n// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunction odd_count(lst){\n\n  let result = [];\n  for (let i = 0; i < lst.length; i++) {\n    let count = 0;\n    for (let j = 0; j < lst[i].length; j++) {\n      if (lst[i][j] % 2 !== 0) {\n        count++;\n      }\n    }\n    result.push(`the number of odd elements ${count}n the str${i + 1}ng ${i + 1} of the ${i + 1}nput.`);\n  }\n  return result;\n}", "prompt": "//Given an array of strings, where each string consists of only digits, return an array.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([\"1234567\"])\n// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n// >>> odd_count([\"3\", \"11111111\"])\n// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunction odd_count(lst){\n", "wholecode": "//Given an array of strings, where each string consists of only digits, return an array.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([\"1234567\"])\n// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n// >>> odd_count([\"3\", \"11111111\"])\n// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunction odd_count(lst){\n  let result = [];\n  for (let i = 0; i < lst.length; i++) {\n    let count = 0;\n    for (let j = 0; j < lst[i].length; j++) {\n      if (lst[i][j] % 2 !== 0) {\n        count++;\n      }\n    }\n    result.push(`the number of odd elements ${count}n the str${i + 1}ng ${i + 1} of the ${i + 1}nput.`);\n  }\n  return result;\n}"}
{"task_id": "HumanEval_109_move_one_ball", "generation": "//We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the array will be randomly ordered. Your task is to determine if\n// it is possible to get an array sorted in non-decreasing order by performing \n// the following operation on the given array:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the array by one\n// position in the right direction. The last element of the array will be moved to\n// the starting position in the array i.e. 0th index. \n// If it is possible to obtain the sorted array by performing the above operation\n// then return true else return false.\n// If the given array is empty then return true.\n// Note: The given array is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([3, 4, 5, 1, 2])\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given array.\n// >>> move_one_ball([3, 5, 4, 1, 2])\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// array by performing any number of right shift operations.\nfunction move_one_ball(arr){\n\n    if(arr.length === 0) return true;\n    let sorted = arr.slice().sort((a,b) => a-b);\n    let i = 0;\n    while(i < arr.length && arr[i] === sorted[i]) i++;\n    return i === arr.length;\n}", "prompt": "//We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the array will be randomly ordered. Your task is to determine if\n// it is possible to get an array sorted in non-decreasing order by performing \n// the following operation on the given array:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the array by one\n// position in the right direction. The last element of the array will be moved to\n// the starting position in the array i.e. 0th index. \n// If it is possible to obtain the sorted array by performing the above operation\n// then return true else return false.\n// If the given array is empty then return true.\n// Note: The given array is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([3, 4, 5, 1, 2])\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given array.\n// >>> move_one_ball([3, 5, 4, 1, 2])\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// array by performing any number of right shift operations.\nfunction move_one_ball(arr){\n", "wholecode": "//We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the array will be randomly ordered. Your task is to determine if\n// it is possible to get an array sorted in non-decreasing order by performing \n// the following operation on the given array:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the array by one\n// position in the right direction. The last element of the array will be moved to\n// the starting position in the array i.e. 0th index. \n// If it is possible to obtain the sorted array by performing the above operation\n// then return true else return false.\n// If the given array is empty then return true.\n// Note: The given array is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([3, 4, 5, 1, 2])\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given array.\n// >>> move_one_ball([3, 5, 4, 1, 2])\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// array by performing any number of right shift operations.\nfunction move_one_ball(arr){\n    if(arr.length === 0) return true;\n    let sorted = arr.slice().sort((a,b) => a-b);\n    let i = 0;\n    while(i < arr.length && arr[i] === sorted[i]) i++;\n    return i === arr.length;\n}"}
{"task_id": "HumanEval_107_even_odd_palindrome", "generation": "//Given a positive integer n, return an array that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// [1, 2]\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// [4, 6]\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned array has the number of even and odd integer palindromes respectively.\nfunction even_odd_palindrome(n){\n\n    let even = 0;\n    let odd = 0;\n    for(let i = 1; i <= n; i++){\n        if(isPalindrome(i)){\n            if(i % 2 === 0){\n                even++;\n            }else{\n                odd++;\n            }\n        }\n    }\n    return [even, odd];\n}\n", "prompt": "//Given a positive integer n, return an array that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// [1, 2]\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// [4, 6]\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned array has the number of even and odd integer palindromes respectively.\nfunction even_odd_palindrome(n){\n", "wholecode": "//Given a positive integer n, return an array that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// [1, 2]\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// [4, 6]\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned array has the number of even and odd integer palindromes respectively.\nfunction even_odd_palindrome(n){\n    let even = 0;\n    let odd = 0;\n    for(let i = 1; i <= n; i++){\n        if(isPalindrome(i)){\n            if(i % 2 === 0){\n                even++;\n            }else{\n                odd++;\n            }\n        }\n    }\n    return [even, odd];\n}\n\nfunction isPalindrome(n){\n    let str = n.toString();\n    let left = 0;\n    let right = str.length - 1;\n    while(left < right){\n        if(str[left] !== str[right]){\n            return false;\n        }\n        left++;\n        right--;\n    }\n    return true;\n}\n\nconsole.log(even_odd_palindrome(3));\nconsole.log(even_odd_palindrome(12));"}
{"task_id": "HumanEval_138_is_equal_to_sum_even", "generation": "//Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunction is_equal_to_sum_even(n){\n\n    if(n%2==1){\n        return false\n    }\n    if(n%4==0){\n        return true\n    }\n    return false\n}", "prompt": "//Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunction is_equal_to_sum_even(n){\n", "wholecode": "//Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunction is_equal_to_sum_even(n){\n    if(n%2==1){\n        return false\n    }\n    if(n%4==0){\n        return true\n    }\n    return false\n}"}
{"task_id": "HumanEval_62_derivative", "generation": "//xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([3, 1, 2, 4, 5])\n// [1, 4, 12, 20]\n// >>> derivative([1, 2, 3])\n// [2, 6]\nfunction derivative(xs){\n\n    let result = [];\n    for(let i = 0; i < xs.length - 1; i++){\n        result.push(xs[i] * (i + 1));\n    }\n    return result;\n}", "prompt": "//xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([3, 1, 2, 4, 5])\n// [1, 4, 12, 20]\n// >>> derivative([1, 2, 3])\n// [2, 6]\nfunction derivative(xs){\n", "wholecode": "//xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([3, 1, 2, 4, 5])\n// [1, 4, 12, 20]\n// >>> derivative([1, 2, 3])\n// [2, 6]\nfunction derivative(xs){\n    let result = [];\n    for(let i = 0; i < xs.length - 1; i++){\n        result.push(xs[i] * (i + 1));\n    }\n    return result;\n}"}
{"task_id": "HumanEval_126_is_sorted", "generation": "//Given an array of numbers, return whether or not they are sorted\n// in ascending order. If array has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([5])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5])\n// false\n// >>> is_sorted([1, 2, 3, 4, 5, 6])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5, 6, 7])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5, 6, 7])\n// false\n// >>> is_sorted([1, 2, 2, 3, 3, 4])\n// true\n// >>> is_sorted([1, 2, 2, 2, 3, 4])\n// false\nfunction is_sorted(lst){\n\n    for (let i = 0; i < lst.length; i++) {\n        if (lst[i] > lst[i+1]) {\n            return false;\n        }\n    }\n    return true;\n}\n", "prompt": "//Given an array of numbers, return whether or not they are sorted\n// in ascending order. If array has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([5])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5])\n// false\n// >>> is_sorted([1, 2, 3, 4, 5, 6])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5, 6, 7])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5, 6, 7])\n// false\n// >>> is_sorted([1, 2, 2, 3, 3, 4])\n// true\n// >>> is_sorted([1, 2, 2, 2, 3, 4])\n// false\nfunction is_sorted(lst){\n", "wholecode": "//Given an array of numbers, return whether or not they are sorted\n// in ascending order. If array has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([5])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5])\n// false\n// >>> is_sorted([1, 2, 3, 4, 5, 6])\n// true\n// >>> is_sorted([1, 2, 3, 4, 5, 6, 7])\n// true\n// >>> is_sorted([1, 3, 2, 4, 5, 6, 7])\n// false\n// >>> is_sorted([1, 2, 2, 3, 3, 4])\n// true\n// >>> is_sorted([1, 2, 2, 2, 3, 4])\n// false\nfunction is_sorted(lst){\n    for (let i = 0; i < lst.length; i++) {\n        if (lst[i] > lst[i+1]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nconsole.log(is_sorted([1, 2, 3, 4, 5, 6, 7]));"}
{"task_id": "HumanEval_161_solve", "generation": "//You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunction solve(s){\n\n    let arr = s.split('')\n    let newArr = []\n    for(let i = 0; i < arr.length; i++){\n      if(arr[i] === arr[i].toUpperCase()){\n        newArr.push(arr[i].toLowerCase())\n      }else if(arr[i] === arr[i].toLowerCase()){\n        newArr.push(arr[i].toUpperCase())\n      }else{\n        newArr.push(arr[i])\n      }\n    }\n    if(newArr.every((el) => el === newArr[0])){\n      return newArr.reverse().join('')\n    }else{\n      return newArr.join('')\n    }\n  }", "prompt": "//You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunction solve(s){\n", "wholecode": "//You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunction solve(s){\n    let arr = s.split('')\n    let newArr = []\n    for(let i = 0; i < arr.length; i++){\n      if(arr[i] === arr[i].toUpperCase()){\n        newArr.push(arr[i].toLowerCase())\n      }else if(arr[i] === arr[i].toLowerCase()){\n        newArr.push(arr[i].toUpperCase())\n      }else{\n        newArr.push(arr[i])\n      }\n    }\n    if(newArr.every((el) => el === newArr[0])){\n      return newArr.reverse().join('')\n    }else{\n      return newArr.join('')\n    }\n  }"}
{"task_id": "HumanEval_130_tri", "generation": "//Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return an array of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// [1, 3, 2, 8]\nfunction tri(n){\n\n    let arr = [1, 3, 2];\n    for(let i = 3; i <= n; i++){\n        if(i % 2 === 0){\n            arr.push(1 + (i / 2));\n        }else{\n            arr.push(arr[i - 1] + arr[i - 2] + arr[i + 1]);\n        }\n    }\n    return arr;\n}", "prompt": "//Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return an array of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// [1, 3, 2, 8]\nfunction tri(n){\n", "wholecode": "//Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return an array of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// [1, 3, 2, 8]\nfunction tri(n){\n    let arr = [1, 3, 2];\n    for(let i = 3; i <= n; i++){\n        if(i % 2 === 0){\n            arr.push(1 + (i / 2));\n        }else{\n            arr.push(arr[i - 1] + arr[i - 2] + arr[i + 1]);\n        }\n    }\n    return arr;\n}"}
{"task_id": "HumanEval_36_fizz_buzz", "generation": "//Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunction fizz_buzz(n){\n\n    let count = 0;\n    for(let i = 0; i < n; i++){\n        if(i % 11 === 0 || i % 13 === 0){\n            count += i.toString().split('').filter(x => x === '7').length;\n        }\n    }\n    return count;\n}", "prompt": "//Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunction fizz_buzz(n){\n", "wholecode": "//Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunction fizz_buzz(n){\n    let count = 0;\n    for(let i = 0; i < n; i++){\n        if(i % 11 === 0 || i % 13 === 0){\n            count += i.toString().split('').filter(x => x === '7').length;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_29_filter_by_prefix", "generation": "//Filter an input array of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([], \"a\")\n// []\n// >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"array\"]\nfunction filter_by_prefix(strings, prefix){\n\n    return strings.filter((string) => string.startsWith(prefix));\n}", "prompt": "//Filter an input array of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([], \"a\")\n// []\n// >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"array\"]\nfunction filter_by_prefix(strings, prefix){\n", "wholecode": "//Filter an input array of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([], \"a\")\n// []\n// >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"array\"]\nfunction filter_by_prefix(strings, prefix){\n    return strings.filter((string) => string.startsWith(prefix));\n}"}
{"task_id": "HumanEval_84_solve", "generation": "//Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 ≤ N ≤ 10000.\n// Output:\n// a string of binary number\nfunction solve(N){\n\n    let sum = 0;\n    let binary = \"\";\n    while(N > 0){\n        sum += N % 2;\n        N = Math.floor(N / 2);\n    }\n    while(sum > 0){\n        binary += sum % 2;\n        sum = Math.floor(sum / 2);\n    }\n    return binary.split(\"\").reverse().join(\"\");\n}", "prompt": "//Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 ≤ N ≤ 10000.\n// Output:\n// a string of binary number\nfunction solve(N){\n", "wholecode": "//Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 ≤ N ≤ 10000.\n// Output:\n// a string of binary number\nfunction solve(N){\n    let sum = 0;\n    let binary = \"\";\n    while(N > 0){\n        sum += N % 2;\n        N = Math.floor(N / 2);\n    }\n    while(sum > 0){\n        binary += sum % 2;\n        sum = Math.floor(sum / 2);\n    }\n    return binary.split(\"\").reverse().join(\"\");\n}"}
{"task_id": "HumanEval_129_minPath", "generation": "//Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered arrays of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered array of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n// [1, 2, 1]\n// >>> minPath([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n// [1]\nfunction minPath(grid, k){\n\n    let minPath = [];\n    let minPathLength = Infinity;\n    let visited = new Set();\n    let queue = [[0, 0, 0]];\n    while (queue.length > 0) {\n        let [row, col, pathLength] = queue.shift();\n        if (pathLength > k) continue;\n        if (pathLength > minPathLength) continue;\n        if (pathLength === k) {\n            if (pathLength < minPathLength) {\n                minPath = [];\n                minPathLength = pathLength;\n            }\n            minPath.push(grid[row][col]);\n            continue;\n        }\n        if (visited.has(`${row},${col}`)) continue;\n        visited.add(`${row},${col}`);\n        for (let [r, c] of [[row - 1, col], [row + 1, col], [row, col - 1], [row, col + 1]]) {\n            if (r < 0 || r >= grid.length || c < 0 || c >= grid[0].length) continue;\n            queue.push([r, c, pathLength + 1]);\n        }\n    }\n    return minPath;\n}", "prompt": "//Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered arrays of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered array of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n// [1, 2, 1]\n// >>> minPath([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n// [1]\nfunction minPath(grid, k){\n", "wholecode": "//Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered arrays of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered array of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n// [1, 2, 1]\n// >>> minPath([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n// [1]\nfunction minPath(grid, k){\n    let minPath = [];\n    let minPathLength = Infinity;\n    let visited = new Set();\n    let queue = [[0, 0, 0]];\n    while (queue.length > 0) {\n        let [row, col, pathLength] = queue.shift();\n        if (pathLength > k) continue;\n        if (pathLength > minPathLength) continue;\n        if (pathLength === k) {\n            if (pathLength < minPathLength) {\n                minPath = [];\n                minPathLength = pathLength;\n            }\n            minPath.push(grid[row][col]);\n            continue;\n        }\n        if (visited.has(`${row},${col}`)) continue;\n        visited.add(`${row},${col}`);\n        for (let [r, c] of [[row - 1, col], [row + 1, col], [row, col - 1], [row, col + 1]]) {\n            if (r < 0 || r >= grid.length || c < 0 || c >= grid[0].length) continue;\n            queue.push([r, c, pathLength + 1]);\n        }\n    }\n    return minPath;\n}"}
{"task_id": "HumanEval_98_count_upper", "generation": "//Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunction count_upper(s){\n\n    let count = 0;\n    for(let i = 0; i < s.length; i++){\n        if(i % 2 === 0 && s[i] === s[i].toUpperCase()){\n            count++;\n        }\n    }\n    return count;\n}", "prompt": "//Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunction count_upper(s){\n", "wholecode": "//Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunction count_upper(s){\n    let count = 0;\n    for(let i = 0; i < s.length; i++){\n        if(i % 2 === 0 && s[i] === s[i].toUpperCase()){\n            count++;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_120_maximum", "generation": "//Given an array arr of integers and a positive integer k, return a sorted array \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([-3, -4, 5], 3)\n// [-4, -3, 5]\n// Example 2:\n// >>> maximum([4, -4, 4], 2)\n// [4, 4]\n// Example 3:\n// >>> maximum([-3, 2, 1, 2, -1, -2, 1], 1)\n// [2]\n// Note:\n// 1. The length of the array will be in the range of [1, 1000].\n// 2. The elements in the array will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunction maximum(arr, k){\n\n    let max = arr.sort((a,b) => b-a);\n    return max.slice(0,k);\n}", "prompt": "//Given an array arr of integers and a positive integer k, return a sorted array \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([-3, -4, 5], 3)\n// [-4, -3, 5]\n// Example 2:\n// >>> maximum([4, -4, 4], 2)\n// [4, 4]\n// Example 3:\n// >>> maximum([-3, 2, 1, 2, -1, -2, 1], 1)\n// [2]\n// Note:\n// 1. The length of the array will be in the range of [1, 1000].\n// 2. The elements in the array will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunction maximum(arr, k){\n", "wholecode": "//Given an array arr of integers and a positive integer k, return a sorted array \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([-3, -4, 5], 3)\n// [-4, -3, 5]\n// Example 2:\n// >>> maximum([4, -4, 4], 2)\n// [4, 4]\n// Example 3:\n// >>> maximum([-3, 2, 1, 2, -1, -2, 1], 1)\n// [2]\n// Note:\n// 1. The length of the array will be in the range of [1, 1000].\n// 2. The elements in the array will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunction maximum(arr, k){\n    let max = arr.sort((a,b) => b-a);\n    return max.slice(0,k);\n}"}
{"task_id": "HumanEval_24_largest_divisor", "generation": "//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n", "prompt": "//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n", "wholecode": "//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunction largest_divisor(n){\n    for(let i = n-1; i > 0; i--){\n        if(n % i === 0){\n            return i\n        }\n    }\n}\n\n//For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15"}
{"task_id": "HumanEval_88_sort_array", "generation": "//Given an array of non-negative integers, return a cojs of the given array after sorting,\n// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given array.\n// Examples:\n// >>> sort_array([])\n// []\n// >>> sort_array([5])\n// [5]\n// >>> sort_array([2, 4, 3, 0, 1, 5])\n// [0, 1, 2, 3, 4, 5]\n// >>> sort_array([2, 4, 3, 0, 1, 5, 6])\n// [6, 5, 4, 3, 2, 1, 0]\nfunction sort_array(array){\n\n    if(array.length === 0){\n        return array;\n    }\n    let sum = array[0] + array[array.length - 1];\n    if(sum % 2 === 0){\n        array.sort((a, b) => b - a);\n    }else{\n        array.sort((a, b) => a - b);\n    }\n    return array;\n}", "prompt": "//Given an array of non-negative integers, return a cojs of the given array after sorting,\n// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given array.\n// Examples:\n// >>> sort_array([])\n// []\n// >>> sort_array([5])\n// [5]\n// >>> sort_array([2, 4, 3, 0, 1, 5])\n// [0, 1, 2, 3, 4, 5]\n// >>> sort_array([2, 4, 3, 0, 1, 5, 6])\n// [6, 5, 4, 3, 2, 1, 0]\nfunction sort_array(array){\n", "wholecode": "//Given an array of non-negative integers, return a cojs of the given array after sorting,\n// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given array.\n// Examples:\n// >>> sort_array([])\n// []\n// >>> sort_array([5])\n// [5]\n// >>> sort_array([2, 4, 3, 0, 1, 5])\n// [0, 1, 2, 3, 4, 5]\n// >>> sort_array([2, 4, 3, 0, 1, 5, 6])\n// [6, 5, 4, 3, 2, 1, 0]\nfunction sort_array(array){\n    if(array.length === 0){\n        return array;\n    }\n    let sum = array[0] + array[array.length - 1];\n    if(sum % 2 === 0){\n        array.sort((a, b) => b - a);\n    }else{\n        array.sort((a, b) => a - b);\n    }\n    return array;\n}"}
{"task_id": "HumanEval_106_f", "generation": "//Implement the function f that takes n as a parameter,\n// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// [1, 2, 6, 24, 15]\nfunction f(n){\n\n    let arr = [];\n    for(let i = 1; i <= n; i++){\n        if(i % 2 === 0){\n            arr.push(i);\n        }else{\n            let sum = 0;\n            for(let j = 1; j <= i; j++){\n                sum += j;\n            }\n            arr.push(sum);\n        }\n    }\n    return arr;\n}", "prompt": "//Implement the function f that takes n as a parameter,\n// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// [1, 2, 6, 24, 15]\nfunction f(n){\n", "wholecode": "//Implement the function f that takes n as a parameter,\n// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// [1, 2, 6, 24, 15]\nfunction f(n){\n    let arr = [];\n    for(let i = 1; i <= n; i++){\n        if(i % 2 === 0){\n            arr.push(i);\n        }else{\n            let sum = 0;\n            for(let j = 1; j <= i; j++){\n                sum += j;\n            }\n            arr.push(sum);\n        }\n    }\n    return arr;\n}"}
{"task_id": "HumanEval_77_iscube", "generation": "//Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunction iscube(a){\n\n    if(a < 0){\n        a = a * -1\n    }\n    if(a === 0){\n        return true\n    }\n    if(a === 1){\n        return true\n    }\n    if(a === 8){\n        return true\n    }\n    if(a === 27){\n        return true\n    }\n    if(a === 64){\n        return true\n    }\n    if(a === 125){\n        return true\n    }\n    if(a === 216){\n        return true\n    }\n    if(a === 343){\n        return true\n    }\n    if(a === 512){\n        return true\n    }\n    if(a === 729){\n        return true\n    }\n    if(a === 1000){\n        return true\n    }\n    if(a === 1331){\n        return true\n    }\n    if(a === 2197){\n        return true\n    }\n    if(a === 3430){\n        return true\n    }\n    if(a === 50625){\n        return true\n    }\n    if(a === 70728){\n        return true\n    }\n    if(a === 100000){\n        return true\n    }\n    if(a === 1382400){\n        return true\n    }\n    if(a === 2197000){\n        return true\n    }\n    if(a === 3125000){\n        return true\n    }\n    if(a === 4782969){\n        return true\n    }\n    if(a === 6561000){\n        return true\n    }\n    if(a === 88473600){\n        return true\n    }\n    if(a === 116640000){\n        return true\n    }\n    if(a === 148877600){\n        return true\n    }\n    if(a === 188956800){\n        return true\n    }\n    if(a === 236196000){\n        return true\n    }\n    if(a === 297913600){\n        return true\n    }\n    if(a === ", "prompt": "//Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunction iscube(a){\n", "wholecode": "//Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunction iscube(a){\n    if(a < 0){\n        a = a * -1\n    }\n    if(a === 0){\n        return true\n    }\n    if(a === 1){\n        return true\n    }\n    if(a === 8){\n        return true\n    }\n    if(a === 27){\n        return true\n    }\n    if(a === 64){\n        return true\n    }\n    if(a === 125){\n        return true\n    }\n    if(a === 216){\n        return true\n    }\n    if(a === 343){\n        return true\n    }\n    if(a === 512){\n        return true\n    }\n    if(a === 729){\n        return true\n    }\n    if(a === 1000){\n        return true\n    }\n    if(a === 1331){\n        return true\n    }\n    if(a === 2197){\n        return true\n    }\n    if(a === 3430){\n        return true\n    }\n    if(a === 50625){\n        return true\n    }\n    if(a === 70728){\n        return true\n    }\n    if(a === 100000){\n        return true\n    }\n    if(a === 1382400){\n        return true\n    }\n    if(a === 2197000){\n        return true\n    }\n    if(a === 3125000){\n        return true\n    }\n    if(a === 4782969){\n        return true\n    }\n    if(a === 6561000){\n        return true\n    }\n    if(a === 88473600){\n        return true\n    }\n    if(a === 116640000){\n        return true\n    }\n    if(a === 148877600){\n        return true\n    }\n    if(a === 188956800){\n        return true\n    }\n    if(a === 236196000){\n        return true\n    }\n    if(a === 297913600){\n        return true\n    }\n    if(a === "}
{"task_id": "HumanEval_93_encode", "generation": "//Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunction encode(message){\n\n    let result = \"\";\n    for(let i = 0; i < message.length; i++){\n        let char = message[i];\n        if(char === char.toUpperCase()){\n            result += char.toLowerCase();\n        }else{\n            result += char.toUpperCase();\n        }\n    }\n    return result;\n}", "prompt": "//Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunction encode(message){\n", "wholecode": "//Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunction encode(message){\n    let result = \"\";\n    for(let i = 0; i < message.length; i++){\n        let char = message[i];\n        if(char === char.toUpperCase()){\n            result += char.toLowerCase();\n        }else{\n            result += char.toUpperCase();\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_91_is_bored", "generation": "//You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunction is_bored(S){\n\n  let count = 0;\n  let arr = S.split(/[.?!]/);\n  for(let i = 0; i < arr.length; i++){\n    if(arr[i].trim() === 'I'){\n      count++;\n    }\n  }\n  return count;\n}", "prompt": "//You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunction is_bored(S){\n", "wholecode": "//You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunction is_bored(S){\n  let count = 0;\n  let arr = S.split(/[.?!]/);\n  for(let i = 0; i < arr.length; i++){\n    if(arr[i].trim() === 'I'){\n      count++;\n    }\n  }\n  return count;\n}"}
{"task_id": "HumanEval_43_pairs_sum_to_zero", "generation": "//pairs_sum_to_zero takes an array of integers as an input.\n// it returns true if there are two distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> pairs_sum_to_zero([1, 3, -2, 1])\n// false\n// >>> pairs_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n// true\n// >>> pairs_sum_to_zero([1])\n// false\nfunction pairs_sum_to_zero(l){\n\n    let set = new Set(l);\n    for(let i = 0; i < l.length; i++){\n        if(set.has(-l[i])){\n            return true;\n        }\n    }\n    return false;\n}", "prompt": "//pairs_sum_to_zero takes an array of integers as an input.\n// it returns true if there are two distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> pairs_sum_to_zero([1, 3, -2, 1])\n// false\n// >>> pairs_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n// true\n// >>> pairs_sum_to_zero([1])\n// false\nfunction pairs_sum_to_zero(l){\n", "wholecode": "//pairs_sum_to_zero takes an array of integers as an input.\n// it returns true if there are two distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> pairs_sum_to_zero([1, 3, -2, 1])\n// false\n// >>> pairs_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n// true\n// >>> pairs_sum_to_zero([1])\n// false\nfunction pairs_sum_to_zero(l){\n    let set = new Set(l);\n    for(let i = 0; i < l.length; i++){\n        if(set.has(-l[i])){\n            return true;\n        }\n    }\n    return false;\n}"}
{"task_id": "HumanEval_71_triangle_area", "generation": "//Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunction triangle_area(a, b, c){\n\n    if (a + b > c && a + c > b && b + c > a) {\n        let s = (a + b + c) / 2;\n        let area = Math.sqrt(s * (s - a) * (s - b) * (s - c));\n        return area.toFixed(2);\n    } else {\n        return -1;\n    }\n}", "prompt": "//Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunction triangle_area(a, b, c){\n", "wholecode": "//Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunction triangle_area(a, b, c){\n    if (a + b > c && a + c > b && b + c > a) {\n        let s = (a + b + c) / 2;\n        let area = Math.sqrt(s * (s - a) * (s - b) * (s - c));\n        return area.toFixed(2);\n    } else {\n        return -1;\n    }\n}"}
{"task_id": "HumanEval_148_bf", "generation": "//There are eight planets in our solar system: the closerst to the Sun \n// is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n// Uranus, Neptune.\n// Write a function that takes two planet names as strings planet1 and planet2. \n// The function should return an array containing all planets whose orbits are \n// located between the orbit of planet1 and the orbit of planet2, sorted by \n// the proximity to the sun. \n// The function should return an empty array if planet1 or planet2\n// are not correct planet names. \n// Examples\n// >>> bf(\"Jupiter\", \"Neptune\")\n// [\"Saturn\", \"Uranus\"]\n// >>> bf(\"Earth\", \"Mercury\")\n// \"Venus\"\n// >>> bf(\"Mercury\", \"Uranus\")\n// [\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"]\nfunction bf(planet1, planet2){\n\n    let planets = [\"Mercury\", \"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\", \"Neptune\"];\n    let planetsBetween = [];\n    for(let i = 0; i < planets.length; i++){\n        if(planets[i] === planet1 || planets[i] === planet2){\n            continue;\n        }else if(planets[i] > planet1 && planets[i] < planet2){\n            planetsBetween.push(planets[i]);\n        }\n    }\n    return planetsBetween;\n}", "prompt": "//There are eight planets in our solar system: the closerst to the Sun \n// is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n// Uranus, Neptune.\n// Write a function that takes two planet names as strings planet1 and planet2. \n// The function should return an array containing all planets whose orbits are \n// located between the orbit of planet1 and the orbit of planet2, sorted by \n// the proximity to the sun. \n// The function should return an empty array if planet1 or planet2\n// are not correct planet names. \n// Examples\n// >>> bf(\"Jupiter\", \"Neptune\")\n// [\"Saturn\", \"Uranus\"]\n// >>> bf(\"Earth\", \"Mercury\")\n// \"Venus\"\n// >>> bf(\"Mercury\", \"Uranus\")\n// [\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"]\nfunction bf(planet1, planet2){\n", "wholecode": "//There are eight planets in our solar system: the closerst to the Sun \n// is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n// Uranus, Neptune.\n// Write a function that takes two planet names as strings planet1 and planet2. \n// The function should return an array containing all planets whose orbits are \n// located between the orbit of planet1 and the orbit of planet2, sorted by \n// the proximity to the sun. \n// The function should return an empty array if planet1 or planet2\n// are not correct planet names. \n// Examples\n// >>> bf(\"Jupiter\", \"Neptune\")\n// [\"Saturn\", \"Uranus\"]\n// >>> bf(\"Earth\", \"Mercury\")\n// \"Venus\"\n// >>> bf(\"Mercury\", \"Uranus\")\n// [\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"]\nfunction bf(planet1, planet2){\n    let planets = [\"Mercury\", \"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\", \"Neptune\"];\n    let planetsBetween = [];\n    for(let i = 0; i < planets.length; i++){\n        if(planets[i] === planet1 || planets[i] === planet2){\n            continue;\n        }else if(planets[i] > planet1 && planets[i] < planet2){\n            planetsBetween.push(planets[i]);\n        }\n    }\n    return planetsBetween;\n}"}
{"task_id": "HumanEval_131_digits", "generation": "//Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunction digits(n){\n\n    let str = n.toString();\n    let result = 1;\n    for(let i = 0; i < str.length; i++){\n        if(str[i] % 2 !== 0){\n            result *= str[i];\n        }\n    }\n    return result;\n}", "prompt": "//Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunction digits(n){\n", "wholecode": "//Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunction digits(n){\n    let str = n.toString();\n    let result = 1;\n    for(let i = 0; i < str.length; i++){\n        if(str[i] % 2 !== 0){\n            result *= str[i];\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_101_words_string", "generation": "//You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return an array of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n// >>> words_string(\"One, two, three, four, five, six\")\n// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunction words_string(s){\n\n    return s.split(/, | /)\n}", "prompt": "//You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return an array of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n// >>> words_string(\"One, two, three, four, five, six\")\n// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunction words_string(s){\n", "wholecode": "//You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return an array of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n// >>> words_string(\"One, two, three, four, five, six\")\n// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunction words_string(s){\n    return s.split(/, | /)\n}"}
{"task_id": "HumanEval_18_how_many_times", "generation": "//Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunction how_many_times(string, substring){\n\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        if(string[i] === substring[0]){\n            let j = 0;\n            while(j < substring.length && i + j < string.length && string[i + j] === substring[j]){\n                j++;\n            }\n            if(j === substring.length){\n                count++;\n            }\n        }\n    }\n    return count;\n}", "prompt": "//Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunction how_many_times(string, substring){\n", "wholecode": "//Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunction how_many_times(string, substring){\n    let count = 0;\n    for(let i = 0; i < string.length; i++){\n        if(string[i] === substring[0]){\n            let j = 0;\n            while(j < substring.length && i + j < string.length && string[i + j] === substring[j]){\n                j++;\n            }\n            if(j === substring.length){\n                count++;\n            }\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_137_compare_one", "generation": "//Create a function that takes integers, floats, or strings representing\n// real numbers, and returns the larger variable in its given variable type.\n// Return undefined if the values are equal.\n// Note: If a real number is represented as a string, the floating point might be . or ,\n// >>> compare_one(1, 2.5)\n// 2.5\n// >>> compare_one(1, \"2,3\")\n// \"2,3\"\n// >>> compare_one(\"5,1\", \"6\")\n// \"6\"\n// >>> compare_one(\"1\", 1)\n// undefined\nfunction compare_one(a, b){\n\n    if (typeof a === \"string\" && typeof b === \"string\") {\n        a = a.replace(/,/g, \".\");\n        b = b.replace(/,/g, \".\");\n        if (a > b) {\n            return a;\n        } else if (a < b) {\n            return b;\n        } else {\n            return undefined;\n        }\n    } else if (typeof a === \"string\") {\n        a = a.replace(/,/g, \".\");\n        if (a > b) {\n            return a;\n        } else {\n            return b;\n        }\n    } else if (typeof b === \"string\") {\n        b = b.replace(/,/g, \".\");\n        if (a > b) {\n            return a;\n        } else {\n            return b;\n        }\n    } else {\n        if (a > b) {\n            return a;\n        } else if (a < b) {\n            return b;\n        } else {\n            return undefined;\n        }\n    }\n}\n", "prompt": "//Create a function that takes integers, floats, or strings representing\n// real numbers, and returns the larger variable in its given variable type.\n// Return undefined if the values are equal.\n// Note: If a real number is represented as a string, the floating point might be . or ,\n// >>> compare_one(1, 2.5)\n// 2.5\n// >>> compare_one(1, \"2,3\")\n// \"2,3\"\n// >>> compare_one(\"5,1\", \"6\")\n// \"6\"\n// >>> compare_one(\"1\", 1)\n// undefined\nfunction compare_one(a, b){\n", "wholecode": "//Create a function that takes integers, floats, or strings representing\n// real numbers, and returns the larger variable in its given variable type.\n// Return undefined if the values are equal.\n// Note: If a real number is represented as a string, the floating point might be . or ,\n// >>> compare_one(1, 2.5)\n// 2.5\n// >>> compare_one(1, \"2,3\")\n// \"2,3\"\n// >>> compare_one(\"5,1\", \"6\")\n// \"6\"\n// >>> compare_one(\"1\", 1)\n// undefined\nfunction compare_one(a, b){\n    if (typeof a === \"string\" && typeof b === \"string\") {\n        a = a.replace(/,/g, \".\");\n        b = b.replace(/,/g, \".\");\n        if (a > b) {\n            return a;\n        } else if (a < b) {\n            return b;\n        } else {\n            return undefined;\n        }\n    } else if (typeof a === \"string\") {\n        a = a.replace(/,/g, \".\");\n        if (a > b) {\n            return a;\n        } else {\n            return b;\n        }\n    } else if (typeof b === \"string\") {\n        b = b.replace(/,/g, \".\");\n        if (a > b) {\n            return a;\n        } else {\n            return b;\n        }\n    } else {\n        if (a > b) {\n            return a;\n        } else if (a < b) {\n            return b;\n        } else {\n            return undefined;\n        }\n    }\n}\n\nconsole.log(compare_one(1, 2.5));\nconsole.log(compare_one(1, \"2,3\"));\nconsole.log(compare_one(\"5,1\", \"6\"));\nconsole.log(compare_one(\"1\", 1));"}
{"task_id": "HumanEval_51_remove_vowels", "generation": "//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text){\n\n    let vowels = ['a', 'e', 'i', 'o', 'u'];\n    let result = '';\n    for (let i = 0; i < text.length; i++) {\n        if (!vowels.includes(text[i].toLowerCase())) {\n            result += text[i];\n        }\n    }\n    return result;\n}", "prompt": "//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text){\n", "wholecode": "//remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunction remove_vowels(text){\n    let vowels = ['a', 'e', 'i', 'o', 'u'];\n    let result = '';\n    for (let i = 0; i < text.length; i++) {\n        if (!vowels.includes(text[i].toLowerCase())) {\n            result += text[i];\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_70_strange_sort_list", "generation": "//Given array of integers, return array in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([1, 2, 3, 4])\n// [1, 4, 2, 3]\n// >>> strange_sort_list([5, 5, 5, 5])\n// [5, 5, 5, 5]\n// >>> strange_sort_list([])\n// []\nfunction strange_sort_list(lst){\n\n    let result = [];\n    let min = Math.min(...lst);\n    let max = Math.max(...lst);\n    while (lst.length > 0) {\n        result.push(min);\n        result.push(max);\n        lst = lst.filter(x => x !== min && x !== max);\n        min = Math.min(...lst);\n        max = Math.max(...lst);\n    }\n    return result;\n}", "prompt": "//Given array of integers, return array in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([1, 2, 3, 4])\n// [1, 4, 2, 3]\n// >>> strange_sort_list([5, 5, 5, 5])\n// [5, 5, 5, 5]\n// >>> strange_sort_list([])\n// []\nfunction strange_sort_list(lst){\n", "wholecode": "//Given array of integers, return array in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([1, 2, 3, 4])\n// [1, 4, 2, 3]\n// >>> strange_sort_list([5, 5, 5, 5])\n// [5, 5, 5, 5]\n// >>> strange_sort_list([])\n// []\nfunction strange_sort_list(lst){\n    let result = [];\n    let min = Math.min(...lst);\n    let max = Math.max(...lst);\n    while (lst.length > 0) {\n        result.push(min);\n        result.push(max);\n        lst = lst.filter(x => x !== min && x !== max);\n        min = Math.min(...lst);\n        max = Math.max(...lst);\n    }\n    return result;\n}"}
{"task_id": "HumanEval_20_find_closest_elements", "generation": "//From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n// [2.0, 2.2]\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n// [2.0, 2.0]\nfunction find_closest_elements(numbers){\n\n    let closest = [];\n    let min = Math.abs(numbers[0] - numbers[1]);\n    for(let i = 0; i < numbers.length; i++){\n        for(let j = i + 1; j < numbers.length; j++){\n            let diff = Math.abs(numbers[i] - numbers[j]);\n            if(diff < min){\n                min = diff;\n                closest = [numbers[i], numbers[j]];\n            }\n        }\n    }\n    return closest;\n}\n", "prompt": "//From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n// [2.0, 2.2]\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n// [2.0, 2.0]\nfunction find_closest_elements(numbers){\n", "wholecode": "//From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n// [2.0, 2.2]\n// >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n// [2.0, 2.0]\nfunction find_closest_elements(numbers){\n    let closest = [];\n    let min = Math.abs(numbers[0] - numbers[1]);\n    for(let i = 0; i < numbers.length; i++){\n        for(let j = i + 1; j < numbers.length; j++){\n            let diff = Math.abs(numbers[i] - numbers[j]);\n            if(diff < min){\n                min = diff;\n                closest = [numbers[i], numbers[j]];\n            }\n        }\n    }\n    return closest;\n}\n\nconsole.log(find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2]));\nconsole.log(find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0]));"}
{"task_id": "HumanEval_76_is_simple_power", "generation": "//Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunction is_simple_power(x, n){\n\n    if (x === 1) return true;\n    if (n === 1) return false;\n    if (x % n !== 0) return false;\n    return is_simple_power(x / n, n);\n}", "prompt": "//Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunction is_simple_power(x, n){\n", "wholecode": "//Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunction is_simple_power(x, n){\n    if (x === 1) return true;\n    if (n === 1) return false;\n    if (x % n !== 0) return false;\n    return is_simple_power(x / n, n);\n}"}
{"task_id": "HumanEval_39_prime_fib", "generation": "//prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunction prime_fib(n){\n\n    let fib = [0,1];\n    let i = 2;\n    while(i < n){\n        fib[i] = fib[i-1] + fib[i-2];\n        i++;\n    }\n    let prime = 0;\n    for(let j = 0; j < fib.length; j++){\n        if(isPrime(fib[j])){\n            prime++;\n        }\n        if(prime === n){\n            return fib[j];\n        }\n    }\n}\n", "prompt": "//prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunction prime_fib(n){\n", "wholecode": "//prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunction prime_fib(n){\n    let fib = [0,1];\n    let i = 2;\n    while(i < n){\n        fib[i] = fib[i-1] + fib[i-2];\n        i++;\n    }\n    let prime = 0;\n    for(let j = 0; j < fib.length; j++){\n        if(isPrime(fib[j])){\n            prime++;\n        }\n        if(prime === n){\n            return fib[j];\n        }\n    }\n}\n\nfunction isPrime(n){\n    if(n < 2){\n        return false;\n    }\n    for(let i = 2; i < n; i++){\n        if(n % i === 0){\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_145_order_by_points", "generation": "//Write a function which sorts the given array of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original array.\n// For example:\n// >>> order_by_points([1, 11, -1, -11, -12])\n// [-1, -11, 1, -12, 11]\n// >>> order_by_points([])\n// []\nfunction order_by_points(nums){\n\n    if(nums.length === 0){\n        return [];\n    }\n    let sorted = nums.sort((a,b) => {\n        let sumA = sumDigits(a);\n        let sumB = sumDigits(b);\n        if(sumA === sumB){\n            return nums.indexOf(a) - nums.indexOf(b);\n        }\n        return sumA - sumB;\n    });\n    return sorted;\n}\n", "prompt": "//Write a function which sorts the given array of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original array.\n// For example:\n// >>> order_by_points([1, 11, -1, -11, -12])\n// [-1, -11, 1, -12, 11]\n// >>> order_by_points([])\n// []\nfunction order_by_points(nums){\n", "wholecode": "//Write a function which sorts the given array of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original array.\n// For example:\n// >>> order_by_points([1, 11, -1, -11, -12])\n// [-1, -11, 1, -12, 11]\n// >>> order_by_points([])\n// []\nfunction order_by_points(nums){\n    if(nums.length === 0){\n        return [];\n    }\n    let sorted = nums.sort((a,b) => {\n        let sumA = sumDigits(a);\n        let sumB = sumDigits(b);\n        if(sumA === sumB){\n            return nums.indexOf(a) - nums.indexOf(b);\n        }\n        return sumA - sumB;\n    });\n    return sorted;\n}\n\nfunction sumDigits(num){\n    let sum = 0;\n    while(num > 0){\n        sum += num % 10;\n        num = Math.floor(num / 10);\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_0_has_close_elements", "generation": "//Check if in given array of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n// false\n// >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n// true\nfunction has_close_elements(numbers, threshold){\n\n    for (let i = 0; i < numbers.length; i++) {\n        for (let j = i + 1; j < numbers.length; j++) {\n            if (Math.abs(numbers[i] - numbers[j]) < threshold) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n", "prompt": "//Check if in given array of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n// false\n// >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n// true\nfunction has_close_elements(numbers, threshold){\n", "wholecode": "//Check if in given array of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n// false\n// >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n// true\nfunction has_close_elements(numbers, threshold){\n    for (let i = 0; i < numbers.length; i++) {\n        for (let j = i + 1; j < numbers.length; j++) {\n            if (Math.abs(numbers[i] - numbers[j]) < threshold) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\nconsole.log(has_close_elements([1.0, 2.0, 3.0], 0.5));\nconsole.log(has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3));"}
{"task_id": "HumanEval_10_make_palindrome", "generation": "//Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction make_palindrome(string){\n\n  var i = 0;\n  var j = string.length - 1;\n  var palindrome = \"\";\n  var prefix = \"\";\n  var suffix = \"\";\n  while(i < j){\n    if(string[i] === string[j]){\n      palindrome = string[i] + palindrome + string[j];\n      i++;\n      j--;\n    } else {\n      prefix = string.slice(0, i);\n      suffix = string.slice(i, j + 1);\n      palindrome = prefix + suffix + prefix.split(\"\").reverse().join(\"\");\n      break;\n    }\n  }\n  return palindrome;\n}", "prompt": "//Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction make_palindrome(string){\n", "wholecode": "//Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunction make_palindrome(string){\n  var i = 0;\n  var j = string.length - 1;\n  var palindrome = \"\";\n  var prefix = \"\";\n  var suffix = \"\";\n  while(i < j){\n    if(string[i] === string[j]){\n      palindrome = string[i] + palindrome + string[j];\n      i++;\n      j--;\n    } else {\n      prefix = string.slice(0, i);\n      suffix = string.slice(i, j + 1);\n      palindrome = prefix + suffix + prefix.split(\"\").reverse().join(\"\");\n      break;\n    }\n  }\n  return palindrome;\n}"}
{"task_id": "HumanEval_11_string_xor", "generation": "//Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunction string_xor(a, b){\n\n    let result = \"\";\n    for(let i = 0; i < a.length; i++){\n      if(a[i] === b[i]){\n        result += \"0\";\n      }else{\n        result += \"1\";\n      }\n    }\n    return result;\n  }", "prompt": "//Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunction string_xor(a, b){\n", "wholecode": "//Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunction string_xor(a, b){\n    let result = \"\";\n    for(let i = 0; i < a.length; i++){\n      if(a[i] === b[i]){\n        result += \"0\";\n      }else{\n        result += \"1\";\n      }\n    }\n    return result;\n  }"}
{"task_id": "HumanEval_139_special_factorial", "generation": "//The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunction special_factorial(n){\n\n    let result = 1;\n    for(let i = 1; i <= n; i++){\n        result *= i;\n    }\n    return result;\n}", "prompt": "//The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunction special_factorial(n){\n", "wholecode": "//The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunction special_factorial(n){\n    let result = 1;\n    for(let i = 1; i <= n; i++){\n        result *= i;\n    }\n    return result;\n}"}
{"task_id": "HumanEval_122_add_elements", "generation": "//Given a non-empty array of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunction add_elements(arr, k){\n\n    let sum = 0;\n    for(let i = 0; i < k; i++){\n        if(arr[i] < 100){\n            sum += arr[i];\n        }\n    }\n    return sum;\n}", "prompt": "//Given a non-empty array of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunction add_elements(arr, k){\n", "wholecode": "//Given a non-empty array of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunction add_elements(arr, k){\n    let sum = 0;\n    for(let i = 0; i < k; i++){\n        if(arr[i] < 100){\n            sum += arr[i];\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_46_fib4", "generation": "//The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunction fib4(n){\n\n    if(n === 0 || n === 1){\n        return 0;\n    }else if(n === 2){\n        return 2;\n    }else if(n === 3){\n        return 0;\n    }else{\n        return fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4);\n    }\n}", "prompt": "//The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunction fib4(n){\n", "wholecode": "//The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunction fib4(n){\n    if(n === 0 || n === 1){\n        return 0;\n    }else if(n === 2){\n        return 2;\n    }else if(n === 3){\n        return 0;\n    }else{\n        return fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4);\n    }\n}"}
{"task_id": "HumanEval_104_unique_digits", "generation": "//Given an array of positive integers x. return a sorted array of all \n// elements that hasn't any even digit.\n// Note: Returned array should be sorted in increasing order.\n// For example:\n// >>> unique_digits([15, 33, 1422, 1])\n// [1, 15, 33]\n// >>> unique_digits([152, 323, 1422, 10])\n// []\nfunction unique_digits(x){\n\n    let result = [];\n    for(let i = 0; i < x.length; i++){\n        let num = x[i];\n        let isEven = false;\n        while(num > 0){\n            let digit = num % 10;\n            if(digit % 2 === 0){\n                isEven = true;\n                break;\n            }\n            num = Math.floor(num / 10);\n        }\n        if(!isEven){\n            result.push(x[i]);\n        }\n    }\n    return result;\n}\n", "prompt": "//Given an array of positive integers x. return a sorted array of all \n// elements that hasn't any even digit.\n// Note: Returned array should be sorted in increasing order.\n// For example:\n// >>> unique_digits([15, 33, 1422, 1])\n// [1, 15, 33]\n// >>> unique_digits([152, 323, 1422, 10])\n// []\nfunction unique_digits(x){\n", "wholecode": "//Given an array of positive integers x. return a sorted array of all \n// elements that hasn't any even digit.\n// Note: Returned array should be sorted in increasing order.\n// For example:\n// >>> unique_digits([15, 33, 1422, 1])\n// [1, 15, 33]\n// >>> unique_digits([152, 323, 1422, 10])\n// []\nfunction unique_digits(x){\n    let result = [];\n    for(let i = 0; i < x.length; i++){\n        let num = x[i];\n        let isEven = false;\n        while(num > 0){\n            let digit = num % 10;\n            if(digit % 2 === 0){\n                isEven = true;\n                break;\n            }\n            num = Math.floor(num / 10);\n        }\n        if(!isEven){\n            result.push(x[i]);\n        }\n    }\n    return result;\n}\n\nconsole.log(unique_digits([15, 33, 1422, 1]));\nconsole.log(unique_digits([152, 323, 1422, 10]));"}
{"task_id": "HumanEval_117_select_words", "generation": "//Given a string s and a natural number n, you have been tasked to implement \n// a function that returns an array of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty array.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// [\"little\"]\n// >>> select_words(\"Mary had a little lamb\", 3)\n// [\"Mary\", \"lamb\"]\n// >>> select_words(\"simple white space\", 2)\n// []\n// >>> select_words(\"Hello world\", 4)\n// [\"world\"]\n// >>> select_words(\"Uncle sam\", 3)\n// [\"Uncle\"]\nfunction select_words(s, n){\n\n    let arr = s.split(' ')\n    let result = []\n    for(let i = 0; i < arr.length; i++){\n        let count = 0\n        for(let j = 0; j < arr[i].length; j++){\n            if(arr[i][j] === 'a' || arr[i][j] === 'e' || arr[i][j] === 'i' || arr[i][j] === 'o' || arr[i][j] === 'u'){\n                count++\n            }\n        }\n        if(count === n){\n            result.push(arr[i])\n        }\n    }\n    return result\n}", "prompt": "//Given a string s and a natural number n, you have been tasked to implement \n// a function that returns an array of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty array.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// [\"little\"]\n// >>> select_words(\"Mary had a little lamb\", 3)\n// [\"Mary\", \"lamb\"]\n// >>> select_words(\"simple white space\", 2)\n// []\n// >>> select_words(\"Hello world\", 4)\n// [\"world\"]\n// >>> select_words(\"Uncle sam\", 3)\n// [\"Uncle\"]\nfunction select_words(s, n){\n", "wholecode": "//Given a string s and a natural number n, you have been tasked to implement \n// a function that returns an array of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty array.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// [\"little\"]\n// >>> select_words(\"Mary had a little lamb\", 3)\n// [\"Mary\", \"lamb\"]\n// >>> select_words(\"simple white space\", 2)\n// []\n// >>> select_words(\"Hello world\", 4)\n// [\"world\"]\n// >>> select_words(\"Uncle sam\", 3)\n// [\"Uncle\"]\nfunction select_words(s, n){\n    let arr = s.split(' ')\n    let result = []\n    for(let i = 0; i < arr.length; i++){\n        let count = 0\n        for(let j = 0; j < arr[i].length; j++){\n            if(arr[i][j] === 'a' || arr[i][j] === 'e' || arr[i][j] === 'i' || arr[i][j] === 'o' || arr[i][j] === 'u'){\n                count++\n            }\n        }\n        if(count === n){\n            result.push(arr[i])\n        }\n    }\n    return result\n}"}
{"task_id": "HumanEval_72_will_it_fly", "generation": "//Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([1, 2], 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([3, 2, 3], 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([3, 2, 3], 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([3], 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunction will_it_fly(q, w){\n\n    let sum = 0;\n    for(let i = 0; i < q.length; i++){\n        sum += q[i];\n    }\n    return sum <= w && q.join('') === q.reverse().join('');\n}", "prompt": "//Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([1, 2], 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([3, 2, 3], 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([3, 2, 3], 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([3], 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunction will_it_fly(q, w){\n", "wholecode": "//Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([1, 2], 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([3, 2, 3], 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([3, 2, 3], 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([3], 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunction will_it_fly(q, w){\n    let sum = 0;\n    for(let i = 0; i < q.length; i++){\n        sum += q[i];\n    }\n    return sum <= w && q.join('') === q.reverse().join('');\n}"}
{"task_id": "HumanEval_55_fib", "generation": "//Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunction fib(n){\n\n    if(n === 1 || n === 2){\n        return 1;\n    }\n    return fib(n-1) + fib(n-2);\n}", "prompt": "//Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunction fib(n){\n", "wholecode": "//Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunction fib(n){\n    if(n === 1 || n === 2){\n        return 1;\n    }\n    return fib(n-1) + fib(n-2);\n}"}
{"task_id": "HumanEval_153_Strongest_Extension", "generation": "//You will be given the name of a class (a string) and an array of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the array.\n// For example, if you are given \"Slices\" as the class and an array of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n// \"my_class.AA\"\nfunction Strongest_Extension(class_name, extensions){\n\n    let max = 0;\n    let max_index = 0;\n    let max_str = \"\";\n    for(let i = 0; i < extensions.length; i++){\n        let cap = 0;\n        let sm = 0;\n        for(let j = 0; j < extensions[i].length; j++){\n            if(extensions[i][j] === extensions[i][j].toUpperCase()){\n                cap++;\n            }else{\n                sm++;\n            }\n        }\n        let strength = cap - sm;\n        if(strength > max){\n            max = strength;\n            max_index = i;\n            max_str = extensions[i];\n        }\n    }\n    return class_name + \".\" + max_str;\n}", "prompt": "//You will be given the name of a class (a string) and an array of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the array.\n// For example, if you are given \"Slices\" as the class and an array of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n// \"my_class.AA\"\nfunction Strongest_Extension(class_name, extensions){\n", "wholecode": "//You will be given the name of a class (a string) and an array of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the array.\n// For example, if you are given \"Slices\" as the class and an array of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n// \"my_class.AA\"\nfunction Strongest_Extension(class_name, extensions){\n    let max = 0;\n    let max_index = 0;\n    let max_str = \"\";\n    for(let i = 0; i < extensions.length; i++){\n        let cap = 0;\n        let sm = 0;\n        for(let j = 0; j < extensions[i].length; j++){\n            if(extensions[i][j] === extensions[i][j].toUpperCase()){\n                cap++;\n            }else{\n                sm++;\n            }\n        }\n        let strength = cap - sm;\n        if(strength > max){\n            max = strength;\n            max_index = i;\n            max_str = extensions[i];\n        }\n    }\n    return class_name + \".\" + max_str;\n}"}
{"task_id": "HumanEval_119_match_parens", "generation": "//You are given an array of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([\"()(\", \")\"])\n// \"Yes\"\n// >>> match_parens([\")\", \")\"])\n// \"No\"\nfunction match_parens(lst){\n\n  let open = 0;\n  let close = 0;\n  for(let i = 0; i < lst.length; i++){\n    if(lst[i] === '('){\n      open++;\n    } else if(lst[i] === ')'){\n      close++;\n    }\n  }\n  if(open === close){\n    return 'Yes';\n  } else {\n    return 'No';\n  }\n}\n", "prompt": "//You are given an array of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([\"()(\", \")\"])\n// \"Yes\"\n// >>> match_parens([\")\", \")\"])\n// \"No\"\nfunction match_parens(lst){\n", "wholecode": "//You are given an array of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([\"()(\", \")\"])\n// \"Yes\"\n// >>> match_parens([\")\", \")\"])\n// \"No\"\nfunction match_parens(lst){\n  let open = 0;\n  let close = 0;\n  for(let i = 0; i < lst.length; i++){\n    if(lst[i] === '('){\n      open++;\n    } else if(lst[i] === ')'){\n      close++;\n    }\n  }\n  if(open === close){\n    return 'Yes';\n  } else {\n    return 'No';\n  }\n}\n\nconsole.log(match_parens([\"()(\", \")\"]));\nconsole.log(match_parens([\")\", \")\"]));"}
{"task_id": "HumanEval_90_next_smallest", "generation": "//You are given an array of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the array.\n// Return undefined if there is no such element.\n// >>> next_smallest([1, 2, 3, 4, 5])\n// 2\n// >>> next_smallest([5, 1, 4, 3, 2])\n// 2\n// >>> next_smallest([])\n// undefined\n// >>> next_smallest([1, 1])\n// undefined\nfunction next_smallest(lst){\n\n    if(lst.length < 2) return undefined\n    let sorted = lst.sort((a,b) => a-b)\n    for(let i = 0; i < sorted.length; i++){\n        if(sorted[i] !== sorted[i+1]) return sorted[i+1]\n    }\n}", "prompt": "//You are given an array of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the array.\n// Return undefined if there is no such element.\n// >>> next_smallest([1, 2, 3, 4, 5])\n// 2\n// >>> next_smallest([5, 1, 4, 3, 2])\n// 2\n// >>> next_smallest([])\n// undefined\n// >>> next_smallest([1, 1])\n// undefined\nfunction next_smallest(lst){\n", "wholecode": "//You are given an array of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the array.\n// Return undefined if there is no such element.\n// >>> next_smallest([1, 2, 3, 4, 5])\n// 2\n// >>> next_smallest([5, 1, 4, 3, 2])\n// 2\n// >>> next_smallest([])\n// undefined\n// >>> next_smallest([1, 1])\n// undefined\nfunction next_smallest(lst){\n    if(lst.length < 2) return undefined\n    let sorted = lst.sort((a,b) => a-b)\n    for(let i = 0; i < sorted.length; i++){\n        if(sorted[i] !== sorted[i+1]) return sorted[i+1]\n    }\n}"}
{"task_id": "HumanEval_92_any_int", "generation": "//Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunction any_int(x, y, z){\n\n    if (x % 1 === 0 && y % 1 === 0 && z % 1 === 0) {\n        if (x === y + z || y === x + z || z === x + y) {\n            return true;\n        } else {\n            return false;\n        }\n    } else {\n        return false;\n    }\n}\n", "prompt": "//Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunction any_int(x, y, z){\n", "wholecode": "//Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunction any_int(x, y, z){\n    if (x % 1 === 0 && y % 1 === 0 && z % 1 === 0) {\n        if (x === y + z || y === x + z || z === x + y) {\n            return true;\n        } else {\n            return false;\n        }\n    } else {\n        return false;\n    }\n}\n\nconsole.log(any_int(5, 2, 7));\nconsole.log(any_int(3, 2, 2));\nconsole.log(any_int(3, -2, 1));\nconsole.log(any_int(3.6, -2.2, 2));"}
{"task_id": "HumanEval_2_truncate_number", "generation": "//Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunction truncate_number(number){\n\n    return number - Math.floor(number);\n}", "prompt": "//Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunction truncate_number(number){\n", "wholecode": "//Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunction truncate_number(number){\n    return number - Math.floor(number);\n}"}
{"task_id": "HumanEval_42_incr_list", "generation": "//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l){\n\n    return l.map(x => x + 1);\n}\n", "prompt": "//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l){\n", "wholecode": "//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l){\n    return l.map(x => x + 1);\n}\n\n//Return array with elements incremented by 1.\n// >>> incr_list([1, 2, 3])\n// [2, 3, 4]\n// >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunction incr_list(l){\n    return l.map(x => x + 1);\n}"}
{"task_id": "HumanEval_150_x_or_y", "generation": "//A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunction x_or_y(n, x, y){\n\n    if (n % 2 == 0){\n        return y;\n    } else {\n        return x;\n    }\n}", "prompt": "//A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunction x_or_y(n, x, y){\n", "wholecode": "//A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunction x_or_y(n, x, y){\n    if (n % 2 == 0){\n        return y;\n    } else {\n        return x;\n    }\n}"}
{"task_id": "HumanEval_49_modp", "generation": "//Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunction modp(n, p){\n\n    return (Math.pow(n, 2) % p);\n}", "prompt": "//Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunction modp(n, p){\n", "wholecode": "//Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunction modp(n, p){\n    return (Math.pow(n, 2) % p);\n}"}
{"task_id": "HumanEval_155_even_odd_count", "generation": "//Given an integer. return an array that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// [1, 1]\n// >>> even_odd_count(123)\n// [1, 2]\nfunction even_odd_count(num){\n\n    let even = 0;\n    let odd = 0;\n    let numStr = num.toString();\n    for(let i = 0; i < numStr.length; i++){\n        if(numStr[i] % 2 === 0){\n            even++;\n        } else {\n            odd++;\n        }\n    }\n    return [even, odd];\n}", "prompt": "//Given an integer. return an array that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// [1, 1]\n// >>> even_odd_count(123)\n// [1, 2]\nfunction even_odd_count(num){\n", "wholecode": "//Given an integer. return an array that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// [1, 1]\n// >>> even_odd_count(123)\n// [1, 2]\nfunction even_odd_count(num){\n    let even = 0;\n    let odd = 0;\n    let numStr = num.toString();\n    for(let i = 0; i < numStr.length; i++){\n        if(numStr[i] % 2 === 0){\n            even++;\n        } else {\n            odd++;\n        }\n    }\n    return [even, odd];\n}"}
{"task_id": "HumanEval_80_is_happy", "generation": "//You are given a string s.\n// Your task is to check if the string is hapjs or not.\n// A string is hapjs if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunction is_happy(s){\n\n    if(s.length < 3){\n        return false\n    }\n    for(let i = 0; i < s.length - 2; i++){\n        if(s[i] === s[i + 1] || s[i] === s[i + 2] || s[i + 1] === s[i + 2]){\n            return false\n        }\n    }\n    return true\n}", "prompt": "//You are given a string s.\n// Your task is to check if the string is hapjs or not.\n// A string is hapjs if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunction is_happy(s){\n", "wholecode": "//You are given a string s.\n// Your task is to check if the string is hapjs or not.\n// A string is hapjs if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunction is_happy(s){\n    if(s.length < 3){\n        return false\n    }\n    for(let i = 0; i < s.length - 2; i++){\n        if(s[i] === s[i + 1] || s[i] === s[i + 2] || s[i + 1] === s[i + 2]){\n            return false\n        }\n    }\n    return true\n}"}
{"task_id": "HumanEval_59_largest_prime_factor", "generation": "//Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunction largest_prime_factor(n){\n\n    let largestPrime = 0;\n    for(let i = 2; i <= n; i++){\n        if(n % i === 0){\n            let isPrime = true;\n            for(let j = 2; j < i; j++){\n                if(i % j === 0){\n                    isPrime = false;\n                    break;\n                }\n            }\n            if(isPrime){\n                largestPrime = i;\n            }\n        }\n    }\n    return largestPrime;\n}", "prompt": "//Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunction largest_prime_factor(n){\n", "wholecode": "//Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunction largest_prime_factor(n){\n    let largestPrime = 0;\n    for(let i = 2; i <= n; i++){\n        if(n % i === 0){\n            let isPrime = true;\n            for(let j = 2; j < i; j++){\n                if(i % j === 0){\n                    isPrime = false;\n                    break;\n                }\n            }\n            if(isPrime){\n                largestPrime = i;\n            }\n        }\n    }\n    return largestPrime;\n}"}
{"task_id": "HumanEval_66_digitSum", "generation": "//Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunction digitSum(s){\n\n    let sum = 0;\n    for(let i = 0; i < s.length; i++){\n        if(s[i] === s[i].toUpperCase()){\n            sum += s[i].charCodeAt();\n        }\n    }\n    return sum;\n}", "prompt": "//Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunction digitSum(s){\n", "wholecode": "//Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunction digitSum(s){\n    let sum = 0;\n    for(let i = 0; i < s.length; i++){\n        if(s[i] === s[i].toUpperCase()){\n            sum += s[i].charCodeAt();\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_21_rescale_to_unit", "generation": "//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit(numbers){\n\n    let min = Math.min(...numbers);\n    let max = Math.max(...numbers);\n    let rescaled = [];\n    for(let i = 0; i < numbers.length; i++){\n        rescaled.push((numbers[i] - min) / (max - min));\n    }\n    return rescaled;\n}\n", "prompt": "//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit(numbers){\n", "wholecode": "//Given array of numbers (of at least two elements), apply a linear transform to that array,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunction rescale_to_unit(numbers){\n    let min = Math.min(...numbers);\n    let max = Math.max(...numbers);\n    let rescaled = [];\n    for(let i = 0; i < numbers.length; i++){\n        rescaled.push((numbers[i] - min) / (max - min));\n    }\n    return rescaled;\n}\n\n//Given an array of numbers, return the sum of the numbers\n// >>> sum([1, 2, 3, 4, 5])\n// 15\nfunction sum(numbers){\n    let sum = 0;\n    for(let i = 0; i < numbers.length; i++){\n        sum += numbers[i];\n    }\n    return sum;\n}\n\n//Given an array of numbers, return the product of the numbers\n// >>> product([1, 2, 3, 4, 5])\n// 120\nfunction product(numbers){\n    let product = 1;\n    for(let i = 0; i < numbers.length; i++){\n        product *= numbers[i];\n    }\n    return product;\n}\n\n//Given an array of numbers, return the average of the numbers\n// >>> average([1, 2, 3, 4, 5])\n// 3\nfunction average(numbers){\n    let sum = 0;\n    for(let i = 0; i < numbers.length; i++){\n        sum += numbers[i];\n    }\n    return sum / numbers.length;\n}\n\n//Given an array of numbers, return the median of the numbers\n// >>> median([1, 2, 3, 4, 5])\n// 3\nfunction median(numbers){\n    numbers.sort((a, b) => a - b);\n    let mid = Math.floor(numbers.length / 2);\n    if(numbers.length % 2 === 0){\n        return (numbers[mid] + numbers[mid - 1]) / 2;\n    } else {\n        return numbers[mid];\n    }\n}\n\n//Given an array of numbers, return the mode of the numbers\n// >>> mode([1, 2, 3, 4, 5])\n// 1\nfunction mode(numbers){\n    let count = {};\n    for(let i = 0; i < numbers.length; i++){\n        if(count[numbers[i]]){\n            count[numbers[i]]++;\n        } else {\n            count[numbers[i]] = 1;\n        }\n    }\n    let max = 0;\n"}
{"task_id": "HumanEval_121_solution", "generation": "//Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([5, 8, 7, 1])\n// 12\n// >>> solution([3, 3, 3, 3, 3])\n// 9\n// >>> solution([30, 13, 24, 321])\n// 0\nfunction solution(lst){\n\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        if(i % 2 === 0 && lst[i] % 2 !== 0){\n            sum += lst[i];\n        }\n    }\n    return sum;\n}", "prompt": "//Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([5, 8, 7, 1])\n// 12\n// >>> solution([3, 3, 3, 3, 3])\n// 9\n// >>> solution([30, 13, 24, 321])\n// 0\nfunction solution(lst){\n", "wholecode": "//Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([5, 8, 7, 1])\n// 12\n// >>> solution([3, 3, 3, 3, 3])\n// 9\n// >>> solution([30, 13, 24, 321])\n// 0\nfunction solution(lst){\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        if(i % 2 === 0 && lst[i] % 2 !== 0){\n            sum += lst[i];\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_68_pluck", "generation": "//\"Given an array representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in an array, [ smalest_value, its index ],\n// If there are no even values or the given array is empty, return [].\n// Example 1:\n// >>> pluck([4, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([1, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([])\n// []\n// Example 4:\n// >>> pluck([5, 0, 3, 0, 4, 2])\n// [0, 1]\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunction pluck(arr){\n\n    let smallest = Infinity;\n    let smallestIndex = Infinity;\n    let smallestValue = Infinity;\n    for(let i = 0; i < arr.length; i++){\n        if(arr[i] % 2 === 0 && arr[i] < smallestValue){\n            smallestValue = arr[i];\n            smallestIndex = i;\n        }\n    }\n    if(smallestValue === Infinity){\n        return [];\n    }\n    return [smallestValue, smallestIndex];\n}", "prompt": "//\"Given an array representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in an array, [ smalest_value, its index ],\n// If there are no even values or the given array is empty, return [].\n// Example 1:\n// >>> pluck([4, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([1, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([])\n// []\n// Example 4:\n// >>> pluck([5, 0, 3, 0, 4, 2])\n// [0, 1]\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunction pluck(arr){\n", "wholecode": "//\"Given an array representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in an array, [ smalest_value, its index ],\n// If there are no even values or the given array is empty, return [].\n// Example 1:\n// >>> pluck([4, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([1, 2, 3])\n// [2, 1]\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([])\n// []\n// Example 4:\n// >>> pluck([5, 0, 3, 0, 4, 2])\n// [0, 1]\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunction pluck(arr){\n    let smallest = Infinity;\n    let smallestIndex = Infinity;\n    let smallestValue = Infinity;\n    for(let i = 0; i < arr.length; i++){\n        if(arr[i] % 2 === 0 && arr[i] < smallestValue){\n            smallestValue = arr[i];\n            smallestIndex = i;\n        }\n    }\n    if(smallestValue === Infinity){\n        return [];\n    }\n    return [smallestValue, smallestIndex];\n}"}
{"task_id": "HumanEval_147_get_max_triples", "generation": "//You are given a positive integer n. You have to create an integer array a of length n.\n// For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunction get_max_triples(n){\n\n    let a = [];\n    for(let i = 1; i <= n; i++){\n        a.push(i * i - i + 1);\n    }\n    let count = 0;\n    for(let i = 0; i < a.length; i++){\n        for(let j = i + 1; j < a.length; j++){\n            for(let k = j + 1; k < a.length; k++){\n                if((a[i] + a[j] + a[k]) % 3 === 0){\n                    count++;\n                }\n            }\n        }\n    }\n    return count;\n}", "prompt": "//You are given a positive integer n. You have to create an integer array a of length n.\n// For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunction get_max_triples(n){\n", "wholecode": "//You are given a positive integer n. You have to create an integer array a of length n.\n// For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunction get_max_triples(n){\n    let a = [];\n    for(let i = 1; i <= n; i++){\n        a.push(i * i - i + 1);\n    }\n    let count = 0;\n    for(let i = 0; i < a.length; i++){\n        for(let j = i + 1; j < a.length; j++){\n            for(let k = j + 1; k < a.length; k++){\n                if((a[i] + a[j] + a[k]) % 3 === 0){\n                    count++;\n                }\n            }\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_110_exchange", "generation": "//In this problem, you will implement a function that takes two arrays of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 an array of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([1, 2, 3, 4], [1, 2, 3, 4])\n// \"YES\"\n// >>> exchange([1, 2, 3, 4], [1, 5, 3, 4])\n// \"NO\"\n// It is assumed that the input arrays will be non-empty.\nfunction exchange(lst1, lst2){\n\n    let even = 0;\n    let odd = 0;\n    for(let i = 0; i < lst1.length; i++){\n        if(lst1[i] % 2 === 0){\n            even++;\n        }else{\n            odd++;\n        }\n    }\n    for(let i = 0; i < lst2.length; i++){\n        if(lst2[i] % 2 === 0){\n            even++;\n        }else{\n            odd++;\n        }\n    }\n    if(even >= lst1.length){\n        return \"YES\";\n    }else{\n        return \"NO\";\n    }\n}", "prompt": "//In this problem, you will implement a function that takes two arrays of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 an array of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([1, 2, 3, 4], [1, 2, 3, 4])\n// \"YES\"\n// >>> exchange([1, 2, 3, 4], [1, 5, 3, 4])\n// \"NO\"\n// It is assumed that the input arrays will be non-empty.\nfunction exchange(lst1, lst2){\n", "wholecode": "//In this problem, you will implement a function that takes two arrays of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 an array of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([1, 2, 3, 4], [1, 2, 3, 4])\n// \"YES\"\n// >>> exchange([1, 2, 3, 4], [1, 5, 3, 4])\n// \"NO\"\n// It is assumed that the input arrays will be non-empty.\nfunction exchange(lst1, lst2){\n    let even = 0;\n    let odd = 0;\n    for(let i = 0; i < lst1.length; i++){\n        if(lst1[i] % 2 === 0){\n            even++;\n        }else{\n            odd++;\n        }\n    }\n    for(let i = 0; i < lst2.length; i++){\n        if(lst2[i] % 2 === 0){\n            even++;\n        }else{\n            odd++;\n        }\n    }\n    if(even >= lst1.length){\n        return \"YES\";\n    }else{\n        return \"NO\";\n    }\n}"}
{"task_id": "HumanEval_47_median", "generation": "//Return median of elements in the array l.\n// >>> median([3, 1, 2, 4, 5])\n// 3\n// >>> median([-10, 4, 6, 1000, 10, 20])\n// 15.0\nfunction median(l){\n\n    l.sort((a,b) => a-b);\n    let mid = Math.floor(l.length/2);\n    if(l.length%2 === 0){\n        return (l[mid] + l[mid-1])/2;\n    }else{\n        return l[mid];\n    }\n}", "prompt": "//Return median of elements in the array l.\n// >>> median([3, 1, 2, 4, 5])\n// 3\n// >>> median([-10, 4, 6, 1000, 10, 20])\n// 15.0\nfunction median(l){\n", "wholecode": "//Return median of elements in the array l.\n// >>> median([3, 1, 2, 4, 5])\n// 3\n// >>> median([-10, 4, 6, 1000, 10, 20])\n// 15.0\nfunction median(l){\n    l.sort((a,b) => a-b);\n    let mid = Math.floor(l.length/2);\n    if(l.length%2 === 0){\n        return (l[mid] + l[mid-1])/2;\n    }else{\n        return l[mid];\n    }\n}"}
{"task_id": "HumanEval_82_prime_length", "generation": "//Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunction prime_length(string){\n\n    let length = string.length;\n    let isPrime = true;\n    for(let i = 2; i < length; i++){\n        if(length % i === 0){\n            isPrime = false;\n        }\n    }\n    return isPrime;\n}", "prompt": "//Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunction prime_length(string){\n", "wholecode": "//Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunction prime_length(string){\n    let length = string.length;\n    let isPrime = true;\n    for(let i = 2; i < length; i++){\n        if(length % i === 0){\n            isPrime = false;\n        }\n    }\n    return isPrime;\n}"}
{"task_id": "HumanEval_73_smallest_change", "generation": "//Given an array arr of integers, find the minimum number of elements that\n// need to be changed to make the array palindromic. A palindromic array is an array that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([1, 2, 3, 5, 4, 7, 9, 6])\n// 4\n// >>> smallest_change([1, 2, 3, 4, 3, 2, 2])\n// 1\n// >>> smallest_change([1, 2, 3, 2, 1])\n// 0\nfunction smallest_change(arr){\n\n    let left = 0;\n    let right = arr.length - 1;\n    let count = 0;\n    while(left < right){\n        if(arr[left] !== arr[right]){\n            count++;\n        }\n        left++;\n        right--;\n    }\n    return count;\n}", "prompt": "//Given an array arr of integers, find the minimum number of elements that\n// need to be changed to make the array palindromic. A palindromic array is an array that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([1, 2, 3, 5, 4, 7, 9, 6])\n// 4\n// >>> smallest_change([1, 2, 3, 4, 3, 2, 2])\n// 1\n// >>> smallest_change([1, 2, 3, 2, 1])\n// 0\nfunction smallest_change(arr){\n", "wholecode": "//Given an array arr of integers, find the minimum number of elements that\n// need to be changed to make the array palindromic. A palindromic array is an array that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([1, 2, 3, 5, 4, 7, 9, 6])\n// 4\n// >>> smallest_change([1, 2, 3, 4, 3, 2, 2])\n// 1\n// >>> smallest_change([1, 2, 3, 2, 1])\n// 0\nfunction smallest_change(arr){\n    let left = 0;\n    let right = arr.length - 1;\n    let count = 0;\n    while(left < right){\n        if(arr[left] !== arr[right]){\n            count++;\n        }\n        left++;\n        right--;\n    }\n    return count;\n}"}
{"task_id": "HumanEval_133_sum_squares", "generation": "//You are given an array of numbers.\n// You need to return the sum of squared numbers in the given array,\n// round each element in the array to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([1.0, 2.0, 3.0])\n// 14\n// >>> lst([1.0, 4.0, 9.0])\n// 98\n// >>> lst([1.0, 3.0, 5.0, 7.0])\n// 84\n// >>> lst([1.4, 4.2, 0.0])\n// 29\n// >>> lst([-2.4, 1.0, 1.0])\n// 6\nfunction sum_squares(lst){\n\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        sum += Math.ceil(lst[i]) ** 2;\n    }\n    return sum;\n}", "prompt": "//You are given an array of numbers.\n// You need to return the sum of squared numbers in the given array,\n// round each element in the array to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([1.0, 2.0, 3.0])\n// 14\n// >>> lst([1.0, 4.0, 9.0])\n// 98\n// >>> lst([1.0, 3.0, 5.0, 7.0])\n// 84\n// >>> lst([1.4, 4.2, 0.0])\n// 29\n// >>> lst([-2.4, 1.0, 1.0])\n// 6\nfunction sum_squares(lst){\n", "wholecode": "//You are given an array of numbers.\n// You need to return the sum of squared numbers in the given array,\n// round each element in the array to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([1.0, 2.0, 3.0])\n// 14\n// >>> lst([1.0, 4.0, 9.0])\n// 98\n// >>> lst([1.0, 3.0, 5.0, 7.0])\n// 84\n// >>> lst([1.4, 4.2, 0.0])\n// 29\n// >>> lst([-2.4, 1.0, 1.0])\n// 6\nfunction sum_squares(lst){\n    let sum = 0;\n    for(let i = 0; i < lst.length; i++){\n        sum += Math.ceil(lst[i]) ** 2;\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_141_file_name_check", "generation": "//Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunction file_name_check(file_name){\n\n    let regex = /^[a-zA-Z][a-zA-Z0-9]{0,2}\\.(txt|exe|dll)$/;\n    return regex.test(file_name) ? \"Yes\" : \"No\";\n}", "prompt": "//Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunction file_name_check(file_name){\n", "wholecode": "//Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunction file_name_check(file_name){\n    let regex = /^[a-zA-Z][a-zA-Z0-9]{0,2}\\.(txt|exe|dll)$/;\n    return regex.test(file_name) ? \"Yes\" : \"No\";\n}"}
{"task_id": "HumanEval_40_triples_sum_to_zero", "generation": "//triples_sum_to_zero takes an array of integers as an input.\n// it returns true if there are three distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> triples_sum_to_zero([1, 3, -2, 1])\n// true\n// >>> triples_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n// true\n// >>> triples_sum_to_zero([1])\n// false\nfunction triples_sum_to_zero(l){\n\n  // TODO: Your code here\n}\n\nmodule.exports = triples_sum_to_zero;", "prompt": "//triples_sum_to_zero takes an array of integers as an input.\n// it returns true if there are three distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> triples_sum_to_zero([1, 3, -2, 1])\n// true\n// >>> triples_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n// true\n// >>> triples_sum_to_zero([1])\n// false\nfunction triples_sum_to_zero(l){\n", "wholecode": "//triples_sum_to_zero takes an array of integers as an input.\n// it returns true if there are three distinct elements in the array that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([1, 3, 5, 0])\n// false\n// >>> triples_sum_to_zero([1, 3, -2, 1])\n// true\n// >>> triples_sum_to_zero([1, 2, 3, 7])\n// false\n// >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n// true\n// >>> triples_sum_to_zero([1])\n// false\nfunction triples_sum_to_zero(l){\n  // TODO: Your code here\n}\n\nmodule.exports = triples_sum_to_zero;"}
{"task_id": "HumanEval_127_intersection", "generation": "//You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([1, 2], [2, 3])\n// \"NO\"\n// >>> intersection([-1, 1], [0, 4])\n// \"NO\"\n// >>> intersection([-3, -1], [-5, 5])\n// \"YES\"\nfunction intersection(interval1, interval2){\n\n    let start = Math.max(interval1[0], interval2[0]);\n    let end = Math.min(interval1[1], interval2[1]);\n    let length = end - start;\n    if(length <= 0){\n        return \"NO\";\n    }\n    if(length === 1){\n        return \"NO\";\n    }\n    if(length === 2){\n        return \"YES\";\n    }\n    if(length === 3){\n        return \"YES\";\n    }\n    if(length === 4){\n        return \"NO\";\n    }\n    if(length === 5){\n        return \"YES\";\n    }\n    if(length === 6){\n        return \"NO\";\n    }\n    if(length === 7){\n        return \"YES\";\n    }\n    if(length === 8){\n        return \"NO\";\n    }\n    if(length === 9){\n        return \"YES\";\n    }\n    if(length === 10){\n        return \"NO\";\n    }\n    if(length === 11){\n        return \"YES\";\n    }\n    if(length === 12){\n        return \"NO\";\n    }\n    if(length === 13){\n        return \"YES\";\n    }\n    if(length === 14){\n        return \"NO\";\n    }\n    if(length === 15){\n        return \"YES\";\n    }\n    if(length === 16){\n        return \"NO\";\n    }\n    if(length === 17){\n        return \"YES\";\n    }\n    if(length === 18){\n        return \"NO\";\n    }\n    if(length === 19){\n        return \"YES\";\n    }\n    if(length === 20){\n        return \"NO\";\n    }\n    if(length === 21){\n        return \"YES\";\n    }\n    if(length === 22){\n        return \"NO\";\n    }\n    if(length === 23){\n        return \"YES\";\n    }\n    if(length === 24){\n        return \"NO\";\n    }\n    if(length === 25){\n        return \"YES\";\n    }\n    if(length === 26){\n        return \"NO\";\n    }\n    if(length === 27){\n        return \"YES\";\n    }\n    if(length === 28){\n        return \"NO\";\n    }\n    if(length === 29){\n        return \"YES\";\n    }\n    if(length === 30){\n        return \"NO\";\n    }\n    if(length === ", "prompt": "//You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([1, 2], [2, 3])\n// \"NO\"\n// >>> intersection([-1, 1], [0, 4])\n// \"NO\"\n// >>> intersection([-3, -1], [-5, 5])\n// \"YES\"\nfunction intersection(interval1, interval2){\n", "wholecode": "//You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([1, 2], [2, 3])\n// \"NO\"\n// >>> intersection([-1, 1], [0, 4])\n// \"NO\"\n// >>> intersection([-3, -1], [-5, 5])\n// \"YES\"\nfunction intersection(interval1, interval2){\n    let start = Math.max(interval1[0], interval2[0]);\n    let end = Math.min(interval1[1], interval2[1]);\n    let length = end - start;\n    if(length <= 0){\n        return \"NO\";\n    }\n    if(length === 1){\n        return \"NO\";\n    }\n    if(length === 2){\n        return \"YES\";\n    }\n    if(length === 3){\n        return \"YES\";\n    }\n    if(length === 4){\n        return \"NO\";\n    }\n    if(length === 5){\n        return \"YES\";\n    }\n    if(length === 6){\n        return \"NO\";\n    }\n    if(length === 7){\n        return \"YES\";\n    }\n    if(length === 8){\n        return \"NO\";\n    }\n    if(length === 9){\n        return \"YES\";\n    }\n    if(length === 10){\n        return \"NO\";\n    }\n    if(length === 11){\n        return \"YES\";\n    }\n    if(length === 12){\n        return \"NO\";\n    }\n    if(length === 13){\n        return \"YES\";\n    }\n    if(length === 14){\n        return \"NO\";\n    }\n    if(length === 15){\n        return \"YES\";\n    }\n    if(length === 16){\n        return \"NO\";\n    }\n    if(length === 17){\n        return \"YES\";\n    }\n    if(length === 18){\n        return \"NO\";\n    }\n    if(length === 19){\n        return \"YES\";\n    }\n    if(length === 20){\n        return \"NO\";\n    }\n    if(length === 21){\n        return \"YES\";\n    }\n    if(length === 22){\n        return \"NO\";\n    }\n    if(length === 23){\n        return \"YES\";\n    }\n    if(length === 24){\n        return \"NO\";\n    }\n    if(length === 25){\n        return \"YES\";\n    }\n    if(length === 26){\n        return \"NO\";\n    }\n    if(length === 27){\n        return \"YES\";\n    }\n    if(length === 28){\n        return \"NO\";\n    }\n    if(length === 29){\n        return \"YES\";\n    }\n    if(length === 30){\n        return \"NO\";\n    }\n    if(length === "}
{"task_id": "HumanEval_1_separate_paren_groups", "generation": "//Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the array of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// [\"()\", \"(())\", \"(()())\"]\nfunction separate_paren_groups(paren_string){\n\n    let result = [];\n    let stack = [];\n    let current = \"\";\n    for(let i = 0; i < paren_string.length; i++){\n        if(paren_string[i] === \" \"){\n            continue;\n        }\n        if(paren_string[i] === \"(\"){\n            stack.push(paren_string[i]);\n            current += paren_string[i];\n        }else if(paren_string[i] === \")\"){\n            stack.pop();\n            current += paren_string[i];\n            if(stack.length === 0){\n                result.push(current);\n                current = \"\";\n            }\n        }\n    }\n    return result;\n}", "prompt": "//Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the array of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// [\"()\", \"(())\", \"(()())\"]\nfunction separate_paren_groups(paren_string){\n", "wholecode": "//Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the array of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// [\"()\", \"(())\", \"(()())\"]\nfunction separate_paren_groups(paren_string){\n    let result = [];\n    let stack = [];\n    let current = \"\";\n    for(let i = 0; i < paren_string.length; i++){\n        if(paren_string[i] === \" \"){\n            continue;\n        }\n        if(paren_string[i] === \"(\"){\n            stack.push(paren_string[i]);\n            current += paren_string[i];\n        }else if(paren_string[i] === \")\"){\n            stack.pop();\n            current += paren_string[i];\n            if(stack.length === 0){\n                result.push(current);\n                current = \"\";\n            }\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_152_compare", "generation": "//I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n// [0, 0, 0, 0, 3, 3]\n// >>> compare([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n// [4, 4, 1, 0, 0, 6]\nfunction compare(game, guess){\n\n    let result = [];\n    for(let i = 0; i < game.length; i++){\n        if(game[i] === guess[i]){\n            result.push(0);\n        } else {\n            result.push(Math.abs(game[i] - guess[i]));\n        }\n    }\n    return result;\n}", "prompt": "//I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n// [0, 0, 0, 0, 3, 3]\n// >>> compare([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n// [4, 4, 1, 0, 0, 6]\nfunction compare(game, guess){\n", "wholecode": "//I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n// [0, 0, 0, 0, 3, 3]\n// >>> compare([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n// [4, 4, 1, 0, 0, 6]\nfunction compare(game, guess){\n    let result = [];\n    for(let i = 0; i < game.length; i++){\n        if(game[i] === guess[i]){\n            result.push(0);\n        } else {\n            result.push(Math.abs(game[i] - guess[i]));\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_83_starts_one_ends", "generation": "//Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunction starts_one_ends(n){\n\n    if(n === 1){\n        return 1;\n    }\n    return 2 * (9 * Math.pow(10, n - 2));\n}\n", "prompt": "//Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunction starts_one_ends(n){\n", "wholecode": "//Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunction starts_one_ends(n){\n    if(n === 1){\n        return 1;\n    }\n    return 2 * (9 * Math.pow(10, n - 2));\n}\n\nconsole.log(starts_one_ends(1));\nconsole.log(starts_one_ends(2));\nconsole.log(starts_one_ends(3));"}
{"task_id": "HumanEval_134_check_if_last_char_is_a_letter", "generation": "//Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunction check_if_last_char_is_a_letter(txt){\n\n    let lastChar = txt.charAt(txt.length - 1);\n    let lastCharIsLetter = lastChar.match(/[a-zA-Z]/);\n    let lastCharIsPartOfWord = lastChar.match(/\\S/);\n    if(lastCharIsLetter && !lastCharIsPartOfWord){\n        return true;\n    }else{\n        return false;\n    }\n}\n", "prompt": "//Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunction check_if_last_char_is_a_letter(txt){\n", "wholecode": "//Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunction check_if_last_char_is_a_letter(txt){\n    let lastChar = txt.charAt(txt.length - 1);\n    let lastCharIsLetter = lastChar.match(/[a-zA-Z]/);\n    let lastCharIsPartOfWord = lastChar.match(/\\S/);\n    if(lastCharIsLetter && !lastCharIsPartOfWord){\n        return true;\n    }else{\n        return false;\n    }\n}\n\nconsole.log(check_if_last_char_is_a_letter(\"apple pie\"));\nconsole.log(check_if_last_char_is_a_letter(\"apple pi e\"));\nconsole.log(check_if_last_char_is_a_letter(\"apple pi e \"));\nconsole.log(check_if_last_char_is_a_letter(\"\"));"}
{"task_id": "HumanEval_124_valid_date", "generation": "//You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunction valid_date(date){\n\n    let arr = date.split('-');\n    let month = arr[0];\n    let day = arr[1];\n    let year = arr[2];\n    if(month < 1 || month > 12){\n        return false;\n    }\n    if(day < 1 || day > 31){\n        return false;\n    }\n    if(month == 4 || month == 6 || month == 9 || month == 11){\n        if(day > 30){\n            return false;\n        }\n    }\n    if(month == 2){\n        if(day > 29){\n            return false;\n        }\n    }\n    if(year < 1){\n        return false;\n    }\n    return true;\n}", "prompt": "//You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunction valid_date(date){\n", "wholecode": "//You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunction valid_date(date){\n    let arr = date.split('-');\n    let month = arr[0];\n    let day = arr[1];\n    let year = arr[2];\n    if(month < 1 || month > 12){\n        return false;\n    }\n    if(day < 1 || day > 31){\n        return false;\n    }\n    if(month == 4 || month == 6 || month == 9 || month == 11){\n        if(day > 30){\n            return false;\n        }\n    }\n    if(month == 2){\n        if(day > 29){\n            return false;\n        }\n    }\n    if(year < 1){\n        return false;\n    }\n    return true;\n}"}
{"task_id": "HumanEval_108_count_nums", "generation": "//Write a function count_nums which takes an array of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([])\n// 0\n// >>> count_nums([-1, 11, -11])\n// 1\n// >>> count_nums([1, 1, 2])\n// 3\nfunction count_nums(arr){\n\n    let count = 0;\n    for(let i = 0; i < arr.length; i++){\n        let sum = 0;\n        let num = arr[i];\n        if(num < 0){\n            num = num.toString();\n            for(let j = 0; j < num.length; j++){\n                if(num[j] === \"-\"){\n                    sum += parseInt(num[j]);\n                }else{\n                    sum += parseInt(num[j]);\n                }\n            }\n        }else{\n            num = num.toString();\n            for(let j = 0; j < num.length; j++){\n                sum += parseInt(num[j]);\n            }\n        }\n        if(sum > 0){\n            count++;\n        }\n    }\n    return count;\n}", "prompt": "//Write a function count_nums which takes an array of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([])\n// 0\n// >>> count_nums([-1, 11, -11])\n// 1\n// >>> count_nums([1, 1, 2])\n// 3\nfunction count_nums(arr){\n", "wholecode": "//Write a function count_nums which takes an array of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([])\n// 0\n// >>> count_nums([-1, 11, -11])\n// 1\n// >>> count_nums([1, 1, 2])\n// 3\nfunction count_nums(arr){\n    let count = 0;\n    for(let i = 0; i < arr.length; i++){\n        let sum = 0;\n        let num = arr[i];\n        if(num < 0){\n            num = num.toString();\n            for(let j = 0; j < num.length; j++){\n                if(num[j] === \"-\"){\n                    sum += parseInt(num[j]);\n                }else{\n                    sum += parseInt(num[j]);\n                }\n            }\n        }else{\n            num = num.toString();\n            for(let j = 0; j < num.length; j++){\n                sum += parseInt(num[j]);\n            }\n        }\n        if(sum > 0){\n            count++;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_86_anti_shuffle", "generation": "//Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunction anti_shuffle(s){\n\n    let arr = s.split(' ')\n    let result = []\n    for(let i = 0; i < arr.length; i++){\n        let word = arr[i]\n        let newWord = word.split('').sort().join('')\n        result.push(newWord)\n    }\n    return result.join(' ')\n}", "prompt": "//Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunction anti_shuffle(s){\n", "wholecode": "//Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunction anti_shuffle(s){\n    let arr = s.split(' ')\n    let result = []\n    for(let i = 0; i < arr.length; i++){\n        let word = arr[i]\n        let newWord = word.split('').sort().join('')\n        result.push(newWord)\n    }\n    return result.join(' ')\n}"}
{"task_id": "HumanEval_48_is_palindrome", "generation": "//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text){\n\n    if(text.length === 0){\n        return true;\n    }\n    if(text.length === 1){\n        return true;\n    }\n    if(text[0] === text[text.length-1]){\n        return is_palindrome(text.slice(1,text.length-1));\n    }\n    return false;\n}\n", "prompt": "//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text){\n", "wholecode": "//Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunction is_palindrome(text){\n    if(text.length === 0){\n        return true;\n    }\n    if(text.length === 1){\n        return true;\n    }\n    if(text[0] === text[text.length-1]){\n        return is_palindrome(text.slice(1,text.length-1));\n    }\n    return false;\n}\n\n// >>> is_palindrome_recursive(\"\")\n// true\n// >>> is_palindrome_recursive(\"aba\")\n// true\n// >>> is_palindrome_recursive(\"aaaaa\")\n// true\n// >>> is_palindrome_recursive(\"zbcd\")\n// false\nfunction is_palindrome_recursive(text){\n    if(text.length === 0){\n        return true;\n    }\n    if(text.length === 1){\n        return true;\n    }\n    if(text[0] === text[text.length-1]){\n        return is_palindrome_recursive(text.slice(1,text.length-1));\n    }\n    return false;\n}"}
{"task_id": "HumanEval_118_get_closest_vowel", "generation": "//You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunction get_closest_vowel(word){\n\n    let vowels = ['a', 'e', 'i', 'o', 'u'];\n    let result = '';\n    for(let i = word.length - 1; i > 0; i--){\n        if(vowels.includes(word[i]) && !vowels.includes(word[i - 1]) && !vowels.includes(word[i + 1])){\n            result = word[i];\n            break;\n        }\n    }\n    return result;\n}", "prompt": "//You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunction get_closest_vowel(word){\n", "wholecode": "//You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunction get_closest_vowel(word){\n    let vowels = ['a', 'e', 'i', 'o', 'u'];\n    let result = '';\n    for(let i = word.length - 1; i > 0; i--){\n        if(vowels.includes(word[i]) && !vowels.includes(word[i - 1]) && !vowels.includes(word[i + 1])){\n            result = word[i];\n            break;\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_31_is_prime", "generation": "//Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunction is_prime(n){\n\n    if(n < 2){\n        return false;\n    }\n    for(let i = 2; i < n; i++){\n        if(n % i === 0){\n            return false;\n        }\n    }\n    return true;\n}", "prompt": "//Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunction is_prime(n){\n", "wholecode": "//Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunction is_prime(n){\n    if(n < 2){\n        return false;\n    }\n    for(let i = 2; i < n; i++){\n        if(n % i === 0){\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_144_simplify", "generation": "//Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunction simplify(x, n){\n\n    let xNum = x.split('/')[0]\n    let xDen = x.split('/')[1]\n    let nNum = n.split('/')[0]\n    let nDen = n.split('/')[1]\n    let xNumTimesNDen = xNum * nDen\n    let nNumTimesxDen = nNum * xDen\n    if (xNumTimesNDen === nNumTimesxDen) {\n        return true\n    } else {\n        return false\n    }\n}", "prompt": "//Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunction simplify(x, n){\n", "wholecode": "//Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunction simplify(x, n){\n    let xNum = x.split('/')[0]\n    let xDen = x.split('/')[1]\n    let nNum = n.split('/')[0]\n    let nDen = n.split('/')[1]\n    let xNumTimesNDen = xNum * nDen\n    let nNumTimesxDen = nNum * xDen\n    if (xNumTimesNDen === nNumTimesxDen) {\n        return true\n    } else {\n        return false\n    }\n}"}
{"task_id": "HumanEval_78_hex_key", "generation": "//You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunction hex_key(num){\n\n    let count = 0;\n    let hex = num.toUpperCase();\n    for(let i = 0; i < hex.length; i++){\n        if(hex[i] === '2' || hex[i] === '3' || hex[i] === '5' || hex[i] === '7' || hex[i] === 'B' || hex[i] === 'D'){\n            count++;\n        }\n    }\n    return count;\n}", "prompt": "//You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunction hex_key(num){\n", "wholecode": "//You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunction hex_key(num){\n    let count = 0;\n    let hex = num.toUpperCase();\n    for(let i = 0; i < hex.length; i++){\n        if(hex[i] === '2' || hex[i] === '3' || hex[i] === '5' || hex[i] === '7' || hex[i] === 'B' || hex[i] === 'D'){\n            count++;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_143_words_in_sentence", "generation": "//You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunction words_in_sentence(sentence){\n\n    let arr = sentence.split(\" \");\n    let result = [];\n    for(let i = 0; i < arr.length; i++){\n        if(isPrime(arr[i].length)){\n            result.push(arr[i]);\n        }\n    }\n    return result.join(\" \");\n}\n", "prompt": "//You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunction words_in_sentence(sentence){\n", "wholecode": "//You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunction words_in_sentence(sentence){\n    let arr = sentence.split(\" \");\n    let result = [];\n    for(let i = 0; i < arr.length; i++){\n        if(isPrime(arr[i].length)){\n            result.push(arr[i]);\n        }\n    }\n    return result.join(\" \");\n}\n\nfunction isPrime(num){\n    if(num < 2){\n        return false;\n    }\n    for(let i = 2; i < num; i++){\n        if(num % i === 0){\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_111_histogram", "generation": "//Given a string representing a space separated lowercase letters, return an object\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// {\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// {\"b\": 4}\n// >>> histogram(\"\")\n// {}\nfunction histogram(test){\n\n    let obj = {}\n    let arr = test.split(\" \")\n    for(let i = 0; i < arr.length; i++){\n        if(obj[arr[i]]){\n            obj[arr[i]] += 1\n        }else{\n            obj[arr[i]] = 1\n        }\n    }\n    let max = Math.max(...Object.values(obj))\n    let result = []\n    for(let key in obj){\n        if(obj[key] === max){\n            result.push(key)\n        }\n    }\n    return result\n}", "prompt": "//Given a string representing a space separated lowercase letters, return an object\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// {\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// {\"b\": 4}\n// >>> histogram(\"\")\n// {}\nfunction histogram(test){\n", "wholecode": "//Given a string representing a space separated lowercase letters, return an object\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// {\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// {\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// {\"b\": 4}\n// >>> histogram(\"\")\n// {}\nfunction histogram(test){\n    let obj = {}\n    let arr = test.split(\" \")\n    for(let i = 0; i < arr.length; i++){\n        if(obj[arr[i]]){\n            obj[arr[i]] += 1\n        }else{\n            obj[arr[i]] = 1\n        }\n    }\n    let max = Math.max(...Object.values(obj))\n    let result = []\n    for(let key in obj){\n        if(obj[key] === max){\n            result.push(key)\n        }\n    }\n    return result\n}\nconsole.log(histogram(\"a b c\"))\nconsole.log(histogram(\"a b b a\"))\nconsole.log(histogram(\"a b c a b\"))\nconsole.log(histogram(\"b b b b a\"))\nconsole.log(histogram(\"\"))"}
{"task_id": "HumanEval_87_get_row", "generation": "//You are given a 2 dimensional data, as a nested arrays,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the array,\n// and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n// each array is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n// [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n// >>> get_row([], 1)\n// []\n// >>> get_row([[], [1], [1, 2, 3]], 3)\n// [[2, 2]]\nfunction get_row(lst, x){\n\n  let result = [];\n  for (let i = 0; i < lst.length; i++) {\n    for (let j = 0; j < lst[i].length; j++) {\n      if (lst[i][j] === x) {\n        result.push([i, j]);\n      }\n    }\n  }\n  return result.sort((a, b) => a[0] - b[0] || b[1] - a[1]);\n}", "prompt": "//You are given a 2 dimensional data, as a nested arrays,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the array,\n// and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n// each array is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n// [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n// >>> get_row([], 1)\n// []\n// >>> get_row([[], [1], [1, 2, 3]], 3)\n// [[2, 2]]\nfunction get_row(lst, x){\n", "wholecode": "//You are given a 2 dimensional data, as a nested arrays,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the array,\n// and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n// each array is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n// [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n// >>> get_row([], 1)\n// []\n// >>> get_row([[], [1], [1, 2, 3]], 3)\n// [[2, 2]]\nfunction get_row(lst, x){\n  let result = [];\n  for (let i = 0; i < lst.length; i++) {\n    for (let j = 0; j < lst[i].length; j++) {\n      if (lst[i][j] === x) {\n        result.push([i, j]);\n      }\n    }\n  }\n  return result.sort((a, b) => a[0] - b[0] || b[1] - a[1]);\n}"}
{"task_id": "HumanEval_123_get_odd_collatz", "generation": "//Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned array sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// [1, 5]\nfunction get_odd_collatz(n){\n\n    let arr = [];\n    let i = n;\n    while(i > 1){\n        if(i % 2 === 0){\n            i = i / 2;\n        }else{\n            i = 3 * i + 1;\n        }\n        if(i % 2 !== 0){\n            arr.push(i);\n        }\n    }\n    return arr;\n}", "prompt": "//Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned array sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// [1, 5]\nfunction get_odd_collatz(n){\n", "wholecode": "//Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned array sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// [1, 5]\nfunction get_odd_collatz(n){\n    let arr = [];\n    let i = n;\n    while(i > 1){\n        if(i % 2 === 0){\n            i = i / 2;\n        }else{\n            i = 3 * i + 1;\n        }\n        if(i % 2 !== 0){\n            arr.push(i);\n        }\n    }\n    return arr;\n}"}
{"task_id": "HumanEval_135_can_arrange", "generation": "//Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given array will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([1, 2, 4, 3, 5])\n// 3\n// >>> can_arrange([1, 2, 3])\n// -1\nfunction can_arrange(arr){\n\n    let i = 0;\n    while(i < arr.length - 1){\n        if(arr[i] > arr[i + 1]){\n            return i;\n        }\n        i++;\n    }\n    return -1;\n}\n", "prompt": "//Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given array will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([1, 2, 4, 3, 5])\n// 3\n// >>> can_arrange([1, 2, 3])\n// -1\nfunction can_arrange(arr){\n", "wholecode": "//Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given array will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([1, 2, 4, 3, 5])\n// 3\n// >>> can_arrange([1, 2, 3])\n// -1\nfunction can_arrange(arr){\n    let i = 0;\n    while(i < arr.length - 1){\n        if(arr[i] > arr[i + 1]){\n            return i;\n        }\n        i++;\n    }\n    return -1;\n}\n\nconsole.log(can_arrange([1, 2, 4, 3, 5]));\nconsole.log(can_arrange([1, 2, 3]));"}
{"task_id": "HumanEval_19_sort_numbers", "generation": "//Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunction sort_numbers(numbers){\n\n    let arr = numbers.split(' ')\n    let obj = {\n        zero: 0,\n        one: 1,\n        two: 2,\n        three: 3,\n        four: 4,\n        five: 5,\n        six: 6,\n        seven: 7,\n        eight: 8,\n        nine: 9\n    }\n    let sorted = arr.sort((a,b) => obj[a] - obj[b])\n    return sorted.join(' ')\n}", "prompt": "//Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunction sort_numbers(numbers){\n", "wholecode": "//Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunction sort_numbers(numbers){\n    let arr = numbers.split(' ')\n    let obj = {\n        zero: 0,\n        one: 1,\n        two: 2,\n        three: 3,\n        four: 4,\n        five: 5,\n        six: 6,\n        seven: 7,\n        eight: 8,\n        nine: 9\n    }\n    let sorted = arr.sort((a,b) => obj[a] - obj[b])\n    return sorted.join(' ')\n}"}
{"task_id": "HumanEval_65_circular_shift", "generation": "//Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunction circular_shift(x, shift){\n\n    let str = x.toString();\n    let len = str.length;\n    if(shift > len){\n        return str.split('').reverse().join('');\n    }\n    let result = str.slice(len - shift) + str.slice(0, len - shift);\n    return result;\n}", "prompt": "//Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunction circular_shift(x, shift){\n", "wholecode": "//Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunction circular_shift(x, shift){\n    let str = x.toString();\n    let len = str.length;\n    if(shift > len){\n        return str.split('').reverse().join('');\n    }\n    let result = str.slice(len - shift) + str.slice(0, len - shift);\n    return result;\n}"}
{"task_id": "HumanEval_142_sum_squares", "generation": "//\"\n// This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// [1, 2, 3]\n// >>> lst\n// []\n// >>> lst\n// [-1, -5, 2, -1, -5]\nfunction sum_squares(lst){\n\n    let sum = 0;\n    for (let i = 0; i < lst.length; i++){\n        if (i % 3 === 0){\n            sum += lst[i] ** 2;\n        } else if (i % 4 === 0 && i % 3 !== 0){\n            sum += lst[i] ** 3;\n        } else {\n            sum += lst[i];\n        }\n    }\n    return sum;\n}", "prompt": "//\"\n// This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// [1, 2, 3]\n// >>> lst\n// []\n// >>> lst\n// [-1, -5, 2, -1, -5]\nfunction sum_squares(lst){\n", "wholecode": "//\"\n// This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// [1, 2, 3]\n// >>> lst\n// []\n// >>> lst\n// [-1, -5, 2, -1, -5]\nfunction sum_squares(lst){\n    let sum = 0;\n    for (let i = 0; i < lst.length; i++){\n        if (i % 3 === 0){\n            sum += lst[i] ** 2;\n        } else if (i % 4 === 0 && i % 3 !== 0){\n            sum += lst[i] ** 3;\n        } else {\n            sum += lst[i];\n        }\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_94_skjkasdkd", "generation": "//You are given an array of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n// 10\n// >>> skjkasdkd([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n// 25\n// >>> skjkasdkd([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n// 13\n// >>> skjkasdkd([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n// 11\n// >>> skjkasdkd([0, 81, 12, 3, 1, 21])\n// 3\n// >>> skjkasdkd([0, 8, 1, 2, 1, 7])\n// 7\nfunction skjkasdkd(lst){\n\n    let max = 0;\n    for (let i = 0; i < lst.length; i++) {\n        if (lst[i] > max) {\n            max = lst[i];\n        }\n    }\n    let sum = 0;\n    for (let i = 2; i <= max; i++) {\n        if (isPrime(i)) {\n            sum += i;\n        }\n    }\n    return sum;\n}\n", "prompt": "//You are given an array of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n// 10\n// >>> skjkasdkd([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n// 25\n// >>> skjkasdkd([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n// 13\n// >>> skjkasdkd([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n// 11\n// >>> skjkasdkd([0, 81, 12, 3, 1, 21])\n// 3\n// >>> skjkasdkd([0, 8, 1, 2, 1, 7])\n// 7\nfunction skjkasdkd(lst){\n", "wholecode": "//You are given an array of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n// 10\n// >>> skjkasdkd([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n// 25\n// >>> skjkasdkd([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n// 13\n// >>> skjkasdkd([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n// 11\n// >>> skjkasdkd([0, 81, 12, 3, 1, 21])\n// 3\n// >>> skjkasdkd([0, 8, 1, 2, 1, 7])\n// 7\nfunction skjkasdkd(lst){\n    let max = 0;\n    for (let i = 0; i < lst.length; i++) {\n        if (lst[i] > max) {\n            max = lst[i];\n        }\n    }\n    let sum = 0;\n    for (let i = 2; i <= max; i++) {\n        if (isPrime(i)) {\n            sum += i;\n        }\n    }\n    return sum;\n}\n\nfunction isPrime(num) {\n    if (num <= 1) {\n        return false;\n    }\n    for (let i = 2; i <= Math.sqrt(num); i++) {\n        if (num % i === 0) {\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_8_sum_product", "generation": "//For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([])\n// [0, 1]\n// >>> sum_product([1, 2, 3, 4])\n// [10, 24]\nfunction sum_product(numbers){\n\n    let sum = 0;\n    let product = 1;\n    for(let i = 0; i < numbers.length; i++){\n        sum += numbers[i];\n        product *= numbers[i];\n    }\n    return [sum, product];\n}", "prompt": "//For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([])\n// [0, 1]\n// >>> sum_product([1, 2, 3, 4])\n// [10, 24]\nfunction sum_product(numbers){\n", "wholecode": "//For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([])\n// [0, 1]\n// >>> sum_product([1, 2, 3, 4])\n// [10, 24]\nfunction sum_product(numbers){\n    let sum = 0;\n    let product = 1;\n    for(let i = 0; i < numbers.length; i++){\n        sum += numbers[i];\n        product *= numbers[i];\n    }\n    return [sum, product];\n}"}
{"task_id": "HumanEval_102_choose_num", "generation": "//This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunction choose_num(x, y){\n\n    let result = -1;\n    for(let i = x; i <= y; i++){\n        if(i % 2 === 0){\n            result = i;\n            break;\n        }\n    }\n    return result;\n}\n", "prompt": "//This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunction choose_num(x, y){\n", "wholecode": "//This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunction choose_num(x, y){\n    let result = -1;\n    for(let i = x; i <= y; i++){\n        if(i % 2 === 0){\n            result = i;\n            break;\n        }\n    }\n    return result;\n}\n\nconsole.log(choose_num(12, 15));\nconsole.log(choose_num(13, 12));"}
{"task_id": "HumanEval_136_largest_smallest_integers", "generation": "//Create a function that returns an array (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in an array.\n// If there is no negative or positive integers, return them as undefined.\n// Examples:\n// >>> largest_smallest_integers([2, 4, 1, 3, 5, 7])\n// [undefined, 1]\n// >>> largest_smallest_integers([])\n// [undefined, undefined]\n// >>> largest_smallest_integers([0])\n// [undefined, undefined]\nfunction largest_smallest_integers(lst){\n\n  let a = undefined;\n  let b = undefined;\n  for (let i = 0; i < lst.length; i++) {\n    if (lst[i] < 0) {\n      if (a === undefined) {\n        a = lst[i];\n      } else if (lst[i] > a) {\n        a = lst[i];\n      }\n    } else if (lst[i] > 0) {\n      if (b === undefined) {\n        b = lst[i];\n      } else if (lst[i] < b) {\n        b = lst[i];\n      }\n    }\n  }\n  return [a, b];\n}", "prompt": "//Create a function that returns an array (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in an array.\n// If there is no negative or positive integers, return them as undefined.\n// Examples:\n// >>> largest_smallest_integers([2, 4, 1, 3, 5, 7])\n// [undefined, 1]\n// >>> largest_smallest_integers([])\n// [undefined, undefined]\n// >>> largest_smallest_integers([0])\n// [undefined, undefined]\nfunction largest_smallest_integers(lst){\n", "wholecode": "//Create a function that returns an array (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in an array.\n// If there is no negative or positive integers, return them as undefined.\n// Examples:\n// >>> largest_smallest_integers([2, 4, 1, 3, 5, 7])\n// [undefined, 1]\n// >>> largest_smallest_integers([])\n// [undefined, undefined]\n// >>> largest_smallest_integers([0])\n// [undefined, undefined]\nfunction largest_smallest_integers(lst){\n  let a = undefined;\n  let b = undefined;\n  for (let i = 0; i < lst.length; i++) {\n    if (lst[i] < 0) {\n      if (a === undefined) {\n        a = lst[i];\n      } else if (lst[i] > a) {\n        a = lst[i];\n      }\n    } else if (lst[i] > 0) {\n      if (b === undefined) {\n        b = lst[i];\n      } else if (lst[i] < b) {\n        b = lst[i];\n      }\n    }\n  }\n  return [a, b];\n}"}
{"task_id": "HumanEval_16_count_distinct_characters", "generation": "//Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunction count_distinct_characters(string){\n\n    let count = 0;\n    let arr = string.split('');\n    let set = new Set(arr);\n    return set.size;\n}", "prompt": "//Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunction count_distinct_characters(string){\n", "wholecode": "//Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunction count_distinct_characters(string){\n    let count = 0;\n    let arr = string.split('');\n    let set = new Set(arr);\n    return set.size;\n}"}
{"task_id": "HumanEval_100_make_a_pile", "generation": "//Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in an array, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// [3, 5, 7]\nfunction make_a_pile(n){\n\n    let arr = [];\n    let i = 0;\n    while(n > 0){\n        if(i % 2 === 0){\n            arr.push(n);\n            n = n - 1;\n        }else{\n            arr.push(n);\n            n = n + 1;\n        }\n        i++;\n    }\n    return arr;\n}", "prompt": "//Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in an array, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// [3, 5, 7]\nfunction make_a_pile(n){\n", "wholecode": "//Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in an array, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// [3, 5, 7]\nfunction make_a_pile(n){\n    let arr = [];\n    let i = 0;\n    while(n > 0){\n        if(i % 2 === 0){\n            arr.push(n);\n            n = n - 1;\n        }else{\n            arr.push(n);\n            n = n + 1;\n        }\n        i++;\n    }\n    return arr;\n}\nconsole.log(make_a_pile(3));"}
{"task_id": "HumanEval_128_prod_signs", "generation": "//You are given an array arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the array, represented by 1, -1 or 0.\n// Note: return undefined for empty arr.\n// Example:\n// >>> prod_signs([1, 2, 2, -4])\n// 9\n// >>> prod_signs([0, 1])\n// 0\n// >>> prod_signs([])\n// undefined\nfunction prod_signs(arr){\n\n    if(arr.length === 0) return undefined;\n    let sign = 1;\n    let sum = 0;\n    for(let i = 0; i < arr.length; i++){\n        if(arr[i] === 0) return 0;\n        if(arr[i] < 0) sign *= -1;\n        sum += Math.abs(arr[i]) * sign;\n    }\n    return sum;\n}", "prompt": "//You are given an array arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the array, represented by 1, -1 or 0.\n// Note: return undefined for empty arr.\n// Example:\n// >>> prod_signs([1, 2, 2, -4])\n// 9\n// >>> prod_signs([0, 1])\n// 0\n// >>> prod_signs([])\n// undefined\nfunction prod_signs(arr){\n", "wholecode": "//You are given an array arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the array, represented by 1, -1 or 0.\n// Note: return undefined for empty arr.\n// Example:\n// >>> prod_signs([1, 2, 2, -4])\n// 9\n// >>> prod_signs([0, 1])\n// 0\n// >>> prod_signs([])\n// undefined\nfunction prod_signs(arr){\n    if(arr.length === 0) return undefined;\n    let sign = 1;\n    let sum = 0;\n    for(let i = 0; i < arr.length; i++){\n        if(arr[i] === 0) return 0;\n        if(arr[i] < 0) sign *= -1;\n        sum += Math.abs(arr[i]) * sign;\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_114_minSubArraySum", "generation": "//Given an array of integers nums, find the minimum sum of any non-empty sub-array\n// of nums.\n// Example\n// >>> minSubArraySum([2, 3, 4, 1, 2, 4])\n// 1\n// >>> minSubArraySum([-1, -2, -3])\n// -6\nfunction minSubArraySum(nums){\n\n    let minSum = nums[0];\n    let currentSum = 0;\n    for(let i = 0; i < nums.length; i++){\n        currentSum += nums[i];\n        if(currentSum < minSum){\n            minSum = currentSum;\n        }\n        if(currentSum > 0){\n            currentSum = 0;\n        }\n    }\n    return minSum;\n}", "prompt": "//Given an array of integers nums, find the minimum sum of any non-empty sub-array\n// of nums.\n// Example\n// >>> minSubArraySum([2, 3, 4, 1, 2, 4])\n// 1\n// >>> minSubArraySum([-1, -2, -3])\n// -6\nfunction minSubArraySum(nums){\n", "wholecode": "//Given an array of integers nums, find the minimum sum of any non-empty sub-array\n// of nums.\n// Example\n// >>> minSubArraySum([2, 3, 4, 1, 2, 4])\n// 1\n// >>> minSubArraySum([-1, -2, -3])\n// -6\nfunction minSubArraySum(nums){\n    let minSum = nums[0];\n    let currentSum = 0;\n    for(let i = 0; i < nums.length; i++){\n        currentSum += nums[i];\n        if(currentSum < minSum){\n            minSum = currentSum;\n        }\n        if(currentSum > 0){\n            currentSum = 0;\n        }\n    }\n    return minSum;\n}"}
{"task_id": "HumanEval_15_string_sequence", "generation": "//Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunction string_sequence(n){\n\n    let str = \"\";\n    for(let i = 0; i <= n; i++){\n        str += i + \" \";\n    }\n    return str.trim();\n}", "prompt": "//Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunction string_sequence(n){\n", "wholecode": "//Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunction string_sequence(n){\n    let str = \"\";\n    for(let i = 0; i <= n; i++){\n        str += i + \" \";\n    }\n    return str.trim();\n}"}
{"task_id": "HumanEval_154_cycpattern_check", "generation": "//You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunction cycpattern_check(a, b){\n\n    let b1 = b + b;\n    return b1.includes(a);\n}", "prompt": "//You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunction cycpattern_check(a, b){\n", "wholecode": "//You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunction cycpattern_check(a, b){\n    let b1 = b + b;\n    return b1.includes(a);\n}"}
{"task_id": "HumanEval_57_monotonic", "generation": "//Return true is array elements are monotonically increasing or decreasing.\n// >>> monotonic([1, 2, 4, 20])\n// true\n// >>> monotonic([1, 20, 4, 10])\n// false\n// >>> monotonic([4, 1, 0, -10])\n// true\nfunction monotonic(l){\n\n    let isIncreasing = true;\n    let isDecreasing = true;\n    for(let i = 0; i < l.length - 1; i++){\n        if(l[i] > l[i + 1]){\n            isIncreasing = false;\n        }\n        if(l[i] < l[i + 1]){\n            isDecreasing = false;\n        }\n    }\n    return isIncreasing || isDecreasing;\n}", "prompt": "//Return true is array elements are monotonically increasing or decreasing.\n// >>> monotonic([1, 2, 4, 20])\n// true\n// >>> monotonic([1, 20, 4, 10])\n// false\n// >>> monotonic([4, 1, 0, -10])\n// true\nfunction monotonic(l){\n", "wholecode": "//Return true is array elements are monotonically increasing or decreasing.\n// >>> monotonic([1, 2, 4, 20])\n// true\n// >>> monotonic([1, 20, 4, 10])\n// false\n// >>> monotonic([4, 1, 0, -10])\n// true\nfunction monotonic(l){\n    let isIncreasing = true;\n    let isDecreasing = true;\n    for(let i = 0; i < l.length - 1; i++){\n        if(l[i] > l[i + 1]){\n            isIncreasing = false;\n        }\n        if(l[i] < l[i + 1]){\n            isDecreasing = false;\n        }\n    }\n    return isIncreasing || isDecreasing;\n}"}
{"task_id": "HumanEval_12_longest", "generation": "//Out of array of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return undefined in case the input array is empty.\n// >>> longest([])\n// undefined\n// >>> longest([\"a\", \"b\", \"c\"])\n// \"a\"\n// >>> longest([\"a\", \"bb\", \"ccc\"])\n// \"ccc\"\nfunction longest(strings){\n\n    if(strings.length === 0){\n        return undefined;\n    }\n    let longest = strings[0];\n    for(let i = 1; i < strings.length; i++){\n        if(strings[i].length > longest.length){\n            longest = strings[i];\n        }\n    }\n    return longest;\n}", "prompt": "//Out of array of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return undefined in case the input array is empty.\n// >>> longest([])\n// undefined\n// >>> longest([\"a\", \"b\", \"c\"])\n// \"a\"\n// >>> longest([\"a\", \"bb\", \"ccc\"])\n// \"ccc\"\nfunction longest(strings){\n", "wholecode": "//Out of array of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return undefined in case the input array is empty.\n// >>> longest([])\n// undefined\n// >>> longest([\"a\", \"b\", \"c\"])\n// \"a\"\n// >>> longest([\"a\", \"bb\", \"ccc\"])\n// \"ccc\"\nfunction longest(strings){\n    if(strings.length === 0){\n        return undefined;\n    }\n    let longest = strings[0];\n    for(let i = 1; i < strings.length; i++){\n        if(strings[i].length > longest.length){\n            longest = strings[i];\n        }\n    }\n    return longest;\n}"}
{"task_id": "HumanEval_52_below_threshold", "generation": "//Return true if all numbers in the array l are below threshold t.\n// >>> below_threshold([1, 2, 4, 10], 100)\n// true\n// >>> below_threshold([1, 20, 4, 10], 5)\n// false\nfunction below_threshold(l, t){\n\n    for (let i = 0; i < l.length; i++) {\n        if (l[i] >= t) {\n            return false;\n        }\n    }\n    return true;\n}", "prompt": "//Return true if all numbers in the array l are below threshold t.\n// >>> below_threshold([1, 2, 4, 10], 100)\n// true\n// >>> below_threshold([1, 20, 4, 10], 5)\n// false\nfunction below_threshold(l, t){\n", "wholecode": "//Return true if all numbers in the array l are below threshold t.\n// >>> below_threshold([1, 2, 4, 10], 100)\n// true\n// >>> below_threshold([1, 20, 4, 10], 5)\n// false\nfunction below_threshold(l, t){\n    for (let i = 0; i < l.length; i++) {\n        if (l[i] >= t) {\n            return false;\n        }\n    }\n    return true;\n}"}
{"task_id": "HumanEval_75_is_multiply_prime", "generation": "//Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunction is_multiply_prime(a){\n\n    let count = 0;\n    for(let i = 2; i < a; i++){\n        if(a % i === 0){\n            count++;\n        }\n    }\n    if(count === 2){\n        return true;\n    }else{\n        return false;\n    }\n}", "prompt": "//Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunction is_multiply_prime(a){\n", "wholecode": "//Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunction is_multiply_prime(a){\n    let count = 0;\n    for(let i = 2; i < a; i++){\n        if(a % i === 0){\n            count++;\n        }\n    }\n    if(count === 2){\n        return true;\n    }else{\n        return false;\n    }\n}\nconsole.log(is_multiply_prime(30));"}
{"task_id": "HumanEval_30_get_positive", "generation": "//Return only positive numbers in the array.\n// >>> get_positive([-1, 2, -4, 5, 6])\n// [2, 5, 6]\n// >>> get_positive([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, 2, 3, 9, 123, 1]\nfunction get_positive(l){\n\n    let positive = [];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] > 0){\n            positive.push(l[i]);\n        }\n    }\n    return positive;\n}\n", "prompt": "//Return only positive numbers in the array.\n// >>> get_positive([-1, 2, -4, 5, 6])\n// [2, 5, 6]\n// >>> get_positive([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, 2, 3, 9, 123, 1]\nfunction get_positive(l){\n", "wholecode": "//Return only positive numbers in the array.\n// >>> get_positive([-1, 2, -4, 5, 6])\n// [2, 5, 6]\n// >>> get_positive([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [5, 3, 2, 3, 9, 123, 1]\nfunction get_positive(l){\n    let positive = [];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] > 0){\n            positive.push(l[i]);\n        }\n    }\n    return positive;\n}\n\n//Return only negative numbers in the array.\n// >>> get_negative([-1, 2, -4, 5, 6])\n// [-1, -4]\n// >>> get_negative([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n// [-5, -3, -3, -10]\nfunction get_negative(l){\n    let negative = [];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] < 0){\n            negative.push(l[i]);\n        }\n    }\n    return negative;\n}\n\n//Return only even numbers in the array.\n// >>> get_even([1, 2, 3, 4, 5, 6, 7, 8, 9])\n// [2, 4, 6, 8]\n// >>> get_even([0, 2, 3, 4, 5, 6, 7, 8, 9])\n// [0, 2, 4, 6, 8]\nfunction get_even(l){\n    let even = [];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] % 2 === 0){\n            even.push(l[i]);\n        }\n    }\n    return even;\n}\n\n//Return only odd numbers in the array.\n// >>> get_odd([1, 2, 3, 4, 5, 6, 7, 8, 9])\n// [1, 3, 5, 7, 9]\n// >>> get_odd([0, 2, 3, 4, 5, 6, 7, 8, 9])\n// [1, 3, 5, 7, 9]\nfunction get_odd(l){\n    let odd = [];\n    for(let i = 0; i < l.length; i++){\n        if(l[i] % 2 !== 0){\n            odd.push(l[i]);\n        }\n    }\n    return odd;\n}\n\n//Return only numbers that are divisible by 3 in"}
{"task_id": "HumanEval_33_sort_third", "generation": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n// [2, 6, 3, 4, 8, 9, 5]\nfunction sort_third(l){\n\n  var l_prime = [];\n  for (var i = 0; i < l.length; i++){\n    if (i % 3 !== 0){\n      l_prime.push(l[i]);\n    }\n  }\n  l_prime.sort(function(a, b){return a - b});\n  return l_prime;\n}", "prompt": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n// [2, 6, 3, 4, 8, 9, 5]\nfunction sort_third(l){\n", "wholecode": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n// [2, 6, 3, 4, 8, 9, 5]\nfunction sort_third(l){\n  var l_prime = [];\n  for (var i = 0; i < l.length; i++){\n    if (i % 3 !== 0){\n      l_prime.push(l[i]);\n    }\n  }\n  l_prime.sort(function(a, b){return a - b});\n  return l_prime;\n}"}
{"task_id": "HumanEval_6_parse_nested_parens", "generation": "//Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// [2, 3, 1, 3]\nfunction parse_nested_parens(paren_string){\n\n  var result = [];\n  var current_level = 0;\n  var max_level = 0;\n  for (var i = 0; i < paren_string.length; i++) {\n    if (paren_string[i] === '(') {\n      current_level++;\n      if (current_level > max_level) {\n        max_level = current_level;\n      }\n    } else if (paren_string[i] === ')') {\n      current_level--;\n    }\n  }\n  result.push(max_level);\n  return result;\n}", "prompt": "//Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// [2, 3, 1, 3]\nfunction parse_nested_parens(paren_string){\n", "wholecode": "//Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// [2, 3, 1, 3]\nfunction parse_nested_parens(paren_string){\n  var result = [];\n  var current_level = 0;\n  var max_level = 0;\n  for (var i = 0; i < paren_string.length; i++) {\n    if (paren_string[i] === '(') {\n      current_level++;\n      if (current_level > max_level) {\n        max_level = current_level;\n      }\n    } else if (paren_string[i] === ')') {\n      current_level--;\n    }\n  }\n  result.push(max_level);\n  return result;\n}"}
{"task_id": "HumanEval_45_triangle_area", "generation": "//Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunction triangle_area(a, h){\n\n    return a * h / 2;\n}\n", "prompt": "//Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunction triangle_area(a, h){\n", "wholecode": "//Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunction triangle_area(a, h){\n    return a * h / 2;\n}\n\n//Given length of a side and high return area for a square.\n// >>> square_area(5)\n// 25\nfunction square_area(a){\n    return a * a;\n}\n\n//Given length of a side and high return area for a rectangle.\n// >>> rectangle_area(5, 3)\n// 15\nfunction rectangle_area(a, b){\n    return a * b;\n}\n\n//Given length of a side and high return area for a circle.\n// >>> circle_area(5)\n// 78.53981633974483\nfunction circle_area(a){\n    return Math.PI * a * a;\n}"}
{"task_id": "HumanEval_97_multiply", "generation": "//Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunction multiply(a, b){\n\n    let aUnit = a.toString().split('').pop();\n    let bUnit = b.toString().split('').pop();\n    return aUnit * bUnit;\n  }", "prompt": "//Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunction multiply(a, b){\n", "wholecode": "//Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunction multiply(a, b){\n    let aUnit = a.toString().split('').pop();\n    let bUnit = b.toString().split('').pop();\n    return aUnit * bUnit;\n  }"}
{"task_id": "HumanEval_4_mean_absolute_deviation", "generation": "//For a given array of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n// 1.0\nfunction mean_absolute_deviation(numbers){\n\n  let sum = 0;\n  let mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;\n  for (let i = 0; i < numbers.length; i++) {\n    sum += Math.abs(numbers[i] - mean);\n  }\n  return sum / numbers.length;\n}", "prompt": "//For a given array of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n// 1.0\nfunction mean_absolute_deviation(numbers){\n", "wholecode": "//For a given array of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n// 1.0\nfunction mean_absolute_deviation(numbers){\n  let sum = 0;\n  let mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;\n  for (let i = 0; i < numbers.length; i++) {\n    sum += Math.abs(numbers[i] - mean);\n  }\n  return sum / numbers.length;\n}"}
{"task_id": "HumanEval_58_common", "generation": "//Return sorted unique common elements for two arrays.\n// >>> common([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n// [1, 5, 653]\n// >>> common([5, 3, 2, 8], [3, 2])\n// [2, 3]\nfunction common(l1, l2){\n\n    let result = [];\n    for(let i = 0; i < l1.length; i++){\n        for(let j = 0; j < l2.length; j++){\n            if(l1[i] === l2[j]){\n                result.push(l1[i]);\n            }\n        }\n    }\n    return result;\n}", "prompt": "//Return sorted unique common elements for two arrays.\n// >>> common([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n// [1, 5, 653]\n// >>> common([5, 3, 2, 8], [3, 2])\n// [2, 3]\nfunction common(l1, l2){\n", "wholecode": "//Return sorted unique common elements for two arrays.\n// >>> common([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n// [1, 5, 653]\n// >>> common([5, 3, 2, 8], [3, 2])\n// [2, 3]\nfunction common(l1, l2){\n    let result = [];\n    for(let i = 0; i < l1.length; i++){\n        for(let j = 0; j < l2.length; j++){\n            if(l1[i] === l2[j]){\n                result.push(l1[i]);\n            }\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_156_int_to_mini_roman", "generation": "//Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunction int_to_mini_roman(number){\n\n    let roman = {\n        1: \"i\",\n        4: \"iv\",\n        5: \"v\",\n        9: \"ix\",\n        10: \"x\",\n        40: \"xl\",\n        50: \"l\",\n        90: \"xc\",\n        100: \"c\",\n        400: \"cd\",\n        500: \"d\",\n        900: \"cm\",\n        1000: \"m\"\n    }\n    let result = \"\";\n    let keys = Object.keys(roman).reverse();\n    for(let i = 0; i < keys.length; i++){\n        while(number >= keys[i]){\n            result += roman[keys[i]];\n            number -= keys[i];\n        }\n    }\n    return result;\n}", "prompt": "//Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunction int_to_mini_roman(number){\n", "wholecode": "//Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunction int_to_mini_roman(number){\n    let roman = {\n        1: \"i\",\n        4: \"iv\",\n        5: \"v\",\n        9: \"ix\",\n        10: \"x\",\n        40: \"xl\",\n        50: \"l\",\n        90: \"xc\",\n        100: \"c\",\n        400: \"cd\",\n        500: \"d\",\n        900: \"cm\",\n        1000: \"m\"\n    }\n    let result = \"\";\n    let keys = Object.keys(roman).reverse();\n    for(let i = 0; i < keys.length; i++){\n        while(number >= keys[i]){\n            result += roman[keys[i]];\n            number -= keys[i];\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_67_fruit_distribution", "generation": "//In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunction fruit_distribution(s, n){\n\n    let arr = s.split(' ')\n    let apples = arr[0].split(' ')\n    let oranges = arr[1].split(' ')\n    let total = apples[0] + oranges[0]\n    return n - total\n}", "prompt": "//In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunction fruit_distribution(s, n){\n", "wholecode": "//In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunction fruit_distribution(s, n){\n    let arr = s.split(' ')\n    let apples = arr[0].split(' ')\n    let oranges = arr[1].split(' ')\n    let total = apples[0] + oranges[0]\n    return n - total\n}"}
{"task_id": "HumanEval_112_reverse_delete", "generation": "//Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return an array containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// [\"bcd\", false]\n// >>> reverse_delete(\"abcdef\", \"b\")\n// [\"acdef\", false]\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// [\"cdedc\", true]\nfunction reverse_delete(s, c){\n\n    let result = s.split('').filter(x => !c.includes(x)).join('')\n    return [result, result === result.split('').reverse().join('')]\n}", "prompt": "//Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return an array containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// [\"bcd\", false]\n// >>> reverse_delete(\"abcdef\", \"b\")\n// [\"acdef\", false]\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// [\"cdedc\", true]\nfunction reverse_delete(s, c){\n", "wholecode": "//Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return an array containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// [\"bcd\", false]\n// >>> reverse_delete(\"abcdef\", \"b\")\n// [\"acdef\", false]\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// [\"cdedc\", true]\nfunction reverse_delete(s, c){\n    let result = s.split('').filter(x => !c.includes(x)).join('')\n    return [result, result === result.split('').reverse().join('')]\n}"}
{"task_id": "HumanEval_13_greatest_common_divisor", "generation": "//Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunction greatest_common_divisor(a, b){\n\n    if (a === 0) return b;\n    return greatest_common_divisor(b % a, a);\n}\n", "prompt": "//Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunction greatest_common_divisor(a, b){\n", "wholecode": "//Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunction greatest_common_divisor(a, b){\n    if (a === 0) return b;\n    return greatest_common_divisor(b % a, a);\n}\n\n//Return a least common multiple of two integers a and b\n// >>> least_common_multiple(3, 5)\n// 15\n// >>> least_common_multiple(25, 15)\n// 75\nfunction least_common_multiple(a, b){\n    return a * b / greatest_common_divisor(a, b);\n}\n\n//Return a number of days between two dates\n// >>> days_between(\"2016-01-01\", \"2016-01-02\")\n// 1\n// >>> days_between(\"2016-01-01\", \"2016-01-01\")\n// 0\nfunction days_between(date1, date2){\n    var date1 = new Date(date1);\n    var date2 = new Date(date2);\n    return Math.abs(date1 - date2) / (1000 * 60 * 60 * 24);\n}\n\n//Return a number of days between two dates\n// >>> days_between(\"2016-01-01\", \"2016-01-02\")\n// 1\n// >>> days_between(\"2016-01-01\", \"2016-01-01\")\n// 0\nfunction days_between(date1, date2){\n    var date1 = new Date(date1);\n    var date2 = new Date(date2);\n    return Math.abs(date1 - date2) / (1000 * 60 * 60 * 24);\n}"}
{"task_id": "HumanEval_125_split_words", "generation": "//Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words(\"Hello world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"Hello,world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"abcdef\")\n// 3\nfunction split_words(txt){\n\n    if(txt.includes(' ')){\n        return txt.split(' ')\n    }else if(txt.includes(',')){\n        return txt.split(',')\n    }else{\n        let count = 0\n        for(let i = 0; i < txt.length; i++){\n            if(txt[i].charCodeAt() % 2 === 1){\n                count++\n            }\n        }\n        return count\n    }\n}", "prompt": "//Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words(\"Hello world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"Hello,world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"abcdef\")\n// 3\nfunction split_words(txt){\n", "wholecode": "//Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words(\"Hello world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"Hello,world!\")\n// [\"Hello\", \"world!\"]\n// >>> split_words(\"abcdef\")\n// 3\nfunction split_words(txt){\n    if(txt.includes(' ')){\n        return txt.split(' ')\n    }else if(txt.includes(',')){\n        return txt.split(',')\n    }else{\n        let count = 0\n        for(let i = 0; i < txt.length; i++){\n            if(txt[i].charCodeAt() % 2 === 1){\n                count++\n            }\n        }\n        return count\n    }\n}"}
{"task_id": "HumanEval_116_sort_array", "generation": "//In this Kata, you have to sort an array of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([1, 5, 2, 3, 4])\n// [1, 2, 3, 4, 5]\n// >>> sort_array([-2, -3, -4, -5, -6])\n// [-6, -5, -4, -3, -2]\n// >>> sort_array([1, 0, 2, 3, 4])\n// [0, 1, 2, 3, 4]\nfunction sort_array(arr){\n\n    let ones = arr.map(x => x.toString(2).split('').filter(x => x === '1').length)\n    return arr.sort((a,b) => ones[a] - ones[b] || a - b)\n}", "prompt": "//In this Kata, you have to sort an array of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([1, 5, 2, 3, 4])\n// [1, 2, 3, 4, 5]\n// >>> sort_array([-2, -3, -4, -5, -6])\n// [-6, -5, -4, -3, -2]\n// >>> sort_array([1, 0, 2, 3, 4])\n// [0, 1, 2, 3, 4]\nfunction sort_array(arr){\n", "wholecode": "//In this Kata, you have to sort an array of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([1, 5, 2, 3, 4])\n// [1, 2, 3, 4, 5]\n// >>> sort_array([-2, -3, -4, -5, -6])\n// [-6, -5, -4, -3, -2]\n// >>> sort_array([1, 0, 2, 3, 4])\n// [0, 1, 2, 3, 4]\nfunction sort_array(arr){\n    let ones = arr.map(x => x.toString(2).split('').filter(x => x === '1').length)\n    return arr.sort((a,b) => ones[a] - ones[b] || a - b)\n}"}
{"task_id": "HumanEval_28_concatenate", "generation": "//Concatenate array of strings into a single string\n// >>> concatenate([])\n// \"\"\n// >>> concatenate([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction concatenate(strings){\n\n    let result = \"\";\n    for(let i = 0; i < strings.length; i++){\n        result += strings[i];\n    }\n    return result;\n}\n", "prompt": "//Concatenate array of strings into a single string\n// >>> concatenate([])\n// \"\"\n// >>> concatenate([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction concatenate(strings){\n", "wholecode": "//Concatenate array of strings into a single string\n// >>> concatenate([])\n// \"\"\n// >>> concatenate([\"a\", \"b\", \"c\"])\n// \"abc\"\nfunction concatenate(strings){\n    let result = \"\";\n    for(let i = 0; i < strings.length; i++){\n        result += strings[i];\n    }\n    return result;\n}\n\n// >>> reverse(\"abc\")\n// \"cba\"\nfunction reverse(str){\n    let result = \"\";\n    for(let i = str.length - 1; i >= 0; i--){\n        result += str[i];\n    }\n    return result;\n}\n\n// >>> isPalindrome(\"abcba\")\n// true\n// >>> isPalindrome(\"abc\")\n// false\nfunction isPalindrome(str){\n    let reversed = reverse(str);\n    return str === reversed;\n}\n\n// >>> isAnagram(\"abc\", \"cba\")\n// true\n// >>> isAnagram(\"abc\", \"cda\")\n// false\nfunction isAnagram(str1, str2){\n    let sorted1 = str1.split(\"\").sort().join(\"\");\n    let sorted2 = str2.split(\"\").sort().join(\"\");\n    return sorted1 === sorted2;\n}\n\n// >>> isAnagram(\"abc\", \"cba\")\n// true\n// >>> isAnagram(\"abc\", \"cda\")\n// false\nfunction isAnagram(str1, str2){\n    let sorted1 = str1.split(\"\").sort().join(\"\");\n    let sorted2 = str2.split(\"\").sort().join(\"\");\n    return sorted1 === sorted2;\n}\n\n// >>> isAnagram(\"abc\", \"cba\")\n// true\n// >>> isAnagram(\"abc\", \"cda\")\n// false\nfunction isAnagram(str1, str2){\n    let sorted1 = str1.split(\"\").sort().join(\"\");\n    let sorted2 = str2.split(\"\").sort().join(\"\");\n    return sorted1 === sorted2;\n}\n\n// >>> isAnagram(\"abc\", \"cba\")\n// true\n// >>> isAnagram(\"abc\", \"cda\")\n// false\nfunction isAnagram(str1, str2){\n    let sorted1 = str1.split(\"\").sort().join(\"\");\n    let sorted2 = str2.split(\"\").sort().join(\"\");\n    return sorted1 === sorted2;\n}\n\n// >>> isAnagram(\"abc\", \"cba\")\n// true\n// >>> isAnagram(\"abc\", \"cda\")\n// false\nfunction isAnagram(str1, str2){\n    let sorted1 = str1.split(\"\").sort().join(\"\");\n    let sorted2 = str2.split(\"\").sort().join(\"\");\n    return sorted1 === sorted2;\n}\n\n// >>> isAnagram(\"abc"}
{"task_id": "HumanEval_149_sorted_list_sum", "generation": "//Write a function that accepts an array of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted array with a sorted order,\n// The array is always an array of strings and never an array of numbers,\n// and it may contain duplicates.\n// The order of the array should be ascending by length of each word, and you\n// should return the array sorted by that rule.\n// If two words have the same length, sort the array alphabetically.\n// The function should return an array of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([\"aa\", \"a\", \"aaa\"])\n// [\"aa\"]\n// >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n// [\"ab\", \"cd\"]\nfunction sorted_list_sum(lst){\n\n    let sum = 0;\n    for (let i = 0; i < lst.length; i++) {\n        sum += lst[i].length;\n    }\n    return sum;\n}", "prompt": "//Write a function that accepts an array of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted array with a sorted order,\n// The array is always an array of strings and never an array of numbers,\n// and it may contain duplicates.\n// The order of the array should be ascending by length of each word, and you\n// should return the array sorted by that rule.\n// If two words have the same length, sort the array alphabetically.\n// The function should return an array of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([\"aa\", \"a\", \"aaa\"])\n// [\"aa\"]\n// >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n// [\"ab\", \"cd\"]\nfunction sorted_list_sum(lst){\n", "wholecode": "//Write a function that accepts an array of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted array with a sorted order,\n// The array is always an array of strings and never an array of numbers,\n// and it may contain duplicates.\n// The order of the array should be ascending by length of each word, and you\n// should return the array sorted by that rule.\n// If two words have the same length, sort the array alphabetically.\n// The function should return an array of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([\"aa\", \"a\", \"aaa\"])\n// [\"aa\"]\n// >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n// [\"ab\", \"cd\"]\nfunction sorted_list_sum(lst){\n    let sum = 0;\n    for (let i = 0; i < lst.length; i++) {\n        sum += lst[i].length;\n    }\n    return sum;\n}\nconsole.log(sorted_list_sum([\"aa\", \"a\", \"aaa\"])); // 4\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\"])); // 10\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 16\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 20\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 24\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 28\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 32\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 36\nconsole.log(sorted_list_sum([\"ab\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\", \"a\", \"aaa\", \"cd\"])); // 40\nconsole.log(sorted_list_sum([\""}
{"task_id": "HumanEval_7_filter_by_substring", "generation": "//Filter an input array of strings only for ones that contain given substring\n// >>> filter_by_substring([], \"a\")\n// []\n// >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"bacd\", \"array\"]\nfunction filter_by_substring(strings, substring){\n\n    return strings.filter((string) => string.includes(substring));\n}", "prompt": "//Filter an input array of strings only for ones that contain given substring\n// >>> filter_by_substring([], \"a\")\n// []\n// >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"bacd\", \"array\"]\nfunction filter_by_substring(strings, substring){\n", "wholecode": "//Filter an input array of strings only for ones that contain given substring\n// >>> filter_by_substring([], \"a\")\n// []\n// >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n// [\"abc\", \"bacd\", \"array\"]\nfunction filter_by_substring(strings, substring){\n    return strings.filter((string) => string.includes(substring));\n}"}
{"task_id": "HumanEval_99_closest_integer", "generation": "//Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunction closest_integer(value){\n\n    let num = Number(value);\n    let num1 = Math.floor(num);\n    let num2 = Math.ceil(num);\n    if (num - num1 > num2 - num){\n        return num2;\n    } else {\n        return num1;\n    }\n}\n", "prompt": "//Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunction closest_integer(value){\n", "wholecode": "//Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunction closest_integer(value){\n    let num = Number(value);\n    let num1 = Math.floor(num);\n    let num2 = Math.ceil(num);\n    if (num - num1 > num2 - num){\n        return num2;\n    } else {\n        return num1;\n    }\n}\n\nconsole.log(closest_integer(\"10\"));\nconsole.log(closest_integer(\"15.3\"));\nconsole.log(closest_integer(\"-14.5\"));"}
{"task_id": "HumanEval_64_vowels_count", "generation": "//Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunction vowels_count(s){\n\n    let count = 0;\n    for(let i = 0; i < s.length; i++){\n        if(s[i] === 'a' || s[i] === 'e' || s[i] === 'i' || s[i] === 'o' || s[i] === 'u'){\n            count++;\n        }\n    }\n    if(s[s.length - 1] === 'y'){\n        count++;\n    }\n    return count;\n}", "prompt": "//Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunction vowels_count(s){\n", "wholecode": "//Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunction vowels_count(s){\n    let count = 0;\n    for(let i = 0; i < s.length; i++){\n        if(s[i] === 'a' || s[i] === 'e' || s[i] === 'i' || s[i] === 'o' || s[i] === 'u'){\n            count++;\n        }\n    }\n    if(s[s.length - 1] === 'y'){\n        count++;\n    }\n    return count;\n}"}
{"task_id": "HumanEval_158_find_max", "generation": "//Write a function that accepts an array of strings.\n// The array contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([\"name\", \"of\", \"string\"])\n// \"string\"\n// >>> find_max([\"name\", \"enam\", \"game\"])\n// \"enam\"\n// >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n// \"aaaaaaa\"\nfunction find_max(words){\n\n    let max = 0;\n    let maxWord = \"\";\n    for(let i = 0; i < words.length; i++){\n        let unique = new Set(words[i]).size;\n        if(unique > max){\n            max = unique;\n            maxWord = words[i];\n        }\n    }\n    return maxWord;\n}", "prompt": "//Write a function that accepts an array of strings.\n// The array contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([\"name\", \"of\", \"string\"])\n// \"string\"\n// >>> find_max([\"name\", \"enam\", \"game\"])\n// \"enam\"\n// >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n// \"aaaaaaa\"\nfunction find_max(words){\n", "wholecode": "//Write a function that accepts an array of strings.\n// The array contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([\"name\", \"of\", \"string\"])\n// \"string\"\n// >>> find_max([\"name\", \"enam\", \"game\"])\n// \"enam\"\n// >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n// \"aaaaaaa\"\nfunction find_max(words){\n    let max = 0;\n    let maxWord = \"\";\n    for(let i = 0; i < words.length; i++){\n        let unique = new Set(words[i]).size;\n        if(unique > max){\n            max = unique;\n            maxWord = words[i];\n        }\n    }\n    return maxWord;\n}"}
{"task_id": "HumanEval_162_string_to_md5", "generation": "//Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_md5(\"Hello world\")\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunction string_to_md5(text){\n\n    if(text === \"\"){\n        return undefined;\n    }\n    return md5(text);\n}", "prompt": "//Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_md5(\"Hello world\")\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunction string_to_md5(text){\n", "wholecode": "//Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return undefined.\n// >>> string_to_md5(\"Hello world\")\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunction string_to_md5(text){\n    if(text === \"\"){\n        return undefined;\n    }\n    return md5(text);\n}"}
{"task_id": "HumanEval_44_change_base", "generation": "//Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunction change_base(x, base){\n\n    let result = \"\";\n    while (x > 0) {\n        result = (x % base) + result;\n        x = Math.floor(x / base);\n    }\n    return result;\n}\n", "prompt": "//Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunction change_base(x, base){\n", "wholecode": "//Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunction change_base(x, base){\n    let result = \"\";\n    while (x > 0) {\n        result = (x % base) + result;\n        x = Math.floor(x / base);\n    }\n    return result;\n}\n\n// console.log(change_base(8, 3));\n// console.log(change_base(8, 2));\n// console.log(change_base(7, 2));\n\n//Given a string of digits, you should replace any digit below 5 with '0' and any digit 5 and above with '1'. Return the resulting string.\n// >>> fake_bin('45385593107843568')\n// \"01011110001100111\"\n// >>> fake_bin('509321967506747')\n// \"10100011110110111\"\n// >>> fake_bin('366058562030849490134388085')\n// \"01101111000010101000001101111\"\nfunction fake_bin(x){\n    let result = \"\";\n    for (let i = 0; i < x.length; i++) {\n        if (x[i] < 5) {\n            result += \"0\";\n        } else {\n            result += \"1\";\n        }\n    }\n    return result;\n}\n\n// console.log(fake_bin('45385593107843568'));\n// console.log(fake_bin('509321967506747'));\n// console.log(fake_bin('366058562030849490134388085'));\n\n//Given a string of digits, you should replace any digit below 5 with '0' and any digit 5 and above with '1'. Return the resulting string.\n// >>> fake_bin('45385593107843568')\n// \"01011110001100111\"\n// >>> fake_bin('509321967506747')\n"}
{"task_id": "HumanEval_157_right_angle_triangle", "generation": "//Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunction right_angle_triangle(a, b, c){\n\n    if(a**2 + b**2 === c**2 || a**2 + c**2 === b**2 || b**2 + c**2 === a**2){\n        return true\n    }else{\n        return false\n    }\n}", "prompt": "//Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunction right_angle_triangle(a, b, c){\n", "wholecode": "//Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunction right_angle_triangle(a, b, c){\n    if(a**2 + b**2 === c**2 || a**2 + c**2 === b**2 || b**2 + c**2 === a**2){\n        return true\n    }else{\n        return false\n    }\n}"}
{"task_id": "HumanEval_81_numerical_letter_grade", "generation": "//It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you an array of GPAs for some students and you have to write \n// a function that can output an array of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([4.0, 3, 1.7, 2, 3.5])\n// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunction numerical_letter_grade(grades){\n\n    let letterGrades = [];\n    for(let i = 0; i < grades.length; i++){\n        if(grades[i] >= 4.0){\n            letterGrades.push(\"A+\");\n        }else if(grades[i] > 3.7){\n            letterGrades.push(\"A\");\n        }else if(grades[i] > 3.3){\n            letterGrades.push(\"A-\");\n        }else if(grades[i] > 3.0){\n            letterGrades.push(\"B+\");\n        }else if(grades[i] > 2.7){\n            letterGrades.push(\"B\");\n        }else if(grades[i] > 2.3){\n            letterGrades.push(\"B-\");\n        }else if(grades[i] > 2.0){\n            letterGrades.push(\"C+\");\n        }else if(grades[i] > 1.7){\n            letterGrades.push(\"C\");\n        }else if(grades[i] > 1.3){\n            letterGrades.push(\"C-\");\n        }else if(grades[i] > 1.0){\n            letterGrades.push(\"D+\");\n        }else if(grades[i] > 0.7){\n            letterGrades.push(\"D\");\n        }else if(grades[i] > 0.0){\n            letterGrades.push(\"D-\");\n        }else{\n            letterGrades.push(\"E\");\n        }\n    }\n    return letterGrades;\n}", "prompt": "//It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you an array of GPAs for some students and you have to write \n// a function that can output an array of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([4.0, 3, 1.7, 2, 3.5])\n// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunction numerical_letter_grade(grades){\n", "wholecode": "//It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you an array of GPAs for some students and you have to write \n// a function that can output an array of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([4.0, 3, 1.7, 2, 3.5])\n// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunction numerical_letter_grade(grades){\n    let letterGrades = [];\n    for(let i = 0; i < grades.length; i++){\n        if(grades[i] >= 4.0){\n            letterGrades.push(\"A+\");\n        }else if(grades[i] > 3.7){\n            letterGrades.push(\"A\");\n        }else if(grades[i] > 3.3){\n            letterGrades.push(\"A-\");\n        }else if(grades[i] > 3.0){\n            letterGrades.push(\"B+\");\n        }else if(grades[i] > 2.7){\n            letterGrades.push(\"B\");\n        }else if(grades[i] > 2.3){\n            letterGrades.push(\"B-\");\n        }else if(grades[i] > 2.0){\n            letterGrades.push(\"C+\");\n        }else if(grades[i] > 1.7){\n            letterGrades.push(\"C\");\n        }else if(grades[i] > 1.3){\n            letterGrades.push(\"C-\");\n        }else if(grades[i] > 1.0){\n            letterGrades.push(\"D+\");\n        }else if(grades[i] > 0.7){\n            letterGrades.push(\"D\");\n        }else if(grades[i] > 0.0){\n            letterGrades.push(\"D-\");\n        }else{\n            letterGrades.push(\"E\");\n        }\n    }\n    return letterGrades;\n}"}
{"task_id": "HumanEval_5_intersperse", "generation": "//Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n// >>> intersperse([], 4)\n// []\n// >>> intersperse([1, 2, 3], 4)\n// [1, 4, 2, 4, 3]\nfunction intersperse(numbers, delimeter){\n\n  if(numbers.length === 0){\n    return [];\n  }\n  let result = [];\n  for(let i = 0; i < numbers.length; i++){\n    result.push(numbers[i]);\n    if(i < numbers.length - 1){\n      result.push(delimeter);\n    }\n  }\n  return result;\n}", "prompt": "//Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n// >>> intersperse([], 4)\n// []\n// >>> intersperse([1, 2, 3], 4)\n// [1, 4, 2, 4, 3]\nfunction intersperse(numbers, delimeter){\n", "wholecode": "//Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n// >>> intersperse([], 4)\n// []\n// >>> intersperse([1, 2, 3], 4)\n// [1, 4, 2, 4, 3]\nfunction intersperse(numbers, delimeter){\n  if(numbers.length === 0){\n    return [];\n  }\n  let result = [];\n  for(let i = 0; i < numbers.length; i++){\n    result.push(numbers[i]);\n    if(i < numbers.length - 1){\n      result.push(delimeter);\n    }\n  }\n  return result;\n}"}
{"task_id": "HumanEval_146_specialFilter", "generation": "//Write a function that takes an array of numbers as input and returns \n// the number of elements in the array that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([15, -73, 14, -15])\n// 1\n// >>> specialFilter([33, -2, -3, 45, 21, 109])\n// 2\nfunction specialFilter(nums){\n\n    let count = 0;\n    for(let i = 0; i < nums.length; i++){\n        let num = nums[i];\n        let firstDigit = Math.abs(num).toString().charAt(0);\n        let lastDigit = Math.abs(num).toString().charAt(Math.abs(num).toString().length - 1);\n        if(num > 10 && firstDigit % 2 !== 0 && lastDigit % 2 !== 0){\n            count++;\n        }\n    }\n    return count;\n}", "prompt": "//Write a function that takes an array of numbers as input and returns \n// the number of elements in the array that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([15, -73, 14, -15])\n// 1\n// >>> specialFilter([33, -2, -3, 45, 21, 109])\n// 2\nfunction specialFilter(nums){\n", "wholecode": "//Write a function that takes an array of numbers as input and returns \n// the number of elements in the array that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([15, -73, 14, -15])\n// 1\n// >>> specialFilter([33, -2, -3, 45, 21, 109])\n// 2\nfunction specialFilter(nums){\n    let count = 0;\n    for(let i = 0; i < nums.length; i++){\n        let num = nums[i];\n        let firstDigit = Math.abs(num).toString().charAt(0);\n        let lastDigit = Math.abs(num).toString().charAt(Math.abs(num).toString().length - 1);\n        if(num > 10 && firstDigit % 2 !== 0 && lastDigit % 2 !== 0){\n            count++;\n        }\n    }\n    return count;\n}"}
{"task_id": "HumanEval_60_sum_to_n", "generation": "//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n){\n\n    let sum = 0;\n    for(let i = 1; i <= n; i++){\n        sum += i;\n    }\n    return sum;\n}\n", "prompt": "//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n){\n", "wholecode": "//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n){\n    let sum = 0;\n    for(let i = 1; i <= n; i++){\n        sum += i;\n    }\n    return sum;\n}\n\n//sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunction sum_to_n(n){\n    let sum = 0;\n    for(let i = 1; i <= n; i++){\n        sum += i;\n    }\n    return sum;\n}"}
{"task_id": "HumanEval_26_remove_duplicates", "generation": "//From an array of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([1, 2, 3, 2, 4])\n// [1, 3, 4]\nfunction remove_duplicates(numbers){\n\n    let result = [];\n    for (let i = 0; i < numbers.length; i++) {\n        if (numbers.indexOf(numbers[i]) === numbers.lastIndexOf(numbers[i])) {\n            result.push(numbers[i]);\n        }\n    }\n    return result;\n}", "prompt": "//From an array of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([1, 2, 3, 2, 4])\n// [1, 3, 4]\nfunction remove_duplicates(numbers){\n", "wholecode": "//From an array of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([1, 2, 3, 2, 4])\n// [1, 3, 4]\nfunction remove_duplicates(numbers){\n    let result = [];\n    for (let i = 0; i < numbers.length; i++) {\n        if (numbers.indexOf(numbers[i]) === numbers.lastIndexOf(numbers[i])) {\n            result.push(numbers[i]);\n        }\n    }\n    return result;\n}"}
{"task_id": "HumanEval_163_generate_integers", "generation": "//Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// [2, 4, 6, 8]\n// >>> generate_integers(8, 2)\n// [2, 4, 6, 8]\n// >>> generate_integers(10, 14)\n// []\nfunction generate_integers(a, b){\n\n    let arr = []\n    for(let i = a; i <= b; i++){\n        if(i % 2 === 0){\n            arr.push(i)\n        }\n    }\n    return arr\n}", "prompt": "//Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// [2, 4, 6, 8]\n// >>> generate_integers(8, 2)\n// [2, 4, 6, 8]\n// >>> generate_integers(10, 14)\n// []\nfunction generate_integers(a, b){\n", "wholecode": "//Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// [2, 4, 6, 8]\n// >>> generate_integers(8, 2)\n// [2, 4, 6, 8]\n// >>> generate_integers(10, 14)\n// []\nfunction generate_integers(a, b){\n    let arr = []\n    for(let i = a; i <= b; i++){\n        if(i % 2 === 0){\n            arr.push(i)\n        }\n    }\n    return arr\n}\nconsole.log(generate_integers(2, 8))\nconsole.log(generate_integers(8, 2))\nconsole.log(generate_integers(10, 14))"}
{"task_id": "HumanEval_9_rolling_max", "generation": "//From a given array of integers, generate an array of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([1, 2, 3, 2, 3, 4, 2])\n// [1, 2, 3, 3, 3, 4, 4]\nfunction rolling_max(numbers){\n\n    let max = numbers[0];\n    let result = [max];\n    for(let i = 1; i < numbers.length; i++){\n        if(numbers[i] > max){\n            max = numbers[i];\n        }\n        result.push(max);\n    }\n    return result;\n}", "prompt": "//From a given array of integers, generate an array of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([1, 2, 3, 2, 3, 4, 2])\n// [1, 2, 3, 3, 3, 4, 4]\nfunction rolling_max(numbers){\n", "wholecode": "//From a given array of integers, generate an array of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([1, 2, 3, 2, 3, 4, 2])\n// [1, 2, 3, 3, 3, 4, 4]\nfunction rolling_max(numbers){\n    let max = numbers[0];\n    let result = [max];\n    for(let i = 1; i < numbers.length; i++){\n        if(numbers[i] > max){\n            max = numbers[i];\n        }\n        result.push(max);\n    }\n    return result;\n}"}
{"task_id": "HumanEval_3_below_zero", "generation": "//You're given an array of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([1, 2, 3])\n// false\n// >>> below_zero([1, 2, -4, 5])\n// true\nfunction below_zero(operations){\n\n    let balance = 0;\n    for (let i = 0; i < operations.length; i++) {\n        if (operations[i] > 0) {\n            balance += operations[i];\n        } else {\n            balance += operations[i];\n            if (balance < 0) {\n                return true;\n            }\n        }\n    }\n    return false;\n}", "prompt": "//You're given an array of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([1, 2, 3])\n// false\n// >>> below_zero([1, 2, -4, 5])\n// true\nfunction below_zero(operations){\n", "wholecode": "//You're given an array of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([1, 2, 3])\n// false\n// >>> below_zero([1, 2, -4, 5])\n// true\nfunction below_zero(operations){\n    let balance = 0;\n    for (let i = 0; i < operations.length; i++) {\n        if (operations[i] > 0) {\n            balance += operations[i];\n        } else {\n            balance += operations[i];\n            if (balance < 0) {\n                return true;\n            }\n        }\n    }\n    return false;\n}"}
{"task_id": "HumanEval_69_search", "generation": "//You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the array.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([4, 1, 2, 2, 3, 1])\n// 2\n// >>> search([1, 2, 2, 3, 3, 3, 4, 4, 4])\n// 3\n// >>> search([5, 5, 4, 4, 4])\n// -1\nfunction search(lst){\n\n    let obj = {}\n    for(let i = 0; i < lst.length; i++){\n        if(obj[lst[i]]){\n            obj[lst[i]] += 1\n        }else{\n            obj[lst[i]] = 1\n        }\n    }\n    let arr = Object.entries(obj)\n    let arr2 = arr.filter((x) => x[1] >= x[0])\n    if(arr2.length === 0){\n        return -1\n    }else{\n        return arr2.sort((a,b) => a[1] - b[1])[arr2.length - 1][0]\n    }\n}", "prompt": "//You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the array.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([4, 1, 2, 2, 3, 1])\n// 2\n// >>> search([1, 2, 2, 3, 3, 3, 4, 4, 4])\n// 3\n// >>> search([5, 5, 4, 4, 4])\n// -1\nfunction search(lst){\n", "wholecode": "//You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the array.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([4, 1, 2, 2, 3, 1])\n// 2\n// >>> search([1, 2, 2, 3, 3, 3, 4, 4, 4])\n// 3\n// >>> search([5, 5, 4, 4, 4])\n// -1\nfunction search(lst){\n    let obj = {}\n    for(let i = 0; i < lst.length; i++){\n        if(obj[lst[i]]){\n            obj[lst[i]] += 1\n        }else{\n            obj[lst[i]] = 1\n        }\n    }\n    let arr = Object.entries(obj)\n    let arr2 = arr.filter((x) => x[1] >= x[0])\n    if(arr2.length === 0){\n        return -1\n    }else{\n        return arr2.sort((a,b) => a[1] - b[1])[arr2.length - 1][0]\n    }\n}"}
{"task_id": "HumanEval_61_correct_bracketing", "generation": "//brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunction correct_bracketing(brackets){\n\n    let count = 0;\n    for(let i = 0; i < brackets.length; i++){\n        if(brackets[i] === \"(\"){\n            count++;\n        }else if(brackets[i] === \")\"){\n            count--;\n        }\n        if(count < 0){\n            return false;\n        }\n    }\n    return count === 0;\n}", "prompt": "//brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunction correct_bracketing(brackets){\n", "wholecode": "//brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunction correct_bracketing(brackets){\n    let count = 0;\n    for(let i = 0; i < brackets.length; i++){\n        if(brackets[i] === \"(\"){\n            count++;\n        }else if(brackets[i] === \")\"){\n            count--;\n        }\n        if(count < 0){\n            return false;\n        }\n    }\n    return count === 0;\n}"}
{"task_id": "HumanEval_37_sort_even", "generation": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_even([5, 6, 3, 4])\n// [3, 6, 5, 4]\nfunction sort_even(l){\n\n  var l2 = [];\n  for (var i = 0; i < l.length; i++) {\n    if (i % 2 === 0) {\n      l2.push(l[i]);\n    }\n  }\n  l2.sort(function(a, b) {\n    return a - b;\n  });\n  for (var i = 0; i < l.length; i++) {\n    if (i % 2 === 0) {\n      l[i] = l2[i/2];\n    }\n  }\n  return l;\n}", "prompt": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_even([5, 6, 3, 4])\n// [3, 6, 5, 4]\nfunction sort_even(l){\n", "wholecode": "//This function takes an array l and returns an array l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([1, 2, 3])\n// [1, 2, 3]\n// >>> sort_even([5, 6, 3, 4])\n// [3, 6, 5, 4]\nfunction sort_even(l){\n  var l2 = [];\n  for (var i = 0; i < l.length; i++) {\n    if (i % 2 === 0) {\n      l2.push(l[i]);\n    }\n  }\n  l2.sort(function(a, b) {\n    return a - b;\n  });\n  for (var i = 0; i < l.length; i++) {\n    if (i % 2 === 0) {\n      l[i] = l2[i/2];\n    }\n  }\n  return l;\n}"}
{"task_id": "HumanEval_54_same_chars", "generation": "//Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunction same_chars(s0, s1){\n\n    let s0Arr = s0.split('');\n    let s1Arr = s1.split('');\n    let s0Set = new Set(s0Arr);\n    let s1Set = new Set(s1Arr);\n    if (s0Set.size === s1Set.size){\n        return true;\n    } else {\n        return false;\n    }\n}\n", "prompt": "//Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunction same_chars(s0, s1){\n", "wholecode": "//Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunction same_chars(s0, s1){\n    let s0Arr = s0.split('');\n    let s1Arr = s1.split('');\n    let s0Set = new Set(s0Arr);\n    let s1Set = new Set(s1Arr);\n    if (s0Set.size === s1Set.size){\n        return true;\n    } else {\n        return false;\n    }\n}\n\nconsole.log(same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\"));\nconsole.log(same_chars(\"abcd\", \"dddddddabc\"));\nconsole.log(same_chars(\"dddddddabc\", \"abcd\"));\nconsole.log(same_chars(\"eabcd\", \"dddddddabc\"));\nconsole.log(same_chars(\"abcd\", \"dddddddabce\"));\nconsole.log(same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\"));"}
{"task_id": "HumanEval_56_correct_bracketing", "generation": "//brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunction correct_bracketing(brackets){\n\n    let count = 0;\n    for(let i = 0; i < brackets.length; i++){\n        if(brackets[i] === \"<\"){\n            count++;\n        }else if(brackets[i] === \">\"){\n            count--;\n        }\n        if(count < 0){\n            return false;\n        }\n    }\n    return count === 0;\n}", "prompt": "//brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunction correct_bracketing(brackets){\n", "wholecode": "//brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunction correct_bracketing(brackets){\n    let count = 0;\n    for(let i = 0; i < brackets.length; i++){\n        if(brackets[i] === \"<\"){\n            count++;\n        }else if(brackets[i] === \">\"){\n            count--;\n        }\n        if(count < 0){\n            return false;\n        }\n    }\n    return count === 0;\n}"}
