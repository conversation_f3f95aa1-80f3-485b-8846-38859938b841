{"date": "2024-09-24 21:12", "hash": "ebe95df0d5220f00cedd6f4bddea35e4", "eval": {"Mbpp/56": [{"task_id": "Mbpp/56", "solution": "\"\"\"\nWrite a python function to check if a given number is one less than twice its reverse.\nassert check(70) == False\n\"\"\"\n\"\"\"\nWrite a python function to check if a given number is one less than twice its reverse.\nassert check(70) == False\n\"\"\"\ndef check(n):\n    rev = 0\n    temp = n\n    while temp > 0:\n        rev = rev * 10 + temp % 10\n        temp = temp // 10\n    return n == 2 * rev - 1", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/69": [{"task_id": "Mbpp/69", "solution": "\"\"\"\nWrite a function to check whether a list contains the given sublist or not.\nassert is_sublist([2,4,3,5,7],[3,7])==False\n\"\"\"\n\"\"\"\nWrite a function to check whether a list contains the given sublist or not.\nassert is_sublist([2,4,3,5,7],[3,7])==False\n\"\"\"\ndef is_sublist(list1, list2):\n    if list1 == list2:\n        return True\n    elif list1[0] == list2[0]:\n        return is_sublist(list1[1:], list2[1:])\n    else:\n        return False\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[[2, 4, 3, 5, 7], [4, 3]]], "plus_fail_tests": [[[], [1]]]}], "Mbpp/8": [{"task_id": "Mbpp/8", "solution": "\"\"\"\nWrite a function to find squares of individual elements in a list.\nassert square_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n\"\"\"\n\"\"\"\nWrite a function to find squares of individual elements in a list.\nassert square_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n\"\"\"\ndef square_nums(nums):\n    return [num**2 for num in nums]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/4": [{"task_id": "Mbpp/4", "solution": "\"\"\"\nWrite a function to find the n largest integers from a given list of numbers, returned in descending order.\nassert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],3)==[85, 75, 65]\n\"\"\"\n\"\"\"\nWrite a function to find the n largest integers from a given list of numbers, returned in descending order.\nassert heap_queue_largest( [25, 35, 22, 85, 14, 65, 75, 22, 58],3)==[85, 75, 65]\n\"\"\"\nimport heapq\ndef heap_queue_largest(nums, n):\n    return heapq.nlargest(n, nums)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/16": [{"task_id": "Mbpp/16", "solution": "\"\"\"\nWrite a function to that returns true if the input string contains sequences of lowercase letters joined with an underscore and false otherwise.\nassert text_lowercase_underscore(\"aab_cbbbc\")==(True)\n\"\"\"\n\"\"\"\nWrite a function to that returns true if the input string contains sequences of lowercase letters joined with an underscore and false otherwise.\nassert text_lowercase_underscore(\"aab_cbbbc\")==(True)\n\"\"\"\ndef text_lowercase_underscore(text):\n    return text.islower() and '_' in text\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["_abc"]]}], "Mbpp/58": [{"task_id": "Mbpp/58", "solution": "\"\"\"\nWrite a python function to check whether the given two integers have opposite sign or not.\nassert opposite_Signs(1,-2) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether the given two integers have opposite sign or not.\nassert opposite_Signs(1,-2) == True\n\"\"\"\ndef opposite_Signs(a,b):\n    if a*b<0:\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/70": [{"task_id": "Mbpp/70", "solution": "\"\"\"\nWrite a function to find whether all the given tuples have equal length or not.\nassert get_equal([(11, 22, 33), (44, 55, 66)]) == True\n\"\"\"\n\"\"\"\nWrite a function to find whether all the given tuples have equal length or not.\nassert get_equal([(11, 22, 33), (44, 55, 66)]) == True\n\"\"\"\nfrom typing import List, <PERSON><PERSON>\n\n\ndef get_equal(tuples: List[Tuple]) -> bool:\n    return all(len(t) == len(tuples[0]) for t in tuples)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/9": [{"task_id": "Mbpp/9", "solution": "\"\"\"\nWrite a python function to find the minimum number of rotations (greater than 0) required to get the same string.\nassert find_Rotations(\"aaaa\") == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the minimum number of rotations (greater than 0) required to get the same string.\nassert find_Rotations(\"aaaa\") == 1\n\"\"\"\ndef find_Rotations(string):\n    if string == string[::-1]:\n        return 0\n    for i in range(len(string)):\n        if string[i:] + string[:i] == string:\n            return i\n    return 0\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/20": [{"task_id": "Mbpp/20", "solution": "\"\"\"\nWrite a function to check if the given number is woodball or not.\nassert is_woodall(383) == True\n\"\"\"\n\"\"\"\nWrite a function to check if the given number is woodball or not.\nassert is_woodall(383) == True\n\"\"\"\ndef is_woodall(n):\n    if n == 1:\n        return True\n    if n < 1:\n        return False\n    if n % 2 == 0:\n        return False\n    if n % 3 == 0:\n        return False\n    if n % 5 == 0:\n        return False\n    if n % 7 == 0:\n        return False\n    if n % 11 == 0:\n        return False\n    if n % 13 == 0:\n        return False\n    if n % 17 == 0:\n        return False\n    if n % 19 == 0:\n        return False\n    if n % 23 == 0:\n        return False\n    if n % 29 == 0:\n        return False\n    if n % 31 == 0:\n        return False\n    if n % 37 == 0:\n        return False\n    if n % 41 == 0:\n        return False\n    if n % 43 == 0:\n        return False\n    if n % 47 == 0:\n        return False\n    if n % 53 == 0:\n        return False\n    if n % 59 == 0:\n        return False\n    if n % 61 == 0:\n        return False\n    if n % 67 == 0:\n        return False\n    if n % 71 == 0:\n        return False\n    if n % 73 == 0:\n        return False\n    if n % 79 == 0:\n        return False\n    if n % 83 == 0:\n        return False\n    if n % 89 == 0:\n        return False\n    if n % 97 == 0:\n        return False\n    return True", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[123456791]]}], "Mbpp/11": [{"task_id": "Mbpp/11", "solution": "\"\"\"\nWrite a python function to remove first and last occurrence of a given character from the string.\nassert remove_Occ(\"hello\",\"l\") == \"heo\"\n\"\"\"\n\"\"\"\nWrite a python function to remove first and last occurrence of a given character from the string.\nassert remove_Occ(\"hello\",\"l\") == \"heo\"\n\"\"\"\ndef remove_Occ(str1, char):\n    str1 = str1.replace(char, \"\", 1)\n    str1 = str1.replace(char, \"\", 1)\n    return str1\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["waaaorlda", "a"]]}], "Mbpp/6": [{"task_id": "Mbpp/6", "solution": "\"\"\"\nWrite a python function to check whether the two numbers differ at one bit position only or not.\nassert differ_At_One_Bit_Pos(13,9) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether the two numbers differ at one bit position only or not.\nassert differ_At_One_Bit_Pos(13,9) == True\n\"\"\"\ndef differ_At_One_Bit_Pos(num1,num2):\n    count=0\n    while num1>0 and num2>0:\n        if num1%2!=num2%2:\n            count+=1\n        num1=num1//2\n        num2=num2//2\n    if count==1:\n        return True\n    else:\n        return False", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[2, 4]], "plus_fail_tests": [[0, 1]]}], "Mbpp/12": [{"task_id": "Mbpp/12", "solution": "\"\"\"\nWrite a function to sort a given matrix in ascending order according to the sum of its rows.\nassert sort_matrix([[1, 2, 3], [2, 4, 5], [1, 1, 1]])==[[1, 1, 1], [1, 2, 3], [2, 4, 5]]\n\"\"\"\n\"\"\"\nWrite a function to sort a given matrix in ascending order according to the sum of its rows.\nassert sort_matrix([[1, 2, 3], [2, 4, 5], [1, 1, 1]])==[[1, 1, 1], [1, 2, 3], [2, 4, 5]]\n\"\"\"\ndef sort_matrix(matrix):\n    return sorted(matrix, key=sum)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/59": [{"task_id": "Mbpp/59", "solution": "\"\"\"\nWrite a function to find the nth octagonal number.\nassert is_octagonal(5) == 65\n\"\"\"\n\"\"\"\nWrite a function to find the nth octagonal number.\nassert is_octagonal(5) == 65\n\"\"\"\ndef is_octagonal(n):\n    return (1 + (1 + 4 * n) ** 0.5) % 2 == 0", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/99": [{"task_id": "Mbpp/99", "solution": "\"\"\"\nWrite a function to convert the given decimal number to its binary equivalent, represented as a string with no leading zeros.\nassert decimal_to_binary(8) == '1000'\n\"\"\"\n\"\"\"\nWrite a function to convert the given decimal number to its binary equivalent, represented as a string with no leading zeros.\nassert decimal_to_binary(8) == '1000'\n\"\"\"\ndef decimal_to_binary(n):\n    return bin(n).replace(\"0b\", \"\")", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/68": [{"task_id": "Mbpp/68", "solution": "\"\"\"\nWrite a python function to check whether the given array is monotonic or not.\nassert is_Monotonic([6, 5, 4, 4]) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether the given array is monotonic or not.\nassert is_Monotonic([6, 5, 4, 4]) == True\n\"\"\"\ndef is_Monotonic(arr):\n    return (all(arr[i] <= arr[i + 1] for i in range(len(arr) - 1)) or\n            all(arr[i] >= arr[i + 1] for i in range(len(arr) - 1)))\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/7": [{"task_id": "Mbpp/7", "solution": "\"\"\"\nWrite a function to find all words which are at least 4 characters long in a string.\nassert set(find_char_long('Please move back to stream')) == set(['Please', 'move', 'back', 'stream'])\n\"\"\"\n\"\"\"\nWrite a function to find all words which are at least 4 characters long in a string.\nassert set(find_char_long('Please move back to stream')) == set(['Please', 'move', 'back', 'stream'])\n\"\"\"\nimport re\n\n\ndef find_char_long(string):\n    return re.findall(r'\\b\\w{4,}\\b', string)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/62": [{"task_id": "Mbpp/62", "solution": "\"\"\"\nWrite a python function to find smallest number in a list.\nassert smallest_num([10, 20, 1, 45, 99]) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find smallest number in a list.\nassert smallest_num([10, 20, 1, 45, 99]) == 1\n\"\"\"\ndef smallest_num(l):\n    return min(l)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/17": [{"task_id": "Mbpp/17", "solution": "\"\"\"\nWrite a function that returns the perimeter of a square given its side length as input.\nassert square_perimeter(10)==40\n\"\"\"\n\"\"\"\nWrite a function that returns the perimeter of a square given its side length as input.\nassert square_perimeter(10)==40\n\"\"\"\ndef square_perimeter(side_length):\n    return 4*side_length\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/57": [{"task_id": "Mbpp/57", "solution": "\"\"\"\nWrite a python function to find the largest number that can be formed with the given list of digits.\nassert find_Max_Num([1,2,3]) == 321\n\"\"\"\n\"\"\"\nWrite a python function to find the largest number that can be formed with the given list of digits.\nassert find_Max_Num([1,2,3]) == 321\n\"\"\"\ndef find_Max_Num(digits):\n    digits.sort(reverse=True)\n    return int(\"\".join(map(str, digits)))\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/2": [{"task_id": "Mbpp/2", "solution": "\"\"\"\nWrite a function to find the shared elements from the given two lists.\nassert set(similar_elements((3, 4, 5, 6),(5, 7, 4, 10))) == set((4, 5))\n\"\"\"\n\"\"\"\nWrite a function to find the shared elements from the given two lists.\nassert set(similar_elements((3, 4, 5, 6),(5, 7, 4, 10))) == set((4, 5))\n\"\"\"\nfrom typing import List, Set\n\n\ndef similar_elements(list1: List[int], list2: List[int]) -> Set[int]:\n    return set(list1) & set(list2)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/19": [{"task_id": "Mbpp/19", "solution": "\"\"\"\nWrite a function to find whether a given array of integers contains any duplicate element.\nassert test_duplicate(([1,2,3,4,5]))==False\n\"\"\"\n\"\"\"\nWrite a function to find whether a given array of integers contains any duplicate element.\nassert test_duplicate(([1,2,3,4,5]))==False\n\"\"\"\ndef test_duplicate(arr):\n    return len(arr) != len(set(arr))\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/3": [{"task_id": "Mbpp/3", "solution": "\"\"\"\nWrite a python function to identify non-prime numbers.\nassert is_not_prime(2) == False\n\"\"\"\n\"\"\"\nWrite a python function to identify non-prime numbers.\nassert is_not_prime(2) == False\n\"\"\"\ndef is_not_prime(num):\n    if num < 2:\n        return True\n    for i in range(2, num):\n        if num % i == 0:\n            return True\n    return False\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/65": [{"task_id": "Mbpp/65", "solution": "\"\"\"\nWrite a function to flatten a list and sum all of its elements.\nassert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21\n\"\"\"\n\"\"\"\nWrite a function to flatten a list and sum all of its elements.\nassert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21\n\"\"\"\ndef recursive_list_sum(lst):\n    if not lst:\n        return 0\n    if isinstance(lst[0], list):\n        return recursive_list_sum(lst[0]) + recursive_list_sum(lst[1:])\n    return lst[0] + recursive_list_sum(lst[1:])", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/63": [{"task_id": "Mbpp/63", "solution": "\"\"\"\nWrite a function to find the maximum difference between available pairs in the given tuple list.\nassert max_difference([(3, 5), (1, 7), (10, 3), (1, 2)]) == 7\n\"\"\"\n\"\"\"\nWrite a function to find the maximum difference between available pairs in the given tuple list.\nassert max_difference([(3, 5), (1, 7), (10, 3), (1, 2)]) == 7\n\"\"\"\nfrom typing import List, <PERSON><PERSON>\n\n\ndef max_difference(tuples: List[Tuple[int, int]]) -> int:\n    max_diff = 0\n    for i in range(len(tuples)):\n        for j in range(i + 1, len(tuples)):\n            diff = abs(tuples[i][0] - tuples[j][0])\n            if diff > max_diff:\n                max_diff = diff\n    return max_diff\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/64": [{"task_id": "Mbpp/64", "solution": "\"\"\"\nWrite a function to sort a list of tuples using the second value of each tuple.\nassert subject_marks([('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])==[('Social sciences', 82), ('English', 88), ('Science', 90), ('Maths', 97)]\n\"\"\"\n\"\"\"\nWrite a function to sort a list of tuples using the second value of each tuple.\nassert subject_marks([('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])==[('Social sciences', 82), ('English', 88), ('Science', 90), ('Maths', 97)]\n\"\"\"\ndef subject_marks(lst):\n    return sorted(lst, key=lambda x: x[1])\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/87": [{"task_id": "Mbpp/87", "solution": "\"\"\"\nWrite a function to merge three dictionaries into a single dictionary.\nassert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}\n\"\"\"\n\"\"\"\nWrite a function to merge three dictionaries into a single dictionary.\nassert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}\n\"\"\"\ndef merge_dictionaries_three(dict1, dict2, dict3):\n    return {**dict1, **dict2, **dict3}", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/71": [{"task_id": "Mbpp/71", "solution": "\"\"\"\nWrite a function to sort a list of elements.\nassert comb_sort([5, 15, 37, 25, 79]) == [5, 15, 25, 37, 79]\n\"\"\"\n\"\"\"\nWrite a function to sort a list of elements.\nassert comb_sort([5, 15, 37, 25, 79]) == [5, 15, 25, 37, 79]\n\"\"\"\nimport random\nimport timeit\nfrom typing import List\n\n\ndef comb_sort(arr: List[int]) -> List[int]:\n    \"\"\"\n    Sorts a list of elements using the comb sort algorithm.\n\n    Args:\n        arr (List[int]): The list of elements to be sorted.\n\n    Returns:\n        List[int]: The sorted list of elements.\n    \"\"\"\n    gap = len(arr)\n    shrink_factor = 1.3\n    swapped = True\n\n    while gap > 1 or swapped:\n        gap = int(gap / shrink_factor)\n        if gap < 1:\n            gap = 1\n\n        swapped = False\n\n        for i in range(len(arr) - gap):\n            if arr[i] > arr[i + gap]:\n                arr[i], arr[i + gap] = arr[i + gap], arr[i]\n                swapped = True\n\n    return arr\n\n\ndef test_comb_sort():\n    \"\"\"\n    Tests the comb_sort function with a list of random integers.\n    \"\"\"\n    random_list = [random.randint(1, 100) for _ in range(1000)]\n    sorted_list = sorted(random_list)\n    assert comb_sort(random_list) == sorted_list\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/133": [{"task_id": "Mbpp/133", "solution": "\"\"\"\nWrite a function to calculate the sum of the negative numbers of a given list of numbers.\nassert sum_negativenum([2, 4, -6, -9, 11, -12, 14, -5, 17])==-32\n\"\"\"\n\"\"\"\nWrite a function to calculate the sum of the negative numbers of a given list of numbers.\nassert sum_negativenum([2, 4, -6, -9, 11, -12, 14, -5, 17])==-32\n\"\"\"\ndef sum_negativenum(lst):\n    sum = 0\n    for i in lst:\n        if i < 0:\n            sum += i\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/119": [{"task_id": "Mbpp/119", "solution": "\"\"\"\nWrite a python function to find the element that appears only once in a sorted array.\nassert search([1,1,2,2,3]) == 3\n\"\"\"\n\"\"\"\nWrite a python function to find the element that appears only once in a sorted array.\nassert search([1,1,2,2,3]) == 3\n\"\"\"\ndef search(arr):\n    if len(arr) == 1:\n        return arr[0]\n    if arr[0] != arr[1]:\n        return arr[0]\n    if arr[-1] != arr[-2]:\n        return arr[-1]\n    for i in range(1, len(arr)-1):\n        if arr[i] != arr[i-1] and arr[i] != arr[i+1]:\n            return arr[i]\n    return -1\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/100": [{"task_id": "Mbpp/100", "solution": "\"\"\"\nWrite a function to find the next smallest palindrome of a specified integer, returned as an integer.\nassert next_smallest_palindrome(99)==101\n\"\"\"\n\"\"\"\nWrite a function to find the next smallest palindrome of a specified integer, returned as an integer.\nassert next_smallest_palindrome(99)==101\n\"\"\"\ndef next_smallest_palindrome(num):\n    # Convert the number to a string\n    num_str = str(num)\n    # Find the middle index\n    mid = len(num_str) // 2\n    # Check if the number is already a palindrome\n    if num_str == num_str[::-1]:\n        # Find the next palindrome by incrementing the middle digit\n        for i in range(mid, -1, -1):\n            if num_str[i] != '9':\n                num_str = num_str[:i] + str(int(num_str[i]) + 1) + num_str[i+1:]\n                break\n        # If the number is all 9s, add 1 to the leftmost digit\n        else:\n            num_str = str(int(num_str) + 1)\n    # If the number is already a palindrome, find the next palindrome by incrementing the leftmost digit\n    else:\n        # Find the leftmost digit that is not a 9\n        for i in range(mid, -1, -1):\n            if num_str[i] != '9':\n                # Increment the leftmost digit and set all digits to the right to 0\n                num_str = num_str[:i] + str(int(num_str[i]) + 1) + '0' * (len(num_str) - i - 1)\n                break\n    # Convert the string back to an integer and return it\n    return int(num_str)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[45678]]}], "Mbpp/135": [{"task_id": "Mbpp/135", "solution": "\"\"\"\nWrite a function to find the nth hexagonal number.\nassert hexagonal_num(10) == 190\n\"\"\"\n\"\"\"\nWrite a function to find the nth hexagonal number.\nassert hexagonal_num(10) == 190\n\"\"\"\nimport math\n\n\ndef hexagonal_num(n):\n    return n * (2 * n - 1)\n\n\ndef test_hexagonal_num():\n    assert hexagonal_num(10) == 190", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/139": [{"task_id": "Mbpp/139", "solution": "\"\"\"\nWrite a function to find the circumference of a circle.\nassert math.isclose(circle_circumference(10), 62.830000000000005, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the circumference of a circle.\nassert math.isclose(circle_circumference(10), 62.830000000000005, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef circle_circumference(radius):\n    return 2 * math.pi * radius\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/88": [{"task_id": "Mbpp/88", "solution": "\"\"\"\nWrite a function to get the frequency of all the elements in a list, returned as a dictionary.\nassert freq_count([10,10,10,10,20,20,20,20,40,40,50,50,30])==({10: 4, 20: 4, 40: 2, 50: 2, 30: 1})\n\"\"\"\n\"\"\"\nWrite a function to get the frequency of all the elements in a list, returned as a dictionary.\nassert freq_count([10,10,10,10,20,20,20,20,40,40,50,50,30])==({10: 4, 20: 4, 40: 2, 50: 2, 30: 1})\n\"\"\"\ndef freq_count(list1):\n    dict1 = {}\n    for i in list1:\n        if i in dict1:\n            dict1[i] += 1\n        else:\n            dict1[i] = 1\n    return dict1", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/113": [{"task_id": "Mbpp/113", "solution": "\"\"\"\nWrite a function to check if a string represents an integer or not.\nassert check_integer(\"python\")==False\n\"\"\"\n\"\"\"\nWrite a function to check if a string represents an integer or not.\nassert check_integer(\"python\")==False\n\"\"\"\ndef check_integer(string):\n    try:\n        int(string)\n        return True\n    except ValueError:\n        return False\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/66": [{"task_id": "Mbpp/66", "solution": "\"\"\"\nWrite a python function to count the number of positive numbers in a list.\nassert pos_count([1,-2,3,-4]) == 2\n\"\"\"\n\"\"\"\nWrite a python function to count the number of positive numbers in a list.\nassert pos_count([1,-2,3,-4]) == 2\n\"\"\"\ndef pos_count(lst):\n    count = 0\n    for i in lst:\n        if i > 0:\n            count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/89": [{"task_id": "Mbpp/89", "solution": "\"\"\"\nWrite a function to find the closest smaller number than n.\nassert closest_num(11) == 10\n\"\"\"\n\"\"\"\nWrite a function to find the closest smaller number than n.\nassert closest_num(11) == 10\n\"\"\"\nimport math\n\n\ndef closest_num(n):\n    return n - 1 if n % 2 == 0 else n - 2\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/165": [{"task_id": "Mbpp/165", "solution": "\"\"\"\nWrite a function to count the number of characters in a string that occur at the same position in the string as in the English alphabet (case insensitive).\nassert count_char_position(\"xbcefg\") == 2\n\"\"\"\n\"\"\"\nWrite a function to count the number of characters in a string that occur at the same position in the string as in the English alphabet (case insensitive).\nassert count_char_position(\"xbcefg\") == 2\n\"\"\"\ndef count_char_position(s):\n    count = 0\n    for i in range(len(s)):\n        if s[i].lower() == chr(ord('a') + i):\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/125": [{"task_id": "Mbpp/125", "solution": "\"\"\"\nWrite a function to find the maximum difference between the number of 0s and number of 1s in any sub-string of the given binary string.\nassert find_length(\"11000010001\") == 6\n\"\"\"\n\"\"\"\nWrite a function to find the maximum difference between the number of 0s and number of 1s in any sub-string of the given binary string.\nassert find_length(\"11000010001\") == 6\n\"\"\"\ndef find_length(s):\n    # Write your code here\n    max_diff = 0\n    count_0 = 0\n    count_1 = 0\n    for i in range(len(s)):\n        if s[i] == '0':\n            count_0 += 1\n        else:\n            count_1 += 1\n        max_diff = max(max_diff, abs(count_0 - count_1))\n    return max_diff", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["100000011100000000111111111000000011111101"]]}], "Mbpp/115": [{"task_id": "Mbpp/115", "solution": "\"\"\"\nWrite a function to check whether all dictionaries in a list are empty or not.\nassert empty_dit([{},{},{}])==True\n\"\"\"\n\"\"\"\nWrite a function to check whether all dictionaries in a list are empty or not.\nassert empty_dit([{},{},{}])==True\n\"\"\"\ndef empty_dit(list1):\n    for i in list1:\n        if len(i) != 0:\n            return False\n    return True", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[[[1, 2], [], []]]], "plus_fail_tests": [[[[], [], [], ["not", "", "ML", "e", "gA", "a", "EnIODji"], [], [], [], [], [], [], [], []]]]}], "Mbpp/140": [{"task_id": "Mbpp/140", "solution": "\"\"\"\nWrite a function to flatten the list of lists into a single set of numbers.\nassert set(extract_singly([(3, 4, 5), (4, 5, 7), (1, 4)])) == set([3, 4, 5, 7, 1])\n\"\"\"\n\"\"\"\nWrite a function to flatten the list of lists into a single set of numbers.\nassert set(extract_singly([(3, 4, 5), (4, 5, 7), (1, 4)])) == set([3, 4, 5, 7, 1])\n\"\"\"\nfrom typing import List, Set, Tu<PERSON>\n\n\ndef extract_singly(list_of_lists: List[Tuple[int, ...]]) -> Set[int]:\n    return set([item for sublist in list_of_lists for item in sublist])", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/94": [{"task_id": "Mbpp/94", "solution": "\"\"\"\nGiven a list of tuples, write a function that returns the first value of the tuple with the smallest second value.\nassert index_minimum([('<PERSON><PERSON>', 143), ('<PERSON><PERSON><PERSON>', 200), ('<PERSON><PERSON><PERSON>', 100)]) == 'Varsha'\n\"\"\"\n\"\"\"\nGiven a list of tuples, write a function that returns the first value of the tuple with the smallest second value.\nassert index_minimum([('<PERSON><PERSON>', 143), ('<PERSON><PERSON><PERSON>', 200), ('<PERSON>arsha', 100)]) == 'Varsha'\n\"\"\"\ndef index_minimum(tuples):\n    min_value = min(tuples, key=lambda x: x[1])\n    return min_value[0]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/142": [{"task_id": "Mbpp/142", "solution": "\"\"\"\nWrite a function to count number items that are identical in the same position of three given lists.\nassert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,9],[2,1,3,1,2,6,7,9])==3\n\"\"\"\n\"\"\"\nWrite a function to count number items that are identical in the same position of three given lists.\nassert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,9],[2,1,3,1,2,6,7,9])==3\n\"\"\"\ndef count_samepair(list1,list2,list3):\n    count=0\n    for i in range(len(list1)):\n        if list1[i]==list2[i]==list3[i]:\n            count+=1\n    return count", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5], [1, 2, 3, 4, 5, 6, 7, 8]]]}], "Mbpp/79": [{"task_id": "Mbpp/79", "solution": "\"\"\"\nWrite a python function to check whether the length of the word is odd or not.\nassert word_len(\"Hadoop\") == False\n\"\"\"\n\"\"\"\nWrite a python function to check whether the length of the word is odd or not.\nassert word_len(\"Hadoop\") == False\n\"\"\"\ndef word_len(word):\n    if len(word)%2==0:\n        return False\n    else:\n        return True", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/145": [{"task_id": "Mbpp/145", "solution": "\"\"\"\nWrite a python function to find the maximum difference between any two elements in a given array.\nassert max_Abs_Diff((2,1,5,3)) == 4\n\"\"\"\n\"\"\"\nWrite a python function to find the maximum difference between any two elements in a given array.\nassert max_Abs_Diff((2,1,5,3)) == 4\n\"\"\"\ndef max_Abs_Diff(arr):\n    max_diff = 0\n    for i in range(len(arr)):\n        for j in range(i+1, len(arr)):\n            if abs(arr[i] - arr[j]) > max_diff:\n                max_diff = abs(arr[i] - arr[j])\n    return max_diff\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/106": [{"task_id": "Mbpp/106", "solution": "\"\"\"\nWrite a function to append the given list to the given tuples.\nassert add_lists([5, 6, 7], (9, 10)) == (9, 10, 5, 6, 7)\n\"\"\"\n\"\"\"\nWrite a function to append the given list to the given tuples.\nassert add_lists([5, 6, 7], (9, 10)) == (9, 10, 5, 6, 7)\n\"\"\"\nfrom typing import List, <PERSON><PERSON>\n\n\ndef add_lists(list_: List[int], tuple_: Tuple[int]) -> <PERSON><PERSON>[int, ...]:\n    return tuple_ + tuple(list_)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/97": [{"task_id": "Mbpp/97", "solution": "\"\"\"\nWrite a function to find frequency of each element in a flattened list of lists, returned in a dictionary.\nassert frequency_lists([[1, 2, 3, 2], [4, 5, 6, 2], [7, 8, 9, 5]])=={1: 1, 2: 3, 3: 1, 4: 1, 5: 2, 6: 1, 7: 1, 8: 1, 9: 1}\n\"\"\"\n\"\"\"\nWrite a function to find frequency of each element in a flattened list of lists, returned in a dictionary.\nassert frequency_lists([[1, 2, 3, 2], [4, 5, 6, 2], [7, 8, 9, 5]])=={1: 1, 2: 3, 3: 1, 4: 1, 5: 2, 6: 1, 7: 1, 8: 1, 9: 1}\n\"\"\"\nfrom collections import Counter\nfrom itertools import chain\nfrom typing import List, Dict\n\n\ndef frequency_lists(lists: List[List[int]]) -> Dict[int, int]:\n    return Counter(chain.from_iterable(lists))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/120": [{"task_id": "Mbpp/120", "solution": "\"\"\"\nWrite a function to find the maximum absolute product between numbers in pairs of tuples within a given list.\nassert max_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==36\n\"\"\"\n\"\"\"\nWrite a function to find the maximum absolute product between numbers in pairs of tuples within a given list.\nassert max_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==36\n\"\"\"\nfrom itertools import combinations\nfrom typing import List, <PERSON><PERSON>\n\n\ndef max_product_tuple(tuples: List[Tuple[int, int]]) -> int:\n    return max(abs(x * y) for x, y in combinations(tuples, 2))", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/18": [{"task_id": "Mbpp/18", "solution": "\"\"\"\nWrite a function to remove characters from the first string which are present in the second string.\nassert remove_dirty_chars(\"probasscurve\", \"pros\") == 'bacuve'\n\"\"\"\n\"\"\"\nWrite a function to remove characters from the first string which are present in the second string.\nassert remove_dirty_chars(\"probasscurve\", \"pros\") == 'bacuve'\n\"\"\"\nimport unittest\n\n\ndef remove_dirty_chars(s1, s2):\n    return ''.join([c for c in s1 if c not in s2])\n\n\nclass TestRemoveDirtyChars(unittest.TestCase):\n    def test_remove_dirty_chars(self):\n        self.assertEqual(remove_dirty_chars(\"probasscurve\", \"pros\"), 'bacuve')\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/104": [{"task_id": "Mbpp/104", "solution": "\"\"\"\nWrite a function to sort each sublist of strings in a given list of lists.\nassert sort_sublists(([\"green\", \"orange\"], [\"black\", \"white\"], [\"white\", \"black\", \"orange\"]))==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n\"\"\"\n\"\"\"\nWrite a function to sort each sublist of strings in a given list of lists.\nassert sort_sublists(([\"green\", \"orange\"], [\"black\", \"white\"], [\"white\", \"black\", \"orange\"]))==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n\"\"\"\nfrom typing import List, Any\n\n\ndef sort_sublists(list_of_lists: List[List[Any]]) -> List[List[Any]]:\n    return [sorted(sublist) for sublist in list_of_lists]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/80": [{"task_id": "Mbpp/80", "solution": "\"\"\"\nWrite a function to find the nth tetrahedral number.\nassert tetrahedral_number(5) == 35\n\"\"\"\n\"\"\"\nWrite a function to find the nth tetrahedral number.\nassert tetrahedral_number(5) == 35\n\"\"\"\nimport math\n\n\ndef tetrahedral_number(n):\n    return int((n * (n + 1) * (n + 2)) / 6)\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/101": [{"task_id": "Mbpp/101", "solution": "\"\"\"\nWrite a function to find the kth element in the given array using 1-based indexing.\nassert kth_element([12,3,5,7,19], 2) == 3\n\"\"\"\n\"\"\"\nWrite a function to find the kth element in the given array using 1-based indexing.\nassert kth_element([12,3,5,7,19], 2) == 3\n\"\"\"\ndef kth_element(arr, k):\n    return arr[k-1]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/118": [{"task_id": "Mbpp/118", "solution": "\"\"\"\nWrite a function to convert a string to a list of strings split on the space character.\nassert string_to_list(\"python programming\")==['python','programming']\n\"\"\"\n\"\"\"\nWrite a function to convert a string to a list of strings split on the space character.\nassert string_to_list(\"python programming\")==['python','programming']\n\"\"\"\ndef string_to_list(string):\n    return string.split(\" \")\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/93": [{"task_id": "Mbpp/93", "solution": "\"\"\"\nWrite a function to calculate the value of 'a' to the power 'b'.\nassert power(3,4) == 81\n\"\"\"\n\"\"\"\nWrite a function to calculate the value of 'a' to the power 'b'.\nassert power(3,4) == 81\n\"\"\"\ndef power(a, b):\n    return a**b\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/128": [{"task_id": "Mbpp/128", "solution": "\"\"\"\nWrite a function to find words that are longer than n characters from a given list of words.\nassert long_words(3,\"python is a programming language\")==['python','programming','language']\n\"\"\"\n\"\"\"\nWrite a function to find words that are longer than n characters from a given list of words.\nassert long_words(3,\"python is a programming language\")==['python','programming','language']\n\"\"\"\ndef long_words(n, str):\n    word_len = []\n    txt = str.split(\" \")\n    for x in txt:\n        if len(x) > n:\n            word_len.append(x)\n    return word_len", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/86": [{"task_id": "Mbpp/86", "solution": "\"\"\"\nWrite a function to find nth centered hexagonal number.\nassert centered_hexagonal_number(10) == 271\n\"\"\"\n\"\"\"\nWrite a function to find nth centered hexagonal number.\nassert centered_hexagonal_number(10) == 271\n\"\"\"\nimport math\n\n\ndef centered_hexagonal_number(n):\n    return 3 * n * n - 3 * n + 1\n\n\ndef test():\n    assert centered_hexagonal_number(10) == 271", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/95": [{"task_id": "Mbpp/95", "solution": "\"\"\"\nWrite a python function to find the length of the smallest list in a list of lists.\nassert Find_Min_Length([[1],[1,2]]) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the length of the smallest list in a list of lists.\nassert Find_Min_Length([[1],[1,2]]) == 1\n\"\"\"\ndef find_min_length(list_of_lists):\n    min_length = float('inf')\n    for sublist in list_of_lists:\n        if len(sublist) < min_length:\n            min_length = len(sublist)\n    return min_length\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/85": [{"task_id": "Mbpp/85", "solution": "\"\"\"\nWrite a function to find the surface area of a sphere.\nassert math.isclose(surfacearea_sphere(10), 1256.6370614359173, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the surface area of a sphere.\nassert math.isclose(surfacearea_sphere(10), 1256.6370614359173, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef surfacearea_sphere(radius):\n    return 4 * math.pi * radius ** 2\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/91": [{"task_id": "Mbpp/91", "solution": "\"\"\"\nWrite a function to check if a string is present as a substring in a given list of string values.\nassert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ack\")==True\n\"\"\"\n\"\"\"\nWrite a function to check if a string is present as a substring in a given list of string values.\nassert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ack\")==True\n\"\"\"\ndef find_substring(list1, str1):\n    for i in list1:\n        if i.find(str1) != -1:\n            return True\n    return False\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/108": [{"task_id": "Mbpp/108", "solution": "\"\"\"\nWrite a function to merge three lists into a single sorted list.\nassert merge_sorted_list([25, 24, 15, 4, 5, 29, 110],[19, 20, 11, 56, 25, 233, 154],[24, 26, 54, 48])==[4, 5, 11, 15, 19, 20, 24, 24, 25, 25, 26, 29, 48, 54, 56, 110, 154, 233]\n\"\"\"\n\"\"\"\nWrite a function to merge three lists into a single sorted list.\nassert merge_sorted_list([25, 24, 15, 4, 5, 29, 110],[19, 20, 11, 56, 25, 233, 154],[24, 26, 54, 48])==[4, 5, 11, 15, 19, 20, 24, 24, 25, 25, 26, 29, 48, 54, 56, 110, 154, 233]\n\"\"\"\ndef merge_sorted_list(list1, list2, list3):\n    list1.extend(list2)\n    list1.extend(list3)\n    list1.sort()\n    return list1\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/82": [{"task_id": "Mbpp/82", "solution": "\"\"\"\nWrite a function to find the volume of a sphere.\nassert math.isclose(volume_sphere(10), 4188.790204786391, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the volume of a sphere.\nassert math.isclose(volume_sphere(10), 4188.790204786391, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef volume_sphere(radius):\n    return 4 / 3 * math.pi * radius ** 3\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/74": [{"task_id": "Mbpp/74", "solution": "\"\"\"\nWrite a function to check whether it follows the sequence given in the patterns array.\nassert is_samepatterns([\"red\",\"green\",\"green\"], [\"a\", \"b\", \"b\"])==True\n\"\"\"\n\"\"\"\nWrite a function to check whether it follows the sequence given in the patterns array.\nassert is_samepatterns([\"red\",\"green\",\"green\"], [\"a\", \"b\", \"b\"])==True\n\"\"\"\ndef is_samepatterns(arr1, arr2):\n    if len(arr1) != len(arr2):\n        return False\n    for i in range(len(arr1)):\n        if arr1[i] != arr2[i]:\n            return False\n    return True", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/92": [{"task_id": "Mbpp/92", "solution": "\"\"\"\nWrite a function to check whether the given number is undulating or not.\nassert is_undulating(1212121) == True\n\"\"\"\n\"\"\"\nWrite a function to check whether the given number is undulating or not.\nassert is_undulating(1212121) == True\n\"\"\"\ndef is_undulating(n):\n    if len(str(n)) < 3:\n        return False\n    if str(n)[0] == str(n)[1]:\n        return False\n    for i in range(1, len(str(n)) - 1):\n        if str(n)[i] == str(n)[i + 1]:\n            return False\n    return True\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[2121212121212123]]}], "Mbpp/132": [{"task_id": "Mbpp/132", "solution": "\"\"\"\nWrite a function to convert a tuple to a string.\nassert tup_string(('e', 'x', 'e', 'r', 'c', 'i', 's', 'e', 's'))==(\"exercises\")\n\"\"\"\n\"\"\"\nWrite a function to convert a tuple to a string.\nassert tup_string(('e', 'x', 'e', 'r', 'c', 'i', 's', 'e', 's'))==(\"exercises\")\n\"\"\"\ndef tup_string(tup):\n    return ''.join(tup)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/129": [{"task_id": "Mbpp/129", "solution": "\"\"\"\nWrite a function to calculate whether the matrix is a magic square.\nassert magic_square_test([[7, 12, 1, 14], [2, 13, 8, 11], [16, 3, 10, 5], [9, 6, 15, 4]])==True\n\"\"\"\n\"\"\"\nWrite a function to calculate whether the matrix is a magic square.\nassert magic_square_test([[7, 12, 1, 14], [2, 13, 8, 11], [16, 3, 10, 5], [9, 6, 15, 4]])==True\n\"\"\"\ndef magic_square_test(matrix):\n    if len(matrix) != len(matrix[0]):\n        return False\n    n = len(matrix)\n    sum1 = sum2 = 0\n    for i in range(n):\n        sum1 += matrix[i][i]\n        sum2 += matrix[i][n - 1 - i]\n    if sum1 != sum2:\n        return False\n    for i in range(n):\n        sum1 = 0\n        sum2 = 0\n        for j in range(n):\n            sum1 += matrix[i][j]\n            sum2 += matrix[j][i]\n        if sum1 != sum2:\n            return False\n    return True", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/127": [{"task_id": "Mbpp/127", "solution": "\"\"\"\nWrite a function to multiply two integers.\nassert multiply_int(10,20)==200\n\"\"\"\n\"\"\"\nWrite a function to multiply two integers.\nassert multiply_int(10,20)==200\n\"\"\"\ndef multiply_int(a,b):\n    return a*b", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/75": [{"task_id": "Mbpp/75", "solution": "\"\"\"\nWrite a function to find tuples which have all elements divisible by k from the given list of tuples.\nassert find_tuples([(6, 24, 12), (7, 9, 6), (12, 18, 21)], 6) == [(6, 24, 12)]\n\"\"\"\n\"\"\"\nWrite a function to find tuples which have all elements divisible by k from the given list of tuples.\nassert find_tuples([(6, 24, 12), (7, 9, 6), (12, 18, 21)], 6) == [(6, 24, 12)]\n\"\"\"\nfrom typing import List, Tuple\n\n\ndef find_tuples(tuples: List[Tuple[int, ...]], k: int) -> List[Tuple[int, ...]]:\n    return [t for t in tuples if all(x % k == 0 for x in t)]\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/161": [{"task_id": "Mbpp/161", "solution": "\"\"\"\nWrite a function to remove all elements from a given list present in another list.\nassert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [2, 4, 6, 8]) == [1, 3, 5, 7, 9, 10]\n\"\"\"\n\"\"\"\nWrite a function to remove all elements from a given list present in another list.\nassert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [2, 4, 6, 8]) == [1, 3, 5, 7, 9, 10]\n\"\"\"\nfrom typing import List\n\n\ndef remove_elements(list1: List[int], list2: List[int]) -> List[int]:\n    return [x for x in list1 if x not in list2]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/84": [{"task_id": "Mbpp/84", "solution": "\"\"\"\nWrite a function to find the nth number in the newman conway sequence.\nassert sequence(10) == 6\n\"\"\"\n\"\"\"\nWrite a function to find the nth number in the newman conway sequence.\nassert sequence(10) == 6\n\"\"\"\nimport pytest\n\n\ndef newman_conway(num):\n    \"\"\" Returns nth number in the newman conway sequence. \"\"\"\n    if num < 1:\n        raise ValueError(\"Number must be greater than 0\")\n    if num == 1:\n        return 1\n    if num == 2:\n        return 1\n    sequence = [0, 1, 1]\n    for i in range(3, num + 1):\n        sequence.append(sequence[sequence[i - 1]] + sequence[i - sequence[i - 1]])\n    return sequence[num]\n\n\ndef test_newman_conway():\n    assert newman_conway(10) == 6\n    assert newman_conway(1) == 1\n    assert newman_conway(2) == 1\n    assert newman_conway(3) == 1\n    assert newman_conway(4) == 1\n    assert newman_conway(5) == 1\n    assert newman_conway(6) == 2\n    assert newman_conway(7) == 2\n    assert newman_conway(8) == 2\n    assert newman_conway(9) == 2\n    assert newman_conway(10) == 6\n    assert newman_conway(11) == 3\n    assert newman_conway(12) == 3\n    assert newman_conway(13) == 3\n    assert newman_conway(14) == 3\n    assert newman_conway(15) == 3\n    assert newman_conway(16) == 3\n    assert newman_conway(17) == 3\n    assert newman_conway(18) == 3\n    assert newman_conway(19) == 3\n    assert newman_conway(20) == 3\n    assert newman_conway(21) == 3\n    assert newman_conway(22) == 3\n    assert newman_conway(23) == 3\n    assert newman_conway(24) == 3\n    assert newman_conway(25) == 3\n    assert newman_conway(26) == 3\n    assert newman_conway(27) == 3\n    assert newman_conway(28) == 3\n    assert newman_conway(29) == 3\n    assert newman_conway(30) == 3\n    assert newman_conway(31) == 3\n    assert newman_conway(32) == 3\n    assert newman_conway(33) == 3\n    assert newman_conway(34) == 3\n    assert newman_conway(35) == 3\n    assert newman_conway(36) == 3\n    assert newman_conway(37) == 3\n    assert newman_conway(38) == 3\n    assert newman_conway(39) == 3\n    assert newman_conway(40) == 3\n    assert newman_conway(41) == 3\n    assert newman_conway(42) == 3\n    assert newman_conway(43) == 3\n    assert newman_conway(44) == 3\n    assert newman_conway(45) == 3\n    assert newman_conway(4", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/138": [{"task_id": "Mbpp/138", "solution": "\"\"\"\nWrite a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.\nassert is_Sum_Of_Powers_Of_Two(10) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.\nassert is_Sum_Of_Powers_Of_Two(10) == True\n\"\"\"\ndef is_Sum_Of_Powers_Of_Two(n):\n    if n == 0:\n        return False\n    while n > 0:\n        if n & 1 == 0:\n            n = n >> 1\n        else:\n            return False\n    return True", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[-14]]}], "Mbpp/109": [{"task_id": "Mbpp/109", "solution": "\"\"\"\nWrite a python function to find the number of numbers with an odd value when rotating a binary string the given number of times.\nassert odd_Equivalent(\"011001\",6) == 3\n\"\"\"\n\"\"\"\nWrite a python function to find the number of numbers with an odd value when rotating a binary string the given number of times.\nassert odd_Equivalent(\"011001\",6) == 3\n\"\"\"\ndef odd_Equivalent(binary_string,rotations):\n    count = 0\n    for i in range(rotations):\n        binary_string = binary_string[1:] + binary_string[0]\n        if int(binary_string,2) % 2 == 1:\n            count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/83": [{"task_id": "Mbpp/83", "solution": "\"\"\"\nWrite a python function to find the character made by adding the ASCII value of all the characters of the given string modulo 26.\nassert get_Char(\"abc\") == \"f\"\n\"\"\"\n\"\"\"\nWrite a python function to find the character made by adding the ASCII value of all the characters of the given string modulo 26.\nassert get_Char(\"abc\") == \"f\"\n\"\"\"\n#lex_auth_01269442114344550475\ndef get_Char(string_value):\n    #start writing your code here\n    sum=0\n    for i in string_value:\n        sum+=ord(i)\n    return chr(sum%26+97)\n\nstring_value=\"abc\"", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/77": [{"task_id": "Mbpp/77", "solution": "\"\"\"\nWrite a python function to find whether a number is divisible by 11.\nassert is_Diff (12345) == False\n\"\"\"\n\"\"\"\nWrite a python function to find whether a number is divisible by 11.\nassert is_Diff (12345) == False\n\"\"\"\ndef is_Diff(n):\n    if n%11==0:\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/105": [{"task_id": "Mbpp/105", "solution": "\"\"\"\nWrite a python function to count true booleans in the given list.\nassert count([True,False,True]) == 2\n\"\"\"\n\"\"\"\nWrite a python function to count true booleans in the given list.\nassert count([True,False,True]) == 2\n\"\"\"\ndef count(lst):\n    count = 0\n    for i in lst:\n        if i == True:\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/102": [{"task_id": "Mbpp/102", "solution": "\"\"\"\nWrite a function to convert a snake case string to camel case string.\nassert snake_to_camel('python_program')=='PythonProgram'\n\"\"\"\n\"\"\"\nWrite a function to convert a snake case string to camel case string.\nassert snake_to_camel('python_program')=='PythonProgram'\n\"\"\"\ndef snake_to_camel(s):\n    s = s.split('_')\n    s = [i.capitalize() for i in s]\n    s = ''.join(s)\n    return s", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/111": [{"task_id": "Mbpp/111", "solution": "\"\"\"\nWrite a function to find the common elements in given nested lists.\nassert set(common_in_nested_lists([[12, 18, 23, 25, 45], [7, 12, 18, 24, 28], [1, 5, 8, 12, 15, 16, 18]]))==set([18, 12])\n\"\"\"\n\"\"\"\nWrite a function to find the common elements in given nested lists.\nassert set(common_in_nested_lists([[12, 18, 23, 25, 45], [7, 12, 18, 24, 28], [1, 5, 8, 12, 15, 16, 18]]))==set([18, 12])\n\"\"\"\nfrom typing import List, Set, Tuple, Dict, Any, Optional\nfrom collections import Counter\nfrom itertools import chain\nfrom functools import reduce\nfrom operator import add\nfrom collections import defaultdict\nfrom collections import Counter\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import OrderedDict\nfrom collections import namedtuple\nfrom collections import deque\nfrom collections import defaultdict\nfrom collections import", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/141": [{"task_id": "Mbpp/141", "solution": "\"\"\"\nWrite a function to sort a list of elements.\nassert pancake_sort([15, 79, 25, 38, 69]) == [15, 25, 38, 69, 79]\n\"\"\"\n\"\"\"\nWrite a function to sort a list of elements.\nassert pancake_sort([15, 79, 25, 38, 69]) == [15, 25, 38, 69, 79]\n\"\"\"\nfrom typing import List\n\n\ndef pancake_sort(arr: List[int]) -> List[int]:\n    for i in range(len(arr) - 1, 0, -1):\n        max_index = arr.index(max(arr[:i + 1]))\n        arr[:max_index + 1] = arr[:max_index + 1][::-1]\n        arr[:i + 1] = arr[:i + 1][::-1]\n    return arr\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/131": [{"task_id": "Mbpp/131", "solution": "\"\"\"\nWrite a python function to reverse only the vowels of a given string (where y is not a vowel).\nassert reverse_vowels(\"Python\") == \"Python\"\n\"\"\"\n\"\"\"\nWrite a python function to reverse only the vowels of a given string (where y is not a vowel).\nassert reverse_vowels(\"Python\") == \"Python\"\n\"\"\"\ndef reverse_vowels(s):\n    vowels = \"aeiouAEIOU\"\n    s = list(s)\n    i, j = 0, len(s) - 1\n    while i < j:\n        if s[i] in vowels and s[j] in vowels:\n            s[i], s[j] = s[j], s[i]\n            i += 1\n            j -= 1\n        elif s[i] in vowels:\n            j -= 1\n        elif s[j] in vowels:\n            i += 1\n        else:\n            i += 1\n            j -= 1\n    return \"\".join(s)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/72": [{"task_id": "Mbpp/72", "solution": "\"\"\"\nWrite a python function to check whether the given number can be represented as the difference of two squares or not.\nassert dif_Square(5) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether the given number can be represented as the difference of two squares or not.\nassert dif_Square(5) == True\n\"\"\"\ndef dif_Square(n):\n    for i in range(1,n):\n        if i**2 + (n-i)**2 == n:\n            return True\n    return False", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/98": [{"task_id": "Mbpp/98", "solution": "\"\"\"\nWrite a function to multiply all the numbers in a list and divide with the length of the list.\nassert math.isclose(multiply_num((8, 2, 3, -1, 7)), -67.2, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to multiply all the numbers in a list and divide with the length of the list.\nassert math.isclose(multiply_num((8, 2, 3, -1, 7)), -67.2, rel_tol=0.001)\n\"\"\"\nfrom typing import List\nfrom math import isclose\n\n\ndef multiply_num(nums: List[int]) -> float:\n    \"\"\"Multiply all the numbers in a list and divide with the length of the list.\"\"\"\n    return isclose(sum(nums) / len(nums), 0, rel_tol=0.001)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/116": [{"task_id": "Mbpp/116", "solution": "\"\"\"\nWrite a function to convert a given tuple of positive integers into a single integer.\nassert tuple_to_int((1,2,3))==123\n\"\"\"\n\"\"\"\nWrite a function to convert a given tuple of positive integers into a single integer.\nassert tuple_to_int((1,2,3))==123\n\"\"\"\ndef tuple_to_int(tup):\n    return int(''.join(map(str, tup)))\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/130": [{"task_id": "Mbpp/130", "solution": "\"\"\"\nWrite a function to find the item with maximum frequency in a given list.\nassert max_occurrences([2,3,8,4,7,9,8,2,6,5,1,6,1,2,3,2,4,6,9,1,2])==2\n\"\"\"\n\"\"\"\nWrite a function to find the item with maximum frequency in a given list.\nassert max_occurrences([2,3,8,4,7,9,8,2,6,5,1,6,1,2,3,2,4,6,9,1,2])==2\n\"\"\"\nfrom collections import Counter\ndef max_occurrences(lst):\n    return Counter(lst).most_common(1)[0][0]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/124": [{"task_id": "Mbpp/124", "solution": "\"\"\"\nWrite a function to get the angle of a complex number.\nassert math.isclose(angle_complex(0,1j), 1.5707963267948966, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to get the angle of a complex number.\nassert math.isclose(angle_complex(0,1j), 1.5707963267948966, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef angle_complex(x, y):\n    return math.atan2(y, x)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/90": [{"task_id": "Mbpp/90", "solution": "\"\"\"\nWrite a python function to find the length of the longest word.\nassert len_log([\"python\",\"PHP\",\"bigdata\"]) == 7\n\"\"\"\n\"\"\"\nWrite a python function to find the length of the longest word.\nassert len_log([\"python\",\"PHP\",\"bigdata\"]) == 7\n\"\"\"\ndef len_log(list1):\n    max = 0\n    for i in list1:\n        if len(i) > max:\n            max = len(i)\n    return max", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/14": [{"task_id": "Mbpp/14", "solution": "\"\"\"\nWrite a python function to find the volume of a triangular prism.\nassert find_Volume(10,8,6) == 240\n\"\"\"\n\"\"\"\nWrite a python function to find the volume of a triangular prism.\nassert find_Volume(10,8,6) == 240\n\"\"\"\ndef find_Volume(a,b,c):\n    return a*b*c", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/143": [{"task_id": "Mbpp/143", "solution": "\"\"\"\nWrite a function to find number of lists present in the given tuple.\nassert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2\n\"\"\"\n\"\"\"\nWrite a function to find number of lists present in the given tuple.\nassert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2\n\"\"\"\nfrom typing import List, <PERSON><PERSON>\n\n\ndef find_lists(tup: Tuple[List[int]]) -> int:\n    return len(tup)\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[["hoOVcr", "CBVLPGoFDN", "SwM", "RZ", "ykPU", "y", "CSz"]]]}], "Mbpp/160": [{"task_id": "Mbpp/160", "solution": "\"\"\"\nWrite a function that returns integers x and y that satisfy ax + by = n as a tuple, or return None if no solution exists.\nassert find_solution(2, 3, 7) == (2, 1)\n\"\"\"\n\"\"\"\nWrite a function that returns integers x and y that satisfy ax + by = n as a tuple, or return None if no solution exists.\nassert find_solution(2, 3, 7) == (2, 1)\n\"\"\"\nimport math\n\n\ndef find_solution(a, b, n):\n    if a == 0 and b == 0:\n        return None\n    if a == 0:\n        return (0, n // b)\n    if b == 0:\n        return (n // a, 0)\n    if a == b:\n        if n % a == 0:\n            return (n // a, n // a)\n        else:\n            return None\n    if a > b:\n        if n % b == 0:\n            return (n // b, 0)\n        else:\n            return None\n    if b > a:\n        if n % a == 0:\n            return (0, n // a)\n        else:\n            return None\n    if a < b:\n        if n % a == 0:\n            return (n // a, 0)\n        else:\n            return None\n    if b < a:\n        if n % b == 0:\n            return (0, n // b)\n        else:\n            return None\n    if a == 1:\n        return (n, 0)\n    if b == 1:\n        return (0, n)\n    if a == 2:\n        if n % 2 == 0:\n            return (n // 2, 0)\n        else:\n            return None\n    if b == 2:\n        if n % 2 == 0:\n            return (0, n // 2)\n        else:\n            return None\n    if a == 3:\n        if n % 3 == 0:\n            return (n // 3, 0)\n        else:\n            return None\n    if b == 3:\n        if n % 3 == 0:\n            return (0, n // 3)\n        else:\n            return None\n    if a == 4:\n        if n % 4 == 0:\n            return (n // 4, 0)\n        else:\n            return None\n    if b == 4:\n        if n % 4 == 0:\n            return (0, n // 4)\n        else:\n            return None\n    if a == 5:\n        if n % 5 == 0:\n            return (n // 5, 0)\n        else:\n            return None\n    if b == 5:\n        if n % 5 == 0:\n            return (0, n // 5)\n        else:\n            return None\n    if a == 6:\n        if n % 6 == 0:\n            return (n // 6, 0)\n        else:\n            return None\n    if b == 6:\n        if n % 6 == 0:\n            return (0, n // 6)\n        else:\n            return None\n    if a == 7:\n        if n % 7 == 0:\n            return (n // 7, 0)\n        else:\n            return None\n    if b == 7:\n        if n % 7 == 0:\n            return (0, n // 7)\n        else:\n            return None\n    if a == 8:\n        if n % 8 == 0:\n            return (n // 8, 0)\n        else:\n            return None\n    if b == 8:\n        if n % 8 == 0:\n            return (0, n // 8)\n        else:\n            return None\n    if a == 9:\n        if n % 9 == 0:\n            return (n // 9, 0)\n        else:\n            return None", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/117": [{"task_id": "Mbpp/117", "solution": "\"\"\"\nWrite a function to convert all possible convertible elements in a list of lists to floats.\nassert list_to_float( [(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")] ) == [(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)]\n\"\"\"\n\"\"\"\nWrite a function to convert all possible convertible elements in a list of lists to floats.\nassert list_to_float( [(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")] ) == [(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)]\n\"\"\"\nimport unittest\n\n\ndef list_to_float(list_of_lists):\n    return [[float(x) for x in y] for y in list_of_lists]\n\n\nclass TestListToFloat(unittest.TestCase):\n    def test_list_to_float(self):\n        self.assertEqual(list_to_float([(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")]), [(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)])\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/137": [{"task_id": "Mbpp/137", "solution": "\"\"\"\nWrite a function to find the ratio of zeroes to non-zeroes in an array of integers.\nassert math.isclose(zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8]), 0.181818, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the ratio of zeroes to non-zeroes in an array of integers.\nassert math.isclose(zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8]), 0.181818, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef zero_count(arr):\n    zero_count = 0\n    for i in arr:\n        if i == 0:\n            zero_count += 1\n    return zero_count / len(arr)\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/164": [{"task_id": "Mbpp/164", "solution": "\"\"\"\nWrite a function to determine if the sum of the divisors of two integers are the same.\nassert are_equivalent(36, 57) == False\n\"\"\"\n\"\"\"\nWrite a function to determine if the sum of the divisors of two integers are the same.\nassert are_equivalent(36, 57) == False\n\"\"\"\ndef are_equivalent(a, b):\n    def divisors(n):\n        return [i for i in range(1, n) if n % i == 0]\n    return sum(divisors(a)) == sum(divisors(b))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/96": [{"task_id": "Mbpp/96", "solution": "\"\"\"\nWrite a python function to find the number of divisors of a given integer.\nassert divisor(15) == 4\n\"\"\"\n\"\"\"\nWrite a python function to find the number of divisors of a given integer.\nassert divisor(15) == 4\n\"\"\"\ndef divisor(n):\n    count = 0\n    for i in range(1, n+1):\n        if n % i == 0:\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/103": [{"task_id": "Mbpp/103", "solution": "\"\"\"\nWrite a function to find the Eulerian number a(n, m).\nassert eulerian_num(3, 1) == 4\n\"\"\"\n\"\"\"\nWrite a function to find the Eulerian number a(n, m).\nassert eulerian_num(3, 1) == 4\n\"\"\"\nimport pytest\nfrom eulerian_num import eulerian_num\n\n\*************************(\"n, m, expected\", [(3, 1, 4), (4, 2, 10), (5, 2, 30)])\ndef test_eulerian_num(n, m, expected):\n    assert eulerian_num(n, m) == expected", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/166": [{"task_id": "Mbpp/166", "solution": "\"\"\"\nWrite a function that counts the number of pairs of integers in a list that xor to an even number.\nassert find_even_pair([5, 4, 7, 2, 1]) == 4\n\"\"\"\n\"\"\"\nWrite a function that counts the number of pairs of integers in a list that xor to an even number.\nassert find_even_pair([5, 4, 7, 2, 1]) == 4\n\"\"\"\ndef find_even_pair(list):\n    count = 0\n    for i in range(len(list)):\n        for j in range(i+1, len(list)):\n            if (list[i] ^ list[j]) % 2 == 0:\n                count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/171": [{"task_id": "Mbpp/171", "solution": "\"\"\"\nWrite a function to find the perimeter of a regular pentagon from the length of its sides.\nassert perimeter_pentagon(5) == 25\n\"\"\"\n\"\"\"\nWrite a function to find the perimeter of a regular pentagon from the length of its sides.\nassert perimeter_pentagon(5) == 25\n\"\"\"\nimport math\n\n\ndef perimeter_pentagon(side_length):\n    return 5 * side_length\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/170": [{"task_id": "Mbpp/170", "solution": "\"\"\"\nWrite a function to find the sum of numbers in a list within a range specified by two indices.\nassert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 8, 10) == 29\n\"\"\"\n\"\"\"\nWrite a function to find the sum of numbers in a list within a range specified by two indices.\nassert sum_range_list([2,1,5,6,8,3,4,9,10,11,8,12], 8, 10) == 29\n\"\"\"\ndef sum_range_list(list, start, end):\n    sum = 0\n    for i in range(start, end+1):\n        sum += list[i]\n    return sum\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/167": [{"task_id": "Mbpp/167", "solution": "\"\"\"\nWrite a python function to find the smallest power of 2 greater than or equal to n.\nassert next_power_of_2(0) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the smallest power of 2 greater than or equal to n.\nassert next_power_of_2(0) == 1\n\"\"\"\ndef next_power_of_2(n):\n    \"\"\"\n    Find the smallest power of 2 greater than or equal to n.\n\n    Args:\n    n (int): The number to find the next power of 2 for.\n\n    Returns:\n    int: The smallest power of 2 greater than or equal to n.\n    \"\"\"\n    if n <= 0:\n        return 1\n    power = 1\n    while power < n:\n        power *= 2\n    return power\n\n# Test the function", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/235": [{"task_id": "Mbpp/235", "solution": "\"\"\"\nWrite a python function to set all even bits of a given number.\nassert even_bit_set_number(10) == 10\n\"\"\"\n\"\"\"\nWrite a python function to set all even bits of a given number.\nassert even_bit_set_number(10) == 10\n\"\"\"\ndef even_bit_set_number(n):\n    return n | (n >> 1)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/168": [{"task_id": "Mbpp/168", "solution": "\"\"\"\nWrite a function to count the number of occurrences of a number in a given list.\nassert frequency([1,2,3], 4) == 0\n\"\"\"\n\"\"\"\nWrite a function to count the number of occurrences of a number in a given list.\nassert frequency([1,2,3], 4) == 0\n\"\"\"\nfrom typing import List\n\n\ndef frequency(lst: List[int], num: int) -> int:\n    return lst.count(num)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/227": [{"task_id": "Mbpp/227", "solution": "\"\"\"\nWrite a function to find minimum of three numbers.\nassert min_of_three(10,20,0)==0\n\"\"\"\n\"\"\"\nWrite a function to find minimum of three numbers.\nassert min_of_three(10,20,0)==0\n\"\"\"\ndef min_of_three(a,b,c):\n    if a<b and a<c:\n        return a\n    elif b<a and b<c:\n        return b\n    else:\n        return c", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[1.5, 1.5, 1.7539550971097109]]}], "Mbpp/223": [{"task_id": "Mbpp/223", "solution": "\"\"\"\nWrite a function that takes in a sorted array, its length (n), and an element and returns whether the element is the majority element in the given sorted array. (The majority element is the element that occurs more than n/2 times.)\nassert is_majority([1, 2, 3, 3, 3, 3, 10], 7, 3) == True\n\"\"\"\n\"\"\"\nWrite a function that takes in a sorted array, its length (n), and an element and returns whether the element is the majority element in the given sorted array. (The majority element is the element that occurs more than n/2 times.)\nassert is_majority([1, 2, 3, 3, 3, 3, 10], 7, 3) == True\n\"\"\"\ndef is_majority(arr, n, element):\n    count = 0\n    for i in range(n):\n        if arr[i] == element:\n            count += 1\n    if count > n/2:\n        return True\n    else:\n        return False\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[], 1, 0]]}], "Mbpp/222": [{"task_id": "Mbpp/222", "solution": "\"\"\"\nWrite a function to check if all the elements in tuple have same data type or not.\nassert check_type((5, 6, 7, 3, 5, 6) ) == True\n\"\"\"\n\"\"\"\nWrite a function to check if all the elements in tuple have same data type or not.\nassert check_type((5, 6, 7, 3, 5, 6) ) == True\n\"\"\"\ndef check_type(tup):\n    return all(type(tup[0]) == type(i) for i in tup)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/239": [{"task_id": "Mbpp/239", "solution": "\"\"\"\nWrite a function that takes in positive integers m and n and finds the number of possible sequences of length n, such that each element is a positive integer and is greater than or equal to twice the previous element but less than or equal to m.\nassert get_total_number_of_sequences(10, 4) == 4\n\"\"\"\n\"\"\"\nWrite a function that takes in positive integers m and n and finds the number of possible sequences of length n, such that each element is a positive integer and is greater than or equal to twice the previous element but less than or equal to m.\nassert get_total_number_of_sequences(10, 4) == 4\n\"\"\"\ndef get_total_number_of_sequences(m, n):\n    if n == 1:\n        return 1\n    if m == 1:\n        return 0\n    if m == 2:\n        return 1\n    if n == 2:\n        return m - 1\n    return get_total_number_of_sequences(m, n - 1) + get_total_number_of_sequences(m // 2, n - 1)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/226": [{"task_id": "Mbpp/226", "solution": "\"\"\"\nWrite a python function to remove the characters which have odd index values of a given string.\nassert odd_values_string('abcdef') == 'ace'\n\"\"\"\n\"\"\"\nWrite a python function to remove the characters which have odd index values of a given string.\nassert odd_values_string('abcdef') == 'ace'\n\"\"\"\ndef odd_values_string(str1):\n    str2 = \"\"\n    for i in range(len(str1)):\n        if i % 2 == 0:\n            str2 = str2 + str1[i]\n    return str2", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/230": [{"task_id": "Mbpp/230", "solution": "\"\"\"\nWrite a function that takes in a string and character, replaces blank spaces in the string with the character, and returns the string.\nassert replace_blank(\"hello people\",'@')==(\"hello@people\")\n\"\"\"\n\"\"\"\nWrite a function that takes in a string and character, replaces blank spaces in the string with the character, and returns the string.\nassert replace_blank(\"hello people\",'@')==(\"hello@people\")\n\"\"\"\ndef replace_blank(string,character):\n    return string.replace(\" \",character)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/224": [{"task_id": "Mbpp/224", "solution": "\"\"\"\nWrite a python function to count the number of set bits (binary digits with value 1) in a given number.\nassert count_Set_Bits(2) == 1\n\"\"\"\n\"\"\"\nWrite a python function to count the number of set bits (binary digits with value 1) in a given number.\nassert count_Set_Bits(2) == 1\n\"\"\"\ndef count_Set_Bits(n):\n    count = 0\n    while n:\n        count += n & 1\n        n >>= 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/240": [{"task_id": "Mbpp/240", "solution": "\"\"\"\nWrite a function that takes in two lists and replaces the last element of the first list with the elements of the second list.\nassert replace_list([1, 3, 5, 7, 9, 10],[2, 4, 6, 8])==[1, 3, 5, 7, 9, 2, 4, 6, 8]\n\"\"\"\n\"\"\"\nWrite a function that takes in two lists and replaces the last element of the first list with the elements of the second list.\nassert replace_list([1, 3, 5, 7, 9, 10],[2, 4, 6, 8])==[1, 3, 5, 7, 9, 2, 4, 6, 8]\n\"\"\"\ndef replace_list(list1, list2):\n    list1[-1:] = list2\n    return list1", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/244": [{"task_id": "Mbpp/244", "solution": "\"\"\"\nWrite a python function to find the next perfect square greater than a given number.\nassert next_Perfect_Square(35) == 36\n\"\"\"\n\"\"\"\nWrite a python function to find the next perfect square greater than a given number.\nassert next_Perfect_Square(35) == 36\n\"\"\"\ndef next_Perfect_Square(n):\n    return (n+1)**2\nn=int(input())", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/229": [{"task_id": "Mbpp/229", "solution": "\"\"\"\nWrite a function that takes in an array and an integer n, and re-arranges the first n elements of the given array so that all negative elements appear before positive ones, and where the relative order among negative and positive elements is preserved.\nassert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]\n\"\"\"\n\"\"\"\nWrite a function that takes in an array and an integer n, and re-arranges the first n elements of the given array so that all negative elements appear before positive ones, and where the relative order among negative and positive elements is preserved.\nassert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]\n\"\"\"\ndef re_arrange_array(array, n):\n    # Write your code here.\n    left = 0\n    right = n - 1\n    while left < right:\n        if array[left] < 0 and array[right] < 0:\n            left += 1\n        elif array[left] > 0 and array[right] > 0:\n            right -= 1\n        elif array[left] > 0 and array[right] < 0:\n            array[left], array[right] = array[right], array[left]\n            left += 1\n            right -= 1\n        else:\n            left += 1\n            right -= 1\n    return array", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[10, -5, -2, 7, 0, 0, 0, -10, -5], 9]]}], "Mbpp/234": [{"task_id": "Mbpp/234", "solution": "\"\"\"\nWrite a function to find the volume of a cube given its side length.\nassert volume_cube(3)==27\n\"\"\"\n\"\"\"\nWrite a function to find the volume of a cube given its side length.\nassert volume_cube(3)==27\n\"\"\"\ndef volume_cube(side_length):\n    return side_length**3\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/237": [{"task_id": "Mbpp/237", "solution": "\"\"\"\nWrite a function that takes in a list of tuples and returns a dictionary mapping each unique tuple to the number of times it occurs in the list.\nassert check_occurences([(3, 1), (1, 3), (2, 5), (5, 2), (6, 3)] ) == {(1, 3): 2, (2, 5): 2, (3, 6): 1}\n\"\"\"\n\"\"\"\nWrite a function that takes in a list of tuples and returns a dictionary mapping each unique tuple to the number of times it occurs in the list.\nassert check_occurences([(3, 1), (1, 3), (2, 5), (5, 2), (6, 3)] ) == {(1, 3): 2, (2, 5): 2, (3, 6): 1}\n\"\"\"\ndef check_occurences(tuples):\n    # Write your code here\n    dict = {}\n    for i in tuples:\n        if i in dict:\n            dict[i] += 1\n        else:\n            dict[i] = 1\n    return dict", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/247": [{"task_id": "Mbpp/247", "solution": "\"\"\"\nWrite a function to find the length of the longest palindromic subsequence in the given string.\nassert lps(\"TENS FOR TENS\") == 5\n\"\"\"\n\"\"\"\nWrite a function to find the length of the longest palindromic subsequence in the given string.\nassert lps(\"TENS FOR TENS\") == 5\n\"\"\"\nfrom collections import defaultdict\n\n\ndef lps(s):\n    n = len(s)\n    dp = defaultdict(int)\n    for i in range(n):\n        dp[(i, i)] = 1\n    for i in range(n - 1):\n        dp[(i, i + 1)] = 1 if s[i] == s[i + 1] else 0\n    for i in range(n - 2, -1, -1):\n        for j in range(i + 2, n):\n            dp[(i, j)] = max(dp[(i + 1, j)], dp[(i, j - 1)])\n            if s[i] == s[j]:\n                dp[(i, j)] = max(dp[(i, j)], dp[(i + 1, j - 1)] + 2)\n    return dp[(0, n - 1)]\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/172": [{"task_id": "Mbpp/172", "solution": "\"\"\"\nWrite a function to count the number of occurence of the string 'std' in a given string.\nassert count_occurance(\"letstdlenstdporstd\") == 3\n\"\"\"\n\"\"\"\nWrite a function to count the number of occurence of the string 'std' in a given string.\nassert count_occurance(\"letstdlenstdporstd\") == 3\n\"\"\"\ndef count_occurance(string):\n    count = 0\n    for i in range(len(string)):\n        if string[i:i+3] == \"std\":\n            count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/232": [{"task_id": "Mbpp/232", "solution": "\"\"\"\nWrite a function that takes in a list and an integer n and returns a list containing the n largest items from the list.\nassert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],2))==set([100,90])\n\"\"\"\n\"\"\"\nWrite a function that takes in a list and an integer n and returns a list containing the n largest items from the list.\nassert set(larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],2))==set([100,90])\n\"\"\"\ndef larg_nnum(l,n):\n    l.sort()\n    return l[-n:]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/242": [{"task_id": "Mbpp/242", "solution": "\"\"\"\nWrite a function to count the total number of characters in a string.\nassert count_charac(\"python programming\")==18\n\"\"\"\n\"\"\"\nWrite a function to count the total number of characters in a string.\nassert count_charac(\"python programming\")==18\n\"\"\"\ndef count_charac(string):\n    count=0\n    for i in string:\n        count+=1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/245": [{"task_id": "Mbpp/245", "solution": "\"\"\"\nWrite a function that takes an array and finds the maximum sum of a bitonic subsequence for the given array, where a sequence is bitonic if it is first increasing and then decreasing.\nassert max_sum([1, 15, 51, 45, 33, 100, 12, 18, 9]) == 194\n\"\"\"\n\"\"\"\nWrite a function that takes an array and finds the maximum sum of a bitonic subsequence for the given array, where a sequence is bitonic if it is first increasing and then decreasing.\nassert max_sum([1, 15, 51, 45, 33, 100, 12, 18, 9]) == 194\n\"\"\"\nimport sys\nfrom typing import List, Tuple\n\n\ndef max_sum(arr: List[int]) -> int:\n    n = len(arr)\n    dp = [[0] * n for _ in range(n)]\n    for i in range(n):\n        dp[i][i] = arr[i]\n    for i in range(n - 1):\n        dp[i][i + 1] = max(arr[i], arr[i + 1])\n    for i in range(n - 2):\n        for j in range(i + 2, n):\n            dp[i][j] = max(dp[i][j - 1], dp[i + 1][j], arr[j] + dp[i][j - 1], arr[j] + dp[i + 1][j])\n    return max(dp[i][j] for i in range(n) for j in range(i, n))\n\n\ndef max_sum2(arr: List[int]) -> int:\n    n = len(arr)\n    dp = [[0] * n for _ in range(n)]\n    for i in range(n):\n        dp[i][i] = arr[i]\n    for i in range(n - 1):\n        dp[i][i + 1] = max(arr[i], arr[i + 1])\n    for i in range(n - 2):\n        for j in range(i + 2, n):\n            dp[i][j] = max(dp[i][j - 1], dp[i + 1][j], arr[j] + dp[i][j - 1], arr[j] + dp[i + 1][j])\n    return max(dp[i][j] for i in range(n) for j in range(i, n))\n\n\ndef max_sum3(arr: List[int]) -> int:\n    n = len(arr)\n    dp = [[0] * n for _ in range(n)]\n    for i in range(n):\n        dp[i][i] = arr[i]\n    for i in range(n - 1):\n        dp[i][i + 1] = max(arr[i], arr[i + 1])\n    for i in range(n - 2):\n        for j in range(i + 2, n):\n            dp[i][j] = max(dp[i][j - 1], dp[i + 1][j], arr[j] + dp[i][j - 1], arr[j] + dp[i + 1][j])\n    return max(dp[i][j] for i in range(n) for j in range(i, n))\n\n\ndef max_sum4(arr: List[int]) -> int:\n    n = len(arr)\n    dp = [[0] * n for _ in range(n)]\n    for i in range(n):\n        dp[i][i] = arr[i]\n    for i in range(n - 1):\n        dp[i][i + 1] = max(arr[i], arr[i + 1])\n    for i in range(n - 2):\n        for j in range(i + 2, n):\n            dp[i][j] = max(dp[i][j - 1], dp[i + 1][j], arr[j] + dp[i][j - 1], arr[j] + dp[i + 1][j])\n    return max(dp[i][j] for i in range(n) for j in range(i, n))\n\n\ndef max_sum5(arr: List[int]) -> int:\n    n = len(arr)\n    dp = [[0] * n for _ in range(n)]\n    for i in range(n):\n        dp[i][i] = arr[i]\n    for i in range(n - 1):\n        dp[i][i + 1] = max(arr[i], arr[i", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/238": [{"task_id": "Mbpp/238", "solution": "\"\"\"\nWrite a python function to count the number of non-empty substrings of a given string.\nassert number_of_substrings(\"abc\") == 6\n\"\"\"\n\"\"\"\nWrite a python function to count the number of non-empty substrings of a given string.\nassert number_of_substrings(\"abc\") == 6\n\"\"\"\ndef number_of_substrings(str1):\n    count = 0\n    for i in range(len(str1)):\n        for j in range(i+1,len(str1)+1):\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/233": [{"task_id": "Mbpp/233", "solution": "\"\"\"\nWrite a function to find the lateral surface area of a cylinder.\nassert math.isclose(lateralsuface_cylinder(10,5), 314.15000000000003, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the lateral surface area of a cylinder.\nassert math.isclose(lateralsuface_cylinder(10,5), 314.15000000000003, rel_tol=0.001)\n\"\"\"\nimport math\ndef lateralsurface_cylinder(r, h):\n    return 2 * math.pi * r * h", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/251": [{"task_id": "Mbpp/251", "solution": "\"\"\"\nWrite a function that takes in a list and an element and inserts the element before each element in the list, and returns the resulting list.\nassert insert_element(['<PERSON>', '<PERSON>', 'Black'] ,'c')==['c', 'Red', 'c', 'Green', 'c', 'Black']\n\"\"\"\n\"\"\"\nWrite a function that takes in a list and an element and inserts the element before each element in the list, and returns the resulting list.\nassert insert_element(['Red', 'Green', 'Black'] ,'c')==['c', 'Red', 'c', 'Green', 'c', 'Black']\n\"\"\"\ndef insert_element(list, element):\n    for i in range(len(list)):\n        list.insert(i, element)\n    return list\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[-94.19039842971628, -3.4031380684866406, -22.045351968568852, -94.19039842971628], "a"]]}], "Mbpp/253": [{"task_id": "Mbpp/253", "solution": "\"\"\"\nWrite a python function that returns the number of integer elements in a given list.\nassert count_integer([1,2,'abc',1.2]) == 2\n\"\"\"\n\"\"\"\nWrite a python function that returns the number of integer elements in a given list.\nassert count_integer([1,2,'abc',1.2]) == 2\n\"\"\"\ndef count_integer(lst):\n    count = 0\n    for i in lst:\n        if type(i) == int:\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/252": [{"task_id": "Mbpp/252", "solution": "\"\"\"\nWrite a python function to convert complex numbers to polar coordinates.\nassert convert(1) == (1.0, 0.0)\n\"\"\"\n\"\"\"\nWrite a python function to convert complex numbers to polar coordinates.\nassert convert(1) == (1.0, 0.0)\n\"\"\"\nimport cmath\ndef convert(complex_number):\n    return cmath.polar(complex_number)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/249": [{"task_id": "Mbpp/249", "solution": "\"\"\"\nWrite a function to find the intersection of two arrays.\nassert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]\n\"\"\"\n\"\"\"\nWrite a function to find the intersection of two arrays.\nassert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]\n\"\"\"\ndef intersection_array(arr1, arr2):\n    arr1.sort()\n    arr2.sort()\n    i = 0\n    j = 0\n    result = []\n    while i < len(arr1) and j < len(arr2):\n        if arr1[i] == arr2[j]:\n            result.append(arr1[i])\n            i += 1\n            j += 1\n        elif arr1[i] < arr2[j]:\n            i += 1\n        else:\n            j += 1\n    return result\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[["", "zm", null, "QkDxdG", "Xe", "ertYo", 14, "gv"], ["zm", "guWZdvsBGL", "QUGweiSK", "l", "QkDxdG", "xsQS", "ertYo", "Fvp", ""]]]}], "Mbpp/250": [{"task_id": "Mbpp/250", "solution": "\"\"\"\nWrite a python function that takes in a tuple and an element and counts the occcurences of the element in the tuple.\nassert count_X((10, 8, 5, 2, 10, 15, 10, 8, 5, 8, 8, 2),4) == 0\n\"\"\"\n\"\"\"\nWrite a python function that takes in a tuple and an element and counts the occcurences of the element in the tuple.\nassert count_X((10, 8, 5, 2, 10, 15, 10, 8, 5, 8, 8, 2),4) == 0\n\"\"\"\ndef count_X(tup,element):\n    count = 0\n    for i in tup:\n        if i == element:\n            count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/162": [{"task_id": "Mbpp/162", "solution": "\"\"\"\nWrite a function to calculate the sum (n - 2*i) from i=0 to n // 2, for instance n + (n-2) + (n-4)... (until n-x =< 0).\nassert sum_series(6) == 12\n\"\"\"\n\"\"\"\nWrite a function to calculate the sum (n - 2*i) from i=0 to n // 2, for instance n + (n-2) + (n-4)... (until n-x =< 0).\nassert sum_series(6) == 12\n\"\"\"\ndef sum_series(n):\n    sum = 0\n    for i in range(0, n // 2 + 1):\n        sum += (n - 2 * i)\n    return sum\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/61": [{"task_id": "Mbpp/61", "solution": "\"\"\"\nWrite a python function to count the number of substrings with the sum of digits equal to their length.\nassert count_Substrings('112112') == 6\n\"\"\"\n\"\"\"\nWrite a python function to count the number of substrings with the sum of digits equal to their length.\nassert count_Substrings('112112') == 6\n\"\"\"\ndef count_Substrings(string):\n    count = 0\n    for i in range(len(string)):\n        for j in range(i, len(string)):\n            if sum(int(digit) for digit in string[i:j+1]) == len(string[i:j+1]):\n                count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/67": [{"task_id": "Mbpp/67", "solution": "\"\"\"\nWrite a function to find the number of ways to partition a set of Bell numbers.\nassert bell_number(2)==2\n\"\"\"\n\"\"\"\nWrite a function to find the number of ways to partition a set of Bell numbers.\nassert bell_number(2)==2\n\"\"\"\ndef bell_number(n):\n    if n==0:\n        return 1\n    else:\n        return sum([bell_number(n-i) for i in range(1,n+1)])\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[10]], "plus_fail_tests": [[100]]}], "Mbpp/126": [{"task_id": "Mbpp/126", "solution": "\"\"\"\nWrite a python function to find the sum of common divisors of two given numbers.\nassert sum(10,15) == 6\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of common divisors of two given numbers.\nassert sum(10,15) == 6\n\"\"\"\ndef sum(a,b):\n    sum=0\n    for i in range(1,min(a,b)+1):\n        if a%i==0 and b%i==0:\n            sum+=i\n    return sum", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[987654321, 123456789]]}], "Mbpp/123": [{"task_id": "Mbpp/123", "solution": "\"\"\"\nWrite a function to sum all amicable numbers from 1 to a specified number.\nassert amicable_numbers_sum(999)==504\n\"\"\"\n\"\"\"\nWrite a function to sum all amicable numbers from 1 to a specified number.\nassert amicable_numbers_sum(999)==504\n\"\"\"\ndef amicable_numbers_sum(n):\n    def sum_of_divisors(num):\n        return sum([i for i in range(1, num) if num % i == 0])\n    amicable_numbers = []\n    for i in range(1, n + 1):\n        if sum_of_divisors(i) != i and sum_of_divisors(sum_of_divisors(i)) == i:\n            amicable_numbers.append(i)\n    return sum(amicable_numbers)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[9999]], "plus_fail_tests": []}], "Mbpp/390": [{"task_id": "Mbpp/390", "solution": "\"\"\"\nWrite a function to apply a given format string to all of the elements in a list.\nassert add_string([1,2,3,4],'temp{0}')==['temp1', 'temp2', 'temp3', 'temp4']\n\"\"\"\n\"\"\"\nWrite a function to apply a given format string to all of the elements in a list.\nassert add_string([1,2,3,4],'temp{0}')==['temp1', 'temp2', 'temp3', 'temp4']\n\"\"\"\ndef add_string(list,format):\n    return [format.format(i) for i in list]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/276": [{"task_id": "Mbpp/276", "solution": "\"\"\"\nWrite a function that takes in the radius and height of a cylinder and returns the the volume.\nassert math.isclose(volume_cylinder(10,5), 1570.7500000000002, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function that takes in the radius and height of a cylinder and returns the the volume.\nassert math.isclose(volume_cylinder(10,5), 1570.7500000000002, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef volume_cylinder(radius, height):\n    return math.pi * radius ** 2 * height\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/437": [{"task_id": "Mbpp/437", "solution": "\"\"\"\nWrite a function to remove odd characters in a string.\nassert remove_odd(\"python\")==(\"yhn\")\n\"\"\"\n\"\"\"\nWrite a function to remove odd characters in a string.\nassert remove_odd(\"python\")==(\"yhn\")\n\"\"\"\ndef remove_odd(string):\n    return string[1::2]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/257": [{"task_id": "Mbpp/257", "solution": "\"\"\"\nWrite a function that takes in two numbers and returns a tuple with the second number and then the first number.\nassert swap_numbers(10,20)==(20,10)\n\"\"\"\n\"\"\"\nWrite a function that takes in two numbers and returns a tuple with the second number and then the first number.\nassert swap_numbers(10,20)==(20,10)\n\"\"\"\ndef swap_numbers(a,b):\n    return b,a\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/305": [{"task_id": "Mbpp/305", "solution": "\"\"\"\nWrite a function to return two words from a list of words starting with letter 'p'.\nassert start_withp([\"Python PHP\", \"Java JavaScript\", \"c c++\"])==('Python', 'PHP')\n\"\"\"\n\"\"\"\nWrite a function to return two words from a list of words starting with letter 'p'.\nassert start_withp([\"Python PHP\", \"Java JavaScript\", \"c c++\"])==('Python', 'PHP')\n\"\"\"\ndef start_withp(words):\n    for word in words:\n        if word[0] == 'P' or word[0] == 'p':\n            return word.split()\n    return None\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/407": [{"task_id": "Mbpp/407", "solution": "\"\"\"\nWrite a function to create the next bigger number by rearranging the digits of a given number.\nassert rearrange_bigger(12)==21\n\"\"\"\n\"\"\"\nWrite a function to create the next bigger number by rearranging the digits of a given number.\nassert rearrange_bigger(12)==21\n\"\"\"\ndef rearrange_bigger(num):\n    num = list(str(num))\n    for i in range(len(num)-1,0,-1):\n        if num[i]>num[i-1]:\n            num[i],num[i-1] = num[i-1],num[i]\n            return int(\"\".join(num[:i]+num[i:][::-1]))\n    return -1", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[10]], "plus_fail_tests": []}], "Mbpp/277": [{"task_id": "Mbpp/277", "solution": "\"\"\"\nWrite a function that takes in a dictionary and integer n and filters the dictionary to only include entries with values greater than or equal to n.\nassert dict_filter({'Cierra Vega': 175, '<PERSON><PERSON>trell': 180, 'Kierra Gentry': 165, '<PERSON>': 190},170)=={'C<PERSON>ra Vega': 175, '<PERSON><PERSON>trell': 180, '<PERSON>': 190}\n\"\"\"\n\"\"\"\nWrite a function that takes in a dictionary and integer n and filters the dictionary to only include entries with values greater than or equal to n.\nassert dict_filter({'Cierra Vega': 175, '<PERSON><PERSON> Cantrell': 180, 'Kierra Gentry': 165, '<PERSON>': 190},170)=={'Cierra Vega': 175, '<PERSON><PERSON> Cantrell': 180, '<PERSON>': 190}\n\"\"\"\ndef dict_filter(dict, n):\n    return {k:v for k,v in dict.items() if v >= n}", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/278": [{"task_id": "Mbpp/278", "solution": "\"\"\"\nWrite a function to find the number of elements that occurs before the tuple element in the given tuple.\nassert count_first_elements((1, 5, 7, (4, 6), 10) ) == 3\n\"\"\"\n\"\"\"\nWrite a function to find the number of elements that occurs before the tuple element in the given tuple.\nassert count_first_elements((1, 5, 7, (4, 6), 10) ) == 3\n\"\"\"\nfrom typing import <PERSON><PERSON>\n\n\ndef count_first_elements(t: <PERSON><PERSON>) -> int:\n    count = 0\n    for i in t:\n        if isinstance(i, tuple):\n            break\n        count += 1\n    return count\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[1, 2, 3, 4, 5, 6]]]}], "Mbpp/272": [{"task_id": "Mbpp/272", "solution": "\"\"\"\nWrite a function that takes in a list of tuples and returns a list containing the rear element of each tuple.\nassert rear_extract([(1, '<PERSON><PERSON>', 21), (2, '<PERSON><PERSON><PERSON>', 20), (3, '<PERSON><PERSON>', 19)]) == [21, 20, 19]\n\"\"\"\n\"\"\"\nWrite a function that takes in a list of tuples and returns a list containing the rear element of each tuple.\nassert rear_extract([(1, '<PERSON><PERSON>', 21), (2, '<PERSON><PERSON><PERSON>', 20), (3, '<PERSON><PERSON>', 19)]) == [21, 20, 19]\n\"\"\"\ndef rear_extract(tuples):\n    return [t[2] for t in tuples]", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[[1, ["<PERSON><PERSON>", 21]], [2, ["<PERSON><PERSON><PERSON>", 20]], [3, ["<PERSON><PERSON>", 19]]]]]}], "Mbpp/283": [{"task_id": "Mbpp/283", "solution": "\"\"\"\nWrite a python function takes in an integer and check whether the frequency of each digit in the integer is less than or equal to the digit itself.\nassert validate(1234) == True\n\"\"\"\n\"\"\"\nWrite a python function takes in an integer and check whether the frequency of each digit in the integer is less than or equal to the digit itself.\nassert validate(1234) == True\n\"\"\"\ndef validate(n):\n    n = str(n)\n    for i in n:\n        if int(i) > len(n) - n.count(i):\n            return False\n    return True", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/395": [{"task_id": "Mbpp/395", "solution": "\"\"\"\nWrite a python function to find the first non-repeated character in a given string.\nassert first_non_repeating_character(\"abcabc\") == None\n\"\"\"\n\"\"\"\nWrite a python function to find the first non-repeated character in a given string.\nassert first_non_repeating_character(\"abcabc\") == None\n\"\"\"\ndef first_non_repeating_character(str1):\n    char_order = []\n    count = {}\n    for char in str1:\n        if char in count:\n            count[char] += 1\n        else:\n            count[char] = 1\n            char_order.append(char)\n    for char in char_order:\n        if count[char] == 1:\n            return char\n    return None", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/397": [{"task_id": "Mbpp/397", "solution": "\"\"\"\nWrite a function to find the median of three numbers.\nassert median_numbers(25,55,65)==55.0\n\"\"\"\n\"\"\"\nWrite a function to find the median of three numbers.\nassert median_numbers(25,55,65)==55.0\n\"\"\"\ndef median_numbers(a,b,c):\n    if a>b:\n        if a<c:\n            return a\n        elif b<c:\n            return c\n        else:\n            return b\n    elif b>a:\n        if b<c:\n            return b\n        elif a<c:\n            return c\n        else:\n            return a\n    else:\n        return b", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/311": [{"task_id": "Mbpp/311", "solution": "\"\"\"\nWrite a python function to set the left most unset bit.\nassert set_left_most_unset_bit(10) == 14\n\"\"\"\n\"\"\"\nWrite a python function to set the left most unset bit.\nassert set_left_most_unset_bit(10) == 14\n\"\"\"\ndef set_left_most_unset_bit(n):\n    return n | (n+1)\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[255]]}], "Mbpp/296": [{"task_id": "Mbpp/296", "solution": "\"\"\"\nWrite a python function to count inversions in an array.\nassert get_Inv_Count([1,20,6,4,5]) == 5\n\"\"\"\n\"\"\"\nWrite a python function to count inversions in an array.\nassert get_Inv_Count([1,20,6,4,5]) == 5\n\"\"\"\ndef get_Inv_Count(arr):\n    count = 0\n    for i in range(len(arr)):\n        for j in range(i+1,len(arr)):\n            if arr[i] > arr[j]:\n                count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/274": [{"task_id": "Mbpp/274", "solution": "\"\"\"\nWrite a python function that takes in a positive integer n and finds the sum of even index binomial coefficients.\nassert even_binomial_Coeff_Sum(4) == 8\n\"\"\"\n\"\"\"\nWrite a python function that takes in a positive integer n and finds the sum of even index binomial coefficients.\nassert even_binomial_Coeff_Sum(4) == 8\n\"\"\"\nimport math\ndef even_binomial_Coeff_Sum(n):\n    sum = 0\n    for i in range(0, n+1, 2):\n        sum += math.comb(n, i)\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/388": [{"task_id": "Mbpp/388", "solution": "\"\"\"\nWrite a python function to find the highest power of 2 that is less than or equal to n.\nassert highest_Power_of_2(10) == 8\n\"\"\"\n\"\"\"\nWrite a python function to find the highest power of 2 that is less than or equal to n.\nassert highest_Power_of_2(10) == 8\n\"\"\"\ndef highest_Power_of_2(n):\n    \"\"\"\n    Find the highest power of 2 that is less than or equal to n.\n\n    Args:\n        n (int): The input number.\n\n    Returns:\n        int: The highest power of 2 that is less than or equal to n.\n    \"\"\"\n    power = 0\n    while n > 1:\n        n = n // 2\n        power += 1\n    return 2 ** power\n\n# Test the function", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/412": [{"task_id": "Mbpp/412", "solution": "\"\"\"\nWrite a python function to remove odd numbers from a given list.\nassert remove_odd([1,2,3]) == [2]\n\"\"\"\n\"\"\"\nWrite a python function to remove odd numbers from a given list.\nassert remove_odd([1,2,3]) == [2]\n\"\"\"\ndef remove_odd(l):\n    return [i for i in l if i%2==0]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/391": [{"task_id": "Mbpp/391", "solution": "\"\"\"\nWrite a function to convert more than one list to nested dictionary.\nassert convert_list_dictionary([\"S001\", \"S002\", \"S003\", \"S004\"],[\"Adina Park\", \"Leyton Marsh\", \"<PERSON>\", \"<PERSON><PERSON>\"] ,[85, 98, 89, 92])==[{'S001': {'Adina Park': 85}}, {'S002': {'Leyton Marsh': 98}}, {'S003': {'<PERSON>': 89}}, {'S004': {'<PERSON><PERSON>': 92}}]\n\"\"\"\n\"\"\"\nWrite a function to convert more than one list to nested dictionary.\nassert convert_list_dictionary([\"S001\", \"S002\", \"S003\", \"S004\"],[\"Adina Park\", \"Leyton Marsh\", \"Duncan Boyle\", \"<PERSON><PERSON> Richards\"] ,[85, 98, 89, 92])==[{'S001': {'Adina Park': 85}}, {'S002': {'Leyton Marsh': 98}}, {'S003': {'<PERSON>': 89}}, {'S004': {'<PERSON><PERSON>': 92}}]\n\"\"\"\ndef convert_list_dictionary(list1, list2, list3):\n    dict1 = {}\n    for i in range(len(list1)):\n        dict1[list1[i]] = {list2[i]: list3[i]}\n    return [dict1]\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/409": [{"task_id": "Mbpp/409", "solution": "\"\"\"\nWrite a function to find the minimum product from the pairs of tuples within a given list.\nassert min_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==8\n\"\"\"\n\"\"\"\nWrite a function to find the minimum product from the pairs of tuples within a given list.\nassert min_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==8\n\"\"\"\ndef min_product_tuple(tuples):\n    min_product = float('inf')\n    for pair in tuples:\n        product = pair[0] * pair[1]\n        if product < min_product:\n            min_product = product\n    return min_product\n\n# Test the function\ntuples = [(2, 7), (2, 6), (1, 8), (4, 9)]\nresult = min_product_tuple(tuples)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/284": [{"task_id": "Mbpp/284", "solution": "\"\"\"\nWrite a function that takes in a list and element and checks whether all items in the list are equal to the given element.\nassert check_element([\"green\", \"orange\", \"black\", \"white\"],'blue')==False\n\"\"\"\n\"\"\"\nWrite a function that takes in a list and element and checks whether all items in the list are equal to the given element.\nassert check_element([\"green\", \"orange\", \"black\", \"white\"],'blue')==False\n\"\"\"\ndef check_element(list,element):\n    for i in list:\n        if i != element:\n            return False\n    return True", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/261": [{"task_id": "Mbpp/261", "solution": "\"\"\"\nWrite a function that takes in two tuples and performs mathematical division operation element-wise across the given tuples.\nassert division_elements((10, 4, 6, 9),(5, 2, 3, 3)) == (2, 2, 2, 3)\n\"\"\"\n\"\"\"\nWrite a function that takes in two tuples and performs mathematical division operation element-wise across the given tuples.\nassert division_elements((10, 4, 6, 9),(5, 2, 3, 3)) == (2, 2, 2, 3)\n\"\"\"\ndef division_elements(tuple1, tuple2):\n    # Check if the tuples have the same length\n    if len(tuple1) != len(tuple2):\n        raise ValueError(\"Tuples must have the same length\")\n\n    # Perform element-wise division\n    result = tuple(a / b for a, b in zip(tuple1, tuple2))\n\n    return result", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/414": [{"task_id": "Mbpp/414", "solution": "\"\"\"\nWrite a python function to check whether any value in a sequence exists in a sequence or not.\nassert overlapping([1,2,3,4,5],[6,7,8,9]) == False\n\"\"\"\n\"\"\"\nWrite a python function to check whether any value in a sequence exists in a sequence or not.\nassert overlapping([1,2,3,4,5],[6,7,8,9]) == False\n\"\"\"\ndef overlapping(seq1,seq2):\n    for i in seq1:\n        for j in seq2:\n            if i==j:\n                return True\n    return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/418": [{"task_id": "Mbpp/418", "solution": "\"\"\"\nWrite a python function to find the element of a list having maximum length.\nassert Find_Max([['A'],['A','B'],['A','B','C']]) == ['A','B','C']\n\"\"\"\n\"\"\"\nWrite a python function to find the element of a list having maximum length.\nassert Find_Max([['A'],['A','B'],['A','B','C']]) == ['A','B','C']\n\"\"\"\ndef Find_Max(list):\n    max = 0\n    for i in list:\n        if len(i) > max:\n            max = len(i)\n    return max", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[[1, 2, 3], [4, 5, 6], []]]]}], "Mbpp/394": [{"task_id": "Mbpp/394", "solution": "\"\"\"\nWrite a function to check if given tuple contains no duplicates.\nassert check_distinct((1, 4, 5, 6, 1, 4)) == False\n\"\"\"\n\"\"\"\nWrite a function to check if given tuple contains no duplicates.\nassert check_distinct((1, 4, 5, 6, 1, 4)) == False\n\"\"\"\nfrom typing import <PERSON>ple\n\n\ndef check_distinct(t: <PERSON><PERSON>) -> bool:\n    return len(set(t)) == len(t)\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/285": [{"task_id": "Mbpp/285", "solution": "\"\"\"\nWrite a function that checks whether a string contains the 'a' character followed by two or three 'b' characters.\nassert text_match_two_three(\"ac\")==(False)\n\"\"\"\n\"\"\"\nWrite a function that checks whether a string contains the 'a' character followed by two or three 'b' characters.\nassert text_match_two_three(\"ac\")==(False)\n\"\"\"\ndef text_match_two_three(text):\n    return \"ab\" in text or \"abbb\" in text", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/267": [{"task_id": "Mbpp/267", "solution": "\"\"\"\nWrite a python function that takes in an integer n and returns the sum of the squares of the first n odd natural numbers.\nassert square_Sum(2) == 10\n\"\"\"\n\"\"\"\nWrite a python function that takes in an integer n and returns the sum of the squares of the first n odd natural numbers.\nassert square_Sum(2) == 10\n\"\"\"\ndef square_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**2\n    return sum\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/292": [{"task_id": "Mbpp/292", "solution": "\"\"\"\nWrite a python function to find quotient of two numbers (rounded down to the nearest integer).\nassert find(10,3) == 3\n\"\"\"\n\"\"\"\nWrite a python function to find quotient of two numbers (rounded down to the nearest integer).\nassert find(10,3) == 3\n\"\"\"\ndef find(a,b):\n    return int(a/b)", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[-1, 2]]}], "Mbpp/419": [{"task_id": "Mbpp/419", "solution": "\"\"\"\nWrite a function to round every number of a given list of numbers and print the total sum multiplied by the length of the list.\nassert round_and_sum([22.4, 4.0, -16.22, -9.10, 11.00, -12.22, 14.20, -5.20, 17.50])==243\n\"\"\"\n\"\"\"\nWrite a function to round every number of a given list of numbers and print the total sum multiplied by the length of the list.\nassert round_and_sum([22.4, 4.0, -16.22, -9.10, 11.00, -12.22, 14.20, -5.20, 17.50])==243\n\"\"\"\ndef round_and_sum(numbers):\n    return sum([round(num) for num in numbers]) * len(numbers)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/262": [{"task_id": "Mbpp/262", "solution": "\"\"\"\nWrite a function that takes in a list and an integer L and splits the given list into two parts where the length of the first part of the list is L, and returns the resulting lists in a tuple.\nassert split_two_parts([1,1,2,3,4,4,5,1],3)==([1, 1, 2], [3, 4, 4, 5, 1])\n\"\"\"\n\"\"\"\nWrite a function that takes in a list and an integer L and splits the given list into two parts where the length of the first part of the list is L, and returns the resulting lists in a tuple.\nassert split_two_parts([1,1,2,3,4,4,5,1],3)==([1, 1, 2], [3, 4, 4, 5, 1])\n\"\"\"\ndef split_two_parts(lst, L):\n    return lst[:L], lst[L:]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/299": [{"task_id": "Mbpp/299", "solution": "\"\"\"\nWrite a function to calculate the maximum aggregate from the list of tuples.\nassert max_aggregate([('<PERSON>',90),('<PERSON>',88),('<PERSON>',7),('<PERSON>',122),('<PERSON>',84)])==('<PERSON>', 212)\n\"\"\"\n\"\"\"\nWrite a function to calculate the maximum aggregate from the list of tuples.\nassert max_aggregate([('<PERSON>',90),('<PERSON>',88),('<PERSON>',7),('<PERSON>',122),('<PERSON>',84)])==('<PERSON>', 212)\n\"\"\"\ndef max_aggregate(tuples):\n    max_aggregate = 0\n    max_name = \"\"\n    for name, score in tuples:\n        if score > max_aggregate:\n            max_aggregate = score\n            max_name = name\n    return max_name, max_aggregate\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/306": [{"task_id": "Mbpp/306", "solution": "\"\"\"\nWrite a function to find the maximum sum of increasing subsequence from prefix until ith index and also including a given kth element which is after i, i.e., k > i .\nassert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11\n\"\"\"\n\"\"\"\nWrite a function to find the maximum sum of increasing subsequence from prefix until ith index and also including a given kth element which is after i, i.e., k > i .\nassert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11\n\"\"\"\ndef max_sum_increasing_subseq(arr, n, k, i):\n    if i == n:\n        return 0\n    if k > i:\n        return max_sum_increasing_subseq(arr, n, k, i+1)\n    return max(arr[i] + max_sum_increasing_subseq(arr, n, k, i+1), max_sum_increasing_subseq(arr, n, k, i+1))\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/265": [{"task_id": "Mbpp/265", "solution": "\"\"\"\nWrite a function that takes in a list and an integer n and splits a list for every nth element, returning a list of the resulting lists.\nassert list_split(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'],3)==[['a', 'd', 'g', 'j', 'm'], ['b', 'e', 'h', 'k', 'n'], ['c', 'f', 'i', 'l']]\n\"\"\"\n\"\"\"\nWrite a function that takes in a list and an integer n and splits a list for every nth element, returning a list of the resulting lists.\nassert list_split(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'],3)==[['a', 'd', 'g', 'j', 'm'], ['b', 'e', 'h', 'k', 'n'], ['c', 'f', 'i', 'l']]\n\"\"\"\ndef list_split(lst, n):\n    return [lst[i::n] for i in range(n)]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/429": [{"task_id": "Mbpp/429", "solution": "\"\"\"\nWrite a function to extract the elementwise and tuples from the given two tuples.\nassert and_tuples((10, 4, 6, 9), (5, 2, 3, 3)) == (0, 0, 2, 1)\n\"\"\"\n\"\"\"\nWrite a function to extract the elementwise and tuples from the given two tuples.\nassert and_tuples((10, 4, 6, 9), (5, 2, 3, 3)) == (0, 0, 2, 1)\n\"\"\"\nfrom typing import <PERSON><PERSON>\n\n\ndef assert_and_tuples(t1: <PERSON><PERSON>[int, ...], t2: <PERSON><PERSON>[int, ...]) -> <PERSON><PERSON>[int, ...]:\n    return tuple(map(lambda x, y: x - y, t1, t2))", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/410": [{"task_id": "Mbpp/410", "solution": "\"\"\"\nWrite a function to find the minimum value in a given heterogeneous list.\nassert min_val(['Python', 3, 2, 4, 5, 'version'])==2\n\"\"\"\n\"\"\"\nWrite a function to find the minimum value in a given heterogeneous list.\nassert min_val(['Python', 3, 2, 4, 5, 'version'])==2\n\"\"\"\ndef min_val(l):\n    min = l[0]\n    for i in l:\n        if type(i) == int:\n            if i < min:\n                min = i\n        elif type(i) == str:\n            if i < min:\n                min = i\n    return min", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/266": [{"task_id": "Mbpp/266", "solution": "\"\"\"\nWrite a function to find the lateral surface area of a cube given its side length.\nassert lateralsurface_cube(5)==100\n\"\"\"\n\"\"\"\nWrite a function to find the lateral surface area of a cube given its side length.\nassert lateralsurface_cube(5)==100\n\"\"\"\ndef lateralsurface_cube(side_length):\n    return 4 * side_length ** 2\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/309": [{"task_id": "Mbpp/309", "solution": "\"\"\"\nWrite a python function to find the maximum of two numbers.\nassert maximum(5,10) == 10\n\"\"\"\n\"\"\"\nWrite a python function to find the maximum of two numbers.\nassert maximum(5,10) == 10\n\"\"\"\ndef maximum(a,b):\n    if a>b:\n        return a\n    else:\n        return b", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/435": [{"task_id": "Mbpp/435", "solution": "\"\"\"\nWrite a python function to find the last digit of a given number.\nassert last_Digit(123) == 3\n\"\"\"\n\"\"\"\nWrite a python function to find the last digit of a given number.\nassert last_Digit(123) == 3\n\"\"\"\ndef last_Digit(n):\n    return n%10\nn=int(input())", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/427": [{"task_id": "Mbpp/427", "solution": "\"\"\"\nWrite a function to convert a date of yyyy-mm-dd format to dd-mm-yyyy format.\nassert change_date_format(\"2026-01-02\") == '02-01-2026'\n\"\"\"\n\"\"\"\nWrite a function to convert a date of yyyy-mm-dd format to dd-mm-yyyy format.\nassert change_date_format(\"2026-01-02\") == '02-01-2026'\n\"\"\"\nimport re\n\n\ndef change_date_format(date: str) -> str:\n    \"\"\"\n    Function to convert a date of yyyy-mm-dd format to dd-mm-yyyy format.\n    :param date: date in yyyy-mm-dd format\n    :return: date in dd-mm-yyyy format\n    \"\"\"\n    if not re.match(r\"^\\d{4}-\\d{2}-\\d{2}$\", date):\n        raise ValueError(\"Invalid date format. Expected yyyy-mm-dd\")\n    return date.replace(\"-\", \"/\")[8:] + \"-\" + date.replace(\"-\", \"/\")[5:7] + \"-\" + date.replace(\"-\", \"/\")[0:4]", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/426": [{"task_id": "Mbpp/426", "solution": "\"\"\"\nWrite a function to filter odd numbers.\nassert filter_oddnumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1,3,5,7,9]\n\"\"\"\n\"\"\"\nWrite a function to filter odd numbers.\nassert filter_oddnumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1,3,5,7,9]\n\"\"\"\ndef filter_oddnumbers(list):\n    return [i for i in list if i%2!=0]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/392": [{"task_id": "Mbpp/392", "solution": "\"\"\"\nWrite a function to find the maximum sum possible by using the given equation f(n) = max( (f(n/2) + f(n/3) + f(n/4) + f(n/5)), n).\nassert get_max_sum(60) == 106\n\"\"\"\n\"\"\"\nWrite a function to find the maximum sum possible by using the given equation f(n) = max( (f(n/2) + f(n/3) + f(n/4) + f(n/5)), n).\nassert get_max_sum(60) == 106\n\"\"\"\nimport math\n\n\ndef get_max_sum(n):\n    if n == 1:\n        return 1\n    if n == 2:\n        return 2\n    if n == 3:\n        return 3\n    if n == 4:\n        return 4\n    if n == 5:\n        return 5\n    if n == 6:\n        return 6\n    if n == 7:\n        return 7\n    if n == 8:\n        return 8\n    if n == 9:\n        return 9\n    if n == 10:\n        return 10\n    if n == 11:\n        return 11\n    if n == 12:\n        return 12\n    if n == 13:\n        return 13\n    if n == 14:\n        return 14\n    if n == 15:\n        return 15\n    if n == 16:\n        return 16\n    if n == 17:\n        return 17\n    if n == 18:\n        return 18\n    if n == 19:\n        return 19\n    if n == 20:\n        return 20\n    if n == 21:\n        return 21\n    if n == 22:\n        return 22\n    if n == 23:\n        return 23\n    if n == 24:\n        return 24\n    if n == 25:\n        return 25\n    if n == 26:\n        return 26\n    if n == 27:\n        return 27\n    if n == 28:\n        return 28\n    if n == 29:\n        return 29\n    if n == 30:\n        return 30\n    if n == 31:\n        return 31\n    if n == 32:\n        return 32\n    if n == 33:\n        return 33\n    if n == 34:\n        return 34\n    if n == 35:\n        return 35\n    if n == 36:\n        return 36\n    if n == 37:\n        return 37\n    if n == 38:\n        return 38\n    if n == 39:\n        return 39\n    if n == 40:\n        return 40\n    if n == 41:\n        return 41\n    if n == 42:\n        return 42\n    if n == 43:\n        return 43\n    if n == 44:\n        return 44\n    if n == 45:\n        return 45\n    if n == 46:\n        return 46\n    if n == 47:\n        return 47\n    if n == 48:\n        return 48\n    if n == 49:\n        return 49\n    if n == 50:\n        return 50\n    if n == 51:\n        return 51\n    if n == 52:\n        return 52\n    if n == 53:\n        return 53\n    if n == 54:\n        return 54\n    if n == 55:\n        return 55\n    if n == ", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/440": [{"task_id": "Mbpp/440", "solution": "\"\"\"\nWrite a function to find the first adverb and their positions in a given sentence.\nassert find_adverb_position(\"clearly!! we can see the sky\")==(0, 7, 'clearly')\n\"\"\"\n\"\"\"\nWrite a function to find the first adverb and their positions in a given sentence.\nassert find_adverb_position(\"clearly!! we can see the sky\")==(0, 7, 'clearly')\n\"\"\"\nimport re\ndef find_adverb_position(sentence):\n    pattern = r\"\\b\\w+ly\\b\"\n    matches = re.finditer(pattern, sentence)\n    adverbs = [(match.start(), match.end(), match.group()) for match in matches]\n    return adverbs", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/424": [{"task_id": "Mbpp/424", "solution": "\"\"\"\nWrite a function to extract only the rear index element of each string in the given tuple.\nassert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']\n\"\"\"\n\"\"\"\nWrite a function to extract only the rear index element of each string in the given tuple.\nassert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']\n\"\"\"\nfrom typing import Tuple\n\n\ndef extract_rear(tup: Tuple[str]) -> str:\n    return [i[-1] for i in tup]\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/405": [{"task_id": "Mbpp/405", "solution": "\"\"\"\nWrite a function to check whether an element exists within a tuple.\nassert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'r')==True\n\"\"\"\n\"\"\"\nWrite a function to check whether an element exists within a tuple.\nassert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'r')==True\n\"\"\"\ndef check_tuplex(tuplex, element):\n    if element in tuplex:\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/413": [{"task_id": "Mbpp/413", "solution": "\"\"\"\nWrite a function to extract the nth element from a given list of tuples.\nassert extract_nth_element([('<PERSON><PERSON> Fulton', 98, 99), ('<PERSON>', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)] ,0)==['<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']\n\"\"\"\n\"\"\"\nWrite a function to extract the nth element from a given list of tuples.\nassert extract_nth_element([('<PERSON>son Fulton', 98, 99), ('<PERSON>', 97, 96), ('<PERSON>', 91, 94), ('<PERSON>', 94, 98)] ,0)==['<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']\n\"\"\"\nfrom typing import List, Tuple, Any\n\n\ndef extract_nth_element(lst: List[Tuple[Any, ...]], n: int) -> List[Any]:\n    return [item[n] for item in lst]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/259": [{"task_id": "Mbpp/259", "solution": "\"\"\"\nWrite a function to maximize the given two tuples.\nassert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))\n\"\"\"\n\"\"\"\nWrite a function to maximize the given two tuples.\nassert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))\n\"\"\"\nfrom typing import List, <PERSON><PERSON>\n\n\ndef maximize_elements(tuples1: List[Tuple[int, int]], tuples2: List[Tuple[int, int]]) -> List[Tuple[int, int]]:\n    return sorted(tuples1 + tuples2, key=lambda x: x[0] + x[1], reverse=True)\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/294": [{"task_id": "Mbpp/294", "solution": "\"\"\"\nWrite a function to find the maximum value in a given heterogeneous list.\nassert max_val(['Python', 3, 2, 4, 5, 'version'])==5\n\"\"\"\n\"\"\"\nWrite a function to find the maximum value in a given heterogeneous list.\nassert max_val(['Python', 3, 2, 4, 5, 'version'])==5\n\"\"\"\ndef max_val(l):\n    max = 0\n    for i in l:\n        if type(i) == int:\n            if i > max:\n                max = i\n        elif type(i) == str:\n            if i.isnumeric():\n                if int(i) > max:\n                    max = int(i)\n    return max\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/279": [{"task_id": "Mbpp/279", "solution": "\"\"\"\nWrite a function to find the nth decagonal number.\nassert is_num_decagonal(3) == 27\n\"\"\"\n\"\"\"\nWrite a function to find the nth decagonal number.\nassert is_num_decagonal(3) == 27\n\"\"\"\nimport math\n\n\ndef is_num_decagonal(n):\n    if n < 0:\n        return False\n    else:\n        return math.sqrt(30 * n + 49) % 1 == 0\n\n\ndef decagonal_number(n):\n    if is_num_decagonal(n):\n        return n * (2 * n - 1)\n    else:\n        return None\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/398": [{"task_id": "Mbpp/398", "solution": "\"\"\"\nWrite a function to compute the sum of digits of each number of a given list.\nassert sum_of_digits([10,2,56])==14\n\"\"\"\n\"\"\"\nWrite a function to compute the sum of digits of each number of a given list.\nassert sum_of_digits([10,2,56])==14\n\"\"\"\ndef sum_of_digits(list1):\n    sum=0\n    for i in list1:\n        sum=sum+sum_of_digits(i)\n    return sum", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[7]]]}], "Mbpp/445": [{"task_id": "Mbpp/445", "solution": "\"\"\"\nWrite a function to perform index wise multiplication of tuple elements in the given two tuples.\nassert index_multiplication(((1, 3), (4, 5), (2, 9), (1, 10)),((6, 7), (3, 9), (1, 1), (7, 3)) ) == ((6, 21), (12, 45), (2, 9), (7, 30))\n\"\"\"\n\"\"\"\nWrite a function to perform index wise multiplication of tuple elements in the given two tuples.\nassert index_multiplication(((1, 3), (4, 5), (2, 9), (1, 10)),((6, 7), (3, 9), (1, 1), (7, 3)) ) == ((6, 21), (12, 45), (2, 9), (7, 30))\n\"\"\"\nfrom typing import <PERSON>ple\n\n\ndef index_multiplication(tup1: <PERSON>ple[Tuple[int, ...]], tup2: <PERSON><PERSON>[Tuple[int, ...]]) -> <PERSON><PERSON>[Tuple[int, ...]]:\n    return tuple(tuple(a * b for a, b in zip(t1, t2)) for t1, t2 in zip(tup1, tup2))\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/297": [{"task_id": "Mbpp/297", "solution": "\"\"\"\nWrite a function to flatten a given nested list structure.\nassert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]\n\"\"\"\n\"\"\"\nWrite a function to flatten a given nested list structure.\nassert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]\n\"\"\"\ndef flatten_list(nested_list):\n    result = []\n    for item in nested_list:\n        if isinstance(item, list):\n            result.extend(flatten_list(item))\n        else:\n            result.append(item)\n    return result\n\n# Test the function\nnested_list = [0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/396": [{"task_id": "Mbpp/396", "solution": "\"\"\"\nWrite a function to check whether the given string starts and ends with the same character or not.\nassert check_char(\"abba\") == \"Valid\"\n\"\"\"\n\"\"\"\nWrite a function to check whether the given string starts and ends with the same character or not.\nassert check_char(\"abba\") == \"Valid\"\n\"\"\"\nimport re\n\n\ndef check_char(string):\n    if re.match(r\"^[a-zA-Z]([a-zA-Z])\\1$\", string):\n        return \"Valid\"\n    else:\n        return \"Invalid\"\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/312": [{"task_id": "Mbpp/312", "solution": "\"\"\"\nWrite a function to find the volume of a cone.\nassert math.isclose(volume_cone(5,12), 314.15926535897927, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the volume of a cone.\nassert math.isclose(volume_cone(5,12), 314.15926535897927, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef volume_cone(radius, height):\n    return (1/3) * math.pi * radius**2 * height\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/293": [{"task_id": "Mbpp/293", "solution": "\"\"\"\nWrite a function to find the third side of a right angled triangle.\nassert otherside_rightangle(7,8)==10.63014581273465\n\"\"\"\n\"\"\"\nWrite a function to find the third side of a right angled triangle.\nassert otherside_rightangle(7,8)==10.63014581273465\n\"\"\"\nimport math\ndef otherside_rightangle(a,b):\n    return math.sqrt(a**2+b**2)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/420": [{"task_id": "Mbpp/420", "solution": "\"\"\"\nWrite a python function to find the cube sum of first n even natural numbers.\nassert cube_Sum(2) == 72\n\"\"\"\n\"\"\"\nWrite a python function to find the cube sum of first n even natural numbers.\nassert cube_Sum(2) == 72\n\"\"\"\n#PF-Assgn-25\ndef cube_sum(n):\n    sum=0\n    for i in range(1,n+1):\n        sum=sum+(2*i)**3\n    return sum", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/428": [{"task_id": "Mbpp/428", "solution": "\"\"\"\nWrite a function to sort the given array by using shell sort.\nassert shell_sort([12, 23, 4, 5, 3, 2, 12, 81, 56, 95]) == [2, 3, 4, 5, 12, 12, 23, 56, 81, 95]\n\"\"\"\n\"\"\"\nWrite a function to sort the given array by using shell sort.\nassert shell_sort([12, 23, 4, 5, 3, 2, 12, 81, 56, 95]) == [2, 3, 4, 5, 12, 12, 23, 56, 81, 95]\n\"\"\"\nfrom typing import List\n\n\ndef shell_sort(arr: List[int]) -> List[int]:\n    n = len(arr)\n    gap = n // 2\n    while gap > 0:\n        for i in range(gap, n):\n            temp = arr[i]\n            j = i\n            while j >= gap and arr[j - gap] > temp:\n                arr[j] = arr[j - gap]\n                j -= gap\n            arr[j] = temp\n        gap //= 2\n    return arr\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/441": [{"task_id": "Mbpp/441", "solution": "\"\"\"\nWrite a function to find the surface area of a cube of a given size.\nassert surfacearea_cube(5)==150\n\"\"\"\n\"\"\"\nWrite a function to find the surface area of a cube of a given size.\nassert surfacearea_cube(5)==150\n\"\"\"\ndef surfacearea_cube(n):\n    return 6*n*n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/301": [{"task_id": "Mbpp/301", "solution": "\"\"\"\nWrite a function to find the depth of a dictionary.\nassert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4\n\"\"\"\n\"\"\"\nWrite a function to find the depth of a dictionary.\nassert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4\n\"\"\"\ndef dict_depth(d):\n    if isinstance(d, dict):\n        return 1 + max(dict_depth(v) for v in d.values()) if d else 0\n    return 0", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/421": [{"task_id": "Mbpp/421", "solution": "\"\"\"\nWrite a function to concatenate each element of tuple by the delimiter.\nassert concatenate_tuple((\"ID\", \"is\", 4, \"UTS\") ) == 'ID-is-4-UTS'\n\"\"\"\n\"\"\"\nWrite a function to concatenate each element of tuple by the delimiter.\nassert concatenate_tuple((\"ID\", \"is\", 4, \"UTS\") ) == 'ID-is-4-UTS'\n\"\"\"\ndef concatenate_tuple(tup, delimiter):\n    return delimiter.join(str(i) for i in tup)\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/415": [{"task_id": "Mbpp/415", "solution": "\"\"\"\nWrite a python function to find a pair with highest product from a given array of integers.\nassert max_Product([1,2,3,4,7,0,8,4]) == (7,8)\n\"\"\"\n\"\"\"\nWrite a python function to find a pair with highest product from a given array of integers.\nassert max_Product([1,2,3,4,7,0,8,4]) == (7,8)\n\"\"\"\ndef max_Product(arr):\n    max1 = max(arr)\n    arr.remove(max1)\n    max2 = max(arr)\n    return (max1,max2)\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/433": [{"task_id": "Mbpp/433", "solution": "\"\"\"\nWrite a function to check whether the entered number is greater than the elements of the given array.\nassert check_greater([1, 2, 3, 4, 5], 4) == False\n\"\"\"\n\"\"\"\nWrite a function to check whether the entered number is greater than the elements of the given array.\nassert check_greater([1, 2, 3, 4, 5], 4) == False\n\"\"\"\nfrom typing import List\n\n\ndef check_greater(arr: List[int], num: int) -> bool:\n    return num > max(arr)", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/447": [{"task_id": "Mbpp/447", "solution": "\"\"\"\nWrite a function to find cubes of individual elements in a list.\nassert cube_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 8, 27, 64, 125, 216, 343, 512, 729, 1000]\n\"\"\"\n\"\"\"\nWrite a function to find cubes of individual elements in a list.\nassert cube_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 8, 27, 64, 125, 216, 343, 512, 729, 1000]\n\"\"\"\ndef cube_nums(nums):\n    return [num**3 for num in nums]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/430": [{"task_id": "Mbpp/430", "solution": "\"\"\"\nWrite a function to find the directrix of a parabola.\nassert parabola_directrix(5,3,2)==-198\n\"\"\"\n\"\"\"\nWrite a function to find the directrix of a parabola.\nassert parabola_directrix(5,3,2)==-198\n\"\"\"\nimport math\ndef parabola_directrix(a,b,c):\n    return -b/(2*a) - math.sqrt((b**2 - 4*a*c)/4*a)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/400": [{"task_id": "Mbpp/400", "solution": "\"\"\"\nWrite a function to extract the number of unique tuples in the given list.\nassert extract_freq([(3, 4), (1, 2), (4, 3), (5, 6)] ) == 3\n\"\"\"\n\"\"\"\nWrite a function to extract the number of unique tuples in the given list.\nassert extract_freq([(3, 4), (1, 2), (4, 3), (5, 6)] ) == 3\n\"\"\"\nfrom collections import Counter\nfrom typing import List, <PERSON>ple\n\n\ndef extract_freq(lst: List[Tuple[int, int]]) -> int:\n    return len(set(Counter(lst).values()))\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/281": [{"task_id": "Mbpp/281", "solution": "\"\"\"\nWrite a python function to check if the elements of a given list are unique or not.\nassert all_unique([1,2,3]) == True\n\"\"\"\n\"\"\"\nWrite a python function to check if the elements of a given list are unique or not.\nassert all_unique([1,2,3]) == True\n\"\"\"\ndef all_unique(l):\n    return len(l) == len(set(l))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/273": [{"task_id": "Mbpp/273", "solution": "\"\"\"\nWrite a function that takes in two tuples and subtracts the elements of the first tuple by the elements of the second tuple with the same index.\nassert substract_elements((10, 4, 5), (2, 5, 18)) == (8, -1, -13)\n\"\"\"\n\"\"\"\nWrite a function that takes in two tuples and subtracts the elements of the first tuple by the elements of the second tuple with the same index.\nassert substract_elements((10, 4, 5), (2, 5, 18)) == (8, -1, -13)\n\"\"\"\ndef substract_elements(tuple1, tuple2):\n    return tuple(a - b for a, b in zip(tuple1, tuple2))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/280": [{"task_id": "Mbpp/280", "solution": "\"\"\"\nWrite a function that takes in an array and element and returns a tuple containing a boolean that indicates if the element is in the array and the index position of the element (or -1 if the element is not found).\nassert sequential_search([11,23,58,31,56,77,43,12,65,19],31) == (True, 3)\n\"\"\"\n\"\"\"\nWrite a function that takes in an array and element and returns a tuple containing a boolean that indicates if the element is in the array and the index position of the element (or -1 if the element is not found).\nassert sequential_search([11,23,58,31,56,77,43,12,65,19],31) == (True, 3)\n\"\"\"\ndef sequential_search(array, element):\n    for i in range(len(array)):\n        if array[i] == element:\n            return True, i\n    return False, -1\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/269": [{"task_id": "Mbpp/269", "solution": "\"\"\"\nWrite a function to find the ascii value of a character.\nassert ascii_value('A')==65\n\"\"\"\n\"\"\"\nWrite a function to find the ascii value of a character.\nassert ascii_value('A')==65\n\"\"\"\ndef ascii_value(char):\n    return ord(char)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/438": [{"task_id": "Mbpp/438", "solution": "\"\"\"\nWrite a function to count bidirectional tuple pairs.\nassert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 3\n\"\"\"\n\"\"\"\nWrite a function to count bidirectional tuple pairs.\nassert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 3\n\"\"\"\nfrom collections import defaultdict\nfrom typing import List, <PERSON><PERSON>\n\n\ndef count_bidirectional(tuples: List[Tuple[int, int]]) -> int:\n    \"\"\"\n    Count bidirectional tuple pairs.\n\n    Args:\n        tuples: List of tuples.\n\n    Returns:\n        Number of bidirectional tuple pairs.\n    \"\"\"\n    # Create a dictionary to store the count of each tuple\n    count_dict = defaultdict(int)\n\n    # Iterate over the tuples and increment the count for each tuple\n    for tup in tuples:\n        count_dict[tup] += 1\n\n    # Count the number of bidirectional tuple pairs\n    bidirectional_count = 0\n    for tup, count in count_dict.items():\n        bidirectional_count += count // 2\n\n    return bidirectional_count\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[[1, 1], [2, 1], [3, 1], [4, 1], [5, 1], [6, 1]]]]}], "Mbpp/446": [{"task_id": "Mbpp/446", "solution": "\"\"\"\nWrite a python function to count the occurence of all elements of list in a tuple.\nassert count_Occurrence(('a', 'a', 'c', 'b', 'd'),['a', 'b'] ) == 3\n\"\"\"\n\"\"\"\nWrite a python function to count the occurence of all elements of list in a tuple.\nassert count_Occurrence(('a', 'a', 'c', 'b', 'd'),['a', 'b'] ) == 3\n\"\"\"\ndef count_Occurrence(tup, lst):\n    count = 0\n    for i in lst:\n        for j in tup:\n            if i == j:\n                count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/431": [{"task_id": "Mbpp/431", "solution": "\"\"\"\nWrite a function that takes two lists and returns true if they have at least one common element.\nassert common_element([1,2,3,4,5], [5,6,7,8,9])==True\n\"\"\"\n\"\"\"\nWrite a function that takes two lists and returns true if they have at least one common element.\nassert common_element([1,2,3,4,5], [5,6,7,8,9])==True\n\"\"\"\ndef common_element(list1, list2):\n    for i in list1:\n        for j in list2:\n            if i == j:\n                return True\n    return False\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/439": [{"task_id": "Mbpp/439", "solution": "\"\"\"\nWrite a function to join a list of multiple integers into a single integer.\nassert multiple_to_single([11, 33, 50])==113350\n\"\"\"\n\"\"\"\nWrite a function to join a list of multiple integers into a single integer.\nassert multiple_to_single([11, 33, 50])==113350\n\"\"\"\ndef multiple_to_single(list):\n    return int(''.join(map(str, list)))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/270": [{"task_id": "Mbpp/270", "solution": "\"\"\"\nWrite a python function to find the sum of even numbers at even positions of a list.\nassert sum_even_and_even_index([5, 6, 12, 1, 18, 8]) == 30\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of even numbers at even positions of a list.\nassert sum_even_and_even_index([5, 6, 12, 1, 18, 8]) == 30\n\"\"\"\n#PF-Exer-28\ndef sum_of_elements(num_list):\n    sum=0\n    for i in range(0,len(num_list)):\n        if i%2==0 and num_list[i]%2==0:\n            sum=sum+num_list[i]\n    return sum\nnum_list=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/425": [{"task_id": "Mbpp/425", "solution": "\"\"\"\nWrite a function to count the number of sublists containing a particular element.\nassert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3\n\"\"\"\n\"\"\"\nWrite a function to count the number of sublists containing a particular element.\nassert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3\n\"\"\"\ndef count_element_in_list(list1,element):\n    count=0\n    for i in list1:\n        if element in i:\n            count+=1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/286": [{"task_id": "Mbpp/286", "solution": "\"\"\"\nWrite a function to find the largest sum of a contiguous array in the modified array which is formed by repeating the given array k times.\nassert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30\n\"\"\"\n\"\"\"\nWrite a function to find the largest sum of a contiguous array in the modified array which is formed by repeating the given array k times.\nassert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30\n\"\"\"\nfrom typing import List\n\n\ndef max_sub_array_sum_repeated(arr: List[int], k: int, n: int) -> int:\n    max_sum = 0\n    for i in range(k):\n        max_sum += max_sub_array_sum(arr, n)\n    return max_sum\n\n\ndef max_sub_array_sum(arr: List[int], n: int) -> int:\n    max_sum = 0\n    current_sum = 0\n    for i in range(n):\n        current_sum += arr[i]\n        if current_sum > max_sum:\n            max_sum = current_sum\n        if current_sum < 0:\n            current_sum = 0\n    return max_sum\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/282": [{"task_id": "Mbpp/282", "solution": "\"\"\"\nWrite a function to subtract two lists element-wise.\nassert sub_list([1, 2, 3],[4,5,6])==[-3,-3,-3]\n\"\"\"\n\"\"\"\nWrite a function to subtract two lists element-wise.\nassert sub_list([1, 2, 3],[4,5,6])==[-3,-3,-3]\n\"\"\"\ndef sub_list(list1, list2):\n    return [x - y for x, y in zip(list1, list2)]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/404": [{"task_id": "Mbpp/404", "solution": "\"\"\"\nWrite a python function to find the minimum of two numbers.\nassert minimum(1,2) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the minimum of two numbers.\nassert minimum(1,2) == 1\n\"\"\"\ndef minimum(a,b):\n    if a<b:\n        return a\n    else:\n        return b", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/448": [{"task_id": "Mbpp/448", "solution": "\"\"\"\nWrite a function to calculate the sum of perrin numbers.\nassert cal_sum(9) == 49\n\"\"\"\n\"\"\"\nWrite a function to calculate the sum of perrin numbers.\nassert cal_sum(9) == 49\n\"\"\"\ndef cal_sum(n):\n    if n == 0:\n        return 0\n    elif n == 1:\n        return 1\n    elif n == 2:\n        return 3\n    else:\n        return cal_sum(n-2) + cal_sum(n-3) + cal_sum(n-5)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/436": [{"task_id": "Mbpp/436", "solution": "\"\"\"\nWrite a python function to return the negative numbers in a list.\nassert neg_nos([-1,4,5,-6]) == [-1,-6]\n\"\"\"\n\"\"\"\nWrite a python function to return the negative numbers in a list.\nassert neg_nos([-1,4,5,-6]) == [-1,-6]\n\"\"\"\ndef neg_nos(lst):\n    return [i for i in lst if i<0]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/287": [{"task_id": "Mbpp/287", "solution": "\"\"\"\nWrite a python function takes in an integer n and returns the sum of squares of first n even natural numbers.\nassert square_Sum(2) == 20\n\"\"\"\n\"\"\"\nWrite a python function takes in an integer n and returns the sum of squares of first n even natural numbers.\nassert square_Sum(2) == 20\n\"\"\"\ndef square_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**2\n    return sum\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/308": [{"task_id": "Mbpp/308", "solution": "\"\"\"\nWrite a function to find the specified number of largest products from two given lists, selecting one factor from each list.\nassert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],3)==[60, 54, 50]\n\"\"\"\n\"\"\"\nWrite a function to find the specified number of largest products from two given lists, selecting one factor from each list.\nassert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],3)==[60, 54, 50]\n\"\"\"\nfrom itertools import combinations\nfrom functools import reduce\nfrom operator import mul\n\n\ndef large_product(list1, list2, n):\n    return sorted([reduce(mul, i) for i in combinations(list1, 1) for j in combinations(list2, 1) if i[0] * j[0] > 0], reverse=True)[:n]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[61.017129591114184, 19.303378474526568, 49.402419273875154, 49.402419273875154, -6.658136072450631, 55.36349167122134, 6.408487552626596, -22.555835342170255, -61.03464372753109, 76.32506038662768, 61.017129591114184], [61.017129591114184, 19.303378474526568, 49.402419273875154, 49.402419273875154, -6.658136072450631, 55.36349167122134, 6.408487552626596, -22.555835342170255, -61.03464372753109, 76.32506038662768, 61.017129591114184], true]]}], "Mbpp/442": [{"task_id": "Mbpp/442", "solution": "\"\"\"\nWrite a function to find the ration of positive numbers in an array of integers.\nassert positive_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.54\n\"\"\"\n\"\"\"\nWrite a function to find the ration of positive numbers in an array of integers.\nassert positive_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.54\n\"\"\"\ndef positive_count(arr):\n    count = 0\n    for i in arr:\n        if i > 0:\n            count += 1\n    return count/len(arr)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/300": [{"task_id": "Mbpp/300", "solution": "\"\"\"\nWrite a function to find the count of all binary sequences of length 2n such that sum of first n bits is same as sum of last n bits.\nassert math.isclose(count_binary_seq(1), 2.0, rel_tol=0.001)\n\"\"\"\n\"\"\"\nWrite a function to find the count of all binary sequences of length 2n such that sum of first n bits is same as sum of last n bits.\nassert math.isclose(count_binary_seq(1), 2.0, rel_tol=0.001)\n\"\"\"\nimport math\n\n\ndef count_binary_seq(n):\n    \"\"\"\n    :param n: int\n    :return: int\n    \"\"\"\n    return math.factorial(2 * n) // (math.factorial(n) * math.factorial(n))\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[50]]}], "Mbpp/268": [{"task_id": "Mbpp/268", "solution": "\"\"\"\nWrite a function to find the n'th star number.\nassert find_star_num(3) == 37\n\"\"\"\n\"\"\"\nWrite a function to find the n'th star number.\nassert find_star_num(3) == 37\n\"\"\"\nimport math\ndef find_star_num(n):\n    return (n**2)*(4*n-2)+1\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/422": [{"task_id": "Mbpp/422", "solution": "\"\"\"\nWrite a python function to find the average of cubes of first n natural numbers.\nassert find_Average_Of_Cube(2) == 4.5\n\"\"\"\n\"\"\"\nWrite a python function to find the average of cubes of first n natural numbers.\nassert find_Average_Of_Cube(2) == 4.5\n\"\"\"\ndef find_Average_Of_Cube(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += i**3\n    return sum/n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/450": [{"task_id": "Mbpp/450", "solution": "\"\"\"\nWrite a function to extract specified size of strings from a given list of string values.\nassert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,8)==['practice', 'solution']\n\"\"\"\n\"\"\"\nWrite a function to extract specified size of strings from a given list of string values.\nassert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,8)==['practice', 'solution']\n\"\"\"\ndef extract_string(str_list, size):\n    return [s for s in str_list if len(s) >= size]\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[["abcdef", "xyz", "pqr", "lmnop", "abcde"], 5]]}], "Mbpp/290": [{"task_id": "Mbpp/290", "solution": "\"\"\"\nWrite a function to find the list of maximum length in a list of lists.\nassert max_length([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])\n\"\"\"\n\"\"\"\nWrite a function to find the list of maximum length in a list of lists.\nassert max_length([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])\n\"\"\"\ndef max_length(lists):\n    max_len = 0\n    max_list = []\n    for i in lists:\n        if len(i) > max_len:\n            max_len = len(i)\n            max_list = i\n    return max_len, max_list\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/310": [{"task_id": "Mbpp/310", "solution": "\"\"\"\nWrite a function to convert a given string to a tuple of characters.\nassert string_to_tuple(\"python 3.0\")==('p', 'y', 't', 'h', 'o', 'n', '3', '.', '0')\n\"\"\"\n\"\"\"\nWrite a function to convert a given string to a tuple of characters.\nassert string_to_tuple(\"python 3.0\")==('p', 'y', 't', 'h', 'o', 'n', '3', '.', '0')\n\"\"\"\ndef string_to_tuple(str):\n    return tuple(str)\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["hello   world"]]}], "Mbpp/264": [{"task_id": "Mbpp/264", "solution": "\"\"\"\nWrite a function to calculate a dog's age in dog's years.\nassert dog_age(12)==61\n\"\"\"\n\"\"\"\nWrite a function to calculate a dog's age in dog's years.\nassert dog_age(12)==61\n\"\"\"\ndef dog_age(age):\n    if age <= 2:\n        return age * 10.5\n    else:\n        return 21 + (age - 2) * 4\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/432": [{"task_id": "Mbpp/432", "solution": "\"\"\"\nWrite a function to find the median length of a trapezium.\nassert median_trapezium(15,25,35)==20\n\"\"\"\n\"\"\"\nWrite a function to find the median length of a trapezium.\nassert median_trapezium(15,25,35)==20\n\"\"\"\ndef median_trapezium(a,b,c):\n    return (a+b+c)/2", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/256": [{"task_id": "Mbpp/256", "solution": "\"\"\"\nWrite a python function that takes in a non-negative number and returns the number of prime numbers less than the given non-negative number.\nassert count_Primes_nums(5) == 2\n\"\"\"\n\"\"\"\nWrite a python function that takes in a non-negative number and returns the number of prime numbers less than the given non-negative number.\nassert count_Primes_nums(5) == 2\n\"\"\"\ndef count_Primes_nums(n):\n    if n <= 1:\n        return 0\n    count = 0\n    for i in range(2, n):\n        if is_prime(i):\n            count += 1\n    return count\n\ndef is_prime(n):\n    if n <= 1:\n        return False\n    for i in range(2, n):\n        if n % i == 0:\n            return False\n    return True\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/451": [{"task_id": "Mbpp/451", "solution": "\"\"\"\nWrite a function to remove all whitespaces from the given string.\nassert remove_whitespaces(' Google    Flutter ') == 'GoogleFlutter'\n\"\"\"\n\"\"\"\nWrite a function to remove all whitespaces from the given string.\nassert remove_whitespaces(' Google    Flutter ') == 'GoogleFlutter'\n\"\"\"\nimport re\n\n\ndef remove_whitespaces(string):\n    return re.sub(r'\\s+', '', string)\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["\n\t"]]}], "Mbpp/454": [{"task_id": "Mbpp/454", "solution": "\"\"\"\nWrite a function that matches a word containing 'z'.\nassert text_match_wordz(\"pythonz.\")==True\n\"\"\"\n\"\"\"\nWrite a function that matches a word containing 'z'.\nassert text_match_wordz(\"pythonz.\")==True\n\"\"\"\nimport re\ndef text_match_wordz(text):\n    patterns = '.*z.*'\n    if re.search(patterns, text):\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/460": [{"task_id": "Mbpp/460", "solution": "\"\"\"\nWrite a python function to get the first element of each sublist.\nassert Extract([[1, 2], [3, 4, 5], [6, 7, 8, 9]]) == [1, 3, 6]\n\"\"\"\n\"\"\"\nWrite a python function to get the first element of each sublist.\nassert Extract([[1, 2], [3, 4, 5], [6, 7, 8, 9]]) == [1, 3, 6]\n\"\"\"\ndef Extract(lst):\n    return [sub[0] for sub in lst]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/475": [{"task_id": "Mbpp/475", "solution": "\"\"\"\nWrite a function to sort a dictionary by value.\nassert sort_counter({'Math':81, 'Physics':83, 'Chemistry':87})==[('Chemistry', 87), ('Physics', 83), ('Math', 81)]\n\"\"\"\n\"\"\"\nWrite a function to sort a dictionary by value.\nassert sort_counter({'Math':81, 'Physics':83, 'Chemistry':87})==[('Chemistry', 87), ('Physics', 83), ('Math', 81)]\n\"\"\"\ndef sort_counter(d):\n    return sorted(d.items(), key=lambda x: x[1], reverse=True)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/459": [{"task_id": "Mbpp/459", "solution": "\"\"\"\nWrite a function to remove uppercase substrings from a given string.\nassert remove_uppercase('cAstyoUrFavoRitETVshoWs') == 'cstyoravoitshos'\n\"\"\"\n\"\"\"\nWrite a function to remove uppercase substrings from a given string.\nassert remove_uppercase('cAstyoUrFavoRitETVshoWs') == 'cstyoravoitshos'\n\"\"\"\nimport re\n\n\ndef remove_uppercase(string: str) -> str:\n    return re.sub(r'[A-Z]', '', string)\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["ThiS%^%!s&a(mY)TesTStR%i*ng"]]}], "Mbpp/455": [{"task_id": "Mbpp/455", "solution": "\"\"\"\nWrite a function to check whether the given month number contains 31 days or not.\nassert check_monthnumb_number(5)==True\n\"\"\"\n\"\"\"\nWrite a function to check whether the given month number contains 31 days or not.\nassert check_monthnumb_number(5)==True\n\"\"\"\ndef check_monthnumb_number(month):\n    if month in [1,3,5,7,8,10,12]:\n        return True\n    elif month in [4,6,9,11]:\n        return False\n    else:\n        return \"Invalid month number\"", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[2]], "plus_fail_tests": [[2]]}], "Mbpp/456": [{"task_id": "Mbpp/456", "solution": "\"\"\"\nWrite a function to reverse each string in a given list of string values.\nassert reverse_string_list(['<PERSON>', '<PERSON>', '<PERSON>', 'White', 'Black'])==['deR', 'neerG', 'eulB', 'etihW', 'kcalB']\n\"\"\"\n\"\"\"\nWrite a function to reverse each string in a given list of string values.\nassert reverse_string_list(['Red', 'Green', 'Blue', 'White', 'Black'])==['deR', 'neerG', 'eulB', 'etihW', 'kcalB']\n\"\"\"\ndef reverse_string_list(list1):\n    list2 = []\n    for i in list1:\n        list2.append(i[::-1])\n    return list2", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/559": [{"task_id": "Mbpp/559", "solution": "\"\"\"\nWrite a function to find the sum of the largest contiguous sublist in the given list.\nassert max_sub_array_sum([-2, -3, 4, -1, -2, 1, 5, -3], 8) == 7\n\"\"\"\n\"\"\"\nWrite a function to find the sum of the largest contiguous sublist in the given list.\nassert max_sub_array_sum([-2, -3, 4, -1, -2, 1, 5, -3], 8) == 7\n\"\"\"\nfrom typing import List\n\n\ndef max_sub_array_sum(arr: List[int], k: int) -> int:\n    \"\"\"\n    Time Complexity: O(n)\n    Space Complexity: O(1)\n    \"\"\"\n    if k > len(arr):\n        raise ValueError(\"k cannot be greater than the length of the array\")\n\n    max_sum = 0\n    current_sum = 0\n    for i in range(k):\n        current_sum += arr[i]\n\n    max_sum = current_sum\n    for i in range(k, len(arr)):\n        current_sum += arr[i] - arr[i - k]\n        max_sum = max(max_sum, current_sum)\n\n    return max_sum\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[-10, -5, -3, -2, -1], 5]]}], "Mbpp/461": [{"task_id": "Mbpp/461", "solution": "\"\"\"\nWrite a python function to count the upper case characters in a given string.\nassert upper_ctr('PYthon') == 1\n\"\"\"\n\"\"\"\nWrite a python function to count the upper case characters in a given string.\nassert upper_ctr('PYthon') == 1\n\"\"\"\ndef upper_ctr(str1):\n    count = 0\n    for i in str1:\n        if i.isupper():\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/458": [{"task_id": "Mbpp/458", "solution": "\"\"\"\nWrite a function to find the area of a rectangle.\nassert rectangle_area(10,20)==200\n\"\"\"\n\"\"\"\nWrite a function to find the area of a rectangle.\nassert rectangle_area(10,20)==200\n\"\"\"\ndef rectangle_area(a,b):\n    return a*b", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/457": [{"task_id": "Mbpp/457", "solution": "\"\"\"\nWrite a python function to find the sublist having minimum length.\nassert Find_Min([[1],[1,2],[1,2,3]]) == [1]\n\"\"\"\n\"\"\"\nWrite a python function to find the sublist having minimum length.\nassert Find_Min([[1],[1,2],[1,2,3]]) == [1]\n\"\"\"\ndef Find_Min(list1):\n    min = len(list1[0])\n    for i in list1:\n        if len(i) < min:\n            min = len(i)\n    return min", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[["x"]]]]}], "Mbpp/556": [{"task_id": "Mbpp/556", "solution": "\"\"\"\nWrite a python function to count the number of pairs whose xor value is odd.\nassert find_Odd_Pair([5,4,7,2,1],5) == 6\n\"\"\"\n\"\"\"\nWrite a python function to count the number of pairs whose xor value is odd.\nassert find_Odd_Pair([5,4,7,2,1],5) == 6\n\"\"\"\ndef find_Odd_Pair(arr,n):\n    count=0\n    for i in range(n):\n        for j in range(i+1,n):\n            if (arr[i]^arr[j])%2!=0:\n                count+=1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/567": [{"task_id": "Mbpp/567", "solution": "\"\"\"\nWrite a function to check whether a specified list is sorted or not.\nassert issort_list([1,2,4,6,8,10,12,14,16,17])==True\n\"\"\"\n\"\"\"\nWrite a function to check whether a specified list is sorted or not.\nassert issort_list([1,2,4,6,8,10,12,14,16,17])==True\n\"\"\"\ndef issort_list(list1):\n    for i in range(len(list1)-1):\n        if list1[i] > list1[i+1]:\n            return False\n    return True", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/558": [{"task_id": "Mbpp/558", "solution": "\"\"\"\nWrite a python function to find the sum of the per-digit difference between two integers.\nassert digit_distance_nums(1,2) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of the per-digit difference between two integers.\nassert digit_distance_nums(1,2) == 1\n\"\"\"\ndef digit_distance_nums(num1,num2):\n    sum=0\n    while num1>0 and num2>0:\n        sum+=abs(num1%10-num2%10)\n        num1//=10\n        num2//=10\n    return sum", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[12345, 9]]}], "Mbpp/468": [{"task_id": "Mbpp/468", "solution": "\"\"\"\nWrite a function to find the maximum product formed by multiplying numbers of an increasing subsequence of that array.\nassert max_product([3, 100, 4, 5, 150, 6]) == 3000\n\"\"\"\n\"\"\"\nWrite a function to find the maximum product formed by multiplying numbers of an increasing subsequence of that array.\nassert max_product([3, 100, 4, 5, 150, 6]) == 3000\n\"\"\"\nfrom typing import List\n\n\ndef max_product(nums: List[int]) -> int:\n    if not nums:\n        return 0\n    max_product = nums[0]\n    for i in range(1, len(nums)):\n        for j in range(i):\n            if nums[i] > nums[j]:\n                max_product = max(max_product, nums[i] * nums[j])\n    return max_product\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[3, -5, 2, 6, -1, 4, -3]]]}], "Mbpp/473": [{"task_id": "Mbpp/473", "solution": "\"\"\"\nWrite a function to find the tuple intersection of elements in the given tuple list irrespective of their order.\nassert tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)] , [(5, 4), (3, 4), (6, 5), (9, 11)]) == {(4, 5), (3, 4), (5, 6)}\n\"\"\"\n\"\"\"\nWrite a function to find the tuple intersection of elements in the given tuple list irrespective of their order.\nassert tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)] , [(5, 4), (3, 4), (6, 5), (9, 11)]) == {(4, 5), (3, 4), (5, 6)}\n\"\"\"\nfrom typing import List, Tuple\n\n\ndef tuple_intersection(tup1: List[Tuple[int, int]], tup2: List[Tuple[int, int]]) -> set:\n    \"\"\"\n    Find the tuple intersection of elements in the given tuple list irrespective of their order.\n    :param tup1: List of tuples\n    :param tup2: List of tuples\n    :return: Set of tuples\n    \"\"\"\n    return set(tup1).intersection(set(tup2))\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[[1, 2, 3], [4, 5, 6], [7, 8, 9], [10, 11, 12]], [[2, 3, 1], [6, 5, 4], [11, 12, 10]]]]}], "Mbpp/479": [{"task_id": "Mbpp/479", "solution": "\"\"\"\nWrite a python function to find the first digit of a given number.\nassert first_Digit(123) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the first digit of a given number.\nassert first_Digit(123) == 1\n\"\"\"\ndef first_Digit(n):\n    while n>=10:\n        n=n/10\n    return int(n)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/560": [{"task_id": "Mbpp/560", "solution": "\"\"\"\nWrite a function to find the union of the elements of two given tuples and output them in sorted order.\nassert union_elements((3, 4, 5, 6),(5, 7, 4, 10) ) == (3, 4, 5, 6, 7, 10)\n\"\"\"\n\"\"\"\nWrite a function to find the union of the elements of two given tuples and output them in sorted order.\nassert union_elements((3, 4, 5, 6),(5, 7, 4, 10) ) == (3, 4, 5, 6, 7, 10)\n\"\"\"\ndef union_elements(t1,t2):\n    t3=t1+t2\n    t3=list(set(t3))\n    t3.sort()\n    return tuple(t3)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/471": [{"task_id": "Mbpp/471", "solution": "\"\"\"\nWrite a python function to find the product of the array multiplication modulo n.\nassert find_remainder([ 100, 10, 5, 25, 35, 14 ],11) ==9\n\"\"\"\n\"\"\"\nWrite a python function to find the product of the array multiplication modulo n.\nassert find_remainder([ 100, 10, 5, 25, 35, 14 ],11) ==9\n\"\"\"\ndef find_remainder(arr,n):\n    product=1\n    for i in arr:\n        product=(product*i)%n\n    return product", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/462": [{"task_id": "Mbpp/462", "solution": "\"\"\"\nWrite a function to find all possible combinations of the elements of a given list.\nassert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]\n\"\"\"\n\"\"\"\nWrite a function to find all possible combinations of the elements of a given list.\nassert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]\n\"\"\"\nfrom itertools import combinations\ndef combinations_list(lst):\n    result = []\n    for i in range(len(lst)+1):\n        for j in combinations(lst, i):\n            result.append(list(j))\n    return result", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/581": [{"task_id": "Mbpp/581", "solution": "\"\"\"\nWrite a python function to find the surface area of a square pyramid with a given base edge and height.\nassert surface_Area(3,4) == 33\n\"\"\"\n\"\"\"\nWrite a python function to find the surface area of a square pyramid with a given base edge and height.\nassert surface_Area(3,4) == 33\n\"\"\"\ndef surface_Area(base_edge,height):\n    return base_edge**2 + 2*base_edge*height", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/564": [{"task_id": "Mbpp/564", "solution": "\"\"\"\nWrite a python function which takes a list of integers and counts the number of possible unordered pairs where both elements are unequal.\nassert count_Pairs([1,2,1],3) == 2\n\"\"\"\n\"\"\"\nWrite a python function which takes a list of integers and counts the number of possible unordered pairs where both elements are unequal.\nassert count_Pairs([1,2,1],3) == 2\n\"\"\"\ndef count_Pairs(lst,n):\n    count = 0\n    for i in range(n):\n        for j in range(i+1,n):\n            if lst[i] != lst[j]:\n                count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/587": [{"task_id": "Mbpp/587", "solution": "\"\"\"\nWrite a function to convert a list to a tuple.\nassert list_tuple([5, 10, 7, 4, 15, 3])==(5, 10, 7, 4, 15, 3)\n\"\"\"\n\"\"\"\nWrite a function to convert a list to a tuple.\nassert list_tuple([5, 10, 7, 4, 15, 3])==(5, 10, 7, 4, 15, 3)\n\"\"\"\ndef list_tuple(list1):\n    return tuple(list1)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/585": [{"task_id": "Mbpp/585", "solution": "\"\"\"\nWrite a function to find the n most expensive items in a given dataset.\nassert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}],1)==[{'name': 'Item-2', 'price': 555.22}]\n\"\"\"\n\"\"\"\nWrite a function to find the n most expensive items in a given dataset.\nassert expensive_items([{'name': 'Item-1', 'price': 101.1},{'name': 'Item-2', 'price': 555.22}],1)==[{'name': 'Item-2', 'price': 555.22}]\n\"\"\"\nimport pytest\nfrom typing import List, Dict\n\n\ndef expensive_items(items: List[Dict[str, float]], n: int) -> List[Dict[str, float]]:\n    \"\"\"\n    Find the n most expensive items in a given dataset.\n\n    Args:\n        items (List[Dict[str, float]]): A list of dictionaries representing items with 'name' and 'price' keys.\n        n (int): The number of most expensive items to return.\n\n    Returns:\n        List[Dict[str, float]]: A list of dictionaries representing the n most expensive items.\n\n    Raises:\n        ValueError: If n is less than 1 or greater than the number of items in the dataset.\n\n    Examples:\n        >>> expensive_items([{'name': 'Item-1', 'price': 101.1}, {'name': 'Item-2', 'price': 555.22}], 1)\n        [{'name': 'Item-2', 'price': 555.22}]\n        >>> expensive_items([{'name': 'Item-1', 'price': 101.1}, {'name': 'Item-2', 'price': 555.22}], 2)\n        [{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-1', 'price': 101.1}]\n    \"\"\"\n    if n < 1 or n > len(items):\n        raise ValueError(\"n must be between 1 and the number of items in the dataset\")\n\n    sorted_items = sorted(items, key=lambda x: x['price'], reverse=True)\n    return sorted_items[:n]\n\n\ndef test_expensive_items():\n    items = [\n        {'name': 'Item-1', 'price': 101.1},\n        {'name': 'Item-2', 'price': 555.22},\n        {'name': 'Item-3', 'price': 200.5},\n        {'name': 'Item-4', 'price': 750.0},\n        {'name': 'Item-5', 'price': 150.0}\n    ]\n    assert expensive_items(items, 1) == [{'name': 'Item-2', 'price': 555.22}]\n    assert expensive_items(items, 2) == [{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-1', 'price': 101.1}]\n    assert expensive_items(items, 3) == [{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-1', 'price': 101.1}, {'name': 'Item-3', 'price': 200.5}]\n    assert expensive_items(items, 4) == [{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-1', 'price': 101.1}, {'name': 'Item-3', 'price': 200.5}, {'name': 'Item-4', 'price': 750.0}]\n    assert expensive_items(items, 5) == [{'name': 'Item-2', 'price': 555.22}, {'name': 'Item-1', 'price': 101.1}, {'name': 'Item-3', 'price': 200.5}, {'name': 'Item-4', 'price': 750.0}, {'name': 'Item", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/582": [{"task_id": "Mbpp/582", "solution": "\"\"\"\nWrite a function to check if a dictionary is empty\nassert my_dict({10})==False\n\"\"\"\n\"\"\"\nWrite a function to check if a dictionary is empty\nassert my_dict({10})==False\n\"\"\"\ndef my_dict(d):\n    if len(d) == 0:\n        return True\n    else:\n        return False\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/472": [{"task_id": "Mbpp/472", "solution": "\"\"\"\nWrite a python function to check whether the given list contains consecutive numbers or not.\nassert check_Consecutive([1,2,3,4,5]) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether the given list contains consecutive numbers or not.\nassert check_Consecutive([1,2,3,4,5]) == True\n\"\"\"\ndef check_Consecutive(list1):\n    list1.sort()\n    for i in range(len(list1)-1):\n        if list1[i]+1 != list1[i+1]:\n            return False\n    return True", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/565": [{"task_id": "Mbpp/565", "solution": "\"\"\"\nWrite a python function to split a string into characters.\nassert split('python') == ['p','y','t','h','o','n']\n\"\"\"\n\"\"\"\nWrite a python function to split a string into characters.\nassert split('python') == ['p','y','t','h','o','n']\n\"\"\"\ndef split(string):\n    return [char for char in string]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/573": [{"task_id": "Mbpp/573", "solution": "\"\"\"\nWrite a python function to calculate the product of the unique numbers in a given list.\nassert unique_product([10, 20, 30, 40, 20, 50, 60, 40]) ==  720000000\n\"\"\"\n\"\"\"\nWrite a python function to calculate the product of the unique numbers in a given list.\nassert unique_product([10, 20, 30, 40, 20, 50, 60, 40]) ==  720000000\n\"\"\"\ndef unique_product(list1):\n    product = 1\n    for i in list1:\n        if list1.count(i) == 1:\n            product = product * i\n    return product", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/470": [{"task_id": "Mbpp/470", "solution": "\"\"\"\nWrite a function to find the pairwise addition of the neighboring elements of the given tuple.\nassert add_pairwise((1, 5, 7, 8, 10)) == (6, 12, 15, 18)\n\"\"\"\n\"\"\"\nWrite a function to find the pairwise addition of the neighboring elements of the given tuple.\nassert add_pairwise((1, 5, 7, 8, 10)) == (6, 12, 15, 18)\n\"\"\"\nfrom typing import <PERSON><PERSON>\n\n\ndef add_pairwise(tup: Tuple[int]) -> <PERSON>ple[int]:\n    \"\"\"\n    Add neighboring elements of the given tuple\n    :param tup: tuple of integers\n    :return: tuple of integers\n    \"\"\"\n    return tuple(a + b for a, b in zip(tup, tup[1:]))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/608": [{"task_id": "Mbpp/608", "solution": "\"\"\"\nWrite a python function to find nth bell number.\nassert bell_Number(2) == 2\n\"\"\"\n\"\"\"\nWrite a python function to find nth bell number.\nassert bell_Number(2) == 2\n\"\"\"\ndef bell_number(n):\n    bell = [[0 for i in range(n+1)] for j in range(n+1)]\n    bell[0][0] = 1\n    for i in range(1, n+1):\n        bell[i][0] = bell[i-1][i-1]\n        for j in range(1, i+1):\n            bell[i][j] = bell[i-1][j-1] + bell[i][j-1]\n    return bell[n][0]\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/622": [{"task_id": "Mbpp/622", "solution": "\"\"\"\nWrite a function to find the median of two sorted lists of same size.\nassert get_median([1, 12, 15, 26, 38], [2, 13, 17, 30, 45], 5) == 16.0\n\"\"\"\n\"\"\"\nWrite a function to find the median of two sorted lists of same size.\nassert get_median([1, 12, 15, 26, 38], [2, 13, 17, 30, 45], 5) == 16.0\n\"\"\"\nimport math\n\n\ndef get_median(list1, list2, size):\n    if size % 2 == 0:\n        return (get_median_helper(list1, list2, size / 2) + get_median_helper(list1, list2, size / 2 + 1)) / 2\n    else:\n        return get_median_helper(list1, list2, size / 2 + 1)\n\n\ndef get_median_helper(list1, list2, index):\n    if index == 1:\n        return min(list1[0], list2[0])\n    else:\n        if list1[index - 1] < list2[index - 1]:\n            return get_median_helper(list1[index:], list2[index:], index)\n        else:\n            return get_median_helper(list1[:index], list2[:index], index)\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/554": [{"task_id": "Mbpp/554", "solution": "\"\"\"\nWrite a python function which takes a list of integers and only returns the odd ones.\nassert Split([1,2,3,4,5,6]) == [1,3,5]\n\"\"\"\n\"\"\"\nWrite a python function which takes a list of integers and only returns the odd ones.\nassert Split([1,2,3,4,5,6]) == [1,3,5]\n\"\"\"\ndef split(list):\n    odd = []\n    for i in list:\n        if i % 2 != 0:\n            odd.append(i)\n    return odd\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/607": [{"task_id": "Mbpp/607", "solution": "\"\"\"\nWrite a function to search a string for a regex pattern. The function should return the matching subtring, a start index and an end index.\nassert find_literals('The quick brown fox jumps over the lazy dog.', 'fox') == ('fox', 16, 19)\n\"\"\"\n\"\"\"\nWrite a function to search a string for a regex pattern. The function should return the matching subtring, a start index and an end index.\nassert find_literals('The quick brown fox jumps over the lazy dog.', 'fox') == ('fox', 16, 19)\n\"\"\"\nimport re\n\n\ndef find_literals(text, pattern):\n    match = re.search(pattern, text)\n    if match:\n        return match.group(), match.start(), match.end()\n    else:\n        return None\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/463": [{"task_id": "Mbpp/463", "solution": "\"\"\"\nWrite a function to find the maximum product subarray of the given array.\nassert max_subarray_product([1, -2, -3, 0, 7, -8, -2]) == 112\n\"\"\"\n\"\"\"\nWrite a function to find the maximum product subarray of the given array.\nassert max_subarray_product([1, -2, -3, 0, 7, -8, -2]) == 112\n\"\"\"\nfrom typing import List\n\n\ndef max_subarray_product(nums: List[int]) -> int:\n    if not nums:\n        return 0\n    max_product = min_product = result = nums[0]\n    for num in nums[1:]:\n        if num < 0:\n            max_product, min_product = min_product, max_product\n        max_product = max(num, max_product * num)\n        min_product = min(num, min_product * num)\n        result = max(result, max_product)\n    return result\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/583": [{"task_id": "Mbpp/583", "solution": "\"\"\"\nWrite a function which returns nth catalan number.\nassert catalan_number(10)==16796\n\"\"\"\n\"\"\"\nWrite a function which returns nth catalan number.\nassert catalan_number(10)==16796\n\"\"\"\ndef catalan_number(n):\n    if n==0 or n==1:\n        return 1\n    else:\n        return catalan_number(n-1)*(4*n-2)/(n+1)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/566": [{"task_id": "Mbpp/566", "solution": "\"\"\"\nWrite a function to get the sum of the digits of a non-negative integer.\nassert sum_digits(345)==12\n\"\"\"\n\"\"\"\nWrite a function to get the sum of the digits of a non-negative integer.\nassert sum_digits(345)==12\n\"\"\"\ndef sum_digits(n):\n    if n == 0:\n        return 0\n    else:\n        return n % 10 + sum_digits(n // 10)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/555": [{"task_id": "Mbpp/555", "solution": "\"\"\"\nWrite a python function to find the difference between the sum of cubes of the first n natural numbers and the sum of the first n natural numbers.\nassert difference(3) == 30\n\"\"\"\n\"\"\"\nWrite a python function to find the difference between the sum of cubes of the first n natural numbers and the sum of the first n natural numbers.\nassert difference(3) == 30\n\"\"\"\n#lex_auth_012694465248329728100\n\ndef difference(n):\n    sum_of_natural_numbers=0\n    sum_of_cubes=0\n    for i in range(1,n+1):\n        sum_of_natural_numbers+=i\n        sum_of_cubes+=i**3\n    return sum_of_cubes-sum_of_natural_numbers\n\n#Provide different values for n and test your program\ndifference(3)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/629": [{"task_id": "Mbpp/629", "solution": "\"\"\"\nWrite a python function to find even numbers from a list of numbers.\nassert Split([1,2,3,4,5]) == [2,4]\n\"\"\"\n\"\"\"\nWrite a python function to find even numbers from a list of numbers.\nassert Split([1,2,3,4,5]) == [2,4]\n\"\"\"\ndef split_list(list1):\n    even_list = []\n    for i in list1:\n        if i % 2 == 0:\n            even_list.append(i)\n    return even_list\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/562": [{"task_id": "Mbpp/562", "solution": "\"\"\"\nWrite a python function to find the length of the longest sublists.\nassert Find_Max_Length([[1],[1,4],[5,6,7,8]]) == 4\n\"\"\"\n\"\"\"\nWrite a python function to find the length of the longest sublists.\nassert Find_Max_Length([[1],[1,4],[5,6,7,8]]) == 4\n\"\"\"\ndef Find_Max_Length(lst):\n    max_len = 0\n    for i in lst:\n        if len(i) > max_len:\n            max_len = len(i)\n    return max_len", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/477": [{"task_id": "Mbpp/477", "solution": "\"\"\"\nWrite a python function to convert the given string to lower case.\nassert is_lower(\"InValid\") == \"invalid\"\n\"\"\"\n\"\"\"\nWrite a python function to convert the given string to lower case.\nassert is_lower(\"InValid\") == \"invalid\"\n\"\"\"\ndef is_lower(str):\n    return str.lower()\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/476": [{"task_id": "Mbpp/476", "solution": "\"\"\"\nWrite a python function to find the sum of the largest and smallest value in a given array.\nassert big_sum([1,2,3]) == 4\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of the largest and smallest value in a given array.\nassert big_sum([1,2,3]) == 4\n\"\"\"\ndef big_sum(arr):\n    return max(arr) + min(arr)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/590": [{"task_id": "Mbpp/590", "solution": "\"\"\"\nWrite a function to convert polar coordinates to rectangular coordinates.\nassert polar_rect(3,4)==((5.0, 0.9272952180016122), (-2+2.4492935982947064e-16j))\n\"\"\"\n\"\"\"\nWrite a function to convert polar coordinates to rectangular coordinates.\nassert polar_rect(3,4)==((5.0, 0.9272952180016122), (-2+2.4492935982947064e-16j))\n\"\"\"\nimport math\ndef polar_rect(r, theta):\n    x = r * math.cos(theta)\n    y = r * math.sin(theta)\n    return (x, y), (x + 1j * y)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/465": [{"task_id": "Mbpp/465", "solution": "\"\"\"\nWrite a function to drop empty items from a given dictionary.\nassert drop_empty({'c1': 'Red', 'c2': 'Green', 'c3':None})=={'c1': 'Red', 'c2': 'Green'}\n\"\"\"\n\"\"\"\nWrite a function to drop empty items from a given dictionary.\nassert drop_empty({'c1': 'Red', 'c2': 'Green', 'c3':None})=={'c1': 'Red', 'c2': 'Green'}\n\"\"\"\ndef drop_empty(dict1):\n    return {k:v for k,v in dict1.items() if v is not None}\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/478": [{"task_id": "Mbpp/478", "solution": "\"\"\"\nWrite a function to remove lowercase substrings from a given string.\nassert remove_lowercase(\"PYTHon\")==('PYTH')\n\"\"\"\n\"\"\"\nWrite a function to remove lowercase substrings from a given string.\nassert remove_lowercase(\"PYTHon\")==('PYTH')\n\"\"\"\ndef remove_lowercase(s):\n    return ''.join([i for i in s if i.isupper()])\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/579": [{"task_id": "Mbpp/579", "solution": "\"\"\"\nWrite a function to find the dissimilar elements in the given two tuples.\nassert find_dissimilar((3, 4, 5, 6), (5, 7, 4, 10)) == (3, 6, 7, 10)\n\"\"\"\n\"\"\"\nWrite a function to find the dissimilar elements in the given two tuples.\nassert find_dissimilar((3, 4, 5, 6), (5, 7, 4, 10)) == (3, 6, 7, 10)\n\"\"\"\nfrom typing import <PERSON><PERSON>\n\n\ndef find_dissimilar(tup1: <PERSON><PERSON>, tup2: <PERSON><PERSON>) -> <PERSON><PERSON>:\n    \"\"\"Find dissimilar elements in the given two tuples.\"\"\"\n    return tuple(set(tup1) ^ set(tup2))", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[["a", "b", "c", "d", "e", "f", "g"], ["h", "i", "j", "k", "l", "m", "n"]]]}], "Mbpp/569": [{"task_id": "Mbpp/569", "solution": "\"\"\"\nWrite a function to sort each sublist of strings in a given list of lists.\nassert sort_sublists([['green', 'orange'], ['black', 'white'], ['white', 'black', 'orange']])==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n\"\"\"\n\"\"\"\nWrite a function to sort each sublist of strings in a given list of lists.\nassert sort_sublists([['green', 'orange'], ['black', 'white'], ['white', 'black', 'orange']])==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]\n\"\"\"\ndef sort_sublists(lists):\n    return [sorted(sublist) for sublist in lists]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/592": [{"task_id": "Mbpp/592", "solution": "\"\"\"\nWrite a python function to find the sum of the product of consecutive binomial co-efficients.\nassert sum_Of_product(3) == 15\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of the product of consecutive binomial co-efficients.\nassert sum_Of_product(3) == 15\n\"\"\"\nimport math\ndef sum_Of_product(n):\n    sum = 0\n    for i in range(0,n+1):\n        sum += math.factorial(n)/(math.factorial(i)*math.factorial(n-i))\n    return sum", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/580": [{"task_id": "Mbpp/580", "solution": "\"\"\"\nWrite a function to remove uneven elements in the nested mixed tuple.\nassert extract_even((4, 5, (7, 6, (2, 4)), 6, 8)) == (4, (6, (2, 4)), 6, 8)\n\"\"\"\n\"\"\"\nWrite a function to remove uneven elements in the nested mixed tuple.\nassert extract_even((4, 5, (7, 6, (2, 4)), 6, 8)) == (4, (6, (2, 4)), 6, 8)\n\"\"\"\nfrom typing import Any, <PERSON><PERSON>\n\n\ndef extract_even(mixed_tuple: Tu<PERSON>[Any, ...]) -> Tuple[Any, ...]:\n    \"\"\"Remove uneven elements in the nested mixed tuple.\"\"\"\n    return tuple(\n        item\n        for item in mixed_tuple\n        if isinstance(item, tuple)\n        and all(isinstance(i, int) and i % 2 == 0 for i in item)\n        or isinstance(item, int) and item % 2 == 0\n    )", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/557": [{"task_id": "Mbpp/557", "solution": "\"\"\"\nWrite a function to toggle the case of all characters in a string.\nassert toggle_string(\"Python\")==(\"pYTHON\")\n\"\"\"\n\"\"\"\nWrite a function to toggle the case of all characters in a string.\nassert toggle_string(\"Python\")==(\"pYTHON\")\n\"\"\"\ndef toggle_string(string):\n    return string.swapcase()", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/604": [{"task_id": "Mbpp/604", "solution": "\"\"\"\nWrite a function to reverse words seperated by spaces in a given string.\nassert reverse_words(\"python program\")==(\"program python\")\n\"\"\"\n\"\"\"\nWrite a function to reverse words seperated by spaces in a given string.\nassert reverse_words(\"python program\")==(\"program python\")\n\"\"\"\ndef reverse_words(s):\n    return \" \".join(s.split()[::-1])\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/578": [{"task_id": "Mbpp/578", "solution": "\"\"\"\nWrite a function to interleave 3 lists of the same length into a single flat list.\nassert interleave_lists([1,2,3,4,5,6,7],[10,20,30,40,50,60,70],[100,200,300,400,500,600,700])==[1, 10, 100, 2, 20, 200, 3, 30, 300, 4, 40, 400, 5, 50, 500, 6, 60, 600, 7, 70, 700]\n\"\"\"\n\"\"\"\nWrite a function to interleave 3 lists of the same length into a single flat list.\nassert interleave_lists([1,2,3,4,5,6,7],[10,20,30,40,50,60,70],[100,200,300,400,500,600,700])==[1, 10, 100, 2, 20, 200, 3, 30, 300, 4, 40, 400, 5, 50, 500, 6, 60, 600, 7, 70, 700]\n\"\"\"\nfrom typing import List\n\n\ndef interleave_lists(list1: List[int], list2: List[int], list3: List[int]) -> List[int]:\n    return [item for sublist in zip(list1, list2, list3) for item in sublist]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/586": [{"task_id": "Mbpp/586", "solution": "\"\"\"\nWrite a python function to split a list at the nth eelment and add the first part to the end.\nassert split_Arr([12,10,5,6,52,36],2) == [5,6,52,36,12,10]\n\"\"\"\n\"\"\"\nWrite a python function to split a list at the nth eelment and add the first part to the end.\nassert split_Arr([12,10,5,6,52,36],2) == [5,6,52,36,12,10]\n\"\"\"\ndef split_Arr(arr,n):\n    return arr[n:] + arr[:n]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/574": [{"task_id": "Mbpp/574", "solution": "\"\"\"\nWrite a function to find the surface area of a cylinder.\nassert surfacearea_cylinder(10,5)==942.45\n\"\"\"\n\"\"\"\nWrite a function to find the surface area of a cylinder.\nassert surfacearea_cylinder(10,5)==942.45\n\"\"\"\nimport math\ndef surfacearea_cylinder(r, h):\n    return 2 * math.pi * r * (r + h)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/563": [{"task_id": "Mbpp/563", "solution": "\"\"\"\nWrite a function to extract values between quotation marks from a string.\nassert extract_values('\"Python\", \"PHP\", \"Java\"')==['Python', 'PHP', 'Java']\n\"\"\"\n\"\"\"\nWrite a function to extract values between quotation marks from a string.\nassert extract_values('\"Python\", \"PHP\", \"Java\"')==['Python', 'PHP', 'Java']\n\"\"\"\nimport re\ndef extract_values(string):\n    return re.findall(r'\"([^\"]*)\"', string)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/594": [{"task_id": "Mbpp/594", "solution": "\"\"\"\nWrite a function to find the difference of the first even and first odd number of a given list.\nassert diff_even_odd([1,3,5,7,4,1,6,8])==3\n\"\"\"\n\"\"\"\nWrite a function to find the difference of the first even and first odd number of a given list.\nassert diff_even_odd([1,3,5,7,4,1,6,8])==3\n\"\"\"\ndef diff_even_odd(l):\n    even = 0\n    odd = 0\n    for i in l:\n        if i%2 == 0:\n            even = i\n            break\n    for i in l:\n        if i%2 != 0:\n            odd = i\n            break\n    return even - odd", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/603": [{"task_id": "Mbpp/603", "solution": "\"\"\"\nWrite a function to get all lucid numbers smaller than or equal to a given integer.\nassert get_ludic(10) == [1, 2, 3, 5, 7]\n\"\"\"\n\"\"\"\nWrite a function to get all lucid numbers smaller than or equal to a given integer.\nassert get_ludic(10) == [1, 2, 3, 5, 7]\n\"\"\"\nfrom math import sqrt\n\n\ndef is_prime(n):\n    if n < 2:\n        return False\n    for i in range(2, int(sqrt(n)) + 1):\n        if n % i == 0:\n            return False\n    return True\n\n\ndef get_ludic(n):\n    ludic = []\n    for i in range(1, n + 1):\n        if is_prime(i):\n            ludic.append(i)\n    return ludic\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/572": [{"task_id": "Mbpp/572", "solution": "\"\"\"\nWrite a python function to remove duplicate numbers from a given number of lists.\nassert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]\n\"\"\"\n\"\"\"\nWrite a python function to remove duplicate numbers from a given number of lists.\nassert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]\n\"\"\"\ndef two_unique_nums(list1):\n    list2 = []\n    for i in list1:\n        if i not in list2:\n            list2.append(i)\n    return list2\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/474": [{"task_id": "Mbpp/474", "solution": "\"\"\"\nWrite a function to replace characters in a string.\nassert replace_char(\"polygon\",'y','l')==(\"pollgon\")\n\"\"\"\n\"\"\"\nWrite a function to replace characters in a string.\nassert replace_char(\"polygon\",'y','l')==(\"pollgon\")\n\"\"\"\ndef replace_char(string,old,new):\n    return string.replace(old,new)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/596": [{"task_id": "Mbpp/596", "solution": "\"\"\"\nWrite a function to find the size in bytes of the given tuple.\nassert tuple_size((\"A\", 1, \"B\", 2, \"C\", 3) ) == sys.getsizeof((\"A\", 1, \"B\", 2, \"C\", 3))\n\"\"\"\n\"\"\"\nWrite a function to find the size in bytes of the given tuple.\nassert tuple_size((\"A\", 1, \"B\", 2, \"C\", 3) ) == sys.getsizeof((\"A\", 1, \"B\", 2, \"C\", 3))\n\"\"\"\nimport sys\n\n\ndef tuple_size(tup):\n    return sys.getsizeof(tup)\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/610": [{"task_id": "Mbpp/610", "solution": "\"\"\"\nWrite a python function which takes a list and returns a list with the same elements, but the k'th element removed.\nassert remove_kth_element([1,1,2,3,4,4,5,1],3)==[1, 1, 3, 4, 4, 5, 1]\n\"\"\"\n\"\"\"\nWrite a python function which takes a list and returns a list with the same elements, but the k'th element removed.\nassert remove_kth_element([1,1,2,3,4,4,5,1],3)==[1, 1, 3, 4, 4, 5, 1]\n\"\"\"\ndef remove_kth_element(list1,k):\n    list1.pop(k-1)\n    return list1", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/620": [{"task_id": "Mbpp/620", "solution": "\"\"\"\nWrite a function to find the size of the largest subset of a list of numbers so that every pair is divisible.\nassert largest_subset([ 1, 3, 6, 13, 17, 18 ]) == 4\n\"\"\"\n\"\"\"\nWrite a function to find the size of the largest subset of a list of numbers so that every pair is divisible.\nassert largest_subset([ 1, 3, 6, 13, 17, 18 ]) == 4\n\"\"\"\nfrom typing import List\n\n\ndef largest_subset(nums: List[int]) -> int:\n    nums.sort()\n    n = len(nums)\n    dp = [1] * n\n    for i in range(1, n):\n        for j in range(i):\n            if nums[i] % nums[j] == 0:\n                dp[i] = max(dp[i], dp[j] + 1)\n    return max(dp)", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[9, 3, 12, 7, 16, 3, 11, 8, 4, 13, 13]]]}], "Mbpp/593": [{"task_id": "Mbpp/593", "solution": "\"\"\"\nWrite a function to remove leading zeroes from an ip address.\nassert removezero_ip(\"**************\")==('************')\n\"\"\"\n\"\"\"\nWrite a function to remove leading zeroes from an ip address.\nassert removezero_ip(\"**************\")==('************')\n\"\"\"\nimport re\ndef removezero_ip(ip):\n    return re.sub(r'\\b0+(?=\\d)', '', ip)\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/611": [{"task_id": "Mbpp/611", "solution": "\"\"\"\nWrite a function which given a matrix represented as a list of lists returns the max of the n'th column.\nassert max_of_nth([[5, 6, 7], [1, 3, 5], [8, 9, 19]], 2) == 19\n\"\"\"\n\"\"\"\nWrite a function which given a matrix represented as a list of lists returns the max of the n'th column.\nassert max_of_nth([[5, 6, 7], [1, 3, 5], [8, 9, 19]], 2) == 19\n\"\"\"\nfrom typing import List\n\n\ndef max_of_nth(matrix: List[List[int]], n: int) -> int:\n    return max([row[n] for row in matrix])", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/618": [{"task_id": "Mbpp/618", "solution": "\"\"\"\nWrite a function to divide two lists element wise.\nassert div_list([4,5,6],[1, 2, 3])==[4.0,2.5,2.0]\n\"\"\"\n\"\"\"\nWrite a function to divide two lists element wise.\nassert div_list([4,5,6],[1, 2, 3])==[4.0,2.5,2.0]\n\"\"\"\ndef div_list(list1, list2):\n    return [x/y for x, y in zip(list1, list2)]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/598": [{"task_id": "Mbpp/598", "solution": "\"\"\"\nWrite a function to check whether the given number is armstrong or not.\nassert armstrong_number(153)==True\n\"\"\"\n\"\"\"\nWrite a function to check whether the given number is armstrong or not.\nassert armstrong_number(153)==True\n\"\"\"\ndef armstrong_number(n):\n    sum=0\n    temp=n\n    while temp>0:\n        digit=temp%10\n        sum+=digit**3\n        temp//=10\n    if n==sum:\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[3]]}], "Mbpp/602": [{"task_id": "Mbpp/602", "solution": "\"\"\"\nWrite a python function to find the first repeated character in a given string.\nassert first_repeated_char(\"abcabc\") == \"a\"\n\"\"\"\n\"\"\"\nWrite a python function to find the first repeated character in a given string.\nassert first_repeated_char(\"abcabc\") == \"a\"\n\"\"\"\ndef first_repeated_char(str1):\n    for i in range(len(str1)):\n        if str1[i] in str1[:i]:\n            return str1[i]\n    return \"None\"", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [["abc"]], "plus_fail_tests": []}], "Mbpp/568": [{"task_id": "Mbpp/568", "solution": "\"\"\"\nWrite a function to create a list of N empty dictionaries.\nassert empty_list(5)==[{},{},{},{},{}]\n\"\"\"\n\"\"\"\nWrite a function to create a list of N empty dictionaries.\nassert empty_list(5)==[{},{},{},{},{}]\n\"\"\"\ndef empty_list(n):\n    return [{} for i in range(n)]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/589": [{"task_id": "Mbpp/589", "solution": "\"\"\"\nWrite a function to find perfect squares between two given numbers.\nassert perfect_squares(1,30)==[1, 4, 9, 16, 25]\n\"\"\"\n\"\"\"\nWrite a function to find perfect squares between two given numbers.\nassert perfect_squares(1,30)==[1, 4, 9, 16, 25]\n\"\"\"\nimport math\ndef perfect_squares(a,b):\n    l=[]\n    for i in range(a,b+1):\n        if math.sqrt(i)==int(math.sqrt(i)):\n            l.append(i)\n    return l", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/588": [{"task_id": "Mbpp/588", "solution": "\"\"\"\nWrite a python function to find the difference between largest and smallest value in a given list.\nassert big_diff([1,2,3,4]) == 3\n\"\"\"\n\"\"\"\nWrite a python function to find the difference between largest and smallest value in a given list.\nassert big_diff([1,2,3,4]) == 3\n\"\"\"\ndef big_diff(nums):\n    return max(nums) - min(nums)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/623": [{"task_id": "Mbpp/623", "solution": "\"\"\"\nWrite a function to compute the n-th power of each number in a list.\nassert nth_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n\"\"\"\n\"\"\"\nWrite a function to compute the n-th power of each number in a list.\nassert nth_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2)==[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]\n\"\"\"\ndef nth_nums(nums, n):\n    return [num**n for num in nums]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/624": [{"task_id": "Mbpp/624", "solution": "\"\"\"\nWrite a python function to convert a given string to uppercase.\nassert is_upper(\"person\") ==\"PERSON\"\n\"\"\"\n\"\"\"\nWrite a python function to convert a given string to uppercase.\nassert is_upper(\"person\") ==\"PERSON\"\n\"\"\"\ndef is_upper(str):\n    return str.upper()\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/628": [{"task_id": "Mbpp/628", "solution": "\"\"\"\nWrite a function to replace all spaces in the given string with '%20'.\nassert replace_spaces(\"My Name is <PERSON><PERSON>\") == 'My%20Name%20is%20Dawood'\n\"\"\"\n\"\"\"\nWrite a function to replace all spaces in the given string with '%20'.\nassert replace_spaces(\"My Name is <PERSON><PERSON>\") == 'My%20Name%20is%20Dawood'\n\"\"\"\nimport unittest\n\n\ndef replace_spaces(string):\n    return string.replace(\" \", \"%20\")\n\n\nclass TestReplaceSpaces(unittest.TestCase):\n    def test_replace_spaces(self):\n        self.assertEqual(replace_spaces(\"My Name is <PERSON><PERSON>\"), \"My%20Name%20is%20Dawood\")\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/612": [{"task_id": "Mbpp/612", "solution": "\"\"\"\nWrite a python function which takes a list of lists, where each sublist has two elements, and returns a list of two lists where the first list has the first element of each sublist and the second one has the second.\nassert merge([['x', 'y'], ['a', 'b'], ['m', 'n']]) == [['x', 'a', 'm'], ['y', 'b', 'n']]\n\"\"\"\n\"\"\"\nWrite a python function which takes a list of lists, where each sublist has two elements, and returns a list of two lists where the first list has the first element of each sublist and the second one has the second.\nassert merge([['x', 'y'], ['a', 'b'], ['m', 'n']]) == [['x', 'a', 'm'], ['y', 'b', 'n']]\n\"\"\"\ndef merge(list_of_lists):\n    return [list(x) for x in zip(*list_of_lists)]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/619": [{"task_id": "Mbpp/619", "solution": "\"\"\"\nWrite a function to move all the numbers to the end of the given string.\nassert move_num('I1love143you55three3000thousand') == 'Iloveyouthreethousand1143553000'\n\"\"\"\n\"\"\"\nWrite a function to move all the numbers to the end of the given string.\nassert move_num('I1love143you55three3000thousand') == 'Iloveyouthreethousand1143553000'\n\"\"\"\nimport re\n\n\ndef move_num(string):\n    return re.sub(r'\\d+', '', string) + re.findall(r'\\d+', string)[::-1]\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/597": [{"task_id": "Mbpp/597", "solution": "\"\"\"\nWrite a function to find kth element from the given two sorted arrays.\nassert find_kth([2, 3, 6, 7, 9], [1, 4, 8, 10], 5) == 6\n\"\"\"\n\"\"\"\nWrite a function to find kth element from the given two sorted arrays.\nassert find_kth([2, 3, 6, 7, 9], [1, 4, 8, 10], 5) == 6\n\"\"\"\nfrom typing import List\n\n\ndef find_kth(arr1: List[int], arr2: List[int], k: int) -> int:\n    if not arr1:\n        return arr2[k - 1]\n    if not arr2:\n        return arr1[k - 1]\n    if k == 1:\n        return min(arr1[0], arr2[0])\n    if arr1[0] < arr2[0]:\n        return find_kth(arr1[1:], arr2, k - 1)\n    return find_kth(arr1, arr2[1:], k - 1)", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[1, 2, 3], [true, true, false], 3]]}], "Mbpp/577": [{"task_id": "Mbpp/577", "solution": "\"\"\"\nWrite a python function to find the last digit in factorial of a given number.\nassert last_Digit_Factorial(4) == 4\n\"\"\"\n\"\"\"\nWrite a python function to find the last digit in factorial of a given number.\nassert last_Digit_Factorial(4) == 4\n\"\"\"\ndef last_Digit_Factorial(n):\n    if n == 0 or n == 1:\n        return 1\n    elif n == 2:\n        return 2\n    elif n == 3:\n        return 6\n    elif n == 4:\n        return 4\n    elif n == 5:\n        return 0\n    elif n == 6:\n        return 2\n    elif n == 7:\n        return 4\n    elif n == 8:\n        return 2\n    elif n == 9:\n        return 0\n    else:\n        return 0\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[1.5869584267664152]]}], "Mbpp/606": [{"task_id": "Mbpp/606", "solution": "\"\"\"\nWrite a function to convert degrees to radians.\nassert radian_degree(90)==1.5707963267948966\n\"\"\"\n\"\"\"\nWrite a function to convert degrees to radians.\nassert radian_degree(90)==1.5707963267948966\n\"\"\"\nimport math\ndef radian_degree(degrees):\n    return math.radians(degrees)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/614": [{"task_id": "Mbpp/614", "solution": "\"\"\"\nWrite a function to find the cumulative sum of all the values that are present in the given tuple list.\nassert cummulative_sum([(1, 3), (5, 6, 7), (2, 6)]) == 30\n\"\"\"\n\"\"\"\nWrite a function to find the cumulative sum of all the values that are present in the given tuple list.\nassert cummulative_sum([(1, 3), (5, 6, 7), (2, 6)]) == 30\n\"\"\"\nfrom typing import List, Tuple\n\n\ndef cummulative_sum(tup_list: List[Tuple[int]]) -> int:\n    \"\"\"\n    Function to find the cumulative sum of all the values that are present in the given tuple list.\n    :param tup_list: List of tuples\n    :return: int\n    \"\"\"\n    return sum([sum(tup) for tup in tup_list])\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/615": [{"task_id": "Mbpp/615", "solution": "\"\"\"\nWrite a function which takes a tuple of tuples and returns the average value for each tuple as a list.\nassert average_tuple(((10, 10, 10, 12), (30, 45, 56, 45), (81, 80, 39, 32), (1, 2, 3, 4)))==[30.5, 34.25, 27.0, 23.25]\n\"\"\"\n\"\"\"\nWrite a function which takes a tuple of tuples and returns the average value for each tuple as a list.\nassert average_tuple(((10, 10, 10, 12), (30, 45, 56, 45), (81, 80, 39, 32), (1, 2, 3, 4)))==[30.5, 34.25, 27.0, 23.25]\n\"\"\"\ndef average_tuple(tpl):\n    return [sum(i)/len(i) for i in zip(*tpl)]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/591": [{"task_id": "Mbpp/591", "solution": "\"\"\"\nWrite a python function to interchange the first and last elements in a list.\nassert swap_List([12, 35, 9, 56, 24]) == [24, 35, 9, 56, 12]\n\"\"\"\n\"\"\"\nWrite a python function to interchange the first and last elements in a list.\nassert swap_List([12, 35, 9, 56, 24]) == [24, 35, 9, 56, 12]\n\"\"\"\ndef swap_List(list):\n    list[0], list[-1] = list[-1], list[0]\n    return list", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/576": [{"task_id": "Mbpp/576", "solution": "\"\"\"\nWrite a python function to check whether a list is sublist of another or not.\nassert is_Sub_Array([1,4,3,5],[1,2]) == False\n\"\"\"\n\"\"\"\nWrite a python function to check whether a list is sublist of another or not.\nassert is_Sub_Array([1,4,3,5],[1,2]) == False\n\"\"\"\ndef is_Sub_Array(list1,list2):\n    if list1 in list2:\n        return True\n    else:\n        return False", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[[1, 2, 1], [1, 2, 1]]], "plus_fail_tests": []}], "Mbpp/600": [{"task_id": "Mbpp/600", "solution": "\"\"\"\nWrite a python function to check whether the given number is even or not.\nassert is_Even(1) == False\n\"\"\"\n\"\"\"\nWrite a python function to check whether the given number is even or not.\nassert is_Even(1) == False\n\"\"\"\ndef is_Even(n):\n    if n%2==0:\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/616": [{"task_id": "Mbpp/616", "solution": "\"\"\"\nWrite a function which takes two tuples of the same length and performs the element wise modulo.\nassert tuple_modulo((10, 4, 5, 6), (5, 6, 7, 5)) == (0, 4, 5, 1)\n\"\"\"\n\"\"\"\nWrite a function which takes two tuples of the same length and performs the element wise modulo.\nassert tuple_modulo((10, 4, 5, 6), (5, 6, 7, 5)) == (0, 4, 5, 1)\n\"\"\"\nfrom typing import <PERSON>ple\n\n\ndef tuple_modulo(t1: <PERSON><PERSON>[int], t2: <PERSON><PERSON>[int]) -> <PERSON><PERSON>[int]:\n    \"\"\"\n    Perform element wise modulo on two tuples of the same length.\n    \"\"\"\n    return tuple(x % y for x, y in zip(t1, t2))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/595": [{"task_id": "Mbpp/595", "solution": "\"\"\"\nWrite a python function to count minimum number of swaps required to convert one binary number represented as a string to another.\nassert min_Swaps(\"1101\",\"1110\") == 1\n\"\"\"\n\"\"\"\nWrite a python function to count minimum number of swaps required to convert one binary number represented as a string to another.\nassert min_Swaps(\"1101\",\"1110\") == 1\n\"\"\"\ndef min_Swaps(str1,str2):\n    count=0\n    for i in range(len(str1)):\n        if str1[i]!=str2[i]:\n            count+=1\n    return count", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["1010101010101010101010101010101010101010101010101010101010101010", "0101010101010101010101010101010101010101010101010101010101010101"]]}], "Mbpp/626": [{"task_id": "Mbpp/626", "solution": "\"\"\"\nWrite a python function to find the area of the largest triangle that can be inscribed in a semicircle with a given radius.\nassert triangle_area(-1) == None\n\"\"\"\n\"\"\"\nWrite a python function to find the area of the largest triangle that can be inscribed in a semicircle with a given radius.\nassert triangle_area(-1) == None\n\"\"\"\nimport math\ndef triangle_area(radius):\n    if radius < 0:\n        return None\n    return (math.pi * radius * radius) / 4", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[2]], "plus_fail_tests": [[0.5]]}], "Mbpp/631": [{"task_id": "Mbpp/631", "solution": "\"\"\"\nWrite a function to replace whitespaces with an underscore and vice versa in a given string.\nassert replace_spaces('Jumanji The Jungle') == 'Jumanji_The_Jungle'\n\"\"\"\n\"\"\"\nWrite a function to replace whitespaces with an underscore and vice versa in a given string.\nassert replace_spaces('Jumanji The Jungle') == 'Jumanji_The_Jungle'\n\"\"\"\nimport re\n\n\ndef replace_spaces(string):\n    return re.sub(r'\\s', '_', string)\n\n\ndef replace_spaces2(string):\n    return string.replace(' ', '_').replace('_', ' ')\n\n\ndef replace_spaces3(string):\n    return re.sub(r'[_]', ' ', string)\n\n\ndef replace_spaces4(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_')\n\n\ndef replace_spaces5(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces6(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces7(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces8(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces9(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces10(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces11(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces12(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces13(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces14(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces15(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces16(string):\n    return re.sub(r'[_]', ' ', string).replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_').replace(' ', '_')\n\n\ndef replace_spaces17(string):\n    return re.sub(r'[_]', '", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/635": [{"task_id": "Mbpp/635", "solution": "\"\"\"\nWrite a function to sort the given list.\nassert heap_sort([1, 3, 5, 7, 9, 2, 4, 6, 8, 0])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n\"\"\"\n\"\"\"\nWrite a function to sort the given list.\nassert heap_sort([1, 3, 5, 7, 9, 2, 4, 6, 8, 0])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n\"\"\"\nimport heapq\ndef heap_sort(arr):\n    heapq.heapify(arr)\n    return [heapq.heappop(arr) for i in range(len(arr))]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/632": [{"task_id": "Mbpp/632", "solution": "\"\"\"\nWrite a python function to move all zeroes to the end of the given list.\nassert move_zero([1,0,2,0,3,4]) == [1,2,3,4,0,0]\n\"\"\"\n\"\"\"\nWrite a python function to move all zeroes to the end of the given list.\nassert move_zero([1,0,2,0,3,4]) == [1,2,3,4,0,0]\n\"\"\"\ndef move_zero(list):\n    for i in list:\n        if i == 0:\n            list.remove(i)\n            list.append(i)\n    return list", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/406": [{"task_id": "Mbpp/406", "solution": "\"\"\"\nWrite a python function to find whether the parity of a given number is odd.\nassert find_Parity(12) == False\n\"\"\"\n\"\"\"\nWrite a python function to find whether the parity of a given number is odd.\nassert find_Parity(12) == False\n\"\"\"\ndef find_Parity(n):\n    count = 0\n    while n:\n        count += n & 1\n        n >>= 1\n    return count % 2 == 1\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[-10]]}], "Mbpp/633": [{"task_id": "Mbpp/633", "solution": "\"\"\"\nWrite a python function to find the sum of xor of all pairs of numbers in the given list.\nassert pair_xor_Sum([5,9,7,6],4) == 47\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of xor of all pairs of numbers in the given list.\nassert pair_xor_Sum([5,9,7,6],4) == 47\n\"\"\"\ndef pair_xor_Sum(lst,n):\n    sum=0\n    for i in range(n):\n        for j in range(i+1,n):\n            sum+=lst[i]^lst[j]\n    return sum\nlst=[5,9,7,6]\nn=len(lst)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/637": [{"task_id": "Mbpp/637", "solution": "\"\"\"\nWrite a function to check whether the given amount has no profit and no loss\nassert noprofit_noloss(1500,1200)==False\n\"\"\"\n\"\"\"\nWrite a function to check whether the given amount has no profit and no loss\nassert noprofit_noloss(1500,1200)==False\n\"\"\"\ndef noprofit_noloss(a,b):\n    if a==b:\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/643": [{"task_id": "Mbpp/643", "solution": "\"\"\"\nWrite a function that checks if a strings contains 'z', except at the start and end of the word.\nassert text_match_wordz_middle(\"pythonzabc.\")==True\n\"\"\"\n\"\"\"\nWrite a function that checks if a strings contains 'z', except at the start and end of the word.\nassert text_match_wordz_middle(\"pythonzabc.\")==True\n\"\"\"\nimport re\ndef text_match_wordz_middle(text):\n    regex = re.compile(r'^(?!.*\\b\\w*z\\b)(?=.*\\b\\w*z\\b)')\n    return regex.search(text) is not None", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/639": [{"task_id": "Mbpp/639", "solution": "\"\"\"\nWrite a function to sum the length of the names of a given list of names after removing the names that start with a lowercase letter.\nassert sample_nam(['sally', '<PERSON>', 'rebecca', '<PERSON>', '<PERSON>', 'keith'])==16\n\"\"\"\n\"\"\"\nWrite a function to sum the length of the names of a given list of names after removing the names that start with a lowercase letter.\nassert sample_nam(['sally', '<PERSON>', 'rebecca', '<PERSON>', '<PERSON>', 'keith'])==16\n\"\"\"\ndef sample_nam(names):\n    return sum(len(name) for name in names if name[0].isupper())", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[["AbCdE", "fGH", "IJKLMnOp", "qrs", "TUVW", "xyz"]]]}], "Mbpp/722": [{"task_id": "Mbpp/722", "solution": "\"\"\"\nThe input is given as - a dictionary with a student name as a key and a tuple of float (student_height, student_weight) as a value, - minimal height, - minimal weight. Write a function to filter students that have height and weight above the minimum.\nassert filter_data({'<PERSON><PERSON><PERSON>': (6.2, 70), '<PERSON><PERSON>trell': (5.9, 65), 'Ki<PERSON>ra <PERSON>': (6.0, 68), '<PERSON>': (5.8, 66)},6.0,70)=={'Cierra Vega': (6.2, 70)}\n\"\"\"\n\"\"\"\nThe input is given as - a dictionary with a student name as a key and a tuple of float (student_height, student_weight) as a value, - minimal height, - minimal weight. Write a function to filter students that have height and weight above the minimum.\nassert filter_data({'Cier<PERSON>': (6.2, 70), '<PERSON><PERSON>trell': (5.9, 65), 'Kierra Gentry': (6.0, 68), '<PERSON>': (5.8, 66)},6.0,70)=={'<PERSON><PERSON><PERSON>': (6.2, 70)}\n\"\"\"\ndef filter_data(data, min_height, min_weight):\n    return {k: v for k, v in data.items() if v[0] > min_height and v[1] > min_weight}\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[{"Alice": [5.6, 60], "Bob": [6.1, 70], "Charlie": [5.9, 65], "Dave": [5.11, 72], "Eve": [6.0, 68], "Frank": [5.7, 63]}, 5.8, 65]]}], "Mbpp/641": [{"task_id": "Mbpp/641", "solution": "\"\"\"\nWrite a function to find the nth nonagonal number.\nassert is_nonagonal(10) == 325\n\"\"\"\n\"\"\"\nWrite a function to find the nth nonagonal number.\nassert is_nonagonal(10) == 325\n\"\"\"\nimport math\n\n\ndef is_nonagonal(n):\n    \"\"\"\n    :param n: int\n    :return: bool\n    \"\"\"\n    return math.sqrt(1 + 21 * n) % 7 == 0\n\n\ndef nth_nonagonal(n):\n    \"\"\"\n    :param n: int\n    :return: int\n    \"\"\"\n    return int((math.sqrt(1 + 21 * n) + 1) / 7) ** 2\n\n\ndef test():\n    assert is_nonagonal(10) == 325\n    assert nth_nonagonal(10) == 325\n    assert is_nonagonal(11) == 364\n    assert nth_nonagonal(11) == 364\n    assert is_nonagonal(12) == 405\n    assert nth_nonagonal(12) == 405\n    assert is_nonagonal(13) == 446\n    assert nth_nonagonal(13) == 446\n    assert is_nonagonal(14) == 487\n    assert nth_nonagonal(14) == 487\n    assert is_nonagonal(15) == 528\n    assert nth_nonagonal(15) == 528\n    assert is_nonagonal(16) == 569\n    assert nth_nonagonal(16) == 569\n    assert is_nonagonal(17) == 610\n    assert nth_nonagonal(17) == 610\n    assert is_nonagonal(18) == 651\n    assert nth_nonagonal(18) == 651\n    assert is_nonagonal(19) == 692\n    assert nth_nonagonal(19) == 692\n    assert is_nonagonal(20) == 733\n    assert nth_nonagonal(20) == 733\n    assert is_nonagonal(21) == 774\n    assert nth_nonagonal(21) == 774\n    assert is_nonagonal(22) == 815\n    assert nth_nonagonal(22) == 815\n    assert is_nonagonal(23) == 856\n    assert nth_nonagonal(23) == 856\n    assert is_nonagonal(24) == 897\n    assert nth_nonagonal(24) == 897\n    assert is_nonagonal(25) == 938\n    assert nth_nonagonal(25) == 938\n    assert is_nonagonal(26) == 979\n    assert nth_nonagonal(26) == 979\n    assert is_nonagonal(27) == 1020\n    assert nth_nonagonal(27) == 1020\n    assert is_nonagonal(28) == 1061\n    assert nth_nonagonal(28) == 1061\n    assert is_nonagonal(29) == 1102\n    assert nth_nonagonal(29) == 1102\n    assert is_nonagonal(30) == 1143\n    assert nth_nonagonal(30) == 1143\n    assert is_nonagonal(31) == 1184\n    assert nth_nonagonal(31) == 1184", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/638": [{"task_id": "Mbpp/638", "solution": "\"\"\"\nWrite a function to calculate the wind chill index rounded to the next integer given the wind velocity in km/h and a temperature in celsius.\nassert wind_chill(120,35)==40\n\"\"\"\n\"\"\"\nWrite a function to calculate the wind chill index rounded to the next integer given the wind velocity in km/h and a temperature in celsius.\nassert wind_chill(120,35)==40\n\"\"\"\ndef wind_chill(velocity,temperature):\n    return round(13.12 + 0.6215 * temperature - 11.37 * velocity ** 0.16 + 0.3965 * temperature * velocity ** 0.16)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/725": [{"task_id": "Mbpp/725", "solution": "\"\"\"\nWrite a function to extract values between quotation marks \" \" of the given string.\nassert extract_quotation('Cortex \"A53\" Based \"multi\" tasking \"Processor\"') == ['A53', 'multi', 'Processor']\n\"\"\"\n\"\"\"\nWrite a function to extract values between quotation marks \" \" of the given string.\nassert extract_quotation('Cortex \"A53\" Based \"multi\" tasking \"Processor\"') == ['A53', 'multi', 'Processor']\n\"\"\"\nimport re\n\n\ndef extract_quotation(string: str) -> list:\n    return re.findall(r'\"([^\"]*)\"', string)\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/720": [{"task_id": "Mbpp/720", "solution": "\"\"\"\nWrite a function to add a dictionary to the tuple. The output should be a tuple.\nassert add_dict_to_tuple((4, 5, 6), {\"MSAM\" : 1, \"is\" : 2, \"best\" : 3} ) == (4, 5, 6, {'MSAM': 1, 'is': 2, 'best': 3})\n\"\"\"\n\"\"\"\nWrite a function to add a dictionary to the tuple. The output should be a tuple.\nassert add_dict_to_tuple((4, 5, 6), {\"MSAM\" : 1, \"is\" : 2, \"best\" : 3} ) == (4, 5, 6, {'MSAM': 1, 'is': 2, 'best': 3})\n\"\"\"\ndef add_dict_to_tuple(tup, dict):\n    return tup + (dict,)\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/644": [{"task_id": "Mbpp/644", "solution": "\"\"\"\nWrite a python function to reverse an array upto a given position.\nassert reverse_Array_Upto_K([1, 2, 3, 4, 5, 6],4) == [4, 3, 2, 1, 5, 6]\n\"\"\"\n\"\"\"\nWrite a python function to reverse an array upto a given position.\nassert reverse_Array_Upto_K([1, 2, 3, 4, 5, 6],4) == [4, 3, 2, 1, 5, 6]\n\"\"\"\ndef reverse_array_Upto_K(arr,k):\n    return arr[:k][::-1] + arr[k:]\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/640": [{"task_id": "Mbpp/640", "solution": "\"\"\"\nWrite a function to remove the parenthesis and what is inbetween them from a string.\nassert remove_parenthesis([\"python (chrome)\"])==(\"python\")\n\"\"\"\n\"\"\"\nWrite a function to remove the parenthesis and what is inbetween them from a string.\nassert remove_parenthesis([\"python (chrome)\"])==(\"python\")\n\"\"\"\nimport re\ndef remove_parenthesis(list):\n    for i in list:\n        i = re.sub(r'\\([^()]*\\)', '', i)\n    return list\n", "base_status": "fail", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/723": [{"task_id": "Mbpp/723", "solution": "\"\"\"\nThe input is defined as two lists of the same length. Write a function to count indices where the lists have the same values.\nassert count_same_pair([1, 2, 3, 4, 5, 6, 7, 8],[2, 2, 3, 1, 2, 6, 7, 9])==4\n\"\"\"\n\"\"\"\nThe input is defined as two lists of the same length. Write a function to count indices where the lists have the same values.\nassert count_same_pair([1, 2, 3, 4, 5, 6, 7, 8],[2, 2, 3, 1, 2, 6, 7, 9])==4\n\"\"\"\ndef count_same_pair(list1, list2):\n    count = 0\n    for i in range(len(list1)):\n        if list1[i] == list2[i]:\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/724": [{"task_id": "Mbpp/724", "solution": "\"\"\"\nWrite a function that takes base and power as arguments and calculate the sum of all digits of the base to the specified power.\nassert power_base_sum(2,100)==115\n\"\"\"\n\"\"\"\nWrite a function that takes base and power as arguments and calculate the sum of all digits of the base to the specified power.\nassert power_base_sum(2,100)==115\n\"\"\"\ndef power_base_sum(base, power):\n    return sum([int(i) for i in str(base**power)])", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/728": [{"task_id": "Mbpp/728", "solution": "\"\"\"\nWrite a function takes as input two lists [a_1,...,a_n], [b_1,...,b_n] and returns [a_1+b_1,...,a_n+b_n].\nassert sum_list([10,20,30],[15,25,35])==[25,45,65]\n\"\"\"\n\"\"\"\nWrite a function takes as input two lists [a_1,...,a_n], [b_1,...,b_n] and returns [a_1+b_1,...,a_n+b_n].\nassert sum_list([10,20,30],[15,25,35])==[25,45,65]\n\"\"\"\ndef sum_list(a,b):\n    return [a[i]+b[i] for i in range(len(a))]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/721": [{"task_id": "Mbpp/721", "solution": "\"\"\"\nGiven a square matrix of size N*N given as a list of lists, where each cell is associated with a specific cost. A path is defined as a specific sequence of cells that starts from the top-left cell move only right or down and ends on bottom right cell. We want to find a path with the maximum average over all existing paths. Average is computed as total cost divided by the number of cells visited in the path.\nassert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]]) == 5.2\n\"\"\"\n\"\"\"\nGiven a square matrix of size N*N given as a list of lists, where each cell is associated with a specific cost. A path is defined as a specific sequence of cells that starts from the top-left cell move only right or down and ends on bottom right cell. We want to find a path with the maximum average over all existing paths. Average is computed as total cost divided by the number of cells visited in the path.\nassert maxAverageOfPath([[1, 2, 3], [6, 5, 4], [7, 3, 9]]) == 5.2\n\"\"\"\ndef maxAverageOfPath(matrix):\n    n = len(matrix)\n    dp = [[0] * n for _ in range(n)]\n    dp[0][0] = matrix[0][0]\n    for i in range(1, n):\n        dp[i][0] = dp[i - 1][0] + matrix[i][0]\n        dp[0][i] = dp[0][i - 1] + matrix[0][i]\n    for i in range(1, n):\n        for j in range(1, n):\n            dp[i][j] = max(dp[i - 1][j], dp[i][j - 1]) + matrix[i][j]\n    return dp[n - 1][n - 1] / (n * n)", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[[0, 0, 0], [0, 0, 0], [0, 0, -1]]]]}], "Mbpp/733": [{"task_id": "Mbpp/733", "solution": "\"\"\"\nWrite a function to find the index of the first occurrence of a given number in a sorted array.\nassert find_first_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 1\n\"\"\"\n\"\"\"\nWrite a function to find the index of the first occurrence of a given number in a sorted array.\nassert find_first_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 1\n\"\"\"\nfrom typing import List\n\n\ndef find_first_occurrence(arr: List[int], target: int) -> int:\n    left, right = 0, len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            if mid == 0 or arr[mid - 1] < target:\n                return mid\n            else:\n                right = mid - 1\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return -1\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/730": [{"task_id": "Mbpp/730", "solution": "\"\"\"\nWrite a function to remove consecutive duplicates of a given list.\nassert consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4 ])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4]\n\"\"\"\n\"\"\"\nWrite a function to remove consecutive duplicates of a given list.\nassert consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4 ])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4]\n\"\"\"\ndef consecutive_duplicates(l):\n    return [i for i, j in zip(l, l[1:]) if i != j]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/726": [{"task_id": "Mbpp/726", "solution": "\"\"\"\nWrite a function that takes as input a tuple of numbers (t_1,...,t_{N+1}) and returns a tuple of length N where the i-th element of the tuple is equal to t_i * t_{i+1}.\nassert multiply_elements((1, 5, 7, 8, 10)) == (5, 35, 56, 80)\n\"\"\"\n\"\"\"\nWrite a function that takes as input a tuple of numbers (t_1,...,t_{N+1}) and returns a tuple of length N where the i-th element of the tuple is equal to t_i * t_{i+1}.\nassert multiply_elements((1, 5, 7, 8, 10)) == (5, 35, 56, 80)\n\"\"\"\ndef multiply_elements(t):\n    return tuple(t[i] * t[i+1] for i in range(len(t)-1))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/736": [{"task_id": "Mbpp/736", "solution": "\"\"\"\nWrite a function to locate the left insertion point for a specified value in sorted order. https://www.w3resource.com/python-exercises/data-structures-and-algorithms/python-data-structure-exercise-24.php\nassert left_insertion([1,2,4,5],6)==4\n\"\"\"\n\"\"\"\nWrite a function to locate the left insertion point for a specified value in sorted order. https://www.w3resource.com/python-exercises/data-structures-and-algorithms/python-data-structure-exercise-24.php\nassert left_insertion([1,2,4,5],6)==4\n\"\"\"\ndef left_insertion(arr,val):\n    for i in range(len(arr)):\n        if arr[i]>=val:\n            return i\n    return len(arr)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/732": [{"task_id": "Mbpp/732", "solution": "\"\"\"\nWrite a function to replace all occurrences of spaces, commas, or dots with a colon.\nassert replace_specialchar('Python language, Programming language.')==('Python:language::Programming:language:')\n\"\"\"\n\"\"\"\nWrite a function to replace all occurrences of spaces, commas, or dots with a colon.\nassert replace_specialchar('Python language, Programming language.')==('Python:language::Programming:language:')\n\"\"\"\nimport re\ndef replace_specialchar(string):\n    return re.sub(r'[ ,.]', ':', string)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/734": [{"task_id": "Mbpp/734", "solution": "\"\"\"\nWrite a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/\nassert sum_Of_Subarray_Prod([1,2,3]) == 20\n\"\"\"\n\"\"\"\nWrite a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/\nassert sum_Of_Subarray_Prod([1,2,3]) == 20\n\"\"\"\ndef sum_Of_Subarray_Prod(arr):\n    n = len(arr)\n    sum = 0\n    for i in range(n):\n        prod = 1\n        for j in range(i, n):\n            prod *= arr[j]\n            sum += prod\n    return sum\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/737": [{"task_id": "Mbpp/737", "solution": "\"\"\"\nWrite a function to check whether the given string is starting with a vowel or not using regex.\nassert check_str(\"annie\")\n\"\"\"\n\"\"\"\nWrite a function to check whether the given string is starting with a vowel or not using regex.\nassert check_str(\"annie\")\n\"\"\"\nimport re\ndef check_str(str1):\n    pattern = re.compile(r'^[aeiouAEIOU]')\n    if pattern.match(str1):\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/750": [{"task_id": "Mbpp/750", "solution": "\"\"\"\nWrite a function to add the given tuple to the given list.\nassert add_tuple([5, 6, 7], (9, 10)) == [5, 6, 7, 9, 10]\n\"\"\"\n\"\"\"\nWrite a function to add the given tuple to the given list.\nassert add_tuple([5, 6, 7], (9, 10)) == [5, 6, 7, 9, 10]\n\"\"\"\ndef add_tuple(list1, tuple1):\n    list1.extend(tuple1)\n    return list1\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/743": [{"task_id": "Mbpp/743", "solution": "\"\"\"\nWrite a function to rotate a given list by specified number of items to the right direction. https://www.geeksforgeeks.org/python-program-right-rotate-list-n/\nassert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3)==[8, 9, 10, 1, 2, 3, 4, 5, 6, 7]\n\"\"\"\n\"\"\"\nWrite a function to rotate a given list by specified number of items to the right direction. https://www.geeksforgeeks.org/python-program-right-rotate-list-n/\nassert rotate_right([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3)==[8, 9, 10, 1, 2, 3, 4, 5, 6, 7]\n\"\"\"\ndef rotate_right(lst, n):\n    return lst[-n:] + lst[:-n]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/753": [{"task_id": "Mbpp/753", "solution": "\"\"\"\nWrite a function to find minimum k records from tuple list. https://www.geeksforgeeks.org/python-find-minimum-k-records-from-tuple-list/ - in this case a verbatim copy of test cases\nassert min_k([('<PERSON><PERSON><PERSON>', 10), ('<PERSON><PERSON><PERSON>', 4), ('<PERSON><PERSON><PERSON>', 2), ('<PERSON><PERSON>', 8)], 2) == [('<PERSON><PERSON><PERSON>', 2), ('<PERSON><PERSON><PERSON>', 4)]\n\"\"\"\n\"\"\"\nWrite a function to find minimum k records from tuple list. https://www.geeksforgeeks.org/python-find-minimum-k-records-from-tuple-list/ - in this case a verbatim copy of test cases\nassert min_k([('<PERSON><PERSON><PERSON>', 10), ('<PERSON><PERSON><PERSON>', 4), ('<PERSON><PERSON>h', 2), ('<PERSON><PERSON>', 8)], 2) == [('<PERSON>kash', 2), ('Akshat', 4)]\n\"\"\"\ndef min_k(tup, k):\n    return sorted(tup, key=lambda x: x[1])[:k]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/744": [{"task_id": "Mbpp/744", "solution": "\"\"\"\nWrite a function to check if the given tuple has any none value or not.\nassert check_none((10, 4, 5, 6, None)) == True\n\"\"\"\n\"\"\"\nWrite a function to check if the given tuple has any none value or not.\nassert check_none((10, 4, 5, 6, None)) == True\n\"\"\"\nfrom typing import <PERSON><PERSON>\n\n\ndef check_none(t: <PERSON><PERSON>) -> bool:\n    return any([i is None for i in t])", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/793": [{"task_id": "Mbpp/793", "solution": "\"\"\"\nWrite a python function to find the last position of an element in a sorted array.\nassert last([1,2,3],1) == 0\n\"\"\"\n\"\"\"\nWrite a python function to find the last position of an element in a sorted array.\nassert last([1,2,3],1) == 0\n\"\"\"\ndef last(arr,ele):\n    low=0\n    high=len(arr)-1\n    while low<=high:\n        mid=(low+high)//2\n        if arr[mid]>ele:\n            high=mid-1\n        elif arr[mid]<ele:\n            low=mid+1\n        else:\n            if mid==len(arr)-1 or arr[mid+1]!=arr[mid]:\n                return mid\n            else:\n                low=mid+1\n    return -1", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/740": [{"task_id": "Mbpp/740", "solution": "\"\"\"\nWrite a function to convert the given tuple to a key-value dictionary using adjacent elements. https://www.geeksforgeeks.org/python-convert-tuple-to-adjacent-pair-dictionary/\nassert tuple_to_dict((1, 5, 7, 10, 13, 5)) == {1: 5, 7: 10, 13: 5}\n\"\"\"\n\"\"\"\nWrite a function to convert the given tuple to a key-value dictionary using adjacent elements. https://www.geeksforgeeks.org/python-convert-tuple-to-adjacent-pair-dictionary/\nassert tuple_to_dict((1, 5, 7, 10, 13, 5)) == {1: 5, 7: 10, 13: 5}\n\"\"\"\ndef tuple_to_dict(tup):\n    dict = {}\n    for i in range(0, len(tup), 2):\n        dict[tup[i]] = tup[i+1]\n    return dict\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/777": [{"task_id": "Mbpp/777", "solution": "\"\"\"\nWrite a python function to find the sum of non-repeated elements in a given list.\nassert find_sum([1,2,3,1,1,4,5,6]) == 21\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of non-repeated elements in a given list.\nassert find_sum([1,2,3,1,1,4,5,6]) == 21\n\"\"\"\ndef find_sum(lst):\n    sum = 0\n    for i in lst:\n        if lst.count(i) == 1:\n            sum += i\n    return sum", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/748": [{"task_id": "Mbpp/748", "solution": "\"\"\"\nWrite a function to put spaces between words starting with capital letters in a given string.\nassert capital_words_spaces(\"Python\") == 'Python'\n\"\"\"\n\"\"\"\nWrite a function to put spaces between words starting with capital letters in a given string.\nassert capital_words_spaces(\"Python\") == 'Python'\n\"\"\"\nimport re\n\n\ndef capital_words_spaces(string):\n    return re.sub(r\"(\\w)([A-Z])\", r\"\\1 \\2\", string)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/731": [{"task_id": "Mbpp/731", "solution": "\"\"\"\nWrite a function to find the lateral surface area of a cone given radius r and the height h.\nassert lateralsurface_cone(5,12)==204.20352248333654\n\"\"\"\n\"\"\"\nWrite a function to find the lateral surface area of a cone given radius r and the height h.\nassert lateralsurface_cone(5,12)==204.20352248333654\n\"\"\"\nimport math\ndef lateralsurface_cone(r,h):\n    return math.pi*r*h", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/749": [{"task_id": "Mbpp/749", "solution": "\"\"\"\nWrite a function to sort a given list of strings of numbers numerically. https://www.geeksforgeeks.org/python-sort-numeric-strings-in-a-list/\nassert sort_numeric_strings( ['4','12','45','7','0','100','200','-12','-500'])==[-500, -12, 0, 4, 7, 12, 45, 100, 200]\n\"\"\"\n\"\"\"\nWrite a function to sort a given list of strings of numbers numerically. https://www.geeksforgeeks.org/python-sort-numeric-strings-in-a-list/\nassert sort_numeric_strings( ['4','12','45','7','0','100','200','-12','-500'])==[-500, -12, 0, 4, 7, 12, 45, 100, 200]\n\"\"\"\ndef sort_numeric_strings(list_of_strings):\n    return sorted(list_of_strings, key=int)\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/773": [{"task_id": "Mbpp/773", "solution": "\"\"\"\nWrite a function to find the occurrence and position of the substrings within a string. Return None if there is no match.\nassert occurance_substring('python programming, python language','python')==('python', 0, 6)\n\"\"\"\n\"\"\"\nWrite a function to find the occurrence and position of the substrings within a string. Return None if there is no match.\nassert occurance_substring('python programming, python language','python')==('python', 0, 6)\n\"\"\"\nimport re\ndef occurance_substring(string,substring):\n    if re.search(substring,string):\n        return re.search(substring,string).span()\n    else:\n        return None", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/751": [{"task_id": "Mbpp/751", "solution": "\"\"\"\nWrite a function to check if the given array represents min heap or not. https://www.geeksforgeeks.org/how-to-check-if-a-given-array-represents-a-binary-heap/\nassert check_min_heap([1, 2, 3, 4, 5, 6]) == True\n\"\"\"\n\"\"\"\nWrite a function to check if the given array represents min heap or not. https://www.geeksforgeeks.org/how-to-check-if-a-given-array-represents-a-binary-heap/\nassert check_min_heap([1, 2, 3, 4, 5, 6]) == True\n\"\"\"\ndef check_min_heap(arr):\n    n = len(arr)\n    for i in range(n):\n        if 2*i+1 < n and arr[i] > arr[2*i+1]:\n            return False\n        if 2*i+2 < n and arr[i] > arr[2*i+2]:\n            return False\n    return True\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/757": [{"task_id": "Mbpp/757", "solution": "\"\"\"\nWrite a function to count the pairs of reverse strings in the given string list. https://www.geeksforgeeks.org/python-program-to-count-the-pairs-of-reverse-strings/\nassert count_reverse_pairs([\"julia\", \"best\", \"tseb\", \"for\", \"ailuj\"])== 2\n\"\"\"\n\"\"\"\nWrite a function to count the pairs of reverse strings in the given string list. https://www.geeksforgeeks.org/python-program-to-count-the-pairs-of-reverse-strings/\nassert count_reverse_pairs([\"julia\", \"best\", \"tseb\", \"for\", \"ailuj\"])== 2\n\"\"\"\ndef count_reverse_pairs(str_list):\n    count = 0\n    for i in range(len(str_list)):\n        for j in range(i+1, len(str_list)):\n            if str_list[i] == str_list[j][::-1]:\n                count += 1\n    return count\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/784": [{"task_id": "Mbpp/784", "solution": "\"\"\"\nWrite a function to find the product of first even and odd number of a given list.\nassert mul_even_odd([1,3,5,7,4,1,6,8])==4\n\"\"\"\n\"\"\"\nWrite a function to find the product of first even and odd number of a given list.\nassert mul_even_odd([1,3,5,7,4,1,6,8])==4\n\"\"\"\ndef mul_even_odd(lst):\n    even = 0\n    odd = 0\n    for i in lst:\n        if i % 2 == 0:\n            even = i\n        else:\n            odd = i\n    return even * odd", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[0, 1, -1, 2, -2, 3, -3]]]}], "Mbpp/765": [{"task_id": "Mbpp/765", "solution": "\"\"\"\nWrite a function to find nth polite number. geeksforgeeks.org/n-th-polite-number/\nassert is_polite(7) == 11\n\"\"\"\n\"\"\"\nWrite a function to find nth polite number. geeksforgeeks.org/n-th-polite-number/\nassert is_polite(7) == 11\n\"\"\"\ndef is_polite(n):\n    if n == 1:\n        return 2\n    if n == 2:\n        return 4\n    if n == 3:\n        return 6\n    if n == 4:\n        return 8\n    if n == 5:\n        return 10\n    if n == 6:\n        return 12\n    if n == 7:\n        return 14\n    if n == 8:\n        return 16\n    if n == 9:\n        return 18\n    if n == 10:\n        return 20\n    if n == 11:\n        return 22\n    if n == 12:\n        return 24\n    if n == 13:\n        return 26\n    if n == 14:\n        return 28\n    if n == 15:\n        return 30\n    if n == 16:\n        return 32\n    if n == 17:\n        return 34\n    if n == 18:\n        return 36\n    if n == 19:\n        return 38\n    if n == 20:\n        return 40\n    if n == 21:\n        return 42\n    if n == 22:\n        return 44\n    if n == 23:\n        return 46\n    if n == 24:\n        return 48\n    if n == 25:\n        return 50\n    if n == 26:\n        return 52\n    if n == 27:\n        return 54\n    if n == 28:\n        return 56\n    if n == 29:\n        return 58\n    if n == 30:\n        return 60\n    if n == 31:\n        return 62\n    if n == 32:\n        return 64\n    if n == 33:\n        return 66\n    if n == 34:\n        return 68\n    if n == 35:\n        return 70\n    if n == 36:\n        return 72\n    if n == 37:\n        return 74\n    if n == 38:\n        return 76\n    if n == 39:\n        return 78\n    if n == 40:\n        return 80\n    if n == 41:\n        return 82\n    if n == 42:\n        return 84\n    if n == 43:\n        return 86\n    if n == 44:\n        return 88\n    if n == 45:\n        return 90\n    if n == 46:\n        return 92\n    if n == 47:\n        return 94\n    if n == 48:\n        return 96\n    if n == 49:\n        return 98\n    if n == 50:\n        return 100\n    if n == 51:\n        return 102\n    if n == 52:\n        return 104\n    if n == 53:\n        return 106\n    if n == 54:\n        return 108\n    if n == 55:\n        return 110", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/758": [{"task_id": "Mbpp/758", "solution": "\"\"\"\nWrite a function to count lists within a list. The function should return a dictionary where every list is converted to a tuple and the value of such tuple is the number of its occurencies in the original list.\nassert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]] )=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}\n\"\"\"\n\"\"\"\nWrite a function to count lists within a list. The function should return a dictionary where every list is converted to a tuple and the value of such tuple is the number of its occurencies in the original list.\nassert unique_sublists([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]] )=={(1, 3): 2, (5, 7): 2, (13, 15, 17): 1, (9, 11): 1}\n\"\"\"\nfrom collections import Counter\nfrom typing import List, Dict, Tuple\n\n\ndef unique_sublists(lst: List[List[int]]) -> Dict[Tuple[int], int]:\n    return Counter(tuple(sublist) for sublist in lst)", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/771": [{"task_id": "Mbpp/771", "solution": "\"\"\"\nWrite a function to check if the given expression is balanced or not. https://www.geeksforgeeks.org/check-for-balanced-parentheses-in-an-expression/\nassert check_expression(\"{()}[{}]\") == True\n\"\"\"\n\"\"\"\nWrite a function to check if the given expression is balanced or not. https://www.geeksforgeeks.org/check-for-balanced-parentheses-in-an-expression/\nassert check_expression(\"{()}[{}]\") == True\n\"\"\"\ndef check_expression(expression):\n    stack = []\n    for char in expression:\n        if char in ['{', '[', '(']:\n            stack.append(char)\n        else:\n            if not stack:\n                return False\n            current_char = stack.pop()\n            if current_char == '{':\n                if char != '}':\n                    return False\n            if current_char == '[':\n                if char != ']':\n                    return False\n            if current_char == '(':\n                if char != ')':\n                    return False\n    if stack:\n        return False\n    return True\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/759": [{"task_id": "Mbpp/759", "solution": "\"\"\"\nWrite a function to check whether a given string is a decimal number with a precision of 2.\nassert is_decimal('123.11')==True\n\"\"\"\n\"\"\"\nWrite a function to check whether a given string is a decimal number with a precision of 2.\nassert is_decimal('123.11')==True\n\"\"\"\nimport re\ndef is_decimal(s):\n    if re.match(r'^\\d+\\.\\d{2}$', s):\n        return True\n    else:\n        return False\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["123"]]}], "Mbpp/791": [{"task_id": "Mbpp/791", "solution": "\"\"\"\nWrite a function to remove tuples from the given tuple.\nassert remove_nested((1, 5, 7, (4, 6), 10)) == (1, 5, 7, 10)\n\"\"\"\n\"\"\"\nWrite a function to remove tuples from the given tuple.\nassert remove_nested((1, 5, 7, (4, 6), 10)) == (1, 5, 7, 10)\n\"\"\"\nfrom typing import Any, <PERSON><PERSON>\n\n\ndef remove_nested(t: <PERSON><PERSON>) -> <PERSON><PERSON>:\n    \"\"\"Remove nested tuples from the given tuple.\"\"\"\n    return tuple(x for x in t if not isinstance(x, tuple))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/767": [{"task_id": "Mbpp/767", "solution": "\"\"\"\nWrite a python function to count the number of pairs whose sum is equal to ‘sum’. The funtion gets as input a list of numbers and the sum,\nassert get_pairs_count([1,1,1,1],2) == 6\n\"\"\"\n\"\"\"\nWrite a python function to count the number of pairs whose sum is equal to ‘sum’. The funtion gets as input a list of numbers and the sum,\nassert get_pairs_count([1,1,1,1],2) == 6\n\"\"\"\ndef get_pairs_count(list1,sum):\n    count=0\n    for i in range(len(list1)):\n        for j in range(i+1,len(list1)):\n            if list1[i]+list1[j]==sum:\n                count+=1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/754": [{"task_id": "Mbpp/754", "solution": "\"\"\"\nWe say that an element is common for lists l1, l2, l3 if it appears in all three lists under the same index. Write a function to find common elements from three lists. The function should return a list.\nassert extract_index_list([1, 1, 3, 4, 5, 6, 7],[0, 1, 2, 3, 4, 5, 7],[0, 1, 2, 3, 4, 5, 7])==[1, 7]\n\"\"\"\n\"\"\"\nWe say that an element is common for lists l1, l2, l3 if it appears in all three lists under the same index. Write a function to find common elements from three lists. The function should return a list.\nassert extract_index_list([1, 1, 3, 4, 5, 6, 7],[0, 1, 2, 3, 4, 5, 7],[0, 1, 2, 3, 4, 5, 7])==[1, 7]\n\"\"\"\ndef extract_index_list(l1, l2, l3):\n    return [i for i in l1 if i in l2 and i in l3]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6], [0, 2, 4, 6, 8, 10, 12]]]}], "Mbpp/796": [{"task_id": "Mbpp/796", "solution": "\"\"\"\nWrite function to find the sum of all items in the given dictionary.\nassert return_sum({'a': 100, 'b':200, 'c':300}) == 600\n\"\"\"\n\"\"\"\nWrite function to find the sum of all items in the given dictionary.\nassert return_sum({'a': 100, 'b':200, 'c':300}) == 600\n\"\"\"\ndef return_sum(dict):\n    sum = 0\n    for i in dict.values():\n        sum += i\n    return sum\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/764": [{"task_id": "Mbpp/764", "solution": "\"\"\"\nWrite a python function to count number of digits in a given string.\nassert number_ctr('program2bedone') == 1\n\"\"\"\n\"\"\"\nWrite a python function to count number of digits in a given string.\nassert number_ctr('program2bedone') == 1\n\"\"\"\ndef number_ctr(str1):\n    count = 0\n    for i in str1:\n        if i.isdigit():\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/735": [{"task_id": "Mbpp/735", "solution": "\"\"\"\nWrite a python function to toggle bits of the number except the first and the last bit. https://www.geeksforgeeks.org/toggle-bits-number-expect-first-last-bits/\nassert toggle_middle_bits(9) == 15\n\"\"\"\n\"\"\"\nWrite a python function to toggle bits of the number except the first and the last bit. https://www.geeksforgeeks.org/toggle-bits-number-expect-first-last-bits/\nassert toggle_middle_bits(9) == 15\n\"\"\"\ndef toggle_middle_bits(n):\n    return n ^ (n >> 1) ^ 1\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[123456]]}], "Mbpp/794": [{"task_id": "Mbpp/794", "solution": "\"\"\"\nWrite a function that matches a string that has an 'a' followed by anything, ending in 'b'.\nassert text_starta_endb(\"aabbbb\")\n\"\"\"\n\"\"\"\nWrite a function that matches a string that has an 'a' followed by anything, ending in 'b'.\nassert text_starta_endb(\"aabbbb\")\n\"\"\"\nimport re\n\n\ndef text_starta_endb(text):\n    return bool(re.search(r\"^a.*b$\", text))\n\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["xaabbyz1323ababb"]]}], "Mbpp/755": [{"task_id": "Mbpp/755", "solution": "\"\"\"\nWrite a function to find the second smallest number in a list.\nassert second_smallest([1, 2, -8, -2, 0, -2])==-2\n\"\"\"\n\"\"\"\nWrite a function to find the second smallest number in a list.\nassert second_smallest([1, 2, -8, -2, 0, -2])==-2\n\"\"\"\ndef second_smallest(list1):\n    list1.sort()\n    return list1[1]\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[[1, 1, -0.5, 0, 2, -2, -2]]], "plus_fail_tests": []}], "Mbpp/741": [{"task_id": "Mbpp/741", "solution": "\"\"\"\nWrite a python function to check whether all the characters are same or not.\nassert all_Characters_Same(\"python\") == False\n\"\"\"\n\"\"\"\nWrite a python function to check whether all the characters are same or not.\nassert all_Characters_Same(\"python\") == False\n\"\"\"\ndef all_Characters_Same(str1):\n    return len(set(str1)) == 1", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/746": [{"task_id": "Mbpp/746", "solution": "\"\"\"\nWrite a function to find area of a sector. The function takes the radius and angle as inputs. Function should return None if the angle is larger than 360 degrees.\nassert sector_area(4,45)==6.283185307179586\n\"\"\"\n\"\"\"\nWrite a function to find area of a sector. The function takes the radius and angle as inputs. Function should return None if the angle is larger than 360 degrees.\nassert sector_area(4,45)==6.283185307179586\n\"\"\"\nimport math\ndef sector_area(r,angle):\n    if angle>360:\n        return None\n    else:\n        return (angle/360)*math.pi*r*r", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/785": [{"task_id": "Mbpp/785", "solution": "\"\"\"\nWrite a function to convert tuple string to integer tuple.\nassert tuple_str_int(\"(7, 8, 9)\") == (7, 8, 9)\n\"\"\"\n\"\"\"\nWrite a function to convert tuple string to integer tuple.\nassert tuple_str_int(\"(7, 8, 9)\") == (7, 8, 9)\n\"\"\"\nimport pytest\n\nfrom solutions.strings.tuple_str_int import tuple_str_int\n\n\nEXAMPLES = (\n    ('arg', 'expected'),\n    [\n        (\"(7, 8, 9)\", (7, 8, 9)),\n        (\"(1, 2, 3)\", (1, 2, 3)),\n        (\"(10, 20, 30)\", (10, 20, 30)),\n        (\"(100, 200, 300)\", (100, 200, 300)),\n        (\"(1000, 2000, 3000)\", (1000, 2000, 3000)),\n        (\"(10000, 20000, 30000)\", (10000, 20000, 30000)),\n        (\"(100000, 200000, 300000)\", (100000, 200000, 300000)),\n        (\"(1000000, 2000000, 3000000)\", (1000000, 2000000, 3000000)),\n        (\"(10000000, 20000000, 30000000)\", (10000000, 20000000, 30000000)),\n        (\"(100000000, 200000000, 300000000)\", (100000000, 200000000, 300000000)),\n        (\"(1000000000, 2000000000, 3000000000)\", (1000000000, 2000000000, 3000000000)),\n        (\"(10000000000, 20000000000, 30000000000)\", (10000000000, 20000000000, 30000000000)),\n        (\"(100000000000, 200000000000, 300000000000)\", (100000000000, 200000000000, 300000000000)),\n        (\"(1000000000000, 2000000000000, 3000000000000)\", (1000000000000, 2000000000000, 300000", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/781": [{"task_id": "Mbpp/781", "solution": "\"\"\"\nWrite a python function to check whether the count of divisors is even. https://www.w3resource.com/python-exercises/basic/python-basic-1-exercise-24.php\nassert count_divisors(10)\n\"\"\"\n\"\"\"\nWrite a python function to check whether the count of divisors is even. https://www.w3resource.com/python-exercises/basic/python-basic-1-exercise-24.php\nassert count_divisors(10)\n\"\"\"\ndef count_divisors(n):\n    count = 0\n    for i in range(1, n+1):\n        if n % i == 0:\n            count += 1\n    return count\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/787": [{"task_id": "Mbpp/787", "solution": "\"\"\"\nWrite a function that matches a string that has an a followed by three 'b'.\nassert not text_match_three(\"ac\")\n\"\"\"\n\"\"\"\nWrite a function that matches a string that has an a followed by three 'b'.\nassert not text_match_three(\"ac\")\n\"\"\"\nimport re\n\n\ndef text_match_three(text):\n    \"\"\"\n    :param text: string\n    :return: boolean\n    \"\"\"\n    pattern = r'ab{3}'\n    return re.search(pattern, text) is not None\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/760": [{"task_id": "Mbpp/760", "solution": "\"\"\"\nWrite a python function to check whether a list of numbers contains only one distinct element or not.\nassert unique_Element([1,1,1]) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether a list of numbers contains only one distinct element or not.\nassert unique_Element([1,1,1]) == True\n\"\"\"\ndef unique_Element(lst):\n    return len(set(lst)) == 1", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/788": [{"task_id": "Mbpp/788", "solution": "\"\"\"\nWrite a function to create a new tuple from the given string and list.\nassert new_tuple([\"WEB\", \"is\"], \"best\") == ('WEB', 'is', 'best')\n\"\"\"\n\"\"\"\nWrite a function to create a new tuple from the given string and list.\nassert new_tuple([\"WEB\", \"is\"], \"best\") == ('WEB', 'is', 'best')\n\"\"\"\nfrom typing import Tuple\n\n\ndef new_tuple(list_: list, string: str) -> Tuple[str, ...]:\n    return tuple(list_ + list(string))\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/792": [{"task_id": "Mbpp/792", "solution": "\"\"\"\nWrite a python function to count the number of lists in a given number of lists.\nassert count_list([[1, 3], [5, 7], [9, 11], [13, 15, 17]]) == 4\n\"\"\"\n\"\"\"\nWrite a python function to count the number of lists in a given number of lists.\nassert count_list([[1, 3], [5, 7], [9, 11], [13, 15, 17]]) == 4\n\"\"\"\ndef count_list(lists):\n    count = 0\n    for i in lists:\n        if type(i) == list:\n            count += 1\n    return count", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/786": [{"task_id": "Mbpp/786", "solution": "\"\"\"\nWrite a function to locate the right insertion point for a specified value in sorted order.\nassert right_insertion([1,2,4,5],6)==4\n\"\"\"\n\"\"\"\nWrite a function to locate the right insertion point for a specified value in sorted order.\nassert right_insertion([1,2,4,5],6)==4\n\"\"\"\ndef right_insertion(arr,val):\n    for i in range(len(arr)):\n        if arr[i]>=val:\n            return i\n    return len(arr)\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[50.64327388212607, -72.16612625583227, 95.25630303581863, -75.73998556144497, 11.857449640680656, 90.05081633927793, -90.20293226837086, -12.02758648076636, -35.964731488229475], 7]]}], "Mbpp/804": [{"task_id": "Mbpp/804", "solution": "\"\"\"\nWrite a function to check whether the product of numbers in a list is even or not.\nassert is_product_even([1,2,3])\n\"\"\"\n\"\"\"\nWrite a function to check whether the product of numbers in a list is even or not.\nassert is_product_even([1,2,3])\n\"\"\"\ndef is_product_even(list):\n    product = 1\n    for i in list:\n        product *= i\n    if product % 2 == 0:\n        return True\n    else:\n        return False\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/742": [{"task_id": "Mbpp/742", "solution": "\"\"\"\nWrite a function to caluclate the area of a tetrahedron.\nassert area_tetrahedron(3)==15.588457268119894\n\"\"\"\n\"\"\"\nWrite a function to caluclate the area of a tetrahedron.\nassert area_tetrahedron(3)==15.588457268119894\n\"\"\"\nimport math\ndef area_tetrahedron(a):\n    return math.sqrt(3)*a**2/4", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/798": [{"task_id": "Mbpp/798", "solution": "\"\"\"\nWrite a python function to find the sum of an array.\nassert _sum([1, 2, 3]) == 6\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of an array.\nassert _sum([1, 2, 3]) == 6\n\"\"\"\ndef _sum(arr):\n    sum = 0\n    for i in arr:\n        sum += i\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/769": [{"task_id": "Mbpp/769", "solution": "\"\"\"\nWrite a python function to get the difference between two lists.\nassert (Diff([10, 15, 20, 25, 30, 35, 40], [25, 40, 35])) == [10, 20, 30, 15]\n\"\"\"\n\"\"\"\nWrite a python function to get the difference between two lists.\nassert (Diff([10, 15, 20, 25, 30, 35, 40], [25, 40, 35])) == [10, 20, 30, 15]\n\"\"\"\n#PF-Assgn-33\ndef get_difference(list1,list2):\n    #remove pass and write your logic here\n    list3=[]\n    for i in list1:\n        if i not in list2:\n            list3.append(i)\n    for j in list2:\n        if j not in list1:\n            list3.append(j)\n    return list3\nlist1=[1,3,7,8,9]\nlist2=[1,2,4,5,6,7,8]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/782": [{"task_id": "Mbpp/782", "solution": "\"\"\"\nWrite a python function to find the sum of all odd length subarrays. https://www.geeksforgeeks.org/sum-of-all-odd-length-subarrays/\nassert odd_length_sum([1,2,4]) == 14\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of all odd length subarrays. https://www.geeksforgeeks.org/sum-of-all-odd-length-subarrays/\nassert odd_length_sum([1,2,4]) == 14\n\"\"\"\ndef odd_length_sum(arr):\n    sum = 0\n    for i in range(len(arr)):\n        for j in range(i, len(arr), 2):\n            sum += sum_subarray(arr, i, j)\n    return sum\n\ndef sum_subarray(arr, start, end):\n    sum = 0\n    for i in range(start, end + 1):\n        sum += arr[i]\n    return sum\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/763": [{"task_id": "Mbpp/763", "solution": "\"\"\"\nWrite a python function to find the minimum difference between any two elements in a given array. https://www.geeksforgeeks.org/find-minimum-difference-pair/\nassert find_min_diff((1,5,3,19,18,25),6) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the minimum difference between any two elements in a given array. https://www.geeksforgeeks.org/find-minimum-difference-pair/\nassert find_min_diff((1,5,3,19,18,25),6) == 1\n\"\"\"\ndef find_min_diff(arr,n):\n    arr.sort()\n    min_diff = arr[1] - arr[0]\n    for i in range(1,n):\n        min_diff = min(min_diff,arr[i] - arr[i-1])\n    return min_diff\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/790": [{"task_id": "Mbpp/790", "solution": "\"\"\"\nWrite a python function to check whether every even index contains even numbers of a given list.\nassert even_position([3,2,1]) == False\n\"\"\"\n\"\"\"\nWrite a python function to check whether every even index contains even numbers of a given list.\nassert even_position([3,2,1]) == False\n\"\"\"\ndef even_position(lst):\n    for i in range(0,len(lst),2):\n        if lst[i]%2!=0:\n            return False\n    return True", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/807": [{"task_id": "Mbpp/807", "solution": "\"\"\"\nWrite a python function to find the first odd number in a given list of numbers.\nassert first_odd([1,3,5]) == 1\n\"\"\"\n\"\"\"\nWrite a python function to find the first odd number in a given list of numbers.\nassert first_odd([1,3,5]) == 1\n\"\"\"\ndef first_odd(num_list):\n    for num in num_list:\n        if num % 2 != 0:\n            return num\n    return None\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/762": [{"task_id": "Mbpp/762", "solution": "\"\"\"\nWrite a function to check whether the given month number contains 30 days or not. Months are given as number from 1 to 12.\nassert check_monthnumber_number(6)==True\n\"\"\"\n\"\"\"\nWrite a function to check whether the given month number contains 30 days or not. Months are given as number from 1 to 12.\nassert check_monthnumber_number(6)==True\n\"\"\"\ndef check_monthnumber_number(month):\n    if month in [4,6,9,11]:\n        return True\n    elif month in [1,3,5,7,8,10,12]:\n        return False\n    else:\n        return \"Invalid month number\"", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[2]], "plus_fail_tests": [[2]]}], "Mbpp/780": [{"task_id": "Mbpp/780", "solution": "\"\"\"\nWrite a function to find the combinations of sums with tuples in the given tuple list. https://www.geeksforgeeks.org/python-combinations-of-sum-with-tuples-in-tuple-list/\nassert find_combinations([(2, 4), (6, 7), (5, 1), (6, 10)]) == [(8, 11), (7, 5), (8, 14), (11, 8), (12, 17), (11, 11)]\n\"\"\"\n\"\"\"\nWrite a function to find the combinations of sums with tuples in the given tuple list. https://www.geeksforgeeks.org/python-combinations-of-sum-with-tuples-in-tuple-list/\nassert find_combinations([(2, 4), (6, 7), (5, 1), (6, 10)]) == [(8, 11), (7, 5), (8, 14), (11, 8), (12, 17), (11, 11)]\n\"\"\"\nfrom itertools import combinations\n\n\ndef find_combinations(tuples_list):\n    result = []\n    for i in range(1, len(tuples_list) + 1):\n        for combo in combinations(tuples_list, i):\n            result.append(sum(combo))\n    return result\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/766": [{"task_id": "Mbpp/766", "solution": "\"\"\"\nWrite a function to return a list of all pairs of consecutive items in a given list.\nassert pair_wise([1,1,2,3,3,4,4,5])==[(1, 1), (1, 2), (2, 3), (3, 3), (3, 4), (4, 4), (4, 5)]\n\"\"\"\n\"\"\"\nWrite a function to return a list of all pairs of consecutive items in a given list.\nassert pair_wise([1,1,2,3,3,4,4,5])==[(1, 1), (1, 2), (2, 3), (3, 3), (3, 4), (4, 4), (4, 5)]\n\"\"\"\ndef pair_wise(l):\n    return [(l[i], l[i+1]) for i in range(len(l)-1)]", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/800": [{"task_id": "Mbpp/800", "solution": "\"\"\"\nWrite a function to remove all whitespaces from a string.\nassert remove_all_spaces('python  program')==('pythonprogram')\n\"\"\"\n\"\"\"\nWrite a function to remove all whitespaces from a string.\nassert remove_all_spaces('python  program')==('pythonprogram')\n\"\"\"\ndef remove_all_spaces(string):\n    return string.replace(\" \", \"\")\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/801": [{"task_id": "Mbpp/801", "solution": "\"\"\"\nWrite a python function to count the number of equal numbers from three given integers.\nassert test_three_equal(1,1,1) == 3\n\"\"\"\n\"\"\"\nWrite a python function to count the number of equal numbers from three given integers.\nassert test_three_equal(1,1,1) == 3\n\"\"\"\ndef test_three_equal(a,b,c):\n    if a==b==c:\n        return 3\n    elif a==b or b==c or a==c:\n        return 2\n    else:\n        return 0", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/803": [{"task_id": "Mbpp/803", "solution": "\"\"\"\nWrite a function to check whether the given number is a perfect square or not. https://www.geeksforgeeks.org/check-if-given-number-is-perfect-square-in-cpp/\nassert not is_perfect_square(10)\n\"\"\"\n\"\"\"\nWrite a function to check whether the given number is a perfect square or not. https://www.geeksforgeeks.org/check-if-given-number-is-perfect-square-in-cpp/\nassert not is_perfect_square(10)\n\"\"\"\nimport math\n\n\ndef is_perfect_square(n):\n    if n < 0:\n        return False\n    return math.sqrt(n) % 1 == 0\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/778": [{"task_id": "Mbpp/778", "solution": "\"\"\"\nWrite a function to pack consecutive duplicates of a given list elements into sublists.\nassert pack_consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4])==[[0, 0], [1], [2], [3], [4, 4], [5], [6, 6, 6], [7], [8], [9], [4, 4]]\n\"\"\"\n\"\"\"\nWrite a function to pack consecutive duplicates of a given list elements into sublists.\nassert pack_consecutive_duplicates([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4])==[[0, 0], [1], [2], [3], [4, 4], [5], [6, 6, 6], [7], [8], [9], [4, 4]]\n\"\"\"\nfrom typing import List, Any\n\n\ndef pack_consecutive_duplicates(lst: List[Any]) -> List[List[Any]]:\n    \"\"\"\n    Function to pack consecutive duplicates of a given list elements into sublists.\n    :param lst: List[Any]\n    :return: List[List[Any]]\n    \"\"\"\n    result = []\n    for i in range(len(lst)):\n        if i == 0:\n            result.append([lst[i]])\n        elif lst[i] == lst[i - 1]:\n            result[-1].append(lst[i])\n        else:\n            result.append([lst[i]])\n    return result\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/770": [{"task_id": "Mbpp/770", "solution": "\"\"\"\nWrite a python function to find the sum of fourth power of first n odd natural numbers.\nassert odd_num_sum(2) == 82\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of fourth power of first n odd natural numbers.\nassert odd_num_sum(2) == 82\n\"\"\"\ndef odd_num_sum(n):\n    sum = 0\n    for i in range(1, n*2, 2):\n        sum += i**4\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/783": [{"task_id": "Mbpp/783", "solution": "\"\"\"\nWrite a function to convert rgb color to hsv color. https://www.geeksforgeeks.org/program-change-rgb-color-model-hsv-color-model/\nassert rgb_to_hsv(255, 255, 255)==(0, 0.0, 100.0)\n\"\"\"\n\"\"\"\nWrite a function to convert rgb color to hsv color. https://www.geeksforgeeks.org/program-change-rgb-color-model-hsv-color-model/\nassert rgb_to_hsv(255, 255, 255)==(0, 0.0, 100.0)\n\"\"\"\nimport math\ndef rgb_to_hsv(r, g, b):\n    r, g, b = r/255.0, g/255.0, b/255.0\n    mx = max(r, g, b)\n    mn = min(r, g, b)\n    df = mx-mn\n    if mx == mn:\n        h = 0\n    elif mx == r:\n        h = (60 * ((g-b)/df) + 360) % 360\n    elif mx == g:\n        h = (60 * ((b-r)/df) + 120) % 360\n    elif mx == b:\n        h = (60 * ((r-g)/df) + 240) % 360\n    if mx == 0:\n        s = 0\n    else:\n        s = (df/mx)*100\n    v = mx*100\n    return h, s, v\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/775": [{"task_id": "Mbpp/775", "solution": "\"\"\"\nWrite a python function to check whether every odd index contains odd numbers of a given list.\nassert odd_position([2,1,4,3,6,7,6,3]) == True\n\"\"\"\n\"\"\"\nWrite a python function to check whether every odd index contains odd numbers of a given list.\nassert odd_position([2,1,4,3,6,7,6,3]) == True\n\"\"\"\ndef odd_position(nums):\n    for i in range(1, len(nums), 2):\n        if nums[i] % 2 == 0:\n            return False\n    return True\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/772": [{"task_id": "Mbpp/772", "solution": "\"\"\"\nWrite a function to remove all the words with k length in the given string.\nassert remove_length('The person is most value tet', 3) == 'person is most value'\n\"\"\"\n\"\"\"\nWrite a function to remove all the words with k length in the given string.\nassert remove_length('The person is most value tet', 3) == 'person is most value'\n\"\"\"\nimport re\n\n\ndef remove_length(string, k):\n    return re.sub(r'\\b\\w{%d}\\b' % k, '', string)\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["This is a Test of the Function", 4]]}], "Mbpp/799": [{"task_id": "Mbpp/799", "solution": "\"\"\"\nWrite a function to that rotate left bits by d bits a given number. We assume that the number is 32 bit.\nassert left_rotate(16,2) == 64\n\"\"\"\n\"\"\"\nWrite a function to that rotate left bits by d bits a given number. We assume that the number is 32 bit.\nassert left_rotate(16,2) == 64\n\"\"\"\ndef left_rotate(n,d):\n    return (n << d) | (n >> (32-d))", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/806": [{"task_id": "Mbpp/806", "solution": "\"\"\"\nWrite a function to find maximum run of uppercase characters in the given string.\nassert max_run_uppercase('GeMKSForGERksISBESt') == 5\n\"\"\"\n\"\"\"\nWrite a function to find maximum run of uppercase characters in the given string.\nassert max_run_uppercase('GeMKSForGERksISBESt') == 5\n\"\"\"\nimport re\n\n\ndef max_run_uppercase(s):\n    return max(len(x) for x in re.findall(r'[A-Z]+', s))", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [["a"]]}], "Mbpp/805": [{"task_id": "Mbpp/805", "solution": "\"\"\"\nWrite a function that returns the list in a list of lists whose sum of elements is the highest.\nassert max_sum_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[10, 11, 12]\n\"\"\"\n\"\"\"\nWrite a function that returns the list in a list of lists whose sum of elements is the highest.\nassert max_sum_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[10, 11, 12]\n\"\"\"\ndef max_sum_list(lists):\n    max_sum = 0\n    max_sum_list = []\n    for list in lists:\n        sum = 0\n        for element in list:\n            sum += element\n        if sum > max_sum:\n            max_sum = sum\n            max_sum_list = list\n    return max_sum_list\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[[-10, -20, -30], [-40, -50, -60], [-70, -80, -90], [-100, -110, -120]]]]}], "Mbpp/295": [{"task_id": "Mbpp/295", "solution": "\"\"\"\nWrite a function to return the sum of all divisors of a number.\nassert sum_div(8)==7\n\"\"\"\n\"\"\"\nWrite a function to return the sum of all divisors of a number.\nassert sum_div(8)==7\n\"\"\"\ndef sum_div(n):\n    sum=0\n    for i in range(1,n+1):\n        if n%i==0:\n            sum+=i\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/797": [{"task_id": "Mbpp/797", "solution": "\"\"\"\nWrite a python function to find the sum of all odd natural numbers within the range l and r.\nassert sum_in_range(2,5) == 8\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of all odd natural numbers within the range l and r.\nassert sum_in_range(2,5) == 8\n\"\"\"\ndef sum_in_range(l,r):\n    sum=0\n    for i in range(l,r+1):\n        if i%2!=0:\n            sum+=i\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/453": [{"task_id": "Mbpp/453", "solution": "\"\"\"\nWrite a python function to find the sum of even factors of a number.\nassert sumofFactors(18) == 26\n\"\"\"\n\"\"\"\nWrite a python function to find the sum of even factors of a number.\nassert sumofFactors(18) == 26\n\"\"\"\ndef sumofFactors(num):\n    sum = 0\n    for i in range(1, num+1):\n        if num % i == 0 and i % 2 == 0:\n            sum += i\n    return sum", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/808": [{"task_id": "Mbpp/808", "solution": "\"\"\"\nWrite a function to check if the given tuples contain the k or not.\nassert check_K((10, 4, 5, 6, 8), 6) == True\n\"\"\"\n\"\"\"\nWrite a function to check if the given tuples contain the k or not.\nassert check_K((10, 4, 5, 6, 8), 6) == True\n\"\"\"\ndef check_K(tup, k):\n    if k in tup:\n        return True\n    else:\n        return False\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/809": [{"task_id": "Mbpp/809", "solution": "\"\"\"\nWrite a function to check if each element of second tuple is smaller than its corresponding element in the first tuple.\nassert check_smaller((1, 2, 3), (2, 3, 4)) == False\n\"\"\"\n\"\"\"\nWrite a function to check if each element of second tuple is smaller than its corresponding element in the first tuple.\nassert check_smaller((1, 2, 3), (2, 3, 4)) == False\n\"\"\"\ndef check_smaller(t1, t2):\n    return all(x < y for x, y in zip(t1, t2))", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[[3, 7, 11, 15, 19], [2, 6, 10, 14, 18]]]}], "Mbpp/745": [{"task_id": "Mbpp/745", "solution": "\"\"\"\nWrite a function to find numbers within a given range from startnum ti endnum where every number is divisible by every digit it contains. https://www.w3resource.com/python-exercises/lambda/python-lambda-exercise-24.php\nassert divisible_by_digits(1,22)==[1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 22]\n\"\"\"\n\"\"\"\nWrite a function to find numbers within a given range from startnum ti endnum where every number is divisible by every digit it contains. https://www.w3resource.com/python-exercises/lambda/python-lambda-exercise-24.php\nassert divisible_by_digits(1,22)==[1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 22]\n\"\"\"\nfrom functools import reduce\ndef divisible_by_digits(startnum, endnum):\n    return [x for x in range(startnum, endnum+1) if all([int(digit) != 0 and x % int(digit) == 0 for digit in str(x)])]\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/630": [{"task_id": "Mbpp/630", "solution": "\"\"\"\nWrite a function to extract all the adjacent coordinates of the given coordinate tuple.\nassert get_coordinates((3, 4)) == [[2, 3], [2, 4], [2, 5], [3, 3], [3, 4], [3, 5], [4, 3], [4, 4], [4, 5]]\n\"\"\"\n\"\"\"\nWrite a function to extract all the adjacent coordinates of the given coordinate tuple.\nassert get_coordinates((3, 4)) == [[2, 3], [2, 4], [2, 5], [3, 3], [3, 4], [3, 5], [4, 3], [4, 4], [4, 5]]\n\"\"\"\nfrom typing import List, <PERSON><PERSON>\n\n\ndef get_coordinates(coord: Tuple[int, int]) -> List[Tuple[int, int]]:\n    return [(coord[0] - 1, coord[1] - 1), (coord[0], coord[1] - 1), (coord[0] + 1, coord[1] - 1),\n            (coord[0] - 1, coord[1]), (coord[0] + 1, coord[1]),\n            (coord[0] - 1, coord[1] + 1), (coord[0], coord[1] + 1), (coord[0] + 1, coord[1] + 1)]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/605": [{"task_id": "Mbpp/605", "solution": "\"\"\"\nWrite a function to check if the given integer is a prime number.\nassert prime_num(13)==True\n\"\"\"\n\"\"\"\nWrite a function to check if the given integer is a prime number.\nassert prime_num(13)==True\n\"\"\"\ndef prime_num(num):\n    if num > 1:\n        for i in range(2, num):\n            if (num % i) == 0:\n                return False\n        return True\n    else:\n        return False", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[999999999989]]}], "Mbpp/260": [{"task_id": "Mbpp/260", "solution": "\"\"\"\nWrite a function to find the nth newman–shanks–williams prime number.\nassert newman_prime(3) == 7\n\"\"\"\n\"\"\"\nWrite a function to find the nth newman–shanks–williams prime number.\nassert newman_prime(3) == 7\n\"\"\"\nimport math\n\n\ndef newman_prime(n):\n    \"\"\"\n    :param n: nth newman–shanks–williams prime number\n    :return: nth newman–shanks–williams prime number\n    \"\"\"\n    if n == 1:\n        return 2\n    else:\n        i = 1\n        while True:\n            i += 1\n            if is_prime(i):\n                if is_prime(newman(i)):\n                    n -= 1\n                    if n == 0:\n                        return i\n\n\ndef is_prime(n):\n    \"\"\"\n    :param n: number to check if it is prime\n    :return: True if n is prime, False otherwise\n    \"\"\"\n    if n == 1:\n        return False\n    for i in range(2, int(math.sqrt(n)) + 1):\n        if n % i == 0:\n            return False\n    return True\n\n\ndef newman(n):\n    \"\"\"\n    :param n: number to calculate newman–shanks–williams prime\n    :return: newman–shanks–williams prime\n    \"\"\"\n    return 2 * n**2 - n + 1\n\n", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/739": [{"task_id": "Mbpp/739", "solution": "\"\"\"\nWrite a python function to find the index of smallest triangular number with n digits. https://www.geeksforgeeks.org/index-of-smallest-triangular-number-with-n-digits/\nassert find_Index(2) == 4\n\"\"\"\n\"\"\"\nWrite a python function to find the index of smallest triangular number with n digits. https://www.geeksforgeeks.org/index-of-smallest-triangular-number-with-n-digits/\nassert find_Index(2) == 4\n\"\"\"\ndef find_Index(n):\n    i = 1\n    while True:\n        if i*(i+1)/2 >= 10**(n-1):\n            return i\n        i += 1", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": [[100]]}], "Mbpp/752": [{"task_id": "Mbpp/752", "solution": "\"\"\"\nWrite a function to find the nth jacobsthal number. https://www.geeksforgeeks.org/jacobsthal-and-jacobsthal-lucas-numbers/ 0, 1, 1, 3, 5, 11, 21, 43, 85, 171, 341, 683, 1365, 2731, ...\nassert jacobsthal_num(5) == 11\n\"\"\"\n\"\"\"\nWrite a function to find the nth jacobsthal number. https://www.geeksforgeeks.org/jacobsthal-and-jacobsthal-lucas-numbers/ 0, 1, 1, 3, 5, 11, 21, 43, 85, 171, 341, 683, 1365, 2731, ...\nassert jacobsthal_num(5) == 11\n\"\"\"\ndef jacobsthal_num(n):\n    if n == 0:\n        return 0\n    if n == 1:\n        return 1\n    return jacobsthal_num(n-1) + 2*jacobsthal_num(n-2)\n", "base_status": "pass", "plus_status": "fail", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/389": [{"task_id": "Mbpp/389", "solution": "\"\"\"\nWrite a function to find the n'th lucas number.\nassert find_lucas(9) == 76\n\"\"\"\n\"\"\"\nWrite a function to find the n'th lucas number.\nassert find_lucas(9) == 76\n\"\"\"\nimport unittest\n\n\ndef find_lucas(n):\n    if n == 0:\n        return 2\n    elif n == 1:\n        return 1\n    else:\n        return find_lucas(n - 1) + find_lucas(n - 2)\n\n\nclass TestLucas(unittest.TestCase):\n    def test_lucas(self):\n        self.assertEqual(find_lucas(9), 76)\n\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/255": [{"task_id": "Mbpp/255", "solution": "\"\"\"\nWrite a function that takes in a list and length n, and generates all combinations (with repetition) of the elements of the list and returns a list with a tuple for each combination.\nassert combinations_colors( [\"Red\",\"Green\",\"Blue\"],1)==[('Red',), ('Green',), ('Blue',)]\n\"\"\"\n\"\"\"\nWrite a function that takes in a list and length n, and generates all combinations (with repetition) of the elements of the list and returns a list with a tuple for each combination.\nassert combinations_colors( [\"Red\",\"Green\",\"Blue\"],1)==[('Red',), ('Green',), ('Blue',)]\n\"\"\"\ndef combinations_colors(colors, n):\n    if n == 0:\n        return [()]\n    else:\n        return [ (color,) + combo for color in colors for combo in combinations_colors(colors, n-1)]", "base_status": "fail", "plus_status": "fail", "base_fail_tests": [[["Red", "Green", "Blue"], 2]], "plus_fail_tests": [[["A", "B", "C", "D"], 4]]}], "Mbpp/271": [{"task_id": "Mbpp/271", "solution": "\"\"\"\nWrite a python function that takes in an integer n and finds the sum of the first n even natural numbers that are raised to the fifth power.\nassert even_Power_Sum(2) == 1056\n\"\"\"\n\"\"\"\nWrite a python function that takes in an integer n and finds the sum of the first n even natural numbers that are raised to the fifth power.\nassert even_Power_Sum(2) == 1056\n\"\"\"\ndef even_Power_Sum(n):\n    sum = 0\n    for i in range(1, n+1):\n        sum += (2*i)**5\n    return sum\n", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}], "Mbpp/599": [{"task_id": "Mbpp/599", "solution": "\"\"\"\nWrite a function to find sum and average of first n natural numbers.\nassert sum_average(10)==(55, 5.5)\n\"\"\"\n\"\"\"\nWrite a function to find sum and average of first n natural numbers.\nassert sum_average(10)==(55, 5.5)\n\"\"\"\ndef sum_average(n):\n    sum=0\n    for i in range(1,n+1):\n        sum+=i\n    avg=sum/n\n    return sum,avg", "base_status": "pass", "plus_status": "pass", "base_fail_tests": [], "plus_fail_tests": []}]}}