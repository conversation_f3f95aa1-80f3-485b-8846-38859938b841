{"code": "def f(nums):\n    output = []\n    for n in nums:\n        output.append((nums.count(n), n))\n    output.sort(reverse=True)\n    return output", "input": "[1, 1, 3, 1, 3, 1]", "output": "[(4, 1), (4, 1), (4, 1), (4, 1), (2, 3), (2, 3)]", "id": "sample_0"}
{"code": "def f(a, b, c):\n    result = {}\n    for d in a, b, c:\n        result.update(dict.fromkeys(d))\n    return result", "input": "(1, ), (1, ), (1, 2)", "output": "{1: None, 2: None}", "id": "sample_1"}
{"code": "def f(text):\n    new_text = list(text)\n    for i in '+':\n        if i in new_text:\n            new_text.remove(i)\n    return ''.join(new_text)", "input": "'hbtofdeiequ'", "output": "'hbtofdeiequ'", "id": "sample_2"}
{"code": "def f(text, value):\n    text_list = list(text)\n    text_list.append(value)\n    return ''.join(text_list)", "input": "'bcksrut', 'q'", "output": "'bcksrutq'", "id": "sample_3"}
{"code": "def f(array):\n    s = ' '\n    s += ''.join(array)\n    return s", "input": "[' ', '  ', '    ', '   ']", "output": "'           '", "id": "sample_4"}
{"code": "def f(text, lower, upper):\n    count = 0\n    new_text = list()\n    for char in text:\n        char = lower if char.isdecimal() else upper\n        if char in ['p', 'C']:\n            count += 1\n        new_text.append(char)\n    return count, ''.join(new_text)", "input": "'DSUWeqExTQdCMGpqur', 'a', 'x'", "output": "(0, 'xxxxxxxxxxxxxxxxxx')", "id": "sample_5"}
{"code": "def f(dic):\n    for k,v in sorted(dic.items(), key=lambda x: len(str(x)))[:-1]:\n        dic.pop(k)\n    return list(dic.items())", "input": "{'11': 52, '65': 34, 'a': 12, '4': 52, '74': 31}", "output": "[('74', 31)]", "id": "sample_6"}
{"code": "def f(list):\n    original = list[:]\n    while len(list) > 1:\n        list.pop(len(list) - 1)\n        for i in range(len(list)):\n            list.pop(i)\n    list = original[:]\n    if list:\n        list.pop(0)\n    return list", "input": "[]", "output": "[]", "id": "sample_7"}
{"code": "def f(string, encryption):\n    if encryption == 0:\n        return string\n    else:\n        return string.upper().encode('rot13')", "input": "'UppEr', 0", "output": "'UppEr'", "id": "sample_8"}
{"code": "def f(t):\n    for c in t:\n        if not c.isnumeric():\n            return False\n    return True", "input": "'#284376598'", "output": "False", "id": "sample_9"}
{"code": "def f(text):\n    new_text = ''\n    for ch in text.lower().strip():\n        if ch.isnumeric() or ch in '\u00c4\u00e4\u00cf\u00ef\u00d6\u00f6\u00dc\u00fc':\n            new_text += ch\n    return new_text", "input": "''", "output": "''", "id": "sample_10"}
{"code": "def f(a, b):\n    for key, value in b.items():\n        if key not in a:\n            a[key] = [value]\n        else:\n            a[key].append(value)\n    return a", "input": "{}, {'foo': 'bar'}", "output": "{'foo': ['bar']}", "id": "sample_11"}
{"code": "def f(s, x):\n    count = 0\n    while s[:len(x)] == x and count < len(s)-len(x):\n        s = s[len(x):]\n        count += len(x)\n    return s", "input": "'If you want to live a happy life! Daniel', 'Daniel'", "output": "'If you want to live a happy life! Daniel'", "id": "sample_12"}
{"code": "def f(names):\n    count = len(names)\n    numberOfNames = 0\n    for i in names:\n        if i.isalpha():\n            numberOfNames += 1\n    return numberOfNames", "input": "['sharron', 'Savannah', 'Mike Cherokee']", "output": "2", "id": "sample_13"}
{"code": "def f(s):\n    arr = list(s.strip())\n    arr.reverse()\n    return ''.join(arr)", "input": "'   OOP   '", "output": "'POO'", "id": "sample_14"}
{"code": "def f(text, wrong, right):\n    new_text = text.replace(wrong, right)\n    return new_text.upper()", "input": "\"zn kgd jw lnt\", \"h\", \"u\"", "output": "'ZN KGD JW LNT'", "id": "sample_15"}
{"code": "def f(text, suffix):\n    if text.endswith(suffix):\n        return text[:-len(suffix)]\n    return text", "input": "'zejrohaj', 'owc'", "output": "'zejrohaj'", "id": "sample_16"}
{"code": "def f(text):\n    return text.find(\",\")", "input": "\"There are, no, commas, in this text\"", "output": "9", "id": "sample_17"}
{"code": "def f(array, elem):\n    k = 0\n    l = array.copy()\n    for i in l:\n        if i > elem:\n            array.insert(k, elem)\n            break\n        k += 1\n    return array", "input": "[5, 4, 3, 2, 1, 0], 3", "output": "[3, 5, 4, 3, 2, 1, 0]", "id": "sample_18"}
{"code": "def f(x, y):\n    tmp = ''.join(['0' if c == '9' else '9' for c in y[::-1]])\n    if (x.isnumeric() and tmp.isnumeric()):\n        return x + tmp\n    else:\n        return x", "input": "\"\", \"sdasdnakjsda80\"", "output": "''", "id": "sample_19"}
{"code": "def f(text):\n    result = ''\n    for i in range(len(text)-1, -1, -1):\n        result += text[i]\n    return result", "input": "'was,'", "output": "',saw'", "id": "sample_20"}
{"code": "def f(array):\n    n = array.pop()\n    array.extend([n, n])\n    return array", "input": "[1, 1, 2, 2]", "output": "[1, 1, 2, 2, 2]", "id": "sample_21"}
{"code": "def f(a):\n    if a == 0:\n        return [0]\n    result = []\n    while a > 0:\n        result.append(a%10)\n        a = a//10\n    result.reverse()\n    return int(''.join(str(i) for i in result))", "input": "000", "output": "[0]", "id": "sample_22"}
{"code": "def f(text, chars):\n    if chars:\n        text = text.rstrip(chars)\n    else:\n        text = text.rstrip(' ')\n    if text == '':\n        return '-'\n    return text", "input": "'new-medium-performing-application - XQuery 2.2', '0123456789-'", "output": "'new-medium-performing-application - XQuery 2.'", "id": "sample_23"}
{"code": "def f(nums, i):\n    nums.pop(i)\n    return nums", "input": "[35, 45, 3, 61, 39, 27, 47], 0", "output": "[45, 3, 61, 39, 27, 47]", "id": "sample_24"}
{"code": "def f(d):\n    d = d.copy()\n    d.popitem()\n    return d", "input": "{\"l\": 1, \"t\": 2, \"x:\": 3}", "output": "{'l': 1, 't': 2}", "id": "sample_25"}
{"code": "def f(items, target):\n    for i in items.split():\n        if i in target:\n            return items.index(i)+1\n        if i.index('.') == len(i)-1 or i.index('.') == 0:\n            return 'error'\n    return '.'", "input": "\"qy. dg. rnvprt rse.. irtwv tx..\", \"wtwdoacb\"", "output": "'error'", "id": "sample_26"}
{"code": "def f(w):\n    ls = list(w)\n    omw = ''\n    while len(ls) > 0:\n        omw += ls.pop(0)\n        if len(ls) * 2 > len(w):\n            return w[len(ls):] == omw\n    return False", "input": "'flak'", "output": "False", "id": "sample_27"}
{"code": "def f(mylist):\n    revl = mylist[:]\n    revl.reverse()\n    mylist.sort(reverse=True)\n    return mylist == revl", "input": "[5, 8]", "output": "True", "id": "sample_28"}
{"code": "def f(text):\n    nums = list(filter(str.isnumeric, text))\n    assert len(nums) > 0\n    return ''.join(nums)", "input": "'-123   \\t+314'", "output": "'123314'", "id": "sample_29"}
{"code": "def f(array):\n    result = []\n    for elem in array:\n        if elem.isascii() or (isinstance(elem, int) and not str(abs(elem)).isascii()):\n            result.append(elem)\n    return result", "input": "[\"a\", \"b\", \"c\"]", "output": "['a', 'b', 'c']", "id": "sample_30"}
{"code": "def f(string):\n    upper = 0\n    for c in string:\n        if c.isupper():\n            upper += 1\n    return upper * (2,1)[upper % 2]", "input": "'PoIOarTvpoead'", "output": "8", "id": "sample_31"}
{"code": "def f(s, sep):\n    reverse = ['*' + e for e in s.split(sep)]\n    return ';'.join(reversed(reverse))", "input": "'volume', 'l'", "output": "'*ume;*vo'", "id": "sample_32"}
{"code": "def f(lists):\n    dic = {}\n    for n in lists:\n        if n in dic:\n            dic[n].append(lists.pop(lists.index(n)))\n        else:\n            dic[n] = lists[:lists.index(n) + 1]\n    return str(dic).replace(' ', '')", "input": "[5, 2, 7, 2, 3, 5]", "output": "'{5:[5,5],2:[5,2,2],7:[5,2,7]}'", "id": "sample_33"}
{"code": "def f(nums, odd1, odd2):\n    while odd1 in nums:\n        nums.remove(odd1)\n    while odd2 in nums:\n        nums.remove(odd2)\n    return nums", "input": "[1, 2, 3, 7, 7, 6, 8, 4, 1, 2, 3, 5, 1, 3, 21, 1, 3], 3, 1", "output": "[2, 7, 7, 6, 8, 4, 2, 5, 21]", "id": "sample_34"}
{"code": "def f(pattern, items):\n    result = []\n    for text in items:\n        pos = text.rfind(pattern)\n        if pos >= 0:\n            result.append(pos)\n\n    return result", "input": "\" B \", [\" bBb \", \" BaB \", \" bB\", \" bBbB \", \" bbb\"]", "output": "[]", "id": "sample_35"}
{"code": "def f(text, chars):\n    return text.rstrip(chars) if text else text", "input": "'ha', ''", "output": "'ha'", "id": "sample_36"}
{"code": "def f(text):\n    text_arr = []\n    for j in range(len(text)):\n        text_arr.append(text[j:])\n    return text_arr", "input": "'123'", "output": "['123', '23', '3']", "id": "sample_37"}
{"code": "def f(string):\n    return string.title().replace(' ', '')", "input": "'1oE-err bzz-bmm'", "output": "'1Oe-ErrBzz-Bmm'", "id": "sample_38"}
{"code": "def f(array, elem):\n    if elem in array:\n        return array.index(elem)\n    return -1", "input": "[6, 2, 7, 1], 6", "output": "0", "id": "sample_39"}
{"code": "def f(text):\n    return text.ljust(len(text) + 1, \"#\")", "input": "\"the cow goes moo\"", "output": "'the cow goes moo#'", "id": "sample_40"}
{"code": "def f(array, values):\n    array.reverse()\n    for value in values:\n        array.insert(len(array) // 2, value)\n    array.reverse()\n    return array", "input": "[58], [21, 92]", "output": "[58, 92, 21]", "id": "sample_41"}
{"code": "def f(nums):\n    nums.clear()\n    for num in nums:\n        nums.append(num*2)\n    return nums", "input": "[4, 3, 2, 1, 2, -1, 4, 2]", "output": "[]", "id": "sample_42"}
{"code": "def f(n):\n    for i in str(n):\n        if not i.isdigit():\n            n = -1\n            break\n    return n", "input": "\"6 ** 2\"", "output": "-1", "id": "sample_43"}
{"code": "def f(text):\n    ls = list(text)\n    for i in range(0, len(ls)):\n        if ls[i]!='+':\n            ls.insert(i, '+')\n            ls.insert(i, '*')\n            break\n    return '+'.join(ls)", "input": "'nzoh'", "output": "'*+++n+z+o+h'", "id": "sample_44"}
{"code": "def f(text, letter):\n    counts = {}\n    for char in text:\n        if char not in counts:\n            counts[char] = 1\n        else:\n            counts[char] += 1\n    return counts.get(letter, 0)", "input": "'za1fd1as8f7afasdfam97adfa', '7'", "output": "2", "id": "sample_45"}
{"code": "def f(l, c):\n    return c.join(l)", "input": "['many', 'letters', 'asvsz', 'hello', 'man'], ''", "output": "'manylettersasvszhelloman'", "id": "sample_46"}
{"code": "def f(text):\n    length = len(text)\n    half = length // 2\n    encode = text[:half].encode('ascii')\n    if text[half:] == encode.decode():\n        return True\n    else:\n        return False", "input": "'bbbbr'", "output": "False", "id": "sample_47"}
{"code": "def f(names):\n    if names == []:\n        return \"\"\n    smallest = names[0]\n    for name in names[1:]:\n        if name < smallest:\n            smallest = name\n    names.remove(smallest)\n    return names.join(smallest)", "input": "[]", "output": "''", "id": "sample_48"}
{"code": "def f(text):\n    if text.isidentifier():\n        return ''.join(c for c in text if c.isdigit())\n    else:\n        return ''.join(text)", "input": "'816'", "output": "'816'", "id": "sample_49"}
{"code": "def f(lst):\n    lst.clear()\n    lst += [1] * (len(lst) + 1)\n    return lst", "input": "['a', 'c', 'v']", "output": "[1]", "id": "sample_50"}
{"code": "s = '<' * 10\ndef f(num):\n    if num % 2 == 0:\n        return s\n    else:\n        return num - 1", "input": "21", "output": "20", "id": "sample_51"}
{"code": "def f(text):\n    a = []\n    for i in range(len(text)):\n        if not text[i].isdecimal():\n            a.append(text[i])\n    return ''.join(a)", "input": "\"seiq7229 d27\"", "output": "'seiq d'", "id": "sample_52"}
{"code": "def f(text):\n    occ = {}\n    for ch in text:\n        name = {'a': 'b', 'b': 'c', 'c': 'd', 'd': 'e', 'e': 'f'}\n        name = name.get(ch, ch)\n        occ[name] = occ.get(name, 0) + 1\n    return [x for _, x in occ.items()]", "input": "\"URW rNB\"", "output": "[1, 1, 1, 1, 1, 1, 1]", "id": "sample_53"}
{"code": "def f(text, s, e):\n    sublist = text[s:e]\n    if not sublist:\n        return -1\n    return sublist.index(min(sublist))", "input": "'happy', 0, 3", "output": "1", "id": "sample_54"}
{"code": "def f(array):\n    array_2 = []\n    for i in array:\n        if i>0:\n            array_2 += [i]\n    array_2.sort(reverse=True)\n    return array_2", "input": "[4, 8, 17, 89, 43, 14]", "output": "[89, 43, 17, 14, 8, 4]", "id": "sample_55"}
{"code": "def f(sentence):\n    for c in sentence:\n        if c.isascii() is False:\n            return False\n        else:\n            continue\n    return True", "input": "'1z1z1'", "output": "True", "id": "sample_56"}
{"code": "def f(text):\n    text = text.upper()\n    count_upper = 0\n    for char in text:\n        if char.isupper():\n            count_upper += 1\n        else:\n            return 'no'\n    return count_upper // 2", "input": "'ax'", "output": "1", "id": "sample_57"}
{"code": "def f(nums):\n    count = len(nums)\n    for i in [i % 2 for i in range(count)]:\n        nums.append(nums[i])\n    return nums", "input": "[-1, 0, 0, 1, 1]", "output": "[-1, 0, 0, 1, 1, -1, 0, -1, 0, -1]", "id": "sample_58"}
{"code": "def f(s):\n    a = [char for char in s if char != ' ']\n    b = a\n    for c in reversed(a):\n        if c == ' ':\n            b.pop()\n        else:\n            break\n    return ''.join(b)", "input": "'hi '", "output": "'hi'", "id": "sample_59"}
{"code": "def f(doc):\n    for x in doc:\n        if x.isalpha():\n            return x.capitalize()\n    return '-'", "input": "'raruwa'", "output": "'R'", "id": "sample_60"}
{"code": "def f(text):\n    texts = text.split()\n    if texts:\n        xtexts = [t for t in texts if t.isascii() and t not in ('nada', '0')]\n        return max(xtexts, key=len) if xtexts else 'nada'\n    return 'nada'", "input": "\"\"", "output": "'nada'", "id": "sample_61"}
{"code": "def f(user):\n    if len(list(user.keys())) > len(list(user.values())):\n        return tuple(user.keys())\n    return tuple(user.values())", "input": "{\"eating\" : \"ja\", \"books\" : \"nee\", \"piano\" : \"coke\", \"excitement\" : \"zoo\"}", "output": "('ja', 'nee', 'coke', 'zoo')", "id": "sample_62"}
{"code": "def f(text, prefix):\n    while text.startswith(prefix):\n        text = text[len(prefix):] or text\n    return text", "input": "'ndbtdabdahesyehu', 'n'", "output": "'dbtdabdahesyehu'", "id": "sample_63"}
{"code": "def f(text, size):\n    counter = len(text)\n    for i in range(size-int(size%2)):\n        text = ' '+text+' '\n        counter += 2\n        if counter >= size:\n            return text", "input": "\"7\", 10", "output": "'     7     '", "id": "sample_64"}
{"code": "def f(nums, index):\n    return nums[index] % 42 + nums.pop(index) * 2", "input": "[3, 2, 0, 3, 7], 3", "output": "9", "id": "sample_65"}
{"code": "def f(text, prefix):\n    prefix_length = len(prefix)\n    if text.startswith(prefix):\n        return text[(prefix_length - 1) // 2:\n                    (prefix_length + 1) // 2 * -1:-1]\n    else:\n        return text", "input": "'happy', 'ha'", "output": "''", "id": "sample_66"}
{"code": "def f(num1, num2, num3):\n    nums = [num1, num2, num3]\n    nums.sort()    \n    return f'{nums[0]},{nums[1]},{nums[2]}'", "input": "6,8,8", "output": "'6,8,8'", "id": "sample_67"}
{"code": "def f(text, pref):\n    if text.startswith(pref):\n        n = len(pref)\n        text = '.'.join(text[n:].split('.')[1:] + text[:n].split('.')[:-1])\n    return text", "input": "'omeunhwpvr.dq', 'omeunh'", "output": "'dq'", "id": "sample_68"}
{"code": "def f(student_marks, name):\n    if name in student_marks:\n        value = student_marks.pop(name)\n        return value\n    return 'Name unknown'", "input": "{'882afmfp': 56}, '6f53p'", "output": "'Name unknown'", "id": "sample_69"}
{"code": "def f(x):\n    a = 0\n    for i in x.split(' '):\n        a += len(i.zfill(len(i)*2))\n    return a", "input": "'999893767522480'", "output": "30", "id": "sample_70"}
{"code": "def f(d, n):\n    for i in range(n):\n        item = d.popitem()\n        d[item[1]] = item[0]\n    return d", "input": "{1: 2, 3: 4, 5: 6, 7: 8, 9: 10}, 1", "output": "{1: 2, 3: 4, 5: 6, 7: 8, 10: 9}", "id": "sample_71"}
{"code": "def f(text):\n    for c in text:\n        if not c.isnumeric():\n            return False\n    return bool(text)", "input": "'99'", "output": "True", "id": "sample_72"}
{"code": "def f(row):\n    return (row.count('1'), row.count('0'))", "input": "\"100010010\"", "output": "(3, 6)", "id": "sample_73"}
{"code": "def f(lst, i, n):\n    lst.insert(i, n)\n    return lst", "input": "[44, 34, 23, 82, 24, 11, 63, 99], 4, 15", "output": "[44, 34, 23, 82, 15, 24, 11, 63, 99]", "id": "sample_74"}
{"code": "def f(array, elem):\n    ind = array.index(elem)\n    return ind * 2 + array[-ind - 1] * 3", "input": "[-1, 2, 1, -8, 2], 2", "output": "-22", "id": "sample_75"}
{"code": "def f(nums):\n    nums = [y for y in nums if y > 0]\n    if len(nums) <= 3:\n        return nums\n    nums.reverse()\n    half = len(nums)//2\n    return nums[:half] + [0]*5 + nums[half:]", "input": "[10, 3, 2, 2, 6, 0]", "output": "[6, 2, 0, 0, 0, 0, 0, 2, 3, 10]", "id": "sample_76"}
{"code": "def f(text, character):\n    subject = text[text.rfind(character):]\n    return subject*text.count(character)", "input": "'h ,lpvvkohh,u', 'i'", "output": "''", "id": "sample_77"}
{"code": "def f(text):\n    if text and text.isupper():\n        cs = str.maketrans(string.ascii_uppercase, string.ascii_lowercase)\n        return text.translate(cs)\n    return text.lower()[:3]", "input": "'mTYWLMwbLRVOqNEf.oLsYkZORKE[Ko[{n'", "output": "'mty'", "id": "sample_78"}
{"code": "def f(arr):\n    arr = list(arr)\n    arr.clear()\n    arr.append('1')\n    arr.append('2')\n    arr.append('3')\n    arr.append('4')\n    return ','.join(arr)", "input": "[0, 1, 2, 3, 4]", "output": "'1,2,3,4'", "id": "sample_79"}
{"code": "def f(s):\n    return ''.join(reversed(s.rstrip()))", "input": "'ab        '", "output": "'ba'", "id": "sample_80"}
{"code": "def f(dic, inx):\n    try:\n        dic[list(dic)[list(dic).index(inx)]] = list(dic)[list(dic).index(inx)].lower()\n    except ValueError:\n        pass\n    return list(dic.items())", "input": "{\"Bulls\": 23, \"White Sox\": 45}, \"Bulls\"", "output": "[('Bulls', 'bulls'), ('White Sox', 45)]", "id": "sample_81"}
{"code": "def f(a, b, c, d):\n    return a and b or c and d", "input": "'CJU', 'BFS', 'WBYDZPVES', 'Y'", "output": "'BFS'", "id": "sample_82"}
{"code": "def f(text):\n    l = text.rpartition('0')\n    if l[2] == '':\n        return '-1:-1'\n    return f'{len(l[0])}:{l[2].find(\"0\") + 1}'", "input": "'qq0tt'", "output": "'2:0'", "id": "sample_83"}
{"code": "def f(text):\n    arr = text.split()\n    result = []\n    for item in arr:\n        if item.endswith('day'):\n            item += 'y'\n        else:\n            item += 'day'\n        result.append(item)\n    return ' '.join(result)", "input": "\"nwv mef ofme bdryl\"", "output": "'nwvday mefday ofmeday bdrylday'", "id": "sample_84"}
{"code": "def f(n):\n    values = {0: 3, 1: 4.5, 2: '-'}\n    res = {}\n    for i, j in values.items():\n        if i % n != 2:\n            res[j] = n // 2\n    return sorted(res)", "input": "12", "output": "[3, 4.5]", "id": "sample_85"}
{"code": "def f(instagram, imgur, wins):\n    photos = [instagram, imgur]\n    if instagram == imgur:\n        return wins\n    if wins == 1:\n        return photos.pop()\n    else:\n        photos.reverse()\n        return photos.pop()", "input": "['sdfs', 'drcr', '2e'], ['sdfs', 'dr2c', 'QWERTY'], 0", "output": "['sdfs', 'drcr', '2e']", "id": "sample_86"}
{"code": "def f(nums):\n    nums.reverse()\n    return ''.join(map(str, nums))", "input": "[-1, 9, 3, 1, -2]", "output": "'-2139-1'", "id": "sample_87"}
{"code": "def f(s1, s2):\n    if s2.endswith(s1):\n        s2 = s2[:len(s1) * -1]\n    return s2", "input": "\"he\", \"hello\"", "output": "'hello'", "id": "sample_88"}
{"code": "def f(char):\n    if char not in 'aeiouAEIOU':\n        return None\n    if char in 'AEIOU':\n        return char.lower()\n    return char.upper()", "input": "'o'", "output": "'O'", "id": "sample_89"}
{"code": "def f(array):\n    return_arr = []\n    for a in array:\n        return_arr.append(a.copy())\n    return return_arr", "input": "[[1, 2, 3], [], [1, 2, 3]]", "output": "[[1, 2, 3], [], [1, 2, 3]]", "id": "sample_90"}
{"code": "def f(s):\n    d = dict.fromkeys(s, 0)\n    return list(d.keys())", "input": "\"12ab23xy\"", "output": "['1', '2', 'a', 'b', '3', 'x', 'y']", "id": "sample_91"}
{"code": "def f(text):\n    return text.isascii()", "input": "'wW\uc758IV]HDJjhgK[dGIUlVO@Ess$coZkBqu[Ct'", "output": "False", "id": "sample_92"}
{"code": "def f(n):\n    length = len(n) + 2\n    revn = list(n)\n    result = ''.join(revn)\n    revn.clear()\n    return result + ('!' * length)", "input": "'iq'", "output": "'iq!!!!'", "id": "sample_93"}
{"code": "def f(a, b):\n    return {**a, **b}", "input": "{'w': 5, 'wi': 10}, {'w': 3}", "output": "{'w': 3, 'wi': 10}", "id": "sample_94"}
{"code": "def f(zoo):\n    return dict((v, k) for k, v in zoo.items())", "input": "{'AAA': 'fr'}", "output": "{'fr': 'AAA'}", "id": "sample_95"}
{"code": "def f(text):\n    return not any([c.isupper() for c in text])", "input": "'lunabotics'", "output": "True", "id": "sample_96"}
{"code": "def f(lst):\n    lst.clear()\n    for i in lst:\n        if i == 3:\n            return False\n    else:\n        return True", "input": "[2, 0]", "output": "True", "id": "sample_97"}
{"code": "def f(s):\n    return sum([s.istitle() for s in s.split()])", "input": "'SOME OF THIS Is uknowN!'", "output": "1", "id": "sample_98"}
{"code": "def f(text, sep, num):\n    return '___'.join(text.rsplit(sep, num))", "input": "'aa+++bb', '+', 1", "output": "'aa++___bb'", "id": "sample_99"}
{"code": "def f(d, rm):\n    res = d.copy()\n    for k in rm:\n        if k in res:\n            del res[k]\n    return res", "input": "{'1': 'a', 1: 'a', 1: 'b', '1': 'b'}, [1]", "output": "{'1': 'b'}", "id": "sample_100"}
{"code": "def f(array, i_num, elem):\n    array.insert(i_num, elem)\n    return array", "input": "[ -4,   1,  0], 1, 4", "output": "[-4, 4, 1, 0]", "id": "sample_101"}
{"code": "def f(names, winners):\n    ls = [names.index(name) for name in names if name in winners]\n    ls.sort(reverse=True)\n    return ls", "input": "['e', 'f', 'j', 'x', 'r', 'k'], ['a', 'v', '2', 'im', 'nb', 'vj', 'z']", "output": "[]", "id": "sample_102"}
{"code": "def f(s):\n    return ''.join((c.casefold() for c in s))", "input": "'abcDEFGhIJ'", "output": "'abcdefghij'", "id": "sample_103"}
{"code": "def f(text):\n    dic = dict()\n    for char in text:\n        dic[char] = dic.get(char, 0) + 1\n    for key in dic:\n        if dic[key] > 1:\n            dic[key] = 1\n    return dic", "input": "\"a\"", "output": "{'a': 1}", "id": "sample_104"}
{"code": "def f(text):\n    if not text.istitle():\n        return text.title()\n    return text.lower()", "input": "\"PermissioN is GRANTed\"", "output": "'Permission Is Granted'", "id": "sample_105"}
{"code": "def f(nums):\n    count = len(nums)\n    for i in range(0, count):\n        nums.insert(i, nums[i]*2)\n    return nums", "input": "[2, 8, -2, 9, 3, 3]", "output": "[4, 4, 4, 4, 4, 4, 2, 8, -2, 9, 3, 3]", "id": "sample_106"}
{"code": "def f(text):\n    result = []\n    for i in range(len(text)):\n        if not text[i].isascii():\n            return False\n        elif text[i].isalnum():\n            result.append(text[i].upper())\n        else:\n            result.append(text[i])\n    return ''.join(result)", "input": "\"ua6hajq\"", "output": "'UA6HAJQ'", "id": "sample_107"}
{"code": "def f(var):\n    amount = len(var) if type(var) == list else 0\n    if type(var) == dict:\n        amount = len(var.keys())\n    nonzero = amount if amount > 0 else 0\n    return nonzero", "input": "1", "output": "0", "id": "sample_108"}
{"code": "def f(nums, spot, idx):\n    nums.insert(spot, idx)\n    return nums", "input": "[1, 0, 1, 1], 0, 9", "output": "[9, 1, 0, 1, 1]", "id": "sample_109"}
{"code": "def f(text):\n    a = ['']\n    b = ''\n    for i in text:\n        if not i.isspace():\n            a.append(b)\n            b = ''\n        else:\n            b += i\n    return len(a)", "input": "\"       \"", "output": "1", "id": "sample_110"}
{"code": "def f(marks):\n    highest = 0\n    lowest = 100\n    for value in marks.values():\n        if value > highest:\n            highest = value\n        if value < lowest:\n            lowest = value\n    return highest, lowest", "input": "{'x': 67, 'v': 89, '': 4, 'alij': 11, 'kgfsd': 72, 'yafby': 83}", "output": "(89, 4)", "id": "sample_111"}
{"code": "def f(sentence):\n    ls = list(sentence)\n    for letter in ls:\n        if not letter.istitle():\n            ls.remove(letter)\n    return ''.join(ls)", "input": "'XYZ LittleRedRidingHood LiTTleBIGGeXEiT fault'", "output": "'XYZLtRRdnHodLTTBIGGeXET fult'", "id": "sample_112"}
{"code": "def f(line):\n    count = 0\n    a = []\n    for i in range(len(line)):\n        count += 1\n        if count%2==0:\n            a.append(line[i].swapcase())\n        else:\n            a.append(line[i])\n    return ''.join(a)", "input": "\"987yhNSHAshd 93275yrgSgbgSshfbsfB\"", "output": "'987YhnShAShD 93275yRgsgBgssHfBsFB'", "id": "sample_113"}
{"code": "def f(text, sep):\n    return text.rsplit(sep, maxsplit=2)", "input": "\"a-.-.b\", \"-.\"", "output": "['a', '', 'b']", "id": "sample_114"}
{"code": "def f(text):\n    res = []\n    for ch in text.encode('utf-8'):\n        if ch == 61:\n            break\n        if ch == 0:\n            pass\n        res.append(f'{ch}; '.encode('utf-8'))\n    return b''.join(res)", "input": "'os||agx5'", "output": "b'111; 115; 124; 124; 97; 103; 120; 53; '", "id": "sample_115"}
{"code": "def f(d, count):\n    for i in range(count):\n        if d == {}:\n            break\n        d.popitem()\n    return d", "input": "{}, 200", "output": "{}", "id": "sample_116"}
{"code": "def f(numbers):\n    for i in range(len(numbers)):\n        if numbers.count('3') > 1:\n            return i\n    return -1", "input": "\"23157\"", "output": "-1", "id": "sample_117"}
{"code": "def f(text, chars):\n    num_applies = 2\n    extra_chars = ''\n    for i in range(num_applies):\n        extra_chars += chars\n        text = text.replace(extra_chars, '')\n    return text", "input": "'zbzquiuqnmfkx', 'mk'", "output": "'zbzquiuqnmfkx'", "id": "sample_118"}
{"code": "def f(text):\n    result = \"\"\n    for i in range(len(text)):\n        if i % 2 == 0:\n            result += text[i].swapcase()\n        else:\n            result += text[i]\n    return result", "input": "\"vsnlygltaw\"", "output": "'VsNlYgLtAw'", "id": "sample_119"}
{"code": "def f(countries):\n    language_country = dict()\n    for country, language in countries.items():\n        if language not in language_country:\n            language_country[language] = []\n        language_country[language].append(country)\n    return language_country", "input": "{}", "output": "{}", "id": "sample_120"}
{"code": "def f(s):\n    nums = ''.join(filter(lambda c:c.isdecimal(), s))\n    if nums == '': return 'none'\n    m = max([int(num) for num in nums.split(',')])\n    return str(m)", "input": "'01,001'", "output": "'1001'", "id": "sample_121"}
{"code": "def f(string):\n    if string[:4] != 'Nuva':\n        return 'no'\n    else:\n        return string.rstrip()", "input": "'Nuva?dlfuyjys'", "output": "'Nuva?dlfuyjys'", "id": "sample_122"}
{"code": "def f(array, elem):\n    for idx, e in enumerate(array):\n        if e > elem and array[idx - 1] < elem:\n            array.insert(idx, elem)\n    return array", "input": "[1, 2, 3, 5, 8], 6", "output": "[1, 2, 3, 5, 6, 8]", "id": "sample_123"}
{"code": "def f(txt, sep, sep_count):\n    o = ''\n    while sep_count > 0 and txt.count(sep) > 0:\n        o += txt.rsplit(sep, 1)[0] + sep\n        txt = txt.rsplit(sep, 1)[1]\n        sep_count -= 1\n    return o + txt", "input": "'i like you', ' ', -1", "output": "'i like you'", "id": "sample_124"}
{"code": "def f(text, res):\n    for c in '*\\n\"':\n        text = text.replace(c, '!' + str(res))\n    if text.startswith('!'):\n        text = text[len(str(res)):]\n    return text", "input": "'\"Leap and the net will appear', 123", "output": "'3Leap and the net will appear'", "id": "sample_125"}
{"code": "def f(text):\n    s = text.rpartition('o')\n    div, div2 = (s[0] == '' and '-' or s[0]), (s[2] == '' and '-' or s[2])\n    return s[1] + div + s[1] + div2", "input": "'kkxkxxfck'", "output": "'-kkxkxxfck'", "id": "sample_126"}
{"code": "def f(text):\n    s = text.splitlines()\n    return len(s)", "input": "\"145\\n\\n12fjkjg\"", "output": "3", "id": "sample_127"}
{"code": "def f(text):\n    odd = ''\n    even = ''\n    for i, c in enumerate(text):\n        if i % 2 == 0:\n            even += c\n        else:\n            odd += c\n    return even + odd.lower()", "input": "'Mammoth'", "output": "'Mmohamt'", "id": "sample_128"}
{"code": "def f(text, search_string):\n    indexes = []\n    while search_string in text:\n        indexes.append(text.rindex(search_string))\n        text = text[:text.rindex(search_string)]\n    return indexes", "input": "'ONBPICJOHRHDJOSNCPNJ9ONTHBQCJ', 'J'", "output": "[28, 19, 12, 6]", "id": "sample_129"}
{"code": "def f(m):\n    items = list(m.items())\n    for i in range(len(items)-2, -1, -1):\n        tmp = items[i]\n        items[i] = items[i+1] \n        items[i+1] = tmp\n    return ['{}={}', '{1}={0}'][len(items) % 2].format(\n        *m.keys(), **m\n    )", "input": "{'l':4, 'h':6, 'o':9}", "output": "'h=l'", "id": "sample_130"}
{"code": "def f(text):\n    a = len(text)\n    count = 0\n    while text:\n        if text.startswith('a'):\n            count += text.find(' ')\n        else:\n            count += text.find('\\n')\n        text = text[text.find('\\n')+1:text.find('\\n')+a+1]\n    return count", "input": "\"a\\nkgf\\nasd\\n\"", "output": "1", "id": "sample_131"}
{"code": "def f(a_str, prefix):\n    if a_str.removeprefix(prefix):\n        return a_str\n    else:\n        return prefix + a_str", "input": "'abc', 'abcd'", "output": "'abc'", "id": "sample_132"}
{"code": "def f(nums, elements):\n    result = []\n    for i in range(len(elements)):\n        result.append(nums.pop())\n    return nums", "input": "[7, 1, 2, 6, 0, 2], [9, 0, 3]", "output": "[7, 1, 2]", "id": "sample_133"}
{"code": "def f(n):\n    t = 0\n    b = ''\n    digits = list(map(int, str(n)))\n    for d in digits:\n        if d == 0: t += 1\n        else: break\n    for _ in range(t):\n        b += str(1) + '0' + str(4)\n    b += str(n)\n    return b", "input": "372359", "output": "'372359'", "id": "sample_134"}
{"code": "def f():\n    d = {\n        'Russia': [('Moscow', 'Russia'), ('Vladivostok', 'Russia')],\n        'Kazakhstan': [('Astana', 'Kazakhstan')],\n    }\n    return list(d.keys())", "input": "", "output": "['Russia', 'Kazakhstan']", "id": "sample_135"}
{"code": "def f(text, width):\n    lines = [line.center(width) for line in text.split('\\n')]\n    return '\\n'.join(lines)", "input": "\"a\\nbc\\n\\nd\\nef\", 5", "output": "'  a  \\n  bc \\n     \\n  d  \\n  ef '", "id": "sample_136"}
{"code": "def f(nums):\n    count = 0\n    for i in range(len(nums)):\n        if len(nums) == 0:\n            break\n        if count % 2 == 0:\n            nums.pop()\n        else:\n            nums.pop(0)\n        count += 1\n    return nums", "input": "[3, 2, 0, 0, 2, 3]", "output": "[]", "id": "sample_137"}
{"code": "def f(text, chars):\n    listchars = list(chars)\n    first = listchars.pop()\n    for i in listchars:\n        text = text[0:text.find(i)]+i+text[text.find(i)+1:]\n    return text", "input": "'tflb omn rtt', 'm'", "output": "'tflb omn rtt'", "id": "sample_138"}
{"code": "def f(first, second):\n    if len(first) < 10 or len(second) < 10:\n        return 'no'\n    for i in range(5):\n        if first[i] != second[i]:\n            return 'no'\n    first.extend(second)\n    return first", "input": "[1, 2, 1], [1, 1, 2]", "output": "'no'", "id": "sample_139"}
{"code": "def f(st):\n    if st.lower().rindex('h', st.lower().rindex('i')) >= st.lower().rindex('i'):\n        return 'Hey'\n    else:\n        return 'Hi'", "input": "'Hi there'", "output": "'Hey'", "id": "sample_140"}
{"code": "def f(li):\n    return [li.count(i) for i in li]", "input": "['k', 'x', 'c', 'x', 'x', 'b', 'l', 'f', 'r', 'n', 'g']", "output": "[1, 3, 1, 3, 3, 1, 1, 1, 1, 1, 1]", "id": "sample_141"}
{"code": "def f(x):\n    if x.islower():\n        return x\n    else:\n        return x[::-1]", "input": "'ykdfhp'", "output": "'ykdfhp'", "id": "sample_142"}
{"code": "def f(s, n):\n    return s.casefold() == n.casefold()", "input": "\"daaX\", \"daaX\"", "output": "True", "id": "sample_143"}
{"code": "def f(vectors):\n    sorted_vecs = []\n    for vec in vectors:\n        vec.sort()\n        sorted_vecs.append(vec)\n    return sorted_vecs", "input": "[]", "output": "[]", "id": "sample_144"}
{"code": "def f(price, product):\n    inventory = ['olives', 'key', 'orange']\n    if product not in inventory:\n        return price\n    else:\n        price *=.85\n        inventory.remove(product)\n    return price", "input": "8.50, 'grapes'", "output": "8.5", "id": "sample_145"}
{"code": "def f(single_digit):\n    result = []\n    for c in range(1, 11):\n        if c != single_digit:\n            result.append(c)\n    return result", "input": "5", "output": "[1, 2, 3, 4, 6, 7, 8, 9, 10]", "id": "sample_146"}
{"code": "def f(nums):\n    middle = len(nums)//2\n    return nums[middle:] + nums[0:middle]", "input": "[1, 1, 1]", "output": "[1, 1, 1]", "id": "sample_147"}
{"code": "def f(forest, animal):\n    index = forest.index(animal)\n    result = list(forest)\n    while index < len(forest)-1:\n        result[index] = forest[index+1]\n        index += 1\n    if index == len(forest)-1:\n        result[index] = '-'\n    return ''.join(result)", "input": "'2imo 12 tfiqr.', 'm'", "output": "'2io 12 tfiqr.-'", "id": "sample_148"}
{"code": "def f(tuple_list, joint):\n    string = ''\n    for num in tuple_list:\n        string += dict.fromkeys(str(num), joint * len(str(num))).popitem()[0] + joint\n    return string", "input": "(32332, 23543, 132323, 33300), ','", "output": "'2,4,2,0,'", "id": "sample_149"}
{"code": "def f(numbers, index):\n    for n in numbers[index:]:\n        numbers.insert(index, n)\n        index += 1\n    return numbers[:index]", "input": "[-2, 4, -4], 0", "output": "[-2, 4, -4]", "id": "sample_150"}
{"code": "def f(text):\n    for c in text:\n        if c.isdigit():\n            if c == '0':\n                c = '.'\n            else:\n                c = '0' if c != '1' else '.'\n    return ''.join(list(text)).replace('.', '0')", "input": "'697 this is the ultimate 7 address to attack'", "output": "'697 this is the ultimate 7 address to attack'", "id": "sample_151"}
{"code": "def f(text):\n    n = 0\n    for char in text:\n        if char.isupper():\n            n += 1\n    return n", "input": "''.join(['A'] * 20)", "output": "20", "id": "sample_152"}
{"code": "def f(text, suffix, num):\n    str_num = str(num)\n    return text.endswith(suffix + str_num)", "input": "'friends and love', 'and', 3", "output": "False", "id": "sample_153"}
{"code": "def f(s, c):\n    s = s.split(' ')\n    return ((c + \"  \") + (\"  \".join(s[::-1])))", "input": "'Hello There', '*'", "output": "'*  There  Hello'", "id": "sample_154"}
{"code": "def f(ip, n):\n    i = 0\n    out = ''\n    for c in ip:\n        if i == n:\n            out += '\\n'\n            i = 0\n        i += 1\n        out += c\n    return out", "input": "\"dskjs hjcdjnxhjicnn\", 4", "output": "'dskj\\ns hj\\ncdjn\\nxhji\\ncnn'", "id": "sample_155"}
{"code": "def f(text, limit, char):\n    if limit < len(text):\n        return text[0:limit]\n    return text.ljust(limit, char)", "input": "'tqzym', 5, 'c'", "output": "'tqzym'", "id": "sample_156"}
{"code": "def f(phrase):\n    ans = 0\n    for w in phrase.split():\n        for ch in w:\n            if ch == \"0\":\n                ans += 1\n    return ans", "input": "\"aboba 212 has 0 digits\"", "output": "1", "id": "sample_157"}
{"code": "def f(arr):\n    n = [item for item in arr if item%2 == 0]\n    m = n+arr\n    for i in m:\n        if m.index(i) >= len(n):\n            m.remove(i)\n    return m", "input": "[3, 6, 4, -2, 5]", "output": "[6, 4, -2, 6, 4, -2]", "id": "sample_158"}
{"code": "def f(st):\n    swapped = ''\n    for ch in reversed(st):\n        swapped += ch.swapcase()\n    return swapped", "input": "'RTiGM'", "output": "'mgItr'", "id": "sample_159"}
{"code": "def f(dictionary):\n    while not dictionary.get(1, len(dictionary)):\n        dictionary.clear()\n        break\n    return dictionary", "input": "{1: 47698, 1: 32849, 1: 38381, 3: 83607}", "output": "{1: 38381, 3: 83607}", "id": "sample_160"}
{"code": "def f(text, value):\n    left, _, right = text.partition(value)\n    return right + left", "input": "'difkj rinpx', 'k'", "output": "'j rinpxdif'", "id": "sample_161"}
{"code": "def f(text):\n    result = ''\n    for char in text:\n        if char.isalnum():\n            result += char.upper()\n    return result", "input": "'\u0441 bishop.Swift'", "output": "'\u0421BISHOPSWIFT'", "id": "sample_162"}
{"code": "def f(text, space_symbol, size):\n    spaces = ''.join(space_symbol for i in range(size-len(text)))\n    return text + spaces", "input": "'w', '))', 7", "output": "'w))))))))))))'", "id": "sample_163"}
{"code": "def f(lst):\n    lst.sort()\n    return lst[0:3]", "input": "[5, 8, 1, 3, 0]", "output": "[0, 1, 3]", "id": "sample_164"}
{"code": "def f(text, lower, upper):\n    return text[lower:upper].isascii()", "input": "'=xtanp|sugv?z', 3, 6", "output": "True", "id": "sample_165"}
{"code": "def f(graph):\n    new_graph = {}\n    for key, value in graph.items():\n        new_graph[key] = {}\n        for subkey in value:\n            new_graph[key][subkey] = ''\n    return new_graph", "input": "{}", "output": "{}", "id": "sample_166"}
{"code": "def f(XAAXX, s):\n    count = 0\n    idx = -1\n    while XAAXX.find('XXXX', idx+1) != -1:\n        idx = XAAXX.find('XXXX', idx+1) \n        count += 1 \n    compound = count * s.title()\n    return XAAXX.replace('XXXX', compound)", "input": "'aaXXXXbbXXXXccXXXXde', 'QW'", "output": "'aaQwQwQwbbQwQwQwccQwQwQwde'", "id": "sample_167"}
{"code": "def f(text, new_value, index):\n    key = text.maketrans(text[index], new_value)\n    return text.translate(key)", "input": "'spain', 'b', 4", "output": "'spaib'", "id": "sample_168"}
{"code": "def f(text):\n    ls = list(text)\n    total = (len(text) - 1) * 2\n    for i in range(1, total+1):\n        if i % 2:\n            ls.append('+')\n        else:\n            ls.insert(0, '+')\n    return ''.join(ls).rjust(total)", "input": "'taole'", "output": "'++++taole++++'", "id": "sample_169"}
{"code": "def f(nums, number):\n    return nums.count(number)", "input": "[12, 0, 13, 4, 12], 12", "output": "2", "id": "sample_170"}
{"code": "def f(nums):\n    count = len(nums) // 2\n    for _ in range(count):\n        nums.pop(0)\n    return nums", "input": "[3, 4, 1, 2, 3]", "output": "[1, 2, 3]", "id": "sample_171"}
{"code": "def f(array):\n    for i in range(len(array)):\n        if array[i] < 0:\n            array.pop(i)\n    return array", "input": "[]", "output": "[]", "id": "sample_172"}
{"code": "def f(list_x):\n    item_count = len(list_x)\n    new_list = []\n    for i in range(item_count):\n        new_list.append(list_x.pop())\n    return new_list", "input": "[5, 8, 6, 8, 4]", "output": "[4, 8, 6, 8, 5]", "id": "sample_173"}
{"code": "def f(lst):\n    lst[1:4] = lst[1:4][::-1]\n    return lst", "input": "[1, 2, 3]", "output": "[1, 3, 2]", "id": "sample_174"}
{"code": "def f(s, amount):\n    lines = s.splitlines()\n    w = max(map(lambda l: l.rfind(' '), lines))\n    ls = [[l, (w + 1) * amount - l.rfind(' ')] for l in lines]\n    for i, line in enumerate(ls):\n        ls[i][0] = line[0] + ' ' * line[1]\n    return '\\n'.join(map(lambda l: l[0], ls))", "input": "'\\n', 2", "output": "' '", "id": "sample_175"}
{"code": "def f(text, to_place):\n    after_place = text[:text.find(to_place, 0) + 1]\n    before_place = text[text.find(to_place, 0) + 1:]\n    return after_place + before_place", "input": "'some text', 'some'", "output": "'some text'", "id": "sample_176"}
{"code": "def f(text):\n    text = list(text)\n    for i in range(len(text)):\n        if i % 2 == 1:\n            text[i] = text[i].swapcase()\n    return ''.join(text)", "input": "'Hey DUdE THis $nd^ &*&this@#'", "output": "'HEy Dude tHIs $Nd^ &*&tHiS@#'", "id": "sample_177"}
{"code": "def f(array, n):\n    return array[n:]", "input": "[0, 0, 1, 2, 2, 2, 2], 4", "output": "[2, 2, 2]", "id": "sample_178"}
{"code": "def f(nums):\n    # Pass in a copy to avoid modifying nums\n    nums = nums[:]\n    count = len(nums)\n    for i in range(-count+1, 0):\n        nums.insert(0, nums[i])\n    return nums", "input": "[7, 1, 2, 6, 0, 2]", "output": "[2, 0, 6, 2, 1, 7, 1, 2, 6, 0, 2]", "id": "sample_179"}
{"code": "def f(nums):\n    a = -1\n    b = nums[1:]\n    while a <= b[0]:\n        nums.remove(b[0])\n        a = 0\n        b = b[1:]\n    return nums", "input": "[-1, 5, 3, -2, -6, 8, 8]", "output": "[-1, -2, -6, 8, 8]", "id": "sample_180"}
{"code": "def f(s):\n    count = 0\n    digits = \"\"\n    for c in s:\n        if c.isdigit():\n            count += 1\n            digits += c\n    return [digits, count]", "input": "\"qwfasgahh329kn12a23\"", "output": "['3291223', 7]", "id": "sample_181"}
{"code": "def f(dic):\n    return sorted(dic.items(), key=lambda x: x[0])", "input": "{'b': 1, 'a': 2}", "output": "[('a', 2), ('b', 1)]", "id": "sample_182"}
{"code": "def f(text):\n    ls = text.split()\n    lines = \" \".join(ls[::3]).splitlines()\n    res = []\n    for i in range(2):\n        ln = ls[1::3]\n        if 3 * i + 1 < len(ln):\n            res.append(\" \".join(ln[3 * i:3 * (i + 1)]))\n    return lines + res", "input": "\"echo hello!!! nice!\"", "output": "['echo']", "id": "sample_183"}
{"code": "def f(digits):\n    digits.reverse()\n    if len(digits) < 2:\n        return digits\n    for i in range(0, len(digits), 2):\n        digits[i], digits[i+1] = digits[i+1], digits[i]\n    return digits", "input": "[1,2]", "output": "[1, 2]", "id": "sample_184"}
{"code": "def f(L):\n    N = len(L)\n    for k in range(1, N//2 + 1):\n        i = k - 1\n        j = N - k\n        while i < j:\n            # swap elements:\n            L[i], L[j] = L[j], L[i]\n            # update i, j:\n            i += 1\n            j -= 1\n    return L", "input": "[16, 14, 12, 7, 9, 11]", "output": "[11, 14, 7, 12, 9, 16]", "id": "sample_185"}
{"code": "def f(text):\n    return ' '.join(map(str.lstrip, text.split()))", "input": "'pvtso'", "output": "'pvtso'", "id": "sample_186"}
{"code": "def f(d, index):\n    length = len(d.items())\n    idx = index % length\n    v = d.popitem()[1]\n    for _ in range(idx):\n        d.popitem()\n    return v", "input": "{27:39}, 1", "output": "39", "id": "sample_187"}
{"code": "def f(strings):\n    new_strings = []\n    for string in strings:\n        first_two = string[:2]\n        if first_two.startswith('a') or first_two.startswith('p'):\n            new_strings.append(first_two)\n\n    return new_strings", "input": "[\"a\", \"b\", \"car\", \"d\"]", "output": "['a']", "id": "sample_188"}
{"code": "def f(out, mapping):\n    for key in mapping:\n        out.format_map(mapping)\n        if len(re.findall(r'{\\w}', out)) == 0:\n            break\n        mapping[key][1] = mapping[key][1][::-1]\n    return out", "input": "\"{{{{}}}}\", {}", "output": "'{{{{}}}}'", "id": "sample_189"}
{"code": "def f(text):\n    short = ''\n    for c in text:\n        if(c.islower()):\n            short += c\n    return short", "input": "'980jio80jic kld094398IIl '", "output": "'jiojickldl'", "id": "sample_190"}
{"code": "def f(string):\n    if string.isupper():\n        return True\n    else:\n        return False", "input": "'Ohno'", "output": "False", "id": "sample_191"}
{"code": "def f(text, suffix):\n    output = text\n    while text.endswith(suffix):\n        output = text[:-len(suffix)]\n        text = output\n    return output", "input": "'!klcd!ma:ri', '!'", "output": "'!klcd!ma:ri'", "id": "sample_192"}
{"code": "def f(string):\n    count = string.count(':')\n    return string.replace(':', '', count - 1)", "input": "'1::1'", "output": "'1:1'", "id": "sample_193"}
{"code": "def f(matr, insert_loc):\n    matr.insert(insert_loc, [])\n    return matr", "input": "[[5, 6, 2, 3], [1, 9, 5, 6]], 0", "output": "[[], [5, 6, 2, 3], [1, 9, 5, 6]]", "id": "sample_194"}
{"code": "def f(text):\n    for p in ['acs', 'asp', 'scn']:\n        text = text.removeprefix(p) + ' '\n    return text.removeprefix(' ')[:-1]", "input": "'ilfdoirwirmtoibsac'", "output": "'ilfdoirwirmtoibsac  '", "id": "sample_195"}
{"code": "def f(text):\n    text = text.replace(' x', ' x.')\n    if text.istitle(): return 'correct'\n    text = text.replace(' x.', ' x')\n    return 'mixed'", "input": "\"398 Is A Poor Year To Sow\"", "output": "'correct'", "id": "sample_196"}
{"code": "def f(temp, timeLimit):\n    s = timeLimit // temp\n    e = timeLimit % temp\n    return [f'{e} oC', f'{s} {e}'][s > 1]", "input": "1, 1234567890", "output": "'1234567890 0'", "id": "sample_197"}
{"code": "def f(text, strip_chars):\n    return text[::-1].strip(strip_chars)[::-1]", "input": "'tcmfsmj', 'cfj'", "output": "'tcmfsm'", "id": "sample_198"}
{"code": "def f(str, char):\n    base = char * (str.count(char) + 1)\n    return str.removesuffix(base)", "input": "'mnmnj krupa...##!@#!@#$$@##', '@'", "output": "'mnmnj krupa...##!@#!@#$$@##'", "id": "sample_199"}
{"code": "def f(text, value):\n    length = len(text)\n    index = 0\n    while length > 0:\n        value = text[index] + value\n        length -= 1\n        index += 1\n    return value", "input": "'jao mt', 'house'", "output": "'tm oajhouse'", "id": "sample_200"}
{"code": "def f(text):\n    chars = []\n    for c in text:\n        if c.isdigit():\n            chars.append(c)\n    return ''.join(chars[::-1])", "input": "'--4yrw 251-//4 6p'", "output": "'641524'", "id": "sample_201"}
{"code": "def f(array, list):\n    array.extend(list)\n    [e for e in array if e % 2 == 0]\n    return [e for e in array if e >= 10]", "input": "[2, 15], [15, 1]", "output": "[15, 15]", "id": "sample_202"}
{"code": "def f(d):\n    d.clear()\n    return d", "input": "{'a': 3, 'b': -1, 'c': 'Dum'}", "output": "{}", "id": "sample_203"}
{"code": "def f(name):\n    return [name[0], name[1][::-1][0]]", "input": "\"master. \"", "output": "['m', 'a']", "id": "sample_204"}
{"code": "def f(a):\n    for _ in range(10):\n        for j in range(len(a)):\n            if a[j] != '#':\n                a = a[j:]\n                break\n        else:\n            a = \"\"\n            break\n    while a[-1] == '#':\n        a = a[:-1]\n    return a", "input": "\"##fiu##nk#he###wumun##\"", "output": "'fiu##nk#he###wumun'", "id": "sample_205"}
{"code": "def f(a):\n    return ' '.join(a.split())", "input": "' h e l l o   w o r l d! '", "output": "'h e l l o w o r l d!'", "id": "sample_206"}
{"code": "def f(commands):\n    d = {}\n    for c in commands:\n        d.update(c)\n    return d", "input": "[{\"brown\": 2}, {\"blue\": 5}, {\"bright\": 4}]", "output": "{'brown': 2, 'blue': 5, 'bright': 4}", "id": "sample_207"}
{"code": "def f(items):\n    result = []\n    for item in items:\n        for d in item:\n            if not d.isdigit():\n                result.append(d)\n    return result", "input": "['123', 'cat', 'd dee']", "output": "['c', 'a', 't', 'd', ' ', 'd', 'e', 'e']", "id": "sample_208"}
{"code": "def f(prefix, s):\n    return str.removeprefix(prefix, s)", "input": "'hymi', 'hymifulhxhzpnyihyf'", "output": "'hymi'", "id": "sample_209"}
{"code": "def f(n, m, num):\n    x_list = list(range(n, m+1))\n    j = 0\n    while True:\n        j = (j + num) % len(x_list)\n        if x_list[j] % 2 == 0:\n            return x_list[j]", "input": "46, 48, 21", "output": "46", "id": "sample_210"}
{"code": "def f(s):\n    count = 0\n    for c in s:\n        if s.rindex(c) != s.index(c):\n            count+=1\n    return count", "input": "\"abca dea ead\"", "output": "10", "id": "sample_211"}
{"code": "def f(nums):\n    for _ in range(len(nums) - 1):\n        nums.reverse()\n    return nums", "input": "[1, -9, 7, 2, 6, -3, 3]", "output": "[1, -9, 7, 2, 6, -3, 3]", "id": "sample_212"}
{"code": "def f(s):\n    return s.replace('(', '[').replace(')', ']')", "input": "\"(ac)\"", "output": "'[ac]'", "id": "sample_213"}
{"code": "def f(sample):\n    i = -1\n    while sample.find('/', i+1) != -1:\n        i = sample.find('/', i+1)\n    return sample.rindex('/', 0, i)", "input": "'present/here/car%2Fwe'", "output": "7", "id": "sample_214"}
{"code": "def f(text):\n    new_text = text\n    while len(text) > 1 and text[0] == text[-1]:\n        new_text = text = text[1:-1]\n    return new_text", "input": "')'", "output": "')'", "id": "sample_215"}
{"code": "def f(letters):\n    count = 0\n    for l in letters:\n        if l.isdigit():\n            count += 1\n    return count", "input": "\"dp ef1 gh2\"", "output": "2", "id": "sample_216"}
{"code": "def f(string):\n    if string.isalnum():\n        return \"ascii encoded is allowed for this language\"\n    return \"more than ASCII\"", "input": "'Str zahrnuje anglo-ameri\u00e6ske vasi piscina and kuca!'", "output": "'more than ASCII'", "id": "sample_217"}
{"code": "def f(string, sep):\n    cnt = string.count(sep)\n    return((string+sep) * cnt)[::-1]", "input": "'caabcfcabfc', 'ab'", "output": "'bacfbacfcbaacbacfbacfcbaac'", "id": "sample_218"}
{"code": "def f(s1, s2):\n    for k in range(0, len(s2)+len(s1)):\n        s1 += s1[0]\n        if s1.find(s2) >= 0:\n            return True\n    return False", "input": "\"Hello\", \")\"", "output": "False", "id": "sample_219"}
{"code": "def f(text, m, n):\n    text = \"{}{}{}\".format(text, text[:m], text[n:])\n    result = \"\"\n    for i in range(n, len(text)-m):\n        result = text[i] + result\n    return result", "input": "\"abcdefgabc\", 1, 2", "output": "'bagfedcacbagfedc'", "id": "sample_220"}
{"code": "def f(text, delim):\n    first, second = text.split(delim)\n    return second + delim + first", "input": "'bpxa24fc5.', '.'", "output": "'.bpxa24fc5'", "id": "sample_221"}
{"code": "def f(mess, char):\n    while mess.find(char, mess.rindex(char) + 1) != -1:\n        mess = mess[:mess.rindex(char) + 1] + mess[mess.rindex(char) + 2:]\n    return mess", "input": "'0aabbaa0b', 'a'", "output": "'0aabbaa0b'", "id": "sample_222"}
{"code": "def f(array, target):\n    count, i = 0, 1\n    for j in range(1, len(array)):\n        if ((array[j] > array[j-1]) and (array[j] <= target)): count += i\n        elif array[j] <= array[j-1]: i = 1\n        else: i += 1\n    return count", "input": "[1, 2, -1, 4], 2", "output": "1", "id": "sample_223"}
{"code": "def f(array, value):\n    array.reverse()\n    array.pop()\n    odd = []\n    while len(array) > 0:\n        tmp = {}\n        tmp[array.pop()] = value\n        odd.append(tmp)\n    result = {}\n    while len(odd) > 0:\n        result.update(odd.pop())\n    return result", "input": "['23'], 123", "output": "{}", "id": "sample_224"}
{"code": "def f(text):\n    if text.islower():\n        return True\n    return False", "input": "\"54882\"", "output": "False", "id": "sample_225"}
{"code": "def f(nums):\n    for i in range(len(nums)):\n        if nums[i] % 3 == 0:\n            nums.append(nums[i])\n    return nums", "input": "[1, 3]", "output": "[1, 3, 3]", "id": "sample_226"}
{"code": "def f(text):\n    text = text.lower()\n    head, tail = text[0], text[1:]\n    return head.upper() + tail", "input": "'Manolo'", "output": "'Manolo'", "id": "sample_227"}
{"code": "def f(text, splitter):\n    return splitter.join(text.lower().split())", "input": "'LlTHH sAfLAPkPhtsWP', '#'", "output": "'llthh#saflapkphtswp'", "id": "sample_228"}
{"code": "def f(dic, value):\n    result = []\n    for e in dic:\n        result.append(e[0])\n        if e[1] == value:\n            result.reverse()\n        else:\n            result.append(e[1])\n    return result", "input": "{'9m':2, 'mA':1, '10K':2, 'Lk':2}, 1", "output": "['9', 'm', 'm', 'A', '1', '0', 'L', 'k']", "id": "sample_229"}
{"code": "def f(text):\n    result = ''\n    i = len(text)-1\n    while i >= 0:\n        c = text[i]\n        if c.isalpha():\n            result += c\n        i -= 1\n    return result", "input": "'102x0zoq'", "output": "'qozx'", "id": "sample_230"}
{"code": "def f(years):\n    a10 = sum(1 for x in years if x <= 1900)\n    a90 = sum(1 for x in years if x > 1910)\n    if a10 > 3:\n        return 3\n    elif a90 > 3:\n        return 1\n    else:\n        return 2", "input": "[1872, 1995, 1945]", "output": "2", "id": "sample_231"}
{"code": "def f(text, changes):\n    result = ''\n    count = 0\n    changes = list(changes)\n    for char in text:\n        result += char if char in 'e' else changes[count % len(changes)]\n        count += (1 if char not in 'e' else 0)\n    return result", "input": "'fssnvd', 'yes'", "output": "'yesyes'", "id": "sample_232"}
{"code": "def f(xs):\n    for idx in reversed(range(-len(xs)-1, -1)):\n        xs.insert(idx, xs.pop(0))\n    return xs", "input": "[1, 2, 3]", "output": "[1, 2, 3]", "id": "sample_233"}
{"code": "def f(text, char):\n    position = len(text)\n    if char in text:\n        position = text.index(char)\n        if position > 1:\n            position = (position + 1) % len(text)\n    return position", "input": "'wduhzxlfk', 'w'", "output": "0", "id": "sample_234"}
{"code": "def f(array, arr):\n    result = []\n    for s in arr:\n        result += list(filter(lambda l: l != '', s.split(arr[array.index(s)])))\n    return result", "input": "[], []", "output": "[]", "id": "sample_235"}
{"code": "def f(array):\n    if len(array) == 1:\n        return ''.join(array)\n    result = list(array)\n    i = 0\n    while i < len(array)-1:\n        for j in range(2):\n            result[i*2] = array[i]\n            i += 1\n    return ''.join(result)", "input": "['ac8', 'qk6', '9wg']", "output": "'ac8qk6qk6'", "id": "sample_236"}
{"code": "def f(text, char):\n    if char in text:\n        suff, char, pref = text.partition(char)\n        pref = suff[:-len(char)] + suff[len(char):] + char + pref\n        return suff + char + pref\n    return text", "input": "'uzlwaqiaj', 'u'", "output": "'uuzlwaqiaj'", "id": "sample_237"}
{"code": "def f(ls, n):\n    answer = 0\n    for i in ls:\n        if i[0] == n:\n            answer = i\n    return answer", "input": "[[1, 9, 4], [83, 0, 5], [9, 6, 100]], 1", "output": "[1, 9, 4]", "id": "sample_238"}
{"code": "def f(text, froms):\n    text = text.lstrip(froms)\n    text = text.rstrip(froms)\n    return text", "input": "'0 t 1cos ', 'st ' + '0\\t\\n  '", "output": "'1co'", "id": "sample_239"}
{"code": "def f(float_number):\n    number = str(float_number)\n    dot = number.find('.')\n    if dot != -1:\n        return number[:dot] + '.' + number[dot+1:].ljust(2, '0')\n    return number + '.00'", "input": "3.121", "output": "'3.121'", "id": "sample_240"}
{"code": "def f(postcode):\n    return postcode[postcode.index('C'):]", "input": "'ED20 CW'", "output": "'CW'", "id": "sample_241"}
{"code": "def f(book):\n    a = book.rsplit(':', 1)\n    if a[0].split(' ')[-1] == a[1].split(' ')[0]:\n        return f(' '.join(a[0].split(' ')[:-1]) + ' ' + a[1])\n    return book", "input": "\"udhv zcvi nhtnfyd :erwuyawa pun\"", "output": "'udhv zcvi nhtnfyd :erwuyawa pun'", "id": "sample_242"}
{"code": "def f(text, char):\n    return char.islower() and text.islower()", "input": "'abc', 'e'", "output": "True", "id": "sample_243"}
{"code": "def f(text, symbols):\n    count = 0\n    if symbols:\n        for i in symbols:\n            count += 1\n        text = text * count\n    return text.rjust(len(text) + count*2)[:-2]", "input": "'', 'BC1ty'", "output": "'        '", "id": "sample_244"}
{"code": "def f(alphabet, s):\n    a = [x for x in alphabet if x.upper() in s]\n    if s.upper() == s:\n        a.append('all_uppercased')\n    return a", "input": "'abcdefghijklmnopqrstuvwxyz', \"uppercased # % ^ @ ! vz.\"", "output": "[]", "id": "sample_245"}
{"code": "def f(haystack, needle):\n    for i in range(haystack.find(needle), -1, -1):\n        if haystack[i:] == needle:\n            return i\n    return -1", "input": "\"345gerghjehg\", \"345\"", "output": "-1", "id": "sample_246"}
{"code": "def f(s):\n    if s.isalpha():\n        return \"yes\"\n    if s == \"\":\n        return \"str is empty\"\n    return \"no\"", "input": "'Boolean'", "output": "'yes'", "id": "sample_247"}
{"code": "def f(a, b):\n    a.sort()\n    b.sort(reverse=True)\n    return a + b", "input": "[666], []", "output": "[666]", "id": "sample_248"}
{"code": "def f(s):\n    count = {}\n    for i in s:\n        if i.islower():\n            count[i.lower()] = s.count(i.lower()) + count.get(i.lower(), 0)\n        else:\n            count[i.lower()] = s.count(i.upper()) + count.get(i.lower(), 0)\n    return count", "input": "\"FSA\"", "output": "{'f': 1, 's': 1, 'a': 1}", "id": "sample_249"}
{"code": "def f(text):\n    count = len(text)\n    for i in range(-count+1, 0):\n        text = text + text[i]\n    return text", "input": "'wlace A'", "output": "'wlace Alc l  '", "id": "sample_250"}
{"code": "def f(messages):\n    phone_code = \"+353\"\n    result = []\n    for message in messages:\n        message.extend(phone_code)\n        result.append(\";\".join(message))\n    return \". \".join(result)", "input": "[['Marie','Nelson','Oscar']]", "output": "'Marie;Nelson;Oscar;+;3;5;3'", "id": "sample_251"}
{"code": "def f(text, char):\n    if char in text:\n        if not text.startswith(char):\n            text = text.replace(char,'')\n    return text", "input": "'\\\\foo', '\\\\'", "output": "'\\\\foo'", "id": "sample_252"}
{"code": "def f(text, pref):\n    length = len(pref)\n    if pref == text[:length]:\n        return text[length:]\n    return text", "input": "'kumwwfv', 'k'", "output": "'umwwfv'", "id": "sample_253"}
{"code": "def f(text, repl):\n    trans = str.maketrans(text.lower(), repl.lower())\n    return text.translate(trans)", "input": "'upper case', 'lower case'", "output": "'lwwer case'", "id": "sample_254"}
{"code": "def f(text, fill, size):\n    if size < 0:\n        size = -size\n    if len(text) > size:\n        return text[len(text) - size:]\n    return text.rjust(size, fill)", "input": "'no asw', 'j', 1", "output": "'w'", "id": "sample_255"}
{"code": "def f(text, sub):\n    a = 0\n    b = len(text) - 1\n\n    while a <= b:\n        c = (a + b) // 2\n        if text.rfind(sub) >= c:\n            a = c + 1\n        else:\n            b = c - 1\n\n    return a", "input": "'dorfunctions', '2'", "output": "0", "id": "sample_256"}
{"code": "def f(text):\n    ls = []\n    for x in text:\n        ls.append(x.splitlines())\n    return ls", "input": "['Hello World\\n\"I am String\"']", "output": "[['Hello World', '\"I am String\"']]", "id": "sample_257"}
{"code": "thigh_o_one = [1, 2, 7, 8, 9]\nthigh_o_two = [1, 2, 7, 9]\ndef f(L, m, start, step):\n    L.insert(start, m)\n    for x in range(start-1, 0, -step):\n        start -= 1\n        L.insert(start, L.pop(L.index(m)-1))\n    return L", "input": "thigh_o_two[:], 3, 3, 2", "output": "[1, 2, 7, 3, 9]", "id": "sample_258"}
{"code": "def f(text):\n    new_text = []\n    for character in text:\n        if character.isupper():\n            new_text.insert(len(new_text) // 2, character)\n    if len(new_text) == 0:\n        new_text = ['-']\n    return ''.join(new_text)", "input": "'String matching is a big part of RexEx library.'", "output": "'RES'", "id": "sample_259"}
{"code": "def f(nums, start, k):\n    nums[start:start+k] = nums[start:start + k][::-1]\n    return nums", "input": "[1, 2, 3, 4, 5, 6], 4, 2", "output": "[1, 2, 3, 4, 6, 5]", "id": "sample_260"}
{"code": "def f(nums, target):\n    lows, higgs = [], []\n    for i in nums:\n        if i < target:\n            lows.append(i)\n        else:\n            higgs.append(i)\n    lows.clear()\n    return lows, higgs", "input": "[12, 516, 5, 2, 3, 214, 51], 5", "output": "([], [12, 516, 5, 214, 51])", "id": "sample_261"}
{"code": "def f(nums):\n    count = len(nums)\n    score = {0: \"F\", 1: \"E\", 2: \"D\", 3: \"C\", 4: \"B\", 5: \"A\", 6: \"\"}\n    result = []\n    for i in range(count):\n        result.append(score.get(nums[i]))\n    return ''.join(result)", "input": "[4, 5]", "output": "'BA'", "id": "sample_262"}
{"code": "def f(base, delta):\n    for j in range(len(delta)):\n        for i in range(len(base)):\n            if base[i] == delta[j][0]:\n                assert delta[j][1] != base[i]\n                base[i] = delta[j][1]\n    return base", "input": "[\"gloss\", \"banana\", \"barn\", \"lawn\"], []", "output": "['gloss', 'banana', 'barn', 'lawn']", "id": "sample_263"}
{"code": "def f(test_str):\n    s = test_str.replace('a', 'A')\n    return s.replace('e', 'A')", "input": "\"papera\"", "output": "'pApArA'", "id": "sample_264"}
{"code": "def f(d, k):\n    new_d = {}\n    for key, val in d.items():\n        if key < k:\n            new_d[key] = val\n    return new_d", "input": "{1: 2, 2: 4, 3: 3}, 3", "output": "{1: 2, 2: 4}", "id": "sample_265"}
{"code": "def f(nums):\n    for i in range(len(nums)-1, -1, -1):\n        if nums[i] % 2 == 1:\n            nums.insert(i+1, nums[i])\n    return nums", "input": "[2, 3, 4, 6, -2]", "output": "[2, 3, 3, 4, 6, -2]", "id": "sample_266"}
{"code": "def f(text, space):\n    if space < 0:\n        return text\n    return text.ljust(len(text) // 2 + space)", "input": "'sowpf', -7", "output": "'sowpf'", "id": "sample_267"}
{"code": "def f(s, separator):\n    for i in range(len(s)):\n        if s[i] == separator:\n            new_s = list(s)\n            new_s[i] = '/'\n            return ' '.join(new_s)", "input": "'h grateful k', ' '", "output": "'h / g r a t e f u l   k'", "id": "sample_268"}
{"code": "def f(array):\n    zero_len = (len(array) - 1) % 3\n    for i in range(zero_len):\n        array[i] = '0'\n    for i in range(zero_len + 1, len(array), 3):\n        array[i - 1:i + 2] = ['0', '0', '0']\n    return array", "input": "[9, 2]", "output": "['0', 2]", "id": "sample_269"}
{"code": "def f(dic):\n    d = {}\n    for key in dic:\n        d[key] = dic.popitem(last = False)[1]\n    return d", "input": "{}", "output": "{}", "id": "sample_270"}
{"code": "def f(text, c):\n    ls = list(text)\n    if c not in text:\n        raise ValueError('Text has no {c}')\n    ls.pop(text.rindex(c))\n    return ''.join(ls)", "input": "'uufhl', 'l'", "output": "'uufh'", "id": "sample_271"}
{"code": "def f(base_list, nums):\n    base_list.extend(nums)\n    res = base_list.copy()\n    for i in range(-len(nums), 0):\n        res.append(res[i])\n    return res", "input": "[9, 7, 5, 3, 1], [2, 4, 6, 8, 0]", "output": "[9, 7, 5, 3, 1, 2, 4, 6, 8, 0, 2, 6, 0, 6, 6]", "id": "sample_272"}
{"code": "def f(name):\n    new_name =''\n    name = name[::-1]\n    for i in range(len(name)):\n        n = name[i]\n        if n !='.' and  new_name.count('.')<2:\n            new_name=n+new_name\n        else:\n            break\n    return new_name", "input": "'.NET'", "output": "'NET'", "id": "sample_273"}
{"code": "def f(nums, target):\n    count = 0\n    for n1 in nums:\n        for n2 in nums:\n            count += (n1+n2==target)\n    return count", "input": "[1, 2, 3], 4", "output": "3", "id": "sample_274"}
{"code": "def f(dic):\n    dic2 = dict(zip(dic.values(), dic.keys()))\n    return dic2", "input": "{-1: \"a\", 0: \"b\", 1: \"c\"}", "output": "{'a': -1, 'b': 0, 'c': 1}", "id": "sample_275"}
{"code": "def f(a):\n    if len(a) >= 2 and a[0] > 0 and a[1] > 0:\n        a.reverse()\n        return a\n    a.append(0)\n    return a", "input": "[]", "output": "[0]", "id": "sample_276"}
{"code": "def f(lst, mode):\n    result = [el for el in lst]\n    if mode:\n        result.reverse()\n    return result", "input": "[1, 2, 3, 4], 1", "output": "[4, 3, 2, 1]", "id": "sample_277"}
{"code": "def f(array1, array2):\n    result = dict.fromkeys(array1)\n    for key in result:\n        result[key] = [el for el in array2 if key * 2 > el]\n    return result", "input": "[0, 132], [5, 991, 32, 997]", "output": "{0: [], 132: [5, 32]}", "id": "sample_278"}
{"code": "def f(text):\n    ans = ''\n    while text != '':\n        x, sep, text = text.partition('(')\n        ans = x + sep.replace('(', '|') + ans\n        ans = ans + text[0] + ans\n        text = text[1:]\n    return ans", "input": "\"\"", "output": "''", "id": "sample_279"}
{"code": "field = 0\n\ndef f(text):\n    global g, field\n    field = text.replace(' ', '')\n    g = text.replace('0', ' ')\n    text = text.replace('1', 'i')\n\n    return text", "input": "'00000000 00000000 01101100 01100101 01101110'", "output": "'00000000 00000000 0ii0ii00 0ii00i0i 0ii0iii0'", "id": "sample_280"}
{"code": "def f(c, index, value):\n    c[index] = value\n    if value >= 3:\n        c.update({'message' : 'xcrWt'})\n    else: \n        del c['message']\n    return c", "input": "{1: 2, 3: 4, 5: 6, 'message': 'qrTHo'}, 8, 2", "output": "{1: 2, 3: 4, 5: 6, 8: 2}", "id": "sample_281"}
{"code": "def f(s1, s2):\n    position = 1\n    count = 0\n    while position > 0:\n        position = s1.find(s2, position)\n        count += 1\n        position += 1\n    return count", "input": "'xinyyexyxx', 'xx'", "output": "2", "id": "sample_282"}
{"code": "def f(dictionary, key):\n    del dictionary[key]\n    if min(dictionary) == key:\n        key = list(dictionary)[0]\n    return key", "input": "{'Iron Man': 4, 'Captain America': 3, 'Black Panther': 0,'Thor': 1, 'Ant-Man': 6}, 'Iron Man'", "output": "'Iron Man'", "id": "sample_283"}
{"code": "def f(text, prefix):\n    idx = 0\n    for letter in prefix:\n        if text[idx] != letter:\n            return None\n        idx += 1\n    return text[idx:]", "input": "'bestest', 'bestest'", "output": "''", "id": "sample_284"}
{"code": "def f(text, ch):\n    \"\"\"Counting vowels in Pirates' Curse\"\"\"\n    return text.count(ch)", "input": "\"This be Pirate's Speak for 'help'!\", ' '", "output": "5", "id": "sample_285"}
{"code": "def f(array, x, i):\n    if i < -len(array) or i > len(array) - 1:\n        return 'no'\n    temp = array[i]\n    array[i] = x\n    return array", "input": "[1,2,3,4,5,6,7,8,9,10], 11, 4", "output": "[1, 2, 3, 4, 11, 6, 7, 8, 9, 10]", "id": "sample_286"}
{"code": "def f(name):\n    if name.islower():\n        name = name.upper()\n    else:\n        name = name.lower()\n    return name", "input": "'Pinneaple'", "output": "'pinneaple'", "id": "sample_287"}
{"code": "def f(d):\n    sorted_pairs = sorted(list(d.items()), key=lambda x: len(str(str(x[0])+str(x[1]))))\n    return [(k, v) for k, v in sorted_pairs if k < v]\n    return ret", "input": "{55: 4, 4: 555, 1: 3, 99: 21, 499: 4, 71: 7, 12: 6}", "output": "[(1, 3), (4, 555)]", "id": "sample_288"}
{"code": "def f(code):\n    return \"{}: {}\".format(code, code.encode())", "input": "'148'", "output": "\"148: b'148'\"", "id": "sample_289"}
{"code": "def f(text, prefix):\n    if text.startswith(prefix):\n        return text.removeprefix(prefix)\n    if prefix in text:\n        return text.replace(prefix, '').strip()\n    return text.upper()", "input": "'abixaaaily', 'al'", "output": "'ABIXAAAILY'", "id": "sample_290"}
{"code": "def f(dictionary, arr):\n    dictionary.update({arr[0]: [arr[1]]})\n    if len(dictionary[arr[0]]) == arr[1]:\n        dictionary[arr[0]] = arr[0]\n    return dictionary", "input": "{}, ['a', 2]", "output": "{'a': [2]}", "id": "sample_291"}
{"code": "def f(text):\n    new_text = [c if c.isdigit() else '*' for c in text]\n    return ''.join(new_text)", "input": "'5f83u23saa'", "output": "'5*83*23***'", "id": "sample_292"}
{"code": "def f(text):\n    s = text.lower()\n    for i in range(len(s)):\n        if s[i] == 'x':\n            return 'no'\n    return text.isupper()", "input": "'dEXE'", "output": "'no'", "id": "sample_293"}
{"code": "def f(n, m, text):\n    if text.strip() == '':\n        return text\n    head, mid, tail = text[0], text[1:-1], text[-1]\n    joined = head.replace(n, m) + mid.replace(n, m) + tail.replace(n, m)\n    return joined", "input": "\"x\", \"$\", \"2xz&5H3*1a@#a*1hris\"", "output": "'2$z&5H3*1a@#a*1hris'", "id": "sample_294"}
{"code": "def f(fruits):\n    if fruits[-1] == fruits[0]:\n        return 'no'\n    else:\n        fruits.pop(0)\n        fruits.pop()\n        fruits.pop(0)\n        fruits.pop()\n        return fruits", "input": "['apple', 'apple', 'pear', 'banana', 'pear', 'orange', 'orange']", "output": "['pear', 'banana', 'pear']", "id": "sample_295"}
{"code": "def f(url):\n    return url.removeprefix('http://www.')", "input": "\"https://www.www.ekapusta.com/image/url\"", "output": "'https://www.www.ekapusta.com/image/url'", "id": "sample_296"}
{"code": "def f(num):\n    if 0 < num < 1000 and num != 6174:\n        return 'Half Life'\n    return 'Not found'", "input": "6173", "output": "'Not found'", "id": "sample_297"}
{"code": "def f(text):\n    new_text = list(text)\n    for i in range(len(new_text)):\n        character = new_text[i]\n        new_character = character.swapcase()\n        new_text[i] = new_character\n    return ''.join(new_text)", "input": "'dst vavf n dmv dfvm gamcu dgcvb.'", "output": "'DST VAVF N DMV DFVM GAMCU DGCVB.'", "id": "sample_298"}
{"code": "def f(text, char):\n    if not text.endswith(char):\n        return f(char + text, char)\n    return text", "input": "'staovk', 'k'", "output": "'staovk'", "id": "sample_299"}
{"code": "def f(nums):\n    count = 1\n    for i in range(count, len(nums) - 1, 2):\n        nums[i] = max(nums[i], nums[count-1])\n        count += 1\n    return nums", "input": "[1, 2, 3]", "output": "[1, 2, 3]", "id": "sample_300"}
{"code": "def f(nums):\n    count = len(nums)\n    for i in range(-count+1, 0):\n        nums.extend([nums[i], nums[i]])\n    return nums", "input": "[0, 6, 2, -1, -2]", "output": "[0, 6, 2, -1, -2, 6, 6, -2, -2, -2, -2, -2, -2]", "id": "sample_301"}
{"code": "def f(string):\n    return string.replace('needles', 'haystacks')", "input": "'wdeejjjzsjsjjsxjjneddaddddddefsfd'", "output": "'wdeejjjzsjsjjsxjjneddaddddddefsfd'", "id": "sample_302"}
{"code": "def f(text):\n    i = (len(text) + 1) // 2\n    result = list(text)\n    while i < len(text):\n        t = result[i].lower()\n        if t == result[i]:\n            i += 1\n        else:\n            result[i] = t\n        i += 2\n    return ''.join(result)", "input": "'mJkLbn'", "output": "'mJklbn'", "id": "sample_303"}
{"code": "def f(d):\n    key1 = sorted(d.items(), key=lambda x: x[0], reverse=True)[0][0]\n    val1 = d.pop(key1)\n    key2 = sorted(d.items(), key=lambda x: x[0], reverse=True)[0][0]\n    val2 = d.pop(key2)\n    return dict({key1: val1, key2: val2})", "input": "{2: 3, 17: 3, 16: 6, 18: 6, 87: 7}", "output": "{87: 7, 18: 6}", "id": "sample_304"}
{"code": "def f(text, char):\n    length = len(text)\n    index = -1\n    for i in range(length):\n        if text[i] == char:\n            index = i\n    if index == -1:\n        index = length // 2\n    new_text = list(text)\n    new_text.pop(index)\n    return ''.join(new_text)", "input": "'o horseto', 'r'", "output": "'o hoseto'", "id": "sample_305"}
{"code": "def f(nums):\n    digits = []\n    for num in nums:\n        if (isinstance(num, str) and num.isnumeric()) or isinstance(num, int):\n            digits.append(num)\n    digits = list(map(int, digits))\n    return digits", "input": "[0, 6, '1', '2', 0]", "output": "[0, 6, 1, 2, 0]", "id": "sample_306"}
{"code": "def f(text):\n    rtext = list(text)\n    for i in range(1, len(rtext) - 1):\n        rtext.insert(i + 1, '|')\n    return ''.join(rtext)", "input": "'pxcznyf'", "output": "'px|||||cznyf'", "id": "sample_307"}
{"code": "def f(strings):\n    occurances = {}\n    for string in strings:\n        if string not in occurances:\n            occurances[string] = strings.count(string)\n    return occurances", "input": "[\"La\", \"Q\", \"9\", \"La\", \"La\"]", "output": "{'La': 3, 'Q': 1, '9': 1}", "id": "sample_308"}
{"code": "def f(text, suffix):\n    text += suffix\n    while text[-len(suffix):] == suffix:\n        text = text[:-1]\n    return text", "input": "'faqo osax f', 'f'", "output": "'faqo osax '", "id": "sample_309"}
{"code": "def f(strands):\n    subs = strands\n    for i, j in enumerate(subs):\n        for _ in range(len(j) // 2):\n            subs[i] = subs[i][-1:] + subs[i][1:-1] + subs[i][0]\n    return ''.join(subs)", "input": "['__', '1', '.', '0', 'r0', '__', 'a_j', '6', '__', '6']", "output": "'__1.00r__j_a6__6'", "id": "sample_310"}
{"code": "def f(text):\n    text = text.replace('#', '1').replace('$', '5')\n    return 'yes' if text.isnumeric() else 'no'", "input": "'A'", "output": "'no'", "id": "sample_311"}
{"code": "def f(str):\n    if str.isalnum():\n        return \"True\"\n    return \"False\"", "input": "'777'", "output": "'True'", "id": "sample_312"}
{"code": "def f(s, l):\n    return s.ljust(l, '=').rpartition('=')[0]", "input": "'urecord', 8", "output": "'urecord'", "id": "sample_313"}
{"code": "def f(text):\n    if ',' in text:\n        before, _, after = text.partition(',')\n        return after + ' ' + before\n    return ',' + text.partition(' ')[-1] + ' 0'", "input": "'244, 105, -90'", "output": "' 105, -90 244'", "id": "sample_314"}
{"code": "def f(challenge):\n    return challenge.casefold().replace('l', ',')", "input": "'czywZ'", "output": "'czywz'", "id": "sample_315"}
{"code": "def f(name):\n    return '| ' + ' '.join(name.split(' ')) + ' |'", "input": "'i am your father'", "output": "'| i am your father |'", "id": "sample_316"}
{"code": "def f(text, a, b):\n    text = text.replace(a, b)\n    return text.replace(b, a)", "input": "' vup a zwwo oihee amuwuuw! ', 'a', 'u'", "output": "' vap a zwwo oihee amawaaw! '", "id": "sample_317"}
{"code": "def f(value, char):\n    total = 0\n    for c in value:\n        if c == char or c == char.lower():\n            total += 1\n    return total", "input": "'234rtccde', 'e'", "output": "1", "id": "sample_318"}
{"code": "def f(needle, haystack):\n    count = 0\n    while needle in haystack:\n        haystack = haystack.replace(needle, '', 1)\n        count += 1\n    return count", "input": "'a', 'xxxaaxaaxx'", "output": "4", "id": "sample_319"}
{"code": "def f(text):\n    index = 1\n    while index < len(text):\n        if text[index] != text[index - 1]:\n            index += 1\n        else:\n            text1 = text[:index]\n            text2 = text[index:].swapcase()\n            return text1 + text2\n    return text.swapcase()", "input": "'USaR'", "output": "'usAr'", "id": "sample_320"}
{"code": "def f(update, starting):\n    d = starting.copy()\n    for k in update:\n        if k in d:\n            d[k] += update[k]\n        else:\n            d[k] = update[k]\n    return d", "input": "{}, {'desciduous': 2}", "output": "{'desciduous': 2}", "id": "sample_321"}
{"code": "def f(chemicals, num):\n    fish = chemicals[1:]\n    chemicals.reverse()\n    for i in range(num):\n        fish.append(chemicals.pop(1))\n    chemicals.reverse()\n    return chemicals", "input": "['lsi', 's', 't', 't', 'd'], 0", "output": "['lsi', 's', 't', 't', 'd']", "id": "sample_322"}
{"code": "def f(text):\n    return len(text.splitlines())", "input": "'ncdsdfdaaa0a1cdscsk*XFd'", "output": "1", "id": "sample_323"}
{"code": "def f(nums):\n    asc, desc = nums.copy(), []\n    asc.reverse()\n    desc = asc[:len(asc)//2]\n    return desc + asc + desc", "input": "[]", "output": "[]", "id": "sample_324"}
{"code": "def f(s):\n    l = list(s)\n    for i in range(len(l)):\n        l[i] = l[i].lower()\n        if not l[i].isdigit():\n            return False\n    return True", "input": "\"\"", "output": "True", "id": "sample_325"}
{"code": "def f(text):\n    number = 0\n    for t in text:\n        if t.isnumeric():\n            number += 1\n    return number", "input": "'Thisisastring'", "output": "0", "id": "sample_326"}
{"code": "def f(lst):\n    new = list()\n    i = len(lst)-1\n    for _ in range(len(lst)):\n        if i%2 == 0:\n            new.append(-lst[i])\n        else:\n            new.append(lst[i])\n        i -= 1\n    return new", "input": "[1, 7, -1, -3]", "output": "[-3, 1, 7, -1]", "id": "sample_327"}
{"code": "def f(array, L):\n    if L <= 0:\n        return array\n    if len(array) < L:\n        array.extend(f(array, L - len(array)))\n    return array", "input": "[1, 2, 3], 4", "output": "[1, 2, 3, 1, 2, 3]", "id": "sample_328"}
{"code": "def f(text):\n    for i in range(len(text)):\n        if text[i] == text[i].upper() and text[i-1].islower():\n            return True\n    return False", "input": "'jh54kkk6'", "output": "True", "id": "sample_329"}
{"code": "def f(text):\n    ans = []\n    for char in text:\n        if char.isdigit():\n            ans.append(char)\n        else:\n            ans.append(' ')\n    return ''.join(ans)", "input": "'m4n2o'", "output": "' 4 2 '", "id": "sample_330"}
{"code": "def f(strand, zmnc):\n    poz = strand.find(zmnc)\n    while poz != -1:\n        strand = strand[poz + 1:]\n        poz = strand.find(zmnc)\n    return strand.rfind(zmnc)", "input": "'', 'abc'", "output": "-1", "id": "sample_331"}
{"code": "def f(nums):\n    count = len(nums)\n    if count == 0:\n        nums = [0] * int(nums.pop())\n    elif count % 2 == 0:\n        nums.clear()\n    else:\n        del nums[:count//2:]\n    return nums", "input": "[-6, -2, 1, -3, 0, 1]", "output": "[]", "id": "sample_332"}
{"code": "def f(places, lazy):\n    places.sort()\n    for lazy in lazy:\n        places.remove(lazy)\n    if len(places) == 1:\n        return 1\n    for i, place in enumerate(places):\n        if places.count(place+1) == 0:\n            return i+1\n    return i+1", "input": "[375, 564, 857, 90, 728, 92], [728]", "output": "1", "id": "sample_333"}
{"code": "def f(a, b):\n    return a.join(b)", "input": "'00', ['nU', ' 9 rCSAz', 'w', ' lpA5BO', 'sizL', 'i7rlVr']", "output": "'nU00 9 rCSAz00w00 lpA5BO00sizL00i7rlVr'", "id": "sample_334"}
{"code": "def f(text, to_remove):\n    new_text = list(text)\n    if to_remove in new_text:\n        index = new_text.index(to_remove)\n        new_text.remove(to_remove)\n        new_text.insert(index, '?')\n        new_text.remove('?')\n    return ''.join(new_text)", "input": "'sjbrlfqmw', 'l'", "output": "'sjbrfqmw'", "id": "sample_335"}
{"code": "def f(s, sep):\n    s += sep\n    return s.rpartition(sep)[0]", "input": "'234dsfssdfs333324314', 's'", "output": "'234dsfssdfs333324314'", "id": "sample_336"}
{"code": "def f(txt):\n    d = []\n    for c in txt:\n        if c.isdigit():\n            continue\n        if c.islower():\n            d.append(c.upper())\n        elif c.isupper():\n            d.append(c.lower())\n    return ''.join(d)", "input": "\"5ll6\"", "output": "'LL'", "id": "sample_337"}
{"code": "def f(my_dict):\n    result = {v: k for k, v in my_dict.items()}\n    return result", "input": "{'a': 1, 'b': 2, 'c': 3, 'd': 2}", "output": "{1: 'a', 2: 'd', 3: 'c'}", "id": "sample_338"}
{"code": "def f(array, elem):\n    elem = str(elem)\n    d = 0\n    for i in array:\n        if str(i) == elem:\n            d += 1\n    return d", "input": "[-1, 2, 1, -8, -8, 2], 2", "output": "2", "id": "sample_339"}
{"code": "def f(text):\n    uppercase_index = text.find('A')\n    if uppercase_index >= 0:\n        return text[:uppercase_index] + text[text.find('a') + 1 :]\n    else:\n        return ''.join(sorted(text))", "input": "'E jIkx HtDpV G'", "output": "'   DEGHIVjkptx'", "id": "sample_340"}
{"code": "def f(cart):\n    while len(cart) > 5:\n        cart.popitem()\n    return cart", "input": "{}", "output": "{}", "id": "sample_341"}
{"code": "def f(text):\n    return text.count('-') == len(text)", "input": "\"---123-4\"", "output": "False", "id": "sample_342"}
{"code": "def f(array, elem):\n    array.extend(elem)\n    return array", "input": "[[1, 2, 3], [1, 2], 1], [[1, 2, 3], 3, [2, 1]]", "output": "[[1, 2, 3], [1, 2], 1, [1, 2, 3], 3, [2, 1]]", "id": "sample_343"}
{"code": "def f(list, operation):\n    new_list = list[:]\n    new_list.sort()\n    operation(new_list)\n    return list", "input": "[6, 4, 2, 8, 15], (lambda x: x.reverse())", "output": "[6, 4, 2, 8, 15]", "id": "sample_344"}
{"code": "def f(a, b):\n    if a < b:\n        return (b, a)\n    return (a, b)", "input": "'ml', 'mv'", "output": "('mv', 'ml')", "id": "sample_345"}
{"code": "def f(filename):\n    suffix = filename.split('.')[-1]\n    f2 = filename + suffix[::-1]\n    return f2.endswith(suffix)", "input": "'docs.doc'", "output": "False", "id": "sample_346"}
{"code": "def f(text):\n    ls = list(text)\n    length = len(ls)\n    for i in range(length):\n        ls.insert(i, ls[i])\n    return ''.join(ls).ljust(length * 2)", "input": "'hzcw'", "output": "'hhhhhzcw'", "id": "sample_347"}
{"code": "def f(dictionary):\n    return dictionary.copy()", "input": "{563: 555, 133: None}", "output": "{563: 555, 133: None}", "id": "sample_348"}
{"code": "def f(dictionary):\n    dictionary[1049] = 55\n    key, value = dictionary.popitem()\n    dictionary[key] = value\n    return dictionary", "input": "{'noeohqhk': 623}", "output": "{'noeohqhk': 623, 1049: 55}", "id": "sample_349"}
{"code": "def f(d):\n    size = len(d)\n    v = [0] * size\n    if size == 0:\n        return v\n    for i, e in enumerate(d.values()):\n        v[i] = e\n    return v", "input": "{'a': 1, 'b': 2, 'c': 3}", "output": "[1, 2, 3]", "id": "sample_350"}
{"code": "def f(text):    \n    try:\n        while 'nnet lloP' in text:\n            text = text.replace('nnet lloP', 'nnet loLp')\n    finally:\n        return text", "input": "'a_A_b_B3 '", "output": "'a_A_b_B3 '", "id": "sample_351"}
{"code": "def f(nums):\n    return nums[len(nums)//2]", "input": "[-1, -3, -5, -7, 0]", "output": "-5", "id": "sample_352"}
{"code": "def f(x):\n    if x == []:\n        return -1\n    else:\n        cache = {}\n        for item in x:\n            if item in cache:\n                cache[item] += 1\n            else:\n                cache[item] = 1\n        return max(cache.values())", "input": "[1, 0, 2, 2, 0, 0, 0, 1]", "output": "4", "id": "sample_353"}
{"code": "def f(description, values):\n    if values[1] is None:\n        values = values[0:1]\n    else:\n        values = values[1:]\n    return description.format(*values)", "input": "'{0}, {0}!!!', ['R', None]", "output": "'R, R!!!'", "id": "sample_354"}
{"code": "def f(text, prefix):\n    return text[len(prefix):]", "input": "'123x John z', 'z'", "output": "'23x John z'", "id": "sample_355"}
{"code": "def f(array, num):\n    reverse = False\n    if num < 0:\n        reverse = True\n        num *= -1\n    array = array[::-1] * num\n    l = len(array)\n    \n    if reverse:\n        array = array[::-1]\n    return array", "input": "[1, 2], 1", "output": "[2, 1]", "id": "sample_356"}
{"code": "def f(s):\n    r = []\n    for i in range(len(s) - 1, 0 - 1, -1):\n        r += s[i]\n    return ''.join(r)", "input": "'crew'", "output": "'werc'", "id": "sample_357"}
{"code": "def f(text, value):\n    indexes = []\n    for i in range(len(text)):\n        if text[i] == value and (i == 0 or text[i-1] != value):\n            indexes.append(i) \n    if len(indexes) % 2 == 1:\n        return text\n    return text[indexes[0]+1:indexes[-1]]", "input": "'btrburger', 'b'", "output": "'tr'", "id": "sample_358"}
{"code": "def f(lines):\n    for i in range(len(lines)):\n        lines[i] = lines[i].center(len(lines[-1]))\n    return lines", "input": "['dZwbSR', 'wijHeq', 'qluVok', 'dxjxbF']", "output": "['dZwbSR', 'wijHeq', 'qluVok', 'dxjxbF']", "id": "sample_359"}
{"code": "def f(text, n):\n    if len(text) <= 2:\n        return text\n    leading_chars = text[0] * (n - len(text) + 1)\n    return leading_chars + text[1:-1] + text[-1]", "input": "'g', 15", "output": "'g'", "id": "sample_360"}
{"code": "def f(text):\n    return text.split(':')[0].count('#')", "input": "\"#! : #!\"", "output": "1", "id": "sample_361"}
{"code": "def f(text):\n    for i in range(len(text)-1):\n        if text[i:].islower():\n            return text[i + 1:]\n    return ''", "input": "'wrazugizoernmgzu'", "output": "'razugizoernmgzu'", "id": "sample_362"}
{"code": "def f(nums):\n    nums.sort()\n    n = len(nums)\n    new_nums = [nums[n//2]]\n    \n    if n % 2 == 0:\n        new_nums = [nums[n//2 - 1], nums[n//2]]\n    \n    for i in range(0, n//2):\n        new_nums.insert(0, nums[n-i-1])\n        new_nums.append(nums[i])\n    return new_nums", "input": "[1]", "output": "[1]", "id": "sample_363"}
{"code": "def f(nums, verdict):\n    res = [x for x in nums if x != 0]\n    result = [[x, verdict(x)] for x in res]\n    if result:\n        return result\n    return 'error - no numbers or all zeros!'", "input": "[0, 3, 0, 1], lambda x: x < 2", "output": "[[3, False], [1, True]]", "id": "sample_364"}
{"code": "def f(n, s):\n    if s.startswith(n):\n        pre, _ = s.split(n, 1)\n        return pre + n + s[len(n):]\n    return s", "input": "'xqc', 'mRcwVqXsRDRb'", "output": "'mRcwVqXsRDRb'", "id": "sample_365"}
{"code": "def f(string):\n    tmp = string.lower()\n    for char in string.lower():\n        if char in tmp:\n            tmp = tmp.replace(char, '', 1)\n    return tmp", "input": "'[ Hello ]+ Hello, World!!_ Hi'", "output": "''", "id": "sample_366"}
{"code": "def f(nums, rmvalue):\n    res = nums[:]\n    while rmvalue in res:\n        popped = res.pop(res.index(rmvalue))\n        if popped != rmvalue:\n            res.append(popped)\n    return res", "input": "[6, 2, 1, 1, 4, 1], 5", "output": "[6, 2, 1, 1, 4, 1]", "id": "sample_367"}
{"code": "def f(string, numbers):\n    arr = []\n    for num in numbers:\n        arr.append(string.zfill(num))\n    return ' '.join(arr)", "input": "'4327', [2, 8, 9, 2, 7, 1]", "output": "'4327 00004327 000004327 4327 0004327 4327'", "id": "sample_368"}
{"code": "def f(var):\n    if var.isdigit():\n        return \"int\"\n    elif var.replace('.', '', 1).isdigit():\n        return \"float\"\n    elif var.count(' ') == len(var) - 1:\n        return \"str\"\n    elif len(var) == 1:\n        return \"char\"\n    else:\n        return \"tuple\"", "input": "\" 99 777\"", "output": "'tuple'", "id": "sample_369"}
{"code": "def f(text):\n    for char in text:\n        if not char.isspace():\n            return False\n    return True", "input": "'     i'", "output": "False", "id": "sample_370"}
{"code": "def f(nums):\n    for odd in nums[:]:\n        if odd % 2 != 0:\n            nums.remove(odd)\n    sum_ = 0\n    for num in nums:\n        sum_ += num\n    return sum_", "input": "[11, 21, 0, 11]", "output": "0", "id": "sample_371"}
{"code": "def f(list_, num):\n    temp = []\n    for i in list_:\n        i = num // 2 * ('%s,' % i)\n        temp.append(i)\n    return temp", "input": "['v'], 1", "output": "['']", "id": "sample_372"}
{"code": "def f(orig):\n    copy = orig\n    copy.append(100)\n    orig.pop()\n    return copy", "input": "[1, 2, 3]", "output": "[1, 2, 3]", "id": "sample_373"}
{"code": "def f(seq, v):\n    a = []\n    for i in seq:\n        if i.endswith(v):\n            a.append(i*2)\n    return a", "input": "[ 'oH', 'ee', 'mb', 'deft', 'n', 'zz', 'f', 'abA' ], 'zz'", "output": "['zzzz']", "id": "sample_374"}
{"code": "def f(a, b):\n    if b in a:\n        return b.join(a.partition(a[a.index(b) + 1]))\n    else:\n        return a", "input": "'sierizam', 'iz'", "output": "'sieriizzizam'", "id": "sample_375"}
{"code": "def f(text):\n    for i in range(len(text)):\n        if text[0:i].startswith(\"two\"):\n            return text[i:]\n    return 'no'", "input": "\"2two programmers\"", "output": "'no'", "id": "sample_376"}
{"code": "def f(text):\n    return ', '.join(text.splitlines())", "input": "\"BYE\\nNO\\nWAY\"", "output": "'BYE, NO, WAY'", "id": "sample_377"}
{"code": "def f(dic, key):\n    dic = dict(dic)\n    v = dic.pop(key, 0)\n    if v == 0:\n        return 'No such key!'\n    while len(dic) > 0:\n        dic[dic.popitem()[1]] = dic.popitem()[0]\n    return int(dic.popitem()[0])", "input": "dict(did=0), 'u'", "output": "'No such key!'", "id": "sample_378"}
{"code": "def f(nums):\n    for i in range(len(nums) - 1, -1, -3):\n        if nums[i] == 0:\n            nums.clear()\n            return False\n    return nums", "input": "[0, 0, 1, 2, 1]", "output": "False", "id": "sample_379"}
{"code": "def f(text, delimiter):\n    text = text.rpartition(delimiter)\n    return text[0] + text[-1]", "input": "'xxjarczx', 'x'", "output": "'xxjarcz'", "id": "sample_380"}
{"code": "def f(text, num_digits):\n    width = max(1, num_digits)\n    return text.zfill(width)", "input": "'19', 5", "output": "'00019'", "id": "sample_381"}
{"code": "def f(a):\n    s = dict(list(a.items())\n    [::-1])\n    return \" \".join([str(i) for i in s.items()])", "input": "{15: \"Qltuf\", 12: \"Rwrepny\"}", "output": "\"(12, 'Rwrepny') (15, 'Qltuf')\"", "id": "sample_382"}
{"code": "def f(text, chars):\n    result = list(text)\n    while chars in result[-3::-2]:\n        result.remove(result[-3])\n        result.remove(result[-3])\n    return ''.join(result).strip('.')", "input": "'ellod!p.nkyp.exa.bi.y.hain', '.n.in.ha.y'", "output": "'ellod!p.nkyp.exa.bi.y.hain'", "id": "sample_383"}
{"code": "def f(text, chars):\n    chars = list(chars)\n    text = list(text)\n    new_text = text\n    while len(new_text) > 0 and text:\n        if new_text[0] in chars:\n            new_text = new_text[1:]\n        else:\n            break \n    return ''.join(new_text)", "input": "'asfdellos', 'Ta'", "output": "'sfdellos'", "id": "sample_384"}
{"code": "def f(lst):\n    i = 0\n    new_list = []\n    while i < len(lst):\n        if lst[i] in lst[i+1:]:\n            new_list.append(lst[i])\n            if len(new_list) == 3:\n                return new_list\n        i += 1\n    return new_list", "input": "[0, 2, 1, 2, 6, 2, 6, 3, 0]", "output": "[0, 2, 2]", "id": "sample_385"}
{"code": "def f(concat, di):\n    count = len(di)\n    for i in range(count):\n        if di[str(i)] in concat:\n            di.pop(str(i))\n    return \"Done!\"", "input": "'mid', {'0':'q','1':'f','2':'w','3':'i'}", "output": "'Done!'", "id": "sample_386"}
{"code": "def f(nums, pos, value):\n    nums.insert(pos, value)\n    return nums", "input": "[3, 1, 2], 2, 0", "output": "[3, 1, 0, 2]", "id": "sample_387"}
{"code": "def f(text, characters):\n    character_list = list(characters) + [' ', '_']\n\n    i = 0\n    while i < len(text) and text[i] in character_list:\n        i += 1\n\n    return text[i:]", "input": "\"2nm_28in\", \"nm\"", "output": "'2nm_28in'", "id": "sample_388"}
{"code": "def f(total, arg):\n    if type(arg) is list:\n        for e in arg:\n            total.extend(e)\n    else:\n        total.extend(arg)\n    return total", "input": "[1, 2, 3], 'nammo'", "output": "[1, 2, 3, 'n', 'a', 'm', 'm', 'o']", "id": "sample_389"}
{"code": "def f(text):\n    if not text.strip():\n        return len(text.strip())\n    return None", "input": "\" \\t \"", "output": "0", "id": "sample_390"}
{"code": "def f(students):\n    seatlist = students\n    seatlist.reverse()\n    cnt = 0\n    for cnt in range(len(seatlist)):\n        cnt += 2\n        seatlist[cnt - 1:cnt] = ['+']\n    seatlist.append('+')\n    return seatlist", "input": "['r', '9']", "output": "['9', '+', '+', '+']", "id": "sample_391"}
{"code": "def f(text):\n    if text.upper() == text:\n        return 'ALL UPPERCASE'\n    return text", "input": "'Hello Is It MyClass'", "output": "'Hello Is It MyClass'", "id": "sample_392"}
{"code": "def f(text):\n    ls = text[::-1]\n    text2 = ''\n    for i in range(len(ls) - 3, 0, -3):\n        text2 += '---'.join(ls[i:i + 3]) + '---'\n    return text2[:-3]", "input": "'scala'", "output": "'a---c---s'", "id": "sample_393"}
{"code": "def f(text):\n    k = text.splitlines()\n    i = 0\n    for j in k:\n        if len(j) == 0:\n            return i\n        i+=1\n    return -1", "input": "\"2 m2 \\n\\nbike\"", "output": "1", "id": "sample_394"}
{"code": "def f(s):\n    for i in range(len(s)):\n        if s[i].isdecimal():\n            return i + (s[i] == '0')\n        elif s[i] == '0':\n            return -1\n    return -1", "input": "\"11\"", "output": "0", "id": "sample_395"}
{"code": "def f(ets):\n    while ets:\n        k, v = ets.popitem()\n        ets[k] = v**2\n    return ets", "input": "{}", "output": "{}", "id": "sample_396"}
{"code": "def f(ls):\n    return dict.fromkeys(ls, 0)", "input": "['x', 'u', 'w', 'j', 3, 6]", "output": "{'x': 0, 'u': 0, 'w': 0, 'j': 0, 3: 0, 6: 0}", "id": "sample_397"}
{"code": "def f(counts):\n    dict = {}\n    for k, v in counts.items():\n        count = counts[k]\n        if count not in dict:\n            dict[count] = []\n        dict[count].append(k)\n    counts.update(dict)\n    return counts", "input": "{'2': 2, '0': 1, '1': 2}", "output": "{'2': 2, '0': 1, '1': 2, 2: ['2', '1'], 1: ['0']}", "id": "sample_398"}
{"code": "def f(text, old, new):\n    if len(old) > 3:\n        return text\n    if old in text and ' ' not in text:\n        return text.replace(old, new*len(old))\n    while old in text:\n        text = text.replace(old, new)\n    return text", "input": "'avacado', 'va', '-'", "output": "'a--cado'", "id": "sample_399"}
{"code": "def f(multi_string):\n    cond_string = map(str.isascii, multi_string.split())\n    if True in cond_string:\n        return ', '.join(x for x in multi_string.split() if x.isascii())\n    return ''", "input": "'I am hungry! eat food.'", "output": "'I, am, hungry!, eat, food.'", "id": "sample_400"}
{"code": "def f(text, suffix):\n    if suffix and text.endswith(suffix):\n        return text[:- len(suffix)]\n    return text", "input": "'mathematics', 'example'", "output": "'mathematics'", "id": "sample_401"}
{"code": "def f(n, l):\n    archive = {}\n    for _ in range(n):\n        archive.clear()\n        archive.update({x + 10: x * 10 for x in l})\n    return archive", "input": "0, ['aaa', 'bbb']", "output": "{}", "id": "sample_402"}
{"code": "def f(full, part):\n    length = len(part)\n    index = full.find(part)\n    count = 0\n    while index >= 0:\n        full = full[index + length:]\n        index = full.find(part)\n        count += 1\n    return count", "input": "'hrsiajiajieihruejfhbrisvlmmy', 'hr'", "output": "2", "id": "sample_403"}
{"code": "def f(no):\n    d = dict.fromkeys(no, False) \n    return sum([1 for i in d.keys()])", "input": "['l', 'f', 'h', 'g', 's', 'b']", "output": "6", "id": "sample_404"}
{"code": "def f(xs):\n    new_x = xs[0] - 1\n    xs.pop(0)\n    while(new_x <= xs[0]):\n        xs.pop(0)\n        new_x -= 1\n    xs.insert(0, new_x)\n    return xs", "input": "[6, 3, 4, 1, 2, 3, 5]", "output": "[5, 3, 4, 1, 2, 3, 5]", "id": "sample_405"}
{"code": "def f(text):\n    ls = list(text)\n    ls[0], ls[-1] = ls[-1].upper(), ls[0].upper()\n    return ''.join(ls).istitle()", "input": "'Josh'", "output": "False", "id": "sample_406"}
{"code": "def f(s):\n    while len(s) > 1:\n        s.clear()\n        s.append(len(s))\n    return s.pop()", "input": "[6, 1, 2, 3]", "output": "0", "id": "sample_407"}
{"code": "def f(m):\n    m.reverse()\n    return m", "input": "[-4, 6, 0, 4, -7, 2, -1]", "output": "[-1, 2, -7, 4, 0, 6, -4]", "id": "sample_408"}
{"code": "def f(text, char):\n    if text:\n        text = text.removeprefix(char)\n        text = text.removeprefix(text[-1])\n        text = text[:-1] + text[-1].capitalize()\n    return text", "input": "'querist', 'u'", "output": "'querisT'", "id": "sample_409"}
{"code": "def f(nums):\n    a = 0\n    for i in range(len(nums)):\n        nums.insert(i, nums[a])\n        a += 1\n    return nums", "input": "[1, 3, -1, 1, -2, 6]", "output": "[1, 1, 1, 1, 1, 1, 1, 3, -1, 1, -2, 6]", "id": "sample_410"}
{"code": "def f(text, pref):\n    if isinstance(pref, list):\n        return ', '.join(text.startswith(x) for x in pref)\n    else:\n        return text.startswith(pref)", "input": "'Hello World', 'W'", "output": "False", "id": "sample_411"}
{"code": "def f(start, end, interval):\n    steps = list(range(start, end + 1, interval))\n    if 1 in steps:\n        steps[-1] = end + 1\n    return len(steps)", "input": "3, 10, 1", "output": "8", "id": "sample_412"}
{"code": "def f(s):\n    return '{}{}{}'.format(s[3:], s[2], s[5:8])", "input": "'jbucwc'", "output": "'cwcuc'", "id": "sample_413"}
{"code": "def f(d):\n    dCopy = d.copy()\n    for key, value in dCopy.items():\n        for i in range(len(value)):\n            value[i] = value[i].upper()\n    return dCopy", "input": "{'X': ['x', 'y']}", "output": "{'X': ['X', 'Y']}", "id": "sample_414"}
{"code": "def f(array):\n    d = dict(array)\n    for key, value in d.items():\n        if value < 0 or value > 9:\n            return None\n    return d", "input": "((8, 5), (8, 2), (5, 3))", "output": "{8: 2, 5: 3}", "id": "sample_415"}
{"code": "def f(text, old, new):\n    index = text.rfind(old, 0, text.find(old))\n    result = list(text)\n    while index > 0:\n        result[index:index+len(old)] = new\n        index = text.rfind(old, 0, index)\n    return ''.join(result)", "input": "'jysrhfm ojwesf xgwwdyr dlrul ymba bpq', 'j', '1'", "output": "'jysrhfm ojwesf xgwwdyr dlrul ymba bpq'", "id": "sample_416"}
{"code": "def f(lst):\n    lst.reverse()\n    lst.pop()\n    lst.reverse()\n    return lst", "input": "[7, 8, 2, 8]", "output": "[8, 2, 8]", "id": "sample_417"}
{"code": "def f(s, p):\n    arr = s.partition(p)\n    part_one, part_two, part_three = len(arr[0]), len(arr[1]), len(arr[2])\n    if part_one >= 2 and part_two <= 2 and part_three >= 2:\n        return (arr[0][::-1] + arr[1] + arr[2][::-1] + '#')\n    return (arr[0] + arr[1] + arr[2])", "input": "\"qqqqq\", \"qqq\"", "output": "'qqqqq'", "id": "sample_418"}
{"code": "def f(text, value):\n    if not value in text:\n        return ''\n    return text.rpartition(value)[0]", "input": "'mmfbifen', 'i'", "output": "'mmfb'", "id": "sample_419"}
{"code": "def f(text):\n    try:\n        return text.isalpha()\n    except:\n        return False", "input": "\"x\"", "output": "True", "id": "sample_420"}
{"code": "def f(str, n):\n    if len(str) < n:\n        return str\n    else:\n        return str.removeprefix(str[:n])", "input": "\"try.\", 5", "output": "'try.'", "id": "sample_421"}
{"code": "def f(array):\n    new_array = array.copy()\n    new_array = reversed(new_array)\n    return [x*x for x in new_array]", "input": "[1, 2, 1]", "output": "[1, 4, 1]", "id": "sample_422"}
{"code": "def f(selfie):\n    lo = len(selfie)\n    for i in range(lo-1, -1, -1):\n        if selfie[i] == selfie[0]:\n            selfie.remove(selfie[lo-1])\n    return selfie", "input": "[4, 2, 5, 1, 3, 2, 6]", "output": "[4, 2, 5, 1, 3, 2]", "id": "sample_423"}
{"code": "def f(s):\n    s = s.replace('\"', '')\n    lst = list(s)\n    col = 0\n    count = 1\n    while col < len(lst) and lst[col] in \".:,\":\n        if lst[col] == \".\":\n            count = ls[col] + 1\n        col += 1\n    return s[col+count:]", "input": "'\"Makers of a Statement\"'", "output": "'akers of a Statement'", "id": "sample_424"}
{"code": "def f(a):\n    a = a.replace('/', ':')\n    z = a.rpartition(':')\n    return [z[0], z[1], z[2]]", "input": "'/CL44     '", "output": "['', ':', 'CL44     ']", "id": "sample_425"}
{"code": "def f(numbers, elem, idx):\n    numbers.insert(idx, elem)\n    return numbers", "input": "[1, 2, 3], 8, 5", "output": "[1, 2, 3, 8]", "id": "sample_426"}
{"code": "def f(s):\n    count = len(s) - 1\n    reverse_s = s[::-1]\n    while count > 0 and reverse_s[::2].rfind('sea') == -1:\n        count -= 1\n        reverse_s = reverse_s[:count]\n    return reverse_s[count:]", "input": "'s a a b s d s a a s a a'", "output": "''", "id": "sample_427"}
{"code": "def f(nums):\n    for i in range(len(nums)):\n        if not i % 2:\n            nums.append(nums[i] * nums[i + 1])\n    return nums", "input": "[]", "output": "[]", "id": "sample_428"}
{"code": "def f(d):\n    result = []\n    while len(d.keys()) > 0:\n        result.append(d.popitem())\n    return result", "input": "{5: 1, 'abc': 2, 'defghi': 2, 87.29: 3}", "output": "[(87.29, 3), ('defghi', 2), ('abc', 2), (5, 1)]", "id": "sample_429"}
{"code": "def f(arr1, arr2):\n    new_arr = arr1.copy()\n    new_arr.extend(arr2)\n    return new_arr", "input": "[5, 1, 3, 7, 8], ['', 0, -1, []]", "output": "[5, 1, 3, 7, 8, '', 0, -1, []]", "id": "sample_430"}
{"code": "def f(n, m):\n    arr = list(range(1, n+1))\n    for i in range(m):\n        arr.clear()\n    return arr", "input": "1, 3", "output": "[]", "id": "sample_431"}
{"code": "def f(length, text):\n    if len(text) == length:\n        return text[::-1]\n    return False", "input": "-5, 'G5ogb6f,c7e.EMm'", "output": "False", "id": "sample_432"}
{"code": "def f(text):\n    text = text.split(',')\n    text.pop(0)\n    text.insert(0, text.pop(text.index('T')))\n    return 'T' + ',' + ','.join(text)", "input": "\"Dmreh,Sspp,T,G ,.tB,Vxk,Cct\"", "output": "'T,T,Sspp,G ,.tB,Vxk,Cct'", "id": "sample_433"}
{"code": "def f(string):\n    try:\n       return string.rfind('e')\n    except AttributeError:\n        return \"Nuk\"", "input": "'eeuseeeoehasa'", "output": "8", "id": "sample_434"}
{"code": "def f(numbers, num, val):\n    while len(numbers) < num:\n        numbers.insert(len(numbers) // 2, val)\n    for _ in range(len(numbers) // (num - 1) - 4):\n        numbers.insert(len(numbers) // 2, val)\n    return ' '.join(numbers)", "input": "[], 0, 1", "output": "''", "id": "sample_435"}
{"code": "def f(s, characters):\n    return [s[i:i+1] for i in characters]", "input": "'s7 6s 1ss', [1, 3, 6, 1, 2]", "output": "['7', '6', '1', '7', ' ']", "id": "sample_436"}
{"code": "def f(tap_hierarchy):\n    hierarchy = {}\n    for gift in tap_hierarchy:\n        hierarchy = hierarchy.fromkeys(gift, None)\n    return hierarchy", "input": "['john', 'doe', 'the', 'john', 'doe']", "output": "{'d': None, 'o': None, 'e': None}", "id": "sample_437"}
{"code": "def f(string):\n    bigTab = 100\n    for i in range(10, 30):\n        if 0 < string.count('\\t') < 20:\n            bigTab = i\n            break\n    return string.expandtabs(bigTab)", "input": "'1  \\t\\t\\t3'", "output": "'1                             3'", "id": "sample_438"}
{"code": "def f(value):\n    parts = value.partition(' ')[::2]\n    return ''.join(parts)", "input": "'coscifysu'", "output": "'coscifysu'", "id": "sample_439"}
{"code": "def f(text):\n    if text.isdecimal():\n        return 'yes'\n    else:\n        return 'no'", "input": "\"abc\"", "output": "'no'", "id": "sample_440"}
{"code": "def f(base, k, v):\n    base[k] = v\n    return base", "input": "{37: 'forty-five'}, '23', 'what?'", "output": "{37: 'forty-five', '23': 'what?'}", "id": "sample_441"}
{"code": "def f(lst):\n    res = []\n    for i in range(len(lst)):\n        if lst[i] % 2 == 0:\n            res.append(lst[i])\n\n    return lst.copy()", "input": "[1, 2, 3, 4]", "output": "[1, 2, 3, 4]", "id": "sample_442"}
{"code": "def f(text):\n    for space in text:\n        if space == ' ':\n            text = text.lstrip()\n        else:\n            text = text.replace('cd', space)\n    return text", "input": "\"lorem ipsum\"", "output": "'lorem ipsum'", "id": "sample_443"}
{"code": "def f(nums):\n    count = len(nums)\n    for i in range(count-1, 0, -2):\n        nums.insert(i, nums.pop(0) + nums.pop(0))\n    return nums", "input": "[-5, 3, -2, -3, -1, 3, 5]", "output": "[5, -2, 2, -5]", "id": "sample_444"}
{"code": "def f(names):\n    parts = names.split(',')\n    for i, part in enumerate(parts):\n        parts[i] = part.replace(' and', '+').title().replace('+', ' and')\n    return ', '.join(parts)", "input": "\"carrot, banana, and strawberry\"", "output": "'Carrot,  Banana,  and Strawberry'", "id": "sample_445"}
{"code": "def f(array):\n    l = len(array)\n    if l % 2 == 0:\n        array.clear()\n    else:\n        array.reverse()\n    return array", "input": "[]", "output": "[]", "id": "sample_446"}
{"code": "def f(text, tab_size):\n    res = ''\n    text = text.replace('\\t', ' '*(tab_size-1))\n    for i in range(len(text)):\n        if text[i] == ' ':\n            res += '|'\n        else:\n            res += text[i]\n    return res", "input": "\"\\ta\", 3", "output": "'||a'", "id": "sample_447"}
{"code": "def f(text, suffix):\n    if suffix == '':\n        suffix = None\n    return text.endswith(suffix)", "input": "'uMeGndkGh', 'kG'", "output": "False", "id": "sample_448"}
{"code": "def f(x):\n    n = len(x)\n    i = 0\n    while i < n and x[i].isdigit():\n        i += 1\n    return i == n", "input": "'1'", "output": "True", "id": "sample_449"}
{"code": "def f(strs):\n    strs = strs.split()\n    for i in range(1, len(strs), 2):\n        strs[i] = ''.join(reversed(strs[i]))\n    return ' '.join(strs)", "input": "'K zBK'", "output": "'K KBz'", "id": "sample_450"}
{"code": "def f(text, char):\n    text = list(text)\n    for count, item in enumerate(text):\n        if item == char:\n            text.remove(item)\n            return ''.join(text)\n    return text", "input": "'pn', 'p'", "output": "'n'", "id": "sample_451"}
{"code": "def f(text):\n    counter = 0\n    for char in text:\n        if char.isalpha():\n            counter += 1\n    return counter", "input": "'l000*',", "output": "1", "id": "sample_452"}
{"code": "def f(string, c):\n    return string.endswith(c)", "input": "'wrsch)xjmb8', 'c'", "output": "False", "id": "sample_453"}
{"code": "def f(d, count):\n    new_dict = {}\n    for _ in range(count):\n        d = d.copy()\n        new_dict = {**d, **new_dict}\n    return new_dict", "input": "{'a': 2, 'b': [], 'c': {}}, 0", "output": "{}", "id": "sample_454"}
{"code": "def f(text):\n    uppers = 0\n    for c in text:\n        if c.isupper():\n            uppers += 1\n    return text.upper() if uppers >= 10 else text", "input": "'?XyZ'", "output": "'?XyZ'", "id": "sample_455"}
{"code": "def f(s, tab):\n    return s.expandtabs(tab)", "input": "\"Join us in Hungary\", 4", "output": "'Join us in Hungary'", "id": "sample_456"}
{"code": "def f(nums):\n    count = list(range(len(nums)))\n    for i in range(len(nums)):\n        nums.pop()\n        if len(count) > 0:\n            count.pop(0)\n    return nums", "input": "[3, 1, 7, 5, 6]", "output": "[]", "id": "sample_457"}
{"code": "def f(text, search_chars, replace_chars):\n    trans_table = str.maketrans(search_chars, replace_chars)\n    return text.translate(trans_table)", "input": "'mmm34mIm', 'mm3', ',po'", "output": "'pppo4pIp'", "id": "sample_458"}
{"code": "def f(arr, d):\n    for i in range(1, len(arr), 2):\n        d.update({arr[i]: arr[i-1]})\n\n    return d", "input": "['b', 'vzjmc', 'f', 'ae', '0'], dict()", "output": "{'vzjmc': 'b', 'ae': 'f'}", "id": "sample_459"}
{"code": "def f(text, amount):\n    length = len(text)\n    pre_text = '|'\n    if amount >= length:\n        extra_space = amount - length\n        pre_text += ' ' * (extra_space // 2)\n        return pre_text + text + pre_text\n    return text", "input": "'GENERAL NAGOOR', 5", "output": "'GENERAL NAGOOR'", "id": "sample_460"}
{"code": "def f(text, search):\n    return search.startswith(text) or False", "input": "'123', '123eenhas0'", "output": "True", "id": "sample_461"}
{"code": "def f(text, value):\n    length = len(text)\n    letters = list(text)\n    if value not in letters:\n        value = letters[0]\n    return value * length", "input": "'ldebgp o', 'o'", "output": "'oooooooo'", "id": "sample_462"}
{"code": "def f(dict):\n    result = dict.copy()\n    remove_keys = []\n    for k, v in dict.items():\n        if v in dict:\n            del result[k]\n    return result", "input": "{-1: -1, 5: 5, 3: 6, -4: -4}", "output": "{3: 6}", "id": "sample_463"}
{"code": "def f(ans):\n    if ans.isdecimal():\n        total = int(ans) * 4 - 50\n        total -= len([c for c in list(ans) if c not in '02468']) * 100\n        return total\n    return 'NAN'", "input": "'0'", "output": "-50", "id": "sample_464"}
{"code": "def f(seq, value):\n    roles = dict.fromkeys(seq, 'north')\n    if value:\n        roles.update(key.strip() for key in value.split(', '))\n    return roles", "input": "['wise king', 'young king'], ''", "output": "{'wise king': 'north', 'young king': 'north'}", "id": "sample_465"}
{"code": "def f(text):\n    length = len(text)\n    index = 0\n    while index < length and text[index].isspace():\n        index += 1\n    return text[index:index+5]", "input": "'-----\\t\\n\\tth\\n-----'", "output": "'-----'", "id": "sample_466"}
{"code": "def f(nums):\n    copy = nums.copy()\n    newDict = dict()\n    for k in copy:\n        newDict[k] = len(copy[k])\n    return newDict", "input": "{}", "output": "{}", "id": "sample_467"}
{"code": "def f(a, b, n):\n    result = m = b\n    for _ in range(n):\n        if m:\n            a, m = a.replace(m, '', 1), None\n            result = m = b\n    return result.join(a.split(b))", "input": "'unrndqafi', 'c', 2", "output": "'unrndqafi'", "id": "sample_468"}
{"code": "def f(text, position, value):\n   length = len(text)\n   index = position % (length)\n   if position < 0:\n       index = length // 2\n   new_text = list(text)\n   new_text.insert(index, value)\n   new_text.pop(length-1)\n   return ''.join(new_text)", "input": "'sduyai', 1, 'y'", "output": "'syduyi'", "id": "sample_469"}
{"code": "def f(number):\n    transl = {'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5}\n    result = []\n    for key, value in transl.items():\n        if value % number == 0:\n            result.append(key)\n    return result", "input": "2", "output": "['B', 'D']", "id": "sample_470"}
{"code": "def f(val, text):\n    indices = [index for index in range(len(text)) if text[index] == val]\n    if len(indices) == 0:\n        return -1\n    else:\n        return indices[0]", "input": "'o', 'fnmart'", "output": "-1", "id": "sample_471"}
{"code": "def f(text):\n    d = {}\n    for char in text.replace('-', '').lower():\n        d[char] = d[char] + 1 if char in d else 1\n    d = sorted(d.items(), key=lambda x: x[1])\n    return [val for i, val in d]", "input": "\"x--y-z-5-C\"", "output": "[1, 1, 1, 1, 1]", "id": "sample_472"}
{"code": "def f(text, value):\n    indexes = list()\n    for i in range(len(text)):\n        if text[i] == value:\n            indexes.append(i)\n    new_text = list(text)\n    for i in indexes:\n        new_text.remove(value)\n    return ''.join(new_text)", "input": "'scedvtvotkwqfoqn', 'o'", "output": "'scedvtvtkwqfqn'", "id": "sample_473"}
{"code": "def f(txt, marker):\n    a = []\n    lines = txt.split('\\n')\n    for line in lines:\n        a.append(line.center(marker))\n    return '\\n'.join(a)", "input": "'#[)[]>[^e>\\n 8', -5", "output": "'#[)[]>[^e>\\n 8'", "id": "sample_474"}
{"code": "def f(array, index):\n    if index < 0:\n        index = len(array) + index\n    return array[index]", "input": "[1], 0", "output": "1", "id": "sample_475"}
{"code": "def f(a, split_on):\n    t = a.split()\n    a = []\n    for i in t:\n        for j in i:\n            a.append(j)\n    if split_on in a:\n        return True\n    else:\n        return False", "input": "\"booty boot-boot bootclass\", 'k'", "output": "False", "id": "sample_476"}
{"code": "def f(text):\n    topic, sep, problem = text.rpartition('|')\n    if problem == 'r':\n        problem = topic.replace('u', 'p')\n    return topic, problem", "input": "'|xduaisf'", "output": "('', 'xduaisf')", "id": "sample_477"}
{"code": "def f(sb):\n    d = {}\n    for s in sb:\n        d[s] = d.get(s, 0) + 1\n    return d", "input": "'meow meow'", "output": "{'m': 2, 'e': 2, 'o': 2, 'w': 2, ' ': 1}", "id": "sample_478"}
{"code": "def f(nums, pop1, pop2):\n    nums.pop(pop1 - 1)\n    nums.pop(pop2 - 1)\n    return nums", "input": "[1, 5, 2, 3, 6], 2, 4", "output": "[1, 2, 3]", "id": "sample_479"}
{"code": "def f(s, c1, c2):\n    if s == '':\n        return s\n    ls = s.split(c1)\n    for index, item in enumerate(ls):\n        if c1 in item:\n            ls[index] = item.replace(c1, c2, 1)\n    return c1.join(ls)", "input": "'', 'mi', 'siast'", "output": "''", "id": "sample_480"}
{"code": "def f(values, item1, item2):\n    if values[-1] == item2:\n        if values[0] not in values[1:]:\n            values.append(values[0])\n    elif values[-1] == item1:\n        if values[0] == item2:\n            values.append(values[0])\n    return values", "input": "[1, 1], 2, 3", "output": "[1, 1]", "id": "sample_481"}
{"code": "def f(text):\n    return text.replace('\\\\\"', '\"')", "input": "'Because it intrigues them'", "output": "'Because it intrigues them'", "id": "sample_482"}
{"code": "def f(text, char):\n    return ' '.join(text.split(char, len(text)))", "input": "'a', 'a'", "output": "' '", "id": "sample_483"}
{"code": "def f(arr):\n    result = []\n    for item in arr:\n        try:\n            if item.isnumeric():\n                result.append(int(item)*2)\n        except ValueError:\n            result.append(item[::-1])\n    return result", "input": "['91', '16', '6r', '5r', 'egr', '', 'f', 'q1f', '-2']", "output": "[182, 32]", "id": "sample_484"}
{"code": "def f(tokens):\n    tokens = tokens.split()\n    if len(tokens) == 2:\n        tokens = list(reversed(tokens))\n    result = ' '.join([tokens[0].ljust(5), tokens[1].ljust(5)])\n    return result", "input": "'gsd avdropj'", "output": "'avdropj gsd  '", "id": "sample_485"}
{"code": "def f(dic):\n    dic_op = dic.copy()\n    for key, val in dic.items():\n        dic_op[key] = val * val\n    return dic_op", "input": "{1:1, 2:2, 3:3}", "output": "{1: 1, 2: 4, 3: 9}", "id": "sample_486"}
{"code": "def f(dict):\n    even_keys = []\n    for key in dict.keys():\n        if key % 2 == 0:\n            even_keys.append(key)\n    return even_keys", "input": "{ 4: 'a' }", "output": "[4]", "id": "sample_487"}
{"code": "def f(text, char):\n    count = text.count(char)\n    chars = list(text)\n    if count > 0:\n        index = chars.index(char) + 1\n        chars[:index:index+1] = [c for c in chars[index:index+count:1]]\n    return ''.join(chars)", "input": "'tezmgvn 651h', '6'", "output": "'5ezmgvn 651h'", "id": "sample_488"}
{"code": "def f(text, value):\n    return text.removeprefix(value.lower())", "input": "'coscifysu', 'cos'", "output": "'cifysu'", "id": "sample_489"}
{"code": "def f(s):\n    return ''.join([c for c in s if c.isspace()])", "input": " '\\ngiyixjkvu\\n\\r\\r \\frgjuo'", "output": "'\\n\\n\\r\\r \\x0c'", "id": "sample_490"}
{"code": "def f(xs):\n    for i in range(-1, -len(xs)-1, -1):\n        xs.extend([xs[i], xs[i]])\n    return xs", "input": "[4, 8, 8, 5]", "output": "[4, 8, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5]", "id": "sample_491"}
{"code": "def f(text, value):\n    ls = list(text)\n    if (ls.count(value)) % 2 == 0:\n        while value in ls:\n            ls.remove(value)\n    else:\n        ls.clear()\n    return ''.join(ls)", "input": "'abbkebaniuwurzvr', 'm'", "output": "'abbkebaniuwurzvr'", "id": "sample_492"}
{"code": "def f(d):\n    keys = []\n    for k in d:\n        keys.append('%s => %s' % (k, d[k]))\n    return keys", "input": "{'-4':'4','1':'2','-':'-3'}", "output": "['-4 => 4', '1 => 2', '- => -3']", "id": "sample_493"}
{"code": "def f(num, l):\n    t = \"\"\n    while l > len(num):\n        t += '0'\n        l -= 1\n    return t + num", "input": "\"1\", 3", "output": "'001'", "id": "sample_494"}
{"code": "def f(s):\n    if str.isascii(s[-5:]):\n        return s[-5:], s[0:][:3]\n    elif str.isascii(s[:5]):\n        return s[:5], s[-5:][3:]\n    else:\n        return s", "input": "'a1234\u00e5r'", "output": "('a1234', '\u00e5r')", "id": "sample_495"}
{"code": "def f(text, value):\n    if isinstance(value, str):\n        return text.count(value) + text.count(value.lower())\n    return text.count(value)", "input": "'eftw{\u044c\u0422\u0441k_1', '\\\\'", "output": "0", "id": "sample_496"}
{"code": "def f(n):\n    b = list(str(n))\n    for i in range(2,len(b)): b[i] += '+'\n    return b", "input": "44", "output": "['4', '4']", "id": "sample_497"}
{"code": "def f(nums, idx, added):\n    nums[idx:idx] = (added,)\n    return nums", "input": "[2, 2, 2, 3, 3], 2, 3", "output": "[2, 2, 3, 2, 3, 3]", "id": "sample_498"}
{"code": "def f(text, length, fillchar):\n    size = len(text)\n    return text.center(length, fillchar)", "input": "'magazine', 25, '.'", "output": "'.........magazine........'", "id": "sample_499"}
{"code": "def f(text, delim):\n    return text[:text[::-1].find(delim)][::-1]", "input": "'dsj osq wi w', ' '", "output": "'d'", "id": "sample_500"}
{"code": "def f(text, char):\n    index = text.rindex(char)\n    result = list(text)\n    while index > 0:\n        result[index] = result[index-1]\n        result[index-1] = char\n        index -= 2\n    return ''.join(result)", "input": "'qpfi jzm', 'j'", "output": "'jqjfj zm'", "id": "sample_501"}
{"code": "def f(name):\n    return '*'.join(name.split(' '))", "input": "'Fred Smith'", "output": "'Fred*Smith'", "id": "sample_502"}
{"code": "def f(d):\n    result = [None] * len(d)\n    a = b = 0\n    while d:\n        result[a] = d.popitem(a == b)\n        a, b = b, (b+1) % len(result)\n    return result", "input": "{}", "output": "[]", "id": "sample_503"}
{"code": "def f(values):\n    values.sort()\n    return values", "input": "[1, 1, 1, 1]", "output": "[1, 1, 1, 1]", "id": "sample_504"}
{"code": "def f(string):\n    while string:\n        if string[-1].isalpha():\n            return string\n        string = string[:-1]\n    return string", "input": "'--4/0-209'", "output": "''", "id": "sample_505"}
{"code": "def f(n):\n    p = ''\n    if n%2 == 1:\n        p+='sn'\n    else:\n        return n*n\n    for x in range(1, n+1):\n        if x%2 == 0:\n            p+='to'\n        else:\n            p+='ts'\n    return p", "input": "1", "output": "'snts'", "id": "sample_506"}
{"code": "def f(text, search):\n    result = text.lower()\n    return result.find(search.lower())", "input": "'car hat', 'car'", "output": "0", "id": "sample_507"}
{"code": "def f(text, sep, maxsplit):\n    splitted = text.rsplit(sep, maxsplit)\n    length = len(splitted)\n    new_splitted = splitted[:length // 2]\n    new_splitted.reverse()\n    new_splitted += splitted[length // 2:]\n    return sep.join(new_splitted)", "input": "'ertubwi', 'p', 5", "output": "'ertubwi'", "id": "sample_508"}
{"code": "def f(value, width):\n    if value >= 0:\n        return str(value).zfill(width)\n\n    if value < 0:\n        return '-' + str(-value).zfill(width)\n    return ''", "input": "5, 1", "output": "'5'", "id": "sample_509"}
{"code": "def f(a, b, c, d, e):\n    key = d\n    if key in a:\n        num = a.pop(key)\n    if b > 3:\n        return ''.join(c)\n    else:\n        return num", "input": "{7: 'ii5p', 1: 'o3Jwus', 3: 'lot9L', 2: '04g', 9: 'Wjf', 8: '5b', 0: 'te6', 5: 'flLO', 6: 'jq', 4: 'vfa0tW'}, 4, 'Wy', 'Wy', 1.0", "output": "'Wy'", "id": "sample_510"}
{"code": "def f(fields, update_dict):\n    di = dict((x, '') for x in fields)\n    di.update(update_dict)\n    return di", "input": "('ct', 'c', 'ca'), {'ca': 'cx'}", "output": "{'ct': '', 'c': '', 'ca': 'cx'}", "id": "sample_511"}
{"code": "def f(s):\n    return len(s) == s.count('0') + s.count('1')", "input": "'102'", "output": "False", "id": "sample_512"}
{"code": "def f(array):\n    while -1 in array:\n        array.pop(-3)\n    while 0 in array:\n        array.pop()\n    while 1 in array:\n        array.pop(0)\n    return array", "input": "[0, 2]", "output": "[]", "id": "sample_513"}
{"code": "def f(text):\n    for item in text.split():\n        text = text.replace('-{}'.format(item), ' ').replace('{}-'.format(item), ' ')\n    return text.strip('-')", "input": "'-stew---corn-and-beans-in soup-.-'", "output": "'stew---corn-and-beans-in soup-.'", "id": "sample_514"}
{"code": "def f(array):\n    result = array.copy()\n    result.reverse()\n    result[:] = [item * 2 for item in result]\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "[10, 8, 6, 4, 2]", "id": "sample_515"}
{"code": "def f(strings, substr):\n    list = [s for s in strings if s.startswith(substr)]\n    return sorted(list, key=len)", "input": "['condor', 'eyes', 'gay', 'isa' ], 'd'", "output": "[]", "id": "sample_516"}
{"code": "def f(text):\n    for i in range(len(text)-1, 0, -1):\n        if not text[i].isupper():\n            return text[0:i]\n    return ''", "input": "'SzHjifnzog'", "output": "'SzHjifnzo'", "id": "sample_517"}
{"code": "def f(text):\n    return not text.isdecimal()", "input": "'the speed is -36 miles per hour'", "output": "True", "id": "sample_518"}
{"code": "def f(d):\n    d['luck'] = 42\n    d.clear()\n    return {1: False, 2 :0}", "input": "{}", "output": "{1: False, 2: 0}", "id": "sample_519"}
{"code": "def f(album_sales):\n    while len(album_sales) != 1:\n        album_sales.append(album_sales.pop(0))\n    return album_sales[0]", "input": "[6]", "output": "6", "id": "sample_520"}
{"code": "def f(nums):\n    m = max(nums)\n    for i in range(m):\n        nums.reverse()\n    return nums", "input": "[43, 0, 4, 77, 5, 2, 0, 9, 77]", "output": "[77, 9, 0, 2, 5, 77, 4, 0, 43]", "id": "sample_521"}
{"code": "def f(numbers):\n    floats = [n % 1 for n in numbers]\n    return floats if 1 in floats else []", "input": "range(100, 120)", "output": "[]", "id": "sample_522"}
{"code": "def f(text):\n    text = list(text)\n    for i in range(len(text)-1, -1, -1):\n        if text[i].isspace():\n            text[i] = '&nbsp;'\n    return ''.join(text)", "input": "'   '", "output": "'&nbsp;&nbsp;&nbsp;'", "id": "sample_523"}
{"code": "def f(dict0):\n    new = dict0.copy()\n    for i in range(len(new)-1):\n        dict0[sorted(new)[i]] = i\n    return dict0", "input": "{2: 5, 4: 1, 3: 5, 1: 3, 5: 1}", "output": "{2: 1, 4: 3, 3: 2, 1: 0, 5: 1}", "id": "sample_524"}
{"code": "def f(c, st, ed):\n    d = {}\n    a, b = 0, 0\n    for x, y in c.items():\n        d[y] = x\n        if y == st:\n            a = x\n        if y == ed:\n            b = x\n    w = d[st]\n    return (w, b) if a > b else (b, w)", "input": "{'TEXT': 7, 'CODE': 3}, 7, 3", "output": "('TEXT', 'CODE')", "id": "sample_525"}
{"code": "def f(label1, char, label2, index):\n    m = label1.rindex(char)\n    if m >= index:\n        return label2[:m - index + 1]\n    return label1 + label2[index - m - 1:]", "input": "'ekwies', 's', 'rpg', 1", "output": "'rpg'", "id": "sample_526"}
{"code": "def f(text, value):\n    return text.ljust(len(value), \"?\")", "input": "\"!?\", \"\"", "output": "'!?'", "id": "sample_527"}
{"code": "def f(s):\n    b = ''\n    c = ''\n    for i in s:\n        c = c + i\n        if s.rfind(c) > -1:\n            return s.rfind(c)\n    return 0", "input": "'papeluchis'", "output": "2", "id": "sample_528"}
{"code": "def f(array):\n    prev = array[0]\n    newArray = array[:]\n    for i in range(1, len(array)):\n        if prev != array[i]:\n            newArray[i] = array[i]\n        else:\n            del newArray[i]\n        prev = array[i]\n    return newArray", "input": "[1, 2, 3]", "output": "[1, 2, 3]", "id": "sample_529"}
{"code": "def f(s, ch):\n    sl = s\n    if ch in s:\n        sl = s.lstrip(ch)\n        if len(sl) == 0:\n            sl = sl + '!?'\n    else:\n        return 'no'\n    return sl", "input": "\"@@@ff\", '@'", "output": "'ff'", "id": "sample_530"}
{"code": "def f(text, x):\n    if text.removeprefix(x) == text:\n        return f(text[1:], x)\n    else:\n        return text", "input": "\"Ibaskdjgblw asdl \", \"djgblw\"", "output": "'djgblw asdl '", "id": "sample_531"}
{"code": "def f(n, array):\n    final = [array.copy()] \n    for i in range(n):\n        arr = array.copy()\n        arr.extend(final[-1])\n        final.append(arr)\n    return final", "input": "1, [1, 2, 3]", "output": "[[1, 2, 3], [1, 2, 3, 1, 2, 3]]", "id": "sample_532"}
{"code": "def f(query, base):\n    net_sum = 0\n    for (key, val) in base.items():\n        if key[0] == query and len(key) == 3:\n            net_sum -= val\n        elif key[-1] == query and len(key) == 3:\n            net_sum += val\n    return net_sum", "input": "'a', {}", "output": "0", "id": "sample_533"}
{"code": "def f(sequence, value):\n    i = max(sequence.index(value) - len(sequence) // 3, 0)\n    result = ''\n    for j, v in enumerate(sequence[i:]):\n        if v == '+':\n            result += value\n        else:\n            result += sequence[i + j]\n    return result", "input": "'hosu', 'o'", "output": "'hosu'", "id": "sample_534"}
{"code": "def f(n):\n    for n in str(n):\n        if n not in \"012\" and n not in list(range(5, 10)):\n            return False\n    return True", "input": "1341240312", "output": "False", "id": "sample_535"}
{"code": "def f(cat):\n    digits = 0\n    for char in cat:\n        if char.isdigit():\n            digits += 1\n    return digits", "input": "'C24Bxxx982ab'", "output": "5", "id": "sample_536"}
{"code": "def f(text, value):\n    new_text = list(text)\n    try:\n        new_text.append(value)\n        length = len(new_text)\n    except IndexError:\n        length = 0\n    return '[' + str(length) + ']'", "input": "'abv', 'a'", "output": "'[4]'", "id": "sample_537"}
{"code": "def f(text, width):\n    return text[:width].center(width, 'z')", "input": "'0574', 9", "output": "'zzz0574zz'", "id": "sample_538"}
{"code": "def f(array):\n    c = array\n    array_copy = array\n\n    while True:\n        c.append('_')\n        if c == array_copy:\n            array_copy[c.index('_')] = ''\n            break\n        \n    return array_copy", "input": "[]", "output": "['']", "id": "sample_539"}
{"code": "def f(a):\n    b = a.copy()\n    for k in range(0, len(a) - 1, 2):\n        b.insert(k + 1, b[k])\n    b.append(b[0])\n    return b", "input": "[5, 5, 5, 6, 4, 9]", "output": "[5, 5, 5, 5, 5, 5, 6, 4, 9, 5]", "id": "sample_540"}
{"code": "def f(text):\n    return ''.join(list(text)).isspace()", "input": "' \\t  \\u3000'", "output": "True", "id": "sample_541"}
{"code": "def f(test, sep, maxsplit):\n    try:\n        return test.rsplit(sep, maxsplit)\n    except:\n        return test.rsplit()", "input": "'ab cd', 'x', 2", "output": "['ab cd']", "id": "sample_542"}
{"code": "def f(item):\n    modified = item.replace('. ', ' , ').replace('&#33; ', '! ').replace('. ', '? ').replace('. ', '. ')\n    return modified[0].upper() + modified[1:]", "input": "'.,,,,,. \u0645\u0646\u0628\u062a'", "output": "'.,,,,, , \u0645\u0646\u0628\u062a'", "id": "sample_543"}
{"code": "def f(text):\n    a = text.split('\\n')\n    b = []\n    for i in range(len(a)):\n        c = a[i].replace('\\t', '    ')\n        b.append(c)\n    return '\\n'.join(b)", "input": "\"\\t\\t\\ttab tab tabulates\"", "output": "'            tab tab tabulates'", "id": "sample_544"}
{"code": "def f(array):\n    result = []\n    index = 0\n    while index < len(array):\n        result.append(array.pop())\n        index += 2\n    return result", "input": "[8, 8, -4, -9, 2, 8, -1, 8]", "output": "[8, -1, 8]", "id": "sample_545"}
{"code": "def f(text, speaker):\n    while text.startswith(speaker):\n        text = text[len(speaker):]\n    return text", "input": "'[CHARRUNNERS]Do you know who the other was? [NEGMENDS]', '[CHARRUNNERS]'", "output": "'Do you know who the other was? [NEGMENDS]'", "id": "sample_546"}
{"code": "def f(letters):\n    letters_only = letters.strip(\"., !?*\")\n    return \"....\".join(letters_only.split(\" \"))", "input": "\"h,e,l,l,o,wo,r,ld,\"", "output": "'h,e,l,l,o,wo,r,ld'", "id": "sample_547"}
{"code": "def f(text, suffix):\n    if suffix and text and text.endswith(suffix):\n        return text.removesuffix(suffix)\n    else:\n        return text", "input": "'spider', 'ed'", "output": "'spider'", "id": "sample_548"}
{"code": "def f(matrix):\n    matrix.reverse()\n    result = []\n    for primary in matrix:\n        max(primary)\n        primary.sort(reverse = True)\n        result.append(primary)\n    return result", "input": "[[1, 1, 1, 1]]", "output": "[[1, 1, 1, 1]]", "id": "sample_549"}
{"code": "def f(nums):\n    for i in range(len(nums)):\n        nums.insert(i, nums[i]**2)\n    return nums", "input": "[1, 2, 4]", "output": "[1, 1, 1, 1, 2, 4]", "id": "sample_550"}
{"code": "def f(data):\n    members = []\n    for item in data:\n        for member in data[item]:\n            if member not in members:\n                members.append(member)\n    return sorted(members)", "input": "{'inf': ['a', 'b'], 'a': [\"inf\", \"c\"], 'd': [\"inf\"]}", "output": "['a', 'b', 'c', 'inf']", "id": "sample_551"}
{"code": "def f(d):\n    result = {}\n    for k, v in d.items():\n        if isinstance(k, float):\n            for i in v:\n                result[i] = k\n        else:\n            result[k] = v\n    return result", "input": "{2: 0.76, 5: [3, 6, 9, 12]}", "output": "{2: 0.76, 5: [3, 6, 9, 12]}", "id": "sample_552"}
{"code": "def f(text, count):\n    for i in range(count):\n        text = text[::-1]\n    return text", "input": "'439m2670hlsw', 3", "output": "'wslh0762m934'", "id": "sample_553"}
{"code": "def f(arr):\n    return list(reversed(arr))", "input": "[2, 0, 1, 9999, 3, -5]", "output": "[-5, 3, 9999, 1, 0, 2]", "id": "sample_554"}
{"code": "def f(text, tabstop):\n    text = text.replace('\\n', '_____')\n    text = text.replace('\\t', tabstop * ' ')\n    text = text.replace('_____', '\\n')\n    return text", "input": "\"odes\\tcode\\twell\", 2", "output": "'odes  code  well'", "id": "sample_555"}
{"code": "def f(text):\n    for i in range(len(text)):\n        if text[i] == ' ':\n            text = text.replace(' ', '\\t', 1)\n    return text.expandtabs(4)", "input": "'\\n\\n\\t\\tz\\td\\ng\\n\\t\\t\\te'", "output": "'\\n\\n        z   d\\ng\\n            e'", "id": "sample_556"}
{"code": "def f(str):\n    d = str.rpartition('ar')\n    return ' '.join((d[0], d[1], d[2]))", "input": "'xxxarmmarxx'", "output": "'xxxarmm ar xx'", "id": "sample_557"}
{"code": "def f(nums, mos):\n    for num in mos:\n        nums.pop(nums.index(num))\n    nums.sort()\n    for num in mos:\n        nums += [num]\n    for i in range(len(nums)-1):\n        if nums[i] > nums[i+1]:\n            return False\n    return True", "input": "[3, 1, 2, 1, 4, 1], [1]", "output": "False", "id": "sample_558"}
{"code": "def f(n):\n    n = str(n)\n    return n[0] + '.'+n[1:].replace('-', '_')", "input": "\"first-second-third\"", "output": "'f.irst_second_third'", "id": "sample_559"}
{"code": "def f(text):\n    x = 0\n    if text.islower():\n        for c in text:\n            if int(c) in list(range(90)):\n                x+=1\n    return x", "input": "\"591237865\"", "output": "0", "id": "sample_560"}
{"code": "def f(text, digit):\n    #different than previous? Just count instances digit\n    count = text.count(digit)\n    return int(digit) * count", "input": "'7Ljnw4Lj', '7'", "output": "7", "id": "sample_561"}
{"code": "def f(text):\n    return text.upper() == str(text)", "input": "'VTBAEPJSLGAHINS'", "output": "True", "id": "sample_562"}
{"code": "def f(text1, text2):\n    nums = []\n    for i in range(len(text2)):\n        nums.append(text1.count(text2[i]))\n    return sum(nums)", "input": "'jivespdcxc', 'sx'", "output": "2", "id": "sample_563"}
{"code": "def f(lists):\n    lists[1].clear()\n    lists[2] += lists[1]\n    return lists[0]", "input": "[[395, 666, 7, 4], [], [4223, 111]]", "output": "[395, 666, 7, 4]", "id": "sample_564"}
{"code": "def f(text):\n    return max(text.find(ch) for ch in 'aeiou')", "input": "\"qsqgijwmmhbchoj\"", "output": "13", "id": "sample_565"}
{"code": "def f(string, code):\n    t = ''\n    try:\n        t = string.encode(code)\n        if t.endswith(b'\\n'):\n            t = t[:-1]\n        t = t.decode('UTF-8')\n        return t\n    except:\n        return t", "input": "\"towaru\", \"UTF-8\"", "output": "'towaru'", "id": "sample_566"}
{"code": "def f(s, n):\n    ls = s.rsplit()\n    out = []\n    while len(ls) >= n:\n        out += ls[-n:]\n        ls = ls[:-n]\n    return ls + ['_'.join(out)]", "input": "'one two three four five', 3", "output": "['one', 'two', 'three_four_five']", "id": "sample_567"}
{"code": "def f(num):\n    letter = 1\n    for i in '1234567890':\n        num = num.replace(i,'')\n        if len(num) == 0: break\n        num = num[letter:] + num[:letter]\n        letter += 1\n    return num", "input": "'bwmm7h'", "output": "'mhbwm'", "id": "sample_568"}
{"code": "def f(txt):\n    coincidences = {}\n    for c in txt:\n        if c in coincidences:\n            coincidences[c] += 1\n        else:\n            coincidences[c] = 1\n    return sum(coincidences.values())", "input": "\"11 1 1\"", "output": "6", "id": "sample_569"}
{"code": "def f(array, index, value):\n    array.insert(0, index + 1)\n    if value >= 1:\n        array.insert(index, value)\n    return array", "input": "[2], 0, 2", "output": "[2, 1, 2]", "id": "sample_570"}
{"code": "def f(input_string, spaces):\n    return input_string.expandtabs(spaces)", "input": "r'a\\tb', 4", "output": "'a\\\\tb'", "id": "sample_571"}
{"code": "def f(data, num):\n    new_dict = {}\n    temp = list(data.items())\n    for i in range(len(temp) - 1, num - 1, -1):\n        new_dict[temp[i]] = None\n    return temp[num:] + list(new_dict.items())", "input": "{1: 9, 2: 10, 3: 1}, 1", "output": "[(2, 10), (3, 1), ((3, 1), None), ((2, 10), None)]", "id": "sample_572"}
{"code": "def f(string, prefix):\n    if string.startswith(prefix):\n        return string.removeprefix(prefix)\n    return string", "input": "\"Vipra\", \"via\"", "output": "'Vipra'", "id": "sample_573"}
{"code": "def f(simpons):\n    while simpons:\n        pop = simpons.pop()\n        if pop == pop.title():\n            return pop\n    return pop", "input": "['George', 'Michael', 'George', 'Costanza']", "output": "'Costanza'", "id": "sample_574"}
{"code": "def f(nums, val):\n    new_list = []\n    [new_list.extend([i] * val) for i in nums]\n    return sum(new_list)", "input": "[10, 4], 3", "output": "42", "id": "sample_575"}
{"code": "def f(array, const):\n    output = ['x']\n    for i in range(1, len(array) + 1):\n        if i % 2 != 0:\n            output.append(array[i - 1] * -2)\n        else:\n            output.append(const)\n    return output", "input": "[1, 2, 3], -1", "output": "['x', -2, -1, -6]", "id": "sample_576"}
{"code": "def f(items):\n    result = []\n    for number in items:\n        d = dict(items).copy()\n        d.popitem()\n        result.append(d)\n        items = d\n    return result", "input": "[(1, 'pos')]", "output": "[{}]", "id": "sample_577"}
{"code": "def f(obj):\n    for k, v in obj.items():\n        if v >= 0:\n            obj[k] = -v\n    return obj", "input": "{'R': 0, 'T': 3, 'F': -6, 'K': 0}", "output": "{'R': 0, 'T': -3, 'F': -6, 'K': 0}", "id": "sample_578"}
{"code": "def f(text):\n    if text.istitle():\n        if len(text) > 1 and text.lower() != text:\n            return text[0].lower() + text[1:]\n    elif text.isalpha():\n        return text.capitalize()\n    return text", "input": "''", "output": "''", "id": "sample_579"}
{"code": "def f(text, char):\n    new_text = text\n    a = []\n    while char in new_text:\n        a.append(new_text.index(char))\n        new_text = new_text.replace(char,\"\",1)\n    return a", "input": "'rvr', 'r'", "output": "[0, 1]", "id": "sample_580"}
{"code": "def f(text, sign):\n    length = len(text)\n    new_text = list(text)\n    sign = list(sign)\n    for i in range(len(sign)):\n        new_text.insert((i * length - 1) // 2 + (i + 1) // 2, sign[i])\n    return ''.join(new_text)", "input": "'akoon', 'sXo'", "output": "'akoXoosn'", "id": "sample_581"}
{"code": "def f(k, j):\n    arr = []\n    for i in range(k):\n        arr.append(j)\n    return arr", "input": "7, 5", "output": "[5, 5, 5, 5, 5, 5, 5]", "id": "sample_582"}
{"code": "def f(text, ch):\n    result = []\n    for line in text.splitlines():\n        if len(line) > 0 and line[0] == ch:\n            result.append(line.lower())\n        else:\n            result.append(line.upper())\n    return \"\\n\".join(result)", "input": "\"t\\nza\\na\", \"t\"", "output": "'t\\nZA\\nA'", "id": "sample_583"}
{"code": "def f(txt):\n    return txt.format(*('0'*20,))", "input": "\"5123807309875480094949830\"", "output": "'5123807309875480094949830'", "id": "sample_584"}
{"code": "def f(text):\n    count = text.count(text[0])\n    ls = list(text)\n    for _ in range(count):\n        ls.remove(ls[0])\n    return ''.join(ls)", "input": "';,,,?'", "output": "',,,?'", "id": "sample_585"}
{"code": "def f(text, char):\n    return text.rindex(char)", "input": "\"breakfast\", \"e\"", "output": "2", "id": "sample_586"}
{"code": "def f(nums, fill):\n    ans = dict.fromkeys(nums, fill)\n    return ans", "input": "[0, 1, 1, 2], 'abcca'", "output": "{0: 'abcca', 1: 'abcca', 2: 'abcca'}", "id": "sample_587"}
{"code": "def f(items, target):\n    if target in items:\n        return items.index(target)\n    return -1", "input": "['''1''', '+', '-', '**', '//', '*', '+'], '**'", "output": "3", "id": "sample_588"}
{"code": "def f(num):\n    num.append(num[-1])\n    return num", "input": "[-70, 20, 9, 1]", "output": "[-70, 20, 9, 1, 1]", "id": "sample_589"}
{"code": "def f(text):\n    for i in range(10, 0, -1):\n        text = text.lstrip(str(i))\n    return text", "input": "\"25000   $\"", "output": "'5000   $'", "id": "sample_590"}
{"code": "def f(arr): \n    counts = [0] * 9 \n    ans = [] \n    for ele in arr: counts[ele - 1] += 1 \n    for i in range(len(counts)): \n        while counts[i] > 0: \n            counts[i] -= 1 \n            ans.append(i + 1)\n    return counts, ans", "input": "[6, 3, 0, 7, 4, 8]", "output": "([0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 4, 6, 7, 8, 9])", "id": "sample_591"}
{"code": "def f(numbers):\n    new_numbers = []\n    for i, _ in enumerate(numbers):\n        new_numbers.append(numbers[len(numbers)-1-i])\n    return new_numbers", "input": "[11, 3]", "output": "[3, 11]", "id": "sample_592"}
{"code": "def f(nums, n):\n    pos = len(nums) - 1\n    for i in range(-len(nums), 0):\n        nums.insert(pos, nums[i])\n    return nums", "input": "[], 14", "output": "[]", "id": "sample_593"}
{"code": "def f(file):\n    return file.index('\\n')", "input": "\"n wez szize lnson tilebi it 504n.\\n\"", "output": "33", "id": "sample_594"}
{"code": "def f(text, prefix):\n    if text.startswith(prefix):\n        text = text.removeprefix(prefix)\n    text = text.capitalize()\n    return text", "input": "'qdhstudentamxupuihbuztn', 'jdm'", "output": "'Qdhstudentamxupuihbuztn'", "id": "sample_595"}
{"code": "def f(txt, alpha):\n    txt = sorted(txt)\n    if txt.index(alpha) % 2 == 0:\n        return txt[::-1]\n    return txt", "input": "['8', '9', '7', '4', '3', '2'], '9'", "output": "['2', '3', '4', '7', '8', '9']", "id": "sample_596"}
{"code": "def f(s):\n    return s.upper()", "input": "\"Jaafodsfa SOdofj AoaFjIs  JAFasIdfSa1\"", "output": "'JAAFODSFA SODOFJ AOAFJIS  JAFASIDFSA1'", "id": "sample_597"}
{"code": "def f(text, n):\n    length = len(text)\n    return text[length*(n%4):length ]", "input": "'abc', 1", "output": "''", "id": "sample_598"}
{"code": "def f(a, b):\n    a = b.join(a)\n    lst = []\n    for i in range(1, len(a)+1, 2):\n        lst.append(a[i-1:][:i])\n        lst.append(a[i-1:][i:])\n    return lst", "input": "[\"a\", \"b\", \"c\"], \" \"", "output": "['a', ' b c', 'b c', '', 'c', '']", "id": "sample_599"}
{"code": "def f(array):\n    just_ns = list(map(lambda num: 'n'*num, array))\n    final_output = []\n    for wipe in just_ns:\n        final_output.append(wipe)\n    return final_output", "input": "[]", "output": "[]", "id": "sample_600"}
{"code": "def f(text):\n    t = 5\n    tab = []\n    for i in text:\n        if i.lower() in 'aeiouy':\n            tab.append(i.upper() * t)\n        else:\n            tab.append(i * t)\n    return ' '.join(tab)", "input": "'csharp'", "output": "'ccccc sssss hhhhh AAAAA rrrrr ppppp'", "id": "sample_601"}
{"code": "def f(nums, target):\n    cnt = nums.count(target)\n    return cnt * 2", "input": "[1, 1], 1", "output": "4", "id": "sample_602"}
{"code": "def f(sentences):\n    if all([sentence.isdecimal() for sentence in sentences.split('.')]):\n        return 'oscillating' \n    else:\n        return 'not oscillating'", "input": "'not numbers'", "output": "'not oscillating'", "id": "sample_603"}
{"code": "def f(text, start):\n    return text.startswith(start)", "input": "\"Hello world\", \"Hello\"", "output": "True", "id": "sample_604"}
{"code": "def f(nums):\n    nums.clear()\n    return \"quack\"", "input": "[2, 5, 1, 7, 9, 3]", "output": "'quack'", "id": "sample_605"}
{"code": "def f(value):\n    ls = list(value)\n    ls.append('NHIB')\n    return ''.join(ls)", "input": "'ruam'", "output": "'ruamNHIB'", "id": "sample_606"}
{"code": "def f(text):\n    for i in ['.', '!', '?']:\n        if text.endswith(i):\n            return True\n    return False", "input": "'. C.'", "output": "True", "id": "sample_607"}
{"code": "def f(aDict):\n    # transpose the keys and values into a new dict\n    return dict([v for v in aDict.items()])", "input": "{1:1, 2:2, 3:3}", "output": "{1: 1, 2: 2, 3: 3}", "id": "sample_608"}
{"code": "def f(array, elem):\n    result = array.copy()\n    while result:\n        key, value = result.popitem()\n        if elem == key or elem == value:\n            result.update(array)\n        del result[key]\n    return result", "input": "{}, 1", "output": "{}", "id": "sample_609"}
{"code": "def f(keys, value):\n    d = dict.fromkeys(keys, value)\n    for i, k in enumerate(d.copy(), 1):\n        if d[k] == d[i]:\n            del d[i]\n    return d", "input": "[1, 2, 1, 1], 3", "output": "{}", "id": "sample_610"}
{"code": "def f(nums):\n    nums[:] = nums[::-1]\n    return nums", "input": "[-6, -2, 1, -3, 0, 1]", "output": "[1, 0, -3, 1, -2, -6]", "id": "sample_611"}
{"code": "def f(d):\n    return dict(d.items())", "input": "{'a': 42, 'b': 1337, 'c': -1, 'd': 5}", "output": "{'a': 42, 'b': 1337, 'c': -1, 'd': 5}", "id": "sample_612"}
{"code": "def f(text):\n    result = ''\n    mid = (len(text) - 1) // 2\n    for i in range(mid):\n        result += text[i]\n    for i in range(mid, len(text)-1):\n        result += text[mid + len(text) - 1 - i]\n    return result.ljust(len(text), text[-1])", "input": "'eat!'", "output": "'e!t!'", "id": "sample_613"}
{"code": "def f(text, substr, occ):\n    n = 0\n    while True:\n        i = text.rfind(substr)\n        if i == -1:\n            break\n        elif n == occ:\n            return i\n        else:\n            n += 1\n            text = text[:i]\n    return -1", "input": "'zjegiymjc', 'j', 2", "output": "-1", "id": "sample_614"}
{"code": "def f(in_list, num):\n    in_list.append(num)\n    return in_list.index(max(in_list[:-1]))", "input": "[-1, 12, -6, -2], -1", "output": "1", "id": "sample_615"}
{"code": "def f(body):\n    ls = list(body)\n    dist = 0\n    for i in range(0, len(ls) - 1):\n        if ls[i - 2 if i - 2 >= 0 else 0] == '\\t':\n            dist += (1 + ls[i - 1].count('\\t')) * 3\n        ls[i] = '[' + ls[i] + ']'\n    return ''.join(ls).expandtabs(4 + dist)", "input": "'\\n\\ny\\n'", "output": "'[\\n][\\n][y]\\n'", "id": "sample_616"}
{"code": "def f(text):\n    if text.isascii():\n        return 'ascii'\n    else:\n        return 'non ascii'", "input": "\"<<<<\"", "output": "'ascii'", "id": "sample_617"}
{"code": "def f(match, fill, n):\n    return fill[:n] + match", "input": "'9', '8', 2", "output": "'89'", "id": "sample_618"}
{"code": "def f(title):\n    return title.lower()", "input": "'   Rock   Paper   SCISSORS  '", "output": "'   rock   paper   scissors  '", "id": "sample_619"}
{"code": "def f(x):\n    return \" \".join(list(x)[::-1])", "input": "\"lert dna ndqmxohi3\"", "output": "'3 i h o x m q d n   a n d   t r e l'", "id": "sample_620"}
{"code": "def f(text, encoding):\n    try:\n        return text.encode(encoding)\n    except LookupError:\n        return str(LookupError)", "input": "'13:45:56', 'shift_jis'", "output": "b'13:45:56'", "id": "sample_621"}
{"code": "def f(s):\n    left, sep, right = s.rpartition('.')\n    new = sep.join([right, left])\n    _, sep, _ = new.rpartition('.')\n    return new.replace(sep, ', ')", "input": "'galgu'", "output": "', g, a, l, g, u, '", "id": "sample_622"}
{"code": "def f(text, rules):\n    for rule in rules:\n        if rule == '@':\n            text = text[::-1]\n        elif rule == '~':\n            text = text.upper()\n        elif text and text[len(text)-1] == rule:\n            text = text[0:len(text)-1]\n    return text", "input": "'hi~!', ['~', '`', '!', '&']", "output": "'HI~'", "id": "sample_623"}
{"code": "def f(text, char):\n    char_index = text.find(char)\n    result = []\n    if char_index > 0:\n        result = list(text[:char_index])\n    result.extend(list(char)+list(text[char_index+len(char):]))\n    return ''.join(result)", "input": "'llomnrpc', 'x'", "output": "'xllomnrpc'", "id": "sample_624"}
{"code": "def f(text):\n    count = 0\n    for i in text:\n        if i in '.?!.,':\n            count += 1\n    return count", "input": "\"bwiajegrwjd??djoda,?\"", "output": "4", "id": "sample_625"}
{"code": "def f(line, equalityMap):\n    rs = {\n        k[0]: k[1] for k in equalityMap\n    }\n    return line.translate(str.maketrans(rs))", "input": "'abab', [('a', 'b'), ('b', 'a')]", "output": "'baba'", "id": "sample_626"}
{"code": "def f(parts):\n    return list(dict(parts).values())", "input": "[('u', 1), ('s', 7), ('u', -5)]", "output": "[-5, 7]", "id": "sample_627"}
{"code": "def f(nums, delete):\n    nums.remove(delete)\n    return nums", "input": "[4, 5, 3, 6, 1], 5", "output": "[4, 3, 6, 1]", "id": "sample_628"}
{"code": "def f(text, dng):\n    if dng not in text:\n        return text\n    if text[-len(dng):] == dng:\n        return text[:-len(dng)]\n    return text[:-1] + f(text[:-2], dng)", "input": "'catNG', 'NG'", "output": "'cat'", "id": "sample_629"}
{"code": "def f(original, string):\n    temp = dict(original)\n    for a, b in string.items():\n        temp[b] = a\n    return temp", "input": "{1: -9, 0: -7}, {1: 2, 0: 3}", "output": "{1: -9, 0: -7, 2: 1, 3: 0}", "id": "sample_630"}
{"code": "def f(text, num):\n    req = num - len(text)\n    text = text.center(num, '*')\n    return text[:req // 2: -req // 2]", "input": "'a', 19", "output": "'*'", "id": "sample_631"}
{"code": "def f(list):\n    for i in range(len(list) - 1, 0, -1):\n        for j in range(i):\n            if list[j] > list[j + 1]:\n                list[j], list[j + 1] = list[j + 1], list[j]\n                list.sort()\n    return list", "input": "[63, 0, 1, 5, 9, 87, 0, 7, 25, 4]", "output": "[0, 0, 1, 4, 5, 7, 9, 25, 63, 87]", "id": "sample_632"}
{"code": "def f(array, elem):\n    array.reverse()\n    try:\n        found = array.index(elem)\n    finally:\n        array.reverse()\n    return found", "input": "[5, -3, 3, 2], 2", "output": "0", "id": "sample_633"}
{"code": "def f(input_string):\n    table = str.maketrans('aioe', 'ioua')\n    while 'a' in input_string or 'A' in input_string:\n        input_string = input_string.translate(table)\n    return input_string", "input": "'biec'", "output": "'biec'", "id": "sample_634"}
{"code": "def f(text):\n    valid_chars = ['-', '_', '+', '.', '/', ' ']\n    text = text.upper()\n    for char in text:\n        if char.isalnum() == False and char not in valid_chars:\n            return False\n    return True", "input": "\"9.twCpTf.H7 HPeaQ^ C7I6U,C:YtW\"", "output": "False", "id": "sample_635"}
{"code": "def f(d):\n    r = {}\n    while len(d) > 0:\n        r = {**r, **d}\n        del d[max(d.keys())]\n    return r", "input": "{ 3: 'A3', 1: 'A1', 2: 'A2' }", "output": "{3: 'A3', 1: 'A1', 2: 'A2'}", "id": "sample_636"}
{"code": "def f(text):\n    text = text.split(' ')\n    for t in text:\n        if not t.isnumeric():\n            return 'no'\n    return 'yes'", "input": "'03625163633 d'", "output": "'no'", "id": "sample_637"}
{"code": "def f(s, suffix):\n    if not suffix:\n        return s\n    while s.endswith(suffix):\n        s = s[:-len(suffix)]\n    return s", "input": "'ababa', 'ab'", "output": "'ababa'", "id": "sample_638"}
{"code": "def f(perc, full):\n    reply = \"\"\n    i = 0\n    while perc[i] == full[i] and i < len(full) and i < len(perc):\n        if perc[i] == full[i]:\n            reply += \"yes \"\n        else:\n            reply += \"no \"\n        i += 1\n    return reply", "input": "\"xabxfiwoexahxaxbxs\", \"xbabcabccb\"", "output": "'yes '", "id": "sample_639"}
{"code": "def f(text):\n    a = 0\n    if text[0] in text[1:]:\n        a += 1\n    for i in range(0, len(text)-1):\n        if text[i] in text[i+1:]:\n            a += 1\n    return a", "input": "\"3eeeeeeoopppppppw14film3oee3\"", "output": "18", "id": "sample_640"}
{"code": "def f(number):\n    return True if number.isdecimal() else False", "input": "'dummy33;d'", "output": "False", "id": "sample_641"}
{"code": "def f(text):\n    i = 0\n    while i < len(text) and text[i].isspace():\n        i+=1\n    if i == len(text):\n        return 'space'\n    return 'no'", "input": "\"     \"", "output": "'space'", "id": "sample_642"}
{"code": "def f(text, suffix):\n    if text.endswith(suffix):\n        text = text[:-1] + text[-1:].swapcase()\n    return text", "input": "'damdrodm', 'm'", "output": "'damdrodM'", "id": "sample_643"}
{"code": "def f(nums, pos):\n    s = slice(None)\n    if pos % 2:\n        s = slice(None, -1)\n    nums[s].reverse()\n    return nums", "input": "[6, 1], 3", "output": "[6, 1]", "id": "sample_644"}
{"code": "def f(nums, target):\n    if nums.count(0):\n        return 0\n    elif nums.count(target) < 3:\n        return 1\n    else:\n        return nums.index(target)", "input": "[1, 1, 1, 2], 3", "output": "1", "id": "sample_645"}
{"code": "def f(text, count):\n    for i in range(count):\n        text = ''.join(reversed(text))\n    return text", "input": "'aBc, ,SzY', 2", "output": "'aBc, ,SzY'", "id": "sample_646"}
{"code": "def f(text, chunks):\n    return text.splitlines(chunks)", "input": "'/alcm@ an)t//eprw)/e!/d\\nujv', 0", "output": "['/alcm@ an)t//eprw)/e!/d', 'ujv']", "id": "sample_647"}
{"code": "def f(list1, list2):\n    l = list1[:]\n    while len(l) > 0:\n        if l[-1] in list2:\n            l.pop()\n        else:\n            return l[-1]\n    return 'missing'", "input": "[0, 4, 5, 6], [13, 23, -5, 0]", "output": "6", "id": "sample_648"}
{"code": "def f(text, tabsize):\n    return '\\n'.join([\n    \tt.expandtabs(tabsize)\n        for t in text.split('\\n')\n    ])", "input": "\"\\tf9\\n\\tldf9\\n\\tadf9!\\n\\tf9?\", 1", "output": "' f9\\n ldf9\\n adf9!\\n f9?'", "id": "sample_649"}
{"code": "def f(string, substring):\n    while string.startswith(substring):\n        string = string[len(substring):len(string)]\n    return string", "input": "'', 'A'", "output": "''", "id": "sample_650"}
{"code": "def f(text, letter):\n    if letter.islower(): letter = letter.upper()\n    text = ''.join([letter if char == letter.lower() else char for char in text])\n    return text.capitalize()", "input": "'E wrestled evil until upperfeat', 'e'", "output": "'E wrestled evil until upperfeat'", "id": "sample_651"}
{"code": "def f(string):\n    if not string or not string[0].isnumeric:\n        return 'INVALID'\n    cur = 0\n    for i in range(len(string)):\n        cur = cur * 10 + int(string[i])\n    return str(cur)", "input": "'3'", "output": "'3'", "id": "sample_652"}
{"code": "def f(text, letter):\n    t = text\n    for alph in text:\n        t = t.replace(alph, \"\")\n    return len(t.split(letter))", "input": "\"c, c, c ,c, c\", \"c\"", "output": "1", "id": "sample_653"}
{"code": "def f(s, from_c, to_c):\n    table = s.maketrans(from_c, to_c)\n    return s.translate(table)", "input": "'aphid', 'i', '?'", "output": "'aph?d'", "id": "sample_654"}
{"code": "def f(s):\n    return s.replace('a', '').replace('r', '')", "input": "'rpaar'", "output": "'p'", "id": "sample_655"}
{"code": "def f(letters):\n    a = [] \n    for i in range(len(letters)):\n        if letters[i] in a:\n            return 'no'\n        a.append(letters[i]) \n    return 'yes'", "input": "['b', 'i', 'r', 'o', 's', 'j', 'v', 'p']", "output": "'yes'", "id": "sample_656"}
{"code": "def f(text):\n    for punct in '!.?,:;':\n        if text.count(punct) > 1:\n            return 'no'\n        if text.endswith(punct):\n            return 'no'\n    return text.title()", "input": "\"djhasghasgdha\"", "output": "'Djhasghasgdha'", "id": "sample_657"}
{"code": "def f(d, get_ary):\n    result = []\n    for key in get_ary:\n        result.append(d.get(key))\n    return result", "input": "{3: \"swims like a bull\"}, [3, 2, 'c', True, 5]", "output": "['swims like a bull', None, None, None, None]", "id": "sample_658"}
{"code": "def f(bots):\n    clean = []\n    for username in bots:\n        if not username.isupper():\n            clean.append(username[:2] + username[-3:])\n    return len(clean)", "input": "['yR?TAJhIW?n', 'o11BgEFDfoe', 'KnHdn2vdEd', 'wvwruuqfhXbGis']", "output": "4", "id": "sample_659"}
{"code": "def f(num):\n    initial = [1]\n    total = initial\n    for _ in range(num):\n        total = [1] + [x+y for x, y in zip(total, total[1:])]\n        initial.append(total[-1])\n    return sum(initial)", "input": "3", "output": "4", "id": "sample_660"}
{"code": "def f(letters, maxsplit):\n    return ''.join(letters.split()[-maxsplit:])", "input": "'elrts,SS ee', 6", "output": "'elrts,SSee'", "id": "sample_661"}
{"code": "def f(values):\n    names = ['Pete', 'Linda', 'Angela']\n    names.extend(values)\n    names.sort()\n    return names", "input": "['Dan', 'Joe', 'Dusty']", "output": "['Angela', 'Dan', 'Dusty', 'Joe', 'Linda', 'Pete']", "id": "sample_662"}
{"code": "def f(container, cron):\n    if not cron in container:\n        return container\n    pref = container[:container.index(cron)].copy()\n    suff = container[container.index(cron) + 1:].copy()\n    return pref + suff", "input": "[], 2", "output": "[]", "id": "sample_663"}
{"code": "def f(tags):\n    resp = \"\"\n    for key in tags:\n        resp += key + \" \"\n    return resp", "input": "{\"3\":\"3\",\"4\":\"5\"}", "output": "'3 4 '", "id": "sample_664"}
{"code": "def f(chars):\n    s = \"\"\n    for ch in chars:\n        if chars.count(ch) % 2 == 0:\n            s += ch.upper()\n        else:\n            s += ch\n    return s", "input": "\"acbced\"", "output": "'aCbCed'", "id": "sample_665"}
{"code": "def f(d1, d2):\n    mmax = 0\n    for k1 in d1:\n        if p := len(d1[k1])+len(d2.get(k1, [])):\n            if p > mmax:\n                mmax = p\n    return mmax", "input": "{ 0: [], 1: [] }, { 0: [0, 0, 0, 0], 2: [2, 2, 2] }", "output": "4", "id": "sample_666"}
{"code": "def f(text):\n    new_text = []\n    for i in range(len(text) // 3):\n        new_text.append(f\"< {text[i * 3: i * 3 + 3]} level={i} >\")\n    last_item = text[len(text) // 3 * 3:]\n    new_text.append(f\"< {last_item} level={len(text) // 3} >\")\n    return new_text", "input": "'C7'", "output": "['< C7 level=0 >']", "id": "sample_667"}
{"code": "def f(text):\n    return text[-1] + text[:-1]", "input": "'hellomyfriendear'", "output": "'rhellomyfriendea'", "id": "sample_668"}
{"code": "def f(t):\n    a, sep, b = t.rpartition('-')\n    if len(b) == len(a):\n        return 'imbalanced'\n    return a + b.replace(sep, '')", "input": "\"fubarbaz\"", "output": "'fubarbaz'", "id": "sample_669"}
{"code": "def f(a, b):\n    d = dict(zip(a, b))\n    a.sort(key=d.get, reverse=True)\n    return [d.pop(x) for x in a]", "input": "['12','ab'], [2,2]", "output": "[2, 2]", "id": "sample_670"}
{"code": "def f(text, char1, char2):\n    t1a = []\n    t2a = []\n    for i in range(len(char1)):\n        t1a.append(char1[i])\n        t2a.append(char2[i])\n    t1 = text.maketrans(dict(zip(t1a, t2a)))\n    return text.translate(t1)", "input": "\"ewriyat emf rwto segya\", \"tey\", \"dgo\"", "output": "'gwrioad gmf rwdo sggoa'", "id": "sample_671"}
{"code": "def f(text, position, value):\n    length = len(text)\n    index = (position % (length + 2)) - 1\n    if index >= length or index < 0:\n        return text\n    text[index] = value\n    return ''.join(text)", "input": "\"1zd\", 0, 'm'", "output": "'1zd'", "id": "sample_672"}
{"code": "def f(string):\n    if string.isupper():\n        return string.lower()\n    elif string.islower():\n        return string.upper()\n    return string", "input": "\"cA\"", "output": "'cA'", "id": "sample_673"}
{"code": "def f(text):\n    ls = list(text)\n    for x in range(len(ls)-1, -1, -1):\n        if len(ls) <= 1: break\n        if ls[x] not in 'zyxwvutsrqponmlkjihgfedcba': ls.pop(ls[x])\n    return ''.join(ls)", "input": "'qq'", "output": "'qq'", "id": "sample_674"}
{"code": "def f(nums, sort_count):\n    nums.sort()\n    return nums[:sort_count]", "input": "[1, 2, 2, 3, 4, 5], 1", "output": "[1]", "id": "sample_675"}
{"code": "def f(text, tab_size):\n    return text.replace('\\t', ' '*tab_size)", "input": "'a', 100", "output": "'a'", "id": "sample_676"}
{"code": "def f(text, length):\n    length = -length if length < 0 else length\n    output = ''\n    for idx in range(length):\n        if text[idx % len(text)] != ' ':\n            output += text[idx % len(text)]\n        else:\n            break\n    return output", "input": "'I got 1 and 0.', 5", "output": "'I'", "id": "sample_677"}
{"code": "def f(text):\n    freq = dict()\n    for c in text.lower():\n        if c in freq:\n            freq[c] += 1\n        else:\n            freq[c] = 1\n    return freq", "input": "\"HI\"", "output": "{'h': 1, 'i': 1}", "id": "sample_678"}
{"code": "def f(text):\n    if text == '':\n        return False\n    first_char = text[0]\n    if text[0].isdigit():\n        return False\n    for last_char in text:\n        if (last_char != '_') and not last_char.isidentifier():\n            return False\n    return True", "input": "'meet'", "output": "True", "id": "sample_679"}
{"code": "def f(text):\n    letters = ''\n    for i in range(len(text)):\n        if text[i].isalnum():\n            letters += text[i]\n    return letters", "input": "\"we@32r71g72ug94=(823658*!@324\"", "output": "'we32r71g72ug94823658324'", "id": "sample_680"}
{"code": "def f(array, ind, elem):\n    array.insert(-5 if ind < 0 else len(array) if ind > len(array) else ind + 1, elem)\n    return array", "input": "[1, 5, 8, 2, 0, 3], 2, 7", "output": "[1, 5, 8, 7, 2, 0, 3]", "id": "sample_681"}
{"code": "def f(text, length, index):\n    ls = text.rsplit(None, index)\n    return '_'.join([l[:length] for l in ls])", "input": "'hypernimovichyp', 2, 2", "output": "'hy'", "id": "sample_682"}
{"code": "def f(dict1, dict2):\n    result = dict1.copy()\n    result.update([(__, dict2[__]) for __ in dict2])\n    return result", "input": "{'disface': 9, 'cam': 7}, {'mforce': 5}", "output": "{'disface': 9, 'cam': 7, 'mforce': 5}", "id": "sample_683"}
{"code": "def f(text):\n    trans = str.maketrans('\"\\'><', '9833')\n    return text.translate(trans)", "input": "\"Transform quotations\\\"\\nnot into numbers.\"", "output": "'Transform quotations9\\nnot into numbers.'", "id": "sample_684"}
{"code": "def f(array, elem):\n    return array.count(elem) + elem", "input": "[1, 1, 1], -2", "output": "-2", "id": "sample_685"}
{"code": "def f(d, l):\n\tnew_d = {}\n\n\tfor k in l:\n\t\tif d.get(k) is not None:\n\t\t\tnew_d[k] = d[k]\n\n\treturn new_d.copy()", "input": "{\"lorem ipsum\" : 12, \"dolor\" : 23}, [\"lorem ipsum\", \"dolor\"]", "output": "{'lorem ipsum': 12, 'dolor': 23}", "id": "sample_686"}
{"code": "def f(text):\n    t = list(text)\n    t.pop(len(t) // 2)\n    t.append(text.lower())\n    return ':'.join([c for c in t])", "input": "'Rjug nzufE'", "output": "'R:j:u:g: :z:u:f:E:rjug nzufe'", "id": "sample_687"}
{"code": "def f(nums):\n    l = []\n    for i in nums:\n        if i not in l:\n            l.append(i)\n    return l", "input": "[3, 1, 9, 0, 2, 0, 8]", "output": "[3, 1, 9, 0, 2, 8]", "id": "sample_688"}
{"code": "def f(arr):\n    count = len(arr)\n    sub = arr.copy()\n    for i in range(0, count, 2):\n        sub[i] *= 5\n    return sub", "input": "[-3, -6, 2, 7]", "output": "[-15, -6, 10, 7]", "id": "sample_689"}
{"code": "def f(n):\n    if str(n).find('.') != -1:\n        return str(int(n)+2.5)\n    return str(n)", "input": "'800'", "output": "'800'", "id": "sample_690"}
{"code": "def f(text, suffix):\n    if suffix and suffix[-1] in text:\n        return f(text.rstrip(suffix[-1]), suffix[:-1])\n    else:\n        return text", "input": "'rpyttc', 'cyt'", "output": "'rpytt'", "id": "sample_691"}
{"code": "def f(array):\n    a = []\n    array.reverse()\n    for i in range(len(array)):\n        if array[i] != 0:\n            a.append(array[i])\n    a.reverse()\n    return a", "input": "[]", "output": "[]", "id": "sample_692"}
{"code": "def f(text):\n    n = int(text.find('8'))\n    return 'x0'*n", "input": "\"sa832d83r xd 8g 26a81xdf\"", "output": "'x0x0'", "id": "sample_693"}
{"code": "def f(d):\n    i = len(d) - 1\n    key = list(d.keys())[i]\n    d.pop(key, None)\n    return key, d", "input": "dict(e=1, d=2, c=3)", "output": "('c', {'e': 1, 'd': 2})", "id": "sample_694"}
{"code": "def f(d):\n    result = {}\n    for ki, li in d.items():\n        result.update({ki: []})\n        for kj, dj in enumerate(li):\n            result[ki].append({})\n            for kk, l in dj.items():\n                result[ki][kj][kk] = l.copy()\n    return result", "input": "{}", "output": "{}", "id": "sample_695"}
{"code": "def f(text):\n    s = 0\n    for i in range(1, len(text)):\n        s += len(text.rpartition(text[i])[0])\n    return s", "input": "'wdj'", "output": "3", "id": "sample_696"}
{"code": "def f(s, sep):\n    sep_index = s.find(sep)\n    prefix = s[:sep_index]\n    middle = s[sep_index:sep_index + len(sep)]\n    right_str = s[sep_index + len(sep):]\n    return prefix, middle, right_str", "input": "\"not it\", \"\"", "output": "('', '', 'not it')", "id": "sample_697"}
{"code": "def f(text):\n    return ''.join(x for x in text if x != ')')", "input": "('(((((((((((d))))))))).))))(((((')", "output": "'(((((((((((d.((((('", "id": "sample_698"}
{"code": "def f(text, elem):\n    if elem != '':\n        while text.startswith(elem):\n            text = text.replace(elem, '')\n        while elem.startswith(text):\n            elem = elem.replace(text, '')\n    return [elem, text]", "input": "\"some\", \"1\"", "output": "['1', 'some']", "id": "sample_699"}
{"code": "def f(text):\n    return len(text) - text.count('bot')", "input": "\"Where is the bot in this world?\"", "output": "30", "id": "sample_700"}
{"code": "def f(stg, tabs):\n    for tab in tabs:\n        stg = stg.rstrip(tab)\n    return stg", "input": "'31849 let it!31849 pass!', ('3','1','8',' ','1','9','2','d')", "output": "'31849 let it!31849 pass!'", "id": "sample_701"}
{"code": "def f(nums):\n    count = len(nums)\n    for i in range(len(nums) - 1, -1, -1):\n        nums.insert(i, nums.pop(0))\n    return nums", "input": "[0, -5, -4]", "output": "[-4, -5, 0]", "id": "sample_702"}
{"code": "def f(text, char):\n    count = text.count(char*2)\n    return text[count:]", "input": "'vzzv2sg', 'z'", "output": "'zzv2sg'", "id": "sample_703"}
{"code": "def f(s, n, c):\n    width = len(c)*n\n    for _ in range(width - len(s)):\n        s = c + s\n    return s", "input": "'.', 0, '99'", "output": "'.'", "id": "sample_704"}
{"code": "def f(cities, name):\n    if not name:\n        return cities\n    if name and name != 'cities':\n        return []\n    return [name + city for city in cities]", "input": "['Sydney', 'Hong Kong', 'Melbourne', 'Sao Paolo', 'Istanbul', 'Boston'], 'Somewhere '", "output": "[]", "id": "sample_705"}
{"code": "def f(r, w):\n    a = []\n    if r[0] == w[0] and w[-1] == r[-1]:\n        a.append(r)\n        a.append(w)\n    else:\n        a.append(w)\n        a.append(r)\n    return a", "input": "\"ab\", \"xy\"", "output": "['xy', 'ab']", "id": "sample_706"}
{"code": "def f(text, position):\n    length = len(text)\n    index = position % (length + 1)\n    if position < 0 or index < 0:\n        index = -1\n    new_text = list(text)\n    new_text.pop(index)\n    return ''.join(new_text)", "input": "'undbs l', 1", "output": "'udbs l'", "id": "sample_707"}
{"code": "def f(string):\n    l = list(string)\n    for i in reversed(range(len(l))):\n        if l[i] != ' ':\n            break\n        l.pop(i)\n    return ''.join(l)", "input": "'    jcmfxv     '", "output": "'    jcmfxv'", "id": "sample_708"}
{"code": "def f(text):\n    my_list = text.split()\n    my_list.sort(reverse=True)\n    return ' '.join(my_list)", "input": "'a loved'", "output": "'loved a'", "id": "sample_709"}
{"code": "def f(playlist, liker_name, song_index):\n    playlist[liker_name] = playlist.get(liker_name, [])\n    playlist[liker_name].append(song_index)\n    return playlist", "input": "{'aki': ['1', '5']}, 'aki', '2'", "output": "{'aki': ['1', '5', '2']}", "id": "sample_710"}
{"code": "def f(text):\n    return text.replace('\\n', '\\t')", "input": "'apples\\n\\t\\npears\\n\\t\\nbananas'", "output": "'apples\\t\\t\\tpears\\t\\t\\tbananas'", "id": "sample_711"}
{"code": "def f(text):\n    created = []\n    for line in text.splitlines():\n        if line == '':\n            break\n        created.append(list(list(line.rstrip())[::-1][flush]))\n    return created[::-1]\n\nflush = 0", "input": "'A(hiccup)A'", "output": "[['A']]", "id": "sample_712"}
{"code": "def f(text, char):\n    if char in text:\n        text = [t.strip() for t in text.split(char) if t]\n        if len(text) > 1:\n            return True\n    return False", "input": "'only one line', ' '", "output": "True", "id": "sample_713"}
{"code": "def f(array):\n    array.reverse()\n    array.clear()\n    array.extend('x'*len(array))\n    array.reverse()\n    return array", "input": "[3, -2, 0]", "output": "[]", "id": "sample_714"}
{"code": "def f(text, char):\n    return text.count(char) % 2 != 0", "input": "'abababac', 'a'", "output": "False", "id": "sample_715"}
{"code": "def f(nums):\n    count = len(nums)\n    while len(nums) > (count//2):\n        nums.clear()\n    return nums", "input": "[2, 1, 2, 3, 1, 6, 3, 8]", "output": "[]", "id": "sample_716"}
{"code": "def f(text):\n    (k, l) = (0, len(text) - 1)\n    while not text[l].isalpha():\n        l -= 1\n    while not text[k].isalpha():\n        k += 1\n    if k != 0 or l != len(text) - 1:\n        return text[k: l+1]\n    else:\n        return text[0]", "input": "\"timetable, 2mil\"", "output": "'t'", "id": "sample_717"}
{"code": "def f(text):\n    t = text\n    for i in text:\n        text = text.replace(i, '')\n    return str(len(text)) + t", "input": "'ThisIsSoAtrocious'", "output": "'0ThisIsSoAtrocious'", "id": "sample_718"}
{"code": "def f(code):\n    lines = code.split(']')\n    result = []\n    level = 0\n    for line in lines:\n        result.append(line[0] + ' ' + '  ' * level + line[1:])\n        level += line.count('{') - line.count('}')\n    return '\\n'.join(result)", "input": "\"if (x) {y = 1;} else {z = 1;}\"", "output": "'i f (x) {y = 1;} else {z = 1;}'", "id": "sample_719"}
{"code": "def f(items, item):\n    while items[-1] == item:\n        items.pop()\n    items.append(item)\n    return len(items)", "input": "'bfreratrrbdbzagbretaredtroefcoiqrrneaosf'.split('-'), 'n'", "output": "2", "id": "sample_720"}
{"code": "def f(nums):\n    count = len(nums)\n    for num in range(2, count):\n        nums.sort()\n    return nums", "input": "[-6, -5, -7, -8, 2]", "output": "[-8, -7, -6, -5, 2]", "id": "sample_721"}
{"code": "def f(text):\n    out = \"\"\n    for i in range(len(text)):\n        if text[i].isupper():\n            out += text[i].lower()\n        else:\n            out += text[i].upper()\n    return out", "input": "',wPzPppdl/'", "output": "',WpZpPPDL/'", "id": "sample_722"}
{"code": "def f(text, separator):\n    splitted = text.splitlines()\n    if separator:\n        return [' '.join(s) for s in splitted]\n    else:\n        return splitted", "input": "'dga nqdk\\rull qcha kl', 1", "output": "['d g a   n q d k', 'u l l   q c h a   k l']", "id": "sample_723"}
{"code": "def f(text, function):\n    cites = [len(text[text.index(function) + len(function):])]\n    for char in text:\n        if char == function:\n            cites.append(len(text[text.index(function) + len(function):]))\n    return cites", "input": "\"010100\", \"010\"", "output": "[3]", "id": "sample_724"}
{"code": "def f(text):\n    result_list = ['3', '3', '3', '3']\n    if result_list:\n        result_list.clear()\n    return len(text)", "input": "\"mrq7y\"", "output": "5", "id": "sample_725"}
{"code": "def f(text):\n    ws = 0\n    for s in text:\n        if s.isspace():\n            ws += 1\n    return ws, len(text)", "input": "\"jcle oq wsnibktxpiozyxmopqkfnrfjds\"", "output": "(2, 34)", "id": "sample_726"}
{"code": "def f(numbers, prefix):\n    return sorted(n[len(prefix):] if (len(n) > len(prefix) and n.startswith(prefix)) else n\n                  for n in numbers)", "input": "['ix', 'dxh', 'snegi', 'wiubvu'], ''", "output": "['dxh', 'ix', 'snegi', 'wiubvu']", "id": "sample_727"}
{"code": "def f(text):\n    result = []\n    for i, ch in enumerate(text):\n        if ch == ch.lower():\n            continue\n        if len(text) - 1 - i < text.rindex(ch.lower()):\n            result.append(ch)\n    return ''.join(result)", "input": "'ru'", "output": "''", "id": "sample_728"}
{"code": "def f(s1,s2):\n    res = []\n    i = s1.rfind(s2)\n    while i != -1:\n        res.append(i+len(s2)-1)\n        i = s1.rfind(s2, 0, i)\n    return res", "input": "'abcdefghabc', 'abc'", "output": "[10, 2]", "id": "sample_729"}
{"code": "def f(text):\n    m = 0\n    cnt = 0\n    for i in text.split():\n        if len(i) > m:\n            cnt += 1\n            m = len(i)\n    return cnt", "input": "\"wys silak v5 e4fi rotbi fwj 78 wigf t8s lcl\"", "output": "2", "id": "sample_730"}
{"code": "def f(text, use):\n    return text.replace(use, '')", "input": "'Chris requires a ride to the airport on Friday.', 'a'", "output": "'Chris requires  ride to the irport on Fridy.'", "id": "sample_731"}
{"code": "def f(char_freq):\n    result = {}\n    for k, v in char_freq.copy().items():\n        result[k] = v // 2\n    return result", "input": "{'u': 20, 'v': 5, 'b': 7, 'w': 3, 'x': 3}", "output": "{'u': 10, 'v': 2, 'b': 3, 'w': 1, 'x': 1}", "id": "sample_732"}
{"code": "def f(text):\n    length = len(text) // 2\n    left_half = text[:length]\n    right_half = text[length:][::-1]\n    return left_half + right_half", "input": "'n'", "output": "'n'", "id": "sample_733"}
{"code": "def f(nums):\n    for i in range(len(nums) - 2, -1, -1):\n        if nums[i] % 2 == 0:\n            nums.remove(nums[i])\n    return nums", "input": "[5, 3, 3, 7]", "output": "[5, 3, 3, 7]", "id": "sample_734"}
{"code": "def f(sentence):\n    if sentence == '':\n        return ''\n    sentence = sentence.replace('(', '')\n    sentence = sentence.replace(')', '')\n    return sentence.capitalize().replace(' ', '')", "input": "'(A (b B))'", "output": "'Abb'", "id": "sample_735"}
{"code": "def f(text, insert):\n    whitespaces = {'\\t', '\\r', '\\v', ' ', '\\f', '\\n'}\n    clean = ''\n    for char in text:\n        if char in whitespaces:\n            clean += insert\n        else:\n            clean += char\n    return clean", "input": "'pi wa', 'chi'", "output": "'pichiwa'", "id": "sample_736"}
{"code": "def f(nums):\n    counts = 0\n    for i in nums:\n        if str(i).isdecimal():\n            if counts == 0:\n                counts += 1\n    return counts", "input": "[0, 6, 2, -1, -2]", "output": "1", "id": "sample_737"}
{"code": "def f(text, characters):\n    for i in range(len(characters)):\n        text = text.rstrip(characters[i::len(characters)])\n    return text", "input": "\"r;r;r;r;r;r;r;r;r\", \"x.r\"", "output": "'r;r;r;r;r;r;r;r;'", "id": "sample_738"}
{"code": "def f(st, pattern):\n    for p in pattern:\n        if not st.startswith(p): return False\n        st = st[len(p):]\n    return True", "input": "'qwbnjrxs', ['jr', 'b', 'r', 'qw']", "output": "False", "id": "sample_739"}
{"code": "def f(plot, delin):\n    if delin in plot:\n        split = plot.index(delin)\n        first = plot[:split]\n        second = plot[split + 1:]\n        return first + second\n    else:\n        return plot", "input": "[1, 2, 3, 4], 3", "output": "[1, 2, 4]", "id": "sample_740"}
{"code": "def f(nums, p):\n    prev_p = p - 1\n    if prev_p < 0: prev_p = len(nums) - 1\n    return nums[prev_p]", "input": "[6, 8, 2, 5, 3, 1, 9, 7], 6", "output": "1", "id": "sample_741"}
{"code": "def f(text):\n    b = True\n    for x in text:\n        if x.isdigit():\n            b = True\n        else:\n            b = False\n            break\n    return b", "input": "\"-1-3\"", "output": "False", "id": "sample_742"}
{"code": "def f(text):\n    string_a, string_b = text.split(',')\n    return -(len(string_a) + (len(string_b)))", "input": "'dog,cat'", "output": "-6", "id": "sample_743"}
{"code": "def f(text, new_ending):\n    result = list(text)\n    result.extend(new_ending)\n    return ''.join(result)", "input": "'jro', 'wdlp'", "output": "'jrowdlp'", "id": "sample_744"}
{"code": "def f(address):\n    suffix_start = address.index('@') + 1\n    if address[suffix_start:].count('.') > 1:\n        address = address.removesuffix('.'.join(address.split('@')[1].split('.')[:2]))\n    return address", "input": "'<EMAIL>'", "output": "'<EMAIL>'", "id": "sample_745"}
{"code": "def f(dct):\n    values = dct.values()\n    result = {}\n    for value in values:\n        item = value.split('.')[0]+'@pinc.uk'\n        result[value] = item\n    return result", "input": "{}", "output": "{}", "id": "sample_746"}
{"code": "def f(text):\n    if text == '42.42':\n        return True\n    for i in range(3, len(text) - 3):\n        if text[i] == '.' and text[i - 3:].isdigit() and text[:i].isdigit():\n            return True\n    return False", "input": "\"123E-10\"", "output": "False", "id": "sample_747"}
{"code": "def f(d):\n    i = iter(d.items())\n    return next(i), next(i)", "input": "{'a': 123, 'b': 456, 'c': 789}", "output": "(('a', 123), ('b', 456))", "id": "sample_748"}
{"code": "def f(text, width):\n    result = \"\"\n    lines = text.split('\\n')\n    for l in lines:\n        result += l.center(width)\n        result += '\\n'\n\n    # Remove the very last empty line\n    result = result[:-1]\n    return result", "input": "'l\\nl', 2", "output": "'l \\nl '", "id": "sample_749"}
{"code": "def f(char_map, text):\n    new_text = ''\n    for ch in text:\n        val = char_map.get(ch)\n        if val is None:\n            new_text += ch\n        else:\n            new_text += val\n    return new_text", "input": "{}, 'hbd'", "output": "'hbd'", "id": "sample_750"}
{"code": "def f(text, char, min_count):\n    count = text.count(char)\n    if count < min_count:\n        return text.swapcase()\n    return text", "input": "\"wwwwhhhtttpp\", 'w', 3", "output": "'wwwwhhhtttpp'", "id": "sample_751"}
{"code": "def f(s, amount):\n    return (amount - len(s)) * 'z' + s", "input": "'abc', 8", "output": "'zzzzzabc'", "id": "sample_752"}
{"code": "def f(bag):\n    values = list(bag.values())\n    tbl = {}\n    for v in range(100):\n        if v in values:\n            tbl[v] = values.count(v)\n    return tbl", "input": "{0: 0, 1: 0, 2: 0, 3: 0, 4: 0}", "output": "{0: 5}", "id": "sample_753"}
{"code": "def f(nums):\n    nums = ['{0:{fill}>{width}}'.format(val, **{'fill': '0', 'width': nums[0]}) for val in nums[1:]]\n    return [str(val) for val in nums]", "input": "['1', '2', '2', '44', '0', '7', '20257']", "output": "['2', '2', '44', '0', '7', '20257']", "id": "sample_754"}
{"code": "def f(replace, text, hide):\n    while hide in text:\n        replace += 'ax'\n        text = text.replace(hide, replace, 1)\n    return text", "input": "'###', \"ph>t#A#BiEcDefW#ON#iiNCU\", '.'", "output": "'ph>t#A#BiEcDefW#ON#iiNCU'", "id": "sample_755"}
{"code": "def f(text):\n    if text.isalnum() and all(i.isdigit() for i in text):\n        return 'integer'\n    return 'string'", "input": "''", "output": "'string'", "id": "sample_756"}
{"code": "def f(text, char, replace):\n    return text.replace(char, replace)", "input": "'a1a8', '1', 'n2'", "output": "'an2a8'", "id": "sample_757"}
{"code": "def f(nums):\n    if nums[::-1] == nums:\n        return True\n    return False", "input": "[0, 3, 6, 2]", "output": "False", "id": "sample_758"}
{"code": "def f(text, sub):\n    index = []\n    starting = 0\n    while starting != -1:\n        starting = text.find(sub, starting)\n        if starting != -1:\n            index.append(starting)\n            starting += len(sub)\n    return index", "input": "'egmdartoa', 'good'", "output": "[]", "id": "sample_759"}
{"code": "def f(d):\n    l = []\n    while len(d) > 0:\n        key = d.popitem()[0]\n        l.append(key)\n    return l;", "input": "dict(f = 1, h = 2, j = 3, k = 4)", "output": "['k', 'j', 'h', 'f']", "id": "sample_760"}
{"code": "def f(array):\n    output = array.copy()\n    output[0::2] = output[-1::-2]\n    output.reverse()\n    return output", "input": "[]", "output": "[]", "id": "sample_761"}
{"code": "def f(text):\n    text = text.lower()\n    capitalize = text.capitalize()\n    return text[:1] + capitalize[1:]", "input": "'this And cPanel'", "output": "'this and cpanel'", "id": "sample_762"}
{"code": "def f(values, text, markers):\n    return text.rstrip(values).rstrip(markers)", "input": "'2Pn', 'yCxpg2C2Pny2', ''", "output": "'yCxpg2C2Pny'", "id": "sample_763"}
{"code": "def f(text, old, new):\n    text2 = text.replace(old, new)\n    old2 = old[::-1]\n    while old2 in text2:\n        text2 = text2.replace(old2, new)\n    return text2", "input": "\"some test string\", \"some\", \"any\"", "output": "'any test string'", "id": "sample_764"}
{"code": "def f(text):\n    return sum(1 for c in text if c.isdigit())", "input": "'so456'", "output": "3", "id": "sample_765"}
{"code": "def f(values, value):\n    length = len(values)\n    new_dict = dict.fromkeys(values, value)\n    new_dict[''.join(sorted(values))] = value * 3\n    return new_dict", "input": "['0','3'], 117", "output": "{'0': 117, '3': 117, '03': 351}", "id": "sample_766"}
{"code": "def f(text):\n    a = text.strip().split(' ')\n    for i in range(len(a)):\n        if a[i].isdigit() is False:\n            return '-'\n    return \" \".join(a)", "input": "\"d khqw whi fwi bbn 41\"", "output": "'-'", "id": "sample_767"}
{"code": "def f(s, o):\n    if s.startswith(o):\n        return s\n    return o + f(s, o[-2::-1])", "input": "'abba', 'bab'", "output": "'bababba'", "id": "sample_768"}
{"code": "def f(text):\n    text_list = [char for char in text]\n    for i, char in enumerate(text_list):\n        text_list[i] = char.swapcase()\n    return ''.join(text_list)", "input": "'akA?riu'", "output": "'AKa?RIU'", "id": "sample_769"}
{"code": "def f(line, char):\n    count = line.count(char)\n    for i in range(count+1, 0, -1):\n        line = line.center(len(line)+i // len(char), char)\n    return line", "input": "'$78'.upper(), '$'", "output": "'$$78$$'", "id": "sample_770"}
{"code": "def f(items):\n    items = list(items)\n    odd_positioned = []\n    while len(items) > 0:\n        position = items.index(min(items))\n        items.pop(position)\n        item = items.pop(position)\n        odd_positioned.append(item)\n    return odd_positioned", "input": "(1, 2, 3, 4, 5, 6, 7, 8)", "output": "[2, 4, 6, 8]", "id": "sample_771"}
{"code": "def f(phrase):\n    result = ''\n    for i in phrase:\n        if not i.islower():\n            result += i\n    return result", "input": "'serjgpoDFdbcA.'", "output": "'DFA.'", "id": "sample_772"}
{"code": "def f(nums, n):\n    return nums.pop(n)", "input": "[-7, 3, 1, -1, -1, 0, 4], 6", "output": "4", "id": "sample_773"}
{"code": "def f(num, name):\n    f_str = 'quiz leader = {}, count = {}'\n    return f_str.format(name, num)", "input": "23, 'Cornareti'", "output": "'quiz leader = Cornareti, count = 23'", "id": "sample_774"}
{"code": "def f(nums):\n    count = len(nums)\n    for i in range(0, count // 2):\n        nums[i], nums[count-i-1] = nums[count-i-1], nums[i]\n    return nums", "input": "[2, 6, 1, 3, 1]", "output": "[1, 3, 1, 6, 2]", "id": "sample_775"}
{"code": "def f(dictionary):\n    a = dictionary.copy()\n    for key in a:\n        if key%2 != 0:\n            del a[key]\n            a['$'+str(key)] = a[key]\n    return a", "input": "{}", "output": "{}", "id": "sample_776"}
{"code": "def f(names, excluded):\n    excluded = excluded\n    for i in range(len(names)):\n        if excluded in names[i]:\n            names[i] = names[i].replace(excluded, \"\")\n    return names", "input": "[\"avc  a .d e\"], \"\"", "output": "['avc  a .d e']", "id": "sample_777"}
{"code": "def f(prefix, text):\n    if text.startswith(prefix):\n        return text\n    else:\n        return prefix + text", "input": "'mjs', 'mjqwmjsqjwisojqwiso'", "output": "'mjsmjqwmjsqjwisojqwiso'", "id": "sample_778"}
{"code": "def f(text):\n    values = text.split()\n    return '${first}y, ${second}x, ${third}r, ${fourth}p' % dict({\n        'first': values[0],\n        'second': values[1],\n        'third': values[2],\n        'fourth': values[3]\n    })", "input": "'python ruby c javascript'", "output": "'${first}y, ${second}x, ${third}r, ${fourth}p'", "id": "sample_779"}
{"code": "def f(ints):\n    counts = [0] * 301\n\n    for i in ints:\n        counts[i] += 1\n\n    r = []\n    for i in range(len(counts)):\n        if counts[i] >= 3:\n            r.append(str(i))\n    counts.clear()\n    return ' '.join(r)", "input": "[2, 3, 5, 2, 4, 5, 2, 89]", "output": "'2'", "id": "sample_780"}
{"code": "def f(s, ch):\n    if ch not in s:\n        return ''\n    s = s.partition(ch)[2][::-1]\n    for i in range(len(s)):\n        s = s.partition(ch)[2][::-1]\n    return s", "input": "'shivajimonto6', '6'", "output": "''", "id": "sample_781"}
{"code": "def f(input):\n    for char in input:\n        if char.isupper():\n            return False\n    return True", "input": "\"a j c n x X k\"", "output": "False", "id": "sample_782"}
{"code": "def f(text, comparison):\n    length = len(comparison)\n    if length <= len(text):\n        for i in range(length):\n            if comparison[length - i - 1] != text[len(text) - i - 1]:\n                return i\n    return length", "input": "\"managed\", \"\"", "output": "0", "id": "sample_783"}
{"code": "def f(key, value):\n    dict_ = {key: value}\n    return dict.popitem(dict_)", "input": "'read', 'Is'", "output": "('read', 'Is')", "id": "sample_784"}
{"code": "def f(n):\n    streak = ''\n    for c in str(n):\n        streak += c.ljust(int(c) * 2)\n    return streak", "input": "1", "output": "'1 '", "id": "sample_785"}
{"code": "def f(text, letter):\n    if letter in text:\n        start = text.index(letter)\n        return text[start + 1:] + text[:start + 1]\n    return text", "input": "'19kefp7', '9'", "output": "'kefp719'", "id": "sample_786"}
{"code": "def f(text):\n    if len(text) == 0:\n        return ''\n    text = text.lower()\n    return text[0].upper() + text[1:]", "input": "'xzd'", "output": "'Xzd'", "id": "sample_787"}
{"code": "def f(text, suffix):\n    if suffix.startswith(\"/\"):\n        return text + suffix[1:]\n    return text", "input": "'hello.txt', '/'", "output": "'hello.txt'", "id": "sample_788"}
{"code": "def f(text, n):\n    if n < 0 or len(text) <= n:\n        return text\n    result = text[0 : n]\n    i = len(result) - 1\n    while i >= 0:\n        if result[i] != text[i]:\n            break\n        i -= 1\n    return text[0 : i + 1]", "input": "'bR', -1", "output": "'bR'", "id": "sample_789"}
{"code": "def f(d):\n    r = {\n        'c': d.copy(),\n        'd': d.copy()\n    }\n    return (r['c'] is r['d'], r['c'] == r['d'])", "input": "{'i': 1, 'love': 'parakeets'}", "output": "(False, True)", "id": "sample_790"}
{"code": "def f(integer, n):\n    i = 1\n    text = str(integer)\n    while (i+len(text) < n):\n        i += len(text)\n    return text.zfill(i+len(text))", "input": "8999,2", "output": "'08999'", "id": "sample_791"}
{"code": "def f(l1, l2):\n    if len(l1) != len(l2):\n        return {}\n    return dict.fromkeys(l1, l2)", "input": "['a', 'b'], ['car', 'dog']", "output": "{'a': ['car', 'dog'], 'b': ['car', 'dog']}", "id": "sample_792"}
{"code": "def f(list, start, end):\n    count = 0\n    for i in range(start, end):\n        for j in range(i, end):\n            if list[i] != list[j]:\n                count += 1\n    return count", "input": "[1, 2, 4, 3, 2, 1], 0, 3", "output": "3", "id": "sample_793"}
{"code": "def f(line):\n    a = []\n    for c in line:\n        if c.isalnum():\n            a.append(c)\n    return ''.join(a)", "input": "\"\\\"\\\\%$ normal chars $%~ qwet42'\"", "output": "'normalcharsqwet42'", "id": "sample_794"}
{"code": "def f(text):\n    return text.title().replace('Io', 'io')", "input": "'Fu,ux zfujijabji pfu.'", "output": "'Fu,Ux Zfujijabji Pfu.'", "id": "sample_795"}
{"code": "def f(str,toget):\n    if str.startswith(toget): return str[len(toget):]\n    else: return str", "input": "'fnuiyh', 'ni'", "output": "'fnuiyh'", "id": "sample_796"}
{"code": "def f(dct):\n    lst = []\n    for key in sorted(dct):\n        lst.append((key, dct[key]))\n    return lst", "input": "{'a': 1, 'b': 2, 'c': 3}", "output": "[('a', 1), ('b', 2), ('c', 3)]", "id": "sample_797"}
{"code": "def f(text, pre):\n    if not text.startswith(pre):\n        return text\n    return text.removeprefix(pre)", "input": "'@hihu@!', '@hihu'", "output": "'@!'", "id": "sample_798"}
{"code": "def f(st):\n    if st[0] == '~':\n        e = st.rjust(10, 's')\n        return f(e)\n    else:\n        return st.rjust(10, 'n')", "input": "'eqe-;ew22'", "output": "'neqe-;ew22'", "id": "sample_799"}