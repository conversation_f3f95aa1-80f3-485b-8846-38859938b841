{"name": "HumanEval_23_strlen", "language": "d", "prompt": "import std.math;\n/*\n Return length of given string\n    >>> strlen(\"\")\n    0L\n    >>> strlen(\"abc\")\n    3L\n    \n*/\nlong strlen(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = strlen;\n\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"x\") == 1L);\n    assert(candidate(\"asdasnakj\") == 9L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_23_strlen", "test": "unittest\n{\n    alias candidate = strlen;\n\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"x\") == 1L);\n    assert(candidate(\"asdasnakj\") == 9L);\n}\nvoid main(){}"}
{"name": "HumanEval_89_encrypt", "language": "d", "prompt": "import std.math;\n/*\nCreate a function encrypt that takes a string as an argument and\n    returns a string encrypted with the alphabet being rotated. \n    The alphabet should be rotated in a manner such that the letters \n    shift down by two multiplied to two places.\n    For example:\n    >>> encrypt(\"hi\")\n    \"lm\"\n    >>> encrypt(\"asdfghjkl\")\n    \"ewhjklnop\"\n    >>> encrypt(\"gf\")\n    \"kj\"\n    >>> encrypt(\"et\")\n    \"ix\"\n    \n*/\nstring encrypt(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = encrypt;\n\n    assert(candidate(\"hi\") == \"lm\");\n    assert(candidate(\"asdfghjkl\") == \"ewhjklnop\");\n    assert(candidate(\"gf\") == \"kj\");\n    assert(candidate(\"et\") == \"ix\");\n    assert(candidate(\"faewfawefaewg\") == \"jeiajeaijeiak\");\n    assert(candidate(\"hellomyfriend\") == \"lippsqcjvmirh\");\n    assert(candidate(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") == \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\");\n    assert(candidate(\"a\") == \"e\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_89_encrypt", "test": "unittest\n{\n    alias candidate = encrypt;\n\n    assert(candidate(\"hi\") == \"lm\");\n    assert(candidate(\"asdfghjkl\") == \"ewhjklnop\");\n    assert(candidate(\"gf\") == \"kj\");\n    assert(candidate(\"et\") == \"ix\");\n    assert(candidate(\"faewfawefaewg\") == \"jeiajeaijeiak\");\n    assert(candidate(\"hellomyfriend\") == \"lippsqcjvmirh\");\n    assert(candidate(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") == \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\");\n    assert(candidate(\"a\") == \"e\");\n}\nvoid main(){}"}
{"name": "HumanEval_95_check_dict_case", "language": "d", "prompt": "import std.math;\n/*\n\n    Given an associative array, return true if all keys are strings in lower \n    case or all keys are strings in upper case, else return false.\n    The function should return false is the given associative array is empty.\n    Examples:\n    >>> check_dict_case([\"a\": \"apple\", \"b\": \"banana\"].nullable)\n    true\n    >>> check_dict_case([\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"].nullable)\n    false\n    >>> check_dict_case([\"a\": \"apple\", 8L: \"banana\", \"a\": \"apple\"].nullable)\n    false\n    >>> check_dict_case([\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"].nullable)\n    false\n    >>> check_dict_case([\"STATE\": \"NC\", \"ZIP\": \"12345\"].nullable)\n    true\n    \n*/\nbool check_dict_case(Nullable!(string[string]) dict) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = check_dict_case;\n\n    assert(candidate([\"p\": \"pineapple\", \"b\": \"banana\"].nullable) == true);\n    assert(candidate([\"p\": \"pineapple\", \"A\": \"banana\", \"B\": \"banana\"].nullable) == false);\n    assert(candidate([\"p\": \"pineapple\", \"5\": \"banana\", \"a\": \"apple\"].nullable) == false);\n    assert(candidate([\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"].nullable) == false);\n    assert(candidate([\"STATE\": \"NC\", \"ZIP\": \"12345\"].nullable) == true);\n    assert(candidate([\"fruit\": \"Orange\", \"taste\": \"Sweet\"].nullable) == true);\n    assert(candidate(Nullable!(string[string]).init) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_95_check_dict_case", "test": "unittest\n{\n    alias candidate = check_dict_case;\n\n    assert(candidate([\"p\": \"pineapple\", \"b\": \"banana\"].nullable) == true);\n    assert(candidate([\"p\": \"pineapple\", \"A\": \"banana\", \"B\": \"banana\"].nullable) == false);\n    assert(candidate([\"p\": \"pineapple\", \"5\": \"banana\", \"a\": \"apple\"].nullable) == false);\n    assert(candidate([\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"].nullable) == false);\n    assert(candidate([\"STATE\": \"NC\", \"ZIP\": \"12345\"].nullable) == true);\n    assert(candidate([\"fruit\": \"Orange\", \"taste\": \"Sweet\"].nullable) == true);\n    assert(candidate(Nullable!(string[string]).init) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_85_add", "language": "d", "prompt": "import std.math;\n/*\nGiven a non-empty array of integers lst. add the even elements that are at odd indices..\n\n\n    Examples:\n    >>> add([4L, 2L, 6L, 7L])\n    2L\n    \n*/\nlong add(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = add;\n\n    assert(candidate([4L, 88L]) == 88L);\n    assert(candidate([4L, 5L, 6L, 7L, 2L, 122L]) == 122L);\n    assert(candidate([4L, 0L, 6L, 7L]) == 0L);\n    assert(candidate([4L, 4L, 6L, 8L]) == 12L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_85_add", "test": "unittest\n{\n    alias candidate = add;\n\n    assert(candidate([4L, 88L]) == 88L);\n    assert(candidate([4L, 5L, 6L, 7L, 2L, 122L]) == 122L);\n    assert(candidate([4L, 0L, 6L, 7L]) == 0L);\n    assert(candidate([4L, 4L, 6L, 8L]) == 12L);\n}\nvoid main(){}"}
{"name": "HumanEval_140_fix_spaces", "language": "d", "prompt": "import std.math;\n/*\n\n    Given a string text, replace all spaces in it with underscores, \n    and if a string has more than 2 consecutive spaces, \n    then replace all consecutive spaces with - \n    \n    >>> fix_spaces(\" Example\")\n    \"Example\"\n    >>> fix_spaces(\" Example 1\")\n    \"Example_1\"\n    >>> fix_spaces(\" Example 2\")\n    \"_Example_2\"\n    >>> fix_spaces(\" Example 3\")\n    \"_Example-3\"\n    \n*/\nstring fix_spaces(string text) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = fix_spaces;\n\n    assert(candidate(\"Example\") == \"Example\");\n    assert(candidate(\"Mudasir Hanif \") == \"Mudasir_Hanif_\");\n    assert(candidate(\"Yellow Yellow  Dirty  Fellow\") == \"Yellow_Yellow__Dirty__Fellow\");\n    assert(candidate(\"Exa   mple\") == \"Exa-mple\");\n    assert(candidate(\"   Exa 1 2 2 mple\") == \"-Exa_1_2_2_mple\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_140_fix_spaces", "test": "unittest\n{\n    alias candidate = fix_spaces;\n\n    assert(candidate(\"Example\") == \"Example\");\n    assert(candidate(\"Mudasir Hanif \") == \"Mudasir_Hanif_\");\n    assert(candidate(\"Yellow Yellow  Dirty  Fellow\") == \"Yellow_Yellow__Dirty__Fellow\");\n    assert(candidate(\"Exa   mple\") == \"Exa-mple\");\n    assert(candidate(\"   Exa 1 2 2 mple\") == \"-Exa_1_2_2_mple\");\n}\nvoid main(){}"}
{"name": "HumanEval_63_fibfib", "language": "d", "prompt": "import std.math;\n/*\nThe FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n    fibfib(0) == 0\n    fibfib(1) == 0\n    fibfib(2) == 1\n    fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n    Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n    >>> fibfib(1L)\n    0L\n    >>> fibfib(5L)\n    4L\n    >>> fibfib(8L)\n    24L\n    \n*/\nlong fibfib(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = fibfib;\n\n    assert(candidate(2L) == 1L);\n    assert(candidate(1L) == 0L);\n    assert(candidate(5L) == 4L);\n    assert(candidate(8L) == 24L);\n    assert(candidate(10L) == 81L);\n    assert(candidate(12L) == 274L);\n    assert(candidate(14L) == 927L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_63_fibfib", "test": "unittest\n{\n    alias candidate = fibfib;\n\n    assert(candidate(2L) == 1L);\n    assert(candidate(1L) == 0L);\n    assert(candidate(5L) == 4L);\n    assert(candidate(8L) == 24L);\n    assert(candidate(10L) == 81L);\n    assert(candidate(12L) == 274L);\n    assert(candidate(14L) == 927L);\n}\nvoid main(){}"}
{"name": "HumanEval_151_double_the_difference", "language": "d", "prompt": "import std.math;\n/*\n\n    Given an array of numbers, return the sum of squares of the numbers\n    in the array that are odd. Ignore numbers that are negative or not integers.\n    \n    >>> double_the_difference([1L, 3L, 2L, 0L])\n    10L\n    >>> double_the_difference([-1L, -2L, 0L])\n    0L\n    >>> double_the_difference([9L, -2L])\n    81L\n    >>> double_the_difference([0L])\n    0L\n   \n    If the input array is empty, return 0.\n    \n*/\nlong double_the_difference(float[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = double_the_difference;\n\n    assert(candidate([]) == 0L);\n    assert(candidate([5.0, 4.0]) == 25L);\n    assert(candidate([0.1, 0.2, 0.3]) == 0L);\n    assert(candidate([-10.0, -20.0, -30.0]) == 0L);\n    assert(candidate([-1.0, -2.0, 8.0]) == 0L);\n    assert(candidate([0.2, 3.0, 5.0]) == 34L);\n    assert(candidate([-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]) == 165L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_151_double_the_difference", "test": "unittest\n{\n    alias candidate = double_the_difference;\n\n    assert(candidate([]) == 0L);\n    assert(candidate([5.0, 4.0]) == 25L);\n    assert(candidate([0.1, 0.2, 0.3]) == 0L);\n    assert(candidate([-10.0, -20.0, -30.0]) == 0L);\n    assert(candidate([-1.0, -2.0, 8.0]) == 0L);\n    assert(candidate([0.2, 3.0, 5.0]) == 34L);\n    assert(candidate([-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]) == 165L);\n}\nvoid main(){}"}
{"name": "HumanEval_41_car_race_collision", "language": "d", "prompt": "import std.math;\n/*\n\n    Imagine a road that's a perfectly straight infinitely long line.\n    n cars are driving left to right;  simultaneously, a different set of n cars\n    are driving right to left.   The two sets of cars start out being very far from\n    each other.  All cars move in the same speed.  Two cars are said to collide\n    when a car that's moving left to right hits a car that's moving right to left.\n    However, the cars are infinitely sturdy and strong; as a result, they continue moving\n    in their trajectory as if they did not collide.\n\n    This function outputs the number of such collisions.\n    \n*/\nlong car_race_collision(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = car_race_collision;\n\n    assert(candidate(2L) == 4L);\n    assert(candidate(3L) == 9L);\n    assert(candidate(4L) == 16L);\n    assert(candidate(8L) == 64L);\n    assert(candidate(10L) == 100L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_41_car_race_collision", "test": "unittest\n{\n    alias candidate = car_race_collision;\n\n    assert(candidate(2L) == 4L);\n    assert(candidate(3L) == 9L);\n    assert(candidate(4L) == 16L);\n    assert(candidate(8L) == 64L);\n    assert(candidate(10L) == 100L);\n}\nvoid main(){}"}
{"name": "HumanEval_17_parse_music", "language": "d", "prompt": "import std.math;\n/*\n Input to this function is a string representing musical notes in a special ASCII format.\n    Your task is to parse this string and return array of integers corresponding to how many beats does each\n    not last.\n\n    Here is a legend:\n    'o' - whole note, lasts four beats\n    'o|' - half note, lasts two beats\n    '.|' - quater note, lasts one beat\n\n    >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n    [4L, 2L, 1L, 2L, 2L, 1L, 1L, 1L, 1L, 4L, 4L]\n    \n*/\nlong[] parse_music(string music_string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = parse_music;\n\n    assert(candidate(\"\") == []);\n    assert(candidate(\"o o o o\") == [4L, 4L, 4L, 4L]);\n    assert(candidate(\".| .| .| .|\") == [1L, 1L, 1L, 1L]);\n    assert(candidate(\"o| o| .| .| o o o o\") == [2L, 2L, 1L, 1L, 4L, 4L, 4L, 4L]);\n    assert(candidate(\"o| .| o| .| o o| o o|\") == [2L, 1L, 2L, 1L, 4L, 2L, 4L, 2L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_17_parse_music", "test": "unittest\n{\n    alias candidate = parse_music;\n\n    assert(candidate(\"\") == []);\n    assert(candidate(\"o o o o\") == [4L, 4L, 4L, 4L]);\n    assert(candidate(\".| .| .| .|\") == [1L, 1L, 1L, 1L]);\n    assert(candidate(\"o| o| .| .| o o o o\") == [2L, 2L, 1L, 1L, 4L, 4L, 4L, 4L]);\n    assert(candidate(\"o| .| o| .| o o| o o|\") == [2L, 1L, 2L, 1L, 4L, 2L, 4L, 2L]);\n}\nvoid main(){}"}
{"name": "HumanEval_79_decimal_to_binary", "language": "d", "prompt": "import std.math;\n/*\nYou will be given a number in decimal form and your task is to convert it to\n    binary format. The function should return a string, with each character representing a binary\n    number. Each character in the string will be '0' or '1'.\n\n    There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n    The extra characters are there to help with the format.\n\n    Examples:\n    >>> decimal_to_binary(15L)\n    \"db1111db\"\n    >>> decimal_to_binary(32L)\n    \"db100000db\"\n    \n*/\nstring decimal_to_binary(long decimal) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = decimal_to_binary;\n\n    assert(candidate(0L) == \"db0db\");\n    assert(candidate(32L) == \"db100000db\");\n    assert(candidate(103L) == \"db1100111db\");\n    assert(candidate(15L) == \"db1111db\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_79_decimal_to_binary", "test": "unittest\n{\n    alias candidate = decimal_to_binary;\n\n    assert(candidate(0L) == \"db0db\");\n    assert(candidate(32L) == \"db100000db\");\n    assert(candidate(103L) == \"db1100111db\");\n    assert(candidate(15L) == \"db1111db\");\n}\nvoid main(){}"}
{"name": "HumanEval_14_all_prefixes", "language": "d", "prompt": "import std.math;\n/*\n Return array of all prefixes from shortest to longest of the input string\n    >>> all_prefixes(\"abc\")\n    [\"a\", \"ab\", \"abc\"]\n    \n*/\nstring[] all_prefixes(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = all_prefixes;\n\n    assert(candidate(\"\") == []);\n    assert(candidate(\"asdfgh\") == [\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"]);\n    assert(candidate(\"WWW\") == [\"W\", \"WW\", \"WWW\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_14_all_prefixes", "test": "unittest\n{\n    alias candidate = all_prefixes;\n\n    assert(candidate(\"\") == []);\n    assert(candidate(\"asdfgh\") == [\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"]);\n    assert(candidate(\"WWW\") == [\"W\", \"WW\", \"WWW\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_53_add", "language": "d", "prompt": "import std.math;\n/*\nAdd two numbers x and y\n    >>> add(2L, 3L)\n    5L\n    >>> add(5L, 7L)\n    12L\n    \n*/\nlong add(long x, long y) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = add;\n\n    assert(candidate(0L, 1L) == 1L);\n    assert(candidate(1L, 0L) == 1L);\n    assert(candidate(2L, 3L) == 5L);\n    assert(candidate(5L, 7L) == 12L);\n    assert(candidate(7L, 5L) == 12L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_53_add", "test": "unittest\n{\n    alias candidate = add;\n\n    assert(candidate(0L, 1L) == 1L);\n    assert(candidate(1L, 0L) == 1L);\n    assert(candidate(2L, 3L) == 5L);\n    assert(candidate(5L, 7L) == 12L);\n    assert(candidate(7L, 5L) == 12L);\n}\nvoid main(){}"}
{"name": "HumanEval_159_eat", "language": "d", "prompt": "import std.math;\n/*\n\n    You're a hungry rabbit, and you already have eaten a certain number of carrots,\n    but now you need to eat more carrots to complete the day's meals.\n    you should return an array of [ total number of eaten carrots after your meals,\n                                    the number of carrots left after your meals ]\n    if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n    \n    Example:\n    >>> eat(5L, 6L, 10L)\n    [11L, 4L]\n    >>> eat(4L, 8L, 9L)\n    [12L, 1L]\n    >>> eat(1L, 10L, 10L)\n    [11L, 0L]\n    >>> eat(2L, 11L, 5L)\n    [7L, 0L]\n    \n    Variables:\n    @number : integer\n        the number of carrots that you have eaten.\n    @need : integer\n        the number of carrots that you need to eat.\n    @remaining : integer\n        the number of remaining carrots thet exist in stock\n    \n    Constrain:\n    * 0 <= number <= 1000\n    * 0 <= need <= 1000\n    * 0 <= remaining <= 1000\n\n    Have fun :)\n    \n*/\nlong[] eat(long number, long need, long remaining) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = eat;\n\n    assert(candidate(5L, 6L, 10L) == [11L, 4L]);\n    assert(candidate(4L, 8L, 9L) == [12L, 1L]);\n    assert(candidate(1L, 10L, 10L) == [11L, 0L]);\n    assert(candidate(2L, 11L, 5L) == [7L, 0L]);\n    assert(candidate(4L, 5L, 7L) == [9L, 2L]);\n    assert(candidate(4L, 5L, 1L) == [5L, 0L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_159_eat", "test": "unittest\n{\n    alias candidate = eat;\n\n    assert(candidate(5L, 6L, 10L) == [11L, 4L]);\n    assert(candidate(4L, 8L, 9L) == [12L, 1L]);\n    assert(candidate(1L, 10L, 10L) == [11L, 0L]);\n    assert(candidate(2L, 11L, 5L) == [7L, 0L]);\n    assert(candidate(4L, 5L, 7L) == [9L, 2L]);\n    assert(candidate(4L, 5L, 1L) == [5L, 0L]);\n}\nvoid main(){}"}
{"name": "HumanEval_115_max_fill", "language": "d", "prompt": "import std.math;\n/*\n\n    You are given a rectangular grid of wells. Each row represents a single well,\n    and each 1 in a row represents a single unit of water.\n    Each well has a corresponding bucket that can be used to extract water from it, \n    and all buckets have the same capacity.\n    Your task is to use the buckets to empty the wells.\n    Output the number of times you need to lower the buckets.\n\n    Example 1:\n    >>> max_fill([[0L, 0L, 1L, 0L], [0L, 1L, 0L, 0L], [1L, 1L, 1L, 1L]], 1L)\n    6L\n\n    Example 2:\n    >>> max_fill([[0L, 0L, 1L, 1L], [0L, 0L, 0L, 0L], [1L, 1L, 1L, 1L], [0L, 1L, 1L, 1L]], 2L)\n    5L\n    \n    Example 3:\n    >>> max_fill([[0L, 0L, 0L], [0L, 0L, 0L]], 5L)\n    0L\n\n    Constraints:\n        * all wells have the same length\n        * 1 <= grid.length <= 10^2\n        * 1 <= grid[:,1].length <= 10^2\n        * grid[i][j] -> 0 | 1\n        * 1 <= capacity <= 10\n    \n*/\nlong max_fill(long[][] grid, long capacity) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = max_fill;\n\n    assert(candidate([[0L, 0L, 1L, 0L], [0L, 1L, 0L, 0L], [1L, 1L, 1L, 1L]], 1L) == 6L);\n    assert(candidate([[0L, 0L, 1L, 1L], [0L, 0L, 0L, 0L], [1L, 1L, 1L, 1L], [0L, 1L, 1L, 1L]], 2L) == 5L);\n    assert(candidate([[0L, 0L, 0L], [0L, 0L, 0L]], 5L) == 0L);\n    assert(candidate([[1L, 1L, 1L, 1L], [1L, 1L, 1L, 1L]], 2L) == 4L);\n    assert(candidate([[1L, 1L, 1L, 1L], [1L, 1L, 1L, 1L]], 9L) == 2L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_115_max_fill", "test": "unittest\n{\n    alias candidate = max_fill;\n\n    assert(candidate([[0L, 0L, 1L, 0L], [0L, 1L, 0L, 0L], [1L, 1L, 1L, 1L]], 1L) == 6L);\n    assert(candidate([[0L, 0L, 1L, 1L], [0L, 0L, 0L, 0L], [1L, 1L, 1L, 1L], [0L, 1L, 1L, 1L]], 2L) == 5L);\n    assert(candidate([[0L, 0L, 0L], [0L, 0L, 0L]], 5L) == 0L);\n    assert(candidate([[1L, 1L, 1L, 1L], [1L, 1L, 1L, 1L]], 2L) == 4L);\n    assert(candidate([[1L, 1L, 1L, 1L], [1L, 1L, 1L, 1L]], 9L) == 2L);\n}\nvoid main(){}"}
{"name": "HumanEval_160_do_algebra", "language": "d", "prompt": "import std.math;\n/*\n\n    Given two arrays operator, and operand. The first array has basic algebra operations, and \n    the second array is an array of integers. Use the two given arrays to build the algebric \n    expression and return the evaluation of this expression.\n\n    The basic algebra operations:\n    Addition ( + ) \n    Subtraction ( - ) \n    Multiplication ( * ) \n    Floor division ( // ) \n    Exponentiation ( ** ) \n\n    Example:\n    operator['+', '*', '-']\n    array = [2, 3, 4, 5]\n    result = 2 + 3 * 4 - 5\n    => result = 9\n\n    Note:\n        The length of operator array is equal to the length of operand array minus one.\n        Operand is an array of of non-negative integers.\n        Operator array has at least one operator, and operand array has at least two operands.\n\n    \n*/\nlong do_algebra(string[] operator, long[] operand) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = do_algebra;\n\n    assert(candidate([\"**\", \"*\", \"+\"], [2L, 3L, 4L, 5L]) == 37L);\n    assert(candidate([\"+\", \"*\", \"-\"], [2L, 3L, 4L, 5L]) == 9L);\n    assert(candidate([\"//\", \"*\"], [7L, 3L, 4L]) == 8L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_160_do_algebra", "test": "unittest\n{\n    alias candidate = do_algebra;\n\n    assert(candidate([\"**\", \"*\", \"+\"], [2L, 3L, 4L, 5L]) == 37L);\n    assert(candidate([\"+\", \"*\", \"-\"], [2L, 3L, 4L, 5L]) == 9L);\n    assert(candidate([\"//\", \"*\"], [7L, 3L, 4L]) == 8L);\n}\nvoid main(){}"}
{"name": "HumanEval_27_flip_case", "language": "d", "prompt": "import std.math;\n/*\n For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n    >>> flip_case(\"Hello\")\n    \"hELLO\"\n    \n*/\nstring flip_case(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = flip_case;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"Hello!\") == \"hELLO!\");\n    assert(candidate(\"These violent delights have violent ends\") == \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_27_flip_case", "test": "unittest\n{\n    alias candidate = flip_case;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"Hello!\") == \"hELLO!\");\n    assert(candidate(\"These violent delights have violent ends\") == \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\");\n}\nvoid main(){}"}
{"name": "HumanEval_105_by_length", "language": "d", "prompt": "import std.math;\n/*\n\n    Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n    reverse the resulting array, and then replace each digit by its corresponding name from\n    \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n\n    For example:\n    >>> by_length([2L, 1L, 1L, 4L, 5L, 8L, 2L, 3L])\n    [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n    \n      If the array is empty, return an empty array:\n    >>> by_length([])\n    []\n    \n      If the array has any strange number ignore it:\n    >>> by_length([1L, -1L, 55L])\n    [\"One\"]\n    \n*/\nstring[] by_length(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = by_length;\n\n    assert(candidate([2L, 1L, 1L, 4L, 5L, 8L, 2L, 3L]) == [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]);\n    assert(candidate([]) == []);\n    assert(candidate([1L, -1L, 55L]) == [\"One\"]);\n    assert(candidate([1L, -1L, 3L, 2L]) == [\"Three\", \"Two\", \"One\"]);\n    assert(candidate([9L, 4L, 8L]) == [\"Nine\", \"Eight\", \"Four\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_105_by_length", "test": "unittest\n{\n    alias candidate = by_length;\n\n    assert(candidate([2L, 1L, 1L, 4L, 5L, 8L, 2L, 3L]) == [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]);\n    assert(candidate([]) == []);\n    assert(candidate([1L, -1L, 55L]) == [\"One\"]);\n    assert(candidate([1L, -1L, 3L, 2L]) == [\"Three\", \"Two\", \"One\"]);\n    assert(candidate([9L, 4L, 8L]) == [\"Nine\", \"Eight\", \"Four\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_25_factorize", "language": "d", "prompt": "import std.math;\n/*\n Return array of prime factors of given integer in the order from smallest to largest.\n    Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n    Input number should be equal to the product of all factors\n    >>> factorize(8L)\n    [2L, 2L, 2L]\n    >>> factorize(25L)\n    [5L, 5L]\n    >>> factorize(70L)\n    [2L, 5L, 7L]\n    \n*/\nlong[] factorize(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = factorize;\n\n    assert(candidate(2L) == [2L]);\n    assert(candidate(4L) == [2L, 2L]);\n    assert(candidate(8L) == [2L, 2L, 2L]);\n    assert(candidate(57L) == [3L, 19L]);\n    assert(candidate(3249L) == [3L, 3L, 19L, 19L]);\n    assert(candidate(185193L) == [3L, 3L, 3L, 19L, 19L, 19L]);\n    assert(candidate(20577L) == [3L, 19L, 19L, 19L]);\n    assert(candidate(18L) == [2L, 3L, 3L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_25_factorize", "test": "unittest\n{\n    alias candidate = factorize;\n\n    assert(candidate(2L) == [2L]);\n    assert(candidate(4L) == [2L, 2L]);\n    assert(candidate(8L) == [2L, 2L, 2L]);\n    assert(candidate(57L) == [3L, 19L]);\n    assert(candidate(3249L) == [3L, 3L, 19L, 19L]);\n    assert(candidate(185193L) == [3L, 3L, 3L, 19L, 19L, 19L]);\n    assert(candidate(20577L) == [3L, 19L, 19L, 19L]);\n    assert(candidate(18L) == [2L, 3L, 3L]);\n}\nvoid main(){}"}
{"name": "HumanEval_96_count_up_to", "language": "d", "prompt": "import std.math;\n/*\nImplement a function that takes an non-negative integer and returns an array of the first n\n    integers that are prime numbers and less than n.\n    for example:\n    >>> count_up_to(5L)\n    [2L, 3L]\n    >>> count_up_to(11L)\n    [2L, 3L, 5L, 7L]\n    >>> count_up_to(0L)\n    []\n    >>> count_up_to(20L)\n    [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L]\n    >>> count_up_to(1L)\n    []\n    >>> count_up_to(18L)\n    [2L, 3L, 5L, 7L, 11L, 13L, 17L]\n    \n*/\nlong[] count_up_to(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = count_up_to;\n\n    assert(candidate(5L) == [2L, 3L]);\n    assert(candidate(6L) == [2L, 3L, 5L]);\n    assert(candidate(7L) == [2L, 3L, 5L]);\n    assert(candidate(10L) == [2L, 3L, 5L, 7L]);\n    assert(candidate(0L) == []);\n    assert(candidate(22L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L]);\n    assert(candidate(1L) == []);\n    assert(candidate(18L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L]);\n    assert(candidate(47L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L, 23L, 29L, 31L, 37L, 41L, 43L]);\n    assert(candidate(101L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L, 23L, 29L, 31L, 37L, 41L, 43L, 47L, 53L, 59L, 61L, 67L, 71L, 73L, 79L, 83L, 89L, 97L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_96_count_up_to", "test": "unittest\n{\n    alias candidate = count_up_to;\n\n    assert(candidate(5L) == [2L, 3L]);\n    assert(candidate(6L) == [2L, 3L, 5L]);\n    assert(candidate(7L) == [2L, 3L, 5L]);\n    assert(candidate(10L) == [2L, 3L, 5L, 7L]);\n    assert(candidate(0L) == []);\n    assert(candidate(22L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L]);\n    assert(candidate(1L) == []);\n    assert(candidate(18L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L]);\n    assert(candidate(47L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L, 23L, 29L, 31L, 37L, 41L, 43L]);\n    assert(candidate(101L) == [2L, 3L, 5L, 7L, 11L, 13L, 17L, 19L, 23L, 29L, 31L, 37L, 41L, 43L, 47L, 53L, 59L, 61L, 67L, 71L, 73L, 79L, 83L, 89L, 97L]);\n}\nvoid main(){}"}
{"name": "HumanEval_34_unique", "language": "d", "prompt": "import std.math;\n/*\nReturn sorted unique elements in an array\n    >>> unique([5L, 3L, 5L, 2L, 3L, 3L, 9L, 0L, 123L])\n    [0L, 2L, 3L, 5L, 9L, 123L]\n    \n*/\nlong[] unique(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = unique;\n\n    assert(candidate([5L, 3L, 5L, 2L, 3L, 3L, 9L, 0L, 123L]) == [0L, 2L, 3L, 5L, 9L, 123L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_34_unique", "test": "unittest\n{\n    alias candidate = unique;\n\n    assert(candidate([5L, 3L, 5L, 2L, 3L, 3L, 9L, 0L, 123L]) == [0L, 2L, 3L, 5L, 9L, 123L]);\n}\nvoid main(){}"}
{"name": "HumanEval_74_total_match", "language": "d", "prompt": "import std.math;\n/*\n\n    Write a function that accepts two arrays of strings and returns the array that has \n    total number of chars in the all strings of the array less than the other array.\n\n    if the two arrays have the same number of chars, return the first array.\n\n    Examples\n    >>> total_match([], [])\n    []\n    >>> total_match([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n    [\"hI\", \"Hi\"]\n    >>> total_match([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n    [\"hi\", \"admin\"]\n    >>> total_match([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n    [\"hI\", \"hi\", \"hi\"]\n    >>> total_match([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n    [\"4\"]\n    \n*/\nstring[] total_match(string[] lst1, string[] lst2) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = total_match;\n\n    assert(candidate([], []) == []);\n    assert(candidate([\"hi\", \"admin\"], [\"hi\", \"hi\"]) == [\"hi\", \"hi\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"]) == [\"hi\", \"admin\"]);\n    assert(candidate([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"]) == [\"4\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hI\", \"Hi\"]) == [\"hI\", \"Hi\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"]) == [\"hI\", \"hi\", \"hi\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hii\"]) == [\"hi\", \"admin\"]);\n    assert(candidate([], [\"this\"]) == []);\n    assert(candidate([\"this\"], []) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_74_total_match", "test": "unittest\n{\n    alias candidate = total_match;\n\n    assert(candidate([], []) == []);\n    assert(candidate([\"hi\", \"admin\"], [\"hi\", \"hi\"]) == [\"hi\", \"hi\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"]) == [\"hi\", \"admin\"]);\n    assert(candidate([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"]) == [\"4\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hI\", \"Hi\"]) == [\"hI\", \"Hi\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"]) == [\"hI\", \"hi\", \"hi\"]);\n    assert(candidate([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hii\"]) == [\"hi\", \"admin\"]);\n    assert(candidate([], [\"this\"]) == []);\n    assert(candidate([\"this\"], []) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_35_max_element", "language": "d", "prompt": "import std.math;\n/*\nReturn maximum element in the array.\n    >>> max_element([1L, 2L, 3L])\n    3L\n    >>> max_element([5L, 3L, -5L, 2L, -3L, 3L, 9L, 0L, 123L, 1L, -10L])\n    123L\n    \n*/\nlong max_element(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = max_element;\n\n    assert(candidate([1L, 2L, 3L]) == 3L);\n    assert(candidate([5L, 3L, -5L, 2L, -3L, 3L, 9L, 0L, 124L, 1L, -10L]) == 124L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_35_max_element", "test": "unittest\n{\n    alias candidate = max_element;\n\n    assert(candidate([1L, 2L, 3L]) == 3L);\n    assert(candidate([5L, 3L, -5L, 2L, -3L, 3L, 9L, 0L, 124L, 1L, -10L]) == 124L);\n}\nvoid main(){}"}
{"name": "HumanEval_132_is_nested", "language": "d", "prompt": "import std.math;\n/*\n\n    Create a function that takes a string as input which contains only square brackets.\n    The function should return true if and only if there is a valid subsequence of brackets \n    where at least one bracket in the subsequence is nested.\n\n    >>> is_nested(\"[[]]\")\n    true\n    >>> is_nested(\"[]]]]]]][[[[[]\")\n    false\n    >>> is_nested(\"[][]\")\n    false\n    >>> is_nested(\"[]\")\n    false\n    >>> is_nested(\"[[][]]\")\n    true\n    >>> is_nested(\"[[]][[\")\n    true\n    \n*/\nbool is_nested(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_nested;\n\n    assert(candidate(\"[[]]\") == true);\n    assert(candidate(\"[]]]]]]][[[[[]\") == false);\n    assert(candidate(\"[][]\") == false);\n    assert(candidate(\"[]\") == false);\n    assert(candidate(\"[[[[]]]]\") == true);\n    assert(candidate(\"[]]]]]]]]]]\") == false);\n    assert(candidate(\"[][][[]]\") == true);\n    assert(candidate(\"[[]\") == false);\n    assert(candidate(\"[]]\") == false);\n    assert(candidate(\"[[]][[\") == true);\n    assert(candidate(\"[[][]]\") == true);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"[[[[[[[[\") == false);\n    assert(candidate(\"]]]]]]]]\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_132_is_nested", "test": "unittest\n{\n    alias candidate = is_nested;\n\n    assert(candidate(\"[[]]\") == true);\n    assert(candidate(\"[]]]]]]][[[[[]\") == false);\n    assert(candidate(\"[][]\") == false);\n    assert(candidate(\"[]\") == false);\n    assert(candidate(\"[[[[]]]]\") == true);\n    assert(candidate(\"[]]]]]]]]]]\") == false);\n    assert(candidate(\"[][][[]]\") == true);\n    assert(candidate(\"[[]\") == false);\n    assert(candidate(\"[]]\") == false);\n    assert(candidate(\"[[]][[\") == true);\n    assert(candidate(\"[[][]]\") == true);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"[[[[[[[[\") == false);\n    assert(candidate(\"]]]]]]]]\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_113_odd_count", "language": "d", "prompt": "import std.math;\n/*\nGiven an array of strings, where each string consists of only digits, return an array.\n    Each element i of the output should be \"the number of odd elements in the\n    string i of the input.\" where all the i's should be replaced by the number\n    of odd digits in the i'th string of the input.\n\n    >>> odd_count([\"1234567\"])\n    [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n    >>> odd_count([\"3\", \"11111111\"])\n    [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\n    \n*/\nstring[] odd_count(string[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = odd_count;\n\n    assert(candidate([\"1234567\"]) == [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]);\n    assert(candidate([\"3\", \"11111111\"]) == [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]);\n    assert(candidate([\"271\", \"137\", \"314\"]) == [\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_113_odd_count", "test": "unittest\n{\n    alias candidate = odd_count;\n\n    assert(candidate([\"1234567\"]) == [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]);\n    assert(candidate([\"3\", \"11111111\"]) == [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]);\n    assert(candidate([\"271\", \"137\", \"314\"]) == [\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_109_move_one_ball", "language": "d", "prompt": "import std.math;\n/*\nWe have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n    numbers in the array will be randomly ordered. Your task is to determine if\n    it is possible to get an array sorted in non-decreasing order by performing \n    the following operation on the given array:\n        You are allowed to perform right shift operation any number of times.\n    \n    One right shift operation means shifting all elements of the array by one\n    position in the right direction. The last element of the array will be moved to\n    the starting position in the array i.e. 0th index. \n\n    If it is possible to obtain the sorted array by performing the above operation\n    then return true else return false.\n    If the given array is empty then return true.\n\n    Note: The given array is guaranteed to have unique elements.\n\n    For Example:\n    \n    >>> move_one_ball([3L, 4L, 5L, 1L, 2L])\n    true\n    Explanation: By performin 2 right shift operations, non-decreasing order can\n                 be achieved for the given array.\n    >>> move_one_ball([3L, 5L, 4L, 1L, 2L])\n    false\n    Explanation:It is not possible to get non-decreasing order for the given\n                array by performing any number of right shift operations.\n                \n    \n*/\nbool move_one_ball(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = move_one_ball;\n\n    assert(candidate([3L, 4L, 5L, 1L, 2L]) == true);\n    assert(candidate([3L, 5L, 10L, 1L, 2L]) == true);\n    assert(candidate([4L, 3L, 1L, 2L]) == false);\n    assert(candidate([3L, 5L, 4L, 1L, 2L]) == false);\n    assert(candidate([]) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_109_move_one_ball", "test": "unittest\n{\n    alias candidate = move_one_ball;\n\n    assert(candidate([3L, 4L, 5L, 1L, 2L]) == true);\n    assert(candidate([3L, 5L, 10L, 1L, 2L]) == true);\n    assert(candidate([4L, 3L, 1L, 2L]) == false);\n    assert(candidate([3L, 5L, 4L, 1L, 2L]) == false);\n    assert(candidate([]) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a positive integer n, return a tuple that has the number of even and odd\n    integer palindromes that fall within the range(1, n), inclusive.\n\n    Example 1:\n\n    >>> even_odd_palindrome(3L)\n    tuple(1L, 2L)\n        Explanation:\n        Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n\n    Example 2:\n\n    >>> even_odd_palindrome(12L)\n    tuple(4L, 6L)\n        Explanation:\n        Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n\n    Note:\n        1. 1 <= n <= 10^3\n        2. returned tuple has the number of even and odd integer palindromes respectively.\n    \n*/\nTuple!(long, long) even_odd_palindrome(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = even_odd_palindrome;\n\n    assert(candidate(123L) == tuple(8L, 13L));\n    assert(candidate(12L) == tuple(4L, 6L));\n    assert(candidate(3L) == tuple(1L, 2L));\n    assert(candidate(63L) == tuple(6L, 8L));\n    assert(candidate(25L) == tuple(5L, 6L));\n    assert(candidate(19L) == tuple(4L, 6L));\n    assert(candidate(9L) == tuple(4L, 5L));\n    assert(candidate(1L) == tuple(0L, 1L));\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "unittest\n{\n    alias candidate = even_odd_palindrome;\n\n    assert(candidate(123L) == tuple(8L, 13L));\n    assert(candidate(12L) == tuple(4L, 6L));\n    assert(candidate(3L) == tuple(1L, 2L));\n    assert(candidate(63L) == tuple(6L, 8L));\n    assert(candidate(25L) == tuple(5L, 6L));\n    assert(candidate(19L) == tuple(4L, 6L));\n    assert(candidate(9L) == tuple(4L, 5L));\n    assert(candidate(1L) == tuple(0L, 1L));\n}\nvoid main(){}"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nEvaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n    Example\n    >>> is_equal_to_sum_even(4L)\n    false\n    >>> is_equal_to_sum_even(6L)\n    false\n    >>> is_equal_to_sum_even(8L)\n    true\n    \n*/\nbool is_equal_to_sum_even(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_equal_to_sum_even;\n\n    assert(candidate(4L) == false);\n    assert(candidate(6L) == false);\n    assert(candidate(8L) == true);\n    assert(candidate(10L) == true);\n    assert(candidate(11L) == false);\n    assert(candidate(12L) == true);\n    assert(candidate(13L) == false);\n    assert(candidate(16L) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "unittest\n{\n    alias candidate = is_equal_to_sum_even;\n\n    assert(candidate(4L) == false);\n    assert(candidate(6L) == false);\n    assert(candidate(8L) == true);\n    assert(candidate(10L) == true);\n    assert(candidate(11L) == false);\n    assert(candidate(12L) == true);\n    assert(candidate(13L) == false);\n    assert(candidate(16L) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_62_derivative", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n xs represent coefficients of a polynomial.\n    xs[0] + xs[1] * x + xs[2] * x^2 + ....\n     Return derivative of this polynomial in the same form.\n    >>> derivative([3L, 1L, 2L, 4L, 5L])\n    [1L, 4L, 12L, 20L]\n    >>> derivative([1L, 2L, 3L])\n    [2L, 6L]\n    \n*/\nlong[] derivative(long[] xs) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = derivative;\n\n    assert(candidate([3L, 1L, 2L, 4L, 5L]) == [1L, 4L, 12L, 20L]);\n    assert(candidate([1L, 2L, 3L]) == [2L, 6L]);\n    assert(candidate([3L, 2L, 1L]) == [2L, 2L]);\n    assert(candidate([3L, 2L, 1L, 0L, 4L]) == [2L, 2L, 0L, 16L]);\n    assert(candidate([1L]) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_62_derivative", "test": "unittest\n{\n    alias candidate = derivative;\n\n    assert(candidate([3L, 1L, 2L, 4L, 5L]) == [1L, 4L, 12L, 20L]);\n    assert(candidate([1L, 2L, 3L]) == [2L, 6L]);\n    assert(candidate([3L, 2L, 1L]) == [2L, 2L]);\n    assert(candidate([3L, 2L, 1L, 0L, 4L]) == [2L, 2L, 0L, 16L]);\n    assert(candidate([1L]) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_126_is_sorted", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given an array of numbers, return whether or not they are sorted\n    in ascending order. If array has more than 1 duplicate of the same\n    number, return false. Assume no negative numbers and only integers.\n\n    Examples\n    >>> is_sorted([5L])\n    true\n    >>> is_sorted([1L, 2L, 3L, 4L, 5L])\n    true\n    >>> is_sorted([1L, 3L, 2L, 4L, 5L])\n    false\n    >>> is_sorted([1L, 2L, 3L, 4L, 5L, 6L])\n    true\n    >>> is_sorted([1L, 2L, 3L, 4L, 5L, 6L, 7L])\n    true\n    >>> is_sorted([1L, 3L, 2L, 4L, 5L, 6L, 7L])\n    false\n    >>> is_sorted([1L, 2L, 2L, 3L, 3L, 4L])\n    true\n    >>> is_sorted([1L, 2L, 2L, 2L, 3L, 4L])\n    false\n    \n*/\nbool is_sorted(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_sorted;\n\n    assert(candidate([5L]) == true);\n    assert(candidate([1L, 2L, 3L, 4L, 5L]) == true);\n    assert(candidate([1L, 3L, 2L, 4L, 5L]) == false);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L]) == true);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L, 7L]) == true);\n    assert(candidate([1L, 3L, 2L, 4L, 5L, 6L, 7L]) == false);\n    assert(candidate([]) == true);\n    assert(candidate([1L]) == true);\n    assert(candidate([3L, 2L, 1L]) == false);\n    assert(candidate([1L, 2L, 2L, 2L, 3L, 4L]) == false);\n    assert(candidate([1L, 2L, 3L, 3L, 3L, 4L]) == false);\n    assert(candidate([1L, 2L, 2L, 3L, 3L, 4L]) == true);\n    assert(candidate([1L, 2L, 3L, 4L]) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_126_is_sorted", "test": "unittest\n{\n    alias candidate = is_sorted;\n\n    assert(candidate([5L]) == true);\n    assert(candidate([1L, 2L, 3L, 4L, 5L]) == true);\n    assert(candidate([1L, 3L, 2L, 4L, 5L]) == false);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L]) == true);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L, 7L]) == true);\n    assert(candidate([1L, 3L, 2L, 4L, 5L, 6L, 7L]) == false);\n    assert(candidate([]) == true);\n    assert(candidate([1L]) == true);\n    assert(candidate([3L, 2L, 1L]) == false);\n    assert(candidate([1L, 2L, 2L, 2L, 3L, 4L]) == false);\n    assert(candidate([1L, 2L, 3L, 3L, 3L, 4L]) == false);\n    assert(candidate([1L, 2L, 2L, 3L, 3L, 4L]) == true);\n    assert(candidate([1L, 2L, 3L, 4L]) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_161_solve", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given a string s.\n    if s[i] is a letter, reverse its case from lower to upper or vise versa, \n    otherwise keep it as it is.\n    If the string contains no letters, reverse the string.\n    The function should return the resulted string.\n    Examples\n    >>> solve(\"1234\")\n    \"4321\"\n    >>> solve(\"ab\")\n    \"AB\"\n    >>> solve(\"#a@C\")\n    \"#A@c\"\n    \n*/\nstring solve(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = solve;\n\n    assert(candidate(\"AsDf\") == \"aSdF\");\n    assert(candidate(\"1234\") == \"4321\");\n    assert(candidate(\"ab\") == \"AB\");\n    assert(candidate(\"#a@C\") == \"#A@c\");\n    assert(candidate(\"#AsdfW^45\") == \"#aSDFw^45\");\n    assert(candidate(\"#6@2\") == \"2@6#\");\n    assert(candidate(\"#$a^D\") == \"#$A^d\");\n    assert(candidate(\"#ccc\") == \"#CCC\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_161_solve", "test": "unittest\n{\n    alias candidate = solve;\n\n    assert(candidate(\"AsDf\") == \"aSdF\");\n    assert(candidate(\"1234\") == \"4321\");\n    assert(candidate(\"ab\") == \"AB\");\n    assert(candidate(\"#a@C\") == \"#A@c\");\n    assert(candidate(\"#AsdfW^45\") == \"#aSDFw^45\");\n    assert(candidate(\"#6@2\") == \"2@6#\");\n    assert(candidate(\"#$a^D\") == \"#$A^d\");\n    assert(candidate(\"#ccc\") == \"#CCC\");\n}\nvoid main(){}"}
{"name": "HumanEval_130_tri", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nEveryone knows Fibonacci sequence, it was studied deeply by mathematicians in \n    the last couple centuries. However, what people don't know is Tribonacci sequence.\n    Tribonacci sequence is defined by the recurrence:\n    tri(1) = 3\n    tri(n) = 1 + n / 2, if n is even.\n    tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n    For example:\n    tri(2) = 1 + (2 / 2) = 2\n    tri(4) = 3\n    tri(3) = tri(2) + tri(1) + tri(4)\n           = 2 + 3 + 3 = 8 \n    You are given a non-negative integer number n, you have to a return an array of the \n    first n + 1 numbers of the Tribonacci sequence.\n    Examples:\n    >>> tri(3L)\n    [1L, 3L, 2L, 8L]\n    \n*/\nlong[] tri(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = tri;\n\n    assert(candidate(3L) == [1L, 3L, 2L, 8L]);\n    assert(candidate(4L) == [1L, 3L, 2L, 8L, 3L]);\n    assert(candidate(5L) == [1L, 3L, 2L, 8L, 3L, 15L]);\n    assert(candidate(6L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L]);\n    assert(candidate(7L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L]);\n    assert(candidate(8L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L, 5L]);\n    assert(candidate(9L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L, 5L, 35L]);\n    assert(candidate(20L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L, 5L, 35L, 6L, 48L, 7L, 63L, 8L, 80L, 9L, 99L, 10L, 120L, 11L]);\n    assert(candidate(0L) == [1L]);\n    assert(candidate(1L) == [1L, 3L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_130_tri", "test": "unittest\n{\n    alias candidate = tri;\n\n    assert(candidate(3L) == [1L, 3L, 2L, 8L]);\n    assert(candidate(4L) == [1L, 3L, 2L, 8L, 3L]);\n    assert(candidate(5L) == [1L, 3L, 2L, 8L, 3L, 15L]);\n    assert(candidate(6L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L]);\n    assert(candidate(7L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L]);\n    assert(candidate(8L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L, 5L]);\n    assert(candidate(9L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L, 5L, 35L]);\n    assert(candidate(20L) == [1L, 3L, 2L, 8L, 3L, 15L, 4L, 24L, 5L, 35L, 6L, 48L, 7L, 63L, 8L, 80L, 9L, 99L, 10L, 120L, 11L]);\n    assert(candidate(0L) == [1L]);\n    assert(candidate(1L) == [1L, 3L]);\n}\nvoid main(){}"}
{"name": "HumanEval_36_fizz_buzz", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n    >>> fizz_buzz(50L)\n    0L\n    >>> fizz_buzz(78L)\n    2L\n    >>> fizz_buzz(79L)\n    3L\n    \n*/\nlong fizz_buzz(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = fizz_buzz;\n\n    assert(candidate(50L) == 0L);\n    assert(candidate(78L) == 2L);\n    assert(candidate(79L) == 3L);\n    assert(candidate(100L) == 3L);\n    assert(candidate(200L) == 6L);\n    assert(candidate(4000L) == 192L);\n    assert(candidate(10000L) == 639L);\n    assert(candidate(100000L) == 8026L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_36_fizz_buzz", "test": "unittest\n{\n    alias candidate = fizz_buzz;\n\n    assert(candidate(50L) == 0L);\n    assert(candidate(78L) == 2L);\n    assert(candidate(79L) == 3L);\n    assert(candidate(100L) == 3L);\n    assert(candidate(200L) == 6L);\n    assert(candidate(4000L) == 192L);\n    assert(candidate(10000L) == 639L);\n    assert(candidate(100000L) == 8026L);\n}\nvoid main(){}"}
{"name": "HumanEval_29_filter_by_prefix", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Filter an input array of strings only for ones that start with a given prefix.\n    >>> filter_by_prefix([], \"a\")\n    []\n    >>> filter_by_prefix([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n    [\"abc\", \"array\"]\n    \n*/\nstring[] filter_by_prefix(string[] strings, string prefix) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = filter_by_prefix;\n\n    assert(candidate([], \"john\") == []);\n    assert(candidate([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_29_filter_by_prefix", "test": "unittest\n{\n    alias candidate = filter_by_prefix;\n\n    assert(candidate([], \"john\") == []);\n    assert(candidate([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_84_solve", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven a positive integer N, return the total sum of its digits in binary.\n    \n    Example\n    >>> solve(1000L)\n    \"1\"\n    >>> solve(150L)\n    \"110\"\n    >>> solve(147L)\n    \"1100\"\n    \n    Variables:\n        @N integer\n             Constraints: 0 \u2264 N \u2264 10000.\n    Output:\n         a string of binary number\n    \n*/\nstring solve(long N) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = solve;\n\n    assert(candidate(1000L) == \"1\");\n    assert(candidate(150L) == \"110\");\n    assert(candidate(147L) == \"1100\");\n    assert(candidate(333L) == \"1001\");\n    assert(candidate(963L) == \"10010\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_84_solve", "test": "unittest\n{\n    alias candidate = solve;\n\n    assert(candidate(1000L) == \"1\");\n    assert(candidate(150L) == \"110\");\n    assert(candidate(147L) == \"1100\");\n    assert(candidate(333L) == \"1001\");\n    assert(candidate(963L) == \"10010\");\n}\nvoid main(){}"}
{"name": "HumanEval_129_minPath", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n    each cell of the grid contains a value. Every integer in the range [1, N * N]\n    inclusive appears exactly once on the cells of the grid.\n\n    You have to find the minimum path of length k in the grid. You can start\n    from any cell, and in each step you can move to any of the neighbor cells,\n    in other words, you can go to cells which share an edge with you current\n    cell.\n    Please note that a path of length k means visiting exactly k cells (not\n    necessarily distinct).\n    You CANNOT go off the grid.\n    A path A (of length k) is considered less than a path B (of length k) if\n    after making the ordered arrays of the values on the cells that A and B go\n    through (let's call them lst_A and lst_B), lst_A is lexicographically less\n    than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n    such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n    lst_A[j] = lst_B[j].\n    It is guaranteed that the answer is unique.\n    Return an ordered array of the values on the cells that the minimum path go through.\n\n    Examples:    \n    >>> minPath([[1L, 2L, 3L], [4L, 5L, 6L], [7L, 8L, 9L]], 3L)\n    [1L, 2L, 1L]\n\n    >>> minPath([[5L, 9L, 3L], [4L, 1L, 6L], [7L, 8L, 2L]], 1L)\n    [1L]\n    \n*/\nlong[] minPath(long[][] grid, long k) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = minPath;\n\n    assert(candidate([[1L, 2L, 3L], [4L, 5L, 6L], [7L, 8L, 9L]], 3L) == [1L, 2L, 1L]);\n    assert(candidate([[5L, 9L, 3L], [4L, 1L, 6L], [7L, 8L, 2L]], 1L) == [1L]);\n    assert(candidate([[1L, 2L, 3L, 4L], [5L, 6L, 7L, 8L], [9L, 10L, 11L, 12L], [13L, 14L, 15L, 16L]], 4L) == [1L, 2L, 1L, 2L]);\n    assert(candidate([[6L, 4L, 13L, 10L], [5L, 7L, 12L, 1L], [3L, 16L, 11L, 15L], [8L, 14L, 9L, 2L]], 7L) == [1L, 10L, 1L, 10L, 1L, 10L, 1L]);\n    assert(candidate([[8L, 14L, 9L, 2L], [6L, 4L, 13L, 15L], [5L, 7L, 1L, 12L], [3L, 10L, 11L, 16L]], 5L) == [1L, 7L, 1L, 7L, 1L]);\n    assert(candidate([[11L, 8L, 7L, 2L], [5L, 16L, 14L, 4L], [9L, 3L, 15L, 6L], [12L, 13L, 10L, 1L]], 9L) == [1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L, 1L]);\n    assert(candidate([[12L, 13L, 10L, 1L], [9L, 3L, 15L, 6L], [5L, 16L, 14L, 4L], [11L, 8L, 7L, 2L]], 12L) == [1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L]);\n    assert(candidate([[2L, 7L, 4L], [3L, 1L, 5L], [6L, 8L, 9L]], 8L) == [1L, 3L, 1L, 3L, 1L, 3L, 1L, 3L]);\n    assert(candidate([[6L, 1L, 5L], [3L, 8L, 9L], [2L, 7L, 4L]], 8L) == [1L, 5L, 1L, 5L, 1L, 5L, 1L, 5L]);\n    assert(candidate([[1L, 2L], [3L, 4L]], 10L) == [1L, 2L, 1L, 2L, 1L, 2L, 1L, 2L, 1L, 2L]);\n    assert(candidate([[1L, 3L], [3L, 2L]], 10L) == [1L, 3L, 1L, 3L, 1L, 3L, 1L, 3L, 1L, 3L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_129_minPath", "test": "unittest\n{\n    alias candidate = minPath;\n\n    assert(candidate([[1L, 2L, 3L], [4L, 5L, 6L], [7L, 8L, 9L]], 3L) == [1L, 2L, 1L]);\n    assert(candidate([[5L, 9L, 3L], [4L, 1L, 6L], [7L, 8L, 2L]], 1L) == [1L]);\n    assert(candidate([[1L, 2L, 3L, 4L], [5L, 6L, 7L, 8L], [9L, 10L, 11L, 12L], [13L, 14L, 15L, 16L]], 4L) == [1L, 2L, 1L, 2L]);\n    assert(candidate([[6L, 4L, 13L, 10L], [5L, 7L, 12L, 1L], [3L, 16L, 11L, 15L], [8L, 14L, 9L, 2L]], 7L) == [1L, 10L, 1L, 10L, 1L, 10L, 1L]);\n    assert(candidate([[8L, 14L, 9L, 2L], [6L, 4L, 13L, 15L], [5L, 7L, 1L, 12L], [3L, 10L, 11L, 16L]], 5L) == [1L, 7L, 1L, 7L, 1L]);\n    assert(candidate([[11L, 8L, 7L, 2L], [5L, 16L, 14L, 4L], [9L, 3L, 15L, 6L], [12L, 13L, 10L, 1L]], 9L) == [1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L, 1L]);\n    assert(candidate([[12L, 13L, 10L, 1L], [9L, 3L, 15L, 6L], [5L, 16L, 14L, 4L], [11L, 8L, 7L, 2L]], 12L) == [1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L, 1L, 6L]);\n    assert(candidate([[2L, 7L, 4L], [3L, 1L, 5L], [6L, 8L, 9L]], 8L) == [1L, 3L, 1L, 3L, 1L, 3L, 1L, 3L]);\n    assert(candidate([[6L, 1L, 5L], [3L, 8L, 9L], [2L, 7L, 4L]], 8L) == [1L, 5L, 1L, 5L, 1L, 5L, 1L, 5L]);\n    assert(candidate([[1L, 2L], [3L, 4L]], 10L) == [1L, 2L, 1L, 2L, 1L, 2L, 1L, 2L, 1L, 2L]);\n    assert(candidate([[1L, 3L], [3L, 2L]], 10L) == [1L, 3L, 1L, 3L, 1L, 3L, 1L, 3L, 1L, 3L]);\n}\nvoid main(){}"}
{"name": "HumanEval_98_count_upper", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a string s, count the number of uppercase vowels in even indices.\n    \n    For example:\n    >>> count_upper(\"aBCdEf\")\n    1L\n    >>> count_upper(\"abcdefg\")\n    0L\n    >>> count_upper(\"dBBE\")\n    0L\n    \n*/\nlong count_upper(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = count_upper;\n\n    assert(candidate(\"aBCdEf\") == 1L);\n    assert(candidate(\"abcdefg\") == 0L);\n    assert(candidate(\"dBBE\") == 0L);\n    assert(candidate(\"B\") == 0L);\n    assert(candidate(\"U\") == 1L);\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"EEEE\") == 2L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_98_count_upper", "test": "unittest\n{\n    alias candidate = count_upper;\n\n    assert(candidate(\"aBCdEf\") == 1L);\n    assert(candidate(\"abcdefg\") == 0L);\n    assert(candidate(\"dBBE\") == 0L);\n    assert(candidate(\"B\") == 0L);\n    assert(candidate(\"U\") == 1L);\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"EEEE\") == 2L);\n}\nvoid main(){}"}
{"name": "HumanEval_120_maximum", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given an array arr of integers and a positive integer k, return a sorted array \n    of length k with the maximum k numbers in arr.\n\n    Example 1:\n\n    >>> maximum([-3L, -4L, 5L], 3L)\n    [-4L, -3L, 5L]\n\n    Example 2:\n\n    >>> maximum([4L, -4L, 4L], 2L)\n    [4L, 4L]\n\n    Example 3:\n\n    >>> maximum([-3L, 2L, 1L, 2L, -1L, -2L, 1L], 1L)\n    [2L]\n\n    Note:\n        1. The length of the array will be in the range of [1, 1000].\n        2. The elements in the array will be in the range of [-1000, 1000].\n        3. 0 <= k <= len(arr)\n    \n*/\nlong[] maximum(long[] arr, long k) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = maximum;\n\n    assert(candidate([-3L, -4L, 5L], 3L) == [-4L, -3L, 5L]);\n    assert(candidate([4L, -4L, 4L], 2L) == [4L, 4L]);\n    assert(candidate([-3L, 2L, 1L, 2L, -1L, -2L, 1L], 1L) == [2L]);\n    assert(candidate([123L, -123L, 20L, 0L, 1L, 2L, -3L], 3L) == [2L, 20L, 123L]);\n    assert(candidate([-123L, 20L, 0L, 1L, 2L, -3L], 4L) == [0L, 1L, 2L, 20L]);\n    assert(candidate([5L, 15L, 0L, 3L, -13L, -8L, 0L], 7L) == [-13L, -8L, 0L, 0L, 3L, 5L, 15L]);\n    assert(candidate([-1L, 0L, 2L, 5L, 3L, -10L], 2L) == [3L, 5L]);\n    assert(candidate([1L, 0L, 5L, -7L], 1L) == [5L]);\n    assert(candidate([4L, -4L], 2L) == [-4L, 4L]);\n    assert(candidate([-10L, 10L], 2L) == [-10L, 10L]);\n    assert(candidate([1L, 2L, 3L, -23L, 243L, -400L, 0L], 0L) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_120_maximum", "test": "unittest\n{\n    alias candidate = maximum;\n\n    assert(candidate([-3L, -4L, 5L], 3L) == [-4L, -3L, 5L]);\n    assert(candidate([4L, -4L, 4L], 2L) == [4L, 4L]);\n    assert(candidate([-3L, 2L, 1L, 2L, -1L, -2L, 1L], 1L) == [2L]);\n    assert(candidate([123L, -123L, 20L, 0L, 1L, 2L, -3L], 3L) == [2L, 20L, 123L]);\n    assert(candidate([-123L, 20L, 0L, 1L, 2L, -3L], 4L) == [0L, 1L, 2L, 20L]);\n    assert(candidate([5L, 15L, 0L, 3L, -13L, -8L, 0L], 7L) == [-13L, -8L, 0L, 0L, 3L, 5L, 15L]);\n    assert(candidate([-1L, 0L, 2L, 5L, 3L, -10L], 2L) == [3L, 5L]);\n    assert(candidate([1L, 0L, 5L, -7L], 1L) == [5L]);\n    assert(candidate([4L, -4L], 2L) == [-4L, 4L]);\n    assert(candidate([-10L, 10L], 2L) == [-10L, 10L]);\n    assert(candidate([1L, 2L, 3L, -23L, 243L, -400L, 0L], 0L) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_24_largest_divisor", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n For a given number n, find the largest number that divides n evenly, smaller than n\n    >>> largest_divisor(15L)\n    5L\n    \n*/\nlong largest_divisor(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = largest_divisor;\n\n    assert(candidate(3L) == 1L);\n    assert(candidate(7L) == 1L);\n    assert(candidate(10L) == 5L);\n    assert(candidate(100L) == 50L);\n    assert(candidate(49L) == 7L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_24_largest_divisor", "test": "unittest\n{\n    alias candidate = largest_divisor;\n\n    assert(candidate(3L) == 1L);\n    assert(candidate(7L) == 1L);\n    assert(candidate(10L) == 5L);\n    assert(candidate(100L) == 50L);\n    assert(candidate(49L) == 7L);\n}\nvoid main(){}"}
{"name": "HumanEval_88_sort_array", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given an array of non-negative integers, return a cod of the given array after sorting,\n    you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n    or sort it in descending order if the sum( first index value, last index value) is even.\n\n    Note:\n    * don't change the given array.\n\n    Examples:\n    >>> sort_array([])\n    []\n    >>> sort_array([5L])\n    [5L]\n    >>> sort_array([2L, 4L, 3L, 0L, 1L, 5L])\n    [0L, 1L, 2L, 3L, 4L, 5L]\n    >>> sort_array([2L, 4L, 3L, 0L, 1L, 5L, 6L])\n    [6L, 5L, 4L, 3L, 2L, 1L, 0L]\n    \n*/\nlong[] sort_array(long[] array) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sort_array;\n\n    assert(candidate([]) == []);\n    assert(candidate([5L]) == [5L]);\n    assert(candidate([2L, 4L, 3L, 0L, 1L, 5L]) == [0L, 1L, 2L, 3L, 4L, 5L]);\n    assert(candidate([2L, 4L, 3L, 0L, 1L, 5L, 6L]) == [6L, 5L, 4L, 3L, 2L, 1L, 0L]);\n    assert(candidate([2L, 1L]) == [1L, 2L]);\n    assert(candidate([15L, 42L, 87L, 32L, 11L, 0L]) == [0L, 11L, 15L, 32L, 42L, 87L]);\n    assert(candidate([21L, 14L, 23L, 11L]) == [23L, 21L, 14L, 11L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_88_sort_array", "test": "unittest\n{\n    alias candidate = sort_array;\n\n    assert(candidate([]) == []);\n    assert(candidate([5L]) == [5L]);\n    assert(candidate([2L, 4L, 3L, 0L, 1L, 5L]) == [0L, 1L, 2L, 3L, 4L, 5L]);\n    assert(candidate([2L, 4L, 3L, 0L, 1L, 5L, 6L]) == [6L, 5L, 4L, 3L, 2L, 1L, 0L]);\n    assert(candidate([2L, 1L]) == [1L, 2L]);\n    assert(candidate([15L, 42L, 87L, 32L, 11L, 0L]) == [0L, 11L, 15L, 32L, 42L, 87L]);\n    assert(candidate([21L, 14L, 23L, 11L]) == [23L, 21L, 14L, 11L]);\n}\nvoid main(){}"}
{"name": "HumanEval_106_f", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Implement the function f that takes n as a parameter,\n    and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n    or the sum of numbers from 1 to i otherwise.\n    i starts from 1.\n    the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n    Example:\n    >>> f(5L)\n    [1L, 2L, 6L, 24L, 15L]\n    \n*/\nlong[] f(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = f;\n\n    assert(candidate(5L) == [1L, 2L, 6L, 24L, 15L]);\n    assert(candidate(7L) == [1L, 2L, 6L, 24L, 15L, 720L, 28L]);\n    assert(candidate(1L) == [1L]);\n    assert(candidate(3L) == [1L, 2L, 6L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_106_f", "test": "unittest\n{\n    alias candidate = f;\n\n    assert(candidate(5L) == [1L, 2L, 6L, 24L, 15L]);\n    assert(candidate(7L) == [1L, 2L, 6L, 24L, 15L, 720L, 28L]);\n    assert(candidate(1L) == [1L]);\n    assert(candidate(3L) == [1L, 2L, 6L]);\n}\nvoid main(){}"}
{"name": "HumanEval_77_iscube", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Write a function that takes an integer a and returns true \n    if this ingeger is a cube of some integer number.\n    Note: you may assume the input is always valid.\n    Examples:\n    >>> iscube(1L)\n    true\n    >>> iscube(2L)\n    false\n    >>> iscube(-1L)\n    true\n    >>> iscube(64L)\n    true\n    >>> iscube(0L)\n    true\n    >>> iscube(180L)\n    false\n    \n*/\nbool iscube(long a) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = iscube;\n\n    assert(candidate(1L) == true);\n    assert(candidate(2L) == false);\n    assert(candidate(-1L) == true);\n    assert(candidate(64L) == true);\n    assert(candidate(180L) == false);\n    assert(candidate(1000L) == true);\n    assert(candidate(0L) == true);\n    assert(candidate(1729L) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_77_iscube", "test": "unittest\n{\n    alias candidate = iscube;\n\n    assert(candidate(1L) == true);\n    assert(candidate(2L) == false);\n    assert(candidate(-1L) == true);\n    assert(candidate(64L) == true);\n    assert(candidate(180L) == false);\n    assert(candidate(1000L) == true);\n    assert(candidate(0L) == true);\n    assert(candidate(1729L) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_93_encode", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Write a function that takes a message, and encodes in such a \n    way that it swaps case of all letters, replaces all vowels in \n    the message with the letter that appears 2 places ahead of that \n    vowel in the english alphabet. \n    Assume only letters. \n    \n    Examples:\n    >>> encode(\"test\")\n    \"TGST\"\n    >>> encode(\"This is a message\")\n    \"tHKS KS C MGSSCGG\"\n    \n*/\nstring encode(string message) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = encode;\n\n    assert(candidate(\"TEST\") == \"tgst\");\n    assert(candidate(\"Mudasir\") == \"mWDCSKR\");\n    assert(candidate(\"YES\") == \"ygs\");\n    assert(candidate(\"This is a message\") == \"tHKS KS C MGSSCGG\");\n    assert(candidate(\"I DoNt KnOw WhAt tO WrItE\") == \"k dQnT kNqW wHcT Tq wRkTg\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_93_encode", "test": "unittest\n{\n    alias candidate = encode;\n\n    assert(candidate(\"TEST\") == \"tgst\");\n    assert(candidate(\"Mudasir\") == \"mWDCSKR\");\n    assert(candidate(\"YES\") == \"ygs\");\n    assert(candidate(\"This is a message\") == \"tHKS KS C MGSSCGG\");\n    assert(candidate(\"I DoNt KnOw WhAt tO WrItE\") == \"k dQnT kNqW wHcT Tq wRkTg\");\n}\nvoid main(){}"}
{"name": "HumanEval_91_is_bored", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You'll be given a string of words, and your task is to count the number\n    of boredoms. A boredom is a sentence that starts with the word \"I\".\n    Sentences are delimited by '.', '?' or '!'.\n   \n    For example:\n    >>> is_bored(\"Hello world\")\n    0L\n    >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n    1L\n    \n*/\nlong is_bored(string S) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_bored;\n\n    assert(candidate(\"Hello world\") == 0L);\n    assert(candidate(\"Is the sky blue?\") == 0L);\n    assert(candidate(\"I love It !\") == 1L);\n    assert(candidate(\"bIt\") == 0L);\n    assert(candidate(\"I feel good today. I will be productive. will kill It\") == 2L);\n    assert(candidate(\"You and I are going for a walk\") == 0L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_91_is_bored", "test": "unittest\n{\n    alias candidate = is_bored;\n\n    assert(candidate(\"Hello world\") == 0L);\n    assert(candidate(\"Is the sky blue?\") == 0L);\n    assert(candidate(\"I love It !\") == 1L);\n    assert(candidate(\"bIt\") == 0L);\n    assert(candidate(\"I feel good today. I will be productive. will kill It\") == 2L);\n    assert(candidate(\"You and I are going for a walk\") == 0L);\n}\nvoid main(){}"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    pairs_sum_to_zero takes an array of integers as an input.\n    it returns true if there are two distinct elements in the array that\n    sum to zero, and false otherwise.\n    >>> pairs_sum_to_zero([1L, 3L, 5L, 0L])\n    false\n    >>> pairs_sum_to_zero([1L, 3L, -2L, 1L])\n    false\n    >>> pairs_sum_to_zero([1L, 2L, 3L, 7L])\n    false\n    >>> pairs_sum_to_zero([2L, 4L, -5L, 3L, 5L, 7L])\n    true\n    >>> pairs_sum_to_zero([1L])\n    false\n    \n*/\nbool pairs_sum_to_zero(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = pairs_sum_to_zero;\n\n    assert(candidate([1L, 3L, 5L, 0L]) == false);\n    assert(candidate([1L, 3L, -2L, 1L]) == false);\n    assert(candidate([1L, 2L, 3L, 7L]) == false);\n    assert(candidate([2L, 4L, -5L, 3L, 5L, 7L]) == true);\n    assert(candidate([1L]) == false);\n    assert(candidate([-3L, 9L, -1L, 3L, 2L, 30L]) == true);\n    assert(candidate([-3L, 9L, -1L, 3L, 2L, 31L]) == true);\n    assert(candidate([-3L, 9L, -1L, 4L, 2L, 30L]) == false);\n    assert(candidate([-3L, 9L, -1L, 4L, 2L, 31L]) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "unittest\n{\n    alias candidate = pairs_sum_to_zero;\n\n    assert(candidate([1L, 3L, 5L, 0L]) == false);\n    assert(candidate([1L, 3L, -2L, 1L]) == false);\n    assert(candidate([1L, 2L, 3L, 7L]) == false);\n    assert(candidate([2L, 4L, -5L, 3L, 5L, 7L]) == true);\n    assert(candidate([1L]) == false);\n    assert(candidate([-3L, 9L, -1L, 3L, 2L, 30L]) == true);\n    assert(candidate([-3L, 9L, -1L, 3L, 2L, 31L]) == true);\n    assert(candidate([-3L, 9L, -1L, 4L, 2L, 30L]) == false);\n    assert(candidate([-3L, 9L, -1L, 4L, 2L, 31L]) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_71_triangle_area", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given the lengths of the three sides of a triangle. Return the area of\n    the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n    Otherwise return -1\n    Three sides make a valid triangle when the sum of any two sides is greater \n    than the third side.\n    Example:\n    >>> triangle_area(3L, 4L, 5L)\n    6.0\n    >>> triangle_area(1L, 2L, 10L)\n    -1L\n    \n*/\nfloat triangle_area(long a, long b, long c) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = triangle_area;\n\n    assert(candidate(3L, 4L, 5L) == 6.0);\n    assert(candidate(1L, 2L, 10L) == -1L);\n    assert(candidate(4L, 8L, 5L) == 8.18);\n    assert(candidate(2L, 2L, 2L) == 1.73);\n    assert(candidate(1L, 2L, 3L) == -1L);\n    assert(candidate(10L, 5L, 7L) == 16.25);\n    assert(candidate(2L, 6L, 3L) == -1L);\n    assert(candidate(1L, 1L, 1L) == 0.43);\n    assert(candidate(2L, 2L, 10L) == -1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_71_triangle_area", "test": "unittest\n{\n    alias candidate = triangle_area;\n\n    assert(candidate(3L, 4L, 5L) == 6.0);\n    assert(candidate(1L, 2L, 10L) == -1L);\n    assert(candidate(4L, 8L, 5L) == 8.18);\n    assert(candidate(2L, 2L, 2L) == 1.73);\n    assert(candidate(1L, 2L, 3L) == -1L);\n    assert(candidate(10L, 5L, 7L) == 16.25);\n    assert(candidate(2L, 6L, 3L) == -1L);\n    assert(candidate(1L, 1L, 1L) == 0.43);\n    assert(candidate(2L, 2L, 10L) == -1L);\n}\nvoid main(){}"}
{"name": "HumanEval_131_digits", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven a positive integer n, return the product of the odd digits.\n    Return 0 if all digits are even.\n    For example:\n    >>> digits(1L)\n    1L\n    >>> digits(4L)\n    0L\n    >>> digits(235L)\n    15L\n    \n*/\nlong digits(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = digits;\n\n    assert(candidate(5L) == 5L);\n    assert(candidate(54L) == 5L);\n    assert(candidate(120L) == 1L);\n    assert(candidate(5014L) == 5L);\n    assert(candidate(98765L) == 315L);\n    assert(candidate(5576543L) == 2625L);\n    assert(candidate(2468L) == 0L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_131_digits", "test": "unittest\n{\n    alias candidate = digits;\n\n    assert(candidate(5L) == 5L);\n    assert(candidate(54L) == 5L);\n    assert(candidate(120L) == 1L);\n    assert(candidate(5014L) == 5L);\n    assert(candidate(98765L) == 315L);\n    assert(candidate(5576543L) == 2625L);\n    assert(candidate(2468L) == 0L);\n}\nvoid main(){}"}
{"name": "HumanEval_101_words_string", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You will be given a string of words separated by commas or spaces. Your task is\n    to split the string into words and return an array of the words.\n    \n    For example:\n    >>> words_string(\"Hi, my name is John\")\n    [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n    >>> words_string(\"One, two, three, four, five, six\")\n    [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\n    \n*/\nstring[] words_string(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = words_string;\n\n    assert(candidate(\"Hi, my name is John\") == [\"Hi\", \"my\", \"name\", \"is\", \"John\"]);\n    assert(candidate(\"One, two, three, four, five, six\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]);\n    assert(candidate(\"Hi, my name\") == [\"Hi\", \"my\", \"name\"]);\n    assert(candidate(\"One,, two, three, four, five, six,\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]);\n    assert(candidate(\"\") == []);\n    assert(candidate(\"ahmed     , gamal\") == [\"ahmed\", \"gamal\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_101_words_string", "test": "unittest\n{\n    alias candidate = words_string;\n\n    assert(candidate(\"Hi, my name is John\") == [\"Hi\", \"my\", \"name\", \"is\", \"John\"]);\n    assert(candidate(\"One, two, three, four, five, six\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]);\n    assert(candidate(\"Hi, my name\") == [\"Hi\", \"my\", \"name\"]);\n    assert(candidate(\"One,, two, three, four, five, six,\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]);\n    assert(candidate(\"\") == []);\n    assert(candidate(\"ahmed     , gamal\") == [\"ahmed\", \"gamal\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_18_how_many_times", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Find how many times a given substring can be found in the original string. Count overlaping cases.\n    >>> how_many_times(\"\", \"a\")\n    0L\n    >>> how_many_times(\"aaa\", \"a\")\n    3L\n    >>> how_many_times(\"aaaa\", \"aa\")\n    3L\n    \n*/\nlong how_many_times(string string, string substring) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = how_many_times;\n\n    assert(candidate(\"\", \"x\") == 0L);\n    assert(candidate(\"xyxyxyx\", \"x\") == 4L);\n    assert(candidate(\"cacacacac\", \"cac\") == 4L);\n    assert(candidate(\"john doe\", \"john\") == 1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_18_how_many_times", "test": "unittest\n{\n    alias candidate = how_many_times;\n\n    assert(candidate(\"\", \"x\") == 0L);\n    assert(candidate(\"xyxyxyx\", \"x\") == 4L);\n    assert(candidate(\"cacacacac\", \"cac\") == 4L);\n    assert(candidate(\"john doe\", \"john\") == 1L);\n}\nvoid main(){}"}
{"name": "HumanEval_51_remove_vowels", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    remove_vowels is a function that takes string and returns string without vowels.\n    >>> remove_vowels(\"\")\n    \"\"\n    >>> remove_vowels(\"abcdef\")\n    \"bcdf\"\n    >>> remove_vowels(\"aaaaa\")\n    \"\"\n    >>> remove_vowels(\"aaBAA\")\n    \"B\"\n    >>> remove_vowels(\"zbcd\")\n    \"zbcd\"\n    \n*/\nstring remove_vowels(string text) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = remove_vowels;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"abcdef\nghijklm\") == \"bcdf\nghjklm\");\n    assert(candidate(\"fedcba\") == \"fdcb\");\n    assert(candidate(\"eeeee\") == \"\");\n    assert(candidate(\"acBAA\") == \"cB\");\n    assert(candidate(\"EcBOO\") == \"cB\");\n    assert(candidate(\"ybcd\") == \"ybcd\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_51_remove_vowels", "test": "unittest\n{\n    alias candidate = remove_vowels;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"abcdef\nghijklm\") == \"bcdf\nghjklm\");\n    assert(candidate(\"fedcba\") == \"fdcb\");\n    assert(candidate(\"eeeee\") == \"\");\n    assert(candidate(\"acBAA\") == \"cB\");\n    assert(candidate(\"EcBOO\") == \"cB\");\n    assert(candidate(\"ybcd\") == \"ybcd\");\n}\nvoid main(){}"}
{"name": "HumanEval_70_strange_sort_list", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given array of integers, return array in strange order.\n    Strange sorting, is when you start with the minimum value,\n    then maximum of the remaining integers, then minimum and so on.\n\n    Examples:\n    >>> strange_sort_list([1L, 2L, 3L, 4L])\n    [1L, 4L, 2L, 3L]\n    >>> strange_sort_list([5L, 5L, 5L, 5L])\n    [5L, 5L, 5L, 5L]\n    >>> strange_sort_list([])\n    []\n    \n*/\nlong[] strange_sort_list(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = strange_sort_list;\n\n    assert(candidate([1L, 2L, 3L, 4L]) == [1L, 4L, 2L, 3L]);\n    assert(candidate([5L, 6L, 7L, 8L, 9L]) == [5L, 9L, 6L, 8L, 7L]);\n    assert(candidate([1L, 2L, 3L, 4L, 5L]) == [1L, 5L, 2L, 4L, 3L]);\n    assert(candidate([5L, 6L, 7L, 8L, 9L, 1L]) == [1L, 9L, 5L, 8L, 6L, 7L]);\n    assert(candidate([5L, 5L, 5L, 5L]) == [5L, 5L, 5L, 5L]);\n    assert(candidate([]) == []);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L]) == [1L, 8L, 2L, 7L, 3L, 6L, 4L, 5L]);\n    assert(candidate([0L, 2L, 2L, 2L, 5L, 5L, -5L, -5L]) == [-5L, 5L, -5L, 5L, 0L, 2L, 2L, 2L]);\n    assert(candidate([111111L]) == [111111L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_70_strange_sort_list", "test": "unittest\n{\n    alias candidate = strange_sort_list;\n\n    assert(candidate([1L, 2L, 3L, 4L]) == [1L, 4L, 2L, 3L]);\n    assert(candidate([5L, 6L, 7L, 8L, 9L]) == [5L, 9L, 6L, 8L, 7L]);\n    assert(candidate([1L, 2L, 3L, 4L, 5L]) == [1L, 5L, 2L, 4L, 3L]);\n    assert(candidate([5L, 6L, 7L, 8L, 9L, 1L]) == [1L, 9L, 5L, 8L, 6L, 7L]);\n    assert(candidate([5L, 5L, 5L, 5L]) == [5L, 5L, 5L, 5L]);\n    assert(candidate([]) == []);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L]) == [1L, 8L, 2L, 7L, 3L, 6L, 4L, 5L]);\n    assert(candidate([0L, 2L, 2L, 2L, 5L, 5L, -5L, -5L]) == [-5L, 5L, -5L, 5L, 0L, 2L, 2L, 2L]);\n    assert(candidate([111111L]) == [111111L]);\n}\nvoid main(){}"}
{"name": "HumanEval_20_find_closest_elements", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n    other and return them in order (smaller number, larger number).\n    >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n    tuple(2.0, 2.2)\n    >>> find_closest_elements([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n    tuple(2.0, 2.0)\n    \n*/\nTuple!(float, float) find_closest_elements(float[] numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = find_closest_elements;\n\n    assert(candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2]) == tuple(3.9, 4.0));\n    assert(candidate([1.0, 2.0, 5.9, 4.0, 5.0]) == tuple(5.0, 5.9));\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.2]) == tuple(2.0, 2.2));\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0]) == tuple(2.0, 2.0));\n    assert(candidate([1.1, 2.2, 3.1, 4.1, 5.1]) == tuple(2.2, 3.1));\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_20_find_closest_elements", "test": "unittest\n{\n    alias candidate = find_closest_elements;\n\n    assert(candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2]) == tuple(3.9, 4.0));\n    assert(candidate([1.0, 2.0, 5.9, 4.0, 5.0]) == tuple(5.0, 5.9));\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.2]) == tuple(2.0, 2.2));\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0]) == tuple(2.0, 2.0));\n    assert(candidate([1.1, 2.2, 3.1, 4.1, 5.1]) == tuple(2.2, 3.1));\n}\nvoid main(){}"}
{"name": "HumanEval_76_is_simple_power", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYour task is to write a function that returns true if a number x is a simple\n    power of n and false in other cases.\n    x is a simple power of n if n**int=x\n    For example:\n    >>> is_simple_power(1L, 4L)\n    true\n    >>> is_simple_power(2L, 2L)\n    true\n    >>> is_simple_power(8L, 2L)\n    true\n    >>> is_simple_power(3L, 2L)\n    false\n    >>> is_simple_power(3L, 1L)\n    false\n    >>> is_simple_power(5L, 3L)\n    false\n    \n*/\nbool is_simple_power(long x, long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_simple_power;\n\n    assert(candidate(16L, 2L) == true);\n    assert(candidate(143214L, 16L) == false);\n    assert(candidate(4L, 2L) == true);\n    assert(candidate(9L, 3L) == true);\n    assert(candidate(16L, 4L) == true);\n    assert(candidate(24L, 2L) == false);\n    assert(candidate(128L, 4L) == false);\n    assert(candidate(12L, 6L) == false);\n    assert(candidate(1L, 1L) == true);\n    assert(candidate(1L, 12L) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_76_is_simple_power", "test": "unittest\n{\n    alias candidate = is_simple_power;\n\n    assert(candidate(16L, 2L) == true);\n    assert(candidate(143214L, 16L) == false);\n    assert(candidate(4L, 2L) == true);\n    assert(candidate(9L, 3L) == true);\n    assert(candidate(16L, 4L) == true);\n    assert(candidate(24L, 2L) == false);\n    assert(candidate(128L, 4L) == false);\n    assert(candidate(12L, 6L) == false);\n    assert(candidate(1L, 1L) == true);\n    assert(candidate(1L, 12L) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_39_prime_fib", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n    >>> prime_fib(1L)\n    2L\n    >>> prime_fib(2L)\n    3L\n    >>> prime_fib(3L)\n    5L\n    >>> prime_fib(4L)\n    13L\n    >>> prime_fib(5L)\n    89L\n    \n*/\nlong prime_fib(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = prime_fib;\n\n    assert(candidate(1L) == 2L);\n    assert(candidate(2L) == 3L);\n    assert(candidate(3L) == 5L);\n    assert(candidate(4L) == 13L);\n    assert(candidate(5L) == 89L);\n    assert(candidate(6L) == 233L);\n    assert(candidate(7L) == 1597L);\n    assert(candidate(8L) == 28657L);\n    assert(candidate(9L) == 514229L);\n    assert(candidate(10L) == 433494437L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_39_prime_fib", "test": "unittest\n{\n    alias candidate = prime_fib;\n\n    assert(candidate(1L) == 2L);\n    assert(candidate(2L) == 3L);\n    assert(candidate(3L) == 5L);\n    assert(candidate(4L) == 13L);\n    assert(candidate(5L) == 89L);\n    assert(candidate(6L) == 233L);\n    assert(candidate(7L) == 1597L);\n    assert(candidate(8L) == 28657L);\n    assert(candidate(9L) == 514229L);\n    assert(candidate(10L) == 433494437L);\n}\nvoid main(){}"}
{"name": "HumanEval_145_order_by_points", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Write a function which sorts the given array of integers\n    in ascending order according to the sum of their digits.\n    Note: if there are several items with similar sum of their digits,\n    order them based on their index in original array.\n\n    For example:\n    >>> order_by_points([1L, 11L, -1L, -11L, -12L])\n    [-1L, -11L, 1L, -12L, 11L]\n    >>> order_by_points([])\n    []\n    \n*/\nlong[] order_by_points(long[] nums) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = order_by_points;\n\n    assert(candidate([1L, 11L, -1L, -11L, -12L]) == [-1L, -11L, 1L, -12L, 11L]);\n    assert(candidate([1234L, 423L, 463L, 145L, 2L, 423L, 423L, 53L, 6L, 37L, 3457L, 3L, 56L, 0L, 46L]) == [0L, 2L, 3L, 6L, 53L, 423L, 423L, 423L, 1234L, 145L, 37L, 46L, 56L, 463L, 3457L]);\n    assert(candidate([]) == []);\n    assert(candidate([1L, -11L, -32L, 43L, 54L, -98L, 2L, -3L]) == [-3L, -32L, -98L, -11L, 1L, 2L, 43L, 54L]);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L, 11L]) == [1L, 10L, 2L, 11L, 3L, 4L, 5L, 6L, 7L, 8L, 9L]);\n    assert(candidate([0L, 6L, 6L, -76L, -21L, 23L, 4L]) == [-76L, -21L, 0L, 4L, 23L, 6L, 6L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_145_order_by_points", "test": "unittest\n{\n    alias candidate = order_by_points;\n\n    assert(candidate([1L, 11L, -1L, -11L, -12L]) == [-1L, -11L, 1L, -12L, 11L]);\n    assert(candidate([1234L, 423L, 463L, 145L, 2L, 423L, 423L, 53L, 6L, 37L, 3457L, 3L, 56L, 0L, 46L]) == [0L, 2L, 3L, 6L, 53L, 423L, 423L, 423L, 1234L, 145L, 37L, 46L, 56L, 463L, 3457L]);\n    assert(candidate([]) == []);\n    assert(candidate([1L, -11L, -32L, 43L, 54L, -98L, 2L, -3L]) == [-3L, -32L, -98L, -11L, 1L, 2L, 43L, 54L]);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L, 11L]) == [1L, 10L, 2L, 11L, 3L, 4L, 5L, 6L, 7L, 8L, 9L]);\n    assert(candidate([0L, 6L, 6L, -76L, -21L, 23L, 4L]) == [-76L, -21L, 0L, 4L, 23L, 6L, 6L]);\n}\nvoid main(){}"}
{"name": "HumanEval_0_has_close_elements", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Check if in given array of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    false\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    true\n    \n*/\nbool has_close_elements(float[] numbers, float threshold) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = has_close_elements;\n\n    assert(candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3) == true);\n    assert(candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05) == false);\n    assert(candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.95) == true);\n    assert(candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.8) == false);\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1) == true);\n    assert(candidate([1.1, 2.2, 3.1, 4.1, 5.1], 1.0) == true);\n    assert(candidate([1.1, 2.2, 3.1, 4.1, 5.1], 0.5) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_0_has_close_elements", "test": "unittest\n{\n    alias candidate = has_close_elements;\n\n    assert(candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3) == true);\n    assert(candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05) == false);\n    assert(candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.95) == true);\n    assert(candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.8) == false);\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1) == true);\n    assert(candidate([1.1, 2.2, 3.1, 4.1, 5.1], 1.0) == true);\n    assert(candidate([1.1, 2.2, 3.1, 4.1, 5.1], 0.5) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_10_make_palindrome", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Find the shortest palindrome that begins with a supplied string.\n    Algorithm idea is simple:\n    - Find the longest postfix of supplied string that is a palindrome.\n    - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n    >>> make_palindrome(\"\")\n    \"\"\n    >>> make_palindrome(\"cat\")\n    \"catac\"\n    >>> make_palindrome(\"cata\")\n    \"catac\"\n    \n*/\nstring make_palindrome(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = make_palindrome;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"x\") == \"x\");\n    assert(candidate(\"xyz\") == \"xyzyx\");\n    assert(candidate(\"xyx\") == \"xyx\");\n    assert(candidate(\"jerry\") == \"jerryrrej\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_10_make_palindrome", "test": "unittest\n{\n    alias candidate = make_palindrome;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"x\") == \"x\");\n    assert(candidate(\"xyz\") == \"xyzyx\");\n    assert(candidate(\"xyx\") == \"xyx\");\n    assert(candidate(\"jerry\") == \"jerryrrej\");\n}\nvoid main(){}"}
{"name": "HumanEval_11_string_xor", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Input are two strings a and b consisting only of 1s and 0s.\n    Perform binary XOR on these inputs and return result also as a string.\n    >>> string_xor(\"010\", \"110\")\n    \"100\"\n    \n*/\nstring string_xor(string a, string b) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = string_xor;\n\n    assert(candidate(\"111000\", \"101010\") == \"010010\");\n    assert(candidate(\"1\", \"1\") == \"0\");\n    assert(candidate(\"0101\", \"0000\") == \"0101\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_11_string_xor", "test": "unittest\n{\n    alias candidate = string_xor;\n\n    assert(candidate(\"111000\", \"101010\") == \"010010\");\n    assert(candidate(\"1\", \"1\") == \"0\");\n    assert(candidate(\"0101\", \"0000\") == \"0101\");\n}\nvoid main(){}"}
{"name": "HumanEval_139_special_factorial", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nThe Brazilian factorial is defined as:\n    brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n    where n > 0\n\n    For example:\n    >>> special_factorial(4L)\n    288L\n\n    The function will receive an integer as input and should return the special\n    factorial of this integer.\n    \n*/\nlong special_factorial(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = special_factorial;\n\n    assert(candidate(4L) == 288L);\n    assert(candidate(5L) == 34560L);\n    assert(candidate(7L) == 125411328000L);\n    assert(candidate(1L) == 1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_139_special_factorial", "test": "unittest\n{\n    alias candidate = special_factorial;\n\n    assert(candidate(4L) == 288L);\n    assert(candidate(5L) == 34560L);\n    assert(candidate(7L) == 125411328000L);\n    assert(candidate(1L) == 1L);\n}\nvoid main(){}"}
{"name": "HumanEval_122_add_elements", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a non-empty array of integers arr and an integer k, return\n    the sum of the elements with at most two digits from the first k elements of arr.\n\n    Example:\n\n    >>> add_elements([111L, 21L, 3L, 4000L, 5L, 6L, 7L, 8L, 9L], 4L)\n    24L\n\n    Constraints:\n        1. 1 <= len(arr) <= 100\n        2. 1 <= k <= len(arr)\n    \n*/\nlong add_elements(long[] arr, long k) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = add_elements;\n\n    assert(candidate([1L, -2L, -3L, 41L, 57L, 76L, 87L, 88L, 99L], 3L) == -4L);\n    assert(candidate([111L, 121L, 3L, 4000L, 5L, 6L], 2L) == 0L);\n    assert(candidate([11L, 21L, 3L, 90L, 5L, 6L, 7L, 8L, 9L], 4L) == 125L);\n    assert(candidate([111L, 21L, 3L, 4000L, 5L, 6L, 7L, 8L, 9L], 4L) == 24L);\n    assert(candidate([1L], 1L) == 1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_122_add_elements", "test": "unittest\n{\n    alias candidate = add_elements;\n\n    assert(candidate([1L, -2L, -3L, 41L, 57L, 76L, 87L, 88L, 99L], 3L) == -4L);\n    assert(candidate([111L, 121L, 3L, 4000L, 5L, 6L], 2L) == 0L);\n    assert(candidate([11L, 21L, 3L, 90L, 5L, 6L, 7L, 8L, 9L], 4L) == 125L);\n    assert(candidate([111L, 21L, 3L, 4000L, 5L, 6L, 7L, 8L, 9L], 4L) == 24L);\n    assert(candidate([1L], 1L) == 1L);\n}\nvoid main(){}"}
{"name": "HumanEval_46_fib4", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nThe Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n    fib4(0) -> 0\n    fib4(1) -> 0\n    fib4(2) -> 2\n    fib4(3) -> 0\n    fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n    Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n    >>> fib4(5L)\n    4L\n    >>> fib4(6L)\n    8L\n    >>> fib4(7L)\n    14L\n    \n*/\nlong fib4(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = fib4;\n\n    assert(candidate(5L) == 4L);\n    assert(candidate(8L) == 28L);\n    assert(candidate(10L) == 104L);\n    assert(candidate(12L) == 386L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_46_fib4", "test": "unittest\n{\n    alias candidate = fib4;\n\n    assert(candidate(5L) == 4L);\n    assert(candidate(8L) == 28L);\n    assert(candidate(10L) == 104L);\n    assert(candidate(12L) == 386L);\n}\nvoid main(){}"}
{"name": "HumanEval_104_unique_digits", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven an array of positive integers x. return a sorted array of all \n    elements that hasn't any even digit.\n\n    Note: Returned array should be sorted in increasing order.\n    \n    For example:\n    >>> unique_digits([15L, 33L, 1422L, 1L])\n    [1L, 15L, 33L]\n    >>> unique_digits([152L, 323L, 1422L, 10L])\n    []\n    \n*/\nlong[] unique_digits(long[] x) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = unique_digits;\n\n    assert(candidate([15L, 33L, 1422L, 1L]) == [1L, 15L, 33L]);\n    assert(candidate([152L, 323L, 1422L, 10L]) == []);\n    assert(candidate([12345L, 2033L, 111L, 151L]) == [111L, 151L]);\n    assert(candidate([135L, 103L, 31L]) == [31L, 135L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_104_unique_digits", "test": "unittest\n{\n    alias candidate = unique_digits;\n\n    assert(candidate([15L, 33L, 1422L, 1L]) == [1L, 15L, 33L]);\n    assert(candidate([152L, 323L, 1422L, 10L]) == []);\n    assert(candidate([12345L, 2033L, 111L, 151L]) == [111L, 151L]);\n    assert(candidate([135L, 103L, 31L]) == [31L, 135L]);\n}\nvoid main(){}"}
{"name": "HumanEval_117_select_words", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven a string s and a natural number n, you have been tasked to implement \n    a function that returns an array of all words from string s that contain exactly \n    n consonants, in order these words appear in the string s.\n    If the string s is empty then the function should return an empty array.\n    Note: you may assume the input string contains only letters and spaces.\n    Examples:\n    >>> select_words(\"Mary had a little lamb\", 4L)\n    [\"little\"]\n    >>> select_words(\"Mary had a little lamb\", 3L)\n    [\"Mary\", \"lamb\"]\n    >>> select_words(\"simple white space\", 2L)\n    []\n    >>> select_words(\"Hello world\", 4L)\n    [\"world\"]\n    >>> select_words(\"Uncle sam\", 3L)\n    [\"Uncle\"]\n    \n*/\nstring[] select_words(string s, long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = select_words;\n\n    assert(candidate(\"Mary had a little lamb\", 4L) == [\"little\"]);\n    assert(candidate(\"Mary had a little lamb\", 3L) == [\"Mary\", \"lamb\"]);\n    assert(candidate(\"simple white space\", 2L) == []);\n    assert(candidate(\"Hello world\", 4L) == [\"world\"]);\n    assert(candidate(\"Uncle sam\", 3L) == [\"Uncle\"]);\n    assert(candidate(\"\", 4L) == []);\n    assert(candidate(\"a b c d e f\", 1L) == [\"b\", \"c\", \"d\", \"f\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_117_select_words", "test": "unittest\n{\n    alias candidate = select_words;\n\n    assert(candidate(\"Mary had a little lamb\", 4L) == [\"little\"]);\n    assert(candidate(\"Mary had a little lamb\", 3L) == [\"Mary\", \"lamb\"]);\n    assert(candidate(\"simple white space\", 2L) == []);\n    assert(candidate(\"Hello world\", 4L) == [\"world\"]);\n    assert(candidate(\"Uncle sam\", 3L) == [\"Uncle\"]);\n    assert(candidate(\"\", 4L) == []);\n    assert(candidate(\"a b c d e f\", 1L) == [\"b\", \"c\", \"d\", \"f\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_72_will_it_fly", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Write a function that returns true if the object q will fly, and false otherwise.\n    The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n\n    Example:\n    >>> will_it_fly([1L, 2L], 5L)\n    false\n    # 1+2 is less than the maximum possible weight, but it's unbalanced.\n\n    >>> will_it_fly([3L, 2L, 3L], 1L)\n    false\n    # it's balanced, but 3+2+3 is more than the maximum possible weight.\n\n    >>> will_it_fly([3L, 2L, 3L], 9L)\n    true\n    # 3+2+3 is less than the maximum possible weight, and it's balanced.\n\n    >>> will_it_fly([3L], 5L)\n    true\n    # 3 is less than the maximum possible weight, and it's balanced.\n    \n*/\nbool will_it_fly(long[] q, long w) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = will_it_fly;\n\n    assert(candidate([3L, 2L, 3L], 9L) == true);\n    assert(candidate([1L, 2L], 5L) == false);\n    assert(candidate([3L], 5L) == true);\n    assert(candidate([3L, 2L, 3L], 1L) == false);\n    assert(candidate([1L, 2L, 3L], 6L) == false);\n    assert(candidate([5L], 5L) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_72_will_it_fly", "test": "unittest\n{\n    alias candidate = will_it_fly;\n\n    assert(candidate([3L, 2L, 3L], 9L) == true);\n    assert(candidate([1L, 2L], 5L) == false);\n    assert(candidate([3L], 5L) == true);\n    assert(candidate([3L, 2L, 3L], 1L) == false);\n    assert(candidate([1L, 2L, 3L], 6L) == false);\n    assert(candidate([5L], 5L) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_55_fib", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn n-th Fibonacci number.\n    >>> fib(10L)\n    55L\n    >>> fib(1L)\n    1L\n    >>> fib(8L)\n    21L\n    \n*/\nlong fib(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = fib;\n\n    assert(candidate(10L) == 55L);\n    assert(candidate(1L) == 1L);\n    assert(candidate(8L) == 21L);\n    assert(candidate(11L) == 89L);\n    assert(candidate(12L) == 144L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_55_fib", "test": "unittest\n{\n    alias candidate = fib;\n\n    assert(candidate(10L) == 55L);\n    assert(candidate(1L) == 1L);\n    assert(candidate(8L) == 21L);\n    assert(candidate(11L) == 89L);\n    assert(candidate(12L) == 144L);\n}\nvoid main(){}"}
{"name": "HumanEval_153_Strongest_Extension", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou will be given the name of a class (a string) and an array of extensions.\n    The extensions are to be used to load additional classes to the class. The\n    strength of the extension is as follows: Let CAP be the number of the uppercase\n    letters in the extension's name, and let SM be the number of lowercase letters \n    in the extension's name, the strength is given by the fraction CAP - SM. \n    You should find the strongest extension and return a string in this \n    format: ClassName.StrongestExtensionName.\n    If there are two or more extensions with the same strength, you should\n    choose the one that comes first in the array.\n    For example, if you are given \"Slices\" as the class and an array of the\n    extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    (its strength is -1).\n    Example:\n    >>> Strongest_Extension(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n    \"my_class.AA\"\n    \n*/\nstring Strongest_Extension(string class_name, string[] extensions) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = Strongest_Extension;\n\n    assert(candidate(\"Watashi\", [\"tEN\", \"niNE\", \"eIGHt8OKe\"]) == \"Watashi.eIGHt8OKe\");\n    assert(candidate(\"Boku123\", [\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"]) == \"Boku123.YEs.WeCaNe\");\n    assert(candidate(\"__YESIMHERE\", [\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"]) == \"__YESIMHERE.NuLl__\");\n    assert(candidate(\"K\", [\"Ta\", \"TAR\", \"t234An\", \"cosSo\"]) == \"K.TAR\");\n    assert(candidate(\"__HAHA\", [\"Tab\", \"123\", \"781345\", \"-_-\"]) == \"__HAHA.123\");\n    assert(candidate(\"YameRore\", [\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"]) == \"YameRore.okIWILL123\");\n    assert(candidate(\"finNNalLLly\", [\"Die\", \"NowW\", \"Wow\", \"WoW\"]) == \"finNNalLLly.WoW\");\n    assert(candidate(\"_\", [\"Bb\", \"91245\"]) == \"_.Bb\");\n    assert(candidate(\"Sp\", [\"671235\", \"Bb\"]) == \"Sp.671235\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_153_Strongest_Extension", "test": "unittest\n{\n    alias candidate = Strongest_Extension;\n\n    assert(candidate(\"Watashi\", [\"tEN\", \"niNE\", \"eIGHt8OKe\"]) == \"Watashi.eIGHt8OKe\");\n    assert(candidate(\"Boku123\", [\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"]) == \"Boku123.YEs.WeCaNe\");\n    assert(candidate(\"__YESIMHERE\", [\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"]) == \"__YESIMHERE.NuLl__\");\n    assert(candidate(\"K\", [\"Ta\", \"TAR\", \"t234An\", \"cosSo\"]) == \"K.TAR\");\n    assert(candidate(\"__HAHA\", [\"Tab\", \"123\", \"781345\", \"-_-\"]) == \"__HAHA.123\");\n    assert(candidate(\"YameRore\", [\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"]) == \"YameRore.okIWILL123\");\n    assert(candidate(\"finNNalLLly\", [\"Die\", \"NowW\", \"Wow\", \"WoW\"]) == \"finNNalLLly.WoW\");\n    assert(candidate(\"_\", [\"Bb\", \"91245\"]) == \"_.Bb\");\n    assert(candidate(\"Sp\", [\"671235\", \"Bb\"]) == \"Sp.671235\");\n}\nvoid main(){}"}
{"name": "HumanEval_119_match_parens", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given an array of two strings, both strings consist of open\n    parentheses '(' or close parentheses ')' only.\n    Your job is to check if it is possible to concatenate the two strings in\n    some order, that the resulting string will be good.\n    A string S is considered to be good if and only if all parentheses in S\n    are balanced. For example: the string '(())()' is good, while the string\n    '())' is not.\n    Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n\n    Examples:\n    >>> match_parens([\"()(\", \")\"])\n    \"Yes\"\n    >>> match_parens([\")\", \")\"])\n    \"No\"\n    \n*/\nstring match_parens(string[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = match_parens;\n\n    assert(candidate([\"()(\", \")\"]) == \"Yes\");\n    assert(candidate([\")\", \")\"]) == \"No\");\n    assert(candidate([\"(()(())\", \"())())\"]) == \"No\");\n    assert(candidate([\")())\", \"(()()(\"]) == \"Yes\");\n    assert(candidate([\"(())))\", \"(()())((\"]) == \"Yes\");\n    assert(candidate([\"()\", \"())\"]) == \"No\");\n    assert(candidate([\"(()(\", \"()))()\"]) == \"Yes\");\n    assert(candidate([\"((((\", \"((())\"]) == \"No\");\n    assert(candidate([\")(()\", \"(()(\"]) == \"No\");\n    assert(candidate([\")(\", \")(\"]) == \"No\");\n    assert(candidate([\"(\", \")\"]) == \"Yes\");\n    assert(candidate([\")\", \"(\"]) == \"Yes\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_119_match_parens", "test": "unittest\n{\n    alias candidate = match_parens;\n\n    assert(candidate([\"()(\", \")\"]) == \"Yes\");\n    assert(candidate([\")\", \")\"]) == \"No\");\n    assert(candidate([\"(()(())\", \"())())\"]) == \"No\");\n    assert(candidate([\")())\", \"(()()(\"]) == \"Yes\");\n    assert(candidate([\"(())))\", \"(()())((\"]) == \"Yes\");\n    assert(candidate([\"()\", \"())\"]) == \"No\");\n    assert(candidate([\"(()(\", \"()))()\"]) == \"Yes\");\n    assert(candidate([\"((((\", \"((())\"]) == \"No\");\n    assert(candidate([\")(()\", \"(()(\"]) == \"No\");\n    assert(candidate([\")(\", \")(\"]) == \"No\");\n    assert(candidate([\"(\", \")\"]) == \"Yes\");\n    assert(candidate([\")\", \"(\"]) == \"Yes\");\n}\nvoid main(){}"}
{"name": "HumanEval_90_next_smallest", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given an array of integers.\n    Write a function next_smallest() that returns the 2nd smallest element of the array.\n    Return null if there is no such element.\n    >>> next_smallest([1L, 2L, 3L, 4L, 5L])\n    2L\n    >>> next_smallest([5L, 1L, 4L, 3L, 2L])\n    2L\n    >>> next_smallest([])\n    None\n    >>> next_smallest([1L, 1L])\n    None\n    \n*/\nNullable!(long) next_smallest(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = next_smallest;\n\n{\n        auto result = candidate([1L, 2L, 3L, 4L, 5L]);\n        assert(!result.isNull && result.get == 2L);\n}\n\n{\n        auto result = candidate([5L, 1L, 4L, 3L, 2L]);\n        assert(!result.isNull && result.get == 2L);\n}\n\n{\n        auto result = candidate([]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([1L, 1L]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([1L, 1L, 1L, 1L, 0L]);\n        assert(!result.isNull && result.get == 1L);\n}\n\n{\n        auto result = candidate([1L, 1L]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([-35L, 34L, 12L, -45L]);\n        assert(!result.isNull && result.get == -35L);\n}\n\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_90_next_smallest", "test": "unittest\n{\n    alias candidate = next_smallest;\n\n{\n        auto result = candidate([1L, 2L, 3L, 4L, 5L]);\n        assert(!result.isNull && result.get == 2L);\n}\n\n{\n        auto result = candidate([5L, 1L, 4L, 3L, 2L]);\n        assert(!result.isNull && result.get == 2L);\n}\n\n{\n        auto result = candidate([]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([1L, 1L]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([1L, 1L, 1L, 1L, 0L]);\n        assert(!result.isNull && result.get == 1L);\n}\n\n{\n        auto result = candidate([1L, 1L]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([-35L, 34L, 12L, -45L]);\n        assert(!result.isNull && result.get == -35L);\n}\n\n}\nvoid main(){}"}
{"name": "HumanEval_92_any_int", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Create a function that takes 3 numbers.\n    Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n    Returns false in any other cases.\n    \n    Examples\n    >>> any_int(5L, 2L, 7L)\n    true\n    \n    >>> any_int(3L, 2L, 2L)\n    false\n\n    >>> any_int(3L, -2L, 1L)\n    true\n    \n    >>> any_int(3.6, -2.2, 2L)\n    false\n  \n\n    \n    \n*/\nbool any_int(float x, float y, float z) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = any_int;\n\n    assert(candidate(2L, 3L, 1L) == true);\n    assert(candidate(2.5, 2L, 3L) == false);\n    assert(candidate(1.5, 5L, 3.5) == false);\n    assert(candidate(2L, 6L, 2L) == false);\n    assert(candidate(4L, 2L, 2L) == true);\n    assert(candidate(2.2, 2.2, 2.2) == false);\n    assert(candidate(-4L, 6L, 2L) == true);\n    assert(candidate(2L, 1L, 1L) == true);\n    assert(candidate(3L, 4L, 7L) == true);\n    assert(candidate(3.0, 4L, 7L) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_92_any_int", "test": "unittest\n{\n    alias candidate = any_int;\n\n    assert(candidate(2L, 3L, 1L) == true);\n    assert(candidate(2.5, 2L, 3L) == false);\n    assert(candidate(1.5, 5L, 3.5) == false);\n    assert(candidate(2L, 6L, 2L) == false);\n    assert(candidate(4L, 2L, 2L) == true);\n    assert(candidate(2.2, 2.2, 2.2) == false);\n    assert(candidate(-4L, 6L, 2L) == true);\n    assert(candidate(2L, 1L, 1L) == true);\n    assert(candidate(3L, 4L, 7L) == true);\n    assert(candidate(3.0, 4L, 7L) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_2_truncate_number", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Given a positive floating point number, it can be decomposed into\n    and integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    >>> truncate_number(3.5)\n    0.5\n    \n*/\nfloat truncate_number(float number) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = truncate_number;\n\n    assert(candidate(3.5) == 0.5);\n    assert(candidate(1.25) == 0.25);\n    assert(candidate(123.0) == 0.0);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_2_truncate_number", "test": "unittest\n{\n    alias candidate = truncate_number;\n\n    assert(candidate(3.5) == 0.5);\n    assert(candidate(1.25) == 0.25);\n    assert(candidate(123.0) == 0.0);\n}\nvoid main(){}"}
{"name": "HumanEval_42_incr_list", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn array with elements incremented by 1.\n    >>> incr_list([1L, 2L, 3L])\n    [2L, 3L, 4L]\n    >>> incr_list([5L, 3L, 5L, 2L, 3L, 3L, 9L, 0L, 123L])\n    [6L, 4L, 6L, 3L, 4L, 4L, 10L, 1L, 124L]\n    \n*/\nlong[] incr_list(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = incr_list;\n\n    assert(candidate([]) == []);\n    assert(candidate([3L, 2L, 1L]) == [4L, 3L, 2L]);\n    assert(candidate([5L, 2L, 5L, 2L, 3L, 3L, 9L, 0L, 123L]) == [6L, 3L, 6L, 3L, 4L, 4L, 10L, 1L, 124L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_42_incr_list", "test": "unittest\n{\n    alias candidate = incr_list;\n\n    assert(candidate([]) == []);\n    assert(candidate([3L, 2L, 1L]) == [4L, 3L, 2L]);\n    assert(candidate([5L, 2L, 5L, 2L, 3L, 3L, 9L, 0L, 123L]) == [6L, 3L, 6L, 3L, 4L, 4L, 10L, 1L, 124L]);\n}\nvoid main(){}"}
{"name": "HumanEval_150_x_or_y", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nA simple program which should return the value of x if n is \n    a prime number and should return the value of y otherwise.\n\n    Examples:\n    >>> x_or_y(7L, 34L, 12L)\n    34L\n    >>> x_or_y(15L, 8L, 5L)\n    5L\n    \n    \n*/\nlong x_or_y(long n, long x, long y) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = x_or_y;\n\n    assert(candidate(7L, 34L, 12L) == 34L);\n    assert(candidate(15L, 8L, 5L) == 5L);\n    assert(candidate(3L, 33L, 5212L) == 33L);\n    assert(candidate(1259L, 3L, 52L) == 3L);\n    assert(candidate(7919L, -1L, 12L) == -1L);\n    assert(candidate(3609L, 1245L, 583L) == 583L);\n    assert(candidate(91L, 56L, 129L) == 129L);\n    assert(candidate(6L, 34L, 1234L) == 1234L);\n    assert(candidate(1L, 2L, 0L) == 0L);\n    assert(candidate(2L, 2L, 0L) == 2L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_150_x_or_y", "test": "unittest\n{\n    alias candidate = x_or_y;\n\n    assert(candidate(7L, 34L, 12L) == 34L);\n    assert(candidate(15L, 8L, 5L) == 5L);\n    assert(candidate(3L, 33L, 5212L) == 33L);\n    assert(candidate(1259L, 3L, 52L) == 3L);\n    assert(candidate(7919L, -1L, 12L) == -1L);\n    assert(candidate(3609L, 1245L, 583L) == 583L);\n    assert(candidate(91L, 56L, 129L) == 129L);\n    assert(candidate(6L, 34L, 1234L) == 1234L);\n    assert(candidate(1L, 2L, 0L) == 0L);\n    assert(candidate(2L, 2L, 0L) == 2L);\n}\nvoid main(){}"}
{"name": "HumanEval_49_modp", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn 2^n modulo p (be aware of numerics).\n    >>> modp(3L, 5L)\n    3L\n    >>> modp(1101L, 101L)\n    2L\n    >>> modp(0L, 101L)\n    1L\n    >>> modp(3L, 11L)\n    8L\n    >>> modp(100L, 101L)\n    1L\n    \n*/\nlong modp(long n, long p) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = modp;\n\n    assert(candidate(3L, 5L) == 3L);\n    assert(candidate(1101L, 101L) == 2L);\n    assert(candidate(0L, 101L) == 1L);\n    assert(candidate(3L, 11L) == 8L);\n    assert(candidate(100L, 101L) == 1L);\n    assert(candidate(30L, 5L) == 4L);\n    assert(candidate(31L, 5L) == 3L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_49_modp", "test": "unittest\n{\n    alias candidate = modp;\n\n    assert(candidate(3L, 5L) == 3L);\n    assert(candidate(1101L, 101L) == 2L);\n    assert(candidate(0L, 101L) == 1L);\n    assert(candidate(3L, 11L) == 8L);\n    assert(candidate(100L, 101L) == 1L);\n    assert(candidate(30L, 5L) == 4L);\n    assert(candidate(31L, 5L) == 3L);\n}\nvoid main(){}"}
{"name": "HumanEval_155_even_odd_count", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven an integer. return a tuple that has the number of even and odd digits respectively.\n\n     Example:\n    >>> even_odd_count(-12L)\n    tuple(1L, 1L)\n    >>> even_odd_count(123L)\n    tuple(1L, 2L)\n    \n*/\nTuple!(long, long) even_odd_count(long num) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = even_odd_count;\n\n    assert(candidate(7L) == tuple(0L, 1L));\n    assert(candidate(-78L) == tuple(1L, 1L));\n    assert(candidate(3452L) == tuple(2L, 2L));\n    assert(candidate(346211L) == tuple(3L, 3L));\n    assert(candidate(-345821L) == tuple(3L, 3L));\n    assert(candidate(-2L) == tuple(1L, 0L));\n    assert(candidate(-45347L) == tuple(2L, 3L));\n    assert(candidate(0L) == tuple(1L, 0L));\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_155_even_odd_count", "test": "unittest\n{\n    alias candidate = even_odd_count;\n\n    assert(candidate(7L) == tuple(0L, 1L));\n    assert(candidate(-78L) == tuple(1L, 1L));\n    assert(candidate(3452L) == tuple(2L, 2L));\n    assert(candidate(346211L) == tuple(3L, 3L));\n    assert(candidate(-345821L) == tuple(3L, 3L));\n    assert(candidate(-2L) == tuple(1L, 0L));\n    assert(candidate(-45347L) == tuple(2L, 3L));\n    assert(candidate(0L) == tuple(1L, 0L));\n}\nvoid main(){}"}
{"name": "HumanEval_80_is_happy", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given a string s.\n    Your task is to check if the string is hapd or not.\n    A string is hapd if its length is at least 3 and every 3 consecutive letters are distinct\n    For example:\n    >>> is_happy(\"a\")\n    false\n    >>> is_happy(\"aa\")\n    false\n    >>> is_happy(\"abcd\")\n    true\n    >>> is_happy(\"aabb\")\n    false\n    >>> is_happy(\"adb\")\n    true\n    >>> is_happy(\"xyy\")\n    false\n    \n*/\nbool is_happy(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_happy;\n\n    assert(candidate(\"a\") == false);\n    assert(candidate(\"aa\") == false);\n    assert(candidate(\"abcd\") == true);\n    assert(candidate(\"aabb\") == false);\n    assert(candidate(\"adb\") == true);\n    assert(candidate(\"xyy\") == false);\n    assert(candidate(\"iopaxpoi\") == true);\n    assert(candidate(\"iopaxioi\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_80_is_happy", "test": "unittest\n{\n    alias candidate = is_happy;\n\n    assert(candidate(\"a\") == false);\n    assert(candidate(\"aa\") == false);\n    assert(candidate(\"abcd\") == true);\n    assert(candidate(\"aabb\") == false);\n    assert(candidate(\"adb\") == true);\n    assert(candidate(\"xyy\") == false);\n    assert(candidate(\"iopaxpoi\") == true);\n    assert(candidate(\"iopaxioi\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_59_largest_prime_factor", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn the largest prime factor of n. Assume n > 1 and is not a prime.\n    >>> largest_prime_factor(13195L)\n    29L\n    >>> largest_prime_factor(2048L)\n    2L\n    \n*/\nlong largest_prime_factor(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = largest_prime_factor;\n\n    assert(candidate(15L) == 5L);\n    assert(candidate(27L) == 3L);\n    assert(candidate(63L) == 7L);\n    assert(candidate(330L) == 11L);\n    assert(candidate(13195L) == 29L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_59_largest_prime_factor", "test": "unittest\n{\n    alias candidate = largest_prime_factor;\n\n    assert(candidate(15L) == 5L);\n    assert(candidate(27L) == 3L);\n    assert(candidate(63L) == 7L);\n    assert(candidate(330L) == 11L);\n    assert(candidate(13195L) == 29L);\n}\nvoid main(){}"}
{"name": "HumanEval_66_digitSum", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nTask\n    Write a function that takes a string as input and returns the sum of the upper characters only'\n    ASCII codes.\n\n    Examples:\n    >>> digitSum(\"\")\n    0L\n    >>> digitSum(\"abAB\")\n    131L\n    >>> digitSum(\"abcCd\")\n    67L\n    >>> digitSum(\"helloE\")\n    69L\n    >>> digitSum(\"woArBld\")\n    131L\n    >>> digitSum(\"aAaaaXa\")\n    153L\n    \n*/\nlong digitSum(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = digitSum;\n\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"abAB\") == 131L);\n    assert(candidate(\"abcCd\") == 67L);\n    assert(candidate(\"helloE\") == 69L);\n    assert(candidate(\"woArBld\") == 131L);\n    assert(candidate(\"aAaaaXa\") == 153L);\n    assert(candidate(\" How are yOu?\") == 151L);\n    assert(candidate(\"You arE Very Smart\") == 327L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_66_digitSum", "test": "unittest\n{\n    alias candidate = digitSum;\n\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"abAB\") == 131L);\n    assert(candidate(\"abcCd\") == 67L);\n    assert(candidate(\"helloE\") == 69L);\n    assert(candidate(\"woArBld\") == 131L);\n    assert(candidate(\"aAaaaXa\") == 153L);\n    assert(candidate(\" How are yOu?\") == 151L);\n    assert(candidate(\"You arE Very Smart\") == 327L);\n}\nvoid main(){}"}
{"name": "HumanEval_21_rescale_to_unit", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Given array of numbers (of at least two elements), apply a linear transform to that array,\n    such that the smallest number will become 0 and the largest will become 1\n    >>> rescale_to_unit([1.0, 2.0, 3.0, 4.0, 5.0])\n    [0.0, 0.25, 0.5, 0.75, 1.0]\n    \n*/\nfloat[] rescale_to_unit(float[] numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = rescale_to_unit;\n\n    assert(candidate([2.0, 49.9]) == [0.0, 1.0]);\n    assert(candidate([100.0, 49.9]) == [1.0, 0.0]);\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0]) == [0.0, 0.25, 0.5, 0.75, 1.0]);\n    assert(candidate([2.0, 1.0, 5.0, 3.0, 4.0]) == [0.25, 0.0, 1.0, 0.5, 0.75]);\n    assert(candidate([12.0, 11.0, 15.0, 13.0, 14.0]) == [0.25, 0.0, 1.0, 0.5, 0.75]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_21_rescale_to_unit", "test": "unittest\n{\n    alias candidate = rescale_to_unit;\n\n    assert(candidate([2.0, 49.9]) == [0.0, 1.0]);\n    assert(candidate([100.0, 49.9]) == [1.0, 0.0]);\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0]) == [0.0, 0.25, 0.5, 0.75, 1.0]);\n    assert(candidate([2.0, 1.0, 5.0, 3.0, 4.0]) == [0.25, 0.0, 1.0, 0.5, 0.75]);\n    assert(candidate([12.0, 11.0, 15.0, 13.0, 14.0]) == [0.25, 0.0, 1.0, 0.5, 0.75]);\n}\nvoid main(){}"}
{"name": "HumanEval_121_solution", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n    \n\n    Examples\n    >>> solution([5L, 8L, 7L, 1L])\n    12L\n    >>> solution([3L, 3L, 3L, 3L, 3L])\n    9L\n    >>> solution([30L, 13L, 24L, 321L])\n    0L\n    \n*/\nlong solution(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = solution;\n\n    assert(candidate([5L, 8L, 7L, 1L]) == 12L);\n    assert(candidate([3L, 3L, 3L, 3L, 3L]) == 9L);\n    assert(candidate([30L, 13L, 24L, 321L]) == 0L);\n    assert(candidate([5L, 9L]) == 5L);\n    assert(candidate([2L, 4L, 8L]) == 0L);\n    assert(candidate([30L, 13L, 23L, 32L]) == 23L);\n    assert(candidate([3L, 13L, 2L, 9L]) == 3L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_121_solution", "test": "unittest\n{\n    alias candidate = solution;\n\n    assert(candidate([5L, 8L, 7L, 1L]) == 12L);\n    assert(candidate([3L, 3L, 3L, 3L, 3L]) == 9L);\n    assert(candidate([30L, 13L, 24L, 321L]) == 0L);\n    assert(candidate([5L, 9L]) == 5L);\n    assert(candidate([2L, 4L, 8L]) == 0L);\n    assert(candidate([30L, 13L, 23L, 32L]) == 23L);\n    assert(candidate([3L, 13L, 2L, 9L]) == 3L);\n}\nvoid main(){}"}
{"name": "HumanEval_68_pluck", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    \"Given an array representing a branch of a tree that has non-negative integer nodes\n    your task is to pluck one of the nodes and return it.\n    The plucked node should be the node with the smallest even value.\n    If multiple nodes with the same smallest even value are found return the node that has smallest index.\n\n    The plucked node should be returned in an array, [ smalest_value, its index ],\n    If there are no even values or the given array is empty, return [].\n\n    Example 1:\n    >>> pluck([4L, 2L, 3L])\n    [2L, 1L]\n    Explanation: 2 has the smallest even value, and 2 has the smallest index.\n\n    Example 2:\n    >>> pluck([1L, 2L, 3L])\n    [2L, 1L]\n    Explanation: 2 has the smallest even value, and 2 has the smallest index.\n\n    Example 3:\n    >>> pluck([])\n    []\n    \n    Example 4:\n    >>> pluck([5L, 0L, 3L, 0L, 4L, 2L])\n    [0L, 1L]\n    Explanation: 0 is the smallest value, but  there are two zeros,\n                 so we will choose the first zero, which has the smallest index.\n\n    Constraints:\n        * 1 <= nodes.length <= 10000\n        * 0 <= node.value\n    \n*/\nlong[] pluck(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = pluck;\n\n    assert(candidate([4L, 2L, 3L]) == [2L, 1L]);\n    assert(candidate([1L, 2L, 3L]) == [2L, 1L]);\n    assert(candidate([]) == []);\n    assert(candidate([5L, 0L, 3L, 0L, 4L, 2L]) == [0L, 1L]);\n    assert(candidate([1L, 2L, 3L, 0L, 5L, 3L]) == [0L, 3L]);\n    assert(candidate([5L, 4L, 8L, 4L, 8L]) == [4L, 1L]);\n    assert(candidate([7L, 6L, 7L, 1L]) == [6L, 1L]);\n    assert(candidate([7L, 9L, 7L, 1L]) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_68_pluck", "test": "unittest\n{\n    alias candidate = pluck;\n\n    assert(candidate([4L, 2L, 3L]) == [2L, 1L]);\n    assert(candidate([1L, 2L, 3L]) == [2L, 1L]);\n    assert(candidate([]) == []);\n    assert(candidate([5L, 0L, 3L, 0L, 4L, 2L]) == [0L, 1L]);\n    assert(candidate([1L, 2L, 3L, 0L, 5L, 3L]) == [0L, 3L]);\n    assert(candidate([5L, 4L, 8L, 4L, 8L]) == [4L, 1L]);\n    assert(candidate([7L, 6L, 7L, 1L]) == [6L, 1L]);\n    assert(candidate([7L, 9L, 7L, 1L]) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_147_get_max_triples", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given a positive integer n. You have to create an integer array a of length n.\n        For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n        Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n    and a[i] + a[j] + a[k] is a multiple of 3.\n\n    Example :\n    >>> get_max_triples(5L)\n    1L\n        Explanation: \n        a = [1, 3, 7, 13, 21]\n        The only valid triple is (1, 7, 13).\n    \n*/\nlong get_max_triples(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = get_max_triples;\n\n    assert(candidate(5L) == 1L);\n    assert(candidate(6L) == 4L);\n    assert(candidate(10L) == 36L);\n    assert(candidate(100L) == 53361L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_147_get_max_triples", "test": "unittest\n{\n    alias candidate = get_max_triples;\n\n    assert(candidate(5L) == 1L);\n    assert(candidate(6L) == 4L);\n    assert(candidate(10L) == 36L);\n    assert(candidate(100L) == 53361L);\n}\nvoid main(){}"}
{"name": "HumanEval_110_exchange", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nIn this problem, you will implement a function that takes two arrays of numbers,\n    and determines whether it is possible to perform an exchange of elements\n    between them to make lst1 an array of only even numbers.\n    There is no limit on the number of exchanged elements between lst1 and lst2.\n    If it is possible to exchange elements between the lst1 and lst2 to make\n    all the elements of lst1 to be even, return \"YES\".\n    Otherwise, return \"NO\".\n    For example:\n    >>> exchange([1L, 2L, 3L, 4L], [1L, 2L, 3L, 4L])\n    \"YES\"\n    >>> exchange([1L, 2L, 3L, 4L], [1L, 5L, 3L, 4L])\n    \"NO\"\n    It is assumed that the input arrays will be non-empty.\n    \n*/\nstring exchange(long[] lst1, long[] lst2) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = exchange;\n\n    assert(candidate([1L, 2L, 3L, 4L], [1L, 2L, 3L, 4L]) == \"YES\");\n    assert(candidate([1L, 2L, 3L, 4L], [1L, 5L, 3L, 4L]) == \"NO\");\n    assert(candidate([1L, 2L, 3L, 4L], [2L, 1L, 4L, 3L]) == \"YES\");\n    assert(candidate([5L, 7L, 3L], [2L, 6L, 4L]) == \"YES\");\n    assert(candidate([5L, 7L, 3L], [2L, 6L, 3L]) == \"NO\");\n    assert(candidate([3L, 2L, 6L, 1L, 8L, 9L], [3L, 5L, 5L, 1L, 1L, 1L]) == \"NO\");\n    assert(candidate([100L, 200L], [200L, 200L]) == \"YES\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_110_exchange", "test": "unittest\n{\n    alias candidate = exchange;\n\n    assert(candidate([1L, 2L, 3L, 4L], [1L, 2L, 3L, 4L]) == \"YES\");\n    assert(candidate([1L, 2L, 3L, 4L], [1L, 5L, 3L, 4L]) == \"NO\");\n    assert(candidate([1L, 2L, 3L, 4L], [2L, 1L, 4L, 3L]) == \"YES\");\n    assert(candidate([5L, 7L, 3L], [2L, 6L, 4L]) == \"YES\");\n    assert(candidate([5L, 7L, 3L], [2L, 6L, 3L]) == \"NO\");\n    assert(candidate([3L, 2L, 6L, 1L, 8L, 9L], [3L, 5L, 5L, 1L, 1L, 1L]) == \"NO\");\n    assert(candidate([100L, 200L], [200L, 200L]) == \"YES\");\n}\nvoid main(){}"}
{"name": "HumanEval_47_median", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn median of elements in the array l.\n    >>> median([3L, 1L, 2L, 4L, 5L])\n    3L\n    >>> median([-10L, 4L, 6L, 1000L, 10L, 20L])\n    15.0\n    \n*/\nfloat median(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = median;\n\n    assert(candidate([3L, 1L, 2L, 4L, 5L]) == 3L);\n    assert(candidate([-10L, 4L, 6L, 1000L, 10L, 20L]) == 8.0);\n    assert(candidate([5L]) == 5L);\n    assert(candidate([6L, 5L]) == 5.5);\n    assert(candidate([8L, 1L, 3L, 9L, 9L, 2L, 7L]) == 7L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_47_median", "test": "unittest\n{\n    alias candidate = median;\n\n    assert(candidate([3L, 1L, 2L, 4L, 5L]) == 3L);\n    assert(candidate([-10L, 4L, 6L, 1000L, 10L, 20L]) == 8.0);\n    assert(candidate([5L]) == 5L);\n    assert(candidate([6L, 5L]) == 5.5);\n    assert(candidate([8L, 1L, 3L, 9L, 9L, 2L, 7L]) == 7L);\n}\nvoid main(){}"}
{"name": "HumanEval_82_prime_length", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nWrite a function that takes a string and returns true if the string\n    length is a prime number or false otherwise\n    Examples\n    >>> prime_length(\"Hello\")\n    true\n    >>> prime_length(\"abcdcba\")\n    true\n    >>> prime_length(\"kittens\")\n    true\n    >>> prime_length(\"orange\")\n    false\n    \n*/\nbool prime_length(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = prime_length;\n\n    assert(candidate(\"Hello\") == true);\n    assert(candidate(\"abcdcba\") == true);\n    assert(candidate(\"kittens\") == true);\n    assert(candidate(\"orange\") == false);\n    assert(candidate(\"wow\") == true);\n    assert(candidate(\"world\") == true);\n    assert(candidate(\"MadaM\") == true);\n    assert(candidate(\"Wow\") == true);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"HI\") == true);\n    assert(candidate(\"go\") == true);\n    assert(candidate(\"gogo\") == false);\n    assert(candidate(\"aaaaaaaaaaaaaaa\") == false);\n    assert(candidate(\"Madam\") == true);\n    assert(candidate(\"M\") == false);\n    assert(candidate(\"0\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_82_prime_length", "test": "unittest\n{\n    alias candidate = prime_length;\n\n    assert(candidate(\"Hello\") == true);\n    assert(candidate(\"abcdcba\") == true);\n    assert(candidate(\"kittens\") == true);\n    assert(candidate(\"orange\") == false);\n    assert(candidate(\"wow\") == true);\n    assert(candidate(\"world\") == true);\n    assert(candidate(\"MadaM\") == true);\n    assert(candidate(\"Wow\") == true);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"HI\") == true);\n    assert(candidate(\"go\") == true);\n    assert(candidate(\"gogo\") == false);\n    assert(candidate(\"aaaaaaaaaaaaaaa\") == false);\n    assert(candidate(\"Madam\") == true);\n    assert(candidate(\"M\") == false);\n    assert(candidate(\"0\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_73_smallest_change", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given an array arr of integers, find the minimum number of elements that\n    need to be changed to make the array palindromic. A palindromic array is an array that\n    is read the same backwards and forwards. In one change, you can change one element to any other element.\n\n    For example:\n    >>> smallest_change([1L, 2L, 3L, 5L, 4L, 7L, 9L, 6L])\n    4L\n    >>> smallest_change([1L, 2L, 3L, 4L, 3L, 2L, 2L])\n    1L\n    >>> smallest_change([1L, 2L, 3L, 2L, 1L])\n    0L\n    \n*/\nlong smallest_change(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = smallest_change;\n\n    assert(candidate([1L, 2L, 3L, 5L, 4L, 7L, 9L, 6L]) == 4L);\n    assert(candidate([1L, 2L, 3L, 4L, 3L, 2L, 2L]) == 1L);\n    assert(candidate([1L, 4L, 2L]) == 1L);\n    assert(candidate([1L, 4L, 4L, 2L]) == 1L);\n    assert(candidate([1L, 2L, 3L, 2L, 1L]) == 0L);\n    assert(candidate([3L, 1L, 1L, 3L]) == 0L);\n    assert(candidate([1L]) == 0L);\n    assert(candidate([0L, 1L]) == 1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_73_smallest_change", "test": "unittest\n{\n    alias candidate = smallest_change;\n\n    assert(candidate([1L, 2L, 3L, 5L, 4L, 7L, 9L, 6L]) == 4L);\n    assert(candidate([1L, 2L, 3L, 4L, 3L, 2L, 2L]) == 1L);\n    assert(candidate([1L, 4L, 2L]) == 1L);\n    assert(candidate([1L, 4L, 4L, 2L]) == 1L);\n    assert(candidate([1L, 2L, 3L, 2L, 1L]) == 0L);\n    assert(candidate([3L, 1L, 1L, 3L]) == 0L);\n    assert(candidate([1L]) == 0L);\n    assert(candidate([0L, 1L]) == 1L);\n}\nvoid main(){}"}
{"name": "HumanEval_133_sum_squares", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given an array of numbers.\n    You need to return the sum of squared numbers in the given array,\n    round each element in the array to the upper int(Ceiling) first.\n    Examples:\n    >>> lst([1.0, 2.0, 3.0])\n    14L\n    >>> lst([1.0, 4.0, 9.0])\n    98L\n    >>> lst([1.0, 3.0, 5.0, 7.0])\n    84L\n    >>> lst([1.4, 4.2, 0.0])\n    29L\n    >>> lst([-2.4, 1.0, 1.0])\n    6L\n    \n\n    \n*/\nlong sum_squares(float[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sum_squares;\n\n    assert(candidate([1.0, 2.0, 3.0]) == 14L);\n    assert(candidate([1.0, 2.0, 3.0]) == 14L);\n    assert(candidate([1.0, 3.0, 5.0, 7.0]) == 84L);\n    assert(candidate([1.4, 4.2, 0.0]) == 29L);\n    assert(candidate([-2.4, 1.0, 1.0]) == 6L);\n    assert(candidate([100.0, 1.0, 15.0, 2.0]) == 10230L);\n    assert(candidate([10000.0, 10000.0]) == 200000000L);\n    assert(candidate([-1.4, 4.6, 6.3]) == 75L);\n    assert(candidate([-1.4, 17.9, 18.9, 19.9]) == 1086L);\n    assert(candidate([0.0]) == 0L);\n    assert(candidate([-1.0]) == 1L);\n    assert(candidate([-1.0, 1.0, 0.0]) == 2L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_133_sum_squares", "test": "unittest\n{\n    alias candidate = sum_squares;\n\n    assert(candidate([1.0, 2.0, 3.0]) == 14L);\n    assert(candidate([1.0, 2.0, 3.0]) == 14L);\n    assert(candidate([1.0, 3.0, 5.0, 7.0]) == 84L);\n    assert(candidate([1.4, 4.2, 0.0]) == 29L);\n    assert(candidate([-2.4, 1.0, 1.0]) == 6L);\n    assert(candidate([100.0, 1.0, 15.0, 2.0]) == 10230L);\n    assert(candidate([10000.0, 10000.0]) == 200000000L);\n    assert(candidate([-1.4, 4.6, 6.3]) == 75L);\n    assert(candidate([-1.4, 17.9, 18.9, 19.9]) == 1086L);\n    assert(candidate([0.0]) == 0L);\n    assert(candidate([-1.0]) == 1L);\n    assert(candidate([-1.0, 1.0, 0.0]) == 2L);\n}\nvoid main(){}"}
{"name": "HumanEval_141_file_name_check", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nCreate a function which takes a string representing a file's name, and returns\n    'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphapet ('a'-'z' and 'A'-'Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    Examples:\n    >>> file_name_check(\"example.txt\")\n    \"Yes\"\n    >>> file_name_check(\"1example.dll\")\n    \"No\"\n    \n*/\nstring file_name_check(string file_name) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = file_name_check;\n\n    assert(candidate(\"example.txt\") == \"Yes\");\n    assert(candidate(\"1example.dll\") == \"No\");\n    assert(candidate(\"s1sdf3.asd\") == \"No\");\n    assert(candidate(\"K.dll\") == \"Yes\");\n    assert(candidate(\"MY16FILE3.exe\") == \"Yes\");\n    assert(candidate(\"His12FILE94.exe\") == \"No\");\n    assert(candidate(\"_Y.txt\") == \"No\");\n    assert(candidate(\"?aREYA.exe\") == \"No\");\n    assert(candidate(\"/this_is_valid.dll\") == \"No\");\n    assert(candidate(\"this_is_valid.wow\") == \"No\");\n    assert(candidate(\"this_is_valid.txt\") == \"Yes\");\n    assert(candidate(\"this_is_valid.txtexe\") == \"No\");\n    assert(candidate(\"#this2_i4s_5valid.ten\") == \"No\");\n    assert(candidate(\"@this1_is6_valid.exe\") == \"No\");\n    assert(candidate(\"this_is_12valid.6exe4.txt\") == \"No\");\n    assert(candidate(\"all.exe.txt\") == \"No\");\n    assert(candidate(\"I563_No.exe\") == \"Yes\");\n    assert(candidate(\"Is3youfault.txt\") == \"Yes\");\n    assert(candidate(\"no_one#knows.dll\") == \"Yes\");\n    assert(candidate(\"1I563_Yes3.exe\") == \"No\");\n    assert(candidate(\"I563_Yes3.txtt\") == \"No\");\n    assert(candidate(\"final..txt\") == \"No\");\n    assert(candidate(\"final132\") == \"No\");\n    assert(candidate(\"_f4indsartal132.\") == \"No\");\n    assert(candidate(\".txt\") == \"No\");\n    assert(candidate(\"s.\") == \"No\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_141_file_name_check", "test": "unittest\n{\n    alias candidate = file_name_check;\n\n    assert(candidate(\"example.txt\") == \"Yes\");\n    assert(candidate(\"1example.dll\") == \"No\");\n    assert(candidate(\"s1sdf3.asd\") == \"No\");\n    assert(candidate(\"K.dll\") == \"Yes\");\n    assert(candidate(\"MY16FILE3.exe\") == \"Yes\");\n    assert(candidate(\"His12FILE94.exe\") == \"No\");\n    assert(candidate(\"_Y.txt\") == \"No\");\n    assert(candidate(\"?aREYA.exe\") == \"No\");\n    assert(candidate(\"/this_is_valid.dll\") == \"No\");\n    assert(candidate(\"this_is_valid.wow\") == \"No\");\n    assert(candidate(\"this_is_valid.txt\") == \"Yes\");\n    assert(candidate(\"this_is_valid.txtexe\") == \"No\");\n    assert(candidate(\"#this2_i4s_5valid.ten\") == \"No\");\n    assert(candidate(\"@this1_is6_valid.exe\") == \"No\");\n    assert(candidate(\"this_is_12valid.6exe4.txt\") == \"No\");\n    assert(candidate(\"all.exe.txt\") == \"No\");\n    assert(candidate(\"I563_No.exe\") == \"Yes\");\n    assert(candidate(\"Is3youfault.txt\") == \"Yes\");\n    assert(candidate(\"no_one#knows.dll\") == \"Yes\");\n    assert(candidate(\"1I563_Yes3.exe\") == \"No\");\n    assert(candidate(\"I563_Yes3.txtt\") == \"No\");\n    assert(candidate(\"final..txt\") == \"No\");\n    assert(candidate(\"final132\") == \"No\");\n    assert(candidate(\"_f4indsartal132.\") == \"No\");\n    assert(candidate(\".txt\") == \"No\");\n    assert(candidate(\"s.\") == \"No\");\n}\nvoid main(){}"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    triples_sum_to_zero takes an array of integers as an input.\n    it returns true if there are three distinct elements in the array that\n    sum to zero, and false otherwise.\n\n    >>> triples_sum_to_zero([1L, 3L, 5L, 0L])\n    false\n    >>> triples_sum_to_zero([1L, 3L, -2L, 1L])\n    true\n    >>> triples_sum_to_zero([1L, 2L, 3L, 7L])\n    false\n    >>> triples_sum_to_zero([2L, 4L, -5L, 3L, 9L, 7L])\n    true\n    >>> triples_sum_to_zero([1L])\n    false\n    \n*/\nbool triples_sum_to_zero(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = triples_sum_to_zero;\n\n    assert(candidate([1L, 3L, 5L, 0L]) == false);\n    assert(candidate([1L, 3L, 5L, -1L]) == false);\n    assert(candidate([1L, 3L, -2L, 1L]) == true);\n    assert(candidate([1L, 2L, 3L, 7L]) == false);\n    assert(candidate([1L, 2L, 5L, 7L]) == false);\n    assert(candidate([2L, 4L, -5L, 3L, 9L, 7L]) == true);\n    assert(candidate([1L]) == false);\n    assert(candidate([1L, 3L, 5L, -100L]) == false);\n    assert(candidate([100L, 3L, 5L, -100L]) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "unittest\n{\n    alias candidate = triples_sum_to_zero;\n\n    assert(candidate([1L, 3L, 5L, 0L]) == false);\n    assert(candidate([1L, 3L, 5L, -1L]) == false);\n    assert(candidate([1L, 3L, -2L, 1L]) == true);\n    assert(candidate([1L, 2L, 3L, 7L]) == false);\n    assert(candidate([1L, 2L, 5L, 7L]) == false);\n    assert(candidate([2L, 4L, -5L, 3L, 9L, 7L]) == true);\n    assert(candidate([1L]) == false);\n    assert(candidate([1L, 3L, 5L, -100L]) == false);\n    assert(candidate([100L, 3L, 5L, -100L]) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_127_intersection", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given two intervals,\n    where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n    The given intervals are closed which means that the interval (start, end)\n    includes both start and end.\n    For each given interval, it is assumed that its start is less or equal its end.\n    Your task is to determine whether the length of intersection of these two \n    intervals is a prime number.\n    Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n    which its length is 1, which not a prime number.\n    If the length of the intersection is a prime number, return \"YES\",\n    otherwise, return \"NO\".\n    If the two intervals don't intersect, return \"NO\".\n\n\n    [input/output] samples:\n    >>> intersection(tuple(1L, 2L), tuple(2L, 3L))\n    \"NO\"\n    >>> intersection(tuple(-1L, 1L), tuple(0L, 4L))\n    \"NO\"\n    >>> intersection(tuple(-3L, -1L), tuple(-5L, 5L))\n    \"YES\"\n    \n*/\nstring intersection(Tuple!(long, long) interval1, Tuple!(long, long) interval2) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = intersection;\n\n    assert(candidate(tuple(1L, 2L), tuple(2L, 3L)) == \"NO\");\n    assert(candidate(tuple(-1L, 1L), tuple(0L, 4L)) == \"NO\");\n    assert(candidate(tuple(-3L, -1L), tuple(-5L, 5L)) == \"YES\");\n    assert(candidate(tuple(-2L, 2L), tuple(-4L, 0L)) == \"YES\");\n    assert(candidate(tuple(-11L, 2L), tuple(-1L, -1L)) == \"NO\");\n    assert(candidate(tuple(1L, 2L), tuple(3L, 5L)) == \"NO\");\n    assert(candidate(tuple(1L, 2L), tuple(1L, 2L)) == \"NO\");\n    assert(candidate(tuple(-2L, -2L), tuple(-3L, -2L)) == \"NO\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_127_intersection", "test": "unittest\n{\n    alias candidate = intersection;\n\n    assert(candidate(tuple(1L, 2L), tuple(2L, 3L)) == \"NO\");\n    assert(candidate(tuple(-1L, 1L), tuple(0L, 4L)) == \"NO\");\n    assert(candidate(tuple(-3L, -1L), tuple(-5L, 5L)) == \"YES\");\n    assert(candidate(tuple(-2L, 2L), tuple(-4L, 0L)) == \"YES\");\n    assert(candidate(tuple(-11L, 2L), tuple(-1L, -1L)) == \"NO\");\n    assert(candidate(tuple(1L, 2L), tuple(3L, 5L)) == \"NO\");\n    assert(candidate(tuple(1L, 2L), tuple(1L, 2L)) == \"NO\");\n    assert(candidate(tuple(-2L, -2L), tuple(-3L, -2L)) == \"NO\");\n}\nvoid main(){}"}
{"name": "HumanEval_1_separate_paren_groups", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n    separate those group into separate strings and return the array of those.\n    Separate groups are balanced (each open brace is properly closed) and not nested within each other\n    Ignore any spaces in the input string.\n    >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n    [\"()\", \"(())\", \"(()())\"]\n    \n*/\nstring[] separate_paren_groups(string paren_string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = separate_paren_groups;\n\n    assert(candidate(\"(()()) ((())) () ((())()())\") == [\"(()())\", \"((()))\", \"()\", \"((())()())\"]);\n    assert(candidate(\"() (()) ((())) (((())))\") == [\"()\", \"(())\", \"((()))\", \"(((())))\"]);\n    assert(candidate(\"(()(())((())))\") == [\"(()(())((())))\"]);\n    assert(candidate(\"( ) (( )) (( )( ))\") == [\"()\", \"(())\", \"(()())\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_1_separate_paren_groups", "test": "unittest\n{\n    alias candidate = separate_paren_groups;\n\n    assert(candidate(\"(()()) ((())) () ((())()())\") == [\"(()())\", \"((()))\", \"()\", \"((())()())\"]);\n    assert(candidate(\"() (()) ((())) (((())))\") == [\"()\", \"(())\", \"((()))\", \"(((())))\"]);\n    assert(candidate(\"(()(())((())))\") == [\"(()(())((())))\"]);\n    assert(candidate(\"( ) (( )) (( )( ))\") == [\"()\", \"(())\", \"(()())\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_152_compare", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nI think we all remember that feeling when the result of some long-awaited\n    event is finally known. The feelings and thoughts you have at that moment are\n    definitely worth noting down and comparing.\n    Your task is to determine if a person correctly guessed the results of a number of matches.\n    You are given two arrays of scores and guesses of equal length, where each index shows a match. \n    Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n    the value is 0, and if not, the value is the absolute difference between the guess and the score.\n    \n    \n    example:\n\n    >>> compare([1L, 2L, 3L, 4L, 5L, 1L], [1L, 2L, 3L, 4L, 2L, -2L])\n    [0L, 0L, 0L, 0L, 3L, 3L]\n    >>> compare([0L, 5L, 0L, 0L, 0L, 4L], [4L, 1L, 1L, 0L, 0L, -2L])\n    [4L, 4L, 1L, 0L, 0L, 6L]\n    \n*/\nlong[] compare(long[] game, long[] guess) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = compare;\n\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 1L], [1L, 2L, 3L, 4L, 2L, -2L]) == [0L, 0L, 0L, 0L, 3L, 3L]);\n    assert(candidate([0L, 0L, 0L, 0L, 0L, 0L], [0L, 0L, 0L, 0L, 0L, 0L]) == [0L, 0L, 0L, 0L, 0L, 0L]);\n    assert(candidate([1L, 2L, 3L], [-1L, -2L, -3L]) == [2L, 4L, 6L]);\n    assert(candidate([1L, 2L, 3L, 5L], [-1L, 2L, 3L, 4L]) == [2L, 0L, 0L, 1L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_152_compare", "test": "unittest\n{\n    alias candidate = compare;\n\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 1L], [1L, 2L, 3L, 4L, 2L, -2L]) == [0L, 0L, 0L, 0L, 3L, 3L]);\n    assert(candidate([0L, 0L, 0L, 0L, 0L, 0L], [0L, 0L, 0L, 0L, 0L, 0L]) == [0L, 0L, 0L, 0L, 0L, 0L]);\n    assert(candidate([1L, 2L, 3L], [-1L, -2L, -3L]) == [2L, 4L, 6L]);\n    assert(candidate([1L, 2L, 3L, 5L], [-1L, 2L, 3L, 4L]) == [2L, 0L, 0L, 1L]);\n}\nvoid main(){}"}
{"name": "HumanEval_83_starts_one_ends", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a positive integer n, return the count of the numbers of n-digit\n    positive integers that start or end with 1.\n    \n*/\nlong starts_one_ends(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = starts_one_ends;\n\n    assert(candidate(1L) == 1L);\n    assert(candidate(2L) == 18L);\n    assert(candidate(3L) == 180L);\n    assert(candidate(4L) == 1800L);\n    assert(candidate(5L) == 18000L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_83_starts_one_ends", "test": "unittest\n{\n    alias candidate = starts_one_ends;\n\n    assert(candidate(1L) == 1L);\n    assert(candidate(2L) == 18L);\n    assert(candidate(3L) == 180L);\n    assert(candidate(4L) == 1800L);\n    assert(candidate(5L) == 18000L);\n}\nvoid main(){}"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Create a function that returns true if the last character\n    of a given string is an alphabetical character and is not\n    a part of a word, and false otherwise.\n    Note: \"word\" is a group of characters separated by space.\n\n    Examples:\n    >>> check_if_last_char_is_a_letter(\"apple pie\")\n    false\n    >>> check_if_last_char_is_a_letter(\"apple pi e\")\n    true\n    >>> check_if_last_char_is_a_letter(\"apple pi e \")\n    false\n    >>> check_if_last_char_is_a_letter(\"\")\n    false\n    \n*/\nbool check_if_last_char_is_a_letter(string txt) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = check_if_last_char_is_a_letter;\n\n    assert(candidate(\"apple\") == false);\n    assert(candidate(\"apple pi e\") == true);\n    assert(candidate(\"eeeee\") == false);\n    assert(candidate(\"A\") == true);\n    assert(candidate(\"Pumpkin pie \") == false);\n    assert(candidate(\"Pumpkin pie 1\") == false);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"eeeee e \") == false);\n    assert(candidate(\"apple pie\") == false);\n    assert(candidate(\"apple pi e \") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "unittest\n{\n    alias candidate = check_if_last_char_is_a_letter;\n\n    assert(candidate(\"apple\") == false);\n    assert(candidate(\"apple pi e\") == true);\n    assert(candidate(\"eeeee\") == false);\n    assert(candidate(\"A\") == true);\n    assert(candidate(\"Pumpkin pie \") == false);\n    assert(candidate(\"Pumpkin pie 1\") == false);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"eeeee e \") == false);\n    assert(candidate(\"apple pie\") == false);\n    assert(candidate(\"apple pi e \") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_124_valid_date", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou have to write a function which validates a given date string and\n    returns true if the date is valid otherwise false.\n    The date is valid if all of the following rules are satisfied:\n    1. The date string is not empty.\n    2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n    3. The months should not be less than 1 or higher than 12.\n    4. The date should be in the format: mm-dd-yyyy\n\n    >>> valid_date(\"03-11-2000\")\n    true\n\n    >>> valid_date(\"15-01-2012\")\n    false\n\n    >>> valid_date(\"04-0-2040\")\n    false\n\n    >>> valid_date(\"06-04-2020\")\n    true\n\n    >>> valid_date(\"06/04/2020\")\n    false\n    \n*/\nbool valid_date(string date) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = valid_date;\n\n    assert(candidate(\"03-11-2000\") == true);\n    assert(candidate(\"15-01-2012\") == false);\n    assert(candidate(\"04-0-2040\") == false);\n    assert(candidate(\"06-04-2020\") == true);\n    assert(candidate(\"01-01-2007\") == true);\n    assert(candidate(\"03-32-2011\") == false);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"04-31-3000\") == false);\n    assert(candidate(\"06-06-2005\") == true);\n    assert(candidate(\"21-31-2000\") == false);\n    assert(candidate(\"04-12-2003\") == true);\n    assert(candidate(\"04122003\") == false);\n    assert(candidate(\"20030412\") == false);\n    assert(candidate(\"2003-04\") == false);\n    assert(candidate(\"2003-04-12\") == false);\n    assert(candidate(\"04-2003\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_124_valid_date", "test": "unittest\n{\n    alias candidate = valid_date;\n\n    assert(candidate(\"03-11-2000\") == true);\n    assert(candidate(\"15-01-2012\") == false);\n    assert(candidate(\"04-0-2040\") == false);\n    assert(candidate(\"06-04-2020\") == true);\n    assert(candidate(\"01-01-2007\") == true);\n    assert(candidate(\"03-32-2011\") == false);\n    assert(candidate(\"\") == false);\n    assert(candidate(\"04-31-3000\") == false);\n    assert(candidate(\"06-06-2005\") == true);\n    assert(candidate(\"21-31-2000\") == false);\n    assert(candidate(\"04-12-2003\") == true);\n    assert(candidate(\"04122003\") == false);\n    assert(candidate(\"20030412\") == false);\n    assert(candidate(\"2003-04\") == false);\n    assert(candidate(\"2003-04-12\") == false);\n    assert(candidate(\"04-2003\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_108_count_nums", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Write a function count_nums which takes an array of integers and returns\n    the number of elements which has a sum of digits > 0.\n    If a number is negative, then its first signed digit will be negative:\n    e.g. -123 has signed digits -1, 2, and 3.\n    >>> count_nums([])\n    0L\n    >>> count_nums([-1L, 11L, -11L])\n    1L\n    >>> count_nums([1L, 1L, 2L])\n    3L\n    \n*/\nlong count_nums(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = count_nums;\n\n    assert(candidate([]) == 0L);\n    assert(candidate([-1L, -2L, 0L]) == 0L);\n    assert(candidate([1L, 1L, 2L, -2L, 3L, 4L, 5L]) == 6L);\n    assert(candidate([1L, 6L, 9L, -6L, 0L, 1L, 5L]) == 5L);\n    assert(candidate([1L, 100L, 98L, -7L, 1L, -1L]) == 4L);\n    assert(candidate([12L, 23L, 34L, -45L, -56L, 0L]) == 5L);\n    assert(candidate([0L, 1L]) == 1L);\n    assert(candidate([1L]) == 1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_108_count_nums", "test": "unittest\n{\n    alias candidate = count_nums;\n\n    assert(candidate([]) == 0L);\n    assert(candidate([-1L, -2L, 0L]) == 0L);\n    assert(candidate([1L, 1L, 2L, -2L, 3L, 4L, 5L]) == 6L);\n    assert(candidate([1L, 6L, 9L, -6L, 0L, 1L, 5L]) == 5L);\n    assert(candidate([1L, 100L, 98L, -7L, 1L, -1L]) == 4L);\n    assert(candidate([12L, 23L, 34L, -45L, -56L, 0L]) == 5L);\n    assert(candidate([0L, 1L]) == 1L);\n    assert(candidate([1L]) == 1L);\n}\nvoid main(){}"}
{"name": "HumanEval_86_anti_shuffle", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Write a function that takes a string and returns an ordered version of it.\n    Ordered version of string, is a string where all words (separated by space)\n    are replaced by a new word where all the characters arranged in\n    ascending order based on ascii value.\n    Note: You should keep the order of words and blank spaces in the sentence.\n\n    For example:\n    >>> anti_shuffle(\"Hi\")\n    \"Hi\"\n    >>> anti_shuffle(\"hello\")\n    \"ehllo\"\n    >>> anti_shuffle(\"Hello World!!!\")\n    \"Hello !!!Wdlor\"\n    \n*/\nstring anti_shuffle(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = anti_shuffle;\n\n    assert(candidate(\"Hi\") == \"Hi\");\n    assert(candidate(\"hello\") == \"ehllo\");\n    assert(candidate(\"number\") == \"bemnru\");\n    assert(candidate(\"abcd\") == \"abcd\");\n    assert(candidate(\"Hello World!!!\") == \"Hello !!!Wdlor\");\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"Hi. My name is Mister Robot. How are you?\") == \".Hi My aemn is Meirst .Rboot How aer ?ouy\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_86_anti_shuffle", "test": "unittest\n{\n    alias candidate = anti_shuffle;\n\n    assert(candidate(\"Hi\") == \"Hi\");\n    assert(candidate(\"hello\") == \"ehllo\");\n    assert(candidate(\"number\") == \"bemnru\");\n    assert(candidate(\"abcd\") == \"abcd\");\n    assert(candidate(\"Hello World!!!\") == \"Hello !!!Wdlor\");\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"Hi. My name is Mister Robot. How are you?\") == \".Hi My aemn is Meirst .Rboot How aer ?ouy\");\n}\nvoid main(){}"}
{"name": "HumanEval_48_is_palindrome", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Checks if given string is a palindrome\n    >>> is_palindrome(\"\")\n    true\n    >>> is_palindrome(\"aba\")\n    true\n    >>> is_palindrome(\"aaaaa\")\n    true\n    >>> is_palindrome(\"zbcd\")\n    false\n    \n*/\nbool is_palindrome(string text) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_palindrome;\n\n    assert(candidate(\"\") == true);\n    assert(candidate(\"aba\") == true);\n    assert(candidate(\"aaaaa\") == true);\n    assert(candidate(\"zbcd\") == false);\n    assert(candidate(\"xywyx\") == true);\n    assert(candidate(\"xywyz\") == false);\n    assert(candidate(\"xywzx\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_48_is_palindrome", "test": "unittest\n{\n    alias candidate = is_palindrome;\n\n    assert(candidate(\"\") == true);\n    assert(candidate(\"aba\") == true);\n    assert(candidate(\"aaaaa\") == true);\n    assert(candidate(\"zbcd\") == false);\n    assert(candidate(\"xywyx\") == true);\n    assert(candidate(\"xywyz\") == false);\n    assert(candidate(\"xywzx\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_118_get_closest_vowel", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given a word. Your task is to find the closest vowel that stands between \n    two consonants from the right side of the word (case sensitive).\n    \n    Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n    find any vowel met the above condition. \n\n    You may assume that the given string contains English letter only.\n\n    Example:\n    >>> get_closest_vowel(\"yogurt\")\n    \"u\"\n    >>> get_closest_vowel(\"FULL\")\n    \"U\"\n    >>> get_closest_vowel(\"quick\")\n    \"\"\n    >>> get_closest_vowel(\"ab\")\n    \"\"\n    \n*/\nstring get_closest_vowel(string word) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = get_closest_vowel;\n\n    assert(candidate(\"yogurt\") == \"u\");\n    assert(candidate(\"full\") == \"u\");\n    assert(candidate(\"easy\") == \"\");\n    assert(candidate(\"eAsy\") == \"\");\n    assert(candidate(\"ali\") == \"\");\n    assert(candidate(\"bad\") == \"a\");\n    assert(candidate(\"most\") == \"o\");\n    assert(candidate(\"ab\") == \"\");\n    assert(candidate(\"ba\") == \"\");\n    assert(candidate(\"quick\") == \"\");\n    assert(candidate(\"anime\") == \"i\");\n    assert(candidate(\"Asia\") == \"\");\n    assert(candidate(\"Above\") == \"o\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_118_get_closest_vowel", "test": "unittest\n{\n    alias candidate = get_closest_vowel;\n\n    assert(candidate(\"yogurt\") == \"u\");\n    assert(candidate(\"full\") == \"u\");\n    assert(candidate(\"easy\") == \"\");\n    assert(candidate(\"eAsy\") == \"\");\n    assert(candidate(\"ali\") == \"\");\n    assert(candidate(\"bad\") == \"a\");\n    assert(candidate(\"most\") == \"o\");\n    assert(candidate(\"ab\") == \"\");\n    assert(candidate(\"ba\") == \"\");\n    assert(candidate(\"quick\") == \"\");\n    assert(candidate(\"anime\") == \"i\");\n    assert(candidate(\"Asia\") == \"\");\n    assert(candidate(\"Above\") == \"o\");\n}\nvoid main(){}"}
{"name": "HumanEval_31_is_prime", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn true if a given number is prime, and false otherwise.\n    >>> is_prime(6L)\n    false\n    >>> is_prime(101L)\n    true\n    >>> is_prime(11L)\n    true\n    >>> is_prime(13441L)\n    true\n    >>> is_prime(61L)\n    true\n    >>> is_prime(4L)\n    false\n    >>> is_prime(1L)\n    false\n    \n*/\nbool is_prime(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_prime;\n\n    assert(candidate(6L) == false);\n    assert(candidate(101L) == true);\n    assert(candidate(11L) == true);\n    assert(candidate(13441L) == true);\n    assert(candidate(61L) == true);\n    assert(candidate(4L) == false);\n    assert(candidate(1L) == false);\n    assert(candidate(5L) == true);\n    assert(candidate(11L) == true);\n    assert(candidate(17L) == true);\n    assert(candidate(85L) == false);\n    assert(candidate(77L) == false);\n    assert(candidate(255379L) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_31_is_prime", "test": "unittest\n{\n    alias candidate = is_prime;\n\n    assert(candidate(6L) == false);\n    assert(candidate(101L) == true);\n    assert(candidate(11L) == true);\n    assert(candidate(13441L) == true);\n    assert(candidate(61L) == true);\n    assert(candidate(4L) == false);\n    assert(candidate(1L) == false);\n    assert(candidate(5L) == true);\n    assert(candidate(11L) == true);\n    assert(candidate(17L) == true);\n    assert(candidate(85L) == false);\n    assert(candidate(77L) == false);\n    assert(candidate(255379L) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_144_simplify", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYour task is to implement a function that will simplify the expression\n    x * n. The function returns true if x * n evaluates to a whole number and false\n    otherwise. Both x and n, are string representation of a fraction, and have the following format,\n    <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n\n    You can assume that x, and n are valid fractions, and do not have zero as denominator.\n\n    >>> simplify(\"1/5\", \"5/1\")\n    true\n    >>> simplify(\"1/6\", \"2/1\")\n    false\n    >>> simplify(\"7/10\", \"10/2\")\n    false\n    \n*/\nbool simplify(string x, string n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = simplify;\n\n    assert(candidate(\"1/5\", \"5/1\") == true);\n    assert(candidate(\"1/6\", \"2/1\") == false);\n    assert(candidate(\"5/1\", \"3/1\") == true);\n    assert(candidate(\"7/10\", \"10/2\") == false);\n    assert(candidate(\"2/10\", \"50/10\") == true);\n    assert(candidate(\"7/2\", \"4/2\") == true);\n    assert(candidate(\"11/6\", \"6/1\") == true);\n    assert(candidate(\"2/3\", \"5/2\") == false);\n    assert(candidate(\"5/2\", \"3/5\") == false);\n    assert(candidate(\"2/4\", \"8/4\") == true);\n    assert(candidate(\"2/4\", \"4/2\") == true);\n    assert(candidate(\"1/5\", \"5/1\") == true);\n    assert(candidate(\"1/5\", \"1/5\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_144_simplify", "test": "unittest\n{\n    alias candidate = simplify;\n\n    assert(candidate(\"1/5\", \"5/1\") == true);\n    assert(candidate(\"1/6\", \"2/1\") == false);\n    assert(candidate(\"5/1\", \"3/1\") == true);\n    assert(candidate(\"7/10\", \"10/2\") == false);\n    assert(candidate(\"2/10\", \"50/10\") == true);\n    assert(candidate(\"7/2\", \"4/2\") == true);\n    assert(candidate(\"11/6\", \"6/1\") == true);\n    assert(candidate(\"2/3\", \"5/2\") == false);\n    assert(candidate(\"5/2\", \"3/5\") == false);\n    assert(candidate(\"2/4\", \"8/4\") == true);\n    assert(candidate(\"2/4\", \"4/2\") == true);\n    assert(candidate(\"1/5\", \"5/1\") == true);\n    assert(candidate(\"1/5\", \"1/5\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_78_hex_key", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou have been tasked to write a function that receives \n    a hexadecimal number as a string and counts the number of hexadecimal \n    digits that are primes (prime number, or a prime, is a natural number \n    greater than 1 that is not a product of two smaller natural numbers).\n    Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n    Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n    So you have to determine a number of the following digits: 2, 3, 5, 7, \n    B (=decimal 11), D (=decimal 13).\n    Note: you may assume the input is always correct or empty string, \n    and symbols A,B,C,D,E,F are always uppercase.\n    Examples:\n    >>> hex_key(\"AB\")\n    1L\n    >>> hex_key(\"1077E\")\n    2L\n    >>> hex_key(\"ABED1A33\")\n    4L\n    >>> hex_key(\"123456789ABCDEF0\")\n    6L\n    >>> hex_key(\"2020\")\n    2L\n    \n*/\nlong hex_key(string num) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = hex_key;\n\n    assert(candidate(\"AB\") == 1L);\n    assert(candidate(\"1077E\") == 2L);\n    assert(candidate(\"ABED1A33\") == 4L);\n    assert(candidate(\"2020\") == 2L);\n    assert(candidate(\"123456789ABCDEF0\") == 6L);\n    assert(candidate(\"112233445566778899AABBCCDDEEFF00\") == 12L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_78_hex_key", "test": "unittest\n{\n    alias candidate = hex_key;\n\n    assert(candidate(\"AB\") == 1L);\n    assert(candidate(\"1077E\") == 2L);\n    assert(candidate(\"ABED1A33\") == 4L);\n    assert(candidate(\"2020\") == 2L);\n    assert(candidate(\"123456789ABCDEF0\") == 6L);\n    assert(candidate(\"112233445566778899AABBCCDDEEFF00\") == 12L);\n}\nvoid main(){}"}
{"name": "HumanEval_143_words_in_sentence", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given a string representing a sentence,\n    the sentence contains some words separated by a space,\n    and you have to return a string that contains the words from the original sentence,\n    whose lengths are prime numbers,\n    the order of the words in the new string should be the same as the original one.\n\n    Example 1:\n    >>> words_in_sentence(\"This is a test\")\n    \"is\"\n\n    Example 2:\n    >>> words_in_sentence(\"lets go for swimming\")\n    \"go for\"\n    \n    Constraints:\n        * 1 <= len(sentence) <= 100\n        * sentence contains only letters\n    \n*/\nstring words_in_sentence(string sentence) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = words_in_sentence;\n\n    assert(candidate(\"This is a test\") == \"is\");\n    assert(candidate(\"lets go for swimming\") == \"go for\");\n    assert(candidate(\"there is no place available here\") == \"there is no place\");\n    assert(candidate(\"Hi I am Hussein\") == \"Hi am Hussein\");\n    assert(candidate(\"go for it\") == \"go for it\");\n    assert(candidate(\"here\") == \"\");\n    assert(candidate(\"here is\") == \"is\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_143_words_in_sentence", "test": "unittest\n{\n    alias candidate = words_in_sentence;\n\n    assert(candidate(\"This is a test\") == \"is\");\n    assert(candidate(\"lets go for swimming\") == \"go for\");\n    assert(candidate(\"there is no place available here\") == \"there is no place\");\n    assert(candidate(\"Hi I am Hussein\") == \"Hi am Hussein\");\n    assert(candidate(\"go for it\") == \"go for it\");\n    assert(candidate(\"here\") == \"\");\n    assert(candidate(\"here is\") == \"is\");\n}\nvoid main(){}"}
{"name": "HumanEval_111_histogram", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven a string representing a space separated lowercase letters, return an associative array\n    of the letter with the most repetition and containing the corresponding count.\n    If several letters have the same occurrence, return all of them.\n    \n    Example:\n    >>> histogram(\"a b c\")\n    [\"a\": 1L, \"b\": 1L, \"c\": 1L].nullable\n    >>> histogram(\"a b b a\")\n    [\"a\": 2L, \"b\": 2L].nullable\n    >>> histogram(\"a b c a b\")\n    [\"a\": 2L, \"b\": 2L].nullable\n    >>> histogram(\"b b b b a\")\n    [\"b\": 4L].nullable\n    >>> histogram(\"\")\n    ___null_dict___\n\n    \n*/\nNullable!(long[string]) histogram(string test) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = histogram;\n\n{\n        auto result = candidate(\"a b b a\");\n        assert(!result.isNull && result.get == [\"a\": 2L, \"b\": 2L]);\n}\n\n{\n        auto result = candidate(\"a b c a b\");\n        assert(!result.isNull && result.get == [\"a\": 2L, \"b\": 2L]);\n}\n\n{\n        auto result = candidate(\"a b c d g\");\n        assert(!result.isNull && result.get == [\"a\": 1L, \"b\": 1L, \"c\": 1L, \"d\": 1L, \"g\": 1L]);\n}\n\n{\n        auto result = candidate(\"r t g\");\n        assert(!result.isNull && result.get == [\"r\": 1L, \"t\": 1L, \"g\": 1L]);\n}\n\n{\n        auto result = candidate(\"b b b b a\");\n        assert(!result.isNull && result.get == [\"b\": 4L]);\n}\n\n{\n        auto result = candidate(\"r t g\");\n        assert(!result.isNull && result.get == [\"r\": 1L, \"t\": 1L, \"g\": 1L]);\n}\n\n{\n        auto result = candidate(\"\");\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate(\"a\");\n        assert(!result.isNull && result.get == [\"a\": 1L]);\n}\n\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_111_histogram", "test": "unittest\n{\n    alias candidate = histogram;\n\n{\n        auto result = candidate(\"a b b a\");\n        assert(!result.isNull && result.get == [\"a\": 2L, \"b\": 2L]);\n}\n\n{\n        auto result = candidate(\"a b c a b\");\n        assert(!result.isNull && result.get == [\"a\": 2L, \"b\": 2L]);\n}\n\n{\n        auto result = candidate(\"a b c d g\");\n        assert(!result.isNull && result.get == [\"a\": 1L, \"b\": 1L, \"c\": 1L, \"d\": 1L, \"g\": 1L]);\n}\n\n{\n        auto result = candidate(\"r t g\");\n        assert(!result.isNull && result.get == [\"r\": 1L, \"t\": 1L, \"g\": 1L]);\n}\n\n{\n        auto result = candidate(\"b b b b a\");\n        assert(!result.isNull && result.get == [\"b\": 4L]);\n}\n\n{\n        auto result = candidate(\"r t g\");\n        assert(!result.isNull && result.get == [\"r\": 1L, \"t\": 1L, \"g\": 1L]);\n}\n\n{\n        auto result = candidate(\"\");\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate(\"a\");\n        assert(!result.isNull && result.get == [\"a\": 1L]);\n}\n\n}\nvoid main(){}"}
{"name": "HumanEval_87_get_row", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given a 2 dimensional data, as a nested arrays,\n    which is similar to matrix, however, unlike matrices,\n    each row may contain a different number of columns.\n    Given lst, and integer x, find integers x in the array,\n    and return array of tuples, [(x1, y1), (x2, y2) ...] such that\n    each tuple is a coordinate - (row, columns), starting with 0.\n    Sort coordinates initially by rows in ascending order.\n    Also, sort coordinates of the row by columns in descending order.\n    \n    Examples:\n    >>> get_row([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 1L, 6L], [1L, 2L, 3L, 4L, 5L, 1L]], 1L)\n    [tuple(0L, 0L), tuple(1L, 4L), tuple(1L, 0L), tuple(2L, 5L), tuple(2L, 0L)]\n    >>> get_row([], 1L)\n    []\n    >>> get_row([[], [1L], [1L, 2L, 3L]], 3L)\n    [tuple(2L, 2L)]\n    \n*/\nTuple!(long, long)[] get_row(long[][] lst, long x) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = get_row;\n\n    assert(candidate([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 1L, 6L], [1L, 2L, 3L, 4L, 5L, 1L]], 1L) == [tuple(0L, 0L), tuple(1L, 4L), tuple(1L, 0L), tuple(2L, 5L), tuple(2L, 0L)]);\n    assert(candidate([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L]], 2L) == [tuple(0L, 1L), tuple(1L, 1L), tuple(2L, 1L), tuple(3L, 1L), tuple(4L, 1L), tuple(5L, 1L)]);\n    assert(candidate([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 1L, 3L, 4L, 5L, 6L], [1L, 2L, 1L, 4L, 5L, 6L], [1L, 2L, 3L, 1L, 5L, 6L], [1L, 2L, 3L, 4L, 1L, 6L], [1L, 2L, 3L, 4L, 5L, 1L]], 1L) == [tuple(0L, 0L), tuple(1L, 0L), tuple(2L, 1L), tuple(2L, 0L), tuple(3L, 2L), tuple(3L, 0L), tuple(4L, 3L), tuple(4L, 0L), tuple(5L, 4L), tuple(5L, 0L), tuple(6L, 5L), tuple(6L, 0L)]);\n    assert(candidate([], 1L) == []);\n    assert(candidate([[1L]], 2L) == []);\n    assert(candidate([[], [1L], [1L, 2L, 3L]], 3L) == [tuple(2L, 2L)]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_87_get_row", "test": "unittest\n{\n    alias candidate = get_row;\n\n    assert(candidate([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 1L, 6L], [1L, 2L, 3L, 4L, 5L, 1L]], 1L) == [tuple(0L, 0L), tuple(1L, 4L), tuple(1L, 0L), tuple(2L, 5L), tuple(2L, 0L)]);\n    assert(candidate([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L]], 2L) == [tuple(0L, 1L), tuple(1L, 1L), tuple(2L, 1L), tuple(3L, 1L), tuple(4L, 1L), tuple(5L, 1L)]);\n    assert(candidate([[1L, 2L, 3L, 4L, 5L, 6L], [1L, 2L, 3L, 4L, 5L, 6L], [1L, 1L, 3L, 4L, 5L, 6L], [1L, 2L, 1L, 4L, 5L, 6L], [1L, 2L, 3L, 1L, 5L, 6L], [1L, 2L, 3L, 4L, 1L, 6L], [1L, 2L, 3L, 4L, 5L, 1L]], 1L) == [tuple(0L, 0L), tuple(1L, 0L), tuple(2L, 1L), tuple(2L, 0L), tuple(3L, 2L), tuple(3L, 0L), tuple(4L, 3L), tuple(4L, 0L), tuple(5L, 4L), tuple(5L, 0L), tuple(6L, 5L), tuple(6L, 0L)]);\n    assert(candidate([], 1L) == []);\n    assert(candidate([[1L]], 2L) == []);\n    assert(candidate([[], [1L], [1L, 2L, 3L]], 3L) == [tuple(2L, 2L)]);\n}\nvoid main(){}"}
{"name": "HumanEval_123_get_odd_collatz", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n\n    The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n    as follows: start with any positive integer n. Then each term is obtained from the \n    previous term as follows: if the previous term is even, the next term is one half of \n    the previous term. If the previous term is odd, the next term is 3 times the previous\n    term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n\n    Note: \n        1. Collatz(1) is [1].\n        2. returned array sorted in increasing order.\n\n    For example:\n    get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n    >>> get_odd_collatz(5L)\n    [1L, 5L]\n    \n*/\nlong[] get_odd_collatz(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = get_odd_collatz;\n\n    assert(candidate(14L) == [1L, 5L, 7L, 11L, 13L, 17L]);\n    assert(candidate(5L) == [1L, 5L]);\n    assert(candidate(12L) == [1L, 3L, 5L]);\n    assert(candidate(1L) == [1L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_123_get_odd_collatz", "test": "unittest\n{\n    alias candidate = get_odd_collatz;\n\n    assert(candidate(14L) == [1L, 5L, 7L, 11L, 13L, 17L]);\n    assert(candidate(5L) == [1L, 5L]);\n    assert(candidate(12L) == [1L, 3L, 5L]);\n    assert(candidate(1L) == [1L]);\n}\nvoid main(){}"}
{"name": "HumanEval_135_can_arrange", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nCreate a function which returns the largest index of an element which\n    is not greater than or equal to the element immediately preceding it. If\n    no such element exists then return -1. The given array will not contain\n    duplicate values.\n\n    Examples:\n    >>> can_arrange([1L, 2L, 4L, 3L, 5L])\n    3L\n    >>> can_arrange([1L, 2L, 3L])\n    -1L\n    \n*/\nlong can_arrange(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = can_arrange;\n\n    assert(candidate([1L, 2L, 4L, 3L, 5L]) == 3L);\n    assert(candidate([1L, 2L, 4L, 5L]) == -1L);\n    assert(candidate([1L, 4L, 2L, 5L, 6L, 7L, 8L, 9L, 10L]) == 2L);\n    assert(candidate([4L, 8L, 5L, 7L, 3L]) == 4L);\n    assert(candidate([]) == -1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_135_can_arrange", "test": "unittest\n{\n    alias candidate = can_arrange;\n\n    assert(candidate([1L, 2L, 4L, 3L, 5L]) == 3L);\n    assert(candidate([1L, 2L, 4L, 5L]) == -1L);\n    assert(candidate([1L, 4L, 2L, 5L, 6L, 7L, 8L, 9L, 10L]) == 2L);\n    assert(candidate([4L, 8L, 5L, 7L, 3L]) == 4L);\n    assert(candidate([]) == -1L);\n}\nvoid main(){}"}
{"name": "HumanEval_19_sort_numbers", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Input is a space-delimited string of numberals from 'zero' to 'nine'.\n    Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n    Return the string with numbers sorted from smallest to largest\n    >>> sort_numbers(\"three one five\")\n    \"one three five\"\n    \n*/\nstring sort_numbers(string numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sort_numbers;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"three\") == \"three\");\n    assert(candidate(\"three five nine\") == \"three five nine\");\n    assert(candidate(\"five zero four seven nine eight\") == \"zero four five seven eight nine\");\n    assert(candidate(\"six five four three two one zero\") == \"zero one two three four five six\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_19_sort_numbers", "test": "unittest\n{\n    alias candidate = sort_numbers;\n\n    assert(candidate(\"\") == \"\");\n    assert(candidate(\"three\") == \"three\");\n    assert(candidate(\"three five nine\") == \"three five nine\");\n    assert(candidate(\"five zero four seven nine eight\") == \"zero four five seven eight nine\");\n    assert(candidate(\"six five four three two one zero\") == \"zero one two three four five six\");\n}\nvoid main(){}"}
{"name": "HumanEval_65_circular_shift", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nCircular shift the digits of the integer x, shift the digits right by shift\n    and return the result as a string.\n    If shift > number of digits, return digits reversed.\n    >>> circular_shift(12L, 1L)\n    \"21\"\n    >>> circular_shift(12L, 2L)\n    \"12\"\n    \n*/\nstring circular_shift(long x, long shift) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = circular_shift;\n\n    assert(candidate(100L, 2L) == \"001\");\n    assert(candidate(12L, 2L) == \"12\");\n    assert(candidate(97L, 8L) == \"79\");\n    assert(candidate(12L, 1L) == \"21\");\n    assert(candidate(11L, 101L) == \"11\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_65_circular_shift", "test": "unittest\n{\n    alias candidate = circular_shift;\n\n    assert(candidate(100L, 2L) == \"001\");\n    assert(candidate(12L, 2L) == \"12\");\n    assert(candidate(97L, 8L) == \"79\");\n    assert(candidate(12L, 1L) == \"21\");\n    assert(candidate(11L, 101L) == \"11\");\n}\nvoid main(){}"}
{"name": "HumanEval_142_sum_squares", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\"\n    This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n    multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n    change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n    \n    Examples:\n    >>> lst\n    [1L, 2L, 3L]\n    >>> lst\n    []\n    >>> lst\n    [-1L, -5L, 2L, -1L, -5L]\n    \n*/\nlong sum_squares(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sum_squares;\n\n    assert(candidate([1L, 2L, 3L]) == 6L);\n    assert(candidate([1L, 4L, 9L]) == 14L);\n    assert(candidate([]) == 0L);\n    assert(candidate([1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L]) == 9L);\n    assert(candidate([-1L, -1L, -1L, -1L, -1L, -1L, -1L, -1L, -1L]) == -3L);\n    assert(candidate([0L]) == 0L);\n    assert(candidate([-1L, -5L, 2L, -1L, -5L]) == -126L);\n    assert(candidate([-56L, -99L, 1L, 0L, -2L]) == 3030L);\n    assert(candidate([-1L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, -1L]) == 0L);\n    assert(candidate([-16L, -9L, -2L, 36L, 36L, 26L, -20L, 25L, -40L, 20L, -4L, 12L, -26L, 35L, 37L]) == -14196L);\n    assert(candidate([-1L, -3L, 17L, -1L, -15L, 13L, -1L, 14L, -14L, -12L, -5L, 14L, -14L, 6L, 13L, 11L, 16L, 16L, 4L, 10L]) == -1448L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_142_sum_squares", "test": "unittest\n{\n    alias candidate = sum_squares;\n\n    assert(candidate([1L, 2L, 3L]) == 6L);\n    assert(candidate([1L, 4L, 9L]) == 14L);\n    assert(candidate([]) == 0L);\n    assert(candidate([1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L]) == 9L);\n    assert(candidate([-1L, -1L, -1L, -1L, -1L, -1L, -1L, -1L, -1L]) == -3L);\n    assert(candidate([0L]) == 0L);\n    assert(candidate([-1L, -5L, 2L, -1L, -5L]) == -126L);\n    assert(candidate([-56L, -99L, 1L, 0L, -2L]) == 3030L);\n    assert(candidate([-1L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, -1L]) == 0L);\n    assert(candidate([-16L, -9L, -2L, 36L, 36L, 26L, -20L, 25L, -40L, 20L, -4L, 12L, -26L, 35L, 37L]) == -14196L);\n    assert(candidate([-1L, -3L, 17L, -1L, -15L, 13L, -1L, 14L, -14L, -12L, -5L, 14L, -14L, 6L, 13L, 11L, 16L, 16L, 4L, 10L]) == -1448L);\n}\nvoid main(){}"}
{"name": "HumanEval_94_skjkasdkd", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given an array of integers.\n    You need to find the largest prime value and return the sum of its digits.\n\n    Examples:\n    >>> skjkasdkd([0L, 3L, 2L, 1L, 3L, 5L, 7L, 4L, 5L, 5L, 5L, 2L, 181L, 32L, 4L, 32L, 3L, 2L, 32L, 324L, 4L, 3L])\n    10L\n    >>> skjkasdkd([1L, 0L, 1L, 8L, 2L, 4597L, 2L, 1L, 3L, 40L, 1L, 2L, 1L, 2L, 4L, 2L, 5L, 1L])\n    25L\n    >>> skjkasdkd([1L, 3L, 1L, 32L, 5107L, 34L, 83278L, 109L, 163L, 23L, 2323L, 32L, 30L, 1L, 9L, 3L])\n    13L\n    >>> skjkasdkd([0L, 724L, 32L, 71L, 99L, 32L, 6L, 0L, 5L, 91L, 83L, 0L, 5L, 6L])\n    11L\n    >>> skjkasdkd([0L, 81L, 12L, 3L, 1L, 21L])\n    3L\n    >>> skjkasdkd([0L, 8L, 1L, 2L, 1L, 7L])\n    7L\n    \n*/\nlong skjkasdkd(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = skjkasdkd;\n\n    assert(candidate([0L, 3L, 2L, 1L, 3L, 5L, 7L, 4L, 5L, 5L, 5L, 2L, 181L, 32L, 4L, 32L, 3L, 2L, 32L, 324L, 4L, 3L]) == 10L);\n    assert(candidate([1L, 0L, 1L, 8L, 2L, 4597L, 2L, 1L, 3L, 40L, 1L, 2L, 1L, 2L, 4L, 2L, 5L, 1L]) == 25L);\n    assert(candidate([1L, 3L, 1L, 32L, 5107L, 34L, 83278L, 109L, 163L, 23L, 2323L, 32L, 30L, 1L, 9L, 3L]) == 13L);\n    assert(candidate([0L, 724L, 32L, 71L, 99L, 32L, 6L, 0L, 5L, 91L, 83L, 0L, 5L, 6L]) == 11L);\n    assert(candidate([0L, 81L, 12L, 3L, 1L, 21L]) == 3L);\n    assert(candidate([0L, 8L, 1L, 2L, 1L, 7L]) == 7L);\n    assert(candidate([8191L]) == 19L);\n    assert(candidate([8191L, 123456L, 127L, 7L]) == 19L);\n    assert(candidate([127L, 97L, 8192L]) == 10L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_94_skjkasdkd", "test": "unittest\n{\n    alias candidate = skjkasdkd;\n\n    assert(candidate([0L, 3L, 2L, 1L, 3L, 5L, 7L, 4L, 5L, 5L, 5L, 2L, 181L, 32L, 4L, 32L, 3L, 2L, 32L, 324L, 4L, 3L]) == 10L);\n    assert(candidate([1L, 0L, 1L, 8L, 2L, 4597L, 2L, 1L, 3L, 40L, 1L, 2L, 1L, 2L, 4L, 2L, 5L, 1L]) == 25L);\n    assert(candidate([1L, 3L, 1L, 32L, 5107L, 34L, 83278L, 109L, 163L, 23L, 2323L, 32L, 30L, 1L, 9L, 3L]) == 13L);\n    assert(candidate([0L, 724L, 32L, 71L, 99L, 32L, 6L, 0L, 5L, 91L, 83L, 0L, 5L, 6L]) == 11L);\n    assert(candidate([0L, 81L, 12L, 3L, 1L, 21L]) == 3L);\n    assert(candidate([0L, 8L, 1L, 2L, 1L, 7L]) == 7L);\n    assert(candidate([8191L]) == 19L);\n    assert(candidate([8191L, 123456L, 127L, 7L]) == 19L);\n    assert(candidate([127L, 97L, 8192L]) == 10L);\n}\nvoid main(){}"}
{"name": "HumanEval_8_sum_product", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n For a given array of integers, return a tuple consisting of a sum and a product of all the integers in an array.\n    Empty sum should be equal to 0 and empty product should be equal to 1.\n    >>> sum_product([])\n    tuple(0L, 1L)\n    >>> sum_product([1L, 2L, 3L, 4L])\n    tuple(10L, 24L)\n    \n*/\nTuple!(long, long) sum_product(long[] numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sum_product;\n\n    assert(candidate([]) == tuple(0L, 1L));\n    assert(candidate([1L, 1L, 1L]) == tuple(3L, 1L));\n    assert(candidate([100L, 0L]) == tuple(100L, 0L));\n    assert(candidate([3L, 5L, 7L]) == tuple(15L, 105L));\n    assert(candidate([10L]) == tuple(10L, 10L));\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_8_sum_product", "test": "unittest\n{\n    alias candidate = sum_product;\n\n    assert(candidate([]) == tuple(0L, 1L));\n    assert(candidate([1L, 1L, 1L]) == tuple(3L, 1L));\n    assert(candidate([100L, 0L]) == tuple(100L, 0L));\n    assert(candidate([3L, 5L, 7L]) == tuple(15L, 105L));\n    assert(candidate([10L]) == tuple(10L, 10L));\n}\nvoid main(){}"}
{"name": "HumanEval_102_choose_num", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nThis function takes two positive numbers x and y and returns the\n    biggest even integer number that is in the range [x, y] inclusive. If \n    there's no such number, then the function should return -1.\n\n    For example:\n    >>> choose_num(12L, 15L)\n    14L\n    >>> choose_num(13L, 12L)\n    -1L\n    \n*/\nlong choose_num(long x, long y) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = choose_num;\n\n    assert(candidate(12L, 15L) == 14L);\n    assert(candidate(13L, 12L) == -1L);\n    assert(candidate(33L, 12354L) == 12354L);\n    assert(candidate(5234L, 5233L) == -1L);\n    assert(candidate(6L, 29L) == 28L);\n    assert(candidate(27L, 10L) == -1L);\n    assert(candidate(7L, 7L) == -1L);\n    assert(candidate(546L, 546L) == 546L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_102_choose_num", "test": "unittest\n{\n    alias candidate = choose_num;\n\n    assert(candidate(12L, 15L) == 14L);\n    assert(candidate(13L, 12L) == -1L);\n    assert(candidate(33L, 12354L) == 12354L);\n    assert(candidate(5234L, 5233L) == -1L);\n    assert(candidate(6L, 29L) == 28L);\n    assert(candidate(27L, 10L) == -1L);\n    assert(candidate(7L, 7L) == -1L);\n    assert(candidate(546L, 546L) == 546L);\n}\nvoid main(){}"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Create a function that returns a tuple (a, b), where 'a' is\n    the largest of negative integers, and 'b' is the smallest\n    of positive integers in an array.\n    If there is no negative or positive integers, return them as null.\n\n    Examples:\n    >>> largest_smallest_integers([2L, 4L, 1L, 3L, 5L, 7L])\n    tuple(None, 1L)\n    >>> largest_smallest_integers([])\n    tuple(None, None)\n    >>> largest_smallest_integers([0L])\n    tuple(None, None)\n    \n*/\nTuple!(Nullable!(long), Nullable!(long)) largest_smallest_integers(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = largest_smallest_integers;\n\n{\n        auto result = candidate([2L, 4L, 1L, 3L, 5L, 7L]);\n        assert(result[0].isNull);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([2L, 4L, 1L, 3L, 5L, 7L, 0L]);\n        assert(result[0].isNull);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([1L, 3L, 2L, 4L, 5L, 6L, -2L]);\n        assert(!result[0].isNull && result[0].get == -2L);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([4L, 5L, 3L, 6L, 2L, 7L, -7L]);\n        assert(!result[0].isNull && result[0].get == -7L);\n        assert(!result[1].isNull && result[1].get == 2L);\n}\n\n{\n        auto result = candidate([7L, 3L, 8L, 4L, 9L, 2L, 5L, -9L]);\n        assert(!result[0].isNull && result[0].get == -9L);\n        assert(!result[1].isNull && result[1].get == 2L);\n}\n\n{\n        auto result = candidate([]);\n        assert(result[0].isNull);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([0L]);\n        assert(result[0].isNull);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([-1L, -3L, -5L, -6L]);\n        assert(!result[0].isNull && result[0].get == -1L);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([-1L, -3L, -5L, -6L, 0L]);\n        assert(!result[0].isNull && result[0].get == -1L);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([-6L, -4L, -4L, -3L, 1L]);\n        assert(!result[0].isNull && result[0].get == -3L);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([-6L, -4L, -4L, -3L, -100L, 1L]);\n        assert(!result[0].isNull && result[0].get == -3L);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "unittest\n{\n    alias candidate = largest_smallest_integers;\n\n{\n        auto result = candidate([2L, 4L, 1L, 3L, 5L, 7L]);\n        assert(result[0].isNull);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([2L, 4L, 1L, 3L, 5L, 7L, 0L]);\n        assert(result[0].isNull);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([1L, 3L, 2L, 4L, 5L, 6L, -2L]);\n        assert(!result[0].isNull && result[0].get == -2L);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([4L, 5L, 3L, 6L, 2L, 7L, -7L]);\n        assert(!result[0].isNull && result[0].get == -7L);\n        assert(!result[1].isNull && result[1].get == 2L);\n}\n\n{\n        auto result = candidate([7L, 3L, 8L, 4L, 9L, 2L, 5L, -9L]);\n        assert(!result[0].isNull && result[0].get == -9L);\n        assert(!result[1].isNull && result[1].get == 2L);\n}\n\n{\n        auto result = candidate([]);\n        assert(result[0].isNull);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([0L]);\n        assert(result[0].isNull);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([-1L, -3L, -5L, -6L]);\n        assert(!result[0].isNull && result[0].get == -1L);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([-1L, -3L, -5L, -6L, 0L]);\n        assert(!result[0].isNull && result[0].get == -1L);\n        assert(result[1].isNull);\n}\n\n{\n        auto result = candidate([-6L, -4L, -4L, -3L, 1L]);\n        assert(!result[0].isNull && result[0].get == -3L);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n{\n        auto result = candidate([-6L, -4L, -4L, -3L, -100L, 1L]);\n        assert(!result[0].isNull && result[0].get == -3L);\n        assert(!result[1].isNull && result[1].get == 1L);\n}\n\n}\nvoid main(){}"}
{"name": "HumanEval_16_count_distinct_characters", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Given a string, find out how many distinct characters (regardless of case) does it consist of\n    >>> count_distinct_characters(\"xyzXYZ\")\n    3L\n    >>> count_distinct_characters(\"Jerry\")\n    4L\n    \n*/\nlong count_distinct_characters(string string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = count_distinct_characters;\n\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"abcde\") == 5L);\n    assert(candidate(\"abcdecadeCADE\") == 5L);\n    assert(candidate(\"aaaaAAAAaaaa\") == 1L);\n    assert(candidate(\"Jerry jERRY JeRRRY\") == 5L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_16_count_distinct_characters", "test": "unittest\n{\n    alias candidate = count_distinct_characters;\n\n    assert(candidate(\"\") == 0L);\n    assert(candidate(\"abcde\") == 5L);\n    assert(candidate(\"abcdecadeCADE\") == 5L);\n    assert(candidate(\"aaaaAAAAaaaa\") == 1L);\n    assert(candidate(\"Jerry jERRY JeRRRY\") == 5L);\n}\nvoid main(){}"}
{"name": "HumanEval_100_make_a_pile", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a positive integer n, you have to make a pile of n levels of stones.\n    The first level has n stones.\n    The number of stones in the next level is:\n        - the next odd number if n is odd.\n        - the next even number if n is even.\n    Return the number of stones in each level in an array, where element at index\n    i represents the number of stones in the level (i+1).\n\n    Examples:\n    >>> make_a_pile(3L)\n    [3L, 5L, 7L]\n    \n*/\nlong[] make_a_pile(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = make_a_pile;\n\n    assert(candidate(3L) == [3L, 5L, 7L]);\n    assert(candidate(4L) == [4L, 6L, 8L, 10L]);\n    assert(candidate(5L) == [5L, 7L, 9L, 11L, 13L]);\n    assert(candidate(6L) == [6L, 8L, 10L, 12L, 14L, 16L]);\n    assert(candidate(8L) == [8L, 10L, 12L, 14L, 16L, 18L, 20L, 22L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_100_make_a_pile", "test": "unittest\n{\n    alias candidate = make_a_pile;\n\n    assert(candidate(3L) == [3L, 5L, 7L]);\n    assert(candidate(4L) == [4L, 6L, 8L, 10L]);\n    assert(candidate(5L) == [5L, 7L, 9L, 11L, 13L]);\n    assert(candidate(6L) == [6L, 8L, 10L, 12L, 14L, 16L]);\n    assert(candidate(8L) == [8L, 10L, 12L, 14L, 16L, 18L, 20L, 22L]);\n}\nvoid main(){}"}
{"name": "HumanEval_128_prod_signs", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given an array arr of integers and you need to return\n    sum of magnitudes of integers multiplied by product of all signs\n    of each number in the array, represented by 1, -1 or 0.\n    Note: return null for empty arr.\n\n    Example:\n    >>> prod_signs([1L, 2L, 2L, -4L])\n    9L\n    >>> prod_signs([0L, 1L])\n    0L\n    >>> prod_signs([])\n    None\n    \n*/\nNullable!(long) prod_signs(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = prod_signs;\n\n{\n        auto result = candidate([1L, 2L, 2L, -4L]);\n        assert(!result.isNull && result.get == -9L);\n}\n\n{\n        auto result = candidate([0L, 1L]);\n        assert(!result.isNull && result.get == 0L);\n}\n\n{\n        auto result = candidate([1L, 1L, 1L, 2L, 3L, -1L, 1L]);\n        assert(!result.isNull && result.get == -10L);\n}\n\n{\n        auto result = candidate([]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([2L, 4L, 1L, 2L, -1L, -1L, 9L]);\n        assert(!result.isNull && result.get == 20L);\n}\n\n{\n        auto result = candidate([-1L, 1L, -1L, 1L]);\n        assert(!result.isNull && result.get == 4L);\n}\n\n{\n        auto result = candidate([-1L, 1L, 1L, 1L]);\n        assert(!result.isNull && result.get == -4L);\n}\n\n{\n        auto result = candidate([-1L, 1L, 1L, 0L]);\n        assert(!result.isNull && result.get == 0L);\n}\n\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_128_prod_signs", "test": "unittest\n{\n    alias candidate = prod_signs;\n\n{\n        auto result = candidate([1L, 2L, 2L, -4L]);\n        assert(!result.isNull && result.get == -9L);\n}\n\n{\n        auto result = candidate([0L, 1L]);\n        assert(!result.isNull && result.get == 0L);\n}\n\n{\n        auto result = candidate([1L, 1L, 1L, 2L, 3L, -1L, 1L]);\n        assert(!result.isNull && result.get == -10L);\n}\n\n{\n        auto result = candidate([]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([2L, 4L, 1L, 2L, -1L, -1L, 9L]);\n        assert(!result.isNull && result.get == 20L);\n}\n\n{\n        auto result = candidate([-1L, 1L, -1L, 1L]);\n        assert(!result.isNull && result.get == 4L);\n}\n\n{\n        auto result = candidate([-1L, 1L, 1L, 1L]);\n        assert(!result.isNull && result.get == -4L);\n}\n\n{\n        auto result = candidate([-1L, 1L, 1L, 0L]);\n        assert(!result.isNull && result.get == 0L);\n}\n\n}\nvoid main(){}"}
{"name": "HumanEval_114_minSubArraySum", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given an array of integers nums, find the minimum sum of any non-empty sub-array\n    of nums.\n    Example\n    >>> minSubArraySum([2L, 3L, 4L, 1L, 2L, 4L])\n    1L\n    >>> minSubArraySum([-1L, -2L, -3L])\n    -6L\n    \n*/\nlong minSubArraySum(long[] nums) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = minSubArraySum;\n\n    assert(candidate([2L, 3L, 4L, 1L, 2L, 4L]) == 1L);\n    assert(candidate([-1L, -2L, -3L]) == -6L);\n    assert(candidate([-1L, -2L, -3L, 2L, -10L]) == -14L);\n    assert(candidate([-9999999999999999L]) == -9999999999999999L);\n    assert(candidate([0L, 10L, 20L, 1000000L]) == 0L);\n    assert(candidate([-1L, -2L, -3L, 10L, -5L]) == -6L);\n    assert(candidate([100L, -1L, -2L, -3L, 10L, -5L]) == -6L);\n    assert(candidate([10L, 11L, 13L, 8L, 3L, 4L]) == 3L);\n    assert(candidate([100L, -33L, 32L, -1L, 0L, -2L]) == -33L);\n    assert(candidate([-10L]) == -10L);\n    assert(candidate([7L]) == 7L);\n    assert(candidate([1L, -1L]) == -1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_114_minSubArraySum", "test": "unittest\n{\n    alias candidate = minSubArraySum;\n\n    assert(candidate([2L, 3L, 4L, 1L, 2L, 4L]) == 1L);\n    assert(candidate([-1L, -2L, -3L]) == -6L);\n    assert(candidate([-1L, -2L, -3L, 2L, -10L]) == -14L);\n    assert(candidate([-9999999999999999L]) == -9999999999999999L);\n    assert(candidate([0L, 10L, 20L, 1000000L]) == 0L);\n    assert(candidate([-1L, -2L, -3L, 10L, -5L]) == -6L);\n    assert(candidate([100L, -1L, -2L, -3L, 10L, -5L]) == -6L);\n    assert(candidate([10L, 11L, 13L, 8L, 3L, 4L]) == 3L);\n    assert(candidate([100L, -33L, 32L, -1L, 0L, -2L]) == -33L);\n    assert(candidate([-10L]) == -10L);\n    assert(candidate([7L]) == 7L);\n    assert(candidate([1L, -1L]) == -1L);\n}\nvoid main(){}"}
{"name": "HumanEval_15_string_sequence", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n    >>> string_sequence(0L)\n    \"0\"\n    >>> string_sequence(5L)\n    \"0 1 2 3 4 5\"\n    \n*/\nstring string_sequence(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = string_sequence;\n\n    assert(candidate(0L) == \"0\");\n    assert(candidate(3L) == \"0 1 2 3\");\n    assert(candidate(10L) == \"0 1 2 3 4 5 6 7 8 9 10\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_15_string_sequence", "test": "unittest\n{\n    alias candidate = string_sequence;\n\n    assert(candidate(0L) == \"0\");\n    assert(candidate(3L) == \"0 1 2 3\");\n    assert(candidate(10L) == \"0 1 2 3 4 5 6 7 8 9 10\");\n}\nvoid main(){}"}
{"name": "HumanEval_154_cycpattern_check", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nYou are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n    >>> cycpattern_check(\"abcd\", \"abd\")\n    false\n    >>> cycpattern_check(\"hello\", \"ell\")\n    true\n    >>> cycpattern_check(\"whassup\", \"psus\")\n    false\n    >>> cycpattern_check(\"abab\", \"baa\")\n    true\n    >>> cycpattern_check(\"efef\", \"eeff\")\n    false\n    >>> cycpattern_check(\"himenss\", \"simen\")\n    true\n\n    \n*/\nbool cycpattern_check(string a, string b) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = cycpattern_check;\n\n    assert(candidate(\"xyzw\", \"xyw\") == false);\n    assert(candidate(\"yello\", \"ell\") == true);\n    assert(candidate(\"whattup\", \"ptut\") == false);\n    assert(candidate(\"efef\", \"fee\") == true);\n    assert(candidate(\"abab\", \"aabb\") == false);\n    assert(candidate(\"winemtt\", \"tinem\") == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_154_cycpattern_check", "test": "unittest\n{\n    alias candidate = cycpattern_check;\n\n    assert(candidate(\"xyzw\", \"xyw\") == false);\n    assert(candidate(\"yello\", \"ell\") == true);\n    assert(candidate(\"whattup\", \"ptut\") == false);\n    assert(candidate(\"efef\", \"fee\") == true);\n    assert(candidate(\"abab\", \"aabb\") == false);\n    assert(candidate(\"winemtt\", \"tinem\") == true);\n}\nvoid main(){}"}
{"name": "HumanEval_57_monotonic", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn true is array elements are monotonically increasing or decreasing.\n    >>> monotonic([1L, 2L, 4L, 20L])\n    true\n    >>> monotonic([1L, 20L, 4L, 10L])\n    false\n    >>> monotonic([4L, 1L, 0L, -10L])\n    true\n    \n*/\nbool monotonic(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = monotonic;\n\n    assert(candidate([1L, 2L, 4L, 10L]) == true);\n    assert(candidate([1L, 2L, 4L, 20L]) == true);\n    assert(candidate([1L, 20L, 4L, 10L]) == false);\n    assert(candidate([4L, 1L, 0L, -10L]) == true);\n    assert(candidate([4L, 1L, 1L, 0L]) == true);\n    assert(candidate([1L, 2L, 3L, 2L, 5L, 60L]) == false);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 60L]) == true);\n    assert(candidate([9L, 9L, 9L, 9L]) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_57_monotonic", "test": "unittest\n{\n    alias candidate = monotonic;\n\n    assert(candidate([1L, 2L, 4L, 10L]) == true);\n    assert(candidate([1L, 2L, 4L, 20L]) == true);\n    assert(candidate([1L, 20L, 4L, 10L]) == false);\n    assert(candidate([4L, 1L, 0L, -10L]) == true);\n    assert(candidate([4L, 1L, 1L, 0L]) == true);\n    assert(candidate([1L, 2L, 3L, 2L, 5L, 60L]) == false);\n    assert(candidate([1L, 2L, 3L, 4L, 5L, 60L]) == true);\n    assert(candidate([9L, 9L, 9L, 9L]) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_12_longest", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Out of array of strings, return the longest one. Return the first one in case of multiple\n    strings of the same length. Return null in case the input array is empty.\n    >>> longest([])\n    None\n    >>> longest([\"a\", \"b\", \"c\"])\n    \"a\"\n    >>> longest([\"a\", \"bb\", \"ccc\"])\n    \"ccc\"\n    \n*/\nNullable!(string) longest(string[] strings) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = longest;\n\n{\n        auto result = candidate([]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([\"x\", \"y\", \"z\"]);\n        assert(!result.isNull && result.get == \"x\");\n}\n\n{\n        auto result = candidate([\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"]);\n        assert(!result.isNull && result.get == \"zzzz\");\n}\n\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_12_longest", "test": "unittest\n{\n    alias candidate = longest;\n\n{\n        auto result = candidate([]);\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate([\"x\", \"y\", \"z\"]);\n        assert(!result.isNull && result.get == \"x\");\n}\n\n{\n        auto result = candidate([\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"]);\n        assert(!result.isNull && result.get == \"zzzz\");\n}\n\n}\nvoid main(){}"}
{"name": "HumanEval_52_below_threshold", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn true if all numbers in the array l are below threshold t.\n    >>> below_threshold([1L, 2L, 4L, 10L], 100L)\n    true\n    >>> below_threshold([1L, 20L, 4L, 10L], 5L)\n    false\n    \n*/\nbool below_threshold(long[] l, long t) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = below_threshold;\n\n    assert(candidate([1L, 2L, 4L, 10L], 100L) == true);\n    assert(candidate([1L, 20L, 4L, 10L], 5L) == false);\n    assert(candidate([1L, 20L, 4L, 10L], 21L) == true);\n    assert(candidate([1L, 20L, 4L, 10L], 22L) == true);\n    assert(candidate([1L, 8L, 4L, 10L], 11L) == true);\n    assert(candidate([1L, 8L, 4L, 10L], 10L) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_52_below_threshold", "test": "unittest\n{\n    alias candidate = below_threshold;\n\n    assert(candidate([1L, 2L, 4L, 10L], 100L) == true);\n    assert(candidate([1L, 20L, 4L, 10L], 5L) == false);\n    assert(candidate([1L, 20L, 4L, 10L], 21L) == true);\n    assert(candidate([1L, 20L, 4L, 10L], 22L) == true);\n    assert(candidate([1L, 8L, 4L, 10L], 11L) == true);\n    assert(candidate([1L, 8L, 4L, 10L], 10L) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_75_is_multiply_prime", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nWrite a function that returns true if the given number is the multiplication of 3 prime numbers\n    and false otherwise.\n    Knowing that (a) is less then 100. \n    Example:\n    >>> is_multiply_prime(30L)\n    true\n    30 = 2 * 3 * 5\n    \n*/\nbool is_multiply_prime(long a) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = is_multiply_prime;\n\n    assert(candidate(5L) == false);\n    assert(candidate(30L) == true);\n    assert(candidate(8L) == true);\n    assert(candidate(10L) == false);\n    assert(candidate(125L) == true);\n    assert(candidate(105L) == true);\n    assert(candidate(126L) == false);\n    assert(candidate(729L) == false);\n    assert(candidate(891L) == false);\n    assert(candidate(1001L) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_75_is_multiply_prime", "test": "unittest\n{\n    alias candidate = is_multiply_prime;\n\n    assert(candidate(5L) == false);\n    assert(candidate(30L) == true);\n    assert(candidate(8L) == true);\n    assert(candidate(10L) == false);\n    assert(candidate(125L) == true);\n    assert(candidate(105L) == true);\n    assert(candidate(126L) == false);\n    assert(candidate(729L) == false);\n    assert(candidate(891L) == false);\n    assert(candidate(1001L) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_30_get_positive", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn only positive numbers in the array.\n    >>> get_positive([-1L, 2L, -4L, 5L, 6L])\n    [2L, 5L, 6L]\n    >>> get_positive([5L, 3L, -5L, 2L, -3L, 3L, 9L, 0L, 123L, 1L, -10L])\n    [5L, 3L, 2L, 3L, 9L, 123L, 1L]\n    \n*/\nlong[] get_positive(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = get_positive;\n\n    assert(candidate([-1L, -2L, 4L, 5L, 6L]) == [4L, 5L, 6L]);\n    assert(candidate([5L, 3L, -5L, 2L, 3L, 3L, 9L, 0L, 123L, 1L, -10L]) == [5L, 3L, 2L, 3L, 3L, 9L, 123L, 1L]);\n    assert(candidate([-1L, -2L]) == []);\n    assert(candidate([]) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_30_get_positive", "test": "unittest\n{\n    alias candidate = get_positive;\n\n    assert(candidate([-1L, -2L, 4L, 5L, 6L]) == [4L, 5L, 6L]);\n    assert(candidate([5L, 3L, -5L, 2L, 3L, 3L, 9L, 0L, 123L, 1L, -10L]) == [5L, 3L, 2L, 3L, 3L, 9L, 123L, 1L]);\n    assert(candidate([-1L, -2L]) == []);\n    assert(candidate([]) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_33_sort_third", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nThis function takes an array l and returns an array l' such that\n    l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n    to the values of the corresponding indicies of l, but sorted.\n    >>> sort_third([1L, 2L, 3L])\n    [1L, 2L, 3L]\n    >>> sort_third([5L, 6L, 3L, 4L, 8L, 9L, 2L])\n    [2L, 6L, 3L, 4L, 8L, 9L, 5L]\n    \n*/\nlong[] sort_third(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sort_third;\n\n    assert(candidate([5L, 6L, 3L, 4L, 8L, 9L, 2L]) == [2L, 6L, 3L, 4L, 8L, 9L, 5L]);\n    assert(candidate([5L, 8L, 3L, 4L, 6L, 9L, 2L]) == [2L, 8L, 3L, 4L, 6L, 9L, 5L]);\n    assert(candidate([5L, 6L, 9L, 4L, 8L, 3L, 2L]) == [2L, 6L, 9L, 4L, 8L, 3L, 5L]);\n    assert(candidate([5L, 6L, 3L, 4L, 8L, 9L, 2L, 1L]) == [2L, 6L, 3L, 4L, 8L, 9L, 5L, 1L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_33_sort_third", "test": "unittest\n{\n    alias candidate = sort_third;\n\n    assert(candidate([5L, 6L, 3L, 4L, 8L, 9L, 2L]) == [2L, 6L, 3L, 4L, 8L, 9L, 5L]);\n    assert(candidate([5L, 8L, 3L, 4L, 6L, 9L, 2L]) == [2L, 8L, 3L, 4L, 6L, 9L, 5L]);\n    assert(candidate([5L, 6L, 9L, 4L, 8L, 3L, 2L]) == [2L, 6L, 9L, 4L, 8L, 3L, 5L]);\n    assert(candidate([5L, 6L, 3L, 4L, 8L, 9L, 2L, 1L]) == [2L, 6L, 3L, 4L, 8L, 9L, 5L, 1L]);\n}\nvoid main(){}"}
{"name": "HumanEval_6_parse_nested_parens", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n    For each of the group, output the deepest level of nesting of parentheses.\n    E.g. (()()) has maximum two levels of nesting while ((())) has three.\n\n    >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n    [2L, 3L, 1L, 3L]\n    \n*/\nlong[] parse_nested_parens(string paren_string) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = parse_nested_parens;\n\n    assert(candidate(\"(()()) ((())) () ((())()())\") == [2L, 3L, 1L, 3L]);\n    assert(candidate(\"() (()) ((())) (((())))\") == [1L, 2L, 3L, 4L]);\n    assert(candidate(\"(()(())((())))\") == [4L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_6_parse_nested_parens", "test": "unittest\n{\n    alias candidate = parse_nested_parens;\n\n    assert(candidate(\"(()()) ((())) () ((())()())\") == [2L, 3L, 1L, 3L]);\n    assert(candidate(\"() (()) ((())) (((())))\") == [1L, 2L, 3L, 4L]);\n    assert(candidate(\"(()(())((())))\") == [4L]);\n}\nvoid main(){}"}
{"name": "HumanEval_45_triangle_area", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nGiven length of a side and high return area for a triangle.\n    >>> triangle_area(5L, 3L)\n    7.5\n    \n*/\nfloat triangle_area(long a, long h) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = triangle_area;\n\n    assert(candidate(5L, 3L) == 7.5);\n    assert(candidate(2L, 2L) == 2.0);\n    assert(candidate(10L, 8L) == 40.0);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_45_triangle_area", "test": "unittest\n{\n    alias candidate = triangle_area;\n\n    assert(candidate(5L, 3L) == 7.5);\n    assert(candidate(2L, 2L) == 2.0);\n    assert(candidate(10L, 8L) == 40.0);\n}\nvoid main(){}"}
{"name": "HumanEval_97_multiply", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nComplete the function that takes two integers and returns \n    the product of their unit digits.\n    Assume the input is always valid.\n    Examples:\n    >>> multiply(148L, 412L)\n    16L\n    >>> multiply(19L, 28L)\n    72L\n    >>> multiply(2020L, 1851L)\n    0L\n    >>> multiply(14L, -15L)\n    20L\n    \n*/\nlong multiply(long a, long b) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = multiply;\n\n    assert(candidate(148L, 412L) == 16L);\n    assert(candidate(19L, 28L) == 72L);\n    assert(candidate(2020L, 1851L) == 0L);\n    assert(candidate(14L, -15L) == 20L);\n    assert(candidate(76L, 67L) == 42L);\n    assert(candidate(17L, 27L) == 49L);\n    assert(candidate(0L, 1L) == 0L);\n    assert(candidate(0L, 0L) == 0L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_97_multiply", "test": "unittest\n{\n    alias candidate = multiply;\n\n    assert(candidate(148L, 412L) == 16L);\n    assert(candidate(19L, 28L) == 72L);\n    assert(candidate(2020L, 1851L) == 0L);\n    assert(candidate(14L, -15L) == 20L);\n    assert(candidate(76L, 67L) == 42L);\n    assert(candidate(17L, 27L) == 49L);\n    assert(candidate(0L, 1L) == 0L);\n    assert(candidate(0L, 0L) == 0L);\n}\nvoid main(){}"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n For a given array of input numbers, calculate Mean Absolute Deviation\n    around the mean of this dataset.\n    Mean Absolute Deviation is the average absolute difference between each\n    element and a centerpoint (mean in this case):\n    MAD = average | x - x_mean |\n    >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n    1.0\n    \n*/\nfloat mean_absolute_deviation(float[] numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = mean_absolute_deviation;\n\n    assert(candidate([1.0, 2.0]) == 0.5);\n    assert(candidate([1.0, 2.0, 3.0, 4.0]) == 1.0);\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0]) == 1.2);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "unittest\n{\n    alias candidate = mean_absolute_deviation;\n\n    assert(candidate([1.0, 2.0]) == 0.5);\n    assert(candidate([1.0, 2.0, 3.0, 4.0]) == 1.0);\n    assert(candidate([1.0, 2.0, 3.0, 4.0, 5.0]) == 1.2);\n}\nvoid main(){}"}
{"name": "HumanEval_58_common", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nReturn sorted unique common elements for two arrays.\n    >>> common([1L, 4L, 3L, 34L, 653L, 2L, 5L], [5L, 7L, 1L, 5L, 9L, 653L, 121L])\n    [1L, 5L, 653L]\n    >>> common([5L, 3L, 2L, 8L], [3L, 2L])\n    [2L, 3L]\n\n    \n*/\nlong[] common(long[] l1, long[] l2) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = common;\n\n    assert(candidate([1L, 4L, 3L, 34L, 653L, 2L, 5L], [5L, 7L, 1L, 5L, 9L, 653L, 121L]) == [1L, 5L, 653L]);\n    assert(candidate([5L, 3L, 2L, 8L], [3L, 2L]) == [2L, 3L]);\n    assert(candidate([4L, 3L, 2L, 8L], [3L, 2L, 4L]) == [2L, 3L, 4L]);\n    assert(candidate([4L, 3L, 2L, 8L], []) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_58_common", "test": "unittest\n{\n    alias candidate = common;\n\n    assert(candidate([1L, 4L, 3L, 34L, 653L, 2L, 5L], [5L, 7L, 1L, 5L, 9L, 653L, 121L]) == [1L, 5L, 653L]);\n    assert(candidate([5L, 3L, 2L, 8L], [3L, 2L]) == [2L, 3L]);\n    assert(candidate([4L, 3L, 2L, 8L], [3L, 2L, 4L]) == [2L, 3L, 4L]);\n    assert(candidate([4L, 3L, 2L, 8L], []) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a positive integer, obtain its roman numeral equivalent as a string,\n    and return it in lowercase.\n    Restrictions: 1 <= num <= 1000\n\n    Examples:\n    >>> int_to_mini_roman(19L)\n    \"xix\"\n    >>> int_to_mini_roman(152L)\n    \"clii\"\n    >>> int_to_mini_roman(426L)\n    \"cdxxvi\"\n    \n*/\nstring int_to_mini_roman(long number) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = int_to_mini_roman;\n\n    assert(candidate(19L) == \"xix\");\n    assert(candidate(152L) == \"clii\");\n    assert(candidate(251L) == \"ccli\");\n    assert(candidate(426L) == \"cdxxvi\");\n    assert(candidate(500L) == \"d\");\n    assert(candidate(1L) == \"i\");\n    assert(candidate(4L) == \"iv\");\n    assert(candidate(43L) == \"xliii\");\n    assert(candidate(90L) == \"xc\");\n    assert(candidate(94L) == \"xciv\");\n    assert(candidate(532L) == \"dxxxii\");\n    assert(candidate(900L) == \"cm\");\n    assert(candidate(994L) == \"cmxciv\");\n    assert(candidate(1000L) == \"m\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "unittest\n{\n    alias candidate = int_to_mini_roman;\n\n    assert(candidate(19L) == \"xix\");\n    assert(candidate(152L) == \"clii\");\n    assert(candidate(251L) == \"ccli\");\n    assert(candidate(426L) == \"cdxxvi\");\n    assert(candidate(500L) == \"d\");\n    assert(candidate(1L) == \"i\");\n    assert(candidate(4L) == \"iv\");\n    assert(candidate(43L) == \"xliii\");\n    assert(candidate(90L) == \"xc\");\n    assert(candidate(94L) == \"xciv\");\n    assert(candidate(532L) == \"dxxxii\");\n    assert(candidate(900L) == \"cm\");\n    assert(candidate(994L) == \"cmxciv\");\n    assert(candidate(1000L) == \"m\");\n}\nvoid main(){}"}
{"name": "HumanEval_67_fruit_distribution", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    In this task, you will be given a string that represents a number of apples and oranges \n    that are distributed in a basket of fruit this basket contains \n    apples, oranges, and mango fruits. Given the string that represents the total number of \n    the oranges and apples and an integer that represent the total number of the fruits \n    in the basket return the number of the mango fruits in the basket.\n    for examble:\n    >>> fruit_distribution(\"5 apples and 6 oranges\", 19L)\n    8L\n    >>> fruit_distribution(\"0 apples and 1 oranges\", 3L)\n    2L\n    >>> fruit_distribution(\"2 apples and 3 oranges\", 100L)\n    95L\n    >>> fruit_distribution(\"100 apples and 1 oranges\", 120L)\n    19L\n    \n*/\nlong fruit_distribution(string s, long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = fruit_distribution;\n\n    assert(candidate(\"5 apples and 6 oranges\", 19L) == 8L);\n    assert(candidate(\"5 apples and 6 oranges\", 21L) == 10L);\n    assert(candidate(\"0 apples and 1 oranges\", 3L) == 2L);\n    assert(candidate(\"1 apples and 0 oranges\", 3L) == 2L);\n    assert(candidate(\"2 apples and 3 oranges\", 100L) == 95L);\n    assert(candidate(\"2 apples and 3 oranges\", 5L) == 0L);\n    assert(candidate(\"1 apples and 100 oranges\", 120L) == 19L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_67_fruit_distribution", "test": "unittest\n{\n    alias candidate = fruit_distribution;\n\n    assert(candidate(\"5 apples and 6 oranges\", 19L) == 8L);\n    assert(candidate(\"5 apples and 6 oranges\", 21L) == 10L);\n    assert(candidate(\"0 apples and 1 oranges\", 3L) == 2L);\n    assert(candidate(\"1 apples and 0 oranges\", 3L) == 2L);\n    assert(candidate(\"2 apples and 3 oranges\", 100L) == 95L);\n    assert(candidate(\"2 apples and 3 oranges\", 5L) == 0L);\n    assert(candidate(\"1 apples and 100 oranges\", 120L) == 19L);\n}\nvoid main(){}"}
{"name": "HumanEval_112_reverse_delete", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nTask\n    We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n    then check if the result string is palindrome.\n    A string is called palindrome if it reads the same backward as forward.\n    You should return a tuple containing the result string and true/false for the check.\n    Example\n    >>> reverse_delete(\"abcde\", \"ae\")\n    tuple(\"bcd\", false)\n    >>> reverse_delete(\"abcdef\", \"b\")\n    tuple(\"acdef\", false)\n    >>> reverse_delete(\"abcdedcba\", \"ab\")\n    tuple(\"cdedc\", true)\n    \n*/\nTuple!(string, bool) reverse_delete(string s, string c) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = reverse_delete;\n\n    assert(candidate(\"abcde\", \"ae\") == tuple(\"bcd\", false));\n    assert(candidate(\"abcdef\", \"b\") == tuple(\"acdef\", false));\n    assert(candidate(\"abcdedcba\", \"ab\") == tuple(\"cdedc\", true));\n    assert(candidate(\"dwik\", \"w\") == tuple(\"dik\", false));\n    assert(candidate(\"a\", \"a\") == tuple(\"\", true));\n    assert(candidate(\"abcdedcba\", \"\") == tuple(\"abcdedcba\", true));\n    assert(candidate(\"abcdedcba\", \"v\") == tuple(\"abcdedcba\", true));\n    assert(candidate(\"vabba\", \"v\") == tuple(\"abba\", true));\n    assert(candidate(\"mamma\", \"mia\") == tuple(\"\", true));\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_112_reverse_delete", "test": "unittest\n{\n    alias candidate = reverse_delete;\n\n    assert(candidate(\"abcde\", \"ae\") == tuple(\"bcd\", false));\n    assert(candidate(\"abcdef\", \"b\") == tuple(\"acdef\", false));\n    assert(candidate(\"abcdedcba\", \"ab\") == tuple(\"cdedc\", true));\n    assert(candidate(\"dwik\", \"w\") == tuple(\"dik\", false));\n    assert(candidate(\"a\", \"a\") == tuple(\"\", true));\n    assert(candidate(\"abcdedcba\", \"\") == tuple(\"abcdedcba\", true));\n    assert(candidate(\"abcdedcba\", \"v\") == tuple(\"abcdedcba\", true));\n    assert(candidate(\"vabba\", \"v\") == tuple(\"abba\", true));\n    assert(candidate(\"mamma\", \"mia\") == tuple(\"\", true));\n}\nvoid main(){}"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Return a greatest common divisor of two integers a and b\n    >>> greatest_common_divisor(3L, 5L)\n    1L\n    >>> greatest_common_divisor(25L, 15L)\n    5L\n    \n*/\nlong greatest_common_divisor(long a, long b) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = greatest_common_divisor;\n\n    assert(candidate(3L, 7L) == 1L);\n    assert(candidate(10L, 15L) == 5L);\n    assert(candidate(49L, 14L) == 7L);\n    assert(candidate(144L, 60L) == 12L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "unittest\n{\n    alias candidate = greatest_common_divisor;\n\n    assert(candidate(3L, 7L) == 1L);\n    assert(candidate(10L, 15L) == 5L);\n    assert(candidate(49L, 14L) == 7L);\n    assert(candidate(144L, 60L) == 12L);\n}\nvoid main(){}"}
{"name": "HumanEval_116_sort_array", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    In this Kata, you have to sort an array of non-negative integers according to\n    number of ones in their binary representation in ascending order.\n    For similar number of ones, sort based on decimal value.\n\n    It must be implemented like this:\n    >>> sort_array([1L, 5L, 2L, 3L, 4L])\n    [1L, 2L, 3L, 4L, 5L]\n    >>> sort_array([-2L, -3L, -4L, -5L, -6L])\n    [-6L, -5L, -4L, -3L, -2L]\n    >>> sort_array([1L, 0L, 2L, 3L, 4L])\n    [0L, 1L, 2L, 3L, 4L]\n    \n*/\nlong[] sort_array(long[] arr) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sort_array;\n\n    assert(candidate([1L, 5L, 2L, 3L, 4L]) == [1L, 2L, 4L, 3L, 5L]);\n    assert(candidate([-2L, -3L, -4L, -5L, -6L]) == [-4L, -2L, -6L, -5L, -3L]);\n    assert(candidate([1L, 0L, 2L, 3L, 4L]) == [0L, 1L, 2L, 4L, 3L]);\n    assert(candidate([]) == []);\n    assert(candidate([2L, 5L, 77L, 4L, 5L, 3L, 5L, 7L, 2L, 3L, 4L]) == [2L, 2L, 4L, 4L, 3L, 3L, 5L, 5L, 5L, 7L, 77L]);\n    assert(candidate([3L, 6L, 44L, 12L, 32L, 5L]) == [32L, 3L, 5L, 6L, 12L, 44L]);\n    assert(candidate([2L, 4L, 8L, 16L, 32L]) == [2L, 4L, 8L, 16L, 32L]);\n    assert(candidate([2L, 4L, 8L, 16L, 32L]) == [2L, 4L, 8L, 16L, 32L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_116_sort_array", "test": "unittest\n{\n    alias candidate = sort_array;\n\n    assert(candidate([1L, 5L, 2L, 3L, 4L]) == [1L, 2L, 4L, 3L, 5L]);\n    assert(candidate([-2L, -3L, -4L, -5L, -6L]) == [-4L, -2L, -6L, -5L, -3L]);\n    assert(candidate([1L, 0L, 2L, 3L, 4L]) == [0L, 1L, 2L, 4L, 3L]);\n    assert(candidate([]) == []);\n    assert(candidate([2L, 5L, 77L, 4L, 5L, 3L, 5L, 7L, 2L, 3L, 4L]) == [2L, 2L, 4L, 4L, 3L, 3L, 5L, 5L, 5L, 7L, 77L]);\n    assert(candidate([3L, 6L, 44L, 12L, 32L, 5L]) == [32L, 3L, 5L, 6L, 12L, 44L]);\n    assert(candidate([2L, 4L, 8L, 16L, 32L]) == [2L, 4L, 8L, 16L, 32L]);\n    assert(candidate([2L, 4L, 8L, 16L, 32L]) == [2L, 4L, 8L, 16L, 32L]);\n}\nvoid main(){}"}
{"name": "HumanEval_28_concatenate", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Concatenate array of strings into a single string\n    >>> concatenate([])\n    \"\"\n    >>> concatenate([\"a\", \"b\", \"c\"])\n    \"abc\"\n    \n*/\nstring concatenate(string[] strings) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = concatenate;\n\n    assert(candidate([]) == \"\");\n    assert(candidate([\"x\", \"y\", \"z\"]) == \"xyz\");\n    assert(candidate([\"x\", \"y\", \"z\", \"w\", \"k\"]) == \"xyzwk\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_28_concatenate", "test": "unittest\n{\n    alias candidate = concatenate;\n\n    assert(candidate([]) == \"\");\n    assert(candidate([\"x\", \"y\", \"z\"]) == \"xyz\");\n    assert(candidate([\"x\", \"y\", \"z\", \"w\", \"k\"]) == \"xyzwk\");\n}\nvoid main(){}"}
{"name": "HumanEval_149_sorted_list_sum", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nWrite a function that accepts an array of strings as a parameter,\n    deletes the strings that have odd lengths from it,\n    and returns the resulted array with a sorted order,\n    The array is always an array of strings and never an array of numbers,\n    and it may contain duplicates.\n    The order of the array should be ascending by length of each word, and you\n    should return the array sorted by that rule.\n    If two words have the same length, sort the array alphabetically.\n    The function should return an array of strings in sorted order.\n    You may assume that all words will have the same length.\n    For example:\n    >>> list_sort([\"aa\", \"a\", \"aaa\"])\n    [\"aa\"]\n    >>> list_sort([\"ab\", \"a\", \"aaa\", \"cd\"])\n    [\"ab\", \"cd\"]\n    \n*/\nstring[] sorted_list_sum(string[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sorted_list_sum;\n\n    assert(candidate([\"aa\", \"a\", \"aaa\"]) == [\"aa\"]);\n    assert(candidate([\"school\", \"AI\", \"asdf\", \"b\"]) == [\"AI\", \"asdf\", \"school\"]);\n    assert(candidate([\"d\", \"b\", \"c\", \"a\"]) == []);\n    assert(candidate([\"d\", \"dcba\", \"abcd\", \"a\"]) == [\"abcd\", \"dcba\"]);\n    assert(candidate([\"AI\", \"ai\", \"au\"]) == [\"AI\", \"ai\", \"au\"]);\n    assert(candidate([\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"]) == []);\n    assert(candidate([\"aaaa\", \"bbbb\", \"dd\", \"cc\"]) == [\"cc\", \"dd\", \"aaaa\", \"bbbb\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_149_sorted_list_sum", "test": "unittest\n{\n    alias candidate = sorted_list_sum;\n\n    assert(candidate([\"aa\", \"a\", \"aaa\"]) == [\"aa\"]);\n    assert(candidate([\"school\", \"AI\", \"asdf\", \"b\"]) == [\"AI\", \"asdf\", \"school\"]);\n    assert(candidate([\"d\", \"b\", \"c\", \"a\"]) == []);\n    assert(candidate([\"d\", \"dcba\", \"abcd\", \"a\"]) == [\"abcd\", \"dcba\"]);\n    assert(candidate([\"AI\", \"ai\", \"au\"]) == [\"AI\", \"ai\", \"au\"]);\n    assert(candidate([\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"]) == []);\n    assert(candidate([\"aaaa\", \"bbbb\", \"dd\", \"cc\"]) == [\"cc\", \"dd\", \"aaaa\", \"bbbb\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_7_filter_by_substring", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Filter an input array of strings only for ones that contain given substring\n    >>> filter_by_substring([], \"a\")\n    []\n    >>> filter_by_substring([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n    [\"abc\", \"bacd\", \"array\"]\n    \n*/\nstring[] filter_by_substring(string[] strings, string substring) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = filter_by_substring;\n\n    assert(candidate([], \"john\") == []);\n    assert(candidate([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"]);\n    assert(candidate([\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xx\") == [\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"]);\n    assert(candidate([\"grunt\", \"trumpet\", \"prune\", \"gruesome\"], \"run\") == [\"grunt\", \"prune\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_7_filter_by_substring", "test": "unittest\n{\n    alias candidate = filter_by_substring;\n\n    assert(candidate([], \"john\") == []);\n    assert(candidate([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"]);\n    assert(candidate([\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xx\") == [\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"]);\n    assert(candidate([\"grunt\", \"trumpet\", \"prune\", \"gruesome\"], \"run\") == [\"grunt\", \"prune\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_99_closest_integer", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Create a function that takes a value (string) representing a number\n    and returns the closest integer to it. If the number is equidistant\n    from two integers, round it away from zero.\n\n    Examples\n    >>> closest_integer(\"10\")\n    10L\n    >>> closest_integer(\"15.3\")\n    15L\n\n    Note:\n    Rounding away from zero means that if the given number is equidistant\n    from two integers, the one you should return is the one that is the\n    farthest from zero. For example closest_integer(\"14.5\") should\n    return 15 and closest_integer(\"-14.5\") should return -15.\n    \n*/\nlong closest_integer(string value) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = closest_integer;\n\n    assert(candidate(\"10\") == 10L);\n    assert(candidate(\"14.5\") == 15L);\n    assert(candidate(\"-15.5\") == -16L);\n    assert(candidate(\"15.3\") == 15L);\n    assert(candidate(\"0\") == 0L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_99_closest_integer", "test": "unittest\n{\n    alias candidate = closest_integer;\n\n    assert(candidate(\"10\") == 10L);\n    assert(candidate(\"14.5\") == 15L);\n    assert(candidate(\"-15.5\") == -16L);\n    assert(candidate(\"15.3\") == 15L);\n    assert(candidate(\"0\") == 0L);\n}\nvoid main(){}"}
{"name": "HumanEval_64_vowels_count", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nWrite a function vowels_count which takes a string representing\n    a word as input and returns the number of vowels in the string.\n    Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n    vowel, but only when it is at the end of the given word.\n\n    Example:\n    >>> vowels_count(\"abcde\")\n    2L\n    >>> vowels_count(\"ACEDY\")\n    3L\n    \n*/\nlong vowels_count(string s) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = vowels_count;\n\n    assert(candidate(\"abcde\") == 2L);\n    assert(candidate(\"Alone\") == 3L);\n    assert(candidate(\"key\") == 2L);\n    assert(candidate(\"bye\") == 1L);\n    assert(candidate(\"keY\") == 2L);\n    assert(candidate(\"bYe\") == 1L);\n    assert(candidate(\"ACEDY\") == 3L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_64_vowels_count", "test": "unittest\n{\n    alias candidate = vowels_count;\n\n    assert(candidate(\"abcde\") == 2L);\n    assert(candidate(\"Alone\") == 3L);\n    assert(candidate(\"key\") == 2L);\n    assert(candidate(\"bye\") == 1L);\n    assert(candidate(\"keY\") == 2L);\n    assert(candidate(\"bYe\") == 1L);\n    assert(candidate(\"ACEDY\") == 3L);\n}\nvoid main(){}"}
{"name": "HumanEval_158_find_max", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nWrite a function that accepts an array of strings.\n    The array contains different words. Return the word with maximum number\n    of unique characters. If multiple strings have maximum number of unique\n    characters, return the one which comes first in lexicographical order.\n\n    >>> find_max([\"name\", \"of\", \"string\"])\n    \"string\"\n    >>> find_max([\"name\", \"enam\", \"game\"])\n    \"enam\"\n    >>> find_max([\"aaaaaaa\", \"bb\", \"cc\"])\n    \"aaaaaaa\"\n    \n*/\nstring find_max(string[] words) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = find_max;\n\n    assert(candidate([\"name\", \"of\", \"string\"]) == \"string\");\n    assert(candidate([\"name\", \"enam\", \"game\"]) == \"enam\");\n    assert(candidate([\"aaaaaaa\", \"bb\", \"cc\"]) == \"aaaaaaa\");\n    assert(candidate([\"abc\", \"cba\"]) == \"abc\");\n    assert(candidate([\"play\", \"this\", \"game\", \"of\", \"footbott\"]) == \"footbott\");\n    assert(candidate([\"we\", \"are\", \"gonna\", \"rock\"]) == \"gonna\");\n    assert(candidate([\"we\", \"are\", \"a\", \"mad\", \"nation\"]) == \"nation\");\n    assert(candidate([\"this\", \"is\", \"a\", \"prrk\"]) == \"this\");\n    assert(candidate([\"b\"]) == \"b\");\n    assert(candidate([\"play\", \"play\", \"play\"]) == \"play\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_158_find_max", "test": "unittest\n{\n    alias candidate = find_max;\n\n    assert(candidate([\"name\", \"of\", \"string\"]) == \"string\");\n    assert(candidate([\"name\", \"enam\", \"game\"]) == \"enam\");\n    assert(candidate([\"aaaaaaa\", \"bb\", \"cc\"]) == \"aaaaaaa\");\n    assert(candidate([\"abc\", \"cba\"]) == \"abc\");\n    assert(candidate([\"play\", \"this\", \"game\", \"of\", \"footbott\"]) == \"footbott\");\n    assert(candidate([\"we\", \"are\", \"gonna\", \"rock\"]) == \"gonna\");\n    assert(candidate([\"we\", \"are\", \"a\", \"mad\", \"nation\"]) == \"nation\");\n    assert(candidate([\"this\", \"is\", \"a\", \"prrk\"]) == \"this\");\n    assert(candidate([\"b\"]) == \"b\");\n    assert(candidate([\"play\", \"play\", \"play\"]) == \"play\");\n}\nvoid main(){}"}
{"name": "HumanEval_162_string_to_md5", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given a string 'text', return its md5 hash equivalent string.\n    If 'text' is an empty string, return null.\n\n    >>> string_to_md5(\"Hello world\")\n    \"3e25960a79dbc69b674cd4ec67a72c62\"\n    \n*/\nNullable!(string) string_to_md5(string text) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = string_to_md5;\n\n{\n        auto result = candidate(\"Hello world\");\n        assert(!result.isNull && result.get == \"3e25960a79dbc69b674cd4ec67a72c62\");\n}\n\n{\n        auto result = candidate(\"\");\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate(\"A B C\");\n        assert(!result.isNull && result.get == \"0ef78513b0cb8cef12743f5aeb35f888\");\n}\n\n{\n        auto result = candidate(\"password\");\n        assert(!result.isNull && result.get == \"5f4dcc3b5aa765d61d8327deb882cf99\");\n}\n\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_162_string_to_md5", "test": "unittest\n{\n    alias candidate = string_to_md5;\n\n{\n        auto result = candidate(\"Hello world\");\n        assert(!result.isNull && result.get == \"3e25960a79dbc69b674cd4ec67a72c62\");\n}\n\n{\n        auto result = candidate(\"\");\n        assert(result.isNull);\n}\n\n{\n        auto result = candidate(\"A B C\");\n        assert(!result.isNull && result.get == \"0ef78513b0cb8cef12743f5aeb35f888\");\n}\n\n{\n        auto result = candidate(\"password\");\n        assert(!result.isNull && result.get == \"5f4dcc3b5aa765d61d8327deb882cf99\");\n}\n\n}\nvoid main(){}"}
{"name": "HumanEval_44_change_base", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nChange numerical base of input number x to base.\n    return string representation after the conversion.\n    base numbers are less than 10.\n    >>> change_base(8L, 3L)\n    \"22\"\n    >>> change_base(8L, 2L)\n    \"1000\"\n    >>> change_base(7L, 2L)\n    \"111\"\n    \n*/\nstring change_base(long x, long base) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = change_base;\n\n    assert(candidate(8L, 3L) == \"22\");\n    assert(candidate(9L, 3L) == \"100\");\n    assert(candidate(234L, 2L) == \"11101010\");\n    assert(candidate(16L, 2L) == \"10000\");\n    assert(candidate(8L, 2L) == \"1000\");\n    assert(candidate(7L, 2L) == \"111\");\n    assert(candidate(2L, 3L) == \"2\");\n    assert(candidate(3L, 4L) == \"3\");\n    assert(candidate(4L, 5L) == \"4\");\n    assert(candidate(5L, 6L) == \"5\");\n    assert(candidate(6L, 7L) == \"6\");\n    assert(candidate(7L, 8L) == \"7\");\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_44_change_base", "test": "unittest\n{\n    alias candidate = change_base;\n\n    assert(candidate(8L, 3L) == \"22\");\n    assert(candidate(9L, 3L) == \"100\");\n    assert(candidate(234L, 2L) == \"11101010\");\n    assert(candidate(16L, 2L) == \"10000\");\n    assert(candidate(8L, 2L) == \"1000\");\n    assert(candidate(7L, 2L) == \"111\");\n    assert(candidate(2L, 3L) == \"2\");\n    assert(candidate(3L, 4L) == \"3\");\n    assert(candidate(4L, 5L) == \"4\");\n    assert(candidate(5L, 6L) == \"5\");\n    assert(candidate(6L, 7L) == \"6\");\n    assert(candidate(7L, 8L) == \"7\");\n}\nvoid main(){}"}
{"name": "HumanEval_157_right_angle_triangle", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given the lengths of the three sides of a triangle. Return true if the three\n    sides form a right-angled triangle, false otherwise.\n    A right-angled triangle is a triangle in which one angle is right angle or \n    90 degree.\n    Example:\n    >>> right_angle_triangle(3L, 4L, 5L)\n    true\n    >>> right_angle_triangle(1L, 2L, 3L)\n    false\n    \n*/\nbool right_angle_triangle(long a, long b, long c) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = right_angle_triangle;\n\n    assert(candidate(3L, 4L, 5L) == true);\n    assert(candidate(1L, 2L, 3L) == false);\n    assert(candidate(10L, 6L, 8L) == true);\n    assert(candidate(2L, 2L, 2L) == false);\n    assert(candidate(7L, 24L, 25L) == true);\n    assert(candidate(10L, 5L, 7L) == false);\n    assert(candidate(5L, 12L, 13L) == true);\n    assert(candidate(15L, 8L, 17L) == true);\n    assert(candidate(48L, 55L, 73L) == true);\n    assert(candidate(1L, 1L, 1L) == false);\n    assert(candidate(2L, 2L, 10L) == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_157_right_angle_triangle", "test": "unittest\n{\n    alias candidate = right_angle_triangle;\n\n    assert(candidate(3L, 4L, 5L) == true);\n    assert(candidate(1L, 2L, 3L) == false);\n    assert(candidate(10L, 6L, 8L) == true);\n    assert(candidate(2L, 2L, 2L) == false);\n    assert(candidate(7L, 24L, 25L) == true);\n    assert(candidate(10L, 5L, 7L) == false);\n    assert(candidate(5L, 12L, 13L) == true);\n    assert(candidate(15L, 8L, 17L) == true);\n    assert(candidate(48L, 55L, 73L) == true);\n    assert(candidate(1L, 1L, 1L) == false);\n    assert(candidate(2L, 2L, 10L) == false);\n}\nvoid main(){}"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nIt is the last week of the semester and the teacher has to give the grades\n    to students. The teacher has been making her own algorithm for grading.\n    The only problem is, she has lost the code she used for grading.\n    She has given you an array of GPAs for some students and you have to write \n    a function that can output an array of letter grades using the following table:\n             GPA       |    Letter grade\n              4.0                A+\n            > 3.7                A \n            > 3.3                A- \n            > 3.0                B+\n            > 2.7                B \n            > 2.3                B-\n            > 2.0                C+\n            > 1.7                C\n            > 1.3                C-\n            > 1.0                D+ \n            > 0.7                D \n            > 0.0                D-\n              0.0                E\n    \n\n    Example:\n    >>> grade_equation([4.0, 3L, 1.7, 2L, 3.5])\n    [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\n    \n*/\nstring[] numerical_letter_grade(float[] grades) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = numerical_letter_grade;\n\n    assert(candidate([4.0, 3L, 1.7, 2L, 3.5]) == [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]);\n    assert(candidate([1.2]) == [\"D+\"]);\n    assert(candidate([0.5]) == [\"D-\"]);\n    assert(candidate([0.0]) == [\"E\"]);\n    assert(candidate([1.0, 0.3, 1.5, 2.8, 3.3]) == [\"D\", \"D-\", \"C-\", \"B\", \"B+\"]);\n    assert(candidate([0.0, 0.7]) == [\"E\", \"D-\"]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "unittest\n{\n    alias candidate = numerical_letter_grade;\n\n    assert(candidate([4.0, 3L, 1.7, 2L, 3.5]) == [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]);\n    assert(candidate([1.2]) == [\"D+\"]);\n    assert(candidate([0.5]) == [\"D-\"]);\n    assert(candidate([0.0]) == [\"E\"]);\n    assert(candidate([1.0, 0.3, 1.5, 2.8, 3.3]) == [\"D\", \"D-\", \"C-\", \"B\", \"B+\"]);\n    assert(candidate([0.0, 0.7]) == [\"E\", \"D-\"]);\n}\nvoid main(){}"}
{"name": "HumanEval_5_intersperse", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n    >>> intersperse([], 4L)\n    []\n    >>> intersperse([1L, 2L, 3L], 4L)\n    [1L, 4L, 2L, 4L, 3L]\n    \n*/\nlong[] intersperse(long[] numbers, long delimeter) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = intersperse;\n\n    assert(candidate([], 7L) == []);\n    assert(candidate([5L, 6L, 3L, 2L], 8L) == [5L, 8L, 6L, 8L, 3L, 8L, 2L]);\n    assert(candidate([2L, 2L, 2L], 2L) == [2L, 2L, 2L, 2L, 2L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_5_intersperse", "test": "unittest\n{\n    alias candidate = intersperse;\n\n    assert(candidate([], 7L) == []);\n    assert(candidate([5L, 6L, 3L, 2L], 8L) == [5L, 8L, 6L, 8L, 3L, 8L, 2L]);\n    assert(candidate([2L, 2L, 2L], 2L) == [2L, 2L, 2L, 2L, 2L]);\n}\nvoid main(){}"}
{"name": "HumanEval_146_specialFilter", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nWrite a function that takes an array of numbers as input and returns \n    the number of elements in the array that are greater than 10 and both \n    first and last digits of a number are odd (1, 3, 5, 7, 9).\n    For example:\n    >>> specialFilter([15L, -73L, 14L, -15L])\n    1L\n    >>> specialFilter([33L, -2L, -3L, 45L, 21L, 109L])\n    2L\n    \n*/\nlong specialFilter(long[] nums) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = specialFilter;\n\n    assert(candidate([5L, -2L, 1L, -5L]) == 0L);\n    assert(candidate([15L, -73L, 14L, -15L]) == 1L);\n    assert(candidate([33L, -2L, -3L, 45L, 21L, 109L]) == 2L);\n    assert(candidate([43L, -12L, 93L, 125L, 121L, 109L]) == 4L);\n    assert(candidate([71L, -2L, -33L, 75L, 21L, 19L]) == 3L);\n    assert(candidate([1L]) == 0L);\n    assert(candidate([]) == 0L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_146_specialFilter", "test": "unittest\n{\n    alias candidate = specialFilter;\n\n    assert(candidate([5L, -2L, 1L, -5L]) == 0L);\n    assert(candidate([15L, -73L, 14L, -15L]) == 1L);\n    assert(candidate([33L, -2L, -3L, 45L, 21L, 109L]) == 2L);\n    assert(candidate([43L, -12L, 93L, 125L, 121L, 109L]) == 4L);\n    assert(candidate([71L, -2L, -33L, 75L, 21L, 19L]) == 3L);\n    assert(candidate([1L]) == 0L);\n    assert(candidate([]) == 0L);\n}\nvoid main(){}"}
{"name": "HumanEval_60_sum_to_n", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nsum_to_n is a function that sums numbers from 1 to n.\n    >>> sum_to_n(30L)\n    465L\n    >>> sum_to_n(100L)\n    5050L\n    >>> sum_to_n(5L)\n    15L\n    >>> sum_to_n(10L)\n    55L\n    >>> sum_to_n(1L)\n    1L\n    \n*/\nlong sum_to_n(long n) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sum_to_n;\n\n    assert(candidate(1L) == 1L);\n    assert(candidate(6L) == 21L);\n    assert(candidate(11L) == 66L);\n    assert(candidate(30L) == 465L);\n    assert(candidate(100L) == 5050L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_60_sum_to_n", "test": "unittest\n{\n    alias candidate = sum_to_n;\n\n    assert(candidate(1L) == 1L);\n    assert(candidate(6L) == 21L);\n    assert(candidate(11L) == 66L);\n    assert(candidate(30L) == 465L);\n    assert(candidate(100L) == 5050L);\n}\nvoid main(){}"}
{"name": "HumanEval_26_remove_duplicates", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n From an array of integers, remove all elements that occur more than once.\n    Keep order of elements left the same as in the input.\n    >>> remove_duplicates([1L, 2L, 3L, 2L, 4L])\n    [1L, 3L, 4L]\n    \n*/\nlong[] remove_duplicates(long[] numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = remove_duplicates;\n\n    assert(candidate([]) == []);\n    assert(candidate([1L, 2L, 3L, 4L]) == [1L, 2L, 3L, 4L]);\n    assert(candidate([1L, 2L, 3L, 2L, 4L, 3L, 5L]) == [1L, 4L, 5L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_26_remove_duplicates", "test": "unittest\n{\n    alias candidate = remove_duplicates;\n\n    assert(candidate([]) == []);\n    assert(candidate([1L, 2L, 3L, 4L]) == [1L, 2L, 3L, 4L]);\n    assert(candidate([1L, 2L, 3L, 2L, 4L, 3L, 5L]) == [1L, 4L, 5L]);\n}\nvoid main(){}"}
{"name": "HumanEval_163_generate_integers", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Given two positive integers a and b, return the even digits between a\n    and b, in ascending order.\n\n    For example:\n    >>> generate_integers(2L, 8L)\n    [2L, 4L, 6L, 8L]\n    >>> generate_integers(8L, 2L)\n    [2L, 4L, 6L, 8L]\n    >>> generate_integers(10L, 14L)\n    []\n    \n*/\nlong[] generate_integers(long a, long b) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = generate_integers;\n\n    assert(candidate(2L, 10L) == [2L, 4L, 6L, 8L]);\n    assert(candidate(10L, 2L) == [2L, 4L, 6L, 8L]);\n    assert(candidate(132L, 2L) == [2L, 4L, 6L, 8L]);\n    assert(candidate(17L, 89L) == []);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_163_generate_integers", "test": "unittest\n{\n    alias candidate = generate_integers;\n\n    assert(candidate(2L, 10L) == [2L, 4L, 6L, 8L]);\n    assert(candidate(10L, 2L) == [2L, 4L, 6L, 8L]);\n    assert(candidate(132L, 2L) == [2L, 4L, 6L, 8L]);\n    assert(candidate(17L, 89L) == []);\n}\nvoid main(){}"}
{"name": "HumanEval_9_rolling_max", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n From a given array of integers, generate an array of rolling maximum element found until given moment\n    in the sequence.\n    >>> rolling_max([1L, 2L, 3L, 2L, 3L, 4L, 2L])\n    [1L, 2L, 3L, 3L, 3L, 4L, 4L]\n    \n*/\nlong[] rolling_max(long[] numbers) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = rolling_max;\n\n    assert(candidate([]) == []);\n    assert(candidate([1L, 2L, 3L, 4L]) == [1L, 2L, 3L, 4L]);\n    assert(candidate([4L, 3L, 2L, 1L]) == [4L, 4L, 4L, 4L]);\n    assert(candidate([3L, 2L, 3L, 100L, 3L]) == [3L, 3L, 3L, 100L, 100L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_9_rolling_max", "test": "unittest\n{\n    alias candidate = rolling_max;\n\n    assert(candidate([]) == []);\n    assert(candidate([1L, 2L, 3L, 4L]) == [1L, 2L, 3L, 4L]);\n    assert(candidate([4L, 3L, 2L, 1L]) == [4L, 4L, 4L, 4L]);\n    assert(candidate([3L, 2L, 3L, 100L, 3L]) == [3L, 3L, 3L, 100L, 100L]);\n}\nvoid main(){}"}
{"name": "HumanEval_3_below_zero", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n You're given an array of deposit and withdrawal operations on a bank account that starts with\n    zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n    at that point function should return true. Otherwise it should return false.\n    >>> below_zero([1L, 2L, 3L])\n    false\n    >>> below_zero([1L, 2L, -4L, 5L])\n    true\n    \n*/\nbool below_zero(long[] operations) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = below_zero;\n\n    assert(candidate([]) == false);\n    assert(candidate([1L, 2L, -3L, 1L, 2L, -3L]) == false);\n    assert(candidate([1L, 2L, -4L, 5L, 6L]) == true);\n    assert(candidate([1L, -1L, 2L, -2L, 5L, -5L, 4L, -4L]) == false);\n    assert(candidate([1L, -1L, 2L, -2L, 5L, -5L, 4L, -5L]) == true);\n    assert(candidate([1L, -2L, 2L, -2L, 5L, -5L, 4L, -4L]) == true);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_3_below_zero", "test": "unittest\n{\n    alias candidate = below_zero;\n\n    assert(candidate([]) == false);\n    assert(candidate([1L, 2L, -3L, 1L, 2L, -3L]) == false);\n    assert(candidate([1L, 2L, -4L, 5L, 6L]) == true);\n    assert(candidate([1L, -1L, 2L, -2L, 5L, -5L, 4L, -4L]) == false);\n    assert(candidate([1L, -1L, 2L, -2L, 5L, -5L, 4L, -5L]) == true);\n    assert(candidate([1L, -2L, 2L, -2L, 5L, -5L, 4L, -4L]) == true);\n}\nvoid main(){}"}
{"name": "HumanEval_69_search", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the array.\n    If no such a value exist, return -1.\n    Examples:\n    >>> search([4L, 1L, 2L, 2L, 3L, 1L])\n    2L\n    >>> search([1L, 2L, 2L, 3L, 3L, 3L, 4L, 4L, 4L])\n    3L\n    >>> search([5L, 5L, 4L, 4L, 4L])\n    -1L\n    \n*/\nlong search(long[] lst) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = search;\n\n    assert(candidate([5L, 5L, 5L, 5L, 1L]) == 1L);\n    assert(candidate([4L, 1L, 4L, 1L, 4L, 4L]) == 4L);\n    assert(candidate([3L, 3L]) == -1L);\n    assert(candidate([8L, 8L, 8L, 8L, 8L, 8L, 8L, 8L]) == 8L);\n    assert(candidate([2L, 3L, 3L, 2L, 2L]) == 2L);\n    assert(candidate([2L, 7L, 8L, 8L, 4L, 8L, 7L, 3L, 9L, 6L, 5L, 10L, 4L, 3L, 6L, 7L, 1L, 7L, 4L, 10L, 8L, 1L]) == 1L);\n    assert(candidate([3L, 2L, 8L, 2L]) == 2L);\n    assert(candidate([6L, 7L, 1L, 8L, 8L, 10L, 5L, 8L, 5L, 3L, 10L]) == 1L);\n    assert(candidate([8L, 8L, 3L, 6L, 5L, 6L, 4L]) == -1L);\n    assert(candidate([6L, 9L, 6L, 7L, 1L, 4L, 7L, 1L, 8L, 8L, 9L, 8L, 10L, 10L, 8L, 4L, 10L, 4L, 10L, 1L, 2L, 9L, 5L, 7L, 9L]) == 1L);\n    assert(candidate([1L, 9L, 10L, 1L, 3L]) == 1L);\n    assert(candidate([6L, 9L, 7L, 5L, 8L, 7L, 5L, 3L, 7L, 5L, 10L, 10L, 3L, 6L, 10L, 2L, 8L, 6L, 5L, 4L, 9L, 5L, 3L, 10L]) == 5L);\n    assert(candidate([1L]) == 1L);\n    assert(candidate([8L, 8L, 10L, 6L, 4L, 3L, 5L, 8L, 2L, 4L, 2L, 8L, 4L, 6L, 10L, 4L, 2L, 1L, 10L, 2L, 1L, 1L, 5L]) == 4L);\n    assert(candidate([2L, 10L, 4L, 8L, 2L, 10L, 5L, 1L, 2L, 9L, 5L, 5L, 6L, 3L, 8L, 6L, 4L, 10L]) == 2L);\n    assert(candidate([1L, 6L, 10L, 1L, 6L, 9L, 10L, 8L, 6L, 8L, 7L, 3L]) == 1L);\n    assert(candidate([9L, 2L, 4L, 1L, 5L, 1L, 5L, 2L, 5L, 7L, 7L, 7L, 3L, 10L, 1L, 5L, 4L, 2L, 8L, 4L, 1L, 9L, 10L, 7L, 10L, 2L, 8L, 10L, 9L, 4L]) == 4L);\n    assert(candidate([2L, 6L, 4L, 2L, 8L, 7L, 5L, 6L, 4L, 10L, 4L, 6L, 3L, 7L, 8L, 8L, 3L, 1L, 4L, 2L, 2L, 10L, 7L]) == 4L);\n    assert(candidate([9L, 8L, 6L, 10L, 2L, 6L, 10L, 2L, 7L, 8L, 10L, 3L, 8L, 2L, 6L, 2L, 3L, 1L]) == 2L);\n    assert(candidate([5L, 5L, 3L, 9L, 5L, 6L, 3L, 2L, 8L, 5L, 6L, 10L, 10L, 6L, 8L, 4L, 10L, 7L, 7L, 10L, 8L]) == -1L);\n    assert(candidate([10L]) == -1L);\n    assert(candidate([9L, 7L, 7L, 2L, 4L, 7L, 2L, 10L, 9L, 7L, 5L, 7L, 2L]) == 2L);\n    assert(candidate([5L, 4L, 10L, 2L, 1L, 1L, 10L, 3L, 6L, 1L, 8L]) == 1L);\n    assert(candidate([7L, 9L, 9L, 9L, 3L, 4L, 1L, 5L, 9L, 1L, 2L, 1L, 1L, 10L, 7L, 5L, 6L, 7L, 6L, 7L, 7L, 6L]) == 1L);\n    assert(candidate([3L, 10L, 10L, 9L, 2L]) == -1L);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_69_search", "test": "unittest\n{\n    alias candidate = search;\n\n    assert(candidate([5L, 5L, 5L, 5L, 1L]) == 1L);\n    assert(candidate([4L, 1L, 4L, 1L, 4L, 4L]) == 4L);\n    assert(candidate([3L, 3L]) == -1L);\n    assert(candidate([8L, 8L, 8L, 8L, 8L, 8L, 8L, 8L]) == 8L);\n    assert(candidate([2L, 3L, 3L, 2L, 2L]) == 2L);\n    assert(candidate([2L, 7L, 8L, 8L, 4L, 8L, 7L, 3L, 9L, 6L, 5L, 10L, 4L, 3L, 6L, 7L, 1L, 7L, 4L, 10L, 8L, 1L]) == 1L);\n    assert(candidate([3L, 2L, 8L, 2L]) == 2L);\n    assert(candidate([6L, 7L, 1L, 8L, 8L, 10L, 5L, 8L, 5L, 3L, 10L]) == 1L);\n    assert(candidate([8L, 8L, 3L, 6L, 5L, 6L, 4L]) == -1L);\n    assert(candidate([6L, 9L, 6L, 7L, 1L, 4L, 7L, 1L, 8L, 8L, 9L, 8L, 10L, 10L, 8L, 4L, 10L, 4L, 10L, 1L, 2L, 9L, 5L, 7L, 9L]) == 1L);\n    assert(candidate([1L, 9L, 10L, 1L, 3L]) == 1L);\n    assert(candidate([6L, 9L, 7L, 5L, 8L, 7L, 5L, 3L, 7L, 5L, 10L, 10L, 3L, 6L, 10L, 2L, 8L, 6L, 5L, 4L, 9L, 5L, 3L, 10L]) == 5L);\n    assert(candidate([1L]) == 1L);\n    assert(candidate([8L, 8L, 10L, 6L, 4L, 3L, 5L, 8L, 2L, 4L, 2L, 8L, 4L, 6L, 10L, 4L, 2L, 1L, 10L, 2L, 1L, 1L, 5L]) == 4L);\n    assert(candidate([2L, 10L, 4L, 8L, 2L, 10L, 5L, 1L, 2L, 9L, 5L, 5L, 6L, 3L, 8L, 6L, 4L, 10L]) == 2L);\n    assert(candidate([1L, 6L, 10L, 1L, 6L, 9L, 10L, 8L, 6L, 8L, 7L, 3L]) == 1L);\n    assert(candidate([9L, 2L, 4L, 1L, 5L, 1L, 5L, 2L, 5L, 7L, 7L, 7L, 3L, 10L, 1L, 5L, 4L, 2L, 8L, 4L, 1L, 9L, 10L, 7L, 10L, 2L, 8L, 10L, 9L, 4L]) == 4L);\n    assert(candidate([2L, 6L, 4L, 2L, 8L, 7L, 5L, 6L, 4L, 10L, 4L, 6L, 3L, 7L, 8L, 8L, 3L, 1L, 4L, 2L, 2L, 10L, 7L]) == 4L);\n    assert(candidate([9L, 8L, 6L, 10L, 2L, 6L, 10L, 2L, 7L, 8L, 10L, 3L, 8L, 2L, 6L, 2L, 3L, 1L]) == 2L);\n    assert(candidate([5L, 5L, 3L, 9L, 5L, 6L, 3L, 2L, 8L, 5L, 6L, 10L, 10L, 6L, 8L, 4L, 10L, 7L, 7L, 10L, 8L]) == -1L);\n    assert(candidate([10L]) == -1L);\n    assert(candidate([9L, 7L, 7L, 2L, 4L, 7L, 2L, 10L, 9L, 7L, 5L, 7L, 2L]) == 2L);\n    assert(candidate([5L, 4L, 10L, 2L, 1L, 1L, 10L, 3L, 6L, 1L, 8L]) == 1L);\n    assert(candidate([7L, 9L, 9L, 9L, 3L, 4L, 1L, 5L, 9L, 1L, 2L, 1L, 1L, 10L, 7L, 5L, 6L, 7L, 6L, 7L, 7L, 6L]) == 1L);\n    assert(candidate([3L, 10L, 10L, 9L, 2L]) == -1L);\n}\nvoid main(){}"}
{"name": "HumanEval_61_correct_bracketing", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n brackets is a string of \"(\" and \")\".\n    return true if every opening bracket has a corresponding closing bracket.\n\n    >>> correct_bracketing(\"(\")\n    false\n    >>> correct_bracketing(\"()\")\n    true\n    >>> correct_bracketing(\"(()())\")\n    true\n    >>> correct_bracketing(\")(()\")\n    false\n    \n*/\nbool correct_bracketing(string brackets) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = correct_bracketing;\n\n    assert(candidate(\"()\") == true);\n    assert(candidate(\"(()())\") == true);\n    assert(candidate(\"()()(()())()\") == true);\n    assert(candidate(\"()()((()()())())(()()(()))\") == true);\n    assert(candidate(\"((()())))\") == false);\n    assert(candidate(\")(()\") == false);\n    assert(candidate(\"(\") == false);\n    assert(candidate(\"((((\") == false);\n    assert(candidate(\")\") == false);\n    assert(candidate(\"(()\") == false);\n    assert(candidate(\"()()(()())())(()\") == false);\n    assert(candidate(\"()()(()())()))()\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_61_correct_bracketing", "test": "unittest\n{\n    alias candidate = correct_bracketing;\n\n    assert(candidate(\"()\") == true);\n    assert(candidate(\"(()())\") == true);\n    assert(candidate(\"()()(()())()\") == true);\n    assert(candidate(\"()()((()()())())(()()(()))\") == true);\n    assert(candidate(\"((()())))\") == false);\n    assert(candidate(\")(()\") == false);\n    assert(candidate(\"(\") == false);\n    assert(candidate(\"((((\") == false);\n    assert(candidate(\")\") == false);\n    assert(candidate(\"(()\") == false);\n    assert(candidate(\"()()(()())())(()\") == false);\n    assert(candidate(\"()()(()())()))()\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_37_sort_even", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\nThis function takes an array l and returns an array l' such that\n    l' is identical to l in the odd indicies, while its values at the even indicies are equal\n    to the values of the even indicies of l, but sorted.\n    >>> sort_even([1L, 2L, 3L])\n    [1L, 2L, 3L]\n    >>> sort_even([5L, 6L, 3L, 4L])\n    [3L, 6L, 5L, 4L]\n    \n*/\nlong[] sort_even(long[] l) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = sort_even;\n\n    assert(candidate([1L, 2L, 3L]) == [1L, 2L, 3L]);\n    assert(candidate([5L, 3L, -5L, 2L, -3L, 3L, 9L, 0L, 123L, 1L, -10L]) == [-10L, 3L, -5L, 2L, -3L, 3L, 5L, 0L, 9L, 1L, 123L]);\n    assert(candidate([5L, 8L, -12L, 4L, 23L, 2L, 3L, 11L, 12L, -10L]) == [-12L, 8L, 3L, 4L, 5L, 2L, 12L, 11L, 23L, -10L]);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_37_sort_even", "test": "unittest\n{\n    alias candidate = sort_even;\n\n    assert(candidate([1L, 2L, 3L]) == [1L, 2L, 3L]);\n    assert(candidate([5L, 3L, -5L, 2L, -3L, 3L, 9L, 0L, 123L, 1L, -10L]) == [-10L, 3L, -5L, 2L, -3L, 3L, 5L, 0L, 9L, 1L, 123L]);\n    assert(candidate([5L, 8L, -12L, 4L, 23L, 2L, 3L, 11L, 12L, -10L]) == [-12L, 8L, 3L, 4L, 5L, 2L, 12L, 11L, 23L, -10L]);\n}\nvoid main(){}"}
{"name": "HumanEval_54_same_chars", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n\n    Check if two words have the same characters.\n    >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n    true\n    >>> same_chars(\"abcd\", \"dddddddabc\")\n    true\n    >>> same_chars(\"dddddddabc\", \"abcd\")\n    true\n    >>> same_chars(\"eabcd\", \"dddddddabc\")\n    false\n    >>> same_chars(\"abcd\", \"dddddddabce\")\n    false\n    >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n    false\n    \n*/\nbool same_chars(string s0, string s1) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = same_chars;\n\n    assert(candidate(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\") == true);\n    assert(candidate(\"abcd\", \"dddddddabc\") == true);\n    assert(candidate(\"dddddddabc\", \"abcd\") == true);\n    assert(candidate(\"eabcd\", \"dddddddabc\") == false);\n    assert(candidate(\"abcd\", \"dddddddabcf\") == false);\n    assert(candidate(\"eabcdzzzz\", \"dddzzzzzzzddddabc\") == false);\n    assert(candidate(\"aabb\", \"aaccc\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_54_same_chars", "test": "unittest\n{\n    alias candidate = same_chars;\n\n    assert(candidate(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\") == true);\n    assert(candidate(\"abcd\", \"dddddddabc\") == true);\n    assert(candidate(\"dddddddabc\", \"abcd\") == true);\n    assert(candidate(\"eabcd\", \"dddddddabc\") == false);\n    assert(candidate(\"abcd\", \"dddddddabcf\") == false);\n    assert(candidate(\"eabcdzzzz\", \"dddzzzzzzzddddabc\") == false);\n    assert(candidate(\"aabb\", \"aaccc\") == false);\n}\nvoid main(){}"}
{"name": "HumanEval_56_correct_bracketing", "language": "d", "prompt": "import std.typecons;\nimport std.math;\n/*\n brackets is a string of \"<\" and \">\".\n    return true if every opening bracket has a corresponding closing bracket.\n\n    >>> correct_bracketing(\"<\")\n    false\n    >>> correct_bracketing(\"<>\")\n    true\n    >>> correct_bracketing(\"<<><>>\")\n    true\n    >>> correct_bracketing(\"><<>\")\n    false\n    \n*/\nbool correct_bracketing(string brackets) \n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "unittest\n{\n    alias candidate = correct_bracketing;\n\n    assert(candidate(\"<>\") == true);\n    assert(candidate(\"<<><>>\") == true);\n    assert(candidate(\"<><><<><>><>\") == true);\n    assert(candidate(\"<><><<<><><>><>><<><><<>>>\") == true);\n    assert(candidate(\"<<<><>>>>\") == false);\n    assert(candidate(\"><<>\") == false);\n    assert(candidate(\"<\") == false);\n    assert(candidate(\"<<<<\") == false);\n    assert(candidate(\">\") == false);\n    assert(candidate(\"<<>\") == false);\n    assert(candidate(\"<><><<><>><>><<>\") == false);\n    assert(candidate(\"<><><<><>><>>><>\") == false);\n}\nvoid main(){}", "stop_tokens": ["\n\n", "\nvoid", "\nbool", "\nint"], "task_id": "HumanEval_56_correct_bracketing", "test": "unittest\n{\n    alias candidate = correct_bracketing;\n\n    assert(candidate(\"<>\") == true);\n    assert(candidate(\"<<><>>\") == true);\n    assert(candidate(\"<><><<><>><>\") == true);\n    assert(candidate(\"<><><<<><><>><>><<><><<>>>\") == true);\n    assert(candidate(\"<<<><>>>>\") == false);\n    assert(candidate(\"><<>\") == false);\n    assert(candidate(\"<\") == false);\n    assert(candidate(\"<<<<\") == false);\n    assert(candidate(\">\") == false);\n    assert(candidate(\"<<>\") == false);\n    assert(candidate(\"<><><<><>><>><<>\") == false);\n    assert(candidate(\"<><><<><>><>>><>\") == false);\n}\nvoid main(){}"}
