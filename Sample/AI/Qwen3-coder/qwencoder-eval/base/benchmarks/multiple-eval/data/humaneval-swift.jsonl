{"name": "HumanEval_23_strlen", "language": "swift", "prompt": "\n/// Return length of given string\n/// >>> strlen(string: \"\")\n/// 0\n/// >>> strlen(string: \"abc\")\n/// 3\nfunc strlen(string: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(strlen(string: \"\") == 0)\nassert(strlen(string: \"x\") == 1)\nassert(strlen(string: \"asdasnakj\") == 9)", "stop_tokens": ["\n}"], "task_id": "HumanEval_23_strlen", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(strlen(string: \"\") == 0)\nassert(strlen(string: \"x\") == 1)\nassert(strlen(string: \"asdasnakj\") == 9)"}
{"name": "HumanEval_89_encrypt", "language": "swift", "prompt": "\n/// Create a function encrypt that takes a string as an argument and\n/// returns a string encrypted with the alphabet being rotated. \n/// The alphabet should be rotated in a manner such that the letters \n/// shift down by two multiplied to two places.\n/// For example:\n/// >>> encrypt(s: \"hi\")\n/// \"lm\"\n/// >>> encrypt(s: \"asdfghjkl\")\n/// \"ewhjklnop\"\n/// >>> encrypt(s: \"gf\")\n/// \"kj\"\n/// >>> encrypt(s: \"et\")\n/// \"ix\"\nfunc encrypt(s: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(encrypt(s: \"hi\") == \"lm\")\nassert(encrypt(s: \"asdfghjkl\") == \"ewhjklnop\")\nassert(encrypt(s: \"gf\") == \"kj\")\nassert(encrypt(s: \"et\") == \"ix\")\nassert(encrypt(s: \"faewfawefaewg\") == \"jeiajeaijeiak\")\nassert(encrypt(s: \"hellomyfriend\") == \"lippsqcjvmirh\")\nassert(encrypt(s: \"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") == \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\")\nassert(encrypt(s: \"a\") == \"e\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_89_encrypt", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(encrypt(s: \"hi\") == \"lm\")\nassert(encrypt(s: \"asdfghjkl\") == \"ewhjklnop\")\nassert(encrypt(s: \"gf\") == \"kj\")\nassert(encrypt(s: \"et\") == \"ix\")\nassert(encrypt(s: \"faewfawefaewg\") == \"jeiajeaijeiak\")\nassert(encrypt(s: \"hellomyfriend\") == \"lippsqcjvmirh\")\nassert(encrypt(s: \"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") == \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\")\nassert(encrypt(s: \"a\") == \"e\")"}
{"name": "HumanEval_85_add", "language": "swift", "prompt": "\n/// Given a non-empty array of integers lst. add the even elements that are at odd indices..\n/// Examples:\n/// >>> add(lst: [4, 2, 6, 7])\n/// 2\nfunc add(lst: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(add(lst: [4, 88]) == 88)\nassert(add(lst: [4, 5, 6, 7, 2, 122]) == 122)\nassert(add(lst: [4, 0, 6, 7]) == 0)\nassert(add(lst: [4, 4, 6, 8]) == 12)", "stop_tokens": ["\n}"], "task_id": "HumanEval_85_add", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(add(lst: [4, 88]) == 88)\nassert(add(lst: [4, 5, 6, 7, 2, 122]) == 122)\nassert(add(lst: [4, 0, 6, 7]) == 0)\nassert(add(lst: [4, 4, 6, 8]) == 12)"}
{"name": "HumanEval_140_fix_spaces", "language": "swift", "prompt": "\n/// Given a string text, replace all spaces in it with underscores, \n/// and if a string has more than 2 consecutive spaces, \n/// then replace all consecutive spaces with - \n/// >>> fix_spaces(text: \" Example\")\n/// \"Example\"\n/// >>> fix_spaces(text: \" Example 1\")\n/// \"Example_1\"\n/// >>> fix_spaces(text: \" Example 2\")\n/// \"_Example_2\"\n/// >>> fix_spaces(text: \" Example 3\")\n/// \"_Example-3\"\nfunc fix_spaces(text: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fix_spaces(text: \"Example\") == \"Example\")\nassert(fix_spaces(text: \"Mudasir Hanif \") == \"Mudasir_Hanif_\")\nassert(fix_spaces(text: \"Yellow Yellow  Dirty  Fellow\") == \"Yellow_Yellow__Dirty__Fellow\")\nassert(fix_spaces(text: \"Exa   mple\") == \"Exa-mple\")\nassert(fix_spaces(text: \"   Exa 1 2 2 mple\") == \"-Exa_1_2_2_mple\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_140_fix_spaces", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fix_spaces(text: \"Example\") == \"Example\")\nassert(fix_spaces(text: \"Mudasir Hanif \") == \"Mudasir_Hanif_\")\nassert(fix_spaces(text: \"Yellow Yellow  Dirty  Fellow\") == \"Yellow_Yellow__Dirty__Fellow\")\nassert(fix_spaces(text: \"Exa   mple\") == \"Exa-mple\")\nassert(fix_spaces(text: \"   Exa 1 2 2 mple\") == \"-Exa_1_2_2_mple\")"}
{"name": "HumanEval_63_fibfib", "language": "swift", "prompt": "\n/// The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n/// fibfib(0) == 0\n/// fibfib(1) == 0\n/// fibfib(2) == 1\n/// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n/// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n/// >>> fibfib(n: 1)\n/// 0\n/// >>> fibfib(n: 5)\n/// 4\n/// >>> fibfib(n: 8)\n/// 24\nfunc fibfib(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fibfib(n: 2) == 1)\nassert(fibfib(n: 1) == 0)\nassert(fibfib(n: 5) == 4)\nassert(fibfib(n: 8) == 24)\nassert(fibfib(n: 10) == 81)\nassert(fibfib(n: 12) == 274)\nassert(fibfib(n: 14) == 927)", "stop_tokens": ["\n}"], "task_id": "HumanEval_63_fibfib", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fibfib(n: 2) == 1)\nassert(fibfib(n: 1) == 0)\nassert(fibfib(n: 5) == 4)\nassert(fibfib(n: 8) == 24)\nassert(fibfib(n: 10) == 81)\nassert(fibfib(n: 12) == 274)\nassert(fibfib(n: 14) == 927)"}
{"name": "HumanEval_151_double_the_difference", "language": "swift", "prompt": "\n/// Given an array of numbers, return the sum of squares of the numbers\n/// in the array that are odd. Ignore numbers that are negative or not integers.\n/// >>> double_the_difference(lst: [1, 3, 2, 0])\n/// 10\n/// >>> double_the_difference(lst: [-1, -2, 0])\n/// 0\n/// >>> double_the_difference(lst: [9, -2])\n/// 81\n/// >>> double_the_difference(lst: [0])\n/// 0\n/// If the input array is empty, return 0.\nfunc double_the_difference(lst: [Double]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(double_the_difference(lst: [] as [Double]) == 0)\nassert(double_the_difference(lst: [5.0, 4.0]) == 25)\nassert(double_the_difference(lst: [0.1, 0.2, 0.3]) == 0)\nassert(double_the_difference(lst: [-10.0, -20.0, -30.0]) == 0)\nassert(double_the_difference(lst: [-1.0, -2.0, 8.0]) == 0)\nassert(double_the_difference(lst: [0.2, 3.0, 5.0]) == 34)\nassert(double_the_difference(lst: [-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]) == 165)", "stop_tokens": ["\n}"], "task_id": "HumanEval_151_double_the_difference", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(double_the_difference(lst: [] as [Double]) == 0)\nassert(double_the_difference(lst: [5.0, 4.0]) == 25)\nassert(double_the_difference(lst: [0.1, 0.2, 0.3]) == 0)\nassert(double_the_difference(lst: [-10.0, -20.0, -30.0]) == 0)\nassert(double_the_difference(lst: [-1.0, -2.0, 8.0]) == 0)\nassert(double_the_difference(lst: [0.2, 3.0, 5.0]) == 34)\nassert(double_the_difference(lst: [-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]) == 165)"}
{"name": "HumanEval_22_filter_integers", "language": "swift", "prompt": "\n/// Filter given array of any swiftthon values only for integers\n/// >>> filter_integers(values: [\"a\", 3.14, 5])\n/// [5]\n/// >>> filter_integers(values: [1, 2, 3, \"abc\", [:] as [AnyHashable : AnyHashable], [] as [AnyHashable]])\n/// [1, 2, 3]\nfunc filter_integers(values: [AnyHashable]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(filter_integers(values: [] as [AnyHashable]) == [] as [Int])\nassert(filter_integers(values: [4, [:] as [AnyHashable : AnyHashable], [] as [AnyHashable], 23.2, 9, \"adasd\"]) == [4, 9])\nassert(filter_integers(values: [3, \"c\", 3, 3, \"a\", \"b\"]) == [3, 3, 3])", "stop_tokens": ["\n}"], "task_id": "HumanEval_22_filter_integers", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(filter_integers(values: [] as [AnyHashable]) == [] as [Int])\nassert(filter_integers(values: [4, [:] as [AnyHashable : AnyHashable], [] as [AnyHashable], 23.2, 9, \"adasd\"]) == [4, 9])\nassert(filter_integers(values: [3, \"c\", 3, 3, \"a\", \"b\"]) == [3, 3, 3])"}
{"name": "HumanEval_41_car_race_collision", "language": "swift", "prompt": "\n/// Imagine a road that's a perfectly straight infinitely long line.\n/// n cars are driving left to right;  simultaneously, a different set of n cars\n/// are driving right to left.   The two sets of cars start out being very far from\n/// each other.  All cars move in the same speed.  Two cars are said to collide\n/// when a car that's moving left to right hits a car that's moving right to left.\n/// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n/// in their trajectory as if they did not collide.\n/// This function outputs the number of such collisions.\nfunc car_race_collision(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(car_race_collision(n: 2) == 4)\nassert(car_race_collision(n: 3) == 9)\nassert(car_race_collision(n: 4) == 16)\nassert(car_race_collision(n: 8) == 64)\nassert(car_race_collision(n: 10) == 100)", "stop_tokens": ["\n}"], "task_id": "HumanEval_41_car_race_collision", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(car_race_collision(n: 2) == 4)\nassert(car_race_collision(n: 3) == 9)\nassert(car_race_collision(n: 4) == 16)\nassert(car_race_collision(n: 8) == 64)\nassert(car_race_collision(n: 10) == 100)"}
{"name": "HumanEval_17_parse_music", "language": "swift", "prompt": "\n/// Input to this function is a string representing musical notes in a special ASCII format.\n/// Your task is to parse this string and return array of integers corresponding to how many beats does each\n/// not last.\n/// Here is a legend:\n/// 'o' - whole note, lasts four beats\n/// 'o|' - half note, lasts two beats\n/// '.|' - quater note, lasts one beat\n/// >>> parse_music(music_string: \"o o| .| o| o| .| .| .| .| o o\")\n/// [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfunc parse_music(music_string: String) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(parse_music(music_string: \"\") == [] as [Int])\nassert(parse_music(music_string: \"o o o o\") == [4, 4, 4, 4])\nassert(parse_music(music_string: \".| .| .| .|\") == [1, 1, 1, 1])\nassert(parse_music(music_string: \"o| o| .| .| o o o o\") == [2, 2, 1, 1, 4, 4, 4, 4])\nassert(parse_music(music_string: \"o| .| o| .| o o| o o|\") == [2, 1, 2, 1, 4, 2, 4, 2])", "stop_tokens": ["\n}"], "task_id": "HumanEval_17_parse_music", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(parse_music(music_string: \"\") == [] as [Int])\nassert(parse_music(music_string: \"o o o o\") == [4, 4, 4, 4])\nassert(parse_music(music_string: \".| .| .| .|\") == [1, 1, 1, 1])\nassert(parse_music(music_string: \"o| o| .| .| o o o o\") == [2, 2, 1, 1, 4, 4, 4, 4])\nassert(parse_music(music_string: \"o| .| o| .| o o| o o|\") == [2, 1, 2, 1, 4, 2, 4, 2])"}
{"name": "HumanEval_79_decimal_to_binary", "language": "swift", "prompt": "\n/// You will be given a number in decimal form and your task is to convert it to\n/// binary format. The function should return a string, with each character representing a binary\n/// number. Each character in the string will be '0' or '1'.\n/// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n/// The extra characters are there to help with the format.\n/// Examples:\n/// >>> decimal_to_binary(decimal: 15)\n/// \"db1111db\"\n/// >>> decimal_to_binary(decimal: 32)\n/// \"db100000db\"\nfunc decimal_to_binary(decimal: Int) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(decimal_to_binary(decimal: 0) == \"db0db\")\nassert(decimal_to_binary(decimal: 32) == \"db100000db\")\nassert(decimal_to_binary(decimal: 103) == \"db1100111db\")\nassert(decimal_to_binary(decimal: 15) == \"db1111db\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_79_decimal_to_binary", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(decimal_to_binary(decimal: 0) == \"db0db\")\nassert(decimal_to_binary(decimal: 32) == \"db100000db\")\nassert(decimal_to_binary(decimal: 103) == \"db1100111db\")\nassert(decimal_to_binary(decimal: 15) == \"db1111db\")"}
{"name": "HumanEval_14_all_prefixes", "language": "swift", "prompt": "\n/// Return array of all prefixes from shortest to longest of the input string\n/// >>> all_prefixes(string: \"abc\")\n/// [\"a\", \"ab\", \"abc\"]\nfunc all_prefixes(string: String) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(all_prefixes(string: \"\") == [] as [String])\nassert(all_prefixes(string: \"asdfgh\") == [\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"])\nassert(all_prefixes(string: \"WWW\") == [\"W\", \"WW\", \"WWW\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_14_all_prefixes", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(all_prefixes(string: \"\") == [] as [String])\nassert(all_prefixes(string: \"asdfgh\") == [\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"])\nassert(all_prefixes(string: \"WWW\") == [\"W\", \"WW\", \"WWW\"])"}
{"name": "HumanEval_53_add", "language": "swift", "prompt": "\n/// Add two numbers x and y\n/// >>> add(x: 2, y: 3)\n/// 5\n/// >>> add(x: 5, y: 7)\n/// 12\nfunc add(x: Int, y: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(add(x: 0, y: 1) == 1)\nassert(add(x: 1, y: 0) == 1)\nassert(add(x: 2, y: 3) == 5)\nassert(add(x: 5, y: 7) == 12)\nassert(add(x: 7, y: 5) == 12)", "stop_tokens": ["\n}"], "task_id": "HumanEval_53_add", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(add(x: 0, y: 1) == 1)\nassert(add(x: 1, y: 0) == 1)\nassert(add(x: 2, y: 3) == 5)\nassert(add(x: 5, y: 7) == 12)\nassert(add(x: 7, y: 5) == 12)"}
{"name": "HumanEval_159_eat", "language": "swift", "prompt": "\n/// You're a hungry rabbit, and you already have eaten a certain number of carrots,\n/// but now you need to eat more carrots to complete the day's meals.\n/// you should return an array of [ total number of eaten carrots after your meals,\n/// the number of carrots left after your meals ]\n/// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n/// Example:\n/// >>> eat(number: 5, need: 6, remaining: 10)\n/// [11, 4]\n/// >>> eat(number: 4, need: 8, remaining: 9)\n/// [12, 1]\n/// >>> eat(number: 1, need: 10, remaining: 10)\n/// [11, 0]\n/// >>> eat(number: 2, need: 11, remaining: 5)\n/// [7, 0]\n/// Variables:\n/// @number : integer\n/// the number of carrots that you have eaten.\n/// @need : integer\n/// the number of carrots that you need to eat.\n/// @remaining : integer\n/// the number of remaining carrots thet exist in stock\n/// Constrain:\n/// * 0 <= number <= 1000\n/// * 0 <= need <= 1000\n/// * 0 <= remaining <= 1000\n/// Have fun :)\nfunc eat(number: Int, need: Int, remaining: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(eat(number: 5, need: 6, remaining: 10) == [11, 4])\nassert(eat(number: 4, need: 8, remaining: 9) == [12, 1])\nassert(eat(number: 1, need: 10, remaining: 10) == [11, 0])\nassert(eat(number: 2, need: 11, remaining: 5) == [7, 0])\nassert(eat(number: 4, need: 5, remaining: 7) == [9, 2])\nassert(eat(number: 4, need: 5, remaining: 1) == [5, 0])", "stop_tokens": ["\n}"], "task_id": "HumanEval_159_eat", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(eat(number: 5, need: 6, remaining: 10) == [11, 4])\nassert(eat(number: 4, need: 8, remaining: 9) == [12, 1])\nassert(eat(number: 1, need: 10, remaining: 10) == [11, 0])\nassert(eat(number: 2, need: 11, remaining: 5) == [7, 0])\nassert(eat(number: 4, need: 5, remaining: 7) == [9, 2])\nassert(eat(number: 4, need: 5, remaining: 1) == [5, 0])"}
{"name": "HumanEval_115_max_fill", "language": "swift", "prompt": "\n/// You are given a rectangular grid of wells. Each row represents a single well,\n/// and each 1 in a row represents a single unit of water.\n/// Each well has a corresponding bucket that can be used to extract water from it, \n/// and all buckets have the same capacity.\n/// Your task is to use the buckets to empty the wells.\n/// Output the number of times you need to lower the buckets.\n/// Example 1:\n/// >>> max_fill(grid: [[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], capacity: 1)\n/// 6\n/// Example 2:\n/// >>> max_fill(grid: [[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], capacity: 2)\n/// 5\n/// Example 3:\n/// >>> max_fill(grid: [[0, 0, 0], [0, 0, 0]], capacity: 5)\n/// 0\n/// Constraints:\n/// * all wells have the same length\n/// * 1 <= grid.length <= 10^2\n/// * 1 <= grid[:,1].length <= 10^2\n/// * grid[i][j] -> 0 | 1\n/// * 1 <= capacity <= 10\nfunc max_fill(grid: [[Int]], capacity: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(max_fill(grid: [[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], capacity: 1) == 6)\nassert(max_fill(grid: [[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], capacity: 2) == 5)\nassert(max_fill(grid: [[0, 0, 0], [0, 0, 0]], capacity: 5) == 0)\nassert(max_fill(grid: [[1, 1, 1, 1], [1, 1, 1, 1]], capacity: 2) == 4)\nassert(max_fill(grid: [[1, 1, 1, 1], [1, 1, 1, 1]], capacity: 9) == 2)", "stop_tokens": ["\n}"], "task_id": "HumanEval_115_max_fill", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(max_fill(grid: [[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], capacity: 1) == 6)\nassert(max_fill(grid: [[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], capacity: 2) == 5)\nassert(max_fill(grid: [[0, 0, 0], [0, 0, 0]], capacity: 5) == 0)\nassert(max_fill(grid: [[1, 1, 1, 1], [1, 1, 1, 1]], capacity: 2) == 4)\nassert(max_fill(grid: [[1, 1, 1, 1], [1, 1, 1, 1]], capacity: 9) == 2)"}
{"name": "HumanEval_160_do_algebra", "language": "swift", "prompt": "\n/// Given two arrays operator, and operand. The first array has basic algebra operations, and \n/// the second array is an array of integers. Use the two given arrays to build the algebric \n/// expression and return the evaluation of this expression.\n/// The basic algebra operations:\n/// Addition ( + ) \n/// Subtraction ( - ) \n/// Multiplication ( * ) \n/// Floor division ( // ) \n/// Exponentiation ( ** ) \n/// Example:\n/// operator['+', '*', '-']\n/// array = [2, 3, 4, 5]\n/// result = 2 + 3 * 4 - 5\n/// => result = 9\n/// Note:\n/// The length of operator array is equal to the length of operand array minus one.\n/// Operand is an array of of non-negative integers.\n/// Operator array has at least one operator, and operand array has at least two operands.\nfunc do_algebra(operator: [String], operand: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(do_algebra(operator: [\"**\", \"*\", \"+\"], operand: [2, 3, 4, 5]) == 37)\nassert(do_algebra(operator: [\"+\", \"*\", \"-\"], operand: [2, 3, 4, 5]) == 9)\nassert(do_algebra(operator: [\"//\", \"*\"], operand: [7, 3, 4]) == 8)", "stop_tokens": ["\n}"], "task_id": "HumanEval_160_do_algebra", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(do_algebra(operator: [\"**\", \"*\", \"+\"], operand: [2, 3, 4, 5]) == 37)\nassert(do_algebra(operator: [\"+\", \"*\", \"-\"], operand: [2, 3, 4, 5]) == 9)\nassert(do_algebra(operator: [\"//\", \"*\"], operand: [7, 3, 4]) == 8)"}
{"name": "HumanEval_27_flip_case", "language": "swift", "prompt": "\n/// For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n/// >>> flip_case(string: \"Hello\")\n/// \"hELLO\"\nfunc flip_case(string: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(flip_case(string: \"\") == \"\")\nassert(flip_case(string: \"Hello!\") == \"hELLO!\")\nassert(flip_case(string: \"These violent delights have violent ends\") == \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_27_flip_case", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(flip_case(string: \"\") == \"\")\nassert(flip_case(string: \"Hello!\") == \"hELLO!\")\nassert(flip_case(string: \"These violent delights have violent ends\") == \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\")"}
{"name": "HumanEval_105_by_length", "language": "swift", "prompt": "\n/// Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n/// reverse the resulting array, and then replace each digit by its corresponding name from\n/// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n/// For example:\n/// >>> by_length(arr: [2, 1, 1, 4, 5, 8, 2, 3])\n/// [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n/// If the array is empty, return an empty array:\n/// >>> by_length(arr: [] as [Int])\n/// [] as [String]\n/// If the array has any strange number ignore it:\n/// >>> by_length(arr: [1, -1, 55])\n/// [\"One\"]\nfunc by_length(arr: [Int]) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(by_length(arr: [2, 1, 1, 4, 5, 8, 2, 3]) == [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"])\nassert(by_length(arr: [] as [Int]) == [] as [String])\nassert(by_length(arr: [1, -1, 55]) == [\"One\"])\nassert(by_length(arr: [1, -1, 3, 2]) == [\"Three\", \"Two\", \"One\"])\nassert(by_length(arr: [9, 4, 8]) == [\"Nine\", \"Eight\", \"Four\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_105_by_length", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(by_length(arr: [2, 1, 1, 4, 5, 8, 2, 3]) == [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"])\nassert(by_length(arr: [] as [Int]) == [] as [String])\nassert(by_length(arr: [1, -1, 55]) == [\"One\"])\nassert(by_length(arr: [1, -1, 3, 2]) == [\"Three\", \"Two\", \"One\"])\nassert(by_length(arr: [9, 4, 8]) == [\"Nine\", \"Eight\", \"Four\"])"}
{"name": "HumanEval_25_factorize", "language": "swift", "prompt": "\n/// Return array of prime factors of given integer in the order from smallest to largest.\n/// Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n/// Input number should be equal to the product of all factors\n/// >>> factorize(n: 8)\n/// [2, 2, 2]\n/// >>> factorize(n: 25)\n/// [5, 5]\n/// >>> factorize(n: 70)\n/// [2, 5, 7]\nfunc factorize(n: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(factorize(n: 2) == [2])\nassert(factorize(n: 4) == [2, 2])\nassert(factorize(n: 8) == [2, 2, 2])\nassert(factorize(n: 57) == [3, 19])\nassert(factorize(n: 3249) == [3, 3, 19, 19])\nassert(factorize(n: 185193) == [3, 3, 3, 19, 19, 19])\nassert(factorize(n: 20577) == [3, 19, 19, 19])\nassert(factorize(n: 18) == [2, 3, 3])", "stop_tokens": ["\n}"], "task_id": "HumanEval_25_factorize", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(factorize(n: 2) == [2])\nassert(factorize(n: 4) == [2, 2])\nassert(factorize(n: 8) == [2, 2, 2])\nassert(factorize(n: 57) == [3, 19])\nassert(factorize(n: 3249) == [3, 3, 19, 19])\nassert(factorize(n: 185193) == [3, 3, 3, 19, 19, 19])\nassert(factorize(n: 20577) == [3, 19, 19, 19])\nassert(factorize(n: 18) == [2, 3, 3])"}
{"name": "HumanEval_96_count_up_to", "language": "swift", "prompt": "\n/// Implement a function that takes an non-negative integer and returns an array of the first n\n/// integers that are prime numbers and less than n.\n/// for example:\n/// >>> count_up_to(n: 5)\n/// [2, 3]\n/// >>> count_up_to(n: 11)\n/// [2, 3, 5, 7]\n/// >>> count_up_to(n: 0)\n/// [] as [Int]\n/// >>> count_up_to(n: 20)\n/// [2, 3, 5, 7, 11, 13, 17, 19]\n/// >>> count_up_to(n: 1)\n/// [] as [Int]\n/// >>> count_up_to(n: 18)\n/// [2, 3, 5, 7, 11, 13, 17]\nfunc count_up_to(n: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_up_to(n: 5) == [2, 3])\nassert(count_up_to(n: 6) == [2, 3, 5])\nassert(count_up_to(n: 7) == [2, 3, 5])\nassert(count_up_to(n: 10) == [2, 3, 5, 7])\nassert(count_up_to(n: 0) == [] as [Int])\nassert(count_up_to(n: 22) == [2, 3, 5, 7, 11, 13, 17, 19])\nassert(count_up_to(n: 1) == [] as [Int])\nassert(count_up_to(n: 18) == [2, 3, 5, 7, 11, 13, 17])\nassert(count_up_to(n: 47) == [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43])\nassert(count_up_to(n: 101) == [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97])", "stop_tokens": ["\n}"], "task_id": "HumanEval_96_count_up_to", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_up_to(n: 5) == [2, 3])\nassert(count_up_to(n: 6) == [2, 3, 5])\nassert(count_up_to(n: 7) == [2, 3, 5])\nassert(count_up_to(n: 10) == [2, 3, 5, 7])\nassert(count_up_to(n: 0) == [] as [Int])\nassert(count_up_to(n: 22) == [2, 3, 5, 7, 11, 13, 17, 19])\nassert(count_up_to(n: 1) == [] as [Int])\nassert(count_up_to(n: 18) == [2, 3, 5, 7, 11, 13, 17])\nassert(count_up_to(n: 47) == [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43])\nassert(count_up_to(n: 101) == [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97])"}
{"name": "HumanEval_34_unique", "language": "swift", "prompt": "\n/// Return sorted unique elements in an array\n/// >>> unique(l: [5, 3, 5, 2, 3, 3, 9, 0, 123])\n/// [0, 2, 3, 5, 9, 123]\nfunc unique(l: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(unique(l: [5, 3, 5, 2, 3, 3, 9, 0, 123]) == [0, 2, 3, 5, 9, 123])", "stop_tokens": ["\n}"], "task_id": "HumanEval_34_unique", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(unique(l: [5, 3, 5, 2, 3, 3, 9, 0, 123]) == [0, 2, 3, 5, 9, 123])"}
{"name": "HumanEval_74_total_match", "language": "swift", "prompt": "\n/// Write a function that accepts two arrays of strings and returns the array that has \n/// total number of chars in the all strings of the array less than the other array.\n/// if the two arrays have the same number of chars, return the first array.\n/// Examples\n/// >>> total_match(lst1: [] as [String], lst2: [] as [String])\n/// [] as [String]\n/// >>> total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"Hi\"])\n/// [\"hI\", \"Hi\"]\n/// >>> total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hi\", \"hi\", \"admin\", \"project\"])\n/// [\"hi\", \"admin\"]\n/// >>> total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"hi\", \"hi\"])\n/// [\"hI\", \"hi\", \"hi\"]\n/// >>> total_match(lst1: [\"4\"], lst2: [\"1\", \"2\", \"3\", \"4\", \"5\"])\n/// [\"4\"]\nfunc total_match(lst1: [String], lst2: [String]) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(total_match(lst1: [] as [String], lst2: [] as [String]) == [] as [String])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hi\", \"hi\"]) == [\"hi\", \"hi\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hi\", \"hi\", \"admin\", \"project\"]) == [\"hi\", \"admin\"])\nassert(total_match(lst1: [\"4\"], lst2: [\"1\", \"2\", \"3\", \"4\", \"5\"]) == [\"4\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"Hi\"]) == [\"hI\", \"Hi\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"hi\", \"hi\"]) == [\"hI\", \"hi\", \"hi\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"hi\", \"hii\"]) == [\"hi\", \"admin\"])\nassert(total_match(lst1: [] as [String], lst2: [\"this\"]) == [] as [String])\nassert(total_match(lst1: [\"this\"], lst2: [] as [String]) == [] as [String])", "stop_tokens": ["\n}"], "task_id": "HumanEval_74_total_match", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(total_match(lst1: [] as [String], lst2: [] as [String]) == [] as [String])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hi\", \"hi\"]) == [\"hi\", \"hi\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hi\", \"hi\", \"admin\", \"project\"]) == [\"hi\", \"admin\"])\nassert(total_match(lst1: [\"4\"], lst2: [\"1\", \"2\", \"3\", \"4\", \"5\"]) == [\"4\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"Hi\"]) == [\"hI\", \"Hi\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"hi\", \"hi\"]) == [\"hI\", \"hi\", \"hi\"])\nassert(total_match(lst1: [\"hi\", \"admin\"], lst2: [\"hI\", \"hi\", \"hii\"]) == [\"hi\", \"admin\"])\nassert(total_match(lst1: [] as [String], lst2: [\"this\"]) == [] as [String])\nassert(total_match(lst1: [\"this\"], lst2: [] as [String]) == [] as [String])"}
{"name": "HumanEval_35_max_element", "language": "swift", "prompt": "\n/// Return maximum element in the array.\n/// >>> max_element(l: [1, 2, 3])\n/// 3\n/// >>> max_element(l: [5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n/// 123\nfunc max_element(l: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(max_element(l: [1, 2, 3]) == 3)\nassert(max_element(l: [5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10]) == 124)", "stop_tokens": ["\n}"], "task_id": "HumanEval_35_max_element", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(max_element(l: [1, 2, 3]) == 3)\nassert(max_element(l: [5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10]) == 124)"}
{"name": "HumanEval_132_is_nested", "language": "swift", "prompt": "\n/// Create a function that takes a string as input which contains only square brackets.\n/// The function should return true if and only if there is a valid subsequence of brackets \n/// where at least one bracket in the subsequence is nested.\n/// >>> is_nested(string: \"[[]]\")\n/// true\n/// >>> is_nested(string: \"[]]]]]]][[[[[]\")\n/// false\n/// >>> is_nested(string: \"[][]\")\n/// false\n/// >>> is_nested(string: \"[]\")\n/// false\n/// >>> is_nested(string: \"[[][]]\")\n/// true\n/// >>> is_nested(string: \"[[]][[\")\n/// true\nfunc is_nested(string: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_nested(string: \"[[]]\") == true)\nassert(is_nested(string: \"[]]]]]]][[[[[]\") == false)\nassert(is_nested(string: \"[][]\") == false)\nassert(is_nested(string: \"[]\") == false)\nassert(is_nested(string: \"[[[[]]]]\") == true)\nassert(is_nested(string: \"[]]]]]]]]]]\") == false)\nassert(is_nested(string: \"[][][[]]\") == true)\nassert(is_nested(string: \"[[]\") == false)\nassert(is_nested(string: \"[]]\") == false)\nassert(is_nested(string: \"[[]][[\") == true)\nassert(is_nested(string: \"[[][]]\") == true)\nassert(is_nested(string: \"\") == false)\nassert(is_nested(string: \"[[[[[[[[\") == false)\nassert(is_nested(string: \"]]]]]]]]\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_132_is_nested", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_nested(string: \"[[]]\") == true)\nassert(is_nested(string: \"[]]]]]]][[[[[]\") == false)\nassert(is_nested(string: \"[][]\") == false)\nassert(is_nested(string: \"[]\") == false)\nassert(is_nested(string: \"[[[[]]]]\") == true)\nassert(is_nested(string: \"[]]]]]]]]]]\") == false)\nassert(is_nested(string: \"[][][[]]\") == true)\nassert(is_nested(string: \"[[]\") == false)\nassert(is_nested(string: \"[]]\") == false)\nassert(is_nested(string: \"[[]][[\") == true)\nassert(is_nested(string: \"[[][]]\") == true)\nassert(is_nested(string: \"\") == false)\nassert(is_nested(string: \"[[[[[[[[\") == false)\nassert(is_nested(string: \"]]]]]]]]\") == false)"}
{"name": "HumanEval_103_rounded_avg", "language": "swift", "prompt": "\nextension Int: Error {}\n        \n/// You are given two positive integers n and m, and your task is to compute the\n/// average of the integers from n through m (including n and m). \n/// Round the answer to the nearest integer and convert that to binary.\n/// If n is greater than m, return -1.\n/// Example:\n/// >>> rounded_avg(n: 1, m: 5)\n/// .success(\"0b11\")\n/// >>> rounded_avg(n: 7, m: 5)\n/// .failure(-1)\n/// >>> rounded_avg(n: 10, m: 20)\n/// .success(\"0b1111\")\n/// >>> rounded_avg(n: 20, m: 33)\n/// .success(\"0b11010\")\nfunc rounded_avg(n: Int, m: Int) -> Result<String, Int> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_103_rounded_avg.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(rounded_avg(n: 1, m: 5) == .success(\"0b11\"))\nassert(rounded_avg(n: 7, m: 13) == .success(\"0b1010\"))\nassert(rounded_avg(n: 964, m: 977) == .success(\"0b1111001010\"))\nassert(rounded_avg(n: 996, m: 997) == .success(\"0b1111100100\"))\nassert(rounded_avg(n: 560, m: 851) == .success(\"0b1011000010\"))\nassert(rounded_avg(n: 185, m: 546) == .success(\"0b101101110\"))\nassert(rounded_avg(n: 362, m: 496) == .success(\"0b110101101\"))\nassert(rounded_avg(n: 350, m: 902) == .success(\"0b1001110010\"))\nassert(rounded_avg(n: 197, m: 233) == .success(\"0b11010111\"))\nassert(rounded_avg(n: 7, m: 5) == .failure(-1))\nassert(rounded_avg(n: 5, m: 1) == .failure(-1))\nassert(rounded_avg(n: 5, m: 5) == .success(\"0b101\"))", "stop_tokens": ["\n}"], "task_id": "HumanEval_103_rounded_avg", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(rounded_avg(n: 1, m: 5) == .success(\"0b11\"))\nassert(rounded_avg(n: 7, m: 13) == .success(\"0b1010\"))\nassert(rounded_avg(n: 964, m: 977) == .success(\"0b1111001010\"))\nassert(rounded_avg(n: 996, m: 997) == .success(\"0b1111100100\"))\nassert(rounded_avg(n: 560, m: 851) == .success(\"0b1011000010\"))\nassert(rounded_avg(n: 185, m: 546) == .success(\"0b101101110\"))\nassert(rounded_avg(n: 362, m: 496) == .success(\"0b110101101\"))\nassert(rounded_avg(n: 350, m: 902) == .success(\"0b1001110010\"))\nassert(rounded_avg(n: 197, m: 233) == .success(\"0b11010111\"))\nassert(rounded_avg(n: 7, m: 5) == .failure(-1))\nassert(rounded_avg(n: 5, m: 1) == .failure(-1))\nassert(rounded_avg(n: 5, m: 5) == .success(\"0b101\"))"}
{"name": "HumanEval_113_odd_count", "language": "swift", "prompt": "\n/// Given an array of strings, where each string consists of only digits, return an array.\n/// Each element i of the output should be \"the number of odd elements in the\n/// string i of the input.\" where all the i's should be replaced by the number\n/// of odd digits in the i'th string of the input.\n/// >>> odd_count(lst: [\"1234567\"])\n/// [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n/// >>> odd_count(lst: [\"3\", \"11111111\"])\n/// [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\nfunc odd_count(lst: [String]) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(odd_count(lst: [\"1234567\"]) == [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"])\nassert(odd_count(lst: [\"3\", \"11111111\"]) == [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"])\nassert(odd_count(lst: [\"271\", \"137\", \"314\"]) == [\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_113_odd_count", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(odd_count(lst: [\"1234567\"]) == [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"])\nassert(odd_count(lst: [\"3\", \"11111111\"]) == [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"])\nassert(odd_count(lst: [\"271\", \"137\", \"314\"]) == [\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"])"}
{"name": "HumanEval_109_move_one_ball", "language": "swift", "prompt": "\n/// We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n/// numbers in the array will be randomly ordered. Your task is to determine if\n/// it is possible to get an array sorted in non-decreasing order by performing \n/// the following operation on the given array:\n/// You are allowed to perform right shift operation any number of times.\n/// One right shift operation means shifting all elements of the array by one\n/// position in the right direction. The last element of the array will be moved to\n/// the starting position in the array i.e. 0th index. \n/// If it is possible to obtain the sorted array by performing the above operation\n/// then return true else return false.\n/// If the given array is empty then return true.\n/// Note: The given array is guaranteed to have unique elements.\n/// For Example:\n/// >>> move_one_ball(arr: [3, 4, 5, 1, 2])\n/// true\n/// Explanation: By performin 2 right shift operations, non-decreasing order can\n/// be achieved for the given array.\n/// >>> move_one_ball(arr: [3, 5, 4, 1, 2])\n/// false\n/// Explanation:It is not possible to get non-decreasing order for the given\n/// array by performing any number of right shift operations.\nfunc move_one_ball(arr: [Int]) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(move_one_ball(arr: [3, 4, 5, 1, 2]) == true)\nassert(move_one_ball(arr: [3, 5, 10, 1, 2]) == true)\nassert(move_one_ball(arr: [4, 3, 1, 2]) == false)\nassert(move_one_ball(arr: [3, 5, 4, 1, 2]) == false)\nassert(move_one_ball(arr: [] as [Int]) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_109_move_one_ball", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(move_one_ball(arr: [3, 4, 5, 1, 2]) == true)\nassert(move_one_ball(arr: [3, 5, 10, 1, 2]) == true)\nassert(move_one_ball(arr: [4, 3, 1, 2]) == false)\nassert(move_one_ball(arr: [3, 5, 4, 1, 2]) == false)\nassert(move_one_ball(arr: [] as [Int]) == true)"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "swift", "prompt": "\n/// Given a positive integer n, return a tuple that has the number of even and odd\n/// integer palindromes that fall within the range(1, n), inclusive.\n/// Example 1:\n/// >>> even_odd_palindrome(n: 3)\n/// (1, 2)\n/// Explanation:\n/// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n/// Example 2:\n/// >>> even_odd_palindrome(n: 12)\n/// (4, 6)\n/// Explanation:\n/// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n/// Note:\n/// 1. 1 <= n <= 10^3\n/// 2. returned tuple has the number of even and odd integer palindromes respectively.\nfunc even_odd_palindrome(n: Int) -> (Int, Int) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(even_odd_palindrome(n: 123) == (8, 13))\nassert(even_odd_palindrome(n: 12) == (4, 6))\nassert(even_odd_palindrome(n: 3) == (1, 2))\nassert(even_odd_palindrome(n: 63) == (6, 8))\nassert(even_odd_palindrome(n: 25) == (5, 6))\nassert(even_odd_palindrome(n: 19) == (4, 6))\nassert(even_odd_palindrome(n: 9) == (4, 5))\nassert(even_odd_palindrome(n: 1) == (0, 1))", "stop_tokens": ["\n}"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(even_odd_palindrome(n: 123) == (8, 13))\nassert(even_odd_palindrome(n: 12) == (4, 6))\nassert(even_odd_palindrome(n: 3) == (1, 2))\nassert(even_odd_palindrome(n: 63) == (6, 8))\nassert(even_odd_palindrome(n: 25) == (5, 6))\nassert(even_odd_palindrome(n: 19) == (4, 6))\nassert(even_odd_palindrome(n: 9) == (4, 5))\nassert(even_odd_palindrome(n: 1) == (0, 1))"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "swift", "prompt": "\n/// Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n/// Example\n/// >>> is_equal_to_sum_even(n: 4)\n/// false\n/// >>> is_equal_to_sum_even(n: 6)\n/// false\n/// >>> is_equal_to_sum_even(n: 8)\n/// true\nfunc is_equal_to_sum_even(n: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_equal_to_sum_even(n: 4) == false)\nassert(is_equal_to_sum_even(n: 6) == false)\nassert(is_equal_to_sum_even(n: 8) == true)\nassert(is_equal_to_sum_even(n: 10) == true)\nassert(is_equal_to_sum_even(n: 11) == false)\nassert(is_equal_to_sum_even(n: 12) == true)\nassert(is_equal_to_sum_even(n: 13) == false)\nassert(is_equal_to_sum_even(n: 16) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_equal_to_sum_even(n: 4) == false)\nassert(is_equal_to_sum_even(n: 6) == false)\nassert(is_equal_to_sum_even(n: 8) == true)\nassert(is_equal_to_sum_even(n: 10) == true)\nassert(is_equal_to_sum_even(n: 11) == false)\nassert(is_equal_to_sum_even(n: 12) == true)\nassert(is_equal_to_sum_even(n: 13) == false)\nassert(is_equal_to_sum_even(n: 16) == true)"}
{"name": "HumanEval_62_derivative", "language": "swift", "prompt": "\n/// xs represent coefficients of a polynomial.\n/// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n/// Return derivative of this polynomial in the same form.\n/// >>> derivative(xs: [3, 1, 2, 4, 5])\n/// [1, 4, 12, 20]\n/// >>> derivative(xs: [1, 2, 3])\n/// [2, 6]\nfunc derivative(xs: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(derivative(xs: [3, 1, 2, 4, 5]) == [1, 4, 12, 20])\nassert(derivative(xs: [1, 2, 3]) == [2, 6])\nassert(derivative(xs: [3, 2, 1]) == [2, 2])\nassert(derivative(xs: [3, 2, 1, 0, 4]) == [2, 2, 0, 16])\nassert(derivative(xs: [1]) == [] as [Int])", "stop_tokens": ["\n}"], "task_id": "HumanEval_62_derivative", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(derivative(xs: [3, 1, 2, 4, 5]) == [1, 4, 12, 20])\nassert(derivative(xs: [1, 2, 3]) == [2, 6])\nassert(derivative(xs: [3, 2, 1]) == [2, 2])\nassert(derivative(xs: [3, 2, 1, 0, 4]) == [2, 2, 0, 16])\nassert(derivative(xs: [1]) == [] as [Int])"}
{"name": "HumanEval_126_is_sorted", "language": "swift", "prompt": "\n/// Given an array of numbers, return whether or not they are sorted\n/// in ascending order. If array has more than 1 duplicate of the same\n/// number, return false. Assume no negative numbers and only integers.\n/// Examples\n/// >>> is_sorted(lst: [5])\n/// true\n/// >>> is_sorted(lst: [1, 2, 3, 4, 5])\n/// true\n/// >>> is_sorted(lst: [1, 3, 2, 4, 5])\n/// false\n/// >>> is_sorted(lst: [1, 2, 3, 4, 5, 6])\n/// true\n/// >>> is_sorted(lst: [1, 2, 3, 4, 5, 6, 7])\n/// true\n/// >>> is_sorted(lst: [1, 3, 2, 4, 5, 6, 7])\n/// false\n/// >>> is_sorted(lst: [1, 2, 2, 3, 3, 4])\n/// true\n/// >>> is_sorted(lst: [1, 2, 2, 2, 3, 4])\n/// false\nfunc is_sorted(lst: [Int]) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_sorted(lst: [5]) == true)\nassert(is_sorted(lst: [1, 2, 3, 4, 5]) == true)\nassert(is_sorted(lst: [1, 3, 2, 4, 5]) == false)\nassert(is_sorted(lst: [1, 2, 3, 4, 5, 6]) == true)\nassert(is_sorted(lst: [1, 2, 3, 4, 5, 6, 7]) == true)\nassert(is_sorted(lst: [1, 3, 2, 4, 5, 6, 7]) == false)\nassert(is_sorted(lst: [] as [Int]) == true)\nassert(is_sorted(lst: [1]) == true)\nassert(is_sorted(lst: [3, 2, 1]) == false)\nassert(is_sorted(lst: [1, 2, 2, 2, 3, 4]) == false)\nassert(is_sorted(lst: [1, 2, 3, 3, 3, 4]) == false)\nassert(is_sorted(lst: [1, 2, 2, 3, 3, 4]) == true)\nassert(is_sorted(lst: [1, 2, 3, 4]) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_126_is_sorted", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_sorted(lst: [5]) == true)\nassert(is_sorted(lst: [1, 2, 3, 4, 5]) == true)\nassert(is_sorted(lst: [1, 3, 2, 4, 5]) == false)\nassert(is_sorted(lst: [1, 2, 3, 4, 5, 6]) == true)\nassert(is_sorted(lst: [1, 2, 3, 4, 5, 6, 7]) == true)\nassert(is_sorted(lst: [1, 3, 2, 4, 5, 6, 7]) == false)\nassert(is_sorted(lst: [] as [Int]) == true)\nassert(is_sorted(lst: [1]) == true)\nassert(is_sorted(lst: [3, 2, 1]) == false)\nassert(is_sorted(lst: [1, 2, 2, 2, 3, 4]) == false)\nassert(is_sorted(lst: [1, 2, 3, 3, 3, 4]) == false)\nassert(is_sorted(lst: [1, 2, 2, 3, 3, 4]) == true)\nassert(is_sorted(lst: [1, 2, 3, 4]) == true)"}
{"name": "HumanEval_161_solve", "language": "swift", "prompt": "\n/// You are given a string s.\n/// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n/// otherwise keep it as it is.\n/// If the string contains no letters, reverse the string.\n/// The function should return the resulted string.\n/// Examples\n/// >>> solve(s: \"1234\")\n/// \"4321\"\n/// >>> solve(s: \"ab\")\n/// \"AB\"\n/// >>> solve(s: \"#a@C\")\n/// \"#A@c\"\nfunc solve(s: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(solve(s: \"AsDf\") == \"aSdF\")\nassert(solve(s: \"1234\") == \"4321\")\nassert(solve(s: \"ab\") == \"AB\")\nassert(solve(s: \"#a@C\") == \"#A@c\")\nassert(solve(s: \"#AsdfW^45\") == \"#aSDFw^45\")\nassert(solve(s: \"#6@2\") == \"2@6#\")\nassert(solve(s: \"#$a^D\") == \"#$A^d\")\nassert(solve(s: \"#ccc\") == \"#CCC\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_161_solve", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(solve(s: \"AsDf\") == \"aSdF\")\nassert(solve(s: \"1234\") == \"4321\")\nassert(solve(s: \"ab\") == \"AB\")\nassert(solve(s: \"#a@C\") == \"#A@c\")\nassert(solve(s: \"#AsdfW^45\") == \"#aSDFw^45\")\nassert(solve(s: \"#6@2\") == \"2@6#\")\nassert(solve(s: \"#$a^D\") == \"#$A^d\")\nassert(solve(s: \"#ccc\") == \"#CCC\")"}
{"name": "HumanEval_130_tri", "language": "swift", "prompt": "\n/// Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n/// the last couple centuries. However, what people don't know is Tribonacci sequence.\n/// Tribonacci sequence is defined by the recurrence:\n/// tri(1) = 3\n/// tri(n) = 1 + n / 2, if n is even.\n/// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n/// For example:\n/// tri(2) = 1 + (2 / 2) = 2\n/// tri(4) = 3\n/// tri(3) = tri(2) + tri(1) + tri(4)\n/// = 2 + 3 + 3 = 8 \n/// You are given a non-negative integer number n, you have to a return an array of the \n/// first n + 1 numbers of the Tribonacci sequence.\n/// Examples:\n/// >>> tri(n: 3)\n/// [1, 3, 2, 8]\nfunc tri(n: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(tri(n: 3) == [1, 3, 2, 8])\nassert(tri(n: 4) == [1, 3, 2, 8, 3])\nassert(tri(n: 5) == [1, 3, 2, 8, 3, 15])\nassert(tri(n: 6) == [1, 3, 2, 8, 3, 15, 4])\nassert(tri(n: 7) == [1, 3, 2, 8, 3, 15, 4, 24])\nassert(tri(n: 8) == [1, 3, 2, 8, 3, 15, 4, 24, 5])\nassert(tri(n: 9) == [1, 3, 2, 8, 3, 15, 4, 24, 5, 35])\nassert(tri(n: 20) == [1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11])\nassert(tri(n: 0) == [1])\nassert(tri(n: 1) == [1, 3])", "stop_tokens": ["\n}"], "task_id": "HumanEval_130_tri", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(tri(n: 3) == [1, 3, 2, 8])\nassert(tri(n: 4) == [1, 3, 2, 8, 3])\nassert(tri(n: 5) == [1, 3, 2, 8, 3, 15])\nassert(tri(n: 6) == [1, 3, 2, 8, 3, 15, 4])\nassert(tri(n: 7) == [1, 3, 2, 8, 3, 15, 4, 24])\nassert(tri(n: 8) == [1, 3, 2, 8, 3, 15, 4, 24, 5])\nassert(tri(n: 9) == [1, 3, 2, 8, 3, 15, 4, 24, 5, 35])\nassert(tri(n: 20) == [1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11])\nassert(tri(n: 0) == [1])\nassert(tri(n: 1) == [1, 3])"}
{"name": "HumanEval_36_fizz_buzz", "language": "swift", "prompt": "\n/// Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n/// >>> fizz_buzz(n: 50)\n/// 0\n/// >>> fizz_buzz(n: 78)\n/// 2\n/// >>> fizz_buzz(n: 79)\n/// 3\nfunc fizz_buzz(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fizz_buzz(n: 50) == 0)\nassert(fizz_buzz(n: 78) == 2)\nassert(fizz_buzz(n: 79) == 3)\nassert(fizz_buzz(n: 100) == 3)\nassert(fizz_buzz(n: 200) == 6)\nassert(fizz_buzz(n: 4000) == 192)\nassert(fizz_buzz(n: 10000) == 639)\nassert(fizz_buzz(n: 100000) == 8026)", "stop_tokens": ["\n}"], "task_id": "HumanEval_36_fizz_buzz", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fizz_buzz(n: 50) == 0)\nassert(fizz_buzz(n: 78) == 2)\nassert(fizz_buzz(n: 79) == 3)\nassert(fizz_buzz(n: 100) == 3)\nassert(fizz_buzz(n: 200) == 6)\nassert(fizz_buzz(n: 4000) == 192)\nassert(fizz_buzz(n: 10000) == 639)\nassert(fizz_buzz(n: 100000) == 8026)"}
{"name": "HumanEval_29_filter_by_prefix", "language": "swift", "prompt": "\n/// Filter an input array of strings only for ones that start with a given prefix.\n/// >>> filter_by_prefix(strings: [] as [String], prefix: \"a\")\n/// [] as [String]\n/// >>> filter_by_prefix(strings: [\"abc\", \"bcd\", \"cde\", \"array\"], prefix: \"a\")\n/// [\"abc\", \"array\"]\nfunc filter_by_prefix(strings: [String], prefix: String) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(filter_by_prefix(strings: [] as [String], prefix: \"john\") == [] as [String])\nassert(filter_by_prefix(strings: [\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], prefix: \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_29_filter_by_prefix", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(filter_by_prefix(strings: [] as [String], prefix: \"john\") == [] as [String])\nassert(filter_by_prefix(strings: [\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], prefix: \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"])"}
{"name": "HumanEval_84_solve", "language": "swift", "prompt": "\n/// Given a positive integer N, return the total sum of its digits in binary.\n/// Example\n/// >>> solve(N: 1000)\n/// \"1\"\n/// >>> solve(N: 150)\n/// \"110\"\n/// >>> solve(N: 147)\n/// \"1100\"\n/// Variables:\n/// @N integer\n/// Constraints: 0 \u2264 N \u2264 10000.\n/// Output:\n/// a string of binary number\nfunc solve(N: Int) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(solve(N: 1000) == \"1\")\nassert(solve(N: 150) == \"110\")\nassert(solve(N: 147) == \"1100\")\nassert(solve(N: 333) == \"1001\")\nassert(solve(N: 963) == \"10010\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_84_solve", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(solve(N: 1000) == \"1\")\nassert(solve(N: 150) == \"110\")\nassert(solve(N: 147) == \"1100\")\nassert(solve(N: 333) == \"1001\")\nassert(solve(N: 963) == \"10010\")"}
{"name": "HumanEval_129_minPath", "language": "swift", "prompt": "\n/// Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n/// each cell of the grid contains a value. Every integer in the range [1, N * N]\n/// inclusive appears exactly once on the cells of the grid.\n/// You have to find the minimum path of length k in the grid. You can start\n/// from any cell, and in each step you can move to any of the neighbor cells,\n/// in other words, you can go to cells which share an edge with you current\n/// cell.\n/// Please note that a path of length k means visiting exactly k cells (not\n/// necessarily distinct).\n/// You CANNOT go off the grid.\n/// A path A (of length k) is considered less than a path B (of length k) if\n/// after making the ordered arrays of the values on the cells that A and B go\n/// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n/// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n/// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n/// lst_A[j] = lst_B[j].\n/// It is guaranteed that the answer is unique.\n/// Return an ordered array of the values on the cells that the minimum path go through.\n/// Examples:    \n/// >>> minPath(grid: [[1, 2, 3], [4, 5, 6], [7, 8, 9]], k: 3)\n/// [1, 2, 1]\n/// >>> minPath(grid: [[5, 9, 3], [4, 1, 6], [7, 8, 2]], k: 1)\n/// [1]\nfunc minPath(grid: [[Int]], k: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(minPath(grid: [[1, 2, 3], [4, 5, 6], [7, 8, 9]], k: 3) == [1, 2, 1])\nassert(minPath(grid: [[5, 9, 3], [4, 1, 6], [7, 8, 2]], k: 1) == [1])\nassert(minPath(grid: [[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12], [13, 14, 15, 16]], k: 4) == [1, 2, 1, 2])\nassert(minPath(grid: [[6, 4, 13, 10], [5, 7, 12, 1], [3, 16, 11, 15], [8, 14, 9, 2]], k: 7) == [1, 10, 1, 10, 1, 10, 1])\nassert(minPath(grid: [[8, 14, 9, 2], [6, 4, 13, 15], [5, 7, 1, 12], [3, 10, 11, 16]], k: 5) == [1, 7, 1, 7, 1])\nassert(minPath(grid: [[11, 8, 7, 2], [5, 16, 14, 4], [9, 3, 15, 6], [12, 13, 10, 1]], k: 9) == [1, 6, 1, 6, 1, 6, 1, 6, 1])\nassert(minPath(grid: [[12, 13, 10, 1], [9, 3, 15, 6], [5, 16, 14, 4], [11, 8, 7, 2]], k: 12) == [1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6])\nassert(minPath(grid: [[2, 7, 4], [3, 1, 5], [6, 8, 9]], k: 8) == [1, 3, 1, 3, 1, 3, 1, 3])\nassert(minPath(grid: [[6, 1, 5], [3, 8, 9], [2, 7, 4]], k: 8) == [1, 5, 1, 5, 1, 5, 1, 5])\nassert(minPath(grid: [[1, 2], [3, 4]], k: 10) == [1, 2, 1, 2, 1, 2, 1, 2, 1, 2])\nassert(minPath(grid: [[1, 3], [3, 2]], k: 10) == [1, 3, 1, 3, 1, 3, 1, 3, 1, 3])", "stop_tokens": ["\n}"], "task_id": "HumanEval_129_minPath", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(minPath(grid: [[1, 2, 3], [4, 5, 6], [7, 8, 9]], k: 3) == [1, 2, 1])\nassert(minPath(grid: [[5, 9, 3], [4, 1, 6], [7, 8, 2]], k: 1) == [1])\nassert(minPath(grid: [[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12], [13, 14, 15, 16]], k: 4) == [1, 2, 1, 2])\nassert(minPath(grid: [[6, 4, 13, 10], [5, 7, 12, 1], [3, 16, 11, 15], [8, 14, 9, 2]], k: 7) == [1, 10, 1, 10, 1, 10, 1])\nassert(minPath(grid: [[8, 14, 9, 2], [6, 4, 13, 15], [5, 7, 1, 12], [3, 10, 11, 16]], k: 5) == [1, 7, 1, 7, 1])\nassert(minPath(grid: [[11, 8, 7, 2], [5, 16, 14, 4], [9, 3, 15, 6], [12, 13, 10, 1]], k: 9) == [1, 6, 1, 6, 1, 6, 1, 6, 1])\nassert(minPath(grid: [[12, 13, 10, 1], [9, 3, 15, 6], [5, 16, 14, 4], [11, 8, 7, 2]], k: 12) == [1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6])\nassert(minPath(grid: [[2, 7, 4], [3, 1, 5], [6, 8, 9]], k: 8) == [1, 3, 1, 3, 1, 3, 1, 3])\nassert(minPath(grid: [[6, 1, 5], [3, 8, 9], [2, 7, 4]], k: 8) == [1, 5, 1, 5, 1, 5, 1, 5])\nassert(minPath(grid: [[1, 2], [3, 4]], k: 10) == [1, 2, 1, 2, 1, 2, 1, 2, 1, 2])\nassert(minPath(grid: [[1, 3], [3, 2]], k: 10) == [1, 3, 1, 3, 1, 3, 1, 3, 1, 3])"}
{"name": "HumanEval_98_count_upper", "language": "swift", "prompt": "\n/// Given a string s, count the number of uppercase vowels in even indices.\n/// For example:\n/// >>> count_upper(s: \"aBCdEf\")\n/// 1\n/// >>> count_upper(s: \"abcdefg\")\n/// 0\n/// >>> count_upper(s: \"dBBE\")\n/// 0\nfunc count_upper(s: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_upper(s: \"aBCdEf\") == 1)\nassert(count_upper(s: \"abcdefg\") == 0)\nassert(count_upper(s: \"dBBE\") == 0)\nassert(count_upper(s: \"B\") == 0)\nassert(count_upper(s: \"U\") == 1)\nassert(count_upper(s: \"\") == 0)\nassert(count_upper(s: \"EEEE\") == 2)", "stop_tokens": ["\n}"], "task_id": "HumanEval_98_count_upper", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_upper(s: \"aBCdEf\") == 1)\nassert(count_upper(s: \"abcdefg\") == 0)\nassert(count_upper(s: \"dBBE\") == 0)\nassert(count_upper(s: \"B\") == 0)\nassert(count_upper(s: \"U\") == 1)\nassert(count_upper(s: \"\") == 0)\nassert(count_upper(s: \"EEEE\") == 2)"}
{"name": "HumanEval_120_maximum", "language": "swift", "prompt": "\n/// Given an array arr of integers and a positive integer k, return a sorted array \n/// of length k with the maximum k numbers in arr.\n/// Example 1:\n/// >>> maximum(arr: [-3, -4, 5], k: 3)\n/// [-4, -3, 5]\n/// Example 2:\n/// >>> maximum(arr: [4, -4, 4], k: 2)\n/// [4, 4]\n/// Example 3:\n/// >>> maximum(arr: [-3, 2, 1, 2, -1, -2, 1], k: 1)\n/// [2]\n/// Note:\n/// 1. The length of the array will be in the range of [1, 1000].\n/// 2. The elements in the array will be in the range of [-1000, 1000].\n/// 3. 0 <= k <= len(arr)\nfunc maximum(arr: [Int], k: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(maximum(arr: [-3, -4, 5], k: 3) == [-4, -3, 5])\nassert(maximum(arr: [4, -4, 4], k: 2) == [4, 4])\nassert(maximum(arr: [-3, 2, 1, 2, -1, -2, 1], k: 1) == [2])\nassert(maximum(arr: [123, -123, 20, 0, 1, 2, -3], k: 3) == [2, 20, 123])\nassert(maximum(arr: [-123, 20, 0, 1, 2, -3], k: 4) == [0, 1, 2, 20])\nassert(maximum(arr: [5, 15, 0, 3, -13, -8, 0], k: 7) == [-13, -8, 0, 0, 3, 5, 15])\nassert(maximum(arr: [-1, 0, 2, 5, 3, -10], k: 2) == [3, 5])\nassert(maximum(arr: [1, 0, 5, -7], k: 1) == [5])\nassert(maximum(arr: [4, -4], k: 2) == [-4, 4])\nassert(maximum(arr: [-10, 10], k: 2) == [-10, 10])\nassert(maximum(arr: [1, 2, 3, -23, 243, -400, 0], k: 0) == [] as [Int])", "stop_tokens": ["\n}"], "task_id": "HumanEval_120_maximum", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(maximum(arr: [-3, -4, 5], k: 3) == [-4, -3, 5])\nassert(maximum(arr: [4, -4, 4], k: 2) == [4, 4])\nassert(maximum(arr: [-3, 2, 1, 2, -1, -2, 1], k: 1) == [2])\nassert(maximum(arr: [123, -123, 20, 0, 1, 2, -3], k: 3) == [2, 20, 123])\nassert(maximum(arr: [-123, 20, 0, 1, 2, -3], k: 4) == [0, 1, 2, 20])\nassert(maximum(arr: [5, 15, 0, 3, -13, -8, 0], k: 7) == [-13, -8, 0, 0, 3, 5, 15])\nassert(maximum(arr: [-1, 0, 2, 5, 3, -10], k: 2) == [3, 5])\nassert(maximum(arr: [1, 0, 5, -7], k: 1) == [5])\nassert(maximum(arr: [4, -4], k: 2) == [-4, 4])\nassert(maximum(arr: [-10, 10], k: 2) == [-10, 10])\nassert(maximum(arr: [1, 2, 3, -23, 243, -400, 0], k: 0) == [] as [Int])"}
{"name": "HumanEval_24_largest_divisor", "language": "swift", "prompt": "\n/// For a given number n, find the largest number that divides n evenly, smaller than n\n/// >>> largest_divisor(n: 15)\n/// 5\nfunc largest_divisor(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(largest_divisor(n: 3) == 1)\nassert(largest_divisor(n: 7) == 1)\nassert(largest_divisor(n: 10) == 5)\nassert(largest_divisor(n: 100) == 50)\nassert(largest_divisor(n: 49) == 7)", "stop_tokens": ["\n}"], "task_id": "HumanEval_24_largest_divisor", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(largest_divisor(n: 3) == 1)\nassert(largest_divisor(n: 7) == 1)\nassert(largest_divisor(n: 10) == 5)\nassert(largest_divisor(n: 100) == 50)\nassert(largest_divisor(n: 49) == 7)"}
{"name": "HumanEval_88_sort_array", "language": "swift", "prompt": "\n/// Given an array of non-negative integers, return a coswift of the given array after sorting,\n/// you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n/// or sort it in descending order if the sum( first index value, last index value) is even.\n/// Note:\n/// * don't change the given array.\n/// Examples:\n/// >>> sort_array(array: [] as [Int])\n/// [] as [Int]\n/// >>> sort_array(array: [5])\n/// [5]\n/// >>> sort_array(array: [2, 4, 3, 0, 1, 5])\n/// [0, 1, 2, 3, 4, 5]\n/// >>> sort_array(array: [2, 4, 3, 0, 1, 5, 6])\n/// [6, 5, 4, 3, 2, 1, 0]\nfunc sort_array(array: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_array(array: [] as [Int]) == [] as [Int])\nassert(sort_array(array: [5]) == [5])\nassert(sort_array(array: [2, 4, 3, 0, 1, 5]) == [0, 1, 2, 3, 4, 5])\nassert(sort_array(array: [2, 4, 3, 0, 1, 5, 6]) == [6, 5, 4, 3, 2, 1, 0])\nassert(sort_array(array: [2, 1]) == [1, 2])\nassert(sort_array(array: [15, 42, 87, 32, 11, 0]) == [0, 11, 15, 32, 42, 87])\nassert(sort_array(array: [21, 14, 23, 11]) == [23, 21, 14, 11])", "stop_tokens": ["\n}"], "task_id": "HumanEval_88_sort_array", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_array(array: [] as [Int]) == [] as [Int])\nassert(sort_array(array: [5]) == [5])\nassert(sort_array(array: [2, 4, 3, 0, 1, 5]) == [0, 1, 2, 3, 4, 5])\nassert(sort_array(array: [2, 4, 3, 0, 1, 5, 6]) == [6, 5, 4, 3, 2, 1, 0])\nassert(sort_array(array: [2, 1]) == [1, 2])\nassert(sort_array(array: [15, 42, 87, 32, 11, 0]) == [0, 11, 15, 32, 42, 87])\nassert(sort_array(array: [21, 14, 23, 11]) == [23, 21, 14, 11])"}
{"name": "HumanEval_106_f", "language": "swift", "prompt": "\n/// Implement the function f that takes n as a parameter,\n/// and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n/// or the sum of numbers from 1 to i otherwise.\n/// i starts from 1.\n/// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n/// Example:\n/// >>> f(n: 5)\n/// [1, 2, 6, 24, 15]\nfunc f(n: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(f(n: 5) == [1, 2, 6, 24, 15])\nassert(f(n: 7) == [1, 2, 6, 24, 15, 720, 28])\nassert(f(n: 1) == [1])\nassert(f(n: 3) == [1, 2, 6])", "stop_tokens": ["\n}"], "task_id": "HumanEval_106_f", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(f(n: 5) == [1, 2, 6, 24, 15])\nassert(f(n: 7) == [1, 2, 6, 24, 15, 720, 28])\nassert(f(n: 1) == [1])\nassert(f(n: 3) == [1, 2, 6])"}
{"name": "HumanEval_77_iscube", "language": "swift", "prompt": "\n/// Write a function that takes an integer a and returns true \n/// if this ingeger is a cube of some integer number.\n/// Note: you may assume the input is always valid.\n/// Examples:\n/// >>> iscube(a: 1)\n/// true\n/// >>> iscube(a: 2)\n/// false\n/// >>> iscube(a: -1)\n/// true\n/// >>> iscube(a: 64)\n/// true\n/// >>> iscube(a: 0)\n/// true\n/// >>> iscube(a: 180)\n/// false\nfunc iscube(a: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(iscube(a: 1) == true)\nassert(iscube(a: 2) == false)\nassert(iscube(a: -1) == true)\nassert(iscube(a: 64) == true)\nassert(iscube(a: 180) == false)\nassert(iscube(a: 1000) == true)\nassert(iscube(a: 0) == true)\nassert(iscube(a: 1729) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_77_iscube", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(iscube(a: 1) == true)\nassert(iscube(a: 2) == false)\nassert(iscube(a: -1) == true)\nassert(iscube(a: 64) == true)\nassert(iscube(a: 180) == false)\nassert(iscube(a: 1000) == true)\nassert(iscube(a: 0) == true)\nassert(iscube(a: 1729) == false)"}
{"name": "HumanEval_93_encode", "language": "swift", "prompt": "\n/// Write a function that takes a message, and encodes in such a \n/// way that it swaps case of all letters, replaces all vowels in \n/// the message with the letter that appears 2 places ahead of that \n/// vowel in the english alphabet. \n/// Assume only letters. \n/// Examples:\n/// >>> encode(message: \"test\")\n/// \"TGST\"\n/// >>> encode(message: \"This is a message\")\n/// \"tHKS KS C MGSSCGG\"\nfunc encode(message: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(encode(message: \"TEST\") == \"tgst\")\nassert(encode(message: \"Mudasir\") == \"mWDCSKR\")\nassert(encode(message: \"YES\") == \"ygs\")\nassert(encode(message: \"This is a message\") == \"tHKS KS C MGSSCGG\")\nassert(encode(message: \"I DoNt KnOw WhAt tO WrItE\") == \"k dQnT kNqW wHcT Tq wRkTg\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_93_encode", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(encode(message: \"TEST\") == \"tgst\")\nassert(encode(message: \"Mudasir\") == \"mWDCSKR\")\nassert(encode(message: \"YES\") == \"ygs\")\nassert(encode(message: \"This is a message\") == \"tHKS KS C MGSSCGG\")\nassert(encode(message: \"I DoNt KnOw WhAt tO WrItE\") == \"k dQnT kNqW wHcT Tq wRkTg\")"}
{"name": "HumanEval_91_is_bored", "language": "swift", "prompt": "\n/// You'll be given a string of words, and your task is to count the number\n/// of boredoms. A boredom is a sentence that starts with the word \"I\".\n/// Sentences are delimited by '.', '?' or '!'.\n/// For example:\n/// >>> is_bored(S: \"Hello world\")\n/// 0\n/// >>> is_bored(S: \"The sky is blue. The sun is shining. I love this weather\")\n/// 1\nfunc is_bored(S: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_bored(S: \"Hello world\") == 0)\nassert(is_bored(S: \"Is the sky blue?\") == 0)\nassert(is_bored(S: \"I love It !\") == 1)\nassert(is_bored(S: \"bIt\") == 0)\nassert(is_bored(S: \"I feel good today. I will be productive. will kill It\") == 2)\nassert(is_bored(S: \"You and I are going for a walk\") == 0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_91_is_bored", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_bored(S: \"Hello world\") == 0)\nassert(is_bored(S: \"Is the sky blue?\") == 0)\nassert(is_bored(S: \"I love It !\") == 1)\nassert(is_bored(S: \"bIt\") == 0)\nassert(is_bored(S: \"I feel good today. I will be productive. will kill It\") == 2)\nassert(is_bored(S: \"You and I are going for a walk\") == 0)"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "swift", "prompt": "\n/// pairs_sum_to_zero takes an array of integers as an input.\n/// it returns true if there are two distinct elements in the array that\n/// sum to zero, and false otherwise.\n/// >>> pairs_sum_to_zero(l: [1, 3, 5, 0])\n/// false\n/// >>> pairs_sum_to_zero(l: [1, 3, -2, 1])\n/// false\n/// >>> pairs_sum_to_zero(l: [1, 2, 3, 7])\n/// false\n/// >>> pairs_sum_to_zero(l: [2, 4, -5, 3, 5, 7])\n/// true\n/// >>> pairs_sum_to_zero(l: [1])\n/// false\nfunc pairs_sum_to_zero(l: [Int]) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(pairs_sum_to_zero(l: [1, 3, 5, 0]) == false)\nassert(pairs_sum_to_zero(l: [1, 3, -2, 1]) == false)\nassert(pairs_sum_to_zero(l: [1, 2, 3, 7]) == false)\nassert(pairs_sum_to_zero(l: [2, 4, -5, 3, 5, 7]) == true)\nassert(pairs_sum_to_zero(l: [1]) == false)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 3, 2, 30]) == true)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 3, 2, 31]) == true)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 4, 2, 30]) == false)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 4, 2, 31]) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(pairs_sum_to_zero(l: [1, 3, 5, 0]) == false)\nassert(pairs_sum_to_zero(l: [1, 3, -2, 1]) == false)\nassert(pairs_sum_to_zero(l: [1, 2, 3, 7]) == false)\nassert(pairs_sum_to_zero(l: [2, 4, -5, 3, 5, 7]) == true)\nassert(pairs_sum_to_zero(l: [1]) == false)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 3, 2, 30]) == true)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 3, 2, 31]) == true)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 4, 2, 30]) == false)\nassert(pairs_sum_to_zero(l: [-3, 9, -1, 4, 2, 31]) == false)"}
{"name": "HumanEval_71_triangle_area", "language": "swift", "prompt": "\n/// Given the lengths of the three sides of a triangle. Return the area of\n/// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n/// Otherwise return -1\n/// Three sides make a valid triangle when the sum of any two sides is greater \n/// than the third side.\n/// Example:\n/// >>> triangle_area(a: 3, b: 4, c: 5)\n/// 6.0\n/// >>> triangle_area(a: 1, b: 2, c: 10)\n/// -1\nfunc triangle_area(a: Int, b: Int, c: Int) -> Double {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(triangle_area(a: 3, b: 4, c: 5) == 6.0)\nassert(triangle_area(a: 1, b: 2, c: 10) == -1)\nassert(triangle_area(a: 4, b: 8, c: 5) == 8.18)\nassert(triangle_area(a: 2, b: 2, c: 2) == 1.73)\nassert(triangle_area(a: 1, b: 2, c: 3) == -1)\nassert(triangle_area(a: 10, b: 5, c: 7) == 16.25)\nassert(triangle_area(a: 2, b: 6, c: 3) == -1)\nassert(triangle_area(a: 1, b: 1, c: 1) == 0.43)\nassert(triangle_area(a: 2, b: 2, c: 10) == -1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_71_triangle_area", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(triangle_area(a: 3, b: 4, c: 5) == 6.0)\nassert(triangle_area(a: 1, b: 2, c: 10) == -1)\nassert(triangle_area(a: 4, b: 8, c: 5) == 8.18)\nassert(triangle_area(a: 2, b: 2, c: 2) == 1.73)\nassert(triangle_area(a: 1, b: 2, c: 3) == -1)\nassert(triangle_area(a: 10, b: 5, c: 7) == 16.25)\nassert(triangle_area(a: 2, b: 6, c: 3) == -1)\nassert(triangle_area(a: 1, b: 1, c: 1) == 0.43)\nassert(triangle_area(a: 2, b: 2, c: 10) == -1)"}
{"name": "HumanEval_131_digits", "language": "swift", "prompt": "\n/// Given a positive integer n, return the product of the odd digits.\n/// Return 0 if all digits are even.\n/// For example:\n/// >>> digits(n: 1)\n/// 1\n/// >>> digits(n: 4)\n/// 0\n/// >>> digits(n: 235)\n/// 15\nfunc digits(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(digits(n: 5) == 5)\nassert(digits(n: 54) == 5)\nassert(digits(n: 120) == 1)\nassert(digits(n: 5014) == 5)\nassert(digits(n: 98765) == 315)\nassert(digits(n: 5576543) == 2625)\nassert(digits(n: 2468) == 0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_131_digits", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(digits(n: 5) == 5)\nassert(digits(n: 54) == 5)\nassert(digits(n: 120) == 1)\nassert(digits(n: 5014) == 5)\nassert(digits(n: 98765) == 315)\nassert(digits(n: 5576543) == 2625)\nassert(digits(n: 2468) == 0)"}
{"name": "HumanEval_101_words_string", "language": "swift", "prompt": "\n/// You will be given a string of words separated by commas or spaces. Your task is\n/// to split the string into words and return an array of the words.\n/// For example:\n/// >>> words_string(s: \"Hi, my name is John\")\n/// [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n/// >>> words_string(s: \"One, two, three, four, five, six\")\n/// [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\nfunc words_string(s: String) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(words_string(s: \"Hi, my name is John\") == [\"Hi\", \"my\", \"name\", \"is\", \"John\"])\nassert(words_string(s: \"One, two, three, four, five, six\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"])\nassert(words_string(s: \"Hi, my name\") == [\"Hi\", \"my\", \"name\"])\nassert(words_string(s: \"One,, two, three, four, five, six,\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"])\nassert(words_string(s: \"\") == [] as [String])\nassert(words_string(s: \"ahmed     , gamal\") == [\"ahmed\", \"gamal\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_101_words_string", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(words_string(s: \"Hi, my name is John\") == [\"Hi\", \"my\", \"name\", \"is\", \"John\"])\nassert(words_string(s: \"One, two, three, four, five, six\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"])\nassert(words_string(s: \"Hi, my name\") == [\"Hi\", \"my\", \"name\"])\nassert(words_string(s: \"One,, two, three, four, five, six,\") == [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"])\nassert(words_string(s: \"\") == [] as [String])\nassert(words_string(s: \"ahmed     , gamal\") == [\"ahmed\", \"gamal\"])"}
{"name": "HumanEval_18_how_many_times", "language": "swift", "prompt": "\n/// Find how many times a given substring can be found in the original string. Count overlaping cases.\n/// >>> how_many_times(string: \"\", substring: \"a\")\n/// 0\n/// >>> how_many_times(string: \"aaa\", substring: \"a\")\n/// 3\n/// >>> how_many_times(string: \"aaaa\", substring: \"aa\")\n/// 3\nfunc how_many_times(string: String, substring: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(how_many_times(string: \"\", substring: \"x\") == 0)\nassert(how_many_times(string: \"xyxyxyx\", substring: \"x\") == 4)\nassert(how_many_times(string: \"cacacacac\", substring: \"cac\") == 4)\nassert(how_many_times(string: \"john doe\", substring: \"john\") == 1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_18_how_many_times", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(how_many_times(string: \"\", substring: \"x\") == 0)\nassert(how_many_times(string: \"xyxyxyx\", substring: \"x\") == 4)\nassert(how_many_times(string: \"cacacacac\", substring: \"cac\") == 4)\nassert(how_many_times(string: \"john doe\", substring: \"john\") == 1)"}
{"name": "HumanEval_137_compare_one", "language": "swift", "prompt": "\nenum Value: Equatable, Hashable {\n    case intValue(Int)\n    case doubleValue(Double)\n    case stringValue(String)\n}\n\n            \n/// Create a function that takes integers, floats, or strings representing\n/// real numbers, and returns the larger variable in its given variable type.\n/// Return nil if the values are equal.\n/// Note: If a real number is represented as a string, the floating point might be . or ,\n/// >>> compare_one(a: .intValue(1), b: .doubleValue(2.5))\n/// .doubleValue(2.5)\n/// >>> compare_one(a: .intValue(1), b: .stringValue(\"2,3\"))\n/// .stringValue(\"2,3\")\n/// >>> compare_one(a: .stringValue(\"5,1\"), b: .stringValue(\"6\"))\n/// .stringValue(\"6\")\n/// >>> compare_one(a: .stringValue(\"1\"), b: .intValue(1))\n/// nil\nfunc compare_one(a: Value, b: Value) -> Value? {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_137_compare_one.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(compare_one(a: .intValue(1), b: .intValue(2)) == .intValue(2))\nassert(compare_one(a: .intValue(1), b: .doubleValue(2.5)) == .doubleValue(2.5))\nassert(compare_one(a: .intValue(2), b: .intValue(3)) == .intValue(3))\nassert(compare_one(a: .intValue(5), b: .intValue(6)) == .intValue(6))\nassert(compare_one(a: .intValue(1), b: .stringValue(\"2,3\")) == .stringValue(\"2,3\"))\nassert(compare_one(a: .stringValue(\"5,1\"), b: .stringValue(\"6\")) == .stringValue(\"6\"))\nassert(compare_one(a: .stringValue(\"1\"), b: .stringValue(\"2\")) == .stringValue(\"2\"))\nassert(compare_one(a: .stringValue(\"1\"), b: .intValue(1)) == nil)", "stop_tokens": ["\n}"], "task_id": "HumanEval_137_compare_one", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(compare_one(a: .intValue(1), b: .intValue(2)) == .intValue(2))\nassert(compare_one(a: .intValue(1), b: .doubleValue(2.5)) == .doubleValue(2.5))\nassert(compare_one(a: .intValue(2), b: .intValue(3)) == .intValue(3))\nassert(compare_one(a: .intValue(5), b: .intValue(6)) == .intValue(6))\nassert(compare_one(a: .intValue(1), b: .stringValue(\"2,3\")) == .stringValue(\"2,3\"))\nassert(compare_one(a: .stringValue(\"5,1\"), b: .stringValue(\"6\")) == .stringValue(\"6\"))\nassert(compare_one(a: .stringValue(\"1\"), b: .stringValue(\"2\")) == .stringValue(\"2\"))\nassert(compare_one(a: .stringValue(\"1\"), b: .intValue(1)) == nil)"}
{"name": "HumanEval_51_remove_vowels", "language": "swift", "prompt": "\n/// remove_vowels is a function that takes string and returns string without vowels.\n/// >>> remove_vowels(text: \"\")\n/// \"\"\n/// >>> remove_vowels(text: \"abcdef\")\n/// \"bcdf\"\n/// >>> remove_vowels(text: \"aaaaa\")\n/// \"\"\n/// >>> remove_vowels(text: \"aaBAA\")\n/// \"B\"\n/// >>> remove_vowels(text: \"zbcd\")\n/// \"zbcd\"\nfunc remove_vowels(text: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(remove_vowels(text: \"\") == \"\")\nassert(remove_vowels(text: \"abcdef\\nghijklm\") == \"bcdf\\nghjklm\")\nassert(remove_vowels(text: \"fedcba\") == \"fdcb\")\nassert(remove_vowels(text: \"eeeee\") == \"\")\nassert(remove_vowels(text: \"acBAA\") == \"cB\")\nassert(remove_vowels(text: \"EcBOO\") == \"cB\")\nassert(remove_vowels(text: \"ybcd\") == \"ybcd\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_51_remove_vowels", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(remove_vowels(text: \"\") == \"\")\nassert(remove_vowels(text: \"abcdef\\nghijklm\") == \"bcdf\\nghjklm\")\nassert(remove_vowels(text: \"fedcba\") == \"fdcb\")\nassert(remove_vowels(text: \"eeeee\") == \"\")\nassert(remove_vowels(text: \"acBAA\") == \"cB\")\nassert(remove_vowels(text: \"EcBOO\") == \"cB\")\nassert(remove_vowels(text: \"ybcd\") == \"ybcd\")"}
{"name": "HumanEval_70_strange_sort_list", "language": "swift", "prompt": "\n/// Given array of integers, return array in strange order.\n/// Strange sorting, is when you start with the minimum value,\n/// then maximum of the remaining integers, then minimum and so on.\n/// Examples:\n/// >>> strange_sort_list(lst: [1, 2, 3, 4])\n/// [1, 4, 2, 3]\n/// >>> strange_sort_list(lst: [5, 5, 5, 5])\n/// [5, 5, 5, 5]\n/// >>> strange_sort_list(lst: [] as [Int])\n/// [] as [Int]\nfunc strange_sort_list(lst: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(strange_sort_list(lst: [1, 2, 3, 4]) == [1, 4, 2, 3])\nassert(strange_sort_list(lst: [5, 6, 7, 8, 9]) == [5, 9, 6, 8, 7])\nassert(strange_sort_list(lst: [1, 2, 3, 4, 5]) == [1, 5, 2, 4, 3])\nassert(strange_sort_list(lst: [5, 6, 7, 8, 9, 1]) == [1, 9, 5, 8, 6, 7])\nassert(strange_sort_list(lst: [5, 5, 5, 5]) == [5, 5, 5, 5])\nassert(strange_sort_list(lst: [] as [Int]) == [] as [Int])\nassert(strange_sort_list(lst: [1, 2, 3, 4, 5, 6, 7, 8]) == [1, 8, 2, 7, 3, 6, 4, 5])\nassert(strange_sort_list(lst: [0, 2, 2, 2, 5, 5, -5, -5]) == [-5, 5, -5, 5, 0, 2, 2, 2])\nassert(strange_sort_list(lst: [111111]) == [111111])", "stop_tokens": ["\n}"], "task_id": "HumanEval_70_strange_sort_list", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(strange_sort_list(lst: [1, 2, 3, 4]) == [1, 4, 2, 3])\nassert(strange_sort_list(lst: [5, 6, 7, 8, 9]) == [5, 9, 6, 8, 7])\nassert(strange_sort_list(lst: [1, 2, 3, 4, 5]) == [1, 5, 2, 4, 3])\nassert(strange_sort_list(lst: [5, 6, 7, 8, 9, 1]) == [1, 9, 5, 8, 6, 7])\nassert(strange_sort_list(lst: [5, 5, 5, 5]) == [5, 5, 5, 5])\nassert(strange_sort_list(lst: [] as [Int]) == [] as [Int])\nassert(strange_sort_list(lst: [1, 2, 3, 4, 5, 6, 7, 8]) == [1, 8, 2, 7, 3, 6, 4, 5])\nassert(strange_sort_list(lst: [0, 2, 2, 2, 5, 5, -5, -5]) == [-5, 5, -5, 5, 0, 2, 2, 2])\nassert(strange_sort_list(lst: [111111]) == [111111])"}
{"name": "HumanEval_20_find_closest_elements", "language": "swift", "prompt": "\n/// From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n/// other and return them in order (smaller number, larger number).\n/// >>> find_closest_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n/// (2.0, 2.2)\n/// >>> find_closest_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n/// (2.0, 2.0)\nfunc find_closest_elements(numbers: [Double]) -> (Double, Double) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(find_closest_elements(numbers: [1.0, 2.0, 3.9, 4.0, 5.0, 2.2]) == (3.9, 4.0))\nassert(find_closest_elements(numbers: [1.0, 2.0, 5.9, 4.0, 5.0]) == (5.0, 5.9))\nassert(find_closest_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.2]) == (2.0, 2.2))\nassert(find_closest_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.0]) == (2.0, 2.0))\nassert(find_closest_elements(numbers: [1.1, 2.2, 3.1, 4.1, 5.1]) == (2.2, 3.1))", "stop_tokens": ["\n}"], "task_id": "HumanEval_20_find_closest_elements", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(find_closest_elements(numbers: [1.0, 2.0, 3.9, 4.0, 5.0, 2.2]) == (3.9, 4.0))\nassert(find_closest_elements(numbers: [1.0, 2.0, 5.9, 4.0, 5.0]) == (5.0, 5.9))\nassert(find_closest_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.2]) == (2.0, 2.2))\nassert(find_closest_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.0]) == (2.0, 2.0))\nassert(find_closest_elements(numbers: [1.1, 2.2, 3.1, 4.1, 5.1]) == (2.2, 3.1))"}
{"name": "HumanEval_76_is_simple_power", "language": "swift", "prompt": "\n/// Your task is to write a function that returns true if a number x is a simple\n/// power of n and false in other cases.\n/// x is a simple power of n if n**int=x\n/// For example:\n/// >>> is_simple_power(x: 1, n: 4)\n/// true\n/// >>> is_simple_power(x: 2, n: 2)\n/// true\n/// >>> is_simple_power(x: 8, n: 2)\n/// true\n/// >>> is_simple_power(x: 3, n: 2)\n/// false\n/// >>> is_simple_power(x: 3, n: 1)\n/// false\n/// >>> is_simple_power(x: 5, n: 3)\n/// false\nfunc is_simple_power(x: Int, n: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_simple_power(x: 16, n: 2) == true)\nassert(is_simple_power(x: 143214, n: 16) == false)\nassert(is_simple_power(x: 4, n: 2) == true)\nassert(is_simple_power(x: 9, n: 3) == true)\nassert(is_simple_power(x: 16, n: 4) == true)\nassert(is_simple_power(x: 24, n: 2) == false)\nassert(is_simple_power(x: 128, n: 4) == false)\nassert(is_simple_power(x: 12, n: 6) == false)\nassert(is_simple_power(x: 1, n: 1) == true)\nassert(is_simple_power(x: 1, n: 12) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_76_is_simple_power", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_simple_power(x: 16, n: 2) == true)\nassert(is_simple_power(x: 143214, n: 16) == false)\nassert(is_simple_power(x: 4, n: 2) == true)\nassert(is_simple_power(x: 9, n: 3) == true)\nassert(is_simple_power(x: 16, n: 4) == true)\nassert(is_simple_power(x: 24, n: 2) == false)\nassert(is_simple_power(x: 128, n: 4) == false)\nassert(is_simple_power(x: 12, n: 6) == false)\nassert(is_simple_power(x: 1, n: 1) == true)\nassert(is_simple_power(x: 1, n: 12) == true)"}
{"name": "HumanEval_39_prime_fib", "language": "swift", "prompt": "\n/// prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n/// >>> prime_fib(n: 1)\n/// 2\n/// >>> prime_fib(n: 2)\n/// 3\n/// >>> prime_fib(n: 3)\n/// 5\n/// >>> prime_fib(n: 4)\n/// 13\n/// >>> prime_fib(n: 5)\n/// 89\nfunc prime_fib(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(prime_fib(n: 1) == 2)\nassert(prime_fib(n: 2) == 3)\nassert(prime_fib(n: 3) == 5)\nassert(prime_fib(n: 4) == 13)\nassert(prime_fib(n: 5) == 89)\nassert(prime_fib(n: 6) == 233)\nassert(prime_fib(n: 7) == 1597)\nassert(prime_fib(n: 8) == 28657)\nassert(prime_fib(n: 9) == 514229)\nassert(prime_fib(n: 10) == 433494437)", "stop_tokens": ["\n}"], "task_id": "HumanEval_39_prime_fib", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(prime_fib(n: 1) == 2)\nassert(prime_fib(n: 2) == 3)\nassert(prime_fib(n: 3) == 5)\nassert(prime_fib(n: 4) == 13)\nassert(prime_fib(n: 5) == 89)\nassert(prime_fib(n: 6) == 233)\nassert(prime_fib(n: 7) == 1597)\nassert(prime_fib(n: 8) == 28657)\nassert(prime_fib(n: 9) == 514229)\nassert(prime_fib(n: 10) == 433494437)"}
{"name": "HumanEval_145_order_by_points", "language": "swift", "prompt": "\n/// Write a function which sorts the given array of integers\n/// in ascending order according to the sum of their digits.\n/// Note: if there are several items with similar sum of their digits,\n/// order them based on their index in original array.\n/// For example:\n/// >>> order_by_points(nums: [1, 11, -1, -11, -12])\n/// [-1, -11, 1, -12, 11]\n/// >>> order_by_points(nums: [] as [Int])\n/// [] as [Int]\nfunc order_by_points(nums: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(order_by_points(nums: [1, 11, -1, -11, -12]) == [-1, -11, 1, -12, 11])\nassert(order_by_points(nums: [1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46]) == [0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457])\nassert(order_by_points(nums: [] as [Int]) == [] as [Int])\nassert(order_by_points(nums: [1, -11, -32, 43, 54, -98, 2, -3]) == [-3, -32, -98, -11, 1, 2, 43, 54])\nassert(order_by_points(nums: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]) == [1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9])\nassert(order_by_points(nums: [0, 6, 6, -76, -21, 23, 4]) == [-76, -21, 0, 4, 23, 6, 6])", "stop_tokens": ["\n}"], "task_id": "HumanEval_145_order_by_points", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(order_by_points(nums: [1, 11, -1, -11, -12]) == [-1, -11, 1, -12, 11])\nassert(order_by_points(nums: [1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46]) == [0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457])\nassert(order_by_points(nums: [] as [Int]) == [] as [Int])\nassert(order_by_points(nums: [1, -11, -32, 43, 54, -98, 2, -3]) == [-3, -32, -98, -11, 1, 2, 43, 54])\nassert(order_by_points(nums: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]) == [1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9])\nassert(order_by_points(nums: [0, 6, 6, -76, -21, 23, 4]) == [-76, -21, 0, 4, 23, 6, 6])"}
{"name": "HumanEval_0_has_close_elements", "language": "swift", "prompt": "\n/// Check if in given array of numbers, are any two numbers closer to each other than\n/// given threshold.\n/// >>> has_close_elements(numbers: [1.0, 2.0, 3.0], threshold: 0.5)\n/// false\n/// >>> has_close_elements(numbers: [1.0, 2.8, 3.0, 4.0, 5.0, 2.0], threshold: 0.3)\n/// true\nfunc has_close_elements(numbers: [Double], threshold: Double) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(has_close_elements(numbers: [1.0, 2.0, 3.9, 4.0, 5.0, 2.2], threshold: 0.3) == true)\nassert(has_close_elements(numbers: [1.0, 2.0, 3.9, 4.0, 5.0, 2.2], threshold: 0.05) == false)\nassert(has_close_elements(numbers: [1.0, 2.0, 5.9, 4.0, 5.0], threshold: 0.95) == true)\nassert(has_close_elements(numbers: [1.0, 2.0, 5.9, 4.0, 5.0], threshold: 0.8) == false)\nassert(has_close_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.0], threshold: 0.1) == true)\nassert(has_close_elements(numbers: [1.1, 2.2, 3.1, 4.1, 5.1], threshold: 1.0) == true)\nassert(has_close_elements(numbers: [1.1, 2.2, 3.1, 4.1, 5.1], threshold: 0.5) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_0_has_close_elements", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(has_close_elements(numbers: [1.0, 2.0, 3.9, 4.0, 5.0, 2.2], threshold: 0.3) == true)\nassert(has_close_elements(numbers: [1.0, 2.0, 3.9, 4.0, 5.0, 2.2], threshold: 0.05) == false)\nassert(has_close_elements(numbers: [1.0, 2.0, 5.9, 4.0, 5.0], threshold: 0.95) == true)\nassert(has_close_elements(numbers: [1.0, 2.0, 5.9, 4.0, 5.0], threshold: 0.8) == false)\nassert(has_close_elements(numbers: [1.0, 2.0, 3.0, 4.0, 5.0, 2.0], threshold: 0.1) == true)\nassert(has_close_elements(numbers: [1.1, 2.2, 3.1, 4.1, 5.1], threshold: 1.0) == true)\nassert(has_close_elements(numbers: [1.1, 2.2, 3.1, 4.1, 5.1], threshold: 0.5) == false)"}
{"name": "HumanEval_10_make_palindrome", "language": "swift", "prompt": "\n/// Find the shortest palindrome that begins with a supplied string.\n/// Algorithm idea is simple:\n/// - Find the longest postfix of supplied string that is a palindrome.\n/// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n/// >>> make_palindrome(string: \"\")\n/// \"\"\n/// >>> make_palindrome(string: \"cat\")\n/// \"catac\"\n/// >>> make_palindrome(string: \"cata\")\n/// \"catac\"\nfunc make_palindrome(string: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(make_palindrome(string: \"\") == \"\")\nassert(make_palindrome(string: \"x\") == \"x\")\nassert(make_palindrome(string: \"xyz\") == \"xyzyx\")\nassert(make_palindrome(string: \"xyx\") == \"xyx\")\nassert(make_palindrome(string: \"jerry\") == \"jerryrrej\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_10_make_palindrome", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(make_palindrome(string: \"\") == \"\")\nassert(make_palindrome(string: \"x\") == \"x\")\nassert(make_palindrome(string: \"xyz\") == \"xyzyx\")\nassert(make_palindrome(string: \"xyx\") == \"xyx\")\nassert(make_palindrome(string: \"jerry\") == \"jerryrrej\")"}
{"name": "HumanEval_11_string_xor", "language": "swift", "prompt": "\n/// Input are two strings a and b consisting only of 1s and 0s.\n/// Perform binary XOR on these inputs and return result also as a string.\n/// >>> string_xor(a: \"010\", b: \"110\")\n/// \"100\"\nfunc string_xor(a: String, b: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(string_xor(a: \"111000\", b: \"101010\") == \"010010\")\nassert(string_xor(a: \"1\", b: \"1\") == \"0\")\nassert(string_xor(a: \"0101\", b: \"0000\") == \"0101\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_11_string_xor", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(string_xor(a: \"111000\", b: \"101010\") == \"010010\")\nassert(string_xor(a: \"1\", b: \"1\") == \"0\")\nassert(string_xor(a: \"0101\", b: \"0000\") == \"0101\")"}
{"name": "HumanEval_139_special_factorial", "language": "swift", "prompt": "\n/// The Brazilian factorial is defined as:\n/// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n/// where n > 0\n/// For example:\n/// >>> special_factorial(n: 4)\n/// 288\n/// The function will receive an integer as input and should return the special\n/// factorial of this integer.\nfunc special_factorial(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(special_factorial(n: 4) == 288)\nassert(special_factorial(n: 5) == 34560)\nassert(special_factorial(n: 7) == 125411328000)\nassert(special_factorial(n: 1) == 1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_139_special_factorial", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(special_factorial(n: 4) == 288)\nassert(special_factorial(n: 5) == 34560)\nassert(special_factorial(n: 7) == 125411328000)\nassert(special_factorial(n: 1) == 1)"}
{"name": "HumanEval_122_add_elements", "language": "swift", "prompt": "\n/// Given a non-empty array of integers arr and an integer k, return\n/// the sum of the elements with at most two digits from the first k elements of arr.\n/// Example:\n/// >>> add_elements(arr: [111, 21, 3, 4000, 5, 6, 7, 8, 9], k: 4)\n/// 24\n/// Constraints:\n/// 1. 1 <= len(arr) <= 100\n/// 2. 1 <= k <= len(arr)\nfunc add_elements(arr: [Int], k: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(add_elements(arr: [1, -2, -3, 41, 57, 76, 87, 88, 99], k: 3) == -4)\nassert(add_elements(arr: [111, 121, 3, 4000, 5, 6], k: 2) == 0)\nassert(add_elements(arr: [11, 21, 3, 90, 5, 6, 7, 8, 9], k: 4) == 125)\nassert(add_elements(arr: [111, 21, 3, 4000, 5, 6, 7, 8, 9], k: 4) == 24)\nassert(add_elements(arr: [1], k: 1) == 1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_122_add_elements", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(add_elements(arr: [1, -2, -3, 41, 57, 76, 87, 88, 99], k: 3) == -4)\nassert(add_elements(arr: [111, 121, 3, 4000, 5, 6], k: 2) == 0)\nassert(add_elements(arr: [11, 21, 3, 90, 5, 6, 7, 8, 9], k: 4) == 125)\nassert(add_elements(arr: [111, 21, 3, 4000, 5, 6, 7, 8, 9], k: 4) == 24)\nassert(add_elements(arr: [1], k: 1) == 1)"}
{"name": "HumanEval_46_fib4", "language": "swift", "prompt": "\n/// The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n/// fib4(0) -> 0\n/// fib4(1) -> 0\n/// fib4(2) -> 2\n/// fib4(3) -> 0\n/// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n/// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n/// >>> fib4(n: 5)\n/// 4\n/// >>> fib4(n: 6)\n/// 8\n/// >>> fib4(n: 7)\n/// 14\nfunc fib4(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fib4(n: 5) == 4)\nassert(fib4(n: 8) == 28)\nassert(fib4(n: 10) == 104)\nassert(fib4(n: 12) == 386)", "stop_tokens": ["\n}"], "task_id": "HumanEval_46_fib4", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fib4(n: 5) == 4)\nassert(fib4(n: 8) == 28)\nassert(fib4(n: 10) == 104)\nassert(fib4(n: 12) == 386)"}
{"name": "HumanEval_104_unique_digits", "language": "swift", "prompt": "\n/// Given an array of positive integers x. return a sorted array of all \n/// elements that hasn't any even digit.\n/// Note: Returned array should be sorted in increasing order.\n/// For example:\n/// >>> unique_digits(x: [15, 33, 1422, 1])\n/// [1, 15, 33]\n/// >>> unique_digits(x: [152, 323, 1422, 10])\n/// [] as [Int]\nfunc unique_digits(x: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(unique_digits(x: [15, 33, 1422, 1]) == [1, 15, 33])\nassert(unique_digits(x: [152, 323, 1422, 10]) == [] as [Int])\nassert(unique_digits(x: [12345, 2033, 111, 151]) == [111, 151])\nassert(unique_digits(x: [135, 103, 31]) == [31, 135])", "stop_tokens": ["\n}"], "task_id": "HumanEval_104_unique_digits", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(unique_digits(x: [15, 33, 1422, 1]) == [1, 15, 33])\nassert(unique_digits(x: [152, 323, 1422, 10]) == [] as [Int])\nassert(unique_digits(x: [12345, 2033, 111, 151]) == [111, 151])\nassert(unique_digits(x: [135, 103, 31]) == [31, 135])"}
{"name": "HumanEval_117_select_words", "language": "swift", "prompt": "\n/// Given a string s and a natural number n, you have been tasked to implement \n/// a function that returns an array of all words from string s that contain exactly \n/// n consonants, in order these words appear in the string s.\n/// If the string s is empty then the function should return an empty array.\n/// Note: you may assume the input string contains only letters and spaces.\n/// Examples:\n/// >>> select_words(s: \"Mary had a little lamb\", n: 4)\n/// [\"little\"]\n/// >>> select_words(s: \"Mary had a little lamb\", n: 3)\n/// [\"Mary\", \"lamb\"]\n/// >>> select_words(s: \"simple white space\", n: 2)\n/// [] as [String]\n/// >>> select_words(s: \"Hello world\", n: 4)\n/// [\"world\"]\n/// >>> select_words(s: \"Uncle sam\", n: 3)\n/// [\"Uncle\"]\nfunc select_words(s: String, n: Int) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(select_words(s: \"Mary had a little lamb\", n: 4) == [\"little\"])\nassert(select_words(s: \"Mary had a little lamb\", n: 3) == [\"Mary\", \"lamb\"])\nassert(select_words(s: \"simple white space\", n: 2) == [] as [String])\nassert(select_words(s: \"Hello world\", n: 4) == [\"world\"])\nassert(select_words(s: \"Uncle sam\", n: 3) == [\"Uncle\"])\nassert(select_words(s: \"\", n: 4) == [] as [String])\nassert(select_words(s: \"a b c d e f\", n: 1) == [\"b\", \"c\", \"d\", \"f\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_117_select_words", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(select_words(s: \"Mary had a little lamb\", n: 4) == [\"little\"])\nassert(select_words(s: \"Mary had a little lamb\", n: 3) == [\"Mary\", \"lamb\"])\nassert(select_words(s: \"simple white space\", n: 2) == [] as [String])\nassert(select_words(s: \"Hello world\", n: 4) == [\"world\"])\nassert(select_words(s: \"Uncle sam\", n: 3) == [\"Uncle\"])\nassert(select_words(s: \"\", n: 4) == [] as [String])\nassert(select_words(s: \"a b c d e f\", n: 1) == [\"b\", \"c\", \"d\", \"f\"])"}
{"name": "HumanEval_72_will_it_fly", "language": "swift", "prompt": "\n/// Write a function that returns true if the object q will fly, and false otherwise.\n/// The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n/// Example:\n/// >>> will_it_fly(q: [1, 2], w: 5)\n/// false\n/// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n/// >>> will_it_fly(q: [3, 2, 3], w: 1)\n/// false\n/// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n/// >>> will_it_fly(q: [3, 2, 3], w: 9)\n/// true\n/// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n/// >>> will_it_fly(q: [3], w: 5)\n/// true\n/// # 3 is less than the maximum possible weight, and it's balanced.\nfunc will_it_fly(q: [Int], w: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(will_it_fly(q: [3, 2, 3], w: 9) == true)\nassert(will_it_fly(q: [1, 2], w: 5) == false)\nassert(will_it_fly(q: [3], w: 5) == true)\nassert(will_it_fly(q: [3, 2, 3], w: 1) == false)\nassert(will_it_fly(q: [1, 2, 3], w: 6) == false)\nassert(will_it_fly(q: [5], w: 5) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_72_will_it_fly", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(will_it_fly(q: [3, 2, 3], w: 9) == true)\nassert(will_it_fly(q: [1, 2], w: 5) == false)\nassert(will_it_fly(q: [3], w: 5) == true)\nassert(will_it_fly(q: [3, 2, 3], w: 1) == false)\nassert(will_it_fly(q: [1, 2, 3], w: 6) == false)\nassert(will_it_fly(q: [5], w: 5) == true)"}
{"name": "HumanEval_55_fib", "language": "swift", "prompt": "\n/// Return n-th Fibonacci number.\n/// >>> fib(n: 10)\n/// 55\n/// >>> fib(n: 1)\n/// 1\n/// >>> fib(n: 8)\n/// 21\nfunc fib(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fib(n: 10) == 55)\nassert(fib(n: 1) == 1)\nassert(fib(n: 8) == 21)\nassert(fib(n: 11) == 89)\nassert(fib(n: 12) == 144)", "stop_tokens": ["\n}"], "task_id": "HumanEval_55_fib", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fib(n: 10) == 55)\nassert(fib(n: 1) == 1)\nassert(fib(n: 8) == 21)\nassert(fib(n: 11) == 89)\nassert(fib(n: 12) == 144)"}
{"name": "HumanEval_153_Strongest_Extension", "language": "swift", "prompt": "\n/// You will be given the name of a class (a string) and an array of extensions.\n/// The extensions are to be used to load additional classes to the class. The\n/// strength of the extension is as follows: Let CAP be the number of the uppercase\n/// letters in the extension's name, and let SM be the number of lowercase letters \n/// in the extension's name, the strength is given by the fraction CAP - SM. \n/// You should find the strongest extension and return a string in this \n/// format: ClassName.StrongestExtensionName.\n/// If there are two or more extensions with the same strength, you should\n/// choose the one that comes first in the array.\n/// For example, if you are given \"Slices\" as the class and an array of the\n/// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n/// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n/// (its strength is -1).\n/// Example:\n/// >>> Strongest_Extension(class_name: \"my_class\", extensions: [\"AA\", \"Be\", \"CC\"])\n/// \"my_class.AA\"\nfunc Strongest_Extension(class_name: String, extensions: [String]) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(Strongest_Extension(class_name: \"Watashi\", extensions: [\"tEN\", \"niNE\", \"eIGHt8OKe\"]) == \"Watashi.eIGHt8OKe\")\nassert(Strongest_Extension(class_name: \"Boku123\", extensions: [\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"]) == \"Boku123.YEs.WeCaNe\")\nassert(Strongest_Extension(class_name: \"__YESIMHERE\", extensions: [\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"]) == \"__YESIMHERE.NuLl__\")\nassert(Strongest_Extension(class_name: \"K\", extensions: [\"Ta\", \"TAR\", \"t234An\", \"cosSo\"]) == \"K.TAR\")\nassert(Strongest_Extension(class_name: \"__HAHA\", extensions: [\"Tab\", \"123\", \"781345\", \"-_-\"]) == \"__HAHA.123\")\nassert(Strongest_Extension(class_name: \"YameRore\", extensions: [\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"]) == \"YameRore.okIWILL123\")\nassert(Strongest_Extension(class_name: \"finNNalLLly\", extensions: [\"Die\", \"NowW\", \"Wow\", \"WoW\"]) == \"finNNalLLly.WoW\")\nassert(Strongest_Extension(class_name: \"_\", extensions: [\"Bb\", \"91245\"]) == \"_.Bb\")\nassert(Strongest_Extension(class_name: \"Sp\", extensions: [\"671235\", \"Bb\"]) == \"Sp.671235\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_153_Strongest_Extension", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(Strongest_Extension(class_name: \"Watashi\", extensions: [\"tEN\", \"niNE\", \"eIGHt8OKe\"]) == \"Watashi.eIGHt8OKe\")\nassert(Strongest_Extension(class_name: \"Boku123\", extensions: [\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"]) == \"Boku123.YEs.WeCaNe\")\nassert(Strongest_Extension(class_name: \"__YESIMHERE\", extensions: [\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"]) == \"__YESIMHERE.NuLl__\")\nassert(Strongest_Extension(class_name: \"K\", extensions: [\"Ta\", \"TAR\", \"t234An\", \"cosSo\"]) == \"K.TAR\")\nassert(Strongest_Extension(class_name: \"__HAHA\", extensions: [\"Tab\", \"123\", \"781345\", \"-_-\"]) == \"__HAHA.123\")\nassert(Strongest_Extension(class_name: \"YameRore\", extensions: [\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"]) == \"YameRore.okIWILL123\")\nassert(Strongest_Extension(class_name: \"finNNalLLly\", extensions: [\"Die\", \"NowW\", \"Wow\", \"WoW\"]) == \"finNNalLLly.WoW\")\nassert(Strongest_Extension(class_name: \"_\", extensions: [\"Bb\", \"91245\"]) == \"_.Bb\")\nassert(Strongest_Extension(class_name: \"Sp\", extensions: [\"671235\", \"Bb\"]) == \"Sp.671235\")"}
{"name": "HumanEval_119_match_parens", "language": "swift", "prompt": "\n/// You are given an array of two strings, both strings consist of open\n/// parentheses '(' or close parentheses ')' only.\n/// Your job is to check if it is possible to concatenate the two strings in\n/// some order, that the resulting string will be good.\n/// A string S is considered to be good if and only if all parentheses in S\n/// are balanced. For example: the string '(())()' is good, while the string\n/// '())' is not.\n/// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n/// Examples:\n/// >>> match_parens(lst: [\"()(\", \")\"])\n/// \"Yes\"\n/// >>> match_parens(lst: [\")\", \")\"])\n/// \"No\"\nfunc match_parens(lst: [String]) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(match_parens(lst: [\"()(\", \")\"]) == \"Yes\")\nassert(match_parens(lst: [\")\", \")\"]) == \"No\")\nassert(match_parens(lst: [\"(()(())\", \"())())\"]) == \"No\")\nassert(match_parens(lst: [\")())\", \"(()()(\"]) == \"Yes\")\nassert(match_parens(lst: [\"(())))\", \"(()())((\"]) == \"Yes\")\nassert(match_parens(lst: [\"()\", \"())\"]) == \"No\")\nassert(match_parens(lst: [\"(()(\", \"()))()\"]) == \"Yes\")\nassert(match_parens(lst: [\"((((\", \"((())\"]) == \"No\")\nassert(match_parens(lst: [\")(()\", \"(()(\"]) == \"No\")\nassert(match_parens(lst: [\")(\", \")(\"]) == \"No\")\nassert(match_parens(lst: [\"(\", \")\"]) == \"Yes\")\nassert(match_parens(lst: [\")\", \"(\"]) == \"Yes\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_119_match_parens", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(match_parens(lst: [\"()(\", \")\"]) == \"Yes\")\nassert(match_parens(lst: [\")\", \")\"]) == \"No\")\nassert(match_parens(lst: [\"(()(())\", \"())())\"]) == \"No\")\nassert(match_parens(lst: [\")())\", \"(()()(\"]) == \"Yes\")\nassert(match_parens(lst: [\"(())))\", \"(()())((\"]) == \"Yes\")\nassert(match_parens(lst: [\"()\", \"())\"]) == \"No\")\nassert(match_parens(lst: [\"(()(\", \"()))()\"]) == \"Yes\")\nassert(match_parens(lst: [\"((((\", \"((())\"]) == \"No\")\nassert(match_parens(lst: [\")(()\", \"(()(\"]) == \"No\")\nassert(match_parens(lst: [\")(\", \")(\"]) == \"No\")\nassert(match_parens(lst: [\"(\", \")\"]) == \"Yes\")\nassert(match_parens(lst: [\")\", \"(\"]) == \"Yes\")"}
{"name": "HumanEval_90_next_smallest", "language": "swift", "prompt": "\n/// You are given an array of integers.\n/// Write a function next_smallest() that returns the 2nd smallest element of the array.\n/// Return nil if there is no such element.\n/// >>> next_smallest(lst: [1, 2, 3, 4, 5])\n/// 2\n/// >>> next_smallest(lst: [5, 1, 4, 3, 2])\n/// 2\n/// >>> next_smallest(lst: [] as [Int])\n/// nil\n/// >>> next_smallest(lst: [1, 1])\n/// nil\nfunc next_smallest(lst: [Int]) -> Int? {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(next_smallest(lst: [1, 2, 3, 4, 5]) == 2)\nassert(next_smallest(lst: [5, 1, 4, 3, 2]) == 2)\nassert(next_smallest(lst: [] as [Int]) == nil)\nassert(next_smallest(lst: [1, 1]) == nil)\nassert(next_smallest(lst: [1, 1, 1, 1, 0]) == 1)\nassert(next_smallest(lst: [1, 1]) == nil)\nassert(next_smallest(lst: [-35, 34, 12, -45]) == -35)", "stop_tokens": ["\n}"], "task_id": "HumanEval_90_next_smallest", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(next_smallest(lst: [1, 2, 3, 4, 5]) == 2)\nassert(next_smallest(lst: [5, 1, 4, 3, 2]) == 2)\nassert(next_smallest(lst: [] as [Int]) == nil)\nassert(next_smallest(lst: [1, 1]) == nil)\nassert(next_smallest(lst: [1, 1, 1, 1, 0]) == 1)\nassert(next_smallest(lst: [1, 1]) == nil)\nassert(next_smallest(lst: [-35, 34, 12, -45]) == -35)"}
{"name": "HumanEval_92_any_int", "language": "swift", "prompt": "\n/// Create a function that takes 3 numbers.\n/// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n/// Returns false in any other cases.\n/// Examples\n/// >>> any_int(x: 5, y: 2, z: 7)\n/// true\n/// >>> any_int(x: 3, y: 2, z: 2)\n/// false\n/// >>> any_int(x: 3, y: -2, z: 1)\n/// true\n/// >>> any_int(x: 3.6, y: -2.2, z: 2)\n/// false\nfunc any_int(x: Double, y: Double, z: Double) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(any_int(x: 2, y: 3, z: 1) == true)\nassert(any_int(x: 2.5, y: 2, z: 3) == false)\nassert(any_int(x: 1.5, y: 5, z: 3.5) == false)\nassert(any_int(x: 2, y: 6, z: 2) == false)\nassert(any_int(x: 4, y: 2, z: 2) == true)\nassert(any_int(x: 2.2, y: 2.2, z: 2.2) == false)\nassert(any_int(x: -4, y: 6, z: 2) == true)\nassert(any_int(x: 2, y: 1, z: 1) == true)\nassert(any_int(x: 3, y: 4, z: 7) == true)\nassert(any_int(x: 3.0, y: 4, z: 7) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_92_any_int", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(any_int(x: 2, y: 3, z: 1) == true)\nassert(any_int(x: 2.5, y: 2, z: 3) == false)\nassert(any_int(x: 1.5, y: 5, z: 3.5) == false)\nassert(any_int(x: 2, y: 6, z: 2) == false)\nassert(any_int(x: 4, y: 2, z: 2) == true)\nassert(any_int(x: 2.2, y: 2.2, z: 2.2) == false)\nassert(any_int(x: -4, y: 6, z: 2) == true)\nassert(any_int(x: 2, y: 1, z: 1) == true)\nassert(any_int(x: 3, y: 4, z: 7) == true)\nassert(any_int(x: 3.0, y: 4, z: 7) == false)"}
{"name": "HumanEval_2_truncate_number", "language": "swift", "prompt": "\n/// Given a positive floating point number, it can be decomposed into\n/// and integer part (largest integer smaller than given number) and decimals\n/// (leftover part always smaller than 1).\n/// Return the decimal part of the number.\n/// >>> truncate_number(number: 3.5)\n/// 0.5\nfunc truncate_number(number: Double) -> Double {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(truncate_number(number: 3.5) == 0.5)\nassert(truncate_number(number: 1.25) == 0.25)\nassert(truncate_number(number: 123.0) == 0.0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_2_truncate_number", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(truncate_number(number: 3.5) == 0.5)\nassert(truncate_number(number: 1.25) == 0.25)\nassert(truncate_number(number: 123.0) == 0.0)"}
{"name": "HumanEval_42_incr_list", "language": "swift", "prompt": "\n/// Return array with elements incremented by 1.\n/// >>> incr_list(l: [1, 2, 3])\n/// [2, 3, 4]\n/// >>> incr_list(l: [5, 3, 5, 2, 3, 3, 9, 0, 123])\n/// [6, 4, 6, 3, 4, 4, 10, 1, 124]\nfunc incr_list(l: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(incr_list(l: [] as [Int]) == [] as [Int])\nassert(incr_list(l: [3, 2, 1]) == [4, 3, 2])\nassert(incr_list(l: [5, 2, 5, 2, 3, 3, 9, 0, 123]) == [6, 3, 6, 3, 4, 4, 10, 1, 124])", "stop_tokens": ["\n}"], "task_id": "HumanEval_42_incr_list", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(incr_list(l: [] as [Int]) == [] as [Int])\nassert(incr_list(l: [3, 2, 1]) == [4, 3, 2])\nassert(incr_list(l: [5, 2, 5, 2, 3, 3, 9, 0, 123]) == [6, 3, 6, 3, 4, 4, 10, 1, 124])"}
{"name": "HumanEval_150_x_or_y", "language": "swift", "prompt": "\n/// A simple program which should return the value of x if n is \n/// a prime number and should return the value of y otherwise.\n/// Examples:\n/// >>> x_or_y(n: 7, x: 34, y: 12)\n/// 34\n/// >>> x_or_y(n: 15, x: 8, y: 5)\n/// 5\nfunc x_or_y(n: Int, x: Int, y: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(x_or_y(n: 7, x: 34, y: 12) == 34)\nassert(x_or_y(n: 15, x: 8, y: 5) == 5)\nassert(x_or_y(n: 3, x: 33, y: 5212) == 33)\nassert(x_or_y(n: 1259, x: 3, y: 52) == 3)\nassert(x_or_y(n: 7919, x: -1, y: 12) == -1)\nassert(x_or_y(n: 3609, x: 1245, y: 583) == 583)\nassert(x_or_y(n: 91, x: 56, y: 129) == 129)\nassert(x_or_y(n: 6, x: 34, y: 1234) == 1234)\nassert(x_or_y(n: 1, x: 2, y: 0) == 0)\nassert(x_or_y(n: 2, x: 2, y: 0) == 2)", "stop_tokens": ["\n}"], "task_id": "HumanEval_150_x_or_y", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(x_or_y(n: 7, x: 34, y: 12) == 34)\nassert(x_or_y(n: 15, x: 8, y: 5) == 5)\nassert(x_or_y(n: 3, x: 33, y: 5212) == 33)\nassert(x_or_y(n: 1259, x: 3, y: 52) == 3)\nassert(x_or_y(n: 7919, x: -1, y: 12) == -1)\nassert(x_or_y(n: 3609, x: 1245, y: 583) == 583)\nassert(x_or_y(n: 91, x: 56, y: 129) == 129)\nassert(x_or_y(n: 6, x: 34, y: 1234) == 1234)\nassert(x_or_y(n: 1, x: 2, y: 0) == 0)\nassert(x_or_y(n: 2, x: 2, y: 0) == 2)"}
{"name": "HumanEval_49_modp", "language": "swift", "prompt": "\n/// Return 2^n modulo p (be aware of numerics).\n/// >>> modp(n: 3, p: 5)\n/// 3\n/// >>> modp(n: 1101, p: 101)\n/// 2\n/// >>> modp(n: 0, p: 101)\n/// 1\n/// >>> modp(n: 3, p: 11)\n/// 8\n/// >>> modp(n: 100, p: 101)\n/// 1\nfunc modp(n: Int, p: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(modp(n: 3, p: 5) == 3)\nassert(modp(n: 1101, p: 101) == 2)\nassert(modp(n: 0, p: 101) == 1)\nassert(modp(n: 3, p: 11) == 8)\nassert(modp(n: 100, p: 101) == 1)\nassert(modp(n: 30, p: 5) == 4)\nassert(modp(n: 31, p: 5) == 3)", "stop_tokens": ["\n}"], "task_id": "HumanEval_49_modp", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(modp(n: 3, p: 5) == 3)\nassert(modp(n: 1101, p: 101) == 2)\nassert(modp(n: 0, p: 101) == 1)\nassert(modp(n: 3, p: 11) == 8)\nassert(modp(n: 100, p: 101) == 1)\nassert(modp(n: 30, p: 5) == 4)\nassert(modp(n: 31, p: 5) == 3)"}
{"name": "HumanEval_155_even_odd_count", "language": "swift", "prompt": "\n/// Given an integer. return a tuple that has the number of even and odd digits respectively.\n/// Example:\n/// >>> even_odd_count(num: -12)\n/// (1, 1)\n/// >>> even_odd_count(num: 123)\n/// (1, 2)\nfunc even_odd_count(num: Int) -> (Int, Int) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(even_odd_count(num: 7) == (0, 1))\nassert(even_odd_count(num: -78) == (1, 1))\nassert(even_odd_count(num: 3452) == (2, 2))\nassert(even_odd_count(num: 346211) == (3, 3))\nassert(even_odd_count(num: -345821) == (3, 3))\nassert(even_odd_count(num: -2) == (1, 0))\nassert(even_odd_count(num: -45347) == (2, 3))\nassert(even_odd_count(num: 0) == (1, 0))", "stop_tokens": ["\n}"], "task_id": "HumanEval_155_even_odd_count", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(even_odd_count(num: 7) == (0, 1))\nassert(even_odd_count(num: -78) == (1, 1))\nassert(even_odd_count(num: 3452) == (2, 2))\nassert(even_odd_count(num: 346211) == (3, 3))\nassert(even_odd_count(num: -345821) == (3, 3))\nassert(even_odd_count(num: -2) == (1, 0))\nassert(even_odd_count(num: -45347) == (2, 3))\nassert(even_odd_count(num: 0) == (1, 0))"}
{"name": "HumanEval_80_is_happy", "language": "swift", "prompt": "\n/// You are given a string s.\n/// Your task is to check if the string is hapswift or not.\n/// A string is hapswift if its length is at least 3 and every 3 consecutive letters are distinct\n/// For example:\n/// >>> is_happy(s: \"a\")\n/// false\n/// >>> is_happy(s: \"aa\")\n/// false\n/// >>> is_happy(s: \"abcd\")\n/// true\n/// >>> is_happy(s: \"aabb\")\n/// false\n/// >>> is_happy(s: \"adb\")\n/// true\n/// >>> is_happy(s: \"xyy\")\n/// false\nfunc is_happy(s: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_happy(s: \"a\") == false)\nassert(is_happy(s: \"aa\") == false)\nassert(is_happy(s: \"abcd\") == true)\nassert(is_happy(s: \"aabb\") == false)\nassert(is_happy(s: \"adb\") == true)\nassert(is_happy(s: \"xyy\") == false)\nassert(is_happy(s: \"iopaxpoi\") == true)\nassert(is_happy(s: \"iopaxioi\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_80_is_happy", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_happy(s: \"a\") == false)\nassert(is_happy(s: \"aa\") == false)\nassert(is_happy(s: \"abcd\") == true)\nassert(is_happy(s: \"aabb\") == false)\nassert(is_happy(s: \"adb\") == true)\nassert(is_happy(s: \"xyy\") == false)\nassert(is_happy(s: \"iopaxpoi\") == true)\nassert(is_happy(s: \"iopaxioi\") == false)"}
{"name": "HumanEval_59_largest_prime_factor", "language": "swift", "prompt": "\n/// Return the largest prime factor of n. Assume n > 1 and is not a prime.\n/// >>> largest_prime_factor(n: 13195)\n/// 29\n/// >>> largest_prime_factor(n: 2048)\n/// 2\nfunc largest_prime_factor(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(largest_prime_factor(n: 15) == 5)\nassert(largest_prime_factor(n: 27) == 3)\nassert(largest_prime_factor(n: 63) == 7)\nassert(largest_prime_factor(n: 330) == 11)\nassert(largest_prime_factor(n: 13195) == 29)", "stop_tokens": ["\n}"], "task_id": "HumanEval_59_largest_prime_factor", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(largest_prime_factor(n: 15) == 5)\nassert(largest_prime_factor(n: 27) == 3)\nassert(largest_prime_factor(n: 63) == 7)\nassert(largest_prime_factor(n: 330) == 11)\nassert(largest_prime_factor(n: 13195) == 29)"}
{"name": "HumanEval_66_digitSum", "language": "swift", "prompt": "\n/// Task\n/// Write a function that takes a string as input and returns the sum of the upper characters only'\n/// ASCII codes.\n/// Examples:\n/// >>> digitSum(s: \"\")\n/// 0\n/// >>> digitSum(s: \"abAB\")\n/// 131\n/// >>> digitSum(s: \"abcCd\")\n/// 67\n/// >>> digitSum(s: \"helloE\")\n/// 69\n/// >>> digitSum(s: \"woArBld\")\n/// 131\n/// >>> digitSum(s: \"aAaaaXa\")\n/// 153\nfunc digitSum(s: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(digitSum(s: \"\") == 0)\nassert(digitSum(s: \"abAB\") == 131)\nassert(digitSum(s: \"abcCd\") == 67)\nassert(digitSum(s: \"helloE\") == 69)\nassert(digitSum(s: \"woArBld\") == 131)\nassert(digitSum(s: \"aAaaaXa\") == 153)\nassert(digitSum(s: \" How are yOu?\") == 151)\nassert(digitSum(s: \"You arE Very Smart\") == 327)", "stop_tokens": ["\n}"], "task_id": "HumanEval_66_digitSum", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(digitSum(s: \"\") == 0)\nassert(digitSum(s: \"abAB\") == 131)\nassert(digitSum(s: \"abcCd\") == 67)\nassert(digitSum(s: \"helloE\") == 69)\nassert(digitSum(s: \"woArBld\") == 131)\nassert(digitSum(s: \"aAaaaXa\") == 153)\nassert(digitSum(s: \" How are yOu?\") == 151)\nassert(digitSum(s: \"You arE Very Smart\") == 327)"}
{"name": "HumanEval_21_rescale_to_unit", "language": "swift", "prompt": "\n/// Given array of numbers (of at least two elements), apply a linear transform to that array,\n/// such that the smallest number will become 0 and the largest will become 1\n/// >>> rescale_to_unit(numbers: [1.0, 2.0, 3.0, 4.0, 5.0])\n/// [0.0, 0.25, 0.5, 0.75, 1.0]\nfunc rescale_to_unit(numbers: [Double]) -> [Double] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(rescale_to_unit(numbers: [2.0, 49.9]) == [0.0, 1.0])\nassert(rescale_to_unit(numbers: [100.0, 49.9]) == [1.0, 0.0])\nassert(rescale_to_unit(numbers: [1.0, 2.0, 3.0, 4.0, 5.0]) == [0.0, 0.25, 0.5, 0.75, 1.0])\nassert(rescale_to_unit(numbers: [2.0, 1.0, 5.0, 3.0, 4.0]) == [0.25, 0.0, 1.0, 0.5, 0.75])\nassert(rescale_to_unit(numbers: [12.0, 11.0, 15.0, 13.0, 14.0]) == [0.25, 0.0, 1.0, 0.5, 0.75])", "stop_tokens": ["\n}"], "task_id": "HumanEval_21_rescale_to_unit", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(rescale_to_unit(numbers: [2.0, 49.9]) == [0.0, 1.0])\nassert(rescale_to_unit(numbers: [100.0, 49.9]) == [1.0, 0.0])\nassert(rescale_to_unit(numbers: [1.0, 2.0, 3.0, 4.0, 5.0]) == [0.0, 0.25, 0.5, 0.75, 1.0])\nassert(rescale_to_unit(numbers: [2.0, 1.0, 5.0, 3.0, 4.0]) == [0.25, 0.0, 1.0, 0.5, 0.75])\nassert(rescale_to_unit(numbers: [12.0, 11.0, 15.0, 13.0, 14.0]) == [0.25, 0.0, 1.0, 0.5, 0.75])"}
{"name": "HumanEval_121_solution", "language": "swift", "prompt": "\n/// Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n/// Examples\n/// >>> solution(lst: [5, 8, 7, 1])\n/// 12\n/// >>> solution(lst: [3, 3, 3, 3, 3])\n/// 9\n/// >>> solution(lst: [30, 13, 24, 321])\n/// 0\nfunc solution(lst: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(solution(lst: [5, 8, 7, 1]) == 12)\nassert(solution(lst: [3, 3, 3, 3, 3]) == 9)\nassert(solution(lst: [30, 13, 24, 321]) == 0)\nassert(solution(lst: [5, 9]) == 5)\nassert(solution(lst: [2, 4, 8]) == 0)\nassert(solution(lst: [30, 13, 23, 32]) == 23)\nassert(solution(lst: [3, 13, 2, 9]) == 3)", "stop_tokens": ["\n}"], "task_id": "HumanEval_121_solution", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(solution(lst: [5, 8, 7, 1]) == 12)\nassert(solution(lst: [3, 3, 3, 3, 3]) == 9)\nassert(solution(lst: [30, 13, 24, 321]) == 0)\nassert(solution(lst: [5, 9]) == 5)\nassert(solution(lst: [2, 4, 8]) == 0)\nassert(solution(lst: [30, 13, 23, 32]) == 23)\nassert(solution(lst: [3, 13, 2, 9]) == 3)"}
{"name": "HumanEval_68_pluck", "language": "swift", "prompt": "\n/// \"Given an array representing a branch of a tree that has non-negative integer nodes\n/// your task is to pluck one of the nodes and return it.\n/// The plucked node should be the node with the smallest even value.\n/// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n/// The plucked node should be returned in an array, [ smalest_value, its index ],\n/// If there are no even values or the given array is empty, return [].\n/// Example 1:\n/// >>> pluck(arr: [4, 2, 3])\n/// [2, 1]\n/// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n/// Example 2:\n/// >>> pluck(arr: [1, 2, 3])\n/// [2, 1]\n/// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n/// Example 3:\n/// >>> pluck(arr: [] as [Int])\n/// [] as [Int]\n/// Example 4:\n/// >>> pluck(arr: [5, 0, 3, 0, 4, 2])\n/// [0, 1]\n/// Explanation: 0 is the smallest value, but  there are two zeros,\n/// so we will choose the first zero, which has the smallest index.\n/// Constraints:\n/// * 1 <= nodes.length <= 10000\n/// * 0 <= node.value\nfunc pluck(arr: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(pluck(arr: [4, 2, 3]) == [2, 1])\nassert(pluck(arr: [1, 2, 3]) == [2, 1])\nassert(pluck(arr: [] as [Int]) == [] as [Int])\nassert(pluck(arr: [5, 0, 3, 0, 4, 2]) == [0, 1])\nassert(pluck(arr: [1, 2, 3, 0, 5, 3]) == [0, 3])\nassert(pluck(arr: [5, 4, 8, 4, 8]) == [4, 1])\nassert(pluck(arr: [7, 6, 7, 1]) == [6, 1])\nassert(pluck(arr: [7, 9, 7, 1]) == [] as [Int])", "stop_tokens": ["\n}"], "task_id": "HumanEval_68_pluck", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(pluck(arr: [4, 2, 3]) == [2, 1])\nassert(pluck(arr: [1, 2, 3]) == [2, 1])\nassert(pluck(arr: [] as [Int]) == [] as [Int])\nassert(pluck(arr: [5, 0, 3, 0, 4, 2]) == [0, 1])\nassert(pluck(arr: [1, 2, 3, 0, 5, 3]) == [0, 3])\nassert(pluck(arr: [5, 4, 8, 4, 8]) == [4, 1])\nassert(pluck(arr: [7, 6, 7, 1]) == [6, 1])\nassert(pluck(arr: [7, 9, 7, 1]) == [] as [Int])"}
{"name": "HumanEval_147_get_max_triples", "language": "swift", "prompt": "\n/// You are given a positive integer n. You have to create an integer array a of length n.\n/// For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n/// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n/// and a[i] + a[j] + a[k] is a multiple of 3.\n/// Example :\n/// >>> get_max_triples(n: 5)\n/// 1\n/// Explanation: \n/// a = [1, 3, 7, 13, 21]\n/// The only valid triple is (1, 7, 13).\nfunc get_max_triples(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_max_triples(n: 5) == 1)\nassert(get_max_triples(n: 6) == 4)\nassert(get_max_triples(n: 10) == 36)\nassert(get_max_triples(n: 100) == 53361)", "stop_tokens": ["\n}"], "task_id": "HumanEval_147_get_max_triples", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_max_triples(n: 5) == 1)\nassert(get_max_triples(n: 6) == 4)\nassert(get_max_triples(n: 10) == 36)\nassert(get_max_triples(n: 100) == 53361)"}
{"name": "HumanEval_110_exchange", "language": "swift", "prompt": "\n/// In this problem, you will implement a function that takes two arrays of numbers,\n/// and determines whether it is possible to perform an exchange of elements\n/// between them to make lst1 an array of only even numbers.\n/// There is no limit on the number of exchanged elements between lst1 and lst2.\n/// If it is possible to exchange elements between the lst1 and lst2 to make\n/// all the elements of lst1 to be even, return \"YES\".\n/// Otherwise, return \"NO\".\n/// For example:\n/// >>> exchange(lst1: [1, 2, 3, 4], lst2: [1, 2, 3, 4])\n/// \"YES\"\n/// >>> exchange(lst1: [1, 2, 3, 4], lst2: [1, 5, 3, 4])\n/// \"NO\"\n/// It is assumed that the input arrays will be non-empty.\nfunc exchange(lst1: [Int], lst2: [Int]) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(exchange(lst1: [1, 2, 3, 4], lst2: [1, 2, 3, 4]) == \"YES\")\nassert(exchange(lst1: [1, 2, 3, 4], lst2: [1, 5, 3, 4]) == \"NO\")\nassert(exchange(lst1: [1, 2, 3, 4], lst2: [2, 1, 4, 3]) == \"YES\")\nassert(exchange(lst1: [5, 7, 3], lst2: [2, 6, 4]) == \"YES\")\nassert(exchange(lst1: [5, 7, 3], lst2: [2, 6, 3]) == \"NO\")\nassert(exchange(lst1: [3, 2, 6, 1, 8, 9], lst2: [3, 5, 5, 1, 1, 1]) == \"NO\")\nassert(exchange(lst1: [100, 200], lst2: [200, 200]) == \"YES\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_110_exchange", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(exchange(lst1: [1, 2, 3, 4], lst2: [1, 2, 3, 4]) == \"YES\")\nassert(exchange(lst1: [1, 2, 3, 4], lst2: [1, 5, 3, 4]) == \"NO\")\nassert(exchange(lst1: [1, 2, 3, 4], lst2: [2, 1, 4, 3]) == \"YES\")\nassert(exchange(lst1: [5, 7, 3], lst2: [2, 6, 4]) == \"YES\")\nassert(exchange(lst1: [5, 7, 3], lst2: [2, 6, 3]) == \"NO\")\nassert(exchange(lst1: [3, 2, 6, 1, 8, 9], lst2: [3, 5, 5, 1, 1, 1]) == \"NO\")\nassert(exchange(lst1: [100, 200], lst2: [200, 200]) == \"YES\")"}
{"name": "HumanEval_47_median", "language": "swift", "prompt": "\n/// Return median of elements in the array l.\n/// >>> median(l: [3, 1, 2, 4, 5])\n/// 3\n/// >>> median(l: [-10, 4, 6, 1000, 10, 20])\n/// 15.0\nfunc median(l: [Int]) -> Double {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(median(l: [3, 1, 2, 4, 5]) == 3)\nassert(median(l: [-10, 4, 6, 1000, 10, 20]) == 8.0)\nassert(median(l: [5]) == 5)\nassert(median(l: [6, 5]) == 5.5)\nassert(median(l: [8, 1, 3, 9, 9, 2, 7]) == 7)", "stop_tokens": ["\n}"], "task_id": "HumanEval_47_median", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(median(l: [3, 1, 2, 4, 5]) == 3)\nassert(median(l: [-10, 4, 6, 1000, 10, 20]) == 8.0)\nassert(median(l: [5]) == 5)\nassert(median(l: [6, 5]) == 5.5)\nassert(median(l: [8, 1, 3, 9, 9, 2, 7]) == 7)"}
{"name": "HumanEval_82_prime_length", "language": "swift", "prompt": "\n/// Write a function that takes a string and returns true if the string\n/// length is a prime number or false otherwise\n/// Examples\n/// >>> prime_length(string: \"Hello\")\n/// true\n/// >>> prime_length(string: \"abcdcba\")\n/// true\n/// >>> prime_length(string: \"kittens\")\n/// true\n/// >>> prime_length(string: \"orange\")\n/// false\nfunc prime_length(string: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(prime_length(string: \"Hello\") == true)\nassert(prime_length(string: \"abcdcba\") == true)\nassert(prime_length(string: \"kittens\") == true)\nassert(prime_length(string: \"orange\") == false)\nassert(prime_length(string: \"wow\") == true)\nassert(prime_length(string: \"world\") == true)\nassert(prime_length(string: \"MadaM\") == true)\nassert(prime_length(string: \"Wow\") == true)\nassert(prime_length(string: \"\") == false)\nassert(prime_length(string: \"HI\") == true)\nassert(prime_length(string: \"go\") == true)\nassert(prime_length(string: \"gogo\") == false)\nassert(prime_length(string: \"aaaaaaaaaaaaaaa\") == false)\nassert(prime_length(string: \"Madam\") == true)\nassert(prime_length(string: \"M\") == false)\nassert(prime_length(string: \"0\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_82_prime_length", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(prime_length(string: \"Hello\") == true)\nassert(prime_length(string: \"abcdcba\") == true)\nassert(prime_length(string: \"kittens\") == true)\nassert(prime_length(string: \"orange\") == false)\nassert(prime_length(string: \"wow\") == true)\nassert(prime_length(string: \"world\") == true)\nassert(prime_length(string: \"MadaM\") == true)\nassert(prime_length(string: \"Wow\") == true)\nassert(prime_length(string: \"\") == false)\nassert(prime_length(string: \"HI\") == true)\nassert(prime_length(string: \"go\") == true)\nassert(prime_length(string: \"gogo\") == false)\nassert(prime_length(string: \"aaaaaaaaaaaaaaa\") == false)\nassert(prime_length(string: \"Madam\") == true)\nassert(prime_length(string: \"M\") == false)\nassert(prime_length(string: \"0\") == false)"}
{"name": "HumanEval_73_smallest_change", "language": "swift", "prompt": "\n/// Given an array arr of integers, find the minimum number of elements that\n/// need to be changed to make the array palindromic. A palindromic array is an array that\n/// is read the same backwards and forwards. In one change, you can change one element to any other element.\n/// For example:\n/// >>> smallest_change(arr: [1, 2, 3, 5, 4, 7, 9, 6])\n/// 4\n/// >>> smallest_change(arr: [1, 2, 3, 4, 3, 2, 2])\n/// 1\n/// >>> smallest_change(arr: [1, 2, 3, 2, 1])\n/// 0\nfunc smallest_change(arr: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(smallest_change(arr: [1, 2, 3, 5, 4, 7, 9, 6]) == 4)\nassert(smallest_change(arr: [1, 2, 3, 4, 3, 2, 2]) == 1)\nassert(smallest_change(arr: [1, 4, 2]) == 1)\nassert(smallest_change(arr: [1, 4, 4, 2]) == 1)\nassert(smallest_change(arr: [1, 2, 3, 2, 1]) == 0)\nassert(smallest_change(arr: [3, 1, 1, 3]) == 0)\nassert(smallest_change(arr: [1]) == 0)\nassert(smallest_change(arr: [0, 1]) == 1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_73_smallest_change", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(smallest_change(arr: [1, 2, 3, 5, 4, 7, 9, 6]) == 4)\nassert(smallest_change(arr: [1, 2, 3, 4, 3, 2, 2]) == 1)\nassert(smallest_change(arr: [1, 4, 2]) == 1)\nassert(smallest_change(arr: [1, 4, 4, 2]) == 1)\nassert(smallest_change(arr: [1, 2, 3, 2, 1]) == 0)\nassert(smallest_change(arr: [3, 1, 1, 3]) == 0)\nassert(smallest_change(arr: [1]) == 0)\nassert(smallest_change(arr: [0, 1]) == 1)"}
{"name": "HumanEval_133_sum_squares", "language": "swift", "prompt": "\n/// You are given an array of numbers.\n/// You need to return the sum of squared numbers in the given array,\n/// round each element in the array to the upper int(Ceiling) first.\n/// Examples:\n/// >>> sum_squares(lst: [1.0, 2.0, 3.0])\n/// 14\n/// >>> sum_squares(lst: [1.0, 4.0, 9.0])\n/// 98\n/// >>> sum_squares(lst: [1.0, 3.0, 5.0, 7.0])\n/// 84\n/// >>> sum_squares(lst: [1.4, 4.2, 0.0])\n/// 29\n/// >>> sum_squares(lst: [-2.4, 1.0, 1.0])\n/// 6\nfunc sum_squares(lst: [Double]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sum_squares(lst: [1.0, 2.0, 3.0]) == 14)\nassert(sum_squares(lst: [1.0, 2.0, 3.0]) == 14)\nassert(sum_squares(lst: [1.0, 3.0, 5.0, 7.0]) == 84)\nassert(sum_squares(lst: [1.4, 4.2, 0.0]) == 29)\nassert(sum_squares(lst: [-2.4, 1.0, 1.0]) == 6)\nassert(sum_squares(lst: [100.0, 1.0, 15.0, 2.0]) == 10230)\nassert(sum_squares(lst: [10000.0, 10000.0]) == 200000000)\nassert(sum_squares(lst: [-1.4, 4.6, 6.3]) == 75)\nassert(sum_squares(lst: [-1.4, 17.9, 18.9, 19.9]) == 1086)\nassert(sum_squares(lst: [0.0]) == 0)\nassert(sum_squares(lst: [-1.0]) == 1)\nassert(sum_squares(lst: [-1.0, 1.0, 0.0]) == 2)", "stop_tokens": ["\n}"], "task_id": "HumanEval_133_sum_squares", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sum_squares(lst: [1.0, 2.0, 3.0]) == 14)\nassert(sum_squares(lst: [1.0, 2.0, 3.0]) == 14)\nassert(sum_squares(lst: [1.0, 3.0, 5.0, 7.0]) == 84)\nassert(sum_squares(lst: [1.4, 4.2, 0.0]) == 29)\nassert(sum_squares(lst: [-2.4, 1.0, 1.0]) == 6)\nassert(sum_squares(lst: [100.0, 1.0, 15.0, 2.0]) == 10230)\nassert(sum_squares(lst: [10000.0, 10000.0]) == 200000000)\nassert(sum_squares(lst: [-1.4, 4.6, 6.3]) == 75)\nassert(sum_squares(lst: [-1.4, 17.9, 18.9, 19.9]) == 1086)\nassert(sum_squares(lst: [0.0]) == 0)\nassert(sum_squares(lst: [-1.0]) == 1)\nassert(sum_squares(lst: [-1.0, 1.0, 0.0]) == 2)"}
{"name": "HumanEval_141_file_name_check", "language": "swift", "prompt": "\n/// Create a function which takes a string representing a file's name, and returns\n/// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n/// A file's name is considered to be valid if and only if all the following conditions \n/// are met:\n/// - There should not be more than three digits ('0'-'9') in the file's name.\n/// - The file's name contains exactly one dot '.'\n/// - The substring before the dot should not be empty, and it starts with a letter from \n/// the latin alphapet ('a'-'z' and 'A'-'Z').\n/// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n/// Examples:\n/// >>> file_name_check(file_name: \"example.txt\")\n/// \"Yes\"\n/// >>> file_name_check(file_name: \"1example.dll\")\n/// \"No\"\nfunc file_name_check(file_name: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(file_name_check(file_name: \"example.txt\") == \"Yes\")\nassert(file_name_check(file_name: \"1example.dll\") == \"No\")\nassert(file_name_check(file_name: \"s1sdf3.asd\") == \"No\")\nassert(file_name_check(file_name: \"K.dll\") == \"Yes\")\nassert(file_name_check(file_name: \"MY16FILE3.exe\") == \"Yes\")\nassert(file_name_check(file_name: \"His12FILE94.exe\") == \"No\")\nassert(file_name_check(file_name: \"_Y.txt\") == \"No\")\nassert(file_name_check(file_name: \"?aREYA.exe\") == \"No\")\nassert(file_name_check(file_name: \"/this_is_valid.dll\") == \"No\")\nassert(file_name_check(file_name: \"this_is_valid.wow\") == \"No\")\nassert(file_name_check(file_name: \"this_is_valid.txt\") == \"Yes\")\nassert(file_name_check(file_name: \"this_is_valid.txtexe\") == \"No\")\nassert(file_name_check(file_name: \"#this2_i4s_5valid.ten\") == \"No\")\nassert(file_name_check(file_name: \"@this1_is6_valid.exe\") == \"No\")\nassert(file_name_check(file_name: \"this_is_12valid.6exe4.txt\") == \"No\")\nassert(file_name_check(file_name: \"all.exe.txt\") == \"No\")\nassert(file_name_check(file_name: \"I563_No.exe\") == \"Yes\")\nassert(file_name_check(file_name: \"Is3youfault.txt\") == \"Yes\")\nassert(file_name_check(file_name: \"no_one#knows.dll\") == \"Yes\")\nassert(file_name_check(file_name: \"1I563_Yes3.exe\") == \"No\")\nassert(file_name_check(file_name: \"I563_Yes3.txtt\") == \"No\")\nassert(file_name_check(file_name: \"final..txt\") == \"No\")\nassert(file_name_check(file_name: \"final132\") == \"No\")\nassert(file_name_check(file_name: \"_f4indsartal132.\") == \"No\")\nassert(file_name_check(file_name: \".txt\") == \"No\")\nassert(file_name_check(file_name: \"s.\") == \"No\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_141_file_name_check", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(file_name_check(file_name: \"example.txt\") == \"Yes\")\nassert(file_name_check(file_name: \"1example.dll\") == \"No\")\nassert(file_name_check(file_name: \"s1sdf3.asd\") == \"No\")\nassert(file_name_check(file_name: \"K.dll\") == \"Yes\")\nassert(file_name_check(file_name: \"MY16FILE3.exe\") == \"Yes\")\nassert(file_name_check(file_name: \"His12FILE94.exe\") == \"No\")\nassert(file_name_check(file_name: \"_Y.txt\") == \"No\")\nassert(file_name_check(file_name: \"?aREYA.exe\") == \"No\")\nassert(file_name_check(file_name: \"/this_is_valid.dll\") == \"No\")\nassert(file_name_check(file_name: \"this_is_valid.wow\") == \"No\")\nassert(file_name_check(file_name: \"this_is_valid.txt\") == \"Yes\")\nassert(file_name_check(file_name: \"this_is_valid.txtexe\") == \"No\")\nassert(file_name_check(file_name: \"#this2_i4s_5valid.ten\") == \"No\")\nassert(file_name_check(file_name: \"@this1_is6_valid.exe\") == \"No\")\nassert(file_name_check(file_name: \"this_is_12valid.6exe4.txt\") == \"No\")\nassert(file_name_check(file_name: \"all.exe.txt\") == \"No\")\nassert(file_name_check(file_name: \"I563_No.exe\") == \"Yes\")\nassert(file_name_check(file_name: \"Is3youfault.txt\") == \"Yes\")\nassert(file_name_check(file_name: \"no_one#knows.dll\") == \"Yes\")\nassert(file_name_check(file_name: \"1I563_Yes3.exe\") == \"No\")\nassert(file_name_check(file_name: \"I563_Yes3.txtt\") == \"No\")\nassert(file_name_check(file_name: \"final..txt\") == \"No\")\nassert(file_name_check(file_name: \"final132\") == \"No\")\nassert(file_name_check(file_name: \"_f4indsartal132.\") == \"No\")\nassert(file_name_check(file_name: \".txt\") == \"No\")\nassert(file_name_check(file_name: \"s.\") == \"No\")"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "swift", "prompt": "\n/// triples_sum_to_zero takes an array of integers as an input.\n/// it returns true if there are three distinct elements in the array that\n/// sum to zero, and false otherwise.\n/// >>> triples_sum_to_zero(l: [1, 3, 5, 0])\n/// false\n/// >>> triples_sum_to_zero(l: [1, 3, -2, 1])\n/// true\n/// >>> triples_sum_to_zero(l: [1, 2, 3, 7])\n/// false\n/// >>> triples_sum_to_zero(l: [2, 4, -5, 3, 9, 7])\n/// true\n/// >>> triples_sum_to_zero(l: [1])\n/// false\nfunc triples_sum_to_zero(l: [Int]) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(triples_sum_to_zero(l: [1, 3, 5, 0]) == false)\nassert(triples_sum_to_zero(l: [1, 3, 5, -1]) == false)\nassert(triples_sum_to_zero(l: [1, 3, -2, 1]) == true)\nassert(triples_sum_to_zero(l: [1, 2, 3, 7]) == false)\nassert(triples_sum_to_zero(l: [1, 2, 5, 7]) == false)\nassert(triples_sum_to_zero(l: [2, 4, -5, 3, 9, 7]) == true)\nassert(triples_sum_to_zero(l: [1]) == false)\nassert(triples_sum_to_zero(l: [1, 3, 5, -100]) == false)\nassert(triples_sum_to_zero(l: [100, 3, 5, -100]) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(triples_sum_to_zero(l: [1, 3, 5, 0]) == false)\nassert(triples_sum_to_zero(l: [1, 3, 5, -1]) == false)\nassert(triples_sum_to_zero(l: [1, 3, -2, 1]) == true)\nassert(triples_sum_to_zero(l: [1, 2, 3, 7]) == false)\nassert(triples_sum_to_zero(l: [1, 2, 5, 7]) == false)\nassert(triples_sum_to_zero(l: [2, 4, -5, 3, 9, 7]) == true)\nassert(triples_sum_to_zero(l: [1]) == false)\nassert(triples_sum_to_zero(l: [1, 3, 5, -100]) == false)\nassert(triples_sum_to_zero(l: [100, 3, 5, -100]) == false)"}
{"name": "HumanEval_127_intersection", "language": "swift", "prompt": "\n/// You are given two intervals,\n/// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n/// The given intervals are closed which means that the interval (start, end)\n/// includes both start and end.\n/// For each given interval, it is assumed that its start is less or equal its end.\n/// Your task is to determine whether the length of intersection of these two \n/// intervals is a prime number.\n/// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n/// which its length is 1, which not a prime number.\n/// If the length of the intersection is a prime number, return \"YES\",\n/// otherwise, return \"NO\".\n/// If the two intervals don't intersect, return \"NO\".\n/// [input/output] samples:\n/// >>> intersection(interval1: (1, 2), interval2: (2, 3))\n/// \"NO\"\n/// >>> intersection(interval1: (-1, 1), interval2: (0, 4))\n/// \"NO\"\n/// >>> intersection(interval1: (-3, -1), interval2: (-5, 5))\n/// \"YES\"\nfunc intersection(interval1: (Int, Int), interval2: (Int, Int)) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(intersection(interval1: (1, 2), interval2: (2, 3)) == \"NO\")\nassert(intersection(interval1: (-1, 1), interval2: (0, 4)) == \"NO\")\nassert(intersection(interval1: (-3, -1), interval2: (-5, 5)) == \"YES\")\nassert(intersection(interval1: (-2, 2), interval2: (-4, 0)) == \"YES\")\nassert(intersection(interval1: (-11, 2), interval2: (-1, -1)) == \"NO\")\nassert(intersection(interval1: (1, 2), interval2: (3, 5)) == \"NO\")\nassert(intersection(interval1: (1, 2), interval2: (1, 2)) == \"NO\")\nassert(intersection(interval1: (-2, -2), interval2: (-3, -2)) == \"NO\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_127_intersection", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(intersection(interval1: (1, 2), interval2: (2, 3)) == \"NO\")\nassert(intersection(interval1: (-1, 1), interval2: (0, 4)) == \"NO\")\nassert(intersection(interval1: (-3, -1), interval2: (-5, 5)) == \"YES\")\nassert(intersection(interval1: (-2, 2), interval2: (-4, 0)) == \"YES\")\nassert(intersection(interval1: (-11, 2), interval2: (-1, -1)) == \"NO\")\nassert(intersection(interval1: (1, 2), interval2: (3, 5)) == \"NO\")\nassert(intersection(interval1: (1, 2), interval2: (1, 2)) == \"NO\")\nassert(intersection(interval1: (-2, -2), interval2: (-3, -2)) == \"NO\")"}
{"name": "HumanEval_1_separate_paren_groups", "language": "swift", "prompt": "\n/// Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n/// separate those group into separate strings and return the array of those.\n/// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n/// Ignore any spaces in the input string.\n/// >>> separate_paren_groups(paren_string: \"( ) (( )) (( )( ))\")\n/// [\"()\", \"(())\", \"(()())\"]\nfunc separate_paren_groups(paren_string: String) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(separate_paren_groups(paren_string: \"(()()) ((())) () ((())()())\") == [\"(()())\", \"((()))\", \"()\", \"((())()())\"])\nassert(separate_paren_groups(paren_string: \"() (()) ((())) (((())))\") == [\"()\", \"(())\", \"((()))\", \"(((())))\"])\nassert(separate_paren_groups(paren_string: \"(()(())((())))\") == [\"(()(())((())))\"])\nassert(separate_paren_groups(paren_string: \"( ) (( )) (( )( ))\") == [\"()\", \"(())\", \"(()())\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_1_separate_paren_groups", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(separate_paren_groups(paren_string: \"(()()) ((())) () ((())()())\") == [\"(()())\", \"((()))\", \"()\", \"((())()())\"])\nassert(separate_paren_groups(paren_string: \"() (()) ((())) (((())))\") == [\"()\", \"(())\", \"((()))\", \"(((())))\"])\nassert(separate_paren_groups(paren_string: \"(()(())((())))\") == [\"(()(())((())))\"])\nassert(separate_paren_groups(paren_string: \"( ) (( )) (( )( ))\") == [\"()\", \"(())\", \"(()())\"])"}
{"name": "HumanEval_152_compare", "language": "swift", "prompt": "\n/// I think we all remember that feeling when the result of some long-awaited\n/// event is finally known. The feelings and thoughts you have at that moment are\n/// definitely worth noting down and comparing.\n/// Your task is to determine if a person correctly guessed the results of a number of matches.\n/// You are given two arrays of scores and guesses of equal length, where each index shows a match. \n/// Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n/// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n/// example:\n/// >>> compare(game: [1, 2, 3, 4, 5, 1], guess: [1, 2, 3, 4, 2, -2])\n/// [0, 0, 0, 0, 3, 3]\n/// >>> compare(game: [0, 5, 0, 0, 0, 4], guess: [4, 1, 1, 0, 0, -2])\n/// [4, 4, 1, 0, 0, 6]\nfunc compare(game: [Int], guess: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(compare(game: [1, 2, 3, 4, 5, 1], guess: [1, 2, 3, 4, 2, -2]) == [0, 0, 0, 0, 3, 3])\nassert(compare(game: [0, 0, 0, 0, 0, 0], guess: [0, 0, 0, 0, 0, 0]) == [0, 0, 0, 0, 0, 0])\nassert(compare(game: [1, 2, 3], guess: [-1, -2, -3]) == [2, 4, 6])\nassert(compare(game: [1, 2, 3, 5], guess: [-1, 2, 3, 4]) == [2, 0, 0, 1])", "stop_tokens": ["\n}"], "task_id": "HumanEval_152_compare", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(compare(game: [1, 2, 3, 4, 5, 1], guess: [1, 2, 3, 4, 2, -2]) == [0, 0, 0, 0, 3, 3])\nassert(compare(game: [0, 0, 0, 0, 0, 0], guess: [0, 0, 0, 0, 0, 0]) == [0, 0, 0, 0, 0, 0])\nassert(compare(game: [1, 2, 3], guess: [-1, -2, -3]) == [2, 4, 6])\nassert(compare(game: [1, 2, 3, 5], guess: [-1, 2, 3, 4]) == [2, 0, 0, 1])"}
{"name": "HumanEval_83_starts_one_ends", "language": "swift", "prompt": "\n/// Given a positive integer n, return the count of the numbers of n-digit\n/// positive integers that start or end with 1.\nfunc starts_one_ends(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(starts_one_ends(n: 1) == 1)\nassert(starts_one_ends(n: 2) == 18)\nassert(starts_one_ends(n: 3) == 180)\nassert(starts_one_ends(n: 4) == 1800)\nassert(starts_one_ends(n: 5) == 18000)", "stop_tokens": ["\n}"], "task_id": "HumanEval_83_starts_one_ends", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(starts_one_ends(n: 1) == 1)\nassert(starts_one_ends(n: 2) == 18)\nassert(starts_one_ends(n: 3) == 180)\nassert(starts_one_ends(n: 4) == 1800)\nassert(starts_one_ends(n: 5) == 18000)"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "swift", "prompt": "\n/// Create a function that returns true if the last character\n/// of a given string is an alphabetical character and is not\n/// a part of a word, and false otherwise.\n/// Note: \"word\" is a group of characters separated by space.\n/// Examples:\n/// >>> check_if_last_char_is_a_letter(txt: \"apple pie\")\n/// false\n/// >>> check_if_last_char_is_a_letter(txt: \"apple pi e\")\n/// true\n/// >>> check_if_last_char_is_a_letter(txt: \"apple pi e \")\n/// false\n/// >>> check_if_last_char_is_a_letter(txt: \"\")\n/// false\nfunc check_if_last_char_is_a_letter(txt: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(check_if_last_char_is_a_letter(txt: \"apple\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"apple pi e\") == true)\nassert(check_if_last_char_is_a_letter(txt: \"eeeee\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"A\") == true)\nassert(check_if_last_char_is_a_letter(txt: \"Pumpkin pie \") == false)\nassert(check_if_last_char_is_a_letter(txt: \"Pumpkin pie 1\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"eeeee e \") == false)\nassert(check_if_last_char_is_a_letter(txt: \"apple pie\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"apple pi e \") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(check_if_last_char_is_a_letter(txt: \"apple\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"apple pi e\") == true)\nassert(check_if_last_char_is_a_letter(txt: \"eeeee\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"A\") == true)\nassert(check_if_last_char_is_a_letter(txt: \"Pumpkin pie \") == false)\nassert(check_if_last_char_is_a_letter(txt: \"Pumpkin pie 1\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"eeeee e \") == false)\nassert(check_if_last_char_is_a_letter(txt: \"apple pie\") == false)\nassert(check_if_last_char_is_a_letter(txt: \"apple pi e \") == false)"}
{"name": "HumanEval_124_valid_date", "language": "swift", "prompt": "\n/// You have to write a function which validates a given date string and\n/// returns true if the date is valid otherwise false.\n/// The date is valid if all of the following rules are satisfied:\n/// 1. The date string is not empty.\n/// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n/// 3. The months should not be less than 1 or higher than 12.\n/// 4. The date should be in the format: mm-dd-yyyy\n/// >>> valid_date(date: \"03-11-2000\")\n/// true\n/// >>> valid_date(date: \"15-01-2012\")\n/// false\n/// >>> valid_date(date: \"04-0-2040\")\n/// false\n/// >>> valid_date(date: \"06-04-2020\")\n/// true\n/// >>> valid_date(date: \"06/04/2020\")\n/// false\nfunc valid_date(date: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(valid_date(date: \"03-11-2000\") == true)\nassert(valid_date(date: \"15-01-2012\") == false)\nassert(valid_date(date: \"04-0-2040\") == false)\nassert(valid_date(date: \"06-04-2020\") == true)\nassert(valid_date(date: \"01-01-2007\") == true)\nassert(valid_date(date: \"03-32-2011\") == false)\nassert(valid_date(date: \"\") == false)\nassert(valid_date(date: \"04-31-3000\") == false)\nassert(valid_date(date: \"06-06-2005\") == true)\nassert(valid_date(date: \"21-31-2000\") == false)\nassert(valid_date(date: \"04-12-2003\") == true)\nassert(valid_date(date: \"04122003\") == false)\nassert(valid_date(date: \"20030412\") == false)\nassert(valid_date(date: \"2003-04\") == false)\nassert(valid_date(date: \"2003-04-12\") == false)\nassert(valid_date(date: \"04-2003\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_124_valid_date", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(valid_date(date: \"03-11-2000\") == true)\nassert(valid_date(date: \"15-01-2012\") == false)\nassert(valid_date(date: \"04-0-2040\") == false)\nassert(valid_date(date: \"06-04-2020\") == true)\nassert(valid_date(date: \"01-01-2007\") == true)\nassert(valid_date(date: \"03-32-2011\") == false)\nassert(valid_date(date: \"\") == false)\nassert(valid_date(date: \"04-31-3000\") == false)\nassert(valid_date(date: \"06-06-2005\") == true)\nassert(valid_date(date: \"21-31-2000\") == false)\nassert(valid_date(date: \"04-12-2003\") == true)\nassert(valid_date(date: \"04122003\") == false)\nassert(valid_date(date: \"20030412\") == false)\nassert(valid_date(date: \"2003-04\") == false)\nassert(valid_date(date: \"2003-04-12\") == false)\nassert(valid_date(date: \"04-2003\") == false)"}
{"name": "HumanEval_108_count_nums", "language": "swift", "prompt": "\n/// Write a function count_nums which takes an array of integers and returns\n/// the number of elements which has a sum of digits > 0.\n/// If a number is negative, then its first signed digit will be negative:\n/// e.g. -123 has signed digits -1, 2, and 3.\n/// >>> count_nums(arr: [] as [Int])\n/// 0\n/// >>> count_nums(arr: [-1, 11, -11])\n/// 1\n/// >>> count_nums(arr: [1, 1, 2])\n/// 3\nfunc count_nums(arr: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_nums(arr: [] as [Int]) == 0)\nassert(count_nums(arr: [-1, -2, 0]) == 0)\nassert(count_nums(arr: [1, 1, 2, -2, 3, 4, 5]) == 6)\nassert(count_nums(arr: [1, 6, 9, -6, 0, 1, 5]) == 5)\nassert(count_nums(arr: [1, 100, 98, -7, 1, -1]) == 4)\nassert(count_nums(arr: [12, 23, 34, -45, -56, 0]) == 5)\nassert(count_nums(arr: [0, 1]) == 1)\nassert(count_nums(arr: [1]) == 1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_108_count_nums", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_nums(arr: [] as [Int]) == 0)\nassert(count_nums(arr: [-1, -2, 0]) == 0)\nassert(count_nums(arr: [1, 1, 2, -2, 3, 4, 5]) == 6)\nassert(count_nums(arr: [1, 6, 9, -6, 0, 1, 5]) == 5)\nassert(count_nums(arr: [1, 100, 98, -7, 1, -1]) == 4)\nassert(count_nums(arr: [12, 23, 34, -45, -56, 0]) == 5)\nassert(count_nums(arr: [0, 1]) == 1)\nassert(count_nums(arr: [1]) == 1)"}
{"name": "HumanEval_86_anti_shuffle", "language": "swift", "prompt": "\n/// Write a function that takes a string and returns an ordered version of it.\n/// Ordered version of string, is a string where all words (separated by space)\n/// are replaced by a new word where all the characters arranged in\n/// ascending order based on ascii value.\n/// Note: You should keep the order of words and blank spaces in the sentence.\n/// For example:\n/// >>> anti_shuffle(s: \"Hi\")\n/// \"Hi\"\n/// >>> anti_shuffle(s: \"hello\")\n/// \"ehllo\"\n/// >>> anti_shuffle(s: \"Hello World!!!\")\n/// \"Hello !!!Wdlor\"\nfunc anti_shuffle(s: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(anti_shuffle(s: \"Hi\") == \"Hi\")\nassert(anti_shuffle(s: \"hello\") == \"ehllo\")\nassert(anti_shuffle(s: \"number\") == \"bemnru\")\nassert(anti_shuffle(s: \"abcd\") == \"abcd\")\nassert(anti_shuffle(s: \"Hello World!!!\") == \"Hello !!!Wdlor\")\nassert(anti_shuffle(s: \"\") == \"\")\nassert(anti_shuffle(s: \"Hi. My name is Mister Robot. How are you?\") == \".Hi My aemn is Meirst .Rboot How aer ?ouy\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_86_anti_shuffle", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(anti_shuffle(s: \"Hi\") == \"Hi\")\nassert(anti_shuffle(s: \"hello\") == \"ehllo\")\nassert(anti_shuffle(s: \"number\") == \"bemnru\")\nassert(anti_shuffle(s: \"abcd\") == \"abcd\")\nassert(anti_shuffle(s: \"Hello World!!!\") == \"Hello !!!Wdlor\")\nassert(anti_shuffle(s: \"\") == \"\")\nassert(anti_shuffle(s: \"Hi. My name is Mister Robot. How are you?\") == \".Hi My aemn is Meirst .Rboot How aer ?ouy\")"}
{"name": "HumanEval_48_is_palindrome", "language": "swift", "prompt": "\n/// Checks if given string is a palindrome\n/// >>> is_palindrome(text: \"\")\n/// true\n/// >>> is_palindrome(text: \"aba\")\n/// true\n/// >>> is_palindrome(text: \"aaaaa\")\n/// true\n/// >>> is_palindrome(text: \"zbcd\")\n/// false\nfunc is_palindrome(text: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_palindrome(text: \"\") == true)\nassert(is_palindrome(text: \"aba\") == true)\nassert(is_palindrome(text: \"aaaaa\") == true)\nassert(is_palindrome(text: \"zbcd\") == false)\nassert(is_palindrome(text: \"xywyx\") == true)\nassert(is_palindrome(text: \"xywyz\") == false)\nassert(is_palindrome(text: \"xywzx\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_48_is_palindrome", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_palindrome(text: \"\") == true)\nassert(is_palindrome(text: \"aba\") == true)\nassert(is_palindrome(text: \"aaaaa\") == true)\nassert(is_palindrome(text: \"zbcd\") == false)\nassert(is_palindrome(text: \"xywyx\") == true)\nassert(is_palindrome(text: \"xywyz\") == false)\nassert(is_palindrome(text: \"xywzx\") == false)"}
{"name": "HumanEval_118_get_closest_vowel", "language": "swift", "prompt": "\n/// You are given a word. Your task is to find the closest vowel that stands between \n/// two consonants from the right side of the word (case sensitive).\n/// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n/// find any vowel met the above condition. \n/// You may assume that the given string contains English letter only.\n/// Example:\n/// >>> get_closest_vowel(word: \"yogurt\")\n/// \"u\"\n/// >>> get_closest_vowel(word: \"FULL\")\n/// \"U\"\n/// >>> get_closest_vowel(word: \"quick\")\n/// \"\"\n/// >>> get_closest_vowel(word: \"ab\")\n/// \"\"\nfunc get_closest_vowel(word: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_closest_vowel(word: \"yogurt\") == \"u\")\nassert(get_closest_vowel(word: \"full\") == \"u\")\nassert(get_closest_vowel(word: \"easy\") == \"\")\nassert(get_closest_vowel(word: \"eAsy\") == \"\")\nassert(get_closest_vowel(word: \"ali\") == \"\")\nassert(get_closest_vowel(word: \"bad\") == \"a\")\nassert(get_closest_vowel(word: \"most\") == \"o\")\nassert(get_closest_vowel(word: \"ab\") == \"\")\nassert(get_closest_vowel(word: \"ba\") == \"\")\nassert(get_closest_vowel(word: \"quick\") == \"\")\nassert(get_closest_vowel(word: \"anime\") == \"i\")\nassert(get_closest_vowel(word: \"Asia\") == \"\")\nassert(get_closest_vowel(word: \"Above\") == \"o\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_118_get_closest_vowel", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_closest_vowel(word: \"yogurt\") == \"u\")\nassert(get_closest_vowel(word: \"full\") == \"u\")\nassert(get_closest_vowel(word: \"easy\") == \"\")\nassert(get_closest_vowel(word: \"eAsy\") == \"\")\nassert(get_closest_vowel(word: \"ali\") == \"\")\nassert(get_closest_vowel(word: \"bad\") == \"a\")\nassert(get_closest_vowel(word: \"most\") == \"o\")\nassert(get_closest_vowel(word: \"ab\") == \"\")\nassert(get_closest_vowel(word: \"ba\") == \"\")\nassert(get_closest_vowel(word: \"quick\") == \"\")\nassert(get_closest_vowel(word: \"anime\") == \"i\")\nassert(get_closest_vowel(word: \"Asia\") == \"\")\nassert(get_closest_vowel(word: \"Above\") == \"o\")"}
{"name": "HumanEval_31_is_prime", "language": "swift", "prompt": "\n/// Return true if a given number is prime, and false otherwise.\n/// >>> is_prime(n: 6)\n/// false\n/// >>> is_prime(n: 101)\n/// true\n/// >>> is_prime(n: 11)\n/// true\n/// >>> is_prime(n: 13441)\n/// true\n/// >>> is_prime(n: 61)\n/// true\n/// >>> is_prime(n: 4)\n/// false\n/// >>> is_prime(n: 1)\n/// false\nfunc is_prime(n: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_prime(n: 6) == false)\nassert(is_prime(n: 101) == true)\nassert(is_prime(n: 11) == true)\nassert(is_prime(n: 13441) == true)\nassert(is_prime(n: 61) == true)\nassert(is_prime(n: 4) == false)\nassert(is_prime(n: 1) == false)\nassert(is_prime(n: 5) == true)\nassert(is_prime(n: 11) == true)\nassert(is_prime(n: 17) == true)\nassert(is_prime(n: 85) == false)\nassert(is_prime(n: 77) == false)\nassert(is_prime(n: 255379) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_31_is_prime", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_prime(n: 6) == false)\nassert(is_prime(n: 101) == true)\nassert(is_prime(n: 11) == true)\nassert(is_prime(n: 13441) == true)\nassert(is_prime(n: 61) == true)\nassert(is_prime(n: 4) == false)\nassert(is_prime(n: 1) == false)\nassert(is_prime(n: 5) == true)\nassert(is_prime(n: 11) == true)\nassert(is_prime(n: 17) == true)\nassert(is_prime(n: 85) == false)\nassert(is_prime(n: 77) == false)\nassert(is_prime(n: 255379) == false)"}
{"name": "HumanEval_144_simplify", "language": "swift", "prompt": "\n/// Your task is to implement a function that will simplify the expression\n/// x * n. The function returns true if x * n evaluates to a whole number and false\n/// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n/// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n/// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n/// >>> simplify(x: \"1/5\", n: \"5/1\")\n/// true\n/// >>> simplify(x: \"1/6\", n: \"2/1\")\n/// false\n/// >>> simplify(x: \"7/10\", n: \"10/2\")\n/// false\nfunc simplify(x: String, n: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(simplify(x: \"1/5\", n: \"5/1\") == true)\nassert(simplify(x: \"1/6\", n: \"2/1\") == false)\nassert(simplify(x: \"5/1\", n: \"3/1\") == true)\nassert(simplify(x: \"7/10\", n: \"10/2\") == false)\nassert(simplify(x: \"2/10\", n: \"50/10\") == true)\nassert(simplify(x: \"7/2\", n: \"4/2\") == true)\nassert(simplify(x: \"11/6\", n: \"6/1\") == true)\nassert(simplify(x: \"2/3\", n: \"5/2\") == false)\nassert(simplify(x: \"5/2\", n: \"3/5\") == false)\nassert(simplify(x: \"2/4\", n: \"8/4\") == true)\nassert(simplify(x: \"2/4\", n: \"4/2\") == true)\nassert(simplify(x: \"1/5\", n: \"5/1\") == true)\nassert(simplify(x: \"1/5\", n: \"1/5\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_144_simplify", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(simplify(x: \"1/5\", n: \"5/1\") == true)\nassert(simplify(x: \"1/6\", n: \"2/1\") == false)\nassert(simplify(x: \"5/1\", n: \"3/1\") == true)\nassert(simplify(x: \"7/10\", n: \"10/2\") == false)\nassert(simplify(x: \"2/10\", n: \"50/10\") == true)\nassert(simplify(x: \"7/2\", n: \"4/2\") == true)\nassert(simplify(x: \"11/6\", n: \"6/1\") == true)\nassert(simplify(x: \"2/3\", n: \"5/2\") == false)\nassert(simplify(x: \"5/2\", n: \"3/5\") == false)\nassert(simplify(x: \"2/4\", n: \"8/4\") == true)\nassert(simplify(x: \"2/4\", n: \"4/2\") == true)\nassert(simplify(x: \"1/5\", n: \"5/1\") == true)\nassert(simplify(x: \"1/5\", n: \"1/5\") == false)"}
{"name": "HumanEval_78_hex_key", "language": "swift", "prompt": "\n/// You have been tasked to write a function that receives \n/// a hexadecimal number as a string and counts the number of hexadecimal \n/// digits that are primes (prime number, or a prime, is a natural number \n/// greater than 1 that is not a product of two smaller natural numbers).\n/// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n/// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n/// So you have to determine a number of the following digits: 2, 3, 5, 7, \n/// B (=decimal 11), D (=decimal 13).\n/// Note: you may assume the input is always correct or empty string, \n/// and symbols A,B,C,D,E,F are always uppercase.\n/// Examples:\n/// >>> hex_key(num: \"AB\")\n/// 1\n/// >>> hex_key(num: \"1077E\")\n/// 2\n/// >>> hex_key(num: \"ABED1A33\")\n/// 4\n/// >>> hex_key(num: \"123456789ABCDEF0\")\n/// 6\n/// >>> hex_key(num: \"2020\")\n/// 2\nfunc hex_key(num: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(hex_key(num: \"AB\") == 1)\nassert(hex_key(num: \"1077E\") == 2)\nassert(hex_key(num: \"ABED1A33\") == 4)\nassert(hex_key(num: \"2020\") == 2)\nassert(hex_key(num: \"123456789ABCDEF0\") == 6)\nassert(hex_key(num: \"112233445566778899AABBCCDDEEFF00\") == 12)", "stop_tokens": ["\n}"], "task_id": "HumanEval_78_hex_key", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(hex_key(num: \"AB\") == 1)\nassert(hex_key(num: \"1077E\") == 2)\nassert(hex_key(num: \"ABED1A33\") == 4)\nassert(hex_key(num: \"2020\") == 2)\nassert(hex_key(num: \"123456789ABCDEF0\") == 6)\nassert(hex_key(num: \"112233445566778899AABBCCDDEEFF00\") == 12)"}
{"name": "HumanEval_143_words_in_sentence", "language": "swift", "prompt": "\n/// You are given a string representing a sentence,\n/// the sentence contains some words separated by a space,\n/// and you have to return a string that contains the words from the original sentence,\n/// whose lengths are prime numbers,\n/// the order of the words in the new string should be the same as the original one.\n/// Example 1:\n/// >>> words_in_sentence(sentence: \"This is a test\")\n/// \"is\"\n/// Example 2:\n/// >>> words_in_sentence(sentence: \"lets go for swimming\")\n/// \"go for\"\n/// Constraints:\n/// * 1 <= len(sentence) <= 100\n/// * sentence contains only letters\nfunc words_in_sentence(sentence: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(words_in_sentence(sentence: \"This is a test\") == \"is\")\nassert(words_in_sentence(sentence: \"lets go for swimming\") == \"go for\")\nassert(words_in_sentence(sentence: \"there is no place available here\") == \"there is no place\")\nassert(words_in_sentence(sentence: \"Hi I am Hussein\") == \"Hi am Hussein\")\nassert(words_in_sentence(sentence: \"go for it\") == \"go for it\")\nassert(words_in_sentence(sentence: \"here\") == \"\")\nassert(words_in_sentence(sentence: \"here is\") == \"is\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_143_words_in_sentence", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(words_in_sentence(sentence: \"This is a test\") == \"is\")\nassert(words_in_sentence(sentence: \"lets go for swimming\") == \"go for\")\nassert(words_in_sentence(sentence: \"there is no place available here\") == \"there is no place\")\nassert(words_in_sentence(sentence: \"Hi I am Hussein\") == \"Hi am Hussein\")\nassert(words_in_sentence(sentence: \"go for it\") == \"go for it\")\nassert(words_in_sentence(sentence: \"here\") == \"\")\nassert(words_in_sentence(sentence: \"here is\") == \"is\")"}
{"name": "HumanEval_111_histogram", "language": "swift", "prompt": "\n/// Given a string representing a space separated lowercase letters, return a dictionary\n/// of the letter with the most repetition and containing the corresponding count.\n/// If several letters have the same occurrence, return all of them.\n/// Example:\n/// >>> histogram(test: \"a b c\")\n/// [\"a\" : 1, \"b\" : 1, \"c\" : 1]\n/// >>> histogram(test: \"a b b a\")\n/// [\"a\" : 2, \"b\" : 2]\n/// >>> histogram(test: \"a b c a b\")\n/// [\"a\" : 2, \"b\" : 2]\n/// >>> histogram(test: \"b b b b a\")\n/// [\"b\" : 4]\n/// >>> histogram(test: \"\")\n/// [:] as [String : Int]\nfunc histogram(test: String) -> [String : Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(histogram(test: \"a b b a\") == [\"a\" : 2, \"b\" : 2])\nassert(histogram(test: \"a b c a b\") == [\"a\" : 2, \"b\" : 2])\nassert(histogram(test: \"a b c d g\") == [\"a\" : 1, \"b\" : 1, \"c\" : 1, \"d\" : 1, \"g\" : 1])\nassert(histogram(test: \"r t g\") == [\"r\" : 1, \"t\" : 1, \"g\" : 1])\nassert(histogram(test: \"b b b b a\") == [\"b\" : 4])\nassert(histogram(test: \"r t g\") == [\"r\" : 1, \"t\" : 1, \"g\" : 1])\nassert(histogram(test: \"\") == [:] as [String : Int])\nassert(histogram(test: \"a\") == [\"a\" : 1])", "stop_tokens": ["\n}"], "task_id": "HumanEval_111_histogram", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(histogram(test: \"a b b a\") == [\"a\" : 2, \"b\" : 2])\nassert(histogram(test: \"a b c a b\") == [\"a\" : 2, \"b\" : 2])\nassert(histogram(test: \"a b c d g\") == [\"a\" : 1, \"b\" : 1, \"c\" : 1, \"d\" : 1, \"g\" : 1])\nassert(histogram(test: \"r t g\") == [\"r\" : 1, \"t\" : 1, \"g\" : 1])\nassert(histogram(test: \"b b b b a\") == [\"b\" : 4])\nassert(histogram(test: \"r t g\") == [\"r\" : 1, \"t\" : 1, \"g\" : 1])\nassert(histogram(test: \"\") == [:] as [String : Int])\nassert(histogram(test: \"a\") == [\"a\" : 1])"}
{"name": "HumanEval_87_get_row", "language": "swift", "prompt": "\n/// You are given a 2 dimensional data, as a nested arrays,\n/// which is similar to matrix, however, unlike matrices,\n/// each row may contain a different number of columns.\n/// Given lst, and integer x, find integers x in the array,\n/// and return array of tuples, [(x1, y1), (x2, y2) ...] such that\n/// each tuple is a coordinate - (row, columns), starting with 0.\n/// Sort coordinates initially by rows in ascending order.\n/// Also, sort coordinates of the row by columns in descending order.\n/// Examples:\n/// >>> get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], x: 1)\n/// [(0, 0), (1, 4), (1, 0), (2, 5), (2, 0)]\n/// >>> get_row(lst: [] as [[Int]], x: 1)\n/// [] as [(Int, Int)]\n/// >>> get_row(lst: [[] as [Int], [1], [1, 2, 3]], x: 3)\n/// [(2, 2)]\nfunc get_row(lst: [[Int]], x: Int) -> [(Int, Int)] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], x: 1) == [(0, 0), (1, 4), (1, 0), (2, 5), (2, 0)])\nassert(get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6]], x: 2) == [(0, 1), (1, 1), (2, 1), (3, 1), (4, 1), (5, 1)])\nassert(get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 1, 3, 4, 5, 6], [1, 2, 1, 4, 5, 6], [1, 2, 3, 1, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], x: 1) == [(0, 0), (1, 0), (2, 1), (2, 0), (3, 2), (3, 0), (4, 3), (4, 0), (5, 4), (5, 0), (6, 5), (6, 0)])\nassert(get_row(lst: [] as [[Int]], x: 1) == [] as [(Int, Int)])\nassert(get_row(lst: [[1]], x: 2) == [] as [(Int, Int)])\nassert(get_row(lst: [[] as [Int], [1], [1, 2, 3]], x: 3) == [(2, 2)])", "stop_tokens": ["\n}"], "task_id": "HumanEval_87_get_row", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], x: 1) == [(0, 0), (1, 4), (1, 0), (2, 5), (2, 0)])\nassert(get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6]], x: 2) == [(0, 1), (1, 1), (2, 1), (3, 1), (4, 1), (5, 1)])\nassert(get_row(lst: [[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 1, 3, 4, 5, 6], [1, 2, 1, 4, 5, 6], [1, 2, 3, 1, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], x: 1) == [(0, 0), (1, 0), (2, 1), (2, 0), (3, 2), (3, 0), (4, 3), (4, 0), (5, 4), (5, 0), (6, 5), (6, 0)])\nassert(get_row(lst: [] as [[Int]], x: 1) == [] as [(Int, Int)])\nassert(get_row(lst: [[1]], x: 2) == [] as [(Int, Int)])\nassert(get_row(lst: [[] as [Int], [1], [1, 2, 3]], x: 3) == [(2, 2)])"}
{"name": "HumanEval_123_get_odd_collatz", "language": "swift", "prompt": "\n/// Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n/// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n/// as follows: start with any positive integer n. Then each term is obtained from the \n/// previous term as follows: if the previous term is even, the next term is one half of \n/// the previous term. If the previous term is odd, the next term is 3 times the previous\n/// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n/// Note: \n/// 1. Collatz(1) is [1].\n/// 2. returned array sorted in increasing order.\n/// For example:\n/// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n/// >>> get_odd_collatz(n: 5)\n/// [1, 5]\nfunc get_odd_collatz(n: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_odd_collatz(n: 14) == [1, 5, 7, 11, 13, 17])\nassert(get_odd_collatz(n: 5) == [1, 5])\nassert(get_odd_collatz(n: 12) == [1, 3, 5])\nassert(get_odd_collatz(n: 1) == [1])", "stop_tokens": ["\n}"], "task_id": "HumanEval_123_get_odd_collatz", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_odd_collatz(n: 14) == [1, 5, 7, 11, 13, 17])\nassert(get_odd_collatz(n: 5) == [1, 5])\nassert(get_odd_collatz(n: 12) == [1, 3, 5])\nassert(get_odd_collatz(n: 1) == [1])"}
{"name": "HumanEval_135_can_arrange", "language": "swift", "prompt": "\n/// Create a function which returns the largest index of an element which\n/// is not greater than or equal to the element immediately preceding it. If\n/// no such element exists then return -1. The given array will not contain\n/// duplicate values.\n/// Examples:\n/// >>> can_arrange(arr: [1, 2, 4, 3, 5])\n/// 3\n/// >>> can_arrange(arr: [1, 2, 3])\n/// -1\nfunc can_arrange(arr: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(can_arrange(arr: [1, 2, 4, 3, 5]) == 3)\nassert(can_arrange(arr: [1, 2, 4, 5]) == -1)\nassert(can_arrange(arr: [1, 4, 2, 5, 6, 7, 8, 9, 10]) == 2)\nassert(can_arrange(arr: [4, 8, 5, 7, 3]) == 4)\nassert(can_arrange(arr: [] as [Int]) == -1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_135_can_arrange", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(can_arrange(arr: [1, 2, 4, 3, 5]) == 3)\nassert(can_arrange(arr: [1, 2, 4, 5]) == -1)\nassert(can_arrange(arr: [1, 4, 2, 5, 6, 7, 8, 9, 10]) == 2)\nassert(can_arrange(arr: [4, 8, 5, 7, 3]) == 4)\nassert(can_arrange(arr: [] as [Int]) == -1)"}
{"name": "HumanEval_19_sort_numbers", "language": "swift", "prompt": "\n/// Input is a space-delimited string of numberals from 'zero' to 'nine'.\n/// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n/// Return the string with numbers sorted from smallest to largest\n/// >>> sort_numbers(numbers: \"three one five\")\n/// \"one three five\"\nfunc sort_numbers(numbers: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_numbers(numbers: \"\") == \"\")\nassert(sort_numbers(numbers: \"three\") == \"three\")\nassert(sort_numbers(numbers: \"three five nine\") == \"three five nine\")\nassert(sort_numbers(numbers: \"five zero four seven nine eight\") == \"zero four five seven eight nine\")\nassert(sort_numbers(numbers: \"six five four three two one zero\") == \"zero one two three four five six\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_19_sort_numbers", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_numbers(numbers: \"\") == \"\")\nassert(sort_numbers(numbers: \"three\") == \"three\")\nassert(sort_numbers(numbers: \"three five nine\") == \"three five nine\")\nassert(sort_numbers(numbers: \"five zero four seven nine eight\") == \"zero four five seven eight nine\")\nassert(sort_numbers(numbers: \"six five four three two one zero\") == \"zero one two three four five six\")"}
{"name": "HumanEval_65_circular_shift", "language": "swift", "prompt": "\n/// Circular shift the digits of the integer x, shift the digits right by shift\n/// and return the result as a string.\n/// If shift > number of digits, return digits reversed.\n/// >>> circular_shift(x: 12, shift: 1)\n/// \"21\"\n/// >>> circular_shift(x: 12, shift: 2)\n/// \"12\"\nfunc circular_shift(x: Int, shift: Int) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(circular_shift(x: 100, shift: 2) == \"001\")\nassert(circular_shift(x: 12, shift: 2) == \"12\")\nassert(circular_shift(x: 97, shift: 8) == \"79\")\nassert(circular_shift(x: 12, shift: 1) == \"21\")\nassert(circular_shift(x: 11, shift: 101) == \"11\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_65_circular_shift", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(circular_shift(x: 100, shift: 2) == \"001\")\nassert(circular_shift(x: 12, shift: 2) == \"12\")\nassert(circular_shift(x: 97, shift: 8) == \"79\")\nassert(circular_shift(x: 12, shift: 1) == \"21\")\nassert(circular_shift(x: 11, shift: 101) == \"11\")"}
{"name": "HumanEval_94_skjkasdkd", "language": "swift", "prompt": "\n/// You are given an array of integers.\n/// You need to find the largest prime value and return the sum of its digits.\n/// Examples:\n/// >>> skjkasdkd(lst: [0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n/// 10\n/// >>> skjkasdkd(lst: [1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n/// 25\n/// >>> skjkasdkd(lst: [1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n/// 13\n/// >>> skjkasdkd(lst: [0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n/// 11\n/// >>> skjkasdkd(lst: [0, 81, 12, 3, 1, 21])\n/// 3\n/// >>> skjkasdkd(lst: [0, 8, 1, 2, 1, 7])\n/// 7\nfunc skjkasdkd(lst: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(skjkasdkd(lst: [0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3]) == 10)\nassert(skjkasdkd(lst: [1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1]) == 25)\nassert(skjkasdkd(lst: [1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3]) == 13)\nassert(skjkasdkd(lst: [0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6]) == 11)\nassert(skjkasdkd(lst: [0, 81, 12, 3, 1, 21]) == 3)\nassert(skjkasdkd(lst: [0, 8, 1, 2, 1, 7]) == 7)\nassert(skjkasdkd(lst: [8191]) == 19)\nassert(skjkasdkd(lst: [8191, 123456, 127, 7]) == 19)\nassert(skjkasdkd(lst: [127, 97, 8192]) == 10)", "stop_tokens": ["\n}"], "task_id": "HumanEval_94_skjkasdkd", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(skjkasdkd(lst: [0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3]) == 10)\nassert(skjkasdkd(lst: [1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1]) == 25)\nassert(skjkasdkd(lst: [1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3]) == 13)\nassert(skjkasdkd(lst: [0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6]) == 11)\nassert(skjkasdkd(lst: [0, 81, 12, 3, 1, 21]) == 3)\nassert(skjkasdkd(lst: [0, 8, 1, 2, 1, 7]) == 7)\nassert(skjkasdkd(lst: [8191]) == 19)\nassert(skjkasdkd(lst: [8191, 123456, 127, 7]) == 19)\nassert(skjkasdkd(lst: [127, 97, 8192]) == 10)"}
{"name": "HumanEval_8_sum_product", "language": "swift", "prompt": "\n/// For a given array of integers, return a tuple consisting of a sum and a product of all the integers in an array.\n/// Empty sum should be equal to 0 and empty product should be equal to 1.\n/// >>> sum_product(numbers: [] as [Int])\n/// (0, 1)\n/// >>> sum_product(numbers: [1, 2, 3, 4])\n/// (10, 24)\nfunc sum_product(numbers: [Int]) -> (Int, Int) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sum_product(numbers: [] as [Int]) == (0, 1))\nassert(sum_product(numbers: [1, 1, 1]) == (3, 1))\nassert(sum_product(numbers: [100, 0]) == (100, 0))\nassert(sum_product(numbers: [3, 5, 7]) == (15, 105))\nassert(sum_product(numbers: [10]) == (10, 10))", "stop_tokens": ["\n}"], "task_id": "HumanEval_8_sum_product", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sum_product(numbers: [] as [Int]) == (0, 1))\nassert(sum_product(numbers: [1, 1, 1]) == (3, 1))\nassert(sum_product(numbers: [100, 0]) == (100, 0))\nassert(sum_product(numbers: [3, 5, 7]) == (15, 105))\nassert(sum_product(numbers: [10]) == (10, 10))"}
{"name": "HumanEval_102_choose_num", "language": "swift", "prompt": "\n/// This function takes two positive numbers x and y and returns the\n/// biggest even integer number that is in the range [x, y] inclusive. If \n/// there's no such number, then the function should return -1.\n/// For example:\n/// >>> choose_num(x: 12, y: 15)\n/// 14\n/// >>> choose_num(x: 13, y: 12)\n/// -1\nfunc choose_num(x: Int, y: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(choose_num(x: 12, y: 15) == 14)\nassert(choose_num(x: 13, y: 12) == -1)\nassert(choose_num(x: 33, y: 12354) == 12354)\nassert(choose_num(x: 5234, y: 5233) == -1)\nassert(choose_num(x: 6, y: 29) == 28)\nassert(choose_num(x: 27, y: 10) == -1)\nassert(choose_num(x: 7, y: 7) == -1)\nassert(choose_num(x: 546, y: 546) == 546)", "stop_tokens": ["\n}"], "task_id": "HumanEval_102_choose_num", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(choose_num(x: 12, y: 15) == 14)\nassert(choose_num(x: 13, y: 12) == -1)\nassert(choose_num(x: 33, y: 12354) == 12354)\nassert(choose_num(x: 5234, y: 5233) == -1)\nassert(choose_num(x: 6, y: 29) == 28)\nassert(choose_num(x: 27, y: 10) == -1)\nassert(choose_num(x: 7, y: 7) == -1)\nassert(choose_num(x: 546, y: 546) == 546)"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "swift", "prompt": "\n/// Create a function that returns a tuple (a, b), where 'a' is\n/// the largest of negative integers, and 'b' is the smallest\n/// of positive integers in an array.\n/// If there is no negative or positive integers, return them as nil.\n/// Examples:\n/// >>> largest_smallest_integers(lst: [2, 4, 1, 3, 5, 7])\n/// (nil, 1)\n/// >>> largest_smallest_integers(lst: [] as [Int])\n/// (nil, nil)\n/// >>> largest_smallest_integers(lst: [0])\n/// (nil, nil)\nfunc largest_smallest_integers(lst: [Int]) -> (Int?, Int?) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(largest_smallest_integers(lst: [2, 4, 1, 3, 5, 7]) == (nil, 1))\nassert(largest_smallest_integers(lst: [2, 4, 1, 3, 5, 7, 0]) == (nil, 1))\nassert(largest_smallest_integers(lst: [1, 3, 2, 4, 5, 6, -2]) == (-2, 1))\nassert(largest_smallest_integers(lst: [4, 5, 3, 6, 2, 7, -7]) == (-7, 2))\nassert(largest_smallest_integers(lst: [7, 3, 8, 4, 9, 2, 5, -9]) == (-9, 2))\nassert(largest_smallest_integers(lst: [] as [Int]) == (nil, nil))\nassert(largest_smallest_integers(lst: [0]) == (nil, nil))\nassert(largest_smallest_integers(lst: [-1, -3, -5, -6]) == (-1, nil))\nassert(largest_smallest_integers(lst: [-1, -3, -5, -6, 0]) == (-1, nil))\nassert(largest_smallest_integers(lst: [-6, -4, -4, -3, 1]) == (-3, 1))\nassert(largest_smallest_integers(lst: [-6, -4, -4, -3, -100, 1]) == (-3, 1))", "stop_tokens": ["\n}"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(largest_smallest_integers(lst: [2, 4, 1, 3, 5, 7]) == (nil, 1))\nassert(largest_smallest_integers(lst: [2, 4, 1, 3, 5, 7, 0]) == (nil, 1))\nassert(largest_smallest_integers(lst: [1, 3, 2, 4, 5, 6, -2]) == (-2, 1))\nassert(largest_smallest_integers(lst: [4, 5, 3, 6, 2, 7, -7]) == (-7, 2))\nassert(largest_smallest_integers(lst: [7, 3, 8, 4, 9, 2, 5, -9]) == (-9, 2))\nassert(largest_smallest_integers(lst: [] as [Int]) == (nil, nil))\nassert(largest_smallest_integers(lst: [0]) == (nil, nil))\nassert(largest_smallest_integers(lst: [-1, -3, -5, -6]) == (-1, nil))\nassert(largest_smallest_integers(lst: [-1, -3, -5, -6, 0]) == (-1, nil))\nassert(largest_smallest_integers(lst: [-6, -4, -4, -3, 1]) == (-3, 1))\nassert(largest_smallest_integers(lst: [-6, -4, -4, -3, -100, 1]) == (-3, 1))"}
{"name": "HumanEval_16_count_distinct_characters", "language": "swift", "prompt": "\n/// Given a string, find out how many distinct characters (regardless of case) does it consist of\n/// >>> count_distinct_characters(string: \"xyzXYZ\")\n/// 3\n/// >>> count_distinct_characters(string: \"Jerry\")\n/// 4\nfunc count_distinct_characters(string: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_distinct_characters(string: \"\") == 0)\nassert(count_distinct_characters(string: \"abcde\") == 5)\nassert(count_distinct_characters(string: \"abcdecadeCADE\") == 5)\nassert(count_distinct_characters(string: \"aaaaAAAAaaaa\") == 1)\nassert(count_distinct_characters(string: \"Jerry jERRY JeRRRY\") == 5)", "stop_tokens": ["\n}"], "task_id": "HumanEval_16_count_distinct_characters", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(count_distinct_characters(string: \"\") == 0)\nassert(count_distinct_characters(string: \"abcde\") == 5)\nassert(count_distinct_characters(string: \"abcdecadeCADE\") == 5)\nassert(count_distinct_characters(string: \"aaaaAAAAaaaa\") == 1)\nassert(count_distinct_characters(string: \"Jerry jERRY JeRRRY\") == 5)"}
{"name": "HumanEval_100_make_a_pile", "language": "swift", "prompt": "\n/// Given a positive integer n, you have to make a pile of n levels of stones.\n/// The first level has n stones.\n/// The number of stones in the next level is:\n/// - the next odd number if n is odd.\n/// - the next even number if n is even.\n/// Return the number of stones in each level in an array, where element at index\n/// i represents the number of stones in the level (i+1).\n/// Examples:\n/// >>> make_a_pile(n: 3)\n/// [3, 5, 7]\nfunc make_a_pile(n: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(make_a_pile(n: 3) == [3, 5, 7])\nassert(make_a_pile(n: 4) == [4, 6, 8, 10])\nassert(make_a_pile(n: 5) == [5, 7, 9, 11, 13])\nassert(make_a_pile(n: 6) == [6, 8, 10, 12, 14, 16])\nassert(make_a_pile(n: 8) == [8, 10, 12, 14, 16, 18, 20, 22])", "stop_tokens": ["\n}"], "task_id": "HumanEval_100_make_a_pile", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(make_a_pile(n: 3) == [3, 5, 7])\nassert(make_a_pile(n: 4) == [4, 6, 8, 10])\nassert(make_a_pile(n: 5) == [5, 7, 9, 11, 13])\nassert(make_a_pile(n: 6) == [6, 8, 10, 12, 14, 16])\nassert(make_a_pile(n: 8) == [8, 10, 12, 14, 16, 18, 20, 22])"}
{"name": "HumanEval_128_prod_signs", "language": "swift", "prompt": "\n/// You are given an array arr of integers and you need to return\n/// sum of magnitudes of integers multiplied by product of all signs\n/// of each number in the array, represented by 1, -1 or 0.\n/// Note: return nil for empty arr.\n/// Example:\n/// >>> prod_signs(arr: [1, 2, 2, -4])\n/// 9\n/// >>> prod_signs(arr: [0, 1])\n/// 0\n/// >>> prod_signs(arr: [] as [Int])\n/// nil\nfunc prod_signs(arr: [Int]) -> Int? {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(prod_signs(arr: [1, 2, 2, -4]) == -9)\nassert(prod_signs(arr: [0, 1]) == 0)\nassert(prod_signs(arr: [1, 1, 1, 2, 3, -1, 1]) == -10)\nassert(prod_signs(arr: [] as [Int]) == nil)\nassert(prod_signs(arr: [2, 4, 1, 2, -1, -1, 9]) == 20)\nassert(prod_signs(arr: [-1, 1, -1, 1]) == 4)\nassert(prod_signs(arr: [-1, 1, 1, 1]) == -4)\nassert(prod_signs(arr: [-1, 1, 1, 0]) == 0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_128_prod_signs", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(prod_signs(arr: [1, 2, 2, -4]) == -9)\nassert(prod_signs(arr: [0, 1]) == 0)\nassert(prod_signs(arr: [1, 1, 1, 2, 3, -1, 1]) == -10)\nassert(prod_signs(arr: [] as [Int]) == nil)\nassert(prod_signs(arr: [2, 4, 1, 2, -1, -1, 9]) == 20)\nassert(prod_signs(arr: [-1, 1, -1, 1]) == 4)\nassert(prod_signs(arr: [-1, 1, 1, 1]) == -4)\nassert(prod_signs(arr: [-1, 1, 1, 0]) == 0)"}
{"name": "HumanEval_114_minSubArraySum", "language": "swift", "prompt": "\n/// Given an array of integers nums, find the minimum sum of any non-empty sub-array\n/// of nums.\n/// Example\n/// >>> minSubArraySum(nums: [2, 3, 4, 1, 2, 4])\n/// 1\n/// >>> minSubArraySum(nums: [-1, -2, -3])\n/// -6\nfunc minSubArraySum(nums: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(minSubArraySum(nums: [2, 3, 4, 1, 2, 4]) == 1)\nassert(minSubArraySum(nums: [-1, -2, -3]) == -6)\nassert(minSubArraySum(nums: [-1, -2, -3, 2, -10]) == -14)\nassert(minSubArraySum(nums: [-9999999999999999]) == -9999999999999999)\nassert(minSubArraySum(nums: [0, 10, 20, 1000000]) == 0)\nassert(minSubArraySum(nums: [-1, -2, -3, 10, -5]) == -6)\nassert(minSubArraySum(nums: [100, -1, -2, -3, 10, -5]) == -6)\nassert(minSubArraySum(nums: [10, 11, 13, 8, 3, 4]) == 3)\nassert(minSubArraySum(nums: [100, -33, 32, -1, 0, -2]) == -33)\nassert(minSubArraySum(nums: [-10]) == -10)\nassert(minSubArraySum(nums: [7]) == 7)\nassert(minSubArraySum(nums: [1, -1]) == -1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_114_minSubArraySum", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(minSubArraySum(nums: [2, 3, 4, 1, 2, 4]) == 1)\nassert(minSubArraySum(nums: [-1, -2, -3]) == -6)\nassert(minSubArraySum(nums: [-1, -2, -3, 2, -10]) == -14)\nassert(minSubArraySum(nums: [-9999999999999999]) == -9999999999999999)\nassert(minSubArraySum(nums: [0, 10, 20, 1000000]) == 0)\nassert(minSubArraySum(nums: [-1, -2, -3, 10, -5]) == -6)\nassert(minSubArraySum(nums: [100, -1, -2, -3, 10, -5]) == -6)\nassert(minSubArraySum(nums: [10, 11, 13, 8, 3, 4]) == 3)\nassert(minSubArraySum(nums: [100, -33, 32, -1, 0, -2]) == -33)\nassert(minSubArraySum(nums: [-10]) == -10)\nassert(minSubArraySum(nums: [7]) == 7)\nassert(minSubArraySum(nums: [1, -1]) == -1)"}
{"name": "HumanEval_15_string_sequence", "language": "swift", "prompt": "\n/// Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n/// >>> string_sequence(n: 0)\n/// \"0\"\n/// >>> string_sequence(n: 5)\n/// \"0 1 2 3 4 5\"\nfunc string_sequence(n: Int) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(string_sequence(n: 0) == \"0\")\nassert(string_sequence(n: 3) == \"0 1 2 3\")\nassert(string_sequence(n: 10) == \"0 1 2 3 4 5 6 7 8 9 10\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_15_string_sequence", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(string_sequence(n: 0) == \"0\")\nassert(string_sequence(n: 3) == \"0 1 2 3\")\nassert(string_sequence(n: 10) == \"0 1 2 3 4 5 6 7 8 9 10\")"}
{"name": "HumanEval_154_cycpattern_check", "language": "swift", "prompt": "\n/// You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n/// >>> cycpattern_check(a: \"abcd\", b: \"abd\")\n/// false\n/// >>> cycpattern_check(a: \"hello\", b: \"ell\")\n/// true\n/// >>> cycpattern_check(a: \"whassup\", b: \"psus\")\n/// false\n/// >>> cycpattern_check(a: \"abab\", b: \"baa\")\n/// true\n/// >>> cycpattern_check(a: \"efef\", b: \"eeff\")\n/// false\n/// >>> cycpattern_check(a: \"himenss\", b: \"simen\")\n/// true\nfunc cycpattern_check(a: String, b: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(cycpattern_check(a: \"xyzw\", b: \"xyw\") == false)\nassert(cycpattern_check(a: \"yello\", b: \"ell\") == true)\nassert(cycpattern_check(a: \"whattup\", b: \"ptut\") == false)\nassert(cycpattern_check(a: \"efef\", b: \"fee\") == true)\nassert(cycpattern_check(a: \"abab\", b: \"aabb\") == false)\nassert(cycpattern_check(a: \"winemtt\", b: \"tinem\") == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_154_cycpattern_check", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(cycpattern_check(a: \"xyzw\", b: \"xyw\") == false)\nassert(cycpattern_check(a: \"yello\", b: \"ell\") == true)\nassert(cycpattern_check(a: \"whattup\", b: \"ptut\") == false)\nassert(cycpattern_check(a: \"efef\", b: \"fee\") == true)\nassert(cycpattern_check(a: \"abab\", b: \"aabb\") == false)\nassert(cycpattern_check(a: \"winemtt\", b: \"tinem\") == true)"}
{"name": "HumanEval_57_monotonic", "language": "swift", "prompt": "\n/// Return true is array elements are monotonically increasing or decreasing.\n/// >>> monotonic(l: [1, 2, 4, 20])\n/// true\n/// >>> monotonic(l: [1, 20, 4, 10])\n/// false\n/// >>> monotonic(l: [4, 1, 0, -10])\n/// true\nfunc monotonic(l: [Int]) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(monotonic(l: [1, 2, 4, 10]) == true)\nassert(monotonic(l: [1, 2, 4, 20]) == true)\nassert(monotonic(l: [1, 20, 4, 10]) == false)\nassert(monotonic(l: [4, 1, 0, -10]) == true)\nassert(monotonic(l: [4, 1, 1, 0]) == true)\nassert(monotonic(l: [1, 2, 3, 2, 5, 60]) == false)\nassert(monotonic(l: [1, 2, 3, 4, 5, 60]) == true)\nassert(monotonic(l: [9, 9, 9, 9]) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_57_monotonic", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(monotonic(l: [1, 2, 4, 10]) == true)\nassert(monotonic(l: [1, 2, 4, 20]) == true)\nassert(monotonic(l: [1, 20, 4, 10]) == false)\nassert(monotonic(l: [4, 1, 0, -10]) == true)\nassert(monotonic(l: [4, 1, 1, 0]) == true)\nassert(monotonic(l: [1, 2, 3, 2, 5, 60]) == false)\nassert(monotonic(l: [1, 2, 3, 4, 5, 60]) == true)\nassert(monotonic(l: [9, 9, 9, 9]) == true)"}
{"name": "HumanEval_12_longest", "language": "swift", "prompt": "\n/// Out of array of strings, return the longest one. Return the first one in case of multiple\n/// strings of the same length. Return nil in case the input array is empty.\n/// >>> longest(strings: [] as [String])\n/// nil\n/// >>> longest(strings: [\"a\", \"b\", \"c\"])\n/// \"a\"\n/// >>> longest(strings: [\"a\", \"bb\", \"ccc\"])\n/// \"ccc\"\nfunc longest(strings: [String]) -> String? {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(longest(strings: [] as [String]) == nil)\nassert(longest(strings: [\"x\", \"y\", \"z\"]) == \"x\")\nassert(longest(strings: [\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"]) == \"zzzz\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_12_longest", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(longest(strings: [] as [String]) == nil)\nassert(longest(strings: [\"x\", \"y\", \"z\"]) == \"x\")\nassert(longest(strings: [\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"]) == \"zzzz\")"}
{"name": "HumanEval_52_below_threshold", "language": "swift", "prompt": "\n/// Return true if all numbers in the array l are below threshold t.\n/// >>> below_threshold(l: [1, 2, 4, 10], t: 100)\n/// true\n/// >>> below_threshold(l: [1, 20, 4, 10], t: 5)\n/// false\nfunc below_threshold(l: [Int], t: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(below_threshold(l: [1, 2, 4, 10], t: 100) == true)\nassert(below_threshold(l: [1, 20, 4, 10], t: 5) == false)\nassert(below_threshold(l: [1, 20, 4, 10], t: 21) == true)\nassert(below_threshold(l: [1, 20, 4, 10], t: 22) == true)\nassert(below_threshold(l: [1, 8, 4, 10], t: 11) == true)\nassert(below_threshold(l: [1, 8, 4, 10], t: 10) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_52_below_threshold", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(below_threshold(l: [1, 2, 4, 10], t: 100) == true)\nassert(below_threshold(l: [1, 20, 4, 10], t: 5) == false)\nassert(below_threshold(l: [1, 20, 4, 10], t: 21) == true)\nassert(below_threshold(l: [1, 20, 4, 10], t: 22) == true)\nassert(below_threshold(l: [1, 8, 4, 10], t: 11) == true)\nassert(below_threshold(l: [1, 8, 4, 10], t: 10) == false)"}
{"name": "HumanEval_75_is_multiply_prime", "language": "swift", "prompt": "\n/// Write a function that returns true if the given number is the multiplication of 3 prime numbers\n/// and false otherwise.\n/// Knowing that (a) is less then 100. \n/// Example:\n/// >>> is_multiply_prime(a: 30)\n/// true\n/// 30 = 2 * 3 * 5\nfunc is_multiply_prime(a: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_multiply_prime(a: 5) == false)\nassert(is_multiply_prime(a: 30) == true)\nassert(is_multiply_prime(a: 8) == true)\nassert(is_multiply_prime(a: 10) == false)\nassert(is_multiply_prime(a: 125) == true)\nassert(is_multiply_prime(a: 105) == true)\nassert(is_multiply_prime(a: 126) == false)\nassert(is_multiply_prime(a: 729) == false)\nassert(is_multiply_prime(a: 891) == false)\nassert(is_multiply_prime(a: 1001) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_75_is_multiply_prime", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(is_multiply_prime(a: 5) == false)\nassert(is_multiply_prime(a: 30) == true)\nassert(is_multiply_prime(a: 8) == true)\nassert(is_multiply_prime(a: 10) == false)\nassert(is_multiply_prime(a: 125) == true)\nassert(is_multiply_prime(a: 105) == true)\nassert(is_multiply_prime(a: 126) == false)\nassert(is_multiply_prime(a: 729) == false)\nassert(is_multiply_prime(a: 891) == false)\nassert(is_multiply_prime(a: 1001) == true)"}
{"name": "HumanEval_30_get_positive", "language": "swift", "prompt": "\n/// Return only positive numbers in the array.\n/// >>> get_positive(l: [-1, 2, -4, 5, 6])\n/// [2, 5, 6]\n/// >>> get_positive(l: [5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n/// [5, 3, 2, 3, 9, 123, 1]\nfunc get_positive(l: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_positive(l: [-1, -2, 4, 5, 6]) == [4, 5, 6])\nassert(get_positive(l: [5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10]) == [5, 3, 2, 3, 3, 9, 123, 1])\nassert(get_positive(l: [-1, -2]) == [] as [Int])\nassert(get_positive(l: [] as [Int]) == [] as [Int])", "stop_tokens": ["\n}"], "task_id": "HumanEval_30_get_positive", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(get_positive(l: [-1, -2, 4, 5, 6]) == [4, 5, 6])\nassert(get_positive(l: [5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10]) == [5, 3, 2, 3, 3, 9, 123, 1])\nassert(get_positive(l: [-1, -2]) == [] as [Int])\nassert(get_positive(l: [] as [Int]) == [] as [Int])"}
{"name": "HumanEval_33_sort_third", "language": "swift", "prompt": "\n/// This function takes an array l and returns an array l' such that\n/// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n/// to the values of the corresponding indicies of l, but sorted.\n/// >>> sort_third(l: [1, 2, 3])\n/// [1, 2, 3]\n/// >>> sort_third(l: [5, 6, 3, 4, 8, 9, 2])\n/// [2, 6, 3, 4, 8, 9, 5]\nfunc sort_third(l: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_third(l: [5, 6, 3, 4, 8, 9, 2]) == [2, 6, 3, 4, 8, 9, 5])\nassert(sort_third(l: [5, 8, 3, 4, 6, 9, 2]) == [2, 8, 3, 4, 6, 9, 5])\nassert(sort_third(l: [5, 6, 9, 4, 8, 3, 2]) == [2, 6, 9, 4, 8, 3, 5])\nassert(sort_third(l: [5, 6, 3, 4, 8, 9, 2, 1]) == [2, 6, 3, 4, 8, 9, 5, 1])", "stop_tokens": ["\n}"], "task_id": "HumanEval_33_sort_third", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_third(l: [5, 6, 3, 4, 8, 9, 2]) == [2, 6, 3, 4, 8, 9, 5])\nassert(sort_third(l: [5, 8, 3, 4, 6, 9, 2]) == [2, 8, 3, 4, 6, 9, 5])\nassert(sort_third(l: [5, 6, 9, 4, 8, 3, 2]) == [2, 6, 9, 4, 8, 3, 5])\nassert(sort_third(l: [5, 6, 3, 4, 8, 9, 2, 1]) == [2, 6, 3, 4, 8, 9, 5, 1])"}
{"name": "HumanEval_6_parse_nested_parens", "language": "swift", "prompt": "\n/// Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n/// For each of the group, output the deepest level of nesting of parentheses.\n/// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n/// >>> parse_nested_parens(paren_string: \"(()()) ((())) () ((())()())\")\n/// [2, 3, 1, 3]\nfunc parse_nested_parens(paren_string: String) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(parse_nested_parens(paren_string: \"(()()) ((())) () ((())()())\") == [2, 3, 1, 3])\nassert(parse_nested_parens(paren_string: \"() (()) ((())) (((())))\") == [1, 2, 3, 4])\nassert(parse_nested_parens(paren_string: \"(()(())((())))\") == [4])", "stop_tokens": ["\n}"], "task_id": "HumanEval_6_parse_nested_parens", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(parse_nested_parens(paren_string: \"(()()) ((())) () ((())()())\") == [2, 3, 1, 3])\nassert(parse_nested_parens(paren_string: \"() (()) ((())) (((())))\") == [1, 2, 3, 4])\nassert(parse_nested_parens(paren_string: \"(()(())((())))\") == [4])"}
{"name": "HumanEval_45_triangle_area", "language": "swift", "prompt": "\n/// Given length of a side and high return area for a triangle.\n/// >>> triangle_area(a: 5, h: 3)\n/// 7.5\nfunc triangle_area(a: Int, h: Int) -> Double {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(triangle_area(a: 5, h: 3) == 7.5)\nassert(triangle_area(a: 2, h: 2) == 2.0)\nassert(triangle_area(a: 10, h: 8) == 40.0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_45_triangle_area", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(triangle_area(a: 5, h: 3) == 7.5)\nassert(triangle_area(a: 2, h: 2) == 2.0)\nassert(triangle_area(a: 10, h: 8) == 40.0)"}
{"name": "HumanEval_97_multiply", "language": "swift", "prompt": "\n/// Complete the function that takes two integers and returns \n/// the product of their unit digits.\n/// Assume the input is always valid.\n/// Examples:\n/// >>> multiply(a: 148, b: 412)\n/// 16\n/// >>> multiply(a: 19, b: 28)\n/// 72\n/// >>> multiply(a: 2020, b: 1851)\n/// 0\n/// >>> multiply(a: 14, b: -15)\n/// 20\nfunc multiply(a: Int, b: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(multiply(a: 148, b: 412) == 16)\nassert(multiply(a: 19, b: 28) == 72)\nassert(multiply(a: 2020, b: 1851) == 0)\nassert(multiply(a: 14, b: -15) == 20)\nassert(multiply(a: 76, b: 67) == 42)\nassert(multiply(a: 17, b: 27) == 49)\nassert(multiply(a: 0, b: 1) == 0)\nassert(multiply(a: 0, b: 0) == 0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_97_multiply", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(multiply(a: 148, b: 412) == 16)\nassert(multiply(a: 19, b: 28) == 72)\nassert(multiply(a: 2020, b: 1851) == 0)\nassert(multiply(a: 14, b: -15) == 20)\nassert(multiply(a: 76, b: 67) == 42)\nassert(multiply(a: 17, b: 27) == 49)\nassert(multiply(a: 0, b: 1) == 0)\nassert(multiply(a: 0, b: 0) == 0)"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "swift", "prompt": "\n/// For a given array of input numbers, calculate Mean Absolute Deviation\n/// around the mean of this dataset.\n/// Mean Absolute Deviation is the average absolute difference between each\n/// element and a centerpoint (mean in this case):\n/// MAD = average | x - x_mean |\n/// >>> mean_absolute_deviation(numbers: [1.0, 2.0, 3.0, 4.0])\n/// 1.0\nfunc mean_absolute_deviation(numbers: [Double]) -> Double {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(mean_absolute_deviation(numbers: [1.0, 2.0]) == 0.5)\nassert(mean_absolute_deviation(numbers: [1.0, 2.0, 3.0, 4.0]) == 1.0)\nassert(mean_absolute_deviation(numbers: [1.0, 2.0, 3.0, 4.0, 5.0]) == 1.2)", "stop_tokens": ["\n}"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(mean_absolute_deviation(numbers: [1.0, 2.0]) == 0.5)\nassert(mean_absolute_deviation(numbers: [1.0, 2.0, 3.0, 4.0]) == 1.0)\nassert(mean_absolute_deviation(numbers: [1.0, 2.0, 3.0, 4.0, 5.0]) == 1.2)"}
{"name": "HumanEval_58_common", "language": "swift", "prompt": "\n/// Return sorted unique common elements for two arrays.\n/// >>> common(l1: [1, 4, 3, 34, 653, 2, 5], l2: [5, 7, 1, 5, 9, 653, 121])\n/// [1, 5, 653]\n/// >>> common(l1: [5, 3, 2, 8], l2: [3, 2])\n/// [2, 3]\nfunc common(l1: [Int], l2: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(common(l1: [1, 4, 3, 34, 653, 2, 5], l2: [5, 7, 1, 5, 9, 653, 121]) == [1, 5, 653])\nassert(common(l1: [5, 3, 2, 8], l2: [3, 2]) == [2, 3])\nassert(common(l1: [4, 3, 2, 8], l2: [3, 2, 4]) == [2, 3, 4])\nassert(common(l1: [4, 3, 2, 8], l2: [] as [Int]) == [] as [Int])", "stop_tokens": ["\n}"], "task_id": "HumanEval_58_common", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(common(l1: [1, 4, 3, 34, 653, 2, 5], l2: [5, 7, 1, 5, 9, 653, 121]) == [1, 5, 653])\nassert(common(l1: [5, 3, 2, 8], l2: [3, 2]) == [2, 3])\nassert(common(l1: [4, 3, 2, 8], l2: [3, 2, 4]) == [2, 3, 4])\nassert(common(l1: [4, 3, 2, 8], l2: [] as [Int]) == [] as [Int])"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "swift", "prompt": "\n/// Given a positive integer, obtain its roman numeral equivalent as a string,\n/// and return it in lowercase.\n/// Restrictions: 1 <= num <= 1000\n/// Examples:\n/// >>> int_to_mini_roman(number: 19)\n/// \"xix\"\n/// >>> int_to_mini_roman(number: 152)\n/// \"clii\"\n/// >>> int_to_mini_roman(number: 426)\n/// \"cdxxvi\"\nfunc int_to_mini_roman(number: Int) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(int_to_mini_roman(number: 19) == \"xix\")\nassert(int_to_mini_roman(number: 152) == \"clii\")\nassert(int_to_mini_roman(number: 251) == \"ccli\")\nassert(int_to_mini_roman(number: 426) == \"cdxxvi\")\nassert(int_to_mini_roman(number: 500) == \"d\")\nassert(int_to_mini_roman(number: 1) == \"i\")\nassert(int_to_mini_roman(number: 4) == \"iv\")\nassert(int_to_mini_roman(number: 43) == \"xliii\")\nassert(int_to_mini_roman(number: 90) == \"xc\")\nassert(int_to_mini_roman(number: 94) == \"xciv\")\nassert(int_to_mini_roman(number: 532) == \"dxxxii\")\nassert(int_to_mini_roman(number: 900) == \"cm\")\nassert(int_to_mini_roman(number: 994) == \"cmxciv\")\nassert(int_to_mini_roman(number: 1000) == \"m\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(int_to_mini_roman(number: 19) == \"xix\")\nassert(int_to_mini_roman(number: 152) == \"clii\")\nassert(int_to_mini_roman(number: 251) == \"ccli\")\nassert(int_to_mini_roman(number: 426) == \"cdxxvi\")\nassert(int_to_mini_roman(number: 500) == \"d\")\nassert(int_to_mini_roman(number: 1) == \"i\")\nassert(int_to_mini_roman(number: 4) == \"iv\")\nassert(int_to_mini_roman(number: 43) == \"xliii\")\nassert(int_to_mini_roman(number: 90) == \"xc\")\nassert(int_to_mini_roman(number: 94) == \"xciv\")\nassert(int_to_mini_roman(number: 532) == \"dxxxii\")\nassert(int_to_mini_roman(number: 900) == \"cm\")\nassert(int_to_mini_roman(number: 994) == \"cmxciv\")\nassert(int_to_mini_roman(number: 1000) == \"m\")"}
{"name": "HumanEval_67_fruit_distribution", "language": "swift", "prompt": "\n/// In this task, you will be given a string that represents a number of apples and oranges \n/// that are distributed in a basket of fruit this basket contains \n/// apples, oranges, and mango fruits. Given the string that represents the total number of \n/// the oranges and apples and an integer that represent the total number of the fruits \n/// in the basket return the number of the mango fruits in the basket.\n/// for examble:\n/// >>> fruit_distribution(s: \"5 apples and 6 oranges\", n: 19)\n/// 8\n/// >>> fruit_distribution(s: \"0 apples and 1 oranges\", n: 3)\n/// 2\n/// >>> fruit_distribution(s: \"2 apples and 3 oranges\", n: 100)\n/// 95\n/// >>> fruit_distribution(s: \"100 apples and 1 oranges\", n: 120)\n/// 19\nfunc fruit_distribution(s: String, n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fruit_distribution(s: \"5 apples and 6 oranges\", n: 19) == 8)\nassert(fruit_distribution(s: \"5 apples and 6 oranges\", n: 21) == 10)\nassert(fruit_distribution(s: \"0 apples and 1 oranges\", n: 3) == 2)\nassert(fruit_distribution(s: \"1 apples and 0 oranges\", n: 3) == 2)\nassert(fruit_distribution(s: \"2 apples and 3 oranges\", n: 100) == 95)\nassert(fruit_distribution(s: \"2 apples and 3 oranges\", n: 5) == 0)\nassert(fruit_distribution(s: \"1 apples and 100 oranges\", n: 120) == 19)", "stop_tokens": ["\n}"], "task_id": "HumanEval_67_fruit_distribution", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(fruit_distribution(s: \"5 apples and 6 oranges\", n: 19) == 8)\nassert(fruit_distribution(s: \"5 apples and 6 oranges\", n: 21) == 10)\nassert(fruit_distribution(s: \"0 apples and 1 oranges\", n: 3) == 2)\nassert(fruit_distribution(s: \"1 apples and 0 oranges\", n: 3) == 2)\nassert(fruit_distribution(s: \"2 apples and 3 oranges\", n: 100) == 95)\nassert(fruit_distribution(s: \"2 apples and 3 oranges\", n: 5) == 0)\nassert(fruit_distribution(s: \"1 apples and 100 oranges\", n: 120) == 19)"}
{"name": "HumanEval_112_reverse_delete", "language": "swift", "prompt": "\n/// Task\n/// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n/// then check if the result string is palindrome.\n/// A string is called palindrome if it reads the same backward as forward.\n/// You should return a tuple containing the result string and true/false for the check.\n/// Example\n/// >>> reverse_delete(s: \"abcde\", c: \"ae\")\n/// (\"bcd\", false)\n/// >>> reverse_delete(s: \"abcdef\", c: \"b\")\n/// (\"acdef\", false)\n/// >>> reverse_delete(s: \"abcdedcba\", c: \"ab\")\n/// (\"cdedc\", true)\nfunc reverse_delete(s: String, c: String) -> (String, Bool) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(reverse_delete(s: \"abcde\", c: \"ae\") == (\"bcd\", false))\nassert(reverse_delete(s: \"abcdef\", c: \"b\") == (\"acdef\", false))\nassert(reverse_delete(s: \"abcdedcba\", c: \"ab\") == (\"cdedc\", true))\nassert(reverse_delete(s: \"dwik\", c: \"w\") == (\"dik\", false))\nassert(reverse_delete(s: \"a\", c: \"a\") == (\"\", true))\nassert(reverse_delete(s: \"abcdedcba\", c: \"\") == (\"abcdedcba\", true))\nassert(reverse_delete(s: \"abcdedcba\", c: \"v\") == (\"abcdedcba\", true))\nassert(reverse_delete(s: \"vabba\", c: \"v\") == (\"abba\", true))\nassert(reverse_delete(s: \"mamma\", c: \"mia\") == (\"\", true))", "stop_tokens": ["\n}"], "task_id": "HumanEval_112_reverse_delete", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(reverse_delete(s: \"abcde\", c: \"ae\") == (\"bcd\", false))\nassert(reverse_delete(s: \"abcdef\", c: \"b\") == (\"acdef\", false))\nassert(reverse_delete(s: \"abcdedcba\", c: \"ab\") == (\"cdedc\", true))\nassert(reverse_delete(s: \"dwik\", c: \"w\") == (\"dik\", false))\nassert(reverse_delete(s: \"a\", c: \"a\") == (\"\", true))\nassert(reverse_delete(s: \"abcdedcba\", c: \"\") == (\"abcdedcba\", true))\nassert(reverse_delete(s: \"abcdedcba\", c: \"v\") == (\"abcdedcba\", true))\nassert(reverse_delete(s: \"vabba\", c: \"v\") == (\"abba\", true))\nassert(reverse_delete(s: \"mamma\", c: \"mia\") == (\"\", true))"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "swift", "prompt": "\n/// Return a greatest common divisor of two integers a and b\n/// >>> greatest_common_divisor(a: 3, b: 5)\n/// 1\n/// >>> greatest_common_divisor(a: 25, b: 15)\n/// 5\nfunc greatest_common_divisor(a: Int, b: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(greatest_common_divisor(a: 3, b: 7) == 1)\nassert(greatest_common_divisor(a: 10, b: 15) == 5)\nassert(greatest_common_divisor(a: 49, b: 14) == 7)\nassert(greatest_common_divisor(a: 144, b: 60) == 12)", "stop_tokens": ["\n}"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(greatest_common_divisor(a: 3, b: 7) == 1)\nassert(greatest_common_divisor(a: 10, b: 15) == 5)\nassert(greatest_common_divisor(a: 49, b: 14) == 7)\nassert(greatest_common_divisor(a: 144, b: 60) == 12)"}
{"name": "HumanEval_125_split_words", "language": "swift", "prompt": "\nextension Int: Error {}\n        \n/// Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n/// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n/// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n/// Examples\n/// >>> split_words(txt: \"Hello world!\")\n/// .success([\"Hello\", \"world!\"])\n/// >>> split_words(txt: \"Hello,world!\")\n/// .success([\"Hello\", \"world!\"])\n/// >>> split_words(txt: \"abcdef\")\n/// .failure(3)\nfunc split_words(txt: String) -> Result<[String], Int> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_125_split_words.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(split_words(txt: \"Hello world!\") == .success([\"Hello\", \"world!\"]))\nassert(split_words(txt: \"Hello,world!\") == .success([\"Hello\", \"world!\"]))\nassert(split_words(txt: \"Hello world,!\") == .success([\"Hello\", \"world,!\"]))\nassert(split_words(txt: \"Hello,Hello,world !\") == .success([\"Hello,Hello,world\", \"!\"]))\nassert(split_words(txt: \"abcdef\") == .failure(3))\nassert(split_words(txt: \"aaabb\") == .failure(2))\nassert(split_words(txt: \"aaaBb\") == .failure(1))\nassert(split_words(txt: \"\") == .failure(0))", "stop_tokens": ["\n}"], "task_id": "HumanEval_125_split_words", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(split_words(txt: \"Hello world!\") == .success([\"Hello\", \"world!\"]))\nassert(split_words(txt: \"Hello,world!\") == .success([\"Hello\", \"world!\"]))\nassert(split_words(txt: \"Hello world,!\") == .success([\"Hello\", \"world,!\"]))\nassert(split_words(txt: \"Hello,Hello,world !\") == .success([\"Hello,Hello,world\", \"!\"]))\nassert(split_words(txt: \"abcdef\") == .failure(3))\nassert(split_words(txt: \"aaabb\") == .failure(2))\nassert(split_words(txt: \"aaaBb\") == .failure(1))\nassert(split_words(txt: \"\") == .failure(0))"}
{"name": "HumanEval_116_sort_array", "language": "swift", "prompt": "\n/// In this Kata, you have to sort an array of non-negative integers according to\n/// number of ones in their binary representation in ascending order.\n/// For similar number of ones, sort based on decimal value.\n/// It must be implemented like this:\n/// >>> sort_array(arr: [1, 5, 2, 3, 4])\n/// [1, 2, 3, 4, 5]\n/// >>> sort_array(arr: [-2, -3, -4, -5, -6])\n/// [-6, -5, -4, -3, -2]\n/// >>> sort_array(arr: [1, 0, 2, 3, 4])\n/// [0, 1, 2, 3, 4]\nfunc sort_array(arr: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_array(arr: [1, 5, 2, 3, 4]) == [1, 2, 4, 3, 5])\nassert(sort_array(arr: [-2, -3, -4, -5, -6]) == [-4, -2, -6, -5, -3])\nassert(sort_array(arr: [1, 0, 2, 3, 4]) == [0, 1, 2, 4, 3])\nassert(sort_array(arr: [] as [Int]) == [] as [Int])\nassert(sort_array(arr: [2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]) == [2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77])\nassert(sort_array(arr: [3, 6, 44, 12, 32, 5]) == [32, 3, 5, 6, 12, 44])\nassert(sort_array(arr: [2, 4, 8, 16, 32]) == [2, 4, 8, 16, 32])\nassert(sort_array(arr: [2, 4, 8, 16, 32]) == [2, 4, 8, 16, 32])", "stop_tokens": ["\n}"], "task_id": "HumanEval_116_sort_array", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_array(arr: [1, 5, 2, 3, 4]) == [1, 2, 4, 3, 5])\nassert(sort_array(arr: [-2, -3, -4, -5, -6]) == [-4, -2, -6, -5, -3])\nassert(sort_array(arr: [1, 0, 2, 3, 4]) == [0, 1, 2, 4, 3])\nassert(sort_array(arr: [] as [Int]) == [] as [Int])\nassert(sort_array(arr: [2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]) == [2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77])\nassert(sort_array(arr: [3, 6, 44, 12, 32, 5]) == [32, 3, 5, 6, 12, 44])\nassert(sort_array(arr: [2, 4, 8, 16, 32]) == [2, 4, 8, 16, 32])\nassert(sort_array(arr: [2, 4, 8, 16, 32]) == [2, 4, 8, 16, 32])"}
{"name": "HumanEval_28_concatenate", "language": "swift", "prompt": "\n/// Concatenate array of strings into a single string\n/// >>> concatenate(strings: [] as [String])\n/// \"\"\n/// >>> concatenate(strings: [\"a\", \"b\", \"c\"])\n/// \"abc\"\nfunc concatenate(strings: [String]) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(concatenate(strings: [] as [String]) == \"\")\nassert(concatenate(strings: [\"x\", \"y\", \"z\"]) == \"xyz\")\nassert(concatenate(strings: [\"x\", \"y\", \"z\", \"w\", \"k\"]) == \"xyzwk\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_28_concatenate", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(concatenate(strings: [] as [String]) == \"\")\nassert(concatenate(strings: [\"x\", \"y\", \"z\"]) == \"xyz\")\nassert(concatenate(strings: [\"x\", \"y\", \"z\", \"w\", \"k\"]) == \"xyzwk\")"}
{"name": "HumanEval_149_sorted_list_sum", "language": "swift", "prompt": "\n/// Write a function that accepts an array of strings as a parameter,\n/// deletes the strings that have odd lengths from it,\n/// and returns the resulted array with a sorted order,\n/// The array is always an array of strings and never an array of numbers,\n/// and it may contain duplicates.\n/// The order of the array should be ascending by length of each word, and you\n/// should return the array sorted by that rule.\n/// If two words have the same length, sort the array alphabetically.\n/// The function should return an array of strings in sorted order.\n/// You may assume that all words will have the same length.\n/// For example:\n/// >>> sorted_list_sum(lst: [\"aa\", \"a\", \"aaa\"])\n/// [\"aa\"]\n/// >>> sorted_list_sum(lst: [\"ab\", \"a\", \"aaa\", \"cd\"])\n/// [\"ab\", \"cd\"]\nfunc sorted_list_sum(lst: [String]) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sorted_list_sum(lst: [\"aa\", \"a\", \"aaa\"]) == [\"aa\"])\nassert(sorted_list_sum(lst: [\"school\", \"AI\", \"asdf\", \"b\"]) == [\"AI\", \"asdf\", \"school\"])\nassert(sorted_list_sum(lst: [\"d\", \"b\", \"c\", \"a\"]) == [] as [String])\nassert(sorted_list_sum(lst: [\"d\", \"dcba\", \"abcd\", \"a\"]) == [\"abcd\", \"dcba\"])\nassert(sorted_list_sum(lst: [\"AI\", \"ai\", \"au\"]) == [\"AI\", \"ai\", \"au\"])\nassert(sorted_list_sum(lst: [\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"]) == [] as [String])\nassert(sorted_list_sum(lst: [\"aaaa\", \"bbbb\", \"dd\", \"cc\"]) == [\"cc\", \"dd\", \"aaaa\", \"bbbb\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_149_sorted_list_sum", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sorted_list_sum(lst: [\"aa\", \"a\", \"aaa\"]) == [\"aa\"])\nassert(sorted_list_sum(lst: [\"school\", \"AI\", \"asdf\", \"b\"]) == [\"AI\", \"asdf\", \"school\"])\nassert(sorted_list_sum(lst: [\"d\", \"b\", \"c\", \"a\"]) == [] as [String])\nassert(sorted_list_sum(lst: [\"d\", \"dcba\", \"abcd\", \"a\"]) == [\"abcd\", \"dcba\"])\nassert(sorted_list_sum(lst: [\"AI\", \"ai\", \"au\"]) == [\"AI\", \"ai\", \"au\"])\nassert(sorted_list_sum(lst: [\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"]) == [] as [String])\nassert(sorted_list_sum(lst: [\"aaaa\", \"bbbb\", \"dd\", \"cc\"]) == [\"cc\", \"dd\", \"aaaa\", \"bbbb\"])"}
{"name": "HumanEval_7_filter_by_substring", "language": "swift", "prompt": "\n/// Filter an input array of strings only for ones that contain given substring\n/// >>> filter_by_substring(strings: [] as [String], substring: \"a\")\n/// [] as [String]\n/// >>> filter_by_substring(strings: [\"abc\", \"bacd\", \"cde\", \"array\"], substring: \"a\")\n/// [\"abc\", \"bacd\", \"array\"]\nfunc filter_by_substring(strings: [String], substring: String) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(filter_by_substring(strings: [] as [String], substring: \"john\") == [] as [String])\nassert(filter_by_substring(strings: [\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], substring: \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"])\nassert(filter_by_substring(strings: [\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"], substring: \"xx\") == [\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"])\nassert(filter_by_substring(strings: [\"grunt\", \"trumpet\", \"prune\", \"gruesome\"], substring: \"run\") == [\"grunt\", \"prune\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_7_filter_by_substring", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(filter_by_substring(strings: [] as [String], substring: \"john\") == [] as [String])\nassert(filter_by_substring(strings: [\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], substring: \"xxx\") == [\"xxx\", \"xxxAAA\", \"xxx\"])\nassert(filter_by_substring(strings: [\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"], substring: \"xx\") == [\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"])\nassert(filter_by_substring(strings: [\"grunt\", \"trumpet\", \"prune\", \"gruesome\"], substring: \"run\") == [\"grunt\", \"prune\"])"}
{"name": "HumanEval_99_closest_integer", "language": "swift", "prompt": "\n/// Create a function that takes a value (string) representing a number\n/// and returns the closest integer to it. If the number is equidistant\n/// from two integers, round it away from zero.\n/// Examples\n/// >>> closest_integer(value: \"10\")\n/// 10\n/// >>> closest_integer(value: \"15.3\")\n/// 15\n/// Note:\n/// Rounding away from zero means that if the given number is equidistant\n/// from two integers, the one you should return is the one that is the\n/// farthest from zero. For example closest_integer(\"14.5\") should\n/// return 15 and closest_integer(\"-14.5\") should return -15.\nfunc closest_integer(value: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(closest_integer(value: \"10\") == 10)\nassert(closest_integer(value: \"14.5\") == 15)\nassert(closest_integer(value: \"-15.5\") == -16)\nassert(closest_integer(value: \"15.3\") == 15)\nassert(closest_integer(value: \"0\") == 0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_99_closest_integer", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(closest_integer(value: \"10\") == 10)\nassert(closest_integer(value: \"14.5\") == 15)\nassert(closest_integer(value: \"-15.5\") == -16)\nassert(closest_integer(value: \"15.3\") == 15)\nassert(closest_integer(value: \"0\") == 0)"}
{"name": "HumanEval_64_vowels_count", "language": "swift", "prompt": "\n/// Write a function vowels_count which takes a string representing\n/// a word as input and returns the number of vowels in the string.\n/// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n/// vowel, but only when it is at the end of the given word.\n/// Example:\n/// >>> vowels_count(s: \"abcde\")\n/// 2\n/// >>> vowels_count(s: \"ACEDY\")\n/// 3\nfunc vowels_count(s: String) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(vowels_count(s: \"abcde\") == 2)\nassert(vowels_count(s: \"Alone\") == 3)\nassert(vowels_count(s: \"key\") == 2)\nassert(vowels_count(s: \"bye\") == 1)\nassert(vowels_count(s: \"keY\") == 2)\nassert(vowels_count(s: \"bYe\") == 1)\nassert(vowels_count(s: \"ACEDY\") == 3)", "stop_tokens": ["\n}"], "task_id": "HumanEval_64_vowels_count", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(vowels_count(s: \"abcde\") == 2)\nassert(vowels_count(s: \"Alone\") == 3)\nassert(vowels_count(s: \"key\") == 2)\nassert(vowels_count(s: \"bye\") == 1)\nassert(vowels_count(s: \"keY\") == 2)\nassert(vowels_count(s: \"bYe\") == 1)\nassert(vowels_count(s: \"ACEDY\") == 3)"}
{"name": "HumanEval_158_find_max", "language": "swift", "prompt": "\n/// Write a function that accepts an array of strings.\n/// The array contains different words. Return the word with maximum number\n/// of unique characters. If multiple strings have maximum number of unique\n/// characters, return the one which comes first in lexicographical order.\n/// >>> find_max(words: [\"name\", \"of\", \"string\"])\n/// \"string\"\n/// >>> find_max(words: [\"name\", \"enam\", \"game\"])\n/// \"enam\"\n/// >>> find_max(words: [\"aaaaaaa\", \"bb\", \"cc\"])\n/// \"aaaaaaa\"\nfunc find_max(words: [String]) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(find_max(words: [\"name\", \"of\", \"string\"]) == \"string\")\nassert(find_max(words: [\"name\", \"enam\", \"game\"]) == \"enam\")\nassert(find_max(words: [\"aaaaaaa\", \"bb\", \"cc\"]) == \"aaaaaaa\")\nassert(find_max(words: [\"abc\", \"cba\"]) == \"abc\")\nassert(find_max(words: [\"play\", \"this\", \"game\", \"of\", \"footbott\"]) == \"footbott\")\nassert(find_max(words: [\"we\", \"are\", \"gonna\", \"rock\"]) == \"gonna\")\nassert(find_max(words: [\"we\", \"are\", \"a\", \"mad\", \"nation\"]) == \"nation\")\nassert(find_max(words: [\"this\", \"is\", \"a\", \"prrk\"]) == \"this\")\nassert(find_max(words: [\"b\"]) == \"b\")\nassert(find_max(words: [\"play\", \"play\", \"play\"]) == \"play\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_158_find_max", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(find_max(words: [\"name\", \"of\", \"string\"]) == \"string\")\nassert(find_max(words: [\"name\", \"enam\", \"game\"]) == \"enam\")\nassert(find_max(words: [\"aaaaaaa\", \"bb\", \"cc\"]) == \"aaaaaaa\")\nassert(find_max(words: [\"abc\", \"cba\"]) == \"abc\")\nassert(find_max(words: [\"play\", \"this\", \"game\", \"of\", \"footbott\"]) == \"footbott\")\nassert(find_max(words: [\"we\", \"are\", \"gonna\", \"rock\"]) == \"gonna\")\nassert(find_max(words: [\"we\", \"are\", \"a\", \"mad\", \"nation\"]) == \"nation\")\nassert(find_max(words: [\"this\", \"is\", \"a\", \"prrk\"]) == \"this\")\nassert(find_max(words: [\"b\"]) == \"b\")\nassert(find_max(words: [\"play\", \"play\", \"play\"]) == \"play\")"}
{"name": "HumanEval_162_string_to_md5", "language": "swift", "prompt": "\n/// Given a string 'text', return its md5 hash equivalent string.\n/// If 'text' is an empty string, return nil.\n/// >>> string_to_md5(text: \"Hello world\")\n/// \"3e25960a79dbc69b674cd4ec67a72c62\"\nfunc string_to_md5(text: String) -> String? {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(string_to_md5(text: \"Hello world\") == \"3e25960a79dbc69b674cd4ec67a72c62\")\nassert(string_to_md5(text: \"\") == nil)\nassert(string_to_md5(text: \"A B C\") == \"0ef78513b0cb8cef12743f5aeb35f888\")\nassert(string_to_md5(text: \"password\") == \"5f4dcc3b5aa765d61d8327deb882cf99\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_162_string_to_md5", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(string_to_md5(text: \"Hello world\") == \"3e25960a79dbc69b674cd4ec67a72c62\")\nassert(string_to_md5(text: \"\") == nil)\nassert(string_to_md5(text: \"A B C\") == \"0ef78513b0cb8cef12743f5aeb35f888\")\nassert(string_to_md5(text: \"password\") == \"5f4dcc3b5aa765d61d8327deb882cf99\")"}
{"name": "HumanEval_44_change_base", "language": "swift", "prompt": "\n/// Change numerical base of input number x to base.\n/// return string representation after the conversion.\n/// base numbers are less than 10.\n/// >>> change_base(x: 8, base: 3)\n/// \"22\"\n/// >>> change_base(x: 8, base: 2)\n/// \"1000\"\n/// >>> change_base(x: 7, base: 2)\n/// \"111\"\nfunc change_base(x: Int, base: Int) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(change_base(x: 8, base: 3) == \"22\")\nassert(change_base(x: 9, base: 3) == \"100\")\nassert(change_base(x: 234, base: 2) == \"11101010\")\nassert(change_base(x: 16, base: 2) == \"10000\")\nassert(change_base(x: 8, base: 2) == \"1000\")\nassert(change_base(x: 7, base: 2) == \"111\")\nassert(change_base(x: 2, base: 3) == \"2\")\nassert(change_base(x: 3, base: 4) == \"3\")\nassert(change_base(x: 4, base: 5) == \"4\")\nassert(change_base(x: 5, base: 6) == \"5\")\nassert(change_base(x: 6, base: 7) == \"6\")\nassert(change_base(x: 7, base: 8) == \"7\")", "stop_tokens": ["\n}"], "task_id": "HumanEval_44_change_base", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(change_base(x: 8, base: 3) == \"22\")\nassert(change_base(x: 9, base: 3) == \"100\")\nassert(change_base(x: 234, base: 2) == \"11101010\")\nassert(change_base(x: 16, base: 2) == \"10000\")\nassert(change_base(x: 8, base: 2) == \"1000\")\nassert(change_base(x: 7, base: 2) == \"111\")\nassert(change_base(x: 2, base: 3) == \"2\")\nassert(change_base(x: 3, base: 4) == \"3\")\nassert(change_base(x: 4, base: 5) == \"4\")\nassert(change_base(x: 5, base: 6) == \"5\")\nassert(change_base(x: 6, base: 7) == \"6\")\nassert(change_base(x: 7, base: 8) == \"7\")"}
{"name": "HumanEval_157_right_angle_triangle", "language": "swift", "prompt": "\n/// Given the lengths of the three sides of a triangle. Return true if the three\n/// sides form a right-angled triangle, false otherwise.\n/// A right-angled triangle is a triangle in which one angle is right angle or \n/// 90 degree.\n/// Example:\n/// >>> right_angle_triangle(a: 3, b: 4, c: 5)\n/// true\n/// >>> right_angle_triangle(a: 1, b: 2, c: 3)\n/// false\nfunc right_angle_triangle(a: Int, b: Int, c: Int) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(right_angle_triangle(a: 3, b: 4, c: 5) == true)\nassert(right_angle_triangle(a: 1, b: 2, c: 3) == false)\nassert(right_angle_triangle(a: 10, b: 6, c: 8) == true)\nassert(right_angle_triangle(a: 2, b: 2, c: 2) == false)\nassert(right_angle_triangle(a: 7, b: 24, c: 25) == true)\nassert(right_angle_triangle(a: 10, b: 5, c: 7) == false)\nassert(right_angle_triangle(a: 5, b: 12, c: 13) == true)\nassert(right_angle_triangle(a: 15, b: 8, c: 17) == true)\nassert(right_angle_triangle(a: 48, b: 55, c: 73) == true)\nassert(right_angle_triangle(a: 1, b: 1, c: 1) == false)\nassert(right_angle_triangle(a: 2, b: 2, c: 10) == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_157_right_angle_triangle", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(right_angle_triangle(a: 3, b: 4, c: 5) == true)\nassert(right_angle_triangle(a: 1, b: 2, c: 3) == false)\nassert(right_angle_triangle(a: 10, b: 6, c: 8) == true)\nassert(right_angle_triangle(a: 2, b: 2, c: 2) == false)\nassert(right_angle_triangle(a: 7, b: 24, c: 25) == true)\nassert(right_angle_triangle(a: 10, b: 5, c: 7) == false)\nassert(right_angle_triangle(a: 5, b: 12, c: 13) == true)\nassert(right_angle_triangle(a: 15, b: 8, c: 17) == true)\nassert(right_angle_triangle(a: 48, b: 55, c: 73) == true)\nassert(right_angle_triangle(a: 1, b: 1, c: 1) == false)\nassert(right_angle_triangle(a: 2, b: 2, c: 10) == false)"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "swift", "prompt": "\n/// It is the last week of the semester and the teacher has to give the grades\n/// to students. The teacher has been making her own algorithm for grading.\n/// The only problem is, she has lost the code she used for grading.\n/// She has given you an array of GPAs for some students and you have to write \n/// a function that can output an array of letter grades using the following table:\n/// GPA       |    Letter grade\n/// 4.0                A+\n/// > 3.7                A \n/// > 3.3                A- \n/// > 3.0                B+\n/// > 2.7                B \n/// > 2.3                B-\n/// > 2.0                C+\n/// > 1.7                C\n/// > 1.3                C-\n/// > 1.0                D+ \n/// > 0.7                D \n/// > 0.0                D-\n/// 0.0                E\n/// Example:\n/// >>> numerical_letter_grade(grades: [4.0, 3, 1.7, 2, 3.5])\n/// [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\nfunc numerical_letter_grade(grades: [Double]) -> [String] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(numerical_letter_grade(grades: [4.0, 3, 1.7, 2, 3.5]) == [\"A+\", \"B\", \"C-\", \"C\", \"A-\"])\nassert(numerical_letter_grade(grades: [1.2]) == [\"D+\"])\nassert(numerical_letter_grade(grades: [0.5]) == [\"D-\"])\nassert(numerical_letter_grade(grades: [0.0]) == [\"E\"])\nassert(numerical_letter_grade(grades: [1.0, 0.3, 1.5, 2.8, 3.3]) == [\"D\", \"D-\", \"C-\", \"B\", \"B+\"])\nassert(numerical_letter_grade(grades: [0.0, 0.7]) == [\"E\", \"D-\"])", "stop_tokens": ["\n}"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(numerical_letter_grade(grades: [4.0, 3, 1.7, 2, 3.5]) == [\"A+\", \"B\", \"C-\", \"C\", \"A-\"])\nassert(numerical_letter_grade(grades: [1.2]) == [\"D+\"])\nassert(numerical_letter_grade(grades: [0.5]) == [\"D-\"])\nassert(numerical_letter_grade(grades: [0.0]) == [\"E\"])\nassert(numerical_letter_grade(grades: [1.0, 0.3, 1.5, 2.8, 3.3]) == [\"D\", \"D-\", \"C-\", \"B\", \"B+\"])\nassert(numerical_letter_grade(grades: [0.0, 0.7]) == [\"E\", \"D-\"])"}
{"name": "HumanEval_5_intersperse", "language": "swift", "prompt": "\n/// Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n/// >>> intersperse(numbers: [] as [Int], delimeter: 4)\n/// [] as [Int]\n/// >>> intersperse(numbers: [1, 2, 3], delimeter: 4)\n/// [1, 4, 2, 4, 3]\nfunc intersperse(numbers: [Int], delimeter: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(intersperse(numbers: [] as [Int], delimeter: 7) == [] as [Int])\nassert(intersperse(numbers: [5, 6, 3, 2], delimeter: 8) == [5, 8, 6, 8, 3, 8, 2])\nassert(intersperse(numbers: [2, 2, 2], delimeter: 2) == [2, 2, 2, 2, 2])", "stop_tokens": ["\n}"], "task_id": "HumanEval_5_intersperse", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(intersperse(numbers: [] as [Int], delimeter: 7) == [] as [Int])\nassert(intersperse(numbers: [5, 6, 3, 2], delimeter: 8) == [5, 8, 6, 8, 3, 8, 2])\nassert(intersperse(numbers: [2, 2, 2], delimeter: 2) == [2, 2, 2, 2, 2])"}
{"name": "HumanEval_146_specialFilter", "language": "swift", "prompt": "\n/// Write a function that takes an array of numbers as input and returns \n/// the number of elements in the array that are greater than 10 and both \n/// first and last digits of a number are odd (1, 3, 5, 7, 9).\n/// For example:\n/// >>> specialFilter(nums: [15, -73, 14, -15])\n/// 1\n/// >>> specialFilter(nums: [33, -2, -3, 45, 21, 109])\n/// 2\nfunc specialFilter(nums: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(specialFilter(nums: [5, -2, 1, -5]) == 0)\nassert(specialFilter(nums: [15, -73, 14, -15]) == 1)\nassert(specialFilter(nums: [33, -2, -3, 45, 21, 109]) == 2)\nassert(specialFilter(nums: [43, -12, 93, 125, 121, 109]) == 4)\nassert(specialFilter(nums: [71, -2, -33, 75, 21, 19]) == 3)\nassert(specialFilter(nums: [1]) == 0)\nassert(specialFilter(nums: [] as [Int]) == 0)", "stop_tokens": ["\n}"], "task_id": "HumanEval_146_specialFilter", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(specialFilter(nums: [5, -2, 1, -5]) == 0)\nassert(specialFilter(nums: [15, -73, 14, -15]) == 1)\nassert(specialFilter(nums: [33, -2, -3, 45, 21, 109]) == 2)\nassert(specialFilter(nums: [43, -12, 93, 125, 121, 109]) == 4)\nassert(specialFilter(nums: [71, -2, -33, 75, 21, 19]) == 3)\nassert(specialFilter(nums: [1]) == 0)\nassert(specialFilter(nums: [] as [Int]) == 0)"}
{"name": "HumanEval_60_sum_to_n", "language": "swift", "prompt": "\n/// sum_to_n is a function that sums numbers from 1 to n.\n/// >>> sum_to_n(n: 30)\n/// 465\n/// >>> sum_to_n(n: 100)\n/// 5050\n/// >>> sum_to_n(n: 5)\n/// 15\n/// >>> sum_to_n(n: 10)\n/// 55\n/// >>> sum_to_n(n: 1)\n/// 1\nfunc sum_to_n(n: Int) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sum_to_n(n: 1) == 1)\nassert(sum_to_n(n: 6) == 21)\nassert(sum_to_n(n: 11) == 66)\nassert(sum_to_n(n: 30) == 465)\nassert(sum_to_n(n: 100) == 5050)", "stop_tokens": ["\n}"], "task_id": "HumanEval_60_sum_to_n", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sum_to_n(n: 1) == 1)\nassert(sum_to_n(n: 6) == 21)\nassert(sum_to_n(n: 11) == 66)\nassert(sum_to_n(n: 30) == 465)\nassert(sum_to_n(n: 100) == 5050)"}
{"name": "HumanEval_26_remove_duplicates", "language": "swift", "prompt": "\n/// From an array of integers, remove all elements that occur more than once.\n/// Keep order of elements left the same as in the input.\n/// >>> remove_duplicates(numbers: [1, 2, 3, 2, 4])\n/// [1, 3, 4]\nfunc remove_duplicates(numbers: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(remove_duplicates(numbers: [] as [Int]) == [] as [Int])\nassert(remove_duplicates(numbers: [1, 2, 3, 4]) == [1, 2, 3, 4])\nassert(remove_duplicates(numbers: [1, 2, 3, 2, 4, 3, 5]) == [1, 4, 5])", "stop_tokens": ["\n}"], "task_id": "HumanEval_26_remove_duplicates", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(remove_duplicates(numbers: [] as [Int]) == [] as [Int])\nassert(remove_duplicates(numbers: [1, 2, 3, 4]) == [1, 2, 3, 4])\nassert(remove_duplicates(numbers: [1, 2, 3, 2, 4, 3, 5]) == [1, 4, 5])"}
{"name": "HumanEval_163_generate_integers", "language": "swift", "prompt": "\n/// Given two positive integers a and b, return the even digits between a\n/// and b, in ascending order.\n/// For example:\n/// >>> generate_integers(a: 2, b: 8)\n/// [2, 4, 6, 8]\n/// >>> generate_integers(a: 8, b: 2)\n/// [2, 4, 6, 8]\n/// >>> generate_integers(a: 10, b: 14)\n/// [] as [Int]\nfunc generate_integers(a: Int, b: Int) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(generate_integers(a: 2, b: 10) == [2, 4, 6, 8])\nassert(generate_integers(a: 10, b: 2) == [2, 4, 6, 8])\nassert(generate_integers(a: 132, b: 2) == [2, 4, 6, 8])\nassert(generate_integers(a: 17, b: 89) == [] as [Int])", "stop_tokens": ["\n}"], "task_id": "HumanEval_163_generate_integers", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(generate_integers(a: 2, b: 10) == [2, 4, 6, 8])\nassert(generate_integers(a: 10, b: 2) == [2, 4, 6, 8])\nassert(generate_integers(a: 132, b: 2) == [2, 4, 6, 8])\nassert(generate_integers(a: 17, b: 89) == [] as [Int])"}
{"name": "HumanEval_9_rolling_max", "language": "swift", "prompt": "\n/// From a given array of integers, generate an array of rolling maximum element found until given moment\n/// in the sequence.\n/// >>> rolling_max(numbers: [1, 2, 3, 2, 3, 4, 2])\n/// [1, 2, 3, 3, 3, 4, 4]\nfunc rolling_max(numbers: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(rolling_max(numbers: [] as [Int]) == [] as [Int])\nassert(rolling_max(numbers: [1, 2, 3, 4]) == [1, 2, 3, 4])\nassert(rolling_max(numbers: [4, 3, 2, 1]) == [4, 4, 4, 4])\nassert(rolling_max(numbers: [3, 2, 3, 100, 3]) == [3, 3, 3, 100, 100])", "stop_tokens": ["\n}"], "task_id": "HumanEval_9_rolling_max", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(rolling_max(numbers: [] as [Int]) == [] as [Int])\nassert(rolling_max(numbers: [1, 2, 3, 4]) == [1, 2, 3, 4])\nassert(rolling_max(numbers: [4, 3, 2, 1]) == [4, 4, 4, 4])\nassert(rolling_max(numbers: [3, 2, 3, 100, 3]) == [3, 3, 3, 100, 100])"}
{"name": "HumanEval_3_below_zero", "language": "swift", "prompt": "\n/// You're given an array of deposit and withdrawal operations on a bank account that starts with\n/// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n/// at that point function should return true. Otherwise it should return false.\n/// >>> below_zero(operations: [1, 2, 3])\n/// false\n/// >>> below_zero(operations: [1, 2, -4, 5])\n/// true\nfunc below_zero(operations: [Int]) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(below_zero(operations: [] as [Int]) == false)\nassert(below_zero(operations: [1, 2, -3, 1, 2, -3]) == false)\nassert(below_zero(operations: [1, 2, -4, 5, 6]) == true)\nassert(below_zero(operations: [1, -1, 2, -2, 5, -5, 4, -4]) == false)\nassert(below_zero(operations: [1, -1, 2, -2, 5, -5, 4, -5]) == true)\nassert(below_zero(operations: [1, -2, 2, -2, 5, -5, 4, -4]) == true)", "stop_tokens": ["\n}"], "task_id": "HumanEval_3_below_zero", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(below_zero(operations: [] as [Int]) == false)\nassert(below_zero(operations: [1, 2, -3, 1, 2, -3]) == false)\nassert(below_zero(operations: [1, 2, -4, 5, 6]) == true)\nassert(below_zero(operations: [1, -1, 2, -2, 5, -5, 4, -4]) == false)\nassert(below_zero(operations: [1, -1, 2, -2, 5, -5, 4, -5]) == true)\nassert(below_zero(operations: [1, -2, 2, -2, 5, -5, 4, -4]) == true)"}
{"name": "HumanEval_69_search", "language": "swift", "prompt": "\n/// You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n/// zero, and has a frequency greater than or equal to the value of the integer itself. \n/// The frequency of an integer is the number of times it appears in the array.\n/// If no such a value exist, return -1.\n/// Examples:\n/// >>> search(lst: [4, 1, 2, 2, 3, 1])\n/// 2\n/// >>> search(lst: [1, 2, 2, 3, 3, 3, 4, 4, 4])\n/// 3\n/// >>> search(lst: [5, 5, 4, 4, 4])\n/// -1\nfunc search(lst: [Int]) -> Int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(search(lst: [5, 5, 5, 5, 1]) == 1)\nassert(search(lst: [4, 1, 4, 1, 4, 4]) == 4)\nassert(search(lst: [3, 3]) == -1)\nassert(search(lst: [8, 8, 8, 8, 8, 8, 8, 8]) == 8)\nassert(search(lst: [2, 3, 3, 2, 2]) == 2)\nassert(search(lst: [2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1]) == 1)\nassert(search(lst: [3, 2, 8, 2]) == 2)\nassert(search(lst: [6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10]) == 1)\nassert(search(lst: [8, 8, 3, 6, 5, 6, 4]) == -1)\nassert(search(lst: [6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9]) == 1)\nassert(search(lst: [1, 9, 10, 1, 3]) == 1)\nassert(search(lst: [6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10]) == 5)\nassert(search(lst: [1]) == 1)\nassert(search(lst: [8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5]) == 4)\nassert(search(lst: [2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10]) == 2)\nassert(search(lst: [1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3]) == 1)\nassert(search(lst: [9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4]) == 4)\nassert(search(lst: [2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7]) == 4)\nassert(search(lst: [9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1]) == 2)\nassert(search(lst: [5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8]) == -1)\nassert(search(lst: [10]) == -1)\nassert(search(lst: [9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2]) == 2)\nassert(search(lst: [5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8]) == 1)\nassert(search(lst: [7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6]) == 1)\nassert(search(lst: [3, 10, 10, 9, 2]) == -1)", "stop_tokens": ["\n}"], "task_id": "HumanEval_69_search", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(search(lst: [5, 5, 5, 5, 1]) == 1)\nassert(search(lst: [4, 1, 4, 1, 4, 4]) == 4)\nassert(search(lst: [3, 3]) == -1)\nassert(search(lst: [8, 8, 8, 8, 8, 8, 8, 8]) == 8)\nassert(search(lst: [2, 3, 3, 2, 2]) == 2)\nassert(search(lst: [2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1]) == 1)\nassert(search(lst: [3, 2, 8, 2]) == 2)\nassert(search(lst: [6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10]) == 1)\nassert(search(lst: [8, 8, 3, 6, 5, 6, 4]) == -1)\nassert(search(lst: [6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9]) == 1)\nassert(search(lst: [1, 9, 10, 1, 3]) == 1)\nassert(search(lst: [6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10]) == 5)\nassert(search(lst: [1]) == 1)\nassert(search(lst: [8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5]) == 4)\nassert(search(lst: [2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10]) == 2)\nassert(search(lst: [1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3]) == 1)\nassert(search(lst: [9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4]) == 4)\nassert(search(lst: [2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7]) == 4)\nassert(search(lst: [9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1]) == 2)\nassert(search(lst: [5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8]) == -1)\nassert(search(lst: [10]) == -1)\nassert(search(lst: [9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2]) == 2)\nassert(search(lst: [5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8]) == 1)\nassert(search(lst: [7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6]) == 1)\nassert(search(lst: [3, 10, 10, 9, 2]) == -1)"}
{"name": "HumanEval_61_correct_bracketing", "language": "swift", "prompt": "\n/// brackets is a string of \"(\" and \")\".\n/// return true if every opening bracket has a corresponding closing bracket.\n/// >>> correct_bracketing(brackets: \"(\")\n/// false\n/// >>> correct_bracketing(brackets: \"()\")\n/// true\n/// >>> correct_bracketing(brackets: \"(()())\")\n/// true\n/// >>> correct_bracketing(brackets: \")(()\")\n/// false\nfunc correct_bracketing(brackets: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(correct_bracketing(brackets: \"()\") == true)\nassert(correct_bracketing(brackets: \"(()())\") == true)\nassert(correct_bracketing(brackets: \"()()(()())()\") == true)\nassert(correct_bracketing(brackets: \"()()((()()())())(()()(()))\") == true)\nassert(correct_bracketing(brackets: \"((()())))\") == false)\nassert(correct_bracketing(brackets: \")(()\") == false)\nassert(correct_bracketing(brackets: \"(\") == false)\nassert(correct_bracketing(brackets: \"((((\") == false)\nassert(correct_bracketing(brackets: \")\") == false)\nassert(correct_bracketing(brackets: \"(()\") == false)\nassert(correct_bracketing(brackets: \"()()(()())())(()\") == false)\nassert(correct_bracketing(brackets: \"()()(()())()))()\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_61_correct_bracketing", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(correct_bracketing(brackets: \"()\") == true)\nassert(correct_bracketing(brackets: \"(()())\") == true)\nassert(correct_bracketing(brackets: \"()()(()())()\") == true)\nassert(correct_bracketing(brackets: \"()()((()()())())(()()(()))\") == true)\nassert(correct_bracketing(brackets: \"((()())))\") == false)\nassert(correct_bracketing(brackets: \")(()\") == false)\nassert(correct_bracketing(brackets: \"(\") == false)\nassert(correct_bracketing(brackets: \"((((\") == false)\nassert(correct_bracketing(brackets: \")\") == false)\nassert(correct_bracketing(brackets: \"(()\") == false)\nassert(correct_bracketing(brackets: \"()()(()())())(()\") == false)\nassert(correct_bracketing(brackets: \"()()(()())()))()\") == false)"}
{"name": "HumanEval_37_sort_even", "language": "swift", "prompt": "\n/// This function takes an array l and returns an array l' such that\n/// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n/// to the values of the even indicies of l, but sorted.\n/// >>> sort_even(l: [1, 2, 3])\n/// [1, 2, 3]\n/// >>> sort_even(l: [5, 6, 3, 4])\n/// [3, 6, 5, 4]\nfunc sort_even(l: [Int]) -> [Int] {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_even(l: [1, 2, 3]) == [1, 2, 3])\nassert(sort_even(l: [5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10]) == [-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123])\nassert(sort_even(l: [5, 8, -12, 4, 23, 2, 3, 11, 12, -10]) == [-12, 8, 3, 4, 5, 2, 12, 11, 23, -10])", "stop_tokens": ["\n}"], "task_id": "HumanEval_37_sort_even", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(sort_even(l: [1, 2, 3]) == [1, 2, 3])\nassert(sort_even(l: [5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10]) == [-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123])\nassert(sort_even(l: [5, 8, -12, 4, 23, 2, 3, 11, 12, -10]) == [-12, 8, 3, 4, 5, 2, 12, 11, 23, -10])"}
{"name": "HumanEval_54_same_chars", "language": "swift", "prompt": "\n/// Check if two words have the same characters.\n/// >>> same_chars(s0: \"eabcdzzzz\", s1: \"dddzzzzzzzddeddabc\")\n/// true\n/// >>> same_chars(s0: \"abcd\", s1: \"dddddddabc\")\n/// true\n/// >>> same_chars(s0: \"dddddddabc\", s1: \"abcd\")\n/// true\n/// >>> same_chars(s0: \"eabcd\", s1: \"dddddddabc\")\n/// false\n/// >>> same_chars(s0: \"abcd\", s1: \"dddddddabce\")\n/// false\n/// >>> same_chars(s0: \"eabcdzzzz\", s1: \"dddzzzzzzzddddabc\")\n/// false\nfunc same_chars(s0: String, s1: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(same_chars(s0: \"eabcdzzzz\", s1: \"dddzzzzzzzddeddabc\") == true)\nassert(same_chars(s0: \"abcd\", s1: \"dddddddabc\") == true)\nassert(same_chars(s0: \"dddddddabc\", s1: \"abcd\") == true)\nassert(same_chars(s0: \"eabcd\", s1: \"dddddddabc\") == false)\nassert(same_chars(s0: \"abcd\", s1: \"dddddddabcf\") == false)\nassert(same_chars(s0: \"eabcdzzzz\", s1: \"dddzzzzzzzddddabc\") == false)\nassert(same_chars(s0: \"aabb\", s1: \"aaccc\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_54_same_chars", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(same_chars(s0: \"eabcdzzzz\", s1: \"dddzzzzzzzddeddabc\") == true)\nassert(same_chars(s0: \"abcd\", s1: \"dddddddabc\") == true)\nassert(same_chars(s0: \"dddddddabc\", s1: \"abcd\") == true)\nassert(same_chars(s0: \"eabcd\", s1: \"dddddddabc\") == false)\nassert(same_chars(s0: \"abcd\", s1: \"dddddddabcf\") == false)\nassert(same_chars(s0: \"eabcdzzzz\", s1: \"dddzzzzzzzddddabc\") == false)\nassert(same_chars(s0: \"aabb\", s1: \"aaccc\") == false)"}
{"name": "HumanEval_56_correct_bracketing", "language": "swift", "prompt": "\n/// brackets is a string of \"<\" and \">\".\n/// return true if every opening bracket has a corresponding closing bracket.\n/// >>> correct_bracketing(brackets: \"<\")\n/// false\n/// >>> correct_bracketing(brackets: \"<>\")\n/// true\n/// >>> correct_bracketing(brackets: \"<<><>>\")\n/// true\n/// >>> correct_bracketing(brackets: \"><<>\")\n/// false\nfunc correct_bracketing(brackets: String) -> Bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(correct_bracketing(brackets: \"<>\") == true)\nassert(correct_bracketing(brackets: \"<<><>>\") == true)\nassert(correct_bracketing(brackets: \"<><><<><>><>\") == true)\nassert(correct_bracketing(brackets: \"<><><<<><><>><>><<><><<>>>\") == true)\nassert(correct_bracketing(brackets: \"<<<><>>>>\") == false)\nassert(correct_bracketing(brackets: \"><<>\") == false)\nassert(correct_bracketing(brackets: \"<\") == false)\nassert(correct_bracketing(brackets: \"<<<<\") == false)\nassert(correct_bracketing(brackets: \">\") == false)\nassert(correct_bracketing(brackets: \"<<>\") == false)\nassert(correct_bracketing(brackets: \"<><><<><>><>><<>\") == false)\nassert(correct_bracketing(brackets: \"<><><<><>><>>><>\") == false)", "stop_tokens": ["\n}"], "task_id": "HumanEval_56_correct_bracketing", "test": "}\n\n\nfunc ==(left: [(Int, Int)], right: [(Int, Int)]) -> Bool {\n    if left.count != right.count {\n        return false\n    }\n    for (l, r) in zip(left, right) {\n        if l != r {\n            return false\n        }\n    }\n    return true\n}\n            \nassert(correct_bracketing(brackets: \"<>\") == true)\nassert(correct_bracketing(brackets: \"<<><>>\") == true)\nassert(correct_bracketing(brackets: \"<><><<><>><>\") == true)\nassert(correct_bracketing(brackets: \"<><><<<><><>><>><<><><<>>>\") == true)\nassert(correct_bracketing(brackets: \"<<<><>>>>\") == false)\nassert(correct_bracketing(brackets: \"><<>\") == false)\nassert(correct_bracketing(brackets: \"<\") == false)\nassert(correct_bracketing(brackets: \"<<<<\") == false)\nassert(correct_bracketing(brackets: \">\") == false)\nassert(correct_bracketing(brackets: \"<<>\") == false)\nassert(correct_bracketing(brackets: \"<><><<><>><>><<>\") == false)\nassert(correct_bracketing(brackets: \"<><><<><>><>>><>\") == false)"}
