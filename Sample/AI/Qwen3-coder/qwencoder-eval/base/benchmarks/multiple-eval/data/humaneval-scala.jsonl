{"name": "HumanEval_23_strlen", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return length of given string\n    // >>> stringLength((\"\"))\n    // (0l)\n    // >>> stringLength((\"abc\"))\n    // (3l)\n    def strlen(string : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(strlen((\"\")) == (0l));\n    assert(strlen((\"x\")) == (1l));\n    assert(strlen((\"asdasnakj\")) == (9l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_23_strlen", "test": "    }\n    def main(args: Array[String]) = {\n    assert(strlen((\"\")) == (0l));\n    assert(strlen((\"x\")) == (1l));\n    assert(strlen((\"asdasnakj\")) == (9l));\n    }\n\n}\n"}
{"name": "HumanEval_89_encrypt", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function encrypt that takes a string as an argument and\n    // returns a string encrypted with the alphabet being rotated. \n    // The alphabet should be rotated in a manner such that the letters \n    // shift down by two multiplied to two places.\n    // For example:\n    // >>> encrypt((\"hi\"))\n    // (\"lm\")\n    // >>> encrypt((\"asdfghjkl\"))\n    // (\"ewhjklnop\")\n    // >>> encrypt((\"gf\"))\n    // (\"kj\")\n    // >>> encrypt((\"et\"))\n    // (\"ix\")\n    def encrypt(s : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(encrypt((\"hi\")).equals((\"lm\")));\n    assert(encrypt((\"asdfghjkl\")).equals((\"ewhjklnop\")));\n    assert(encrypt((\"gf\")).equals((\"kj\")));\n    assert(encrypt((\"et\")).equals((\"ix\")));\n    assert(encrypt((\"faewfawefaewg\")).equals((\"jeiajeaijeiak\")));\n    assert(encrypt((\"hellomyfriend\")).equals((\"lippsqcjvmirh\")));\n    assert(encrypt((\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")).equals((\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\")));\n    assert(encrypt((\"a\")).equals((\"e\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_89_encrypt", "test": "    }\n    def main(args: Array[String]) = {\n    assert(encrypt((\"hi\")).equals((\"lm\")));\n    assert(encrypt((\"asdfghjkl\")).equals((\"ewhjklnop\")));\n    assert(encrypt((\"gf\")).equals((\"kj\")));\n    assert(encrypt((\"et\")).equals((\"ix\")));\n    assert(encrypt((\"faewfawefaewg\")).equals((\"jeiajeaijeiak\")));\n    assert(encrypt((\"hellomyfriend\")).equals((\"lippsqcjvmirh\")));\n    assert(encrypt((\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")).equals((\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\")));\n    assert(encrypt((\"a\")).equals((\"e\")));\n    }\n\n}\n"}
{"name": "HumanEval_95_check_dict_case", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a map, return true if all keys are strings in lower \n    // case or all keys are strings in upper case, else return false.\n    // The function should return false is the given map is empty.\n    // Examples:\n    // >>> checkDictCase((Map[String,String](\"a\" -> \"apple\", \"b\" -> \"banana\")))\n    // (true)\n    // >>> checkDictCase((Map[String,String](\"a\" -> \"apple\", \"A\" -> \"banana\", \"B\" -> \"banana\")))\n    // (false)\n    // >>> checkDictCase((Map[String,String](\"a\" -> \"apple\", 8l -> \"banana\", \"a\" -> \"apple\")))\n    // (false)\n    // >>> checkDictCase((Map[String,String](\"Name\" -> \"John\", \"Age\" -> \"36\", \"City\" -> \"Houston\")))\n    // (false)\n    // >>> checkDictCase((Map[String,String](\"STATE\" -> \"NC\", \"ZIP\" -> \"12345\")))\n    // (true)\n    def checkDictCase(dict : Map[String,String]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(checkDictCase((Map[String,String](\"p\" -> \"pineapple\", \"b\" -> \"banana\"))) == (true));\n    assert(checkDictCase((Map[String,String](\"p\" -> \"pineapple\", \"A\" -> \"banana\", \"B\" -> \"banana\"))) == (false));\n    assert(checkDictCase((Map[String,String](\"p\" -> \"pineapple\", \"5\" -> \"banana\", \"a\" -> \"apple\"))) == (false));\n    assert(checkDictCase((Map[String,String](\"Name\" -> \"John\", \"Age\" -> \"36\", \"City\" -> \"Houston\"))) == (false));\n    assert(checkDictCase((Map[String,String](\"STATE\" -> \"NC\", \"ZIP\" -> \"12345\"))) == (true));\n    assert(checkDictCase((Map[String,String](\"fruit\" -> \"Orange\", \"taste\" -> \"Sweet\"))) == (true));\n    assert(checkDictCase((Map[String,String]())) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_95_check_dict_case", "test": "    }\n    def main(args: Array[String]) = {\n    assert(checkDictCase((Map[String,String](\"p\" -> \"pineapple\", \"b\" -> \"banana\"))) == (true));\n    assert(checkDictCase((Map[String,String](\"p\" -> \"pineapple\", \"A\" -> \"banana\", \"B\" -> \"banana\"))) == (false));\n    assert(checkDictCase((Map[String,String](\"p\" -> \"pineapple\", \"5\" -> \"banana\", \"a\" -> \"apple\"))) == (false));\n    assert(checkDictCase((Map[String,String](\"Name\" -> \"John\", \"Age\" -> \"36\", \"City\" -> \"Houston\"))) == (false));\n    assert(checkDictCase((Map[String,String](\"STATE\" -> \"NC\", \"ZIP\" -> \"12345\"))) == (true));\n    assert(checkDictCase((Map[String,String](\"fruit\" -> \"Orange\", \"taste\" -> \"Sweet\"))) == (true));\n    assert(checkDictCase((Map[String,String]())) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_85_add", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a non-empty list of integers lst. add the even elements that are at odd indices..\n    // Examples:\n    // >>> add((List[Long](4l.toLong, 2l.toLong, 6l.toLong, 7l.toLong)))\n    // (2l)\n    def add(lst : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(add((List[Long](4l.toLong, 88l.toLong))) == (88l));\n    assert(add((List[Long](4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 2l.toLong, 122l.toLong))) == (122l));\n    assert(add((List[Long](4l.toLong, 0l.toLong, 6l.toLong, 7l.toLong))) == (0l));\n    assert(add((List[Long](4l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_85_add", "test": "    }\n    def main(args: Array[String]) = {\n    assert(add((List[Long](4l.toLong, 88l.toLong))) == (88l));\n    assert(add((List[Long](4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 2l.toLong, 122l.toLong))) == (122l));\n    assert(add((List[Long](4l.toLong, 0l.toLong, 6l.toLong, 7l.toLong))) == (0l));\n    assert(add((List[Long](4l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))) == (12l));\n    }\n\n}\n"}
{"name": "HumanEval_140_fix_spaces", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string text, replace all spaces in it with underscores, \n    // and if a string has more than 2 consecutive spaces, \n    // then replace all consecutive spaces with - \n    // >>> fixSpaces((\" Example\"))\n    // (\"Example\")\n    // >>> fixSpaces((\" Example 1\"))\n    // (\"Example_1\")\n    // >>> fixSpaces((\" Example 2\"))\n    // (\"_Example_2\")\n    // >>> fixSpaces((\" Example 3\"))\n    // (\"_Example-3\")\n    def fixSpaces(text : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fixSpaces((\"Example\")).equals((\"Example\")));\n    assert(fixSpaces((\"Mudasir Hanif \")).equals((\"Mudasir_Hanif_\")));\n    assert(fixSpaces((\"Yellow Yellow  Dirty  Fellow\")).equals((\"Yellow_Yellow__Dirty__Fellow\")));\n    assert(fixSpaces((\"Exa   mple\")).equals((\"Exa-mple\")));\n    assert(fixSpaces((\"   Exa 1 2 2 mple\")).equals((\"-Exa_1_2_2_mple\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_140_fix_spaces", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fixSpaces((\"Example\")).equals((\"Example\")));\n    assert(fixSpaces((\"Mudasir Hanif \")).equals((\"Mudasir_Hanif_\")));\n    assert(fixSpaces((\"Yellow Yellow  Dirty  Fellow\")).equals((\"Yellow_Yellow__Dirty__Fellow\")));\n    assert(fixSpaces((\"Exa   mple\")).equals((\"Exa-mple\")));\n    assert(fixSpaces((\"   Exa 1 2 2 mple\")).equals((\"-Exa_1_2_2_mple\")));\n    }\n\n}\n"}
{"name": "HumanEval_63_fibfib", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n    // fibfib(0) == 0\n    // fibfib(1) == 0\n    // fibfib(2) == 1\n    // fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n    // Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n    // >>> fibfib((1l))\n    // (0l)\n    // >>> fibfib((5l))\n    // (4l)\n    // >>> fibfib((8l))\n    // (24l)\n    def fibfib(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fibfib((2l)) == (1l));\n    assert(fibfib((1l)) == (0l));\n    assert(fibfib((5l)) == (4l));\n    assert(fibfib((8l)) == (24l));\n    assert(fibfib((10l)) == (81l));\n    assert(fibfib((12l)) == (274l));\n    assert(fibfib((14l)) == (927l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_63_fibfib", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fibfib((2l)) == (1l));\n    assert(fibfib((1l)) == (0l));\n    assert(fibfib((5l)) == (4l));\n    assert(fibfib((8l)) == (24l));\n    assert(fibfib((10l)) == (81l));\n    assert(fibfib((12l)) == (274l));\n    assert(fibfib((14l)) == (927l));\n    }\n\n}\n"}
{"name": "HumanEval_151_double_the_difference", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of numbers, return the sum of squares of the numbers\n    // in the list that are odd. Ignore numbers that are negative or not integers.\n    // >>> doubleTheDifference((List[Float](1l.toLong, 3l.toLong, 2l.toLong, 0l.toLong)))\n    // (10l)\n    // >>> doubleTheDifference((List[Float](-1l.toLong, -2l.toLong, 0l.toLong)))\n    // (0l)\n    // >>> doubleTheDifference((List[Float](9l.toLong, -2l.toLong)))\n    // (81l)\n    // >>> doubleTheDifference((List[Float](0l.toLong)))\n    // (0l)\n    // If the input list is empty, return 0.\n    def doubleTheDifference(lst : List[Float]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(doubleTheDifference((List[Float]())) == (0l));\n    assert(doubleTheDifference((List[Float](5.0f.toFloat, 4.0f.toFloat))) == (25l));\n    assert(doubleTheDifference((List[Float](0.1f.toFloat, 0.2f.toFloat, 0.3f.toFloat))) == (0l));\n    assert(doubleTheDifference((List[Float](-10.0f.toFloat, -20.0f.toFloat, -30.0f.toFloat))) == (0l));\n    assert(doubleTheDifference((List[Float](-1.0f.toFloat, -2.0f.toFloat, 8.0f.toFloat))) == (0l));\n    assert(doubleTheDifference((List[Float](0.2f.toFloat, 3.0f.toFloat, 5.0f.toFloat))) == (34l));\n    assert(doubleTheDifference((List[Float](-9.0f.toFloat, -7.0f.toFloat, -5.0f.toFloat, -3.0f.toFloat, -1.0f.toFloat, 1.0f.toFloat, 3.0f.toFloat, 5.0f.toFloat, 7.0f.toFloat, 9.0f.toFloat))) == (165l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_151_double_the_difference", "test": "    }\n    def main(args: Array[String]) = {\n    assert(doubleTheDifference((List[Float]())) == (0l));\n    assert(doubleTheDifference((List[Float](5.0f.toFloat, 4.0f.toFloat))) == (25l));\n    assert(doubleTheDifference((List[Float](0.1f.toFloat, 0.2f.toFloat, 0.3f.toFloat))) == (0l));\n    assert(doubleTheDifference((List[Float](-10.0f.toFloat, -20.0f.toFloat, -30.0f.toFloat))) == (0l));\n    assert(doubleTheDifference((List[Float](-1.0f.toFloat, -2.0f.toFloat, 8.0f.toFloat))) == (0l));\n    assert(doubleTheDifference((List[Float](0.2f.toFloat, 3.0f.toFloat, 5.0f.toFloat))) == (34l));\n    assert(doubleTheDifference((List[Float](-9.0f.toFloat, -7.0f.toFloat, -5.0f.toFloat, -3.0f.toFloat, -1.0f.toFloat, 1.0f.toFloat, 3.0f.toFloat, 5.0f.toFloat, 7.0f.toFloat, 9.0f.toFloat))) == (165l));\n    }\n\n}\n"}
{"name": "HumanEval_22_filter_integers", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Filter given list of any scalathon values only for integers\n    // >>> filterIntegers((List[Any](\"a\", 3.14f, 5l)))\n    // (List[Long](5l.toLong))\n    // >>> filterIntegers((List[Any](1l, 2l, 3l, \"abc\", Map[Long,Long](), List[Long]())))\n    // (List[Long](1l.toLong, 2l.toLong, 3l.toLong))\n    def filterIntegers(values : List[Any]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(filterIntegers((List[Any]())).equals((List[Long]())));\n    assert(filterIntegers((List[Any](4l, Map[Long,Long](), List[Long](), 23.2f, 9l, \"adasd\"))).equals((List[Long](4l.toLong, 9l.toLong))));\n    assert(filterIntegers((List[Any](3l, \"c\", 3l, 3l, \"a\", \"b\"))).equals((List[Long](3l.toLong, 3l.toLong, 3l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_22_filter_integers", "test": "    }\n    def main(args: Array[String]) = {\n    assert(filterIntegers((List[Any]())).equals((List[Long]())));\n    assert(filterIntegers((List[Any](4l, Map[Long,Long](), List[Long](), 23.2f, 9l, \"adasd\"))).equals((List[Long](4l.toLong, 9l.toLong))));\n    assert(filterIntegers((List[Any](3l, \"c\", 3l, 3l, \"a\", \"b\"))).equals((List[Long](3l.toLong, 3l.toLong, 3l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_41_car_race_collision", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Imagine a road that's a perfectly straight infinitely long line.\n    // n cars are driving left to right;  simultaneously, a different set of n cars\n    // are driving right to left.   The two sets of cars start out being very far from\n    // each other.  All cars move in the same speed.  Two cars are said to collide\n    // when a car that's moving left to right hits a car that's moving right to left.\n    // However, the cars are infinitely sturdy and strong; as a result, they continue moving\n    // in their trajectory as if they did not collide.\n    // This function outputs the number of such collisions.\n    def carRaceCollision(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(carRaceCollision((2l)) == (4l));\n    assert(carRaceCollision((3l)) == (9l));\n    assert(carRaceCollision((4l)) == (16l));\n    assert(carRaceCollision((8l)) == (64l));\n    assert(carRaceCollision((10l)) == (100l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_41_car_race_collision", "test": "    }\n    def main(args: Array[String]) = {\n    assert(carRaceCollision((2l)) == (4l));\n    assert(carRaceCollision((3l)) == (9l));\n    assert(carRaceCollision((4l)) == (16l));\n    assert(carRaceCollision((8l)) == (64l));\n    assert(carRaceCollision((10l)) == (100l));\n    }\n\n}\n"}
{"name": "HumanEval_17_parse_music", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Input to this function is a string representing musical notes in a special ASCII format.\n    // Your task is to parse this string and return list of integers corresponding to how many beats does each\n    // not last.\n    // Here is a legend:\n    // 'o' - whole note, lasts four beats\n    // 'o|' - half note, lasts two beats\n    // '.|' - quater note, lasts one beat\n    // >>> parseMusic((\"o o| .| o| o| .| .| .| .| o o\"))\n    // (List[Long](4l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 4l.toLong, 4l.toLong))\n    def parseMusic(music_string : String) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(parseMusic((\"\")).equals((List[Long]())));\n    assert(parseMusic((\"o o o o\")).equals((List[Long](4l.toLong, 4l.toLong, 4l.toLong, 4l.toLong))));\n    assert(parseMusic((\".| .| .| .|\")).equals((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))));\n    assert(parseMusic((\"o| o| .| .| o o o o\")).equals((List[Long](2l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 4l.toLong, 4l.toLong, 4l.toLong, 4l.toLong))));\n    assert(parseMusic((\"o| .| o| .| o o| o o|\")).equals((List[Long](2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 4l.toLong, 2l.toLong, 4l.toLong, 2l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_17_parse_music", "test": "    }\n    def main(args: Array[String]) = {\n    assert(parseMusic((\"\")).equals((List[Long]())));\n    assert(parseMusic((\"o o o o\")).equals((List[Long](4l.toLong, 4l.toLong, 4l.toLong, 4l.toLong))));\n    assert(parseMusic((\".| .| .| .|\")).equals((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))));\n    assert(parseMusic((\"o| o| .| .| o o o o\")).equals((List[Long](2l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 4l.toLong, 4l.toLong, 4l.toLong, 4l.toLong))));\n    assert(parseMusic((\"o| .| o| .| o o| o o|\")).equals((List[Long](2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 4l.toLong, 2l.toLong, 4l.toLong, 2l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_79_decimal_to_binary", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You will be given a number in decimal form and your task is to convert it to\n    // binary format. The function should return a string, with each character representing a binary\n    // number. Each character in the string will be '0' or '1'.\n    // There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n    // The extra characters are there to help with the format.\n    // Examples:\n    // >>> decimalToBinary((15l))\n    // (\"db1111db\")\n    // >>> decimalToBinary((32l))\n    // (\"db100000db\")\n    def decimalToBinary(decimal : Long) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(decimalToBinary((0l)).equals((\"db0db\")));\n    assert(decimalToBinary((32l)).equals((\"db100000db\")));\n    assert(decimalToBinary((103l)).equals((\"db1100111db\")));\n    assert(decimalToBinary((15l)).equals((\"db1111db\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_79_decimal_to_binary", "test": "    }\n    def main(args: Array[String]) = {\n    assert(decimalToBinary((0l)).equals((\"db0db\")));\n    assert(decimalToBinary((32l)).equals((\"db100000db\")));\n    assert(decimalToBinary((103l)).equals((\"db1100111db\")));\n    assert(decimalToBinary((15l)).equals((\"db1111db\")));\n    }\n\n}\n"}
{"name": "HumanEval_14_all_prefixes", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return list of all prefixes from shortest to longest of the input string\n    // >>> allPrefixes((\"abc\"))\n    // (List[String](\"a\", \"ab\", \"abc\"))\n    def allPrefixes(string : String) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(allPrefixes((\"\")).equals((List[String]())));\n    assert(allPrefixes((\"asdfgh\")).equals((List[String](\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"))));\n    assert(allPrefixes((\"WWW\")).equals((List[String](\"W\", \"WW\", \"WWW\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_14_all_prefixes", "test": "    }\n    def main(args: Array[String]) = {\n    assert(allPrefixes((\"\")).equals((List[String]())));\n    assert(allPrefixes((\"asdfgh\")).equals((List[String](\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"))));\n    assert(allPrefixes((\"WWW\")).equals((List[String](\"W\", \"WW\", \"WWW\"))));\n    }\n\n}\n"}
{"name": "HumanEval_53_add", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Add two numbers x and y\n    // >>> add((2l), (3l))\n    // (5l)\n    // >>> add((5l), (7l))\n    // (12l)\n    def add(x : Long, y : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(add((0l), (1l)) == (1l));\n    assert(add((1l), (0l)) == (1l));\n    assert(add((2l), (3l)) == (5l));\n    assert(add((5l), (7l)) == (12l));\n    assert(add((7l), (5l)) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_53_add", "test": "    }\n    def main(args: Array[String]) = {\n    assert(add((0l), (1l)) == (1l));\n    assert(add((1l), (0l)) == (1l));\n    assert(add((2l), (3l)) == (5l));\n    assert(add((5l), (7l)) == (12l));\n    assert(add((7l), (5l)) == (12l));\n    }\n\n}\n"}
{"name": "HumanEval_159_eat", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You're a hungry rabbit, and you already have eaten a certain number of carrots,\n    // but now you need to eat more carrots to complete the day's meals.\n    // you should return a list of [ total number of eaten carrots after your meals,\n    // the number of carrots left after your meals ]\n    // if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n    // Example:\n    // >>> eat((5l), (6l), (10l))\n    // (List[Long](11l.toLong, 4l.toLong))\n    // >>> eat((4l), (8l), (9l))\n    // (List[Long](12l.toLong, 1l.toLong))\n    // >>> eat((1l), (10l), (10l))\n    // (List[Long](11l.toLong, 0l.toLong))\n    // >>> eat((2l), (11l), (5l))\n    // (List[Long](7l.toLong, 0l.toLong))\n    // Variables:\n    // @number : integer\n    // the number of carrots that you have eaten.\n    // @need : integer\n    // the number of carrots that you need to eat.\n    // @remaining : integer\n    // the number of remaining carrots thet exist in stock\n    // Constrain:\n    // * 0 <= number <= 1000\n    // * 0 <= need <= 1000\n    // * 0 <= remaining <= 1000\n    // Have fun :)\n    def eat(number : Long, need : Long, remaining : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(eat((5l), (6l), (10l)).equals((List[Long](11l.toLong, 4l.toLong))));\n    assert(eat((4l), (8l), (9l)).equals((List[Long](12l.toLong, 1l.toLong))));\n    assert(eat((1l), (10l), (10l)).equals((List[Long](11l.toLong, 0l.toLong))));\n    assert(eat((2l), (11l), (5l)).equals((List[Long](7l.toLong, 0l.toLong))));\n    assert(eat((4l), (5l), (7l)).equals((List[Long](9l.toLong, 2l.toLong))));\n    assert(eat((4l), (5l), (1l)).equals((List[Long](5l.toLong, 0l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_159_eat", "test": "    }\n    def main(args: Array[String]) = {\n    assert(eat((5l), (6l), (10l)).equals((List[Long](11l.toLong, 4l.toLong))));\n    assert(eat((4l), (8l), (9l)).equals((List[Long](12l.toLong, 1l.toLong))));\n    assert(eat((1l), (10l), (10l)).equals((List[Long](11l.toLong, 0l.toLong))));\n    assert(eat((2l), (11l), (5l)).equals((List[Long](7l.toLong, 0l.toLong))));\n    assert(eat((4l), (5l), (7l)).equals((List[Long](9l.toLong, 2l.toLong))));\n    assert(eat((4l), (5l), (1l)).equals((List[Long](5l.toLong, 0l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_115_max_fill", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a rectangular grid of wells. Each row represents a single well,\n    // and each 1 in a row represents a single unit of water.\n    // Each well has a corresponding bucket that can be used to extract water from it, \n    // and all buckets have the same capacity.\n    // Your task is to use the buckets to empty the wells.\n    // Output the number of times you need to lower the buckets.\n    // Example 1:\n    // >>> maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 1l.toLong, 0l.toLong), List[Long](0l.toLong, 1l.toLong, 0l.toLong, 0l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (1l))\n    // (6l)\n    // Example 2:\n    // >>> maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 1l.toLong, 1l.toLong), List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](0l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (2l))\n    // (5l)\n    // Example 3:\n    // >>> maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 0l.toLong), List[Long](0l.toLong, 0l.toLong, 0l.toLong))), (5l))\n    // (0l)\n    // Constraints:\n    // * all wells have the same length\n    // * 1 <= grid.length <= 10^2\n    // * 1 <= grid[:,1].length <= 10^2\n    // * grid[i][j] -> 0 | 1\n    // * 1 <= capacity <= 10\n    def maxFill(grid : List[List[Long]], capacity : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 1l.toLong, 0l.toLong), List[Long](0l.toLong, 1l.toLong, 0l.toLong, 0l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (1l)) == (6l));\n    assert(maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 1l.toLong, 1l.toLong), List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](0l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (2l)) == (5l));\n    assert(maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 0l.toLong), List[Long](0l.toLong, 0l.toLong, 0l.toLong))), (5l)) == (0l));\n    assert(maxFill((List[List[Long]](List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (2l)) == (4l));\n    assert(maxFill((List[List[Long]](List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (9l)) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_115_max_fill", "test": "    }\n    def main(args: Array[String]) = {\n    assert(maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 1l.toLong, 0l.toLong), List[Long](0l.toLong, 1l.toLong, 0l.toLong, 0l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (1l)) == (6l));\n    assert(maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 1l.toLong, 1l.toLong), List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](0l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (2l)) == (5l));\n    assert(maxFill((List[List[Long]](List[Long](0l.toLong, 0l.toLong, 0l.toLong), List[Long](0l.toLong, 0l.toLong, 0l.toLong))), (5l)) == (0l));\n    assert(maxFill((List[List[Long]](List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (2l)) == (4l));\n    assert(maxFill((List[List[Long]](List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong), List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))), (9l)) == (2l));\n    }\n\n}\n"}
{"name": "HumanEval_160_do_algebra", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given two lists operator, and operand. The first list has basic algebra operations, and \n    // the second list is a list of integers. Use the two given lists to build the algebric \n    // expression and return the evaluation of this expression.\n    // The basic algebra operations:\n    // Addition ( + ) \n    // Subtraction ( - ) \n    // Multiplication ( * ) \n    // Floor division ( // ) \n    // Exponentiation ( ** ) \n    // Example:\n    // operator['+', '*', '-']\n    // list = [2, 3, 4, 5]\n    // result = 2 + 3 * 4 - 5\n    // => result = 9\n    // Note:\n    // The length of operator list is equal to the length of operand list minus one.\n    // Operand is a list of of non-negative integers.\n    // Operator list has at least one operator, and operand list has at least two operands.\n    def doAlgebra(op : List[String], operand : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(doAlgebra((List[String](\"**\", \"*\", \"+\")), (List[Long](2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (37l));\n    assert(doAlgebra((List[String](\"+\", \"*\", \"-\")), (List[Long](2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (9l));\n    assert(doAlgebra((List[String](\"//\", \"*\")), (List[Long](7l.toLong, 3l.toLong, 4l.toLong))) == (8l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_160_do_algebra", "test": "    }\n    def main(args: Array[String]) = {\n    assert(doAlgebra((List[String](\"**\", \"*\", \"+\")), (List[Long](2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (37l));\n    assert(doAlgebra((List[String](\"+\", \"*\", \"-\")), (List[Long](2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (9l));\n    assert(doAlgebra((List[String](\"//\", \"*\")), (List[Long](7l.toLong, 3l.toLong, 4l.toLong))) == (8l));\n    }\n\n}\n"}
{"name": "HumanEval_27_flip_case", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n    // >>> flipCase((\"Hello\"))\n    // (\"hELLO\")\n    def flipCase(string : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(flipCase((\"\")).equals((\"\")));\n    assert(flipCase((\"Hello!\")).equals((\"hELLO!\")));\n    assert(flipCase((\"These violent delights have violent ends\")).equals((\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_27_flip_case", "test": "    }\n    def main(args: Array[String]) = {\n    assert(flipCase((\"\")).equals((\"\")));\n    assert(flipCase((\"Hello!\")).equals((\"hELLO!\")));\n    assert(flipCase((\"These violent delights have violent ends\")).equals((\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\")));\n    }\n\n}\n"}
{"name": "HumanEval_105_by_length", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of integers, sort the integers that are between 1 and 9 inclusive,\n    // reverse the resulting list, and then replace each digit by its corresponding name from\n    // \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n    // For example:\n    // >>> byLength((List[Long](2l.toLong, 1l.toLong, 1l.toLong, 4l.toLong, 5l.toLong, 8l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[String](\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"))\n    // If the list is empty, return an empty list:\n    // >>> byLength((List[Long]()))\n    // (List[String]())\n    // If the list has any strange number ignore it:\n    // >>> byLength((List[Long](1l.toLong, -1l.toLong, 55l.toLong)))\n    // (List[String](\"One\"))\n    def byLength(arr : List[Long]) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(byLength((List[Long](2l.toLong, 1l.toLong, 1l.toLong, 4l.toLong, 5l.toLong, 8l.toLong, 2l.toLong, 3l.toLong))).equals((List[String](\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"))));\n    assert(byLength((List[Long]())).equals((List[String]())));\n    assert(byLength((List[Long](1l.toLong, -1l.toLong, 55l.toLong))).equals((List[String](\"One\"))));\n    assert(byLength((List[Long](1l.toLong, -1l.toLong, 3l.toLong, 2l.toLong))).equals((List[String](\"Three\", \"Two\", \"One\"))));\n    assert(byLength((List[Long](9l.toLong, 4l.toLong, 8l.toLong))).equals((List[String](\"Nine\", \"Eight\", \"Four\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_105_by_length", "test": "    }\n    def main(args: Array[String]) = {\n    assert(byLength((List[Long](2l.toLong, 1l.toLong, 1l.toLong, 4l.toLong, 5l.toLong, 8l.toLong, 2l.toLong, 3l.toLong))).equals((List[String](\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"))));\n    assert(byLength((List[Long]())).equals((List[String]())));\n    assert(byLength((List[Long](1l.toLong, -1l.toLong, 55l.toLong))).equals((List[String](\"One\"))));\n    assert(byLength((List[Long](1l.toLong, -1l.toLong, 3l.toLong, 2l.toLong))).equals((List[String](\"Three\", \"Two\", \"One\"))));\n    assert(byLength((List[Long](9l.toLong, 4l.toLong, 8l.toLong))).equals((List[String](\"Nine\", \"Eight\", \"Four\"))));\n    }\n\n}\n"}
{"name": "HumanEval_25_factorize", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return list of prime factors of given integer in the order from smallest to largest.\n    // Each of the factors should be listed number of times corresponding to how many times it appeares in factorization.\n    // Input number should be equal to the product of all factors\n    // >>> factorize((8l))\n    // (List[Long](2l.toLong, 2l.toLong, 2l.toLong))\n    // >>> factorize((25l))\n    // (List[Long](5l.toLong, 5l.toLong))\n    // >>> factorize((70l))\n    // (List[Long](2l.toLong, 5l.toLong, 7l.toLong))\n    def factorize(n : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(factorize((2l)).equals((List[Long](2l.toLong))));\n    assert(factorize((4l)).equals((List[Long](2l.toLong, 2l.toLong))));\n    assert(factorize((8l)).equals((List[Long](2l.toLong, 2l.toLong, 2l.toLong))));\n    assert(factorize((57l)).equals((List[Long](3l.toLong, 19l.toLong))));\n    assert(factorize((3249l)).equals((List[Long](3l.toLong, 3l.toLong, 19l.toLong, 19l.toLong))));\n    assert(factorize((185193l)).equals((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 19l.toLong, 19l.toLong, 19l.toLong))));\n    assert(factorize((20577l)).equals((List[Long](3l.toLong, 19l.toLong, 19l.toLong, 19l.toLong))));\n    assert(factorize((18l)).equals((List[Long](2l.toLong, 3l.toLong, 3l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_25_factorize", "test": "    }\n    def main(args: Array[String]) = {\n    assert(factorize((2l)).equals((List[Long](2l.toLong))));\n    assert(factorize((4l)).equals((List[Long](2l.toLong, 2l.toLong))));\n    assert(factorize((8l)).equals((List[Long](2l.toLong, 2l.toLong, 2l.toLong))));\n    assert(factorize((57l)).equals((List[Long](3l.toLong, 19l.toLong))));\n    assert(factorize((3249l)).equals((List[Long](3l.toLong, 3l.toLong, 19l.toLong, 19l.toLong))));\n    assert(factorize((185193l)).equals((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 19l.toLong, 19l.toLong, 19l.toLong))));\n    assert(factorize((20577l)).equals((List[Long](3l.toLong, 19l.toLong, 19l.toLong, 19l.toLong))));\n    assert(factorize((18l)).equals((List[Long](2l.toLong, 3l.toLong, 3l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_96_count_up_to", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Implement a function that takes an non-negative integer and returns a list of the first n\n    // integers that are prime numbers and less than n.\n    // for example:\n    // >>> countUpTo((5l))\n    // (List[Long](2l.toLong, 3l.toLong))\n    // >>> countUpTo((11l))\n    // (List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))\n    // >>> countUpTo((0l))\n    // (List[Long]())\n    // >>> countUpTo((20l))\n    // (List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong))\n    // >>> countUpTo((1l))\n    // (List[Long]())\n    // >>> countUpTo((18l))\n    // (List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong))\n    def countUpTo(n : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(countUpTo((5l)).equals((List[Long](2l.toLong, 3l.toLong))));\n    assert(countUpTo((6l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong))));\n    assert(countUpTo((7l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong))));\n    assert(countUpTo((10l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))));\n    assert(countUpTo((0l)).equals((List[Long]())));\n    assert(countUpTo((22l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong))));\n    assert(countUpTo((1l)).equals((List[Long]())));\n    assert(countUpTo((18l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong))));\n    assert(countUpTo((47l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong, 23l.toLong, 29l.toLong, 31l.toLong, 37l.toLong, 41l.toLong, 43l.toLong))));\n    assert(countUpTo((101l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong, 23l.toLong, 29l.toLong, 31l.toLong, 37l.toLong, 41l.toLong, 43l.toLong, 47l.toLong, 53l.toLong, 59l.toLong, 61l.toLong, 67l.toLong, 71l.toLong, 73l.toLong, 79l.toLong, 83l.toLong, 89l.toLong, 97l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_96_count_up_to", "test": "    }\n    def main(args: Array[String]) = {\n    assert(countUpTo((5l)).equals((List[Long](2l.toLong, 3l.toLong))));\n    assert(countUpTo((6l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong))));\n    assert(countUpTo((7l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong))));\n    assert(countUpTo((10l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))));\n    assert(countUpTo((0l)).equals((List[Long]())));\n    assert(countUpTo((22l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong))));\n    assert(countUpTo((1l)).equals((List[Long]())));\n    assert(countUpTo((18l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong))));\n    assert(countUpTo((47l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong, 23l.toLong, 29l.toLong, 31l.toLong, 37l.toLong, 41l.toLong, 43l.toLong))));\n    assert(countUpTo((101l)).equals((List[Long](2l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong, 19l.toLong, 23l.toLong, 29l.toLong, 31l.toLong, 37l.toLong, 41l.toLong, 43l.toLong, 47l.toLong, 53l.toLong, 59l.toLong, 61l.toLong, 67l.toLong, 71l.toLong, 73l.toLong, 79l.toLong, 83l.toLong, 89l.toLong, 97l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_34_unique", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return sorted unique elements in a list\n    // >>> unique((List[Long](5l.toLong, 3l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong)))\n    // (List[Long](0l.toLong, 2l.toLong, 3l.toLong, 5l.toLong, 9l.toLong, 123l.toLong))\n    def unique(l : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(unique((List[Long](5l.toLong, 3l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong))).equals((List[Long](0l.toLong, 2l.toLong, 3l.toLong, 5l.toLong, 9l.toLong, 123l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_34_unique", "test": "    }\n    def main(args: Array[String]) = {\n    assert(unique((List[Long](5l.toLong, 3l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong))).equals((List[Long](0l.toLong, 2l.toLong, 3l.toLong, 5l.toLong, 9l.toLong, 123l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_74_total_match", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that accepts two lists of strings and returns the list that has \n    // total number of chars in the all strings of the list less than the other list.\n    // if the two lists have the same number of chars, return the first list.\n    // Examples\n    // >>> totalMatch((List[String]()), (List[String]()))\n    // (List[String]())\n    // >>> totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"Hi\")))\n    // (List[String](\"hI\", \"Hi\"))\n    // >>> totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hi\", \"hi\", \"admin\", \"project\")))\n    // (List[String](\"hi\", \"admin\"))\n    // >>> totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"hi\", \"hi\")))\n    // (List[String](\"hI\", \"hi\", \"hi\"))\n    // >>> totalMatch((List[String](\"4\")), (List[String](\"1\", \"2\", \"3\", \"4\", \"5\")))\n    // (List[String](\"4\"))\n    def totalMatch(lst1 : List[String], lst2 : List[String]) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(totalMatch((List[String]()), (List[String]())).equals((List[String]())));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hi\", \"hi\"))).equals((List[String](\"hi\", \"hi\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hi\", \"hi\", \"admin\", \"project\"))).equals((List[String](\"hi\", \"admin\"))));\n    assert(totalMatch((List[String](\"4\")), (List[String](\"1\", \"2\", \"3\", \"4\", \"5\"))).equals((List[String](\"4\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"Hi\"))).equals((List[String](\"hI\", \"Hi\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"hi\", \"hi\"))).equals((List[String](\"hI\", \"hi\", \"hi\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"hi\", \"hii\"))).equals((List[String](\"hi\", \"admin\"))));\n    assert(totalMatch((List[String]()), (List[String](\"this\"))).equals((List[String]())));\n    assert(totalMatch((List[String](\"this\")), (List[String]())).equals((List[String]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_74_total_match", "test": "    }\n    def main(args: Array[String]) = {\n    assert(totalMatch((List[String]()), (List[String]())).equals((List[String]())));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hi\", \"hi\"))).equals((List[String](\"hi\", \"hi\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hi\", \"hi\", \"admin\", \"project\"))).equals((List[String](\"hi\", \"admin\"))));\n    assert(totalMatch((List[String](\"4\")), (List[String](\"1\", \"2\", \"3\", \"4\", \"5\"))).equals((List[String](\"4\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"Hi\"))).equals((List[String](\"hI\", \"Hi\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"hi\", \"hi\"))).equals((List[String](\"hI\", \"hi\", \"hi\"))));\n    assert(totalMatch((List[String](\"hi\", \"admin\")), (List[String](\"hI\", \"hi\", \"hii\"))).equals((List[String](\"hi\", \"admin\"))));\n    assert(totalMatch((List[String]()), (List[String](\"this\"))).equals((List[String]())));\n    assert(totalMatch((List[String](\"this\")), (List[String]())).equals((List[String]())));\n    }\n\n}\n"}
{"name": "HumanEval_35_max_element", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return maximum element in the list.\n    // >>> maxElement((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (3l)\n    // >>> maxElement((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong, 1l.toLong, -10l.toLong)))\n    // (123l)\n    def maxElement(l : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(maxElement((List[Long](1l.toLong, 2l.toLong, 3l.toLong))) == (3l));\n    assert(maxElement((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 124l.toLong, 1l.toLong, -10l.toLong))) == (124l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_35_max_element", "test": "    }\n    def main(args: Array[String]) = {\n    assert(maxElement((List[Long](1l.toLong, 2l.toLong, 3l.toLong))) == (3l));\n    assert(maxElement((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 124l.toLong, 1l.toLong, -10l.toLong))) == (124l));\n    }\n\n}\n"}
{"name": "HumanEval_132_is_nested", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function that takes a string as input which contains only square brackets.\n    // The function should return true if and only if there is a valid subsequence of brackets \n    // where at least one bracket in the subsequence is nested.\n    // >>> isNested((\"[[]]\"))\n    // (true)\n    // >>> isNested((\"[]]]]]]][[[[[]\"))\n    // (false)\n    // >>> isNested((\"[][]\"))\n    // (false)\n    // >>> isNested((\"[]\"))\n    // (false)\n    // >>> isNested((\"[[][]]\"))\n    // (true)\n    // >>> isNested((\"[[]][[\"))\n    // (true)\n    def isNested(string : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isNested((\"[[]]\")) == (true));\n    assert(isNested((\"[]]]]]]][[[[[]\")) == (false));\n    assert(isNested((\"[][]\")) == (false));\n    assert(isNested((\"[]\")) == (false));\n    assert(isNested((\"[[[[]]]]\")) == (true));\n    assert(isNested((\"[]]]]]]]]]]\")) == (false));\n    assert(isNested((\"[][][[]]\")) == (true));\n    assert(isNested((\"[[]\")) == (false));\n    assert(isNested((\"[]]\")) == (false));\n    assert(isNested((\"[[]][[\")) == (true));\n    assert(isNested((\"[[][]]\")) == (true));\n    assert(isNested((\"\")) == (false));\n    assert(isNested((\"[[[[[[[[\")) == (false));\n    assert(isNested((\"]]]]]]]]\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_132_is_nested", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isNested((\"[[]]\")) == (true));\n    assert(isNested((\"[]]]]]]][[[[[]\")) == (false));\n    assert(isNested((\"[][]\")) == (false));\n    assert(isNested((\"[]\")) == (false));\n    assert(isNested((\"[[[[]]]]\")) == (true));\n    assert(isNested((\"[]]]]]]]]]]\")) == (false));\n    assert(isNested((\"[][][[]]\")) == (true));\n    assert(isNested((\"[[]\")) == (false));\n    assert(isNested((\"[]]\")) == (false));\n    assert(isNested((\"[[]][[\")) == (true));\n    assert(isNested((\"[[][]]\")) == (true));\n    assert(isNested((\"\")) == (false));\n    assert(isNested((\"[[[[[[[[\")) == (false));\n    assert(isNested((\"]]]]]]]]\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_103_rounded_avg", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given two positive integers n and m, and your task is to compute the\n    // average of the integers from n through m (including n and m). \n    // Round the answer to the nearest integer and convert that to binary.\n    // If n is greater than m, return -1.\n    // Example:\n    // >>> roundedAvg((1l), (5l))\n    // \"0b11\"\n    // >>> roundedAvg((7l), (5l))\n    // -1l\n    // >>> roundedAvg((10l), (20l))\n    // \"0b1111\"\n    // >>> roundedAvg((20l), (33l))\n    // \"0b11010\"\n    def roundedAvg(n : Long, m : Long) : Either[String, Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_103_rounded_avg.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(roundedAvg((1l), (5l)).equals(\"0b11\"));\n    assert(roundedAvg((7l), (13l)).equals(\"0b1010\"));\n    assert(roundedAvg((964l), (977l)).equals(\"0b1111001010\"));\n    assert(roundedAvg((996l), (997l)).equals(\"0b1111100100\"));\n    assert(roundedAvg((560l), (851l)).equals(\"0b1011000010\"));\n    assert(roundedAvg((185l), (546l)).equals(\"0b101101110\"));\n    assert(roundedAvg((362l), (496l)).equals(\"0b110101101\"));\n    assert(roundedAvg((350l), (902l)).equals(\"0b1001110010\"));\n    assert(roundedAvg((197l), (233l)).equals(\"0b11010111\"));\n    assert(roundedAvg((7l), (5l)).equals(-1l));\n    assert(roundedAvg((5l), (1l)).equals(-1l));\n    assert(roundedAvg((5l), (5l)).equals(\"0b101\"));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_103_rounded_avg", "test": "    }\n    def main(args: Array[String]) = {\n    assert(roundedAvg((1l), (5l)).equals(\"0b11\"));\n    assert(roundedAvg((7l), (13l)).equals(\"0b1010\"));\n    assert(roundedAvg((964l), (977l)).equals(\"0b1111001010\"));\n    assert(roundedAvg((996l), (997l)).equals(\"0b1111100100\"));\n    assert(roundedAvg((560l), (851l)).equals(\"0b1011000010\"));\n    assert(roundedAvg((185l), (546l)).equals(\"0b101101110\"));\n    assert(roundedAvg((362l), (496l)).equals(\"0b110101101\"));\n    assert(roundedAvg((350l), (902l)).equals(\"0b1001110010\"));\n    assert(roundedAvg((197l), (233l)).equals(\"0b11010111\"));\n    assert(roundedAvg((7l), (5l)).equals(-1l));\n    assert(roundedAvg((5l), (1l)).equals(-1l));\n    assert(roundedAvg((5l), (5l)).equals(\"0b101\"));\n    }\n\n}\n"}
{"name": "HumanEval_113_odd_count", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of strings, where each string consists of only digits, return a list.\n    // Each element i of the output should be \"the number of odd elements in the\n    // string i of the input.\" where all the i's should be replaced by the number\n    // of odd digits in the i'th string of the input.\n    // >>> oddCount((List[String](\"1234567\")))\n    // (List[String](\"the number of odd elements 4n the str4ng 4 of the 4nput.\"))\n    // >>> oddCount((List[String](\"3\", \"11111111\")))\n    // (List[String](\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"))\n    def oddCount(lst : List[String]) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(oddCount((List[String](\"1234567\"))).equals((List[String](\"the number of odd elements 4n the str4ng 4 of the 4nput.\"))));\n    assert(oddCount((List[String](\"3\", \"11111111\"))).equals((List[String](\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"))));\n    assert(oddCount((List[String](\"271\", \"137\", \"314\"))).equals((List[String](\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_113_odd_count", "test": "    }\n    def main(args: Array[String]) = {\n    assert(oddCount((List[String](\"1234567\"))).equals((List[String](\"the number of odd elements 4n the str4ng 4 of the 4nput.\"))));\n    assert(oddCount((List[String](\"3\", \"11111111\"))).equals((List[String](\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"))));\n    assert(oddCount((List[String](\"271\", \"137\", \"314\"))).equals((List[String](\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"))));\n    }\n\n}\n"}
{"name": "HumanEval_109_move_one_ball", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // We have a list 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n    // numbers in the list will be randomly ordered. Your task is to determine if\n    // it is possible to get a list sorted in non-decreasing order by performing \n    // the following operation on the given list:\n    // You are allowed to perform right shift operation any number of times.\n    // One right shift operation means shifting all elements of the list by one\n    // position in the right direction. The last element of the list will be moved to\n    // the starting position in the list i.e. 0th index. \n    // If it is possible to obtain the sorted list by performing the above operation\n    // then return true else return false.\n    // If the given list is empty then return true.\n    // Note: The given list is guaranteed to have unique elements.\n    // For Example:\n    // >>> moveOneBall((List[Long](3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong, 2l.toLong)))\n    // (true)\n    // Explanation: By performin 2 right shift operations, non-decreasing order can\n    // be achieved for the given list.\n    // >>> moveOneBall((List[Long](3l.toLong, 5l.toLong, 4l.toLong, 1l.toLong, 2l.toLong)))\n    // (false)\n    // Explanation:It is not possible to get non-decreasing order for the given\n    // list by performing any number of right shift operations.\n    def moveOneBall(arr : List[Long]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(moveOneBall((List[Long](3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong, 2l.toLong))) == (true));\n    assert(moveOneBall((List[Long](3l.toLong, 5l.toLong, 10l.toLong, 1l.toLong, 2l.toLong))) == (true));\n    assert(moveOneBall((List[Long](4l.toLong, 3l.toLong, 1l.toLong, 2l.toLong))) == (false));\n    assert(moveOneBall((List[Long](3l.toLong, 5l.toLong, 4l.toLong, 1l.toLong, 2l.toLong))) == (false));\n    assert(moveOneBall((List[Long]())) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_109_move_one_ball", "test": "    }\n    def main(args: Array[String]) = {\n    assert(moveOneBall((List[Long](3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong, 2l.toLong))) == (true));\n    assert(moveOneBall((List[Long](3l.toLong, 5l.toLong, 10l.toLong, 1l.toLong, 2l.toLong))) == (true));\n    assert(moveOneBall((List[Long](4l.toLong, 3l.toLong, 1l.toLong, 2l.toLong))) == (false));\n    assert(moveOneBall((List[Long](3l.toLong, 5l.toLong, 4l.toLong, 1l.toLong, 2l.toLong))) == (false));\n    assert(moveOneBall((List[Long]())) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer n, return a tuple that has the number of even and odd\n    // integer palindromes that fall within the range(1, n), inclusive.\n    // Example 1:\n    // >>> evenOddPalindrome((3l))\n    // ((1l, 2l))\n    // Explanation:\n    // Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n    // Example 2:\n    // >>> evenOddPalindrome((12l))\n    // ((4l, 6l))\n    // Explanation:\n    // Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n    // Note:\n    // 1. 1 <= n <= 10^3\n    // 2. returned tuple has the number of even and odd integer palindromes respectively.\n    def evenOddPalindrome(n : Long) : Tuple2[Long, Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(evenOddPalindrome((123l)).equals(((8l, 13l))));\n    assert(evenOddPalindrome((12l)).equals(((4l, 6l))));\n    assert(evenOddPalindrome((3l)).equals(((1l, 2l))));\n    assert(evenOddPalindrome((63l)).equals(((6l, 8l))));\n    assert(evenOddPalindrome((25l)).equals(((5l, 6l))));\n    assert(evenOddPalindrome((19l)).equals(((4l, 6l))));\n    assert(evenOddPalindrome((9l)).equals(((4l, 5l))));\n    assert(evenOddPalindrome((1l)).equals(((0l, 1l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "    }\n    def main(args: Array[String]) = {\n    assert(evenOddPalindrome((123l)).equals(((8l, 13l))));\n    assert(evenOddPalindrome((12l)).equals(((4l, 6l))));\n    assert(evenOddPalindrome((3l)).equals(((1l, 2l))));\n    assert(evenOddPalindrome((63l)).equals(((6l, 8l))));\n    assert(evenOddPalindrome((25l)).equals(((5l, 6l))));\n    assert(evenOddPalindrome((19l)).equals(((4l, 6l))));\n    assert(evenOddPalindrome((9l)).equals(((4l, 5l))));\n    assert(evenOddPalindrome((1l)).equals(((0l, 1l))));\n    }\n\n}\n"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n    // Example\n    // >>> isEqualToSumEven((4l))\n    // (false)\n    // >>> isEqualToSumEven((6l))\n    // (false)\n    // >>> isEqualToSumEven((8l))\n    // (true)\n    def isEqualToSumEven(n : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isEqualToSumEven((4l)) == (false));\n    assert(isEqualToSumEven((6l)) == (false));\n    assert(isEqualToSumEven((8l)) == (true));\n    assert(isEqualToSumEven((10l)) == (true));\n    assert(isEqualToSumEven((11l)) == (false));\n    assert(isEqualToSumEven((12l)) == (true));\n    assert(isEqualToSumEven((13l)) == (false));\n    assert(isEqualToSumEven((16l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isEqualToSumEven((4l)) == (false));\n    assert(isEqualToSumEven((6l)) == (false));\n    assert(isEqualToSumEven((8l)) == (true));\n    assert(isEqualToSumEven((10l)) == (true));\n    assert(isEqualToSumEven((11l)) == (false));\n    assert(isEqualToSumEven((12l)) == (true));\n    assert(isEqualToSumEven((13l)) == (false));\n    assert(isEqualToSumEven((16l)) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_62_derivative", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // xs represent coefficients of a polynomial.\n    // xs[0] + xs[1] * x + xs[2] * x^2 + ....\n    // Return derivative of this polynomial in the same form.\n    // >>> derivative((List[Long](3l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong)))\n    // (List[Long](1l.toLong, 4l.toLong, 12l.toLong, 20l.toLong))\n    // >>> derivative((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[Long](2l.toLong, 6l.toLong))\n    def derivative(xs : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(derivative((List[Long](3l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))).equals((List[Long](1l.toLong, 4l.toLong, 12l.toLong, 20l.toLong))));\n    assert(derivative((List[Long](1l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](2l.toLong, 6l.toLong))));\n    assert(derivative((List[Long](3l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](2l.toLong, 2l.toLong))));\n    assert(derivative((List[Long](3l.toLong, 2l.toLong, 1l.toLong, 0l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 2l.toLong, 0l.toLong, 16l.toLong))));\n    assert(derivative((List[Long](1l.toLong))).equals((List[Long]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_62_derivative", "test": "    }\n    def main(args: Array[String]) = {\n    assert(derivative((List[Long](3l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))).equals((List[Long](1l.toLong, 4l.toLong, 12l.toLong, 20l.toLong))));\n    assert(derivative((List[Long](1l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](2l.toLong, 6l.toLong))));\n    assert(derivative((List[Long](3l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](2l.toLong, 2l.toLong))));\n    assert(derivative((List[Long](3l.toLong, 2l.toLong, 1l.toLong, 0l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 2l.toLong, 0l.toLong, 16l.toLong))));\n    assert(derivative((List[Long](1l.toLong))).equals((List[Long]())));\n    }\n\n}\n"}
{"name": "HumanEval_126_is_sorted", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of numbers, return whether or not they are sorted\n    // in ascending order. If list has more than 1 duplicate of the same\n    // number, return false. Assume no negative numbers and only integers.\n    // Examples\n    // >>> isSorted((List[Long](5l.toLong)))\n    // (true)\n    // >>> isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong)))\n    // (true)\n    // >>> isSorted((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong)))\n    // (false)\n    // >>> isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong)))\n    // (true)\n    // >>> isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong)))\n    // (true)\n    // >>> isSorted((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong)))\n    // (false)\n    // >>> isSorted((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 4l.toLong)))\n    // (true)\n    // >>> isSorted((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)))\n    // (false)\n    def isSorted(lst : List[Long]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isSorted((List[Long](5l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong))) == (false));\n    assert(isSorted((List[Long]())) == (true));\n    assert(isSorted((List[Long](1l.toLong))) == (true));\n    assert(isSorted((List[Long](3l.toLong, 2l.toLong, 1l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 4l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 4l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_126_is_sorted", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isSorted((List[Long](5l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong))) == (false));\n    assert(isSorted((List[Long]())) == (true));\n    assert(isSorted((List[Long](1l.toLong))) == (true));\n    assert(isSorted((List[Long](3l.toLong, 2l.toLong, 1l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 4l.toLong))) == (false));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 4l.toLong))) == (true));\n    assert(isSorted((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_161_solve", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a string s.\n    // if s[i] is a letter, reverse its case from lower to upper or vise versa, \n    // otherwise keep it as it is.\n    // If the string contains no letters, reverse the string.\n    // The function should return the resulted string.\n    // Examples\n    // >>> solve((\"1234\"))\n    // (\"4321\")\n    // >>> solve((\"ab\"))\n    // (\"AB\")\n    // >>> solve((\"#a@C\"))\n    // (\"#A@c\")\n    def solve(s : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(solve((\"AsDf\")).equals((\"aSdF\")));\n    assert(solve((\"1234\")).equals((\"4321\")));\n    assert(solve((\"ab\")).equals((\"AB\")));\n    assert(solve((\"#a@C\")).equals((\"#A@c\")));\n    assert(solve((\"#AsdfW^45\")).equals((\"#aSDFw^45\")));\n    assert(solve((\"#6@2\")).equals((\"2@6#\")));\n    assert(solve((\"#$a^D\")).equals((\"#$A^d\")));\n    assert(solve((\"#ccc\")).equals((\"#CCC\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_161_solve", "test": "    }\n    def main(args: Array[String]) = {\n    assert(solve((\"AsDf\")).equals((\"aSdF\")));\n    assert(solve((\"1234\")).equals((\"4321\")));\n    assert(solve((\"ab\")).equals((\"AB\")));\n    assert(solve((\"#a@C\")).equals((\"#A@c\")));\n    assert(solve((\"#AsdfW^45\")).equals((\"#aSDFw^45\")));\n    assert(solve((\"#6@2\")).equals((\"2@6#\")));\n    assert(solve((\"#$a^D\")).equals((\"#$A^d\")));\n    assert(solve((\"#ccc\")).equals((\"#CCC\")));\n    }\n\n}\n"}
{"name": "HumanEval_130_tri", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n    // the last couple centuries. However, what people don't know is Tribonacci sequence.\n    // Tribonacci sequence is defined by the recurrence:\n    // tri(1) = 3\n    // tri(n) = 1 + n / 2, if n is even.\n    // tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n    // For example:\n    // tri(2) = 1 + (2 / 2) = 2\n    // tri(4) = 3\n    // tri(3) = tri(2) + tri(1) + tri(4)\n    // = 2 + 3 + 3 = 8 \n    // You are given a non-negative integer number n, you have to a return a list of the \n    // first n + 1 numbers of the Tribonacci sequence.\n    // Examples:\n    // >>> tri((3l))\n    // (List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong))\n    def tri(n : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(tri((3l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong))));\n    assert(tri((4l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong))));\n    assert(tri((5l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong))));\n    assert(tri((6l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong))));\n    assert(tri((7l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong))));\n    assert(tri((8l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong, 5l.toLong))));\n    assert(tri((9l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong, 5l.toLong, 35l.toLong))));\n    assert(tri((20l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong, 5l.toLong, 35l.toLong, 6l.toLong, 48l.toLong, 7l.toLong, 63l.toLong, 8l.toLong, 80l.toLong, 9l.toLong, 99l.toLong, 10l.toLong, 120l.toLong, 11l.toLong))));\n    assert(tri((0l)).equals((List[Long](1l.toLong))));\n    assert(tri((1l)).equals((List[Long](1l.toLong, 3l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_130_tri", "test": "    }\n    def main(args: Array[String]) = {\n    assert(tri((3l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong))));\n    assert(tri((4l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong))));\n    assert(tri((5l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong))));\n    assert(tri((6l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong))));\n    assert(tri((7l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong))));\n    assert(tri((8l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong, 5l.toLong))));\n    assert(tri((9l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong, 5l.toLong, 35l.toLong))));\n    assert(tri((20l)).equals((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 3l.toLong, 15l.toLong, 4l.toLong, 24l.toLong, 5l.toLong, 35l.toLong, 6l.toLong, 48l.toLong, 7l.toLong, 63l.toLong, 8l.toLong, 80l.toLong, 9l.toLong, 99l.toLong, 10l.toLong, 120l.toLong, 11l.toLong))));\n    assert(tri((0l)).equals((List[Long](1l.toLong))));\n    assert(tri((1l)).equals((List[Long](1l.toLong, 3l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_36_fizz_buzz", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n    // >>> fizzBuzz((50l))\n    // (0l)\n    // >>> fizzBuzz((78l))\n    // (2l)\n    // >>> fizzBuzz((79l))\n    // (3l)\n    def fizzBuzz(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fizzBuzz((50l)) == (0l));\n    assert(fizzBuzz((78l)) == (2l));\n    assert(fizzBuzz((79l)) == (3l));\n    assert(fizzBuzz((100l)) == (3l));\n    assert(fizzBuzz((200l)) == (6l));\n    assert(fizzBuzz((4000l)) == (192l));\n    assert(fizzBuzz((10000l)) == (639l));\n    assert(fizzBuzz((100000l)) == (8026l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_36_fizz_buzz", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fizzBuzz((50l)) == (0l));\n    assert(fizzBuzz((78l)) == (2l));\n    assert(fizzBuzz((79l)) == (3l));\n    assert(fizzBuzz((100l)) == (3l));\n    assert(fizzBuzz((200l)) == (6l));\n    assert(fizzBuzz((4000l)) == (192l));\n    assert(fizzBuzz((10000l)) == (639l));\n    assert(fizzBuzz((100000l)) == (8026l));\n    }\n\n}\n"}
{"name": "HumanEval_29_filter_by_prefix", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Filter an input list of strings only for ones that start with a given prefix.\n    // >>> filterByPrefix((List[String]()), (\"a\"))\n    // (List[String]())\n    // >>> filterByPrefix((List[String](\"abc\", \"bcd\", \"cde\", \"array\")), (\"a\"))\n    // (List[String](\"abc\", \"array\"))\n    def filterByPrefix(strings : List[String], prefix : String) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(filterByPrefix((List[String]()), (\"john\")).equals((List[String]())));\n    assert(filterByPrefix((List[String](\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\")), (\"xxx\")).equals((List[String](\"xxx\", \"xxxAAA\", \"xxx\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_29_filter_by_prefix", "test": "    }\n    def main(args: Array[String]) = {\n    assert(filterByPrefix((List[String]()), (\"john\")).equals((List[String]())));\n    assert(filterByPrefix((List[String](\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\")), (\"xxx\")).equals((List[String](\"xxx\", \"xxxAAA\", \"xxx\"))));\n    }\n\n}\n"}
{"name": "HumanEval_84_solve", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer N, return the total sum of its digits in binary.\n    // Example\n    // >>> solve((1000l))\n    // (\"1\")\n    // >>> solve((150l))\n    // (\"110\")\n    // >>> solve((147l))\n    // (\"1100\")\n    // Variables:\n    // @N integer\n    // Constraints: 0 \u2264 N \u2264 10000.\n    // Output:\n    // a string of binary number\n    def solve(N : Long) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(solve((1000l)).equals((\"1\")));\n    assert(solve((150l)).equals((\"110\")));\n    assert(solve((147l)).equals((\"1100\")));\n    assert(solve((333l)).equals((\"1001\")));\n    assert(solve((963l)).equals((\"10010\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_84_solve", "test": "    }\n    def main(args: Array[String]) = {\n    assert(solve((1000l)).equals((\"1\")));\n    assert(solve((150l)).equals((\"110\")));\n    assert(solve((147l)).equals((\"1100\")));\n    assert(solve((333l)).equals((\"1001\")));\n    assert(solve((963l)).equals((\"10010\")));\n    }\n\n}\n"}
{"name": "HumanEval_129_minPath", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n    // each cell of the grid contains a value. Every integer in the range [1, N * N]\n    // inclusive appears exactly once on the cells of the grid.\n    // You have to find the minimum path of length k in the grid. You can start\n    // from any cell, and in each step you can move to any of the neighbor cells,\n    // in other words, you can go to cells which share an edge with you current\n    // cell.\n    // Please note that a path of length k means visiting exactly k cells (not\n    // necessarily distinct).\n    // You CANNOT go off the grid.\n    // A path A (of length k) is considered less than a path B (of length k) if\n    // after making the ordered lists of the values on the cells that A and B go\n    // through (let's call them lst_A and lst_B), lst_A is lexicographically less\n    // than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n    // such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n    // lst_A[j] = lst_B[j].\n    // It is guaranteed that the answer is unique.\n    // Return an ordered list of the values on the cells that the minimum path go through.\n    // Examples:    \n    // >>> minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong), List[Long](4l.toLong, 5l.toLong, 6l.toLong), List[Long](7l.toLong, 8l.toLong, 9l.toLong))), (3l))\n    // (List[Long](1l.toLong, 2l.toLong, 1l.toLong))\n    // >>> minPath((List[List[Long]](List[Long](5l.toLong, 9l.toLong, 3l.toLong), List[Long](4l.toLong, 1l.toLong, 6l.toLong), List[Long](7l.toLong, 8l.toLong, 2l.toLong))), (1l))\n    // (List[Long](1l.toLong))\n    def minPath(grid : List[List[Long]], k : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong), List[Long](4l.toLong, 5l.toLong, 6l.toLong), List[Long](7l.toLong, 8l.toLong, 9l.toLong))), (3l)).equals((List[Long](1l.toLong, 2l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](5l.toLong, 9l.toLong, 3l.toLong), List[Long](4l.toLong, 1l.toLong, 6l.toLong), List[Long](7l.toLong, 8l.toLong, 2l.toLong))), (1l)).equals((List[Long](1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong), List[Long](5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong), List[Long](9l.toLong, 10l.toLong, 11l.toLong, 12l.toLong), List[Long](13l.toLong, 14l.toLong, 15l.toLong, 16l.toLong))), (4l)).equals((List[Long](1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](6l.toLong, 4l.toLong, 13l.toLong, 10l.toLong), List[Long](5l.toLong, 7l.toLong, 12l.toLong, 1l.toLong), List[Long](3l.toLong, 16l.toLong, 11l.toLong, 15l.toLong), List[Long](8l.toLong, 14l.toLong, 9l.toLong, 2l.toLong))), (7l)).equals((List[Long](1l.toLong, 10l.toLong, 1l.toLong, 10l.toLong, 1l.toLong, 10l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](8l.toLong, 14l.toLong, 9l.toLong, 2l.toLong), List[Long](6l.toLong, 4l.toLong, 13l.toLong, 15l.toLong), List[Long](5l.toLong, 7l.toLong, 1l.toLong, 12l.toLong), List[Long](3l.toLong, 10l.toLong, 11l.toLong, 16l.toLong))), (5l)).equals((List[Long](1l.toLong, 7l.toLong, 1l.toLong, 7l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](11l.toLong, 8l.toLong, 7l.toLong, 2l.toLong), List[Long](5l.toLong, 16l.toLong, 14l.toLong, 4l.toLong), List[Long](9l.toLong, 3l.toLong, 15l.toLong, 6l.toLong), List[Long](12l.toLong, 13l.toLong, 10l.toLong, 1l.toLong))), (9l)).equals((List[Long](1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](12l.toLong, 13l.toLong, 10l.toLong, 1l.toLong), List[Long](9l.toLong, 3l.toLong, 15l.toLong, 6l.toLong), List[Long](5l.toLong, 16l.toLong, 14l.toLong, 4l.toLong), List[Long](11l.toLong, 8l.toLong, 7l.toLong, 2l.toLong))), (12l)).equals((List[Long](1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](2l.toLong, 7l.toLong, 4l.toLong), List[Long](3l.toLong, 1l.toLong, 5l.toLong), List[Long](6l.toLong, 8l.toLong, 9l.toLong))), (8l)).equals((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](6l.toLong, 1l.toLong, 5l.toLong), List[Long](3l.toLong, 8l.toLong, 9l.toLong), List[Long](2l.toLong, 7l.toLong, 4l.toLong))), (8l)).equals((List[Long](1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong), List[Long](3l.toLong, 4l.toLong))), (10l)).equals((List[Long](1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 3l.toLong), List[Long](3l.toLong, 2l.toLong))), (10l)).equals((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_129_minPath", "test": "    }\n    def main(args: Array[String]) = {\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong), List[Long](4l.toLong, 5l.toLong, 6l.toLong), List[Long](7l.toLong, 8l.toLong, 9l.toLong))), (3l)).equals((List[Long](1l.toLong, 2l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](5l.toLong, 9l.toLong, 3l.toLong), List[Long](4l.toLong, 1l.toLong, 6l.toLong), List[Long](7l.toLong, 8l.toLong, 2l.toLong))), (1l)).equals((List[Long](1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong), List[Long](5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong), List[Long](9l.toLong, 10l.toLong, 11l.toLong, 12l.toLong), List[Long](13l.toLong, 14l.toLong, 15l.toLong, 16l.toLong))), (4l)).equals((List[Long](1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](6l.toLong, 4l.toLong, 13l.toLong, 10l.toLong), List[Long](5l.toLong, 7l.toLong, 12l.toLong, 1l.toLong), List[Long](3l.toLong, 16l.toLong, 11l.toLong, 15l.toLong), List[Long](8l.toLong, 14l.toLong, 9l.toLong, 2l.toLong))), (7l)).equals((List[Long](1l.toLong, 10l.toLong, 1l.toLong, 10l.toLong, 1l.toLong, 10l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](8l.toLong, 14l.toLong, 9l.toLong, 2l.toLong), List[Long](6l.toLong, 4l.toLong, 13l.toLong, 15l.toLong), List[Long](5l.toLong, 7l.toLong, 1l.toLong, 12l.toLong), List[Long](3l.toLong, 10l.toLong, 11l.toLong, 16l.toLong))), (5l)).equals((List[Long](1l.toLong, 7l.toLong, 1l.toLong, 7l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](11l.toLong, 8l.toLong, 7l.toLong, 2l.toLong), List[Long](5l.toLong, 16l.toLong, 14l.toLong, 4l.toLong), List[Long](9l.toLong, 3l.toLong, 15l.toLong, 6l.toLong), List[Long](12l.toLong, 13l.toLong, 10l.toLong, 1l.toLong))), (9l)).equals((List[Long](1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](12l.toLong, 13l.toLong, 10l.toLong, 1l.toLong), List[Long](9l.toLong, 3l.toLong, 15l.toLong, 6l.toLong), List[Long](5l.toLong, 16l.toLong, 14l.toLong, 4l.toLong), List[Long](11l.toLong, 8l.toLong, 7l.toLong, 2l.toLong))), (12l)).equals((List[Long](1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong, 1l.toLong, 6l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](2l.toLong, 7l.toLong, 4l.toLong), List[Long](3l.toLong, 1l.toLong, 5l.toLong), List[Long](6l.toLong, 8l.toLong, 9l.toLong))), (8l)).equals((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](6l.toLong, 1l.toLong, 5l.toLong), List[Long](3l.toLong, 8l.toLong, 9l.toLong), List[Long](2l.toLong, 7l.toLong, 4l.toLong))), (8l)).equals((List[Long](1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 2l.toLong), List[Long](3l.toLong, 4l.toLong))), (10l)).equals((List[Long](1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong))));\n    assert(minPath((List[List[Long]](List[Long](1l.toLong, 3l.toLong), List[Long](3l.toLong, 2l.toLong))), (10l)).equals((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_98_count_upper", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string s, count the number of uppercase vowels in even indices.\n    // For example:\n    // >>> countUpper((\"aBCdEf\"))\n    // (1l)\n    // >>> countUpper((\"abcdefg\"))\n    // (0l)\n    // >>> countUpper((\"dBBE\"))\n    // (0l)\n    def countUpper(s : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(countUpper((\"aBCdEf\")) == (1l));\n    assert(countUpper((\"abcdefg\")) == (0l));\n    assert(countUpper((\"dBBE\")) == (0l));\n    assert(countUpper((\"B\")) == (0l));\n    assert(countUpper((\"U\")) == (1l));\n    assert(countUpper((\"\")) == (0l));\n    assert(countUpper((\"EEEE\")) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_98_count_upper", "test": "    }\n    def main(args: Array[String]) = {\n    assert(countUpper((\"aBCdEf\")) == (1l));\n    assert(countUpper((\"abcdefg\")) == (0l));\n    assert(countUpper((\"dBBE\")) == (0l));\n    assert(countUpper((\"B\")) == (0l));\n    assert(countUpper((\"U\")) == (1l));\n    assert(countUpper((\"\")) == (0l));\n    assert(countUpper((\"EEEE\")) == (2l));\n    }\n\n}\n"}
{"name": "HumanEval_120_maximum", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list arr of integers and a positive integer k, return a sorted list \n    // of length k with the maximum k numbers in arr.\n    // Example 1:\n    // >>> maximum((List[Long](-3l.toLong, -4l.toLong, 5l.toLong)), (3l))\n    // (List[Long](-4l.toLong, -3l.toLong, 5l.toLong))\n    // Example 2:\n    // >>> maximum((List[Long](4l.toLong, -4l.toLong, 4l.toLong)), (2l))\n    // (List[Long](4l.toLong, 4l.toLong))\n    // Example 3:\n    // >>> maximum((List[Long](-3l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, -1l.toLong, -2l.toLong, 1l.toLong)), (1l))\n    // (List[Long](2l.toLong))\n    // Note:\n    // 1. The length of the list will be in the range of [1, 1000].\n    // 2. The elements in the list will be in the range of [-1000, 1000].\n    // 3. 0 <= k <= len(arr)\n    def maximum(arr : List[Long], k : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(maximum((List[Long](-3l.toLong, -4l.toLong, 5l.toLong)), (3l)).equals((List[Long](-4l.toLong, -3l.toLong, 5l.toLong))));\n    assert(maximum((List[Long](4l.toLong, -4l.toLong, 4l.toLong)), (2l)).equals((List[Long](4l.toLong, 4l.toLong))));\n    assert(maximum((List[Long](-3l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, -1l.toLong, -2l.toLong, 1l.toLong)), (1l)).equals((List[Long](2l.toLong))));\n    assert(maximum((List[Long](123l.toLong, -123l.toLong, 20l.toLong, 0l.toLong, 1l.toLong, 2l.toLong, -3l.toLong)), (3l)).equals((List[Long](2l.toLong, 20l.toLong, 123l.toLong))));\n    assert(maximum((List[Long](-123l.toLong, 20l.toLong, 0l.toLong, 1l.toLong, 2l.toLong, -3l.toLong)), (4l)).equals((List[Long](0l.toLong, 1l.toLong, 2l.toLong, 20l.toLong))));\n    assert(maximum((List[Long](5l.toLong, 15l.toLong, 0l.toLong, 3l.toLong, -13l.toLong, -8l.toLong, 0l.toLong)), (7l)).equals((List[Long](-13l.toLong, -8l.toLong, 0l.toLong, 0l.toLong, 3l.toLong, 5l.toLong, 15l.toLong))));\n    assert(maximum((List[Long](-1l.toLong, 0l.toLong, 2l.toLong, 5l.toLong, 3l.toLong, -10l.toLong)), (2l)).equals((List[Long](3l.toLong, 5l.toLong))));\n    assert(maximum((List[Long](1l.toLong, 0l.toLong, 5l.toLong, -7l.toLong)), (1l)).equals((List[Long](5l.toLong))));\n    assert(maximum((List[Long](4l.toLong, -4l.toLong)), (2l)).equals((List[Long](-4l.toLong, 4l.toLong))));\n    assert(maximum((List[Long](-10l.toLong, 10l.toLong)), (2l)).equals((List[Long](-10l.toLong, 10l.toLong))));\n    assert(maximum((List[Long](1l.toLong, 2l.toLong, 3l.toLong, -23l.toLong, 243l.toLong, -400l.toLong, 0l.toLong)), (0l)).equals((List[Long]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_120_maximum", "test": "    }\n    def main(args: Array[String]) = {\n    assert(maximum((List[Long](-3l.toLong, -4l.toLong, 5l.toLong)), (3l)).equals((List[Long](-4l.toLong, -3l.toLong, 5l.toLong))));\n    assert(maximum((List[Long](4l.toLong, -4l.toLong, 4l.toLong)), (2l)).equals((List[Long](4l.toLong, 4l.toLong))));\n    assert(maximum((List[Long](-3l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, -1l.toLong, -2l.toLong, 1l.toLong)), (1l)).equals((List[Long](2l.toLong))));\n    assert(maximum((List[Long](123l.toLong, -123l.toLong, 20l.toLong, 0l.toLong, 1l.toLong, 2l.toLong, -3l.toLong)), (3l)).equals((List[Long](2l.toLong, 20l.toLong, 123l.toLong))));\n    assert(maximum((List[Long](-123l.toLong, 20l.toLong, 0l.toLong, 1l.toLong, 2l.toLong, -3l.toLong)), (4l)).equals((List[Long](0l.toLong, 1l.toLong, 2l.toLong, 20l.toLong))));\n    assert(maximum((List[Long](5l.toLong, 15l.toLong, 0l.toLong, 3l.toLong, -13l.toLong, -8l.toLong, 0l.toLong)), (7l)).equals((List[Long](-13l.toLong, -8l.toLong, 0l.toLong, 0l.toLong, 3l.toLong, 5l.toLong, 15l.toLong))));\n    assert(maximum((List[Long](-1l.toLong, 0l.toLong, 2l.toLong, 5l.toLong, 3l.toLong, -10l.toLong)), (2l)).equals((List[Long](3l.toLong, 5l.toLong))));\n    assert(maximum((List[Long](1l.toLong, 0l.toLong, 5l.toLong, -7l.toLong)), (1l)).equals((List[Long](5l.toLong))));\n    assert(maximum((List[Long](4l.toLong, -4l.toLong)), (2l)).equals((List[Long](-4l.toLong, 4l.toLong))));\n    assert(maximum((List[Long](-10l.toLong, 10l.toLong)), (2l)).equals((List[Long](-10l.toLong, 10l.toLong))));\n    assert(maximum((List[Long](1l.toLong, 2l.toLong, 3l.toLong, -23l.toLong, 243l.toLong, -400l.toLong, 0l.toLong)), (0l)).equals((List[Long]())));\n    }\n\n}\n"}
{"name": "HumanEval_24_largest_divisor", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // For a given number n, find the largest number that divides n evenly, smaller than n\n    // >>> largestDivisor((15l))\n    // (5l)\n    def largestDivisor(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(largestDivisor((3l)) == (1l));\n    assert(largestDivisor((7l)) == (1l));\n    assert(largestDivisor((10l)) == (5l));\n    assert(largestDivisor((100l)) == (50l));\n    assert(largestDivisor((49l)) == (7l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_24_largest_divisor", "test": "    }\n    def main(args: Array[String]) = {\n    assert(largestDivisor((3l)) == (1l));\n    assert(largestDivisor((7l)) == (1l));\n    assert(largestDivisor((10l)) == (5l));\n    assert(largestDivisor((100l)) == (50l));\n    assert(largestDivisor((49l)) == (7l));\n    }\n\n}\n"}
{"name": "HumanEval_88_sort_array", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of non-negative integers, return a coscala of the given list after sorting,\n    // you will sort the given list in ascending order if the sum( first index value, last index value) is odd,\n    // or sort it in descending order if the sum( first index value, last index value) is even.\n    // Note:\n    // * don't change the given list.\n    // Examples:\n    // >>> sortArray((List[Long]()))\n    // (List[Long]())\n    // >>> sortArray((List[Long](5l.toLong)))\n    // (List[Long](5l.toLong))\n    // >>> sortArray((List[Long](2l.toLong, 4l.toLong, 3l.toLong, 0l.toLong, 1l.toLong, 5l.toLong)))\n    // (List[Long](0l.toLong, 1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))\n    // >>> sortArray((List[Long](2l.toLong, 4l.toLong, 3l.toLong, 0l.toLong, 1l.toLong, 5l.toLong, 6l.toLong)))\n    // (List[Long](6l.toLong, 5l.toLong, 4l.toLong, 3l.toLong, 2l.toLong, 1l.toLong, 0l.toLong))\n    def sortArray(array : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sortArray((List[Long]())).equals((List[Long]())));\n    assert(sortArray((List[Long](5l.toLong))).equals((List[Long](5l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 3l.toLong, 0l.toLong, 1l.toLong, 5l.toLong))).equals((List[Long](0l.toLong, 1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 3l.toLong, 0l.toLong, 1l.toLong, 5l.toLong, 6l.toLong))).equals((List[Long](6l.toLong, 5l.toLong, 4l.toLong, 3l.toLong, 2l.toLong, 1l.toLong, 0l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 1l.toLong))).equals((List[Long](1l.toLong, 2l.toLong))));\n    assert(sortArray((List[Long](15l.toLong, 42l.toLong, 87l.toLong, 32l.toLong, 11l.toLong, 0l.toLong))).equals((List[Long](0l.toLong, 11l.toLong, 15l.toLong, 32l.toLong, 42l.toLong, 87l.toLong))));\n    assert(sortArray((List[Long](21l.toLong, 14l.toLong, 23l.toLong, 11l.toLong))).equals((List[Long](23l.toLong, 21l.toLong, 14l.toLong, 11l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_88_sort_array", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sortArray((List[Long]())).equals((List[Long]())));\n    assert(sortArray((List[Long](5l.toLong))).equals((List[Long](5l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 3l.toLong, 0l.toLong, 1l.toLong, 5l.toLong))).equals((List[Long](0l.toLong, 1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 3l.toLong, 0l.toLong, 1l.toLong, 5l.toLong, 6l.toLong))).equals((List[Long](6l.toLong, 5l.toLong, 4l.toLong, 3l.toLong, 2l.toLong, 1l.toLong, 0l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 1l.toLong))).equals((List[Long](1l.toLong, 2l.toLong))));\n    assert(sortArray((List[Long](15l.toLong, 42l.toLong, 87l.toLong, 32l.toLong, 11l.toLong, 0l.toLong))).equals((List[Long](0l.toLong, 11l.toLong, 15l.toLong, 32l.toLong, 42l.toLong, 87l.toLong))));\n    assert(sortArray((List[Long](21l.toLong, 14l.toLong, 23l.toLong, 11l.toLong))).equals((List[Long](23l.toLong, 21l.toLong, 14l.toLong, 11l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_106_f", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Implement the function f that takes n as a parameter,\n    // and returns a list of size n, such that the value of the element at index i is the factorial of i if i is even\n    // or the sum of numbers from 1 to i otherwise.\n    // i starts from 1.\n    // the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n    // Example:\n    // >>> f((5l))\n    // (List[Long](1l.toLong, 2l.toLong, 6l.toLong, 24l.toLong, 15l.toLong))\n    def f(n : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(f((5l)).equals((List[Long](1l.toLong, 2l.toLong, 6l.toLong, 24l.toLong, 15l.toLong))));\n    assert(f((7l)).equals((List[Long](1l.toLong, 2l.toLong, 6l.toLong, 24l.toLong, 15l.toLong, 720l.toLong, 28l.toLong))));\n    assert(f((1l)).equals((List[Long](1l.toLong))));\n    assert(f((3l)).equals((List[Long](1l.toLong, 2l.toLong, 6l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_106_f", "test": "    }\n    def main(args: Array[String]) = {\n    assert(f((5l)).equals((List[Long](1l.toLong, 2l.toLong, 6l.toLong, 24l.toLong, 15l.toLong))));\n    assert(f((7l)).equals((List[Long](1l.toLong, 2l.toLong, 6l.toLong, 24l.toLong, 15l.toLong, 720l.toLong, 28l.toLong))));\n    assert(f((1l)).equals((List[Long](1l.toLong))));\n    assert(f((3l)).equals((List[Long](1l.toLong, 2l.toLong, 6l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_77_iscube", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that takes an integer a and returns true \n    // if this ingeger is a cube of some integer number.\n    // Note: you may assume the input is always valid.\n    // Examples:\n    // >>> iscube((1l))\n    // (true)\n    // >>> iscube((2l))\n    // (false)\n    // >>> iscube((-1l))\n    // (true)\n    // >>> iscube((64l))\n    // (true)\n    // >>> iscube((0l))\n    // (true)\n    // >>> iscube((180l))\n    // (false)\n    def iscube(a : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(iscube((1l)) == (true));\n    assert(iscube((2l)) == (false));\n    assert(iscube((-1l)) == (true));\n    assert(iscube((64l)) == (true));\n    assert(iscube((180l)) == (false));\n    assert(iscube((1000l)) == (true));\n    assert(iscube((0l)) == (true));\n    assert(iscube((1729l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_77_iscube", "test": "    }\n    def main(args: Array[String]) = {\n    assert(iscube((1l)) == (true));\n    assert(iscube((2l)) == (false));\n    assert(iscube((-1l)) == (true));\n    assert(iscube((64l)) == (true));\n    assert(iscube((180l)) == (false));\n    assert(iscube((1000l)) == (true));\n    assert(iscube((0l)) == (true));\n    assert(iscube((1729l)) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_93_encode", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that takes a message, and encodes in such a \n    // way that it swaps case of all letters, replaces all vowels in \n    // the message with the letter that appears 2 places ahead of that \n    // vowel in the english alphabet. \n    // Assume only letters. \n    // Examples:\n    // >>> encode((\"test\"))\n    // (\"TGST\")\n    // >>> encode((\"This is a message\"))\n    // (\"tHKS KS C MGSSCGG\")\n    def encode(message : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(encode((\"TEST\")).equals((\"tgst\")));\n    assert(encode((\"Mudasir\")).equals((\"mWDCSKR\")));\n    assert(encode((\"YES\")).equals((\"ygs\")));\n    assert(encode((\"This is a message\")).equals((\"tHKS KS C MGSSCGG\")));\n    assert(encode((\"I DoNt KnOw WhAt tO WrItE\")).equals((\"k dQnT kNqW wHcT Tq wRkTg\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_93_encode", "test": "    }\n    def main(args: Array[String]) = {\n    assert(encode((\"TEST\")).equals((\"tgst\")));\n    assert(encode((\"Mudasir\")).equals((\"mWDCSKR\")));\n    assert(encode((\"YES\")).equals((\"ygs\")));\n    assert(encode((\"This is a message\")).equals((\"tHKS KS C MGSSCGG\")));\n    assert(encode((\"I DoNt KnOw WhAt tO WrItE\")).equals((\"k dQnT kNqW wHcT Tq wRkTg\")));\n    }\n\n}\n"}
{"name": "HumanEval_91_is_bored", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You'll be given a string of words, and your task is to count the number\n    // of boredoms. A boredom is a sentence that starts with the word \"I\".\n    // Sentences are delimited by '.', '?' or '!'.\n    // For example:\n    // >>> isBored((\"Hello world\"))\n    // (0l)\n    // >>> isBored((\"The sky is blue. The sun is shining. I love this weather\"))\n    // (1l)\n    def isBored(S : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isBored((\"Hello world\")) == (0l));\n    assert(isBored((\"Is the sky blue?\")) == (0l));\n    assert(isBored((\"I love It !\")) == (1l));\n    assert(isBored((\"bIt\")) == (0l));\n    assert(isBored((\"I feel good today. I will be productive. will kill It\")) == (2l));\n    assert(isBored((\"You and I are going for a walk\")) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_91_is_bored", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isBored((\"Hello world\")) == (0l));\n    assert(isBored((\"Is the sky blue?\")) == (0l));\n    assert(isBored((\"I love It !\")) == (1l));\n    assert(isBored((\"bIt\")) == (0l));\n    assert(isBored((\"I feel good today. I will be productive. will kill It\")) == (2l));\n    assert(isBored((\"You and I are going for a walk\")) == (0l));\n    }\n\n}\n"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // pairs_sum_to_zero takes a list of integers as an input.\n    // it returns true if there are two distinct elements in the list that\n    // sum to zero, and false otherwise.\n    // >>> pairsSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, 0l.toLong)))\n    // (false)\n    // >>> pairsSumToZero((List[Long](1l.toLong, 3l.toLong, -2l.toLong, 1l.toLong)))\n    // (false)\n    // >>> pairsSumToZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 7l.toLong)))\n    // (false)\n    // >>> pairsSumToZero((List[Long](2l.toLong, 4l.toLong, -5l.toLong, 3l.toLong, 5l.toLong, 7l.toLong)))\n    // (true)\n    // >>> pairsSumToZero((List[Long](1l.toLong)))\n    // (false)\n    def pairsSumToZero(l : List[Long]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(pairsSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, 0l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](1l.toLong, 3l.toLong, -2l.toLong, 1l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 7l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](2l.toLong, 4l.toLong, -5l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))) == (true));\n    assert(pairsSumToZero((List[Long](1l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 3l.toLong, 2l.toLong, 30l.toLong))) == (true));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 3l.toLong, 2l.toLong, 31l.toLong))) == (true));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 4l.toLong, 2l.toLong, 30l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 4l.toLong, 2l.toLong, 31l.toLong))) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "    }\n    def main(args: Array[String]) = {\n    assert(pairsSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, 0l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](1l.toLong, 3l.toLong, -2l.toLong, 1l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 7l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](2l.toLong, 4l.toLong, -5l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))) == (true));\n    assert(pairsSumToZero((List[Long](1l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 3l.toLong, 2l.toLong, 30l.toLong))) == (true));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 3l.toLong, 2l.toLong, 31l.toLong))) == (true));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 4l.toLong, 2l.toLong, 30l.toLong))) == (false));\n    assert(pairsSumToZero((List[Long](-3l.toLong, 9l.toLong, -1l.toLong, 4l.toLong, 2l.toLong, 31l.toLong))) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_71_triangle_area", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given the lengths of the three sides of a triangle. Return the area of\n    // the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n    // Otherwise return -1\n    // Three sides make a valid triangle when the sum of any two sides is greater \n    // than the third side.\n    // Example:\n    // >>> triangleArea((3l), (4l), (5l))\n    // (6.0f)\n    // >>> triangleArea((1l), (2l), (10l))\n    // -1l\n    def triangleArea(a : Long, b : Long, c : Long) : Float = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(triangleArea((3l), (4l), (5l)) == (6.0f));\n    assert(triangleArea((1l), (2l), (10l)) == -1l);\n    assert(triangleArea((4l), (8l), (5l)) == (8.18f));\n    assert(triangleArea((2l), (2l), (2l)) == (1.73f));\n    assert(triangleArea((1l), (2l), (3l)) == -1l);\n    assert(triangleArea((10l), (5l), (7l)) == (16.25f));\n    assert(triangleArea((2l), (6l), (3l)) == -1l);\n    assert(triangleArea((1l), (1l), (1l)) == (0.43f));\n    assert(triangleArea((2l), (2l), (10l)) == -1l);\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_71_triangle_area", "test": "    }\n    def main(args: Array[String]) = {\n    assert(triangleArea((3l), (4l), (5l)) == (6.0f));\n    assert(triangleArea((1l), (2l), (10l)) == -1l);\n    assert(triangleArea((4l), (8l), (5l)) == (8.18f));\n    assert(triangleArea((2l), (2l), (2l)) == (1.73f));\n    assert(triangleArea((1l), (2l), (3l)) == -1l);\n    assert(triangleArea((10l), (5l), (7l)) == (16.25f));\n    assert(triangleArea((2l), (6l), (3l)) == -1l);\n    assert(triangleArea((1l), (1l), (1l)) == (0.43f));\n    assert(triangleArea((2l), (2l), (10l)) == -1l);\n    }\n\n}\n"}
{"name": "HumanEval_148_bf", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // There are eight planets in our solar system: the closerst to the Sun \n    // is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n    // Uranus, Neptune.\n    // Write a function that takes two planet names as strings planet1 and planet2. \n    // The function should return a tuple containing all planets whose orbits are \n    // located between the orbit of planet1 and the orbit of planet2, sorted by \n    // the proximity to the sun. \n    // The function should return an empty tuple if planet1 or planet2\n    // are not correct planet names. \n    // Examples\n    // >>> bf((\"Jupiter\"), (\"Neptune\"))\n    // (List[String](\"Saturn\", \"Uranus\"))\n    // >>> bf((\"Earth\"), (\"Mercury\"))\n    // (List[String](\"Venus\"))\n    // >>> bf((\"Mercury\"), (\"Uranus\"))\n    // (List[String](\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"))\n    def bf(planet1 : String, planet2 : String) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(bf((\"Jupiter\"), (\"Neptune\")).equals((List[String](\"Saturn\", \"Uranus\"))));\n    assert(bf((\"Earth\"), (\"Mercury\")).equals((List[String](\"Venus\"))));\n    assert(bf((\"Mercury\"), (\"Uranus\")).equals((List[String](\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"))));\n    assert(bf((\"Neptune\"), (\"Venus\")).equals((List[String](\"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\"))));\n    assert(bf((\"Earth\"), (\"Earth\")).equals((List[String]())));\n    assert(bf((\"Mars\"), (\"Earth\")).equals((List[String]())));\n    assert(bf((\"Jupiter\"), (\"Makemake\")).equals((List[String]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_148_bf", "test": "    }\n    def main(args: Array[String]) = {\n    assert(bf((\"Jupiter\"), (\"Neptune\")).equals((List[String](\"Saturn\", \"Uranus\"))));\n    assert(bf((\"Earth\"), (\"Mercury\")).equals((List[String](\"Venus\"))));\n    assert(bf((\"Mercury\"), (\"Uranus\")).equals((List[String](\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"))));\n    assert(bf((\"Neptune\"), (\"Venus\")).equals((List[String](\"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\"))));\n    assert(bf((\"Earth\"), (\"Earth\")).equals((List[String]())));\n    assert(bf((\"Mars\"), (\"Earth\")).equals((List[String]())));\n    assert(bf((\"Jupiter\"), (\"Makemake\")).equals((List[String]())));\n    }\n\n}\n"}
{"name": "HumanEval_131_digits", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer n, return the product of the odd digits.\n    // Return 0 if all digits are even.\n    // For example:\n    // >>> digits((1l))\n    // (1l)\n    // >>> digits((4l))\n    // (0l)\n    // >>> digits((235l))\n    // (15l)\n    def digits(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(digits((5l)) == (5l));\n    assert(digits((54l)) == (5l));\n    assert(digits((120l)) == (1l));\n    assert(digits((5014l)) == (5l));\n    assert(digits((98765l)) == (315l));\n    assert(digits((5576543l)) == (2625l));\n    assert(digits((2468l)) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_131_digits", "test": "    }\n    def main(args: Array[String]) = {\n    assert(digits((5l)) == (5l));\n    assert(digits((54l)) == (5l));\n    assert(digits((120l)) == (1l));\n    assert(digits((5014l)) == (5l));\n    assert(digits((98765l)) == (315l));\n    assert(digits((5576543l)) == (2625l));\n    assert(digits((2468l)) == (0l));\n    }\n\n}\n"}
{"name": "HumanEval_101_words_string", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You will be given a string of words separated by commas or spaces. Your task is\n    // to split the string into words and return a list of the words.\n    // For example:\n    // >>> wordsString((\"Hi, my name is John\"))\n    // (List[String](\"Hi\", \"my\", \"name\", \"is\", \"John\"))\n    // >>> wordsString((\"One, two, three, four, five, six\"))\n    // (List[String](\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"))\n    def wordsString(s : String) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(wordsString((\"Hi, my name is John\")).equals((List[String](\"Hi\", \"my\", \"name\", \"is\", \"John\"))));\n    assert(wordsString((\"One, two, three, four, five, six\")).equals((List[String](\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"))));\n    assert(wordsString((\"Hi, my name\")).equals((List[String](\"Hi\", \"my\", \"name\"))));\n    assert(wordsString((\"One,, two, three, four, five, six,\")).equals((List[String](\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"))));\n    assert(wordsString((\"\")).equals((List[String]())));\n    assert(wordsString((\"ahmed     , gamal\")).equals((List[String](\"ahmed\", \"gamal\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_101_words_string", "test": "    }\n    def main(args: Array[String]) = {\n    assert(wordsString((\"Hi, my name is John\")).equals((List[String](\"Hi\", \"my\", \"name\", \"is\", \"John\"))));\n    assert(wordsString((\"One, two, three, four, five, six\")).equals((List[String](\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"))));\n    assert(wordsString((\"Hi, my name\")).equals((List[String](\"Hi\", \"my\", \"name\"))));\n    assert(wordsString((\"One,, two, three, four, five, six,\")).equals((List[String](\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"))));\n    assert(wordsString((\"\")).equals((List[String]())));\n    assert(wordsString((\"ahmed     , gamal\")).equals((List[String](\"ahmed\", \"gamal\"))));\n    }\n\n}\n"}
{"name": "HumanEval_18_how_many_times", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Find how many times a given substring can be found in the original string. Count overlaping cases.\n    // >>> howManyTimes((\"\"), (\"a\"))\n    // (0l)\n    // >>> howManyTimes((\"aaa\"), (\"a\"))\n    // (3l)\n    // >>> howManyTimes((\"aaaa\"), (\"aa\"))\n    // (3l)\n    def howManyTimes(string : String, substring : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(howManyTimes((\"\"), (\"x\")) == (0l));\n    assert(howManyTimes((\"xyxyxyx\"), (\"x\")) == (4l));\n    assert(howManyTimes((\"cacacacac\"), (\"cac\")) == (4l));\n    assert(howManyTimes((\"john doe\"), (\"john\")) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_18_how_many_times", "test": "    }\n    def main(args: Array[String]) = {\n    assert(howManyTimes((\"\"), (\"x\")) == (0l));\n    assert(howManyTimes((\"xyxyxyx\"), (\"x\")) == (4l));\n    assert(howManyTimes((\"cacacacac\"), (\"cac\")) == (4l));\n    assert(howManyTimes((\"john doe\"), (\"john\")) == (1l));\n    }\n\n}\n"}
{"name": "HumanEval_51_remove_vowels", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // remove_vowels is a function that takes string and returns string without vowels.\n    // >>> removeVowels((\"\"))\n    // (\"\")\n    // >>> removeVowels((\"abcdef\"))\n    // (\"bcdf\")\n    // >>> removeVowels((\"aaaaa\"))\n    // (\"\")\n    // >>> removeVowels((\"aaBAA\"))\n    // (\"B\")\n    // >>> removeVowels((\"zbcd\"))\n    // (\"zbcd\")\n    def removeVowels(text : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(removeVowels((\"\")).equals((\"\")));\n    assert(removeVowels((\"abcdef\\nghijklm\")).equals((\"bcdf\\nghjklm\")));\n    assert(removeVowels((\"fedcba\")).equals((\"fdcb\")));\n    assert(removeVowels((\"eeeee\")).equals((\"\")));\n    assert(removeVowels((\"acBAA\")).equals((\"cB\")));\n    assert(removeVowels((\"EcBOO\")).equals((\"cB\")));\n    assert(removeVowels((\"ybcd\")).equals((\"ybcd\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_51_remove_vowels", "test": "    }\n    def main(args: Array[String]) = {\n    assert(removeVowels((\"\")).equals((\"\")));\n    assert(removeVowels((\"abcdef\\nghijklm\")).equals((\"bcdf\\nghjklm\")));\n    assert(removeVowels((\"fedcba\")).equals((\"fdcb\")));\n    assert(removeVowels((\"eeeee\")).equals((\"\")));\n    assert(removeVowels((\"acBAA\")).equals((\"cB\")));\n    assert(removeVowels((\"EcBOO\")).equals((\"cB\")));\n    assert(removeVowels((\"ybcd\")).equals((\"ybcd\")));\n    }\n\n}\n"}
{"name": "HumanEval_70_strange_sort_list", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given list of integers, return list in strange order.\n    // Strange sorting, is when you start with the minimum value,\n    // then maximum of the remaining integers, then minimum and so on.\n    // Examples:\n    // >>> strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)))\n    // (List[Long](1l.toLong, 4l.toLong, 2l.toLong, 3l.toLong))\n    // >>> strangeSortList((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong)))\n    // (List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong))\n    // >>> strangeSortList((List[Long]()))\n    // (List[Long]())\n    def strangeSortList(lst : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 4l.toLong, 2l.toLong, 3l.toLong))));\n    assert(strangeSortList((List[Long](5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong))).equals((List[Long](5l.toLong, 9l.toLong, 6l.toLong, 8l.toLong, 7l.toLong))));\n    assert(strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))).equals((List[Long](1l.toLong, 5l.toLong, 2l.toLong, 4l.toLong, 3l.toLong))));\n    assert(strangeSortList((List[Long](5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong, 1l.toLong))).equals((List[Long](1l.toLong, 9l.toLong, 5l.toLong, 8l.toLong, 6l.toLong, 7l.toLong))));\n    assert(strangeSortList((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong))).equals((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong))));\n    assert(strangeSortList((List[Long]())).equals((List[Long]())));\n    assert(strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong))).equals((List[Long](1l.toLong, 8l.toLong, 2l.toLong, 7l.toLong, 3l.toLong, 6l.toLong, 4l.toLong, 5l.toLong))));\n    assert(strangeSortList((List[Long](0l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 5l.toLong, 5l.toLong, -5l.toLong, -5l.toLong))).equals((List[Long](-5l.toLong, 5l.toLong, -5l.toLong, 5l.toLong, 0l.toLong, 2l.toLong, 2l.toLong, 2l.toLong))));\n    assert(strangeSortList((List[Long](111111l.toLong))).equals((List[Long](111111l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_70_strange_sort_list", "test": "    }\n    def main(args: Array[String]) = {\n    assert(strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 4l.toLong, 2l.toLong, 3l.toLong))));\n    assert(strangeSortList((List[Long](5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong))).equals((List[Long](5l.toLong, 9l.toLong, 6l.toLong, 8l.toLong, 7l.toLong))));\n    assert(strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))).equals((List[Long](1l.toLong, 5l.toLong, 2l.toLong, 4l.toLong, 3l.toLong))));\n    assert(strangeSortList((List[Long](5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong, 1l.toLong))).equals((List[Long](1l.toLong, 9l.toLong, 5l.toLong, 8l.toLong, 6l.toLong, 7l.toLong))));\n    assert(strangeSortList((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong))).equals((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong))));\n    assert(strangeSortList((List[Long]())).equals((List[Long]())));\n    assert(strangeSortList((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong))).equals((List[Long](1l.toLong, 8l.toLong, 2l.toLong, 7l.toLong, 3l.toLong, 6l.toLong, 4l.toLong, 5l.toLong))));\n    assert(strangeSortList((List[Long](0l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 5l.toLong, 5l.toLong, -5l.toLong, -5l.toLong))).equals((List[Long](-5l.toLong, 5l.toLong, -5l.toLong, 5l.toLong, 0l.toLong, 2l.toLong, 2l.toLong, 2l.toLong))));\n    assert(strangeSortList((List[Long](111111l.toLong))).equals((List[Long](111111l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_20_find_closest_elements", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // From a supplied list of numbers (of length at least two) select and return two that are the closest to each\n    // other and return them in order (smaller number, larger number).\n    // >>> findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat)))\n    // ((2.0f, 2.2f))\n    // >>> findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.0f.toFloat)))\n    // ((2.0f, 2.0f))\n    def findClosestElements(numbers : List[Float]) : Tuple2[Float, Float] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat))).equals(((3.9f, 4.0f))));\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 5.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat))).equals(((5.0f, 5.9f))));\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat))).equals(((2.0f, 2.2f))));\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.0f.toFloat))).equals(((2.0f, 2.0f))));\n    assert(findClosestElements((List[Float](1.1f.toFloat, 2.2f.toFloat, 3.1f.toFloat, 4.1f.toFloat, 5.1f.toFloat))).equals(((2.2f, 3.1f))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_20_find_closest_elements", "test": "    }\n    def main(args: Array[String]) = {\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat))).equals(((3.9f, 4.0f))));\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 5.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat))).equals(((5.0f, 5.9f))));\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat))).equals(((2.0f, 2.2f))));\n    assert(findClosestElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.0f.toFloat))).equals(((2.0f, 2.0f))));\n    assert(findClosestElements((List[Float](1.1f.toFloat, 2.2f.toFloat, 3.1f.toFloat, 4.1f.toFloat, 5.1f.toFloat))).equals(((2.2f, 3.1f))));\n    }\n\n}\n"}
{"name": "HumanEval_76_is_simple_power", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Your task is to write a function that returns true if a number x is a simple\n    // power of n and false in other cases.\n    // x is a simple power of n if n**int=x\n    // For example:\n    // >>> isSimplePower((1l), (4l))\n    // (true)\n    // >>> isSimplePower((2l), (2l))\n    // (true)\n    // >>> isSimplePower((8l), (2l))\n    // (true)\n    // >>> isSimplePower((3l), (2l))\n    // (false)\n    // >>> isSimplePower((3l), (1l))\n    // (false)\n    // >>> isSimplePower((5l), (3l))\n    // (false)\n    def isSimplePower(x : Long, n : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isSimplePower((16l), (2l)) == (true));\n    assert(isSimplePower((143214l), (16l)) == (false));\n    assert(isSimplePower((4l), (2l)) == (true));\n    assert(isSimplePower((9l), (3l)) == (true));\n    assert(isSimplePower((16l), (4l)) == (true));\n    assert(isSimplePower((24l), (2l)) == (false));\n    assert(isSimplePower((128l), (4l)) == (false));\n    assert(isSimplePower((12l), (6l)) == (false));\n    assert(isSimplePower((1l), (1l)) == (true));\n    assert(isSimplePower((1l), (12l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_76_is_simple_power", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isSimplePower((16l), (2l)) == (true));\n    assert(isSimplePower((143214l), (16l)) == (false));\n    assert(isSimplePower((4l), (2l)) == (true));\n    assert(isSimplePower((9l), (3l)) == (true));\n    assert(isSimplePower((16l), (4l)) == (true));\n    assert(isSimplePower((24l), (2l)) == (false));\n    assert(isSimplePower((128l), (4l)) == (false));\n    assert(isSimplePower((12l), (6l)) == (false));\n    assert(isSimplePower((1l), (1l)) == (true));\n    assert(isSimplePower((1l), (12l)) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_39_prime_fib", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n    // >>> primeFib((1l))\n    // (2l)\n    // >>> primeFib((2l))\n    // (3l)\n    // >>> primeFib((3l))\n    // (5l)\n    // >>> primeFib((4l))\n    // (13l)\n    // >>> primeFib((5l))\n    // (89l)\n    def primeFib(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(primeFib((1l)) == (2l));\n    assert(primeFib((2l)) == (3l));\n    assert(primeFib((3l)) == (5l));\n    assert(primeFib((4l)) == (13l));\n    assert(primeFib((5l)) == (89l));\n    assert(primeFib((6l)) == (233l));\n    assert(primeFib((7l)) == (1597l));\n    assert(primeFib((8l)) == (28657l));\n    assert(primeFib((9l)) == (514229l));\n    assert(primeFib((10l)) == (433494437l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_39_prime_fib", "test": "    }\n    def main(args: Array[String]) = {\n    assert(primeFib((1l)) == (2l));\n    assert(primeFib((2l)) == (3l));\n    assert(primeFib((3l)) == (5l));\n    assert(primeFib((4l)) == (13l));\n    assert(primeFib((5l)) == (89l));\n    assert(primeFib((6l)) == (233l));\n    assert(primeFib((7l)) == (1597l));\n    assert(primeFib((8l)) == (28657l));\n    assert(primeFib((9l)) == (514229l));\n    assert(primeFib((10l)) == (433494437l));\n    }\n\n}\n"}
{"name": "HumanEval_145_order_by_points", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function which sorts the given list of integers\n    // in ascending order according to the sum of their digits.\n    // Note: if there are several items with similar sum of their digits,\n    // order them based on their index in original list.\n    // For example:\n    // >>> orderByPoints((List[Long](1l.toLong, 11l.toLong, -1l.toLong, -11l.toLong, -12l.toLong)))\n    // (List[Long](-1l.toLong, -11l.toLong, 1l.toLong, -12l.toLong, 11l.toLong))\n    // >>> orderByPoints((List[Long]()))\n    // (List[Long]())\n    def orderByPoints(nums : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(orderByPoints((List[Long](1l.toLong, 11l.toLong, -1l.toLong, -11l.toLong, -12l.toLong))).equals((List[Long](-1l.toLong, -11l.toLong, 1l.toLong, -12l.toLong, 11l.toLong))));\n    assert(orderByPoints((List[Long](1234l.toLong, 423l.toLong, 463l.toLong, 145l.toLong, 2l.toLong, 423l.toLong, 423l.toLong, 53l.toLong, 6l.toLong, 37l.toLong, 3457l.toLong, 3l.toLong, 56l.toLong, 0l.toLong, 46l.toLong))).equals((List[Long](0l.toLong, 2l.toLong, 3l.toLong, 6l.toLong, 53l.toLong, 423l.toLong, 423l.toLong, 423l.toLong, 1234l.toLong, 145l.toLong, 37l.toLong, 46l.toLong, 56l.toLong, 463l.toLong, 3457l.toLong))));\n    assert(orderByPoints((List[Long]())).equals((List[Long]())));\n    assert(orderByPoints((List[Long](1l.toLong, -11l.toLong, -32l.toLong, 43l.toLong, 54l.toLong, -98l.toLong, 2l.toLong, -3l.toLong))).equals((List[Long](-3l.toLong, -32l.toLong, -98l.toLong, -11l.toLong, 1l.toLong, 2l.toLong, 43l.toLong, 54l.toLong))));\n    assert(orderByPoints((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong, 10l.toLong, 11l.toLong))).equals((List[Long](1l.toLong, 10l.toLong, 2l.toLong, 11l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong))));\n    assert(orderByPoints((List[Long](0l.toLong, 6l.toLong, 6l.toLong, -76l.toLong, -21l.toLong, 23l.toLong, 4l.toLong))).equals((List[Long](-76l.toLong, -21l.toLong, 0l.toLong, 4l.toLong, 23l.toLong, 6l.toLong, 6l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_145_order_by_points", "test": "    }\n    def main(args: Array[String]) = {\n    assert(orderByPoints((List[Long](1l.toLong, 11l.toLong, -1l.toLong, -11l.toLong, -12l.toLong))).equals((List[Long](-1l.toLong, -11l.toLong, 1l.toLong, -12l.toLong, 11l.toLong))));\n    assert(orderByPoints((List[Long](1234l.toLong, 423l.toLong, 463l.toLong, 145l.toLong, 2l.toLong, 423l.toLong, 423l.toLong, 53l.toLong, 6l.toLong, 37l.toLong, 3457l.toLong, 3l.toLong, 56l.toLong, 0l.toLong, 46l.toLong))).equals((List[Long](0l.toLong, 2l.toLong, 3l.toLong, 6l.toLong, 53l.toLong, 423l.toLong, 423l.toLong, 423l.toLong, 1234l.toLong, 145l.toLong, 37l.toLong, 46l.toLong, 56l.toLong, 463l.toLong, 3457l.toLong))));\n    assert(orderByPoints((List[Long]())).equals((List[Long]())));\n    assert(orderByPoints((List[Long](1l.toLong, -11l.toLong, -32l.toLong, 43l.toLong, 54l.toLong, -98l.toLong, 2l.toLong, -3l.toLong))).equals((List[Long](-3l.toLong, -32l.toLong, -98l.toLong, -11l.toLong, 1l.toLong, 2l.toLong, 43l.toLong, 54l.toLong))));\n    assert(orderByPoints((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong, 10l.toLong, 11l.toLong))).equals((List[Long](1l.toLong, 10l.toLong, 2l.toLong, 11l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong))));\n    assert(orderByPoints((List[Long](0l.toLong, 6l.toLong, 6l.toLong, -76l.toLong, -21l.toLong, 23l.toLong, 4l.toLong))).equals((List[Long](-76l.toLong, -21l.toLong, 0l.toLong, 4l.toLong, 23l.toLong, 6l.toLong, 6l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_0_has_close_elements", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Check if in given list of numbers, are any two numbers closer to each other than\n    // given threshold.\n    // >>> hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat)), (0.5f))\n    // (false)\n    // >>> hasCloseElements((List[Float](1.0f.toFloat, 2.8f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.0f.toFloat)), (0.3f))\n    // (true)\n    def hasCloseElements(numbers : List[Float], threshold : Float) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat)), (0.3f)) == (true));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat)), (0.05f)) == (false));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 5.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat)), (0.95f)) == (true));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 5.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat)), (0.8f)) == (false));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.0f.toFloat)), (0.1f)) == (true));\n    assert(hasCloseElements((List[Float](1.1f.toFloat, 2.2f.toFloat, 3.1f.toFloat, 4.1f.toFloat, 5.1f.toFloat)), (1.0f)) == (true));\n    assert(hasCloseElements((List[Float](1.1f.toFloat, 2.2f.toFloat, 3.1f.toFloat, 4.1f.toFloat, 5.1f.toFloat)), (0.5f)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_0_has_close_elements", "test": "    }\n    def main(args: Array[String]) = {\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat)), (0.3f)) == (true));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.2f.toFloat)), (0.05f)) == (false));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 5.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat)), (0.95f)) == (true));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 5.9f.toFloat, 4.0f.toFloat, 5.0f.toFloat)), (0.8f)) == (false));\n    assert(hasCloseElements((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat, 2.0f.toFloat)), (0.1f)) == (true));\n    assert(hasCloseElements((List[Float](1.1f.toFloat, 2.2f.toFloat, 3.1f.toFloat, 4.1f.toFloat, 5.1f.toFloat)), (1.0f)) == (true));\n    assert(hasCloseElements((List[Float](1.1f.toFloat, 2.2f.toFloat, 3.1f.toFloat, 4.1f.toFloat, 5.1f.toFloat)), (0.5f)) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_10_make_palindrome", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Find the shortest palindrome that begins with a supplied string.\n    // Algorithm idea is simple:\n    // - Find the longest postfix of supplied string that is a palindrome.\n    // - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n    // >>> makePalindrome((\"\"))\n    // (\"\")\n    // >>> makePalindrome((\"cat\"))\n    // (\"catac\")\n    // >>> makePalindrome((\"cata\"))\n    // (\"catac\")\n    def makePalindrome(string : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(makePalindrome((\"\")).equals((\"\")));\n    assert(makePalindrome((\"x\")).equals((\"x\")));\n    assert(makePalindrome((\"xyz\")).equals((\"xyzyx\")));\n    assert(makePalindrome((\"xyx\")).equals((\"xyx\")));\n    assert(makePalindrome((\"jerry\")).equals((\"jerryrrej\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_10_make_palindrome", "test": "    }\n    def main(args: Array[String]) = {\n    assert(makePalindrome((\"\")).equals((\"\")));\n    assert(makePalindrome((\"x\")).equals((\"x\")));\n    assert(makePalindrome((\"xyz\")).equals((\"xyzyx\")));\n    assert(makePalindrome((\"xyx\")).equals((\"xyx\")));\n    assert(makePalindrome((\"jerry\")).equals((\"jerryrrej\")));\n    }\n\n}\n"}
{"name": "HumanEval_11_string_xor", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Input are two strings a and b consisting only of 1s and 0s.\n    // Perform binary XOR on these inputs and return result also as a string.\n    // >>> stringXor((\"010\"), (\"110\"))\n    // (\"100\")\n    def stringXor(a : String, b : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(stringXor((\"111000\"), (\"101010\")).equals((\"010010\")));\n    assert(stringXor((\"1\"), (\"1\")).equals((\"0\")));\n    assert(stringXor((\"0101\"), (\"0000\")).equals((\"0101\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_11_string_xor", "test": "    }\n    def main(args: Array[String]) = {\n    assert(stringXor((\"111000\"), (\"101010\")).equals((\"010010\")));\n    assert(stringXor((\"1\"), (\"1\")).equals((\"0\")));\n    assert(stringXor((\"0101\"), (\"0000\")).equals((\"0101\")));\n    }\n\n}\n"}
{"name": "HumanEval_139_special_factorial", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // The Brazilian factorial is defined as:\n    // brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n    // where n > 0\n    // For example:\n    // >>> specialFactorial((4l))\n    // (288l)\n    // The function will receive an integer as input and should return the special\n    // factorial of this integer.\n    def specialFactorial(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(specialFactorial((4l)) == (288l));\n    assert(specialFactorial((5l)) == (34560l));\n    assert(specialFactorial((7l)) == (125411328000l));\n    assert(specialFactorial((1l)) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_139_special_factorial", "test": "    }\n    def main(args: Array[String]) = {\n    assert(specialFactorial((4l)) == (288l));\n    assert(specialFactorial((5l)) == (34560l));\n    assert(specialFactorial((7l)) == (125411328000l));\n    assert(specialFactorial((1l)) == (1l));\n    }\n\n}\n"}
{"name": "HumanEval_122_add_elements", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a non-empty list of integers arr and an integer k, return\n    // the sum of the elements with at most two digits from the first k elements of arr.\n    // Example:\n    // >>> addElements((List[Long](111l.toLong, 21l.toLong, 3l.toLong, 4000l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong)), (4l))\n    // (24l)\n    // Constraints:\n    // 1. 1 <= len(arr) <= 100\n    // 2. 1 <= k <= len(arr)\n    def addElements(arr : List[Long], k : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(addElements((List[Long](1l.toLong, -2l.toLong, -3l.toLong, 41l.toLong, 57l.toLong, 76l.toLong, 87l.toLong, 88l.toLong, 99l.toLong)), (3l)) == (-4l));\n    assert(addElements((List[Long](111l.toLong, 121l.toLong, 3l.toLong, 4000l.toLong, 5l.toLong, 6l.toLong)), (2l)) == (0l));\n    assert(addElements((List[Long](11l.toLong, 21l.toLong, 3l.toLong, 90l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong)), (4l)) == (125l));\n    assert(addElements((List[Long](111l.toLong, 21l.toLong, 3l.toLong, 4000l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong)), (4l)) == (24l));\n    assert(addElements((List[Long](1l.toLong)), (1l)) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_122_add_elements", "test": "    }\n    def main(args: Array[String]) = {\n    assert(addElements((List[Long](1l.toLong, -2l.toLong, -3l.toLong, 41l.toLong, 57l.toLong, 76l.toLong, 87l.toLong, 88l.toLong, 99l.toLong)), (3l)) == (-4l));\n    assert(addElements((List[Long](111l.toLong, 121l.toLong, 3l.toLong, 4000l.toLong, 5l.toLong, 6l.toLong)), (2l)) == (0l));\n    assert(addElements((List[Long](11l.toLong, 21l.toLong, 3l.toLong, 90l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong)), (4l)) == (125l));\n    assert(addElements((List[Long](111l.toLong, 21l.toLong, 3l.toLong, 4000l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong)), (4l)) == (24l));\n    assert(addElements((List[Long](1l.toLong)), (1l)) == (1l));\n    }\n\n}\n"}
{"name": "HumanEval_46_fib4", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n    // fib4(0) -> 0\n    // fib4(1) -> 0\n    // fib4(2) -> 2\n    // fib4(3) -> 0\n    // fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n    // Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n    // >>> fib4((5l))\n    // (4l)\n    // >>> fib4((6l))\n    // (8l)\n    // >>> fib4((7l))\n    // (14l)\n    def fib4(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fib4((5l)) == (4l));\n    assert(fib4((8l)) == (28l));\n    assert(fib4((10l)) == (104l));\n    assert(fib4((12l)) == (386l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_46_fib4", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fib4((5l)) == (4l));\n    assert(fib4((8l)) == (28l));\n    assert(fib4((10l)) == (104l));\n    assert(fib4((12l)) == (386l));\n    }\n\n}\n"}
{"name": "HumanEval_104_unique_digits", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of positive integers x. return a sorted list of all \n    // elements that hasn't any even digit.\n    // Note: Returned list should be sorted in increasing order.\n    // For example:\n    // >>> uniqueDigits((List[Long](15l.toLong, 33l.toLong, 1422l.toLong, 1l.toLong)))\n    // (List[Long](1l.toLong, 15l.toLong, 33l.toLong))\n    // >>> uniqueDigits((List[Long](152l.toLong, 323l.toLong, 1422l.toLong, 10l.toLong)))\n    // (List[Long]())\n    def uniqueDigits(x : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(uniqueDigits((List[Long](15l.toLong, 33l.toLong, 1422l.toLong, 1l.toLong))).equals((List[Long](1l.toLong, 15l.toLong, 33l.toLong))));\n    assert(uniqueDigits((List[Long](152l.toLong, 323l.toLong, 1422l.toLong, 10l.toLong))).equals((List[Long]())));\n    assert(uniqueDigits((List[Long](12345l.toLong, 2033l.toLong, 111l.toLong, 151l.toLong))).equals((List[Long](111l.toLong, 151l.toLong))));\n    assert(uniqueDigits((List[Long](135l.toLong, 103l.toLong, 31l.toLong))).equals((List[Long](31l.toLong, 135l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_104_unique_digits", "test": "    }\n    def main(args: Array[String]) = {\n    assert(uniqueDigits((List[Long](15l.toLong, 33l.toLong, 1422l.toLong, 1l.toLong))).equals((List[Long](1l.toLong, 15l.toLong, 33l.toLong))));\n    assert(uniqueDigits((List[Long](152l.toLong, 323l.toLong, 1422l.toLong, 10l.toLong))).equals((List[Long]())));\n    assert(uniqueDigits((List[Long](12345l.toLong, 2033l.toLong, 111l.toLong, 151l.toLong))).equals((List[Long](111l.toLong, 151l.toLong))));\n    assert(uniqueDigits((List[Long](135l.toLong, 103l.toLong, 31l.toLong))).equals((List[Long](31l.toLong, 135l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_117_select_words", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string s and a natural number n, you have been tasked to implement \n    // a function that returns a list of all words from string s that contain exactly \n    // n consonants, in order these words appear in the string s.\n    // If the string s is empty then the function should return an empty list.\n    // Note: you may assume the input string contains only letters and spaces.\n    // Examples:\n    // >>> selectWords((\"Mary had a little lamb\"), (4l))\n    // (List[String](\"little\"))\n    // >>> selectWords((\"Mary had a little lamb\"), (3l))\n    // (List[String](\"Mary\", \"lamb\"))\n    // >>> selectWords((\"simple white space\"), (2l))\n    // (List[String]())\n    // >>> selectWords((\"Hello world\"), (4l))\n    // (List[String](\"world\"))\n    // >>> selectWords((\"Uncle sam\"), (3l))\n    // (List[String](\"Uncle\"))\n    def selectWords(s : String, n : Long) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(selectWords((\"Mary had a little lamb\"), (4l)).equals((List[String](\"little\"))));\n    assert(selectWords((\"Mary had a little lamb\"), (3l)).equals((List[String](\"Mary\", \"lamb\"))));\n    assert(selectWords((\"simple white space\"), (2l)).equals((List[String]())));\n    assert(selectWords((\"Hello world\"), (4l)).equals((List[String](\"world\"))));\n    assert(selectWords((\"Uncle sam\"), (3l)).equals((List[String](\"Uncle\"))));\n    assert(selectWords((\"\"), (4l)).equals((List[String]())));\n    assert(selectWords((\"a b c d e f\"), (1l)).equals((List[String](\"b\", \"c\", \"d\", \"f\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_117_select_words", "test": "    }\n    def main(args: Array[String]) = {\n    assert(selectWords((\"Mary had a little lamb\"), (4l)).equals((List[String](\"little\"))));\n    assert(selectWords((\"Mary had a little lamb\"), (3l)).equals((List[String](\"Mary\", \"lamb\"))));\n    assert(selectWords((\"simple white space\"), (2l)).equals((List[String]())));\n    assert(selectWords((\"Hello world\"), (4l)).equals((List[String](\"world\"))));\n    assert(selectWords((\"Uncle sam\"), (3l)).equals((List[String](\"Uncle\"))));\n    assert(selectWords((\"\"), (4l)).equals((List[String]())));\n    assert(selectWords((\"a b c d e f\"), (1l)).equals((List[String](\"b\", \"c\", \"d\", \"f\"))));\n    }\n\n}\n"}
{"name": "HumanEval_72_will_it_fly", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that returns true if the object q will fly, and false otherwise.\n    // The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n    // Example:\n    // >>> willItFly((List[Long](1l.toLong, 2l.toLong)), (5l))\n    // (false)\n    // # 1+2 is less than the maximum possible weight, but it's unbalanced.\n    // >>> willItFly((List[Long](3l.toLong, 2l.toLong, 3l.toLong)), (1l))\n    // (false)\n    // # it's balanced, but 3+2+3 is more than the maximum possible weight.\n    // >>> willItFly((List[Long](3l.toLong, 2l.toLong, 3l.toLong)), (9l))\n    // (true)\n    // # 3+2+3 is less than the maximum possible weight, and it's balanced.\n    // >>> willItFly((List[Long](3l.toLong)), (5l))\n    // (true)\n    // # 3 is less than the maximum possible weight, and it's balanced.\n    def willItFly(q : List[Long], w : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(willItFly((List[Long](3l.toLong, 2l.toLong, 3l.toLong)), (9l)) == (true));\n    assert(willItFly((List[Long](1l.toLong, 2l.toLong)), (5l)) == (false));\n    assert(willItFly((List[Long](3l.toLong)), (5l)) == (true));\n    assert(willItFly((List[Long](3l.toLong, 2l.toLong, 3l.toLong)), (1l)) == (false));\n    assert(willItFly((List[Long](1l.toLong, 2l.toLong, 3l.toLong)), (6l)) == (false));\n    assert(willItFly((List[Long](5l.toLong)), (5l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_72_will_it_fly", "test": "    }\n    def main(args: Array[String]) = {\n    assert(willItFly((List[Long](3l.toLong, 2l.toLong, 3l.toLong)), (9l)) == (true));\n    assert(willItFly((List[Long](1l.toLong, 2l.toLong)), (5l)) == (false));\n    assert(willItFly((List[Long](3l.toLong)), (5l)) == (true));\n    assert(willItFly((List[Long](3l.toLong, 2l.toLong, 3l.toLong)), (1l)) == (false));\n    assert(willItFly((List[Long](1l.toLong, 2l.toLong, 3l.toLong)), (6l)) == (false));\n    assert(willItFly((List[Long](5l.toLong)), (5l)) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_55_fib", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return n-th Fibonacci number.\n    // >>> fib((10l))\n    // (55l)\n    // >>> fib((1l))\n    // (1l)\n    // >>> fib((8l))\n    // (21l)\n    def fib(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fib((10l)) == (55l));\n    assert(fib((1l)) == (1l));\n    assert(fib((8l)) == (21l));\n    assert(fib((11l)) == (89l));\n    assert(fib((12l)) == (144l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_55_fib", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fib((10l)) == (55l));\n    assert(fib((1l)) == (1l));\n    assert(fib((8l)) == (21l));\n    assert(fib((11l)) == (89l));\n    assert(fib((12l)) == (144l));\n    }\n\n}\n"}
{"name": "HumanEval_153_Strongest_Extension", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You will be given the name of a class (a string) and a list of extensions.\n    // The extensions are to be used to load additional classes to the class. The\n    // strength of the extension is as follows: Let CAP be the number of the uppercase\n    // letters in the extension's name, and let SM be the number of lowercase letters \n    // in the extension's name, the strength is given by the fraction CAP - SM. \n    // You should find the strongest extension and return a string in this \n    // format: ClassName.StrongestExtensionName.\n    // If there are two or more extensions with the same strength, you should\n    // choose the one that comes first in the list.\n    // For example, if you are given \"Slices\" as the class and a list of the\n    // extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    // return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    // (its strength is -1).\n    // Example:\n    // >>> StrongestExtension((\"my_class\"), (List[String](\"AA\", \"Be\", \"CC\")))\n    // (\"my_class.AA\")\n    def StrongestExtension(class_name : String, extensions : List[String]) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(StrongestExtension((\"Watashi\"), (List[String](\"tEN\", \"niNE\", \"eIGHt8OKe\"))).equals((\"Watashi.eIGHt8OKe\")));\n    assert(StrongestExtension((\"Boku123\"), (List[String](\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"))).equals((\"Boku123.YEs.WeCaNe\")));\n    assert(StrongestExtension((\"__YESIMHERE\"), (List[String](\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"))).equals((\"__YESIMHERE.NuLl__\")));\n    assert(StrongestExtension((\"K\"), (List[String](\"Ta\", \"TAR\", \"t234An\", \"cosSo\"))).equals((\"K.TAR\")));\n    assert(StrongestExtension((\"__HAHA\"), (List[String](\"Tab\", \"123\", \"781345\", \"-_-\"))).equals((\"__HAHA.123\")));\n    assert(StrongestExtension((\"YameRore\"), (List[String](\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"))).equals((\"YameRore.okIWILL123\")));\n    assert(StrongestExtension((\"finNNalLLly\"), (List[String](\"Die\", \"NowW\", \"Wow\", \"WoW\"))).equals((\"finNNalLLly.WoW\")));\n    assert(StrongestExtension((\"_\"), (List[String](\"Bb\", \"91245\"))).equals((\"_.Bb\")));\n    assert(StrongestExtension((\"Sp\"), (List[String](\"671235\", \"Bb\"))).equals((\"Sp.671235\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_153_Strongest_Extension", "test": "    }\n    def main(args: Array[String]) = {\n    assert(StrongestExtension((\"Watashi\"), (List[String](\"tEN\", \"niNE\", \"eIGHt8OKe\"))).equals((\"Watashi.eIGHt8OKe\")));\n    assert(StrongestExtension((\"Boku123\"), (List[String](\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"))).equals((\"Boku123.YEs.WeCaNe\")));\n    assert(StrongestExtension((\"__YESIMHERE\"), (List[String](\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"))).equals((\"__YESIMHERE.NuLl__\")));\n    assert(StrongestExtension((\"K\"), (List[String](\"Ta\", \"TAR\", \"t234An\", \"cosSo\"))).equals((\"K.TAR\")));\n    assert(StrongestExtension((\"__HAHA\"), (List[String](\"Tab\", \"123\", \"781345\", \"-_-\"))).equals((\"__HAHA.123\")));\n    assert(StrongestExtension((\"YameRore\"), (List[String](\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"))).equals((\"YameRore.okIWILL123\")));\n    assert(StrongestExtension((\"finNNalLLly\"), (List[String](\"Die\", \"NowW\", \"Wow\", \"WoW\"))).equals((\"finNNalLLly.WoW\")));\n    assert(StrongestExtension((\"_\"), (List[String](\"Bb\", \"91245\"))).equals((\"_.Bb\")));\n    assert(StrongestExtension((\"Sp\"), (List[String](\"671235\", \"Bb\"))).equals((\"Sp.671235\")));\n    }\n\n}\n"}
{"name": "HumanEval_119_match_parens", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a list of two strings, both strings consist of open\n    // parentheses '(' or close parentheses ')' only.\n    // Your job is to check if it is possible to concatenate the two strings in\n    // some order, that the resulting string will be good.\n    // A string S is considered to be good if and only if all parentheses in S\n    // are balanced. For example: the string '(())()' is good, while the string\n    // '())' is not.\n    // Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n    // Examples:\n    // >>> matchParens((List[String](\"()(\", \")\")))\n    // (\"Yes\")\n    // >>> matchParens((List[String](\")\", \")\")))\n    // (\"No\")\n    def matchParens(lst : List[String]) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(matchParens((List[String](\"()(\", \")\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\")\", \")\"))).equals((\"No\")));\n    assert(matchParens((List[String](\"(()(())\", \"())())\"))).equals((\"No\")));\n    assert(matchParens((List[String](\")())\", \"(()()(\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\"(())))\", \"(()())((\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\"()\", \"())\"))).equals((\"No\")));\n    assert(matchParens((List[String](\"(()(\", \"()))()\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\"((((\", \"((())\"))).equals((\"No\")));\n    assert(matchParens((List[String](\")(()\", \"(()(\"))).equals((\"No\")));\n    assert(matchParens((List[String](\")(\", \")(\"))).equals((\"No\")));\n    assert(matchParens((List[String](\"(\", \")\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\")\", \"(\"))).equals((\"Yes\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_119_match_parens", "test": "    }\n    def main(args: Array[String]) = {\n    assert(matchParens((List[String](\"()(\", \")\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\")\", \")\"))).equals((\"No\")));\n    assert(matchParens((List[String](\"(()(())\", \"())())\"))).equals((\"No\")));\n    assert(matchParens((List[String](\")())\", \"(()()(\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\"(())))\", \"(()())((\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\"()\", \"())\"))).equals((\"No\")));\n    assert(matchParens((List[String](\"(()(\", \"()))()\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\"((((\", \"((())\"))).equals((\"No\")));\n    assert(matchParens((List[String](\")(()\", \"(()(\"))).equals((\"No\")));\n    assert(matchParens((List[String](\")(\", \")(\"))).equals((\"No\")));\n    assert(matchParens((List[String](\"(\", \")\"))).equals((\"Yes\")));\n    assert(matchParens((List[String](\")\", \"(\"))).equals((\"Yes\")));\n    }\n\n}\n"}
{"name": "HumanEval_90_next_smallest", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a list of integers.\n    // Write a function next_smallest() that returns the 2nd smallest element of the list.\n    // Return None if there is no such element.\n    // >>> nextSmallest((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong)))\n    // 2l\n    // >>> nextSmallest((List[Long](5l.toLong, 1l.toLong, 4l.toLong, 3l.toLong, 2l.toLong)))\n    // 2l\n    // >>> nextSmallest((List[Long]()))\n    // None\n    // >>> nextSmallest((List[Long](1l.toLong, 1l.toLong)))\n    // None\n    def nextSmallest(lst : List[Long]) : Option[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(nextSmallest((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))).equals(2l));\n    assert(nextSmallest((List[Long](5l.toLong, 1l.toLong, 4l.toLong, 3l.toLong, 2l.toLong))).equals(2l));\n    assert(nextSmallest((List[Long]())).equals(None));\n    assert(nextSmallest((List[Long](1l.toLong, 1l.toLong))).equals(None));\n    assert(nextSmallest((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 0l.toLong))).equals(1l));\n    assert(nextSmallest((List[Long](1l.toLong, 1l.toLong))).equals(None));\n    assert(nextSmallest((List[Long](-35l.toLong, 34l.toLong, 12l.toLong, -45l.toLong))).equals(-35l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_90_next_smallest", "test": "    }\n    def main(args: Array[String]) = {\n    assert(nextSmallest((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))).equals(2l));\n    assert(nextSmallest((List[Long](5l.toLong, 1l.toLong, 4l.toLong, 3l.toLong, 2l.toLong))).equals(2l));\n    assert(nextSmallest((List[Long]())).equals(None));\n    assert(nextSmallest((List[Long](1l.toLong, 1l.toLong))).equals(None));\n    assert(nextSmallest((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 0l.toLong))).equals(1l));\n    assert(nextSmallest((List[Long](1l.toLong, 1l.toLong))).equals(None));\n    assert(nextSmallest((List[Long](-35l.toLong, 34l.toLong, 12l.toLong, -45l.toLong))).equals(-35l));\n    }\n\n}\n"}
{"name": "HumanEval_92_any_int", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function that takes 3 numbers.\n    // Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n    // Returns false in any other cases.\n    // Examples\n    // >>> anyInt(5l, 2l, 7l)\n    // (true)\n    // >>> anyInt(3l, 2l, 2l)\n    // (false)\n    // >>> anyInt(3l, -2l, 1l)\n    // (true)\n    // >>> anyInt((3.6f), (-2.2f), 2l)\n    // (false)\n    def anyInt(x : Float, y : Float, z : Float) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(anyInt(2l, 3l, 1l) == (true));\n    assert(anyInt((2.5f), 2l, 3l) == (false));\n    assert(anyInt((1.5f), 5l, (3.5f)) == (false));\n    assert(anyInt(2l, 6l, 2l) == (false));\n    assert(anyInt(4l, 2l, 2l) == (true));\n    assert(anyInt((2.2f), (2.2f), (2.2f)) == (false));\n    assert(anyInt(-4l, 6l, 2l) == (true));\n    assert(anyInt(2l, 1l, 1l) == (true));\n    assert(anyInt(3l, 4l, 7l) == (true));\n    assert(anyInt((3.0f), 4l, 7l) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_92_any_int", "test": "    }\n    def main(args: Array[String]) = {\n    assert(anyInt(2l, 3l, 1l) == (true));\n    assert(anyInt((2.5f), 2l, 3l) == (false));\n    assert(anyInt((1.5f), 5l, (3.5f)) == (false));\n    assert(anyInt(2l, 6l, 2l) == (false));\n    assert(anyInt(4l, 2l, 2l) == (true));\n    assert(anyInt((2.2f), (2.2f), (2.2f)) == (false));\n    assert(anyInt(-4l, 6l, 2l) == (true));\n    assert(anyInt(2l, 1l, 1l) == (true));\n    assert(anyInt(3l, 4l, 7l) == (true));\n    assert(anyInt((3.0f), 4l, 7l) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_2_truncate_number", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive floating point number, it can be decomposed into\n    // and integer part (largest integer smaller than given number) and decimals\n    // (leftover part always smaller than 1).\n    // Return the decimal part of the number.\n    // >>> truncateNumber((3.5f))\n    // (0.5f)\n    def truncateNumber(number : Float) : Float = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(truncateNumber((3.5f)) == (0.5f));\n    assert(truncateNumber((1.25f)) == (0.25f));\n    assert(truncateNumber((123.0f)) == (0.0f));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_2_truncate_number", "test": "    }\n    def main(args: Array[String]) = {\n    assert(truncateNumber((3.5f)) == (0.5f));\n    assert(truncateNumber((1.25f)) == (0.25f));\n    assert(truncateNumber((123.0f)) == (0.0f));\n    }\n\n}\n"}
{"name": "HumanEval_42_incr_list", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return list with elements incremented by 1.\n    // >>> incrList((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[Long](2l.toLong, 3l.toLong, 4l.toLong))\n    // >>> incrList((List[Long](5l.toLong, 3l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong)))\n    // (List[Long](6l.toLong, 4l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 4l.toLong, 10l.toLong, 1l.toLong, 124l.toLong))\n    def incrList(l : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(incrList((List[Long]())).equals((List[Long]())));\n    assert(incrList((List[Long](3l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](4l.toLong, 3l.toLong, 2l.toLong))));\n    assert(incrList((List[Long](5l.toLong, 2l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong))).equals((List[Long](6l.toLong, 3l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 4l.toLong, 10l.toLong, 1l.toLong, 124l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_42_incr_list", "test": "    }\n    def main(args: Array[String]) = {\n    assert(incrList((List[Long]())).equals((List[Long]())));\n    assert(incrList((List[Long](3l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](4l.toLong, 3l.toLong, 2l.toLong))));\n    assert(incrList((List[Long](5l.toLong, 2l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong))).equals((List[Long](6l.toLong, 3l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 4l.toLong, 10l.toLong, 1l.toLong, 124l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_150_x_or_y", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // A simple program which should return the value of x if n is \n    // a prime number and should return the value of y otherwise.\n    // Examples:\n    // >>> xOrY((7l), (34l), (12l))\n    // (34l)\n    // >>> xOrY((15l), (8l), (5l))\n    // (5l)\n    def xOrY(n : Long, x : Long, y : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(xOrY((7l), (34l), (12l)) == (34l));\n    assert(xOrY((15l), (8l), (5l)) == (5l));\n    assert(xOrY((3l), (33l), (5212l)) == (33l));\n    assert(xOrY((1259l), (3l), (52l)) == (3l));\n    assert(xOrY((7919l), (-1l), (12l)) == (-1l));\n    assert(xOrY((3609l), (1245l), (583l)) == (583l));\n    assert(xOrY((91l), (56l), (129l)) == (129l));\n    assert(xOrY((6l), (34l), (1234l)) == (1234l));\n    assert(xOrY((1l), (2l), (0l)) == (0l));\n    assert(xOrY((2l), (2l), (0l)) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_150_x_or_y", "test": "    }\n    def main(args: Array[String]) = {\n    assert(xOrY((7l), (34l), (12l)) == (34l));\n    assert(xOrY((15l), (8l), (5l)) == (5l));\n    assert(xOrY((3l), (33l), (5212l)) == (33l));\n    assert(xOrY((1259l), (3l), (52l)) == (3l));\n    assert(xOrY((7919l), (-1l), (12l)) == (-1l));\n    assert(xOrY((3609l), (1245l), (583l)) == (583l));\n    assert(xOrY((91l), (56l), (129l)) == (129l));\n    assert(xOrY((6l), (34l), (1234l)) == (1234l));\n    assert(xOrY((1l), (2l), (0l)) == (0l));\n    assert(xOrY((2l), (2l), (0l)) == (2l));\n    }\n\n}\n"}
{"name": "HumanEval_49_modp", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return 2^n modulo p (be aware of numerics).\n    // >>> modp((3l), (5l))\n    // (3l)\n    // >>> modp((1101l), (101l))\n    // (2l)\n    // >>> modp((0l), (101l))\n    // (1l)\n    // >>> modp((3l), (11l))\n    // (8l)\n    // >>> modp((100l), (101l))\n    // (1l)\n    def modp(n : Long, p : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(modp((3l), (5l)) == (3l));\n    assert(modp((1101l), (101l)) == (2l));\n    assert(modp((0l), (101l)) == (1l));\n    assert(modp((3l), (11l)) == (8l));\n    assert(modp((100l), (101l)) == (1l));\n    assert(modp((30l), (5l)) == (4l));\n    assert(modp((31l), (5l)) == (3l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_49_modp", "test": "    }\n    def main(args: Array[String]) = {\n    assert(modp((3l), (5l)) == (3l));\n    assert(modp((1101l), (101l)) == (2l));\n    assert(modp((0l), (101l)) == (1l));\n    assert(modp((3l), (11l)) == (8l));\n    assert(modp((100l), (101l)) == (1l));\n    assert(modp((30l), (5l)) == (4l));\n    assert(modp((31l), (5l)) == (3l));\n    }\n\n}\n"}
{"name": "HumanEval_155_even_odd_count", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given an integer. return a tuple that has the number of even and odd digits respectively.\n    // Example:\n    // >>> evenOddCount((-12l))\n    // ((1l, 1l))\n    // >>> evenOddCount((123l))\n    // ((1l, 2l))\n    def evenOddCount(num : Long) : Tuple2[Long, Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(evenOddCount((7l)).equals(((0l, 1l))));\n    assert(evenOddCount((-78l)).equals(((1l, 1l))));\n    assert(evenOddCount((3452l)).equals(((2l, 2l))));\n    assert(evenOddCount((346211l)).equals(((3l, 3l))));\n    assert(evenOddCount((-345821l)).equals(((3l, 3l))));\n    assert(evenOddCount((-2l)).equals(((1l, 0l))));\n    assert(evenOddCount((-45347l)).equals(((2l, 3l))));\n    assert(evenOddCount((0l)).equals(((1l, 0l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_155_even_odd_count", "test": "    }\n    def main(args: Array[String]) = {\n    assert(evenOddCount((7l)).equals(((0l, 1l))));\n    assert(evenOddCount((-78l)).equals(((1l, 1l))));\n    assert(evenOddCount((3452l)).equals(((2l, 2l))));\n    assert(evenOddCount((346211l)).equals(((3l, 3l))));\n    assert(evenOddCount((-345821l)).equals(((3l, 3l))));\n    assert(evenOddCount((-2l)).equals(((1l, 0l))));\n    assert(evenOddCount((-45347l)).equals(((2l, 3l))));\n    assert(evenOddCount((0l)).equals(((1l, 0l))));\n    }\n\n}\n"}
{"name": "HumanEval_80_is_happy", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a string s.\n    // Your task is to check if the string is hapscala or not.\n    // A string is hapscala if its length is at least 3 and every 3 consecutive letters are distinct\n    // For example:\n    // >>> isHappy((\"a\"))\n    // (false)\n    // >>> isHappy((\"aa\"))\n    // (false)\n    // >>> isHappy((\"abcd\"))\n    // (true)\n    // >>> isHappy((\"aabb\"))\n    // (false)\n    // >>> isHappy((\"adb\"))\n    // (true)\n    // >>> isHappy((\"xyy\"))\n    // (false)\n    def isHappy(s : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isHappy((\"a\")) == (false));\n    assert(isHappy((\"aa\")) == (false));\n    assert(isHappy((\"abcd\")) == (true));\n    assert(isHappy((\"aabb\")) == (false));\n    assert(isHappy((\"adb\")) == (true));\n    assert(isHappy((\"xyy\")) == (false));\n    assert(isHappy((\"iopaxpoi\")) == (true));\n    assert(isHappy((\"iopaxioi\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_80_is_happy", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isHappy((\"a\")) == (false));\n    assert(isHappy((\"aa\")) == (false));\n    assert(isHappy((\"abcd\")) == (true));\n    assert(isHappy((\"aabb\")) == (false));\n    assert(isHappy((\"adb\")) == (true));\n    assert(isHappy((\"xyy\")) == (false));\n    assert(isHappy((\"iopaxpoi\")) == (true));\n    assert(isHappy((\"iopaxioi\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_59_largest_prime_factor", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return the largest prime factor of n. Assume n > 1 and is not a prime.\n    // >>> largestPrimeFactor((13195l))\n    // (29l)\n    // >>> largestPrimeFactor((2048l))\n    // (2l)\n    def largestPrimeFactor(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(largestPrimeFactor((15l)) == (5l));\n    assert(largestPrimeFactor((27l)) == (3l));\n    assert(largestPrimeFactor((63l)) == (7l));\n    assert(largestPrimeFactor((330l)) == (11l));\n    assert(largestPrimeFactor((13195l)) == (29l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_59_largest_prime_factor", "test": "    }\n    def main(args: Array[String]) = {\n    assert(largestPrimeFactor((15l)) == (5l));\n    assert(largestPrimeFactor((27l)) == (3l));\n    assert(largestPrimeFactor((63l)) == (7l));\n    assert(largestPrimeFactor((330l)) == (11l));\n    assert(largestPrimeFactor((13195l)) == (29l));\n    }\n\n}\n"}
{"name": "HumanEval_66_digitSum", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Task\n    // Write a function that takes a string as input and returns the sum of the upper characters only'\n    // ASCII codes.\n    // Examples:\n    // >>> digitSum((\"\"))\n    // (0l)\n    // >>> digitSum((\"abAB\"))\n    // (131l)\n    // >>> digitSum((\"abcCd\"))\n    // (67l)\n    // >>> digitSum((\"helloE\"))\n    // (69l)\n    // >>> digitSum((\"woArBld\"))\n    // (131l)\n    // >>> digitSum((\"aAaaaXa\"))\n    // (153l)\n    def digitSum(s : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(digitSum((\"\")) == (0l));\n    assert(digitSum((\"abAB\")) == (131l));\n    assert(digitSum((\"abcCd\")) == (67l));\n    assert(digitSum((\"helloE\")) == (69l));\n    assert(digitSum((\"woArBld\")) == (131l));\n    assert(digitSum((\"aAaaaXa\")) == (153l));\n    assert(digitSum((\" How are yOu?\")) == (151l));\n    assert(digitSum((\"You arE Very Smart\")) == (327l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_66_digitSum", "test": "    }\n    def main(args: Array[String]) = {\n    assert(digitSum((\"\")) == (0l));\n    assert(digitSum((\"abAB\")) == (131l));\n    assert(digitSum((\"abcCd\")) == (67l));\n    assert(digitSum((\"helloE\")) == (69l));\n    assert(digitSum((\"woArBld\")) == (131l));\n    assert(digitSum((\"aAaaaXa\")) == (153l));\n    assert(digitSum((\" How are yOu?\")) == (151l));\n    assert(digitSum((\"You arE Very Smart\")) == (327l));\n    }\n\n}\n"}
{"name": "HumanEval_21_rescale_to_unit", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given list of numbers (of at least two elements), apply a linear transform to that list,\n    // such that the smallest number will become 0 and the largest will become 1\n    // >>> rescaleToUnit((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat)))\n    // (List[Float](0.0f.toFloat, 0.25f.toFloat, 0.5f.toFloat, 0.75f.toFloat, 1.0f.toFloat))\n    def rescaleToUnit(numbers : List[Float]) : List[Float] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(rescaleToUnit((List[Float](2.0f.toFloat, 49.9f.toFloat))).equals((List[Float](0.0f.toFloat, 1.0f.toFloat))));\n    assert(rescaleToUnit((List[Float](100.0f.toFloat, 49.9f.toFloat))).equals((List[Float](1.0f.toFloat, 0.0f.toFloat))));\n    assert(rescaleToUnit((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat))).equals((List[Float](0.0f.toFloat, 0.25f.toFloat, 0.5f.toFloat, 0.75f.toFloat, 1.0f.toFloat))));\n    assert(rescaleToUnit((List[Float](2.0f.toFloat, 1.0f.toFloat, 5.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat))).equals((List[Float](0.25f.toFloat, 0.0f.toFloat, 1.0f.toFloat, 0.5f.toFloat, 0.75f.toFloat))));\n    assert(rescaleToUnit((List[Float](12.0f.toFloat, 11.0f.toFloat, 15.0f.toFloat, 13.0f.toFloat, 14.0f.toFloat))).equals((List[Float](0.25f.toFloat, 0.0f.toFloat, 1.0f.toFloat, 0.5f.toFloat, 0.75f.toFloat))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_21_rescale_to_unit", "test": "    }\n    def main(args: Array[String]) = {\n    assert(rescaleToUnit((List[Float](2.0f.toFloat, 49.9f.toFloat))).equals((List[Float](0.0f.toFloat, 1.0f.toFloat))));\n    assert(rescaleToUnit((List[Float](100.0f.toFloat, 49.9f.toFloat))).equals((List[Float](1.0f.toFloat, 0.0f.toFloat))));\n    assert(rescaleToUnit((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat))).equals((List[Float](0.0f.toFloat, 0.25f.toFloat, 0.5f.toFloat, 0.75f.toFloat, 1.0f.toFloat))));\n    assert(rescaleToUnit((List[Float](2.0f.toFloat, 1.0f.toFloat, 5.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat))).equals((List[Float](0.25f.toFloat, 0.0f.toFloat, 1.0f.toFloat, 0.5f.toFloat, 0.75f.toFloat))));\n    assert(rescaleToUnit((List[Float](12.0f.toFloat, 11.0f.toFloat, 15.0f.toFloat, 13.0f.toFloat, 14.0f.toFloat))).equals((List[Float](0.25f.toFloat, 0.0f.toFloat, 1.0f.toFloat, 0.5f.toFloat, 0.75f.toFloat))));\n    }\n\n}\n"}
{"name": "HumanEval_121_solution", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a non-empty list of integers, return the sum of all of the odd elements that are in even positions.\n    // Examples\n    // >>> solution((List[Long](5l.toLong, 8l.toLong, 7l.toLong, 1l.toLong)))\n    // (12l)\n    // >>> solution((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 3l.toLong)))\n    // (9l)\n    // >>> solution((List[Long](30l.toLong, 13l.toLong, 24l.toLong, 321l.toLong)))\n    // (0l)\n    def solution(lst : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(solution((List[Long](5l.toLong, 8l.toLong, 7l.toLong, 1l.toLong))) == (12l));\n    assert(solution((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 3l.toLong))) == (9l));\n    assert(solution((List[Long](30l.toLong, 13l.toLong, 24l.toLong, 321l.toLong))) == (0l));\n    assert(solution((List[Long](5l.toLong, 9l.toLong))) == (5l));\n    assert(solution((List[Long](2l.toLong, 4l.toLong, 8l.toLong))) == (0l));\n    assert(solution((List[Long](30l.toLong, 13l.toLong, 23l.toLong, 32l.toLong))) == (23l));\n    assert(solution((List[Long](3l.toLong, 13l.toLong, 2l.toLong, 9l.toLong))) == (3l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_121_solution", "test": "    }\n    def main(args: Array[String]) = {\n    assert(solution((List[Long](5l.toLong, 8l.toLong, 7l.toLong, 1l.toLong))) == (12l));\n    assert(solution((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 3l.toLong))) == (9l));\n    assert(solution((List[Long](30l.toLong, 13l.toLong, 24l.toLong, 321l.toLong))) == (0l));\n    assert(solution((List[Long](5l.toLong, 9l.toLong))) == (5l));\n    assert(solution((List[Long](2l.toLong, 4l.toLong, 8l.toLong))) == (0l));\n    assert(solution((List[Long](30l.toLong, 13l.toLong, 23l.toLong, 32l.toLong))) == (23l));\n    assert(solution((List[Long](3l.toLong, 13l.toLong, 2l.toLong, 9l.toLong))) == (3l));\n    }\n\n}\n"}
{"name": "HumanEval_68_pluck", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // \"Given a list representing a branch of a tree that has non-negative integer nodes\n    // your task is to pluck one of the nodes and return it.\n    // The plucked node should be the node with the smallest even value.\n    // If multiple nodes with the same smallest even value are found return the node that has smallest index.\n    // The plucked node should be returned in a list, [ smalest_value, its index ],\n    // If there are no even values or the given list is empty, return [].\n    // Example 1:\n    // >>> pluck((List[Long](4l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[Long](2l.toLong, 1l.toLong))\n    // Explanation: 2 has the smallest even value, and 2 has the smallest index.\n    // Example 2:\n    // >>> pluck((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[Long](2l.toLong, 1l.toLong))\n    // Explanation: 2 has the smallest even value, and 2 has the smallest index.\n    // Example 3:\n    // >>> pluck((List[Long]()))\n    // (List[Long]())\n    // Example 4:\n    // >>> pluck((List[Long](5l.toLong, 0l.toLong, 3l.toLong, 0l.toLong, 4l.toLong, 2l.toLong)))\n    // (List[Long](0l.toLong, 1l.toLong))\n    // Explanation: 0 is the smallest value, but  there are two zeros,\n    // so we will choose the first zero, which has the smallest index.\n    // Constraints:\n    // * 1 <= nodes.length <= 10000\n    // * 0 <= node.value\n    def pluck(arr : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(pluck((List[Long](4l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](2l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](1l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](2l.toLong, 1l.toLong))));\n    assert(pluck((List[Long]())).equals((List[Long]())));\n    assert(pluck((List[Long](5l.toLong, 0l.toLong, 3l.toLong, 0l.toLong, 4l.toLong, 2l.toLong))).equals((List[Long](0l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 0l.toLong, 5l.toLong, 3l.toLong))).equals((List[Long](0l.toLong, 3l.toLong))));\n    assert(pluck((List[Long](5l.toLong, 4l.toLong, 8l.toLong, 4l.toLong, 8l.toLong))).equals((List[Long](4l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](7l.toLong, 6l.toLong, 7l.toLong, 1l.toLong))).equals((List[Long](6l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](7l.toLong, 9l.toLong, 7l.toLong, 1l.toLong))).equals((List[Long]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_68_pluck", "test": "    }\n    def main(args: Array[String]) = {\n    assert(pluck((List[Long](4l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](2l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](1l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](2l.toLong, 1l.toLong))));\n    assert(pluck((List[Long]())).equals((List[Long]())));\n    assert(pluck((List[Long](5l.toLong, 0l.toLong, 3l.toLong, 0l.toLong, 4l.toLong, 2l.toLong))).equals((List[Long](0l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 0l.toLong, 5l.toLong, 3l.toLong))).equals((List[Long](0l.toLong, 3l.toLong))));\n    assert(pluck((List[Long](5l.toLong, 4l.toLong, 8l.toLong, 4l.toLong, 8l.toLong))).equals((List[Long](4l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](7l.toLong, 6l.toLong, 7l.toLong, 1l.toLong))).equals((List[Long](6l.toLong, 1l.toLong))));\n    assert(pluck((List[Long](7l.toLong, 9l.toLong, 7l.toLong, 1l.toLong))).equals((List[Long]())));\n    }\n\n}\n"}
{"name": "HumanEval_147_get_max_triples", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a positive integer n. You have to create an integer list a of length n.\n    // For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n    // Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n    // and a[i] + a[j] + a[k] is a multiple of 3.\n    // Example :\n    // >>> getMaxTriples((5l))\n    // (1l)\n    // Explanation: \n    // a = [1, 3, 7, 13, 21]\n    // The only valid triple is (1, 7, 13).\n    def getMaxTriples(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(getMaxTriples((5l)) == (1l));\n    assert(getMaxTriples((6l)) == (4l));\n    assert(getMaxTriples((10l)) == (36l));\n    assert(getMaxTriples((100l)) == (53361l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_147_get_max_triples", "test": "    }\n    def main(args: Array[String]) = {\n    assert(getMaxTriples((5l)) == (1l));\n    assert(getMaxTriples((6l)) == (4l));\n    assert(getMaxTriples((10l)) == (36l));\n    assert(getMaxTriples((100l)) == (53361l));\n    }\n\n}\n"}
{"name": "HumanEval_110_exchange", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // In this problem, you will implement a function that takes two lists of numbers,\n    // and determines whether it is possible to perform an exchange of elements\n    // between them to make lst1 a list of only even numbers.\n    // There is no limit on the number of exchanged elements between lst1 and lst2.\n    // If it is possible to exchange elements between the lst1 and lst2 to make\n    // all the elements of lst1 to be even, return \"YES\".\n    // Otherwise, return \"NO\".\n    // For example:\n    // >>> exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)))\n    // (\"YES\")\n    // >>> exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](1l.toLong, 5l.toLong, 3l.toLong, 4l.toLong)))\n    // (\"NO\")\n    // It is assumed that the input lists will be non-empty.\n    def exchange(lst1 : List[Long], lst2 : List[Long]) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((\"YES\")));\n    assert(exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](1l.toLong, 5l.toLong, 3l.toLong, 4l.toLong))).equals((\"NO\")));\n    assert(exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](2l.toLong, 1l.toLong, 4l.toLong, 3l.toLong))).equals((\"YES\")));\n    assert(exchange((List[Long](5l.toLong, 7l.toLong, 3l.toLong)), (List[Long](2l.toLong, 6l.toLong, 4l.toLong))).equals((\"YES\")));\n    assert(exchange((List[Long](5l.toLong, 7l.toLong, 3l.toLong)), (List[Long](2l.toLong, 6l.toLong, 3l.toLong))).equals((\"NO\")));\n    assert(exchange((List[Long](3l.toLong, 2l.toLong, 6l.toLong, 1l.toLong, 8l.toLong, 9l.toLong)), (List[Long](3l.toLong, 5l.toLong, 5l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))).equals((\"NO\")));\n    assert(exchange((List[Long](100l.toLong, 200l.toLong)), (List[Long](200l.toLong, 200l.toLong))).equals((\"YES\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_110_exchange", "test": "    }\n    def main(args: Array[String]) = {\n    assert(exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((\"YES\")));\n    assert(exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](1l.toLong, 5l.toLong, 3l.toLong, 4l.toLong))).equals((\"NO\")));\n    assert(exchange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)), (List[Long](2l.toLong, 1l.toLong, 4l.toLong, 3l.toLong))).equals((\"YES\")));\n    assert(exchange((List[Long](5l.toLong, 7l.toLong, 3l.toLong)), (List[Long](2l.toLong, 6l.toLong, 4l.toLong))).equals((\"YES\")));\n    assert(exchange((List[Long](5l.toLong, 7l.toLong, 3l.toLong)), (List[Long](2l.toLong, 6l.toLong, 3l.toLong))).equals((\"NO\")));\n    assert(exchange((List[Long](3l.toLong, 2l.toLong, 6l.toLong, 1l.toLong, 8l.toLong, 9l.toLong)), (List[Long](3l.toLong, 5l.toLong, 5l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))).equals((\"NO\")));\n    assert(exchange((List[Long](100l.toLong, 200l.toLong)), (List[Long](200l.toLong, 200l.toLong))).equals((\"YES\")));\n    }\n\n}\n"}
{"name": "HumanEval_47_median", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return median of elements in the list l.\n    // >>> median((List[Long](3l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong)))\n    // 3l\n    // >>> median((List[Long](-10l.toLong, 4l.toLong, 6l.toLong, 1000l.toLong, 10l.toLong, 20l.toLong)))\n    // (15.0f)\n    def median(l : List[Long]) : Float = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(median((List[Long](3l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))) == 3l);\n    assert(median((List[Long](-10l.toLong, 4l.toLong, 6l.toLong, 1000l.toLong, 10l.toLong, 20l.toLong))) == (8.0f));\n    assert(median((List[Long](5l.toLong))) == 5l);\n    assert(median((List[Long](6l.toLong, 5l.toLong))) == (5.5f));\n    assert(median((List[Long](8l.toLong, 1l.toLong, 3l.toLong, 9l.toLong, 9l.toLong, 2l.toLong, 7l.toLong))) == 7l);\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_47_median", "test": "    }\n    def main(args: Array[String]) = {\n    assert(median((List[Long](3l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))) == 3l);\n    assert(median((List[Long](-10l.toLong, 4l.toLong, 6l.toLong, 1000l.toLong, 10l.toLong, 20l.toLong))) == (8.0f));\n    assert(median((List[Long](5l.toLong))) == 5l);\n    assert(median((List[Long](6l.toLong, 5l.toLong))) == (5.5f));\n    assert(median((List[Long](8l.toLong, 1l.toLong, 3l.toLong, 9l.toLong, 9l.toLong, 2l.toLong, 7l.toLong))) == 7l);\n    }\n\n}\n"}
{"name": "HumanEval_82_prime_length", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that takes a string and returns true if the string\n    // length is a prime number or false otherwise\n    // Examples\n    // >>> primeLength((\"Hello\"))\n    // (true)\n    // >>> primeLength((\"abcdcba\"))\n    // (true)\n    // >>> primeLength((\"kittens\"))\n    // (true)\n    // >>> primeLength((\"orange\"))\n    // (false)\n    def primeLength(string : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(primeLength((\"Hello\")) == (true));\n    assert(primeLength((\"abcdcba\")) == (true));\n    assert(primeLength((\"kittens\")) == (true));\n    assert(primeLength((\"orange\")) == (false));\n    assert(primeLength((\"wow\")) == (true));\n    assert(primeLength((\"world\")) == (true));\n    assert(primeLength((\"MadaM\")) == (true));\n    assert(primeLength((\"Wow\")) == (true));\n    assert(primeLength((\"\")) == (false));\n    assert(primeLength((\"HI\")) == (true));\n    assert(primeLength((\"go\")) == (true));\n    assert(primeLength((\"gogo\")) == (false));\n    assert(primeLength((\"aaaaaaaaaaaaaaa\")) == (false));\n    assert(primeLength((\"Madam\")) == (true));\n    assert(primeLength((\"M\")) == (false));\n    assert(primeLength((\"0\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_82_prime_length", "test": "    }\n    def main(args: Array[String]) = {\n    assert(primeLength((\"Hello\")) == (true));\n    assert(primeLength((\"abcdcba\")) == (true));\n    assert(primeLength((\"kittens\")) == (true));\n    assert(primeLength((\"orange\")) == (false));\n    assert(primeLength((\"wow\")) == (true));\n    assert(primeLength((\"world\")) == (true));\n    assert(primeLength((\"MadaM\")) == (true));\n    assert(primeLength((\"Wow\")) == (true));\n    assert(primeLength((\"\")) == (false));\n    assert(primeLength((\"HI\")) == (true));\n    assert(primeLength((\"go\")) == (true));\n    assert(primeLength((\"gogo\")) == (false));\n    assert(primeLength((\"aaaaaaaaaaaaaaa\")) == (false));\n    assert(primeLength((\"Madam\")) == (true));\n    assert(primeLength((\"M\")) == (false));\n    assert(primeLength((\"0\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_73_smallest_change", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list arr of integers, find the minimum number of elements that\n    // need to be changed to make the list palindromic. A palindromic list is a list that\n    // is read the same backwards and forwards. In one change, you can change one element to any other element.\n    // For example:\n    // >>> smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 5l.toLong, 4l.toLong, 7l.toLong, 9l.toLong, 6l.toLong)))\n    // (4l)\n    // >>> smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 3l.toLong, 2l.toLong, 2l.toLong)))\n    // (1l)\n    // >>> smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 1l.toLong)))\n    // (0l)\n    def smallestChange(arr : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 5l.toLong, 4l.toLong, 7l.toLong, 9l.toLong, 6l.toLong))) == (4l));\n    assert(smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 3l.toLong, 2l.toLong, 2l.toLong))) == (1l));\n    assert(smallestChange((List[Long](1l.toLong, 4l.toLong, 2l.toLong))) == (1l));\n    assert(smallestChange((List[Long](1l.toLong, 4l.toLong, 4l.toLong, 2l.toLong))) == (1l));\n    assert(smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 1l.toLong))) == (0l));\n    assert(smallestChange((List[Long](3l.toLong, 1l.toLong, 1l.toLong, 3l.toLong))) == (0l));\n    assert(smallestChange((List[Long](1l.toLong))) == (0l));\n    assert(smallestChange((List[Long](0l.toLong, 1l.toLong))) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_73_smallest_change", "test": "    }\n    def main(args: Array[String]) = {\n    assert(smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 5l.toLong, 4l.toLong, 7l.toLong, 9l.toLong, 6l.toLong))) == (4l));\n    assert(smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 3l.toLong, 2l.toLong, 2l.toLong))) == (1l));\n    assert(smallestChange((List[Long](1l.toLong, 4l.toLong, 2l.toLong))) == (1l));\n    assert(smallestChange((List[Long](1l.toLong, 4l.toLong, 4l.toLong, 2l.toLong))) == (1l));\n    assert(smallestChange((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 1l.toLong))) == (0l));\n    assert(smallestChange((List[Long](3l.toLong, 1l.toLong, 1l.toLong, 3l.toLong))) == (0l));\n    assert(smallestChange((List[Long](1l.toLong))) == (0l));\n    assert(smallestChange((List[Long](0l.toLong, 1l.toLong))) == (1l));\n    }\n\n}\n"}
{"name": "HumanEval_133_sum_squares", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a list of numbers.\n    // You need to return the sum of squared numbers in the given list,\n    // round each element in the list to the upper int(Ceiling) first.\n    // Examples:\n    // >>> lst((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat)))\n    // (14l)\n    // >>> lst((List[Float](1.0f.toFloat, 4.0f.toFloat, 9.0f.toFloat)))\n    // (98l)\n    // >>> lst((List[Float](1.0f.toFloat, 3.0f.toFloat, 5.0f.toFloat, 7.0f.toFloat)))\n    // (84l)\n    // >>> lst((List[Float](1.4f.toFloat, 4.2f.toFloat, 0.0f.toFloat)))\n    // (29l)\n    // >>> lst((List[Float](-2.4f.toFloat, 1.0f.toFloat, 1.0f.toFloat)))\n    // (6l)\n    def sumSquares(lst : List[Float]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sumSquares((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat))) == (14l));\n    assert(sumSquares((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat))) == (14l));\n    assert(sumSquares((List[Float](1.0f.toFloat, 3.0f.toFloat, 5.0f.toFloat, 7.0f.toFloat))) == (84l));\n    assert(sumSquares((List[Float](1.4f.toFloat, 4.2f.toFloat, 0.0f.toFloat))) == (29l));\n    assert(sumSquares((List[Float](-2.4f.toFloat, 1.0f.toFloat, 1.0f.toFloat))) == (6l));\n    assert(sumSquares((List[Float](100.0f.toFloat, 1.0f.toFloat, 15.0f.toFloat, 2.0f.toFloat))) == (10230l));\n    assert(sumSquares((List[Float](10000.0f.toFloat, 10000.0f.toFloat))) == (200000000l));\n    assert(sumSquares((List[Float](-1.4f.toFloat, 4.6f.toFloat, 6.3f.toFloat))) == (75l));\n    assert(sumSquares((List[Float](-1.4f.toFloat, 17.9f.toFloat, 18.9f.toFloat, 19.9f.toFloat))) == (1086l));\n    assert(sumSquares((List[Float](0.0f.toFloat))) == (0l));\n    assert(sumSquares((List[Float](-1.0f.toFloat))) == (1l));\n    assert(sumSquares((List[Float](-1.0f.toFloat, 1.0f.toFloat, 0.0f.toFloat))) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_133_sum_squares", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sumSquares((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat))) == (14l));\n    assert(sumSquares((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat))) == (14l));\n    assert(sumSquares((List[Float](1.0f.toFloat, 3.0f.toFloat, 5.0f.toFloat, 7.0f.toFloat))) == (84l));\n    assert(sumSquares((List[Float](1.4f.toFloat, 4.2f.toFloat, 0.0f.toFloat))) == (29l));\n    assert(sumSquares((List[Float](-2.4f.toFloat, 1.0f.toFloat, 1.0f.toFloat))) == (6l));\n    assert(sumSquares((List[Float](100.0f.toFloat, 1.0f.toFloat, 15.0f.toFloat, 2.0f.toFloat))) == (10230l));\n    assert(sumSquares((List[Float](10000.0f.toFloat, 10000.0f.toFloat))) == (200000000l));\n    assert(sumSquares((List[Float](-1.4f.toFloat, 4.6f.toFloat, 6.3f.toFloat))) == (75l));\n    assert(sumSquares((List[Float](-1.4f.toFloat, 17.9f.toFloat, 18.9f.toFloat, 19.9f.toFloat))) == (1086l));\n    assert(sumSquares((List[Float](0.0f.toFloat))) == (0l));\n    assert(sumSquares((List[Float](-1.0f.toFloat))) == (1l));\n    assert(sumSquares((List[Float](-1.0f.toFloat, 1.0f.toFloat, 0.0f.toFloat))) == (2l));\n    }\n\n}\n"}
{"name": "HumanEval_141_file_name_check", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function which takes a string representing a file's name, and returns\n    // 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    // A file's name is considered to be valid if and only if all the following conditions \n    // are met:\n    // - There should not be more than three digits ('0'-'9') in the file's name.\n    // - The file's name contains exactly one dot '.'\n    // - The substring before the dot should not be empty, and it starts with a letter from \n    // the latin alphapet ('a'-'z' and 'A'-'Z').\n    // - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    // Examples:\n    // >>> fileNameCheck((\"example.txt\"))\n    // (\"Yes\")\n    // >>> fileNameCheck((\"1example.dll\"))\n    // (\"No\")\n    def fileNameCheck(file_name : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fileNameCheck((\"example.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"1example.dll\")).equals((\"No\")));\n    assert(fileNameCheck((\"s1sdf3.asd\")).equals((\"No\")));\n    assert(fileNameCheck((\"K.dll\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"MY16FILE3.exe\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"His12FILE94.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"_Y.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"?aREYA.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"/this_is_valid.dll\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_valid.wow\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_valid.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"this_is_valid.txtexe\")).equals((\"No\")));\n    assert(fileNameCheck((\"#this2_i4s_5valid.ten\")).equals((\"No\")));\n    assert(fileNameCheck((\"@this1_is6_valid.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_12valid.6exe4.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"all.exe.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"I563_No.exe\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"Is3youfault.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"no_one#knows.dll\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"1I563_Yes3.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"I563_Yes3.txtt\")).equals((\"No\")));\n    assert(fileNameCheck((\"final..txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"final132\")).equals((\"No\")));\n    assert(fileNameCheck((\"_f4indsartal132.\")).equals((\"No\")));\n    assert(fileNameCheck((\".txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"s.\")).equals((\"No\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_141_file_name_check", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fileNameCheck((\"example.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"1example.dll\")).equals((\"No\")));\n    assert(fileNameCheck((\"s1sdf3.asd\")).equals((\"No\")));\n    assert(fileNameCheck((\"K.dll\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"MY16FILE3.exe\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"His12FILE94.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"_Y.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"?aREYA.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"/this_is_valid.dll\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_valid.wow\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_valid.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"this_is_valid.txtexe\")).equals((\"No\")));\n    assert(fileNameCheck((\"#this2_i4s_5valid.ten\")).equals((\"No\")));\n    assert(fileNameCheck((\"@this1_is6_valid.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_12valid.6exe4.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"all.exe.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"I563_No.exe\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"Is3youfault.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"no_one#knows.dll\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"1I563_Yes3.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"I563_Yes3.txtt\")).equals((\"No\")));\n    assert(fileNameCheck((\"final..txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"final132\")).equals((\"No\")));\n    assert(fileNameCheck((\"_f4indsartal132.\")).equals((\"No\")));\n    assert(fileNameCheck((\".txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"s.\")).equals((\"No\")));\n    }\n\n}\n"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // triples_sum_to_zero takes a list of integers as an input.\n    // it returns true if there are three distinct elements in the list that\n    // sum to zero, and false otherwise.\n    // >>> triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, 0l.toLong)))\n    // (false)\n    // >>> triplesSumToZero((List[Long](1l.toLong, 3l.toLong, -2l.toLong, 1l.toLong)))\n    // (true)\n    // >>> triplesSumToZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 7l.toLong)))\n    // (false)\n    // >>> triplesSumToZero((List[Long](2l.toLong, 4l.toLong, -5l.toLong, 3l.toLong, 9l.toLong, 7l.toLong)))\n    // (true)\n    // >>> triplesSumToZero((List[Long](1l.toLong)))\n    // (false)\n    def triplesSumToZero(l : List[Long]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, 0l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, -1l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, -2l.toLong, 1l.toLong))) == (true));\n    assert(triplesSumToZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 7l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 2l.toLong, 5l.toLong, 7l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](2l.toLong, 4l.toLong, -5l.toLong, 3l.toLong, 9l.toLong, 7l.toLong))) == (true));\n    assert(triplesSumToZero((List[Long](1l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, -100l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](100l.toLong, 3l.toLong, 5l.toLong, -100l.toLong))) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "    }\n    def main(args: Array[String]) = {\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, 0l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, -1l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, -2l.toLong, 1l.toLong))) == (true));\n    assert(triplesSumToZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 7l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 2l.toLong, 5l.toLong, 7l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](2l.toLong, 4l.toLong, -5l.toLong, 3l.toLong, 9l.toLong, 7l.toLong))) == (true));\n    assert(triplesSumToZero((List[Long](1l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](1l.toLong, 3l.toLong, 5l.toLong, -100l.toLong))) == (false));\n    assert(triplesSumToZero((List[Long](100l.toLong, 3l.toLong, 5l.toLong, -100l.toLong))) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_127_intersection", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given two intervals,\n    // where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n    // The given intervals are closed which means that the interval (start, end)\n    // includes both start and end.\n    // For each given interval, it is assumed that its start is less or equal its end.\n    // Your task is to determine whether the length of intersection of these two \n    // intervals is a prime number.\n    // Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n    // which its length is 1, which not a prime number.\n    // If the length of the intersection is a prime number, return \"YES\",\n    // otherwise, return \"NO\".\n    // If the two intervals don't intersect, return \"NO\".\n    // [input/output] samples:\n    // >>> intersection(((1l, 2l)), ((2l, 3l)))\n    // (\"NO\")\n    // >>> intersection(((-1l, 1l)), ((0l, 4l)))\n    // (\"NO\")\n    // >>> intersection(((-3l, -1l)), ((-5l, 5l)))\n    // (\"YES\")\n    def intersection(interval1 : Tuple2[Long, Long], interval2 : Tuple2[Long, Long]) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(intersection(((1l, 2l)), ((2l, 3l))).equals((\"NO\")));\n    assert(intersection(((-1l, 1l)), ((0l, 4l))).equals((\"NO\")));\n    assert(intersection(((-3l, -1l)), ((-5l, 5l))).equals((\"YES\")));\n    assert(intersection(((-2l, 2l)), ((-4l, 0l))).equals((\"YES\")));\n    assert(intersection(((-11l, 2l)), ((-1l, -1l))).equals((\"NO\")));\n    assert(intersection(((1l, 2l)), ((3l, 5l))).equals((\"NO\")));\n    assert(intersection(((1l, 2l)), ((1l, 2l))).equals((\"NO\")));\n    assert(intersection(((-2l, -2l)), ((-3l, -2l))).equals((\"NO\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_127_intersection", "test": "    }\n    def main(args: Array[String]) = {\n    assert(intersection(((1l, 2l)), ((2l, 3l))).equals((\"NO\")));\n    assert(intersection(((-1l, 1l)), ((0l, 4l))).equals((\"NO\")));\n    assert(intersection(((-3l, -1l)), ((-5l, 5l))).equals((\"YES\")));\n    assert(intersection(((-2l, 2l)), ((-4l, 0l))).equals((\"YES\")));\n    assert(intersection(((-11l, 2l)), ((-1l, -1l))).equals((\"NO\")));\n    assert(intersection(((1l, 2l)), ((3l, 5l))).equals((\"NO\")));\n    assert(intersection(((1l, 2l)), ((1l, 2l))).equals((\"NO\")));\n    assert(intersection(((-2l, -2l)), ((-3l, -2l))).equals((\"NO\")));\n    }\n\n}\n"}
{"name": "HumanEval_1_separate_paren_groups", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n    // separate those group into separate strings and return the list of those.\n    // Separate groups are balanced (each open brace is properly closed) and not nested within each other\n    // Ignore any spaces in the input string.\n    // >>> separateParenGroups((\"( ) (( )) (( )( ))\"))\n    // (List[String](\"()\", \"(())\", \"(()())\"))\n    def separateParenGroups(paren_string : String) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(separateParenGroups((\"(()()) ((())) () ((())()())\")).equals((List[String](\"(()())\", \"((()))\", \"()\", \"((())()())\"))));\n    assert(separateParenGroups((\"() (()) ((())) (((())))\")).equals((List[String](\"()\", \"(())\", \"((()))\", \"(((())))\"))));\n    assert(separateParenGroups((\"(()(())((())))\")).equals((List[String](\"(()(())((())))\"))));\n    assert(separateParenGroups((\"( ) (( )) (( )( ))\")).equals((List[String](\"()\", \"(())\", \"(()())\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_1_separate_paren_groups", "test": "    }\n    def main(args: Array[String]) = {\n    assert(separateParenGroups((\"(()()) ((())) () ((())()())\")).equals((List[String](\"(()())\", \"((()))\", \"()\", \"((())()())\"))));\n    assert(separateParenGroups((\"() (()) ((())) (((())))\")).equals((List[String](\"()\", \"(())\", \"((()))\", \"(((())))\"))));\n    assert(separateParenGroups((\"(()(())((())))\")).equals((List[String](\"(()(())((())))\"))));\n    assert(separateParenGroups((\"( ) (( )) (( )( ))\")).equals((List[String](\"()\", \"(())\", \"(()())\"))));\n    }\n\n}\n"}
{"name": "HumanEval_152_compare", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // I think we all remember that feeling when the result of some long-awaited\n    // event is finally known. The feelings and thoughts you have at that moment are\n    // definitely worth noting down and comparing.\n    // Your task is to determine if a person correctly guessed the results of a number of matches.\n    // You are given two lists of scores and guesses of equal length, where each index shows a match. \n    // Return a list of the same length denoting how far off each guess was. If they have guessed correctly,\n    // the value is 0, and if not, the value is the absolute difference between the guess and the score.\n    // example:\n    // >>> compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong)), (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 2l.toLong, -2l.toLong)))\n    // (List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 3l.toLong, 3l.toLong))\n    // >>> compare((List[Long](0l.toLong, 5l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 4l.toLong)), (List[Long](4l.toLong, 1l.toLong, 1l.toLong, 0l.toLong, 0l.toLong, -2l.toLong)))\n    // (List[Long](4l.toLong, 4l.toLong, 1l.toLong, 0l.toLong, 0l.toLong, 6l.toLong))\n    def compare(game : List[Long], guess : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong)), (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 2l.toLong, -2l.toLong))).equals((List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 3l.toLong, 3l.toLong))));\n    assert(compare((List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong)), (List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong))).equals((List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong))));\n    assert(compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong)), (List[Long](-1l.toLong, -2l.toLong, -3l.toLong))).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong))));\n    assert(compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 5l.toLong)), (List[Long](-1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 0l.toLong, 0l.toLong, 1l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_152_compare", "test": "    }\n    def main(args: Array[String]) = {\n    assert(compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong)), (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 2l.toLong, -2l.toLong))).equals((List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 3l.toLong, 3l.toLong))));\n    assert(compare((List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong)), (List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong))).equals((List[Long](0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong))));\n    assert(compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong)), (List[Long](-1l.toLong, -2l.toLong, -3l.toLong))).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong))));\n    assert(compare((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 5l.toLong)), (List[Long](-1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 0l.toLong, 0l.toLong, 1l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_83_starts_one_ends", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer n, return the count of the numbers of n-digit\n    // positive integers that start or end with 1.\n    def startsOneEnds(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(startsOneEnds((1l)) == (1l));\n    assert(startsOneEnds((2l)) == (18l));\n    assert(startsOneEnds((3l)) == (180l));\n    assert(startsOneEnds((4l)) == (1800l));\n    assert(startsOneEnds((5l)) == (18000l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_83_starts_one_ends", "test": "    }\n    def main(args: Array[String]) = {\n    assert(startsOneEnds((1l)) == (1l));\n    assert(startsOneEnds((2l)) == (18l));\n    assert(startsOneEnds((3l)) == (180l));\n    assert(startsOneEnds((4l)) == (1800l));\n    assert(startsOneEnds((5l)) == (18000l));\n    }\n\n}\n"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function that returns true if the last character\n    // of a given string is an alphabetical character and is not\n    // a part of a word, and false otherwise.\n    // Note: \"word\" is a group of characters separated by space.\n    // Examples:\n    // >>> checkIfLastCharIsALetter((\"apple pie\"))\n    // (false)\n    // >>> checkIfLastCharIsALetter((\"apple pi e\"))\n    // (true)\n    // >>> checkIfLastCharIsALetter((\"apple pi e \"))\n    // (false)\n    // >>> checkIfLastCharIsALetter((\"\"))\n    // (false)\n    def checkIfLastCharIsALetter(txt : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(checkIfLastCharIsALetter((\"apple\")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pi e\")) == (true));\n    assert(checkIfLastCharIsALetter((\"eeeee\")) == (false));\n    assert(checkIfLastCharIsALetter((\"A\")) == (true));\n    assert(checkIfLastCharIsALetter((\"Pumpkin pie \")) == (false));\n    assert(checkIfLastCharIsALetter((\"Pumpkin pie 1\")) == (false));\n    assert(checkIfLastCharIsALetter((\"\")) == (false));\n    assert(checkIfLastCharIsALetter((\"eeeee e \")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pie\")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pi e \")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "    }\n    def main(args: Array[String]) = {\n    assert(checkIfLastCharIsALetter((\"apple\")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pi e\")) == (true));\n    assert(checkIfLastCharIsALetter((\"eeeee\")) == (false));\n    assert(checkIfLastCharIsALetter((\"A\")) == (true));\n    assert(checkIfLastCharIsALetter((\"Pumpkin pie \")) == (false));\n    assert(checkIfLastCharIsALetter((\"Pumpkin pie 1\")) == (false));\n    assert(checkIfLastCharIsALetter((\"\")) == (false));\n    assert(checkIfLastCharIsALetter((\"eeeee e \")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pie\")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pi e \")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_124_valid_date", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You have to write a function which validates a given date string and\n    // returns true if the date is valid otherwise false.\n    // The date is valid if all of the following rules are satisfied:\n    // 1. The date string is not empty.\n    // 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n    // 3. The months should not be less than 1 or higher than 12.\n    // 4. The date should be in the format: mm-dd-yyyy\n    // >>> validDate((\"03-11-2000\"))\n    // (true)\n    // >>> validDate((\"15-01-2012\"))\n    // (false)\n    // >>> validDate((\"04-0-2040\"))\n    // (false)\n    // >>> validDate((\"06-04-2020\"))\n    // (true)\n    // >>> validDate((\"06/04/2020\"))\n    // (false)\n    def validDate(date : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(validDate((\"03-11-2000\")) == (true));\n    assert(validDate((\"15-01-2012\")) == (false));\n    assert(validDate((\"04-0-2040\")) == (false));\n    assert(validDate((\"06-04-2020\")) == (true));\n    assert(validDate((\"01-01-2007\")) == (true));\n    assert(validDate((\"03-32-2011\")) == (false));\n    assert(validDate((\"\")) == (false));\n    assert(validDate((\"04-31-3000\")) == (false));\n    assert(validDate((\"06-06-2005\")) == (true));\n    assert(validDate((\"21-31-2000\")) == (false));\n    assert(validDate((\"04-12-2003\")) == (true));\n    assert(validDate((\"04122003\")) == (false));\n    assert(validDate((\"20030412\")) == (false));\n    assert(validDate((\"2003-04\")) == (false));\n    assert(validDate((\"2003-04-12\")) == (false));\n    assert(validDate((\"04-2003\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_124_valid_date", "test": "    }\n    def main(args: Array[String]) = {\n    assert(validDate((\"03-11-2000\")) == (true));\n    assert(validDate((\"15-01-2012\")) == (false));\n    assert(validDate((\"04-0-2040\")) == (false));\n    assert(validDate((\"06-04-2020\")) == (true));\n    assert(validDate((\"01-01-2007\")) == (true));\n    assert(validDate((\"03-32-2011\")) == (false));\n    assert(validDate((\"\")) == (false));\n    assert(validDate((\"04-31-3000\")) == (false));\n    assert(validDate((\"06-06-2005\")) == (true));\n    assert(validDate((\"21-31-2000\")) == (false));\n    assert(validDate((\"04-12-2003\")) == (true));\n    assert(validDate((\"04122003\")) == (false));\n    assert(validDate((\"20030412\")) == (false));\n    assert(validDate((\"2003-04\")) == (false));\n    assert(validDate((\"2003-04-12\")) == (false));\n    assert(validDate((\"04-2003\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_108_count_nums", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function count_nums which takes a list of integers and returns\n    // the number of elements which has a sum of digits > 0.\n    // If a number is negative, then its first signed digit will be negative:\n    // e.g. -123 has signed digits -1, 2, and 3.\n    // >>> countNums((List[Long]()))\n    // (0l)\n    // >>> countNums((List[Long](-1l.toLong, 11l.toLong, -11l.toLong)))\n    // (1l)\n    // >>> countNums((List[Long](1l.toLong, 1l.toLong, 2l.toLong)))\n    // (3l)\n    def countNums(arr : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(countNums((List[Long]())) == (0l));\n    assert(countNums((List[Long](-1l.toLong, -2l.toLong, 0l.toLong))) == (0l));\n    assert(countNums((List[Long](1l.toLong, 1l.toLong, 2l.toLong, -2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (6l));\n    assert(countNums((List[Long](1l.toLong, 6l.toLong, 9l.toLong, -6l.toLong, 0l.toLong, 1l.toLong, 5l.toLong))) == (5l));\n    assert(countNums((List[Long](1l.toLong, 100l.toLong, 98l.toLong, -7l.toLong, 1l.toLong, -1l.toLong))) == (4l));\n    assert(countNums((List[Long](12l.toLong, 23l.toLong, 34l.toLong, -45l.toLong, -56l.toLong, 0l.toLong))) == (5l));\n    assert(countNums((List[Long](0l.toLong, 1l.toLong))) == (1l));\n    assert(countNums((List[Long](1l.toLong))) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_108_count_nums", "test": "    }\n    def main(args: Array[String]) = {\n    assert(countNums((List[Long]())) == (0l));\n    assert(countNums((List[Long](-1l.toLong, -2l.toLong, 0l.toLong))) == (0l));\n    assert(countNums((List[Long](1l.toLong, 1l.toLong, 2l.toLong, -2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))) == (6l));\n    assert(countNums((List[Long](1l.toLong, 6l.toLong, 9l.toLong, -6l.toLong, 0l.toLong, 1l.toLong, 5l.toLong))) == (5l));\n    assert(countNums((List[Long](1l.toLong, 100l.toLong, 98l.toLong, -7l.toLong, 1l.toLong, -1l.toLong))) == (4l));\n    assert(countNums((List[Long](12l.toLong, 23l.toLong, 34l.toLong, -45l.toLong, -56l.toLong, 0l.toLong))) == (5l));\n    assert(countNums((List[Long](0l.toLong, 1l.toLong))) == (1l));\n    assert(countNums((List[Long](1l.toLong))) == (1l));\n    }\n\n}\n"}
{"name": "HumanEval_86_anti_shuffle", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that takes a string and returns an ordered version of it.\n    // Ordered version of string, is a string where all words (separated by space)\n    // are replaced by a new word where all the characters arranged in\n    // ascending order based on ascii value.\n    // Note: You should keep the order of words and blank spaces in the sentence.\n    // For example:\n    // >>> antiShuffle((\"Hi\"))\n    // (\"Hi\")\n    // >>> antiShuffle((\"hello\"))\n    // (\"ehllo\")\n    // >>> antiShuffle((\"Hello World!!!\"))\n    // (\"Hello !!!Wdlor\")\n    def antiShuffle(s : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(antiShuffle((\"Hi\")).equals((\"Hi\")));\n    assert(antiShuffle((\"hello\")).equals((\"ehllo\")));\n    assert(antiShuffle((\"number\")).equals((\"bemnru\")));\n    assert(antiShuffle((\"abcd\")).equals((\"abcd\")));\n    assert(antiShuffle((\"Hello World!!!\")).equals((\"Hello !!!Wdlor\")));\n    assert(antiShuffle((\"\")).equals((\"\")));\n    assert(antiShuffle((\"Hi. My name is Mister Robot. How are you?\")).equals((\".Hi My aemn is Meirst .Rboot How aer ?ouy\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_86_anti_shuffle", "test": "    }\n    def main(args: Array[String]) = {\n    assert(antiShuffle((\"Hi\")).equals((\"Hi\")));\n    assert(antiShuffle((\"hello\")).equals((\"ehllo\")));\n    assert(antiShuffle((\"number\")).equals((\"bemnru\")));\n    assert(antiShuffle((\"abcd\")).equals((\"abcd\")));\n    assert(antiShuffle((\"Hello World!!!\")).equals((\"Hello !!!Wdlor\")));\n    assert(antiShuffle((\"\")).equals((\"\")));\n    assert(antiShuffle((\"Hi. My name is Mister Robot. How are you?\")).equals((\".Hi My aemn is Meirst .Rboot How aer ?ouy\")));\n    }\n\n}\n"}
{"name": "HumanEval_48_is_palindrome", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Checks if given string is a palindrome\n    // >>> isPalindrome((\"\"))\n    // (true)\n    // >>> isPalindrome((\"aba\"))\n    // (true)\n    // >>> isPalindrome((\"aaaaa\"))\n    // (true)\n    // >>> isPalindrome((\"zbcd\"))\n    // (false)\n    def isPalindrome(text : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isPalindrome((\"\")) == (true));\n    assert(isPalindrome((\"aba\")) == (true));\n    assert(isPalindrome((\"aaaaa\")) == (true));\n    assert(isPalindrome((\"zbcd\")) == (false));\n    assert(isPalindrome((\"xywyx\")) == (true));\n    assert(isPalindrome((\"xywyz\")) == (false));\n    assert(isPalindrome((\"xywzx\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_48_is_palindrome", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isPalindrome((\"\")) == (true));\n    assert(isPalindrome((\"aba\")) == (true));\n    assert(isPalindrome((\"aaaaa\")) == (true));\n    assert(isPalindrome((\"zbcd\")) == (false));\n    assert(isPalindrome((\"xywyx\")) == (true));\n    assert(isPalindrome((\"xywyz\")) == (false));\n    assert(isPalindrome((\"xywzx\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_118_get_closest_vowel", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a word. Your task is to find the closest vowel that stands between \n    // two consonants from the right side of the word (case sensitive).\n    // Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n    // find any vowel met the above condition. \n    // You may assume that the given string contains English letter only.\n    // Example:\n    // >>> getClosestVowel((\"yogurt\"))\n    // (\"u\")\n    // >>> getClosestVowel((\"FULL\"))\n    // (\"U\")\n    // >>> getClosestVowel((\"quick\"))\n    // (\"\")\n    // >>> getClosestVowel((\"ab\"))\n    // (\"\")\n    def getClosestVowel(word : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(getClosestVowel((\"yogurt\")).equals((\"u\")));\n    assert(getClosestVowel((\"full\")).equals((\"u\")));\n    assert(getClosestVowel((\"easy\")).equals((\"\")));\n    assert(getClosestVowel((\"eAsy\")).equals((\"\")));\n    assert(getClosestVowel((\"ali\")).equals((\"\")));\n    assert(getClosestVowel((\"bad\")).equals((\"a\")));\n    assert(getClosestVowel((\"most\")).equals((\"o\")));\n    assert(getClosestVowel((\"ab\")).equals((\"\")));\n    assert(getClosestVowel((\"ba\")).equals((\"\")));\n    assert(getClosestVowel((\"quick\")).equals((\"\")));\n    assert(getClosestVowel((\"anime\")).equals((\"i\")));\n    assert(getClosestVowel((\"Asia\")).equals((\"\")));\n    assert(getClosestVowel((\"Above\")).equals((\"o\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_118_get_closest_vowel", "test": "    }\n    def main(args: Array[String]) = {\n    assert(getClosestVowel((\"yogurt\")).equals((\"u\")));\n    assert(getClosestVowel((\"full\")).equals((\"u\")));\n    assert(getClosestVowel((\"easy\")).equals((\"\")));\n    assert(getClosestVowel((\"eAsy\")).equals((\"\")));\n    assert(getClosestVowel((\"ali\")).equals((\"\")));\n    assert(getClosestVowel((\"bad\")).equals((\"a\")));\n    assert(getClosestVowel((\"most\")).equals((\"o\")));\n    assert(getClosestVowel((\"ab\")).equals((\"\")));\n    assert(getClosestVowel((\"ba\")).equals((\"\")));\n    assert(getClosestVowel((\"quick\")).equals((\"\")));\n    assert(getClosestVowel((\"anime\")).equals((\"i\")));\n    assert(getClosestVowel((\"Asia\")).equals((\"\")));\n    assert(getClosestVowel((\"Above\")).equals((\"o\")));\n    }\n\n}\n"}
{"name": "HumanEval_31_is_prime", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return true if a given number is prime, and false otherwise.\n    // >>> isPrime((6l))\n    // (false)\n    // >>> isPrime((101l))\n    // (true)\n    // >>> isPrime((11l))\n    // (true)\n    // >>> isPrime((13441l))\n    // (true)\n    // >>> isPrime((61l))\n    // (true)\n    // >>> isPrime((4l))\n    // (false)\n    // >>> isPrime((1l))\n    // (false)\n    def isPrime(n : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isPrime((6l)) == (false));\n    assert(isPrime((101l)) == (true));\n    assert(isPrime((11l)) == (true));\n    assert(isPrime((13441l)) == (true));\n    assert(isPrime((61l)) == (true));\n    assert(isPrime((4l)) == (false));\n    assert(isPrime((1l)) == (false));\n    assert(isPrime((5l)) == (true));\n    assert(isPrime((11l)) == (true));\n    assert(isPrime((17l)) == (true));\n    assert(isPrime((85l)) == (false));\n    assert(isPrime((77l)) == (false));\n    assert(isPrime((255379l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_31_is_prime", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isPrime((6l)) == (false));\n    assert(isPrime((101l)) == (true));\n    assert(isPrime((11l)) == (true));\n    assert(isPrime((13441l)) == (true));\n    assert(isPrime((61l)) == (true));\n    assert(isPrime((4l)) == (false));\n    assert(isPrime((1l)) == (false));\n    assert(isPrime((5l)) == (true));\n    assert(isPrime((11l)) == (true));\n    assert(isPrime((17l)) == (true));\n    assert(isPrime((85l)) == (false));\n    assert(isPrime((77l)) == (false));\n    assert(isPrime((255379l)) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_144_simplify", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Your task is to implement a function that will simplify the expression\n    // x * n. The function returns true if x * n evaluates to a whole number and false\n    // otherwise. Both x and n, are string representation of a fraction, and have the following format,\n    // <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n    // You can assume that x, and n are valid fractions, and do not have zero as denominator.\n    // >>> simplify((\"1/5\"), (\"5/1\"))\n    // (true)\n    // >>> simplify((\"1/6\"), (\"2/1\"))\n    // (false)\n    // >>> simplify((\"7/10\"), (\"10/2\"))\n    // (false)\n    def simplify(x : String, n : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(simplify((\"1/5\"), (\"5/1\")) == (true));\n    assert(simplify((\"1/6\"), (\"2/1\")) == (false));\n    assert(simplify((\"5/1\"), (\"3/1\")) == (true));\n    assert(simplify((\"7/10\"), (\"10/2\")) == (false));\n    assert(simplify((\"2/10\"), (\"50/10\")) == (true));\n    assert(simplify((\"7/2\"), (\"4/2\")) == (true));\n    assert(simplify((\"11/6\"), (\"6/1\")) == (true));\n    assert(simplify((\"2/3\"), (\"5/2\")) == (false));\n    assert(simplify((\"5/2\"), (\"3/5\")) == (false));\n    assert(simplify((\"2/4\"), (\"8/4\")) == (true));\n    assert(simplify((\"2/4\"), (\"4/2\")) == (true));\n    assert(simplify((\"1/5\"), (\"5/1\")) == (true));\n    assert(simplify((\"1/5\"), (\"1/5\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_144_simplify", "test": "    }\n    def main(args: Array[String]) = {\n    assert(simplify((\"1/5\"), (\"5/1\")) == (true));\n    assert(simplify((\"1/6\"), (\"2/1\")) == (false));\n    assert(simplify((\"5/1\"), (\"3/1\")) == (true));\n    assert(simplify((\"7/10\"), (\"10/2\")) == (false));\n    assert(simplify((\"2/10\"), (\"50/10\")) == (true));\n    assert(simplify((\"7/2\"), (\"4/2\")) == (true));\n    assert(simplify((\"11/6\"), (\"6/1\")) == (true));\n    assert(simplify((\"2/3\"), (\"5/2\")) == (false));\n    assert(simplify((\"5/2\"), (\"3/5\")) == (false));\n    assert(simplify((\"2/4\"), (\"8/4\")) == (true));\n    assert(simplify((\"2/4\"), (\"4/2\")) == (true));\n    assert(simplify((\"1/5\"), (\"5/1\")) == (true));\n    assert(simplify((\"1/5\"), (\"1/5\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_78_hex_key", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You have been tasked to write a function that receives \n    // a hexadecimal number as a string and counts the number of hexadecimal \n    // digits that are primes (prime number, or a prime, is a natural number \n    // greater than 1 that is not a product of two smaller natural numbers).\n    // Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n    // Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n    // So you have to determine a number of the following digits: 2, 3, 5, 7, \n    // B (=decimal 11), D (=decimal 13).\n    // Note: you may assume the input is always correct or empty string, \n    // and symbols A,B,C,D,E,F are always uppercase.\n    // Examples:\n    // >>> hexKey((\"AB\"))\n    // (1l)\n    // >>> hexKey((\"1077E\"))\n    // (2l)\n    // >>> hexKey((\"ABED1A33\"))\n    // (4l)\n    // >>> hexKey((\"123456789ABCDEF0\"))\n    // (6l)\n    // >>> hexKey((\"2020\"))\n    // (2l)\n    def hexKey(num : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(hexKey((\"AB\")) == (1l));\n    assert(hexKey((\"1077E\")) == (2l));\n    assert(hexKey((\"ABED1A33\")) == (4l));\n    assert(hexKey((\"2020\")) == (2l));\n    assert(hexKey((\"123456789ABCDEF0\")) == (6l));\n    assert(hexKey((\"112233445566778899AABBCCDDEEFF00\")) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_78_hex_key", "test": "    }\n    def main(args: Array[String]) = {\n    assert(hexKey((\"AB\")) == (1l));\n    assert(hexKey((\"1077E\")) == (2l));\n    assert(hexKey((\"ABED1A33\")) == (4l));\n    assert(hexKey((\"2020\")) == (2l));\n    assert(hexKey((\"123456789ABCDEF0\")) == (6l));\n    assert(hexKey((\"112233445566778899AABBCCDDEEFF00\")) == (12l));\n    }\n\n}\n"}
{"name": "HumanEval_143_words_in_sentence", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a string representing a sentence,\n    // the sentence contains some words separated by a space,\n    // and you have to return a string that contains the words from the original sentence,\n    // whose lengths are prime numbers,\n    // the order of the words in the new string should be the same as the original one.\n    // Example 1:\n    // >>> wordsInSentence((\"This is a test\"))\n    // (\"is\")\n    // Example 2:\n    // >>> wordsInSentence((\"lets go for swimming\"))\n    // (\"go for\")\n    // Constraints:\n    // * 1 <= len(sentence) <= 100\n    // * sentence contains only letters\n    def wordsInSentence(sentence : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(wordsInSentence((\"This is a test\")).equals((\"is\")));\n    assert(wordsInSentence((\"lets go for swimming\")).equals((\"go for\")));\n    assert(wordsInSentence((\"there is no place available here\")).equals((\"there is no place\")));\n    assert(wordsInSentence((\"Hi I am Hussein\")).equals((\"Hi am Hussein\")));\n    assert(wordsInSentence((\"go for it\")).equals((\"go for it\")));\n    assert(wordsInSentence((\"here\")).equals((\"\")));\n    assert(wordsInSentence((\"here is\")).equals((\"is\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_143_words_in_sentence", "test": "    }\n    def main(args: Array[String]) = {\n    assert(wordsInSentence((\"This is a test\")).equals((\"is\")));\n    assert(wordsInSentence((\"lets go for swimming\")).equals((\"go for\")));\n    assert(wordsInSentence((\"there is no place available here\")).equals((\"there is no place\")));\n    assert(wordsInSentence((\"Hi I am Hussein\")).equals((\"Hi am Hussein\")));\n    assert(wordsInSentence((\"go for it\")).equals((\"go for it\")));\n    assert(wordsInSentence((\"here\")).equals((\"\")));\n    assert(wordsInSentence((\"here is\")).equals((\"is\")));\n    }\n\n}\n"}
{"name": "HumanEval_111_histogram", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string representing a space separated lowercase letters, return a map\n    // of the letter with the most repetition and containing the corresponding count.\n    // If several letters have the same occurrence, return all of them.\n    // Example:\n    // >>> histogram((\"a b c\"))\n    // (Map[String,Long](\"a\" -> 1l, \"b\" -> 1l, \"c\" -> 1l))\n    // >>> histogram((\"a b b a\"))\n    // (Map[String,Long](\"a\" -> 2l, \"b\" -> 2l))\n    // >>> histogram((\"a b c a b\"))\n    // (Map[String,Long](\"a\" -> 2l, \"b\" -> 2l))\n    // >>> histogram((\"b b b b a\"))\n    // (Map[String,Long](\"b\" -> 4l))\n    // >>> histogram((\"\"))\n    // (Map[String,Long]())\n    def histogram(test : String) : Map[String,Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(histogram((\"a b b a\")).equals((Map[String,Long](\"a\" -> 2l, \"b\" -> 2l))));\n    assert(histogram((\"a b c a b\")).equals((Map[String,Long](\"a\" -> 2l, \"b\" -> 2l))));\n    assert(histogram((\"a b c d g\")).equals((Map[String,Long](\"a\" -> 1l, \"b\" -> 1l, \"c\" -> 1l, \"d\" -> 1l, \"g\" -> 1l))));\n    assert(histogram((\"r t g\")).equals((Map[String,Long](\"r\" -> 1l, \"t\" -> 1l, \"g\" -> 1l))));\n    assert(histogram((\"b b b b a\")).equals((Map[String,Long](\"b\" -> 4l))));\n    assert(histogram((\"r t g\")).equals((Map[String,Long](\"r\" -> 1l, \"t\" -> 1l, \"g\" -> 1l))));\n    assert(histogram((\"\")).equals((Map[String,Long]())));\n    assert(histogram((\"a\")).equals((Map[String,Long](\"a\" -> 1l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_111_histogram", "test": "    }\n    def main(args: Array[String]) = {\n    assert(histogram((\"a b b a\")).equals((Map[String,Long](\"a\" -> 2l, \"b\" -> 2l))));\n    assert(histogram((\"a b c a b\")).equals((Map[String,Long](\"a\" -> 2l, \"b\" -> 2l))));\n    assert(histogram((\"a b c d g\")).equals((Map[String,Long](\"a\" -> 1l, \"b\" -> 1l, \"c\" -> 1l, \"d\" -> 1l, \"g\" -> 1l))));\n    assert(histogram((\"r t g\")).equals((Map[String,Long](\"r\" -> 1l, \"t\" -> 1l, \"g\" -> 1l))));\n    assert(histogram((\"b b b b a\")).equals((Map[String,Long](\"b\" -> 4l))));\n    assert(histogram((\"r t g\")).equals((Map[String,Long](\"r\" -> 1l, \"t\" -> 1l, \"g\" -> 1l))));\n    assert(histogram((\"\")).equals((Map[String,Long]())));\n    assert(histogram((\"a\")).equals((Map[String,Long](\"a\" -> 1l))));\n    }\n\n}\n"}
{"name": "HumanEval_87_get_row", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a 2 dimensional data, as a nested lists,\n    // which is similar to matrix, however, unlike matrices,\n    // each row may contain a different number of columns.\n    // Given lst, and integer x, find integers x in the list,\n    // and return list of tuples, [(x1, y1), (x2, y2) ...] such that\n    // each tuple is a coordinate - (row, columns), starting with 0.\n    // Sort coordinates initially by rows in ascending order.\n    // Also, sort coordinates of the row by columns in descending order.\n    // Examples:\n    // >>> getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong))), (1l))\n    // (List[Tuple2[Long, Long]]((0l, 0l), (1l, 4l), (1l, 0l), (2l, 5l), (2l, 0l)))\n    // >>> getRow((List[List[Long]]()), (1l))\n    // (List[Tuple2[Long, Long]]())\n    // >>> getRow((List[List[Long]](List[Long](), List[Long](1l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong))), (3l))\n    // (List[Tuple2[Long, Long]]((2l, 2l)))\n    def getRow(lst : List[List[Long]], x : Long) : List[Tuple2[Long, Long]] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong))), (1l)).equals((List[Tuple2[Long, Long]]((0l, 0l), (1l, 4l), (1l, 0l), (2l, 5l), (2l, 0l)))));\n    assert(getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong))), (2l)).equals((List[Tuple2[Long, Long]]((0l, 1l), (1l, 1l), (2l, 1l), (3l, 1l), (4l, 1l), (5l, 1l)))));\n    assert(getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 1l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 1l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 1l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong))), (1l)).equals((List[Tuple2[Long, Long]]((0l, 0l), (1l, 0l), (2l, 1l), (2l, 0l), (3l, 2l), (3l, 0l), (4l, 3l), (4l, 0l), (5l, 4l), (5l, 0l), (6l, 5l), (6l, 0l)))));\n    assert(getRow((List[List[Long]]()), (1l)).equals((List[Tuple2[Long, Long]]())));\n    assert(getRow((List[List[Long]](List[Long](1l.toLong))), (2l)).equals((List[Tuple2[Long, Long]]())));\n    assert(getRow((List[List[Long]](List[Long](), List[Long](1l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong))), (3l)).equals((List[Tuple2[Long, Long]]((2l, 2l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_87_get_row", "test": "    }\n    def main(args: Array[String]) = {\n    assert(getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong))), (1l)).equals((List[Tuple2[Long, Long]]((0l, 0l), (1l, 4l), (1l, 0l), (2l, 5l), (2l, 0l)))));\n    assert(getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong))), (2l)).equals((List[Tuple2[Long, Long]]((0l, 1l), (1l, 1l), (2l, 1l), (3l, 1l), (4l, 1l), (5l, 1l)))));\n    assert(getRow((List[List[Long]](List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 1l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 1l.toLong, 4l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 1l.toLong, 5l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 6l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 1l.toLong))), (1l)).equals((List[Tuple2[Long, Long]]((0l, 0l), (1l, 0l), (2l, 1l), (2l, 0l), (3l, 2l), (3l, 0l), (4l, 3l), (4l, 0l), (5l, 4l), (5l, 0l), (6l, 5l), (6l, 0l)))));\n    assert(getRow((List[List[Long]]()), (1l)).equals((List[Tuple2[Long, Long]]())));\n    assert(getRow((List[List[Long]](List[Long](1l.toLong))), (2l)).equals((List[Tuple2[Long, Long]]())));\n    assert(getRow((List[List[Long]](List[Long](), List[Long](1l.toLong), List[Long](1l.toLong, 2l.toLong, 3l.toLong))), (3l)).equals((List[Tuple2[Long, Long]]((2l, 2l)))));\n    }\n\n}\n"}
{"name": "HumanEval_123_get_odd_collatz", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n    // The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n    // as follows: start with any positive integer n. Then each term is obtained from the \n    // previous term as follows: if the previous term is even, the next term is one half of \n    // the previous term. If the previous term is odd, the next term is 3 times the previous\n    // term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n    // Note: \n    // 1. Collatz(1) is [1].\n    // 2. returned list sorted in increasing order.\n    // For example:\n    // get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n    // >>> getOddCollatz((5l))\n    // (List[Long](1l.toLong, 5l.toLong))\n    def getOddCollatz(n : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(getOddCollatz((14l)).equals((List[Long](1l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong))));\n    assert(getOddCollatz((5l)).equals((List[Long](1l.toLong, 5l.toLong))));\n    assert(getOddCollatz((12l)).equals((List[Long](1l.toLong, 3l.toLong, 5l.toLong))));\n    assert(getOddCollatz((1l)).equals((List[Long](1l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_123_get_odd_collatz", "test": "    }\n    def main(args: Array[String]) = {\n    assert(getOddCollatz((14l)).equals((List[Long](1l.toLong, 5l.toLong, 7l.toLong, 11l.toLong, 13l.toLong, 17l.toLong))));\n    assert(getOddCollatz((5l)).equals((List[Long](1l.toLong, 5l.toLong))));\n    assert(getOddCollatz((12l)).equals((List[Long](1l.toLong, 3l.toLong, 5l.toLong))));\n    assert(getOddCollatz((1l)).equals((List[Long](1l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_135_can_arrange", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function which returns the largest index of an element which\n    // is not greater than or equal to the element immediately preceding it. If\n    // no such element exists then return -1. The given list will not contain\n    // duplicate values.\n    // Examples:\n    // >>> canArrange((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong)))\n    // (3l)\n    // >>> canArrange((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (-1l)\n    def canArrange(arr : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(canArrange((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong))) == (3l));\n    assert(canArrange((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))) == (-1l));\n    assert(canArrange((List[Long](1l.toLong, 4l.toLong, 2l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong, 10l.toLong))) == (2l));\n    assert(canArrange((List[Long](4l.toLong, 8l.toLong, 5l.toLong, 7l.toLong, 3l.toLong))) == (4l));\n    assert(canArrange((List[Long]())) == (-1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_135_can_arrange", "test": "    }\n    def main(args: Array[String]) = {\n    assert(canArrange((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong))) == (3l));\n    assert(canArrange((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 5l.toLong))) == (-1l));\n    assert(canArrange((List[Long](1l.toLong, 4l.toLong, 2l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 8l.toLong, 9l.toLong, 10l.toLong))) == (2l));\n    assert(canArrange((List[Long](4l.toLong, 8l.toLong, 5l.toLong, 7l.toLong, 3l.toLong))) == (4l));\n    assert(canArrange((List[Long]())) == (-1l));\n    }\n\n}\n"}
{"name": "HumanEval_19_sort_numbers", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Input is a space-delimited string of numberals from 'zero' to 'nine'.\n    // Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n    // Return the string with numbers sorted from smallest to largest\n    // >>> sortNumbers((\"three one five\"))\n    // (\"one three five\")\n    def sortNumbers(numbers : String) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sortNumbers((\"\")).equals((\"\")));\n    assert(sortNumbers((\"three\")).equals((\"three\")));\n    assert(sortNumbers((\"three five nine\")).equals((\"three five nine\")));\n    assert(sortNumbers((\"five zero four seven nine eight\")).equals((\"zero four five seven eight nine\")));\n    assert(sortNumbers((\"six five four three two one zero\")).equals((\"zero one two three four five six\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_19_sort_numbers", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sortNumbers((\"\")).equals((\"\")));\n    assert(sortNumbers((\"three\")).equals((\"three\")));\n    assert(sortNumbers((\"three five nine\")).equals((\"three five nine\")));\n    assert(sortNumbers((\"five zero four seven nine eight\")).equals((\"zero four five seven eight nine\")));\n    assert(sortNumbers((\"six five four three two one zero\")).equals((\"zero one two three four five six\")));\n    }\n\n}\n"}
{"name": "HumanEval_65_circular_shift", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Circular shift the digits of the integer x, shift the digits right by shift\n    // and return the result as a string.\n    // If shift > number of digits, return digits reversed.\n    // >>> circularShift((12l), (1l))\n    // (\"21\")\n    // >>> circularShift((12l), (2l))\n    // (\"12\")\n    def circularShift(x : Long, shift : Long) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(circularShift((100l), (2l)).equals((\"001\")));\n    assert(circularShift((12l), (2l)).equals((\"12\")));\n    assert(circularShift((97l), (8l)).equals((\"79\")));\n    assert(circularShift((12l), (1l)).equals((\"21\")));\n    assert(circularShift((11l), (101l)).equals((\"11\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_65_circular_shift", "test": "    }\n    def main(args: Array[String]) = {\n    assert(circularShift((100l), (2l)).equals((\"001\")));\n    assert(circularShift((12l), (2l)).equals((\"12\")));\n    assert(circularShift((97l), (8l)).equals((\"79\")));\n    assert(circularShift((12l), (1l)).equals((\"21\")));\n    assert(circularShift((11l), (101l)).equals((\"11\")));\n    }\n\n}\n"}
{"name": "HumanEval_142_sum_squares", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // \"\n    // This function will take a list of integers. For all entries in the list, the function shall square the integer entry if its index is a \n    // multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n    // change the entries in the list whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n    // Examples:\n    // >>> lst\n    // List[Long](1l.toLong, 2l.toLong, 3l.toLong)\n    // >>> lst\n    // List[Long]()\n    // >>> lst\n    // List[Long](-1l.toLong, -5l.toLong, 2l.toLong, -1l.toLong, -5l.toLong)\n    def sumSquares(lst : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sumSquares((List[Long](1l.toLong, 2l.toLong, 3l.toLong))) == (6l));\n    assert(sumSquares((List[Long](1l.toLong, 4l.toLong, 9l.toLong))) == (14l));\n    assert(sumSquares((List[Long]())) == (0l));\n    assert(sumSquares((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))) == (9l));\n    assert(sumSquares((List[Long](-1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong))) == (-3l));\n    assert(sumSquares((List[Long](0l.toLong))) == (0l));\n    assert(sumSquares((List[Long](-1l.toLong, -5l.toLong, 2l.toLong, -1l.toLong, -5l.toLong))) == (-126l));\n    assert(sumSquares((List[Long](-56l.toLong, -99l.toLong, 1l.toLong, 0l.toLong, -2l.toLong))) == (3030l));\n    assert(sumSquares((List[Long](-1l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, -1l.toLong))) == (0l));\n    assert(sumSquares((List[Long](-16l.toLong, -9l.toLong, -2l.toLong, 36l.toLong, 36l.toLong, 26l.toLong, -20l.toLong, 25l.toLong, -40l.toLong, 20l.toLong, -4l.toLong, 12l.toLong, -26l.toLong, 35l.toLong, 37l.toLong))) == (-14196l));\n    assert(sumSquares((List[Long](-1l.toLong, -3l.toLong, 17l.toLong, -1l.toLong, -15l.toLong, 13l.toLong, -1l.toLong, 14l.toLong, -14l.toLong, -12l.toLong, -5l.toLong, 14l.toLong, -14l.toLong, 6l.toLong, 13l.toLong, 11l.toLong, 16l.toLong, 16l.toLong, 4l.toLong, 10l.toLong))) == (-1448l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_142_sum_squares", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sumSquares((List[Long](1l.toLong, 2l.toLong, 3l.toLong))) == (6l));\n    assert(sumSquares((List[Long](1l.toLong, 4l.toLong, 9l.toLong))) == (14l));\n    assert(sumSquares((List[Long]())) == (0l));\n    assert(sumSquares((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))) == (9l));\n    assert(sumSquares((List[Long](-1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong, -1l.toLong))) == (-3l));\n    assert(sumSquares((List[Long](0l.toLong))) == (0l));\n    assert(sumSquares((List[Long](-1l.toLong, -5l.toLong, 2l.toLong, -1l.toLong, -5l.toLong))) == (-126l));\n    assert(sumSquares((List[Long](-56l.toLong, -99l.toLong, 1l.toLong, 0l.toLong, -2l.toLong))) == (3030l));\n    assert(sumSquares((List[Long](-1l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, 0l.toLong, -1l.toLong))) == (0l));\n    assert(sumSquares((List[Long](-16l.toLong, -9l.toLong, -2l.toLong, 36l.toLong, 36l.toLong, 26l.toLong, -20l.toLong, 25l.toLong, -40l.toLong, 20l.toLong, -4l.toLong, 12l.toLong, -26l.toLong, 35l.toLong, 37l.toLong))) == (-14196l));\n    assert(sumSquares((List[Long](-1l.toLong, -3l.toLong, 17l.toLong, -1l.toLong, -15l.toLong, 13l.toLong, -1l.toLong, 14l.toLong, -14l.toLong, -12l.toLong, -5l.toLong, 14l.toLong, -14l.toLong, 6l.toLong, 13l.toLong, 11l.toLong, 16l.toLong, 16l.toLong, 4l.toLong, 10l.toLong))) == (-1448l));\n    }\n\n}\n"}
{"name": "HumanEval_94_skjkasdkd", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a list of integers.\n    // You need to find the largest prime value and return the sum of its digits.\n    // Examples:\n    // >>> skjkasdkd((List[Long](0l.toLong, 3l.toLong, 2l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 4l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 2l.toLong, 181l.toLong, 32l.toLong, 4l.toLong, 32l.toLong, 3l.toLong, 2l.toLong, 32l.toLong, 324l.toLong, 4l.toLong, 3l.toLong)))\n    // (10l)\n    // >>> skjkasdkd((List[Long](1l.toLong, 0l.toLong, 1l.toLong, 8l.toLong, 2l.toLong, 4597l.toLong, 2l.toLong, 1l.toLong, 3l.toLong, 40l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 2l.toLong, 5l.toLong, 1l.toLong)))\n    // (25l)\n    // >>> skjkasdkd((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 32l.toLong, 5107l.toLong, 34l.toLong, 83278l.toLong, 109l.toLong, 163l.toLong, 23l.toLong, 2323l.toLong, 32l.toLong, 30l.toLong, 1l.toLong, 9l.toLong, 3l.toLong)))\n    // (13l)\n    // >>> skjkasdkd((List[Long](0l.toLong, 724l.toLong, 32l.toLong, 71l.toLong, 99l.toLong, 32l.toLong, 6l.toLong, 0l.toLong, 5l.toLong, 91l.toLong, 83l.toLong, 0l.toLong, 5l.toLong, 6l.toLong)))\n    // (11l)\n    // >>> skjkasdkd((List[Long](0l.toLong, 81l.toLong, 12l.toLong, 3l.toLong, 1l.toLong, 21l.toLong)))\n    // (3l)\n    // >>> skjkasdkd((List[Long](0l.toLong, 8l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 7l.toLong)))\n    // (7l)\n    def skjkasdkd(lst : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(skjkasdkd((List[Long](0l.toLong, 3l.toLong, 2l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 4l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 2l.toLong, 181l.toLong, 32l.toLong, 4l.toLong, 32l.toLong, 3l.toLong, 2l.toLong, 32l.toLong, 324l.toLong, 4l.toLong, 3l.toLong))) == (10l));\n    assert(skjkasdkd((List[Long](1l.toLong, 0l.toLong, 1l.toLong, 8l.toLong, 2l.toLong, 4597l.toLong, 2l.toLong, 1l.toLong, 3l.toLong, 40l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 2l.toLong, 5l.toLong, 1l.toLong))) == (25l));\n    assert(skjkasdkd((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 32l.toLong, 5107l.toLong, 34l.toLong, 83278l.toLong, 109l.toLong, 163l.toLong, 23l.toLong, 2323l.toLong, 32l.toLong, 30l.toLong, 1l.toLong, 9l.toLong, 3l.toLong))) == (13l));\n    assert(skjkasdkd((List[Long](0l.toLong, 724l.toLong, 32l.toLong, 71l.toLong, 99l.toLong, 32l.toLong, 6l.toLong, 0l.toLong, 5l.toLong, 91l.toLong, 83l.toLong, 0l.toLong, 5l.toLong, 6l.toLong))) == (11l));\n    assert(skjkasdkd((List[Long](0l.toLong, 81l.toLong, 12l.toLong, 3l.toLong, 1l.toLong, 21l.toLong))) == (3l));\n    assert(skjkasdkd((List[Long](0l.toLong, 8l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 7l.toLong))) == (7l));\n    assert(skjkasdkd((List[Long](8191l.toLong))) == (19l));\n    assert(skjkasdkd((List[Long](8191l.toLong, 123456l.toLong, 127l.toLong, 7l.toLong))) == (19l));\n    assert(skjkasdkd((List[Long](127l.toLong, 97l.toLong, 8192l.toLong))) == (10l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_94_skjkasdkd", "test": "    }\n    def main(args: Array[String]) = {\n    assert(skjkasdkd((List[Long](0l.toLong, 3l.toLong, 2l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 4l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 2l.toLong, 181l.toLong, 32l.toLong, 4l.toLong, 32l.toLong, 3l.toLong, 2l.toLong, 32l.toLong, 324l.toLong, 4l.toLong, 3l.toLong))) == (10l));\n    assert(skjkasdkd((List[Long](1l.toLong, 0l.toLong, 1l.toLong, 8l.toLong, 2l.toLong, 4597l.toLong, 2l.toLong, 1l.toLong, 3l.toLong, 40l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 2l.toLong, 5l.toLong, 1l.toLong))) == (25l));\n    assert(skjkasdkd((List[Long](1l.toLong, 3l.toLong, 1l.toLong, 32l.toLong, 5107l.toLong, 34l.toLong, 83278l.toLong, 109l.toLong, 163l.toLong, 23l.toLong, 2323l.toLong, 32l.toLong, 30l.toLong, 1l.toLong, 9l.toLong, 3l.toLong))) == (13l));\n    assert(skjkasdkd((List[Long](0l.toLong, 724l.toLong, 32l.toLong, 71l.toLong, 99l.toLong, 32l.toLong, 6l.toLong, 0l.toLong, 5l.toLong, 91l.toLong, 83l.toLong, 0l.toLong, 5l.toLong, 6l.toLong))) == (11l));\n    assert(skjkasdkd((List[Long](0l.toLong, 81l.toLong, 12l.toLong, 3l.toLong, 1l.toLong, 21l.toLong))) == (3l));\n    assert(skjkasdkd((List[Long](0l.toLong, 8l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 7l.toLong))) == (7l));\n    assert(skjkasdkd((List[Long](8191l.toLong))) == (19l));\n    assert(skjkasdkd((List[Long](8191l.toLong, 123456l.toLong, 127l.toLong, 7l.toLong))) == (19l));\n    assert(skjkasdkd((List[Long](127l.toLong, 97l.toLong, 8192l.toLong))) == (10l));\n    }\n\n}\n"}
{"name": "HumanEval_8_sum_product", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // For a given list of integers, return a tuple consisting of a sum and a product of all the integers in a list.\n    // Empty sum should be equal to 0 and empty product should be equal to 1.\n    // >>> sumProduct((List[Long]()))\n    // ((0l, 1l))\n    // >>> sumProduct((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)))\n    // ((10l, 24l))\n    def sumProduct(numbers : List[Long]) : Tuple2[Long, Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sumProduct((List[Long]())).equals(((0l, 1l))));\n    assert(sumProduct((List[Long](1l.toLong, 1l.toLong, 1l.toLong))).equals(((3l, 1l))));\n    assert(sumProduct((List[Long](100l.toLong, 0l.toLong))).equals(((100l, 0l))));\n    assert(sumProduct((List[Long](3l.toLong, 5l.toLong, 7l.toLong))).equals(((15l, 105l))));\n    assert(sumProduct((List[Long](10l.toLong))).equals(((10l, 10l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_8_sum_product", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sumProduct((List[Long]())).equals(((0l, 1l))));\n    assert(sumProduct((List[Long](1l.toLong, 1l.toLong, 1l.toLong))).equals(((3l, 1l))));\n    assert(sumProduct((List[Long](100l.toLong, 0l.toLong))).equals(((100l, 0l))));\n    assert(sumProduct((List[Long](3l.toLong, 5l.toLong, 7l.toLong))).equals(((15l, 105l))));\n    assert(sumProduct((List[Long](10l.toLong))).equals(((10l, 10l))));\n    }\n\n}\n"}
{"name": "HumanEval_102_choose_num", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // This function takes two positive numbers x and y and returns the\n    // biggest even integer number that is in the range [x, y] inclusive. If \n    // there's no such number, then the function should return -1.\n    // For example:\n    // >>> chooseNum((12l), (15l))\n    // (14l)\n    // >>> chooseNum((13l), (12l))\n    // (-1l)\n    def chooseNum(x : Long, y : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(chooseNum((12l), (15l)) == (14l));\n    assert(chooseNum((13l), (12l)) == (-1l));\n    assert(chooseNum((33l), (12354l)) == (12354l));\n    assert(chooseNum((5234l), (5233l)) == (-1l));\n    assert(chooseNum((6l), (29l)) == (28l));\n    assert(chooseNum((27l), (10l)) == (-1l));\n    assert(chooseNum((7l), (7l)) == (-1l));\n    assert(chooseNum((546l), (546l)) == (546l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_102_choose_num", "test": "    }\n    def main(args: Array[String]) = {\n    assert(chooseNum((12l), (15l)) == (14l));\n    assert(chooseNum((13l), (12l)) == (-1l));\n    assert(chooseNum((33l), (12354l)) == (12354l));\n    assert(chooseNum((5234l), (5233l)) == (-1l));\n    assert(chooseNum((6l), (29l)) == (28l));\n    assert(chooseNum((27l), (10l)) == (-1l));\n    assert(chooseNum((7l), (7l)) == (-1l));\n    assert(chooseNum((546l), (546l)) == (546l));\n    }\n\n}\n"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function that returns a tuple (a, b), where 'a' is\n    // the largest of negative integers, and 'b' is the smallest\n    // of positive integers in a list.\n    // If there is no negative or positive integers, return them as None.\n    // Examples:\n    // >>> largestSmallestIntegers((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong)))\n    // (Some(None), Some(1l))\n    // >>> largestSmallestIntegers((List[Long]()))\n    // (Some(None), Some(None))\n    // >>> largestSmallestIntegers((List[Long](0l.toLong)))\n    // (Some(None), Some(None))\n    def largestSmallestIntegers(lst : List[Long]) : Tuple2[Option[Long], Option[Long]] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(largestSmallestIntegers((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))).equals((Some(None), Some(1l))));\n    assert(largestSmallestIntegers((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 0l.toLong))).equals((Some(None), Some(1l))));\n    assert(largestSmallestIntegers((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, -2l.toLong))).equals((-2l, 1l)));\n    assert(largestSmallestIntegers((List[Long](4l.toLong, 5l.toLong, 3l.toLong, 6l.toLong, 2l.toLong, 7l.toLong, -7l.toLong))).equals((-7l, 2l)));\n    assert(largestSmallestIntegers((List[Long](7l.toLong, 3l.toLong, 8l.toLong, 4l.toLong, 9l.toLong, 2l.toLong, 5l.toLong, -9l.toLong))).equals((-9l, 2l)));\n    assert(largestSmallestIntegers((List[Long]())).equals((Some(None), Some(None))));\n    assert(largestSmallestIntegers((List[Long](0l.toLong))).equals((Some(None), Some(None))));\n    assert(largestSmallestIntegers((List[Long](-1l.toLong, -3l.toLong, -5l.toLong, -6l.toLong))).equals((Some(-1l), Some(None))));\n    assert(largestSmallestIntegers((List[Long](-1l.toLong, -3l.toLong, -5l.toLong, -6l.toLong, 0l.toLong))).equals((Some(-1l), Some(None))));\n    assert(largestSmallestIntegers((List[Long](-6l.toLong, -4l.toLong, -4l.toLong, -3l.toLong, 1l.toLong))).equals((-3l, 1l)));\n    assert(largestSmallestIntegers((List[Long](-6l.toLong, -4l.toLong, -4l.toLong, -3l.toLong, -100l.toLong, 1l.toLong))).equals((-3l, 1l)));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "    }\n    def main(args: Array[String]) = {\n    assert(largestSmallestIntegers((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong))).equals((Some(None), Some(1l))));\n    assert(largestSmallestIntegers((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 0l.toLong))).equals((Some(None), Some(1l))));\n    assert(largestSmallestIntegers((List[Long](1l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong, -2l.toLong))).equals((-2l, 1l)));\n    assert(largestSmallestIntegers((List[Long](4l.toLong, 5l.toLong, 3l.toLong, 6l.toLong, 2l.toLong, 7l.toLong, -7l.toLong))).equals((-7l, 2l)));\n    assert(largestSmallestIntegers((List[Long](7l.toLong, 3l.toLong, 8l.toLong, 4l.toLong, 9l.toLong, 2l.toLong, 5l.toLong, -9l.toLong))).equals((-9l, 2l)));\n    assert(largestSmallestIntegers((List[Long]())).equals((Some(None), Some(None))));\n    assert(largestSmallestIntegers((List[Long](0l.toLong))).equals((Some(None), Some(None))));\n    assert(largestSmallestIntegers((List[Long](-1l.toLong, -3l.toLong, -5l.toLong, -6l.toLong))).equals((Some(-1l), Some(None))));\n    assert(largestSmallestIntegers((List[Long](-1l.toLong, -3l.toLong, -5l.toLong, -6l.toLong, 0l.toLong))).equals((Some(-1l), Some(None))));\n    assert(largestSmallestIntegers((List[Long](-6l.toLong, -4l.toLong, -4l.toLong, -3l.toLong, 1l.toLong))).equals((-3l, 1l)));\n    assert(largestSmallestIntegers((List[Long](-6l.toLong, -4l.toLong, -4l.toLong, -3l.toLong, -100l.toLong, 1l.toLong))).equals((-3l, 1l)));\n    }\n\n}\n"}
{"name": "HumanEval_16_count_distinct_characters", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string, find out how many distinct characters (regardless of case) does it consist of\n    // >>> countDistinctCharacters((\"xyzXYZ\"))\n    // (3l)\n    // >>> countDistinctCharacters((\"Jerry\"))\n    // (4l)\n    def countDistinctCharacters(string : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(countDistinctCharacters((\"\")) == (0l));\n    assert(countDistinctCharacters((\"abcde\")) == (5l));\n    assert(countDistinctCharacters((\"abcdecadeCADE\")) == (5l));\n    assert(countDistinctCharacters((\"aaaaAAAAaaaa\")) == (1l));\n    assert(countDistinctCharacters((\"Jerry jERRY JeRRRY\")) == (5l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_16_count_distinct_characters", "test": "    }\n    def main(args: Array[String]) = {\n    assert(countDistinctCharacters((\"\")) == (0l));\n    assert(countDistinctCharacters((\"abcde\")) == (5l));\n    assert(countDistinctCharacters((\"abcdecadeCADE\")) == (5l));\n    assert(countDistinctCharacters((\"aaaaAAAAaaaa\")) == (1l));\n    assert(countDistinctCharacters((\"Jerry jERRY JeRRRY\")) == (5l));\n    }\n\n}\n"}
{"name": "HumanEval_100_make_a_pile", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer n, you have to make a pile of n levels of stones.\n    // The first level has n stones.\n    // The number of stones in the next level is:\n    // - the next odd number if n is odd.\n    // - the next even number if n is even.\n    // Return the number of stones in each level in a list, where element at index\n    // i represents the number of stones in the level (i+1).\n    // Examples:\n    // >>> makeAPile((3l))\n    // (List[Long](3l.toLong, 5l.toLong, 7l.toLong))\n    def makeAPile(n : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(makeAPile((3l)).equals((List[Long](3l.toLong, 5l.toLong, 7l.toLong))));\n    assert(makeAPile((4l)).equals((List[Long](4l.toLong, 6l.toLong, 8l.toLong, 10l.toLong))));\n    assert(makeAPile((5l)).equals((List[Long](5l.toLong, 7l.toLong, 9l.toLong, 11l.toLong, 13l.toLong))));\n    assert(makeAPile((6l)).equals((List[Long](6l.toLong, 8l.toLong, 10l.toLong, 12l.toLong, 14l.toLong, 16l.toLong))));\n    assert(makeAPile((8l)).equals((List[Long](8l.toLong, 10l.toLong, 12l.toLong, 14l.toLong, 16l.toLong, 18l.toLong, 20l.toLong, 22l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_100_make_a_pile", "test": "    }\n    def main(args: Array[String]) = {\n    assert(makeAPile((3l)).equals((List[Long](3l.toLong, 5l.toLong, 7l.toLong))));\n    assert(makeAPile((4l)).equals((List[Long](4l.toLong, 6l.toLong, 8l.toLong, 10l.toLong))));\n    assert(makeAPile((5l)).equals((List[Long](5l.toLong, 7l.toLong, 9l.toLong, 11l.toLong, 13l.toLong))));\n    assert(makeAPile((6l)).equals((List[Long](6l.toLong, 8l.toLong, 10l.toLong, 12l.toLong, 14l.toLong, 16l.toLong))));\n    assert(makeAPile((8l)).equals((List[Long](8l.toLong, 10l.toLong, 12l.toLong, 14l.toLong, 16l.toLong, 18l.toLong, 20l.toLong, 22l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_128_prod_signs", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a list arr of integers and you need to return\n    // sum of magnitudes of integers multiplied by product of all signs\n    // of each number in the list, represented by 1, -1 or 0.\n    // Note: return None for empty arr.\n    // Example:\n    // >>> prodSigns((List[Long](1l.toLong, 2l.toLong, 2l.toLong, -4l.toLong)))\n    // 9l\n    // >>> prodSigns((List[Long](0l.toLong, 1l.toLong)))\n    // 0l\n    // >>> prodSigns((List[Long]()))\n    // None\n    def prodSigns(arr : List[Long]) : Option[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(prodSigns((List[Long](1l.toLong, 2l.toLong, 2l.toLong, -4l.toLong))).equals(-9l));\n    assert(prodSigns((List[Long](0l.toLong, 1l.toLong))).equals(0l));\n    assert(prodSigns((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 2l.toLong, 3l.toLong, -1l.toLong, 1l.toLong))).equals(-10l));\n    assert(prodSigns((List[Long]())).equals(None));\n    assert(prodSigns((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 2l.toLong, -1l.toLong, -1l.toLong, 9l.toLong))).equals(20l));\n    assert(prodSigns((List[Long](-1l.toLong, 1l.toLong, -1l.toLong, 1l.toLong))).equals(4l));\n    assert(prodSigns((List[Long](-1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))).equals(-4l));\n    assert(prodSigns((List[Long](-1l.toLong, 1l.toLong, 1l.toLong, 0l.toLong))).equals(0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_128_prod_signs", "test": "    }\n    def main(args: Array[String]) = {\n    assert(prodSigns((List[Long](1l.toLong, 2l.toLong, 2l.toLong, -4l.toLong))).equals(-9l));\n    assert(prodSigns((List[Long](0l.toLong, 1l.toLong))).equals(0l));\n    assert(prodSigns((List[Long](1l.toLong, 1l.toLong, 1l.toLong, 2l.toLong, 3l.toLong, -1l.toLong, 1l.toLong))).equals(-10l));\n    assert(prodSigns((List[Long]())).equals(None));\n    assert(prodSigns((List[Long](2l.toLong, 4l.toLong, 1l.toLong, 2l.toLong, -1l.toLong, -1l.toLong, 9l.toLong))).equals(20l));\n    assert(prodSigns((List[Long](-1l.toLong, 1l.toLong, -1l.toLong, 1l.toLong))).equals(4l));\n    assert(prodSigns((List[Long](-1l.toLong, 1l.toLong, 1l.toLong, 1l.toLong))).equals(-4l));\n    assert(prodSigns((List[Long](-1l.toLong, 1l.toLong, 1l.toLong, 0l.toLong))).equals(0l));\n    }\n\n}\n"}
{"name": "HumanEval_114_minSubArraySum", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a list of integers nums, find the minimum sum of any non-empty sub-list\n    // of nums.\n    // Example\n    // >>> minSubArraySum((List[Long](2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 2l.toLong, 4l.toLong)))\n    // (1l)\n    // >>> minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong)))\n    // (-6l)\n    def minSubArraySum(nums : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(minSubArraySum((List[Long](2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 2l.toLong, 4l.toLong))) == (1l));\n    assert(minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong))) == (-6l));\n    assert(minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong, 2l.toLong, -10l.toLong))) == (-14l));\n    assert(minSubArraySum((List[Long](-9999999999999999l.toLong))) == (-9999999999999999l));\n    assert(minSubArraySum((List[Long](0l.toLong, 10l.toLong, 20l.toLong, 1000000l.toLong))) == (0l));\n    assert(minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong, 10l.toLong, -5l.toLong))) == (-6l));\n    assert(minSubArraySum((List[Long](100l.toLong, -1l.toLong, -2l.toLong, -3l.toLong, 10l.toLong, -5l.toLong))) == (-6l));\n    assert(minSubArraySum((List[Long](10l.toLong, 11l.toLong, 13l.toLong, 8l.toLong, 3l.toLong, 4l.toLong))) == (3l));\n    assert(minSubArraySum((List[Long](100l.toLong, -33l.toLong, 32l.toLong, -1l.toLong, 0l.toLong, -2l.toLong))) == (-33l));\n    assert(minSubArraySum((List[Long](-10l.toLong))) == (-10l));\n    assert(minSubArraySum((List[Long](7l.toLong))) == (7l));\n    assert(minSubArraySum((List[Long](1l.toLong, -1l.toLong))) == (-1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_114_minSubArraySum", "test": "    }\n    def main(args: Array[String]) = {\n    assert(minSubArraySum((List[Long](2l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 2l.toLong, 4l.toLong))) == (1l));\n    assert(minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong))) == (-6l));\n    assert(minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong, 2l.toLong, -10l.toLong))) == (-14l));\n    assert(minSubArraySum((List[Long](-9999999999999999l.toLong))) == (-9999999999999999l));\n    assert(minSubArraySum((List[Long](0l.toLong, 10l.toLong, 20l.toLong, 1000000l.toLong))) == (0l));\n    assert(minSubArraySum((List[Long](-1l.toLong, -2l.toLong, -3l.toLong, 10l.toLong, -5l.toLong))) == (-6l));\n    assert(minSubArraySum((List[Long](100l.toLong, -1l.toLong, -2l.toLong, -3l.toLong, 10l.toLong, -5l.toLong))) == (-6l));\n    assert(minSubArraySum((List[Long](10l.toLong, 11l.toLong, 13l.toLong, 8l.toLong, 3l.toLong, 4l.toLong))) == (3l));\n    assert(minSubArraySum((List[Long](100l.toLong, -33l.toLong, 32l.toLong, -1l.toLong, 0l.toLong, -2l.toLong))) == (-33l));\n    assert(minSubArraySum((List[Long](-10l.toLong))) == (-10l));\n    assert(minSubArraySum((List[Long](7l.toLong))) == (7l));\n    assert(minSubArraySum((List[Long](1l.toLong, -1l.toLong))) == (-1l));\n    }\n\n}\n"}
{"name": "HumanEval_15_string_sequence", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n    // >>> stringSequence((0l))\n    // (\"0\")\n    // >>> stringSequence((5l))\n    // (\"0 1 2 3 4 5\")\n    def stringSequence(n : Long) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(stringSequence((0l)).equals((\"0\")));\n    assert(stringSequence((3l)).equals((\"0 1 2 3\")));\n    assert(stringSequence((10l)).equals((\"0 1 2 3 4 5 6 7 8 9 10\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_15_string_sequence", "test": "    }\n    def main(args: Array[String]) = {\n    assert(stringSequence((0l)).equals((\"0\")));\n    assert(stringSequence((3l)).equals((\"0 1 2 3\")));\n    assert(stringSequence((10l)).equals((\"0 1 2 3 4 5 6 7 8 9 10\")));\n    }\n\n}\n"}
{"name": "HumanEval_154_cycpattern_check", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n    // >>> cycpatternCheck((\"abcd\"), (\"abd\"))\n    // (false)\n    // >>> cycpatternCheck((\"hello\"), (\"ell\"))\n    // (true)\n    // >>> cycpatternCheck((\"whassup\"), (\"psus\"))\n    // (false)\n    // >>> cycpatternCheck((\"abab\"), (\"baa\"))\n    // (true)\n    // >>> cycpatternCheck((\"efef\"), (\"eeff\"))\n    // (false)\n    // >>> cycpatternCheck((\"himenss\"), (\"simen\"))\n    // (true)\n    def cycpatternCheck(a : String, b : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(cycpatternCheck((\"xyzw\"), (\"xyw\")) == (false));\n    assert(cycpatternCheck((\"yello\"), (\"ell\")) == (true));\n    assert(cycpatternCheck((\"whattup\"), (\"ptut\")) == (false));\n    assert(cycpatternCheck((\"efef\"), (\"fee\")) == (true));\n    assert(cycpatternCheck((\"abab\"), (\"aabb\")) == (false));\n    assert(cycpatternCheck((\"winemtt\"), (\"tinem\")) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_154_cycpattern_check", "test": "    }\n    def main(args: Array[String]) = {\n    assert(cycpatternCheck((\"xyzw\"), (\"xyw\")) == (false));\n    assert(cycpatternCheck((\"yello\"), (\"ell\")) == (true));\n    assert(cycpatternCheck((\"whattup\"), (\"ptut\")) == (false));\n    assert(cycpatternCheck((\"efef\"), (\"fee\")) == (true));\n    assert(cycpatternCheck((\"abab\"), (\"aabb\")) == (false));\n    assert(cycpatternCheck((\"winemtt\"), (\"tinem\")) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_57_monotonic", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return true is list elements are monotonically increasing or decreasing.\n    // >>> monotonic((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 20l.toLong)))\n    // (true)\n    // >>> monotonic((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)))\n    // (false)\n    // >>> monotonic((List[Long](4l.toLong, 1l.toLong, 0l.toLong, -10l.toLong)))\n    // (true)\n    def monotonic(l : List[Long]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 10l.toLong))) == (true));\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 20l.toLong))) == (true));\n    assert(monotonic((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong))) == (false));\n    assert(monotonic((List[Long](4l.toLong, 1l.toLong, 0l.toLong, -10l.toLong))) == (true));\n    assert(monotonic((List[Long](4l.toLong, 1l.toLong, 1l.toLong, 0l.toLong))) == (true));\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 5l.toLong, 60l.toLong))) == (false));\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 60l.toLong))) == (true));\n    assert(monotonic((List[Long](9l.toLong, 9l.toLong, 9l.toLong, 9l.toLong))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_57_monotonic", "test": "    }\n    def main(args: Array[String]) = {\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 10l.toLong))) == (true));\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 20l.toLong))) == (true));\n    assert(monotonic((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong))) == (false));\n    assert(monotonic((List[Long](4l.toLong, 1l.toLong, 0l.toLong, -10l.toLong))) == (true));\n    assert(monotonic((List[Long](4l.toLong, 1l.toLong, 1l.toLong, 0l.toLong))) == (true));\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 5l.toLong, 60l.toLong))) == (false));\n    assert(monotonic((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 60l.toLong))) == (true));\n    assert(monotonic((List[Long](9l.toLong, 9l.toLong, 9l.toLong, 9l.toLong))) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_12_longest", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Out of list of strings, return the longest one. Return the first one in case of multiple\n    // strings of the same length. Return None in case the input list is empty.\n    // >>> longest((List[String]()))\n    // None\n    // >>> longest((List[String](\"a\", \"b\", \"c\")))\n    // \"a\"\n    // >>> longest((List[String](\"a\", \"bb\", \"ccc\")))\n    // \"ccc\"\n    def longest(strings : List[String]) : Option[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(longest((List[String]())).equals(None));\n    assert(longest((List[String](\"x\", \"y\", \"z\"))).equals(\"x\"));\n    assert(longest((List[String](\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"))).equals(\"zzzz\"));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_12_longest", "test": "    }\n    def main(args: Array[String]) = {\n    assert(longest((List[String]())).equals(None));\n    assert(longest((List[String](\"x\", \"y\", \"z\"))).equals(\"x\"));\n    assert(longest((List[String](\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"))).equals(\"zzzz\"));\n    }\n\n}\n"}
{"name": "HumanEval_52_below_threshold", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return true if all numbers in the list l are below threshold t.\n    // >>> belowThreshold((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 10l.toLong)), (100l))\n    // (true)\n    // >>> belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (5l))\n    // (false)\n    def belowThreshold(l : List[Long], t : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(belowThreshold((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 10l.toLong)), (100l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (5l)) == (false));\n    assert(belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (21l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (22l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 8l.toLong, 4l.toLong, 10l.toLong)), (11l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 8l.toLong, 4l.toLong, 10l.toLong)), (10l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_52_below_threshold", "test": "    }\n    def main(args: Array[String]) = {\n    assert(belowThreshold((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 10l.toLong)), (100l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (5l)) == (false));\n    assert(belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (21l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 20l.toLong, 4l.toLong, 10l.toLong)), (22l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 8l.toLong, 4l.toLong, 10l.toLong)), (11l)) == (true));\n    assert(belowThreshold((List[Long](1l.toLong, 8l.toLong, 4l.toLong, 10l.toLong)), (10l)) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_75_is_multiply_prime", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that returns true if the given number is the multiplication of 3 prime numbers\n    // and false otherwise.\n    // Knowing that (a) is less then 100. \n    // Example:\n    // >>> isMultiplyPrime((30l))\n    // (true)\n    // 30 = 2 * 3 * 5\n    def isMultiplyPrime(a : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(isMultiplyPrime((5l)) == (false));\n    assert(isMultiplyPrime((30l)) == (true));\n    assert(isMultiplyPrime((8l)) == (true));\n    assert(isMultiplyPrime((10l)) == (false));\n    assert(isMultiplyPrime((125l)) == (true));\n    assert(isMultiplyPrime((105l)) == (true));\n    assert(isMultiplyPrime((126l)) == (false));\n    assert(isMultiplyPrime((729l)) == (false));\n    assert(isMultiplyPrime((891l)) == (false));\n    assert(isMultiplyPrime((1001l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_75_is_multiply_prime", "test": "    }\n    def main(args: Array[String]) = {\n    assert(isMultiplyPrime((5l)) == (false));\n    assert(isMultiplyPrime((30l)) == (true));\n    assert(isMultiplyPrime((8l)) == (true));\n    assert(isMultiplyPrime((10l)) == (false));\n    assert(isMultiplyPrime((125l)) == (true));\n    assert(isMultiplyPrime((105l)) == (true));\n    assert(isMultiplyPrime((126l)) == (false));\n    assert(isMultiplyPrime((729l)) == (false));\n    assert(isMultiplyPrime((891l)) == (false));\n    assert(isMultiplyPrime((1001l)) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_30_get_positive", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return only positive numbers in the list.\n    // >>> getPositive((List[Long](-1l.toLong, 2l.toLong, -4l.toLong, 5l.toLong, 6l.toLong)))\n    // (List[Long](2l.toLong, 5l.toLong, 6l.toLong))\n    // >>> getPositive((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong, 1l.toLong, -10l.toLong)))\n    // (List[Long](5l.toLong, 3l.toLong, 2l.toLong, 3l.toLong, 9l.toLong, 123l.toLong, 1l.toLong))\n    def getPositive(l : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(getPositive((List[Long](-1l.toLong, -2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong))).equals((List[Long](4l.toLong, 5l.toLong, 6l.toLong))));\n    assert(getPositive((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong, 1l.toLong, -10l.toLong))).equals((List[Long](5l.toLong, 3l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 123l.toLong, 1l.toLong))));\n    assert(getPositive((List[Long](-1l.toLong, -2l.toLong))).equals((List[Long]())));\n    assert(getPositive((List[Long]())).equals((List[Long]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_30_get_positive", "test": "    }\n    def main(args: Array[String]) = {\n    assert(getPositive((List[Long](-1l.toLong, -2l.toLong, 4l.toLong, 5l.toLong, 6l.toLong))).equals((List[Long](4l.toLong, 5l.toLong, 6l.toLong))));\n    assert(getPositive((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong, 1l.toLong, -10l.toLong))).equals((List[Long](5l.toLong, 3l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 9l.toLong, 123l.toLong, 1l.toLong))));\n    assert(getPositive((List[Long](-1l.toLong, -2l.toLong))).equals((List[Long]())));\n    assert(getPositive((List[Long]())).equals((List[Long]())));\n    }\n\n}\n"}
{"name": "HumanEval_33_sort_third", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // This function takes a list l and returns a list l' such that\n    // l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n    // to the values of the corresponding indicies of l, but sorted.\n    // >>> sortThird((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[Long](1l.toLong, 2l.toLong, 3l.toLong))\n    // >>> sortThird((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 2l.toLong)))\n    // (List[Long](2l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 5l.toLong))\n    def sortThird(l : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sortThird((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 5l.toLong))));\n    assert(sortThird((List[Long](5l.toLong, 8l.toLong, 3l.toLong, 4l.toLong, 6l.toLong, 9l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 8l.toLong, 3l.toLong, 4l.toLong, 6l.toLong, 9l.toLong, 5l.toLong))));\n    assert(sortThird((List[Long](5l.toLong, 6l.toLong, 9l.toLong, 4l.toLong, 8l.toLong, 3l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 6l.toLong, 9l.toLong, 4l.toLong, 8l.toLong, 3l.toLong, 5l.toLong))));\n    assert(sortThird((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](2l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 5l.toLong, 1l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_33_sort_third", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sortThird((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 5l.toLong))));\n    assert(sortThird((List[Long](5l.toLong, 8l.toLong, 3l.toLong, 4l.toLong, 6l.toLong, 9l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 8l.toLong, 3l.toLong, 4l.toLong, 6l.toLong, 9l.toLong, 5l.toLong))));\n    assert(sortThird((List[Long](5l.toLong, 6l.toLong, 9l.toLong, 4l.toLong, 8l.toLong, 3l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 6l.toLong, 9l.toLong, 4l.toLong, 8l.toLong, 3l.toLong, 5l.toLong))));\n    assert(sortThird((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](2l.toLong, 6l.toLong, 3l.toLong, 4l.toLong, 8l.toLong, 9l.toLong, 5l.toLong, 1l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_6_parse_nested_parens", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n    // For each of the group, output the deepest level of nesting of parentheses.\n    // E.g. (()()) has maximum two levels of nesting while ((())) has three.\n    // >>> parseNestedParens((\"(()()) ((())) () ((())()())\"))\n    // (List[Long](2l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))\n    def parseNestedParens(paren_string : String) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(parseNestedParens((\"(()()) ((())) () ((())()())\")).equals((List[Long](2l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))));\n    assert(parseNestedParens((\"() (()) ((())) (((())))\")).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(parseNestedParens((\"(()(())((())))\")).equals((List[Long](4l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_6_parse_nested_parens", "test": "    }\n    def main(args: Array[String]) = {\n    assert(parseNestedParens((\"(()()) ((())) () ((())()())\")).equals((List[Long](2l.toLong, 3l.toLong, 1l.toLong, 3l.toLong))));\n    assert(parseNestedParens((\"() (()) ((())) (((())))\")).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(parseNestedParens((\"(()(())((())))\")).equals((List[Long](4l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_45_triangle_area", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given length of a side and high return area for a triangle.\n    // >>> triangleArea((5l), (3l))\n    // (7.5f)\n    def triangleArea(a : Long, h : Long) : Float = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(triangleArea((5l), (3l)) == (7.5f));\n    assert(triangleArea((2l), (2l)) == (2.0f));\n    assert(triangleArea((10l), (8l)) == (40.0f));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_45_triangle_area", "test": "    }\n    def main(args: Array[String]) = {\n    assert(triangleArea((5l), (3l)) == (7.5f));\n    assert(triangleArea((2l), (2l)) == (2.0f));\n    assert(triangleArea((10l), (8l)) == (40.0f));\n    }\n\n}\n"}
{"name": "HumanEval_97_multiply", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Complete the function that takes two integers and returns \n    // the product of their unit digits.\n    // Assume the input is always valid.\n    // Examples:\n    // >>> multiply((148l), (412l))\n    // (16l)\n    // >>> multiply((19l), (28l))\n    // (72l)\n    // >>> multiply((2020l), (1851l))\n    // (0l)\n    // >>> multiply((14l), (-15l))\n    // (20l)\n    def multiply(a : Long, b : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(multiply((148l), (412l)) == (16l));\n    assert(multiply((19l), (28l)) == (72l));\n    assert(multiply((2020l), (1851l)) == (0l));\n    assert(multiply((14l), (-15l)) == (20l));\n    assert(multiply((76l), (67l)) == (42l));\n    assert(multiply((17l), (27l)) == (49l));\n    assert(multiply((0l), (1l)) == (0l));\n    assert(multiply((0l), (0l)) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_97_multiply", "test": "    }\n    def main(args: Array[String]) = {\n    assert(multiply((148l), (412l)) == (16l));\n    assert(multiply((19l), (28l)) == (72l));\n    assert(multiply((2020l), (1851l)) == (0l));\n    assert(multiply((14l), (-15l)) == (20l));\n    assert(multiply((76l), (67l)) == (42l));\n    assert(multiply((17l), (27l)) == (49l));\n    assert(multiply((0l), (1l)) == (0l));\n    assert(multiply((0l), (0l)) == (0l));\n    }\n\n}\n"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // For a given list of input numbers, calculate Mean Absolute Deviation\n    // around the mean of this dataset.\n    // Mean Absolute Deviation is the average absolute difference between each\n    // element and a centerpoint (mean in this case):\n    // MAD = average | x - x_mean |\n    // >>> meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat)))\n    // (1.0f)\n    def meanAbsoluteDeviation(numbers : List[Float]) : Float = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat))) == (0.5f));\n    assert(meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat))) == (1.0f));\n    assert(meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat))) == (1.2f));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "    }\n    def main(args: Array[String]) = {\n    assert(meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat))) == (0.5f));\n    assert(meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat))) == (1.0f));\n    assert(meanAbsoluteDeviation((List[Float](1.0f.toFloat, 2.0f.toFloat, 3.0f.toFloat, 4.0f.toFloat, 5.0f.toFloat))) == (1.2f));\n    }\n\n}\n"}
{"name": "HumanEval_58_common", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return sorted unique common elements for two lists.\n    // >>> common((List[Long](1l.toLong, 4l.toLong, 3l.toLong, 34l.toLong, 653l.toLong, 2l.toLong, 5l.toLong)), (List[Long](5l.toLong, 7l.toLong, 1l.toLong, 5l.toLong, 9l.toLong, 653l.toLong, 121l.toLong)))\n    // (List[Long](1l.toLong, 5l.toLong, 653l.toLong))\n    // >>> common((List[Long](5l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long](3l.toLong, 2l.toLong)))\n    // (List[Long](2l.toLong, 3l.toLong))\n    def common(l1 : List[Long], l2 : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(common((List[Long](1l.toLong, 4l.toLong, 3l.toLong, 34l.toLong, 653l.toLong, 2l.toLong, 5l.toLong)), (List[Long](5l.toLong, 7l.toLong, 1l.toLong, 5l.toLong, 9l.toLong, 653l.toLong, 121l.toLong))).equals((List[Long](1l.toLong, 5l.toLong, 653l.toLong))));\n    assert(common((List[Long](5l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long](3l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 3l.toLong))));\n    assert(common((List[Long](4l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long](3l.toLong, 2l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(common((List[Long](4l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long]())).equals((List[Long]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_58_common", "test": "    }\n    def main(args: Array[String]) = {\n    assert(common((List[Long](1l.toLong, 4l.toLong, 3l.toLong, 34l.toLong, 653l.toLong, 2l.toLong, 5l.toLong)), (List[Long](5l.toLong, 7l.toLong, 1l.toLong, 5l.toLong, 9l.toLong, 653l.toLong, 121l.toLong))).equals((List[Long](1l.toLong, 5l.toLong, 653l.toLong))));\n    assert(common((List[Long](5l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long](3l.toLong, 2l.toLong))).equals((List[Long](2l.toLong, 3l.toLong))));\n    assert(common((List[Long](4l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long](3l.toLong, 2l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(common((List[Long](4l.toLong, 3l.toLong, 2l.toLong, 8l.toLong)), (List[Long]())).equals((List[Long]())));\n    }\n\n}\n"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a positive integer, obtain its roman numeral equivalent as a string,\n    // and return it in lowercase.\n    // Restrictions: 1 <= num <= 1000\n    // Examples:\n    // >>> intToMiniRoman((19l))\n    // (\"xix\")\n    // >>> intToMiniRoman((152l))\n    // (\"clii\")\n    // >>> intToMiniRoman((426l))\n    // (\"cdxxvi\")\n    def intToMiniRoman(number : Long) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(intToMiniRoman((19l)).equals((\"xix\")));\n    assert(intToMiniRoman((152l)).equals((\"clii\")));\n    assert(intToMiniRoman((251l)).equals((\"ccli\")));\n    assert(intToMiniRoman((426l)).equals((\"cdxxvi\")));\n    assert(intToMiniRoman((500l)).equals((\"d\")));\n    assert(intToMiniRoman((1l)).equals((\"i\")));\n    assert(intToMiniRoman((4l)).equals((\"iv\")));\n    assert(intToMiniRoman((43l)).equals((\"xliii\")));\n    assert(intToMiniRoman((90l)).equals((\"xc\")));\n    assert(intToMiniRoman((94l)).equals((\"xciv\")));\n    assert(intToMiniRoman((532l)).equals((\"dxxxii\")));\n    assert(intToMiniRoman((900l)).equals((\"cm\")));\n    assert(intToMiniRoman((994l)).equals((\"cmxciv\")));\n    assert(intToMiniRoman((1000l)).equals((\"m\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "    }\n    def main(args: Array[String]) = {\n    assert(intToMiniRoman((19l)).equals((\"xix\")));\n    assert(intToMiniRoman((152l)).equals((\"clii\")));\n    assert(intToMiniRoman((251l)).equals((\"ccli\")));\n    assert(intToMiniRoman((426l)).equals((\"cdxxvi\")));\n    assert(intToMiniRoman((500l)).equals((\"d\")));\n    assert(intToMiniRoman((1l)).equals((\"i\")));\n    assert(intToMiniRoman((4l)).equals((\"iv\")));\n    assert(intToMiniRoman((43l)).equals((\"xliii\")));\n    assert(intToMiniRoman((90l)).equals((\"xc\")));\n    assert(intToMiniRoman((94l)).equals((\"xciv\")));\n    assert(intToMiniRoman((532l)).equals((\"dxxxii\")));\n    assert(intToMiniRoman((900l)).equals((\"cm\")));\n    assert(intToMiniRoman((994l)).equals((\"cmxciv\")));\n    assert(intToMiniRoman((1000l)).equals((\"m\")));\n    }\n\n}\n"}
{"name": "HumanEval_67_fruit_distribution", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // In this task, you will be given a string that represents a number of apples and oranges \n    // that are distributed in a basket of fruit this basket contains \n    // apples, oranges, and mango fruits. Given the string that represents the total number of \n    // the oranges and apples and an integer that represent the total number of the fruits \n    // in the basket return the number of the mango fruits in the basket.\n    // for examble:\n    // >>> fruitDistribution((\"5 apples and 6 oranges\"), (19l))\n    // (8l)\n    // >>> fruitDistribution((\"0 apples and 1 oranges\"), (3l))\n    // (2l)\n    // >>> fruitDistribution((\"2 apples and 3 oranges\"), (100l))\n    // (95l)\n    // >>> fruitDistribution((\"100 apples and 1 oranges\"), (120l))\n    // (19l)\n    def fruitDistribution(s : String, n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(fruitDistribution((\"5 apples and 6 oranges\"), (19l)) == (8l));\n    assert(fruitDistribution((\"5 apples and 6 oranges\"), (21l)) == (10l));\n    assert(fruitDistribution((\"0 apples and 1 oranges\"), (3l)) == (2l));\n    assert(fruitDistribution((\"1 apples and 0 oranges\"), (3l)) == (2l));\n    assert(fruitDistribution((\"2 apples and 3 oranges\"), (100l)) == (95l));\n    assert(fruitDistribution((\"2 apples and 3 oranges\"), (5l)) == (0l));\n    assert(fruitDistribution((\"1 apples and 100 oranges\"), (120l)) == (19l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_67_fruit_distribution", "test": "    }\n    def main(args: Array[String]) = {\n    assert(fruitDistribution((\"5 apples and 6 oranges\"), (19l)) == (8l));\n    assert(fruitDistribution((\"5 apples and 6 oranges\"), (21l)) == (10l));\n    assert(fruitDistribution((\"0 apples and 1 oranges\"), (3l)) == (2l));\n    assert(fruitDistribution((\"1 apples and 0 oranges\"), (3l)) == (2l));\n    assert(fruitDistribution((\"2 apples and 3 oranges\"), (100l)) == (95l));\n    assert(fruitDistribution((\"2 apples and 3 oranges\"), (5l)) == (0l));\n    assert(fruitDistribution((\"1 apples and 100 oranges\"), (120l)) == (19l));\n    }\n\n}\n"}
{"name": "HumanEval_112_reverse_delete", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Task\n    // We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n    // then check if the result string is palindrome.\n    // A string is called palindrome if it reads the same backward as forward.\n    // You should return a tuple containing the result string and true/false for the check.\n    // Example\n    // >>> reverseDelete((\"abcde\"), (\"ae\"))\n    // ((\"bcd\", false))\n    // >>> reverseDelete((\"abcdef\"), (\"b\"))\n    // ((\"acdef\", false))\n    // >>> reverseDelete((\"abcdedcba\"), (\"ab\"))\n    // ((\"cdedc\", true))\n    def reverseDelete(s : String, c : String) : Tuple2[String, Boolean] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(reverseDelete((\"abcde\"), (\"ae\")).equals(((\"bcd\", false))));\n    assert(reverseDelete((\"abcdef\"), (\"b\")).equals(((\"acdef\", false))));\n    assert(reverseDelete((\"abcdedcba\"), (\"ab\")).equals(((\"cdedc\", true))));\n    assert(reverseDelete((\"dwik\"), (\"w\")).equals(((\"dik\", false))));\n    assert(reverseDelete((\"a\"), (\"a\")).equals(((\"\", true))));\n    assert(reverseDelete((\"abcdedcba\"), (\"\")).equals(((\"abcdedcba\", true))));\n    assert(reverseDelete((\"abcdedcba\"), (\"v\")).equals(((\"abcdedcba\", true))));\n    assert(reverseDelete((\"vabba\"), (\"v\")).equals(((\"abba\", true))));\n    assert(reverseDelete((\"mamma\"), (\"mia\")).equals(((\"\", true))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_112_reverse_delete", "test": "    }\n    def main(args: Array[String]) = {\n    assert(reverseDelete((\"abcde\"), (\"ae\")).equals(((\"bcd\", false))));\n    assert(reverseDelete((\"abcdef\"), (\"b\")).equals(((\"acdef\", false))));\n    assert(reverseDelete((\"abcdedcba\"), (\"ab\")).equals(((\"cdedc\", true))));\n    assert(reverseDelete((\"dwik\"), (\"w\")).equals(((\"dik\", false))));\n    assert(reverseDelete((\"a\"), (\"a\")).equals(((\"\", true))));\n    assert(reverseDelete((\"abcdedcba\"), (\"\")).equals(((\"abcdedcba\", true))));\n    assert(reverseDelete((\"abcdedcba\"), (\"v\")).equals(((\"abcdedcba\", true))));\n    assert(reverseDelete((\"vabba\"), (\"v\")).equals(((\"abba\", true))));\n    assert(reverseDelete((\"mamma\"), (\"mia\")).equals(((\"\", true))));\n    }\n\n}\n"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Return a greatest common divisor of two integers a and b\n    // >>> greatestCommonDivisor((3l), (5l))\n    // (1l)\n    // >>> greatestCommonDivisor((25l), (15l))\n    // (5l)\n    def greatestCommonDivisor(a : Long, b : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(greatestCommonDivisor((3l), (7l)) == (1l));\n    assert(greatestCommonDivisor((10l), (15l)) == (5l));\n    assert(greatestCommonDivisor((49l), (14l)) == (7l));\n    assert(greatestCommonDivisor((144l), (60l)) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "    }\n    def main(args: Array[String]) = {\n    assert(greatestCommonDivisor((3l), (7l)) == (1l));\n    assert(greatestCommonDivisor((10l), (15l)) == (5l));\n    assert(greatestCommonDivisor((49l), (14l)) == (7l));\n    assert(greatestCommonDivisor((144l), (60l)) == (12l));\n    }\n\n}\n"}
{"name": "HumanEval_125_split_words", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string of words, return a list of words split on whitespace, if no whitespaces exists in the text you\n    // should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n    // alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n    // Examples\n    // >>> splitWords((\"Hello world!\"))\n    // List[String](\"Hello\", \"world!\")\n    // >>> splitWords((\"Hello,world!\"))\n    // List[String](\"Hello\", \"world!\")\n    // >>> splitWords((\"abcdef\"))\n    // 3l\n    def splitWords(txt : String) : Either[List[String], Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_125_split_words.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(splitWords((\"Hello world!\")).equals(List[String](\"Hello\", \"world!\")));\n    assert(splitWords((\"Hello,world!\")).equals(List[String](\"Hello\", \"world!\")));\n    assert(splitWords((\"Hello world,!\")).equals(List[String](\"Hello\", \"world,!\")));\n    assert(splitWords((\"Hello,Hello,world !\")).equals(List[String](\"Hello,Hello,world\", \"!\")));\n    assert(splitWords((\"abcdef\")).equals(3l));\n    assert(splitWords((\"aaabb\")).equals(2l));\n    assert(splitWords((\"aaaBb\")).equals(1l));\n    assert(splitWords((\"\")).equals(0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_125_split_words", "test": "    }\n    def main(args: Array[String]) = {\n    assert(splitWords((\"Hello world!\")).equals(List[String](\"Hello\", \"world!\")));\n    assert(splitWords((\"Hello,world!\")).equals(List[String](\"Hello\", \"world!\")));\n    assert(splitWords((\"Hello world,!\")).equals(List[String](\"Hello\", \"world,!\")));\n    assert(splitWords((\"Hello,Hello,world !\")).equals(List[String](\"Hello,Hello,world\", \"!\")));\n    assert(splitWords((\"abcdef\")).equals(3l));\n    assert(splitWords((\"aaabb\")).equals(2l));\n    assert(splitWords((\"aaaBb\")).equals(1l));\n    assert(splitWords((\"\")).equals(0l));\n    }\n\n}\n"}
{"name": "HumanEval_116_sort_array", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // In this Kata, you have to sort a list of non-negative integers according to\n    // number of ones in their binary representation in ascending order.\n    // For similar number of ones, sort based on decimal value.\n    // It must be implemented like this:\n    // >>> sortArray((List[Long](1l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)))\n    // (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 5l.toLong))\n    // >>> sortArray((List[Long](-2l.toLong, -3l.toLong, -4l.toLong, -5l.toLong, -6l.toLong)))\n    // (List[Long](-6l.toLong, -5l.toLong, -4l.toLong, -3l.toLong, -2l.toLong))\n    // >>> sortArray((List[Long](1l.toLong, 0l.toLong, 2l.toLong, 3l.toLong, 4l.toLong)))\n    // (List[Long](0l.toLong, 1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))\n    def sortArray(arr : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sortArray((List[Long](1l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong))));\n    assert(sortArray((List[Long](-2l.toLong, -3l.toLong, -4l.toLong, -5l.toLong, -6l.toLong))).equals((List[Long](-4l.toLong, -2l.toLong, -6l.toLong, -5l.toLong, -3l.toLong))));\n    assert(sortArray((List[Long](1l.toLong, 0l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](0l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong))));\n    assert(sortArray((List[Long]())).equals((List[Long]())));\n    assert(sortArray((List[Long](2l.toLong, 5l.toLong, 77l.toLong, 4l.toLong, 5l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 2l.toLong, 4l.toLong, 4l.toLong, 3l.toLong, 3l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 7l.toLong, 77l.toLong))));\n    assert(sortArray((List[Long](3l.toLong, 6l.toLong, 44l.toLong, 12l.toLong, 32l.toLong, 5l.toLong))).equals((List[Long](32l.toLong, 3l.toLong, 5l.toLong, 6l.toLong, 12l.toLong, 44l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))).equals((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))).equals((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_116_sort_array", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sortArray((List[Long](1l.toLong, 5l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong))));\n    assert(sortArray((List[Long](-2l.toLong, -3l.toLong, -4l.toLong, -5l.toLong, -6l.toLong))).equals((List[Long](-4l.toLong, -2l.toLong, -6l.toLong, -5l.toLong, -3l.toLong))));\n    assert(sortArray((List[Long](1l.toLong, 0l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](0l.toLong, 1l.toLong, 2l.toLong, 4l.toLong, 3l.toLong))));\n    assert(sortArray((List[Long]())).equals((List[Long]())));\n    assert(sortArray((List[Long](2l.toLong, 5l.toLong, 77l.toLong, 4l.toLong, 5l.toLong, 3l.toLong, 5l.toLong, 7l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](2l.toLong, 2l.toLong, 4l.toLong, 4l.toLong, 3l.toLong, 3l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 7l.toLong, 77l.toLong))));\n    assert(sortArray((List[Long](3l.toLong, 6l.toLong, 44l.toLong, 12l.toLong, 32l.toLong, 5l.toLong))).equals((List[Long](32l.toLong, 3l.toLong, 5l.toLong, 6l.toLong, 12l.toLong, 44l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))).equals((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))));\n    assert(sortArray((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))).equals((List[Long](2l.toLong, 4l.toLong, 8l.toLong, 16l.toLong, 32l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_28_concatenate", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Concatenate list of strings into a single string\n    // >>> concatenate((List[String]()))\n    // (\"\")\n    // >>> concatenate((List[String](\"a\", \"b\", \"c\")))\n    // (\"abc\")\n    def concatenate(strings : List[String]) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(concatenate((List[String]())).equals((\"\")));\n    assert(concatenate((List[String](\"x\", \"y\", \"z\"))).equals((\"xyz\")));\n    assert(concatenate((List[String](\"x\", \"y\", \"z\", \"w\", \"k\"))).equals((\"xyzwk\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_28_concatenate", "test": "    }\n    def main(args: Array[String]) = {\n    assert(concatenate((List[String]())).equals((\"\")));\n    assert(concatenate((List[String](\"x\", \"y\", \"z\"))).equals((\"xyz\")));\n    assert(concatenate((List[String](\"x\", \"y\", \"z\", \"w\", \"k\"))).equals((\"xyzwk\")));\n    }\n\n}\n"}
{"name": "HumanEval_149_sorted_list_sum", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that accepts a list of strings as a parameter,\n    // deletes the strings that have odd lengths from it,\n    // and returns the resulted list with a sorted order,\n    // The list is always a list of strings and never a list of numbers,\n    // and it may contain duplicates.\n    // The order of the list should be ascending by length of each word, and you\n    // should return the list sorted by that rule.\n    // If two words have the same length, sort the list alphabetically.\n    // The function should return a list of strings in sorted order.\n    // You may assume that all words will have the same length.\n    // For example:\n    // >>> listSort((List[String](\"aa\", \"a\", \"aaa\")))\n    // (List[String](\"aa\"))\n    // >>> listSort((List[String](\"ab\", \"a\", \"aaa\", \"cd\")))\n    // (List[String](\"ab\", \"cd\"))\n    def sortedListSum(lst : List[String]) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sortedListSum((List[String](\"aa\", \"a\", \"aaa\"))).equals((List[String](\"aa\"))));\n    assert(sortedListSum((List[String](\"school\", \"AI\", \"asdf\", \"b\"))).equals((List[String](\"AI\", \"asdf\", \"school\"))));\n    assert(sortedListSum((List[String](\"d\", \"b\", \"c\", \"a\"))).equals((List[String]())));\n    assert(sortedListSum((List[String](\"d\", \"dcba\", \"abcd\", \"a\"))).equals((List[String](\"abcd\", \"dcba\"))));\n    assert(sortedListSum((List[String](\"AI\", \"ai\", \"au\"))).equals((List[String](\"AI\", \"ai\", \"au\"))));\n    assert(sortedListSum((List[String](\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"))).equals((List[String]())));\n    assert(sortedListSum((List[String](\"aaaa\", \"bbbb\", \"dd\", \"cc\"))).equals((List[String](\"cc\", \"dd\", \"aaaa\", \"bbbb\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_149_sorted_list_sum", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sortedListSum((List[String](\"aa\", \"a\", \"aaa\"))).equals((List[String](\"aa\"))));\n    assert(sortedListSum((List[String](\"school\", \"AI\", \"asdf\", \"b\"))).equals((List[String](\"AI\", \"asdf\", \"school\"))));\n    assert(sortedListSum((List[String](\"d\", \"b\", \"c\", \"a\"))).equals((List[String]())));\n    assert(sortedListSum((List[String](\"d\", \"dcba\", \"abcd\", \"a\"))).equals((List[String](\"abcd\", \"dcba\"))));\n    assert(sortedListSum((List[String](\"AI\", \"ai\", \"au\"))).equals((List[String](\"AI\", \"ai\", \"au\"))));\n    assert(sortedListSum((List[String](\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"))).equals((List[String]())));\n    assert(sortedListSum((List[String](\"aaaa\", \"bbbb\", \"dd\", \"cc\"))).equals((List[String](\"cc\", \"dd\", \"aaaa\", \"bbbb\"))));\n    }\n\n}\n"}
{"name": "HumanEval_7_filter_by_substring", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Filter an input list of strings only for ones that contain given substring\n    // >>> filterBySubstring((List[String]()), (\"a\"))\n    // (List[String]())\n    // >>> filterBySubstring((List[String](\"abc\", \"bacd\", \"cde\", \"array\")), (\"a\"))\n    // (List[String](\"abc\", \"bacd\", \"array\"))\n    def filterBySubstring(strings : List[String], substring : String) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(filterBySubstring((List[String]()), (\"john\")).equals((List[String]())));\n    assert(filterBySubstring((List[String](\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\")), (\"xxx\")).equals((List[String](\"xxx\", \"xxxAAA\", \"xxx\"))));\n    assert(filterBySubstring((List[String](\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\")), (\"xx\")).equals((List[String](\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"))));\n    assert(filterBySubstring((List[String](\"grunt\", \"trumpet\", \"prune\", \"gruesome\")), (\"run\")).equals((List[String](\"grunt\", \"prune\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_7_filter_by_substring", "test": "    }\n    def main(args: Array[String]) = {\n    assert(filterBySubstring((List[String]()), (\"john\")).equals((List[String]())));\n    assert(filterBySubstring((List[String](\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\")), (\"xxx\")).equals((List[String](\"xxx\", \"xxxAAA\", \"xxx\"))));\n    assert(filterBySubstring((List[String](\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\")), (\"xx\")).equals((List[String](\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"))));\n    assert(filterBySubstring((List[String](\"grunt\", \"trumpet\", \"prune\", \"gruesome\")), (\"run\")).equals((List[String](\"grunt\", \"prune\"))));\n    }\n\n}\n"}
{"name": "HumanEval_99_closest_integer", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Create a function that takes a value (string) representing a number\n    // and returns the closest integer to it. If the number is equidistant\n    // from two integers, round it away from zero.\n    // Examples\n    // >>> closestInteger((\"10\"))\n    // (10l)\n    // >>> closestInteger((\"15.3\"))\n    // (15l)\n    // Note:\n    // Rounding away from zero means that if the given number is equidistant\n    // from two integers, the one you should return is the one that is the\n    // farthest from zero. For example closest_integer(\"14.5\") should\n    // return 15 and closest_integer(\"-14.5\") should return -15.\n    def closestInteger(value : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(closestInteger((\"10\")) == (10l));\n    assert(closestInteger((\"14.5\")) == (15l));\n    assert(closestInteger((\"-15.5\")) == (-16l));\n    assert(closestInteger((\"15.3\")) == (15l));\n    assert(closestInteger((\"0\")) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_99_closest_integer", "test": "    }\n    def main(args: Array[String]) = {\n    assert(closestInteger((\"10\")) == (10l));\n    assert(closestInteger((\"14.5\")) == (15l));\n    assert(closestInteger((\"-15.5\")) == (-16l));\n    assert(closestInteger((\"15.3\")) == (15l));\n    assert(closestInteger((\"0\")) == (0l));\n    }\n\n}\n"}
{"name": "HumanEval_64_vowels_count", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function vowels_count which takes a string representing\n    // a word as input and returns the number of vowels in the string.\n    // Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n    // vowel, but only when it is at the end of the given word.\n    // Example:\n    // >>> vowelsCount((\"abcde\"))\n    // (2l)\n    // >>> vowelsCount((\"ACEDY\"))\n    // (3l)\n    def vowelsCount(s : String) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(vowelsCount((\"abcde\")) == (2l));\n    assert(vowelsCount((\"Alone\")) == (3l));\n    assert(vowelsCount((\"key\")) == (2l));\n    assert(vowelsCount((\"bye\")) == (1l));\n    assert(vowelsCount((\"keY\")) == (2l));\n    assert(vowelsCount((\"bYe\")) == (1l));\n    assert(vowelsCount((\"ACEDY\")) == (3l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_64_vowels_count", "test": "    }\n    def main(args: Array[String]) = {\n    assert(vowelsCount((\"abcde\")) == (2l));\n    assert(vowelsCount((\"Alone\")) == (3l));\n    assert(vowelsCount((\"key\")) == (2l));\n    assert(vowelsCount((\"bye\")) == (1l));\n    assert(vowelsCount((\"keY\")) == (2l));\n    assert(vowelsCount((\"bYe\")) == (1l));\n    assert(vowelsCount((\"ACEDY\")) == (3l));\n    }\n\n}\n"}
{"name": "HumanEval_158_find_max", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that accepts a list of strings.\n    // The list contains different words. Return the word with maximum number\n    // of unique characters. If multiple strings have maximum number of unique\n    // characters, return the one which comes first in lexicographical order.\n    // >>> findMax((List[String](\"name\", \"of\", \"string\")))\n    // (\"string\")\n    // >>> findMax((List[String](\"name\", \"enam\", \"game\")))\n    // (\"enam\")\n    // >>> findMax((List[String](\"aaaaaaa\", \"bb\", \"cc\")))\n    // (\"aaaaaaa\")\n    def findMax(words : List[String]) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(findMax((List[String](\"name\", \"of\", \"string\"))).equals((\"string\")));\n    assert(findMax((List[String](\"name\", \"enam\", \"game\"))).equals((\"enam\")));\n    assert(findMax((List[String](\"aaaaaaa\", \"bb\", \"cc\"))).equals((\"aaaaaaa\")));\n    assert(findMax((List[String](\"abc\", \"cba\"))).equals((\"abc\")));\n    assert(findMax((List[String](\"play\", \"this\", \"game\", \"of\", \"footbott\"))).equals((\"footbott\")));\n    assert(findMax((List[String](\"we\", \"are\", \"gonna\", \"rock\"))).equals((\"gonna\")));\n    assert(findMax((List[String](\"we\", \"are\", \"a\", \"mad\", \"nation\"))).equals((\"nation\")));\n    assert(findMax((List[String](\"this\", \"is\", \"a\", \"prrk\"))).equals((\"this\")));\n    assert(findMax((List[String](\"b\"))).equals((\"b\")));\n    assert(findMax((List[String](\"play\", \"play\", \"play\"))).equals((\"play\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_158_find_max", "test": "    }\n    def main(args: Array[String]) = {\n    assert(findMax((List[String](\"name\", \"of\", \"string\"))).equals((\"string\")));\n    assert(findMax((List[String](\"name\", \"enam\", \"game\"))).equals((\"enam\")));\n    assert(findMax((List[String](\"aaaaaaa\", \"bb\", \"cc\"))).equals((\"aaaaaaa\")));\n    assert(findMax((List[String](\"abc\", \"cba\"))).equals((\"abc\")));\n    assert(findMax((List[String](\"play\", \"this\", \"game\", \"of\", \"footbott\"))).equals((\"footbott\")));\n    assert(findMax((List[String](\"we\", \"are\", \"gonna\", \"rock\"))).equals((\"gonna\")));\n    assert(findMax((List[String](\"we\", \"are\", \"a\", \"mad\", \"nation\"))).equals((\"nation\")));\n    assert(findMax((List[String](\"this\", \"is\", \"a\", \"prrk\"))).equals((\"this\")));\n    assert(findMax((List[String](\"b\"))).equals((\"b\")));\n    assert(findMax((List[String](\"play\", \"play\", \"play\"))).equals((\"play\")));\n    }\n\n}\n"}
{"name": "HumanEval_162_string_to_md5", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given a string 'text', return its md5 hash equivalent string.\n    // If 'text' is an empty string, return None.\n    // >>> stringToMd5((\"Hello world\"))\n    // \"3e25960a79dbc69b674cd4ec67a72c62\"\n    def stringToMd5(text : String) : Option[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(stringToMd5((\"Hello world\")).equals(\"3e25960a79dbc69b674cd4ec67a72c62\"));\n    assert(stringToMd5((\"\")).equals(None));\n    assert(stringToMd5((\"A B C\")).equals(\"0ef78513b0cb8cef12743f5aeb35f888\"));\n    assert(stringToMd5((\"password\")).equals(\"5f4dcc3b5aa765d61d8327deb882cf99\"));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_162_string_to_md5", "test": "    }\n    def main(args: Array[String]) = {\n    assert(stringToMd5((\"Hello world\")).equals(\"3e25960a79dbc69b674cd4ec67a72c62\"));\n    assert(stringToMd5((\"\")).equals(None));\n    assert(stringToMd5((\"A B C\")).equals(\"0ef78513b0cb8cef12743f5aeb35f888\"));\n    assert(stringToMd5((\"password\")).equals(\"5f4dcc3b5aa765d61d8327deb882cf99\"));\n    }\n\n}\n"}
{"name": "HumanEval_44_change_base", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Change numerical base of input number x to base.\n    // return string representation after the conversion.\n    // base numbers are less than 10.\n    // >>> changeBase((8l), (3l))\n    // (\"22\")\n    // >>> changeBase((8l), (2l))\n    // (\"1000\")\n    // >>> changeBase((7l), (2l))\n    // (\"111\")\n    def changeBase(x : Long, base : Long) : String = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(changeBase((8l), (3l)).equals((\"22\")));\n    assert(changeBase((9l), (3l)).equals((\"100\")));\n    assert(changeBase((234l), (2l)).equals((\"11101010\")));\n    assert(changeBase((16l), (2l)).equals((\"10000\")));\n    assert(changeBase((8l), (2l)).equals((\"1000\")));\n    assert(changeBase((7l), (2l)).equals((\"111\")));\n    assert(changeBase((2l), (3l)).equals((\"2\")));\n    assert(changeBase((3l), (4l)).equals((\"3\")));\n    assert(changeBase((4l), (5l)).equals((\"4\")));\n    assert(changeBase((5l), (6l)).equals((\"5\")));\n    assert(changeBase((6l), (7l)).equals((\"6\")));\n    assert(changeBase((7l), (8l)).equals((\"7\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_44_change_base", "test": "    }\n    def main(args: Array[String]) = {\n    assert(changeBase((8l), (3l)).equals((\"22\")));\n    assert(changeBase((9l), (3l)).equals((\"100\")));\n    assert(changeBase((234l), (2l)).equals((\"11101010\")));\n    assert(changeBase((16l), (2l)).equals((\"10000\")));\n    assert(changeBase((8l), (2l)).equals((\"1000\")));\n    assert(changeBase((7l), (2l)).equals((\"111\")));\n    assert(changeBase((2l), (3l)).equals((\"2\")));\n    assert(changeBase((3l), (4l)).equals((\"3\")));\n    assert(changeBase((4l), (5l)).equals((\"4\")));\n    assert(changeBase((5l), (6l)).equals((\"5\")));\n    assert(changeBase((6l), (7l)).equals((\"6\")));\n    assert(changeBase((7l), (8l)).equals((\"7\")));\n    }\n\n}\n"}
{"name": "HumanEval_157_right_angle_triangle", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given the lengths of the three sides of a triangle. Return true if the three\n    // sides form a right-angled triangle, false otherwise.\n    // A right-angled triangle is a triangle in which one angle is right angle or \n    // 90 degree.\n    // Example:\n    // >>> rightAngleTriangle((3l), (4l), (5l))\n    // (true)\n    // >>> rightAngleTriangle((1l), (2l), (3l))\n    // (false)\n    def rightAngleTriangle(a : Long, b : Long, c : Long) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(rightAngleTriangle((3l), (4l), (5l)) == (true));\n    assert(rightAngleTriangle((1l), (2l), (3l)) == (false));\n    assert(rightAngleTriangle((10l), (6l), (8l)) == (true));\n    assert(rightAngleTriangle((2l), (2l), (2l)) == (false));\n    assert(rightAngleTriangle((7l), (24l), (25l)) == (true));\n    assert(rightAngleTriangle((10l), (5l), (7l)) == (false));\n    assert(rightAngleTriangle((5l), (12l), (13l)) == (true));\n    assert(rightAngleTriangle((15l), (8l), (17l)) == (true));\n    assert(rightAngleTriangle((48l), (55l), (73l)) == (true));\n    assert(rightAngleTriangle((1l), (1l), (1l)) == (false));\n    assert(rightAngleTriangle((2l), (2l), (10l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_157_right_angle_triangle", "test": "    }\n    def main(args: Array[String]) = {\n    assert(rightAngleTriangle((3l), (4l), (5l)) == (true));\n    assert(rightAngleTriangle((1l), (2l), (3l)) == (false));\n    assert(rightAngleTriangle((10l), (6l), (8l)) == (true));\n    assert(rightAngleTriangle((2l), (2l), (2l)) == (false));\n    assert(rightAngleTriangle((7l), (24l), (25l)) == (true));\n    assert(rightAngleTriangle((10l), (5l), (7l)) == (false));\n    assert(rightAngleTriangle((5l), (12l), (13l)) == (true));\n    assert(rightAngleTriangle((15l), (8l), (17l)) == (true));\n    assert(rightAngleTriangle((48l), (55l), (73l)) == (true));\n    assert(rightAngleTriangle((1l), (1l), (1l)) == (false));\n    assert(rightAngleTriangle((2l), (2l), (10l)) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // It is the last week of the semester and the teacher has to give the grades\n    // to students. The teacher has been making her own algorithm for grading.\n    // The only problem is, she has lost the code she used for grading.\n    // She has given you a list of GPAs for some students and you have to write \n    // a function that can output a list of letter grades using the following table:\n    // GPA       |    Letter grade\n    // 4.0                A+\n    // > 3.7                A \n    // > 3.3                A- \n    // > 3.0                B+\n    // > 2.7                B \n    // > 2.3                B-\n    // > 2.0                C+\n    // > 1.7                C\n    // > 1.3                C-\n    // > 1.0                D+ \n    // > 0.7                D \n    // > 0.0                D-\n    // 0.0                E\n    // Example:\n    // >>> gradeEquation((List[Float](4.0f.toFloat, 3l.toFloat, 1.7f.toFloat, 2l.toFloat, 3.5f.toFloat)))\n    // (List[String](\"A+\", \"B\", \"C-\", \"C\", \"A-\"))\n    def numericalLetterGrade(grades : List[Float]) : List[String] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(numericalLetterGrade((List[Float](4.0f.toFloat, 3l.toFloat, 1.7f.toFloat, 2l.toFloat, 3.5f.toFloat))).equals((List[String](\"A+\", \"B\", \"C-\", \"C\", \"A-\"))));\n    assert(numericalLetterGrade((List[Float](1.2f.toFloat))).equals((List[String](\"D+\"))));\n    assert(numericalLetterGrade((List[Float](0.5f.toFloat))).equals((List[String](\"D-\"))));\n    assert(numericalLetterGrade((List[Float](0.0f.toFloat))).equals((List[String](\"E\"))));\n    assert(numericalLetterGrade((List[Float](1.0f.toFloat, 0.3f.toFloat, 1.5f.toFloat, 2.8f.toFloat, 3.3f.toFloat))).equals((List[String](\"D\", \"D-\", \"C-\", \"B\", \"B+\"))));\n    assert(numericalLetterGrade((List[Float](0.0f.toFloat, 0.7f.toFloat))).equals((List[String](\"E\", \"D-\"))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "    }\n    def main(args: Array[String]) = {\n    assert(numericalLetterGrade((List[Float](4.0f.toFloat, 3l.toFloat, 1.7f.toFloat, 2l.toFloat, 3.5f.toFloat))).equals((List[String](\"A+\", \"B\", \"C-\", \"C\", \"A-\"))));\n    assert(numericalLetterGrade((List[Float](1.2f.toFloat))).equals((List[String](\"D+\"))));\n    assert(numericalLetterGrade((List[Float](0.5f.toFloat))).equals((List[String](\"D-\"))));\n    assert(numericalLetterGrade((List[Float](0.0f.toFloat))).equals((List[String](\"E\"))));\n    assert(numericalLetterGrade((List[Float](1.0f.toFloat, 0.3f.toFloat, 1.5f.toFloat, 2.8f.toFloat, 3.3f.toFloat))).equals((List[String](\"D\", \"D-\", \"C-\", \"B\", \"B+\"))));\n    assert(numericalLetterGrade((List[Float](0.0f.toFloat, 0.7f.toFloat))).equals((List[String](\"E\", \"D-\"))));\n    }\n\n}\n"}
{"name": "HumanEval_5_intersperse", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Insert a number 'delimeter' between every two consecutive elements of input list `numbers'\n    // >>> intersperse((List[Long]()), (4l))\n    // (List[Long]())\n    // >>> intersperse((List[Long](1l.toLong, 2l.toLong, 3l.toLong)), (4l))\n    // (List[Long](1l.toLong, 4l.toLong, 2l.toLong, 4l.toLong, 3l.toLong))\n    def intersperse(numbers : List[Long], delimeter : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(intersperse((List[Long]()), (7l)).equals((List[Long]())));\n    assert(intersperse((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 2l.toLong)), (8l)).equals((List[Long](5l.toLong, 8l.toLong, 6l.toLong, 8l.toLong, 3l.toLong, 8l.toLong, 2l.toLong))));\n    assert(intersperse((List[Long](2l.toLong, 2l.toLong, 2l.toLong)), (2l)).equals((List[Long](2l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 2l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_5_intersperse", "test": "    }\n    def main(args: Array[String]) = {\n    assert(intersperse((List[Long]()), (7l)).equals((List[Long]())));\n    assert(intersperse((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 2l.toLong)), (8l)).equals((List[Long](5l.toLong, 8l.toLong, 6l.toLong, 8l.toLong, 3l.toLong, 8l.toLong, 2l.toLong))));\n    assert(intersperse((List[Long](2l.toLong, 2l.toLong, 2l.toLong)), (2l)).equals((List[Long](2l.toLong, 2l.toLong, 2l.toLong, 2l.toLong, 2l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_146_specialFilter", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Write a function that takes a list of numbers as input and returns \n    // the number of elements in the list that are greater than 10 and both \n    // first and last digits of a number are odd (1, 3, 5, 7, 9).\n    // For example:\n    // >>> specialFilter((List[Long](15l.toLong, -73l.toLong, 14l.toLong, -15l.toLong)))\n    // (1l)\n    // >>> specialFilter((List[Long](33l.toLong, -2l.toLong, -3l.toLong, 45l.toLong, 21l.toLong, 109l.toLong)))\n    // (2l)\n    def specialFilter(nums : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(specialFilter((List[Long](5l.toLong, -2l.toLong, 1l.toLong, -5l.toLong))) == (0l));\n    assert(specialFilter((List[Long](15l.toLong, -73l.toLong, 14l.toLong, -15l.toLong))) == (1l));\n    assert(specialFilter((List[Long](33l.toLong, -2l.toLong, -3l.toLong, 45l.toLong, 21l.toLong, 109l.toLong))) == (2l));\n    assert(specialFilter((List[Long](43l.toLong, -12l.toLong, 93l.toLong, 125l.toLong, 121l.toLong, 109l.toLong))) == (4l));\n    assert(specialFilter((List[Long](71l.toLong, -2l.toLong, -33l.toLong, 75l.toLong, 21l.toLong, 19l.toLong))) == (3l));\n    assert(specialFilter((List[Long](1l.toLong))) == (0l));\n    assert(specialFilter((List[Long]())) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_146_specialFilter", "test": "    }\n    def main(args: Array[String]) = {\n    assert(specialFilter((List[Long](5l.toLong, -2l.toLong, 1l.toLong, -5l.toLong))) == (0l));\n    assert(specialFilter((List[Long](15l.toLong, -73l.toLong, 14l.toLong, -15l.toLong))) == (1l));\n    assert(specialFilter((List[Long](33l.toLong, -2l.toLong, -3l.toLong, 45l.toLong, 21l.toLong, 109l.toLong))) == (2l));\n    assert(specialFilter((List[Long](43l.toLong, -12l.toLong, 93l.toLong, 125l.toLong, 121l.toLong, 109l.toLong))) == (4l));\n    assert(specialFilter((List[Long](71l.toLong, -2l.toLong, -33l.toLong, 75l.toLong, 21l.toLong, 19l.toLong))) == (3l));\n    assert(specialFilter((List[Long](1l.toLong))) == (0l));\n    assert(specialFilter((List[Long]())) == (0l));\n    }\n\n}\n"}
{"name": "HumanEval_60_sum_to_n", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // sum_to_n is a function that sums numbers from 1 to n.\n    // >>> sumToN((30l))\n    // (465l)\n    // >>> sumToN((100l))\n    // (5050l)\n    // >>> sumToN((5l))\n    // (15l)\n    // >>> sumToN((10l))\n    // (55l)\n    // >>> sumToN((1l))\n    // (1l)\n    def sumToN(n : Long) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sumToN((1l)) == (1l));\n    assert(sumToN((6l)) == (21l));\n    assert(sumToN((11l)) == (66l));\n    assert(sumToN((30l)) == (465l));\n    assert(sumToN((100l)) == (5050l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_60_sum_to_n", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sumToN((1l)) == (1l));\n    assert(sumToN((6l)) == (21l));\n    assert(sumToN((11l)) == (66l));\n    assert(sumToN((30l)) == (465l));\n    assert(sumToN((100l)) == (5050l));\n    }\n\n}\n"}
{"name": "HumanEval_26_remove_duplicates", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // From a list of integers, remove all elements that occur more than once.\n    // Keep order of elements left the same as in the input.\n    // >>> removeDuplicates((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 4l.toLong)))\n    // (List[Long](1l.toLong, 3l.toLong, 4l.toLong))\n    def removeDuplicates(numbers : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(removeDuplicates((List[Long]())).equals((List[Long]())));\n    assert(removeDuplicates((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(removeDuplicates((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong))).equals((List[Long](1l.toLong, 4l.toLong, 5l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_26_remove_duplicates", "test": "    }\n    def main(args: Array[String]) = {\n    assert(removeDuplicates((List[Long]())).equals((List[Long]())));\n    assert(removeDuplicates((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(removeDuplicates((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 4l.toLong, 3l.toLong, 5l.toLong))).equals((List[Long](1l.toLong, 4l.toLong, 5l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_163_generate_integers", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Given two positive integers a and b, return the even digits between a\n    // and b, in ascending order.\n    // For example:\n    // >>> generateIntegers((2l), (8l))\n    // (List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))\n    // >>> generateIntegers((8l), (2l))\n    // (List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))\n    // >>> generateIntegers((10l), (14l))\n    // (List[Long]())\n    def generateIntegers(a : Long, b : Long) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(generateIntegers((2l), (10l)).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))));\n    assert(generateIntegers((10l), (2l)).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))));\n    assert(generateIntegers((132l), (2l)).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))));\n    assert(generateIntegers((17l), (89l)).equals((List[Long]())));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_163_generate_integers", "test": "    }\n    def main(args: Array[String]) = {\n    assert(generateIntegers((2l), (10l)).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))));\n    assert(generateIntegers((10l), (2l)).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))));\n    assert(generateIntegers((132l), (2l)).equals((List[Long](2l.toLong, 4l.toLong, 6l.toLong, 8l.toLong))));\n    assert(generateIntegers((17l), (89l)).equals((List[Long]())));\n    }\n\n}\n"}
{"name": "HumanEval_9_rolling_max", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // From a given list of integers, generate a list of rolling maximum element found until given moment\n    // in the sequence.\n    // >>> rollingMax((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 2l.toLong, 3l.toLong, 4l.toLong, 2l.toLong)))\n    // (List[Long](1l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 4l.toLong, 4l.toLong))\n    def rollingMax(numbers : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(rollingMax((List[Long]())).equals((List[Long]())));\n    assert(rollingMax((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(rollingMax((List[Long](4l.toLong, 3l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](4l.toLong, 4l.toLong, 4l.toLong, 4l.toLong))));\n    assert(rollingMax((List[Long](3l.toLong, 2l.toLong, 3l.toLong, 100l.toLong, 3l.toLong))).equals((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 100l.toLong, 100l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_9_rolling_max", "test": "    }\n    def main(args: Array[String]) = {\n    assert(rollingMax((List[Long]())).equals((List[Long]())));\n    assert(rollingMax((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong, 4l.toLong))));\n    assert(rollingMax((List[Long](4l.toLong, 3l.toLong, 2l.toLong, 1l.toLong))).equals((List[Long](4l.toLong, 4l.toLong, 4l.toLong, 4l.toLong))));\n    assert(rollingMax((List[Long](3l.toLong, 2l.toLong, 3l.toLong, 100l.toLong, 3l.toLong))).equals((List[Long](3l.toLong, 3l.toLong, 3l.toLong, 100l.toLong, 100l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_3_below_zero", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You're given a list of deposit and withdrawal operations on a bank account that starts with\n    // zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n    // at that point function should return true. Otherwise it should return false.\n    // >>> belowZero((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (false)\n    // >>> belowZero((List[Long](1l.toLong, 2l.toLong, -4l.toLong, 5l.toLong)))\n    // (true)\n    def belowZero(operations : List[Long]) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(belowZero((List[Long]())) == (false));\n    assert(belowZero((List[Long](1l.toLong, 2l.toLong, -3l.toLong, 1l.toLong, 2l.toLong, -3l.toLong))) == (false));\n    assert(belowZero((List[Long](1l.toLong, 2l.toLong, -4l.toLong, 5l.toLong, 6l.toLong))) == (true));\n    assert(belowZero((List[Long](1l.toLong, -1l.toLong, 2l.toLong, -2l.toLong, 5l.toLong, -5l.toLong, 4l.toLong, -4l.toLong))) == (false));\n    assert(belowZero((List[Long](1l.toLong, -1l.toLong, 2l.toLong, -2l.toLong, 5l.toLong, -5l.toLong, 4l.toLong, -5l.toLong))) == (true));\n    assert(belowZero((List[Long](1l.toLong, -2l.toLong, 2l.toLong, -2l.toLong, 5l.toLong, -5l.toLong, 4l.toLong, -4l.toLong))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_3_below_zero", "test": "    }\n    def main(args: Array[String]) = {\n    assert(belowZero((List[Long]())) == (false));\n    assert(belowZero((List[Long](1l.toLong, 2l.toLong, -3l.toLong, 1l.toLong, 2l.toLong, -3l.toLong))) == (false));\n    assert(belowZero((List[Long](1l.toLong, 2l.toLong, -4l.toLong, 5l.toLong, 6l.toLong))) == (true));\n    assert(belowZero((List[Long](1l.toLong, -1l.toLong, 2l.toLong, -2l.toLong, 5l.toLong, -5l.toLong, 4l.toLong, -4l.toLong))) == (false));\n    assert(belowZero((List[Long](1l.toLong, -1l.toLong, 2l.toLong, -2l.toLong, 5l.toLong, -5l.toLong, 4l.toLong, -5l.toLong))) == (true));\n    assert(belowZero((List[Long](1l.toLong, -2l.toLong, 2l.toLong, -2l.toLong, 5l.toLong, -5l.toLong, 4l.toLong, -4l.toLong))) == (true));\n    }\n\n}\n"}
{"name": "HumanEval_69_search", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    // zero, and has a frequency greater than or equal to the value of the integer itself. \n    // The frequency of an integer is the number of times it appears in the list.\n    // If no such a value exist, return -1.\n    // Examples:\n    // >>> search((List[Long](4l.toLong, 1l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 1l.toLong)))\n    // (2l)\n    // >>> search((List[Long](1l.toLong, 2l.toLong, 2l.toLong, 3l.toLong, 3l.toLong, 3l.toLong, 4l.toLong, 4l.toLong, 4l.toLong)))\n    // (3l)\n    // >>> search((List[Long](5l.toLong, 5l.toLong, 4l.toLong, 4l.toLong, 4l.toLong)))\n    // (-1l)\n    def search(lst : List[Long]) : Long = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(search((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 1l.toLong))) == (1l));\n    assert(search((List[Long](4l.toLong, 1l.toLong, 4l.toLong, 1l.toLong, 4l.toLong, 4l.toLong))) == (4l));\n    assert(search((List[Long](3l.toLong, 3l.toLong))) == (-1l));\n    assert(search((List[Long](8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong))) == (8l));\n    assert(search((List[Long](2l.toLong, 3l.toLong, 3l.toLong, 2l.toLong, 2l.toLong))) == (2l));\n    assert(search((List[Long](2l.toLong, 7l.toLong, 8l.toLong, 8l.toLong, 4l.toLong, 8l.toLong, 7l.toLong, 3l.toLong, 9l.toLong, 6l.toLong, 5l.toLong, 10l.toLong, 4l.toLong, 3l.toLong, 6l.toLong, 7l.toLong, 1l.toLong, 7l.toLong, 4l.toLong, 10l.toLong, 8l.toLong, 1l.toLong))) == (1l));\n    assert(search((List[Long](3l.toLong, 2l.toLong, 8l.toLong, 2l.toLong))) == (2l));\n    assert(search((List[Long](6l.toLong, 7l.toLong, 1l.toLong, 8l.toLong, 8l.toLong, 10l.toLong, 5l.toLong, 8l.toLong, 5l.toLong, 3l.toLong, 10l.toLong))) == (1l));\n    assert(search((List[Long](8l.toLong, 8l.toLong, 3l.toLong, 6l.toLong, 5l.toLong, 6l.toLong, 4l.toLong))) == (-1l));\n    assert(search((List[Long](6l.toLong, 9l.toLong, 6l.toLong, 7l.toLong, 1l.toLong, 4l.toLong, 7l.toLong, 1l.toLong, 8l.toLong, 8l.toLong, 9l.toLong, 8l.toLong, 10l.toLong, 10l.toLong, 8l.toLong, 4l.toLong, 10l.toLong, 4l.toLong, 10l.toLong, 1l.toLong, 2l.toLong, 9l.toLong, 5l.toLong, 7l.toLong, 9l.toLong))) == (1l));\n    assert(search((List[Long](1l.toLong, 9l.toLong, 10l.toLong, 1l.toLong, 3l.toLong))) == (1l));\n    assert(search((List[Long](6l.toLong, 9l.toLong, 7l.toLong, 5l.toLong, 8l.toLong, 7l.toLong, 5l.toLong, 3l.toLong, 7l.toLong, 5l.toLong, 10l.toLong, 10l.toLong, 3l.toLong, 6l.toLong, 10l.toLong, 2l.toLong, 8l.toLong, 6l.toLong, 5l.toLong, 4l.toLong, 9l.toLong, 5l.toLong, 3l.toLong, 10l.toLong))) == (5l));\n    assert(search((List[Long](1l.toLong))) == (1l));\n    assert(search((List[Long](8l.toLong, 8l.toLong, 10l.toLong, 6l.toLong, 4l.toLong, 3l.toLong, 5l.toLong, 8l.toLong, 2l.toLong, 4l.toLong, 2l.toLong, 8l.toLong, 4l.toLong, 6l.toLong, 10l.toLong, 4l.toLong, 2l.toLong, 1l.toLong, 10l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 5l.toLong))) == (4l));\n    assert(search((List[Long](2l.toLong, 10l.toLong, 4l.toLong, 8l.toLong, 2l.toLong, 10l.toLong, 5l.toLong, 1l.toLong, 2l.toLong, 9l.toLong, 5l.toLong, 5l.toLong, 6l.toLong, 3l.toLong, 8l.toLong, 6l.toLong, 4l.toLong, 10l.toLong))) == (2l));\n    assert(search((List[Long](1l.toLong, 6l.toLong, 10l.toLong, 1l.toLong, 6l.toLong, 9l.toLong, 10l.toLong, 8l.toLong, 6l.toLong, 8l.toLong, 7l.toLong, 3l.toLong))) == (1l));\n    assert(search((List[Long](9l.toLong, 2l.toLong, 4l.toLong, 1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong, 2l.toLong, 5l.toLong, 7l.toLong, 7l.toLong, 7l.toLong, 3l.toLong, 10l.toLong, 1l.toLong, 5l.toLong, 4l.toLong, 2l.toLong, 8l.toLong, 4l.toLong, 1l.toLong, 9l.toLong, 10l.toLong, 7l.toLong, 10l.toLong, 2l.toLong, 8l.toLong, 10l.toLong, 9l.toLong, 4l.toLong))) == (4l));\n    assert(search((List[Long](2l.toLong, 6l.toLong, 4l.toLong, 2l.toLong, 8l.toLong, 7l.toLong, 5l.toLong, 6l.toLong, 4l.toLong, 10l.toLong, 4l.toLong, 6l.toLong, 3l.toLong, 7l.toLong, 8l.toLong, 8l.toLong, 3l.toLong, 1l.toLong, 4l.toLong, 2l.toLong, 2l.toLong, 10l.toLong, 7l.toLong))) == (4l));\n    assert(search((List[Long](9l.toLong, 8l.toLong, 6l.toLong, 10l.toLong, 2l.toLong, 6l.toLong, 10l.toLong, 2l.toLong, 7l.toLong, 8l.toLong, 10l.toLong, 3l.toLong, 8l.toLong, 2l.toLong, 6l.toLong, 2l.toLong, 3l.toLong, 1l.toLong))) == (2l));\n    assert(search((List[Long](5l.toLong, 5l.toLong, 3l.toLong, 9l.toLong, 5l.toLong, 6l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 5l.toLong, 6l.toLong, 10l.toLong, 10l.toLong, 6l.toLong, 8l.toLong, 4l.toLong, 10l.toLong, 7l.toLong, 7l.toLong, 10l.toLong, 8l.toLong))) == (-1l));\n    assert(search((List[Long](10l.toLong))) == (-1l));\n    assert(search((List[Long](9l.toLong, 7l.toLong, 7l.toLong, 2l.toLong, 4l.toLong, 7l.toLong, 2l.toLong, 10l.toLong, 9l.toLong, 7l.toLong, 5l.toLong, 7l.toLong, 2l.toLong))) == (2l));\n    assert(search((List[Long](5l.toLong, 4l.toLong, 10l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 10l.toLong, 3l.toLong, 6l.toLong, 1l.toLong, 8l.toLong))) == (1l));\n    assert(search((List[Long](7l.toLong, 9l.toLong, 9l.toLong, 9l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 5l.toLong, 9l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 10l.toLong, 7l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 6l.toLong, 7l.toLong, 7l.toLong, 6l.toLong))) == (1l));\n    assert(search((List[Long](3l.toLong, 10l.toLong, 10l.toLong, 9l.toLong, 2l.toLong))) == (-1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_69_search", "test": "    }\n    def main(args: Array[String]) = {\n    assert(search((List[Long](5l.toLong, 5l.toLong, 5l.toLong, 5l.toLong, 1l.toLong))) == (1l));\n    assert(search((List[Long](4l.toLong, 1l.toLong, 4l.toLong, 1l.toLong, 4l.toLong, 4l.toLong))) == (4l));\n    assert(search((List[Long](3l.toLong, 3l.toLong))) == (-1l));\n    assert(search((List[Long](8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong, 8l.toLong))) == (8l));\n    assert(search((List[Long](2l.toLong, 3l.toLong, 3l.toLong, 2l.toLong, 2l.toLong))) == (2l));\n    assert(search((List[Long](2l.toLong, 7l.toLong, 8l.toLong, 8l.toLong, 4l.toLong, 8l.toLong, 7l.toLong, 3l.toLong, 9l.toLong, 6l.toLong, 5l.toLong, 10l.toLong, 4l.toLong, 3l.toLong, 6l.toLong, 7l.toLong, 1l.toLong, 7l.toLong, 4l.toLong, 10l.toLong, 8l.toLong, 1l.toLong))) == (1l));\n    assert(search((List[Long](3l.toLong, 2l.toLong, 8l.toLong, 2l.toLong))) == (2l));\n    assert(search((List[Long](6l.toLong, 7l.toLong, 1l.toLong, 8l.toLong, 8l.toLong, 10l.toLong, 5l.toLong, 8l.toLong, 5l.toLong, 3l.toLong, 10l.toLong))) == (1l));\n    assert(search((List[Long](8l.toLong, 8l.toLong, 3l.toLong, 6l.toLong, 5l.toLong, 6l.toLong, 4l.toLong))) == (-1l));\n    assert(search((List[Long](6l.toLong, 9l.toLong, 6l.toLong, 7l.toLong, 1l.toLong, 4l.toLong, 7l.toLong, 1l.toLong, 8l.toLong, 8l.toLong, 9l.toLong, 8l.toLong, 10l.toLong, 10l.toLong, 8l.toLong, 4l.toLong, 10l.toLong, 4l.toLong, 10l.toLong, 1l.toLong, 2l.toLong, 9l.toLong, 5l.toLong, 7l.toLong, 9l.toLong))) == (1l));\n    assert(search((List[Long](1l.toLong, 9l.toLong, 10l.toLong, 1l.toLong, 3l.toLong))) == (1l));\n    assert(search((List[Long](6l.toLong, 9l.toLong, 7l.toLong, 5l.toLong, 8l.toLong, 7l.toLong, 5l.toLong, 3l.toLong, 7l.toLong, 5l.toLong, 10l.toLong, 10l.toLong, 3l.toLong, 6l.toLong, 10l.toLong, 2l.toLong, 8l.toLong, 6l.toLong, 5l.toLong, 4l.toLong, 9l.toLong, 5l.toLong, 3l.toLong, 10l.toLong))) == (5l));\n    assert(search((List[Long](1l.toLong))) == (1l));\n    assert(search((List[Long](8l.toLong, 8l.toLong, 10l.toLong, 6l.toLong, 4l.toLong, 3l.toLong, 5l.toLong, 8l.toLong, 2l.toLong, 4l.toLong, 2l.toLong, 8l.toLong, 4l.toLong, 6l.toLong, 10l.toLong, 4l.toLong, 2l.toLong, 1l.toLong, 10l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 5l.toLong))) == (4l));\n    assert(search((List[Long](2l.toLong, 10l.toLong, 4l.toLong, 8l.toLong, 2l.toLong, 10l.toLong, 5l.toLong, 1l.toLong, 2l.toLong, 9l.toLong, 5l.toLong, 5l.toLong, 6l.toLong, 3l.toLong, 8l.toLong, 6l.toLong, 4l.toLong, 10l.toLong))) == (2l));\n    assert(search((List[Long](1l.toLong, 6l.toLong, 10l.toLong, 1l.toLong, 6l.toLong, 9l.toLong, 10l.toLong, 8l.toLong, 6l.toLong, 8l.toLong, 7l.toLong, 3l.toLong))) == (1l));\n    assert(search((List[Long](9l.toLong, 2l.toLong, 4l.toLong, 1l.toLong, 5l.toLong, 1l.toLong, 5l.toLong, 2l.toLong, 5l.toLong, 7l.toLong, 7l.toLong, 7l.toLong, 3l.toLong, 10l.toLong, 1l.toLong, 5l.toLong, 4l.toLong, 2l.toLong, 8l.toLong, 4l.toLong, 1l.toLong, 9l.toLong, 10l.toLong, 7l.toLong, 10l.toLong, 2l.toLong, 8l.toLong, 10l.toLong, 9l.toLong, 4l.toLong))) == (4l));\n    assert(search((List[Long](2l.toLong, 6l.toLong, 4l.toLong, 2l.toLong, 8l.toLong, 7l.toLong, 5l.toLong, 6l.toLong, 4l.toLong, 10l.toLong, 4l.toLong, 6l.toLong, 3l.toLong, 7l.toLong, 8l.toLong, 8l.toLong, 3l.toLong, 1l.toLong, 4l.toLong, 2l.toLong, 2l.toLong, 10l.toLong, 7l.toLong))) == (4l));\n    assert(search((List[Long](9l.toLong, 8l.toLong, 6l.toLong, 10l.toLong, 2l.toLong, 6l.toLong, 10l.toLong, 2l.toLong, 7l.toLong, 8l.toLong, 10l.toLong, 3l.toLong, 8l.toLong, 2l.toLong, 6l.toLong, 2l.toLong, 3l.toLong, 1l.toLong))) == (2l));\n    assert(search((List[Long](5l.toLong, 5l.toLong, 3l.toLong, 9l.toLong, 5l.toLong, 6l.toLong, 3l.toLong, 2l.toLong, 8l.toLong, 5l.toLong, 6l.toLong, 10l.toLong, 10l.toLong, 6l.toLong, 8l.toLong, 4l.toLong, 10l.toLong, 7l.toLong, 7l.toLong, 10l.toLong, 8l.toLong))) == (-1l));\n    assert(search((List[Long](10l.toLong))) == (-1l));\n    assert(search((List[Long](9l.toLong, 7l.toLong, 7l.toLong, 2l.toLong, 4l.toLong, 7l.toLong, 2l.toLong, 10l.toLong, 9l.toLong, 7l.toLong, 5l.toLong, 7l.toLong, 2l.toLong))) == (2l));\n    assert(search((List[Long](5l.toLong, 4l.toLong, 10l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 10l.toLong, 3l.toLong, 6l.toLong, 1l.toLong, 8l.toLong))) == (1l));\n    assert(search((List[Long](7l.toLong, 9l.toLong, 9l.toLong, 9l.toLong, 3l.toLong, 4l.toLong, 1l.toLong, 5l.toLong, 9l.toLong, 1l.toLong, 2l.toLong, 1l.toLong, 1l.toLong, 10l.toLong, 7l.toLong, 5l.toLong, 6l.toLong, 7l.toLong, 6l.toLong, 7l.toLong, 7l.toLong, 6l.toLong))) == (1l));\n    assert(search((List[Long](3l.toLong, 10l.toLong, 10l.toLong, 9l.toLong, 2l.toLong))) == (-1l));\n    }\n\n}\n"}
{"name": "HumanEval_61_correct_bracketing", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // brackets is a string of \"(\" and \")\".\n    // return true if every opening bracket has a corresponding closing bracket.\n    // >>> correctBracketing((\"(\"))\n    // (false)\n    // >>> correctBracketing((\"()\"))\n    // (true)\n    // >>> correctBracketing((\"(()())\"))\n    // (true)\n    // >>> correctBracketing((\")(()\"))\n    // (false)\n    def correctBracketing(brackets : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(correctBracketing((\"()\")) == (true));\n    assert(correctBracketing((\"(()())\")) == (true));\n    assert(correctBracketing((\"()()(()())()\")) == (true));\n    assert(correctBracketing((\"()()((()()())())(()()(()))\")) == (true));\n    assert(correctBracketing((\"((()())))\")) == (false));\n    assert(correctBracketing((\")(()\")) == (false));\n    assert(correctBracketing((\"(\")) == (false));\n    assert(correctBracketing((\"((((\")) == (false));\n    assert(correctBracketing((\")\")) == (false));\n    assert(correctBracketing((\"(()\")) == (false));\n    assert(correctBracketing((\"()()(()())())(()\")) == (false));\n    assert(correctBracketing((\"()()(()())()))()\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_61_correct_bracketing", "test": "    }\n    def main(args: Array[String]) = {\n    assert(correctBracketing((\"()\")) == (true));\n    assert(correctBracketing((\"(()())\")) == (true));\n    assert(correctBracketing((\"()()(()())()\")) == (true));\n    assert(correctBracketing((\"()()((()()())())(()()(()))\")) == (true));\n    assert(correctBracketing((\"((()())))\")) == (false));\n    assert(correctBracketing((\")(()\")) == (false));\n    assert(correctBracketing((\"(\")) == (false));\n    assert(correctBracketing((\"((((\")) == (false));\n    assert(correctBracketing((\")\")) == (false));\n    assert(correctBracketing((\"(()\")) == (false));\n    assert(correctBracketing((\"()()(()())())(()\")) == (false));\n    assert(correctBracketing((\"()()(()())()))()\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_37_sort_even", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // This function takes a list l and returns a list l' such that\n    // l' is identical to l in the odd indicies, while its values at the even indicies are equal\n    // to the values of the even indicies of l, but sorted.\n    // >>> sortEven((List[Long](1l.toLong, 2l.toLong, 3l.toLong)))\n    // (List[Long](1l.toLong, 2l.toLong, 3l.toLong))\n    // >>> sortEven((List[Long](5l.toLong, 6l.toLong, 3l.toLong, 4l.toLong)))\n    // (List[Long](3l.toLong, 6l.toLong, 5l.toLong, 4l.toLong))\n    def sortEven(l : List[Long]) : List[Long] = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sortEven((List[Long](1l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong))));\n    assert(sortEven((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong, 1l.toLong, -10l.toLong))).equals((List[Long](-10l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 5l.toLong, 0l.toLong, 9l.toLong, 1l.toLong, 123l.toLong))));\n    assert(sortEven((List[Long](5l.toLong, 8l.toLong, -12l.toLong, 4l.toLong, 23l.toLong, 2l.toLong, 3l.toLong, 11l.toLong, 12l.toLong, -10l.toLong))).equals((List[Long](-12l.toLong, 8l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 2l.toLong, 12l.toLong, 11l.toLong, 23l.toLong, -10l.toLong))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_37_sort_even", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sortEven((List[Long](1l.toLong, 2l.toLong, 3l.toLong))).equals((List[Long](1l.toLong, 2l.toLong, 3l.toLong))));\n    assert(sortEven((List[Long](5l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 9l.toLong, 0l.toLong, 123l.toLong, 1l.toLong, -10l.toLong))).equals((List[Long](-10l.toLong, 3l.toLong, -5l.toLong, 2l.toLong, -3l.toLong, 3l.toLong, 5l.toLong, 0l.toLong, 9l.toLong, 1l.toLong, 123l.toLong))));\n    assert(sortEven((List[Long](5l.toLong, 8l.toLong, -12l.toLong, 4l.toLong, 23l.toLong, 2l.toLong, 3l.toLong, 11l.toLong, 12l.toLong, -10l.toLong))).equals((List[Long](-12l.toLong, 8l.toLong, 3l.toLong, 4l.toLong, 5l.toLong, 2l.toLong, 12l.toLong, 11l.toLong, 23l.toLong, -10l.toLong))));\n    }\n\n}\n"}
{"name": "HumanEval_54_same_chars", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // Check if two words have the same characters.\n    // >>> sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\"))\n    // (true)\n    // >>> sameChars((\"abcd\"), (\"dddddddabc\"))\n    // (true)\n    // >>> sameChars((\"dddddddabc\"), (\"abcd\"))\n    // (true)\n    // >>> sameChars((\"eabcd\"), (\"dddddddabc\"))\n    // (false)\n    // >>> sameChars((\"abcd\"), (\"dddddddabce\"))\n    // (false)\n    // >>> sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\"))\n    // (false)\n    def sameChars(s0 : String, s1 : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\")) == (true));\n    assert(sameChars((\"abcd\"), (\"dddddddabc\")) == (true));\n    assert(sameChars((\"dddddddabc\"), (\"abcd\")) == (true));\n    assert(sameChars((\"eabcd\"), (\"dddddddabc\")) == (false));\n    assert(sameChars((\"abcd\"), (\"dddddddabcf\")) == (false));\n    assert(sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\")) == (false));\n    assert(sameChars((\"aabb\"), (\"aaccc\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_54_same_chars", "test": "    }\n    def main(args: Array[String]) = {\n    assert(sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\")) == (true));\n    assert(sameChars((\"abcd\"), (\"dddddddabc\")) == (true));\n    assert(sameChars((\"dddddddabc\"), (\"abcd\")) == (true));\n    assert(sameChars((\"eabcd\"), (\"dddddddabc\")) == (false));\n    assert(sameChars((\"abcd\"), (\"dddddddabcf\")) == (false));\n    assert(sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\")) == (false));\n    assert(sameChars((\"aabb\"), (\"aaccc\")) == (false));\n    }\n\n}\n"}
{"name": "HumanEval_56_correct_bracketing", "language": "scala", "prompt": "import scala.math._\nimport scala.collection.mutable._\nobject Problem {\n    // brackets is a string of \"<\" and \">\".\n    // return true if every opening bracket has a corresponding closing bracket.\n    // >>> correctBracketing((\"<\"))\n    // (false)\n    // >>> correctBracketing((\"<>\"))\n    // (true)\n    // >>> correctBracketing((\"<<><>>\"))\n    // (true)\n    // >>> correctBracketing((\"><<>\"))\n    // (false)\n    def correctBracketing(brackets : String) : Boolean = {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "    }\n    def main(args: Array[String]) = {\n    assert(correctBracketing((\"<>\")) == (true));\n    assert(correctBracketing((\"<<><>>\")) == (true));\n    assert(correctBracketing((\"<><><<><>><>\")) == (true));\n    assert(correctBracketing((\"<><><<<><><>><>><<><><<>>>\")) == (true));\n    assert(correctBracketing((\"<<<><>>>>\")) == (false));\n    assert(correctBracketing((\"><<>\")) == (false));\n    assert(correctBracketing((\"<\")) == (false));\n    assert(correctBracketing((\"<<<<\")) == (false));\n    assert(correctBracketing((\">\")) == (false));\n    assert(correctBracketing((\"<<>\")) == (false));\n    assert(correctBracketing((\"<><><<><>><>><<>\")) == (false));\n    assert(correctBracketing((\"<><><<><>><>>><>\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_56_correct_bracketing", "test": "    }\n    def main(args: Array[String]) = {\n    assert(correctBracketing((\"<>\")) == (true));\n    assert(correctBracketing((\"<<><>>\")) == (true));\n    assert(correctBracketing((\"<><><<><>><>\")) == (true));\n    assert(correctBracketing((\"<><><<<><><>><>><<><><<>>>\")) == (true));\n    assert(correctBracketing((\"<<<><>>>>\")) == (false));\n    assert(correctBracketing((\"><<>\")) == (false));\n    assert(correctBracketing((\"<\")) == (false));\n    assert(correctBracketing((\"<<<<\")) == (false));\n    assert(correctBracketing((\">\")) == (false));\n    assert(correctBracketing((\"<<>\")) == (false));\n    assert(correctBracketing((\"<><><<><>><>><<>\")) == (false));\n    assert(correctBracketing((\"<><><<><>><>>><>\")) == (false));\n    }\n\n}\n"}
