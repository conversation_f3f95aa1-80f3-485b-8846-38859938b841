{"name": "HumanEval_23_strlen", "language": "rkt", "prompt": "#lang racket\n\n;; Return length of given string\n;; >>> (strlen \"\")\n;; 0\n;; >>> (strlen \"abc\")\n;; 3\n(define (strlen string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate strlen))\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"x\") 1 0.001)\n    (check-within (candidate \"asdasnakj\") 9 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_23_strlen", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate strlen))\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"x\") 1 0.001)\n    (check-within (candidate \"asdasnakj\") 9 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_89_encrypt", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function encrypt that takes a string as an argument and\n;; returns a string encrypted with the alphabet being rotated. \n;; The alphabet should be rotated in a manner such that the letters \n;; shift down by two multiplied to two places.\n;; For example:\n;; >>> (encrypt \"hi\")\n;; \"lm\"\n;; >>> (encrypt \"asdfghjkl\")\n;; \"ewhjklnop\"\n;; >>> (encrypt \"gf\")\n;; \"kj\"\n;; >>> (encrypt \"et\")\n;; \"ix\"\n(define (encrypt s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate encrypt))\n    (check-within (candidate \"hi\") \"lm\" 0.001)\n    (check-within (candidate \"asdfghjkl\") \"ewhjklnop\" 0.001)\n    (check-within (candidate \"gf\") \"kj\" 0.001)\n    (check-within (candidate \"et\") \"ix\" 0.001)\n    (check-within (candidate \"faewfawefaewg\") \"jeiajeaijeiak\" 0.001)\n    (check-within (candidate \"hellomyfriend\") \"lippsqcjvmirh\" 0.001)\n    (check-within (candidate \"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\" 0.001)\n    (check-within (candidate \"a\") \"e\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_89_encrypt", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate encrypt))\n    (check-within (candidate \"hi\") \"lm\" 0.001)\n    (check-within (candidate \"asdfghjkl\") \"ewhjklnop\" 0.001)\n    (check-within (candidate \"gf\") \"kj\" 0.001)\n    (check-within (candidate \"et\") \"ix\" 0.001)\n    (check-within (candidate \"faewfawefaewg\") \"jeiajeaijeiak\" 0.001)\n    (check-within (candidate \"hellomyfriend\") \"lippsqcjvmirh\" 0.001)\n    (check-within (candidate \"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\" 0.001)\n    (check-within (candidate \"a\") \"e\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_95_check_dict_case", "language": "rkt", "prompt": "#lang racket\n\n;; Given a hash, return #t if all keys are strings in lower \n;; case or all keys are strings in upper case, else return #f.\n;; The function should return #f is the given hash is empty.\n;; Examples:\n;; >>> (check_dict_case #hash((\"a\" .  \"apple\") (\"b\" .  \"banana\")))\n;; #t\n;; >>> (check_dict_case #hash((\"a\" .  \"apple\") (\"A\" .  \"banana\") (\"B\" .  \"banana\")))\n;; #f\n;; >>> (check_dict_case #hash((\"a\" .  \"apple\") (8 .  \"banana\") (\"a\" .  \"apple\")))\n;; #f\n;; >>> (check_dict_case #hash((\"Name\" .  \"John\") (\"Age\" .  \"36\") (\"City\" .  \"Houston\")))\n;; #f\n;; >>> (check_dict_case #hash((\"STATE\" .  \"NC\") (\"ZIP\" .  \"12345\")))\n;; #t\n(define (check_dict_case dict)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate check_dict_case))\n    (check-within (candidate #hash((\"p\" .  \"pineapple\") (\"b\" .  \"banana\"))) #t 0.001)\n    (check-within (candidate #hash((\"p\" .  \"pineapple\") (\"A\" .  \"banana\") (\"B\" .  \"banana\"))) #f 0.001)\n    (check-within (candidate #hash((\"p\" .  \"pineapple\") (\"5\" .  \"banana\") (\"a\" .  \"apple\"))) #f 0.001)\n    (check-within (candidate #hash((\"Name\" .  \"John\") (\"Age\" .  \"36\") (\"City\" .  \"Houston\"))) #f 0.001)\n    (check-within (candidate #hash((\"STATE\" .  \"NC\") (\"ZIP\" .  \"12345\"))) #t 0.001)\n    (check-within (candidate #hash((\"fruit\" .  \"Orange\") (\"taste\" .  \"Sweet\"))) #t 0.001)\n    (check-within (candidate #hash()) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_95_check_dict_case", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate check_dict_case))\n    (check-within (candidate #hash((\"p\" .  \"pineapple\") (\"b\" .  \"banana\"))) #t 0.001)\n    (check-within (candidate #hash((\"p\" .  \"pineapple\") (\"A\" .  \"banana\") (\"B\" .  \"banana\"))) #f 0.001)\n    (check-within (candidate #hash((\"p\" .  \"pineapple\") (\"5\" .  \"banana\") (\"a\" .  \"apple\"))) #f 0.001)\n    (check-within (candidate #hash((\"Name\" .  \"John\") (\"Age\" .  \"36\") (\"City\" .  \"Houston\"))) #f 0.001)\n    (check-within (candidate #hash((\"STATE\" .  \"NC\") (\"ZIP\" .  \"12345\"))) #t 0.001)\n    (check-within (candidate #hash((\"fruit\" .  \"Orange\") (\"taste\" .  \"Sweet\"))) #t 0.001)\n    (check-within (candidate #hash()) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_85_add", "language": "rkt", "prompt": "#lang racket\n\n;; Given a non-empty list of integers lst. add the even elements that are at odd indices..\n;; Examples:\n;; >>> (add (list 4 2 6 7))\n;; 2\n(define (add lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate add))\n    (check-within (candidate (list 4 88)) 88 0.001)\n    (check-within (candidate (list 4 5 6 7 2 122)) 122 0.001)\n    (check-within (candidate (list 4 0 6 7)) 0 0.001)\n    (check-within (candidate (list 4 4 6 8)) 12 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_85_add", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate add))\n    (check-within (candidate (list 4 88)) 88 0.001)\n    (check-within (candidate (list 4 5 6 7 2 122)) 122 0.001)\n    (check-within (candidate (list 4 0 6 7)) 0 0.001)\n    (check-within (candidate (list 4 4 6 8)) 12 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_140_fix_spaces", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string text, replace all spaces in it with underscores, \n;; and if a string has more than 2 consecutive spaces, \n;; then replace all consecutive spaces with - \n;; >>> (fix_spaces \" Example\")\n;; \"Example\"\n;; >>> (fix_spaces \" Example 1\")\n;; \"Example_1\"\n;; >>> (fix_spaces \" Example 2\")\n;; \"_Example_2\"\n;; >>> (fix_spaces \" Example 3\")\n;; \"_Example-3\"\n(define (fix_spaces text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fix_spaces))\n    (check-within (candidate \"Example\") \"Example\" 0.001)\n    (check-within (candidate \"Mudasir Hanif \") \"Mudasir_Hanif_\" 0.001)\n    (check-within (candidate \"Yellow Yellow  Dirty  Fellow\") \"Yellow_Yellow__Dirty__Fellow\" 0.001)\n    (check-within (candidate \"Exa   mple\") \"Exa-mple\" 0.001)\n    (check-within (candidate \"   Exa 1 2 2 mple\") \"-Exa_1_2_2_mple\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_140_fix_spaces", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fix_spaces))\n    (check-within (candidate \"Example\") \"Example\" 0.001)\n    (check-within (candidate \"Mudasir Hanif \") \"Mudasir_Hanif_\" 0.001)\n    (check-within (candidate \"Yellow Yellow  Dirty  Fellow\") \"Yellow_Yellow__Dirty__Fellow\" 0.001)\n    (check-within (candidate \"Exa   mple\") \"Exa-mple\" 0.001)\n    (check-within (candidate \"   Exa 1 2 2 mple\") \"-Exa_1_2_2_mple\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_63_fibfib", "language": "rkt", "prompt": "#lang racket\n\n;; The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n;; fibfib(0) == 0\n;; fibfib(1) == 0\n;; fibfib(2) == 1\n;; fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n;; Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n;; >>> (fibfib 1)\n;; 0\n;; >>> (fibfib 5)\n;; 4\n;; >>> (fibfib 8)\n;; 24\n(define (fibfib n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fibfib))\n    (check-within (candidate 2) 1 0.001)\n    (check-within (candidate 1) 0 0.001)\n    (check-within (candidate 5) 4 0.001)\n    (check-within (candidate 8) 24 0.001)\n    (check-within (candidate 10) 81 0.001)\n    (check-within (candidate 12) 274 0.001)\n    (check-within (candidate 14) 927 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_63_fibfib", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fibfib))\n    (check-within (candidate 2) 1 0.001)\n    (check-within (candidate 1) 0 0.001)\n    (check-within (candidate 5) 4 0.001)\n    (check-within (candidate 8) 24 0.001)\n    (check-within (candidate 10) 81 0.001)\n    (check-within (candidate 12) 274 0.001)\n    (check-within (candidate 14) 927 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_151_double_the_difference", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of numbers, return the sum of squares of the numbers\n;; in the list that are odd. Ignore numbers that are negative or not integers.\n;; >>> (double_the_difference (list 1 3 2 0))\n;; 10\n;; >>> (double_the_difference (list -1 -2 0))\n;; 0\n;; >>> (double_the_difference (list 9 -2))\n;; 81\n;; >>> (double_the_difference (list 0))\n;; 0\n;; If the input list is empty, return 0.\n(define (double_the_difference lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate double_the_difference))\n    (check-within (candidate (list )) 0 0.001)\n    (check-within (candidate (list 5.0 4.0)) 25 0.001)\n    (check-within (candidate (list 0.1 0.2 0.3)) 0 0.001)\n    (check-within (candidate (list -10.0 -20.0 -30.0)) 0 0.001)\n    (check-within (candidate (list -1.0 -2.0 8.0)) 0 0.001)\n    (check-within (candidate (list 0.2 3.0 5.0)) 34 0.001)\n    (check-within (candidate (list -9.0 -7.0 -5.0 -3.0 -1.0 1.0 3.0 5.0 7.0 9.0)) 165 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_151_double_the_difference", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate double_the_difference))\n    (check-within (candidate (list )) 0 0.001)\n    (check-within (candidate (list 5.0 4.0)) 25 0.001)\n    (check-within (candidate (list 0.1 0.2 0.3)) 0 0.001)\n    (check-within (candidate (list -10.0 -20.0 -30.0)) 0 0.001)\n    (check-within (candidate (list -1.0 -2.0 8.0)) 0 0.001)\n    (check-within (candidate (list 0.2 3.0 5.0)) 34 0.001)\n    (check-within (candidate (list -9.0 -7.0 -5.0 -3.0 -1.0 1.0 3.0 5.0 7.0 9.0)) 165 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_22_filter_integers", "language": "rkt", "prompt": "#lang racket\n\n;; Filter given list of any rktthon values only for integers\n;; >>> (filter_integers (list \"a\" 3.14 5))\n;; (list 5)\n;; >>> (filter_integers (list 1 2 3 \"abc\" #hash() (list )))\n;; (list 1 2 3)\n(define (filter_integers values)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate filter_integers))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 4 #hash() (list ) 23.2 9 \"adasd\")) (list 4 9) 0.001)\n    (check-within (candidate (list 3 \"c\" 3 3 \"a\" \"b\")) (list 3 3 3) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_22_filter_integers", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate filter_integers))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 4 #hash() (list ) 23.2 9 \"adasd\")) (list 4 9) 0.001)\n    (check-within (candidate (list 3 \"c\" 3 3 \"a\" \"b\")) (list 3 3 3) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_41_car_race_collision", "language": "rkt", "prompt": "#lang racket\n\n;; Imagine a road that's a perfectly straight infinitely long line.\n;; n cars are driving left to right;  simultaneously, a different set of n cars\n;; are driving right to left.   The two sets of cars start out being very far from\n;; each other.  All cars move in the same speed.  Two cars are said to collide\n;; when a car that's moving left to right hits a car that's moving right to left.\n;; However, the cars are infinitely sturdy and strong; as a result, they continue moving\n;; in their trajectory as if they did not collide.\n;; This function outputs the number of such collisions.\n(define (car_race_collision n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate car_race_collision))\n    (check-within (candidate 2) 4 0.001)\n    (check-within (candidate 3) 9 0.001)\n    (check-within (candidate 4) 16 0.001)\n    (check-within (candidate 8) 64 0.001)\n    (check-within (candidate 10) 100 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_41_car_race_collision", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate car_race_collision))\n    (check-within (candidate 2) 4 0.001)\n    (check-within (candidate 3) 9 0.001)\n    (check-within (candidate 4) 16 0.001)\n    (check-within (candidate 8) 64 0.001)\n    (check-within (candidate 10) 100 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_17_parse_music", "language": "rkt", "prompt": "#lang racket\n\n;; Input to this function is a string representing musical notes in a special ASCII format.\n;; Your task is to parse this string and return list of integers corresponding to how many beats does each\n;; not last.\n;; Here is a legend:\n;; 'o' - whole note, lasts four beats\n;; 'o|' - half note, lasts two beats\n;; '.|' - quater note, lasts one beat\n;; >>> (parse_music \"o o| .| o| o| .| .| .| .| o o\")\n;; (list 4 2 1 2 2 1 1 1 1 4 4)\n(define (parse_music music_string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate parse_music))\n    (check-within (candidate \"\") (list ) 0.001)\n    (check-within (candidate \"o o o o\") (list 4 4 4 4) 0.001)\n    (check-within (candidate \".| .| .| .|\") (list 1 1 1 1) 0.001)\n    (check-within (candidate \"o| o| .| .| o o o o\") (list 2 2 1 1 4 4 4 4) 0.001)\n    (check-within (candidate \"o| .| o| .| o o| o o|\") (list 2 1 2 1 4 2 4 2) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_17_parse_music", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate parse_music))\n    (check-within (candidate \"\") (list ) 0.001)\n    (check-within (candidate \"o o o o\") (list 4 4 4 4) 0.001)\n    (check-within (candidate \".| .| .| .|\") (list 1 1 1 1) 0.001)\n    (check-within (candidate \"o| o| .| .| o o o o\") (list 2 2 1 1 4 4 4 4) 0.001)\n    (check-within (candidate \"o| .| o| .| o o| o o|\") (list 2 1 2 1 4 2 4 2) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_79_decimal_to_binary", "language": "rkt", "prompt": "#lang racket\n\n;; You will be given a number in decimal form and your task is to convert it to\n;; binary format. The function should return a string, with each character representing a binary\n;; number. Each character in the string will be '0' or '1'.\n;; There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n;; The extra characters are there to help with the format.\n;; Examples:\n;; >>> (decimal_to_binary 15)\n;; \"db1111db\"\n;; >>> (decimal_to_binary 32)\n;; \"db100000db\"\n(define (decimal_to_binary decimal)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate decimal_to_binary))\n    (check-within (candidate 0) \"db0db\" 0.001)\n    (check-within (candidate 32) \"db100000db\" 0.001)\n    (check-within (candidate 103) \"db1100111db\" 0.001)\n    (check-within (candidate 15) \"db1111db\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_79_decimal_to_binary", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate decimal_to_binary))\n    (check-within (candidate 0) \"db0db\" 0.001)\n    (check-within (candidate 32) \"db100000db\" 0.001)\n    (check-within (candidate 103) \"db1100111db\" 0.001)\n    (check-within (candidate 15) \"db1111db\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_14_all_prefixes", "language": "rkt", "prompt": "#lang racket\n\n;; Return list of all prefixes from shortest to longest of the input string\n;; >>> (all_prefixes \"abc\")\n;; (list \"a\" \"ab\" \"abc\")\n(define (all_prefixes string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate all_prefixes))\n    (check-within (candidate \"\") (list ) 0.001)\n    (check-within (candidate \"asdfgh\") (list \"a\" \"as\" \"asd\" \"asdf\" \"asdfg\" \"asdfgh\") 0.001)\n    (check-within (candidate \"WWW\") (list \"W\" \"WW\" \"WWW\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_14_all_prefixes", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate all_prefixes))\n    (check-within (candidate \"\") (list ) 0.001)\n    (check-within (candidate \"asdfgh\") (list \"a\" \"as\" \"asd\" \"asdf\" \"asdfg\" \"asdfgh\") 0.001)\n    (check-within (candidate \"WWW\") (list \"W\" \"WW\" \"WWW\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_53_add", "language": "rkt", "prompt": "#lang racket\n\n;; Add two numbers x and y\n;; >>> (add 2 3)\n;; 5\n;; >>> (add 5 7)\n;; 12\n(define (add x y)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate add))\n    (check-within (candidate 0 1) 1 0.001)\n    (check-within (candidate 1 0) 1 0.001)\n    (check-within (candidate 2 3) 5 0.001)\n    (check-within (candidate 5 7) 12 0.001)\n    (check-within (candidate 7 5) 12 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_53_add", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate add))\n    (check-within (candidate 0 1) 1 0.001)\n    (check-within (candidate 1 0) 1 0.001)\n    (check-within (candidate 2 3) 5 0.001)\n    (check-within (candidate 5 7) 12 0.001)\n    (check-within (candidate 7 5) 12 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_159_eat", "language": "rkt", "prompt": "#lang racket\n\n;; You're a hungry rabbit, and you already have eaten a certain number of carrots,\n;; but now you need to eat more carrots to complete the day's meals.\n;; you should return a list of [ total number of eaten carrots after your meals,\n;; the number of carrots left after your meals ]\n;; if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n;; Example:\n;; >>> (eat 5 6 10)\n;; (list 11 4)\n;; >>> (eat 4 8 9)\n;; (list 12 1)\n;; >>> (eat 1 10 10)\n;; (list 11 0)\n;; >>> (eat 2 11 5)\n;; (list 7 0)\n;; Variables:\n;; @number : integer\n;; the number of carrots that you have eaten.\n;; @need : integer\n;; the number of carrots that you need to eat.\n;; @remaining : integer\n;; the number of remaining carrots thet exist in stock\n;; Constrain:\n;; * 0 <= number <= 1000\n;; * 0 <= need <= 1000\n;; * 0 <= remaining <= 1000\n;; Have fun :)\n(define (eat number need remaining)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate eat))\n    (check-within (candidate 5 6 10) (list 11 4) 0.001)\n    (check-within (candidate 4 8 9) (list 12 1) 0.001)\n    (check-within (candidate 1 10 10) (list 11 0) 0.001)\n    (check-within (candidate 2 11 5) (list 7 0) 0.001)\n    (check-within (candidate 4 5 7) (list 9 2) 0.001)\n    (check-within (candidate 4 5 1) (list 5 0) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_159_eat", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate eat))\n    (check-within (candidate 5 6 10) (list 11 4) 0.001)\n    (check-within (candidate 4 8 9) (list 12 1) 0.001)\n    (check-within (candidate 1 10 10) (list 11 0) 0.001)\n    (check-within (candidate 2 11 5) (list 7 0) 0.001)\n    (check-within (candidate 4 5 7) (list 9 2) 0.001)\n    (check-within (candidate 4 5 1) (list 5 0) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_115_max_fill", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a rectangular grid of wells. Each row represents a single well,\n;; and each 1 in a row represents a single unit of water.\n;; Each well has a corresponding bucket that can be used to extract water from it, \n;; and all buckets have the same capacity.\n;; Your task is to use the buckets to empty the wells.\n;; Output the number of times you need to lower the buckets.\n;; Example 1:\n;; >>> (max_fill (list (list 0 0 1 0) (list 0 1 0 0) (list 1 1 1 1)) 1)\n;; 6\n;; Example 2:\n;; >>> (max_fill (list (list 0 0 1 1) (list 0 0 0 0) (list 1 1 1 1) (list 0 1 1 1)) 2)\n;; 5\n;; Example 3:\n;; >>> (max_fill (list (list 0 0 0) (list 0 0 0)) 5)\n;; 0\n;; Constraints:\n;; * all wells have the same length\n;; * 1 <= grid.length <= 10^2\n;; * 1 <= grid[:,1].length <= 10^2\n;; * grid[i][j] -> 0 | 1\n;; * 1 <= capacity <= 10\n(define (max_fill grid capacity)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate max_fill))\n    (check-within (candidate (list (list 0 0 1 0) (list 0 1 0 0) (list 1 1 1 1)) 1) 6 0.001)\n    (check-within (candidate (list (list 0 0 1 1) (list 0 0 0 0) (list 1 1 1 1) (list 0 1 1 1)) 2) 5 0.001)\n    (check-within (candidate (list (list 0 0 0) (list 0 0 0)) 5) 0 0.001)\n    (check-within (candidate (list (list 1 1 1 1) (list 1 1 1 1)) 2) 4 0.001)\n    (check-within (candidate (list (list 1 1 1 1) (list 1 1 1 1)) 9) 2 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_115_max_fill", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate max_fill))\n    (check-within (candidate (list (list 0 0 1 0) (list 0 1 0 0) (list 1 1 1 1)) 1) 6 0.001)\n    (check-within (candidate (list (list 0 0 1 1) (list 0 0 0 0) (list 1 1 1 1) (list 0 1 1 1)) 2) 5 0.001)\n    (check-within (candidate (list (list 0 0 0) (list 0 0 0)) 5) 0 0.001)\n    (check-within (candidate (list (list 1 1 1 1) (list 1 1 1 1)) 2) 4 0.001)\n    (check-within (candidate (list (list 1 1 1 1) (list 1 1 1 1)) 9) 2 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_160_do_algebra", "language": "rkt", "prompt": "#lang racket\n\n;; Given two lists operator, and operand. The first list has basic algebra operations, and \n;; the second list is a list of integers. Use the two given lists to build the algebric \n;; expression and return the evaluation of this expression.\n;; The basic algebra operations:\n;; Addition ( + ) \n;; Subtraction ( - ) \n;; Multiplication ( * ) \n;; Floor division ( // ) \n;; Exponentiation ( ** ) \n;; Example:\n;; operator['+', '*', '-']\n;; list = [2, 3, 4, 5]\n;; result = 2 + 3 * 4 - 5\n;; => result = 9\n;; Note:\n;; The length of operator list is equal to the length of operand list minus one.\n;; Operand is a list of of non-negative integers.\n;; Operator list has at least one operator, and operand list has at least two operands.\n(define (do_algebra operator operand)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate do_algebra))\n    (check-within (candidate (list \"**\" \"*\" \"+\") (list 2 3 4 5)) 37 0.001)\n    (check-within (candidate (list \"+\" \"*\" \"-\") (list 2 3 4 5)) 9 0.001)\n    (check-within (candidate (list \"//\" \"*\") (list 7 3 4)) 8 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_160_do_algebra", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate do_algebra))\n    (check-within (candidate (list \"**\" \"*\" \"+\") (list 2 3 4 5)) 37 0.001)\n    (check-within (candidate (list \"+\" \"*\" \"-\") (list 2 3 4 5)) 9 0.001)\n    (check-within (candidate (list \"//\" \"*\") (list 7 3 4)) 8 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_27_flip_case", "language": "rkt", "prompt": "#lang racket\n\n;; For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n;; >>> (flip_case \"Hello\")\n;; \"hELLO\"\n(define (flip_case string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate flip_case))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"Hello!\") \"hELLO!\" 0.001)\n    (check-within (candidate \"These violent delights have violent ends\") \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_27_flip_case", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate flip_case))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"Hello!\") \"hELLO!\" 0.001)\n    (check-within (candidate \"These violent delights have violent ends\") \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_105_by_length", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of integers, sort the integers that are between 1 and 9 inclusive,\n;; reverse the resulting list, and then replace each digit by its corresponding name from\n;; \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n;; For example:\n;; >>> (by_length (list 2 1 1 4 5 8 2 3))\n;; (list \"Eight\" \"Five\" \"Four\" \"Three\" \"Two\" \"Two\" \"One\" \"One\")\n;; If the list is empty, return an empty list:\n;; >>> (by_length (list ))\n;; (list )\n;; If the list has any strange number ignore it:\n;; >>> (by_length (list 1 -1 55))\n;; (list \"One\")\n(define (by_length arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate by_length))\n    (check-within (candidate (list 2 1 1 4 5 8 2 3)) (list \"Eight\" \"Five\" \"Four\" \"Three\" \"Two\" \"Two\" \"One\" \"One\") 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 -1 55)) (list \"One\") 0.001)\n    (check-within (candidate (list 1 -1 3 2)) (list \"Three\" \"Two\" \"One\") 0.001)\n    (check-within (candidate (list 9 4 8)) (list \"Nine\" \"Eight\" \"Four\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_105_by_length", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate by_length))\n    (check-within (candidate (list 2 1 1 4 5 8 2 3)) (list \"Eight\" \"Five\" \"Four\" \"Three\" \"Two\" \"Two\" \"One\" \"One\") 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 -1 55)) (list \"One\") 0.001)\n    (check-within (candidate (list 1 -1 3 2)) (list \"Three\" \"Two\" \"One\") 0.001)\n    (check-within (candidate (list 9 4 8)) (list \"Nine\" \"Eight\" \"Four\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_25_factorize", "language": "rkt", "prompt": "#lang racket\n\n;; Return list of prime factors of given integer in the order from smallest to largest.\n;; Each of the factors should be listed number of times corresponding to how many times it appeares in factorization.\n;; Input number should be equal to the product of all factors\n;; >>> (factorize 8)\n;; (list 2 2 2)\n;; >>> (factorize 25)\n;; (list 5 5)\n;; >>> (factorize 70)\n;; (list 2 5 7)\n(define (factorize n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate factorize))\n    (check-within (candidate 2) (list 2) 0.001)\n    (check-within (candidate 4) (list 2 2) 0.001)\n    (check-within (candidate 8) (list 2 2 2) 0.001)\n    (check-within (candidate 57) (list 3 19) 0.001)\n    (check-within (candidate 3249) (list 3 3 19 19) 0.001)\n    (check-within (candidate 185193) (list 3 3 3 19 19 19) 0.001)\n    (check-within (candidate 20577) (list 3 19 19 19) 0.001)\n    (check-within (candidate 18) (list 2 3 3) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_25_factorize", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate factorize))\n    (check-within (candidate 2) (list 2) 0.001)\n    (check-within (candidate 4) (list 2 2) 0.001)\n    (check-within (candidate 8) (list 2 2 2) 0.001)\n    (check-within (candidate 57) (list 3 19) 0.001)\n    (check-within (candidate 3249) (list 3 3 19 19) 0.001)\n    (check-within (candidate 185193) (list 3 3 3 19 19 19) 0.001)\n    (check-within (candidate 20577) (list 3 19 19 19) 0.001)\n    (check-within (candidate 18) (list 2 3 3) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_96_count_up_to", "language": "rkt", "prompt": "#lang racket\n\n;; Implement a function that takes an non-negative integer and returns a list of the first n\n;; integers that are prime numbers and less than n.\n;; for example:\n;; >>> (count_up_to 5)\n;; (list 2 3)\n;; >>> (count_up_to 11)\n;; (list 2 3 5 7)\n;; >>> (count_up_to 0)\n;; (list )\n;; >>> (count_up_to 20)\n;; (list 2 3 5 7 11 13 17 19)\n;; >>> (count_up_to 1)\n;; (list )\n;; >>> (count_up_to 18)\n;; (list 2 3 5 7 11 13 17)\n(define (count_up_to n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_up_to))\n    (check-within (candidate 5) (list 2 3) 0.001)\n    (check-within (candidate 6) (list 2 3 5) 0.001)\n    (check-within (candidate 7) (list 2 3 5) 0.001)\n    (check-within (candidate 10) (list 2 3 5 7) 0.001)\n    (check-within (candidate 0) (list ) 0.001)\n    (check-within (candidate 22) (list 2 3 5 7 11 13 17 19) 0.001)\n    (check-within (candidate 1) (list ) 0.001)\n    (check-within (candidate 18) (list 2 3 5 7 11 13 17) 0.001)\n    (check-within (candidate 47) (list 2 3 5 7 11 13 17 19 23 29 31 37 41 43) 0.001)\n    (check-within (candidate 101) (list 2 3 5 7 11 13 17 19 23 29 31 37 41 43 47 53 59 61 67 71 73 79 83 89 97) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_96_count_up_to", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_up_to))\n    (check-within (candidate 5) (list 2 3) 0.001)\n    (check-within (candidate 6) (list 2 3 5) 0.001)\n    (check-within (candidate 7) (list 2 3 5) 0.001)\n    (check-within (candidate 10) (list 2 3 5 7) 0.001)\n    (check-within (candidate 0) (list ) 0.001)\n    (check-within (candidate 22) (list 2 3 5 7 11 13 17 19) 0.001)\n    (check-within (candidate 1) (list ) 0.001)\n    (check-within (candidate 18) (list 2 3 5 7 11 13 17) 0.001)\n    (check-within (candidate 47) (list 2 3 5 7 11 13 17 19 23 29 31 37 41 43) 0.001)\n    (check-within (candidate 101) (list 2 3 5 7 11 13 17 19 23 29 31 37 41 43 47 53 59 61 67 71 73 79 83 89 97) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_34_unique", "language": "rkt", "prompt": "#lang racket\n\n;; Return sorted unique elements in a list\n;; >>> (unique (list 5 3 5 2 3 3 9 0 123))\n;; (list 0 2 3 5 9 123)\n(define (unique l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate unique))\n    (check-within (candidate (list 5 3 5 2 3 3 9 0 123)) (list 0 2 3 5 9 123) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_34_unique", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate unique))\n    (check-within (candidate (list 5 3 5 2 3 3 9 0 123)) (list 0 2 3 5 9 123) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_74_total_match", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that accepts two lists of strings and returns the list that has \n;; total number of chars in the all strings of the list less than the other list.\n;; if the two lists have the same number of chars, return the first list.\n;; Examples\n;; >>> (total_match (list ) (list ))\n;; (list )\n;; >>> (total_match (list \"hi\" \"admin\") (list \"hI\" \"Hi\"))\n;; (list \"hI\" \"Hi\")\n;; >>> (total_match (list \"hi\" \"admin\") (list \"hi\" \"hi\" \"admin\" \"project\"))\n;; (list \"hi\" \"admin\")\n;; >>> (total_match (list \"hi\" \"admin\") (list \"hI\" \"hi\" \"hi\"))\n;; (list \"hI\" \"hi\" \"hi\")\n;; >>> (total_match (list \"4\") (list \"1\" \"2\" \"3\" \"4\" \"5\"))\n;; (list \"4\")\n(define (total_match lst1 lst2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate total_match))\n    (check-within (candidate (list ) (list )) (list ) 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hi\" \"hi\")) (list \"hi\" \"hi\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hi\" \"hi\" \"admin\" \"project\")) (list \"hi\" \"admin\") 0.001)\n    (check-within (candidate (list \"4\") (list \"1\" \"2\" \"3\" \"4\" \"5\")) (list \"4\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hI\" \"Hi\")) (list \"hI\" \"Hi\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hI\" \"hi\" \"hi\")) (list \"hI\" \"hi\" \"hi\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hI\" \"hi\" \"hii\")) (list \"hi\" \"admin\") 0.001)\n    (check-within (candidate (list ) (list \"this\")) (list ) 0.001)\n    (check-within (candidate (list \"this\") (list )) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_74_total_match", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate total_match))\n    (check-within (candidate (list ) (list )) (list ) 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hi\" \"hi\")) (list \"hi\" \"hi\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hi\" \"hi\" \"admin\" \"project\")) (list \"hi\" \"admin\") 0.001)\n    (check-within (candidate (list \"4\") (list \"1\" \"2\" \"3\" \"4\" \"5\")) (list \"4\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hI\" \"Hi\")) (list \"hI\" \"Hi\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hI\" \"hi\" \"hi\")) (list \"hI\" \"hi\" \"hi\") 0.001)\n    (check-within (candidate (list \"hi\" \"admin\") (list \"hI\" \"hi\" \"hii\")) (list \"hi\" \"admin\") 0.001)\n    (check-within (candidate (list ) (list \"this\")) (list ) 0.001)\n    (check-within (candidate (list \"this\") (list )) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_35_max_element", "language": "rkt", "prompt": "#lang racket\n\n;; Return maximum element in the list.\n;; >>> (max_element (list 1 2 3))\n;; 3\n;; >>> (max_element (list 5 3 -5 2 -3 3 9 0 123 1 -10))\n;; 123\n(define (max_element l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate max_element))\n    (check-within (candidate (list 1 2 3)) 3 0.001)\n    (check-within (candidate (list 5 3 -5 2 -3 3 9 0 124 1 -10)) 124 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_35_max_element", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate max_element))\n    (check-within (candidate (list 1 2 3)) 3 0.001)\n    (check-within (candidate (list 5 3 -5 2 -3 3 9 0 124 1 -10)) 124 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_132_is_nested", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function that takes a string as input which contains only square brackets.\n;; The function should return #t if and only if there is a valid subsequence of brackets \n;; where at least one bracket in the subsequence is nested.\n;; >>> (is_nested \"[[]]\")\n;; #t\n;; >>> (is_nested \"[]]]]]]][[[[[]\")\n;; #f\n;; >>> (is_nested \"[][]\")\n;; #f\n;; >>> (is_nested \"[]\")\n;; #f\n;; >>> (is_nested \"[[][]]\")\n;; #t\n;; >>> (is_nested \"[[]][[\")\n;; #t\n(define (is_nested string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_nested))\n    (check-within (candidate \"[[]]\") #t 0.001)\n    (check-within (candidate \"[]]]]]]][[[[[]\") #f 0.001)\n    (check-within (candidate \"[][]\") #f 0.001)\n    (check-within (candidate \"[]\") #f 0.001)\n    (check-within (candidate \"[[[[]]]]\") #t 0.001)\n    (check-within (candidate \"[]]]]]]]]]]\") #f 0.001)\n    (check-within (candidate \"[][][[]]\") #t 0.001)\n    (check-within (candidate \"[[]\") #f 0.001)\n    (check-within (candidate \"[]]\") #f 0.001)\n    (check-within (candidate \"[[]][[\") #t 0.001)\n    (check-within (candidate \"[[][]]\") #t 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"[[[[[[[[\") #f 0.001)\n    (check-within (candidate \"]]]]]]]]\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_132_is_nested", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_nested))\n    (check-within (candidate \"[[]]\") #t 0.001)\n    (check-within (candidate \"[]]]]]]][[[[[]\") #f 0.001)\n    (check-within (candidate \"[][]\") #f 0.001)\n    (check-within (candidate \"[]\") #f 0.001)\n    (check-within (candidate \"[[[[]]]]\") #t 0.001)\n    (check-within (candidate \"[]]]]]]]]]]\") #f 0.001)\n    (check-within (candidate \"[][][[]]\") #t 0.001)\n    (check-within (candidate \"[[]\") #f 0.001)\n    (check-within (candidate \"[]]\") #f 0.001)\n    (check-within (candidate \"[[]][[\") #t 0.001)\n    (check-within (candidate \"[[][]]\") #t 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"[[[[[[[[\") #f 0.001)\n    (check-within (candidate \"]]]]]]]]\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_103_rounded_avg", "language": "rkt", "prompt": "#lang racket\n\n;; You are given two positive integers n and m, and your task is to compute the\n;; average of the integers from n through m (including n and m). \n;; Round the answer to the nearest integer and convert that to binary.\n;; If n is greater than m, return -1.\n;; Example:\n;; >>> (rounded_avg 1 5)\n;; \"0b11\"\n;; >>> (rounded_avg 7 5)\n;; -1\n;; >>> (rounded_avg 10 20)\n;; \"0b1111\"\n;; >>> (rounded_avg 20 33)\n;; \"0b11010\"\n(define (rounded_avg n m)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_103_rounded_avg.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate rounded_avg))\n    (check-within (candidate 1 5) \"0b11\" 0.001)\n    (check-within (candidate 7 13) \"0b1010\" 0.001)\n    (check-within (candidate 964 977) \"0b1111001010\" 0.001)\n    (check-within (candidate 996 997) \"0b1111100100\" 0.001)\n    (check-within (candidate 560 851) \"0b1011000010\" 0.001)\n    (check-within (candidate 185 546) \"0b101101110\" 0.001)\n    (check-within (candidate 362 496) \"0b110101101\" 0.001)\n    (check-within (candidate 350 902) \"0b1001110010\" 0.001)\n    (check-within (candidate 197 233) \"0b11010111\" 0.001)\n    (check-within (candidate 7 5) -1 0.001)\n    (check-within (candidate 5 1) -1 0.001)\n    (check-within (candidate 5 5) \"0b101\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_103_rounded_avg", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate rounded_avg))\n    (check-within (candidate 1 5) \"0b11\" 0.001)\n    (check-within (candidate 7 13) \"0b1010\" 0.001)\n    (check-within (candidate 964 977) \"0b1111001010\" 0.001)\n    (check-within (candidate 996 997) \"0b1111100100\" 0.001)\n    (check-within (candidate 560 851) \"0b1011000010\" 0.001)\n    (check-within (candidate 185 546) \"0b101101110\" 0.001)\n    (check-within (candidate 362 496) \"0b110101101\" 0.001)\n    (check-within (candidate 350 902) \"0b1001110010\" 0.001)\n    (check-within (candidate 197 233) \"0b11010111\" 0.001)\n    (check-within (candidate 7 5) -1 0.001)\n    (check-within (candidate 5 1) -1 0.001)\n    (check-within (candidate 5 5) \"0b101\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_113_odd_count", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of strings, where each string consists of only digits, return a list.\n;; Each element i of the output should be \"the number of odd elements in the\n;; string i of the input.\" where all the i's should be replaced by the number\n;; of odd digits in the i'th string of the input.\n;; >>> (odd_count (list \"1234567\"))\n;; (list \"the number of odd elements 4n the str4ng 4 of the 4nput.\")\n;; >>> (odd_count (list \"3\" \"11111111\"))\n;; (list \"the number of odd elements 1n the str1ng 1 of the 1nput.\" \"the number of odd elements 8n the str8ng 8 of the 8nput.\")\n(define (odd_count lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate odd_count))\n    (check-within (candidate (list \"1234567\")) (list \"the number of odd elements 4n the str4ng 4 of the 4nput.\") 0.001)\n    (check-within (candidate (list \"3\" \"11111111\")) (list \"the number of odd elements 1n the str1ng 1 of the 1nput.\" \"the number of odd elements 8n the str8ng 8 of the 8nput.\") 0.001)\n    (check-within (candidate (list \"271\" \"137\" \"314\")) (list \"the number of odd elements 2n the str2ng 2 of the 2nput.\" \"the number of odd elements 3n the str3ng 3 of the 3nput.\" \"the number of odd elements 2n the str2ng 2 of the 2nput.\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_113_odd_count", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate odd_count))\n    (check-within (candidate (list \"1234567\")) (list \"the number of odd elements 4n the str4ng 4 of the 4nput.\") 0.001)\n    (check-within (candidate (list \"3\" \"11111111\")) (list \"the number of odd elements 1n the str1ng 1 of the 1nput.\" \"the number of odd elements 8n the str8ng 8 of the 8nput.\") 0.001)\n    (check-within (candidate (list \"271\" \"137\" \"314\")) (list \"the number of odd elements 2n the str2ng 2 of the 2nput.\" \"the number of odd elements 3n the str3ng 3 of the 3nput.\" \"the number of odd elements 2n the str2ng 2 of the 2nput.\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_109_move_one_ball", "language": "rkt", "prompt": "#lang racket\n\n;; We have a list 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n;; numbers in the list will be randomly ordered. Your task is to determine if\n;; it is possible to get a list sorted in non-decreasing order by performing \n;; the following operation on the given list:\n;; You are allowed to perform right shift operation any number of times.\n;; One right shift operation means shifting all elements of the list by one\n;; position in the right direction. The last element of the list will be moved to\n;; the starting position in the list i.e. 0th index. \n;; If it is possible to obtain the sorted list by performing the above operation\n;; then return #t else return #f.\n;; If the given list is empty then return #t.\n;; Note: The given list is guaranteed to have unique elements.\n;; For Example:\n;; >>> (move_one_ball (list 3 4 5 1 2))\n;; #t\n;; Explanation: By performin 2 right shift operations, non-decreasing order can\n;; be achieved for the given list.\n;; >>> (move_one_ball (list 3 5 4 1 2))\n;; #f\n;; Explanation:It is not possible to get non-decreasing order for the given\n;; list by performing any number of right shift operations.\n(define (move_one_ball arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate move_one_ball))\n    (check-within (candidate (list 3 4 5 1 2)) #t 0.001)\n    (check-within (candidate (list 3 5 10 1 2)) #t 0.001)\n    (check-within (candidate (list 4 3 1 2)) #f 0.001)\n    (check-within (candidate (list 3 5 4 1 2)) #f 0.001)\n    (check-within (candidate (list )) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_109_move_one_ball", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate move_one_ball))\n    (check-within (candidate (list 3 4 5 1 2)) #t 0.001)\n    (check-within (candidate (list 3 5 10 1 2)) #t 0.001)\n    (check-within (candidate (list 4 3 1 2)) #f 0.001)\n    (check-within (candidate (list 3 5 4 1 2)) #f 0.001)\n    (check-within (candidate (list )) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer n, return a list that has the number of even and odd\n;; integer palindromes that fall within the range(1, n), inclusive.\n;; Example 1:\n;; >>> (even_odd_palindrome 3)\n;; (list 1 2)\n;; Explanation:\n;; Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n;; Example 2:\n;; >>> (even_odd_palindrome 12)\n;; (list 4 6)\n;; Explanation:\n;; Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n;; Note:\n;; 1. 1 <= n <= 10^3\n;; 2. returned list has the number of even and odd integer palindromes respectively.\n(define (even_odd_palindrome n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate even_odd_palindrome))\n    (check-within (candidate 123) (list 8 13) 0.001)\n    (check-within (candidate 12) (list 4 6) 0.001)\n    (check-within (candidate 3) (list 1 2) 0.001)\n    (check-within (candidate 63) (list 6 8) 0.001)\n    (check-within (candidate 25) (list 5 6) 0.001)\n    (check-within (candidate 19) (list 4 6) 0.001)\n    (check-within (candidate 9) (list 4 5) 0.001)\n    (check-within (candidate 1) (list 0 1) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_107_even_odd_palindrome", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate even_odd_palindrome))\n    (check-within (candidate 123) (list 8 13) 0.001)\n    (check-within (candidate 12) (list 4 6) 0.001)\n    (check-within (candidate 3) (list 1 2) 0.001)\n    (check-within (candidate 63) (list 6 8) 0.001)\n    (check-within (candidate 25) (list 5 6) 0.001)\n    (check-within (candidate 19) (list 4 6) 0.001)\n    (check-within (candidate 9) (list 4 5) 0.001)\n    (check-within (candidate 1) (list 0 1) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "rkt", "prompt": "#lang racket\n\n;; Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n;; Example\n;; >>> (is_equal_to_sum_even 4)\n;; #f\n;; >>> (is_equal_to_sum_even 6)\n;; #f\n;; >>> (is_equal_to_sum_even 8)\n;; #t\n(define (is_equal_to_sum_even n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_equal_to_sum_even))\n    (check-within (candidate 4) #f 0.001)\n    (check-within (candidate 6) #f 0.001)\n    (check-within (candidate 8) #t 0.001)\n    (check-within (candidate 10) #t 0.001)\n    (check-within (candidate 11) #f 0.001)\n    (check-within (candidate 12) #t 0.001)\n    (check-within (candidate 13) #f 0.001)\n    (check-within (candidate 16) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_equal_to_sum_even))\n    (check-within (candidate 4) #f 0.001)\n    (check-within (candidate 6) #f 0.001)\n    (check-within (candidate 8) #t 0.001)\n    (check-within (candidate 10) #t 0.001)\n    (check-within (candidate 11) #f 0.001)\n    (check-within (candidate 12) #t 0.001)\n    (check-within (candidate 13) #f 0.001)\n    (check-within (candidate 16) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_62_derivative", "language": "rkt", "prompt": "#lang racket\n\n;; xs represent coefficients of a polynomial.\n;; xs[0] + xs[1] * x + xs[2] * x^2 + ....\n;; Return derivative of this polynomial in the same form.\n;; >>> (derivative (list 3 1 2 4 5))\n;; (list 1 4 12 20)\n;; >>> (derivative (list 1 2 3))\n;; (list 2 6)\n(define (derivative xs)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate derivative))\n    (check-within (candidate (list 3 1 2 4 5)) (list 1 4 12 20) 0.001)\n    (check-within (candidate (list 1 2 3)) (list 2 6) 0.001)\n    (check-within (candidate (list 3 2 1)) (list 2 2) 0.001)\n    (check-within (candidate (list 3 2 1 0 4)) (list 2 2 0 16) 0.001)\n    (check-within (candidate (list 1)) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_62_derivative", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate derivative))\n    (check-within (candidate (list 3 1 2 4 5)) (list 1 4 12 20) 0.001)\n    (check-within (candidate (list 1 2 3)) (list 2 6) 0.001)\n    (check-within (candidate (list 3 2 1)) (list 2 2) 0.001)\n    (check-within (candidate (list 3 2 1 0 4)) (list 2 2 0 16) 0.001)\n    (check-within (candidate (list 1)) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_126_is_sorted", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of numbers, return whether or not they are sorted\n;; in ascending order. If list has more than 1 duplicate of the same\n;; number, return #f. Assume no negative numbers and only integers.\n;; Examples\n;; >>> (is_sorted (list 5))\n;; #t\n;; >>> (is_sorted (list 1 2 3 4 5))\n;; #t\n;; >>> (is_sorted (list 1 3 2 4 5))\n;; #f\n;; >>> (is_sorted (list 1 2 3 4 5 6))\n;; #t\n;; >>> (is_sorted (list 1 2 3 4 5 6 7))\n;; #t\n;; >>> (is_sorted (list 1 3 2 4 5 6 7))\n;; #f\n;; >>> (is_sorted (list 1 2 2 3 3 4))\n;; #t\n;; >>> (is_sorted (list 1 2 2 2 3 4))\n;; #f\n(define (is_sorted lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_sorted))\n    (check-within (candidate (list 5)) #t 0.001)\n    (check-within (candidate (list 1 2 3 4 5)) #t 0.001)\n    (check-within (candidate (list 1 3 2 4 5)) #f 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6)) #t 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6 7)) #t 0.001)\n    (check-within (candidate (list 1 3 2 4 5 6 7)) #f 0.001)\n    (check-within (candidate (list )) #t 0.001)\n    (check-within (candidate (list 1)) #t 0.001)\n    (check-within (candidate (list 3 2 1)) #f 0.001)\n    (check-within (candidate (list 1 2 2 2 3 4)) #f 0.001)\n    (check-within (candidate (list 1 2 3 3 3 4)) #f 0.001)\n    (check-within (candidate (list 1 2 2 3 3 4)) #t 0.001)\n    (check-within (candidate (list 1 2 3 4)) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_126_is_sorted", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_sorted))\n    (check-within (candidate (list 5)) #t 0.001)\n    (check-within (candidate (list 1 2 3 4 5)) #t 0.001)\n    (check-within (candidate (list 1 3 2 4 5)) #f 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6)) #t 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6 7)) #t 0.001)\n    (check-within (candidate (list 1 3 2 4 5 6 7)) #f 0.001)\n    (check-within (candidate (list )) #t 0.001)\n    (check-within (candidate (list 1)) #t 0.001)\n    (check-within (candidate (list 3 2 1)) #f 0.001)\n    (check-within (candidate (list 1 2 2 2 3 4)) #f 0.001)\n    (check-within (candidate (list 1 2 3 3 3 4)) #f 0.001)\n    (check-within (candidate (list 1 2 2 3 3 4)) #t 0.001)\n    (check-within (candidate (list 1 2 3 4)) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_161_solve", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a string s.\n;; if s[i] is a letter, reverse its case from lower to upper or vise versa, \n;; otherwise keep it as it is.\n;; If the string contains no letters, reverse the string.\n;; The function should return the resulted string.\n;; Examples\n;; >>> (solve \"1234\")\n;; \"4321\"\n;; >>> (solve \"ab\")\n;; \"AB\"\n;; >>> (solve \"#a@C\")\n;; \"#A@c\"\n(define (solve s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate solve))\n    (check-within (candidate \"AsDf\") \"aSdF\" 0.001)\n    (check-within (candidate \"1234\") \"4321\" 0.001)\n    (check-within (candidate \"ab\") \"AB\" 0.001)\n    (check-within (candidate \"#a@C\") \"#A@c\" 0.001)\n    (check-within (candidate \"#AsdfW^45\") \"#aSDFw^45\" 0.001)\n    (check-within (candidate \"#6@2\") \"2@6#\" 0.001)\n    (check-within (candidate \"#$a^D\") \"#$A^d\" 0.001)\n    (check-within (candidate \"#ccc\") \"#CCC\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_161_solve", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate solve))\n    (check-within (candidate \"AsDf\") \"aSdF\" 0.001)\n    (check-within (candidate \"1234\") \"4321\" 0.001)\n    (check-within (candidate \"ab\") \"AB\" 0.001)\n    (check-within (candidate \"#a@C\") \"#A@c\" 0.001)\n    (check-within (candidate \"#AsdfW^45\") \"#aSDFw^45\" 0.001)\n    (check-within (candidate \"#6@2\") \"2@6#\" 0.001)\n    (check-within (candidate \"#$a^D\") \"#$A^d\" 0.001)\n    (check-within (candidate \"#ccc\") \"#CCC\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_130_tri", "language": "rkt", "prompt": "#lang racket\n\n;; Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n;; the last couple centuries. However, what people don't know is Tribonacci sequence.\n;; Tribonacci sequence is defined by the recurrence:\n;; tri(1) = 3\n;; tri(n) = 1 + n / 2, if n is even.\n;; tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n;; For example:\n;; tri(2) = 1 + (2 / 2) = 2\n;; tri(4) = 3\n;; tri(3) = tri(2) + tri(1) + tri(4)\n;; = 2 + 3 + 3 = 8 \n;; You are given a non-negative integer number n, you have to a return a list of the \n;; first n + 1 numbers of the Tribonacci sequence.\n;; Examples:\n;; >>> (tri 3)\n;; (list 1 3 2 8)\n(define (tri n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate tri))\n    (check-within (candidate 3) (list 1 3 2 8) 0.001)\n    (check-within (candidate 4) (list 1 3 2 8 3) 0.001)\n    (check-within (candidate 5) (list 1 3 2 8 3 15) 0.001)\n    (check-within (candidate 6) (list 1 3 2 8 3 15 4) 0.001)\n    (check-within (candidate 7) (list 1 3 2 8 3 15 4 24) 0.001)\n    (check-within (candidate 8) (list 1 3 2 8 3 15 4 24 5) 0.001)\n    (check-within (candidate 9) (list 1 3 2 8 3 15 4 24 5 35) 0.001)\n    (check-within (candidate 20) (list 1 3 2 8 3 15 4 24 5 35 6 48 7 63 8 80 9 99 10 120 11) 0.001)\n    (check-within (candidate 0) (list 1) 0.001)\n    (check-within (candidate 1) (list 1 3) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_130_tri", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate tri))\n    (check-within (candidate 3) (list 1 3 2 8) 0.001)\n    (check-within (candidate 4) (list 1 3 2 8 3) 0.001)\n    (check-within (candidate 5) (list 1 3 2 8 3 15) 0.001)\n    (check-within (candidate 6) (list 1 3 2 8 3 15 4) 0.001)\n    (check-within (candidate 7) (list 1 3 2 8 3 15 4 24) 0.001)\n    (check-within (candidate 8) (list 1 3 2 8 3 15 4 24 5) 0.001)\n    (check-within (candidate 9) (list 1 3 2 8 3 15 4 24 5 35) 0.001)\n    (check-within (candidate 20) (list 1 3 2 8 3 15 4 24 5 35 6 48 7 63 8 80 9 99 10 120 11) 0.001)\n    (check-within (candidate 0) (list 1) 0.001)\n    (check-within (candidate 1) (list 1 3) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_36_fizz_buzz", "language": "rkt", "prompt": "#lang racket\n\n;; Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n;; >>> (fizz_buzz 50)\n;; 0\n;; >>> (fizz_buzz 78)\n;; 2\n;; >>> (fizz_buzz 79)\n;; 3\n(define (fizz_buzz n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fizz_buzz))\n    (check-within (candidate 50) 0 0.001)\n    (check-within (candidate 78) 2 0.001)\n    (check-within (candidate 79) 3 0.001)\n    (check-within (candidate 100) 3 0.001)\n    (check-within (candidate 200) 6 0.001)\n    (check-within (candidate 4000) 192 0.001)\n    (check-within (candidate 10000) 639 0.001)\n    (check-within (candidate 100000) 8026 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_36_fizz_buzz", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fizz_buzz))\n    (check-within (candidate 50) 0 0.001)\n    (check-within (candidate 78) 2 0.001)\n    (check-within (candidate 79) 3 0.001)\n    (check-within (candidate 100) 3 0.001)\n    (check-within (candidate 200) 6 0.001)\n    (check-within (candidate 4000) 192 0.001)\n    (check-within (candidate 10000) 639 0.001)\n    (check-within (candidate 100000) 8026 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_29_filter_by_prefix", "language": "rkt", "prompt": "#lang racket\n\n;; Filter an input list of strings only for ones that start with a given prefix.\n;; >>> (filter_by_prefix (list ) \"a\")\n;; (list )\n;; >>> (filter_by_prefix (list \"abc\" \"bcd\" \"cde\" \"array\") \"a\")\n;; (list \"abc\" \"array\")\n(define (filter_by_prefix strings prefix)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate filter_by_prefix))\n    (check-within (candidate (list ) \"john\") (list ) 0.001)\n    (check-within (candidate (list \"xxx\" \"asd\" \"xxy\" \"john doe\" \"xxxAAA\" \"xxx\") \"xxx\") (list \"xxx\" \"xxxAAA\" \"xxx\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_29_filter_by_prefix", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate filter_by_prefix))\n    (check-within (candidate (list ) \"john\") (list ) 0.001)\n    (check-within (candidate (list \"xxx\" \"asd\" \"xxy\" \"john doe\" \"xxxAAA\" \"xxx\") \"xxx\") (list \"xxx\" \"xxxAAA\" \"xxx\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_84_solve", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer N, return the total sum of its digits in binary.\n;; Example\n;; >>> (solve 1000)\n;; \"1\"\n;; >>> (solve 150)\n;; \"110\"\n;; >>> (solve 147)\n;; \"1100\"\n;; Variables:\n;; @N integer\n;; Constraints: 0 \u2264 N \u2264 10000.\n;; Output:\n;; a string of binary number\n(define (solve N)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate solve))\n    (check-within (candidate 1000) \"1\" 0.001)\n    (check-within (candidate 150) \"110\" 0.001)\n    (check-within (candidate 147) \"1100\" 0.001)\n    (check-within (candidate 333) \"1001\" 0.001)\n    (check-within (candidate 963) \"10010\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_84_solve", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate solve))\n    (check-within (candidate 1000) \"1\" 0.001)\n    (check-within (candidate 150) \"110\" 0.001)\n    (check-within (candidate 147) \"1100\" 0.001)\n    (check-within (candidate 333) \"1001\" 0.001)\n    (check-within (candidate 963) \"10010\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_129_minPath", "language": "rkt", "prompt": "#lang racket\n\n;; Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n;; each cell of the grid contains a value. Every integer in the range [1, N * N]\n;; inclusive appears exactly once on the cells of the grid.\n;; You have to find the minimum path of length k in the grid. You can start\n;; from any cell, and in each step you can move to any of the neighbor cells,\n;; in other words, you can go to cells which share an edge with you current\n;; cell.\n;; Please note that a path of length k means visiting exactly k cells (not\n;; necessarily distinct).\n;; You CANNOT go off the grid.\n;; A path A (of length k) is considered less than a path B (of length k) if\n;; after making the ordered lists of the values on the cells that A and B go\n;; through (let's call them lst_A and lst_B), lst_A is lexicographically less\n;; than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n;; such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n;; lst_A[j] = lst_B[j].\n;; It is guaranteed that the answer is unique.\n;; Return an ordered list of the values on the cells that the minimum path go through.\n;; Examples:    \n;; >>> (minPath (list (list 1 2 3) (list 4 5 6) (list 7 8 9)) 3)\n;; (list 1 2 1)\n;; >>> (minPath (list (list 5 9 3) (list 4 1 6) (list 7 8 2)) 1)\n;; (list 1)\n(define (minPath grid k)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate minPath))\n    (check-within (candidate (list (list 1 2 3) (list 4 5 6) (list 7 8 9)) 3) (list 1 2 1) 0.001)\n    (check-within (candidate (list (list 5 9 3) (list 4 1 6) (list 7 8 2)) 1) (list 1) 0.001)\n    (check-within (candidate (list (list 1 2 3 4) (list 5 6 7 8) (list 9 10 11 12) (list 13 14 15 16)) 4) (list 1 2 1 2) 0.001)\n    (check-within (candidate (list (list 6 4 13 10) (list 5 7 12 1) (list 3 16 11 15) (list 8 14 9 2)) 7) (list 1 10 1 10 1 10 1) 0.001)\n    (check-within (candidate (list (list 8 14 9 2) (list 6 4 13 15) (list 5 7 1 12) (list 3 10 11 16)) 5) (list 1 7 1 7 1) 0.001)\n    (check-within (candidate (list (list 11 8 7 2) (list 5 16 14 4) (list 9 3 15 6) (list 12 13 10 1)) 9) (list 1 6 1 6 1 6 1 6 1) 0.001)\n    (check-within (candidate (list (list 12 13 10 1) (list 9 3 15 6) (list 5 16 14 4) (list 11 8 7 2)) 12) (list 1 6 1 6 1 6 1 6 1 6 1 6) 0.001)\n    (check-within (candidate (list (list 2 7 4) (list 3 1 5) (list 6 8 9)) 8) (list 1 3 1 3 1 3 1 3) 0.001)\n    (check-within (candidate (list (list 6 1 5) (list 3 8 9) (list 2 7 4)) 8) (list 1 5 1 5 1 5 1 5) 0.001)\n    (check-within (candidate (list (list 1 2) (list 3 4)) 10) (list 1 2 1 2 1 2 1 2 1 2) 0.001)\n    (check-within (candidate (list (list 1 3) (list 3 2)) 10) (list 1 3 1 3 1 3 1 3 1 3) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_129_minPath", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate minPath))\n    (check-within (candidate (list (list 1 2 3) (list 4 5 6) (list 7 8 9)) 3) (list 1 2 1) 0.001)\n    (check-within (candidate (list (list 5 9 3) (list 4 1 6) (list 7 8 2)) 1) (list 1) 0.001)\n    (check-within (candidate (list (list 1 2 3 4) (list 5 6 7 8) (list 9 10 11 12) (list 13 14 15 16)) 4) (list 1 2 1 2) 0.001)\n    (check-within (candidate (list (list 6 4 13 10) (list 5 7 12 1) (list 3 16 11 15) (list 8 14 9 2)) 7) (list 1 10 1 10 1 10 1) 0.001)\n    (check-within (candidate (list (list 8 14 9 2) (list 6 4 13 15) (list 5 7 1 12) (list 3 10 11 16)) 5) (list 1 7 1 7 1) 0.001)\n    (check-within (candidate (list (list 11 8 7 2) (list 5 16 14 4) (list 9 3 15 6) (list 12 13 10 1)) 9) (list 1 6 1 6 1 6 1 6 1) 0.001)\n    (check-within (candidate (list (list 12 13 10 1) (list 9 3 15 6) (list 5 16 14 4) (list 11 8 7 2)) 12) (list 1 6 1 6 1 6 1 6 1 6 1 6) 0.001)\n    (check-within (candidate (list (list 2 7 4) (list 3 1 5) (list 6 8 9)) 8) (list 1 3 1 3 1 3 1 3) 0.001)\n    (check-within (candidate (list (list 6 1 5) (list 3 8 9) (list 2 7 4)) 8) (list 1 5 1 5 1 5 1 5) 0.001)\n    (check-within (candidate (list (list 1 2) (list 3 4)) 10) (list 1 2 1 2 1 2 1 2 1 2) 0.001)\n    (check-within (candidate (list (list 1 3) (list 3 2)) 10) (list 1 3 1 3 1 3 1 3 1 3) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_98_count_upper", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string s, count the number of uppercase vowels in even indices.\n;; For example:\n;; >>> (count_upper \"aBCdEf\")\n;; 1\n;; >>> (count_upper \"abcdefg\")\n;; 0\n;; >>> (count_upper \"dBBE\")\n;; 0\n(define (count_upper s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_upper))\n    (check-within (candidate \"aBCdEf\") 1 0.001)\n    (check-within (candidate \"abcdefg\") 0 0.001)\n    (check-within (candidate \"dBBE\") 0 0.001)\n    (check-within (candidate \"B\") 0 0.001)\n    (check-within (candidate \"U\") 1 0.001)\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"EEEE\") 2 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_98_count_upper", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_upper))\n    (check-within (candidate \"aBCdEf\") 1 0.001)\n    (check-within (candidate \"abcdefg\") 0 0.001)\n    (check-within (candidate \"dBBE\") 0 0.001)\n    (check-within (candidate \"B\") 0 0.001)\n    (check-within (candidate \"U\") 1 0.001)\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"EEEE\") 2 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_120_maximum", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list arr of integers and a positive integer k, return a sorted list \n;; of length k with the maximum k numbers in arr.\n;; Example 1:\n;; >>> (maximum (list -3 -4 5) 3)\n;; (list -4 -3 5)\n;; Example 2:\n;; >>> (maximum (list 4 -4 4) 2)\n;; (list 4 4)\n;; Example 3:\n;; >>> (maximum (list -3 2 1 2 -1 -2 1) 1)\n;; (list 2)\n;; Note:\n;; 1. The length of the list will be in the range of [1, 1000].\n;; 2. The elements in the list will be in the range of [-1000, 1000].\n;; 3. 0 <= k <= len(arr)\n(define (maximum arr k)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate maximum))\n    (check-within (candidate (list -3 -4 5) 3) (list -4 -3 5) 0.001)\n    (check-within (candidate (list 4 -4 4) 2) (list 4 4) 0.001)\n    (check-within (candidate (list -3 2 1 2 -1 -2 1) 1) (list 2) 0.001)\n    (check-within (candidate (list 123 -123 20 0 1 2 -3) 3) (list 2 20 123) 0.001)\n    (check-within (candidate (list -123 20 0 1 2 -3) 4) (list 0 1 2 20) 0.001)\n    (check-within (candidate (list 5 15 0 3 -13 -8 0) 7) (list -13 -8 0 0 3 5 15) 0.001)\n    (check-within (candidate (list -1 0 2 5 3 -10) 2) (list 3 5) 0.001)\n    (check-within (candidate (list 1 0 5 -7) 1) (list 5) 0.001)\n    (check-within (candidate (list 4 -4) 2) (list -4 4) 0.001)\n    (check-within (candidate (list -10 10) 2) (list -10 10) 0.001)\n    (check-within (candidate (list 1 2 3 -23 243 -400 0) 0) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_120_maximum", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate maximum))\n    (check-within (candidate (list -3 -4 5) 3) (list -4 -3 5) 0.001)\n    (check-within (candidate (list 4 -4 4) 2) (list 4 4) 0.001)\n    (check-within (candidate (list -3 2 1 2 -1 -2 1) 1) (list 2) 0.001)\n    (check-within (candidate (list 123 -123 20 0 1 2 -3) 3) (list 2 20 123) 0.001)\n    (check-within (candidate (list -123 20 0 1 2 -3) 4) (list 0 1 2 20) 0.001)\n    (check-within (candidate (list 5 15 0 3 -13 -8 0) 7) (list -13 -8 0 0 3 5 15) 0.001)\n    (check-within (candidate (list -1 0 2 5 3 -10) 2) (list 3 5) 0.001)\n    (check-within (candidate (list 1 0 5 -7) 1) (list 5) 0.001)\n    (check-within (candidate (list 4 -4) 2) (list -4 4) 0.001)\n    (check-within (candidate (list -10 10) 2) (list -10 10) 0.001)\n    (check-within (candidate (list 1 2 3 -23 243 -400 0) 0) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_24_largest_divisor", "language": "rkt", "prompt": "#lang racket\n\n;; For a given number n, find the largest number that divides n evenly, smaller than n\n;; >>> (largest_divisor 15)\n;; 5\n(define (largest_divisor n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate largest_divisor))\n    (check-within (candidate 3) 1 0.001)\n    (check-within (candidate 7) 1 0.001)\n    (check-within (candidate 10) 5 0.001)\n    (check-within (candidate 100) 50 0.001)\n    (check-within (candidate 49) 7 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_24_largest_divisor", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate largest_divisor))\n    (check-within (candidate 3) 1 0.001)\n    (check-within (candidate 7) 1 0.001)\n    (check-within (candidate 10) 5 0.001)\n    (check-within (candidate 100) 50 0.001)\n    (check-within (candidate 49) 7 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_88_sort_array", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of non-negative integers, return a corkt of the given list after sorting,\n;; you will sort the given list in ascending order if the sum( first index value, last index value) is odd,\n;; or sort it in descending order if the sum( first index value, last index value) is even.\n;; Note:\n;; * don't change the given list.\n;; Examples:\n;; >>> (sort_array (list ))\n;; (list )\n;; >>> (sort_array (list 5))\n;; (list 5)\n;; >>> (sort_array (list 2 4 3 0 1 5))\n;; (list 0 1 2 3 4 5)\n;; >>> (sort_array (list 2 4 3 0 1 5 6))\n;; (list 6 5 4 3 2 1 0)\n(define (sort_array array)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_array))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 5)) (list 5) 0.001)\n    (check-within (candidate (list 2 4 3 0 1 5)) (list 0 1 2 3 4 5) 0.001)\n    (check-within (candidate (list 2 4 3 0 1 5 6)) (list 6 5 4 3 2 1 0) 0.001)\n    (check-within (candidate (list 2 1)) (list 1 2) 0.001)\n    (check-within (candidate (list 15 42 87 32 11 0)) (list 0 11 15 32 42 87) 0.001)\n    (check-within (candidate (list 21 14 23 11)) (list 23 21 14 11) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_88_sort_array", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_array))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 5)) (list 5) 0.001)\n    (check-within (candidate (list 2 4 3 0 1 5)) (list 0 1 2 3 4 5) 0.001)\n    (check-within (candidate (list 2 4 3 0 1 5 6)) (list 6 5 4 3 2 1 0) 0.001)\n    (check-within (candidate (list 2 1)) (list 1 2) 0.001)\n    (check-within (candidate (list 15 42 87 32 11 0)) (list 0 11 15 32 42 87) 0.001)\n    (check-within (candidate (list 21 14 23 11)) (list 23 21 14 11) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_106_f", "language": "rkt", "prompt": "#lang racket\n\n;; Implement the function f that takes n as a parameter,\n;; and returns a list of size n, such that the value of the element at index i is the factorial of i if i is even\n;; or the sum of numbers from 1 to i otherwise.\n;; i starts from 1.\n;; the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n;; Example:\n;; >>> (f 5)\n;; (list 1 2 6 24 15)\n(define (f n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate f))\n    (check-within (candidate 5) (list 1 2 6 24 15) 0.001)\n    (check-within (candidate 7) (list 1 2 6 24 15 720 28) 0.001)\n    (check-within (candidate 1) (list 1) 0.001)\n    (check-within (candidate 3) (list 1 2 6) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_106_f", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate f))\n    (check-within (candidate 5) (list 1 2 6 24 15) 0.001)\n    (check-within (candidate 7) (list 1 2 6 24 15 720 28) 0.001)\n    (check-within (candidate 1) (list 1) 0.001)\n    (check-within (candidate 3) (list 1 2 6) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_77_iscube", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that takes an integer a and returns #t \n;; if this ingeger is a cube of some integer number.\n;; Note: you may assume the input is always valid.\n;; Examples:\n;; >>> (iscube 1)\n;; #t\n;; >>> (iscube 2)\n;; #f\n;; >>> (iscube -1)\n;; #t\n;; >>> (iscube 64)\n;; #t\n;; >>> (iscube 0)\n;; #t\n;; >>> (iscube 180)\n;; #f\n(define (iscube a)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate iscube))\n    (check-within (candidate 1) #t 0.001)\n    (check-within (candidate 2) #f 0.001)\n    (check-within (candidate -1) #t 0.001)\n    (check-within (candidate 64) #t 0.001)\n    (check-within (candidate 180) #f 0.001)\n    (check-within (candidate 1000) #t 0.001)\n    (check-within (candidate 0) #t 0.001)\n    (check-within (candidate 1729) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_77_iscube", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate iscube))\n    (check-within (candidate 1) #t 0.001)\n    (check-within (candidate 2) #f 0.001)\n    (check-within (candidate -1) #t 0.001)\n    (check-within (candidate 64) #t 0.001)\n    (check-within (candidate 180) #f 0.001)\n    (check-within (candidate 1000) #t 0.001)\n    (check-within (candidate 0) #t 0.001)\n    (check-within (candidate 1729) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_93_encode", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that takes a message, and encodes in such a \n;; way that it swaps case of all letters, replaces all vowels in \n;; the message with the letter that appears 2 places ahead of that \n;; vowel in the english alphabet. \n;; Assume only letters. \n;; Examples:\n;; >>> (encode \"test\")\n;; \"TGST\"\n;; >>> (encode \"This is a message\")\n;; \"tHKS KS C MGSSCGG\"\n(define (encode message)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate encode))\n    (check-within (candidate \"TEST\") \"tgst\" 0.001)\n    (check-within (candidate \"Mudasir\") \"mWDCSKR\" 0.001)\n    (check-within (candidate \"YES\") \"ygs\" 0.001)\n    (check-within (candidate \"This is a message\") \"tHKS KS C MGSSCGG\" 0.001)\n    (check-within (candidate \"I DoNt KnOw WhAt tO WrItE\") \"k dQnT kNqW wHcT Tq wRkTg\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_93_encode", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate encode))\n    (check-within (candidate \"TEST\") \"tgst\" 0.001)\n    (check-within (candidate \"Mudasir\") \"mWDCSKR\" 0.001)\n    (check-within (candidate \"YES\") \"ygs\" 0.001)\n    (check-within (candidate \"This is a message\") \"tHKS KS C MGSSCGG\" 0.001)\n    (check-within (candidate \"I DoNt KnOw WhAt tO WrItE\") \"k dQnT kNqW wHcT Tq wRkTg\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_91_is_bored", "language": "rkt", "prompt": "#lang racket\n\n;; You'll be given a string of words, and your task is to count the number\n;; of boredoms. A boredom is a sentence that starts with the word \"I\".\n;; Sentences are delimited by '.', '?' or '!'.\n;; For example:\n;; >>> (is_bored \"Hello world\")\n;; 0\n;; >>> (is_bored \"The sky is blue. The sun is shining. I love this weather\")\n;; 1\n(define (is_bored S)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_bored))\n    (check-within (candidate \"Hello world\") 0 0.001)\n    (check-within (candidate \"Is the sky blue?\") 0 0.001)\n    (check-within (candidate \"I love It !\") 1 0.001)\n    (check-within (candidate \"bIt\") 0 0.001)\n    (check-within (candidate \"I feel good today. I will be productive. will kill It\") 2 0.001)\n    (check-within (candidate \"You and I are going for a walk\") 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_91_is_bored", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_bored))\n    (check-within (candidate \"Hello world\") 0 0.001)\n    (check-within (candidate \"Is the sky blue?\") 0 0.001)\n    (check-within (candidate \"I love It !\") 1 0.001)\n    (check-within (candidate \"bIt\") 0 0.001)\n    (check-within (candidate \"I feel good today. I will be productive. will kill It\") 2 0.001)\n    (check-within (candidate \"You and I are going for a walk\") 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "rkt", "prompt": "#lang racket\n\n;; pairs_sum_to_zero takes a list of integers as an input.\n;; it returns #t if there are two distinct elements in the list that\n;; sum to zero, and #f otherwise.\n;; >>> (pairs_sum_to_zero (list 1 3 5 0))\n;; #f\n;; >>> (pairs_sum_to_zero (list 1 3 -2 1))\n;; #f\n;; >>> (pairs_sum_to_zero (list 1 2 3 7))\n;; #f\n;; >>> (pairs_sum_to_zero (list 2 4 -5 3 5 7))\n;; #t\n;; >>> (pairs_sum_to_zero (list 1))\n;; #f\n(define (pairs_sum_to_zero l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate pairs_sum_to_zero))\n    (check-within (candidate (list 1 3 5 0)) #f 0.001)\n    (check-within (candidate (list 1 3 -2 1)) #f 0.001)\n    (check-within (candidate (list 1 2 3 7)) #f 0.001)\n    (check-within (candidate (list 2 4 -5 3 5 7)) #t 0.001)\n    (check-within (candidate (list 1)) #f 0.001)\n    (check-within (candidate (list -3 9 -1 3 2 30)) #t 0.001)\n    (check-within (candidate (list -3 9 -1 3 2 31)) #t 0.001)\n    (check-within (candidate (list -3 9 -1 4 2 30)) #f 0.001)\n    (check-within (candidate (list -3 9 -1 4 2 31)) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate pairs_sum_to_zero))\n    (check-within (candidate (list 1 3 5 0)) #f 0.001)\n    (check-within (candidate (list 1 3 -2 1)) #f 0.001)\n    (check-within (candidate (list 1 2 3 7)) #f 0.001)\n    (check-within (candidate (list 2 4 -5 3 5 7)) #t 0.001)\n    (check-within (candidate (list 1)) #f 0.001)\n    (check-within (candidate (list -3 9 -1 3 2 30)) #t 0.001)\n    (check-within (candidate (list -3 9 -1 3 2 31)) #t 0.001)\n    (check-within (candidate (list -3 9 -1 4 2 30)) #f 0.001)\n    (check-within (candidate (list -3 9 -1 4 2 31)) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_71_triangle_area", "language": "rkt", "prompt": "#lang racket\n\n;; Given the lengths of the three sides of a triangle. Return the area of\n;; the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n;; Otherwise return -1\n;; Three sides make a valid triangle when the sum of any two sides is greater \n;; than the third side.\n;; Example:\n;; >>> (triangle_area 3 4 5)\n;; 6.0\n;; >>> (triangle_area 1 2 10)\n;; -1\n(define (triangle_area a b c)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate triangle_area))\n    (check-within (candidate 3 4 5) 6.0 0.001)\n    (check-within (candidate 1 2 10) -1 0.001)\n    (check-within (candidate 4 8 5) 8.18 0.001)\n    (check-within (candidate 2 2 2) 1.73 0.001)\n    (check-within (candidate 1 2 3) -1 0.001)\n    (check-within (candidate 10 5 7) 16.25 0.001)\n    (check-within (candidate 2 6 3) -1 0.001)\n    (check-within (candidate 1 1 1) 0.43 0.001)\n    (check-within (candidate 2 2 10) -1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_71_triangle_area", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate triangle_area))\n    (check-within (candidate 3 4 5) 6.0 0.001)\n    (check-within (candidate 1 2 10) -1 0.001)\n    (check-within (candidate 4 8 5) 8.18 0.001)\n    (check-within (candidate 2 2 2) 1.73 0.001)\n    (check-within (candidate 1 2 3) -1 0.001)\n    (check-within (candidate 10 5 7) 16.25 0.001)\n    (check-within (candidate 2 6 3) -1 0.001)\n    (check-within (candidate 1 1 1) 0.43 0.001)\n    (check-within (candidate 2 2 10) -1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_148_bf", "language": "rkt", "prompt": "#lang racket\n\n;; There are eight planets in our solar system: the closerst to the Sun \n;; is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n;; Uranus, Neptune.\n;; Write a function that takes two planet names as strings planet1 and planet2. \n;; The function should return a list containing all planets whose orbits are \n;; located between the orbit of planet1 and the orbit of planet2, sorted by \n;; the proximity to the sun. \n;; The function should return an empty list if planet1 or planet2\n;; are not correct planet names. \n;; Examples\n;; >>> (bf \"Jupiter\" \"Neptune\")\n;; (list \"Saturn\" \"Uranus\")\n;; >>> (bf \"Earth\" \"Mercury\")\n;; \"Venus\"\n;; >>> (bf \"Mercury\" \"Uranus\")\n;; (list \"Venus\" \"Earth\" \"Mars\" \"Jupiter\" \"Saturn\")\n(define (bf planet1 planet2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate bf))\n    (check-within (candidate \"Jupiter\" \"Neptune\") (list \"Saturn\" \"Uranus\") 0.001)\n    (check-within (candidate \"Earth\" \"Mercury\") (list \"Venus\") 0.001)\n    (check-within (candidate \"Mercury\" \"Uranus\") (list \"Venus\" \"Earth\" \"Mars\" \"Jupiter\" \"Saturn\") 0.001)\n    (check-within (candidate \"Neptune\" \"Venus\") (list \"Earth\" \"Mars\" \"Jupiter\" \"Saturn\" \"Uranus\") 0.001)\n    (check-within (candidate \"Earth\" \"Earth\") (list ) 0.001)\n    (check-within (candidate \"Mars\" \"Earth\") (list ) 0.001)\n    (check-within (candidate \"Jupiter\" \"Makemake\") (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_148_bf", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate bf))\n    (check-within (candidate \"Jupiter\" \"Neptune\") (list \"Saturn\" \"Uranus\") 0.001)\n    (check-within (candidate \"Earth\" \"Mercury\") (list \"Venus\") 0.001)\n    (check-within (candidate \"Mercury\" \"Uranus\") (list \"Venus\" \"Earth\" \"Mars\" \"Jupiter\" \"Saturn\") 0.001)\n    (check-within (candidate \"Neptune\" \"Venus\") (list \"Earth\" \"Mars\" \"Jupiter\" \"Saturn\" \"Uranus\") 0.001)\n    (check-within (candidate \"Earth\" \"Earth\") (list ) 0.001)\n    (check-within (candidate \"Mars\" \"Earth\") (list ) 0.001)\n    (check-within (candidate \"Jupiter\" \"Makemake\") (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_131_digits", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer n, return the product of the odd digits.\n;; Return 0 if all digits are even.\n;; For example:\n;; >>> (digits 1)\n;; 1\n;; >>> (digits 4)\n;; 0\n;; >>> (digits 235)\n;; 15\n(define (digits n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate digits))\n    (check-within (candidate 5) 5 0.001)\n    (check-within (candidate 54) 5 0.001)\n    (check-within (candidate 120) 1 0.001)\n    (check-within (candidate 5014) 5 0.001)\n    (check-within (candidate 98765) 315 0.001)\n    (check-within (candidate 5576543) 2625 0.001)\n    (check-within (candidate 2468) 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_131_digits", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate digits))\n    (check-within (candidate 5) 5 0.001)\n    (check-within (candidate 54) 5 0.001)\n    (check-within (candidate 120) 1 0.001)\n    (check-within (candidate 5014) 5 0.001)\n    (check-within (candidate 98765) 315 0.001)\n    (check-within (candidate 5576543) 2625 0.001)\n    (check-within (candidate 2468) 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_101_words_string", "language": "rkt", "prompt": "#lang racket\n\n;; You will be given a string of words separated by commas or spaces. Your task is\n;; to split the string into words and return a list of the words.\n;; For example:\n;; >>> (words_string \"Hi, my name is John\")\n;; (list \"Hi\" \"my\" \"name\" \"is\" \"John\")\n;; >>> (words_string \"One, two, three, four, five, six\")\n;; (list \"One\" \"two\" \"three\" \"four\" \"five\" \"six\")\n(define (words_string s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate words_string))\n    (check-within (candidate \"Hi, my name is John\") (list \"Hi\" \"my\" \"name\" \"is\" \"John\") 0.001)\n    (check-within (candidate \"One, two, three, four, five, six\") (list \"One\" \"two\" \"three\" \"four\" \"five\" \"six\") 0.001)\n    (check-within (candidate \"Hi, my name\") (list \"Hi\" \"my\" \"name\") 0.001)\n    (check-within (candidate \"One,, two, three, four, five, six,\") (list \"One\" \"two\" \"three\" \"four\" \"five\" \"six\") 0.001)\n    (check-within (candidate \"\") (list ) 0.001)\n    (check-within (candidate \"ahmed     , gamal\") (list \"ahmed\" \"gamal\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_101_words_string", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate words_string))\n    (check-within (candidate \"Hi, my name is John\") (list \"Hi\" \"my\" \"name\" \"is\" \"John\") 0.001)\n    (check-within (candidate \"One, two, three, four, five, six\") (list \"One\" \"two\" \"three\" \"four\" \"five\" \"six\") 0.001)\n    (check-within (candidate \"Hi, my name\") (list \"Hi\" \"my\" \"name\") 0.001)\n    (check-within (candidate \"One,, two, three, four, five, six,\") (list \"One\" \"two\" \"three\" \"four\" \"five\" \"six\") 0.001)\n    (check-within (candidate \"\") (list ) 0.001)\n    (check-within (candidate \"ahmed     , gamal\") (list \"ahmed\" \"gamal\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_18_how_many_times", "language": "rkt", "prompt": "#lang racket\n\n;; Find how many times a given substring can be found in the original string. Count overlaping cases.\n;; >>> (how_many_times \"\" \"a\")\n;; 0\n;; >>> (how_many_times \"aaa\" \"a\")\n;; 3\n;; >>> (how_many_times \"aaaa\" \"aa\")\n;; 3\n(define (how_many_times string substring)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate how_many_times))\n    (check-within (candidate \"\" \"x\") 0 0.001)\n    (check-within (candidate \"xyxyxyx\" \"x\") 4 0.001)\n    (check-within (candidate \"cacacacac\" \"cac\") 4 0.001)\n    (check-within (candidate \"john doe\" \"john\") 1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_18_how_many_times", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate how_many_times))\n    (check-within (candidate \"\" \"x\") 0 0.001)\n    (check-within (candidate \"xyxyxyx\" \"x\") 4 0.001)\n    (check-within (candidate \"cacacacac\" \"cac\") 4 0.001)\n    (check-within (candidate \"john doe\" \"john\") 1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_137_compare_one", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function that takes integers, floats, or strings representing\n;; real numbers, and returns the larger variable in its given variable type.\n;; Return #f if the values are equal.\n;; Note: If a real number is represented as a string, the floating point might be . or ,\n;; >>> (compare_one 1 2.5)\n;; 2.5\n;; >>> (compare_one 1 \"2,3\")\n;; \"2,3\"\n;; >>> (compare_one \"5,1\" \"6\")\n;; \"6\"\n;; >>> (compare_one \"1\" 1)\n;; #f\n(define (compare_one a b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_137_compare_one.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate compare_one))\n    (check-within (candidate 1 2) 2 0.001)\n    (check-within (candidate 1 2.5) 2.5 0.001)\n    (check-within (candidate 2 3) 3 0.001)\n    (check-within (candidate 5 6) 6 0.001)\n    (check-within (candidate 1 \"2,3\") \"2,3\" 0.001)\n    (check-within (candidate \"5,1\" \"6\") \"6\" 0.001)\n    (check-within (candidate \"1\" \"2\") \"2\" 0.001)\n    (check-within (candidate \"1\" 1) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_137_compare_one", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate compare_one))\n    (check-within (candidate 1 2) 2 0.001)\n    (check-within (candidate 1 2.5) 2.5 0.001)\n    (check-within (candidate 2 3) 3 0.001)\n    (check-within (candidate 5 6) 6 0.001)\n    (check-within (candidate 1 \"2,3\") \"2,3\" 0.001)\n    (check-within (candidate \"5,1\" \"6\") \"6\" 0.001)\n    (check-within (candidate \"1\" \"2\") \"2\" 0.001)\n    (check-within (candidate \"1\" 1) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_51_remove_vowels", "language": "rkt", "prompt": "#lang racket\n\n;; remove_vowels is a function that takes string and returns string without vowels.\n;; >>> (remove_vowels \"\")\n;; \"\"\n;; >>> (remove_vowels \"abcdef\")\n;; \"bcdf\"\n;; >>> (remove_vowels \"aaaaa\")\n;; \"\"\n;; >>> (remove_vowels \"aaBAA\")\n;; \"B\"\n;; >>> (remove_vowels \"zbcd\")\n;; \"zbcd\"\n(define (remove_vowels text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate remove_vowels))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"abcdef\nghijklm\") \"bcdf\nghjklm\" 0.001)\n    (check-within (candidate \"fedcba\") \"fdcb\" 0.001)\n    (check-within (candidate \"eeeee\") \"\" 0.001)\n    (check-within (candidate \"acBAA\") \"cB\" 0.001)\n    (check-within (candidate \"EcBOO\") \"cB\" 0.001)\n    (check-within (candidate \"ybcd\") \"ybcd\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_51_remove_vowels", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate remove_vowels))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"abcdef\nghijklm\") \"bcdf\nghjklm\" 0.001)\n    (check-within (candidate \"fedcba\") \"fdcb\" 0.001)\n    (check-within (candidate \"eeeee\") \"\" 0.001)\n    (check-within (candidate \"acBAA\") \"cB\" 0.001)\n    (check-within (candidate \"EcBOO\") \"cB\" 0.001)\n    (check-within (candidate \"ybcd\") \"ybcd\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_70_strange_sort_list", "language": "rkt", "prompt": "#lang racket\n\n;; Given list of integers, return list in strange order.\n;; Strange sorting, is when you start with the minimum value,\n;; then maximum of the remaining integers, then minimum and so on.\n;; Examples:\n;; >>> (strange_sort_list (list 1 2 3 4))\n;; (list 1 4 2 3)\n;; >>> (strange_sort_list (list 5 5 5 5))\n;; (list 5 5 5 5)\n;; >>> (strange_sort_list (list ))\n;; (list )\n(define (strange_sort_list lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate strange_sort_list))\n    (check-within (candidate (list 1 2 3 4)) (list 1 4 2 3) 0.001)\n    (check-within (candidate (list 5 6 7 8 9)) (list 5 9 6 8 7) 0.001)\n    (check-within (candidate (list 1 2 3 4 5)) (list 1 5 2 4 3) 0.001)\n    (check-within (candidate (list 5 6 7 8 9 1)) (list 1 9 5 8 6 7) 0.001)\n    (check-within (candidate (list 5 5 5 5)) (list 5 5 5 5) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6 7 8)) (list 1 8 2 7 3 6 4 5) 0.001)\n    (check-within (candidate (list 0 2 2 2 5 5 -5 -5)) (list -5 5 -5 5 0 2 2 2) 0.001)\n    (check-within (candidate (list 111111)) (list 111111) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_70_strange_sort_list", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate strange_sort_list))\n    (check-within (candidate (list 1 2 3 4)) (list 1 4 2 3) 0.001)\n    (check-within (candidate (list 5 6 7 8 9)) (list 5 9 6 8 7) 0.001)\n    (check-within (candidate (list 1 2 3 4 5)) (list 1 5 2 4 3) 0.001)\n    (check-within (candidate (list 5 6 7 8 9 1)) (list 1 9 5 8 6 7) 0.001)\n    (check-within (candidate (list 5 5 5 5)) (list 5 5 5 5) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6 7 8)) (list 1 8 2 7 3 6 4 5) 0.001)\n    (check-within (candidate (list 0 2 2 2 5 5 -5 -5)) (list -5 5 -5 5 0 2 2 2) 0.001)\n    (check-within (candidate (list 111111)) (list 111111) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_20_find_closest_elements", "language": "rkt", "prompt": "#lang racket\n\n;; From a supplied list of numbers (of length at least two) select and return two that are the closest to each\n;; other and return them in order (smaller number, larger number).\n;; >>> (find_closest_elements (list 1.0 2.0 3.0 4.0 5.0 2.2))\n;; (list 2.0 2.2)\n;; >>> (find_closest_elements (list 1.0 2.0 3.0 4.0 5.0 2.0))\n;; (list 2.0 2.0)\n(define (find_closest_elements numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate find_closest_elements))\n    (check-within (candidate (list 1.0 2.0 3.9 4.0 5.0 2.2)) (list 3.9 4.0) 0.001)\n    (check-within (candidate (list 1.0 2.0 5.9 4.0 5.0)) (list 5.0 5.9) 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0 2.2)) (list 2.0 2.2) 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0 2.0)) (list 2.0 2.0) 0.001)\n    (check-within (candidate (list 1.1 2.2 3.1 4.1 5.1)) (list 2.2 3.1) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_20_find_closest_elements", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate find_closest_elements))\n    (check-within (candidate (list 1.0 2.0 3.9 4.0 5.0 2.2)) (list 3.9 4.0) 0.001)\n    (check-within (candidate (list 1.0 2.0 5.9 4.0 5.0)) (list 5.0 5.9) 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0 2.2)) (list 2.0 2.2) 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0 2.0)) (list 2.0 2.0) 0.001)\n    (check-within (candidate (list 1.1 2.2 3.1 4.1 5.1)) (list 2.2 3.1) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_76_is_simple_power", "language": "rkt", "prompt": "#lang racket\n\n;; Your task is to write a function that returns true if a number x is a simple\n;; power of n and false in other cases.\n;; x is a simple power of n if n**int=x\n;; For example:\n;; >>> (is_simple_power 1 4)\n;; #t\n;; >>> (is_simple_power 2 2)\n;; #t\n;; >>> (is_simple_power 8 2)\n;; #t\n;; >>> (is_simple_power 3 2)\n;; #f\n;; >>> (is_simple_power 3 1)\n;; #f\n;; >>> (is_simple_power 5 3)\n;; #f\n(define (is_simple_power x n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_simple_power))\n    (check-within (candidate 16 2) #t 0.001)\n    (check-within (candidate 143214 16) #f 0.001)\n    (check-within (candidate 4 2) #t 0.001)\n    (check-within (candidate 9 3) #t 0.001)\n    (check-within (candidate 16 4) #t 0.001)\n    (check-within (candidate 24 2) #f 0.001)\n    (check-within (candidate 128 4) #f 0.001)\n    (check-within (candidate 12 6) #f 0.001)\n    (check-within (candidate 1 1) #t 0.001)\n    (check-within (candidate 1 12) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_76_is_simple_power", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_simple_power))\n    (check-within (candidate 16 2) #t 0.001)\n    (check-within (candidate 143214 16) #f 0.001)\n    (check-within (candidate 4 2) #t 0.001)\n    (check-within (candidate 9 3) #t 0.001)\n    (check-within (candidate 16 4) #t 0.001)\n    (check-within (candidate 24 2) #f 0.001)\n    (check-within (candidate 128 4) #f 0.001)\n    (check-within (candidate 12 6) #f 0.001)\n    (check-within (candidate 1 1) #t 0.001)\n    (check-within (candidate 1 12) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_39_prime_fib", "language": "rkt", "prompt": "#lang racket\n\n;; prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n;; >>> (prime_fib 1)\n;; 2\n;; >>> (prime_fib 2)\n;; 3\n;; >>> (prime_fib 3)\n;; 5\n;; >>> (prime_fib 4)\n;; 13\n;; >>> (prime_fib 5)\n;; 89\n(define (prime_fib n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate prime_fib))\n    (check-within (candidate 1) 2 0.001)\n    (check-within (candidate 2) 3 0.001)\n    (check-within (candidate 3) 5 0.001)\n    (check-within (candidate 4) 13 0.001)\n    (check-within (candidate 5) 89 0.001)\n    (check-within (candidate 6) 233 0.001)\n    (check-within (candidate 7) 1597 0.001)\n    (check-within (candidate 8) 28657 0.001)\n    (check-within (candidate 9) 514229 0.001)\n    (check-within (candidate 10) 433494437 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_39_prime_fib", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate prime_fib))\n    (check-within (candidate 1) 2 0.001)\n    (check-within (candidate 2) 3 0.001)\n    (check-within (candidate 3) 5 0.001)\n    (check-within (candidate 4) 13 0.001)\n    (check-within (candidate 5) 89 0.001)\n    (check-within (candidate 6) 233 0.001)\n    (check-within (candidate 7) 1597 0.001)\n    (check-within (candidate 8) 28657 0.001)\n    (check-within (candidate 9) 514229 0.001)\n    (check-within (candidate 10) 433494437 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_145_order_by_points", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function which sorts the given list of integers\n;; in ascending order according to the sum of their digits.\n;; Note: if there are several items with similar sum of their digits,\n;; order them based on their index in original list.\n;; For example:\n;; >>> (order_by_points (list 1 11 -1 -11 -12))\n;; (list -1 -11 1 -12 11)\n;; >>> (order_by_points (list ))\n;; (list )\n(define (order_by_points nums)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate order_by_points))\n    (check-within (candidate (list 1 11 -1 -11 -12)) (list -1 -11 1 -12 11) 0.001)\n    (check-within (candidate (list 1234 423 463 145 2 423 423 53 6 37 3457 3 56 0 46)) (list 0 2 3 6 53 423 423 423 1234 145 37 46 56 463 3457) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 -11 -32 43 54 -98 2 -3)) (list -3 -32 -98 -11 1 2 43 54) 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6 7 8 9 10 11)) (list 1 10 2 11 3 4 5 6 7 8 9) 0.001)\n    (check-within (candidate (list 0 6 6 -76 -21 23 4)) (list -76 -21 0 4 23 6 6) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_145_order_by_points", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate order_by_points))\n    (check-within (candidate (list 1 11 -1 -11 -12)) (list -1 -11 1 -12 11) 0.001)\n    (check-within (candidate (list 1234 423 463 145 2 423 423 53 6 37 3457 3 56 0 46)) (list 0 2 3 6 53 423 423 423 1234 145 37 46 56 463 3457) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 -11 -32 43 54 -98 2 -3)) (list -3 -32 -98 -11 1 2 43 54) 0.001)\n    (check-within (candidate (list 1 2 3 4 5 6 7 8 9 10 11)) (list 1 10 2 11 3 4 5 6 7 8 9) 0.001)\n    (check-within (candidate (list 0 6 6 -76 -21 23 4)) (list -76 -21 0 4 23 6 6) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_0_has_close_elements", "language": "rkt", "prompt": "#lang racket\n\n;; Check if in given list of numbers, are any two numbers closer to each other than\n;; given threshold.\n;; >>> (has_close_elements (list 1.0 2.0 3.0) 0.5)\n;; #f\n;; >>> (has_close_elements (list 1.0 2.8 3.0 4.0 5.0 2.0) 0.3)\n;; #t\n(define (has_close_elements numbers threshold)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate has_close_elements))\n    (check-within (candidate (list 1.0 2.0 3.9 4.0 5.0 2.2) 0.3) #t 0.001)\n    (check-within (candidate (list 1.0 2.0 3.9 4.0 5.0 2.2) 0.05) #f 0.001)\n    (check-within (candidate (list 1.0 2.0 5.9 4.0 5.0) 0.95) #t 0.001)\n    (check-within (candidate (list 1.0 2.0 5.9 4.0 5.0) 0.8) #f 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0 2.0) 0.1) #t 0.001)\n    (check-within (candidate (list 1.1 2.2 3.1 4.1 5.1) 1.0) #t 0.001)\n    (check-within (candidate (list 1.1 2.2 3.1 4.1 5.1) 0.5) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_0_has_close_elements", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate has_close_elements))\n    (check-within (candidate (list 1.0 2.0 3.9 4.0 5.0 2.2) 0.3) #t 0.001)\n    (check-within (candidate (list 1.0 2.0 3.9 4.0 5.0 2.2) 0.05) #f 0.001)\n    (check-within (candidate (list 1.0 2.0 5.9 4.0 5.0) 0.95) #t 0.001)\n    (check-within (candidate (list 1.0 2.0 5.9 4.0 5.0) 0.8) #f 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0 2.0) 0.1) #t 0.001)\n    (check-within (candidate (list 1.1 2.2 3.1 4.1 5.1) 1.0) #t 0.001)\n    (check-within (candidate (list 1.1 2.2 3.1 4.1 5.1) 0.5) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_10_make_palindrome", "language": "rkt", "prompt": "#lang racket\n\n;; Find the shortest palindrome that begins with a supplied string.\n;; Algorithm idea is simple:\n;; - Find the longest postfix of supplied string that is a palindrome.\n;; - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n;; >>> (make_palindrome \"\")\n;; \"\"\n;; >>> (make_palindrome \"cat\")\n;; \"catac\"\n;; >>> (make_palindrome \"cata\")\n;; \"catac\"\n(define (make_palindrome string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate make_palindrome))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"x\") \"x\" 0.001)\n    (check-within (candidate \"xyz\") \"xyzyx\" 0.001)\n    (check-within (candidate \"xyx\") \"xyx\" 0.001)\n    (check-within (candidate \"jerry\") \"jerryrrej\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_10_make_palindrome", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate make_palindrome))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"x\") \"x\" 0.001)\n    (check-within (candidate \"xyz\") \"xyzyx\" 0.001)\n    (check-within (candidate \"xyx\") \"xyx\" 0.001)\n    (check-within (candidate \"jerry\") \"jerryrrej\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_11_string_xor", "language": "rkt", "prompt": "#lang racket\n\n;; Input are two strings a and b consisting only of 1s and 0s.\n;; Perform binary XOR on these inputs and return result also as a string.\n;; >>> (string_xor \"010\" \"110\")\n;; \"100\"\n(define (string_xor a b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate string_xor))\n    (check-within (candidate \"111000\" \"101010\") \"010010\" 0.001)\n    (check-within (candidate \"1\" \"1\") \"0\" 0.001)\n    (check-within (candidate \"0101\" \"0000\") \"0101\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_11_string_xor", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate string_xor))\n    (check-within (candidate \"111000\" \"101010\") \"010010\" 0.001)\n    (check-within (candidate \"1\" \"1\") \"0\" 0.001)\n    (check-within (candidate \"0101\" \"0000\") \"0101\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_139_special_factorial", "language": "rkt", "prompt": "#lang racket\n\n;; The Brazilian factorial is defined as:\n;; brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n;; where n > 0\n;; For example:\n;; >>> (special_factorial 4)\n;; 288\n;; The function will receive an integer as input and should return the special\n;; factorial of this integer.\n(define (special_factorial n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate special_factorial))\n    (check-within (candidate 4) 288 0.001)\n    (check-within (candidate 5) 34560 0.001)\n    (check-within (candidate 7) 125411328000 0.001)\n    (check-within (candidate 1) 1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_139_special_factorial", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate special_factorial))\n    (check-within (candidate 4) 288 0.001)\n    (check-within (candidate 5) 34560 0.001)\n    (check-within (candidate 7) 125411328000 0.001)\n    (check-within (candidate 1) 1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_122_add_elements", "language": "rkt", "prompt": "#lang racket\n\n;; Given a non-empty list of integers arr and an integer k, return\n;; the sum of the elements with at most two digits from the first k elements of arr.\n;; Example:\n;; >>> (add_elements (list 111 21 3 4000 5 6 7 8 9) 4)\n;; 24\n;; Constraints:\n;; 1. 1 <= len(arr) <= 100\n;; 2. 1 <= k <= len(arr)\n(define (add_elements arr k)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate add_elements))\n    (check-within (candidate (list 1 -2 -3 41 57 76 87 88 99) 3) -4 0.001)\n    (check-within (candidate (list 111 121 3 4000 5 6) 2) 0 0.001)\n    (check-within (candidate (list 11 21 3 90 5 6 7 8 9) 4) 125 0.001)\n    (check-within (candidate (list 111 21 3 4000 5 6 7 8 9) 4) 24 0.001)\n    (check-within (candidate (list 1) 1) 1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_122_add_elements", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate add_elements))\n    (check-within (candidate (list 1 -2 -3 41 57 76 87 88 99) 3) -4 0.001)\n    (check-within (candidate (list 111 121 3 4000 5 6) 2) 0 0.001)\n    (check-within (candidate (list 11 21 3 90 5 6 7 8 9) 4) 125 0.001)\n    (check-within (candidate (list 111 21 3 4000 5 6 7 8 9) 4) 24 0.001)\n    (check-within (candidate (list 1) 1) 1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_46_fib4", "language": "rkt", "prompt": "#lang racket\n\n;; The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n;; fib4(0) -> 0\n;; fib4(1) -> 0\n;; fib4(2) -> 2\n;; fib4(3) -> 0\n;; fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n;; Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n;; >>> (fib4 5)\n;; 4\n;; >>> (fib4 6)\n;; 8\n;; >>> (fib4 7)\n;; 14\n(define (fib4 n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fib4))\n    (check-within (candidate 5) 4 0.001)\n    (check-within (candidate 8) 28 0.001)\n    (check-within (candidate 10) 104 0.001)\n    (check-within (candidate 12) 386 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_46_fib4", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fib4))\n    (check-within (candidate 5) 4 0.001)\n    (check-within (candidate 8) 28 0.001)\n    (check-within (candidate 10) 104 0.001)\n    (check-within (candidate 12) 386 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_104_unique_digits", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of positive integers x. return a sorted list of all \n;; elements that hasn't any even digit.\n;; Note: Returned list should be sorted in increasing order.\n;; For example:\n;; >>> (unique_digits (list 15 33 1422 1))\n;; (list 1 15 33)\n;; >>> (unique_digits (list 152 323 1422 10))\n;; (list )\n(define (unique_digits x)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate unique_digits))\n    (check-within (candidate (list 15 33 1422 1)) (list 1 15 33) 0.001)\n    (check-within (candidate (list 152 323 1422 10)) (list ) 0.001)\n    (check-within (candidate (list 12345 2033 111 151)) (list 111 151) 0.001)\n    (check-within (candidate (list 135 103 31)) (list 31 135) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_104_unique_digits", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate unique_digits))\n    (check-within (candidate (list 15 33 1422 1)) (list 1 15 33) 0.001)\n    (check-within (candidate (list 152 323 1422 10)) (list ) 0.001)\n    (check-within (candidate (list 12345 2033 111 151)) (list 111 151) 0.001)\n    (check-within (candidate (list 135 103 31)) (list 31 135) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_117_select_words", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string s and a natural number n, you have been tasked to implement \n;; a function that returns a list of all words from string s that contain exactly \n;; n consonants, in order these words appear in the string s.\n;; If the string s is empty then the function should return an empty list.\n;; Note: you may assume the input string contains only letters and spaces.\n;; Examples:\n;; >>> (select_words \"Mary had a little lamb\" 4)\n;; (list \"little\")\n;; >>> (select_words \"Mary had a little lamb\" 3)\n;; (list \"Mary\" \"lamb\")\n;; >>> (select_words \"simple white space\" 2)\n;; (list )\n;; >>> (select_words \"Hello world\" 4)\n;; (list \"world\")\n;; >>> (select_words \"Uncle sam\" 3)\n;; (list \"Uncle\")\n(define (select_words s n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate select_words))\n    (check-within (candidate \"Mary had a little lamb\" 4) (list \"little\") 0.001)\n    (check-within (candidate \"Mary had a little lamb\" 3) (list \"Mary\" \"lamb\") 0.001)\n    (check-within (candidate \"simple white space\" 2) (list ) 0.001)\n    (check-within (candidate \"Hello world\" 4) (list \"world\") 0.001)\n    (check-within (candidate \"Uncle sam\" 3) (list \"Uncle\") 0.001)\n    (check-within (candidate \"\" 4) (list ) 0.001)\n    (check-within (candidate \"a b c d e f\" 1) (list \"b\" \"c\" \"d\" \"f\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_117_select_words", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate select_words))\n    (check-within (candidate \"Mary had a little lamb\" 4) (list \"little\") 0.001)\n    (check-within (candidate \"Mary had a little lamb\" 3) (list \"Mary\" \"lamb\") 0.001)\n    (check-within (candidate \"simple white space\" 2) (list ) 0.001)\n    (check-within (candidate \"Hello world\" 4) (list \"world\") 0.001)\n    (check-within (candidate \"Uncle sam\" 3) (list \"Uncle\") 0.001)\n    (check-within (candidate \"\" 4) (list ) 0.001)\n    (check-within (candidate \"a b c d e f\" 1) (list \"b\" \"c\" \"d\" \"f\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_72_will_it_fly", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that returns #t if the object q will fly, and #f otherwise.\n;; The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n;; Example:\n;; >>> (will_it_fly (list 1 2) 5)\n;; #f\n;; # 1+2 is less than the maximum possible weight, but it's unbalanced.\n;; >>> (will_it_fly (list 3 2 3) 1)\n;; #f\n;; # it's balanced, but 3+2+3 is more than the maximum possible weight.\n;; >>> (will_it_fly (list 3 2 3) 9)\n;; #t\n;; # 3+2+3 is less than the maximum possible weight, and it's balanced.\n;; >>> (will_it_fly (list 3) 5)\n;; #t\n;; # 3 is less than the maximum possible weight, and it's balanced.\n(define (will_it_fly q w)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate will_it_fly))\n    (check-within (candidate (list 3 2 3) 9) #t 0.001)\n    (check-within (candidate (list 1 2) 5) #f 0.001)\n    (check-within (candidate (list 3) 5) #t 0.001)\n    (check-within (candidate (list 3 2 3) 1) #f 0.001)\n    (check-within (candidate (list 1 2 3) 6) #f 0.001)\n    (check-within (candidate (list 5) 5) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_72_will_it_fly", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate will_it_fly))\n    (check-within (candidate (list 3 2 3) 9) #t 0.001)\n    (check-within (candidate (list 1 2) 5) #f 0.001)\n    (check-within (candidate (list 3) 5) #t 0.001)\n    (check-within (candidate (list 3 2 3) 1) #f 0.001)\n    (check-within (candidate (list 1 2 3) 6) #f 0.001)\n    (check-within (candidate (list 5) 5) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_55_fib", "language": "rkt", "prompt": "#lang racket\n\n;; Return n-th Fibonacci number.\n;; >>> (fib 10)\n;; 55\n;; >>> (fib 1)\n;; 1\n;; >>> (fib 8)\n;; 21\n(define (fib n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fib))\n    (check-within (candidate 10) 55 0.001)\n    (check-within (candidate 1) 1 0.001)\n    (check-within (candidate 8) 21 0.001)\n    (check-within (candidate 11) 89 0.001)\n    (check-within (candidate 12) 144 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_55_fib", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fib))\n    (check-within (candidate 10) 55 0.001)\n    (check-within (candidate 1) 1 0.001)\n    (check-within (candidate 8) 21 0.001)\n    (check-within (candidate 11) 89 0.001)\n    (check-within (candidate 12) 144 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_153_Strongest_Extension", "language": "rkt", "prompt": "#lang racket\n\n;; You will be given the name of a class (a string) and a list of extensions.\n;; The extensions are to be used to load additional classes to the class. The\n;; strength of the extension is as follows: Let CAP be the number of the uppercase\n;; letters in the extension's name, and let SM be the number of lowercase letters \n;; in the extension's name, the strength is given by the fraction CAP - SM. \n;; You should find the strongest extension and return a string in this \n;; format: ClassName.StrongestExtensionName.\n;; If there are two or more extensions with the same strength, you should\n;; choose the one that comes first in the list.\n;; For example, if you are given \"Slices\" as the class and a list of the\n;; extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n;; return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n;; (its strength is -1).\n;; Example:\n;; >>> (Strongest_Extension \"my_class\" (list \"AA\" \"Be\" \"CC\"))\n;; \"my_class.AA\"\n(define (Strongest_Extension class_name extensions)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate Strongest_Extension))\n    (check-within (candidate \"Watashi\" (list \"tEN\" \"niNE\" \"eIGHt8OKe\")) \"Watashi.eIGHt8OKe\" 0.001)\n    (check-within (candidate \"Boku123\" (list \"nani\" \"NazeDa\" \"YEs.WeCaNe\" \"32145tggg\")) \"Boku123.YEs.WeCaNe\" 0.001)\n    (check-within (candidate \"__YESIMHERE\" (list \"t\" \"eMptY\" \"nothing\" \"zeR00\" \"NuLl__\" \"123NoooneB321\")) \"__YESIMHERE.NuLl__\" 0.001)\n    (check-within (candidate \"K\" (list \"Ta\" \"TAR\" \"t234An\" \"cosSo\")) \"K.TAR\" 0.001)\n    (check-within (candidate \"__HAHA\" (list \"Tab\" \"123\" \"781345\" \"-_-\")) \"__HAHA.123\" 0.001)\n    (check-within (candidate \"YameRore\" (list \"HhAas\" \"okIWILL123\" \"WorkOut\" \"Fails\" \"-_-\")) \"YameRore.okIWILL123\" 0.001)\n    (check-within (candidate \"finNNalLLly\" (list \"Die\" \"NowW\" \"Wow\" \"WoW\")) \"finNNalLLly.WoW\" 0.001)\n    (check-within (candidate \"_\" (list \"Bb\" \"91245\")) \"_.Bb\" 0.001)\n    (check-within (candidate \"Sp\" (list \"671235\" \"Bb\")) \"Sp.671235\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_153_Strongest_Extension", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate Strongest_Extension))\n    (check-within (candidate \"Watashi\" (list \"tEN\" \"niNE\" \"eIGHt8OKe\")) \"Watashi.eIGHt8OKe\" 0.001)\n    (check-within (candidate \"Boku123\" (list \"nani\" \"NazeDa\" \"YEs.WeCaNe\" \"32145tggg\")) \"Boku123.YEs.WeCaNe\" 0.001)\n    (check-within (candidate \"__YESIMHERE\" (list \"t\" \"eMptY\" \"nothing\" \"zeR00\" \"NuLl__\" \"123NoooneB321\")) \"__YESIMHERE.NuLl__\" 0.001)\n    (check-within (candidate \"K\" (list \"Ta\" \"TAR\" \"t234An\" \"cosSo\")) \"K.TAR\" 0.001)\n    (check-within (candidate \"__HAHA\" (list \"Tab\" \"123\" \"781345\" \"-_-\")) \"__HAHA.123\" 0.001)\n    (check-within (candidate \"YameRore\" (list \"HhAas\" \"okIWILL123\" \"WorkOut\" \"Fails\" \"-_-\")) \"YameRore.okIWILL123\" 0.001)\n    (check-within (candidate \"finNNalLLly\" (list \"Die\" \"NowW\" \"Wow\" \"WoW\")) \"finNNalLLly.WoW\" 0.001)\n    (check-within (candidate \"_\" (list \"Bb\" \"91245\")) \"_.Bb\" 0.001)\n    (check-within (candidate \"Sp\" (list \"671235\" \"Bb\")) \"Sp.671235\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_119_match_parens", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a list of two strings, both strings consist of open\n;; parentheses '(' or close parentheses ')' only.\n;; Your job is to check if it is possible to concatenate the two strings in\n;; some order, that the resulting string will be good.\n;; A string S is considered to be good if and only if all parentheses in S\n;; are balanced. For example: the string '(())()' is good, while the string\n;; '())' is not.\n;; Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n;; Examples:\n;; >>> (match_parens (list \"()(\" \")\"))\n;; \"Yes\"\n;; >>> (match_parens (list \")\" \")\"))\n;; \"No\"\n(define (match_parens lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate match_parens))\n    (check-within (candidate (list \"()(\" \")\")) \"Yes\" 0.001)\n    (check-within (candidate (list \")\" \")\")) \"No\" 0.001)\n    (check-within (candidate (list \"(()(())\" \"())())\")) \"No\" 0.001)\n    (check-within (candidate (list \")())\" \"(()()(\")) \"Yes\" 0.001)\n    (check-within (candidate (list \"(())))\" \"(()())((\")) \"Yes\" 0.001)\n    (check-within (candidate (list \"()\" \"())\")) \"No\" 0.001)\n    (check-within (candidate (list \"(()(\" \"()))()\")) \"Yes\" 0.001)\n    (check-within (candidate (list \"((((\" \"((())\")) \"No\" 0.001)\n    (check-within (candidate (list \")(()\" \"(()(\")) \"No\" 0.001)\n    (check-within (candidate (list \")(\" \")(\")) \"No\" 0.001)\n    (check-within (candidate (list \"(\" \")\")) \"Yes\" 0.001)\n    (check-within (candidate (list \")\" \"(\")) \"Yes\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_119_match_parens", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate match_parens))\n    (check-within (candidate (list \"()(\" \")\")) \"Yes\" 0.001)\n    (check-within (candidate (list \")\" \")\")) \"No\" 0.001)\n    (check-within (candidate (list \"(()(())\" \"())())\")) \"No\" 0.001)\n    (check-within (candidate (list \")())\" \"(()()(\")) \"Yes\" 0.001)\n    (check-within (candidate (list \"(())))\" \"(()())((\")) \"Yes\" 0.001)\n    (check-within (candidate (list \"()\" \"())\")) \"No\" 0.001)\n    (check-within (candidate (list \"(()(\" \"()))()\")) \"Yes\" 0.001)\n    (check-within (candidate (list \"((((\" \"((())\")) \"No\" 0.001)\n    (check-within (candidate (list \")(()\" \"(()(\")) \"No\" 0.001)\n    (check-within (candidate (list \")(\" \")(\")) \"No\" 0.001)\n    (check-within (candidate (list \"(\" \")\")) \"Yes\" 0.001)\n    (check-within (candidate (list \")\" \"(\")) \"Yes\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_90_next_smallest", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a list of integers.\n;; Write a function next_smallest() that returns the 2nd smallest element of the list.\n;; Return #f if there is no such element.\n;; >>> (next_smallest (list 1 2 3 4 5))\n;; 2\n;; >>> (next_smallest (list 5 1 4 3 2))\n;; 2\n;; >>> (next_smallest (list ))\n;; #f\n;; >>> (next_smallest (list 1 1))\n;; #f\n(define (next_smallest lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate next_smallest))\n    (check-within (candidate (list 1 2 3 4 5)) 2 0.001)\n    (check-within (candidate (list 5 1 4 3 2)) 2 0.001)\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list 1 1)) #f 0.001)\n    (check-within (candidate (list 1 1 1 1 0)) 1 0.001)\n    (check-within (candidate (list 1 1)) #f 0.001)\n    (check-within (candidate (list -35 34 12 -45)) -35 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_90_next_smallest", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate next_smallest))\n    (check-within (candidate (list 1 2 3 4 5)) 2 0.001)\n    (check-within (candidate (list 5 1 4 3 2)) 2 0.001)\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list 1 1)) #f 0.001)\n    (check-within (candidate (list 1 1 1 1 0)) 1 0.001)\n    (check-within (candidate (list 1 1)) #f 0.001)\n    (check-within (candidate (list -35 34 12 -45)) -35 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_92_any_int", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function that takes 3 numbers.\n;; Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n;; Returns false in any other cases.\n;; Examples\n;; >>> (any_int 5 2 7)\n;; #t\n;; >>> (any_int 3 2 2)\n;; #f\n;; >>> (any_int 3 -2 1)\n;; #t\n;; >>> (any_int 3.6 -2.2 2)\n;; #f\n(define (any_int x y z)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate any_int))\n    (check-within (candidate 2 3 1) #t 0.001)\n    (check-within (candidate 2.5 2 3) #f 0.001)\n    (check-within (candidate 1.5 5 3.5) #f 0.001)\n    (check-within (candidate 2 6 2) #f 0.001)\n    (check-within (candidate 4 2 2) #t 0.001)\n    (check-within (candidate 2.2 2.2 2.2) #f 0.001)\n    (check-within (candidate -4 6 2) #t 0.001)\n    (check-within (candidate 2 1 1) #t 0.001)\n    (check-within (candidate 3 4 7) #t 0.001)\n    (check-within (candidate 3.0 4 7) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_92_any_int", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate any_int))\n    (check-within (candidate 2 3 1) #t 0.001)\n    (check-within (candidate 2.5 2 3) #f 0.001)\n    (check-within (candidate 1.5 5 3.5) #f 0.001)\n    (check-within (candidate 2 6 2) #f 0.001)\n    (check-within (candidate 4 2 2) #t 0.001)\n    (check-within (candidate 2.2 2.2 2.2) #f 0.001)\n    (check-within (candidate -4 6 2) #t 0.001)\n    (check-within (candidate 2 1 1) #t 0.001)\n    (check-within (candidate 3 4 7) #t 0.001)\n    (check-within (candidate 3.0 4 7) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_2_truncate_number", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive floating point number, it can be decomposed into\n;; and integer part (largest integer smaller than given number) and decimals\n;; (leftover part always smaller than 1).\n;; Return the decimal part of the number.\n;; >>> (truncate_number 3.5)\n;; 0.5\n(define (truncate_number number)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate truncate_number))\n    (check-within (candidate 3.5) 0.5 0.001)\n    (check-within (candidate 1.25) 0.25 0.001)\n    (check-within (candidate 123.0) 0.0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_2_truncate_number", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate truncate_number))\n    (check-within (candidate 3.5) 0.5 0.001)\n    (check-within (candidate 1.25) 0.25 0.001)\n    (check-within (candidate 123.0) 0.0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_42_incr_list", "language": "rkt", "prompt": "#lang racket\n\n;; Return list with elements incremented by 1.\n;; >>> (incr_list (list 1 2 3))\n;; (list 2 3 4)\n;; >>> (incr_list (list 5 3 5 2 3 3 9 0 123))\n;; (list 6 4 6 3 4 4 10 1 124)\n(define (incr_list l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate incr_list))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 3 2 1)) (list 4 3 2) 0.001)\n    (check-within (candidate (list 5 2 5 2 3 3 9 0 123)) (list 6 3 6 3 4 4 10 1 124) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_42_incr_list", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate incr_list))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 3 2 1)) (list 4 3 2) 0.001)\n    (check-within (candidate (list 5 2 5 2 3 3 9 0 123)) (list 6 3 6 3 4 4 10 1 124) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_150_x_or_y", "language": "rkt", "prompt": "#lang racket\n\n;; A simple program which should return the value of x if n is \n;; a prime number and should return the value of y otherwise.\n;; Examples:\n;; >>> (x_or_y 7 34 12)\n;; 34\n;; >>> (x_or_y 15 8 5)\n;; 5\n(define (x_or_y n x y)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate x_or_y))\n    (check-within (candidate 7 34 12) 34 0.001)\n    (check-within (candidate 15 8 5) 5 0.001)\n    (check-within (candidate 3 33 5212) 33 0.001)\n    (check-within (candidate 1259 3 52) 3 0.001)\n    (check-within (candidate 7919 -1 12) -1 0.001)\n    (check-within (candidate 3609 1245 583) 583 0.001)\n    (check-within (candidate 91 56 129) 129 0.001)\n    (check-within (candidate 6 34 1234) 1234 0.001)\n    (check-within (candidate 1 2 0) 0 0.001)\n    (check-within (candidate 2 2 0) 2 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_150_x_or_y", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate x_or_y))\n    (check-within (candidate 7 34 12) 34 0.001)\n    (check-within (candidate 15 8 5) 5 0.001)\n    (check-within (candidate 3 33 5212) 33 0.001)\n    (check-within (candidate 1259 3 52) 3 0.001)\n    (check-within (candidate 7919 -1 12) -1 0.001)\n    (check-within (candidate 3609 1245 583) 583 0.001)\n    (check-within (candidate 91 56 129) 129 0.001)\n    (check-within (candidate 6 34 1234) 1234 0.001)\n    (check-within (candidate 1 2 0) 0 0.001)\n    (check-within (candidate 2 2 0) 2 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_49_modp", "language": "rkt", "prompt": "#lang racket\n\n;; Return 2^n modulo p (be aware of numerics).\n;; >>> (modp 3 5)\n;; 3\n;; >>> (modp 1101 101)\n;; 2\n;; >>> (modp 0 101)\n;; 1\n;; >>> (modp 3 11)\n;; 8\n;; >>> (modp 100 101)\n;; 1\n(define (modp n p)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate modp))\n    (check-within (candidate 3 5) 3 0.001)\n    (check-within (candidate 1101 101) 2 0.001)\n    (check-within (candidate 0 101) 1 0.001)\n    (check-within (candidate 3 11) 8 0.001)\n    (check-within (candidate 100 101) 1 0.001)\n    (check-within (candidate 30 5) 4 0.001)\n    (check-within (candidate 31 5) 3 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_49_modp", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate modp))\n    (check-within (candidate 3 5) 3 0.001)\n    (check-within (candidate 1101 101) 2 0.001)\n    (check-within (candidate 0 101) 1 0.001)\n    (check-within (candidate 3 11) 8 0.001)\n    (check-within (candidate 100 101) 1 0.001)\n    (check-within (candidate 30 5) 4 0.001)\n    (check-within (candidate 31 5) 3 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_155_even_odd_count", "language": "rkt", "prompt": "#lang racket\n\n;; Given an integer. return a list that has the number of even and odd digits respectively.\n;; Example:\n;; >>> (even_odd_count -12)\n;; (list 1 1)\n;; >>> (even_odd_count 123)\n;; (list 1 2)\n(define (even_odd_count num)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate even_odd_count))\n    (check-within (candidate 7) (list 0 1) 0.001)\n    (check-within (candidate -78) (list 1 1) 0.001)\n    (check-within (candidate 3452) (list 2 2) 0.001)\n    (check-within (candidate 346211) (list 3 3) 0.001)\n    (check-within (candidate -345821) (list 3 3) 0.001)\n    (check-within (candidate -2) (list 1 0) 0.001)\n    (check-within (candidate -45347) (list 2 3) 0.001)\n    (check-within (candidate 0) (list 1 0) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_155_even_odd_count", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate even_odd_count))\n    (check-within (candidate 7) (list 0 1) 0.001)\n    (check-within (candidate -78) (list 1 1) 0.001)\n    (check-within (candidate 3452) (list 2 2) 0.001)\n    (check-within (candidate 346211) (list 3 3) 0.001)\n    (check-within (candidate -345821) (list 3 3) 0.001)\n    (check-within (candidate -2) (list 1 0) 0.001)\n    (check-within (candidate -45347) (list 2 3) 0.001)\n    (check-within (candidate 0) (list 1 0) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_80_is_happy", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a string s.\n;; Your task is to check if the string is haprkt or not.\n;; A string is haprkt if its length is at least 3 and every 3 consecutive letters are distinct\n;; For example:\n;; >>> (is_happy \"a\")\n;; #f\n;; >>> (is_happy \"aa\")\n;; #f\n;; >>> (is_happy \"abcd\")\n;; #t\n;; >>> (is_happy \"aabb\")\n;; #f\n;; >>> (is_happy \"adb\")\n;; #t\n;; >>> (is_happy \"xyy\")\n;; #f\n(define (is_happy s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_happy))\n    (check-within (candidate \"a\") #f 0.001)\n    (check-within (candidate \"aa\") #f 0.001)\n    (check-within (candidate \"abcd\") #t 0.001)\n    (check-within (candidate \"aabb\") #f 0.001)\n    (check-within (candidate \"adb\") #t 0.001)\n    (check-within (candidate \"xyy\") #f 0.001)\n    (check-within (candidate \"iopaxpoi\") #t 0.001)\n    (check-within (candidate \"iopaxioi\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_80_is_happy", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_happy))\n    (check-within (candidate \"a\") #f 0.001)\n    (check-within (candidate \"aa\") #f 0.001)\n    (check-within (candidate \"abcd\") #t 0.001)\n    (check-within (candidate \"aabb\") #f 0.001)\n    (check-within (candidate \"adb\") #t 0.001)\n    (check-within (candidate \"xyy\") #f 0.001)\n    (check-within (candidate \"iopaxpoi\") #t 0.001)\n    (check-within (candidate \"iopaxioi\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_59_largest_prime_factor", "language": "rkt", "prompt": "#lang racket\n\n;; Return the largest prime factor of n. Assume n > 1 and is not a prime.\n;; >>> (largest_prime_factor 13195)\n;; 29\n;; >>> (largest_prime_factor 2048)\n;; 2\n(define (largest_prime_factor n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate largest_prime_factor))\n    (check-within (candidate 15) 5 0.001)\n    (check-within (candidate 27) 3 0.001)\n    (check-within (candidate 63) 7 0.001)\n    (check-within (candidate 330) 11 0.001)\n    (check-within (candidate 13195) 29 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_59_largest_prime_factor", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate largest_prime_factor))\n    (check-within (candidate 15) 5 0.001)\n    (check-within (candidate 27) 3 0.001)\n    (check-within (candidate 63) 7 0.001)\n    (check-within (candidate 330) 11 0.001)\n    (check-within (candidate 13195) 29 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_66_digitSum", "language": "rkt", "prompt": "#lang racket\n\n;; Task\n;; Write a function that takes a string as input and returns the sum of the upper characters only'\n;; ASCII codes.\n;; Examples:\n;; >>> (digitSum \"\")\n;; 0\n;; >>> (digitSum \"abAB\")\n;; 131\n;; >>> (digitSum \"abcCd\")\n;; 67\n;; >>> (digitSum \"helloE\")\n;; 69\n;; >>> (digitSum \"woArBld\")\n;; 131\n;; >>> (digitSum \"aAaaaXa\")\n;; 153\n(define (digitSum s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate digitSum))\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"abAB\") 131 0.001)\n    (check-within (candidate \"abcCd\") 67 0.001)\n    (check-within (candidate \"helloE\") 69 0.001)\n    (check-within (candidate \"woArBld\") 131 0.001)\n    (check-within (candidate \"aAaaaXa\") 153 0.001)\n    (check-within (candidate \" How are yOu?\") 151 0.001)\n    (check-within (candidate \"You arE Very Smart\") 327 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_66_digitSum", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate digitSum))\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"abAB\") 131 0.001)\n    (check-within (candidate \"abcCd\") 67 0.001)\n    (check-within (candidate \"helloE\") 69 0.001)\n    (check-within (candidate \"woArBld\") 131 0.001)\n    (check-within (candidate \"aAaaaXa\") 153 0.001)\n    (check-within (candidate \" How are yOu?\") 151 0.001)\n    (check-within (candidate \"You arE Very Smart\") 327 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_21_rescale_to_unit", "language": "rkt", "prompt": "#lang racket\n\n;; Given list of numbers (of at least two elements), apply a linear transform to that list,\n;; such that the smallest number will become 0 and the largest will become 1\n;; >>> (rescale_to_unit (list 1.0 2.0 3.0 4.0 5.0))\n;; (list 0.0 0.25 0.5 0.75 1.0)\n(define (rescale_to_unit numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate rescale_to_unit))\n    (check-within (candidate (list 2.0 49.9)) (list 0.0 1.0) 0.001)\n    (check-within (candidate (list 100.0 49.9)) (list 1.0 0.0) 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0)) (list 0.0 0.25 0.5 0.75 1.0) 0.001)\n    (check-within (candidate (list 2.0 1.0 5.0 3.0 4.0)) (list 0.25 0.0 1.0 0.5 0.75) 0.001)\n    (check-within (candidate (list 12.0 11.0 15.0 13.0 14.0)) (list 0.25 0.0 1.0 0.5 0.75) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_21_rescale_to_unit", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate rescale_to_unit))\n    (check-within (candidate (list 2.0 49.9)) (list 0.0 1.0) 0.001)\n    (check-within (candidate (list 100.0 49.9)) (list 1.0 0.0) 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0)) (list 0.0 0.25 0.5 0.75 1.0) 0.001)\n    (check-within (candidate (list 2.0 1.0 5.0 3.0 4.0)) (list 0.25 0.0 1.0 0.5 0.75) 0.001)\n    (check-within (candidate (list 12.0 11.0 15.0 13.0 14.0)) (list 0.25 0.0 1.0 0.5 0.75) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_121_solution", "language": "rkt", "prompt": "#lang racket\n\n;; Given a non-empty list of integers, return the sum of all of the odd elements that are in even positions.\n;; Examples\n;; >>> (solution (list 5 8 7 1))\n;; 12\n;; >>> (solution (list 3 3 3 3 3))\n;; 9\n;; >>> (solution (list 30 13 24 321))\n;; 0\n(define (solution lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate solution))\n    (check-within (candidate (list 5 8 7 1)) 12 0.001)\n    (check-within (candidate (list 3 3 3 3 3)) 9 0.001)\n    (check-within (candidate (list 30 13 24 321)) 0 0.001)\n    (check-within (candidate (list 5 9)) 5 0.001)\n    (check-within (candidate (list 2 4 8)) 0 0.001)\n    (check-within (candidate (list 30 13 23 32)) 23 0.001)\n    (check-within (candidate (list 3 13 2 9)) 3 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_121_solution", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate solution))\n    (check-within (candidate (list 5 8 7 1)) 12 0.001)\n    (check-within (candidate (list 3 3 3 3 3)) 9 0.001)\n    (check-within (candidate (list 30 13 24 321)) 0 0.001)\n    (check-within (candidate (list 5 9)) 5 0.001)\n    (check-within (candidate (list 2 4 8)) 0 0.001)\n    (check-within (candidate (list 30 13 23 32)) 23 0.001)\n    (check-within (candidate (list 3 13 2 9)) 3 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_68_pluck", "language": "rkt", "prompt": "#lang racket\n\n;; \"Given a list representing a branch of a tree that has non-negative integer nodes\n;; your task is to pluck one of the nodes and return it.\n;; The plucked node should be the node with the smallest even value.\n;; If multiple nodes with the same smallest even value are found return the node that has smallest index.\n;; The plucked node should be returned in a list, [ smalest_value, its index ],\n;; If there are no even values or the given list is empty, return [].\n;; Example 1:\n;; >>> (pluck (list 4 2 3))\n;; (list 2 1)\n;; Explanation: 2 has the smallest even value, and 2 has the smallest index.\n;; Example 2:\n;; >>> (pluck (list 1 2 3))\n;; (list 2 1)\n;; Explanation: 2 has the smallest even value, and 2 has the smallest index.\n;; Example 3:\n;; >>> (pluck (list ))\n;; (list )\n;; Example 4:\n;; >>> (pluck (list 5 0 3 0 4 2))\n;; (list 0 1)\n;; Explanation: 0 is the smallest value, but  there are two zeros,\n;; so we will choose the first zero, which has the smallest index.\n;; Constraints:\n;; * 1 <= nodes.length <= 10000\n;; * 0 <= node.value\n(define (pluck arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate pluck))\n    (check-within (candidate (list 4 2 3)) (list 2 1) 0.001)\n    (check-within (candidate (list 1 2 3)) (list 2 1) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 5 0 3 0 4 2)) (list 0 1) 0.001)\n    (check-within (candidate (list 1 2 3 0 5 3)) (list 0 3) 0.001)\n    (check-within (candidate (list 5 4 8 4 8)) (list 4 1) 0.001)\n    (check-within (candidate (list 7 6 7 1)) (list 6 1) 0.001)\n    (check-within (candidate (list 7 9 7 1)) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_68_pluck", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate pluck))\n    (check-within (candidate (list 4 2 3)) (list 2 1) 0.001)\n    (check-within (candidate (list 1 2 3)) (list 2 1) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 5 0 3 0 4 2)) (list 0 1) 0.001)\n    (check-within (candidate (list 1 2 3 0 5 3)) (list 0 3) 0.001)\n    (check-within (candidate (list 5 4 8 4 8)) (list 4 1) 0.001)\n    (check-within (candidate (list 7 6 7 1)) (list 6 1) 0.001)\n    (check-within (candidate (list 7 9 7 1)) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_147_get_max_triples", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a positive integer n. You have to create an integer list a of length n.\n;; For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n;; Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n;; and a[i] + a[j] + a[k] is a multiple of 3.\n;; Example :\n;; >>> (get_max_triples 5)\n;; 1\n;; Explanation: \n;; a = [1, 3, 7, 13, 21]\n;; The only valid triple is (1, 7, 13).\n(define (get_max_triples n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_max_triples))\n    (check-within (candidate 5) 1 0.001)\n    (check-within (candidate 6) 4 0.001)\n    (check-within (candidate 10) 36 0.001)\n    (check-within (candidate 100) 53361 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_147_get_max_triples", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_max_triples))\n    (check-within (candidate 5) 1 0.001)\n    (check-within (candidate 6) 4 0.001)\n    (check-within (candidate 10) 36 0.001)\n    (check-within (candidate 100) 53361 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_110_exchange", "language": "rkt", "prompt": "#lang racket\n\n;; In this problem, you will implement a function that takes two lists of numbers,\n;; and determines whether it is possible to perform an exchange of elements\n;; between them to make lst1 a list of only even numbers.\n;; There is no limit on the number of exchanged elements between lst1 and lst2.\n;; If it is possible to exchange elements between the lst1 and lst2 to make\n;; all the elements of lst1 to be even, return \"YES\".\n;; Otherwise, return \"NO\".\n;; For example:\n;; >>> (exchange (list 1 2 3 4) (list 1 2 3 4))\n;; \"YES\"\n;; >>> (exchange (list 1 2 3 4) (list 1 5 3 4))\n;; \"NO\"\n;; It is assumed that the input lists will be non-empty.\n(define (exchange lst1 lst2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate exchange))\n    (check-within (candidate (list 1 2 3 4) (list 1 2 3 4)) \"YES\" 0.001)\n    (check-within (candidate (list 1 2 3 4) (list 1 5 3 4)) \"NO\" 0.001)\n    (check-within (candidate (list 1 2 3 4) (list 2 1 4 3)) \"YES\" 0.001)\n    (check-within (candidate (list 5 7 3) (list 2 6 4)) \"YES\" 0.001)\n    (check-within (candidate (list 5 7 3) (list 2 6 3)) \"NO\" 0.001)\n    (check-within (candidate (list 3 2 6 1 8 9) (list 3 5 5 1 1 1)) \"NO\" 0.001)\n    (check-within (candidate (list 100 200) (list 200 200)) \"YES\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_110_exchange", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate exchange))\n    (check-within (candidate (list 1 2 3 4) (list 1 2 3 4)) \"YES\" 0.001)\n    (check-within (candidate (list 1 2 3 4) (list 1 5 3 4)) \"NO\" 0.001)\n    (check-within (candidate (list 1 2 3 4) (list 2 1 4 3)) \"YES\" 0.001)\n    (check-within (candidate (list 5 7 3) (list 2 6 4)) \"YES\" 0.001)\n    (check-within (candidate (list 5 7 3) (list 2 6 3)) \"NO\" 0.001)\n    (check-within (candidate (list 3 2 6 1 8 9) (list 3 5 5 1 1 1)) \"NO\" 0.001)\n    (check-within (candidate (list 100 200) (list 200 200)) \"YES\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_47_median", "language": "rkt", "prompt": "#lang racket\n\n;; Return median of elements in the list l.\n;; >>> (median (list 3 1 2 4 5))\n;; 3\n;; >>> (median (list -10 4 6 1000 10 20))\n;; 15.0\n(define (median l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate median))\n    (check-within (candidate (list 3 1 2 4 5)) 3 0.001)\n    (check-within (candidate (list -10 4 6 1000 10 20)) 8.0 0.001)\n    (check-within (candidate (list 5)) 5 0.001)\n    (check-within (candidate (list 6 5)) 5.5 0.001)\n    (check-within (candidate (list 8 1 3 9 9 2 7)) 7 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_47_median", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate median))\n    (check-within (candidate (list 3 1 2 4 5)) 3 0.001)\n    (check-within (candidate (list -10 4 6 1000 10 20)) 8.0 0.001)\n    (check-within (candidate (list 5)) 5 0.001)\n    (check-within (candidate (list 6 5)) 5.5 0.001)\n    (check-within (candidate (list 8 1 3 9 9 2 7)) 7 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_82_prime_length", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that takes a string and returns #t if the string\n;; length is a prime number or #f otherwise\n;; Examples\n;; >>> (prime_length \"Hello\")\n;; #t\n;; >>> (prime_length \"abcdcba\")\n;; #t\n;; >>> (prime_length \"kittens\")\n;; #t\n;; >>> (prime_length \"orange\")\n;; #f\n(define (prime_length string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate prime_length))\n    (check-within (candidate \"Hello\") #t 0.001)\n    (check-within (candidate \"abcdcba\") #t 0.001)\n    (check-within (candidate \"kittens\") #t 0.001)\n    (check-within (candidate \"orange\") #f 0.001)\n    (check-within (candidate \"wow\") #t 0.001)\n    (check-within (candidate \"world\") #t 0.001)\n    (check-within (candidate \"MadaM\") #t 0.001)\n    (check-within (candidate \"Wow\") #t 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"HI\") #t 0.001)\n    (check-within (candidate \"go\") #t 0.001)\n    (check-within (candidate \"gogo\") #f 0.001)\n    (check-within (candidate \"aaaaaaaaaaaaaaa\") #f 0.001)\n    (check-within (candidate \"Madam\") #t 0.001)\n    (check-within (candidate \"M\") #f 0.001)\n    (check-within (candidate \"0\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_82_prime_length", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate prime_length))\n    (check-within (candidate \"Hello\") #t 0.001)\n    (check-within (candidate \"abcdcba\") #t 0.001)\n    (check-within (candidate \"kittens\") #t 0.001)\n    (check-within (candidate \"orange\") #f 0.001)\n    (check-within (candidate \"wow\") #t 0.001)\n    (check-within (candidate \"world\") #t 0.001)\n    (check-within (candidate \"MadaM\") #t 0.001)\n    (check-within (candidate \"Wow\") #t 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"HI\") #t 0.001)\n    (check-within (candidate \"go\") #t 0.001)\n    (check-within (candidate \"gogo\") #f 0.001)\n    (check-within (candidate \"aaaaaaaaaaaaaaa\") #f 0.001)\n    (check-within (candidate \"Madam\") #t 0.001)\n    (check-within (candidate \"M\") #f 0.001)\n    (check-within (candidate \"0\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_73_smallest_change", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list arr of integers, find the minimum number of elements that\n;; need to be changed to make the list palindromic. A palindromic list is a list that\n;; is read the same backwards and forwards. In one change, you can change one element to any other element.\n;; For example:\n;; >>> (smallest_change (list 1 2 3 5 4 7 9 6))\n;; 4\n;; >>> (smallest_change (list 1 2 3 4 3 2 2))\n;; 1\n;; >>> (smallest_change (list 1 2 3 2 1))\n;; 0\n(define (smallest_change arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate smallest_change))\n    (check-within (candidate (list 1 2 3 5 4 7 9 6)) 4 0.001)\n    (check-within (candidate (list 1 2 3 4 3 2 2)) 1 0.001)\n    (check-within (candidate (list 1 4 2)) 1 0.001)\n    (check-within (candidate (list 1 4 4 2)) 1 0.001)\n    (check-within (candidate (list 1 2 3 2 1)) 0 0.001)\n    (check-within (candidate (list 3 1 1 3)) 0 0.001)\n    (check-within (candidate (list 1)) 0 0.001)\n    (check-within (candidate (list 0 1)) 1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_73_smallest_change", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate smallest_change))\n    (check-within (candidate (list 1 2 3 5 4 7 9 6)) 4 0.001)\n    (check-within (candidate (list 1 2 3 4 3 2 2)) 1 0.001)\n    (check-within (candidate (list 1 4 2)) 1 0.001)\n    (check-within (candidate (list 1 4 4 2)) 1 0.001)\n    (check-within (candidate (list 1 2 3 2 1)) 0 0.001)\n    (check-within (candidate (list 3 1 1 3)) 0 0.001)\n    (check-within (candidate (list 1)) 0 0.001)\n    (check-within (candidate (list 0 1)) 1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_133_sum_squares", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a list of numbers.\n;; You need to return the sum of squared numbers in the given list,\n;; round each element in the list to the upper int(Ceiling) first.\n;; Examples:\n;; >>> (lst (list 1.0 2.0 3.0))\n;; 14\n;; >>> (lst (list 1.0 4.0 9.0))\n;; 98\n;; >>> (lst (list 1.0 3.0 5.0 7.0))\n;; 84\n;; >>> (lst (list 1.4 4.2 0.0))\n;; 29\n;; >>> (lst (list -2.4 1.0 1.0))\n;; 6\n(define (sum_squares lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_squares))\n    (check-within (candidate (list 1.0 2.0 3.0)) 14 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0)) 14 0.001)\n    (check-within (candidate (list 1.0 3.0 5.0 7.0)) 84 0.001)\n    (check-within (candidate (list 1.4 4.2 0.0)) 29 0.001)\n    (check-within (candidate (list -2.4 1.0 1.0)) 6 0.001)\n    (check-within (candidate (list 100.0 1.0 15.0 2.0)) 10230 0.001)\n    (check-within (candidate (list 10000.0 10000.0)) 200000000 0.001)\n    (check-within (candidate (list -1.4 4.6 6.3)) 75 0.001)\n    (check-within (candidate (list -1.4 17.9 18.9 19.9)) 1086 0.001)\n    (check-within (candidate (list 0.0)) 0 0.001)\n    (check-within (candidate (list -1.0)) 1 0.001)\n    (check-within (candidate (list -1.0 1.0 0.0)) 2 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_133_sum_squares", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_squares))\n    (check-within (candidate (list 1.0 2.0 3.0)) 14 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0)) 14 0.001)\n    (check-within (candidate (list 1.0 3.0 5.0 7.0)) 84 0.001)\n    (check-within (candidate (list 1.4 4.2 0.0)) 29 0.001)\n    (check-within (candidate (list -2.4 1.0 1.0)) 6 0.001)\n    (check-within (candidate (list 100.0 1.0 15.0 2.0)) 10230 0.001)\n    (check-within (candidate (list 10000.0 10000.0)) 200000000 0.001)\n    (check-within (candidate (list -1.4 4.6 6.3)) 75 0.001)\n    (check-within (candidate (list -1.4 17.9 18.9 19.9)) 1086 0.001)\n    (check-within (candidate (list 0.0)) 0 0.001)\n    (check-within (candidate (list -1.0)) 1 0.001)\n    (check-within (candidate (list -1.0 1.0 0.0)) 2 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_141_file_name_check", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function which takes a string representing a file's name, and returns\n;; 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n;; A file's name is considered to be valid if and only if all the following conditions \n;; are met:\n;; - There should not be more than three digits ('0'-'9') in the file's name.\n;; - The file's name contains exactly one dot '.'\n;; - The substring before the dot should not be empty, and it starts with a letter from \n;; the latin alphapet ('a'-'z' and 'A'-'Z').\n;; - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n;; Examples:\n;; >>> (file_name_check \"example.txt\")\n;; \"Yes\"\n;; >>> (file_name_check \"1example.dll\")\n;; \"No\"\n(define (file_name_check file_name)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate file_name_check))\n    (check-within (candidate \"example.txt\") \"Yes\" 0.001)\n    (check-within (candidate \"1example.dll\") \"No\" 0.001)\n    (check-within (candidate \"s1sdf3.asd\") \"No\" 0.001)\n    (check-within (candidate \"K.dll\") \"Yes\" 0.001)\n    (check-within (candidate \"MY16FILE3.exe\") \"Yes\" 0.001)\n    (check-within (candidate \"His12FILE94.exe\") \"No\" 0.001)\n    (check-within (candidate \"_Y.txt\") \"No\" 0.001)\n    (check-within (candidate \"?aREYA.exe\") \"No\" 0.001)\n    (check-within (candidate \"/this_is_valid.dll\") \"No\" 0.001)\n    (check-within (candidate \"this_is_valid.wow\") \"No\" 0.001)\n    (check-within (candidate \"this_is_valid.txt\") \"Yes\" 0.001)\n    (check-within (candidate \"this_is_valid.txtexe\") \"No\" 0.001)\n    (check-within (candidate \"#this2_i4s_5valid.ten\") \"No\" 0.001)\n    (check-within (candidate \"@this1_is6_valid.exe\") \"No\" 0.001)\n    (check-within (candidate \"this_is_12valid.6exe4.txt\") \"No\" 0.001)\n    (check-within (candidate \"all.exe.txt\") \"No\" 0.001)\n    (check-within (candidate \"I563_No.exe\") \"Yes\" 0.001)\n    (check-within (candidate \"Is3youfault.txt\") \"Yes\" 0.001)\n    (check-within (candidate \"no_one#knows.dll\") \"Yes\" 0.001)\n    (check-within (candidate \"1I563_Yes3.exe\") \"No\" 0.001)\n    (check-within (candidate \"I563_Yes3.txtt\") \"No\" 0.001)\n    (check-within (candidate \"final..txt\") \"No\" 0.001)\n    (check-within (candidate \"final132\") \"No\" 0.001)\n    (check-within (candidate \"_f4indsartal132.\") \"No\" 0.001)\n    (check-within (candidate \".txt\") \"No\" 0.001)\n    (check-within (candidate \"s.\") \"No\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_141_file_name_check", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate file_name_check))\n    (check-within (candidate \"example.txt\") \"Yes\" 0.001)\n    (check-within (candidate \"1example.dll\") \"No\" 0.001)\n    (check-within (candidate \"s1sdf3.asd\") \"No\" 0.001)\n    (check-within (candidate \"K.dll\") \"Yes\" 0.001)\n    (check-within (candidate \"MY16FILE3.exe\") \"Yes\" 0.001)\n    (check-within (candidate \"His12FILE94.exe\") \"No\" 0.001)\n    (check-within (candidate \"_Y.txt\") \"No\" 0.001)\n    (check-within (candidate \"?aREYA.exe\") \"No\" 0.001)\n    (check-within (candidate \"/this_is_valid.dll\") \"No\" 0.001)\n    (check-within (candidate \"this_is_valid.wow\") \"No\" 0.001)\n    (check-within (candidate \"this_is_valid.txt\") \"Yes\" 0.001)\n    (check-within (candidate \"this_is_valid.txtexe\") \"No\" 0.001)\n    (check-within (candidate \"#this2_i4s_5valid.ten\") \"No\" 0.001)\n    (check-within (candidate \"@this1_is6_valid.exe\") \"No\" 0.001)\n    (check-within (candidate \"this_is_12valid.6exe4.txt\") \"No\" 0.001)\n    (check-within (candidate \"all.exe.txt\") \"No\" 0.001)\n    (check-within (candidate \"I563_No.exe\") \"Yes\" 0.001)\n    (check-within (candidate \"Is3youfault.txt\") \"Yes\" 0.001)\n    (check-within (candidate \"no_one#knows.dll\") \"Yes\" 0.001)\n    (check-within (candidate \"1I563_Yes3.exe\") \"No\" 0.001)\n    (check-within (candidate \"I563_Yes3.txtt\") \"No\" 0.001)\n    (check-within (candidate \"final..txt\") \"No\" 0.001)\n    (check-within (candidate \"final132\") \"No\" 0.001)\n    (check-within (candidate \"_f4indsartal132.\") \"No\" 0.001)\n    (check-within (candidate \".txt\") \"No\" 0.001)\n    (check-within (candidate \"s.\") \"No\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "rkt", "prompt": "#lang racket\n\n;; triples_sum_to_zero takes a list of integers as an input.\n;; it returns #t if there are three distinct elements in the list that\n;; sum to zero, and #f otherwise.\n;; >>> (triples_sum_to_zero (list 1 3 5 0))\n;; #f\n;; >>> (triples_sum_to_zero (list 1 3 -2 1))\n;; #t\n;; >>> (triples_sum_to_zero (list 1 2 3 7))\n;; #f\n;; >>> (triples_sum_to_zero (list 2 4 -5 3 9 7))\n;; #t\n;; >>> (triples_sum_to_zero (list 1))\n;; #f\n(define (triples_sum_to_zero l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate triples_sum_to_zero))\n    (check-within (candidate (list 1 3 5 0)) #f 0.001)\n    (check-within (candidate (list 1 3 5 -1)) #f 0.001)\n    (check-within (candidate (list 1 3 -2 1)) #t 0.001)\n    (check-within (candidate (list 1 2 3 7)) #f 0.001)\n    (check-within (candidate (list 1 2 5 7)) #f 0.001)\n    (check-within (candidate (list 2 4 -5 3 9 7)) #t 0.001)\n    (check-within (candidate (list 1)) #f 0.001)\n    (check-within (candidate (list 1 3 5 -100)) #f 0.001)\n    (check-within (candidate (list 100 3 5 -100)) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate triples_sum_to_zero))\n    (check-within (candidate (list 1 3 5 0)) #f 0.001)\n    (check-within (candidate (list 1 3 5 -1)) #f 0.001)\n    (check-within (candidate (list 1 3 -2 1)) #t 0.001)\n    (check-within (candidate (list 1 2 3 7)) #f 0.001)\n    (check-within (candidate (list 1 2 5 7)) #f 0.001)\n    (check-within (candidate (list 2 4 -5 3 9 7)) #t 0.001)\n    (check-within (candidate (list 1)) #f 0.001)\n    (check-within (candidate (list 1 3 5 -100)) #f 0.001)\n    (check-within (candidate (list 100 3 5 -100)) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_127_intersection", "language": "rkt", "prompt": "#lang racket\n\n;; You are given two intervals,\n;; where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n;; The given intervals are closed which means that the interval (start, end)\n;; includes both start and end.\n;; For each given interval, it is assumed that its start is less or equal its end.\n;; Your task is to determine whether the length of intersection of these two \n;; intervals is a prime number.\n;; Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n;; which its length is 1, which not a prime number.\n;; If the length of the intersection is a prime number, return \"YES\",\n;; otherwise, return \"NO\".\n;; If the two intervals don't intersect, return \"NO\".\n;; [input/output] samples:\n;; >>> (intersection (list 1 2) (list 2 3))\n;; \"NO\"\n;; >>> (intersection (list -1 1) (list 0 4))\n;; \"NO\"\n;; >>> (intersection (list -3 -1) (list -5 5))\n;; \"YES\"\n(define (intersection interval1 interval2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate intersection))\n    (check-within (candidate (list 1 2) (list 2 3)) \"NO\" 0.001)\n    (check-within (candidate (list -1 1) (list 0 4)) \"NO\" 0.001)\n    (check-within (candidate (list -3 -1) (list -5 5)) \"YES\" 0.001)\n    (check-within (candidate (list -2 2) (list -4 0)) \"YES\" 0.001)\n    (check-within (candidate (list -11 2) (list -1 -1)) \"NO\" 0.001)\n    (check-within (candidate (list 1 2) (list 3 5)) \"NO\" 0.001)\n    (check-within (candidate (list 1 2) (list 1 2)) \"NO\" 0.001)\n    (check-within (candidate (list -2 -2) (list -3 -2)) \"NO\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_127_intersection", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate intersection))\n    (check-within (candidate (list 1 2) (list 2 3)) \"NO\" 0.001)\n    (check-within (candidate (list -1 1) (list 0 4)) \"NO\" 0.001)\n    (check-within (candidate (list -3 -1) (list -5 5)) \"YES\" 0.001)\n    (check-within (candidate (list -2 2) (list -4 0)) \"YES\" 0.001)\n    (check-within (candidate (list -11 2) (list -1 -1)) \"NO\" 0.001)\n    (check-within (candidate (list 1 2) (list 3 5)) \"NO\" 0.001)\n    (check-within (candidate (list 1 2) (list 1 2)) \"NO\" 0.001)\n    (check-within (candidate (list -2 -2) (list -3 -2)) \"NO\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_1_separate_paren_groups", "language": "rkt", "prompt": "#lang racket\n\n;; Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n;; separate those group into separate strings and return the list of those.\n;; Separate groups are balanced (each open brace is properly closed) and not nested within each other\n;; Ignore any spaces in the input string.\n;; >>> (separate_paren_groups \"( ) (( )) (( )( ))\")\n;; (list \"()\" \"(())\" \"(()())\")\n(define (separate_paren_groups paren_string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate separate_paren_groups))\n    (check-within (candidate \"(()()) ((())) () ((())()())\") (list \"(()())\" \"((()))\" \"()\" \"((())()())\") 0.001)\n    (check-within (candidate \"() (()) ((())) (((())))\") (list \"()\" \"(())\" \"((()))\" \"(((())))\") 0.001)\n    (check-within (candidate \"(()(())((())))\") (list \"(()(())((())))\") 0.001)\n    (check-within (candidate \"( ) (( )) (( )( ))\") (list \"()\" \"(())\" \"(()())\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_1_separate_paren_groups", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate separate_paren_groups))\n    (check-within (candidate \"(()()) ((())) () ((())()())\") (list \"(()())\" \"((()))\" \"()\" \"((())()())\") 0.001)\n    (check-within (candidate \"() (()) ((())) (((())))\") (list \"()\" \"(())\" \"((()))\" \"(((())))\") 0.001)\n    (check-within (candidate \"(()(())((())))\") (list \"(()(())((())))\") 0.001)\n    (check-within (candidate \"( ) (( )) (( )( ))\") (list \"()\" \"(())\" \"(()())\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_152_compare", "language": "rkt", "prompt": "#lang racket\n\n;; I think we all remember that feeling when the result of some long-awaited\n;; event is finally known. The feelings and thoughts you have at that moment are\n;; definitely worth noting down and comparing.\n;; Your task is to determine if a person correctly guessed the results of a number of matches.\n;; You are given two lists of scores and guesses of equal length, where each index shows a match. \n;; Return a list of the same length denoting how far off each guess was. If they have guessed correctly,\n;; the value is 0, and if not, the value is the absolute difference between the guess and the score.\n;; example:\n;; >>> (compare (list 1 2 3 4 5 1) (list 1 2 3 4 2 -2))\n;; (list 0 0 0 0 3 3)\n;; >>> (compare (list 0 5 0 0 0 4) (list 4 1 1 0 0 -2))\n;; (list 4 4 1 0 0 6)\n(define (compare game guess)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate compare))\n    (check-within (candidate (list 1 2 3 4 5 1) (list 1 2 3 4 2 -2)) (list 0 0 0 0 3 3) 0.001)\n    (check-within (candidate (list 0 0 0 0 0 0) (list 0 0 0 0 0 0)) (list 0 0 0 0 0 0) 0.001)\n    (check-within (candidate (list 1 2 3) (list -1 -2 -3)) (list 2 4 6) 0.001)\n    (check-within (candidate (list 1 2 3 5) (list -1 2 3 4)) (list 2 0 0 1) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_152_compare", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate compare))\n    (check-within (candidate (list 1 2 3 4 5 1) (list 1 2 3 4 2 -2)) (list 0 0 0 0 3 3) 0.001)\n    (check-within (candidate (list 0 0 0 0 0 0) (list 0 0 0 0 0 0)) (list 0 0 0 0 0 0) 0.001)\n    (check-within (candidate (list 1 2 3) (list -1 -2 -3)) (list 2 4 6) 0.001)\n    (check-within (candidate (list 1 2 3 5) (list -1 2 3 4)) (list 2 0 0 1) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_83_starts_one_ends", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer n, return the count of the numbers of n-digit\n;; positive integers that start or end with 1.\n(define (starts_one_ends n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate starts_one_ends))\n    (check-within (candidate 1) 1 0.001)\n    (check-within (candidate 2) 18 0.001)\n    (check-within (candidate 3) 180 0.001)\n    (check-within (candidate 4) 1800 0.001)\n    (check-within (candidate 5) 18000 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_83_starts_one_ends", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate starts_one_ends))\n    (check-within (candidate 1) 1 0.001)\n    (check-within (candidate 2) 18 0.001)\n    (check-within (candidate 3) 180 0.001)\n    (check-within (candidate 4) 1800 0.001)\n    (check-within (candidate 5) 18000 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function that returns #t if the last character\n;; of a given string is an alphabetical character and is not\n;; a part of a word, and #f otherwise.\n;; Note: \"word\" is a group of characters separated by space.\n;; Examples:\n;; >>> (check_if_last_char_is_a_letter \"apple pie\")\n;; #f\n;; >>> (check_if_last_char_is_a_letter \"apple pi e\")\n;; #t\n;; >>> (check_if_last_char_is_a_letter \"apple pi e \")\n;; #f\n;; >>> (check_if_last_char_is_a_letter \"\")\n;; #f\n(define (check_if_last_char_is_a_letter txt)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate check_if_last_char_is_a_letter))\n    (check-within (candidate \"apple\") #f 0.001)\n    (check-within (candidate \"apple pi e\") #t 0.001)\n    (check-within (candidate \"eeeee\") #f 0.001)\n    (check-within (candidate \"A\") #t 0.001)\n    (check-within (candidate \"Pumpkin pie \") #f 0.001)\n    (check-within (candidate \"Pumpkin pie 1\") #f 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"eeeee e \") #f 0.001)\n    (check-within (candidate \"apple pie\") #f 0.001)\n    (check-within (candidate \"apple pi e \") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate check_if_last_char_is_a_letter))\n    (check-within (candidate \"apple\") #f 0.001)\n    (check-within (candidate \"apple pi e\") #t 0.001)\n    (check-within (candidate \"eeeee\") #f 0.001)\n    (check-within (candidate \"A\") #t 0.001)\n    (check-within (candidate \"Pumpkin pie \") #f 0.001)\n    (check-within (candidate \"Pumpkin pie 1\") #f 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"eeeee e \") #f 0.001)\n    (check-within (candidate \"apple pie\") #f 0.001)\n    (check-within (candidate \"apple pi e \") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_124_valid_date", "language": "rkt", "prompt": "#lang racket\n\n;; You have to write a function which validates a given date string and\n;; returns #t if the date is valid otherwise #f.\n;; The date is valid if all of the following rules are satisfied:\n;; 1. The date string is not empty.\n;; 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n;; 3. The months should not be less than 1 or higher than 12.\n;; 4. The date should be in the format: mm-dd-yyyy\n;; >>> (valid_date \"03-11-2000\")\n;; #t\n;; >>> (valid_date \"15-01-2012\")\n;; #f\n;; >>> (valid_date \"04-0-2040\")\n;; #f\n;; >>> (valid_date \"06-04-2020\")\n;; #t\n;; >>> (valid_date \"06/04/2020\")\n;; #f\n(define (valid_date date)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate valid_date))\n    (check-within (candidate \"03-11-2000\") #t 0.001)\n    (check-within (candidate \"15-01-2012\") #f 0.001)\n    (check-within (candidate \"04-0-2040\") #f 0.001)\n    (check-within (candidate \"06-04-2020\") #t 0.001)\n    (check-within (candidate \"01-01-2007\") #t 0.001)\n    (check-within (candidate \"03-32-2011\") #f 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"04-31-3000\") #f 0.001)\n    (check-within (candidate \"06-06-2005\") #t 0.001)\n    (check-within (candidate \"21-31-2000\") #f 0.001)\n    (check-within (candidate \"04-12-2003\") #t 0.001)\n    (check-within (candidate \"04122003\") #f 0.001)\n    (check-within (candidate \"20030412\") #f 0.001)\n    (check-within (candidate \"2003-04\") #f 0.001)\n    (check-within (candidate \"2003-04-12\") #f 0.001)\n    (check-within (candidate \"04-2003\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_124_valid_date", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate valid_date))\n    (check-within (candidate \"03-11-2000\") #t 0.001)\n    (check-within (candidate \"15-01-2012\") #f 0.001)\n    (check-within (candidate \"04-0-2040\") #f 0.001)\n    (check-within (candidate \"06-04-2020\") #t 0.001)\n    (check-within (candidate \"01-01-2007\") #t 0.001)\n    (check-within (candidate \"03-32-2011\") #f 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"04-31-3000\") #f 0.001)\n    (check-within (candidate \"06-06-2005\") #t 0.001)\n    (check-within (candidate \"21-31-2000\") #f 0.001)\n    (check-within (candidate \"04-12-2003\") #t 0.001)\n    (check-within (candidate \"04122003\") #f 0.001)\n    (check-within (candidate \"20030412\") #f 0.001)\n    (check-within (candidate \"2003-04\") #f 0.001)\n    (check-within (candidate \"2003-04-12\") #f 0.001)\n    (check-within (candidate \"04-2003\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_108_count_nums", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function count_nums which takes a list of integers and returns\n;; the number of elements which has a sum of digits > 0.\n;; If a number is negative, then its first signed digit will be negative:\n;; e.g. -123 has signed digits -1, 2, and 3.\n;; >>> (count_nums (list ))\n;; 0\n;; >>> (count_nums (list -1 11 -11))\n;; 1\n;; >>> (count_nums (list 1 1 2))\n;; 3\n(define (count_nums arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_nums))\n    (check-within (candidate (list )) 0 0.001)\n    (check-within (candidate (list -1 -2 0)) 0 0.001)\n    (check-within (candidate (list 1 1 2 -2 3 4 5)) 6 0.001)\n    (check-within (candidate (list 1 6 9 -6 0 1 5)) 5 0.001)\n    (check-within (candidate (list 1 100 98 -7 1 -1)) 4 0.001)\n    (check-within (candidate (list 12 23 34 -45 -56 0)) 5 0.001)\n    (check-within (candidate (list 0 1)) 1 0.001)\n    (check-within (candidate (list 1)) 1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_108_count_nums", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_nums))\n    (check-within (candidate (list )) 0 0.001)\n    (check-within (candidate (list -1 -2 0)) 0 0.001)\n    (check-within (candidate (list 1 1 2 -2 3 4 5)) 6 0.001)\n    (check-within (candidate (list 1 6 9 -6 0 1 5)) 5 0.001)\n    (check-within (candidate (list 1 100 98 -7 1 -1)) 4 0.001)\n    (check-within (candidate (list 12 23 34 -45 -56 0)) 5 0.001)\n    (check-within (candidate (list 0 1)) 1 0.001)\n    (check-within (candidate (list 1)) 1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_86_anti_shuffle", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that takes a string and returns an ordered version of it.\n;; Ordered version of string, is a string where all words (separated by space)\n;; are replaced by a new word where all the characters arranged in\n;; ascending order based on ascii value.\n;; Note: You should keep the order of words and blank spaces in the sentence.\n;; For example:\n;; >>> (anti_shuffle \"Hi\")\n;; \"Hi\"\n;; >>> (anti_shuffle \"hello\")\n;; \"ehllo\"\n;; >>> (anti_shuffle \"Hello World!!!\")\n;; \"Hello !!!Wdlor\"\n(define (anti_shuffle s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate anti_shuffle))\n    (check-within (candidate \"Hi\") \"Hi\" 0.001)\n    (check-within (candidate \"hello\") \"ehllo\" 0.001)\n    (check-within (candidate \"number\") \"bemnru\" 0.001)\n    (check-within (candidate \"abcd\") \"abcd\" 0.001)\n    (check-within (candidate \"Hello World!!!\") \"Hello !!!Wdlor\" 0.001)\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"Hi. My name is Mister Robot. How are you?\") \".Hi My aemn is Meirst .Rboot How aer ?ouy\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_86_anti_shuffle", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate anti_shuffle))\n    (check-within (candidate \"Hi\") \"Hi\" 0.001)\n    (check-within (candidate \"hello\") \"ehllo\" 0.001)\n    (check-within (candidate \"number\") \"bemnru\" 0.001)\n    (check-within (candidate \"abcd\") \"abcd\" 0.001)\n    (check-within (candidate \"Hello World!!!\") \"Hello !!!Wdlor\" 0.001)\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"Hi. My name is Mister Robot. How are you?\") \".Hi My aemn is Meirst .Rboot How aer ?ouy\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_48_is_palindrome", "language": "rkt", "prompt": "#lang racket\n\n;; Checks if given string is a palindrome\n;; >>> (is_palindrome \"\")\n;; #t\n;; >>> (is_palindrome \"aba\")\n;; #t\n;; >>> (is_palindrome \"aaaaa\")\n;; #t\n;; >>> (is_palindrome \"zbcd\")\n;; #f\n(define (is_palindrome text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_palindrome))\n    (check-within (candidate \"\") #t 0.001)\n    (check-within (candidate \"aba\") #t 0.001)\n    (check-within (candidate \"aaaaa\") #t 0.001)\n    (check-within (candidate \"zbcd\") #f 0.001)\n    (check-within (candidate \"xywyx\") #t 0.001)\n    (check-within (candidate \"xywyz\") #f 0.001)\n    (check-within (candidate \"xywzx\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_48_is_palindrome", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_palindrome))\n    (check-within (candidate \"\") #t 0.001)\n    (check-within (candidate \"aba\") #t 0.001)\n    (check-within (candidate \"aaaaa\") #t 0.001)\n    (check-within (candidate \"zbcd\") #f 0.001)\n    (check-within (candidate \"xywyx\") #t 0.001)\n    (check-within (candidate \"xywyz\") #f 0.001)\n    (check-within (candidate \"xywzx\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_118_get_closest_vowel", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a word. Your task is to find the closest vowel that stands between \n;; two consonants from the right side of the word (case sensitive).\n;; Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n;; find any vowel met the above condition. \n;; You may assume that the given string contains English letter only.\n;; Example:\n;; >>> (get_closest_vowel \"yogurt\")\n;; \"u\"\n;; >>> (get_closest_vowel \"FULL\")\n;; \"U\"\n;; >>> (get_closest_vowel \"quick\")\n;; \"\"\n;; >>> (get_closest_vowel \"ab\")\n;; \"\"\n(define (get_closest_vowel word)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_closest_vowel))\n    (check-within (candidate \"yogurt\") \"u\" 0.001)\n    (check-within (candidate \"full\") \"u\" 0.001)\n    (check-within (candidate \"easy\") \"\" 0.001)\n    (check-within (candidate \"eAsy\") \"\" 0.001)\n    (check-within (candidate \"ali\") \"\" 0.001)\n    (check-within (candidate \"bad\") \"a\" 0.001)\n    (check-within (candidate \"most\") \"o\" 0.001)\n    (check-within (candidate \"ab\") \"\" 0.001)\n    (check-within (candidate \"ba\") \"\" 0.001)\n    (check-within (candidate \"quick\") \"\" 0.001)\n    (check-within (candidate \"anime\") \"i\" 0.001)\n    (check-within (candidate \"Asia\") \"\" 0.001)\n    (check-within (candidate \"Above\") \"o\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_118_get_closest_vowel", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_closest_vowel))\n    (check-within (candidate \"yogurt\") \"u\" 0.001)\n    (check-within (candidate \"full\") \"u\" 0.001)\n    (check-within (candidate \"easy\") \"\" 0.001)\n    (check-within (candidate \"eAsy\") \"\" 0.001)\n    (check-within (candidate \"ali\") \"\" 0.001)\n    (check-within (candidate \"bad\") \"a\" 0.001)\n    (check-within (candidate \"most\") \"o\" 0.001)\n    (check-within (candidate \"ab\") \"\" 0.001)\n    (check-within (candidate \"ba\") \"\" 0.001)\n    (check-within (candidate \"quick\") \"\" 0.001)\n    (check-within (candidate \"anime\") \"i\" 0.001)\n    (check-within (candidate \"Asia\") \"\" 0.001)\n    (check-within (candidate \"Above\") \"o\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_31_is_prime", "language": "rkt", "prompt": "#lang racket\n\n;; Return true if a given number is prime, and false otherwise.\n;; >>> (is_prime 6)\n;; #f\n;; >>> (is_prime 101)\n;; #t\n;; >>> (is_prime 11)\n;; #t\n;; >>> (is_prime 13441)\n;; #t\n;; >>> (is_prime 61)\n;; #t\n;; >>> (is_prime 4)\n;; #f\n;; >>> (is_prime 1)\n;; #f\n(define (is_prime n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_prime))\n    (check-within (candidate 6) #f 0.001)\n    (check-within (candidate 101) #t 0.001)\n    (check-within (candidate 11) #t 0.001)\n    (check-within (candidate 13441) #t 0.001)\n    (check-within (candidate 61) #t 0.001)\n    (check-within (candidate 4) #f 0.001)\n    (check-within (candidate 1) #f 0.001)\n    (check-within (candidate 5) #t 0.001)\n    (check-within (candidate 11) #t 0.001)\n    (check-within (candidate 17) #t 0.001)\n    (check-within (candidate 85) #f 0.001)\n    (check-within (candidate 77) #f 0.001)\n    (check-within (candidate 255379) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_31_is_prime", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_prime))\n    (check-within (candidate 6) #f 0.001)\n    (check-within (candidate 101) #t 0.001)\n    (check-within (candidate 11) #t 0.001)\n    (check-within (candidate 13441) #t 0.001)\n    (check-within (candidate 61) #t 0.001)\n    (check-within (candidate 4) #f 0.001)\n    (check-within (candidate 1) #f 0.001)\n    (check-within (candidate 5) #t 0.001)\n    (check-within (candidate 11) #t 0.001)\n    (check-within (candidate 17) #t 0.001)\n    (check-within (candidate 85) #f 0.001)\n    (check-within (candidate 77) #f 0.001)\n    (check-within (candidate 255379) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_144_simplify", "language": "rkt", "prompt": "#lang racket\n\n;; Your task is to implement a function that will simplify the expression\n;; x * n. The function returns #t if x * n evaluates to a whole number and #f\n;; otherwise. Both x and n, are string representation of a fraction, and have the following format,\n;; <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n;; You can assume that x, and n are valid fractions, and do not have zero as denominator.\n;; >>> (simplify \"1/5\" \"5/1\")\n;; #t\n;; >>> (simplify \"1/6\" \"2/1\")\n;; #f\n;; >>> (simplify \"7/10\" \"10/2\")\n;; #f\n(define (simplify x n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate simplify))\n    (check-within (candidate \"1/5\" \"5/1\") #t 0.001)\n    (check-within (candidate \"1/6\" \"2/1\") #f 0.001)\n    (check-within (candidate \"5/1\" \"3/1\") #t 0.001)\n    (check-within (candidate \"7/10\" \"10/2\") #f 0.001)\n    (check-within (candidate \"2/10\" \"50/10\") #t 0.001)\n    (check-within (candidate \"7/2\" \"4/2\") #t 0.001)\n    (check-within (candidate \"11/6\" \"6/1\") #t 0.001)\n    (check-within (candidate \"2/3\" \"5/2\") #f 0.001)\n    (check-within (candidate \"5/2\" \"3/5\") #f 0.001)\n    (check-within (candidate \"2/4\" \"8/4\") #t 0.001)\n    (check-within (candidate \"2/4\" \"4/2\") #t 0.001)\n    (check-within (candidate \"1/5\" \"5/1\") #t 0.001)\n    (check-within (candidate \"1/5\" \"1/5\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_144_simplify", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate simplify))\n    (check-within (candidate \"1/5\" \"5/1\") #t 0.001)\n    (check-within (candidate \"1/6\" \"2/1\") #f 0.001)\n    (check-within (candidate \"5/1\" \"3/1\") #t 0.001)\n    (check-within (candidate \"7/10\" \"10/2\") #f 0.001)\n    (check-within (candidate \"2/10\" \"50/10\") #t 0.001)\n    (check-within (candidate \"7/2\" \"4/2\") #t 0.001)\n    (check-within (candidate \"11/6\" \"6/1\") #t 0.001)\n    (check-within (candidate \"2/3\" \"5/2\") #f 0.001)\n    (check-within (candidate \"5/2\" \"3/5\") #f 0.001)\n    (check-within (candidate \"2/4\" \"8/4\") #t 0.001)\n    (check-within (candidate \"2/4\" \"4/2\") #t 0.001)\n    (check-within (candidate \"1/5\" \"5/1\") #t 0.001)\n    (check-within (candidate \"1/5\" \"1/5\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_78_hex_key", "language": "rkt", "prompt": "#lang racket\n\n;; You have been tasked to write a function that receives \n;; a hexadecimal number as a string and counts the number of hexadecimal \n;; digits that are primes (prime number, or a prime, is a natural number \n;; greater than 1 that is not a product of two smaller natural numbers).\n;; Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n;; Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n;; So you have to determine a number of the following digits: 2, 3, 5, 7, \n;; B (=decimal 11), D (=decimal 13).\n;; Note: you may assume the input is always correct or empty string, \n;; and symbols A,B,C,D,E,F are always uppercase.\n;; Examples:\n;; >>> (hex_key \"AB\")\n;; 1\n;; >>> (hex_key \"1077E\")\n;; 2\n;; >>> (hex_key \"ABED1A33\")\n;; 4\n;; >>> (hex_key \"123456789ABCDEF0\")\n;; 6\n;; >>> (hex_key \"2020\")\n;; 2\n(define (hex_key num)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate hex_key))\n    (check-within (candidate \"AB\") 1 0.001)\n    (check-within (candidate \"1077E\") 2 0.001)\n    (check-within (candidate \"ABED1A33\") 4 0.001)\n    (check-within (candidate \"2020\") 2 0.001)\n    (check-within (candidate \"123456789ABCDEF0\") 6 0.001)\n    (check-within (candidate \"112233445566778899AABBCCDDEEFF00\") 12 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_78_hex_key", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate hex_key))\n    (check-within (candidate \"AB\") 1 0.001)\n    (check-within (candidate \"1077E\") 2 0.001)\n    (check-within (candidate \"ABED1A33\") 4 0.001)\n    (check-within (candidate \"2020\") 2 0.001)\n    (check-within (candidate \"123456789ABCDEF0\") 6 0.001)\n    (check-within (candidate \"112233445566778899AABBCCDDEEFF00\") 12 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_143_words_in_sentence", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a string representing a sentence,\n;; the sentence contains some words separated by a space,\n;; and you have to return a string that contains the words from the original sentence,\n;; whose lengths are prime numbers,\n;; the order of the words in the new string should be the same as the original one.\n;; Example 1:\n;; >>> (words_in_sentence \"This is a test\")\n;; \"is\"\n;; Example 2:\n;; >>> (words_in_sentence \"lets go for swimming\")\n;; \"go for\"\n;; Constraints:\n;; * 1 <= len(sentence) <= 100\n;; * sentence contains only letters\n(define (words_in_sentence sentence)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate words_in_sentence))\n    (check-within (candidate \"This is a test\") \"is\" 0.001)\n    (check-within (candidate \"lets go for swimming\") \"go for\" 0.001)\n    (check-within (candidate \"there is no place available here\") \"there is no place\" 0.001)\n    (check-within (candidate \"Hi I am Hussein\") \"Hi am Hussein\" 0.001)\n    (check-within (candidate \"go for it\") \"go for it\" 0.001)\n    (check-within (candidate \"here\") \"\" 0.001)\n    (check-within (candidate \"here is\") \"is\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_143_words_in_sentence", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate words_in_sentence))\n    (check-within (candidate \"This is a test\") \"is\" 0.001)\n    (check-within (candidate \"lets go for swimming\") \"go for\" 0.001)\n    (check-within (candidate \"there is no place available here\") \"there is no place\" 0.001)\n    (check-within (candidate \"Hi I am Hussein\") \"Hi am Hussein\" 0.001)\n    (check-within (candidate \"go for it\") \"go for it\" 0.001)\n    (check-within (candidate \"here\") \"\" 0.001)\n    (check-within (candidate \"here is\") \"is\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_111_histogram", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string representing a space separated lowercase letters, return a hash\n;; of the letter with the most repetition and containing the corresponding count.\n;; If several letters have the same occurrence, return all of them.\n;; Example:\n;; >>> (histogram \"a b c\")\n;; #hash((\"a\" .  1) (\"b\" .  1) (\"c\" .  1))\n;; >>> (histogram \"a b b a\")\n;; #hash((\"a\" .  2) (\"b\" .  2))\n;; >>> (histogram \"a b c a b\")\n;; #hash((\"a\" .  2) (\"b\" .  2))\n;; >>> (histogram \"b b b b a\")\n;; #hash((\"b\" .  4))\n;; >>> (histogram \"\")\n;; #hash()\n(define (histogram test)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate histogram))\n    (check-within (candidate \"a b b a\") #hash((\"a\" .  2) (\"b\" .  2)) 0.001)\n    (check-within (candidate \"a b c a b\") #hash((\"a\" .  2) (\"b\" .  2)) 0.001)\n    (check-within (candidate \"a b c d g\") #hash((\"a\" .  1) (\"b\" .  1) (\"c\" .  1) (\"d\" .  1) (\"g\" .  1)) 0.001)\n    (check-within (candidate \"r t g\") #hash((\"r\" .  1) (\"t\" .  1) (\"g\" .  1)) 0.001)\n    (check-within (candidate \"b b b b a\") #hash((\"b\" .  4)) 0.001)\n    (check-within (candidate \"r t g\") #hash((\"r\" .  1) (\"t\" .  1) (\"g\" .  1)) 0.001)\n    (check-within (candidate \"\") #hash() 0.001)\n    (check-within (candidate \"a\") #hash((\"a\" .  1)) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_111_histogram", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate histogram))\n    (check-within (candidate \"a b b a\") #hash((\"a\" .  2) (\"b\" .  2)) 0.001)\n    (check-within (candidate \"a b c a b\") #hash((\"a\" .  2) (\"b\" .  2)) 0.001)\n    (check-within (candidate \"a b c d g\") #hash((\"a\" .  1) (\"b\" .  1) (\"c\" .  1) (\"d\" .  1) (\"g\" .  1)) 0.001)\n    (check-within (candidate \"r t g\") #hash((\"r\" .  1) (\"t\" .  1) (\"g\" .  1)) 0.001)\n    (check-within (candidate \"b b b b a\") #hash((\"b\" .  4)) 0.001)\n    (check-within (candidate \"r t g\") #hash((\"r\" .  1) (\"t\" .  1) (\"g\" .  1)) 0.001)\n    (check-within (candidate \"\") #hash() 0.001)\n    (check-within (candidate \"a\") #hash((\"a\" .  1)) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_87_get_row", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a 2 dimensional data, as a nested lists,\n;; which is similar to matrix, however, unlike matrices,\n;; each row may contain a different number of columns.\n;; Given lst, and integer x, find integers x in the list,\n;; and return list of lists, [(x1, y1), (x2, y2) ...] such that\n;; each list is a coordinate - (row, columns), starting with 0.\n;; Sort coordinates initially by rows in ascending order.\n;; Also, sort coordinates of the row by columns in descending order.\n;; Examples:\n;; >>> (get_row (list (list 1 2 3 4 5 6) (list 1 2 3 4 1 6) (list 1 2 3 4 5 1)) 1)\n;; (list (list 0 0) (list 1 4) (list 1 0) (list 2 5) (list 2 0))\n;; >>> (get_row (list ) 1)\n;; (list )\n;; >>> (get_row (list (list ) (list 1) (list 1 2 3)) 3)\n;; (list (list 2 2))\n(define (get_row lst x)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_row))\n    (check-within (candidate (list (list 1 2 3 4 5 6) (list 1 2 3 4 1 6) (list 1 2 3 4 5 1)) 1) (list (list 0 0) (list 1 4) (list 1 0) (list 2 5) (list 2 0)) 0.001)\n    (check-within (candidate (list (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6)) 2) (list (list 0 1) (list 1 1) (list 2 1) (list 3 1) (list 4 1) (list 5 1)) 0.001)\n    (check-within (candidate (list (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 1 3 4 5 6) (list 1 2 1 4 5 6) (list 1 2 3 1 5 6) (list 1 2 3 4 1 6) (list 1 2 3 4 5 1)) 1) (list (list 0 0) (list 1 0) (list 2 1) (list 2 0) (list 3 2) (list 3 0) (list 4 3) (list 4 0) (list 5 4) (list 5 0) (list 6 5) (list 6 0)) 0.001)\n    (check-within (candidate (list ) 1) (list ) 0.001)\n    (check-within (candidate (list (list 1)) 2) (list ) 0.001)\n    (check-within (candidate (list (list ) (list 1) (list 1 2 3)) 3) (list (list 2 2)) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_87_get_row", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_row))\n    (check-within (candidate (list (list 1 2 3 4 5 6) (list 1 2 3 4 1 6) (list 1 2 3 4 5 1)) 1) (list (list 0 0) (list 1 4) (list 1 0) (list 2 5) (list 2 0)) 0.001)\n    (check-within (candidate (list (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 2 3 4 5 6)) 2) (list (list 0 1) (list 1 1) (list 2 1) (list 3 1) (list 4 1) (list 5 1)) 0.001)\n    (check-within (candidate (list (list 1 2 3 4 5 6) (list 1 2 3 4 5 6) (list 1 1 3 4 5 6) (list 1 2 1 4 5 6) (list 1 2 3 1 5 6) (list 1 2 3 4 1 6) (list 1 2 3 4 5 1)) 1) (list (list 0 0) (list 1 0) (list 2 1) (list 2 0) (list 3 2) (list 3 0) (list 4 3) (list 4 0) (list 5 4) (list 5 0) (list 6 5) (list 6 0)) 0.001)\n    (check-within (candidate (list ) 1) (list ) 0.001)\n    (check-within (candidate (list (list 1)) 2) (list ) 0.001)\n    (check-within (candidate (list (list ) (list 1) (list 1 2 3)) 3) (list (list 2 2)) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_123_get_odd_collatz", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n;; The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n;; as follows: start with any positive integer n. Then each term is obtained from the \n;; previous term as follows: if the previous term is even, the next term is one half of \n;; the previous term. If the previous term is odd, the next term is 3 times the previous\n;; term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n;; Note: \n;; 1. Collatz(1) is [1].\n;; 2. returned list sorted in increasing order.\n;; For example:\n;; get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n;; >>> (get_odd_collatz 5)\n;; (list 1 5)\n(define (get_odd_collatz n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_odd_collatz))\n    (check-within (candidate 14) (list 1 5 7 11 13 17) 0.001)\n    (check-within (candidate 5) (list 1 5) 0.001)\n    (check-within (candidate 12) (list 1 3 5) 0.001)\n    (check-within (candidate 1) (list 1) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_123_get_odd_collatz", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_odd_collatz))\n    (check-within (candidate 14) (list 1 5 7 11 13 17) 0.001)\n    (check-within (candidate 5) (list 1 5) 0.001)\n    (check-within (candidate 12) (list 1 3 5) 0.001)\n    (check-within (candidate 1) (list 1) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_135_can_arrange", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function which returns the largest index of an element which\n;; is not greater than or equal to the element immediately preceding it. If\n;; no such element exists then return -1. The given list will not contain\n;; duplicate values.\n;; Examples:\n;; >>> (can_arrange (list 1 2 4 3 5))\n;; 3\n;; >>> (can_arrange (list 1 2 3))\n;; -1\n(define (can_arrange arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate can_arrange))\n    (check-within (candidate (list 1 2 4 3 5)) 3 0.001)\n    (check-within (candidate (list 1 2 4 5)) -1 0.001)\n    (check-within (candidate (list 1 4 2 5 6 7 8 9 10)) 2 0.001)\n    (check-within (candidate (list 4 8 5 7 3)) 4 0.001)\n    (check-within (candidate (list )) -1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_135_can_arrange", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate can_arrange))\n    (check-within (candidate (list 1 2 4 3 5)) 3 0.001)\n    (check-within (candidate (list 1 2 4 5)) -1 0.001)\n    (check-within (candidate (list 1 4 2 5 6 7 8 9 10)) 2 0.001)\n    (check-within (candidate (list 4 8 5 7 3)) 4 0.001)\n    (check-within (candidate (list )) -1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_19_sort_numbers", "language": "rkt", "prompt": "#lang racket\n\n;; Input is a space-delimited string of numberals from 'zero' to 'nine'.\n;; Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n;; Return the string with numbers sorted from smallest to largest\n;; >>> (sort_numbers \"three one five\")\n;; \"one three five\"\n(define (sort_numbers numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_numbers))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"three\") \"three\" 0.001)\n    (check-within (candidate \"three five nine\") \"three five nine\" 0.001)\n    (check-within (candidate \"five zero four seven nine eight\") \"zero four five seven eight nine\" 0.001)\n    (check-within (candidate \"six five four three two one zero\") \"zero one two three four five six\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_19_sort_numbers", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_numbers))\n    (check-within (candidate \"\") \"\" 0.001)\n    (check-within (candidate \"three\") \"three\" 0.001)\n    (check-within (candidate \"three five nine\") \"three five nine\" 0.001)\n    (check-within (candidate \"five zero four seven nine eight\") \"zero four five seven eight nine\" 0.001)\n    (check-within (candidate \"six five four three two one zero\") \"zero one two three four five six\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_65_circular_shift", "language": "rkt", "prompt": "#lang racket\n\n;; Circular shift the digits of the integer x, shift the digits right by shift\n;; and return the result as a string.\n;; If shift > number of digits, return digits reversed.\n;; >>> (circular_shift 12 1)\n;; \"21\"\n;; >>> (circular_shift 12 2)\n;; \"12\"\n(define (circular_shift x shift)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate circular_shift))\n    (check-within (candidate 100 2) \"001\" 0.001)\n    (check-within (candidate 12 2) \"12\" 0.001)\n    (check-within (candidate 97 8) \"79\" 0.001)\n    (check-within (candidate 12 1) \"21\" 0.001)\n    (check-within (candidate 11 101) \"11\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_65_circular_shift", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate circular_shift))\n    (check-within (candidate 100 2) \"001\" 0.001)\n    (check-within (candidate 12 2) \"12\" 0.001)\n    (check-within (candidate 97 8) \"79\" 0.001)\n    (check-within (candidate 12 1) \"21\" 0.001)\n    (check-within (candidate 11 101) \"11\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_142_sum_squares", "language": "rkt", "prompt": "#lang racket\n\n;; \"\n;; This function will take a list of integers. For all entries in the list, the function shall square the integer entry if its index is a \n;; multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n;; change the entries in the list whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n;; Examples:\n;; >>> lst\n;; (list 1 2 3)\n;; >>> lst\n;; (list )\n;; >>> lst\n;; (list -1 -5 2 -1 -5)\n(define (sum_squares lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_squares))\n    (check-within (candidate (list 1 2 3)) 6 0.001)\n    (check-within (candidate (list 1 4 9)) 14 0.001)\n    (check-within (candidate (list )) 0 0.001)\n    (check-within (candidate (list 1 1 1 1 1 1 1 1 1)) 9 0.001)\n    (check-within (candidate (list -1 -1 -1 -1 -1 -1 -1 -1 -1)) -3 0.001)\n    (check-within (candidate (list 0)) 0 0.001)\n    (check-within (candidate (list -1 -5 2 -1 -5)) -126 0.001)\n    (check-within (candidate (list -56 -99 1 0 -2)) 3030 0.001)\n    (check-within (candidate (list -1 0 0 0 0 0 0 0 -1)) 0 0.001)\n    (check-within (candidate (list -16 -9 -2 36 36 26 -20 25 -40 20 -4 12 -26 35 37)) -14196 0.001)\n    (check-within (candidate (list -1 -3 17 -1 -15 13 -1 14 -14 -12 -5 14 -14 6 13 11 16 16 4 10)) -1448 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_142_sum_squares", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_squares))\n    (check-within (candidate (list 1 2 3)) 6 0.001)\n    (check-within (candidate (list 1 4 9)) 14 0.001)\n    (check-within (candidate (list )) 0 0.001)\n    (check-within (candidate (list 1 1 1 1 1 1 1 1 1)) 9 0.001)\n    (check-within (candidate (list -1 -1 -1 -1 -1 -1 -1 -1 -1)) -3 0.001)\n    (check-within (candidate (list 0)) 0 0.001)\n    (check-within (candidate (list -1 -5 2 -1 -5)) -126 0.001)\n    (check-within (candidate (list -56 -99 1 0 -2)) 3030 0.001)\n    (check-within (candidate (list -1 0 0 0 0 0 0 0 -1)) 0 0.001)\n    (check-within (candidate (list -16 -9 -2 36 36 26 -20 25 -40 20 -4 12 -26 35 37)) -14196 0.001)\n    (check-within (candidate (list -1 -3 17 -1 -15 13 -1 14 -14 -12 -5 14 -14 6 13 11 16 16 4 10)) -1448 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_94_skjkasdkd", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a list of integers.\n;; You need to find the largest prime value and return the sum of its digits.\n;; Examples:\n;; >>> (skjkasdkd (list 0 3 2 1 3 5 7 4 5 5 5 2 181 32 4 32 3 2 32 324 4 3))\n;; 10\n;; >>> (skjkasdkd (list 1 0 1 8 2 4597 2 1 3 40 1 2 1 2 4 2 5 1))\n;; 25\n;; >>> (skjkasdkd (list 1 3 1 32 5107 34 83278 109 163 23 2323 32 30 1 9 3))\n;; 13\n;; >>> (skjkasdkd (list 0 724 32 71 99 32 6 0 5 91 83 0 5 6))\n;; 11\n;; >>> (skjkasdkd (list 0 81 12 3 1 21))\n;; 3\n;; >>> (skjkasdkd (list 0 8 1 2 1 7))\n;; 7\n(define (skjkasdkd lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate skjkasdkd))\n    (check-within (candidate (list 0 3 2 1 3 5 7 4 5 5 5 2 181 32 4 32 3 2 32 324 4 3)) 10 0.001)\n    (check-within (candidate (list 1 0 1 8 2 4597 2 1 3 40 1 2 1 2 4 2 5 1)) 25 0.001)\n    (check-within (candidate (list 1 3 1 32 5107 34 83278 109 163 23 2323 32 30 1 9 3)) 13 0.001)\n    (check-within (candidate (list 0 724 32 71 99 32 6 0 5 91 83 0 5 6)) 11 0.001)\n    (check-within (candidate (list 0 81 12 3 1 21)) 3 0.001)\n    (check-within (candidate (list 0 8 1 2 1 7)) 7 0.001)\n    (check-within (candidate (list 8191)) 19 0.001)\n    (check-within (candidate (list 8191 123456 127 7)) 19 0.001)\n    (check-within (candidate (list 127 97 8192)) 10 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_94_skjkasdkd", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate skjkasdkd))\n    (check-within (candidate (list 0 3 2 1 3 5 7 4 5 5 5 2 181 32 4 32 3 2 32 324 4 3)) 10 0.001)\n    (check-within (candidate (list 1 0 1 8 2 4597 2 1 3 40 1 2 1 2 4 2 5 1)) 25 0.001)\n    (check-within (candidate (list 1 3 1 32 5107 34 83278 109 163 23 2323 32 30 1 9 3)) 13 0.001)\n    (check-within (candidate (list 0 724 32 71 99 32 6 0 5 91 83 0 5 6)) 11 0.001)\n    (check-within (candidate (list 0 81 12 3 1 21)) 3 0.001)\n    (check-within (candidate (list 0 8 1 2 1 7)) 7 0.001)\n    (check-within (candidate (list 8191)) 19 0.001)\n    (check-within (candidate (list 8191 123456 127 7)) 19 0.001)\n    (check-within (candidate (list 127 97 8192)) 10 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_8_sum_product", "language": "rkt", "prompt": "#lang racket\n\n;; For a given list of integers, return a list consisting of a sum and a product of all the integers in a list.\n;; Empty sum should be equal to 0 and empty product should be equal to 1.\n;; >>> (sum_product (list ))\n;; (list 0 1)\n;; >>> (sum_product (list 1 2 3 4))\n;; (list 10 24)\n(define (sum_product numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_product))\n    (check-within (candidate (list )) (list 0 1) 0.001)\n    (check-within (candidate (list 1 1 1)) (list 3 1) 0.001)\n    (check-within (candidate (list 100 0)) (list 100 0) 0.001)\n    (check-within (candidate (list 3 5 7)) (list 15 105) 0.001)\n    (check-within (candidate (list 10)) (list 10 10) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_8_sum_product", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_product))\n    (check-within (candidate (list )) (list 0 1) 0.001)\n    (check-within (candidate (list 1 1 1)) (list 3 1) 0.001)\n    (check-within (candidate (list 100 0)) (list 100 0) 0.001)\n    (check-within (candidate (list 3 5 7)) (list 15 105) 0.001)\n    (check-within (candidate (list 10)) (list 10 10) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_102_choose_num", "language": "rkt", "prompt": "#lang racket\n\n;; This function takes two positive numbers x and y and returns the\n;; biggest even integer number that is in the range [x, y] inclusive. If \n;; there's no such number, then the function should return -1.\n;; For example:\n;; >>> (choose_num 12 15)\n;; 14\n;; >>> (choose_num 13 12)\n;; -1\n(define (choose_num x y)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate choose_num))\n    (check-within (candidate 12 15) 14 0.001)\n    (check-within (candidate 13 12) -1 0.001)\n    (check-within (candidate 33 12354) 12354 0.001)\n    (check-within (candidate 5234 5233) -1 0.001)\n    (check-within (candidate 6 29) 28 0.001)\n    (check-within (candidate 27 10) -1 0.001)\n    (check-within (candidate 7 7) -1 0.001)\n    (check-within (candidate 546 546) 546 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_102_choose_num", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate choose_num))\n    (check-within (candidate 12 15) 14 0.001)\n    (check-within (candidate 13 12) -1 0.001)\n    (check-within (candidate 33 12354) 12354 0.001)\n    (check-within (candidate 5234 5233) -1 0.001)\n    (check-within (candidate 6 29) 28 0.001)\n    (check-within (candidate 27 10) -1 0.001)\n    (check-within (candidate 7 7) -1 0.001)\n    (check-within (candidate 546 546) 546 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function that returns a list (a, b), where 'a' is\n;; the largest of negative integers, and 'b' is the smallest\n;; of positive integers in a list.\n;; If there is no negative or positive integers, return them as #f.\n;; Examples:\n;; >>> (largest_smallest_integers (list 2 4 1 3 5 7))\n;; (list #f 1)\n;; >>> (largest_smallest_integers (list ))\n;; (list #f #f)\n;; >>> (largest_smallest_integers (list 0))\n;; (list #f #f)\n(define (largest_smallest_integers lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate largest_smallest_integers))\n    (check-within (candidate (list 2 4 1 3 5 7)) (list #f 1) 0.001)\n    (check-within (candidate (list 2 4 1 3 5 7 0)) (list #f 1) 0.001)\n    (check-within (candidate (list 1 3 2 4 5 6 -2)) (list -2 1) 0.001)\n    (check-within (candidate (list 4 5 3 6 2 7 -7)) (list -7 2) 0.001)\n    (check-within (candidate (list 7 3 8 4 9 2 5 -9)) (list -9 2) 0.001)\n    (check-within (candidate (list )) (list #f #f) 0.001)\n    (check-within (candidate (list 0)) (list #f #f) 0.001)\n    (check-within (candidate (list -1 -3 -5 -6)) (list -1 #f) 0.001)\n    (check-within (candidate (list -1 -3 -5 -6 0)) (list -1 #f) 0.001)\n    (check-within (candidate (list -6 -4 -4 -3 1)) (list -3 1) 0.001)\n    (check-within (candidate (list -6 -4 -4 -3 -100 1)) (list -3 1) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_136_largest_smallest_integers", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate largest_smallest_integers))\n    (check-within (candidate (list 2 4 1 3 5 7)) (list #f 1) 0.001)\n    (check-within (candidate (list 2 4 1 3 5 7 0)) (list #f 1) 0.001)\n    (check-within (candidate (list 1 3 2 4 5 6 -2)) (list -2 1) 0.001)\n    (check-within (candidate (list 4 5 3 6 2 7 -7)) (list -7 2) 0.001)\n    (check-within (candidate (list 7 3 8 4 9 2 5 -9)) (list -9 2) 0.001)\n    (check-within (candidate (list )) (list #f #f) 0.001)\n    (check-within (candidate (list 0)) (list #f #f) 0.001)\n    (check-within (candidate (list -1 -3 -5 -6)) (list -1 #f) 0.001)\n    (check-within (candidate (list -1 -3 -5 -6 0)) (list -1 #f) 0.001)\n    (check-within (candidate (list -6 -4 -4 -3 1)) (list -3 1) 0.001)\n    (check-within (candidate (list -6 -4 -4 -3 -100 1)) (list -3 1) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_16_count_distinct_characters", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string, find out how many distinct characters (regardless of case) does it consist of\n;; >>> (count_distinct_characters \"xyzXYZ\")\n;; 3\n;; >>> (count_distinct_characters \"Jerry\")\n;; 4\n(define (count_distinct_characters string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_distinct_characters))\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"abcde\") 5 0.001)\n    (check-within (candidate \"abcdecadeCADE\") 5 0.001)\n    (check-within (candidate \"aaaaAAAAaaaa\") 1 0.001)\n    (check-within (candidate \"Jerry jERRY JeRRRY\") 5 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_16_count_distinct_characters", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate count_distinct_characters))\n    (check-within (candidate \"\") 0 0.001)\n    (check-within (candidate \"abcde\") 5 0.001)\n    (check-within (candidate \"abcdecadeCADE\") 5 0.001)\n    (check-within (candidate \"aaaaAAAAaaaa\") 1 0.001)\n    (check-within (candidate \"Jerry jERRY JeRRRY\") 5 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_100_make_a_pile", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer n, you have to make a pile of n levels of stones.\n;; The first level has n stones.\n;; The number of stones in the next level is:\n;; - the next odd number if n is odd.\n;; - the next even number if n is even.\n;; Return the number of stones in each level in a list, where element at index\n;; i represents the number of stones in the level (i+1).\n;; Examples:\n;; >>> (make_a_pile 3)\n;; (list 3 5 7)\n(define (make_a_pile n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate make_a_pile))\n    (check-within (candidate 3) (list 3 5 7) 0.001)\n    (check-within (candidate 4) (list 4 6 8 10) 0.001)\n    (check-within (candidate 5) (list 5 7 9 11 13) 0.001)\n    (check-within (candidate 6) (list 6 8 10 12 14 16) 0.001)\n    (check-within (candidate 8) (list 8 10 12 14 16 18 20 22) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_100_make_a_pile", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate make_a_pile))\n    (check-within (candidate 3) (list 3 5 7) 0.001)\n    (check-within (candidate 4) (list 4 6 8 10) 0.001)\n    (check-within (candidate 5) (list 5 7 9 11 13) 0.001)\n    (check-within (candidate 6) (list 6 8 10 12 14 16) 0.001)\n    (check-within (candidate 8) (list 8 10 12 14 16 18 20 22) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_128_prod_signs", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a list arr of integers and you need to return\n;; sum of magnitudes of integers multiplied by product of all signs\n;; of each number in the list, represented by 1, -1 or 0.\n;; Note: return #f for empty arr.\n;; Example:\n;; >>> (prod_signs (list 1 2 2 -4))\n;; 9\n;; >>> (prod_signs (list 0 1))\n;; 0\n;; >>> (prod_signs (list ))\n;; #f\n(define (prod_signs arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate prod_signs))\n    (check-within (candidate (list 1 2 2 -4)) -9 0.001)\n    (check-within (candidate (list 0 1)) 0 0.001)\n    (check-within (candidate (list 1 1 1 2 3 -1 1)) -10 0.001)\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list 2 4 1 2 -1 -1 9)) 20 0.001)\n    (check-within (candidate (list -1 1 -1 1)) 4 0.001)\n    (check-within (candidate (list -1 1 1 1)) -4 0.001)\n    (check-within (candidate (list -1 1 1 0)) 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_128_prod_signs", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate prod_signs))\n    (check-within (candidate (list 1 2 2 -4)) -9 0.001)\n    (check-within (candidate (list 0 1)) 0 0.001)\n    (check-within (candidate (list 1 1 1 2 3 -1 1)) -10 0.001)\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list 2 4 1 2 -1 -1 9)) 20 0.001)\n    (check-within (candidate (list -1 1 -1 1)) 4 0.001)\n    (check-within (candidate (list -1 1 1 1)) -4 0.001)\n    (check-within (candidate (list -1 1 1 0)) 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_114_minSubArraySum", "language": "rkt", "prompt": "#lang racket\n\n;; Given a list of integers nums, find the minimum sum of any non-empty sub-list\n;; of nums.\n;; Example\n;; >>> (minSubArraySum (list 2 3 4 1 2 4))\n;; 1\n;; >>> (minSubArraySum (list -1 -2 -3))\n;; -6\n(define (minSubArraySum nums)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate minSubArraySum))\n    (check-within (candidate (list 2 3 4 1 2 4)) 1 0.001)\n    (check-within (candidate (list -1 -2 -3)) -6 0.001)\n    (check-within (candidate (list -1 -2 -3 2 -10)) -14 0.001)\n    (check-within (candidate (list -9999999999999999)) -9999999999999999 0.001)\n    (check-within (candidate (list 0 10 20 1000000)) 0 0.001)\n    (check-within (candidate (list -1 -2 -3 10 -5)) -6 0.001)\n    (check-within (candidate (list 100 -1 -2 -3 10 -5)) -6 0.001)\n    (check-within (candidate (list 10 11 13 8 3 4)) 3 0.001)\n    (check-within (candidate (list 100 -33 32 -1 0 -2)) -33 0.001)\n    (check-within (candidate (list -10)) -10 0.001)\n    (check-within (candidate (list 7)) 7 0.001)\n    (check-within (candidate (list 1 -1)) -1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_114_minSubArraySum", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate minSubArraySum))\n    (check-within (candidate (list 2 3 4 1 2 4)) 1 0.001)\n    (check-within (candidate (list -1 -2 -3)) -6 0.001)\n    (check-within (candidate (list -1 -2 -3 2 -10)) -14 0.001)\n    (check-within (candidate (list -9999999999999999)) -9999999999999999 0.001)\n    (check-within (candidate (list 0 10 20 1000000)) 0 0.001)\n    (check-within (candidate (list -1 -2 -3 10 -5)) -6 0.001)\n    (check-within (candidate (list 100 -1 -2 -3 10 -5)) -6 0.001)\n    (check-within (candidate (list 10 11 13 8 3 4)) 3 0.001)\n    (check-within (candidate (list 100 -33 32 -1 0 -2)) -33 0.001)\n    (check-within (candidate (list -10)) -10 0.001)\n    (check-within (candidate (list 7)) 7 0.001)\n    (check-within (candidate (list 1 -1)) -1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_15_string_sequence", "language": "rkt", "prompt": "#lang racket\n\n;; Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n;; >>> (string_sequence 0)\n;; \"0\"\n;; >>> (string_sequence 5)\n;; \"0 1 2 3 4 5\"\n(define (string_sequence n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate string_sequence))\n    (check-within (candidate 0) \"0\" 0.001)\n    (check-within (candidate 3) \"0 1 2 3\" 0.001)\n    (check-within (candidate 10) \"0 1 2 3 4 5 6 7 8 9 10\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_15_string_sequence", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate string_sequence))\n    (check-within (candidate 0) \"0\" 0.001)\n    (check-within (candidate 3) \"0 1 2 3\" 0.001)\n    (check-within (candidate 10) \"0 1 2 3 4 5 6 7 8 9 10\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_154_cycpattern_check", "language": "rkt", "prompt": "#lang racket\n\n;; You are given 2 words. You need to return #t if the second word or any of its rotations is a substring in the first word\n;; >>> (cycpattern_check \"abcd\" \"abd\")\n;; #f\n;; >>> (cycpattern_check \"hello\" \"ell\")\n;; #t\n;; >>> (cycpattern_check \"whassup\" \"psus\")\n;; #f\n;; >>> (cycpattern_check \"abab\" \"baa\")\n;; #t\n;; >>> (cycpattern_check \"efef\" \"eeff\")\n;; #f\n;; >>> (cycpattern_check \"himenss\" \"simen\")\n;; #t\n(define (cycpattern_check a b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate cycpattern_check))\n    (check-within (candidate \"xyzw\" \"xyw\") #f 0.001)\n    (check-within (candidate \"yello\" \"ell\") #t 0.001)\n    (check-within (candidate \"whattup\" \"ptut\") #f 0.001)\n    (check-within (candidate \"efef\" \"fee\") #t 0.001)\n    (check-within (candidate \"abab\" \"aabb\") #f 0.001)\n    (check-within (candidate \"winemtt\" \"tinem\") #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_154_cycpattern_check", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate cycpattern_check))\n    (check-within (candidate \"xyzw\" \"xyw\") #f 0.001)\n    (check-within (candidate \"yello\" \"ell\") #t 0.001)\n    (check-within (candidate \"whattup\" \"ptut\") #f 0.001)\n    (check-within (candidate \"efef\" \"fee\") #t 0.001)\n    (check-within (candidate \"abab\" \"aabb\") #f 0.001)\n    (check-within (candidate \"winemtt\" \"tinem\") #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_57_monotonic", "language": "rkt", "prompt": "#lang racket\n\n;; Return #t is list elements are monotonically increasing or decreasing.\n;; >>> (monotonic (list 1 2 4 20))\n;; #t\n;; >>> (monotonic (list 1 20 4 10))\n;; #f\n;; >>> (monotonic (list 4 1 0 -10))\n;; #t\n(define (monotonic l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate monotonic))\n    (check-within (candidate (list 1 2 4 10)) #t 0.001)\n    (check-within (candidate (list 1 2 4 20)) #t 0.001)\n    (check-within (candidate (list 1 20 4 10)) #f 0.001)\n    (check-within (candidate (list 4 1 0 -10)) #t 0.001)\n    (check-within (candidate (list 4 1 1 0)) #t 0.001)\n    (check-within (candidate (list 1 2 3 2 5 60)) #f 0.001)\n    (check-within (candidate (list 1 2 3 4 5 60)) #t 0.001)\n    (check-within (candidate (list 9 9 9 9)) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_57_monotonic", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate monotonic))\n    (check-within (candidate (list 1 2 4 10)) #t 0.001)\n    (check-within (candidate (list 1 2 4 20)) #t 0.001)\n    (check-within (candidate (list 1 20 4 10)) #f 0.001)\n    (check-within (candidate (list 4 1 0 -10)) #t 0.001)\n    (check-within (candidate (list 4 1 1 0)) #t 0.001)\n    (check-within (candidate (list 1 2 3 2 5 60)) #f 0.001)\n    (check-within (candidate (list 1 2 3 4 5 60)) #t 0.001)\n    (check-within (candidate (list 9 9 9 9)) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_12_longest", "language": "rkt", "prompt": "#lang racket\n\n;; Out of list of strings, return the longest one. Return the first one in case of multiple\n;; strings of the same length. Return #f in case the input list is empty.\n;; >>> (longest (list ))\n;; #f\n;; >>> (longest (list \"a\" \"b\" \"c\"))\n;; \"a\"\n;; >>> (longest (list \"a\" \"bb\" \"ccc\"))\n;; \"ccc\"\n(define (longest strings)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate longest))\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list \"x\" \"y\" \"z\")) \"x\" 0.001)\n    (check-within (candidate (list \"x\" \"yyy\" \"zzzz\" \"www\" \"kkkk\" \"abc\")) \"zzzz\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_12_longest", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate longest))\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list \"x\" \"y\" \"z\")) \"x\" 0.001)\n    (check-within (candidate (list \"x\" \"yyy\" \"zzzz\" \"www\" \"kkkk\" \"abc\")) \"zzzz\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_52_below_threshold", "language": "rkt", "prompt": "#lang racket\n\n;; Return #t if all numbers in the list l are below threshold t.\n;; >>> (below_threshold (list 1 2 4 10) 100)\n;; #t\n;; >>> (below_threshold (list 1 20 4 10) 5)\n;; #f\n(define (below_threshold l t)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate below_threshold))\n    (check-within (candidate (list 1 2 4 10) 100) #t 0.001)\n    (check-within (candidate (list 1 20 4 10) 5) #f 0.001)\n    (check-within (candidate (list 1 20 4 10) 21) #t 0.001)\n    (check-within (candidate (list 1 20 4 10) 22) #t 0.001)\n    (check-within (candidate (list 1 8 4 10) 11) #t 0.001)\n    (check-within (candidate (list 1 8 4 10) 10) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_52_below_threshold", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate below_threshold))\n    (check-within (candidate (list 1 2 4 10) 100) #t 0.001)\n    (check-within (candidate (list 1 20 4 10) 5) #f 0.001)\n    (check-within (candidate (list 1 20 4 10) 21) #t 0.001)\n    (check-within (candidate (list 1 20 4 10) 22) #t 0.001)\n    (check-within (candidate (list 1 8 4 10) 11) #t 0.001)\n    (check-within (candidate (list 1 8 4 10) 10) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_75_is_multiply_prime", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that returns true if the given number is the multiplication of 3 prime numbers\n;; and false otherwise.\n;; Knowing that (a) is less then 100. \n;; Example:\n;; >>> (is_multiply_prime 30)\n;; #t\n;; 30 = 2 * 3 * 5\n(define (is_multiply_prime a)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_multiply_prime))\n    (check-within (candidate 5) #f 0.001)\n    (check-within (candidate 30) #t 0.001)\n    (check-within (candidate 8) #t 0.001)\n    (check-within (candidate 10) #f 0.001)\n    (check-within (candidate 125) #t 0.001)\n    (check-within (candidate 105) #t 0.001)\n    (check-within (candidate 126) #f 0.001)\n    (check-within (candidate 729) #f 0.001)\n    (check-within (candidate 891) #f 0.001)\n    (check-within (candidate 1001) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_75_is_multiply_prime", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate is_multiply_prime))\n    (check-within (candidate 5) #f 0.001)\n    (check-within (candidate 30) #t 0.001)\n    (check-within (candidate 8) #t 0.001)\n    (check-within (candidate 10) #f 0.001)\n    (check-within (candidate 125) #t 0.001)\n    (check-within (candidate 105) #t 0.001)\n    (check-within (candidate 126) #f 0.001)\n    (check-within (candidate 729) #f 0.001)\n    (check-within (candidate 891) #f 0.001)\n    (check-within (candidate 1001) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_30_get_positive", "language": "rkt", "prompt": "#lang racket\n\n;; Return only positive numbers in the list.\n;; >>> (get_positive (list -1 2 -4 5 6))\n;; (list 2 5 6)\n;; >>> (get_positive (list 5 3 -5 2 -3 3 9 0 123 1 -10))\n;; (list 5 3 2 3 9 123 1)\n(define (get_positive l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_positive))\n    (check-within (candidate (list -1 -2 4 5 6)) (list 4 5 6) 0.001)\n    (check-within (candidate (list 5 3 -5 2 3 3 9 0 123 1 -10)) (list 5 3 2 3 3 9 123 1) 0.001)\n    (check-within (candidate (list -1 -2)) (list ) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_30_get_positive", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate get_positive))\n    (check-within (candidate (list -1 -2 4 5 6)) (list 4 5 6) 0.001)\n    (check-within (candidate (list 5 3 -5 2 3 3 9 0 123 1 -10)) (list 5 3 2 3 3 9 123 1) 0.001)\n    (check-within (candidate (list -1 -2)) (list ) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_33_sort_third", "language": "rkt", "prompt": "#lang racket\n\n;; This function takes a list l and returns a list l' such that\n;; l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n;; to the values of the corresponding indicies of l, but sorted.\n;; >>> (sort_third (list 1 2 3))\n;; (list 1 2 3)\n;; >>> (sort_third (list 5 6 3 4 8 9 2))\n;; (list 2 6 3 4 8 9 5)\n(define (sort_third l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_third))\n    (check-within (candidate (list 5 6 3 4 8 9 2)) (list 2 6 3 4 8 9 5) 0.001)\n    (check-within (candidate (list 5 8 3 4 6 9 2)) (list 2 8 3 4 6 9 5) 0.001)\n    (check-within (candidate (list 5 6 9 4 8 3 2)) (list 2 6 9 4 8 3 5) 0.001)\n    (check-within (candidate (list 5 6 3 4 8 9 2 1)) (list 2 6 3 4 8 9 5 1) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_33_sort_third", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_third))\n    (check-within (candidate (list 5 6 3 4 8 9 2)) (list 2 6 3 4 8 9 5) 0.001)\n    (check-within (candidate (list 5 8 3 4 6 9 2)) (list 2 8 3 4 6 9 5) 0.001)\n    (check-within (candidate (list 5 6 9 4 8 3 2)) (list 2 6 9 4 8 3 5) 0.001)\n    (check-within (candidate (list 5 6 3 4 8 9 2 1)) (list 2 6 3 4 8 9 5 1) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_6_parse_nested_parens", "language": "rkt", "prompt": "#lang racket\n\n;; Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n;; For each of the group, output the deepest level of nesting of parentheses.\n;; E.g. (()()) has maximum two levels of nesting while ((())) has three.\n;; >>> (parse_nested_parens \"(()()) ((())) () ((())()())\")\n;; (list 2 3 1 3)\n(define (parse_nested_parens paren_string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate parse_nested_parens))\n    (check-within (candidate \"(()()) ((())) () ((())()())\") (list 2 3 1 3) 0.001)\n    (check-within (candidate \"() (()) ((())) (((())))\") (list 1 2 3 4) 0.001)\n    (check-within (candidate \"(()(())((())))\") (list 4) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_6_parse_nested_parens", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate parse_nested_parens))\n    (check-within (candidate \"(()()) ((())) () ((())()())\") (list 2 3 1 3) 0.001)\n    (check-within (candidate \"() (()) ((())) (((())))\") (list 1 2 3 4) 0.001)\n    (check-within (candidate \"(()(())((())))\") (list 4) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_45_triangle_area", "language": "rkt", "prompt": "#lang racket\n\n;; Given length of a side and high return area for a triangle.\n;; >>> (triangle_area 5 3)\n;; 7.5\n(define (triangle_area a h)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate triangle_area))\n    (check-within (candidate 5 3) 7.5 0.001)\n    (check-within (candidate 2 2) 2.0 0.001)\n    (check-within (candidate 10 8) 40.0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_45_triangle_area", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate triangle_area))\n    (check-within (candidate 5 3) 7.5 0.001)\n    (check-within (candidate 2 2) 2.0 0.001)\n    (check-within (candidate 10 8) 40.0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_97_multiply", "language": "rkt", "prompt": "#lang racket\n\n;; Complete the function that takes two integers and returns \n;; the product of their unit digits.\n;; Assume the input is always valid.\n;; Examples:\n;; >>> (multiply 148 412)\n;; 16\n;; >>> (multiply 19 28)\n;; 72\n;; >>> (multiply 2020 1851)\n;; 0\n;; >>> (multiply 14 -15)\n;; 20\n(define (multiply a b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate multiply))\n    (check-within (candidate 148 412) 16 0.001)\n    (check-within (candidate 19 28) 72 0.001)\n    (check-within (candidate 2020 1851) 0 0.001)\n    (check-within (candidate 14 -15) 20 0.001)\n    (check-within (candidate 76 67) 42 0.001)\n    (check-within (candidate 17 27) 49 0.001)\n    (check-within (candidate 0 1) 0 0.001)\n    (check-within (candidate 0 0) 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_97_multiply", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate multiply))\n    (check-within (candidate 148 412) 16 0.001)\n    (check-within (candidate 19 28) 72 0.001)\n    (check-within (candidate 2020 1851) 0 0.001)\n    (check-within (candidate 14 -15) 20 0.001)\n    (check-within (candidate 76 67) 42 0.001)\n    (check-within (candidate 17 27) 49 0.001)\n    (check-within (candidate 0 1) 0 0.001)\n    (check-within (candidate 0 0) 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "rkt", "prompt": "#lang racket\n\n;; For a given list of input numbers, calculate Mean Absolute Deviation\n;; around the mean of this dataset.\n;; Mean Absolute Deviation is the average absolute difference between each\n;; element and a centerpoint (mean in this case):\n;; MAD = average | x - x_mean |\n;; >>> (mean_absolute_deviation (list 1.0 2.0 3.0 4.0))\n;; 1.0\n(define (mean_absolute_deviation numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate mean_absolute_deviation))\n    (check-within (candidate (list 1.0 2.0)) 0.5 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0)) 1.0 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0)) 1.2 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate mean_absolute_deviation))\n    (check-within (candidate (list 1.0 2.0)) 0.5 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0)) 1.0 0.001)\n    (check-within (candidate (list 1.0 2.0 3.0 4.0 5.0)) 1.2 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_58_common", "language": "rkt", "prompt": "#lang racket\n\n;; Return sorted unique common elements for two lists.\n;; >>> (common (list 1 4 3 34 653 2 5) (list 5 7 1 5 9 653 121))\n;; (list 1 5 653)\n;; >>> (common (list 5 3 2 8) (list 3 2))\n;; (list 2 3)\n(define (common l1 l2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate common))\n    (check-within (candidate (list 1 4 3 34 653 2 5) (list 5 7 1 5 9 653 121)) (list 1 5 653) 0.001)\n    (check-within (candidate (list 5 3 2 8) (list 3 2)) (list 2 3) 0.001)\n    (check-within (candidate (list 4 3 2 8) (list 3 2 4)) (list 2 3 4) 0.001)\n    (check-within (candidate (list 4 3 2 8) (list )) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_58_common", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate common))\n    (check-within (candidate (list 1 4 3 34 653 2 5) (list 5 7 1 5 9 653 121)) (list 1 5 653) 0.001)\n    (check-within (candidate (list 5 3 2 8) (list 3 2)) (list 2 3) 0.001)\n    (check-within (candidate (list 4 3 2 8) (list 3 2 4)) (list 2 3 4) 0.001)\n    (check-within (candidate (list 4 3 2 8) (list )) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "rkt", "prompt": "#lang racket\n\n;; Given a positive integer, obtain its roman numeral equivalent as a string,\n;; and return it in lowercase.\n;; Restrictions: 1 <= num <= 1000\n;; Examples:\n;; >>> (int_to_mini_roman 19)\n;; \"xix\"\n;; >>> (int_to_mini_roman 152)\n;; \"clii\"\n;; >>> (int_to_mini_roman 426)\n;; \"cdxxvi\"\n(define (int_to_mini_roman number)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate int_to_mini_roman))\n    (check-within (candidate 19) \"xix\" 0.001)\n    (check-within (candidate 152) \"clii\" 0.001)\n    (check-within (candidate 251) \"ccli\" 0.001)\n    (check-within (candidate 426) \"cdxxvi\" 0.001)\n    (check-within (candidate 500) \"d\" 0.001)\n    (check-within (candidate 1) \"i\" 0.001)\n    (check-within (candidate 4) \"iv\" 0.001)\n    (check-within (candidate 43) \"xliii\" 0.001)\n    (check-within (candidate 90) \"xc\" 0.001)\n    (check-within (candidate 94) \"xciv\" 0.001)\n    (check-within (candidate 532) \"dxxxii\" 0.001)\n    (check-within (candidate 900) \"cm\" 0.001)\n    (check-within (candidate 994) \"cmxciv\" 0.001)\n    (check-within (candidate 1000) \"m\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_156_int_to_mini_roman", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate int_to_mini_roman))\n    (check-within (candidate 19) \"xix\" 0.001)\n    (check-within (candidate 152) \"clii\" 0.001)\n    (check-within (candidate 251) \"ccli\" 0.001)\n    (check-within (candidate 426) \"cdxxvi\" 0.001)\n    (check-within (candidate 500) \"d\" 0.001)\n    (check-within (candidate 1) \"i\" 0.001)\n    (check-within (candidate 4) \"iv\" 0.001)\n    (check-within (candidate 43) \"xliii\" 0.001)\n    (check-within (candidate 90) \"xc\" 0.001)\n    (check-within (candidate 94) \"xciv\" 0.001)\n    (check-within (candidate 532) \"dxxxii\" 0.001)\n    (check-within (candidate 900) \"cm\" 0.001)\n    (check-within (candidate 994) \"cmxciv\" 0.001)\n    (check-within (candidate 1000) \"m\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_67_fruit_distribution", "language": "rkt", "prompt": "#lang racket\n\n;; In this task, you will be given a string that represents a number of apples and oranges \n;; that are distributed in a basket of fruit this basket contains \n;; apples, oranges, and mango fruits. Given the string that represents the total number of \n;; the oranges and apples and an integer that represent the total number of the fruits \n;; in the basket return the number of the mango fruits in the basket.\n;; for examble:\n;; >>> (fruit_distribution \"5 apples and 6 oranges\" 19)\n;; 8\n;; >>> (fruit_distribution \"0 apples and 1 oranges\" 3)\n;; 2\n;; >>> (fruit_distribution \"2 apples and 3 oranges\" 100)\n;; 95\n;; >>> (fruit_distribution \"100 apples and 1 oranges\" 120)\n;; 19\n(define (fruit_distribution s n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fruit_distribution))\n    (check-within (candidate \"5 apples and 6 oranges\" 19) 8 0.001)\n    (check-within (candidate \"5 apples and 6 oranges\" 21) 10 0.001)\n    (check-within (candidate \"0 apples and 1 oranges\" 3) 2 0.001)\n    (check-within (candidate \"1 apples and 0 oranges\" 3) 2 0.001)\n    (check-within (candidate \"2 apples and 3 oranges\" 100) 95 0.001)\n    (check-within (candidate \"2 apples and 3 oranges\" 5) 0 0.001)\n    (check-within (candidate \"1 apples and 100 oranges\" 120) 19 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_67_fruit_distribution", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate fruit_distribution))\n    (check-within (candidate \"5 apples and 6 oranges\" 19) 8 0.001)\n    (check-within (candidate \"5 apples and 6 oranges\" 21) 10 0.001)\n    (check-within (candidate \"0 apples and 1 oranges\" 3) 2 0.001)\n    (check-within (candidate \"1 apples and 0 oranges\" 3) 2 0.001)\n    (check-within (candidate \"2 apples and 3 oranges\" 100) 95 0.001)\n    (check-within (candidate \"2 apples and 3 oranges\" 5) 0 0.001)\n    (check-within (candidate \"1 apples and 100 oranges\" 120) 19 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_112_reverse_delete", "language": "rkt", "prompt": "#lang racket\n\n;; Task\n;; We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n;; then check if the result string is palindrome.\n;; A string is called palindrome if it reads the same backward as forward.\n;; You should return a list containing the result string and #t/#f for the check.\n;; Example\n;; >>> (reverse_delete \"abcde\" \"ae\")\n;; (list \"bcd\" #f)\n;; >>> (reverse_delete \"abcdef\" \"b\")\n;; (list \"acdef\" #f)\n;; >>> (reverse_delete \"abcdedcba\" \"ab\")\n;; (list \"cdedc\" #t)\n(define (reverse_delete s c)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate reverse_delete))\n    (check-within (candidate \"abcde\" \"ae\") (list \"bcd\" #f) 0.001)\n    (check-within (candidate \"abcdef\" \"b\") (list \"acdef\" #f) 0.001)\n    (check-within (candidate \"abcdedcba\" \"ab\") (list \"cdedc\" #t) 0.001)\n    (check-within (candidate \"dwik\" \"w\") (list \"dik\" #f) 0.001)\n    (check-within (candidate \"a\" \"a\") (list \"\" #t) 0.001)\n    (check-within (candidate \"abcdedcba\" \"\") (list \"abcdedcba\" #t) 0.001)\n    (check-within (candidate \"abcdedcba\" \"v\") (list \"abcdedcba\" #t) 0.001)\n    (check-within (candidate \"vabba\" \"v\") (list \"abba\" #t) 0.001)\n    (check-within (candidate \"mamma\" \"mia\") (list \"\" #t) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_112_reverse_delete", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate reverse_delete))\n    (check-within (candidate \"abcde\" \"ae\") (list \"bcd\" #f) 0.001)\n    (check-within (candidate \"abcdef\" \"b\") (list \"acdef\" #f) 0.001)\n    (check-within (candidate \"abcdedcba\" \"ab\") (list \"cdedc\" #t) 0.001)\n    (check-within (candidate \"dwik\" \"w\") (list \"dik\" #f) 0.001)\n    (check-within (candidate \"a\" \"a\") (list \"\" #t) 0.001)\n    (check-within (candidate \"abcdedcba\" \"\") (list \"abcdedcba\" #t) 0.001)\n    (check-within (candidate \"abcdedcba\" \"v\") (list \"abcdedcba\" #t) 0.001)\n    (check-within (candidate \"vabba\" \"v\") (list \"abba\" #t) 0.001)\n    (check-within (candidate \"mamma\" \"mia\") (list \"\" #t) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "rkt", "prompt": "#lang racket\n\n;; Return a greatest common divisor of two integers a and b\n;; >>> (greatest_common_divisor 3 5)\n;; 1\n;; >>> (greatest_common_divisor 25 15)\n;; 5\n(define (greatest_common_divisor a b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate greatest_common_divisor))\n    (check-within (candidate 3 7) 1 0.001)\n    (check-within (candidate 10 15) 5 0.001)\n    (check-within (candidate 49 14) 7 0.001)\n    (check-within (candidate 144 60) 12 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_13_greatest_common_divisor", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate greatest_common_divisor))\n    (check-within (candidate 3 7) 1 0.001)\n    (check-within (candidate 10 15) 5 0.001)\n    (check-within (candidate 49 14) 7 0.001)\n    (check-within (candidate 144 60) 12 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_125_split_words", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string of words, return a list of words split on whitespace, if no whitespaces exists in the text you\n;; should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n;; alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n;; Examples\n;; >>> (split_words \"Hello world!\")\n;; (list \"Hello\" \"world!\")\n;; >>> (split_words \"Hello,world!\")\n;; (list \"Hello\" \"world!\")\n;; >>> (split_words \"abcdef\")\n;; 3\n(define (split_words txt)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_125_split_words.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate split_words))\n    (check-within (candidate \"Hello world!\") (list \"Hello\" \"world!\") 0.001)\n    (check-within (candidate \"Hello,world!\") (list \"Hello\" \"world!\") 0.001)\n    (check-within (candidate \"Hello world,!\") (list \"Hello\" \"world,!\") 0.001)\n    (check-within (candidate \"Hello,Hello,world !\") (list \"Hello,Hello,world\" \"!\") 0.001)\n    (check-within (candidate \"abcdef\") 3 0.001)\n    (check-within (candidate \"aaabb\") 2 0.001)\n    (check-within (candidate \"aaaBb\") 1 0.001)\n    (check-within (candidate \"\") 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_125_split_words", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate split_words))\n    (check-within (candidate \"Hello world!\") (list \"Hello\" \"world!\") 0.001)\n    (check-within (candidate \"Hello,world!\") (list \"Hello\" \"world!\") 0.001)\n    (check-within (candidate \"Hello world,!\") (list \"Hello\" \"world,!\") 0.001)\n    (check-within (candidate \"Hello,Hello,world !\") (list \"Hello,Hello,world\" \"!\") 0.001)\n    (check-within (candidate \"abcdef\") 3 0.001)\n    (check-within (candidate \"aaabb\") 2 0.001)\n    (check-within (candidate \"aaaBb\") 1 0.001)\n    (check-within (candidate \"\") 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_116_sort_array", "language": "rkt", "prompt": "#lang racket\n\n;; In this Kata, you have to sort a list of non-negative integers according to\n;; number of ones in their binary representation in ascending order.\n;; For similar number of ones, sort based on decimal value.\n;; It must be implemented like this:\n;; >>> (sort_array (list 1 5 2 3 4))\n;; (list 1 2 3 4 5)\n;; >>> (sort_array (list -2 -3 -4 -5 -6))\n;; (list -6 -5 -4 -3 -2)\n;; >>> (sort_array (list 1 0 2 3 4))\n;; (list 0 1 2 3 4)\n(define (sort_array arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_array))\n    (check-within (candidate (list 1 5 2 3 4)) (list 1 2 4 3 5) 0.001)\n    (check-within (candidate (list -2 -3 -4 -5 -6)) (list -4 -2 -6 -5 -3) 0.001)\n    (check-within (candidate (list 1 0 2 3 4)) (list 0 1 2 4 3) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 2 5 77 4 5 3 5 7 2 3 4)) (list 2 2 4 4 3 3 5 5 5 7 77) 0.001)\n    (check-within (candidate (list 3 6 44 12 32 5)) (list 32 3 5 6 12 44) 0.001)\n    (check-within (candidate (list 2 4 8 16 32)) (list 2 4 8 16 32) 0.001)\n    (check-within (candidate (list 2 4 8 16 32)) (list 2 4 8 16 32) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_116_sort_array", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_array))\n    (check-within (candidate (list 1 5 2 3 4)) (list 1 2 4 3 5) 0.001)\n    (check-within (candidate (list -2 -3 -4 -5 -6)) (list -4 -2 -6 -5 -3) 0.001)\n    (check-within (candidate (list 1 0 2 3 4)) (list 0 1 2 4 3) 0.001)\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 2 5 77 4 5 3 5 7 2 3 4)) (list 2 2 4 4 3 3 5 5 5 7 77) 0.001)\n    (check-within (candidate (list 3 6 44 12 32 5)) (list 32 3 5 6 12 44) 0.001)\n    (check-within (candidate (list 2 4 8 16 32)) (list 2 4 8 16 32) 0.001)\n    (check-within (candidate (list 2 4 8 16 32)) (list 2 4 8 16 32) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_28_concatenate", "language": "rkt", "prompt": "#lang racket\n\n;; Concatenate list of strings into a single string\n;; >>> (concatenate (list ))\n;; \"\"\n;; >>> (concatenate (list \"a\" \"b\" \"c\"))\n;; \"abc\"\n(define (concatenate strings)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate concatenate))\n    (check-within (candidate (list )) \"\" 0.001)\n    (check-within (candidate (list \"x\" \"y\" \"z\")) \"xyz\" 0.001)\n    (check-within (candidate (list \"x\" \"y\" \"z\" \"w\" \"k\")) \"xyzwk\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_28_concatenate", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate concatenate))\n    (check-within (candidate (list )) \"\" 0.001)\n    (check-within (candidate (list \"x\" \"y\" \"z\")) \"xyz\" 0.001)\n    (check-within (candidate (list \"x\" \"y\" \"z\" \"w\" \"k\")) \"xyzwk\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_149_sorted_list_sum", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that accepts a list of strings as a parameter,\n;; deletes the strings that have odd lengths from it,\n;; and returns the resulted list with a sorted order,\n;; The list is always a list of strings and never a list of numbers,\n;; and it may contain duplicates.\n;; The order of the list should be ascending by length of each word, and you\n;; should return the list sorted by that rule.\n;; If two words have the same length, sort the list alphabetically.\n;; The function should return a list of strings in sorted order.\n;; You may assume that all words will have the same length.\n;; For example:\n;; >>> (list_sort (list \"aa\" \"a\" \"aaa\"))\n;; (list \"aa\")\n;; >>> (list_sort (list \"ab\" \"a\" \"aaa\" \"cd\"))\n;; (list \"ab\" \"cd\")\n(define (sorted_list_sum lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sorted_list_sum))\n    (check-within (candidate (list \"aa\" \"a\" \"aaa\")) (list \"aa\") 0.001)\n    (check-within (candidate (list \"school\" \"AI\" \"asdf\" \"b\")) (list \"AI\" \"asdf\" \"school\") 0.001)\n    (check-within (candidate (list \"d\" \"b\" \"c\" \"a\")) (list ) 0.001)\n    (check-within (candidate (list \"d\" \"dcba\" \"abcd\" \"a\")) (list \"abcd\" \"dcba\") 0.001)\n    (check-within (candidate (list \"AI\" \"ai\" \"au\")) (list \"AI\" \"ai\" \"au\") 0.001)\n    (check-within (candidate (list \"a\" \"b\" \"b\" \"c\" \"c\" \"a\")) (list ) 0.001)\n    (check-within (candidate (list \"aaaa\" \"bbbb\" \"dd\" \"cc\")) (list \"cc\" \"dd\" \"aaaa\" \"bbbb\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_149_sorted_list_sum", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sorted_list_sum))\n    (check-within (candidate (list \"aa\" \"a\" \"aaa\")) (list \"aa\") 0.001)\n    (check-within (candidate (list \"school\" \"AI\" \"asdf\" \"b\")) (list \"AI\" \"asdf\" \"school\") 0.001)\n    (check-within (candidate (list \"d\" \"b\" \"c\" \"a\")) (list ) 0.001)\n    (check-within (candidate (list \"d\" \"dcba\" \"abcd\" \"a\")) (list \"abcd\" \"dcba\") 0.001)\n    (check-within (candidate (list \"AI\" \"ai\" \"au\")) (list \"AI\" \"ai\" \"au\") 0.001)\n    (check-within (candidate (list \"a\" \"b\" \"b\" \"c\" \"c\" \"a\")) (list ) 0.001)\n    (check-within (candidate (list \"aaaa\" \"bbbb\" \"dd\" \"cc\")) (list \"cc\" \"dd\" \"aaaa\" \"bbbb\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_7_filter_by_substring", "language": "rkt", "prompt": "#lang racket\n\n;; Filter an input list of strings only for ones that contain given substring\n;; >>> (filter_by_substring (list ) \"a\")\n;; (list )\n;; >>> (filter_by_substring (list \"abc\" \"bacd\" \"cde\" \"array\") \"a\")\n;; (list \"abc\" \"bacd\" \"array\")\n(define (filter_by_substring strings substring)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate filter_by_substring))\n    (check-within (candidate (list ) \"john\") (list ) 0.001)\n    (check-within (candidate (list \"xxx\" \"asd\" \"xxy\" \"john doe\" \"xxxAAA\" \"xxx\") \"xxx\") (list \"xxx\" \"xxxAAA\" \"xxx\") 0.001)\n    (check-within (candidate (list \"xxx\" \"asd\" \"aaaxxy\" \"john doe\" \"xxxAAA\" \"xxx\") \"xx\") (list \"xxx\" \"aaaxxy\" \"xxxAAA\" \"xxx\") 0.001)\n    (check-within (candidate (list \"grunt\" \"trumpet\" \"prune\" \"gruesome\") \"run\") (list \"grunt\" \"prune\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_7_filter_by_substring", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate filter_by_substring))\n    (check-within (candidate (list ) \"john\") (list ) 0.001)\n    (check-within (candidate (list \"xxx\" \"asd\" \"xxy\" \"john doe\" \"xxxAAA\" \"xxx\") \"xxx\") (list \"xxx\" \"xxxAAA\" \"xxx\") 0.001)\n    (check-within (candidate (list \"xxx\" \"asd\" \"aaaxxy\" \"john doe\" \"xxxAAA\" \"xxx\") \"xx\") (list \"xxx\" \"aaaxxy\" \"xxxAAA\" \"xxx\") 0.001)\n    (check-within (candidate (list \"grunt\" \"trumpet\" \"prune\" \"gruesome\") \"run\") (list \"grunt\" \"prune\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_99_closest_integer", "language": "rkt", "prompt": "#lang racket\n\n;; Create a function that takes a value (string) representing a number\n;; and returns the closest integer to it. If the number is equidistant\n;; from two integers, round it away from zero.\n;; Examples\n;; >>> (closest_integer \"10\")\n;; 10\n;; >>> (closest_integer \"15.3\")\n;; 15\n;; Note:\n;; Rounding away from zero means that if the given number is equidistant\n;; from two integers, the one you should return is the one that is the\n;; farthest from zero. For example closest_integer(\"14.5\") should\n;; return 15 and closest_integer(\"-14.5\") should return -15.\n(define (closest_integer value)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate closest_integer))\n    (check-within (candidate \"10\") 10 0.001)\n    (check-within (candidate \"14.5\") 15 0.001)\n    (check-within (candidate \"-15.5\") -16 0.001)\n    (check-within (candidate \"15.3\") 15 0.001)\n    (check-within (candidate \"0\") 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_99_closest_integer", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate closest_integer))\n    (check-within (candidate \"10\") 10 0.001)\n    (check-within (candidate \"14.5\") 15 0.001)\n    (check-within (candidate \"-15.5\") -16 0.001)\n    (check-within (candidate \"15.3\") 15 0.001)\n    (check-within (candidate \"0\") 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_64_vowels_count", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function vowels_count which takes a string representing\n;; a word as input and returns the number of vowels in the string.\n;; Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n;; vowel, but only when it is at the end of the given word.\n;; Example:\n;; >>> (vowels_count \"abcde\")\n;; 2\n;; >>> (vowels_count \"ACEDY\")\n;; 3\n(define (vowels_count s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate vowels_count))\n    (check-within (candidate \"abcde\") 2 0.001)\n    (check-within (candidate \"Alone\") 3 0.001)\n    (check-within (candidate \"key\") 2 0.001)\n    (check-within (candidate \"bye\") 1 0.001)\n    (check-within (candidate \"keY\") 2 0.001)\n    (check-within (candidate \"bYe\") 1 0.001)\n    (check-within (candidate \"ACEDY\") 3 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_64_vowels_count", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate vowels_count))\n    (check-within (candidate \"abcde\") 2 0.001)\n    (check-within (candidate \"Alone\") 3 0.001)\n    (check-within (candidate \"key\") 2 0.001)\n    (check-within (candidate \"bye\") 1 0.001)\n    (check-within (candidate \"keY\") 2 0.001)\n    (check-within (candidate \"bYe\") 1 0.001)\n    (check-within (candidate \"ACEDY\") 3 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_158_find_max", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that accepts a list of strings.\n;; The list contains different words. Return the word with maximum number\n;; of unique characters. If multiple strings have maximum number of unique\n;; characters, return the one which comes first in lexicographical order.\n;; >>> (find_max (list \"name\" \"of\" \"string\"))\n;; \"string\"\n;; >>> (find_max (list \"name\" \"enam\" \"game\"))\n;; \"enam\"\n;; >>> (find_max (list \"aaaaaaa\" \"bb\" \"cc\"))\n;; \"aaaaaaa\"\n(define (find_max words)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate find_max))\n    (check-within (candidate (list \"name\" \"of\" \"string\")) \"string\" 0.001)\n    (check-within (candidate (list \"name\" \"enam\" \"game\")) \"enam\" 0.001)\n    (check-within (candidate (list \"aaaaaaa\" \"bb\" \"cc\")) \"aaaaaaa\" 0.001)\n    (check-within (candidate (list \"abc\" \"cba\")) \"abc\" 0.001)\n    (check-within (candidate (list \"play\" \"this\" \"game\" \"of\" \"footbott\")) \"footbott\" 0.001)\n    (check-within (candidate (list \"we\" \"are\" \"gonna\" \"rock\")) \"gonna\" 0.001)\n    (check-within (candidate (list \"we\" \"are\" \"a\" \"mad\" \"nation\")) \"nation\" 0.001)\n    (check-within (candidate (list \"this\" \"is\" \"a\" \"prrk\")) \"this\" 0.001)\n    (check-within (candidate (list \"b\")) \"b\" 0.001)\n    (check-within (candidate (list \"play\" \"play\" \"play\")) \"play\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_158_find_max", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate find_max))\n    (check-within (candidate (list \"name\" \"of\" \"string\")) \"string\" 0.001)\n    (check-within (candidate (list \"name\" \"enam\" \"game\")) \"enam\" 0.001)\n    (check-within (candidate (list \"aaaaaaa\" \"bb\" \"cc\")) \"aaaaaaa\" 0.001)\n    (check-within (candidate (list \"abc\" \"cba\")) \"abc\" 0.001)\n    (check-within (candidate (list \"play\" \"this\" \"game\" \"of\" \"footbott\")) \"footbott\" 0.001)\n    (check-within (candidate (list \"we\" \"are\" \"gonna\" \"rock\")) \"gonna\" 0.001)\n    (check-within (candidate (list \"we\" \"are\" \"a\" \"mad\" \"nation\")) \"nation\" 0.001)\n    (check-within (candidate (list \"this\" \"is\" \"a\" \"prrk\")) \"this\" 0.001)\n    (check-within (candidate (list \"b\")) \"b\" 0.001)\n    (check-within (candidate (list \"play\" \"play\" \"play\")) \"play\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_162_string_to_md5", "language": "rkt", "prompt": "#lang racket\n\n;; Given a string 'text', return its md5 hash equivalent string.\n;; If 'text' is an empty string, return #f.\n;; >>> (string_to_md5 \"Hello world\")\n;; \"3e25960a79dbc69b674cd4ec67a72c62\"\n(define (string_to_md5 text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate string_to_md5))\n    (check-within (candidate \"Hello world\") \"3e25960a79dbc69b674cd4ec67a72c62\" 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"A B C\") \"0ef78513b0cb8cef12743f5aeb35f888\" 0.001)\n    (check-within (candidate \"password\") \"5f4dcc3b5aa765d61d8327deb882cf99\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_162_string_to_md5", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate string_to_md5))\n    (check-within (candidate \"Hello world\") \"3e25960a79dbc69b674cd4ec67a72c62\" 0.001)\n    (check-within (candidate \"\") #f 0.001)\n    (check-within (candidate \"A B C\") \"0ef78513b0cb8cef12743f5aeb35f888\" 0.001)\n    (check-within (candidate \"password\") \"5f4dcc3b5aa765d61d8327deb882cf99\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_44_change_base", "language": "rkt", "prompt": "#lang racket\n\n;; Change numerical base of input number x to base.\n;; return string representation after the conversion.\n;; base numbers are less than 10.\n;; >>> (change_base 8 3)\n;; \"22\"\n;; >>> (change_base 8 2)\n;; \"1000\"\n;; >>> (change_base 7 2)\n;; \"111\"\n(define (change_base x base)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate change_base))\n    (check-within (candidate 8 3) \"22\" 0.001)\n    (check-within (candidate 9 3) \"100\" 0.001)\n    (check-within (candidate 234 2) \"11101010\" 0.001)\n    (check-within (candidate 16 2) \"10000\" 0.001)\n    (check-within (candidate 8 2) \"1000\" 0.001)\n    (check-within (candidate 7 2) \"111\" 0.001)\n    (check-within (candidate 2 3) \"2\" 0.001)\n    (check-within (candidate 3 4) \"3\" 0.001)\n    (check-within (candidate 4 5) \"4\" 0.001)\n    (check-within (candidate 5 6) \"5\" 0.001)\n    (check-within (candidate 6 7) \"6\" 0.001)\n    (check-within (candidate 7 8) \"7\" 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_44_change_base", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate change_base))\n    (check-within (candidate 8 3) \"22\" 0.001)\n    (check-within (candidate 9 3) \"100\" 0.001)\n    (check-within (candidate 234 2) \"11101010\" 0.001)\n    (check-within (candidate 16 2) \"10000\" 0.001)\n    (check-within (candidate 8 2) \"1000\" 0.001)\n    (check-within (candidate 7 2) \"111\" 0.001)\n    (check-within (candidate 2 3) \"2\" 0.001)\n    (check-within (candidate 3 4) \"3\" 0.001)\n    (check-within (candidate 4 5) \"4\" 0.001)\n    (check-within (candidate 5 6) \"5\" 0.001)\n    (check-within (candidate 6 7) \"6\" 0.001)\n    (check-within (candidate 7 8) \"7\" 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_157_right_angle_triangle", "language": "rkt", "prompt": "#lang racket\n\n;; Given the lengths of the three sides of a triangle. Return #t if the three\n;; sides form a right-angled triangle, #f otherwise.\n;; A right-angled triangle is a triangle in which one angle is right angle or \n;; 90 degree.\n;; Example:\n;; >>> (right_angle_triangle 3 4 5)\n;; #t\n;; >>> (right_angle_triangle 1 2 3)\n;; #f\n(define (right_angle_triangle a b c)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate right_angle_triangle))\n    (check-within (candidate 3 4 5) #t 0.001)\n    (check-within (candidate 1 2 3) #f 0.001)\n    (check-within (candidate 10 6 8) #t 0.001)\n    (check-within (candidate 2 2 2) #f 0.001)\n    (check-within (candidate 7 24 25) #t 0.001)\n    (check-within (candidate 10 5 7) #f 0.001)\n    (check-within (candidate 5 12 13) #t 0.001)\n    (check-within (candidate 15 8 17) #t 0.001)\n    (check-within (candidate 48 55 73) #t 0.001)\n    (check-within (candidate 1 1 1) #f 0.001)\n    (check-within (candidate 2 2 10) #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_157_right_angle_triangle", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate right_angle_triangle))\n    (check-within (candidate 3 4 5) #t 0.001)\n    (check-within (candidate 1 2 3) #f 0.001)\n    (check-within (candidate 10 6 8) #t 0.001)\n    (check-within (candidate 2 2 2) #f 0.001)\n    (check-within (candidate 7 24 25) #t 0.001)\n    (check-within (candidate 10 5 7) #f 0.001)\n    (check-within (candidate 5 12 13) #t 0.001)\n    (check-within (candidate 15 8 17) #t 0.001)\n    (check-within (candidate 48 55 73) #t 0.001)\n    (check-within (candidate 1 1 1) #f 0.001)\n    (check-within (candidate 2 2 10) #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "rkt", "prompt": "#lang racket\n\n;; It is the last week of the semester and the teacher has to give the grades\n;; to students. The teacher has been making her own algorithm for grading.\n;; The only problem is, she has lost the code she used for grading.\n;; She has given you a list of GPAs for some students and you have to write \n;; a function that can output a list of letter grades using the following table:\n;; GPA       |    Letter grade\n;; 4.0                A+\n;; > 3.7                A \n;; > 3.3                A- \n;; > 3.0                B+\n;; > 2.7                B \n;; > 2.3                B-\n;; > 2.0                C+\n;; > 1.7                C\n;; > 1.3                C-\n;; > 1.0                D+ \n;; > 0.7                D \n;; > 0.0                D-\n;; 0.0                E\n;; Example:\n;; >>> (grade_equation (list 4.0 3 1.7 2 3.5))\n;; (list \"A+\" \"B\" \"C-\" \"C\" \"A-\")\n(define (numerical_letter_grade grades)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate numerical_letter_grade))\n    (check-within (candidate (list 4.0 3 1.7 2 3.5)) (list \"A+\" \"B\" \"C-\" \"C\" \"A-\") 0.001)\n    (check-within (candidate (list 1.2)) (list \"D+\") 0.001)\n    (check-within (candidate (list 0.5)) (list \"D-\") 0.001)\n    (check-within (candidate (list 0.0)) (list \"E\") 0.001)\n    (check-within (candidate (list 1.0 0.3 1.5 2.8 3.3)) (list \"D\" \"D-\" \"C-\" \"B\" \"B+\") 0.001)\n    (check-within (candidate (list 0.0 0.7)) (list \"E\" \"D-\") 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_81_numerical_letter_grade", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate numerical_letter_grade))\n    (check-within (candidate (list 4.0 3 1.7 2 3.5)) (list \"A+\" \"B\" \"C-\" \"C\" \"A-\") 0.001)\n    (check-within (candidate (list 1.2)) (list \"D+\") 0.001)\n    (check-within (candidate (list 0.5)) (list \"D-\") 0.001)\n    (check-within (candidate (list 0.0)) (list \"E\") 0.001)\n    (check-within (candidate (list 1.0 0.3 1.5 2.8 3.3)) (list \"D\" \"D-\" \"C-\" \"B\" \"B+\") 0.001)\n    (check-within (candidate (list 0.0 0.7)) (list \"E\" \"D-\") 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_5_intersperse", "language": "rkt", "prompt": "#lang racket\n\n;; Insert a number 'delimeter' between every two consecutive elements of input list `numbers'\n;; >>> (intersperse (list ) 4)\n;; (list )\n;; >>> (intersperse (list 1 2 3) 4)\n;; (list 1 4 2 4 3)\n(define (intersperse numbers delimeter)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate intersperse))\n    (check-within (candidate (list ) 7) (list ) 0.001)\n    (check-within (candidate (list 5 6 3 2) 8) (list 5 8 6 8 3 8 2) 0.001)\n    (check-within (candidate (list 2 2 2) 2) (list 2 2 2 2 2) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_5_intersperse", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate intersperse))\n    (check-within (candidate (list ) 7) (list ) 0.001)\n    (check-within (candidate (list 5 6 3 2) 8) (list 5 8 6 8 3 8 2) 0.001)\n    (check-within (candidate (list 2 2 2) 2) (list 2 2 2 2 2) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_146_specialFilter", "language": "rkt", "prompt": "#lang racket\n\n;; Write a function that takes a list of numbers as input and returns \n;; the number of elements in the list that are greater than 10 and both \n;; first and last digits of a number are odd (1, 3, 5, 7, 9).\n;; For example:\n;; >>> (specialFilter (list 15 -73 14 -15))\n;; 1\n;; >>> (specialFilter (list 33 -2 -3 45 21 109))\n;; 2\n(define (specialFilter nums)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate specialFilter))\n    (check-within (candidate (list 5 -2 1 -5)) 0 0.001)\n    (check-within (candidate (list 15 -73 14 -15)) 1 0.001)\n    (check-within (candidate (list 33 -2 -3 45 21 109)) 2 0.001)\n    (check-within (candidate (list 43 -12 93 125 121 109)) 4 0.001)\n    (check-within (candidate (list 71 -2 -33 75 21 19)) 3 0.001)\n    (check-within (candidate (list 1)) 0 0.001)\n    (check-within (candidate (list )) 0 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_146_specialFilter", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate specialFilter))\n    (check-within (candidate (list 5 -2 1 -5)) 0 0.001)\n    (check-within (candidate (list 15 -73 14 -15)) 1 0.001)\n    (check-within (candidate (list 33 -2 -3 45 21 109)) 2 0.001)\n    (check-within (candidate (list 43 -12 93 125 121 109)) 4 0.001)\n    (check-within (candidate (list 71 -2 -33 75 21 19)) 3 0.001)\n    (check-within (candidate (list 1)) 0 0.001)\n    (check-within (candidate (list )) 0 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_60_sum_to_n", "language": "rkt", "prompt": "#lang racket\n\n;; sum_to_n is a function that sums numbers from 1 to n.\n;; >>> (sum_to_n 30)\n;; 465\n;; >>> (sum_to_n 100)\n;; 5050\n;; >>> (sum_to_n 5)\n;; 15\n;; >>> (sum_to_n 10)\n;; 55\n;; >>> (sum_to_n 1)\n;; 1\n(define (sum_to_n n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_to_n))\n    (check-within (candidate 1) 1 0.001)\n    (check-within (candidate 6) 21 0.001)\n    (check-within (candidate 11) 66 0.001)\n    (check-within (candidate 30) 465 0.001)\n    (check-within (candidate 100) 5050 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_60_sum_to_n", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sum_to_n))\n    (check-within (candidate 1) 1 0.001)\n    (check-within (candidate 6) 21 0.001)\n    (check-within (candidate 11) 66 0.001)\n    (check-within (candidate 30) 465 0.001)\n    (check-within (candidate 100) 5050 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_26_remove_duplicates", "language": "rkt", "prompt": "#lang racket\n\n;; From a list of integers, remove all elements that occur more than once.\n;; Keep order of elements left the same as in the input.\n;; >>> (remove_duplicates (list 1 2 3 2 4))\n;; (list 1 3 4)\n(define (remove_duplicates numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate remove_duplicates))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 2 3 4)) (list 1 2 3 4) 0.001)\n    (check-within (candidate (list 1 2 3 2 4 3 5)) (list 1 4 5) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_26_remove_duplicates", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate remove_duplicates))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 2 3 4)) (list 1 2 3 4) 0.001)\n    (check-within (candidate (list 1 2 3 2 4 3 5)) (list 1 4 5) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_163_generate_integers", "language": "rkt", "prompt": "#lang racket\n\n;; Given two positive integers a and b, return the even digits between a\n;; and b, in ascending order.\n;; For example:\n;; >>> (generate_integers 2 8)\n;; (list 2 4 6 8)\n;; >>> (generate_integers 8 2)\n;; (list 2 4 6 8)\n;; >>> (generate_integers 10 14)\n;; (list )\n(define (generate_integers a b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate generate_integers))\n    (check-within (candidate 2 10) (list 2 4 6 8) 0.001)\n    (check-within (candidate 10 2) (list 2 4 6 8) 0.001)\n    (check-within (candidate 132 2) (list 2 4 6 8) 0.001)\n    (check-within (candidate 17 89) (list ) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_163_generate_integers", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate generate_integers))\n    (check-within (candidate 2 10) (list 2 4 6 8) 0.001)\n    (check-within (candidate 10 2) (list 2 4 6 8) 0.001)\n    (check-within (candidate 132 2) (list 2 4 6 8) 0.001)\n    (check-within (candidate 17 89) (list ) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_9_rolling_max", "language": "rkt", "prompt": "#lang racket\n\n;; From a given list of integers, generate a list of rolling maximum element found until given moment\n;; in the sequence.\n;; >>> (rolling_max (list 1 2 3 2 3 4 2))\n;; (list 1 2 3 3 3 4 4)\n(define (rolling_max numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate rolling_max))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 2 3 4)) (list 1 2 3 4) 0.001)\n    (check-within (candidate (list 4 3 2 1)) (list 4 4 4 4) 0.001)\n    (check-within (candidate (list 3 2 3 100 3)) (list 3 3 3 100 100) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_9_rolling_max", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate rolling_max))\n    (check-within (candidate (list )) (list ) 0.001)\n    (check-within (candidate (list 1 2 3 4)) (list 1 2 3 4) 0.001)\n    (check-within (candidate (list 4 3 2 1)) (list 4 4 4 4) 0.001)\n    (check-within (candidate (list 3 2 3 100 3)) (list 3 3 3 100 100) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_3_below_zero", "language": "rkt", "prompt": "#lang racket\n\n;; You're given a list of deposit and withdrawal operations on a bank account that starts with\n;; zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n;; at that point function should return #t. Otherwise it should return #f.\n;; >>> (below_zero (list 1 2 3))\n;; #f\n;; >>> (below_zero (list 1 2 -4 5))\n;; #t\n(define (below_zero operations)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate below_zero))\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list 1 2 -3 1 2 -3)) #f 0.001)\n    (check-within (candidate (list 1 2 -4 5 6)) #t 0.001)\n    (check-within (candidate (list 1 -1 2 -2 5 -5 4 -4)) #f 0.001)\n    (check-within (candidate (list 1 -1 2 -2 5 -5 4 -5)) #t 0.001)\n    (check-within (candidate (list 1 -2 2 -2 5 -5 4 -4)) #t 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_3_below_zero", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate below_zero))\n    (check-within (candidate (list )) #f 0.001)\n    (check-within (candidate (list 1 2 -3 1 2 -3)) #f 0.001)\n    (check-within (candidate (list 1 2 -4 5 6)) #t 0.001)\n    (check-within (candidate (list 1 -1 2 -2 5 -5 4 -4)) #f 0.001)\n    (check-within (candidate (list 1 -1 2 -2 5 -5 4 -5)) #t 0.001)\n    (check-within (candidate (list 1 -2 2 -2 5 -5 4 -4)) #t 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_69_search", "language": "rkt", "prompt": "#lang racket\n\n;; You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n;; zero, and has a frequency greater than or equal to the value of the integer itself. \n;; The frequency of an integer is the number of times it appears in the list.\n;; If no such a value exist, return -1.\n;; Examples:\n;; >>> (search (list 4 1 2 2 3 1))\n;; 2\n;; >>> (search (list 1 2 2 3 3 3 4 4 4))\n;; 3\n;; >>> (search (list 5 5 4 4 4))\n;; -1\n(define (search lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate search))\n    (check-within (candidate (list 5 5 5 5 1)) 1 0.001)\n    (check-within (candidate (list 4 1 4 1 4 4)) 4 0.001)\n    (check-within (candidate (list 3 3)) -1 0.001)\n    (check-within (candidate (list 8 8 8 8 8 8 8 8)) 8 0.001)\n    (check-within (candidate (list 2 3 3 2 2)) 2 0.001)\n    (check-within (candidate (list 2 7 8 8 4 8 7 3 9 6 5 10 4 3 6 7 1 7 4 10 8 1)) 1 0.001)\n    (check-within (candidate (list 3 2 8 2)) 2 0.001)\n    (check-within (candidate (list 6 7 1 8 8 10 5 8 5 3 10)) 1 0.001)\n    (check-within (candidate (list 8 8 3 6 5 6 4)) -1 0.001)\n    (check-within (candidate (list 6 9 6 7 1 4 7 1 8 8 9 8 10 10 8 4 10 4 10 1 2 9 5 7 9)) 1 0.001)\n    (check-within (candidate (list 1 9 10 1 3)) 1 0.001)\n    (check-within (candidate (list 6 9 7 5 8 7 5 3 7 5 10 10 3 6 10 2 8 6 5 4 9 5 3 10)) 5 0.001)\n    (check-within (candidate (list 1)) 1 0.001)\n    (check-within (candidate (list 8 8 10 6 4 3 5 8 2 4 2 8 4 6 10 4 2 1 10 2 1 1 5)) 4 0.001)\n    (check-within (candidate (list 2 10 4 8 2 10 5 1 2 9 5 5 6 3 8 6 4 10)) 2 0.001)\n    (check-within (candidate (list 1 6 10 1 6 9 10 8 6 8 7 3)) 1 0.001)\n    (check-within (candidate (list 9 2 4 1 5 1 5 2 5 7 7 7 3 10 1 5 4 2 8 4 1 9 10 7 10 2 8 10 9 4)) 4 0.001)\n    (check-within (candidate (list 2 6 4 2 8 7 5 6 4 10 4 6 3 7 8 8 3 1 4 2 2 10 7)) 4 0.001)\n    (check-within (candidate (list 9 8 6 10 2 6 10 2 7 8 10 3 8 2 6 2 3 1)) 2 0.001)\n    (check-within (candidate (list 5 5 3 9 5 6 3 2 8 5 6 10 10 6 8 4 10 7 7 10 8)) -1 0.001)\n    (check-within (candidate (list 10)) -1 0.001)\n    (check-within (candidate (list 9 7 7 2 4 7 2 10 9 7 5 7 2)) 2 0.001)\n    (check-within (candidate (list 5 4 10 2 1 1 10 3 6 1 8)) 1 0.001)\n    (check-within (candidate (list 7 9 9 9 3 4 1 5 9 1 2 1 1 10 7 5 6 7 6 7 7 6)) 1 0.001)\n    (check-within (candidate (list 3 10 10 9 2)) -1 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_69_search", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate search))\n    (check-within (candidate (list 5 5 5 5 1)) 1 0.001)\n    (check-within (candidate (list 4 1 4 1 4 4)) 4 0.001)\n    (check-within (candidate (list 3 3)) -1 0.001)\n    (check-within (candidate (list 8 8 8 8 8 8 8 8)) 8 0.001)\n    (check-within (candidate (list 2 3 3 2 2)) 2 0.001)\n    (check-within (candidate (list 2 7 8 8 4 8 7 3 9 6 5 10 4 3 6 7 1 7 4 10 8 1)) 1 0.001)\n    (check-within (candidate (list 3 2 8 2)) 2 0.001)\n    (check-within (candidate (list 6 7 1 8 8 10 5 8 5 3 10)) 1 0.001)\n    (check-within (candidate (list 8 8 3 6 5 6 4)) -1 0.001)\n    (check-within (candidate (list 6 9 6 7 1 4 7 1 8 8 9 8 10 10 8 4 10 4 10 1 2 9 5 7 9)) 1 0.001)\n    (check-within (candidate (list 1 9 10 1 3)) 1 0.001)\n    (check-within (candidate (list 6 9 7 5 8 7 5 3 7 5 10 10 3 6 10 2 8 6 5 4 9 5 3 10)) 5 0.001)\n    (check-within (candidate (list 1)) 1 0.001)\n    (check-within (candidate (list 8 8 10 6 4 3 5 8 2 4 2 8 4 6 10 4 2 1 10 2 1 1 5)) 4 0.001)\n    (check-within (candidate (list 2 10 4 8 2 10 5 1 2 9 5 5 6 3 8 6 4 10)) 2 0.001)\n    (check-within (candidate (list 1 6 10 1 6 9 10 8 6 8 7 3)) 1 0.001)\n    (check-within (candidate (list 9 2 4 1 5 1 5 2 5 7 7 7 3 10 1 5 4 2 8 4 1 9 10 7 10 2 8 10 9 4)) 4 0.001)\n    (check-within (candidate (list 2 6 4 2 8 7 5 6 4 10 4 6 3 7 8 8 3 1 4 2 2 10 7)) 4 0.001)\n    (check-within (candidate (list 9 8 6 10 2 6 10 2 7 8 10 3 8 2 6 2 3 1)) 2 0.001)\n    (check-within (candidate (list 5 5 3 9 5 6 3 2 8 5 6 10 10 6 8 4 10 7 7 10 8)) -1 0.001)\n    (check-within (candidate (list 10)) -1 0.001)\n    (check-within (candidate (list 9 7 7 2 4 7 2 10 9 7 5 7 2)) 2 0.001)\n    (check-within (candidate (list 5 4 10 2 1 1 10 3 6 1 8)) 1 0.001)\n    (check-within (candidate (list 7 9 9 9 3 4 1 5 9 1 2 1 1 10 7 5 6 7 6 7 7 6)) 1 0.001)\n    (check-within (candidate (list 3 10 10 9 2)) -1 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_61_correct_bracketing", "language": "rkt", "prompt": "#lang racket\n\n;; brackets is a string of \"(\" and \")\".\n;; return #t if every opening bracket has a corresponding closing bracket.\n;; >>> (correct_bracketing \"(\")\n;; #f\n;; >>> (correct_bracketing \"()\")\n;; #t\n;; >>> (correct_bracketing \"(()())\")\n;; #t\n;; >>> (correct_bracketing \")(()\")\n;; #f\n(define (correct_bracketing brackets)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate correct_bracketing))\n    (check-within (candidate \"()\") #t 0.001)\n    (check-within (candidate \"(()())\") #t 0.001)\n    (check-within (candidate \"()()(()())()\") #t 0.001)\n    (check-within (candidate \"()()((()()())())(()()(()))\") #t 0.001)\n    (check-within (candidate \"((()())))\") #f 0.001)\n    (check-within (candidate \")(()\") #f 0.001)\n    (check-within (candidate \"(\") #f 0.001)\n    (check-within (candidate \"((((\") #f 0.001)\n    (check-within (candidate \")\") #f 0.001)\n    (check-within (candidate \"(()\") #f 0.001)\n    (check-within (candidate \"()()(()())())(()\") #f 0.001)\n    (check-within (candidate \"()()(()())()))()\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_61_correct_bracketing", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate correct_bracketing))\n    (check-within (candidate \"()\") #t 0.001)\n    (check-within (candidate \"(()())\") #t 0.001)\n    (check-within (candidate \"()()(()())()\") #t 0.001)\n    (check-within (candidate \"()()((()()())())(()()(()))\") #t 0.001)\n    (check-within (candidate \"((()())))\") #f 0.001)\n    (check-within (candidate \")(()\") #f 0.001)\n    (check-within (candidate \"(\") #f 0.001)\n    (check-within (candidate \"((((\") #f 0.001)\n    (check-within (candidate \")\") #f 0.001)\n    (check-within (candidate \"(()\") #f 0.001)\n    (check-within (candidate \"()()(()())())(()\") #f 0.001)\n    (check-within (candidate \"()()(()())()))()\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_37_sort_even", "language": "rkt", "prompt": "#lang racket\n\n;; This function takes a list l and returns a list l' such that\n;; l' is identical to l in the odd indicies, while its values at the even indicies are equal\n;; to the values of the even indicies of l, but sorted.\n;; >>> (sort_even (list 1 2 3))\n;; (list 1 2 3)\n;; >>> (sort_even (list 5 6 3 4))\n;; (list 3 6 5 4)\n(define (sort_even l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_even))\n    (check-within (candidate (list 1 2 3)) (list 1 2 3) 0.001)\n    (check-within (candidate (list 5 3 -5 2 -3 3 9 0 123 1 -10)) (list -10 3 -5 2 -3 3 5 0 9 1 123) 0.001)\n    (check-within (candidate (list 5 8 -12 4 23 2 3 11 12 -10)) (list -12 8 3 4 5 2 12 11 23 -10) 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_37_sort_even", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate sort_even))\n    (check-within (candidate (list 1 2 3)) (list 1 2 3) 0.001)\n    (check-within (candidate (list 5 3 -5 2 -3 3 9 0 123 1 -10)) (list -10 3 -5 2 -3 3 5 0 9 1 123) 0.001)\n    (check-within (candidate (list 5 8 -12 4 23 2 3 11 12 -10)) (list -12 8 3 4 5 2 12 11 23 -10) 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_54_same_chars", "language": "rkt", "prompt": "#lang racket\n\n;; Check if two words have the same characters.\n;; >>> (same_chars \"eabcdzzzz\" \"dddzzzzzzzddeddabc\")\n;; #t\n;; >>> (same_chars \"abcd\" \"dddddddabc\")\n;; #t\n;; >>> (same_chars \"dddddddabc\" \"abcd\")\n;; #t\n;; >>> (same_chars \"eabcd\" \"dddddddabc\")\n;; #f\n;; >>> (same_chars \"abcd\" \"dddddddabce\")\n;; #f\n;; >>> (same_chars \"eabcdzzzz\" \"dddzzzzzzzddddabc\")\n;; #f\n(define (same_chars s0 s1)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate same_chars))\n    (check-within (candidate \"eabcdzzzz\" \"dddzzzzzzzddeddabc\") #t 0.001)\n    (check-within (candidate \"abcd\" \"dddddddabc\") #t 0.001)\n    (check-within (candidate \"dddddddabc\" \"abcd\") #t 0.001)\n    (check-within (candidate \"eabcd\" \"dddddddabc\") #f 0.001)\n    (check-within (candidate \"abcd\" \"dddddddabcf\") #f 0.001)\n    (check-within (candidate \"eabcdzzzz\" \"dddzzzzzzzddddabc\") #f 0.001)\n    (check-within (candidate \"aabb\" \"aaccc\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_54_same_chars", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate same_chars))\n    (check-within (candidate \"eabcdzzzz\" \"dddzzzzzzzddeddabc\") #t 0.001)\n    (check-within (candidate \"abcd\" \"dddddddabc\") #t 0.001)\n    (check-within (candidate \"dddddddabc\" \"abcd\") #t 0.001)\n    (check-within (candidate \"eabcd\" \"dddddddabc\") #f 0.001)\n    (check-within (candidate \"abcd\" \"dddddddabcf\") #f 0.001)\n    (check-within (candidate \"eabcdzzzz\" \"dddzzzzzzzddddabc\") #f 0.001)\n    (check-within (candidate \"aabb\" \"aaccc\") #f 0.001)\n))\n\n(test-humaneval)"}
{"name": "HumanEval_56_correct_bracketing", "language": "rkt", "prompt": "#lang racket\n\n;; brackets is a string of \"<\" and \">\".\n;; return #t if every opening bracket has a corresponding closing bracket.\n;; >>> (correct_bracketing \"<\")\n;; #f\n;; >>> (correct_bracketing \"<>\")\n;; #t\n;; >>> (correct_bracketing \"<<><>>\")\n;; #t\n;; >>> (correct_bracketing \"><<>\")\n;; #f\n(define (correct_bracketing brackets)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate correct_bracketing))\n    (check-within (candidate \"<>\") #t 0.001)\n    (check-within (candidate \"<<><>>\") #t 0.001)\n    (check-within (candidate \"<><><<><>><>\") #t 0.001)\n    (check-within (candidate \"<><><<<><><>><>><<><><<>>>\") #t 0.001)\n    (check-within (candidate \"<<<><>>>>\") #f 0.001)\n    (check-within (candidate \"><<>\") #f 0.001)\n    (check-within (candidate \"<\") #f 0.001)\n    (check-within (candidate \"<<<<\") #f 0.001)\n    (check-within (candidate \">\") #f 0.001)\n    (check-within (candidate \"<<>\") #f 0.001)\n    (check-within (candidate \"<><><<><>><>><<>\") #f 0.001)\n    (check-within (candidate \"<><><<><>><>>><>\") #f 0.001)\n))\n\n(test-humaneval)", "stop_tokens": ["\n(define ", "\n#|", "\n;", "\n("], "task_id": "HumanEval_56_correct_bracketing", "test": "(require rackunit)\n\n(define (test-humaneval) \n\n  (let (( candidate correct_bracketing))\n    (check-within (candidate \"<>\") #t 0.001)\n    (check-within (candidate \"<<><>>\") #t 0.001)\n    (check-within (candidate \"<><><<><>><>\") #t 0.001)\n    (check-within (candidate \"<><><<<><><>><>><<><><<>>>\") #t 0.001)\n    (check-within (candidate \"<<<><>>>>\") #f 0.001)\n    (check-within (candidate \"><<>\") #f 0.001)\n    (check-within (candidate \"<\") #f 0.001)\n    (check-within (candidate \"<<<<\") #f 0.001)\n    (check-within (candidate \">\") #f 0.001)\n    (check-within (candidate \"<<>\") #f 0.001)\n    (check-within (candidate \"<><><<><>><>><<>\") #f 0.001)\n    (check-within (candidate \"<><><<><>><>>><>\") #f 0.001)\n))\n\n(test-humaneval)"}
