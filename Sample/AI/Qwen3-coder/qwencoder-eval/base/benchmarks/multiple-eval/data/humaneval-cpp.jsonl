{"name": "HumanEval_23_strlen", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return length of given string\n// >>> string_length((\"\"))\n// (0)\n// >>> string_length((\"abc\"))\n// (3)\nlong string_length(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = string_length;\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"x\")) == (1));\n    assert(candidate((\"asdasnakj\")) == (9));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_23_strlen", "test": "}\nint main() {\n    auto candidate = string_length;\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"x\")) == (1));\n    assert(candidate((\"asdasnakj\")) == (9));\n}\n"}
{"name": "HumanEval_89_encrypt", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt((\"hi\"))\n// (\"lm\")\n// >>> encrypt((\"asdfghjkl\"))\n// (\"ewhjklnop\")\n// >>> encrypt((\"gf\"))\n// (\"kj\")\n// >>> encrypt((\"et\"))\n// (\"ix\")\nstd::string encrypt(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = encrypt;\n    assert(candidate((\"hi\")) == (\"lm\"));\n    assert(candidate((\"asdfghjkl\")) == (\"ewhjklnop\"));\n    assert(candidate((\"gf\")) == (\"kj\"));\n    assert(candidate((\"et\")) == (\"ix\"));\n    assert(candidate((\"faewfawefaewg\")) == (\"jeiajeaijeiak\"));\n    assert(candidate((\"hellomyfriend\")) == (\"lippsqcjvmirh\"));\n    assert(candidate((\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")) == (\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\"));\n    assert(candidate((\"a\")) == (\"e\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_89_encrypt", "test": "}\nint main() {\n    auto candidate = encrypt;\n    assert(candidate((\"hi\")) == (\"lm\"));\n    assert(candidate((\"asdfghjkl\")) == (\"ewhjklnop\"));\n    assert(candidate((\"gf\")) == (\"kj\"));\n    assert(candidate((\"et\")) == (\"ix\"));\n    assert(candidate((\"faewfawefaewg\")) == (\"jeiajeaijeiak\"));\n    assert(candidate((\"hellomyfriend\")) == (\"lippsqcjvmirh\"));\n    assert(candidate((\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")) == (\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\"));\n    assert(candidate((\"a\")) == (\"e\"));\n}\n"}
{"name": "HumanEval_95_check_dict_case", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a map, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given map is empty.\n// Examples:\n// >>> check_dict_case((std::map<std::string,std::string>({{\"a\", \"apple\"}, {\"b\", \"banana\"}})))\n// (true)\n// >>> check_dict_case((std::map<std::string,std::string>({{\"a\", \"apple\"}, {\"A\", \"banana\"}, {\"B\", \"banana\"}})))\n// (false)\n// >>> check_dict_case((std::map<std::string,std::string>({{\"a\", \"apple\"}, {8, \"banana\"}, {\"a\", \"apple\"}})))\n// (false)\n// >>> check_dict_case((std::map<std::string,std::string>({{\"Name\", \"John\"}, {\"Age\", \"36\"}, {\"City\", \"Houston\"}})))\n// (false)\n// >>> check_dict_case((std::map<std::string,std::string>({{\"STATE\", \"NC\"}, {\"ZIP\", \"12345\"}})))\n// (true)\nbool check_dict_case(std::map<std::string,std::string> dict) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = check_dict_case;\n    assert(candidate((std::map<std::string,std::string>({{\"p\", \"pineapple\"}, {\"b\", \"banana\"}}))) == (true));\n    assert(candidate((std::map<std::string,std::string>({{\"p\", \"pineapple\"}, {\"A\", \"banana\"}, {\"B\", \"banana\"}}))) == (false));\n    assert(candidate((std::map<std::string,std::string>({{\"p\", \"pineapple\"}, {\"5\", \"banana\"}, {\"a\", \"apple\"}}))) == (false));\n    assert(candidate((std::map<std::string,std::string>({{\"Name\", \"John\"}, {\"Age\", \"36\"}, {\"City\", \"Houston\"}}))) == (false));\n    assert(candidate((std::map<std::string,std::string>({{\"STATE\", \"NC\"}, {\"ZIP\", \"12345\"}}))) == (true));\n    assert(candidate((std::map<std::string,std::string>({{\"fruit\", \"Orange\"}, {\"taste\", \"Sweet\"}}))) == (true));\n    assert(candidate((std::map<std::string,std::string>())) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_95_check_dict_case", "test": "}\nint main() {\n    auto candidate = check_dict_case;\n    assert(candidate((std::map<std::string,std::string>({{\"p\", \"pineapple\"}, {\"b\", \"banana\"}}))) == (true));\n    assert(candidate((std::map<std::string,std::string>({{\"p\", \"pineapple\"}, {\"A\", \"banana\"}, {\"B\", \"banana\"}}))) == (false));\n    assert(candidate((std::map<std::string,std::string>({{\"p\", \"pineapple\"}, {\"5\", \"banana\"}, {\"a\", \"apple\"}}))) == (false));\n    assert(candidate((std::map<std::string,std::string>({{\"Name\", \"John\"}, {\"Age\", \"36\"}, {\"City\", \"Houston\"}}))) == (false));\n    assert(candidate((std::map<std::string,std::string>({{\"STATE\", \"NC\"}, {\"ZIP\", \"12345\"}}))) == (true));\n    assert(candidate((std::map<std::string,std::string>({{\"fruit\", \"Orange\"}, {\"taste\", \"Sweet\"}}))) == (true));\n    assert(candidate((std::map<std::string,std::string>())) == (false));\n}\n"}
{"name": "HumanEval_85_add", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a non-empty vector of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add((std::vector<long>({(long)4, (long)2, (long)6, (long)7})))\n// (2)\nlong add(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = add;\n    assert(candidate((std::vector<long>({(long)4, (long)88}))) == (88));\n    assert(candidate((std::vector<long>({(long)4, (long)5, (long)6, (long)7, (long)2, (long)122}))) == (122));\n    assert(candidate((std::vector<long>({(long)4, (long)0, (long)6, (long)7}))) == (0));\n    assert(candidate((std::vector<long>({(long)4, (long)4, (long)6, (long)8}))) == (12));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_85_add", "test": "}\nint main() {\n    auto candidate = add;\n    assert(candidate((std::vector<long>({(long)4, (long)88}))) == (88));\n    assert(candidate((std::vector<long>({(long)4, (long)5, (long)6, (long)7, (long)2, (long)122}))) == (122));\n    assert(candidate((std::vector<long>({(long)4, (long)0, (long)6, (long)7}))) == (0));\n    assert(candidate((std::vector<long>({(long)4, (long)4, (long)6, (long)8}))) == (12));\n}\n"}
{"name": "HumanEval_140_fix_spaces", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces((\" Example\"))\n// (\"Example\")\n// >>> fix_spaces((\" Example 1\"))\n// (\"Example_1\")\n// >>> fix_spaces((\" Example 2\"))\n// (\"_Example_2\")\n// >>> fix_spaces((\" Example 3\"))\n// (\"_Example-3\")\nstd::string fix_spaces(std::string text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = fix_spaces;\n    assert(candidate((\"Example\")) == (\"Example\"));\n    assert(candidate((\"Mudasir Hanif \")) == (\"Mudasir_Hanif_\"));\n    assert(candidate((\"Yellow Yellow  Dirty  Fellow\")) == (\"Yellow_Yellow__Dirty__Fellow\"));\n    assert(candidate((\"Exa   mple\")) == (\"Exa-mple\"));\n    assert(candidate((\"   Exa 1 2 2 mple\")) == (\"-Exa_1_2_2_mple\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_140_fix_spaces", "test": "}\nint main() {\n    auto candidate = fix_spaces;\n    assert(candidate((\"Example\")) == (\"Example\"));\n    assert(candidate((\"Mudasir Hanif \")) == (\"Mudasir_Hanif_\"));\n    assert(candidate((\"Yellow Yellow  Dirty  Fellow\")) == (\"Yellow_Yellow__Dirty__Fellow\"));\n    assert(candidate((\"Exa   mple\")) == (\"Exa-mple\"));\n    assert(candidate((\"   Exa 1 2 2 mple\")) == (\"-Exa_1_2_2_mple\"));\n}\n"}
{"name": "HumanEval_63_fibfib", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib((1))\n// (0)\n// >>> fibfib((5))\n// (4)\n// >>> fibfib((8))\n// (24)\nlong fibfib(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = fibfib;\n    assert(candidate((2)) == (1));\n    assert(candidate((1)) == (0));\n    assert(candidate((5)) == (4));\n    assert(candidate((8)) == (24));\n    assert(candidate((10)) == (81));\n    assert(candidate((12)) == (274));\n    assert(candidate((14)) == (927));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_63_fibfib", "test": "}\nint main() {\n    auto candidate = fibfib;\n    assert(candidate((2)) == (1));\n    assert(candidate((1)) == (0));\n    assert(candidate((5)) == (4));\n    assert(candidate((8)) == (24));\n    assert(candidate((10)) == (81));\n    assert(candidate((12)) == (274));\n    assert(candidate((14)) == (927));\n}\n"}
{"name": "HumanEval_151_double_the_difference", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of numbers, return the sum of squares of the numbers\n// in the vector that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference((std::vector<float>({(long)1, (long)3, (long)2, (long)0})))\n// (10)\n// >>> double_the_difference((std::vector<float>({(long)-1, (long)-2, (long)0})))\n// (0)\n// >>> double_the_difference((std::vector<float>({(long)9, (long)-2})))\n// (81)\n// >>> double_the_difference((std::vector<float>({(long)0})))\n// (0)\n// If the input vector is empty, return 0.\nlong double_the_difference(std::vector<float> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = double_the_difference;\n    assert(candidate((std::vector<float>())) == (0));\n    assert(candidate((std::vector<float>({(float)5.0f, (float)4.0f}))) == (25));\n    assert(candidate((std::vector<float>({(float)0.1f, (float)0.2f, (float)0.3f}))) == (0));\n    assert(candidate((std::vector<float>({(float)-10.0f, (float)-20.0f, (float)-30.0f}))) == (0));\n    assert(candidate((std::vector<float>({(float)-1.0f, (float)-2.0f, (float)8.0f}))) == (0));\n    assert(candidate((std::vector<float>({(float)0.2f, (float)3.0f, (float)5.0f}))) == (34));\n    assert(candidate((std::vector<float>({(float)-9.0f, (float)-7.0f, (float)-5.0f, (float)-3.0f, (float)-1.0f, (float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f, (float)9.0f}))) == (165));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_151_double_the_difference", "test": "}\nint main() {\n    auto candidate = double_the_difference;\n    assert(candidate((std::vector<float>())) == (0));\n    assert(candidate((std::vector<float>({(float)5.0f, (float)4.0f}))) == (25));\n    assert(candidate((std::vector<float>({(float)0.1f, (float)0.2f, (float)0.3f}))) == (0));\n    assert(candidate((std::vector<float>({(float)-10.0f, (float)-20.0f, (float)-30.0f}))) == (0));\n    assert(candidate((std::vector<float>({(float)-1.0f, (float)-2.0f, (float)8.0f}))) == (0));\n    assert(candidate((std::vector<float>({(float)0.2f, (float)3.0f, (float)5.0f}))) == (34));\n    assert(candidate((std::vector<float>({(float)-9.0f, (float)-7.0f, (float)-5.0f, (float)-3.0f, (float)-1.0f, (float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f, (float)9.0f}))) == (165));\n}\n"}
{"name": "HumanEval_22_filter_integers", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Filter given vector of any cppthon values only for integers\n// >>> filter_integers((std::vector<std::any>({(std::string)\"a\", (std::string)3.14f, (std::string)5})))\n// (std::vector<long>({(long)5}))\n// >>> filter_integers((std::vector<std::any>({1, 2, 3, \"abc\", std::map<long,long>(), std::vector<long>()})))\n// (std::vector<long>({(long)1, (long)2, (long)3}))\nstd::vector<long> filter_integers(std::vector<std::any> values) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = filter_integers;\n    assert(candidate((std::vector<std::any>())) == (std::vector<long>()));\n    assert(candidate((std::vector<std::any>({4, std::map<long,long>(), std::vector<long>(), 23.2f, 9, \"adasd\"}))) == (std::vector<long>({(long)4, (long)9})));\n    assert(candidate((std::vector<std::any>({3, \"c\", 3, 3, \"a\", \"b\"}))) == (std::vector<long>({(long)3, (long)3, (long)3})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_22_filter_integers", "test": "}\nint main() {\n    auto candidate = filter_integers;\n    assert(candidate((std::vector<std::any>())) == (std::vector<long>()));\n    assert(candidate((std::vector<std::any>({4, std::map<long,long>(), std::vector<long>(), 23.2f, 9, \"adasd\"}))) == (std::vector<long>({(long)4, (long)9})));\n    assert(candidate((std::vector<std::any>({3, \"c\", 3, 3, \"a\", \"b\"}))) == (std::vector<long>({(long)3, (long)3, (long)3})));\n}\n"}
{"name": "HumanEval_41_car_race_collision", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nlong car_race_collision(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = car_race_collision;\n    assert(candidate((2)) == (4));\n    assert(candidate((3)) == (9));\n    assert(candidate((4)) == (16));\n    assert(candidate((8)) == (64));\n    assert(candidate((10)) == (100));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_41_car_race_collision", "test": "}\nint main() {\n    auto candidate = car_race_collision;\n    assert(candidate((2)) == (4));\n    assert(candidate((3)) == (9));\n    assert(candidate((4)) == (16));\n    assert(candidate((8)) == (64));\n    assert(candidate((10)) == (100));\n}\n"}
{"name": "HumanEval_17_parse_music", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return vector of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music((\"o o| .| o| o| .| .| .| .| o o\"))\n// (std::vector<long>({(long)4, (long)2, (long)1, (long)2, (long)2, (long)1, (long)1, (long)1, (long)1, (long)4, (long)4}))\nstd::vector<long> parse_music(std::string music_string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = parse_music;\n    assert(candidate((\"\")) == (std::vector<long>()));\n    assert(candidate((\"o o o o\")) == (std::vector<long>({(long)4, (long)4, (long)4, (long)4})));\n    assert(candidate((\".| .| .| .|\")) == (std::vector<long>({(long)1, (long)1, (long)1, (long)1})));\n    assert(candidate((\"o| o| .| .| o o o o\")) == (std::vector<long>({(long)2, (long)2, (long)1, (long)1, (long)4, (long)4, (long)4, (long)4})));\n    assert(candidate((\"o| .| o| .| o o| o o|\")) == (std::vector<long>({(long)2, (long)1, (long)2, (long)1, (long)4, (long)2, (long)4, (long)2})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_17_parse_music", "test": "}\nint main() {\n    auto candidate = parse_music;\n    assert(candidate((\"\")) == (std::vector<long>()));\n    assert(candidate((\"o o o o\")) == (std::vector<long>({(long)4, (long)4, (long)4, (long)4})));\n    assert(candidate((\".| .| .| .|\")) == (std::vector<long>({(long)1, (long)1, (long)1, (long)1})));\n    assert(candidate((\"o| o| .| .| o o o o\")) == (std::vector<long>({(long)2, (long)2, (long)1, (long)1, (long)4, (long)4, (long)4, (long)4})));\n    assert(candidate((\"o| .| o| .| o o| o o|\")) == (std::vector<long>({(long)2, (long)1, (long)2, (long)1, (long)4, (long)2, (long)4, (long)2})));\n}\n"}
{"name": "HumanEval_79_decimal_to_binary", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary((15))\n// (\"db1111db\")\n// >>> decimal_to_binary((32))\n// (\"db100000db\")\nstd::string decimal_to_binary(long decimal) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = decimal_to_binary;\n    assert(candidate((0)) == (\"db0db\"));\n    assert(candidate((32)) == (\"db100000db\"));\n    assert(candidate((103)) == (\"db1100111db\"));\n    assert(candidate((15)) == (\"db1111db\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_79_decimal_to_binary", "test": "}\nint main() {\n    auto candidate = decimal_to_binary;\n    assert(candidate((0)) == (\"db0db\"));\n    assert(candidate((32)) == (\"db100000db\"));\n    assert(candidate((103)) == (\"db1100111db\"));\n    assert(candidate((15)) == (\"db1111db\"));\n}\n"}
{"name": "HumanEval_14_all_prefixes", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return vector of all prefixes from shortest to longest of the input string\n// >>> all_prefixes((\"abc\"))\n// (std::vector<std::string>({(std::string)\"a\", (std::string)\"ab\", (std::string)\"abc\"}))\nstd::vector<std::string> all_prefixes(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = all_prefixes;\n    assert(candidate((\"\")) == (std::vector<std::string>()));\n    assert(candidate((\"asdfgh\")) == (std::vector<std::string>({(std::string)\"a\", (std::string)\"as\", (std::string)\"asd\", (std::string)\"asdf\", (std::string)\"asdfg\", (std::string)\"asdfgh\"})));\n    assert(candidate((\"WWW\")) == (std::vector<std::string>({(std::string)\"W\", (std::string)\"WW\", (std::string)\"WWW\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_14_all_prefixes", "test": "}\nint main() {\n    auto candidate = all_prefixes;\n    assert(candidate((\"\")) == (std::vector<std::string>()));\n    assert(candidate((\"asdfgh\")) == (std::vector<std::string>({(std::string)\"a\", (std::string)\"as\", (std::string)\"asd\", (std::string)\"asdf\", (std::string)\"asdfg\", (std::string)\"asdfgh\"})));\n    assert(candidate((\"WWW\")) == (std::vector<std::string>({(std::string)\"W\", (std::string)\"WW\", (std::string)\"WWW\"})));\n}\n"}
{"name": "HumanEval_53_add", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Add two numbers x and y\n// >>> add((2), (3))\n// (5)\n// >>> add((5), (7))\n// (12)\nlong add(long x, long y) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = add;\n    assert(candidate((0), (1)) == (1));\n    assert(candidate((1), (0)) == (1));\n    assert(candidate((2), (3)) == (5));\n    assert(candidate((5), (7)) == (12));\n    assert(candidate((7), (5)) == (12));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_53_add", "test": "}\nint main() {\n    auto candidate = add;\n    assert(candidate((0), (1)) == (1));\n    assert(candidate((1), (0)) == (1));\n    assert(candidate((2), (3)) == (5));\n    assert(candidate((5), (7)) == (12));\n    assert(candidate((7), (5)) == (12));\n}\n"}
{"name": "HumanEval_159_eat", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return a vector of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat((5), (6), (10))\n// (std::vector<long>({(long)11, (long)4}))\n// >>> eat((4), (8), (9))\n// (std::vector<long>({(long)12, (long)1}))\n// >>> eat((1), (10), (10))\n// (std::vector<long>({(long)11, (long)0}))\n// >>> eat((2), (11), (5))\n// (std::vector<long>({(long)7, (long)0}))\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nstd::vector<long> eat(long number, long need, long remaining) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = eat;\n    assert(candidate((5), (6), (10)) == (std::vector<long>({(long)11, (long)4})));\n    assert(candidate((4), (8), (9)) == (std::vector<long>({(long)12, (long)1})));\n    assert(candidate((1), (10), (10)) == (std::vector<long>({(long)11, (long)0})));\n    assert(candidate((2), (11), (5)) == (std::vector<long>({(long)7, (long)0})));\n    assert(candidate((4), (5), (7)) == (std::vector<long>({(long)9, (long)2})));\n    assert(candidate((4), (5), (1)) == (std::vector<long>({(long)5, (long)0})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_159_eat", "test": "}\nint main() {\n    auto candidate = eat;\n    assert(candidate((5), (6), (10)) == (std::vector<long>({(long)11, (long)4})));\n    assert(candidate((4), (8), (9)) == (std::vector<long>({(long)12, (long)1})));\n    assert(candidate((1), (10), (10)) == (std::vector<long>({(long)11, (long)0})));\n    assert(candidate((2), (11), (5)) == (std::vector<long>({(long)7, (long)0})));\n    assert(candidate((4), (5), (7)) == (std::vector<long>({(long)9, (long)2})));\n    assert(candidate((4), (5), (1)) == (std::vector<long>({(long)5, (long)0})));\n}\n"}
{"name": "HumanEval_115_max_fill", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)1, (long)0}), (std::vector<long>)std::vector<long>({(long)0, (long)1, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (1))\n// (6)\n// Example 2:\n// >>> max_fill((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)0, (long)1, (long)1, (long)1})})), (2))\n// (5)\n// Example 3:\n// >>> max_fill((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0})})), (5))\n// (0)\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nlong max_fill(std::vector<std::vector<long>> grid, long capacity) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = max_fill;\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)1, (long)0}), (std::vector<long>)std::vector<long>({(long)0, (long)1, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (1)) == (6));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)0, (long)1, (long)1, (long)1})})), (2)) == (5));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0})})), (5)) == (0));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (2)) == (4));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (9)) == (2));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_115_max_fill", "test": "}\nint main() {\n    auto candidate = max_fill;\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)1, (long)0}), (std::vector<long>)std::vector<long>({(long)0, (long)1, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (1)) == (6));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)0, (long)1, (long)1, (long)1})})), (2)) == (5));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0}), (std::vector<long>)std::vector<long>({(long)0, (long)0, (long)0})})), (5)) == (0));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (2)) == (4));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)1, (long)1})})), (9)) == (2));\n}\n"}
{"name": "HumanEval_160_do_algebra", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given two vectors operator, and operand. The first vector has basic algebra operations, and \n// the second vector is a vector of integers. Use the two given vectors to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// vector = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator vector is equal to the length of operand vector minus one.\n// Operand is a vector of of non-negative integers.\n// Operator vector has at least one operator, and operand vector has at least two operands.\nlong do_algebra(std::vector<std::string> op, std::vector<long> operand) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = do_algebra;\n    assert(candidate((std::vector<std::string>({(std::string)\"**\", (std::string)\"*\", (std::string)\"+\"})), (std::vector<long>({(long)2, (long)3, (long)4, (long)5}))) == (37));\n    assert(candidate((std::vector<std::string>({(std::string)\"+\", (std::string)\"*\", (std::string)\"-\"})), (std::vector<long>({(long)2, (long)3, (long)4, (long)5}))) == (9));\n    assert(candidate((std::vector<std::string>({(std::string)\"//\", (std::string)\"*\"})), (std::vector<long>({(long)7, (long)3, (long)4}))) == (8));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_160_do_algebra", "test": "}\nint main() {\n    auto candidate = do_algebra;\n    assert(candidate((std::vector<std::string>({(std::string)\"**\", (std::string)\"*\", (std::string)\"+\"})), (std::vector<long>({(long)2, (long)3, (long)4, (long)5}))) == (37));\n    assert(candidate((std::vector<std::string>({(std::string)\"+\", (std::string)\"*\", (std::string)\"-\"})), (std::vector<long>({(long)2, (long)3, (long)4, (long)5}))) == (9));\n    assert(candidate((std::vector<std::string>({(std::string)\"//\", (std::string)\"*\"})), (std::vector<long>({(long)7, (long)3, (long)4}))) == (8));\n}\n"}
{"name": "HumanEval_27_flip_case", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case((\"Hello\"))\n// (\"hELLO\")\nstd::string flip_case(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = flip_case;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"Hello!\")) == (\"hELLO!\"));\n    assert(candidate((\"These violent delights have violent ends\")) == (\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_27_flip_case", "test": "}\nint main() {\n    auto candidate = flip_case;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"Hello!\")) == (\"hELLO!\"));\n    assert(candidate((\"These violent delights have violent ends\")) == (\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\"));\n}\n"}
{"name": "HumanEval_105_by_length", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting vector, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length((std::vector<long>({(long)2, (long)1, (long)1, (long)4, (long)5, (long)8, (long)2, (long)3})))\n// (std::vector<std::string>({(std::string)\"Eight\", (std::string)\"Five\", (std::string)\"Four\", (std::string)\"Three\", (std::string)\"Two\", (std::string)\"Two\", (std::string)\"One\", (std::string)\"One\"}))\n// If the vector is empty, return an empty vector:\n// >>> by_length((std::vector<long>()))\n// (std::vector<std::string>())\n// If the vector has any strange number ignore it:\n// >>> by_length((std::vector<long>({(long)1, (long)-1, (long)55})))\n// (std::vector<std::string>({(std::string)\"One\"}))\nstd::vector<std::string> by_length(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = by_length;\n    assert(candidate((std::vector<long>({(long)2, (long)1, (long)1, (long)4, (long)5, (long)8, (long)2, (long)3}))) == (std::vector<std::string>({(std::string)\"Eight\", (std::string)\"Five\", (std::string)\"Four\", (std::string)\"Three\", (std::string)\"Two\", (std::string)\"Two\", (std::string)\"One\", (std::string)\"One\"})));\n    assert(candidate((std::vector<long>())) == (std::vector<std::string>()));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)55}))) == (std::vector<std::string>({(std::string)\"One\"})));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)3, (long)2}))) == (std::vector<std::string>({(std::string)\"Three\", (std::string)\"Two\", (std::string)\"One\"})));\n    assert(candidate((std::vector<long>({(long)9, (long)4, (long)8}))) == (std::vector<std::string>({(std::string)\"Nine\", (std::string)\"Eight\", (std::string)\"Four\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_105_by_length", "test": "}\nint main() {\n    auto candidate = by_length;\n    assert(candidate((std::vector<long>({(long)2, (long)1, (long)1, (long)4, (long)5, (long)8, (long)2, (long)3}))) == (std::vector<std::string>({(std::string)\"Eight\", (std::string)\"Five\", (std::string)\"Four\", (std::string)\"Three\", (std::string)\"Two\", (std::string)\"Two\", (std::string)\"One\", (std::string)\"One\"})));\n    assert(candidate((std::vector<long>())) == (std::vector<std::string>()));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)55}))) == (std::vector<std::string>({(std::string)\"One\"})));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)3, (long)2}))) == (std::vector<std::string>({(std::string)\"Three\", (std::string)\"Two\", (std::string)\"One\"})));\n    assert(candidate((std::vector<long>({(long)9, (long)4, (long)8}))) == (std::vector<std::string>({(std::string)\"Nine\", (std::string)\"Eight\", (std::string)\"Four\"})));\n}\n"}
{"name": "HumanEval_25_factorize", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return vector of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be vectored number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize((8))\n// (std::vector<long>({(long)2, (long)2, (long)2}))\n// >>> factorize((25))\n// (std::vector<long>({(long)5, (long)5}))\n// >>> factorize((70))\n// (std::vector<long>({(long)2, (long)5, (long)7}))\nstd::vector<long> factorize(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = factorize;\n    assert(candidate((2)) == (std::vector<long>({(long)2})));\n    assert(candidate((4)) == (std::vector<long>({(long)2, (long)2})));\n    assert(candidate((8)) == (std::vector<long>({(long)2, (long)2, (long)2})));\n    assert(candidate((57)) == (std::vector<long>({(long)3, (long)19})));\n    assert(candidate((3249)) == (std::vector<long>({(long)3, (long)3, (long)19, (long)19})));\n    assert(candidate((185193)) == (std::vector<long>({(long)3, (long)3, (long)3, (long)19, (long)19, (long)19})));\n    assert(candidate((20577)) == (std::vector<long>({(long)3, (long)19, (long)19, (long)19})));\n    assert(candidate((18)) == (std::vector<long>({(long)2, (long)3, (long)3})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_25_factorize", "test": "}\nint main() {\n    auto candidate = factorize;\n    assert(candidate((2)) == (std::vector<long>({(long)2})));\n    assert(candidate((4)) == (std::vector<long>({(long)2, (long)2})));\n    assert(candidate((8)) == (std::vector<long>({(long)2, (long)2, (long)2})));\n    assert(candidate((57)) == (std::vector<long>({(long)3, (long)19})));\n    assert(candidate((3249)) == (std::vector<long>({(long)3, (long)3, (long)19, (long)19})));\n    assert(candidate((185193)) == (std::vector<long>({(long)3, (long)3, (long)3, (long)19, (long)19, (long)19})));\n    assert(candidate((20577)) == (std::vector<long>({(long)3, (long)19, (long)19, (long)19})));\n    assert(candidate((18)) == (std::vector<long>({(long)2, (long)3, (long)3})));\n}\n"}
{"name": "HumanEval_96_count_up_to", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Implement a function that takes an non-negative integer and returns a vector of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to((5))\n// (std::vector<long>({(long)2, (long)3}))\n// >>> count_up_to((11))\n// (std::vector<long>({(long)2, (long)3, (long)5, (long)7}))\n// >>> count_up_to((0))\n// (std::vector<long>())\n// >>> count_up_to((20))\n// (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19}))\n// >>> count_up_to((1))\n// (std::vector<long>())\n// >>> count_up_to((18))\n// (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17}))\nstd::vector<long> count_up_to(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = count_up_to;\n    assert(candidate((5)) == (std::vector<long>({(long)2, (long)3})));\n    assert(candidate((6)) == (std::vector<long>({(long)2, (long)3, (long)5})));\n    assert(candidate((7)) == (std::vector<long>({(long)2, (long)3, (long)5})));\n    assert(candidate((10)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7})));\n    assert(candidate((0)) == (std::vector<long>()));\n    assert(candidate((22)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19})));\n    assert(candidate((1)) == (std::vector<long>()));\n    assert(candidate((18)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17})));\n    assert(candidate((47)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19, (long)23, (long)29, (long)31, (long)37, (long)41, (long)43})));\n    assert(candidate((101)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19, (long)23, (long)29, (long)31, (long)37, (long)41, (long)43, (long)47, (long)53, (long)59, (long)61, (long)67, (long)71, (long)73, (long)79, (long)83, (long)89, (long)97})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_96_count_up_to", "test": "}\nint main() {\n    auto candidate = count_up_to;\n    assert(candidate((5)) == (std::vector<long>({(long)2, (long)3})));\n    assert(candidate((6)) == (std::vector<long>({(long)2, (long)3, (long)5})));\n    assert(candidate((7)) == (std::vector<long>({(long)2, (long)3, (long)5})));\n    assert(candidate((10)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7})));\n    assert(candidate((0)) == (std::vector<long>()));\n    assert(candidate((22)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19})));\n    assert(candidate((1)) == (std::vector<long>()));\n    assert(candidate((18)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17})));\n    assert(candidate((47)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19, (long)23, (long)29, (long)31, (long)37, (long)41, (long)43})));\n    assert(candidate((101)) == (std::vector<long>({(long)2, (long)3, (long)5, (long)7, (long)11, (long)13, (long)17, (long)19, (long)23, (long)29, (long)31, (long)37, (long)41, (long)43, (long)47, (long)53, (long)59, (long)61, (long)67, (long)71, (long)73, (long)79, (long)83, (long)89, (long)97})));\n}\n"}
{"name": "HumanEval_34_unique", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return sorted unique elements in a vector\n// >>> unique((std::vector<long>({(long)5, (long)3, (long)5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123})))\n// (std::vector<long>({(long)0, (long)2, (long)3, (long)5, (long)9, (long)123}))\nstd::vector<long> unique(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = unique;\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123}))) == (std::vector<long>({(long)0, (long)2, (long)3, (long)5, (long)9, (long)123})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_34_unique", "test": "}\nint main() {\n    auto candidate = unique;\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123}))) == (std::vector<long>({(long)0, (long)2, (long)3, (long)5, (long)9, (long)123})));\n}\n"}
{"name": "HumanEval_74_total_match", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that accepts two vectors of strings and returns the vector that has \n// total number of chars in the all strings of the vector less than the other vector.\n// if the two vectors have the same number of chars, return the first vector.\n// Examples\n// >>> total_match((std::vector<std::string>()), (std::vector<std::string>()))\n// (std::vector<std::string>())\n// >>> total_match((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"Hi\"})))\n// (std::vector<std::string>({(std::string)\"hI\", (std::string)\"Hi\"}))\n// >>> total_match((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\", (std::string)\"admin\", (std::string)\"project\"})))\n// (std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"}))\n// >>> total_match((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hi\"})))\n// (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hi\"}))\n// >>> total_match((std::vector<std::string>({(std::string)\"4\"})), (std::vector<std::string>({(std::string)\"1\", (std::string)\"2\", (std::string)\"3\", (std::string)\"4\", (std::string)\"5\"})))\n// (std::vector<std::string>({(std::string)\"4\"}))\nstd::vector<std::string> total_match(std::vector<std::string> lst1, std::vector<std::string> lst2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = total_match;\n    assert(candidate((std::vector<std::string>()), (std::vector<std::string>())) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\"}))) == (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\", (std::string)\"admin\", (std::string)\"project\"}))) == (std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"4\"})), (std::vector<std::string>({(std::string)\"1\", (std::string)\"2\", (std::string)\"3\", (std::string)\"4\", (std::string)\"5\"}))) == (std::vector<std::string>({(std::string)\"4\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"Hi\"}))) == (std::vector<std::string>({(std::string)\"hI\", (std::string)\"Hi\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hi\"}))) == (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hi\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hii\"}))) == (std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})));\n    assert(candidate((std::vector<std::string>()), (std::vector<std::string>({(std::string)\"this\"}))) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"this\"})), (std::vector<std::string>())) == (std::vector<std::string>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_74_total_match", "test": "}\nint main() {\n    auto candidate = total_match;\n    assert(candidate((std::vector<std::string>()), (std::vector<std::string>())) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\"}))) == (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hi\", (std::string)\"hi\", (std::string)\"admin\", (std::string)\"project\"}))) == (std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"4\"})), (std::vector<std::string>({(std::string)\"1\", (std::string)\"2\", (std::string)\"3\", (std::string)\"4\", (std::string)\"5\"}))) == (std::vector<std::string>({(std::string)\"4\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"Hi\"}))) == (std::vector<std::string>({(std::string)\"hI\", (std::string)\"Hi\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hi\"}))) == (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hi\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})), (std::vector<std::string>({(std::string)\"hI\", (std::string)\"hi\", (std::string)\"hii\"}))) == (std::vector<std::string>({(std::string)\"hi\", (std::string)\"admin\"})));\n    assert(candidate((std::vector<std::string>()), (std::vector<std::string>({(std::string)\"this\"}))) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"this\"})), (std::vector<std::string>())) == (std::vector<std::string>()));\n}\n"}
{"name": "HumanEval_35_max_element", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return maximum element in the vector.\n// >>> max_element((std::vector<long>({(long)1, (long)2, (long)3})))\n// (3)\n// >>> max_element((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)9, (long)0, (long)123, (long)1, (long)-10})))\n// (123)\nlong max_element(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = max_element;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (3));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)9, (long)0, (long)124, (long)1, (long)-10}))) == (124));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_35_max_element", "test": "}\nint main() {\n    auto candidate = max_element;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (3));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)9, (long)0, (long)124, (long)1, (long)-10}))) == (124));\n}\n"}
{"name": "HumanEval_132_is_nested", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested((\"[[]]\"))\n// (true)\n// >>> is_nested((\"[]]]]]]][[[[[]\"))\n// (false)\n// >>> is_nested((\"[][]\"))\n// (false)\n// >>> is_nested((\"[]\"))\n// (false)\n// >>> is_nested((\"[[][]]\"))\n// (true)\n// >>> is_nested((\"[[]][[\"))\n// (true)\nbool is_nested(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_nested;\n    assert(candidate((\"[[]]\")) == (true));\n    assert(candidate((\"[]]]]]]][[[[[]\")) == (false));\n    assert(candidate((\"[][]\")) == (false));\n    assert(candidate((\"[]\")) == (false));\n    assert(candidate((\"[[[[]]]]\")) == (true));\n    assert(candidate((\"[]]]]]]]]]]\")) == (false));\n    assert(candidate((\"[][][[]]\")) == (true));\n    assert(candidate((\"[[]\")) == (false));\n    assert(candidate((\"[]]\")) == (false));\n    assert(candidate((\"[[]][[\")) == (true));\n    assert(candidate((\"[[][]]\")) == (true));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"[[[[[[[[\")) == (false));\n    assert(candidate((\"]]]]]]]]\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_132_is_nested", "test": "}\nint main() {\n    auto candidate = is_nested;\n    assert(candidate((\"[[]]\")) == (true));\n    assert(candidate((\"[]]]]]]][[[[[]\")) == (false));\n    assert(candidate((\"[][]\")) == (false));\n    assert(candidate((\"[]\")) == (false));\n    assert(candidate((\"[[[[]]]]\")) == (true));\n    assert(candidate((\"[]]]]]]]]]]\")) == (false));\n    assert(candidate((\"[][][[]]\")) == (true));\n    assert(candidate((\"[[]\")) == (false));\n    assert(candidate((\"[]]\")) == (false));\n    assert(candidate((\"[[]][[\")) == (true));\n    assert(candidate((\"[[][]]\")) == (true));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"[[[[[[[[\")) == (false));\n    assert(candidate((\"]]]]]]]]\")) == (false));\n}\n"}
{"name": "HumanEval_103_rounded_avg", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\nunion Union_std_string_long{\n    std::string f0;\n    long f1;    Union_std_string_long(std::string _f0) : f0(_f0) {}\n    Union_std_string_long(long _f1) : f1(_f1) {}\n    ~Union_std_string_long() {}\n    bool operator==(std::string f) {\n        return f0 == f ;\n    }    bool operator==(long f) {\n        return f1 == f ;\n    }\n};\n// You are given two positive integers n and m, and your task is to compute the\n// average of the integers from n through m (including n and m). \n// Round the answer to the nearest integer and convert that to binary.\n// If n is greater than m, return -1.\n// Example:\n// >>> rounded_avg((1), (5))\n// \"0b11\"\n// >>> rounded_avg((7), (5))\n// -1\n// >>> rounded_avg((10), (20))\n// \"0b1111\"\n// >>> rounded_avg((20), (33))\n// \"0b11010\"\nUnion_std_string_long rounded_avg(long n, long m) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_103_rounded_avg.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = rounded_avg;\n    assert(candidate((1), (5)) == \"0b11\");\n    assert(candidate((7), (13)) == \"0b1010\");\n    assert(candidate((964), (977)) == \"0b1111001010\");\n    assert(candidate((996), (997)) == \"0b1111100100\");\n    assert(candidate((560), (851)) == \"0b1011000010\");\n    assert(candidate((185), (546)) == \"0b101101110\");\n    assert(candidate((362), (496)) == \"0b110101101\");\n    assert(candidate((350), (902)) == \"0b1001110010\");\n    assert(candidate((197), (233)) == \"0b11010111\");\n    assert(candidate((7), (5)) == -1);\n    assert(candidate((5), (1)) == -1);\n    assert(candidate((5), (5)) == \"0b101\");\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_103_rounded_avg", "test": "}\nint main() {\n    auto candidate = rounded_avg;\n    assert(candidate((1), (5)) == \"0b11\");\n    assert(candidate((7), (13)) == \"0b1010\");\n    assert(candidate((964), (977)) == \"0b1111001010\");\n    assert(candidate((996), (997)) == \"0b1111100100\");\n    assert(candidate((560), (851)) == \"0b1011000010\");\n    assert(candidate((185), (546)) == \"0b101101110\");\n    assert(candidate((362), (496)) == \"0b110101101\");\n    assert(candidate((350), (902)) == \"0b1001110010\");\n    assert(candidate((197), (233)) == \"0b11010111\");\n    assert(candidate((7), (5)) == -1);\n    assert(candidate((5), (1)) == -1);\n    assert(candidate((5), (5)) == \"0b101\");\n}\n"}
{"name": "HumanEval_113_odd_count", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of strings, where each string consists of only digits, return a vector.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count((std::vector<std::string>({(std::string)\"1234567\"})))\n// (std::vector<std::string>({(std::string)\"the number of odd elements 4n the str4ng 4 of the 4nput.\"}))\n// >>> odd_count((std::vector<std::string>({(std::string)\"3\", (std::string)\"11111111\"})))\n// (std::vector<std::string>({(std::string)\"the number of odd elements 1n the str1ng 1 of the 1nput.\", (std::string)\"the number of odd elements 8n the str8ng 8 of the 8nput.\"}))\nstd::vector<std::string> odd_count(std::vector<std::string> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = odd_count;\n    assert(candidate((std::vector<std::string>({(std::string)\"1234567\"}))) == (std::vector<std::string>({(std::string)\"the number of odd elements 4n the str4ng 4 of the 4nput.\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"3\", (std::string)\"11111111\"}))) == (std::vector<std::string>({(std::string)\"the number of odd elements 1n the str1ng 1 of the 1nput.\", (std::string)\"the number of odd elements 8n the str8ng 8 of the 8nput.\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"271\", (std::string)\"137\", (std::string)\"314\"}))) == (std::vector<std::string>({(std::string)\"the number of odd elements 2n the str2ng 2 of the 2nput.\", (std::string)\"the number of odd elements 3n the str3ng 3 of the 3nput.\", (std::string)\"the number of odd elements 2n the str2ng 2 of the 2nput.\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_113_odd_count", "test": "}\nint main() {\n    auto candidate = odd_count;\n    assert(candidate((std::vector<std::string>({(std::string)\"1234567\"}))) == (std::vector<std::string>({(std::string)\"the number of odd elements 4n the str4ng 4 of the 4nput.\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"3\", (std::string)\"11111111\"}))) == (std::vector<std::string>({(std::string)\"the number of odd elements 1n the str1ng 1 of the 1nput.\", (std::string)\"the number of odd elements 8n the str8ng 8 of the 8nput.\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"271\", (std::string)\"137\", (std::string)\"314\"}))) == (std::vector<std::string>({(std::string)\"the number of odd elements 2n the str2ng 2 of the 2nput.\", (std::string)\"the number of odd elements 3n the str3ng 3 of the 3nput.\", (std::string)\"the number of odd elements 2n the str2ng 2 of the 2nput.\"})));\n}\n"}
{"name": "HumanEval_109_move_one_ball", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// We have a vector 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the vector will be randomly ordered. Your task is to determine if\n// it is possible to get a vector sorted in non-decreasing order by performing \n// the following operation on the given vector:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the vector by one\n// position in the right direction. The last element of the vector will be moved to\n// the starting position in the vector i.e. 0th index. \n// If it is possible to obtain the sorted vector by performing the above operation\n// then return true else return false.\n// If the given vector is empty then return true.\n// Note: The given vector is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball((std::vector<long>({(long)3, (long)4, (long)5, (long)1, (long)2})))\n// (true)\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given vector.\n// >>> move_one_ball((std::vector<long>({(long)3, (long)5, (long)4, (long)1, (long)2})))\n// (false)\n// Explanation:It is not possible to get non-decreasing order for the given\n// vector by performing any number of right shift operations.\nbool move_one_ball(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = move_one_ball;\n    assert(candidate((std::vector<long>({(long)3, (long)4, (long)5, (long)1, (long)2}))) == (true));\n    assert(candidate((std::vector<long>({(long)3, (long)5, (long)10, (long)1, (long)2}))) == (true));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)1, (long)2}))) == (false));\n    assert(candidate((std::vector<long>({(long)3, (long)5, (long)4, (long)1, (long)2}))) == (false));\n    assert(candidate((std::vector<long>())) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_109_move_one_ball", "test": "}\nint main() {\n    auto candidate = move_one_ball;\n    assert(candidate((std::vector<long>({(long)3, (long)4, (long)5, (long)1, (long)2}))) == (true));\n    assert(candidate((std::vector<long>({(long)3, (long)5, (long)10, (long)1, (long)2}))) == (true));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)1, (long)2}))) == (false));\n    assert(candidate((std::vector<long>({(long)3, (long)5, (long)4, (long)1, (long)2}))) == (false));\n    assert(candidate((std::vector<long>())) == (true));\n}\n"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer n, return a tuple that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome((3))\n// (std::make_tuple(1, 2))\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome((12))\n// (std::make_tuple(4, 6))\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned tuple has the number of even and odd integer palindromes respectively.\nstd::tuple<long, long> even_odd_palindrome(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = even_odd_palindrome;\n    assert(candidate((123)) == (std::make_tuple(8, 13)));\n    assert(candidate((12)) == (std::make_tuple(4, 6)));\n    assert(candidate((3)) == (std::make_tuple(1, 2)));\n    assert(candidate((63)) == (std::make_tuple(6, 8)));\n    assert(candidate((25)) == (std::make_tuple(5, 6)));\n    assert(candidate((19)) == (std::make_tuple(4, 6)));\n    assert(candidate((9)) == (std::make_tuple(4, 5)));\n    assert(candidate((1)) == (std::make_tuple(0, 1)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "}\nint main() {\n    auto candidate = even_odd_palindrome;\n    assert(candidate((123)) == (std::make_tuple(8, 13)));\n    assert(candidate((12)) == (std::make_tuple(4, 6)));\n    assert(candidate((3)) == (std::make_tuple(1, 2)));\n    assert(candidate((63)) == (std::make_tuple(6, 8)));\n    assert(candidate((25)) == (std::make_tuple(5, 6)));\n    assert(candidate((19)) == (std::make_tuple(4, 6)));\n    assert(candidate((9)) == (std::make_tuple(4, 5)));\n    assert(candidate((1)) == (std::make_tuple(0, 1)));\n}\n"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even((4))\n// (false)\n// >>> is_equal_to_sum_even((6))\n// (false)\n// >>> is_equal_to_sum_even((8))\n// (true)\nbool is_equal_to_sum_even(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_equal_to_sum_even;\n    assert(candidate((4)) == (false));\n    assert(candidate((6)) == (false));\n    assert(candidate((8)) == (true));\n    assert(candidate((10)) == (true));\n    assert(candidate((11)) == (false));\n    assert(candidate((12)) == (true));\n    assert(candidate((13)) == (false));\n    assert(candidate((16)) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "}\nint main() {\n    auto candidate = is_equal_to_sum_even;\n    assert(candidate((4)) == (false));\n    assert(candidate((6)) == (false));\n    assert(candidate((8)) == (true));\n    assert(candidate((10)) == (true));\n    assert(candidate((11)) == (false));\n    assert(candidate((12)) == (true));\n    assert(candidate((13)) == (false));\n    assert(candidate((16)) == (true));\n}\n"}
{"name": "HumanEval_62_derivative", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative((std::vector<long>({(long)3, (long)1, (long)2, (long)4, (long)5})))\n// (std::vector<long>({(long)1, (long)4, (long)12, (long)20}))\n// >>> derivative((std::vector<long>({(long)1, (long)2, (long)3})))\n// (std::vector<long>({(long)2, (long)6}))\nstd::vector<long> derivative(std::vector<long> xs) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = derivative;\n    assert(candidate((std::vector<long>({(long)3, (long)1, (long)2, (long)4, (long)5}))) == (std::vector<long>({(long)1, (long)4, (long)12, (long)20})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (std::vector<long>({(long)2, (long)6})));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1}))) == (std::vector<long>({(long)2, (long)2})));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1, (long)0, (long)4}))) == (std::vector<long>({(long)2, (long)2, (long)0, (long)16})));\n    assert(candidate((std::vector<long>({(long)1}))) == (std::vector<long>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_62_derivative", "test": "}\nint main() {\n    auto candidate = derivative;\n    assert(candidate((std::vector<long>({(long)3, (long)1, (long)2, (long)4, (long)5}))) == (std::vector<long>({(long)1, (long)4, (long)12, (long)20})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (std::vector<long>({(long)2, (long)6})));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1}))) == (std::vector<long>({(long)2, (long)2})));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1, (long)0, (long)4}))) == (std::vector<long>({(long)2, (long)2, (long)0, (long)16})));\n    assert(candidate((std::vector<long>({(long)1}))) == (std::vector<long>()));\n}\n"}
{"name": "HumanEval_126_is_sorted", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of numbers, return whether or not they are sorted\n// in ascending order. If vector has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted((std::vector<long>({(long)5})))\n// (true)\n// >>> is_sorted((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5})))\n// (true)\n// >>> is_sorted((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5})))\n// (false)\n// >>> is_sorted((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6})))\n// (true)\n// >>> is_sorted((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7})))\n// (true)\n// >>> is_sorted((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5, (long)6, (long)7})))\n// (false)\n// >>> is_sorted((std::vector<long>({(long)1, (long)2, (long)2, (long)3, (long)3, (long)4})))\n// (true)\n// >>> is_sorted((std::vector<long>({(long)1, (long)2, (long)2, (long)2, (long)3, (long)4})))\n// (false)\nbool is_sorted(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_sorted;\n    assert(candidate((std::vector<long>({(long)5}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5, (long)6, (long)7}))) == (false));\n    assert(candidate((std::vector<long>())) == (true));\n    assert(candidate((std::vector<long>({(long)1}))) == (true));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)2, (long)2, (long)3, (long)4}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)3, (long)3, (long)4}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)2, (long)3, (long)3, (long)4}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_126_is_sorted", "test": "}\nint main() {\n    auto candidate = is_sorted;\n    assert(candidate((std::vector<long>({(long)5}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5, (long)6, (long)7}))) == (false));\n    assert(candidate((std::vector<long>())) == (true));\n    assert(candidate((std::vector<long>({(long)1}))) == (true));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)2, (long)2, (long)3, (long)4}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)3, (long)3, (long)4}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)2, (long)3, (long)3, (long)4}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (true));\n}\n"}
{"name": "HumanEval_161_solve", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve((\"1234\"))\n// (\"4321\")\n// >>> solve((\"ab\"))\n// (\"AB\")\n// >>> solve((\"#a@C\"))\n// (\"#A@c\")\nstd::string solve(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = solve;\n    assert(candidate((\"AsDf\")) == (\"aSdF\"));\n    assert(candidate((\"1234\")) == (\"4321\"));\n    assert(candidate((\"ab\")) == (\"AB\"));\n    assert(candidate((\"#a@C\")) == (\"#A@c\"));\n    assert(candidate((\"#AsdfW^45\")) == (\"#aSDFw^45\"));\n    assert(candidate((\"#6@2\")) == (\"2@6#\"));\n    assert(candidate((\"#$a^D\")) == (\"#$A^d\"));\n    assert(candidate((\"#ccc\")) == (\"#CCC\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_161_solve", "test": "}\nint main() {\n    auto candidate = solve;\n    assert(candidate((\"AsDf\")) == (\"aSdF\"));\n    assert(candidate((\"1234\")) == (\"4321\"));\n    assert(candidate((\"ab\")) == (\"AB\"));\n    assert(candidate((\"#a@C\")) == (\"#A@c\"));\n    assert(candidate((\"#AsdfW^45\")) == (\"#aSDFw^45\"));\n    assert(candidate((\"#6@2\")) == (\"2@6#\"));\n    assert(candidate((\"#$a^D\")) == (\"#$A^d\"));\n    assert(candidate((\"#ccc\")) == (\"#CCC\"));\n}\n"}
{"name": "HumanEval_130_tri", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return a vector of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri((3))\n// (std::vector<long>({(long)1, (long)3, (long)2, (long)8}))\nstd::vector<long> tri(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = tri;\n    assert(candidate((3)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8})));\n    assert(candidate((4)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3})));\n    assert(candidate((5)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15})));\n    assert(candidate((6)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4})));\n    assert(candidate((7)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24})));\n    assert(candidate((8)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24, (long)5})));\n    assert(candidate((9)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24, (long)5, (long)35})));\n    assert(candidate((20)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24, (long)5, (long)35, (long)6, (long)48, (long)7, (long)63, (long)8, (long)80, (long)9, (long)99, (long)10, (long)120, (long)11})));\n    assert(candidate((0)) == (std::vector<long>({(long)1})));\n    assert(candidate((1)) == (std::vector<long>({(long)1, (long)3})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_130_tri", "test": "}\nint main() {\n    auto candidate = tri;\n    assert(candidate((3)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8})));\n    assert(candidate((4)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3})));\n    assert(candidate((5)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15})));\n    assert(candidate((6)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4})));\n    assert(candidate((7)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24})));\n    assert(candidate((8)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24, (long)5})));\n    assert(candidate((9)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24, (long)5, (long)35})));\n    assert(candidate((20)) == (std::vector<long>({(long)1, (long)3, (long)2, (long)8, (long)3, (long)15, (long)4, (long)24, (long)5, (long)35, (long)6, (long)48, (long)7, (long)63, (long)8, (long)80, (long)9, (long)99, (long)10, (long)120, (long)11})));\n    assert(candidate((0)) == (std::vector<long>({(long)1})));\n    assert(candidate((1)) == (std::vector<long>({(long)1, (long)3})));\n}\n"}
{"name": "HumanEval_36_fizz_buzz", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz((50))\n// (0)\n// >>> fizz_buzz((78))\n// (2)\n// >>> fizz_buzz((79))\n// (3)\nlong fizz_buzz(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = fizz_buzz;\n    assert(candidate((50)) == (0));\n    assert(candidate((78)) == (2));\n    assert(candidate((79)) == (3));\n    assert(candidate((100)) == (3));\n    assert(candidate((200)) == (6));\n    assert(candidate((4000)) == (192));\n    assert(candidate((10000)) == (639));\n    assert(candidate((100000)) == (8026));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_36_fizz_buzz", "test": "}\nint main() {\n    auto candidate = fizz_buzz;\n    assert(candidate((50)) == (0));\n    assert(candidate((78)) == (2));\n    assert(candidate((79)) == (3));\n    assert(candidate((100)) == (3));\n    assert(candidate((200)) == (6));\n    assert(candidate((4000)) == (192));\n    assert(candidate((10000)) == (639));\n    assert(candidate((100000)) == (8026));\n}\n"}
{"name": "HumanEval_29_filter_by_prefix", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Filter an input vector of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix((std::vector<std::string>()), (\"a\"))\n// (std::vector<std::string>())\n// >>> filter_by_prefix((std::vector<std::string>({(std::string)\"abc\", (std::string)\"bcd\", (std::string)\"cde\", (std::string)\"array\"})), (\"a\"))\n// (std::vector<std::string>({(std::string)\"abc\", (std::string)\"array\"}))\nstd::vector<std::string> filter_by_prefix(std::vector<std::string> strings, std::string prefix) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = filter_by_prefix;\n    assert(candidate((std::vector<std::string>()), (\"john\")) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"xxx\", (std::string)\"asd\", (std::string)\"xxy\", (std::string)\"john doe\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})), (\"xxx\")) == (std::vector<std::string>({(std::string)\"xxx\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_29_filter_by_prefix", "test": "}\nint main() {\n    auto candidate = filter_by_prefix;\n    assert(candidate((std::vector<std::string>()), (\"john\")) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"xxx\", (std::string)\"asd\", (std::string)\"xxy\", (std::string)\"john doe\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})), (\"xxx\")) == (std::vector<std::string>({(std::string)\"xxx\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})));\n}\n"}
{"name": "HumanEval_84_solve", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve((1000))\n// (\"1\")\n// >>> solve((150))\n// (\"110\")\n// >>> solve((147))\n// (\"1100\")\n// Variables:\n// @N integer\n// Constraints: 0 \u2264 N \u2264 10000.\n// Output:\n// a string of binary number\nstd::string solve(long N) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = solve;\n    assert(candidate((1000)) == (\"1\"));\n    assert(candidate((150)) == (\"110\"));\n    assert(candidate((147)) == (\"1100\"));\n    assert(candidate((333)) == (\"1001\"));\n    assert(candidate((963)) == (\"10010\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_84_solve", "test": "}\nint main() {\n    auto candidate = solve;\n    assert(candidate((1000)) == (\"1\"));\n    assert(candidate((150)) == (\"110\"));\n    assert(candidate((147)) == (\"1100\"));\n    assert(candidate((333)) == (\"1001\"));\n    assert(candidate((963)) == (\"10010\"));\n}\n"}
{"name": "HumanEval_129_minPath", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered vectors of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered vector of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3}), (std::vector<long>)std::vector<long>({(long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)7, (long)8, (long)9})})), (3))\n// (std::vector<long>({(long)1, (long)2, (long)1}))\n// >>> minPath((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)5, (long)9, (long)3}), (std::vector<long>)std::vector<long>({(long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)7, (long)8, (long)2})})), (1))\n// (std::vector<long>({(long)1}))\nstd::vector<long> minPath(std::vector<std::vector<long>> grid, long k) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = minPath;\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3}), (std::vector<long>)std::vector<long>({(long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)7, (long)8, (long)9})})), (3)) == (std::vector<long>({(long)1, (long)2, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)5, (long)9, (long)3}), (std::vector<long>)std::vector<long>({(long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)7, (long)8, (long)2})})), (1)) == (std::vector<long>({(long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4}), (std::vector<long>)std::vector<long>({(long)5, (long)6, (long)7, (long)8}), (std::vector<long>)std::vector<long>({(long)9, (long)10, (long)11, (long)12}), (std::vector<long>)std::vector<long>({(long)13, (long)14, (long)15, (long)16})})), (4)) == (std::vector<long>({(long)1, (long)2, (long)1, (long)2})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)6, (long)4, (long)13, (long)10}), (std::vector<long>)std::vector<long>({(long)5, (long)7, (long)12, (long)1}), (std::vector<long>)std::vector<long>({(long)3, (long)16, (long)11, (long)15}), (std::vector<long>)std::vector<long>({(long)8, (long)14, (long)9, (long)2})})), (7)) == (std::vector<long>({(long)1, (long)10, (long)1, (long)10, (long)1, (long)10, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)8, (long)14, (long)9, (long)2}), (std::vector<long>)std::vector<long>({(long)6, (long)4, (long)13, (long)15}), (std::vector<long>)std::vector<long>({(long)5, (long)7, (long)1, (long)12}), (std::vector<long>)std::vector<long>({(long)3, (long)10, (long)11, (long)16})})), (5)) == (std::vector<long>({(long)1, (long)7, (long)1, (long)7, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)11, (long)8, (long)7, (long)2}), (std::vector<long>)std::vector<long>({(long)5, (long)16, (long)14, (long)4}), (std::vector<long>)std::vector<long>({(long)9, (long)3, (long)15, (long)6}), (std::vector<long>)std::vector<long>({(long)12, (long)13, (long)10, (long)1})})), (9)) == (std::vector<long>({(long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)12, (long)13, (long)10, (long)1}), (std::vector<long>)std::vector<long>({(long)9, (long)3, (long)15, (long)6}), (std::vector<long>)std::vector<long>({(long)5, (long)16, (long)14, (long)4}), (std::vector<long>)std::vector<long>({(long)11, (long)8, (long)7, (long)2})})), (12)) == (std::vector<long>({(long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)2, (long)7, (long)4}), (std::vector<long>)std::vector<long>({(long)3, (long)1, (long)5}), (std::vector<long>)std::vector<long>({(long)6, (long)8, (long)9})})), (8)) == (std::vector<long>({(long)1, (long)3, (long)1, (long)3, (long)1, (long)3, (long)1, (long)3})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)6, (long)1, (long)5}), (std::vector<long>)std::vector<long>({(long)3, (long)8, (long)9}), (std::vector<long>)std::vector<long>({(long)2, (long)7, (long)4})})), (8)) == (std::vector<long>({(long)1, (long)5, (long)1, (long)5, (long)1, (long)5, (long)1, (long)5})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2}), (std::vector<long>)std::vector<long>({(long)3, (long)4})})), (10)) == (std::vector<long>({(long)1, (long)2, (long)1, (long)2, (long)1, (long)2, (long)1, (long)2, (long)1, (long)2})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)3}), (std::vector<long>)std::vector<long>({(long)3, (long)2})})), (10)) == (std::vector<long>({(long)1, (long)3, (long)1, (long)3, (long)1, (long)3, (long)1, (long)3, (long)1, (long)3})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_129_minPath", "test": "}\nint main() {\n    auto candidate = minPath;\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3}), (std::vector<long>)std::vector<long>({(long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)7, (long)8, (long)9})})), (3)) == (std::vector<long>({(long)1, (long)2, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)5, (long)9, (long)3}), (std::vector<long>)std::vector<long>({(long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)7, (long)8, (long)2})})), (1)) == (std::vector<long>({(long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4}), (std::vector<long>)std::vector<long>({(long)5, (long)6, (long)7, (long)8}), (std::vector<long>)std::vector<long>({(long)9, (long)10, (long)11, (long)12}), (std::vector<long>)std::vector<long>({(long)13, (long)14, (long)15, (long)16})})), (4)) == (std::vector<long>({(long)1, (long)2, (long)1, (long)2})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)6, (long)4, (long)13, (long)10}), (std::vector<long>)std::vector<long>({(long)5, (long)7, (long)12, (long)1}), (std::vector<long>)std::vector<long>({(long)3, (long)16, (long)11, (long)15}), (std::vector<long>)std::vector<long>({(long)8, (long)14, (long)9, (long)2})})), (7)) == (std::vector<long>({(long)1, (long)10, (long)1, (long)10, (long)1, (long)10, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)8, (long)14, (long)9, (long)2}), (std::vector<long>)std::vector<long>({(long)6, (long)4, (long)13, (long)15}), (std::vector<long>)std::vector<long>({(long)5, (long)7, (long)1, (long)12}), (std::vector<long>)std::vector<long>({(long)3, (long)10, (long)11, (long)16})})), (5)) == (std::vector<long>({(long)1, (long)7, (long)1, (long)7, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)11, (long)8, (long)7, (long)2}), (std::vector<long>)std::vector<long>({(long)5, (long)16, (long)14, (long)4}), (std::vector<long>)std::vector<long>({(long)9, (long)3, (long)15, (long)6}), (std::vector<long>)std::vector<long>({(long)12, (long)13, (long)10, (long)1})})), (9)) == (std::vector<long>({(long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)12, (long)13, (long)10, (long)1}), (std::vector<long>)std::vector<long>({(long)9, (long)3, (long)15, (long)6}), (std::vector<long>)std::vector<long>({(long)5, (long)16, (long)14, (long)4}), (std::vector<long>)std::vector<long>({(long)11, (long)8, (long)7, (long)2})})), (12)) == (std::vector<long>({(long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6, (long)1, (long)6})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)2, (long)7, (long)4}), (std::vector<long>)std::vector<long>({(long)3, (long)1, (long)5}), (std::vector<long>)std::vector<long>({(long)6, (long)8, (long)9})})), (8)) == (std::vector<long>({(long)1, (long)3, (long)1, (long)3, (long)1, (long)3, (long)1, (long)3})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)6, (long)1, (long)5}), (std::vector<long>)std::vector<long>({(long)3, (long)8, (long)9}), (std::vector<long>)std::vector<long>({(long)2, (long)7, (long)4})})), (8)) == (std::vector<long>({(long)1, (long)5, (long)1, (long)5, (long)1, (long)5, (long)1, (long)5})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2}), (std::vector<long>)std::vector<long>({(long)3, (long)4})})), (10)) == (std::vector<long>({(long)1, (long)2, (long)1, (long)2, (long)1, (long)2, (long)1, (long)2, (long)1, (long)2})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)3}), (std::vector<long>)std::vector<long>({(long)3, (long)2})})), (10)) == (std::vector<long>({(long)1, (long)3, (long)1, (long)3, (long)1, (long)3, (long)1, (long)3, (long)1, (long)3})));\n}\n"}
{"name": "HumanEval_98_count_upper", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper((\"aBCdEf\"))\n// (1)\n// >>> count_upper((\"abcdefg\"))\n// (0)\n// >>> count_upper((\"dBBE\"))\n// (0)\nlong count_upper(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = count_upper;\n    assert(candidate((\"aBCdEf\")) == (1));\n    assert(candidate((\"abcdefg\")) == (0));\n    assert(candidate((\"dBBE\")) == (0));\n    assert(candidate((\"B\")) == (0));\n    assert(candidate((\"U\")) == (1));\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"EEEE\")) == (2));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_98_count_upper", "test": "}\nint main() {\n    auto candidate = count_upper;\n    assert(candidate((\"aBCdEf\")) == (1));\n    assert(candidate((\"abcdefg\")) == (0));\n    assert(candidate((\"dBBE\")) == (0));\n    assert(candidate((\"B\")) == (0));\n    assert(candidate((\"U\")) == (1));\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"EEEE\")) == (2));\n}\n"}
{"name": "HumanEval_120_maximum", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector arr of integers and a positive integer k, return a sorted vector \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum((std::vector<long>({(long)-3, (long)-4, (long)5})), (3))\n// (std::vector<long>({(long)-4, (long)-3, (long)5}))\n// Example 2:\n// >>> maximum((std::vector<long>({(long)4, (long)-4, (long)4})), (2))\n// (std::vector<long>({(long)4, (long)4}))\n// Example 3:\n// >>> maximum((std::vector<long>({(long)-3, (long)2, (long)1, (long)2, (long)-1, (long)-2, (long)1})), (1))\n// (std::vector<long>({(long)2}))\n// Note:\n// 1. The length of the vector will be in the range of [1, 1000].\n// 2. The elements in the vector will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nstd::vector<long> maximum(std::vector<long> arr, long k) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = maximum;\n    assert(candidate((std::vector<long>({(long)-3, (long)-4, (long)5})), (3)) == (std::vector<long>({(long)-4, (long)-3, (long)5})));\n    assert(candidate((std::vector<long>({(long)4, (long)-4, (long)4})), (2)) == (std::vector<long>({(long)4, (long)4})));\n    assert(candidate((std::vector<long>({(long)-3, (long)2, (long)1, (long)2, (long)-1, (long)-2, (long)1})), (1)) == (std::vector<long>({(long)2})));\n    assert(candidate((std::vector<long>({(long)123, (long)-123, (long)20, (long)0, (long)1, (long)2, (long)-3})), (3)) == (std::vector<long>({(long)2, (long)20, (long)123})));\n    assert(candidate((std::vector<long>({(long)-123, (long)20, (long)0, (long)1, (long)2, (long)-3})), (4)) == (std::vector<long>({(long)0, (long)1, (long)2, (long)20})));\n    assert(candidate((std::vector<long>({(long)5, (long)15, (long)0, (long)3, (long)-13, (long)-8, (long)0})), (7)) == (std::vector<long>({(long)-13, (long)-8, (long)0, (long)0, (long)3, (long)5, (long)15})));\n    assert(candidate((std::vector<long>({(long)-1, (long)0, (long)2, (long)5, (long)3, (long)-10})), (2)) == (std::vector<long>({(long)3, (long)5})));\n    assert(candidate((std::vector<long>({(long)1, (long)0, (long)5, (long)-7})), (1)) == (std::vector<long>({(long)5})));\n    assert(candidate((std::vector<long>({(long)4, (long)-4})), (2)) == (std::vector<long>({(long)-4, (long)4})));\n    assert(candidate((std::vector<long>({(long)-10, (long)10})), (2)) == (std::vector<long>({(long)-10, (long)10})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)-23, (long)243, (long)-400, (long)0})), (0)) == (std::vector<long>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_120_maximum", "test": "}\nint main() {\n    auto candidate = maximum;\n    assert(candidate((std::vector<long>({(long)-3, (long)-4, (long)5})), (3)) == (std::vector<long>({(long)-4, (long)-3, (long)5})));\n    assert(candidate((std::vector<long>({(long)4, (long)-4, (long)4})), (2)) == (std::vector<long>({(long)4, (long)4})));\n    assert(candidate((std::vector<long>({(long)-3, (long)2, (long)1, (long)2, (long)-1, (long)-2, (long)1})), (1)) == (std::vector<long>({(long)2})));\n    assert(candidate((std::vector<long>({(long)123, (long)-123, (long)20, (long)0, (long)1, (long)2, (long)-3})), (3)) == (std::vector<long>({(long)2, (long)20, (long)123})));\n    assert(candidate((std::vector<long>({(long)-123, (long)20, (long)0, (long)1, (long)2, (long)-3})), (4)) == (std::vector<long>({(long)0, (long)1, (long)2, (long)20})));\n    assert(candidate((std::vector<long>({(long)5, (long)15, (long)0, (long)3, (long)-13, (long)-8, (long)0})), (7)) == (std::vector<long>({(long)-13, (long)-8, (long)0, (long)0, (long)3, (long)5, (long)15})));\n    assert(candidate((std::vector<long>({(long)-1, (long)0, (long)2, (long)5, (long)3, (long)-10})), (2)) == (std::vector<long>({(long)3, (long)5})));\n    assert(candidate((std::vector<long>({(long)1, (long)0, (long)5, (long)-7})), (1)) == (std::vector<long>({(long)5})));\n    assert(candidate((std::vector<long>({(long)4, (long)-4})), (2)) == (std::vector<long>({(long)-4, (long)4})));\n    assert(candidate((std::vector<long>({(long)-10, (long)10})), (2)) == (std::vector<long>({(long)-10, (long)10})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)-23, (long)243, (long)-400, (long)0})), (0)) == (std::vector<long>()));\n}\n"}
{"name": "HumanEval_24_largest_divisor", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor((15))\n// (5)\nlong largest_divisor(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = largest_divisor;\n    assert(candidate((3)) == (1));\n    assert(candidate((7)) == (1));\n    assert(candidate((10)) == (5));\n    assert(candidate((100)) == (50));\n    assert(candidate((49)) == (7));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_24_largest_divisor", "test": "}\nint main() {\n    auto candidate = largest_divisor;\n    assert(candidate((3)) == (1));\n    assert(candidate((7)) == (1));\n    assert(candidate((10)) == (5));\n    assert(candidate((100)) == (50));\n    assert(candidate((49)) == (7));\n}\n"}
{"name": "HumanEval_88_sort_array", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of non-negative integers, return a cocpp of the given vector after sorting,\n// you will sort the given vector in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given vector.\n// Examples:\n// >>> sort_array((std::vector<long>()))\n// (std::vector<long>())\n// >>> sort_array((std::vector<long>({(long)5})))\n// (std::vector<long>({(long)5}))\n// >>> sort_array((std::vector<long>({(long)2, (long)4, (long)3, (long)0, (long)1, (long)5})))\n// (std::vector<long>({(long)0, (long)1, (long)2, (long)3, (long)4, (long)5}))\n// >>> sort_array((std::vector<long>({(long)2, (long)4, (long)3, (long)0, (long)1, (long)5, (long)6})))\n// (std::vector<long>({(long)6, (long)5, (long)4, (long)3, (long)2, (long)1, (long)0}))\nstd::vector<long> sort_array(std::vector<long> array) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sort_array;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)5}))) == (std::vector<long>({(long)5})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)3, (long)0, (long)1, (long)5}))) == (std::vector<long>({(long)0, (long)1, (long)2, (long)3, (long)4, (long)5})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)3, (long)0, (long)1, (long)5, (long)6}))) == (std::vector<long>({(long)6, (long)5, (long)4, (long)3, (long)2, (long)1, (long)0})));\n    assert(candidate((std::vector<long>({(long)2, (long)1}))) == (std::vector<long>({(long)1, (long)2})));\n    assert(candidate((std::vector<long>({(long)15, (long)42, (long)87, (long)32, (long)11, (long)0}))) == (std::vector<long>({(long)0, (long)11, (long)15, (long)32, (long)42, (long)87})));\n    assert(candidate((std::vector<long>({(long)21, (long)14, (long)23, (long)11}))) == (std::vector<long>({(long)23, (long)21, (long)14, (long)11})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_88_sort_array", "test": "}\nint main() {\n    auto candidate = sort_array;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)5}))) == (std::vector<long>({(long)5})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)3, (long)0, (long)1, (long)5}))) == (std::vector<long>({(long)0, (long)1, (long)2, (long)3, (long)4, (long)5})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)3, (long)0, (long)1, (long)5, (long)6}))) == (std::vector<long>({(long)6, (long)5, (long)4, (long)3, (long)2, (long)1, (long)0})));\n    assert(candidate((std::vector<long>({(long)2, (long)1}))) == (std::vector<long>({(long)1, (long)2})));\n    assert(candidate((std::vector<long>({(long)15, (long)42, (long)87, (long)32, (long)11, (long)0}))) == (std::vector<long>({(long)0, (long)11, (long)15, (long)32, (long)42, (long)87})));\n    assert(candidate((std::vector<long>({(long)21, (long)14, (long)23, (long)11}))) == (std::vector<long>({(long)23, (long)21, (long)14, (long)11})));\n}\n"}
{"name": "HumanEval_106_f", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Implement the function f that takes n as a parameter,\n// and returns a vector of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f((5))\n// (std::vector<long>({(long)1, (long)2, (long)6, (long)24, (long)15}))\nstd::vector<long> f(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = f;\n    assert(candidate((5)) == (std::vector<long>({(long)1, (long)2, (long)6, (long)24, (long)15})));\n    assert(candidate((7)) == (std::vector<long>({(long)1, (long)2, (long)6, (long)24, (long)15, (long)720, (long)28})));\n    assert(candidate((1)) == (std::vector<long>({(long)1})));\n    assert(candidate((3)) == (std::vector<long>({(long)1, (long)2, (long)6})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_106_f", "test": "}\nint main() {\n    auto candidate = f;\n    assert(candidate((5)) == (std::vector<long>({(long)1, (long)2, (long)6, (long)24, (long)15})));\n    assert(candidate((7)) == (std::vector<long>({(long)1, (long)2, (long)6, (long)24, (long)15, (long)720, (long)28})));\n    assert(candidate((1)) == (std::vector<long>({(long)1})));\n    assert(candidate((3)) == (std::vector<long>({(long)1, (long)2, (long)6})));\n}\n"}
{"name": "HumanEval_77_iscube", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube((1))\n// (true)\n// >>> iscube((2))\n// (false)\n// >>> iscube((-1))\n// (true)\n// >>> iscube((64))\n// (true)\n// >>> iscube((0))\n// (true)\n// >>> iscube((180))\n// (false)\nbool iscube(long a) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = iscube;\n    assert(candidate((1)) == (true));\n    assert(candidate((2)) == (false));\n    assert(candidate((-1)) == (true));\n    assert(candidate((64)) == (true));\n    assert(candidate((180)) == (false));\n    assert(candidate((1000)) == (true));\n    assert(candidate((0)) == (true));\n    assert(candidate((1729)) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_77_iscube", "test": "}\nint main() {\n    auto candidate = iscube;\n    assert(candidate((1)) == (true));\n    assert(candidate((2)) == (false));\n    assert(candidate((-1)) == (true));\n    assert(candidate((64)) == (true));\n    assert(candidate((180)) == (false));\n    assert(candidate((1000)) == (true));\n    assert(candidate((0)) == (true));\n    assert(candidate((1729)) == (false));\n}\n"}
{"name": "HumanEval_93_encode", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode((\"test\"))\n// (\"TGST\")\n// >>> encode((\"This is a message\"))\n// (\"tHKS KS C MGSSCGG\")\nstd::string encode(std::string message) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = encode;\n    assert(candidate((\"TEST\")) == (\"tgst\"));\n    assert(candidate((\"Mudasir\")) == (\"mWDCSKR\"));\n    assert(candidate((\"YES\")) == (\"ygs\"));\n    assert(candidate((\"This is a message\")) == (\"tHKS KS C MGSSCGG\"));\n    assert(candidate((\"I DoNt KnOw WhAt tO WrItE\")) == (\"k dQnT kNqW wHcT Tq wRkTg\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_93_encode", "test": "}\nint main() {\n    auto candidate = encode;\n    assert(candidate((\"TEST\")) == (\"tgst\"));\n    assert(candidate((\"Mudasir\")) == (\"mWDCSKR\"));\n    assert(candidate((\"YES\")) == (\"ygs\"));\n    assert(candidate((\"This is a message\")) == (\"tHKS KS C MGSSCGG\"));\n    assert(candidate((\"I DoNt KnOw WhAt tO WrItE\")) == (\"k dQnT kNqW wHcT Tq wRkTg\"));\n}\n"}
{"name": "HumanEval_91_is_bored", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored((\"Hello world\"))\n// (0)\n// >>> is_bored((\"The sky is blue. The sun is shining. I love this weather\"))\n// (1)\nlong is_bored(std::string S) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_bored;\n    assert(candidate((\"Hello world\")) == (0));\n    assert(candidate((\"Is the sky blue?\")) == (0));\n    assert(candidate((\"I love It !\")) == (1));\n    assert(candidate((\"bIt\")) == (0));\n    assert(candidate((\"I feel good today. I will be productive. will kill It\")) == (2));\n    assert(candidate((\"You and I are going for a walk\")) == (0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_91_is_bored", "test": "}\nint main() {\n    auto candidate = is_bored;\n    assert(candidate((\"Hello world\")) == (0));\n    assert(candidate((\"Is the sky blue?\")) == (0));\n    assert(candidate((\"I love It !\")) == (1));\n    assert(candidate((\"bIt\")) == (0));\n    assert(candidate((\"I feel good today. I will be productive. will kill It\")) == (2));\n    assert(candidate((\"You and I are going for a walk\")) == (0));\n}\n"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// pairs_sum_to_zero takes a vector of integers as an input.\n// it returns true if there are two distinct elements in the vector that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero((std::vector<long>({(long)1, (long)3, (long)5, (long)0})))\n// (false)\n// >>> pairs_sum_to_zero((std::vector<long>({(long)1, (long)3, (long)-2, (long)1})))\n// (false)\n// >>> pairs_sum_to_zero((std::vector<long>({(long)1, (long)2, (long)3, (long)7})))\n// (false)\n// >>> pairs_sum_to_zero((std::vector<long>({(long)2, (long)4, (long)-5, (long)3, (long)5, (long)7})))\n// (true)\n// >>> pairs_sum_to_zero((std::vector<long>({(long)1})))\n// (false)\nbool pairs_sum_to_zero(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = pairs_sum_to_zero;\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)0}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)-2, (long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)7}))) == (false));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)-5, (long)3, (long)5, (long)7}))) == (true));\n    assert(candidate((std::vector<long>({(long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)3, (long)2, (long)30}))) == (true));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)3, (long)2, (long)31}))) == (true));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)4, (long)2, (long)30}))) == (false));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)4, (long)2, (long)31}))) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "}\nint main() {\n    auto candidate = pairs_sum_to_zero;\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)0}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)-2, (long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)7}))) == (false));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)-5, (long)3, (long)5, (long)7}))) == (true));\n    assert(candidate((std::vector<long>({(long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)3, (long)2, (long)30}))) == (true));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)3, (long)2, (long)31}))) == (true));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)4, (long)2, (long)30}))) == (false));\n    assert(candidate((std::vector<long>({(long)-3, (long)9, (long)-1, (long)4, (long)2, (long)31}))) == (false));\n}\n"}
{"name": "HumanEval_71_triangle_area", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area((3), (4), (5))\n// (6.0f)\n// >>> triangle_area((1), (2), (10))\n// (float(-1))\nfloat triangle_area(long a, long b, long c) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = triangle_area;\n    assert(candidate((3), (4), (5)) == (6.0f));\n    assert(candidate((1), (2), (10)) == (float(-1)));\n    assert(candidate((4), (8), (5)) == (8.18f));\n    assert(candidate((2), (2), (2)) == (1.73f));\n    assert(candidate((1), (2), (3)) == (float(-1)));\n    assert(candidate((10), (5), (7)) == (16.25f));\n    assert(candidate((2), (6), (3)) == (float(-1)));\n    assert(candidate((1), (1), (1)) == (0.43f));\n    assert(candidate((2), (2), (10)) == (float(-1)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_71_triangle_area", "test": "}\nint main() {\n    auto candidate = triangle_area;\n    assert(candidate((3), (4), (5)) == (6.0f));\n    assert(candidate((1), (2), (10)) == (float(-1)));\n    assert(candidate((4), (8), (5)) == (8.18f));\n    assert(candidate((2), (2), (2)) == (1.73f));\n    assert(candidate((1), (2), (3)) == (float(-1)));\n    assert(candidate((10), (5), (7)) == (16.25f));\n    assert(candidate((2), (6), (3)) == (float(-1)));\n    assert(candidate((1), (1), (1)) == (0.43f));\n    assert(candidate((2), (2), (10)) == (float(-1)));\n}\n"}
{"name": "HumanEval_148_bf", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// There are eight planets in our solar system: the closerst to the Sun \n// is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n// Uranus, Neptune.\n// Write a function that takes two planet names as strings planet1 and planet2. \n// The function should return a tuple containing all planets whose orbits are \n// located between the orbit of planet1 and the orbit of planet2, sorted by \n// the proximity to the sun. \n// The function should return an empty tuple if planet1 or planet2\n// are not correct planet names. \n// Examples\n// >>> bf((\"Jupiter\"), (\"Neptune\"))\n// (std::vector<std::string>({(std::string)\"Saturn\", (std::string)\"Uranus\"}))\n// >>> bf((\"Earth\"), (\"Mercury\"))\n// (std::vector<std::string>(\"Venus\"))\n// >>> bf((\"Mercury\"), (\"Uranus\"))\n// (std::vector<std::string>({(std::string)\"Venus\", (std::string)\"Earth\", (std::string)\"Mars\", (std::string)\"Jupiter\", (std::string)\"Saturn\"}))\nstd::vector<std::string> bf(std::string planet1, std::string planet2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = bf;\n    assert(candidate((\"Jupiter\"), (\"Neptune\")) == (std::vector<std::string>({(std::string)\"Saturn\", (std::string)\"Uranus\"})));\n    assert(candidate((\"Earth\"), (\"Mercury\")) == (std::vector<std::string>({(std::string)\"Venus\"})));\n    assert(candidate((\"Mercury\"), (\"Uranus\")) == (std::vector<std::string>({(std::string)\"Venus\", (std::string)\"Earth\", (std::string)\"Mars\", (std::string)\"Jupiter\", (std::string)\"Saturn\"})));\n    assert(candidate((\"Neptune\"), (\"Venus\")) == (std::vector<std::string>({(std::string)\"Earth\", (std::string)\"Mars\", (std::string)\"Jupiter\", (std::string)\"Saturn\", (std::string)\"Uranus\"})));\n    assert(candidate((\"Earth\"), (\"Earth\")) == (std::vector<std::string>()));\n    assert(candidate((\"Mars\"), (\"Earth\")) == (std::vector<std::string>()));\n    assert(candidate((\"Jupiter\"), (\"Makemake\")) == (std::vector<std::string>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_148_bf", "test": "}\nint main() {\n    auto candidate = bf;\n    assert(candidate((\"Jupiter\"), (\"Neptune\")) == (std::vector<std::string>({(std::string)\"Saturn\", (std::string)\"Uranus\"})));\n    assert(candidate((\"Earth\"), (\"Mercury\")) == (std::vector<std::string>({(std::string)\"Venus\"})));\n    assert(candidate((\"Mercury\"), (\"Uranus\")) == (std::vector<std::string>({(std::string)\"Venus\", (std::string)\"Earth\", (std::string)\"Mars\", (std::string)\"Jupiter\", (std::string)\"Saturn\"})));\n    assert(candidate((\"Neptune\"), (\"Venus\")) == (std::vector<std::string>({(std::string)\"Earth\", (std::string)\"Mars\", (std::string)\"Jupiter\", (std::string)\"Saturn\", (std::string)\"Uranus\"})));\n    assert(candidate((\"Earth\"), (\"Earth\")) == (std::vector<std::string>()));\n    assert(candidate((\"Mars\"), (\"Earth\")) == (std::vector<std::string>()));\n    assert(candidate((\"Jupiter\"), (\"Makemake\")) == (std::vector<std::string>()));\n}\n"}
{"name": "HumanEval_131_digits", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits((1))\n// (1)\n// >>> digits((4))\n// (0)\n// >>> digits((235))\n// (15)\nlong digits(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = digits;\n    assert(candidate((5)) == (5));\n    assert(candidate((54)) == (5));\n    assert(candidate((120)) == (1));\n    assert(candidate((5014)) == (5));\n    assert(candidate((98765)) == (315));\n    assert(candidate((5576543)) == (2625));\n    assert(candidate((2468)) == (0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_131_digits", "test": "}\nint main() {\n    auto candidate = digits;\n    assert(candidate((5)) == (5));\n    assert(candidate((54)) == (5));\n    assert(candidate((120)) == (1));\n    assert(candidate((5014)) == (5));\n    assert(candidate((98765)) == (315));\n    assert(candidate((5576543)) == (2625));\n    assert(candidate((2468)) == (0));\n}\n"}
{"name": "HumanEval_101_words_string", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return a vector of the words.\n// For example:\n// >>> words_string((\"Hi, my name is John\"))\n// (std::vector<std::string>({(std::string)\"Hi\", (std::string)\"my\", (std::string)\"name\", (std::string)\"is\", (std::string)\"John\"}))\n// >>> words_string((\"One, two, three, four, five, six\"))\n// (std::vector<std::string>({(std::string)\"One\", (std::string)\"two\", (std::string)\"three\", (std::string)\"four\", (std::string)\"five\", (std::string)\"six\"}))\nstd::vector<std::string> words_string(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = words_string;\n    assert(candidate((\"Hi, my name is John\")) == (std::vector<std::string>({(std::string)\"Hi\", (std::string)\"my\", (std::string)\"name\", (std::string)\"is\", (std::string)\"John\"})));\n    assert(candidate((\"One, two, three, four, five, six\")) == (std::vector<std::string>({(std::string)\"One\", (std::string)\"two\", (std::string)\"three\", (std::string)\"four\", (std::string)\"five\", (std::string)\"six\"})));\n    assert(candidate((\"Hi, my name\")) == (std::vector<std::string>({(std::string)\"Hi\", (std::string)\"my\", (std::string)\"name\"})));\n    assert(candidate((\"One,, two, three, four, five, six,\")) == (std::vector<std::string>({(std::string)\"One\", (std::string)\"two\", (std::string)\"three\", (std::string)\"four\", (std::string)\"five\", (std::string)\"six\"})));\n    assert(candidate((\"\")) == (std::vector<std::string>()));\n    assert(candidate((\"ahmed     , gamal\")) == (std::vector<std::string>({(std::string)\"ahmed\", (std::string)\"gamal\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_101_words_string", "test": "}\nint main() {\n    auto candidate = words_string;\n    assert(candidate((\"Hi, my name is John\")) == (std::vector<std::string>({(std::string)\"Hi\", (std::string)\"my\", (std::string)\"name\", (std::string)\"is\", (std::string)\"John\"})));\n    assert(candidate((\"One, two, three, four, five, six\")) == (std::vector<std::string>({(std::string)\"One\", (std::string)\"two\", (std::string)\"three\", (std::string)\"four\", (std::string)\"five\", (std::string)\"six\"})));\n    assert(candidate((\"Hi, my name\")) == (std::vector<std::string>({(std::string)\"Hi\", (std::string)\"my\", (std::string)\"name\"})));\n    assert(candidate((\"One,, two, three, four, five, six,\")) == (std::vector<std::string>({(std::string)\"One\", (std::string)\"two\", (std::string)\"three\", (std::string)\"four\", (std::string)\"five\", (std::string)\"six\"})));\n    assert(candidate((\"\")) == (std::vector<std::string>()));\n    assert(candidate((\"ahmed     , gamal\")) == (std::vector<std::string>({(std::string)\"ahmed\", (std::string)\"gamal\"})));\n}\n"}
{"name": "HumanEval_18_how_many_times", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times((\"\"), (\"a\"))\n// (0)\n// >>> how_many_times((\"aaa\"), (\"a\"))\n// (3)\n// >>> how_many_times((\"aaaa\"), (\"aa\"))\n// (3)\nlong how_many_times(std::string string, std::string substring) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = how_many_times;\n    assert(candidate((\"\"), (\"x\")) == (0));\n    assert(candidate((\"xyxyxyx\"), (\"x\")) == (4));\n    assert(candidate((\"cacacacac\"), (\"cac\")) == (4));\n    assert(candidate((\"john doe\"), (\"john\")) == (1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_18_how_many_times", "test": "}\nint main() {\n    auto candidate = how_many_times;\n    assert(candidate((\"\"), (\"x\")) == (0));\n    assert(candidate((\"xyxyxyx\"), (\"x\")) == (4));\n    assert(candidate((\"cacacacac\"), (\"cac\")) == (4));\n    assert(candidate((\"john doe\"), (\"john\")) == (1));\n}\n"}
{"name": "HumanEval_137_compare_one", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\nunion Union_long_float_std_string{\n    long f0;\n    float f1;\n    std::string f2;    Union_long_float_std_string(long _f0) : f0(_f0) {}\n    Union_long_float_std_string(float _f1) : f1(_f1) {}\n    Union_long_float_std_string(std::string _f2) : f2(_f2) {}\n    ~Union_long_float_std_string() {}\n    bool operator==(long f) {\n        return f0 == f ;\n    }    bool operator==(float f) {\n        return f1 == f ;\n    }    bool operator==(std::string f) {\n        return f2 == f ;\n    }\n};\nunion Union_long_float_std_string_std_nullopt{\n    long f0;\n    float f1;\n    std::string f2;\n    std::nullopt f3;    Union_long_float_std_string_std_nullopt(long _f0) : f0(_f0) {}\n    Union_long_float_std_string_std_nullopt(float _f1) : f1(_f1) {}\n    Union_long_float_std_string_std_nullopt(std::string _f2) : f2(_f2) {}\n    Union_long_float_std_string_std_nullopt(std::nullopt _f3) : f3(_f3) {}\n    ~Union_long_float_std_string_std_nullopt() {}\n    bool operator==(long f) {\n        return f0 == f ;\n    }    bool operator==(float f) {\n        return f1 == f ;\n    }    bool operator==(std::string f) {\n        return f2 == f ;\n    }    bool operator==(std::nullopt f) {\n        return f3 == f ;\n    }\n};\n// Create a function that takes integers, floats, or strings representing\n// real numbers, and returns the larger variable in its given variable type.\n// Return None if the values are equal.\n// Note: If a real number is represented as a string, the floating point might be . or ,\n// >>> compare_one(1, 2.5f)\n// 2.5f\n// >>> compare_one(1, \"2,3\")\n// \"2,3\"\n// >>> compare_one(\"5,1\", \"6\")\n// \"6\"\n// >>> compare_one(\"1\", 1)\n// std::nullopt\nUnion_long_float_std_string_std_nullopt compare_one(Union_long_float_std_string a, Union_long_float_std_string b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_137_compare_one.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = compare_one;\n    assert(candidate(1, 2) == 2);\n    assert(candidate(1, 2.5f) == 2.5f);\n    assert(candidate(2, 3) == 3);\n    assert(candidate(5, 6) == 6);\n    assert(candidate(1, \"2,3\") == \"2,3\");\n    assert(candidate(\"5,1\", \"6\") == \"6\");\n    assert(candidate(\"1\", \"2\") == \"2\");\n    assert(candidate(\"1\", 1) == std::nullopt);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_137_compare_one", "test": "}\nint main() {\n    auto candidate = compare_one;\n    assert(candidate(1, 2) == 2);\n    assert(candidate(1, 2.5f) == 2.5f);\n    assert(candidate(2, 3) == 3);\n    assert(candidate(5, 6) == 6);\n    assert(candidate(1, \"2,3\") == \"2,3\");\n    assert(candidate(\"5,1\", \"6\") == \"6\");\n    assert(candidate(\"1\", \"2\") == \"2\");\n    assert(candidate(\"1\", 1) == std::nullopt);\n}\n"}
{"name": "HumanEval_51_remove_vowels", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels((\"\"))\n// (\"\")\n// >>> remove_vowels((\"abcdef\"))\n// (\"bcdf\")\n// >>> remove_vowels((\"aaaaa\"))\n// (\"\")\n// >>> remove_vowels((\"aaBAA\"))\n// (\"B\")\n// >>> remove_vowels((\"zbcd\"))\n// (\"zbcd\")\nstd::string remove_vowels(std::string text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = remove_vowels;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"abcdef\\nghijklm\")) == (\"bcdf\\nghjklm\"));\n    assert(candidate((\"fedcba\")) == (\"fdcb\"));\n    assert(candidate((\"eeeee\")) == (\"\"));\n    assert(candidate((\"acBAA\")) == (\"cB\"));\n    assert(candidate((\"EcBOO\")) == (\"cB\"));\n    assert(candidate((\"ybcd\")) == (\"ybcd\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_51_remove_vowels", "test": "}\nint main() {\n    auto candidate = remove_vowels;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"abcdef\\nghijklm\")) == (\"bcdf\\nghjklm\"));\n    assert(candidate((\"fedcba\")) == (\"fdcb\"));\n    assert(candidate((\"eeeee\")) == (\"\"));\n    assert(candidate((\"acBAA\")) == (\"cB\"));\n    assert(candidate((\"EcBOO\")) == (\"cB\"));\n    assert(candidate((\"ybcd\")) == (\"ybcd\"));\n}\n"}
{"name": "HumanEval_70_strange_sort_list", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given vector of integers, return vector in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list((std::vector<long>({(long)1, (long)2, (long)3, (long)4})))\n// (std::vector<long>({(long)1, (long)4, (long)2, (long)3}))\n// >>> strange_sort_list((std::vector<long>({(long)5, (long)5, (long)5, (long)5})))\n// (std::vector<long>({(long)5, (long)5, (long)5, (long)5}))\n// >>> strange_sort_list((std::vector<long>()))\n// (std::vector<long>())\nstd::vector<long> strange_sort_list(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = strange_sort_list;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)4, (long)2, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)7, (long)8, (long)9}))) == (std::vector<long>({(long)5, (long)9, (long)6, (long)8, (long)7})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))) == (std::vector<long>({(long)1, (long)5, (long)2, (long)4, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)7, (long)8, (long)9, (long)1}))) == (std::vector<long>({(long)1, (long)9, (long)5, (long)8, (long)6, (long)7})));\n    assert(candidate((std::vector<long>({(long)5, (long)5, (long)5, (long)5}))) == (std::vector<long>({(long)5, (long)5, (long)5, (long)5})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7, (long)8}))) == (std::vector<long>({(long)1, (long)8, (long)2, (long)7, (long)3, (long)6, (long)4, (long)5})));\n    assert(candidate((std::vector<long>({(long)0, (long)2, (long)2, (long)2, (long)5, (long)5, (long)-5, (long)-5}))) == (std::vector<long>({(long)-5, (long)5, (long)-5, (long)5, (long)0, (long)2, (long)2, (long)2})));\n    assert(candidate((std::vector<long>({(long)111111}))) == (std::vector<long>({(long)111111})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_70_strange_sort_list", "test": "}\nint main() {\n    auto candidate = strange_sort_list;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)4, (long)2, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)7, (long)8, (long)9}))) == (std::vector<long>({(long)5, (long)9, (long)6, (long)8, (long)7})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))) == (std::vector<long>({(long)1, (long)5, (long)2, (long)4, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)7, (long)8, (long)9, (long)1}))) == (std::vector<long>({(long)1, (long)9, (long)5, (long)8, (long)6, (long)7})));\n    assert(candidate((std::vector<long>({(long)5, (long)5, (long)5, (long)5}))) == (std::vector<long>({(long)5, (long)5, (long)5, (long)5})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7, (long)8}))) == (std::vector<long>({(long)1, (long)8, (long)2, (long)7, (long)3, (long)6, (long)4, (long)5})));\n    assert(candidate((std::vector<long>({(long)0, (long)2, (long)2, (long)2, (long)5, (long)5, (long)-5, (long)-5}))) == (std::vector<long>({(long)-5, (long)5, (long)-5, (long)5, (long)0, (long)2, (long)2, (long)2})));\n    assert(candidate((std::vector<long>({(long)111111}))) == (std::vector<long>({(long)111111})));\n}\n"}
{"name": "HumanEval_20_find_closest_elements", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// From a supplied vector of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.2f})))\n// (std::make_tuple(2.0f, 2.2f))\n// >>> find_closest_elements((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f})))\n// (std::make_tuple(2.0f, 2.0f))\nstd::tuple<float, float> find_closest_elements(std::vector<float> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = find_closest_elements;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f}))) == (std::make_tuple(3.9f, 4.0f)));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f}))) == (std::make_tuple(5.0f, 5.9f)));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.2f}))) == (std::make_tuple(2.0f, 2.2f)));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f}))) == (std::make_tuple(2.0f, 2.0f)));\n    assert(candidate((std::vector<float>({(float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f}))) == (std::make_tuple(2.2f, 3.1f)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_20_find_closest_elements", "test": "}\nint main() {\n    auto candidate = find_closest_elements;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f}))) == (std::make_tuple(3.9f, 4.0f)));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f}))) == (std::make_tuple(5.0f, 5.9f)));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.2f}))) == (std::make_tuple(2.0f, 2.2f)));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f}))) == (std::make_tuple(2.0f, 2.0f)));\n    assert(candidate((std::vector<float>({(float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f}))) == (std::make_tuple(2.2f, 3.1f)));\n}\n"}
{"name": "HumanEval_76_is_simple_power", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power((1), (4))\n// (true)\n// >>> is_simple_power((2), (2))\n// (true)\n// >>> is_simple_power((8), (2))\n// (true)\n// >>> is_simple_power((3), (2))\n// (false)\n// >>> is_simple_power((3), (1))\n// (false)\n// >>> is_simple_power((5), (3))\n// (false)\nbool is_simple_power(long x, long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_simple_power;\n    assert(candidate((16), (2)) == (true));\n    assert(candidate((143214), (16)) == (false));\n    assert(candidate((4), (2)) == (true));\n    assert(candidate((9), (3)) == (true));\n    assert(candidate((16), (4)) == (true));\n    assert(candidate((24), (2)) == (false));\n    assert(candidate((128), (4)) == (false));\n    assert(candidate((12), (6)) == (false));\n    assert(candidate((1), (1)) == (true));\n    assert(candidate((1), (12)) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_76_is_simple_power", "test": "}\nint main() {\n    auto candidate = is_simple_power;\n    assert(candidate((16), (2)) == (true));\n    assert(candidate((143214), (16)) == (false));\n    assert(candidate((4), (2)) == (true));\n    assert(candidate((9), (3)) == (true));\n    assert(candidate((16), (4)) == (true));\n    assert(candidate((24), (2)) == (false));\n    assert(candidate((128), (4)) == (false));\n    assert(candidate((12), (6)) == (false));\n    assert(candidate((1), (1)) == (true));\n    assert(candidate((1), (12)) == (true));\n}\n"}
{"name": "HumanEval_39_prime_fib", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib((1))\n// (2)\n// >>> prime_fib((2))\n// (3)\n// >>> prime_fib((3))\n// (5)\n// >>> prime_fib((4))\n// (13)\n// >>> prime_fib((5))\n// (89)\nlong prime_fib(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = prime_fib;\n    assert(candidate((1)) == (2));\n    assert(candidate((2)) == (3));\n    assert(candidate((3)) == (5));\n    assert(candidate((4)) == (13));\n    assert(candidate((5)) == (89));\n    assert(candidate((6)) == (233));\n    assert(candidate((7)) == (1597));\n    assert(candidate((8)) == (28657));\n    assert(candidate((9)) == (514229));\n    assert(candidate((10)) == (433494437));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_39_prime_fib", "test": "}\nint main() {\n    auto candidate = prime_fib;\n    assert(candidate((1)) == (2));\n    assert(candidate((2)) == (3));\n    assert(candidate((3)) == (5));\n    assert(candidate((4)) == (13));\n    assert(candidate((5)) == (89));\n    assert(candidate((6)) == (233));\n    assert(candidate((7)) == (1597));\n    assert(candidate((8)) == (28657));\n    assert(candidate((9)) == (514229));\n    assert(candidate((10)) == (433494437));\n}\n"}
{"name": "HumanEval_145_order_by_points", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function which sorts the given vector of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original vector.\n// For example:\n// >>> order_by_points((std::vector<long>({(long)1, (long)11, (long)-1, (long)-11, (long)-12})))\n// (std::vector<long>({(long)-1, (long)-11, (long)1, (long)-12, (long)11}))\n// >>> order_by_points((std::vector<long>()))\n// (std::vector<long>())\nstd::vector<long> order_by_points(std::vector<long> nums) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = order_by_points;\n    assert(candidate((std::vector<long>({(long)1, (long)11, (long)-1, (long)-11, (long)-12}))) == (std::vector<long>({(long)-1, (long)-11, (long)1, (long)-12, (long)11})));\n    assert(candidate((std::vector<long>({(long)1234, (long)423, (long)463, (long)145, (long)2, (long)423, (long)423, (long)53, (long)6, (long)37, (long)3457, (long)3, (long)56, (long)0, (long)46}))) == (std::vector<long>({(long)0, (long)2, (long)3, (long)6, (long)53, (long)423, (long)423, (long)423, (long)1234, (long)145, (long)37, (long)46, (long)56, (long)463, (long)3457})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)-11, (long)-32, (long)43, (long)54, (long)-98, (long)2, (long)-3}))) == (std::vector<long>({(long)-3, (long)-32, (long)-98, (long)-11, (long)1, (long)2, (long)43, (long)54})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7, (long)8, (long)9, (long)10, (long)11}))) == (std::vector<long>({(long)1, (long)10, (long)2, (long)11, (long)3, (long)4, (long)5, (long)6, (long)7, (long)8, (long)9})));\n    assert(candidate((std::vector<long>({(long)0, (long)6, (long)6, (long)-76, (long)-21, (long)23, (long)4}))) == (std::vector<long>({(long)-76, (long)-21, (long)0, (long)4, (long)23, (long)6, (long)6})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_145_order_by_points", "test": "}\nint main() {\n    auto candidate = order_by_points;\n    assert(candidate((std::vector<long>({(long)1, (long)11, (long)-1, (long)-11, (long)-12}))) == (std::vector<long>({(long)-1, (long)-11, (long)1, (long)-12, (long)11})));\n    assert(candidate((std::vector<long>({(long)1234, (long)423, (long)463, (long)145, (long)2, (long)423, (long)423, (long)53, (long)6, (long)37, (long)3457, (long)3, (long)56, (long)0, (long)46}))) == (std::vector<long>({(long)0, (long)2, (long)3, (long)6, (long)53, (long)423, (long)423, (long)423, (long)1234, (long)145, (long)37, (long)46, (long)56, (long)463, (long)3457})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)-11, (long)-32, (long)43, (long)54, (long)-98, (long)2, (long)-3}))) == (std::vector<long>({(long)-3, (long)-32, (long)-98, (long)-11, (long)1, (long)2, (long)43, (long)54})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6, (long)7, (long)8, (long)9, (long)10, (long)11}))) == (std::vector<long>({(long)1, (long)10, (long)2, (long)11, (long)3, (long)4, (long)5, (long)6, (long)7, (long)8, (long)9})));\n    assert(candidate((std::vector<long>({(long)0, (long)6, (long)6, (long)-76, (long)-21, (long)23, (long)4}))) == (std::vector<long>({(long)-76, (long)-21, (long)0, (long)4, (long)23, (long)6, (long)6})));\n}\n"}
{"name": "HumanEval_0_has_close_elements", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Check if in given vector of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f})), (0.5f))\n// (false)\n// >>> has_close_elements((std::vector<float>({(float)1.0f, (float)2.8f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f})), (0.3f))\n// (true)\nbool has_close_elements(std::vector<float> numbers, float threshold) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = has_close_elements;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f})), (0.3f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f})), (0.05f)) == (false));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f})), (0.95f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f})), (0.8f)) == (false));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f})), (0.1f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f})), (1.0f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f})), (0.5f)) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_0_has_close_elements", "test": "}\nint main() {\n    auto candidate = has_close_elements;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f})), (0.3f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f})), (0.05f)) == (false));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f})), (0.95f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f})), (0.8f)) == (false));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f})), (0.1f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f})), (1.0f)) == (true));\n    assert(candidate((std::vector<float>({(float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f})), (0.5f)) == (false));\n}\n"}
{"name": "HumanEval_10_make_palindrome", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome((\"\"))\n// (\"\")\n// >>> make_palindrome((\"cat\"))\n// (\"catac\")\n// >>> make_palindrome((\"cata\"))\n// (\"catac\")\nstd::string make_palindrome(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = make_palindrome;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"x\")) == (\"x\"));\n    assert(candidate((\"xyz\")) == (\"xyzyx\"));\n    assert(candidate((\"xyx\")) == (\"xyx\"));\n    assert(candidate((\"jerry\")) == (\"jerryrrej\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_10_make_palindrome", "test": "}\nint main() {\n    auto candidate = make_palindrome;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"x\")) == (\"x\"));\n    assert(candidate((\"xyz\")) == (\"xyzyx\"));\n    assert(candidate((\"xyx\")) == (\"xyx\"));\n    assert(candidate((\"jerry\")) == (\"jerryrrej\"));\n}\n"}
{"name": "HumanEval_11_string_xor", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor((\"010\"), (\"110\"))\n// (\"100\")\nstd::string string_xor(std::string a, std::string b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = string_xor;\n    assert(candidate((\"111000\"), (\"101010\")) == (\"010010\"));\n    assert(candidate((\"1\"), (\"1\")) == (\"0\"));\n    assert(candidate((\"0101\"), (\"0000\")) == (\"0101\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_11_string_xor", "test": "}\nint main() {\n    auto candidate = string_xor;\n    assert(candidate((\"111000\"), (\"101010\")) == (\"010010\"));\n    assert(candidate((\"1\"), (\"1\")) == (\"0\"));\n    assert(candidate((\"0101\"), (\"0000\")) == (\"0101\"));\n}\n"}
{"name": "HumanEval_139_special_factorial", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial((4))\n// (288)\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nlong special_factorial(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = special_factorial;\n    assert(candidate((4)) == (288));\n    assert(candidate((5)) == (34560));\n    assert(candidate((7)) == (125411328000));\n    assert(candidate((1)) == (1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_139_special_factorial", "test": "}\nint main() {\n    auto candidate = special_factorial;\n    assert(candidate((4)) == (288));\n    assert(candidate((5)) == (34560));\n    assert(candidate((7)) == (125411328000));\n    assert(candidate((1)) == (1));\n}\n"}
{"name": "HumanEval_122_add_elements", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a non-empty vector of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements((std::vector<long>({(long)111, (long)21, (long)3, (long)4000, (long)5, (long)6, (long)7, (long)8, (long)9})), (4))\n// (24)\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nlong add_elements(std::vector<long> arr, long k) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = add_elements;\n    assert(candidate((std::vector<long>({(long)1, (long)-2, (long)-3, (long)41, (long)57, (long)76, (long)87, (long)88, (long)99})), (3)) == (-4));\n    assert(candidate((std::vector<long>({(long)111, (long)121, (long)3, (long)4000, (long)5, (long)6})), (2)) == (0));\n    assert(candidate((std::vector<long>({(long)11, (long)21, (long)3, (long)90, (long)5, (long)6, (long)7, (long)8, (long)9})), (4)) == (125));\n    assert(candidate((std::vector<long>({(long)111, (long)21, (long)3, (long)4000, (long)5, (long)6, (long)7, (long)8, (long)9})), (4)) == (24));\n    assert(candidate((std::vector<long>({(long)1})), (1)) == (1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_122_add_elements", "test": "}\nint main() {\n    auto candidate = add_elements;\n    assert(candidate((std::vector<long>({(long)1, (long)-2, (long)-3, (long)41, (long)57, (long)76, (long)87, (long)88, (long)99})), (3)) == (-4));\n    assert(candidate((std::vector<long>({(long)111, (long)121, (long)3, (long)4000, (long)5, (long)6})), (2)) == (0));\n    assert(candidate((std::vector<long>({(long)11, (long)21, (long)3, (long)90, (long)5, (long)6, (long)7, (long)8, (long)9})), (4)) == (125));\n    assert(candidate((std::vector<long>({(long)111, (long)21, (long)3, (long)4000, (long)5, (long)6, (long)7, (long)8, (long)9})), (4)) == (24));\n    assert(candidate((std::vector<long>({(long)1})), (1)) == (1));\n}\n"}
{"name": "HumanEval_46_fib4", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4((5))\n// (4)\n// >>> fib4((6))\n// (8)\n// >>> fib4((7))\n// (14)\nlong fib4(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = fib4;\n    assert(candidate((5)) == (4));\n    assert(candidate((8)) == (28));\n    assert(candidate((10)) == (104));\n    assert(candidate((12)) == (386));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_46_fib4", "test": "}\nint main() {\n    auto candidate = fib4;\n    assert(candidate((5)) == (4));\n    assert(candidate((8)) == (28));\n    assert(candidate((10)) == (104));\n    assert(candidate((12)) == (386));\n}\n"}
{"name": "HumanEval_104_unique_digits", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of positive integers x. return a sorted vector of all \n// elements that hasn't any even digit.\n// Note: Returned vector should be sorted in increasing order.\n// For example:\n// >>> unique_digits((std::vector<long>({(long)15, (long)33, (long)1422, (long)1})))\n// (std::vector<long>({(long)1, (long)15, (long)33}))\n// >>> unique_digits((std::vector<long>({(long)152, (long)323, (long)1422, (long)10})))\n// (std::vector<long>())\nstd::vector<long> unique_digits(std::vector<long> x) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = unique_digits;\n    assert(candidate((std::vector<long>({(long)15, (long)33, (long)1422, (long)1}))) == (std::vector<long>({(long)1, (long)15, (long)33})));\n    assert(candidate((std::vector<long>({(long)152, (long)323, (long)1422, (long)10}))) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)12345, (long)2033, (long)111, (long)151}))) == (std::vector<long>({(long)111, (long)151})));\n    assert(candidate((std::vector<long>({(long)135, (long)103, (long)31}))) == (std::vector<long>({(long)31, (long)135})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_104_unique_digits", "test": "}\nint main() {\n    auto candidate = unique_digits;\n    assert(candidate((std::vector<long>({(long)15, (long)33, (long)1422, (long)1}))) == (std::vector<long>({(long)1, (long)15, (long)33})));\n    assert(candidate((std::vector<long>({(long)152, (long)323, (long)1422, (long)10}))) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)12345, (long)2033, (long)111, (long)151}))) == (std::vector<long>({(long)111, (long)151})));\n    assert(candidate((std::vector<long>({(long)135, (long)103, (long)31}))) == (std::vector<long>({(long)31, (long)135})));\n}\n"}
{"name": "HumanEval_117_select_words", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a string s and a natural number n, you have been tasked to implement \n// a function that returns a vector of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty vector.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words((\"Mary had a little lamb\"), (4))\n// (std::vector<std::string>({(std::string)\"little\"}))\n// >>> select_words((\"Mary had a little lamb\"), (3))\n// (std::vector<std::string>({(std::string)\"Mary\", (std::string)\"lamb\"}))\n// >>> select_words((\"simple white space\"), (2))\n// (std::vector<std::string>())\n// >>> select_words((\"Hello world\"), (4))\n// (std::vector<std::string>({(std::string)\"world\"}))\n// >>> select_words((\"Uncle sam\"), (3))\n// (std::vector<std::string>({(std::string)\"Uncle\"}))\nstd::vector<std::string> select_words(std::string s, long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = select_words;\n    assert(candidate((\"Mary had a little lamb\"), (4)) == (std::vector<std::string>({(std::string)\"little\"})));\n    assert(candidate((\"Mary had a little lamb\"), (3)) == (std::vector<std::string>({(std::string)\"Mary\", (std::string)\"lamb\"})));\n    assert(candidate((\"simple white space\"), (2)) == (std::vector<std::string>()));\n    assert(candidate((\"Hello world\"), (4)) == (std::vector<std::string>({(std::string)\"world\"})));\n    assert(candidate((\"Uncle sam\"), (3)) == (std::vector<std::string>({(std::string)\"Uncle\"})));\n    assert(candidate((\"\"), (4)) == (std::vector<std::string>()));\n    assert(candidate((\"a b c d e f\"), (1)) == (std::vector<std::string>({(std::string)\"b\", (std::string)\"c\", (std::string)\"d\", (std::string)\"f\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_117_select_words", "test": "}\nint main() {\n    auto candidate = select_words;\n    assert(candidate((\"Mary had a little lamb\"), (4)) == (std::vector<std::string>({(std::string)\"little\"})));\n    assert(candidate((\"Mary had a little lamb\"), (3)) == (std::vector<std::string>({(std::string)\"Mary\", (std::string)\"lamb\"})));\n    assert(candidate((\"simple white space\"), (2)) == (std::vector<std::string>()));\n    assert(candidate((\"Hello world\"), (4)) == (std::vector<std::string>({(std::string)\"world\"})));\n    assert(candidate((\"Uncle sam\"), (3)) == (std::vector<std::string>({(std::string)\"Uncle\"})));\n    assert(candidate((\"\"), (4)) == (std::vector<std::string>()));\n    assert(candidate((\"a b c d e f\"), (1)) == (std::vector<std::string>({(std::string)\"b\", (std::string)\"c\", (std::string)\"d\", (std::string)\"f\"})));\n}\n"}
{"name": "HumanEval_72_will_it_fly", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic vector) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly((std::vector<long>({(long)1, (long)2})), (5))\n// (false)\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly((std::vector<long>({(long)3, (long)2, (long)3})), (1))\n// (false)\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly((std::vector<long>({(long)3, (long)2, (long)3})), (9))\n// (true)\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly((std::vector<long>({(long)3})), (5))\n// (true)\n// # 3 is less than the maximum possible weight, and it's balanced.\nbool will_it_fly(std::vector<long> q, long w) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = will_it_fly;\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)3})), (9)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2})), (5)) == (false));\n    assert(candidate((std::vector<long>({(long)3})), (5)) == (true));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)3})), (1)) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3})), (6)) == (false));\n    assert(candidate((std::vector<long>({(long)5})), (5)) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_72_will_it_fly", "test": "}\nint main() {\n    auto candidate = will_it_fly;\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)3})), (9)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2})), (5)) == (false));\n    assert(candidate((std::vector<long>({(long)3})), (5)) == (true));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)3})), (1)) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3})), (6)) == (false));\n    assert(candidate((std::vector<long>({(long)5})), (5)) == (true));\n}\n"}
{"name": "HumanEval_55_fib", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return n-th Fibonacci number.\n// >>> fib((10))\n// (55)\n// >>> fib((1))\n// (1)\n// >>> fib((8))\n// (21)\nlong fib(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = fib;\n    assert(candidate((10)) == (55));\n    assert(candidate((1)) == (1));\n    assert(candidate((8)) == (21));\n    assert(candidate((11)) == (89));\n    assert(candidate((12)) == (144));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_55_fib", "test": "}\nint main() {\n    auto candidate = fib;\n    assert(candidate((10)) == (55));\n    assert(candidate((1)) == (1));\n    assert(candidate((8)) == (21));\n    assert(candidate((11)) == (89));\n    assert(candidate((12)) == (144));\n}\n"}
{"name": "HumanEval_153_Strongest_Extension", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You will be given the name of a class (a string) and a vector of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the vector.\n// For example, if you are given \"Slices\" as the class and a vector of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension((\"my_class\"), (std::vector<std::string>({(std::string)\"AA\", (std::string)\"Be\", (std::string)\"CC\"})))\n// (\"my_class.AA\")\nstd::string Strongest_Extension(std::string class_name, std::vector<std::string> extensions) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = Strongest_Extension;\n    assert(candidate((\"Watashi\"), (std::vector<std::string>({(std::string)\"tEN\", (std::string)\"niNE\", (std::string)\"eIGHt8OKe\"}))) == (\"Watashi.eIGHt8OKe\"));\n    assert(candidate((\"Boku123\"), (std::vector<std::string>({(std::string)\"nani\", (std::string)\"NazeDa\", (std::string)\"YEs.WeCaNe\", (std::string)\"32145tggg\"}))) == (\"Boku123.YEs.WeCaNe\"));\n    assert(candidate((\"__YESIMHERE\"), (std::vector<std::string>({(std::string)\"t\", (std::string)\"eMptY\", (std::string)\"nothing\", (std::string)\"zeR00\", (std::string)\"NuLl__\", (std::string)\"123NoooneB321\"}))) == (\"__YESIMHERE.NuLl__\"));\n    assert(candidate((\"K\"), (std::vector<std::string>({(std::string)\"Ta\", (std::string)\"TAR\", (std::string)\"t234An\", (std::string)\"cosSo\"}))) == (\"K.TAR\"));\n    assert(candidate((\"__HAHA\"), (std::vector<std::string>({(std::string)\"Tab\", (std::string)\"123\", (std::string)\"781345\", (std::string)\"-_-\"}))) == (\"__HAHA.123\"));\n    assert(candidate((\"YameRore\"), (std::vector<std::string>({(std::string)\"HhAas\", (std::string)\"okIWILL123\", (std::string)\"WorkOut\", (std::string)\"Fails\", (std::string)\"-_-\"}))) == (\"YameRore.okIWILL123\"));\n    assert(candidate((\"finNNalLLly\"), (std::vector<std::string>({(std::string)\"Die\", (std::string)\"NowW\", (std::string)\"Wow\", (std::string)\"WoW\"}))) == (\"finNNalLLly.WoW\"));\n    assert(candidate((\"_\"), (std::vector<std::string>({(std::string)\"Bb\", (std::string)\"91245\"}))) == (\"_.Bb\"));\n    assert(candidate((\"Sp\"), (std::vector<std::string>({(std::string)\"671235\", (std::string)\"Bb\"}))) == (\"Sp.671235\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_153_Strongest_Extension", "test": "}\nint main() {\n    auto candidate = Strongest_Extension;\n    assert(candidate((\"Watashi\"), (std::vector<std::string>({(std::string)\"tEN\", (std::string)\"niNE\", (std::string)\"eIGHt8OKe\"}))) == (\"Watashi.eIGHt8OKe\"));\n    assert(candidate((\"Boku123\"), (std::vector<std::string>({(std::string)\"nani\", (std::string)\"NazeDa\", (std::string)\"YEs.WeCaNe\", (std::string)\"32145tggg\"}))) == (\"Boku123.YEs.WeCaNe\"));\n    assert(candidate((\"__YESIMHERE\"), (std::vector<std::string>({(std::string)\"t\", (std::string)\"eMptY\", (std::string)\"nothing\", (std::string)\"zeR00\", (std::string)\"NuLl__\", (std::string)\"123NoooneB321\"}))) == (\"__YESIMHERE.NuLl__\"));\n    assert(candidate((\"K\"), (std::vector<std::string>({(std::string)\"Ta\", (std::string)\"TAR\", (std::string)\"t234An\", (std::string)\"cosSo\"}))) == (\"K.TAR\"));\n    assert(candidate((\"__HAHA\"), (std::vector<std::string>({(std::string)\"Tab\", (std::string)\"123\", (std::string)\"781345\", (std::string)\"-_-\"}))) == (\"__HAHA.123\"));\n    assert(candidate((\"YameRore\"), (std::vector<std::string>({(std::string)\"HhAas\", (std::string)\"okIWILL123\", (std::string)\"WorkOut\", (std::string)\"Fails\", (std::string)\"-_-\"}))) == (\"YameRore.okIWILL123\"));\n    assert(candidate((\"finNNalLLly\"), (std::vector<std::string>({(std::string)\"Die\", (std::string)\"NowW\", (std::string)\"Wow\", (std::string)\"WoW\"}))) == (\"finNNalLLly.WoW\"));\n    assert(candidate((\"_\"), (std::vector<std::string>({(std::string)\"Bb\", (std::string)\"91245\"}))) == (\"_.Bb\"));\n    assert(candidate((\"Sp\"), (std::vector<std::string>({(std::string)\"671235\", (std::string)\"Bb\"}))) == (\"Sp.671235\"));\n}\n"}
{"name": "HumanEval_119_match_parens", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a vector of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens((std::vector<std::string>({(std::string)\"()(\", (std::string)\")\"})))\n// (\"Yes\")\n// >>> match_parens((std::vector<std::string>({(std::string)\")\", (std::string)\")\"})))\n// (\"No\")\nstd::string match_parens(std::vector<std::string> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = match_parens;\n    assert(candidate((std::vector<std::string>({(std::string)\"()(\", (std::string)\")\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")\", (std::string)\")\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(()(())\", (std::string)\"())())\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")())\", (std::string)\"(()()(\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(())))\", (std::string)\"(()())((\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"()\", (std::string)\"())\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(()(\", (std::string)\"()))()\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"((((\", (std::string)\"((())\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")(()\", (std::string)\"(()(\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")(\", (std::string)\")(\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(\", (std::string)\")\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")\", (std::string)\"(\"}))) == (\"Yes\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_119_match_parens", "test": "}\nint main() {\n    auto candidate = match_parens;\n    assert(candidate((std::vector<std::string>({(std::string)\"()(\", (std::string)\")\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")\", (std::string)\")\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(()(())\", (std::string)\"())())\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")())\", (std::string)\"(()()(\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(())))\", (std::string)\"(()())((\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"()\", (std::string)\"())\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(()(\", (std::string)\"()))()\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"((((\", (std::string)\"((())\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")(()\", (std::string)\"(()(\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")(\", (std::string)\")(\"}))) == (\"No\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"(\", (std::string)\")\"}))) == (\"Yes\"));\n    assert(candidate((std::vector<std::string>({(std::string)\")\", (std::string)\"(\"}))) == (\"Yes\"));\n}\n"}
{"name": "HumanEval_90_next_smallest", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a vector of integers.\n// Write a function next_smallest() that returns the 2nd smallest element of the vector.\n// Return None if there is no such element.\n// >>> next_smallest((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5})))\n// 2\n// >>> next_smallest((std::vector<long>({(long)5, (long)1, (long)4, (long)3, (long)2})))\n// 2\n// >>> next_smallest((std::vector<long>()))\n// std::nullopt\n// >>> next_smallest((std::vector<long>({(long)1, (long)1})))\n// std::nullopt\nstd::optional<long> next_smallest(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = next_smallest;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))) == 2);\n    assert(candidate((std::vector<long>({(long)5, (long)1, (long)4, (long)3, (long)2}))) == 2);\n    assert(candidate((std::vector<long>())) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)1, (long)1}))) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1, (long)1, (long)0}))) == 1);\n    assert(candidate((std::vector<long>({(long)1, (long)1}))) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)-35, (long)34, (long)12, (long)-45}))) == -35);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_90_next_smallest", "test": "}\nint main() {\n    auto candidate = next_smallest;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))) == 2);\n    assert(candidate((std::vector<long>({(long)5, (long)1, (long)4, (long)3, (long)2}))) == 2);\n    assert(candidate((std::vector<long>())) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)1, (long)1}))) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1, (long)1, (long)0}))) == 1);\n    assert(candidate((std::vector<long>({(long)1, (long)1}))) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)-35, (long)34, (long)12, (long)-45}))) == -35);\n}\n"}
{"name": "HumanEval_92_any_int", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int((float(5)), (float(2)), (float(7)))\n// (true)\n// >>> any_int((float(3)), (float(2)), (float(2)))\n// (false)\n// >>> any_int((float(3)), (float(-2)), (float(1)))\n// (true)\n// >>> any_int((3.6f), (-2.2f), (float(2)))\n// (false)\nbool any_int(float x, float y, float z) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = any_int;\n    assert(candidate((float(2)), (float(3)), (float(1))) == (true));\n    assert(candidate((2.5f), (float(2)), (float(3))) == (false));\n    assert(candidate((1.5f), (float(5)), (3.5f)) == (false));\n    assert(candidate((float(2)), (float(6)), (float(2))) == (false));\n    assert(candidate((float(4)), (float(2)), (float(2))) == (true));\n    assert(candidate((2.2f), (2.2f), (2.2f)) == (false));\n    assert(candidate((float(-4)), (float(6)), (float(2))) == (true));\n    assert(candidate((float(2)), (float(1)), (float(1))) == (true));\n    assert(candidate((float(3)), (float(4)), (float(7))) == (true));\n    assert(candidate((3.0f), (float(4)), (float(7))) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_92_any_int", "test": "}\nint main() {\n    auto candidate = any_int;\n    assert(candidate((float(2)), (float(3)), (float(1))) == (true));\n    assert(candidate((2.5f), (float(2)), (float(3))) == (false));\n    assert(candidate((1.5f), (float(5)), (3.5f)) == (false));\n    assert(candidate((float(2)), (float(6)), (float(2))) == (false));\n    assert(candidate((float(4)), (float(2)), (float(2))) == (true));\n    assert(candidate((2.2f), (2.2f), (2.2f)) == (false));\n    assert(candidate((float(-4)), (float(6)), (float(2))) == (true));\n    assert(candidate((float(2)), (float(1)), (float(1))) == (true));\n    assert(candidate((float(3)), (float(4)), (float(7))) == (true));\n    assert(candidate((3.0f), (float(4)), (float(7))) == (false));\n}\n"}
{"name": "HumanEval_2_truncate_number", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number((3.5f))\n// (0.5f)\nfloat truncate_number(float number) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = truncate_number;\n    assert(candidate((3.5f)) == (0.5f));\n    assert(candidate((1.25f)) == (0.25f));\n    assert(candidate((123.0f)) == (0.0f));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_2_truncate_number", "test": "}\nint main() {\n    auto candidate = truncate_number;\n    assert(candidate((3.5f)) == (0.5f));\n    assert(candidate((1.25f)) == (0.25f));\n    assert(candidate((123.0f)) == (0.0f));\n}\n"}
{"name": "HumanEval_42_incr_list", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return vector with elements incremented by 1.\n// >>> incr_list((std::vector<long>({(long)1, (long)2, (long)3})))\n// (std::vector<long>({(long)2, (long)3, (long)4}))\n// >>> incr_list((std::vector<long>({(long)5, (long)3, (long)5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123})))\n// (std::vector<long>({(long)6, (long)4, (long)6, (long)3, (long)4, (long)4, (long)10, (long)1, (long)124}))\nstd::vector<long> incr_list(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = incr_list;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1}))) == (std::vector<long>({(long)4, (long)3, (long)2})));\n    assert(candidate((std::vector<long>({(long)5, (long)2, (long)5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123}))) == (std::vector<long>({(long)6, (long)3, (long)6, (long)3, (long)4, (long)4, (long)10, (long)1, (long)124})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_42_incr_list", "test": "}\nint main() {\n    auto candidate = incr_list;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)1}))) == (std::vector<long>({(long)4, (long)3, (long)2})));\n    assert(candidate((std::vector<long>({(long)5, (long)2, (long)5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123}))) == (std::vector<long>({(long)6, (long)3, (long)6, (long)3, (long)4, (long)4, (long)10, (long)1, (long)124})));\n}\n"}
{"name": "HumanEval_150_x_or_y", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y((7), (34), (12))\n// (34)\n// >>> x_or_y((15), (8), (5))\n// (5)\nlong x_or_y(long n, long x, long y) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = x_or_y;\n    assert(candidate((7), (34), (12)) == (34));\n    assert(candidate((15), (8), (5)) == (5));\n    assert(candidate((3), (33), (5212)) == (33));\n    assert(candidate((1259), (3), (52)) == (3));\n    assert(candidate((7919), (-1), (12)) == (-1));\n    assert(candidate((3609), (1245), (583)) == (583));\n    assert(candidate((91), (56), (129)) == (129));\n    assert(candidate((6), (34), (1234)) == (1234));\n    assert(candidate((1), (2), (0)) == (0));\n    assert(candidate((2), (2), (0)) == (2));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_150_x_or_y", "test": "}\nint main() {\n    auto candidate = x_or_y;\n    assert(candidate((7), (34), (12)) == (34));\n    assert(candidate((15), (8), (5)) == (5));\n    assert(candidate((3), (33), (5212)) == (33));\n    assert(candidate((1259), (3), (52)) == (3));\n    assert(candidate((7919), (-1), (12)) == (-1));\n    assert(candidate((3609), (1245), (583)) == (583));\n    assert(candidate((91), (56), (129)) == (129));\n    assert(candidate((6), (34), (1234)) == (1234));\n    assert(candidate((1), (2), (0)) == (0));\n    assert(candidate((2), (2), (0)) == (2));\n}\n"}
{"name": "HumanEval_49_modp", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return 2^n modulo p (be aware of numerics).\n// >>> modp((3), (5))\n// (3)\n// >>> modp((1101), (101))\n// (2)\n// >>> modp((0), (101))\n// (1)\n// >>> modp((3), (11))\n// (8)\n// >>> modp((100), (101))\n// (1)\nlong modp(long n, long p) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = modp;\n    assert(candidate((3), (5)) == (3));\n    assert(candidate((1101), (101)) == (2));\n    assert(candidate((0), (101)) == (1));\n    assert(candidate((3), (11)) == (8));\n    assert(candidate((100), (101)) == (1));\n    assert(candidate((30), (5)) == (4));\n    assert(candidate((31), (5)) == (3));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_49_modp", "test": "}\nint main() {\n    auto candidate = modp;\n    assert(candidate((3), (5)) == (3));\n    assert(candidate((1101), (101)) == (2));\n    assert(candidate((0), (101)) == (1));\n    assert(candidate((3), (11)) == (8));\n    assert(candidate((100), (101)) == (1));\n    assert(candidate((30), (5)) == (4));\n    assert(candidate((31), (5)) == (3));\n}\n"}
{"name": "HumanEval_155_even_odd_count", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given an integer. return a tuple that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count((-12))\n// (std::make_tuple(1, 1))\n// >>> even_odd_count((123))\n// (std::make_tuple(1, 2))\nstd::tuple<long, long> even_odd_count(long num) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = even_odd_count;\n    assert(candidate((7)) == (std::make_tuple(0, 1)));\n    assert(candidate((-78)) == (std::make_tuple(1, 1)));\n    assert(candidate((3452)) == (std::make_tuple(2, 2)));\n    assert(candidate((346211)) == (std::make_tuple(3, 3)));\n    assert(candidate((-345821)) == (std::make_tuple(3, 3)));\n    assert(candidate((-2)) == (std::make_tuple(1, 0)));\n    assert(candidate((-45347)) == (std::make_tuple(2, 3)));\n    assert(candidate((0)) == (std::make_tuple(1, 0)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_155_even_odd_count", "test": "}\nint main() {\n    auto candidate = even_odd_count;\n    assert(candidate((7)) == (std::make_tuple(0, 1)));\n    assert(candidate((-78)) == (std::make_tuple(1, 1)));\n    assert(candidate((3452)) == (std::make_tuple(2, 2)));\n    assert(candidate((346211)) == (std::make_tuple(3, 3)));\n    assert(candidate((-345821)) == (std::make_tuple(3, 3)));\n    assert(candidate((-2)) == (std::make_tuple(1, 0)));\n    assert(candidate((-45347)) == (std::make_tuple(2, 3)));\n    assert(candidate((0)) == (std::make_tuple(1, 0)));\n}\n"}
{"name": "HumanEval_80_is_happy", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a string s.\n// Your task is to check if the string is hapcpp or not.\n// A string is hapcpp if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy((\"a\"))\n// (false)\n// >>> is_happy((\"aa\"))\n// (false)\n// >>> is_happy((\"abcd\"))\n// (true)\n// >>> is_happy((\"aabb\"))\n// (false)\n// >>> is_happy((\"adb\"))\n// (true)\n// >>> is_happy((\"xyy\"))\n// (false)\nbool is_happy(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_happy;\n    assert(candidate((\"a\")) == (false));\n    assert(candidate((\"aa\")) == (false));\n    assert(candidate((\"abcd\")) == (true));\n    assert(candidate((\"aabb\")) == (false));\n    assert(candidate((\"adb\")) == (true));\n    assert(candidate((\"xyy\")) == (false));\n    assert(candidate((\"iopaxpoi\")) == (true));\n    assert(candidate((\"iopaxioi\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_80_is_happy", "test": "}\nint main() {\n    auto candidate = is_happy;\n    assert(candidate((\"a\")) == (false));\n    assert(candidate((\"aa\")) == (false));\n    assert(candidate((\"abcd\")) == (true));\n    assert(candidate((\"aabb\")) == (false));\n    assert(candidate((\"adb\")) == (true));\n    assert(candidate((\"xyy\")) == (false));\n    assert(candidate((\"iopaxpoi\")) == (true));\n    assert(candidate((\"iopaxioi\")) == (false));\n}\n"}
{"name": "HumanEval_59_largest_prime_factor", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor((13195))\n// (29)\n// >>> largest_prime_factor((2048))\n// (2)\nlong largest_prime_factor(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = largest_prime_factor;\n    assert(candidate((15)) == (5));\n    assert(candidate((27)) == (3));\n    assert(candidate((63)) == (7));\n    assert(candidate((330)) == (11));\n    assert(candidate((13195)) == (29));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_59_largest_prime_factor", "test": "}\nint main() {\n    auto candidate = largest_prime_factor;\n    assert(candidate((15)) == (5));\n    assert(candidate((27)) == (3));\n    assert(candidate((63)) == (7));\n    assert(candidate((330)) == (11));\n    assert(candidate((13195)) == (29));\n}\n"}
{"name": "HumanEval_66_digitSum", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum((\"\"))\n// (0)\n// >>> digitSum((\"abAB\"))\n// (131)\n// >>> digitSum((\"abcCd\"))\n// (67)\n// >>> digitSum((\"helloE\"))\n// (69)\n// >>> digitSum((\"woArBld\"))\n// (131)\n// >>> digitSum((\"aAaaaXa\"))\n// (153)\nlong digitSum(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = digitSum;\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"abAB\")) == (131));\n    assert(candidate((\"abcCd\")) == (67));\n    assert(candidate((\"helloE\")) == (69));\n    assert(candidate((\"woArBld\")) == (131));\n    assert(candidate((\"aAaaaXa\")) == (153));\n    assert(candidate((\" How are yOu?\")) == (151));\n    assert(candidate((\"You arE Very Smart\")) == (327));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_66_digitSum", "test": "}\nint main() {\n    auto candidate = digitSum;\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"abAB\")) == (131));\n    assert(candidate((\"abcCd\")) == (67));\n    assert(candidate((\"helloE\")) == (69));\n    assert(candidate((\"woArBld\")) == (131));\n    assert(candidate((\"aAaaaXa\")) == (153));\n    assert(candidate((\" How are yOu?\")) == (151));\n    assert(candidate((\"You arE Very Smart\")) == (327));\n}\n"}
{"name": "HumanEval_21_rescale_to_unit", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given vector of numbers (of at least two elements), apply a linear transform to that vector,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f})))\n// (std::vector<float>({(float)0.0f, (float)0.25f, (float)0.5f, (float)0.75f, (float)1.0f}))\nstd::vector<float> rescale_to_unit(std::vector<float> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = rescale_to_unit;\n    assert(candidate((std::vector<float>({(float)2.0f, (float)49.9f}))) == (std::vector<float>({(float)0.0f, (float)1.0f})));\n    assert(candidate((std::vector<float>({(float)100.0f, (float)49.9f}))) == (std::vector<float>({(float)1.0f, (float)0.0f})));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f}))) == (std::vector<float>({(float)0.0f, (float)0.25f, (float)0.5f, (float)0.75f, (float)1.0f})));\n    assert(candidate((std::vector<float>({(float)2.0f, (float)1.0f, (float)5.0f, (float)3.0f, (float)4.0f}))) == (std::vector<float>({(float)0.25f, (float)0.0f, (float)1.0f, (float)0.5f, (float)0.75f})));\n    assert(candidate((std::vector<float>({(float)12.0f, (float)11.0f, (float)15.0f, (float)13.0f, (float)14.0f}))) == (std::vector<float>({(float)0.25f, (float)0.0f, (float)1.0f, (float)0.5f, (float)0.75f})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_21_rescale_to_unit", "test": "}\nint main() {\n    auto candidate = rescale_to_unit;\n    assert(candidate((std::vector<float>({(float)2.0f, (float)49.9f}))) == (std::vector<float>({(float)0.0f, (float)1.0f})));\n    assert(candidate((std::vector<float>({(float)100.0f, (float)49.9f}))) == (std::vector<float>({(float)1.0f, (float)0.0f})));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f}))) == (std::vector<float>({(float)0.0f, (float)0.25f, (float)0.5f, (float)0.75f, (float)1.0f})));\n    assert(candidate((std::vector<float>({(float)2.0f, (float)1.0f, (float)5.0f, (float)3.0f, (float)4.0f}))) == (std::vector<float>({(float)0.25f, (float)0.0f, (float)1.0f, (float)0.5f, (float)0.75f})));\n    assert(candidate((std::vector<float>({(float)12.0f, (float)11.0f, (float)15.0f, (float)13.0f, (float)14.0f}))) == (std::vector<float>({(float)0.25f, (float)0.0f, (float)1.0f, (float)0.5f, (float)0.75f})));\n}\n"}
{"name": "HumanEval_121_solution", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a non-empty vector of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution((std::vector<long>({(long)5, (long)8, (long)7, (long)1})))\n// (12)\n// >>> solution((std::vector<long>({(long)3, (long)3, (long)3, (long)3, (long)3})))\n// (9)\n// >>> solution((std::vector<long>({(long)30, (long)13, (long)24, (long)321})))\n// (0)\nlong solution(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = solution;\n    assert(candidate((std::vector<long>({(long)5, (long)8, (long)7, (long)1}))) == (12));\n    assert(candidate((std::vector<long>({(long)3, (long)3, (long)3, (long)3, (long)3}))) == (9));\n    assert(candidate((std::vector<long>({(long)30, (long)13, (long)24, (long)321}))) == (0));\n    assert(candidate((std::vector<long>({(long)5, (long)9}))) == (5));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)8}))) == (0));\n    assert(candidate((std::vector<long>({(long)30, (long)13, (long)23, (long)32}))) == (23));\n    assert(candidate((std::vector<long>({(long)3, (long)13, (long)2, (long)9}))) == (3));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_121_solution", "test": "}\nint main() {\n    auto candidate = solution;\n    assert(candidate((std::vector<long>({(long)5, (long)8, (long)7, (long)1}))) == (12));\n    assert(candidate((std::vector<long>({(long)3, (long)3, (long)3, (long)3, (long)3}))) == (9));\n    assert(candidate((std::vector<long>({(long)30, (long)13, (long)24, (long)321}))) == (0));\n    assert(candidate((std::vector<long>({(long)5, (long)9}))) == (5));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)8}))) == (0));\n    assert(candidate((std::vector<long>({(long)30, (long)13, (long)23, (long)32}))) == (23));\n    assert(candidate((std::vector<long>({(long)3, (long)13, (long)2, (long)9}))) == (3));\n}\n"}
{"name": "HumanEval_68_pluck", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// \"Given a vector representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in a vector, [ smalest_value, its index ],\n// If there are no even values or the given vector is empty, return [].\n// Example 1:\n// >>> pluck((std::vector<long>({(long)4, (long)2, (long)3})))\n// (std::vector<long>({(long)2, (long)1}))\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck((std::vector<long>({(long)1, (long)2, (long)3})))\n// (std::vector<long>({(long)2, (long)1}))\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck((std::vector<long>()))\n// (std::vector<long>())\n// Example 4:\n// >>> pluck((std::vector<long>({(long)5, (long)0, (long)3, (long)0, (long)4, (long)2})))\n// (std::vector<long>({(long)0, (long)1}))\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nstd::vector<long> pluck(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = pluck;\n    assert(candidate((std::vector<long>({(long)4, (long)2, (long)3}))) == (std::vector<long>({(long)2, (long)1})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (std::vector<long>({(long)2, (long)1})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)5, (long)0, (long)3, (long)0, (long)4, (long)2}))) == (std::vector<long>({(long)0, (long)1})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)0, (long)5, (long)3}))) == (std::vector<long>({(long)0, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)4, (long)8, (long)4, (long)8}))) == (std::vector<long>({(long)4, (long)1})));\n    assert(candidate((std::vector<long>({(long)7, (long)6, (long)7, (long)1}))) == (std::vector<long>({(long)6, (long)1})));\n    assert(candidate((std::vector<long>({(long)7, (long)9, (long)7, (long)1}))) == (std::vector<long>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_68_pluck", "test": "}\nint main() {\n    auto candidate = pluck;\n    assert(candidate((std::vector<long>({(long)4, (long)2, (long)3}))) == (std::vector<long>({(long)2, (long)1})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (std::vector<long>({(long)2, (long)1})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)5, (long)0, (long)3, (long)0, (long)4, (long)2}))) == (std::vector<long>({(long)0, (long)1})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)0, (long)5, (long)3}))) == (std::vector<long>({(long)0, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)4, (long)8, (long)4, (long)8}))) == (std::vector<long>({(long)4, (long)1})));\n    assert(candidate((std::vector<long>({(long)7, (long)6, (long)7, (long)1}))) == (std::vector<long>({(long)6, (long)1})));\n    assert(candidate((std::vector<long>({(long)7, (long)9, (long)7, (long)1}))) == (std::vector<long>()));\n}\n"}
{"name": "HumanEval_147_get_max_triples", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a positive integer n. You have to create an integer vector a of length n.\n// For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples((5))\n// (1)\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nlong get_max_triples(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = get_max_triples;\n    assert(candidate((5)) == (1));\n    assert(candidate((6)) == (4));\n    assert(candidate((10)) == (36));\n    assert(candidate((100)) == (53361));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_147_get_max_triples", "test": "}\nint main() {\n    auto candidate = get_max_triples;\n    assert(candidate((5)) == (1));\n    assert(candidate((6)) == (4));\n    assert(candidate((10)) == (36));\n    assert(candidate((100)) == (53361));\n}\n"}
{"name": "HumanEval_110_exchange", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// In this problem, you will implement a function that takes two vectors of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 a vector of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)1, (long)2, (long)3, (long)4})))\n// (\"YES\")\n// >>> exchange((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)1, (long)5, (long)3, (long)4})))\n// (\"NO\")\n// It is assumed that the input vectors will be non-empty.\nstd::string exchange(std::vector<long> lst1, std::vector<long> lst2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = exchange;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (\"YES\"));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)1, (long)5, (long)3, (long)4}))) == (\"NO\"));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)2, (long)1, (long)4, (long)3}))) == (\"YES\"));\n    assert(candidate((std::vector<long>({(long)5, (long)7, (long)3})), (std::vector<long>({(long)2, (long)6, (long)4}))) == (\"YES\"));\n    assert(candidate((std::vector<long>({(long)5, (long)7, (long)3})), (std::vector<long>({(long)2, (long)6, (long)3}))) == (\"NO\"));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)6, (long)1, (long)8, (long)9})), (std::vector<long>({(long)3, (long)5, (long)5, (long)1, (long)1, (long)1}))) == (\"NO\"));\n    assert(candidate((std::vector<long>({(long)100, (long)200})), (std::vector<long>({(long)200, (long)200}))) == (\"YES\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_110_exchange", "test": "}\nint main() {\n    auto candidate = exchange;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (\"YES\"));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)1, (long)5, (long)3, (long)4}))) == (\"NO\"));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4})), (std::vector<long>({(long)2, (long)1, (long)4, (long)3}))) == (\"YES\"));\n    assert(candidate((std::vector<long>({(long)5, (long)7, (long)3})), (std::vector<long>({(long)2, (long)6, (long)4}))) == (\"YES\"));\n    assert(candidate((std::vector<long>({(long)5, (long)7, (long)3})), (std::vector<long>({(long)2, (long)6, (long)3}))) == (\"NO\"));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)6, (long)1, (long)8, (long)9})), (std::vector<long>({(long)3, (long)5, (long)5, (long)1, (long)1, (long)1}))) == (\"NO\"));\n    assert(candidate((std::vector<long>({(long)100, (long)200})), (std::vector<long>({(long)200, (long)200}))) == (\"YES\"));\n}\n"}
{"name": "HumanEval_47_median", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return median of elements in the vector l.\n// >>> median((std::vector<long>({(long)3, (long)1, (long)2, (long)4, (long)5})))\n// (float(3))\n// >>> median((std::vector<long>({(long)-10, (long)4, (long)6, (long)1000, (long)10, (long)20})))\n// (15.0f)\nfloat median(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = median;\n    assert(candidate((std::vector<long>({(long)3, (long)1, (long)2, (long)4, (long)5}))) == (float(3)));\n    assert(candidate((std::vector<long>({(long)-10, (long)4, (long)6, (long)1000, (long)10, (long)20}))) == (8.0f));\n    assert(candidate((std::vector<long>({(long)5}))) == (float(5)));\n    assert(candidate((std::vector<long>({(long)6, (long)5}))) == (5.5f));\n    assert(candidate((std::vector<long>({(long)8, (long)1, (long)3, (long)9, (long)9, (long)2, (long)7}))) == (float(7)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_47_median", "test": "}\nint main() {\n    auto candidate = median;\n    assert(candidate((std::vector<long>({(long)3, (long)1, (long)2, (long)4, (long)5}))) == (float(3)));\n    assert(candidate((std::vector<long>({(long)-10, (long)4, (long)6, (long)1000, (long)10, (long)20}))) == (8.0f));\n    assert(candidate((std::vector<long>({(long)5}))) == (float(5)));\n    assert(candidate((std::vector<long>({(long)6, (long)5}))) == (5.5f));\n    assert(candidate((std::vector<long>({(long)8, (long)1, (long)3, (long)9, (long)9, (long)2, (long)7}))) == (float(7)));\n}\n"}
{"name": "HumanEval_82_prime_length", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length((\"Hello\"))\n// (true)\n// >>> prime_length((\"abcdcba\"))\n// (true)\n// >>> prime_length((\"kittens\"))\n// (true)\n// >>> prime_length((\"orange\"))\n// (false)\nbool prime_length(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = prime_length;\n    assert(candidate((\"Hello\")) == (true));\n    assert(candidate((\"abcdcba\")) == (true));\n    assert(candidate((\"kittens\")) == (true));\n    assert(candidate((\"orange\")) == (false));\n    assert(candidate((\"wow\")) == (true));\n    assert(candidate((\"world\")) == (true));\n    assert(candidate((\"MadaM\")) == (true));\n    assert(candidate((\"Wow\")) == (true));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"HI\")) == (true));\n    assert(candidate((\"go\")) == (true));\n    assert(candidate((\"gogo\")) == (false));\n    assert(candidate((\"aaaaaaaaaaaaaaa\")) == (false));\n    assert(candidate((\"Madam\")) == (true));\n    assert(candidate((\"M\")) == (false));\n    assert(candidate((\"0\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_82_prime_length", "test": "}\nint main() {\n    auto candidate = prime_length;\n    assert(candidate((\"Hello\")) == (true));\n    assert(candidate((\"abcdcba\")) == (true));\n    assert(candidate((\"kittens\")) == (true));\n    assert(candidate((\"orange\")) == (false));\n    assert(candidate((\"wow\")) == (true));\n    assert(candidate((\"world\")) == (true));\n    assert(candidate((\"MadaM\")) == (true));\n    assert(candidate((\"Wow\")) == (true));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"HI\")) == (true));\n    assert(candidate((\"go\")) == (true));\n    assert(candidate((\"gogo\")) == (false));\n    assert(candidate((\"aaaaaaaaaaaaaaa\")) == (false));\n    assert(candidate((\"Madam\")) == (true));\n    assert(candidate((\"M\")) == (false));\n    assert(candidate((\"0\")) == (false));\n}\n"}
{"name": "HumanEval_73_smallest_change", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector arr of integers, find the minimum number of elements that\n// need to be changed to make the vector palindromic. A palindromic vector is a vector that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change((std::vector<long>({(long)1, (long)2, (long)3, (long)5, (long)4, (long)7, (long)9, (long)6})))\n// (4)\n// >>> smallest_change((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)3, (long)2, (long)2})))\n// (1)\n// >>> smallest_change((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)1})))\n// (0)\nlong smallest_change(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = smallest_change;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)5, (long)4, (long)7, (long)9, (long)6}))) == (4));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)3, (long)2, (long)2}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)2}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)4, (long)2}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)1}))) == (0));\n    assert(candidate((std::vector<long>({(long)3, (long)1, (long)1, (long)3}))) == (0));\n    assert(candidate((std::vector<long>({(long)1}))) == (0));\n    assert(candidate((std::vector<long>({(long)0, (long)1}))) == (1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_73_smallest_change", "test": "}\nint main() {\n    auto candidate = smallest_change;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)5, (long)4, (long)7, (long)9, (long)6}))) == (4));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)3, (long)2, (long)2}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)2}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)4, (long)2}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)1}))) == (0));\n    assert(candidate((std::vector<long>({(long)3, (long)1, (long)1, (long)3}))) == (0));\n    assert(candidate((std::vector<long>({(long)1}))) == (0));\n    assert(candidate((std::vector<long>({(long)0, (long)1}))) == (1));\n}\n"}
{"name": "HumanEval_133_sum_squares", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a vector of numbers.\n// You need to return the sum of squared numbers in the given vector,\n// round each element in the vector to the upper int(Ceiling) first.\n// Examples:\n// >>> lst((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f})))\n// (14)\n// >>> lst((std::vector<float>({(float)1.0f, (float)4.0f, (float)9.0f})))\n// (98)\n// >>> lst((std::vector<float>({(float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f})))\n// (84)\n// >>> lst((std::vector<float>({(float)1.4f, (float)4.2f, (float)0.0f})))\n// (29)\n// >>> lst((std::vector<float>({(float)-2.4f, (float)1.0f, (float)1.0f})))\n// (6)\nlong sum_squares(std::vector<float> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sum_squares;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f}))) == (14));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f}))) == (14));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f}))) == (84));\n    assert(candidate((std::vector<float>({(float)1.4f, (float)4.2f, (float)0.0f}))) == (29));\n    assert(candidate((std::vector<float>({(float)-2.4f, (float)1.0f, (float)1.0f}))) == (6));\n    assert(candidate((std::vector<float>({(float)100.0f, (float)1.0f, (float)15.0f, (float)2.0f}))) == (10230));\n    assert(candidate((std::vector<float>({(float)10000.0f, (float)10000.0f}))) == (200000000));\n    assert(candidate((std::vector<float>({(float)-1.4f, (float)4.6f, (float)6.3f}))) == (75));\n    assert(candidate((std::vector<float>({(float)-1.4f, (float)17.9f, (float)18.9f, (float)19.9f}))) == (1086));\n    assert(candidate((std::vector<float>({(float)0.0f}))) == (0));\n    assert(candidate((std::vector<float>({(float)-1.0f}))) == (1));\n    assert(candidate((std::vector<float>({(float)-1.0f, (float)1.0f, (float)0.0f}))) == (2));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_133_sum_squares", "test": "}\nint main() {\n    auto candidate = sum_squares;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f}))) == (14));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f}))) == (14));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f}))) == (84));\n    assert(candidate((std::vector<float>({(float)1.4f, (float)4.2f, (float)0.0f}))) == (29));\n    assert(candidate((std::vector<float>({(float)-2.4f, (float)1.0f, (float)1.0f}))) == (6));\n    assert(candidate((std::vector<float>({(float)100.0f, (float)1.0f, (float)15.0f, (float)2.0f}))) == (10230));\n    assert(candidate((std::vector<float>({(float)10000.0f, (float)10000.0f}))) == (200000000));\n    assert(candidate((std::vector<float>({(float)-1.4f, (float)4.6f, (float)6.3f}))) == (75));\n    assert(candidate((std::vector<float>({(float)-1.4f, (float)17.9f, (float)18.9f, (float)19.9f}))) == (1086));\n    assert(candidate((std::vector<float>({(float)0.0f}))) == (0));\n    assert(candidate((std::vector<float>({(float)-1.0f}))) == (1));\n    assert(candidate((std::vector<float>({(float)-1.0f, (float)1.0f, (float)0.0f}))) == (2));\n}\n"}
{"name": "HumanEval_141_file_name_check", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check((\"example.txt\"))\n// (\"Yes\")\n// >>> file_name_check((\"1example.dll\"))\n// (\"No\")\nstd::string file_name_check(std::string file_name) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = file_name_check;\n    assert(candidate((\"example.txt\")) == (\"Yes\"));\n    assert(candidate((\"1example.dll\")) == (\"No\"));\n    assert(candidate((\"s1sdf3.asd\")) == (\"No\"));\n    assert(candidate((\"K.dll\")) == (\"Yes\"));\n    assert(candidate((\"MY16FILE3.exe\")) == (\"Yes\"));\n    assert(candidate((\"His12FILE94.exe\")) == (\"No\"));\n    assert(candidate((\"_Y.txt\")) == (\"No\"));\n    assert(candidate((\"?aREYA.exe\")) == (\"No\"));\n    assert(candidate((\"/this_is_valid.dll\")) == (\"No\"));\n    assert(candidate((\"this_is_valid.wow\")) == (\"No\"));\n    assert(candidate((\"this_is_valid.txt\")) == (\"Yes\"));\n    assert(candidate((\"this_is_valid.txtexe\")) == (\"No\"));\n    assert(candidate((\"#this2_i4s_5valid.ten\")) == (\"No\"));\n    assert(candidate((\"@this1_is6_valid.exe\")) == (\"No\"));\n    assert(candidate((\"this_is_12valid.6exe4.txt\")) == (\"No\"));\n    assert(candidate((\"all.exe.txt\")) == (\"No\"));\n    assert(candidate((\"I563_No.exe\")) == (\"Yes\"));\n    assert(candidate((\"Is3youfault.txt\")) == (\"Yes\"));\n    assert(candidate((\"no_one#knows.dll\")) == (\"Yes\"));\n    assert(candidate((\"1I563_Yes3.exe\")) == (\"No\"));\n    assert(candidate((\"I563_Yes3.txtt\")) == (\"No\"));\n    assert(candidate((\"final..txt\")) == (\"No\"));\n    assert(candidate((\"final132\")) == (\"No\"));\n    assert(candidate((\"_f4indsartal132.\")) == (\"No\"));\n    assert(candidate((\".txt\")) == (\"No\"));\n    assert(candidate((\"s.\")) == (\"No\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_141_file_name_check", "test": "}\nint main() {\n    auto candidate = file_name_check;\n    assert(candidate((\"example.txt\")) == (\"Yes\"));\n    assert(candidate((\"1example.dll\")) == (\"No\"));\n    assert(candidate((\"s1sdf3.asd\")) == (\"No\"));\n    assert(candidate((\"K.dll\")) == (\"Yes\"));\n    assert(candidate((\"MY16FILE3.exe\")) == (\"Yes\"));\n    assert(candidate((\"His12FILE94.exe\")) == (\"No\"));\n    assert(candidate((\"_Y.txt\")) == (\"No\"));\n    assert(candidate((\"?aREYA.exe\")) == (\"No\"));\n    assert(candidate((\"/this_is_valid.dll\")) == (\"No\"));\n    assert(candidate((\"this_is_valid.wow\")) == (\"No\"));\n    assert(candidate((\"this_is_valid.txt\")) == (\"Yes\"));\n    assert(candidate((\"this_is_valid.txtexe\")) == (\"No\"));\n    assert(candidate((\"#this2_i4s_5valid.ten\")) == (\"No\"));\n    assert(candidate((\"@this1_is6_valid.exe\")) == (\"No\"));\n    assert(candidate((\"this_is_12valid.6exe4.txt\")) == (\"No\"));\n    assert(candidate((\"all.exe.txt\")) == (\"No\"));\n    assert(candidate((\"I563_No.exe\")) == (\"Yes\"));\n    assert(candidate((\"Is3youfault.txt\")) == (\"Yes\"));\n    assert(candidate((\"no_one#knows.dll\")) == (\"Yes\"));\n    assert(candidate((\"1I563_Yes3.exe\")) == (\"No\"));\n    assert(candidate((\"I563_Yes3.txtt\")) == (\"No\"));\n    assert(candidate((\"final..txt\")) == (\"No\"));\n    assert(candidate((\"final132\")) == (\"No\"));\n    assert(candidate((\"_f4indsartal132.\")) == (\"No\"));\n    assert(candidate((\".txt\")) == (\"No\"));\n    assert(candidate((\"s.\")) == (\"No\"));\n}\n"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// triples_sum_to_zero takes a vector of integers as an input.\n// it returns true if there are three distinct elements in the vector that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero((std::vector<long>({(long)1, (long)3, (long)5, (long)0})))\n// (false)\n// >>> triples_sum_to_zero((std::vector<long>({(long)1, (long)3, (long)-2, (long)1})))\n// (true)\n// >>> triples_sum_to_zero((std::vector<long>({(long)1, (long)2, (long)3, (long)7})))\n// (false)\n// >>> triples_sum_to_zero((std::vector<long>({(long)2, (long)4, (long)-5, (long)3, (long)9, (long)7})))\n// (true)\n// >>> triples_sum_to_zero((std::vector<long>({(long)1})))\n// (false)\nbool triples_sum_to_zero(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = triples_sum_to_zero;\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)0}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)-1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)-2, (long)1}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)7}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)5, (long)7}))) == (false));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)-5, (long)3, (long)9, (long)7}))) == (true));\n    assert(candidate((std::vector<long>({(long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)-100}))) == (false));\n    assert(candidate((std::vector<long>({(long)100, (long)3, (long)5, (long)-100}))) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "}\nint main() {\n    auto candidate = triples_sum_to_zero;\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)0}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)-1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)-2, (long)1}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)7}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)5, (long)7}))) == (false));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)-5, (long)3, (long)9, (long)7}))) == (true));\n    assert(candidate((std::vector<long>({(long)1}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)5, (long)-100}))) == (false));\n    assert(candidate((std::vector<long>({(long)100, (long)3, (long)5, (long)-100}))) == (false));\n}\n"}
{"name": "HumanEval_127_intersection", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection((std::make_tuple(1, 2)), (std::make_tuple(2, 3)))\n// (\"NO\")\n// >>> intersection((std::make_tuple(-1, 1)), (std::make_tuple(0, 4)))\n// (\"NO\")\n// >>> intersection((std::make_tuple(-3, -1)), (std::make_tuple(-5, 5)))\n// (\"YES\")\nstd::string intersection(std::tuple<long, long> interval1, std::tuple<long, long> interval2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = intersection;\n    assert(candidate((std::make_tuple(1, 2)), (std::make_tuple(2, 3))) == (\"NO\"));\n    assert(candidate((std::make_tuple(-1, 1)), (std::make_tuple(0, 4))) == (\"NO\"));\n    assert(candidate((std::make_tuple(-3, -1)), (std::make_tuple(-5, 5))) == (\"YES\"));\n    assert(candidate((std::make_tuple(-2, 2)), (std::make_tuple(-4, 0))) == (\"YES\"));\n    assert(candidate((std::make_tuple(-11, 2)), (std::make_tuple(-1, -1))) == (\"NO\"));\n    assert(candidate((std::make_tuple(1, 2)), (std::make_tuple(3, 5))) == (\"NO\"));\n    assert(candidate((std::make_tuple(1, 2)), (std::make_tuple(1, 2))) == (\"NO\"));\n    assert(candidate((std::make_tuple(-2, -2)), (std::make_tuple(-3, -2))) == (\"NO\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_127_intersection", "test": "}\nint main() {\n    auto candidate = intersection;\n    assert(candidate((std::make_tuple(1, 2)), (std::make_tuple(2, 3))) == (\"NO\"));\n    assert(candidate((std::make_tuple(-1, 1)), (std::make_tuple(0, 4))) == (\"NO\"));\n    assert(candidate((std::make_tuple(-3, -1)), (std::make_tuple(-5, 5))) == (\"YES\"));\n    assert(candidate((std::make_tuple(-2, 2)), (std::make_tuple(-4, 0))) == (\"YES\"));\n    assert(candidate((std::make_tuple(-11, 2)), (std::make_tuple(-1, -1))) == (\"NO\"));\n    assert(candidate((std::make_tuple(1, 2)), (std::make_tuple(3, 5))) == (\"NO\"));\n    assert(candidate((std::make_tuple(1, 2)), (std::make_tuple(1, 2))) == (\"NO\"));\n    assert(candidate((std::make_tuple(-2, -2)), (std::make_tuple(-3, -2))) == (\"NO\"));\n}\n"}
{"name": "HumanEval_1_separate_paren_groups", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the vector of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups((\"( ) (( )) (( )( ))\"))\n// (std::vector<std::string>({(std::string)\"()\", (std::string)\"(())\", (std::string)\"(()())\"}))\nstd::vector<std::string> separate_paren_groups(std::string paren_string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = separate_paren_groups;\n    assert(candidate((\"(()()) ((())) () ((())()())\")) == (std::vector<std::string>({(std::string)\"(()())\", (std::string)\"((()))\", (std::string)\"()\", (std::string)\"((())()())\"})));\n    assert(candidate((\"() (()) ((())) (((())))\")) == (std::vector<std::string>({(std::string)\"()\", (std::string)\"(())\", (std::string)\"((()))\", (std::string)\"(((())))\"})));\n    assert(candidate((\"(()(())((())))\")) == (std::vector<std::string>({(std::string)\"(()(())((())))\"})));\n    assert(candidate((\"( ) (( )) (( )( ))\")) == (std::vector<std::string>({(std::string)\"()\", (std::string)\"(())\", (std::string)\"(()())\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_1_separate_paren_groups", "test": "}\nint main() {\n    auto candidate = separate_paren_groups;\n    assert(candidate((\"(()()) ((())) () ((())()())\")) == (std::vector<std::string>({(std::string)\"(()())\", (std::string)\"((()))\", (std::string)\"()\", (std::string)\"((())()())\"})));\n    assert(candidate((\"() (()) ((())) (((())))\")) == (std::vector<std::string>({(std::string)\"()\", (std::string)\"(())\", (std::string)\"((()))\", (std::string)\"(((())))\"})));\n    assert(candidate((\"(()(())((())))\")) == (std::vector<std::string>({(std::string)\"(()(())((())))\"})));\n    assert(candidate((\"( ) (( )) (( )( ))\")) == (std::vector<std::string>({(std::string)\"()\", (std::string)\"(())\", (std::string)\"(()())\"})));\n}\n"}
{"name": "HumanEval_152_compare", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two vectors of scores and guesses of equal length, where each index shows a match. \n// Return a vector of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})), (std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)2, (long)-2})))\n// (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)3, (long)3}))\n// >>> compare((std::vector<long>({(long)0, (long)5, (long)0, (long)0, (long)0, (long)4})), (std::vector<long>({(long)4, (long)1, (long)1, (long)0, (long)0, (long)-2})))\n// (std::vector<long>({(long)4, (long)4, (long)1, (long)0, (long)0, (long)6}))\nstd::vector<long> compare(std::vector<long> game, std::vector<long> guess) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = compare;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})), (std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)2, (long)-2}))) == (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)3, (long)3})));\n    assert(candidate((std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)0, (long)0})), (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)0, (long)0}))) == (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)0, (long)0})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3})), (std::vector<long>({(long)-1, (long)-2, (long)-3}))) == (std::vector<long>({(long)2, (long)4, (long)6})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)5})), (std::vector<long>({(long)-1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)2, (long)0, (long)0, (long)1})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_152_compare", "test": "}\nint main() {\n    auto candidate = compare;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})), (std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)2, (long)-2}))) == (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)3, (long)3})));\n    assert(candidate((std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)0, (long)0})), (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)0, (long)0}))) == (std::vector<long>({(long)0, (long)0, (long)0, (long)0, (long)0, (long)0})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3})), (std::vector<long>({(long)-1, (long)-2, (long)-3}))) == (std::vector<long>({(long)2, (long)4, (long)6})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)5})), (std::vector<long>({(long)-1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)2, (long)0, (long)0, (long)1})));\n}\n"}
{"name": "HumanEval_83_starts_one_ends", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nlong starts_one_ends(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = starts_one_ends;\n    assert(candidate((1)) == (1));\n    assert(candidate((2)) == (18));\n    assert(candidate((3)) == (180));\n    assert(candidate((4)) == (1800));\n    assert(candidate((5)) == (18000));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_83_starts_one_ends", "test": "}\nint main() {\n    auto candidate = starts_one_ends;\n    assert(candidate((1)) == (1));\n    assert(candidate((2)) == (18));\n    assert(candidate((3)) == (180));\n    assert(candidate((4)) == (1800));\n    assert(candidate((5)) == (18000));\n}\n"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter((\"apple pie\"))\n// (false)\n// >>> check_if_last_char_is_a_letter((\"apple pi e\"))\n// (true)\n// >>> check_if_last_char_is_a_letter((\"apple pi e \"))\n// (false)\n// >>> check_if_last_char_is_a_letter((\"\"))\n// (false)\nbool check_if_last_char_is_a_letter(std::string txt) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = check_if_last_char_is_a_letter;\n    assert(candidate((\"apple\")) == (false));\n    assert(candidate((\"apple pi e\")) == (true));\n    assert(candidate((\"eeeee\")) == (false));\n    assert(candidate((\"A\")) == (true));\n    assert(candidate((\"Pumpkin pie \")) == (false));\n    assert(candidate((\"Pumpkin pie 1\")) == (false));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"eeeee e \")) == (false));\n    assert(candidate((\"apple pie\")) == (false));\n    assert(candidate((\"apple pi e \")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "}\nint main() {\n    auto candidate = check_if_last_char_is_a_letter;\n    assert(candidate((\"apple\")) == (false));\n    assert(candidate((\"apple pi e\")) == (true));\n    assert(candidate((\"eeeee\")) == (false));\n    assert(candidate((\"A\")) == (true));\n    assert(candidate((\"Pumpkin pie \")) == (false));\n    assert(candidate((\"Pumpkin pie 1\")) == (false));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"eeeee e \")) == (false));\n    assert(candidate((\"apple pie\")) == (false));\n    assert(candidate((\"apple pi e \")) == (false));\n}\n"}
{"name": "HumanEval_124_valid_date", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date((\"03-11-2000\"))\n// (true)\n// >>> valid_date((\"15-01-2012\"))\n// (false)\n// >>> valid_date((\"04-0-2040\"))\n// (false)\n// >>> valid_date((\"06-04-2020\"))\n// (true)\n// >>> valid_date((\"06/04/2020\"))\n// (false)\nbool valid_date(std::string date) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = valid_date;\n    assert(candidate((\"03-11-2000\")) == (true));\n    assert(candidate((\"15-01-2012\")) == (false));\n    assert(candidate((\"04-0-2040\")) == (false));\n    assert(candidate((\"06-04-2020\")) == (true));\n    assert(candidate((\"01-01-2007\")) == (true));\n    assert(candidate((\"03-32-2011\")) == (false));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"04-31-3000\")) == (false));\n    assert(candidate((\"06-06-2005\")) == (true));\n    assert(candidate((\"21-31-2000\")) == (false));\n    assert(candidate((\"04-12-2003\")) == (true));\n    assert(candidate((\"04122003\")) == (false));\n    assert(candidate((\"20030412\")) == (false));\n    assert(candidate((\"2003-04\")) == (false));\n    assert(candidate((\"2003-04-12\")) == (false));\n    assert(candidate((\"04-2003\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_124_valid_date", "test": "}\nint main() {\n    auto candidate = valid_date;\n    assert(candidate((\"03-11-2000\")) == (true));\n    assert(candidate((\"15-01-2012\")) == (false));\n    assert(candidate((\"04-0-2040\")) == (false));\n    assert(candidate((\"06-04-2020\")) == (true));\n    assert(candidate((\"01-01-2007\")) == (true));\n    assert(candidate((\"03-32-2011\")) == (false));\n    assert(candidate((\"\")) == (false));\n    assert(candidate((\"04-31-3000\")) == (false));\n    assert(candidate((\"06-06-2005\")) == (true));\n    assert(candidate((\"21-31-2000\")) == (false));\n    assert(candidate((\"04-12-2003\")) == (true));\n    assert(candidate((\"04122003\")) == (false));\n    assert(candidate((\"20030412\")) == (false));\n    assert(candidate((\"2003-04\")) == (false));\n    assert(candidate((\"2003-04-12\")) == (false));\n    assert(candidate((\"04-2003\")) == (false));\n}\n"}
{"name": "HumanEval_108_count_nums", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function count_nums which takes a vector of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums((std::vector<long>()))\n// (0)\n// >>> count_nums((std::vector<long>({(long)-1, (long)11, (long)-11})))\n// (1)\n// >>> count_nums((std::vector<long>({(long)1, (long)1, (long)2})))\n// (3)\nlong count_nums(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = count_nums;\n    assert(candidate((std::vector<long>())) == (0));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)0}))) == (0));\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)2, (long)-2, (long)3, (long)4, (long)5}))) == (6));\n    assert(candidate((std::vector<long>({(long)1, (long)6, (long)9, (long)-6, (long)0, (long)1, (long)5}))) == (5));\n    assert(candidate((std::vector<long>({(long)1, (long)100, (long)98, (long)-7, (long)1, (long)-1}))) == (4));\n    assert(candidate((std::vector<long>({(long)12, (long)23, (long)34, (long)-45, (long)-56, (long)0}))) == (5));\n    assert(candidate((std::vector<long>({(long)0, (long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)1}))) == (1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_108_count_nums", "test": "}\nint main() {\n    auto candidate = count_nums;\n    assert(candidate((std::vector<long>())) == (0));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)0}))) == (0));\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)2, (long)-2, (long)3, (long)4, (long)5}))) == (6));\n    assert(candidate((std::vector<long>({(long)1, (long)6, (long)9, (long)-6, (long)0, (long)1, (long)5}))) == (5));\n    assert(candidate((std::vector<long>({(long)1, (long)100, (long)98, (long)-7, (long)1, (long)-1}))) == (4));\n    assert(candidate((std::vector<long>({(long)12, (long)23, (long)34, (long)-45, (long)-56, (long)0}))) == (5));\n    assert(candidate((std::vector<long>({(long)0, (long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)1}))) == (1));\n}\n"}
{"name": "HumanEval_86_anti_shuffle", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle((\"Hi\"))\n// (\"Hi\")\n// >>> anti_shuffle((\"hello\"))\n// (\"ehllo\")\n// >>> anti_shuffle((\"Hello World!!!\"))\n// (\"Hello !!!Wdlor\")\nstd::string anti_shuffle(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = anti_shuffle;\n    assert(candidate((\"Hi\")) == (\"Hi\"));\n    assert(candidate((\"hello\")) == (\"ehllo\"));\n    assert(candidate((\"number\")) == (\"bemnru\"));\n    assert(candidate((\"abcd\")) == (\"abcd\"));\n    assert(candidate((\"Hello World!!!\")) == (\"Hello !!!Wdlor\"));\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"Hi. My name is Mister Robot. How are you?\")) == (\".Hi My aemn is Meirst .Rboot How aer ?ouy\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_86_anti_shuffle", "test": "}\nint main() {\n    auto candidate = anti_shuffle;\n    assert(candidate((\"Hi\")) == (\"Hi\"));\n    assert(candidate((\"hello\")) == (\"ehllo\"));\n    assert(candidate((\"number\")) == (\"bemnru\"));\n    assert(candidate((\"abcd\")) == (\"abcd\"));\n    assert(candidate((\"Hello World!!!\")) == (\"Hello !!!Wdlor\"));\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"Hi. My name is Mister Robot. How are you?\")) == (\".Hi My aemn is Meirst .Rboot How aer ?ouy\"));\n}\n"}
{"name": "HumanEval_48_is_palindrome", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Checks if given string is a palindrome\n// >>> is_palindrome((\"\"))\n// (true)\n// >>> is_palindrome((\"aba\"))\n// (true)\n// >>> is_palindrome((\"aaaaa\"))\n// (true)\n// >>> is_palindrome((\"zbcd\"))\n// (false)\nbool is_palindrome(std::string text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_palindrome;\n    assert(candidate((\"\")) == (true));\n    assert(candidate((\"aba\")) == (true));\n    assert(candidate((\"aaaaa\")) == (true));\n    assert(candidate((\"zbcd\")) == (false));\n    assert(candidate((\"xywyx\")) == (true));\n    assert(candidate((\"xywyz\")) == (false));\n    assert(candidate((\"xywzx\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_48_is_palindrome", "test": "}\nint main() {\n    auto candidate = is_palindrome;\n    assert(candidate((\"\")) == (true));\n    assert(candidate((\"aba\")) == (true));\n    assert(candidate((\"aaaaa\")) == (true));\n    assert(candidate((\"zbcd\")) == (false));\n    assert(candidate((\"xywyx\")) == (true));\n    assert(candidate((\"xywyz\")) == (false));\n    assert(candidate((\"xywzx\")) == (false));\n}\n"}
{"name": "HumanEval_118_get_closest_vowel", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel((\"yogurt\"))\n// (\"u\")\n// >>> get_closest_vowel((\"FULL\"))\n// (\"U\")\n// >>> get_closest_vowel((\"quick\"))\n// (\"\")\n// >>> get_closest_vowel((\"ab\"))\n// (\"\")\nstd::string get_closest_vowel(std::string word) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = get_closest_vowel;\n    assert(candidate((\"yogurt\")) == (\"u\"));\n    assert(candidate((\"full\")) == (\"u\"));\n    assert(candidate((\"easy\")) == (\"\"));\n    assert(candidate((\"eAsy\")) == (\"\"));\n    assert(candidate((\"ali\")) == (\"\"));\n    assert(candidate((\"bad\")) == (\"a\"));\n    assert(candidate((\"most\")) == (\"o\"));\n    assert(candidate((\"ab\")) == (\"\"));\n    assert(candidate((\"ba\")) == (\"\"));\n    assert(candidate((\"quick\")) == (\"\"));\n    assert(candidate((\"anime\")) == (\"i\"));\n    assert(candidate((\"Asia\")) == (\"\"));\n    assert(candidate((\"Above\")) == (\"o\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_118_get_closest_vowel", "test": "}\nint main() {\n    auto candidate = get_closest_vowel;\n    assert(candidate((\"yogurt\")) == (\"u\"));\n    assert(candidate((\"full\")) == (\"u\"));\n    assert(candidate((\"easy\")) == (\"\"));\n    assert(candidate((\"eAsy\")) == (\"\"));\n    assert(candidate((\"ali\")) == (\"\"));\n    assert(candidate((\"bad\")) == (\"a\"));\n    assert(candidate((\"most\")) == (\"o\"));\n    assert(candidate((\"ab\")) == (\"\"));\n    assert(candidate((\"ba\")) == (\"\"));\n    assert(candidate((\"quick\")) == (\"\"));\n    assert(candidate((\"anime\")) == (\"i\"));\n    assert(candidate((\"Asia\")) == (\"\"));\n    assert(candidate((\"Above\")) == (\"o\"));\n}\n"}
{"name": "HumanEval_31_is_prime", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return true if a given number is prime, and false otherwise.\n// >>> is_prime((6))\n// (false)\n// >>> is_prime((101))\n// (true)\n// >>> is_prime((11))\n// (true)\n// >>> is_prime((13441))\n// (true)\n// >>> is_prime((61))\n// (true)\n// >>> is_prime((4))\n// (false)\n// >>> is_prime((1))\n// (false)\nbool is_prime(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_prime;\n    assert(candidate((6)) == (false));\n    assert(candidate((101)) == (true));\n    assert(candidate((11)) == (true));\n    assert(candidate((13441)) == (true));\n    assert(candidate((61)) == (true));\n    assert(candidate((4)) == (false));\n    assert(candidate((1)) == (false));\n    assert(candidate((5)) == (true));\n    assert(candidate((11)) == (true));\n    assert(candidate((17)) == (true));\n    assert(candidate((85)) == (false));\n    assert(candidate((77)) == (false));\n    assert(candidate((255379)) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_31_is_prime", "test": "}\nint main() {\n    auto candidate = is_prime;\n    assert(candidate((6)) == (false));\n    assert(candidate((101)) == (true));\n    assert(candidate((11)) == (true));\n    assert(candidate((13441)) == (true));\n    assert(candidate((61)) == (true));\n    assert(candidate((4)) == (false));\n    assert(candidate((1)) == (false));\n    assert(candidate((5)) == (true));\n    assert(candidate((11)) == (true));\n    assert(candidate((17)) == (true));\n    assert(candidate((85)) == (false));\n    assert(candidate((77)) == (false));\n    assert(candidate((255379)) == (false));\n}\n"}
{"name": "HumanEval_144_simplify", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify((\"1/5\"), (\"5/1\"))\n// (true)\n// >>> simplify((\"1/6\"), (\"2/1\"))\n// (false)\n// >>> simplify((\"7/10\"), (\"10/2\"))\n// (false)\nbool simplify(std::string x, std::string n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = simplify;\n    assert(candidate((\"1/5\"), (\"5/1\")) == (true));\n    assert(candidate((\"1/6\"), (\"2/1\")) == (false));\n    assert(candidate((\"5/1\"), (\"3/1\")) == (true));\n    assert(candidate((\"7/10\"), (\"10/2\")) == (false));\n    assert(candidate((\"2/10\"), (\"50/10\")) == (true));\n    assert(candidate((\"7/2\"), (\"4/2\")) == (true));\n    assert(candidate((\"11/6\"), (\"6/1\")) == (true));\n    assert(candidate((\"2/3\"), (\"5/2\")) == (false));\n    assert(candidate((\"5/2\"), (\"3/5\")) == (false));\n    assert(candidate((\"2/4\"), (\"8/4\")) == (true));\n    assert(candidate((\"2/4\"), (\"4/2\")) == (true));\n    assert(candidate((\"1/5\"), (\"5/1\")) == (true));\n    assert(candidate((\"1/5\"), (\"1/5\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_144_simplify", "test": "}\nint main() {\n    auto candidate = simplify;\n    assert(candidate((\"1/5\"), (\"5/1\")) == (true));\n    assert(candidate((\"1/6\"), (\"2/1\")) == (false));\n    assert(candidate((\"5/1\"), (\"3/1\")) == (true));\n    assert(candidate((\"7/10\"), (\"10/2\")) == (false));\n    assert(candidate((\"2/10\"), (\"50/10\")) == (true));\n    assert(candidate((\"7/2\"), (\"4/2\")) == (true));\n    assert(candidate((\"11/6\"), (\"6/1\")) == (true));\n    assert(candidate((\"2/3\"), (\"5/2\")) == (false));\n    assert(candidate((\"5/2\"), (\"3/5\")) == (false));\n    assert(candidate((\"2/4\"), (\"8/4\")) == (true));\n    assert(candidate((\"2/4\"), (\"4/2\")) == (true));\n    assert(candidate((\"1/5\"), (\"5/1\")) == (true));\n    assert(candidate((\"1/5\"), (\"1/5\")) == (false));\n}\n"}
{"name": "HumanEval_78_hex_key", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key((\"AB\"))\n// (1)\n// >>> hex_key((\"1077E\"))\n// (2)\n// >>> hex_key((\"ABED1A33\"))\n// (4)\n// >>> hex_key((\"123456789ABCDEF0\"))\n// (6)\n// >>> hex_key((\"2020\"))\n// (2)\nlong hex_key(std::string num) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = hex_key;\n    assert(candidate((\"AB\")) == (1));\n    assert(candidate((\"1077E\")) == (2));\n    assert(candidate((\"ABED1A33\")) == (4));\n    assert(candidate((\"2020\")) == (2));\n    assert(candidate((\"123456789ABCDEF0\")) == (6));\n    assert(candidate((\"112233445566778899AABBCCDDEEFF00\")) == (12));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_78_hex_key", "test": "}\nint main() {\n    auto candidate = hex_key;\n    assert(candidate((\"AB\")) == (1));\n    assert(candidate((\"1077E\")) == (2));\n    assert(candidate((\"ABED1A33\")) == (4));\n    assert(candidate((\"2020\")) == (2));\n    assert(candidate((\"123456789ABCDEF0\")) == (6));\n    assert(candidate((\"112233445566778899AABBCCDDEEFF00\")) == (12));\n}\n"}
{"name": "HumanEval_143_words_in_sentence", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence((\"This is a test\"))\n// (\"is\")\n// Example 2:\n// >>> words_in_sentence((\"lets go for swimming\"))\n// (\"go for\")\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nstd::string words_in_sentence(std::string sentence) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = words_in_sentence;\n    assert(candidate((\"This is a test\")) == (\"is\"));\n    assert(candidate((\"lets go for swimming\")) == (\"go for\"));\n    assert(candidate((\"there is no place available here\")) == (\"there is no place\"));\n    assert(candidate((\"Hi I am Hussein\")) == (\"Hi am Hussein\"));\n    assert(candidate((\"go for it\")) == (\"go for it\"));\n    assert(candidate((\"here\")) == (\"\"));\n    assert(candidate((\"here is\")) == (\"is\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_143_words_in_sentence", "test": "}\nint main() {\n    auto candidate = words_in_sentence;\n    assert(candidate((\"This is a test\")) == (\"is\"));\n    assert(candidate((\"lets go for swimming\")) == (\"go for\"));\n    assert(candidate((\"there is no place available here\")) == (\"there is no place\"));\n    assert(candidate((\"Hi I am Hussein\")) == (\"Hi am Hussein\"));\n    assert(candidate((\"go for it\")) == (\"go for it\"));\n    assert(candidate((\"here\")) == (\"\"));\n    assert(candidate((\"here is\")) == (\"is\"));\n}\n"}
{"name": "HumanEval_111_histogram", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a string representing a space separated lowercase letters, return a map\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram((\"a b c\"))\n// (std::map<std::string,long>({{\"a\", 1}, {\"b\", 1}, {\"c\", 1}}))\n// >>> histogram((\"a b b a\"))\n// (std::map<std::string,long>({{\"a\", 2}, {\"b\", 2}}))\n// >>> histogram((\"a b c a b\"))\n// (std::map<std::string,long>({{\"a\", 2}, {\"b\", 2}}))\n// >>> histogram((\"b b b b a\"))\n// (std::map<std::string,long>({{\"b\", 4}}))\n// >>> histogram((\"\"))\n// (std::map<std::string,long>())\nstd::map<std::string,long> histogram(std::string test) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = histogram;\n    assert(candidate((\"a b b a\")) == (std::map<std::string,long>({{\"a\", 2}, {\"b\", 2}})));\n    assert(candidate((\"a b c a b\")) == (std::map<std::string,long>({{\"a\", 2}, {\"b\", 2}})));\n    assert(candidate((\"a b c d g\")) == (std::map<std::string,long>({{\"a\", 1}, {\"b\", 1}, {\"c\", 1}, {\"d\", 1}, {\"g\", 1}})));\n    assert(candidate((\"r t g\")) == (std::map<std::string,long>({{\"r\", 1}, {\"t\", 1}, {\"g\", 1}})));\n    assert(candidate((\"b b b b a\")) == (std::map<std::string,long>({{\"b\", 4}})));\n    assert(candidate((\"r t g\")) == (std::map<std::string,long>({{\"r\", 1}, {\"t\", 1}, {\"g\", 1}})));\n    assert(candidate((\"\")) == (std::map<std::string,long>()));\n    assert(candidate((\"a\")) == (std::map<std::string,long>({{\"a\", 1}})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_111_histogram", "test": "}\nint main() {\n    auto candidate = histogram;\n    assert(candidate((\"a b b a\")) == (std::map<std::string,long>({{\"a\", 2}, {\"b\", 2}})));\n    assert(candidate((\"a b c a b\")) == (std::map<std::string,long>({{\"a\", 2}, {\"b\", 2}})));\n    assert(candidate((\"a b c d g\")) == (std::map<std::string,long>({{\"a\", 1}, {\"b\", 1}, {\"c\", 1}, {\"d\", 1}, {\"g\", 1}})));\n    assert(candidate((\"r t g\")) == (std::map<std::string,long>({{\"r\", 1}, {\"t\", 1}, {\"g\", 1}})));\n    assert(candidate((\"b b b b a\")) == (std::map<std::string,long>({{\"b\", 4}})));\n    assert(candidate((\"r t g\")) == (std::map<std::string,long>({{\"r\", 1}, {\"t\", 1}, {\"g\", 1}})));\n    assert(candidate((\"\")) == (std::map<std::string,long>()));\n    assert(candidate((\"a\")) == (std::map<std::string,long>({{\"a\", 1}})));\n}\n"}
{"name": "HumanEval_87_get_row", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a 2 dimensional data, as a nested vectors,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the vector,\n// and return vector of tuples, [(x1, y1), (x2, y2) ...] such that\n// each tuple is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})})), (1))\n// (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 0), (std::tuple<long, long>)std::make_tuple(1, 4), (std::tuple<long, long>)std::make_tuple(1, 0), (std::tuple<long, long>)std::make_tuple(2, 5), (std::tuple<long, long>)std::make_tuple(2, 0)}))\n// >>> get_row((std::vector<std::vector<long>>()), (1))\n// (std::vector<std::tuple<long, long>>())\n// >>> get_row((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>(), (std::vector<long>)std::vector<long>({(long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3})})), (3))\n// (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(2, 2)}))\nstd::vector<std::tuple<long, long>> get_row(std::vector<std::vector<long>> lst, long x) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = get_row;\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})})), (1)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 0), (std::tuple<long, long>)std::make_tuple(1, 4), (std::tuple<long, long>)std::make_tuple(1, 0), (std::tuple<long, long>)std::make_tuple(2, 5), (std::tuple<long, long>)std::make_tuple(2, 0)})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6})})), (2)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 1), (std::tuple<long, long>)std::make_tuple(1, 1), (std::tuple<long, long>)std::make_tuple(2, 1), (std::tuple<long, long>)std::make_tuple(3, 1), (std::tuple<long, long>)std::make_tuple(4, 1), (std::tuple<long, long>)std::make_tuple(5, 1)})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)1, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)1, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})})), (1)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 0), (std::tuple<long, long>)std::make_tuple(1, 0), (std::tuple<long, long>)std::make_tuple(2, 1), (std::tuple<long, long>)std::make_tuple(2, 0), (std::tuple<long, long>)std::make_tuple(3, 2), (std::tuple<long, long>)std::make_tuple(3, 0), (std::tuple<long, long>)std::make_tuple(4, 3), (std::tuple<long, long>)std::make_tuple(4, 0), (std::tuple<long, long>)std::make_tuple(5, 4), (std::tuple<long, long>)std::make_tuple(5, 0), (std::tuple<long, long>)std::make_tuple(6, 5), (std::tuple<long, long>)std::make_tuple(6, 0)})));\n    assert(candidate((std::vector<std::vector<long>>()), (1)) == (std::vector<std::tuple<long, long>>()));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1})})), (2)) == (std::vector<std::tuple<long, long>>()));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>(), (std::vector<long>)std::vector<long>({(long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3})})), (3)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(2, 2)})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_87_get_row", "test": "}\nint main() {\n    auto candidate = get_row;\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})})), (1)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 0), (std::tuple<long, long>)std::make_tuple(1, 4), (std::tuple<long, long>)std::make_tuple(1, 0), (std::tuple<long, long>)std::make_tuple(2, 5), (std::tuple<long, long>)std::make_tuple(2, 0)})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6})})), (2)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 1), (std::tuple<long, long>)std::make_tuple(1, 1), (std::tuple<long, long>)std::make_tuple(2, 1), (std::tuple<long, long>)std::make_tuple(3, 1), (std::tuple<long, long>)std::make_tuple(4, 1), (std::tuple<long, long>)std::make_tuple(5, 1)})));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)1, (long)3, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)1, (long)4, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)1, (long)5, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)1, (long)6}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)1})})), (1)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(0, 0), (std::tuple<long, long>)std::make_tuple(1, 0), (std::tuple<long, long>)std::make_tuple(2, 1), (std::tuple<long, long>)std::make_tuple(2, 0), (std::tuple<long, long>)std::make_tuple(3, 2), (std::tuple<long, long>)std::make_tuple(3, 0), (std::tuple<long, long>)std::make_tuple(4, 3), (std::tuple<long, long>)std::make_tuple(4, 0), (std::tuple<long, long>)std::make_tuple(5, 4), (std::tuple<long, long>)std::make_tuple(5, 0), (std::tuple<long, long>)std::make_tuple(6, 5), (std::tuple<long, long>)std::make_tuple(6, 0)})));\n    assert(candidate((std::vector<std::vector<long>>()), (1)) == (std::vector<std::tuple<long, long>>()));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>({(long)1})})), (2)) == (std::vector<std::tuple<long, long>>()));\n    assert(candidate((std::vector<std::vector<long>>({(std::vector<long>)std::vector<long>(), (std::vector<long>)std::vector<long>({(long)1}), (std::vector<long>)std::vector<long>({(long)1, (long)2, (long)3})})), (3)) == (std::vector<std::tuple<long, long>>({(std::tuple<long, long>)std::make_tuple(2, 2)})));\n}\n"}
{"name": "HumanEval_123_get_odd_collatz", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer n, return a sorted vector that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned vector sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz((5))\n// (std::vector<long>({(long)1, (long)5}))\nstd::vector<long> get_odd_collatz(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = get_odd_collatz;\n    assert(candidate((14)) == (std::vector<long>({(long)1, (long)5, (long)7, (long)11, (long)13, (long)17})));\n    assert(candidate((5)) == (std::vector<long>({(long)1, (long)5})));\n    assert(candidate((12)) == (std::vector<long>({(long)1, (long)3, (long)5})));\n    assert(candidate((1)) == (std::vector<long>({(long)1})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_123_get_odd_collatz", "test": "}\nint main() {\n    auto candidate = get_odd_collatz;\n    assert(candidate((14)) == (std::vector<long>({(long)1, (long)5, (long)7, (long)11, (long)13, (long)17})));\n    assert(candidate((5)) == (std::vector<long>({(long)1, (long)5})));\n    assert(candidate((12)) == (std::vector<long>({(long)1, (long)3, (long)5})));\n    assert(candidate((1)) == (std::vector<long>({(long)1})));\n}\n"}
{"name": "HumanEval_135_can_arrange", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given vector will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange((std::vector<long>({(long)1, (long)2, (long)4, (long)3, (long)5})))\n// (3)\n// >>> can_arrange((std::vector<long>({(long)1, (long)2, (long)3})))\n// (-1)\nlong can_arrange(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = can_arrange;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)3, (long)5}))) == (3));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)5}))) == (-1));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)2, (long)5, (long)6, (long)7, (long)8, (long)9, (long)10}))) == (2));\n    assert(candidate((std::vector<long>({(long)4, (long)8, (long)5, (long)7, (long)3}))) == (4));\n    assert(candidate((std::vector<long>())) == (-1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_135_can_arrange", "test": "}\nint main() {\n    auto candidate = can_arrange;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)3, (long)5}))) == (3));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)5}))) == (-1));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)2, (long)5, (long)6, (long)7, (long)8, (long)9, (long)10}))) == (2));\n    assert(candidate((std::vector<long>({(long)4, (long)8, (long)5, (long)7, (long)3}))) == (4));\n    assert(candidate((std::vector<long>())) == (-1));\n}\n"}
{"name": "HumanEval_19_sort_numbers", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers((\"three one five\"))\n// (\"one three five\")\nstd::string sort_numbers(std::string numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sort_numbers;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"three\")) == (\"three\"));\n    assert(candidate((\"three five nine\")) == (\"three five nine\"));\n    assert(candidate((\"five zero four seven nine eight\")) == (\"zero four five seven eight nine\"));\n    assert(candidate((\"six five four three two one zero\")) == (\"zero one two three four five six\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_19_sort_numbers", "test": "}\nint main() {\n    auto candidate = sort_numbers;\n    assert(candidate((\"\")) == (\"\"));\n    assert(candidate((\"three\")) == (\"three\"));\n    assert(candidate((\"three five nine\")) == (\"three five nine\"));\n    assert(candidate((\"five zero four seven nine eight\")) == (\"zero four five seven eight nine\"));\n    assert(candidate((\"six five four three two one zero\")) == (\"zero one two three four five six\"));\n}\n"}
{"name": "HumanEval_65_circular_shift", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift((12), (1))\n// (\"21\")\n// >>> circular_shift((12), (2))\n// (\"12\")\nstd::string circular_shift(long x, long shift) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = circular_shift;\n    assert(candidate((100), (2)) == (\"001\"));\n    assert(candidate((12), (2)) == (\"12\"));\n    assert(candidate((97), (8)) == (\"79\"));\n    assert(candidate((12), (1)) == (\"21\"));\n    assert(candidate((11), (101)) == (\"11\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_65_circular_shift", "test": "}\nint main() {\n    auto candidate = circular_shift;\n    assert(candidate((100), (2)) == (\"001\"));\n    assert(candidate((12), (2)) == (\"12\"));\n    assert(candidate((97), (8)) == (\"79\"));\n    assert(candidate((12), (1)) == (\"21\"));\n    assert(candidate((11), (101)) == (\"11\"));\n}\n"}
{"name": "HumanEval_142_sum_squares", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// \"\n// This function will take a vector of integers. For all entries in the vector, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the vector whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// (long({(long)1, (long)2, (long)3}))\n// >>> lst\n// (long())\n// >>> lst\n// (long({(long)-1, (long)-5, (long)2, (long)-1, (long)-5}))\nlong sum_squares(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sum_squares;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (6));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)9}))) == (14));\n    assert(candidate((std::vector<long>())) == (0));\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1, (long)1, (long)1, (long)1, (long)1, (long)1, (long)1}))) == (9));\n    assert(candidate((std::vector<long>({(long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1}))) == (-3));\n    assert(candidate((std::vector<long>({(long)0}))) == (0));\n    assert(candidate((std::vector<long>({(long)-1, (long)-5, (long)2, (long)-1, (long)-5}))) == (-126));\n    assert(candidate((std::vector<long>({(long)-56, (long)-99, (long)1, (long)0, (long)-2}))) == (3030));\n    assert(candidate((std::vector<long>({(long)-1, (long)0, (long)0, (long)0, (long)0, (long)0, (long)0, (long)0, (long)-1}))) == (0));\n    assert(candidate((std::vector<long>({(long)-16, (long)-9, (long)-2, (long)36, (long)36, (long)26, (long)-20, (long)25, (long)-40, (long)20, (long)-4, (long)12, (long)-26, (long)35, (long)37}))) == (-14196));\n    assert(candidate((std::vector<long>({(long)-1, (long)-3, (long)17, (long)-1, (long)-15, (long)13, (long)-1, (long)14, (long)-14, (long)-12, (long)-5, (long)14, (long)-14, (long)6, (long)13, (long)11, (long)16, (long)16, (long)4, (long)10}))) == (-1448));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_142_sum_squares", "test": "}\nint main() {\n    auto candidate = sum_squares;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (6));\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)9}))) == (14));\n    assert(candidate((std::vector<long>())) == (0));\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1, (long)1, (long)1, (long)1, (long)1, (long)1, (long)1}))) == (9));\n    assert(candidate((std::vector<long>({(long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1, (long)-1}))) == (-3));\n    assert(candidate((std::vector<long>({(long)0}))) == (0));\n    assert(candidate((std::vector<long>({(long)-1, (long)-5, (long)2, (long)-1, (long)-5}))) == (-126));\n    assert(candidate((std::vector<long>({(long)-56, (long)-99, (long)1, (long)0, (long)-2}))) == (3030));\n    assert(candidate((std::vector<long>({(long)-1, (long)0, (long)0, (long)0, (long)0, (long)0, (long)0, (long)0, (long)-1}))) == (0));\n    assert(candidate((std::vector<long>({(long)-16, (long)-9, (long)-2, (long)36, (long)36, (long)26, (long)-20, (long)25, (long)-40, (long)20, (long)-4, (long)12, (long)-26, (long)35, (long)37}))) == (-14196));\n    assert(candidate((std::vector<long>({(long)-1, (long)-3, (long)17, (long)-1, (long)-15, (long)13, (long)-1, (long)14, (long)-14, (long)-12, (long)-5, (long)14, (long)-14, (long)6, (long)13, (long)11, (long)16, (long)16, (long)4, (long)10}))) == (-1448));\n}\n"}
{"name": "HumanEval_94_skjkasdkd", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a vector of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd((std::vector<long>({(long)0, (long)3, (long)2, (long)1, (long)3, (long)5, (long)7, (long)4, (long)5, (long)5, (long)5, (long)2, (long)181, (long)32, (long)4, (long)32, (long)3, (long)2, (long)32, (long)324, (long)4, (long)3})))\n// (10)\n// >>> skjkasdkd((std::vector<long>({(long)1, (long)0, (long)1, (long)8, (long)2, (long)4597, (long)2, (long)1, (long)3, (long)40, (long)1, (long)2, (long)1, (long)2, (long)4, (long)2, (long)5, (long)1})))\n// (25)\n// >>> skjkasdkd((std::vector<long>({(long)1, (long)3, (long)1, (long)32, (long)5107, (long)34, (long)83278, (long)109, (long)163, (long)23, (long)2323, (long)32, (long)30, (long)1, (long)9, (long)3})))\n// (13)\n// >>> skjkasdkd((std::vector<long>({(long)0, (long)724, (long)32, (long)71, (long)99, (long)32, (long)6, (long)0, (long)5, (long)91, (long)83, (long)0, (long)5, (long)6})))\n// (11)\n// >>> skjkasdkd((std::vector<long>({(long)0, (long)81, (long)12, (long)3, (long)1, (long)21})))\n// (3)\n// >>> skjkasdkd((std::vector<long>({(long)0, (long)8, (long)1, (long)2, (long)1, (long)7})))\n// (7)\nlong skjkasdkd(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = skjkasdkd;\n    assert(candidate((std::vector<long>({(long)0, (long)3, (long)2, (long)1, (long)3, (long)5, (long)7, (long)4, (long)5, (long)5, (long)5, (long)2, (long)181, (long)32, (long)4, (long)32, (long)3, (long)2, (long)32, (long)324, (long)4, (long)3}))) == (10));\n    assert(candidate((std::vector<long>({(long)1, (long)0, (long)1, (long)8, (long)2, (long)4597, (long)2, (long)1, (long)3, (long)40, (long)1, (long)2, (long)1, (long)2, (long)4, (long)2, (long)5, (long)1}))) == (25));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)1, (long)32, (long)5107, (long)34, (long)83278, (long)109, (long)163, (long)23, (long)2323, (long)32, (long)30, (long)1, (long)9, (long)3}))) == (13));\n    assert(candidate((std::vector<long>({(long)0, (long)724, (long)32, (long)71, (long)99, (long)32, (long)6, (long)0, (long)5, (long)91, (long)83, (long)0, (long)5, (long)6}))) == (11));\n    assert(candidate((std::vector<long>({(long)0, (long)81, (long)12, (long)3, (long)1, (long)21}))) == (3));\n    assert(candidate((std::vector<long>({(long)0, (long)8, (long)1, (long)2, (long)1, (long)7}))) == (7));\n    assert(candidate((std::vector<long>({(long)8191}))) == (19));\n    assert(candidate((std::vector<long>({(long)8191, (long)123456, (long)127, (long)7}))) == (19));\n    assert(candidate((std::vector<long>({(long)127, (long)97, (long)8192}))) == (10));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_94_skjkasdkd", "test": "}\nint main() {\n    auto candidate = skjkasdkd;\n    assert(candidate((std::vector<long>({(long)0, (long)3, (long)2, (long)1, (long)3, (long)5, (long)7, (long)4, (long)5, (long)5, (long)5, (long)2, (long)181, (long)32, (long)4, (long)32, (long)3, (long)2, (long)32, (long)324, (long)4, (long)3}))) == (10));\n    assert(candidate((std::vector<long>({(long)1, (long)0, (long)1, (long)8, (long)2, (long)4597, (long)2, (long)1, (long)3, (long)40, (long)1, (long)2, (long)1, (long)2, (long)4, (long)2, (long)5, (long)1}))) == (25));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)1, (long)32, (long)5107, (long)34, (long)83278, (long)109, (long)163, (long)23, (long)2323, (long)32, (long)30, (long)1, (long)9, (long)3}))) == (13));\n    assert(candidate((std::vector<long>({(long)0, (long)724, (long)32, (long)71, (long)99, (long)32, (long)6, (long)0, (long)5, (long)91, (long)83, (long)0, (long)5, (long)6}))) == (11));\n    assert(candidate((std::vector<long>({(long)0, (long)81, (long)12, (long)3, (long)1, (long)21}))) == (3));\n    assert(candidate((std::vector<long>({(long)0, (long)8, (long)1, (long)2, (long)1, (long)7}))) == (7));\n    assert(candidate((std::vector<long>({(long)8191}))) == (19));\n    assert(candidate((std::vector<long>({(long)8191, (long)123456, (long)127, (long)7}))) == (19));\n    assert(candidate((std::vector<long>({(long)127, (long)97, (long)8192}))) == (10));\n}\n"}
{"name": "HumanEval_8_sum_product", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// For a given vector of integers, return a tuple consisting of a sum and a product of all the integers in a vector.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product((std::vector<long>()))\n// (std::make_tuple(0, 1))\n// >>> sum_product((std::vector<long>({(long)1, (long)2, (long)3, (long)4})))\n// (std::make_tuple(10, 24))\nstd::tuple<long, long> sum_product(std::vector<long> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sum_product;\n    assert(candidate((std::vector<long>())) == (std::make_tuple(0, 1)));\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1}))) == (std::make_tuple(3, 1)));\n    assert(candidate((std::vector<long>({(long)100, (long)0}))) == (std::make_tuple(100, 0)));\n    assert(candidate((std::vector<long>({(long)3, (long)5, (long)7}))) == (std::make_tuple(15, 105)));\n    assert(candidate((std::vector<long>({(long)10}))) == (std::make_tuple(10, 10)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_8_sum_product", "test": "}\nint main() {\n    auto candidate = sum_product;\n    assert(candidate((std::vector<long>())) == (std::make_tuple(0, 1)));\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1}))) == (std::make_tuple(3, 1)));\n    assert(candidate((std::vector<long>({(long)100, (long)0}))) == (std::make_tuple(100, 0)));\n    assert(candidate((std::vector<long>({(long)3, (long)5, (long)7}))) == (std::make_tuple(15, 105)));\n    assert(candidate((std::vector<long>({(long)10}))) == (std::make_tuple(10, 10)));\n}\n"}
{"name": "HumanEval_102_choose_num", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num((12), (15))\n// (14)\n// >>> choose_num((13), (12))\n// (-1)\nlong choose_num(long x, long y) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = choose_num;\n    assert(candidate((12), (15)) == (14));\n    assert(candidate((13), (12)) == (-1));\n    assert(candidate((33), (12354)) == (12354));\n    assert(candidate((5234), (5233)) == (-1));\n    assert(candidate((6), (29)) == (28));\n    assert(candidate((27), (10)) == (-1));\n    assert(candidate((7), (7)) == (-1));\n    assert(candidate((546), (546)) == (546));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_102_choose_num", "test": "}\nint main() {\n    auto candidate = choose_num;\n    assert(candidate((12), (15)) == (14));\n    assert(candidate((13), (12)) == (-1));\n    assert(candidate((33), (12354)) == (12354));\n    assert(candidate((5234), (5233)) == (-1));\n    assert(candidate((6), (29)) == (28));\n    assert(candidate((27), (10)) == (-1));\n    assert(candidate((7), (7)) == (-1));\n    assert(candidate((546), (546)) == (546));\n}\n"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function that returns a tuple (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in a vector.\n// If there is no negative or positive integers, return them as None.\n// Examples:\n// >>> largest_smallest_integers((std::vector<long>({(long)2, (long)4, (long)1, (long)3, (long)5, (long)7})))\n// std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(1))\n// >>> largest_smallest_integers((std::vector<long>()))\n// std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(std::nullopt))\n// >>> largest_smallest_integers((std::vector<long>({(long)0})))\n// std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(std::nullopt))\nstd::tuple<std::optional<long>, std::optional<long>> largest_smallest_integers(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = largest_smallest_integers;\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)1, (long)3, (long)5, (long)7}))) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(1)));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)1, (long)3, (long)5, (long)7, (long)0}))) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(1)));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5, (long)6, (long)-2}))) == std::make_tuple(-2, 1));\n    assert(candidate((std::vector<long>({(long)4, (long)5, (long)3, (long)6, (long)2, (long)7, (long)-7}))) == std::make_tuple(-7, 2));\n    assert(candidate((std::vector<long>({(long)7, (long)3, (long)8, (long)4, (long)9, (long)2, (long)5, (long)-9}))) == std::make_tuple(-9, 2));\n    assert(candidate((std::vector<long>())) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)0}))) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)-1, (long)-3, (long)-5, (long)-6}))) == std::make_tuple(std::optional<long>(-1), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)-1, (long)-3, (long)-5, (long)-6, (long)0}))) == std::make_tuple(std::optional<long>(-1), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)-6, (long)-4, (long)-4, (long)-3, (long)1}))) == std::make_tuple(-3, 1));\n    assert(candidate((std::vector<long>({(long)-6, (long)-4, (long)-4, (long)-3, (long)-100, (long)1}))) == std::make_tuple(-3, 1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "}\nint main() {\n    auto candidate = largest_smallest_integers;\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)1, (long)3, (long)5, (long)7}))) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(1)));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)1, (long)3, (long)5, (long)7, (long)0}))) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(1)));\n    assert(candidate((std::vector<long>({(long)1, (long)3, (long)2, (long)4, (long)5, (long)6, (long)-2}))) == std::make_tuple(-2, 1));\n    assert(candidate((std::vector<long>({(long)4, (long)5, (long)3, (long)6, (long)2, (long)7, (long)-7}))) == std::make_tuple(-7, 2));\n    assert(candidate((std::vector<long>({(long)7, (long)3, (long)8, (long)4, (long)9, (long)2, (long)5, (long)-9}))) == std::make_tuple(-9, 2));\n    assert(candidate((std::vector<long>())) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)0}))) == std::make_tuple(std::optional<long>(std::nullopt), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)-1, (long)-3, (long)-5, (long)-6}))) == std::make_tuple(std::optional<long>(-1), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)-1, (long)-3, (long)-5, (long)-6, (long)0}))) == std::make_tuple(std::optional<long>(-1), std::optional<long>(std::nullopt)));\n    assert(candidate((std::vector<long>({(long)-6, (long)-4, (long)-4, (long)-3, (long)1}))) == std::make_tuple(-3, 1));\n    assert(candidate((std::vector<long>({(long)-6, (long)-4, (long)-4, (long)-3, (long)-100, (long)1}))) == std::make_tuple(-3, 1));\n}\n"}
{"name": "HumanEval_16_count_distinct_characters", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters((\"xyzXYZ\"))\n// (3)\n// >>> count_distinct_characters((\"Jerry\"))\n// (4)\nlong count_distinct_characters(std::string string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = count_distinct_characters;\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"abcde\")) == (5));\n    assert(candidate((\"abcdecadeCADE\")) == (5));\n    assert(candidate((\"aaaaAAAAaaaa\")) == (1));\n    assert(candidate((\"Jerry jERRY JeRRRY\")) == (5));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_16_count_distinct_characters", "test": "}\nint main() {\n    auto candidate = count_distinct_characters;\n    assert(candidate((\"\")) == (0));\n    assert(candidate((\"abcde\")) == (5));\n    assert(candidate((\"abcdecadeCADE\")) == (5));\n    assert(candidate((\"aaaaAAAAaaaa\")) == (1));\n    assert(candidate((\"Jerry jERRY JeRRRY\")) == (5));\n}\n"}
{"name": "HumanEval_100_make_a_pile", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in a vector, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile((3))\n// (std::vector<long>({(long)3, (long)5, (long)7}))\nstd::vector<long> make_a_pile(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = make_a_pile;\n    assert(candidate((3)) == (std::vector<long>({(long)3, (long)5, (long)7})));\n    assert(candidate((4)) == (std::vector<long>({(long)4, (long)6, (long)8, (long)10})));\n    assert(candidate((5)) == (std::vector<long>({(long)5, (long)7, (long)9, (long)11, (long)13})));\n    assert(candidate((6)) == (std::vector<long>({(long)6, (long)8, (long)10, (long)12, (long)14, (long)16})));\n    assert(candidate((8)) == (std::vector<long>({(long)8, (long)10, (long)12, (long)14, (long)16, (long)18, (long)20, (long)22})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_100_make_a_pile", "test": "}\nint main() {\n    auto candidate = make_a_pile;\n    assert(candidate((3)) == (std::vector<long>({(long)3, (long)5, (long)7})));\n    assert(candidate((4)) == (std::vector<long>({(long)4, (long)6, (long)8, (long)10})));\n    assert(candidate((5)) == (std::vector<long>({(long)5, (long)7, (long)9, (long)11, (long)13})));\n    assert(candidate((6)) == (std::vector<long>({(long)6, (long)8, (long)10, (long)12, (long)14, (long)16})));\n    assert(candidate((8)) == (std::vector<long>({(long)8, (long)10, (long)12, (long)14, (long)16, (long)18, (long)20, (long)22})));\n}\n"}
{"name": "HumanEval_128_prod_signs", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a vector arr of integers and you need to return\n// sum of magnitudes of integers multiplied by product of all signs\n// of each number in the vector, represented by 1, -1 or 0.\n// Note: return None for empty arr.\n// Example:\n// >>> prod_signs((std::vector<long>({(long)1, (long)2, (long)2, (long)-4})))\n// 9\n// >>> prod_signs((std::vector<long>({(long)0, (long)1})))\n// 0\n// >>> prod_signs((std::vector<long>()))\n// std::nullopt\nstd::optional<long> prod_signs(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = prod_signs;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)2, (long)-4}))) == -9);\n    assert(candidate((std::vector<long>({(long)0, (long)1}))) == 0);\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1, (long)2, (long)3, (long)-1, (long)1}))) == -10);\n    assert(candidate((std::vector<long>())) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)1, (long)2, (long)-1, (long)-1, (long)9}))) == 20);\n    assert(candidate((std::vector<long>({(long)-1, (long)1, (long)-1, (long)1}))) == 4);\n    assert(candidate((std::vector<long>({(long)-1, (long)1, (long)1, (long)1}))) == -4);\n    assert(candidate((std::vector<long>({(long)-1, (long)1, (long)1, (long)0}))) == 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_128_prod_signs", "test": "}\nint main() {\n    auto candidate = prod_signs;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)2, (long)-4}))) == -9);\n    assert(candidate((std::vector<long>({(long)0, (long)1}))) == 0);\n    assert(candidate((std::vector<long>({(long)1, (long)1, (long)1, (long)2, (long)3, (long)-1, (long)1}))) == -10);\n    assert(candidate((std::vector<long>())) == std::nullopt);\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)1, (long)2, (long)-1, (long)-1, (long)9}))) == 20);\n    assert(candidate((std::vector<long>({(long)-1, (long)1, (long)-1, (long)1}))) == 4);\n    assert(candidate((std::vector<long>({(long)-1, (long)1, (long)1, (long)1}))) == -4);\n    assert(candidate((std::vector<long>({(long)-1, (long)1, (long)1, (long)0}))) == 0);\n}\n"}
{"name": "HumanEval_114_minSubArraySum", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a vector of integers nums, find the minimum sum of any non-empty sub-vector\n// of nums.\n// Example\n// >>> minSubArraySum((std::vector<long>({(long)2, (long)3, (long)4, (long)1, (long)2, (long)4})))\n// (1)\n// >>> minSubArraySum((std::vector<long>({(long)-1, (long)-2, (long)-3})))\n// (-6)\nlong minSubArraySum(std::vector<long> nums) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = minSubArraySum;\n    assert(candidate((std::vector<long>({(long)2, (long)3, (long)4, (long)1, (long)2, (long)4}))) == (1));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)-3}))) == (-6));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)-3, (long)2, (long)-10}))) == (-14));\n    assert(candidate((std::vector<long>({(long)-9999999999999999}))) == (-9999999999999999));\n    assert(candidate((std::vector<long>({(long)0, (long)10, (long)20, (long)1000000}))) == (0));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)-3, (long)10, (long)-5}))) == (-6));\n    assert(candidate((std::vector<long>({(long)100, (long)-1, (long)-2, (long)-3, (long)10, (long)-5}))) == (-6));\n    assert(candidate((std::vector<long>({(long)10, (long)11, (long)13, (long)8, (long)3, (long)4}))) == (3));\n    assert(candidate((std::vector<long>({(long)100, (long)-33, (long)32, (long)-1, (long)0, (long)-2}))) == (-33));\n    assert(candidate((std::vector<long>({(long)-10}))) == (-10));\n    assert(candidate((std::vector<long>({(long)7}))) == (7));\n    assert(candidate((std::vector<long>({(long)1, (long)-1}))) == (-1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_114_minSubArraySum", "test": "}\nint main() {\n    auto candidate = minSubArraySum;\n    assert(candidate((std::vector<long>({(long)2, (long)3, (long)4, (long)1, (long)2, (long)4}))) == (1));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)-3}))) == (-6));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)-3, (long)2, (long)-10}))) == (-14));\n    assert(candidate((std::vector<long>({(long)-9999999999999999}))) == (-9999999999999999));\n    assert(candidate((std::vector<long>({(long)0, (long)10, (long)20, (long)1000000}))) == (0));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)-3, (long)10, (long)-5}))) == (-6));\n    assert(candidate((std::vector<long>({(long)100, (long)-1, (long)-2, (long)-3, (long)10, (long)-5}))) == (-6));\n    assert(candidate((std::vector<long>({(long)10, (long)11, (long)13, (long)8, (long)3, (long)4}))) == (3));\n    assert(candidate((std::vector<long>({(long)100, (long)-33, (long)32, (long)-1, (long)0, (long)-2}))) == (-33));\n    assert(candidate((std::vector<long>({(long)-10}))) == (-10));\n    assert(candidate((std::vector<long>({(long)7}))) == (7));\n    assert(candidate((std::vector<long>({(long)1, (long)-1}))) == (-1));\n}\n"}
{"name": "HumanEval_15_string_sequence", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence((0))\n// (\"0\")\n// >>> string_sequence((5))\n// (\"0 1 2 3 4 5\")\nstd::string string_sequence(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = string_sequence;\n    assert(candidate((0)) == (\"0\"));\n    assert(candidate((3)) == (\"0 1 2 3\"));\n    assert(candidate((10)) == (\"0 1 2 3 4 5 6 7 8 9 10\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_15_string_sequence", "test": "}\nint main() {\n    auto candidate = string_sequence;\n    assert(candidate((0)) == (\"0\"));\n    assert(candidate((3)) == (\"0 1 2 3\"));\n    assert(candidate((10)) == (\"0 1 2 3 4 5 6 7 8 9 10\"));\n}\n"}
{"name": "HumanEval_154_cycpattern_check", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check((\"abcd\"), (\"abd\"))\n// (false)\n// >>> cycpattern_check((\"hello\"), (\"ell\"))\n// (true)\n// >>> cycpattern_check((\"whassup\"), (\"psus\"))\n// (false)\n// >>> cycpattern_check((\"abab\"), (\"baa\"))\n// (true)\n// >>> cycpattern_check((\"efef\"), (\"eeff\"))\n// (false)\n// >>> cycpattern_check((\"himenss\"), (\"simen\"))\n// (true)\nbool cycpattern_check(std::string a, std::string b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = cycpattern_check;\n    assert(candidate((\"xyzw\"), (\"xyw\")) == (false));\n    assert(candidate((\"yello\"), (\"ell\")) == (true));\n    assert(candidate((\"whattup\"), (\"ptut\")) == (false));\n    assert(candidate((\"efef\"), (\"fee\")) == (true));\n    assert(candidate((\"abab\"), (\"aabb\")) == (false));\n    assert(candidate((\"winemtt\"), (\"tinem\")) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_154_cycpattern_check", "test": "}\nint main() {\n    auto candidate = cycpattern_check;\n    assert(candidate((\"xyzw\"), (\"xyw\")) == (false));\n    assert(candidate((\"yello\"), (\"ell\")) == (true));\n    assert(candidate((\"whattup\"), (\"ptut\")) == (false));\n    assert(candidate((\"efef\"), (\"fee\")) == (true));\n    assert(candidate((\"abab\"), (\"aabb\")) == (false));\n    assert(candidate((\"winemtt\"), (\"tinem\")) == (true));\n}\n"}
{"name": "HumanEval_57_monotonic", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return true is vector elements are monotonically increasing or decreasing.\n// >>> monotonic((std::vector<long>({(long)1, (long)2, (long)4, (long)20})))\n// (true)\n// >>> monotonic((std::vector<long>({(long)1, (long)20, (long)4, (long)10})))\n// (false)\n// >>> monotonic((std::vector<long>({(long)4, (long)1, (long)0, (long)-10})))\n// (true)\nbool monotonic(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = monotonic;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)10}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)20}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10}))) == (false));\n    assert(candidate((std::vector<long>({(long)4, (long)1, (long)0, (long)-10}))) == (true));\n    assert(candidate((std::vector<long>({(long)4, (long)1, (long)1, (long)0}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)5, (long)60}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)60}))) == (true));\n    assert(candidate((std::vector<long>({(long)9, (long)9, (long)9, (long)9}))) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_57_monotonic", "test": "}\nint main() {\n    auto candidate = monotonic;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)10}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)20}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10}))) == (false));\n    assert(candidate((std::vector<long>({(long)4, (long)1, (long)0, (long)-10}))) == (true));\n    assert(candidate((std::vector<long>({(long)4, (long)1, (long)1, (long)0}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)5, (long)60}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5, (long)60}))) == (true));\n    assert(candidate((std::vector<long>({(long)9, (long)9, (long)9, (long)9}))) == (true));\n}\n"}
{"name": "HumanEval_12_longest", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Out of vector of strings, return the longest one. Return the first one in case of multiple\n// strings of the same length. Return None in case the input vector is empty.\n// >>> longest((std::vector<std::string>()))\n// std::nullopt\n// >>> longest((std::vector<std::string>({(std::string)\"a\", (std::string)\"b\", (std::string)\"c\"})))\n// \"a\"\n// >>> longest((std::vector<std::string>({(std::string)\"a\", (std::string)\"bb\", (std::string)\"ccc\"})))\n// \"ccc\"\nstd::optional<std::string> longest(std::vector<std::string> strings) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = longest;\n    assert(candidate((std::vector<std::string>())) == std::nullopt);\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"y\", (std::string)\"z\"}))) == \"x\");\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"yyy\", (std::string)\"zzzz\", (std::string)\"www\", (std::string)\"kkkk\", (std::string)\"abc\"}))) == \"zzzz\");\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_12_longest", "test": "}\nint main() {\n    auto candidate = longest;\n    assert(candidate((std::vector<std::string>())) == std::nullopt);\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"y\", (std::string)\"z\"}))) == \"x\");\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"yyy\", (std::string)\"zzzz\", (std::string)\"www\", (std::string)\"kkkk\", (std::string)\"abc\"}))) == \"zzzz\");\n}\n"}
{"name": "HumanEval_52_below_threshold", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return true if all numbers in the vector l are below threshold t.\n// >>> below_threshold((std::vector<long>({(long)1, (long)2, (long)4, (long)10})), (100))\n// (true)\n// >>> below_threshold((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (5))\n// (false)\nbool below_threshold(std::vector<long> l, long t) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = below_threshold;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)10})), (100)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (5)) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (21)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (22)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)8, (long)4, (long)10})), (11)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)8, (long)4, (long)10})), (10)) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_52_below_threshold", "test": "}\nint main() {\n    auto candidate = below_threshold;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)4, (long)10})), (100)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (5)) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (21)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)20, (long)4, (long)10})), (22)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)8, (long)4, (long)10})), (11)) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)8, (long)4, (long)10})), (10)) == (false));\n}\n"}
{"name": "HumanEval_75_is_multiply_prime", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime((30))\n// (true)\n// 30 = 2 * 3 * 5\nbool is_multiply_prime(long a) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = is_multiply_prime;\n    assert(candidate((5)) == (false));\n    assert(candidate((30)) == (true));\n    assert(candidate((8)) == (true));\n    assert(candidate((10)) == (false));\n    assert(candidate((125)) == (true));\n    assert(candidate((105)) == (true));\n    assert(candidate((126)) == (false));\n    assert(candidate((729)) == (false));\n    assert(candidate((891)) == (false));\n    assert(candidate((1001)) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_75_is_multiply_prime", "test": "}\nint main() {\n    auto candidate = is_multiply_prime;\n    assert(candidate((5)) == (false));\n    assert(candidate((30)) == (true));\n    assert(candidate((8)) == (true));\n    assert(candidate((10)) == (false));\n    assert(candidate((125)) == (true));\n    assert(candidate((105)) == (true));\n    assert(candidate((126)) == (false));\n    assert(candidate((729)) == (false));\n    assert(candidate((891)) == (false));\n    assert(candidate((1001)) == (true));\n}\n"}
{"name": "HumanEval_30_get_positive", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return only positive numbers in the vector.\n// >>> get_positive((std::vector<long>({(long)-1, (long)2, (long)-4, (long)5, (long)6})))\n// (std::vector<long>({(long)2, (long)5, (long)6}))\n// >>> get_positive((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)9, (long)0, (long)123, (long)1, (long)-10})))\n// (std::vector<long>({(long)5, (long)3, (long)2, (long)3, (long)9, (long)123, (long)1}))\nstd::vector<long> get_positive(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = get_positive;\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)4, (long)5, (long)6}))) == (std::vector<long>({(long)4, (long)5, (long)6})));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123, (long)1, (long)-10}))) == (std::vector<long>({(long)5, (long)3, (long)2, (long)3, (long)3, (long)9, (long)123, (long)1})));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2}))) == (std::vector<long>()));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_30_get_positive", "test": "}\nint main() {\n    auto candidate = get_positive;\n    assert(candidate((std::vector<long>({(long)-1, (long)-2, (long)4, (long)5, (long)6}))) == (std::vector<long>({(long)4, (long)5, (long)6})));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)3, (long)3, (long)9, (long)0, (long)123, (long)1, (long)-10}))) == (std::vector<long>({(long)5, (long)3, (long)2, (long)3, (long)3, (long)9, (long)123, (long)1})));\n    assert(candidate((std::vector<long>({(long)-1, (long)-2}))) == (std::vector<long>()));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n}\n"}
{"name": "HumanEval_33_sort_third", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// This function takes a vector l and returns a vector l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third((std::vector<long>({(long)1, (long)2, (long)3})))\n// (std::vector<long>({(long)1, (long)2, (long)3}))\n// >>> sort_third((std::vector<long>({(long)5, (long)6, (long)3, (long)4, (long)8, (long)9, (long)2})))\n// (std::vector<long>({(long)2, (long)6, (long)3, (long)4, (long)8, (long)9, (long)5}))\nstd::vector<long> sort_third(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sort_third;\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)3, (long)4, (long)8, (long)9, (long)2}))) == (std::vector<long>({(long)2, (long)6, (long)3, (long)4, (long)8, (long)9, (long)5})));\n    assert(candidate((std::vector<long>({(long)5, (long)8, (long)3, (long)4, (long)6, (long)9, (long)2}))) == (std::vector<long>({(long)2, (long)8, (long)3, (long)4, (long)6, (long)9, (long)5})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)9, (long)4, (long)8, (long)3, (long)2}))) == (std::vector<long>({(long)2, (long)6, (long)9, (long)4, (long)8, (long)3, (long)5})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)3, (long)4, (long)8, (long)9, (long)2, (long)1}))) == (std::vector<long>({(long)2, (long)6, (long)3, (long)4, (long)8, (long)9, (long)5, (long)1})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_33_sort_third", "test": "}\nint main() {\n    auto candidate = sort_third;\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)3, (long)4, (long)8, (long)9, (long)2}))) == (std::vector<long>({(long)2, (long)6, (long)3, (long)4, (long)8, (long)9, (long)5})));\n    assert(candidate((std::vector<long>({(long)5, (long)8, (long)3, (long)4, (long)6, (long)9, (long)2}))) == (std::vector<long>({(long)2, (long)8, (long)3, (long)4, (long)6, (long)9, (long)5})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)9, (long)4, (long)8, (long)3, (long)2}))) == (std::vector<long>({(long)2, (long)6, (long)9, (long)4, (long)8, (long)3, (long)5})));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)3, (long)4, (long)8, (long)9, (long)2, (long)1}))) == (std::vector<long>({(long)2, (long)6, (long)3, (long)4, (long)8, (long)9, (long)5, (long)1})));\n}\n"}
{"name": "HumanEval_6_parse_nested_parens", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens((\"(()()) ((())) () ((())()())\"))\n// (std::vector<long>({(long)2, (long)3, (long)1, (long)3}))\nstd::vector<long> parse_nested_parens(std::string paren_string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = parse_nested_parens;\n    assert(candidate((\"(()()) ((())) () ((())()())\")) == (std::vector<long>({(long)2, (long)3, (long)1, (long)3})));\n    assert(candidate((\"() (()) ((())) (((())))\")) == (std::vector<long>({(long)1, (long)2, (long)3, (long)4})));\n    assert(candidate((\"(()(())((())))\")) == (std::vector<long>({(long)4})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_6_parse_nested_parens", "test": "}\nint main() {\n    auto candidate = parse_nested_parens;\n    assert(candidate((\"(()()) ((())) () ((())()())\")) == (std::vector<long>({(long)2, (long)3, (long)1, (long)3})));\n    assert(candidate((\"() (()) ((())) (((())))\")) == (std::vector<long>({(long)1, (long)2, (long)3, (long)4})));\n    assert(candidate((\"(()(())((())))\")) == (std::vector<long>({(long)4})));\n}\n"}
{"name": "HumanEval_45_triangle_area", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given length of a side and high return area for a triangle.\n// >>> triangle_area((5), (3))\n// (7.5f)\nfloat triangle_area(long a, long h) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = triangle_area;\n    assert(candidate((5), (3)) == (7.5f));\n    assert(candidate((2), (2)) == (2.0f));\n    assert(candidate((10), (8)) == (40.0f));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_45_triangle_area", "test": "}\nint main() {\n    auto candidate = triangle_area;\n    assert(candidate((5), (3)) == (7.5f));\n    assert(candidate((2), (2)) == (2.0f));\n    assert(candidate((10), (8)) == (40.0f));\n}\n"}
{"name": "HumanEval_97_multiply", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply((148), (412))\n// (16)\n// >>> multiply((19), (28))\n// (72)\n// >>> multiply((2020), (1851))\n// (0)\n// >>> multiply((14), (-15))\n// (20)\nlong multiply(long a, long b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = multiply;\n    assert(candidate((148), (412)) == (16));\n    assert(candidate((19), (28)) == (72));\n    assert(candidate((2020), (1851)) == (0));\n    assert(candidate((14), (-15)) == (20));\n    assert(candidate((76), (67)) == (42));\n    assert(candidate((17), (27)) == (49));\n    assert(candidate((0), (1)) == (0));\n    assert(candidate((0), (0)) == (0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_97_multiply", "test": "}\nint main() {\n    auto candidate = multiply;\n    assert(candidate((148), (412)) == (16));\n    assert(candidate((19), (28)) == (72));\n    assert(candidate((2020), (1851)) == (0));\n    assert(candidate((14), (-15)) == (20));\n    assert(candidate((76), (67)) == (42));\n    assert(candidate((17), (27)) == (49));\n    assert(candidate((0), (1)) == (0));\n    assert(candidate((0), (0)) == (0));\n}\n"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// For a given vector of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f})))\n// (1.0f)\nfloat mean_absolute_deviation(std::vector<float> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = mean_absolute_deviation;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f}))) == (0.5f));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f}))) == (1.0f));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f}))) == (1.2f));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "}\nint main() {\n    auto candidate = mean_absolute_deviation;\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f}))) == (0.5f));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f}))) == (1.0f));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f}))) == (1.2f));\n}\n"}
{"name": "HumanEval_58_common", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return sorted unique common elements for two vectors.\n// >>> common((std::vector<long>({(long)1, (long)4, (long)3, (long)34, (long)653, (long)2, (long)5})), (std::vector<long>({(long)5, (long)7, (long)1, (long)5, (long)9, (long)653, (long)121})))\n// (std::vector<long>({(long)1, (long)5, (long)653}))\n// >>> common((std::vector<long>({(long)5, (long)3, (long)2, (long)8})), (std::vector<long>({(long)3, (long)2})))\n// (std::vector<long>({(long)2, (long)3}))\nstd::vector<long> common(std::vector<long> l1, std::vector<long> l2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = common;\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)3, (long)34, (long)653, (long)2, (long)5})), (std::vector<long>({(long)5, (long)7, (long)1, (long)5, (long)9, (long)653, (long)121}))) == (std::vector<long>({(long)1, (long)5, (long)653})));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)2, (long)8})), (std::vector<long>({(long)3, (long)2}))) == (std::vector<long>({(long)2, (long)3})));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)2, (long)8})), (std::vector<long>({(long)3, (long)2, (long)4}))) == (std::vector<long>({(long)2, (long)3, (long)4})));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)2, (long)8})), (std::vector<long>())) == (std::vector<long>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_58_common", "test": "}\nint main() {\n    auto candidate = common;\n    assert(candidate((std::vector<long>({(long)1, (long)4, (long)3, (long)34, (long)653, (long)2, (long)5})), (std::vector<long>({(long)5, (long)7, (long)1, (long)5, (long)9, (long)653, (long)121}))) == (std::vector<long>({(long)1, (long)5, (long)653})));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)2, (long)8})), (std::vector<long>({(long)3, (long)2}))) == (std::vector<long>({(long)2, (long)3})));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)2, (long)8})), (std::vector<long>({(long)3, (long)2, (long)4}))) == (std::vector<long>({(long)2, (long)3, (long)4})));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)2, (long)8})), (std::vector<long>())) == (std::vector<long>()));\n}\n"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman((19))\n// (\"xix\")\n// >>> int_to_mini_roman((152))\n// (\"clii\")\n// >>> int_to_mini_roman((426))\n// (\"cdxxvi\")\nstd::string int_to_mini_roman(long number) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = int_to_mini_roman;\n    assert(candidate((19)) == (\"xix\"));\n    assert(candidate((152)) == (\"clii\"));\n    assert(candidate((251)) == (\"ccli\"));\n    assert(candidate((426)) == (\"cdxxvi\"));\n    assert(candidate((500)) == (\"d\"));\n    assert(candidate((1)) == (\"i\"));\n    assert(candidate((4)) == (\"iv\"));\n    assert(candidate((43)) == (\"xliii\"));\n    assert(candidate((90)) == (\"xc\"));\n    assert(candidate((94)) == (\"xciv\"));\n    assert(candidate((532)) == (\"dxxxii\"));\n    assert(candidate((900)) == (\"cm\"));\n    assert(candidate((994)) == (\"cmxciv\"));\n    assert(candidate((1000)) == (\"m\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "}\nint main() {\n    auto candidate = int_to_mini_roman;\n    assert(candidate((19)) == (\"xix\"));\n    assert(candidate((152)) == (\"clii\"));\n    assert(candidate((251)) == (\"ccli\"));\n    assert(candidate((426)) == (\"cdxxvi\"));\n    assert(candidate((500)) == (\"d\"));\n    assert(candidate((1)) == (\"i\"));\n    assert(candidate((4)) == (\"iv\"));\n    assert(candidate((43)) == (\"xliii\"));\n    assert(candidate((90)) == (\"xc\"));\n    assert(candidate((94)) == (\"xciv\"));\n    assert(candidate((532)) == (\"dxxxii\"));\n    assert(candidate((900)) == (\"cm\"));\n    assert(candidate((994)) == (\"cmxciv\"));\n    assert(candidate((1000)) == (\"m\"));\n}\n"}
{"name": "HumanEval_67_fruit_distribution", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution((\"5 apples and 6 oranges\"), (19))\n// (8)\n// >>> fruit_distribution((\"0 apples and 1 oranges\"), (3))\n// (2)\n// >>> fruit_distribution((\"2 apples and 3 oranges\"), (100))\n// (95)\n// >>> fruit_distribution((\"100 apples and 1 oranges\"), (120))\n// (19)\nlong fruit_distribution(std::string s, long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = fruit_distribution;\n    assert(candidate((\"5 apples and 6 oranges\"), (19)) == (8));\n    assert(candidate((\"5 apples and 6 oranges\"), (21)) == (10));\n    assert(candidate((\"0 apples and 1 oranges\"), (3)) == (2));\n    assert(candidate((\"1 apples and 0 oranges\"), (3)) == (2));\n    assert(candidate((\"2 apples and 3 oranges\"), (100)) == (95));\n    assert(candidate((\"2 apples and 3 oranges\"), (5)) == (0));\n    assert(candidate((\"1 apples and 100 oranges\"), (120)) == (19));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_67_fruit_distribution", "test": "}\nint main() {\n    auto candidate = fruit_distribution;\n    assert(candidate((\"5 apples and 6 oranges\"), (19)) == (8));\n    assert(candidate((\"5 apples and 6 oranges\"), (21)) == (10));\n    assert(candidate((\"0 apples and 1 oranges\"), (3)) == (2));\n    assert(candidate((\"1 apples and 0 oranges\"), (3)) == (2));\n    assert(candidate((\"2 apples and 3 oranges\"), (100)) == (95));\n    assert(candidate((\"2 apples and 3 oranges\"), (5)) == (0));\n    assert(candidate((\"1 apples and 100 oranges\"), (120)) == (19));\n}\n"}
{"name": "HumanEval_112_reverse_delete", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return a tuple containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete((\"abcde\"), (\"ae\"))\n// (std::make_tuple(\"bcd\", false))\n// >>> reverse_delete((\"abcdef\"), (\"b\"))\n// (std::make_tuple(\"acdef\", false))\n// >>> reverse_delete((\"abcdedcba\"), (\"ab\"))\n// (std::make_tuple(\"cdedc\", true))\nstd::tuple<std::string, bool> reverse_delete(std::string s, std::string c) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = reverse_delete;\n    assert(candidate((\"abcde\"), (\"ae\")) == (std::make_tuple(\"bcd\", false)));\n    assert(candidate((\"abcdef\"), (\"b\")) == (std::make_tuple(\"acdef\", false)));\n    assert(candidate((\"abcdedcba\"), (\"ab\")) == (std::make_tuple(\"cdedc\", true)));\n    assert(candidate((\"dwik\"), (\"w\")) == (std::make_tuple(\"dik\", false)));\n    assert(candidate((\"a\"), (\"a\")) == (std::make_tuple(\"\", true)));\n    assert(candidate((\"abcdedcba\"), (\"\")) == (std::make_tuple(\"abcdedcba\", true)));\n    assert(candidate((\"abcdedcba\"), (\"v\")) == (std::make_tuple(\"abcdedcba\", true)));\n    assert(candidate((\"vabba\"), (\"v\")) == (std::make_tuple(\"abba\", true)));\n    assert(candidate((\"mamma\"), (\"mia\")) == (std::make_tuple(\"\", true)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_112_reverse_delete", "test": "}\nint main() {\n    auto candidate = reverse_delete;\n    assert(candidate((\"abcde\"), (\"ae\")) == (std::make_tuple(\"bcd\", false)));\n    assert(candidate((\"abcdef\"), (\"b\")) == (std::make_tuple(\"acdef\", false)));\n    assert(candidate((\"abcdedcba\"), (\"ab\")) == (std::make_tuple(\"cdedc\", true)));\n    assert(candidate((\"dwik\"), (\"w\")) == (std::make_tuple(\"dik\", false)));\n    assert(candidate((\"a\"), (\"a\")) == (std::make_tuple(\"\", true)));\n    assert(candidate((\"abcdedcba\"), (\"\")) == (std::make_tuple(\"abcdedcba\", true)));\n    assert(candidate((\"abcdedcba\"), (\"v\")) == (std::make_tuple(\"abcdedcba\", true)));\n    assert(candidate((\"vabba\"), (\"v\")) == (std::make_tuple(\"abba\", true)));\n    assert(candidate((\"mamma\"), (\"mia\")) == (std::make_tuple(\"\", true)));\n}\n"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor((3), (5))\n// (1)\n// >>> greatest_common_divisor((25), (15))\n// (5)\nlong greatest_common_divisor(long a, long b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = greatest_common_divisor;\n    assert(candidate((3), (7)) == (1));\n    assert(candidate((10), (15)) == (5));\n    assert(candidate((49), (14)) == (7));\n    assert(candidate((144), (60)) == (12));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "}\nint main() {\n    auto candidate = greatest_common_divisor;\n    assert(candidate((3), (7)) == (1));\n    assert(candidate((10), (15)) == (5));\n    assert(candidate((49), (14)) == (7));\n    assert(candidate((144), (60)) == (12));\n}\n"}
{"name": "HumanEval_125_split_words", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\nunion Union_std_vector_std_string__long{\n    std::vector<std::string> f0;\n    long f1;    Union_std_vector_std_string__long(std::vector<std::string> _f0) : f0(_f0) {}\n    Union_std_vector_std_string__long(long _f1) : f1(_f1) {}\n    ~Union_std_vector_std_string__long() {}\n    bool operator==(std::vector<std::string> f) {\n        return f0 == f ;\n    }    bool operator==(long f) {\n        return f1 == f ;\n    }\n};\n// Given a string of words, return a vector of words split on whitespace, if no whitespaces exists in the text you\n// should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n// alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n// Examples\n// >>> split_words((\"Hello world!\"))\n// std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world!\"})\n// >>> split_words((\"Hello,world!\"))\n// std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world!\"})\n// >>> split_words((\"abcdef\"))\n// 3\nUnion_std_vector_std_string__long split_words(std::string txt) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_125_split_words.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = split_words;\n    assert(candidate((\"Hello world!\")) == std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world!\"}));\n    assert(candidate((\"Hello,world!\")) == std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world!\"}));\n    assert(candidate((\"Hello world,!\")) == std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world,!\"}));\n    assert(candidate((\"Hello,Hello,world !\")) == std::vector<std::string>({(std::string)\"Hello,Hello,world\", (std::string)\"!\"}));\n    assert(candidate((\"abcdef\")) == 3);\n    assert(candidate((\"aaabb\")) == 2);\n    assert(candidate((\"aaaBb\")) == 1);\n    assert(candidate((\"\")) == 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_125_split_words", "test": "}\nint main() {\n    auto candidate = split_words;\n    assert(candidate((\"Hello world!\")) == std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world!\"}));\n    assert(candidate((\"Hello,world!\")) == std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world!\"}));\n    assert(candidate((\"Hello world,!\")) == std::vector<std::string>({(std::string)\"Hello\", (std::string)\"world,!\"}));\n    assert(candidate((\"Hello,Hello,world !\")) == std::vector<std::string>({(std::string)\"Hello,Hello,world\", (std::string)\"!\"}));\n    assert(candidate((\"abcdef\")) == 3);\n    assert(candidate((\"aaabb\")) == 2);\n    assert(candidate((\"aaaBb\")) == 1);\n    assert(candidate((\"\")) == 0);\n}\n"}
{"name": "HumanEval_116_sort_array", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// In this Kata, you have to sort a vector of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array((std::vector<long>({(long)1, (long)5, (long)2, (long)3, (long)4})))\n// (std::vector<long>({(long)1, (long)2, (long)3, (long)4, (long)5}))\n// >>> sort_array((std::vector<long>({(long)-2, (long)-3, (long)-4, (long)-5, (long)-6})))\n// (std::vector<long>({(long)-6, (long)-5, (long)-4, (long)-3, (long)-2}))\n// >>> sort_array((std::vector<long>({(long)1, (long)0, (long)2, (long)3, (long)4})))\n// (std::vector<long>({(long)0, (long)1, (long)2, (long)3, (long)4}))\nstd::vector<long> sort_array(std::vector<long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sort_array;\n    assert(candidate((std::vector<long>({(long)1, (long)5, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)2, (long)4, (long)3, (long)5})));\n    assert(candidate((std::vector<long>({(long)-2, (long)-3, (long)-4, (long)-5, (long)-6}))) == (std::vector<long>({(long)-4, (long)-2, (long)-6, (long)-5, (long)-3})));\n    assert(candidate((std::vector<long>({(long)1, (long)0, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)0, (long)1, (long)2, (long)4, (long)3})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)2, (long)5, (long)77, (long)4, (long)5, (long)3, (long)5, (long)7, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)2, (long)2, (long)4, (long)4, (long)3, (long)3, (long)5, (long)5, (long)5, (long)7, (long)77})));\n    assert(candidate((std::vector<long>({(long)3, (long)6, (long)44, (long)12, (long)32, (long)5}))) == (std::vector<long>({(long)32, (long)3, (long)5, (long)6, (long)12, (long)44})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32}))) == (std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32}))) == (std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_116_sort_array", "test": "}\nint main() {\n    auto candidate = sort_array;\n    assert(candidate((std::vector<long>({(long)1, (long)5, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)2, (long)4, (long)3, (long)5})));\n    assert(candidate((std::vector<long>({(long)-2, (long)-3, (long)-4, (long)-5, (long)-6}))) == (std::vector<long>({(long)-4, (long)-2, (long)-6, (long)-5, (long)-3})));\n    assert(candidate((std::vector<long>({(long)1, (long)0, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)0, (long)1, (long)2, (long)4, (long)3})));\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)2, (long)5, (long)77, (long)4, (long)5, (long)3, (long)5, (long)7, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)2, (long)2, (long)4, (long)4, (long)3, (long)3, (long)5, (long)5, (long)5, (long)7, (long)77})));\n    assert(candidate((std::vector<long>({(long)3, (long)6, (long)44, (long)12, (long)32, (long)5}))) == (std::vector<long>({(long)32, (long)3, (long)5, (long)6, (long)12, (long)44})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32}))) == (std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32})));\n    assert(candidate((std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32}))) == (std::vector<long>({(long)2, (long)4, (long)8, (long)16, (long)32})));\n}\n"}
{"name": "HumanEval_28_concatenate", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Concatenate vector of strings into a single string\n// >>> concatenate((std::vector<std::string>()))\n// (\"\")\n// >>> concatenate((std::vector<std::string>({(std::string)\"a\", (std::string)\"b\", (std::string)\"c\"})))\n// (\"abc\")\nstd::string concatenate(std::vector<std::string> strings) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = concatenate;\n    assert(candidate((std::vector<std::string>())) == (\"\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"y\", (std::string)\"z\"}))) == (\"xyz\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"y\", (std::string)\"z\", (std::string)\"w\", (std::string)\"k\"}))) == (\"xyzwk\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_28_concatenate", "test": "}\nint main() {\n    auto candidate = concatenate;\n    assert(candidate((std::vector<std::string>())) == (\"\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"y\", (std::string)\"z\"}))) == (\"xyz\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"x\", (std::string)\"y\", (std::string)\"z\", (std::string)\"w\", (std::string)\"k\"}))) == (\"xyzwk\"));\n}\n"}
{"name": "HumanEval_149_sorted_list_sum", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that accepts a vector of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted vector with a sorted order,\n// The vector is always a vector of strings and never a vector of numbers,\n// and it may contain duplicates.\n// The order of the vector should be ascending by length of each word, and you\n// should return the vector sorted by that rule.\n// If two words have the same length, sort the vector alphabetically.\n// The function should return a vector of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort((std::vector<std::string>({(std::string)\"aa\", (std::string)\"a\", (std::string)\"aaa\"})))\n// (std::vector<std::string>({(std::string)\"aa\"}))\n// >>> list_sort((std::vector<std::string>({(std::string)\"ab\", (std::string)\"a\", (std::string)\"aaa\", (std::string)\"cd\"})))\n// (std::vector<std::string>({(std::string)\"ab\", (std::string)\"cd\"}))\nstd::vector<std::string> sorted_list_sum(std::vector<std::string> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sorted_list_sum;\n    assert(candidate((std::vector<std::string>({(std::string)\"aa\", (std::string)\"a\", (std::string)\"aaa\"}))) == (std::vector<std::string>({(std::string)\"aa\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"school\", (std::string)\"AI\", (std::string)\"asdf\", (std::string)\"b\"}))) == (std::vector<std::string>({(std::string)\"AI\", (std::string)\"asdf\", (std::string)\"school\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"d\", (std::string)\"b\", (std::string)\"c\", (std::string)\"a\"}))) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"d\", (std::string)\"dcba\", (std::string)\"abcd\", (std::string)\"a\"}))) == (std::vector<std::string>({(std::string)\"abcd\", (std::string)\"dcba\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"AI\", (std::string)\"ai\", (std::string)\"au\"}))) == (std::vector<std::string>({(std::string)\"AI\", (std::string)\"ai\", (std::string)\"au\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"a\", (std::string)\"b\", (std::string)\"b\", (std::string)\"c\", (std::string)\"c\", (std::string)\"a\"}))) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"aaaa\", (std::string)\"bbbb\", (std::string)\"dd\", (std::string)\"cc\"}))) == (std::vector<std::string>({(std::string)\"cc\", (std::string)\"dd\", (std::string)\"aaaa\", (std::string)\"bbbb\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_149_sorted_list_sum", "test": "}\nint main() {\n    auto candidate = sorted_list_sum;\n    assert(candidate((std::vector<std::string>({(std::string)\"aa\", (std::string)\"a\", (std::string)\"aaa\"}))) == (std::vector<std::string>({(std::string)\"aa\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"school\", (std::string)\"AI\", (std::string)\"asdf\", (std::string)\"b\"}))) == (std::vector<std::string>({(std::string)\"AI\", (std::string)\"asdf\", (std::string)\"school\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"d\", (std::string)\"b\", (std::string)\"c\", (std::string)\"a\"}))) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"d\", (std::string)\"dcba\", (std::string)\"abcd\", (std::string)\"a\"}))) == (std::vector<std::string>({(std::string)\"abcd\", (std::string)\"dcba\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"AI\", (std::string)\"ai\", (std::string)\"au\"}))) == (std::vector<std::string>({(std::string)\"AI\", (std::string)\"ai\", (std::string)\"au\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"a\", (std::string)\"b\", (std::string)\"b\", (std::string)\"c\", (std::string)\"c\", (std::string)\"a\"}))) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"aaaa\", (std::string)\"bbbb\", (std::string)\"dd\", (std::string)\"cc\"}))) == (std::vector<std::string>({(std::string)\"cc\", (std::string)\"dd\", (std::string)\"aaaa\", (std::string)\"bbbb\"})));\n}\n"}
{"name": "HumanEval_7_filter_by_substring", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Filter an input vector of strings only for ones that contain given substring\n// >>> filter_by_substring((std::vector<std::string>()), (\"a\"))\n// (std::vector<std::string>())\n// >>> filter_by_substring((std::vector<std::string>({(std::string)\"abc\", (std::string)\"bacd\", (std::string)\"cde\", (std::string)\"array\"})), (\"a\"))\n// (std::vector<std::string>({(std::string)\"abc\", (std::string)\"bacd\", (std::string)\"array\"}))\nstd::vector<std::string> filter_by_substring(std::vector<std::string> strings, std::string substring) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = filter_by_substring;\n    assert(candidate((std::vector<std::string>()), (\"john\")) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"xxx\", (std::string)\"asd\", (std::string)\"xxy\", (std::string)\"john doe\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})), (\"xxx\")) == (std::vector<std::string>({(std::string)\"xxx\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"xxx\", (std::string)\"asd\", (std::string)\"aaaxxy\", (std::string)\"john doe\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})), (\"xx\")) == (std::vector<std::string>({(std::string)\"xxx\", (std::string)\"aaaxxy\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"grunt\", (std::string)\"trumpet\", (std::string)\"prune\", (std::string)\"gruesome\"})), (\"run\")) == (std::vector<std::string>({(std::string)\"grunt\", (std::string)\"prune\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_7_filter_by_substring", "test": "}\nint main() {\n    auto candidate = filter_by_substring;\n    assert(candidate((std::vector<std::string>()), (\"john\")) == (std::vector<std::string>()));\n    assert(candidate((std::vector<std::string>({(std::string)\"xxx\", (std::string)\"asd\", (std::string)\"xxy\", (std::string)\"john doe\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})), (\"xxx\")) == (std::vector<std::string>({(std::string)\"xxx\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"xxx\", (std::string)\"asd\", (std::string)\"aaaxxy\", (std::string)\"john doe\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})), (\"xx\")) == (std::vector<std::string>({(std::string)\"xxx\", (std::string)\"aaaxxy\", (std::string)\"xxxAAA\", (std::string)\"xxx\"})));\n    assert(candidate((std::vector<std::string>({(std::string)\"grunt\", (std::string)\"trumpet\", (std::string)\"prune\", (std::string)\"gruesome\"})), (\"run\")) == (std::vector<std::string>({(std::string)\"grunt\", (std::string)\"prune\"})));\n}\n"}
{"name": "HumanEval_99_closest_integer", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer((\"10\"))\n// (10)\n// >>> closest_integer((\"15.3\"))\n// (15)\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nlong closest_integer(std::string value) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = closest_integer;\n    assert(candidate((\"10\")) == (10));\n    assert(candidate((\"14.5\")) == (15));\n    assert(candidate((\"-15.5\")) == (-16));\n    assert(candidate((\"15.3\")) == (15));\n    assert(candidate((\"0\")) == (0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_99_closest_integer", "test": "}\nint main() {\n    auto candidate = closest_integer;\n    assert(candidate((\"10\")) == (10));\n    assert(candidate((\"14.5\")) == (15));\n    assert(candidate((\"-15.5\")) == (-16));\n    assert(candidate((\"15.3\")) == (15));\n    assert(candidate((\"0\")) == (0));\n}\n"}
{"name": "HumanEval_64_vowels_count", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count((\"abcde\"))\n// (2)\n// >>> vowels_count((\"ACEDY\"))\n// (3)\nlong vowels_count(std::string s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = vowels_count;\n    assert(candidate((\"abcde\")) == (2));\n    assert(candidate((\"Alone\")) == (3));\n    assert(candidate((\"key\")) == (2));\n    assert(candidate((\"bye\")) == (1));\n    assert(candidate((\"keY\")) == (2));\n    assert(candidate((\"bYe\")) == (1));\n    assert(candidate((\"ACEDY\")) == (3));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_64_vowels_count", "test": "}\nint main() {\n    auto candidate = vowels_count;\n    assert(candidate((\"abcde\")) == (2));\n    assert(candidate((\"Alone\")) == (3));\n    assert(candidate((\"key\")) == (2));\n    assert(candidate((\"bye\")) == (1));\n    assert(candidate((\"keY\")) == (2));\n    assert(candidate((\"bYe\")) == (1));\n    assert(candidate((\"ACEDY\")) == (3));\n}\n"}
{"name": "HumanEval_158_find_max", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that accepts a vector of strings.\n// The vector contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max((std::vector<std::string>({(std::string)\"name\", (std::string)\"of\", (std::string)\"string\"})))\n// (\"string\")\n// >>> find_max((std::vector<std::string>({(std::string)\"name\", (std::string)\"enam\", (std::string)\"game\"})))\n// (\"enam\")\n// >>> find_max((std::vector<std::string>({(std::string)\"aaaaaaa\", (std::string)\"bb\", (std::string)\"cc\"})))\n// (\"aaaaaaa\")\nstd::string find_max(std::vector<std::string> words) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = find_max;\n    assert(candidate((std::vector<std::string>({(std::string)\"name\", (std::string)\"of\", (std::string)\"string\"}))) == (\"string\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"name\", (std::string)\"enam\", (std::string)\"game\"}))) == (\"enam\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"aaaaaaa\", (std::string)\"bb\", (std::string)\"cc\"}))) == (\"aaaaaaa\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"abc\", (std::string)\"cba\"}))) == (\"abc\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"play\", (std::string)\"this\", (std::string)\"game\", (std::string)\"of\", (std::string)\"footbott\"}))) == (\"footbott\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"we\", (std::string)\"are\", (std::string)\"gonna\", (std::string)\"rock\"}))) == (\"gonna\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"we\", (std::string)\"are\", (std::string)\"a\", (std::string)\"mad\", (std::string)\"nation\"}))) == (\"nation\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"this\", (std::string)\"is\", (std::string)\"a\", (std::string)\"prrk\"}))) == (\"this\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"b\"}))) == (\"b\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"play\", (std::string)\"play\", (std::string)\"play\"}))) == (\"play\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_158_find_max", "test": "}\nint main() {\n    auto candidate = find_max;\n    assert(candidate((std::vector<std::string>({(std::string)\"name\", (std::string)\"of\", (std::string)\"string\"}))) == (\"string\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"name\", (std::string)\"enam\", (std::string)\"game\"}))) == (\"enam\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"aaaaaaa\", (std::string)\"bb\", (std::string)\"cc\"}))) == (\"aaaaaaa\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"abc\", (std::string)\"cba\"}))) == (\"abc\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"play\", (std::string)\"this\", (std::string)\"game\", (std::string)\"of\", (std::string)\"footbott\"}))) == (\"footbott\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"we\", (std::string)\"are\", (std::string)\"gonna\", (std::string)\"rock\"}))) == (\"gonna\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"we\", (std::string)\"are\", (std::string)\"a\", (std::string)\"mad\", (std::string)\"nation\"}))) == (\"nation\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"this\", (std::string)\"is\", (std::string)\"a\", (std::string)\"prrk\"}))) == (\"this\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"b\"}))) == (\"b\"));\n    assert(candidate((std::vector<std::string>({(std::string)\"play\", (std::string)\"play\", (std::string)\"play\"}))) == (\"play\"));\n}\n"}
{"name": "HumanEval_162_string_to_md5", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given a string 'text', return its md5 hash equivalent string.\n// If 'text' is an empty string, return None.\n// >>> string_to_md5((\"Hello world\"))\n// \"3e25960a79dbc69b674cd4ec67a72c62\"\nstd::optional<std::string> string_to_md5(std::string text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = string_to_md5;\n    assert(candidate((\"Hello world\")) == \"3e25960a79dbc69b674cd4ec67a72c62\");\n    assert(candidate((\"\")) == std::nullopt);\n    assert(candidate((\"A B C\")) == \"0ef78513b0cb8cef12743f5aeb35f888\");\n    assert(candidate((\"password\")) == \"5f4dcc3b5aa765d61d8327deb882cf99\");\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_162_string_to_md5", "test": "}\nint main() {\n    auto candidate = string_to_md5;\n    assert(candidate((\"Hello world\")) == \"3e25960a79dbc69b674cd4ec67a72c62\");\n    assert(candidate((\"\")) == std::nullopt);\n    assert(candidate((\"A B C\")) == \"0ef78513b0cb8cef12743f5aeb35f888\");\n    assert(candidate((\"password\")) == \"5f4dcc3b5aa765d61d8327deb882cf99\");\n}\n"}
{"name": "HumanEval_44_change_base", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base((8), (3))\n// (\"22\")\n// >>> change_base((8), (2))\n// (\"1000\")\n// >>> change_base((7), (2))\n// (\"111\")\nstd::string change_base(long x, long base) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = change_base;\n    assert(candidate((8), (3)) == (\"22\"));\n    assert(candidate((9), (3)) == (\"100\"));\n    assert(candidate((234), (2)) == (\"11101010\"));\n    assert(candidate((16), (2)) == (\"10000\"));\n    assert(candidate((8), (2)) == (\"1000\"));\n    assert(candidate((7), (2)) == (\"111\"));\n    assert(candidate((2), (3)) == (\"2\"));\n    assert(candidate((3), (4)) == (\"3\"));\n    assert(candidate((4), (5)) == (\"4\"));\n    assert(candidate((5), (6)) == (\"5\"));\n    assert(candidate((6), (7)) == (\"6\"));\n    assert(candidate((7), (8)) == (\"7\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_44_change_base", "test": "}\nint main() {\n    auto candidate = change_base;\n    assert(candidate((8), (3)) == (\"22\"));\n    assert(candidate((9), (3)) == (\"100\"));\n    assert(candidate((234), (2)) == (\"11101010\"));\n    assert(candidate((16), (2)) == (\"10000\"));\n    assert(candidate((8), (2)) == (\"1000\"));\n    assert(candidate((7), (2)) == (\"111\"));\n    assert(candidate((2), (3)) == (\"2\"));\n    assert(candidate((3), (4)) == (\"3\"));\n    assert(candidate((4), (5)) == (\"4\"));\n    assert(candidate((5), (6)) == (\"5\"));\n    assert(candidate((6), (7)) == (\"6\"));\n    assert(candidate((7), (8)) == (\"7\"));\n}\n"}
{"name": "HumanEval_157_right_angle_triangle", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle((3), (4), (5))\n// (true)\n// >>> right_angle_triangle((1), (2), (3))\n// (false)\nbool right_angle_triangle(long a, long b, long c) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = right_angle_triangle;\n    assert(candidate((3), (4), (5)) == (true));\n    assert(candidate((1), (2), (3)) == (false));\n    assert(candidate((10), (6), (8)) == (true));\n    assert(candidate((2), (2), (2)) == (false));\n    assert(candidate((7), (24), (25)) == (true));\n    assert(candidate((10), (5), (7)) == (false));\n    assert(candidate((5), (12), (13)) == (true));\n    assert(candidate((15), (8), (17)) == (true));\n    assert(candidate((48), (55), (73)) == (true));\n    assert(candidate((1), (1), (1)) == (false));\n    assert(candidate((2), (2), (10)) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_157_right_angle_triangle", "test": "}\nint main() {\n    auto candidate = right_angle_triangle;\n    assert(candidate((3), (4), (5)) == (true));\n    assert(candidate((1), (2), (3)) == (false));\n    assert(candidate((10), (6), (8)) == (true));\n    assert(candidate((2), (2), (2)) == (false));\n    assert(candidate((7), (24), (25)) == (true));\n    assert(candidate((10), (5), (7)) == (false));\n    assert(candidate((5), (12), (13)) == (true));\n    assert(candidate((15), (8), (17)) == (true));\n    assert(candidate((48), (55), (73)) == (true));\n    assert(candidate((1), (1), (1)) == (false));\n    assert(candidate((2), (2), (10)) == (false));\n}\n"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you a vector of GPAs for some students and you have to write \n// a function that can output a vector of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation((std::vector<float>({(float)4.0f, (float)3, (float)1.7f, (float)2, (float)3.5f})))\n// (std::vector<std::string>({(std::string)\"A+\", (std::string)\"B\", (std::string)\"C-\", (std::string)\"C\", (std::string)\"A-\"}))\nstd::vector<std::string> numerical_letter_grade(std::vector<float> grades) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = numerical_letter_grade;\n    assert(candidate((std::vector<float>({(float)4.0f, (float)3, (float)1.7f, (float)2, (float)3.5f}))) == (std::vector<std::string>({(std::string)\"A+\", (std::string)\"B\", (std::string)\"C-\", (std::string)\"C\", (std::string)\"A-\"})));\n    assert(candidate((std::vector<float>({(float)1.2f}))) == (std::vector<std::string>({(std::string)\"D+\"})));\n    assert(candidate((std::vector<float>({(float)0.5f}))) == (std::vector<std::string>({(std::string)\"D-\"})));\n    assert(candidate((std::vector<float>({(float)0.0f}))) == (std::vector<std::string>({(std::string)\"E\"})));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)0.3f, (float)1.5f, (float)2.8f, (float)3.3f}))) == (std::vector<std::string>({(std::string)\"D\", (std::string)\"D-\", (std::string)\"C-\", (std::string)\"B\", (std::string)\"B+\"})));\n    assert(candidate((std::vector<float>({(float)0.0f, (float)0.7f}))) == (std::vector<std::string>({(std::string)\"E\", (std::string)\"D-\"})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "}\nint main() {\n    auto candidate = numerical_letter_grade;\n    assert(candidate((std::vector<float>({(float)4.0f, (float)3, (float)1.7f, (float)2, (float)3.5f}))) == (std::vector<std::string>({(std::string)\"A+\", (std::string)\"B\", (std::string)\"C-\", (std::string)\"C\", (std::string)\"A-\"})));\n    assert(candidate((std::vector<float>({(float)1.2f}))) == (std::vector<std::string>({(std::string)\"D+\"})));\n    assert(candidate((std::vector<float>({(float)0.5f}))) == (std::vector<std::string>({(std::string)\"D-\"})));\n    assert(candidate((std::vector<float>({(float)0.0f}))) == (std::vector<std::string>({(std::string)\"E\"})));\n    assert(candidate((std::vector<float>({(float)1.0f, (float)0.3f, (float)1.5f, (float)2.8f, (float)3.3f}))) == (std::vector<std::string>({(std::string)\"D\", (std::string)\"D-\", (std::string)\"C-\", (std::string)\"B\", (std::string)\"B+\"})));\n    assert(candidate((std::vector<float>({(float)0.0f, (float)0.7f}))) == (std::vector<std::string>({(std::string)\"E\", (std::string)\"D-\"})));\n}\n"}
{"name": "HumanEval_5_intersperse", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Insert a number 'delimeter' between every two consecutive elements of input vector `numbers'\n// >>> intersperse((std::vector<long>()), (4))\n// (std::vector<long>())\n// >>> intersperse((std::vector<long>({(long)1, (long)2, (long)3})), (4))\n// (std::vector<long>({(long)1, (long)4, (long)2, (long)4, (long)3}))\nstd::vector<long> intersperse(std::vector<long> numbers, long delimeter) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = intersperse;\n    assert(candidate((std::vector<long>()), (7)) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)3, (long)2})), (8)) == (std::vector<long>({(long)5, (long)8, (long)6, (long)8, (long)3, (long)8, (long)2})));\n    assert(candidate((std::vector<long>({(long)2, (long)2, (long)2})), (2)) == (std::vector<long>({(long)2, (long)2, (long)2, (long)2, (long)2})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_5_intersperse", "test": "}\nint main() {\n    auto candidate = intersperse;\n    assert(candidate((std::vector<long>()), (7)) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)5, (long)6, (long)3, (long)2})), (8)) == (std::vector<long>({(long)5, (long)8, (long)6, (long)8, (long)3, (long)8, (long)2})));\n    assert(candidate((std::vector<long>({(long)2, (long)2, (long)2})), (2)) == (std::vector<long>({(long)2, (long)2, (long)2, (long)2, (long)2})));\n}\n"}
{"name": "HumanEval_146_specialFilter", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Write a function that takes a vector of numbers as input and returns \n// the number of elements in the vector that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter((std::vector<long>({(long)15, (long)-73, (long)14, (long)-15})))\n// (1)\n// >>> specialFilter((std::vector<long>({(long)33, (long)-2, (long)-3, (long)45, (long)21, (long)109})))\n// (2)\nlong specialFilter(std::vector<long> nums) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = specialFilter;\n    assert(candidate((std::vector<long>({(long)5, (long)-2, (long)1, (long)-5}))) == (0));\n    assert(candidate((std::vector<long>({(long)15, (long)-73, (long)14, (long)-15}))) == (1));\n    assert(candidate((std::vector<long>({(long)33, (long)-2, (long)-3, (long)45, (long)21, (long)109}))) == (2));\n    assert(candidate((std::vector<long>({(long)43, (long)-12, (long)93, (long)125, (long)121, (long)109}))) == (4));\n    assert(candidate((std::vector<long>({(long)71, (long)-2, (long)-33, (long)75, (long)21, (long)19}))) == (3));\n    assert(candidate((std::vector<long>({(long)1}))) == (0));\n    assert(candidate((std::vector<long>())) == (0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_146_specialFilter", "test": "}\nint main() {\n    auto candidate = specialFilter;\n    assert(candidate((std::vector<long>({(long)5, (long)-2, (long)1, (long)-5}))) == (0));\n    assert(candidate((std::vector<long>({(long)15, (long)-73, (long)14, (long)-15}))) == (1));\n    assert(candidate((std::vector<long>({(long)33, (long)-2, (long)-3, (long)45, (long)21, (long)109}))) == (2));\n    assert(candidate((std::vector<long>({(long)43, (long)-12, (long)93, (long)125, (long)121, (long)109}))) == (4));\n    assert(candidate((std::vector<long>({(long)71, (long)-2, (long)-33, (long)75, (long)21, (long)19}))) == (3));\n    assert(candidate((std::vector<long>({(long)1}))) == (0));\n    assert(candidate((std::vector<long>())) == (0));\n}\n"}
{"name": "HumanEval_60_sum_to_n", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n((30))\n// (465)\n// >>> sum_to_n((100))\n// (5050)\n// >>> sum_to_n((5))\n// (15)\n// >>> sum_to_n((10))\n// (55)\n// >>> sum_to_n((1))\n// (1)\nlong sum_to_n(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sum_to_n;\n    assert(candidate((1)) == (1));\n    assert(candidate((6)) == (21));\n    assert(candidate((11)) == (66));\n    assert(candidate((30)) == (465));\n    assert(candidate((100)) == (5050));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_60_sum_to_n", "test": "}\nint main() {\n    auto candidate = sum_to_n;\n    assert(candidate((1)) == (1));\n    assert(candidate((6)) == (21));\n    assert(candidate((11)) == (66));\n    assert(candidate((30)) == (465));\n    assert(candidate((100)) == (5050));\n}\n"}
{"name": "HumanEval_26_remove_duplicates", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// From a vector of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)4})))\n// (std::vector<long>({(long)1, (long)3, (long)4}))\nstd::vector<long> remove_duplicates(std::vector<long> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = remove_duplicates;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)2, (long)3, (long)4})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)4, (long)3, (long)5}))) == (std::vector<long>({(long)1, (long)4, (long)5})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_26_remove_duplicates", "test": "}\nint main() {\n    auto candidate = remove_duplicates;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)2, (long)3, (long)4})));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)4, (long)3, (long)5}))) == (std::vector<long>({(long)1, (long)4, (long)5})));\n}\n"}
{"name": "HumanEval_163_generate_integers", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers((2), (8))\n// (std::vector<long>({(long)2, (long)4, (long)6, (long)8}))\n// >>> generate_integers((8), (2))\n// (std::vector<long>({(long)2, (long)4, (long)6, (long)8}))\n// >>> generate_integers((10), (14))\n// (std::vector<long>())\nstd::vector<long> generate_integers(long a, long b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = generate_integers;\n    assert(candidate((2), (10)) == (std::vector<long>({(long)2, (long)4, (long)6, (long)8})));\n    assert(candidate((10), (2)) == (std::vector<long>({(long)2, (long)4, (long)6, (long)8})));\n    assert(candidate((132), (2)) == (std::vector<long>({(long)2, (long)4, (long)6, (long)8})));\n    assert(candidate((17), (89)) == (std::vector<long>()));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_163_generate_integers", "test": "}\nint main() {\n    auto candidate = generate_integers;\n    assert(candidate((2), (10)) == (std::vector<long>({(long)2, (long)4, (long)6, (long)8})));\n    assert(candidate((10), (2)) == (std::vector<long>({(long)2, (long)4, (long)6, (long)8})));\n    assert(candidate((132), (2)) == (std::vector<long>({(long)2, (long)4, (long)6, (long)8})));\n    assert(candidate((17), (89)) == (std::vector<long>()));\n}\n"}
{"name": "HumanEval_9_rolling_max", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// From a given vector of integers, generate a vector of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max((std::vector<long>({(long)1, (long)2, (long)3, (long)2, (long)3, (long)4, (long)2})))\n// (std::vector<long>({(long)1, (long)2, (long)3, (long)3, (long)3, (long)4, (long)4}))\nstd::vector<long> rolling_max(std::vector<long> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = rolling_max;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)2, (long)3, (long)4})));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)2, (long)1}))) == (std::vector<long>({(long)4, (long)4, (long)4, (long)4})));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)3, (long)100, (long)3}))) == (std::vector<long>({(long)3, (long)3, (long)3, (long)100, (long)100})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_9_rolling_max", "test": "}\nint main() {\n    auto candidate = rolling_max;\n    assert(candidate((std::vector<long>())) == (std::vector<long>()));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3, (long)4}))) == (std::vector<long>({(long)1, (long)2, (long)3, (long)4})));\n    assert(candidate((std::vector<long>({(long)4, (long)3, (long)2, (long)1}))) == (std::vector<long>({(long)4, (long)4, (long)4, (long)4})));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)3, (long)100, (long)3}))) == (std::vector<long>({(long)3, (long)3, (long)3, (long)100, (long)100})));\n}\n"}
{"name": "HumanEval_3_below_zero", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You're given a vector of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero((std::vector<long>({(long)1, (long)2, (long)3})))\n// (false)\n// >>> below_zero((std::vector<long>({(long)1, (long)2, (long)-4, (long)5})))\n// (true)\nbool below_zero(std::vector<long> operations) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = below_zero;\n    assert(candidate((std::vector<long>())) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)-3, (long)1, (long)2, (long)-3}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)-4, (long)5, (long)6}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)2, (long)-2, (long)5, (long)-5, (long)4, (long)-4}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)2, (long)-2, (long)5, (long)-5, (long)4, (long)-5}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)-2, (long)2, (long)-2, (long)5, (long)-5, (long)4, (long)-4}))) == (true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_3_below_zero", "test": "}\nint main() {\n    auto candidate = below_zero;\n    assert(candidate((std::vector<long>())) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)-3, (long)1, (long)2, (long)-3}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)-4, (long)5, (long)6}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)2, (long)-2, (long)5, (long)-5, (long)4, (long)-4}))) == (false));\n    assert(candidate((std::vector<long>({(long)1, (long)-1, (long)2, (long)-2, (long)5, (long)-5, (long)4, (long)-5}))) == (true));\n    assert(candidate((std::vector<long>({(long)1, (long)-2, (long)2, (long)-2, (long)5, (long)-5, (long)4, (long)-4}))) == (true));\n}\n"}
{"name": "HumanEval_69_search", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// You are given a non-empty vector of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the vector.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search((std::vector<long>({(long)4, (long)1, (long)2, (long)2, (long)3, (long)1})))\n// (2)\n// >>> search((std::vector<long>({(long)1, (long)2, (long)2, (long)3, (long)3, (long)3, (long)4, (long)4, (long)4})))\n// (3)\n// >>> search((std::vector<long>({(long)5, (long)5, (long)4, (long)4, (long)4})))\n// (-1)\nlong search(std::vector<long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = search;\n    assert(candidate((std::vector<long>({(long)5, (long)5, (long)5, (long)5, (long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)4, (long)1, (long)4, (long)1, (long)4, (long)4}))) == (4));\n    assert(candidate((std::vector<long>({(long)3, (long)3}))) == (-1));\n    assert(candidate((std::vector<long>({(long)8, (long)8, (long)8, (long)8, (long)8, (long)8, (long)8, (long)8}))) == (8));\n    assert(candidate((std::vector<long>({(long)2, (long)3, (long)3, (long)2, (long)2}))) == (2));\n    assert(candidate((std::vector<long>({(long)2, (long)7, (long)8, (long)8, (long)4, (long)8, (long)7, (long)3, (long)9, (long)6, (long)5, (long)10, (long)4, (long)3, (long)6, (long)7, (long)1, (long)7, (long)4, (long)10, (long)8, (long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)8, (long)2}))) == (2));\n    assert(candidate((std::vector<long>({(long)6, (long)7, (long)1, (long)8, (long)8, (long)10, (long)5, (long)8, (long)5, (long)3, (long)10}))) == (1));\n    assert(candidate((std::vector<long>({(long)8, (long)8, (long)3, (long)6, (long)5, (long)6, (long)4}))) == (-1));\n    assert(candidate((std::vector<long>({(long)6, (long)9, (long)6, (long)7, (long)1, (long)4, (long)7, (long)1, (long)8, (long)8, (long)9, (long)8, (long)10, (long)10, (long)8, (long)4, (long)10, (long)4, (long)10, (long)1, (long)2, (long)9, (long)5, (long)7, (long)9}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)9, (long)10, (long)1, (long)3}))) == (1));\n    assert(candidate((std::vector<long>({(long)6, (long)9, (long)7, (long)5, (long)8, (long)7, (long)5, (long)3, (long)7, (long)5, (long)10, (long)10, (long)3, (long)6, (long)10, (long)2, (long)8, (long)6, (long)5, (long)4, (long)9, (long)5, (long)3, (long)10}))) == (5));\n    assert(candidate((std::vector<long>({(long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)8, (long)8, (long)10, (long)6, (long)4, (long)3, (long)5, (long)8, (long)2, (long)4, (long)2, (long)8, (long)4, (long)6, (long)10, (long)4, (long)2, (long)1, (long)10, (long)2, (long)1, (long)1, (long)5}))) == (4));\n    assert(candidate((std::vector<long>({(long)2, (long)10, (long)4, (long)8, (long)2, (long)10, (long)5, (long)1, (long)2, (long)9, (long)5, (long)5, (long)6, (long)3, (long)8, (long)6, (long)4, (long)10}))) == (2));\n    assert(candidate((std::vector<long>({(long)1, (long)6, (long)10, (long)1, (long)6, (long)9, (long)10, (long)8, (long)6, (long)8, (long)7, (long)3}))) == (1));\n    assert(candidate((std::vector<long>({(long)9, (long)2, (long)4, (long)1, (long)5, (long)1, (long)5, (long)2, (long)5, (long)7, (long)7, (long)7, (long)3, (long)10, (long)1, (long)5, (long)4, (long)2, (long)8, (long)4, (long)1, (long)9, (long)10, (long)7, (long)10, (long)2, (long)8, (long)10, (long)9, (long)4}))) == (4));\n    assert(candidate((std::vector<long>({(long)2, (long)6, (long)4, (long)2, (long)8, (long)7, (long)5, (long)6, (long)4, (long)10, (long)4, (long)6, (long)3, (long)7, (long)8, (long)8, (long)3, (long)1, (long)4, (long)2, (long)2, (long)10, (long)7}))) == (4));\n    assert(candidate((std::vector<long>({(long)9, (long)8, (long)6, (long)10, (long)2, (long)6, (long)10, (long)2, (long)7, (long)8, (long)10, (long)3, (long)8, (long)2, (long)6, (long)2, (long)3, (long)1}))) == (2));\n    assert(candidate((std::vector<long>({(long)5, (long)5, (long)3, (long)9, (long)5, (long)6, (long)3, (long)2, (long)8, (long)5, (long)6, (long)10, (long)10, (long)6, (long)8, (long)4, (long)10, (long)7, (long)7, (long)10, (long)8}))) == (-1));\n    assert(candidate((std::vector<long>({(long)10}))) == (-1));\n    assert(candidate((std::vector<long>({(long)9, (long)7, (long)7, (long)2, (long)4, (long)7, (long)2, (long)10, (long)9, (long)7, (long)5, (long)7, (long)2}))) == (2));\n    assert(candidate((std::vector<long>({(long)5, (long)4, (long)10, (long)2, (long)1, (long)1, (long)10, (long)3, (long)6, (long)1, (long)8}))) == (1));\n    assert(candidate((std::vector<long>({(long)7, (long)9, (long)9, (long)9, (long)3, (long)4, (long)1, (long)5, (long)9, (long)1, (long)2, (long)1, (long)1, (long)10, (long)7, (long)5, (long)6, (long)7, (long)6, (long)7, (long)7, (long)6}))) == (1));\n    assert(candidate((std::vector<long>({(long)3, (long)10, (long)10, (long)9, (long)2}))) == (-1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_69_search", "test": "}\nint main() {\n    auto candidate = search;\n    assert(candidate((std::vector<long>({(long)5, (long)5, (long)5, (long)5, (long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)4, (long)1, (long)4, (long)1, (long)4, (long)4}))) == (4));\n    assert(candidate((std::vector<long>({(long)3, (long)3}))) == (-1));\n    assert(candidate((std::vector<long>({(long)8, (long)8, (long)8, (long)8, (long)8, (long)8, (long)8, (long)8}))) == (8));\n    assert(candidate((std::vector<long>({(long)2, (long)3, (long)3, (long)2, (long)2}))) == (2));\n    assert(candidate((std::vector<long>({(long)2, (long)7, (long)8, (long)8, (long)4, (long)8, (long)7, (long)3, (long)9, (long)6, (long)5, (long)10, (long)4, (long)3, (long)6, (long)7, (long)1, (long)7, (long)4, (long)10, (long)8, (long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)3, (long)2, (long)8, (long)2}))) == (2));\n    assert(candidate((std::vector<long>({(long)6, (long)7, (long)1, (long)8, (long)8, (long)10, (long)5, (long)8, (long)5, (long)3, (long)10}))) == (1));\n    assert(candidate((std::vector<long>({(long)8, (long)8, (long)3, (long)6, (long)5, (long)6, (long)4}))) == (-1));\n    assert(candidate((std::vector<long>({(long)6, (long)9, (long)6, (long)7, (long)1, (long)4, (long)7, (long)1, (long)8, (long)8, (long)9, (long)8, (long)10, (long)10, (long)8, (long)4, (long)10, (long)4, (long)10, (long)1, (long)2, (long)9, (long)5, (long)7, (long)9}))) == (1));\n    assert(candidate((std::vector<long>({(long)1, (long)9, (long)10, (long)1, (long)3}))) == (1));\n    assert(candidate((std::vector<long>({(long)6, (long)9, (long)7, (long)5, (long)8, (long)7, (long)5, (long)3, (long)7, (long)5, (long)10, (long)10, (long)3, (long)6, (long)10, (long)2, (long)8, (long)6, (long)5, (long)4, (long)9, (long)5, (long)3, (long)10}))) == (5));\n    assert(candidate((std::vector<long>({(long)1}))) == (1));\n    assert(candidate((std::vector<long>({(long)8, (long)8, (long)10, (long)6, (long)4, (long)3, (long)5, (long)8, (long)2, (long)4, (long)2, (long)8, (long)4, (long)6, (long)10, (long)4, (long)2, (long)1, (long)10, (long)2, (long)1, (long)1, (long)5}))) == (4));\n    assert(candidate((std::vector<long>({(long)2, (long)10, (long)4, (long)8, (long)2, (long)10, (long)5, (long)1, (long)2, (long)9, (long)5, (long)5, (long)6, (long)3, (long)8, (long)6, (long)4, (long)10}))) == (2));\n    assert(candidate((std::vector<long>({(long)1, (long)6, (long)10, (long)1, (long)6, (long)9, (long)10, (long)8, (long)6, (long)8, (long)7, (long)3}))) == (1));\n    assert(candidate((std::vector<long>({(long)9, (long)2, (long)4, (long)1, (long)5, (long)1, (long)5, (long)2, (long)5, (long)7, (long)7, (long)7, (long)3, (long)10, (long)1, (long)5, (long)4, (long)2, (long)8, (long)4, (long)1, (long)9, (long)10, (long)7, (long)10, (long)2, (long)8, (long)10, (long)9, (long)4}))) == (4));\n    assert(candidate((std::vector<long>({(long)2, (long)6, (long)4, (long)2, (long)8, (long)7, (long)5, (long)6, (long)4, (long)10, (long)4, (long)6, (long)3, (long)7, (long)8, (long)8, (long)3, (long)1, (long)4, (long)2, (long)2, (long)10, (long)7}))) == (4));\n    assert(candidate((std::vector<long>({(long)9, (long)8, (long)6, (long)10, (long)2, (long)6, (long)10, (long)2, (long)7, (long)8, (long)10, (long)3, (long)8, (long)2, (long)6, (long)2, (long)3, (long)1}))) == (2));\n    assert(candidate((std::vector<long>({(long)5, (long)5, (long)3, (long)9, (long)5, (long)6, (long)3, (long)2, (long)8, (long)5, (long)6, (long)10, (long)10, (long)6, (long)8, (long)4, (long)10, (long)7, (long)7, (long)10, (long)8}))) == (-1));\n    assert(candidate((std::vector<long>({(long)10}))) == (-1));\n    assert(candidate((std::vector<long>({(long)9, (long)7, (long)7, (long)2, (long)4, (long)7, (long)2, (long)10, (long)9, (long)7, (long)5, (long)7, (long)2}))) == (2));\n    assert(candidate((std::vector<long>({(long)5, (long)4, (long)10, (long)2, (long)1, (long)1, (long)10, (long)3, (long)6, (long)1, (long)8}))) == (1));\n    assert(candidate((std::vector<long>({(long)7, (long)9, (long)9, (long)9, (long)3, (long)4, (long)1, (long)5, (long)9, (long)1, (long)2, (long)1, (long)1, (long)10, (long)7, (long)5, (long)6, (long)7, (long)6, (long)7, (long)7, (long)6}))) == (1));\n    assert(candidate((std::vector<long>({(long)3, (long)10, (long)10, (long)9, (long)2}))) == (-1));\n}\n"}
{"name": "HumanEval_61_correct_bracketing", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing((\"(\"))\n// (false)\n// >>> correct_bracketing((\"()\"))\n// (true)\n// >>> correct_bracketing((\"(()())\"))\n// (true)\n// >>> correct_bracketing((\")(()\"))\n// (false)\nbool correct_bracketing(std::string brackets) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = correct_bracketing;\n    assert(candidate((\"()\")) == (true));\n    assert(candidate((\"(()())\")) == (true));\n    assert(candidate((\"()()(()())()\")) == (true));\n    assert(candidate((\"()()((()()())())(()()(()))\")) == (true));\n    assert(candidate((\"((()())))\")) == (false));\n    assert(candidate((\")(()\")) == (false));\n    assert(candidate((\"(\")) == (false));\n    assert(candidate((\"((((\")) == (false));\n    assert(candidate((\")\")) == (false));\n    assert(candidate((\"(()\")) == (false));\n    assert(candidate((\"()()(()())())(()\")) == (false));\n    assert(candidate((\"()()(()())()))()\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_61_correct_bracketing", "test": "}\nint main() {\n    auto candidate = correct_bracketing;\n    assert(candidate((\"()\")) == (true));\n    assert(candidate((\"(()())\")) == (true));\n    assert(candidate((\"()()(()())()\")) == (true));\n    assert(candidate((\"()()((()()())())(()()(()))\")) == (true));\n    assert(candidate((\"((()())))\")) == (false));\n    assert(candidate((\")(()\")) == (false));\n    assert(candidate((\"(\")) == (false));\n    assert(candidate((\"((((\")) == (false));\n    assert(candidate((\")\")) == (false));\n    assert(candidate((\"(()\")) == (false));\n    assert(candidate((\"()()(()())())(()\")) == (false));\n    assert(candidate((\"()()(()())()))()\")) == (false));\n}\n"}
{"name": "HumanEval_37_sort_even", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// This function takes a vector l and returns a vector l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even((std::vector<long>({(long)1, (long)2, (long)3})))\n// (std::vector<long>({(long)1, (long)2, (long)3}))\n// >>> sort_even((std::vector<long>({(long)5, (long)6, (long)3, (long)4})))\n// (std::vector<long>({(long)3, (long)6, (long)5, (long)4}))\nstd::vector<long> sort_even(std::vector<long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = sort_even;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (std::vector<long>({(long)1, (long)2, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)9, (long)0, (long)123, (long)1, (long)-10}))) == (std::vector<long>({(long)-10, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)5, (long)0, (long)9, (long)1, (long)123})));\n    assert(candidate((std::vector<long>({(long)5, (long)8, (long)-12, (long)4, (long)23, (long)2, (long)3, (long)11, (long)12, (long)-10}))) == (std::vector<long>({(long)-12, (long)8, (long)3, (long)4, (long)5, (long)2, (long)12, (long)11, (long)23, (long)-10})));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_37_sort_even", "test": "}\nint main() {\n    auto candidate = sort_even;\n    assert(candidate((std::vector<long>({(long)1, (long)2, (long)3}))) == (std::vector<long>({(long)1, (long)2, (long)3})));\n    assert(candidate((std::vector<long>({(long)5, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)9, (long)0, (long)123, (long)1, (long)-10}))) == (std::vector<long>({(long)-10, (long)3, (long)-5, (long)2, (long)-3, (long)3, (long)5, (long)0, (long)9, (long)1, (long)123})));\n    assert(candidate((std::vector<long>({(long)5, (long)8, (long)-12, (long)4, (long)23, (long)2, (long)3, (long)11, (long)12, (long)-10}))) == (std::vector<long>({(long)-12, (long)8, (long)3, (long)4, (long)5, (long)2, (long)12, (long)11, (long)23, (long)-10})));\n}\n"}
{"name": "HumanEval_54_same_chars", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// Check if two words have the same characters.\n// >>> same_chars((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\"))\n// (true)\n// >>> same_chars((\"abcd\"), (\"dddddddabc\"))\n// (true)\n// >>> same_chars((\"dddddddabc\"), (\"abcd\"))\n// (true)\n// >>> same_chars((\"eabcd\"), (\"dddddddabc\"))\n// (false)\n// >>> same_chars((\"abcd\"), (\"dddddddabce\"))\n// (false)\n// >>> same_chars((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\"))\n// (false)\nbool same_chars(std::string s0, std::string s1) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = same_chars;\n    assert(candidate((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\")) == (true));\n    assert(candidate((\"abcd\"), (\"dddddddabc\")) == (true));\n    assert(candidate((\"dddddddabc\"), (\"abcd\")) == (true));\n    assert(candidate((\"eabcd\"), (\"dddddddabc\")) == (false));\n    assert(candidate((\"abcd\"), (\"dddddddabcf\")) == (false));\n    assert(candidate((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\")) == (false));\n    assert(candidate((\"aabb\"), (\"aaccc\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_54_same_chars", "test": "}\nint main() {\n    auto candidate = same_chars;\n    assert(candidate((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\")) == (true));\n    assert(candidate((\"abcd\"), (\"dddddddabc\")) == (true));\n    assert(candidate((\"dddddddabc\"), (\"abcd\")) == (true));\n    assert(candidate((\"eabcd\"), (\"dddddddabc\")) == (false));\n    assert(candidate((\"abcd\"), (\"dddddddabcf\")) == (false));\n    assert(candidate((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\")) == (false));\n    assert(candidate((\"aabb\"), (\"aaccc\")) == (false));\n}\n"}
{"name": "HumanEval_56_correct_bracketing", "language": "cpp", "prompt": "#include<assert.h>\n#include<bits/stdc++.h>\n// brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing((\"<\"))\n// (false)\n// >>> correct_bracketing((\"<>\"))\n// (true)\n// >>> correct_bracketing((\"<<><>>\"))\n// (true)\n// >>> correct_bracketing((\"><<>\"))\n// (false)\nbool correct_bracketing(std::string brackets) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\nint main() {\n    auto candidate = correct_bracketing;\n    assert(candidate((\"<>\")) == (true));\n    assert(candidate((\"<<><>>\")) == (true));\n    assert(candidate((\"<><><<><>><>\")) == (true));\n    assert(candidate((\"<><><<<><><>><>><<><><<>>>\")) == (true));\n    assert(candidate((\"<<<><>>>>\")) == (false));\n    assert(candidate((\"><<>\")) == (false));\n    assert(candidate((\"<\")) == (false));\n    assert(candidate((\"<<<<\")) == (false));\n    assert(candidate((\">\")) == (false));\n    assert(candidate((\"<<>\")) == (false));\n    assert(candidate((\"<><><<><>><>><<>\")) == (false));\n    assert(candidate((\"<><><<><>><>>><>\")) == (false));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_56_correct_bracketing", "test": "}\nint main() {\n    auto candidate = correct_bracketing;\n    assert(candidate((\"<>\")) == (true));\n    assert(candidate((\"<<><>>\")) == (true));\n    assert(candidate((\"<><><<><>><>\")) == (true));\n    assert(candidate((\"<><><<<><><>><>><<><><<>>>\")) == (true));\n    assert(candidate((\"<<<><>>>>\")) == (false));\n    assert(candidate((\"><<>\")) == (false));\n    assert(candidate((\"<\")) == (false));\n    assert(candidate((\"<<<<\")) == (false));\n    assert(candidate((\">\")) == (false));\n    assert(candidate((\"<<>\")) == (false));\n    assert(candidate((\"<><><<><>><>><<>\")) == (false));\n    assert(candidate((\"<><><<><>><>>><>\")) == (false));\n}\n"}
