{"name": "HumanEval_23_strlen", "language": "rs", "prompt": "/// Return length of given string\n/// >>> strlen(String::from(\"\"))\n/// 0\n/// >>> strlen(String::from(\"abc\"))\n/// 3\nfn strlen(string: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = strlen;\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"x\")), 1);\n    assert_eq!(candidate(String::from(\"asdasnakj\")), 9);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_23_strlen", "test": "}\n\nfn main() {\n    let candidate = strlen;\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"x\")), 1);\n    assert_eq!(candidate(String::from(\"asdasnakj\")), 9);\n}\n"}
{"name": "HumanEval_89_encrypt", "language": "rs", "prompt": "/// Create a function encrypt that takes a string as an argument and\n/// returns a string encrypted with the alphabet being rotated. \n/// The alphabet should be rotated in a manner such that the letters \n/// shift down by two multiplied to two places.\n/// For example:\n/// >>> encrypt(String::from(\"hi\"))\n/// String::from(\"lm\")\n/// >>> encrypt(String::from(\"asdfghjkl\"))\n/// String::from(\"ewhjklnop\")\n/// >>> encrypt(String::from(\"gf\"))\n/// String::from(\"kj\")\n/// >>> encrypt(String::from(\"et\"))\n/// String::from(\"ix\")\nfn encrypt(s: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = encrypt;\n    assert_eq!(candidate(String::from(\"hi\")), String::from(\"lm\"));\n    assert_eq!(candidate(String::from(\"asdfghjkl\")), String::from(\"ewhjklnop\"));\n    assert_eq!(candidate(String::from(\"gf\")), String::from(\"kj\"));\n    assert_eq!(candidate(String::from(\"et\")), String::from(\"ix\"));\n    assert_eq!(candidate(String::from(\"faewfawefaewg\")), String::from(\"jeiajeaijeiak\"));\n    assert_eq!(candidate(String::from(\"hellomyfriend\")), String::from(\"lippsqcjvmirh\"));\n    assert_eq!(candidate(String::from(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")), String::from(\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\"));\n    assert_eq!(candidate(String::from(\"a\")), String::from(\"e\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_89_encrypt", "test": "}\n\nfn main() {\n    let candidate = encrypt;\n    assert_eq!(candidate(String::from(\"hi\")), String::from(\"lm\"));\n    assert_eq!(candidate(String::from(\"asdfghjkl\")), String::from(\"ewhjklnop\"));\n    assert_eq!(candidate(String::from(\"gf\")), String::from(\"kj\"));\n    assert_eq!(candidate(String::from(\"et\")), String::from(\"ix\"));\n    assert_eq!(candidate(String::from(\"faewfawefaewg\")), String::from(\"jeiajeaijeiak\"));\n    assert_eq!(candidate(String::from(\"hellomyfriend\")), String::from(\"lippsqcjvmirh\"));\n    assert_eq!(candidate(String::from(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")), String::from(\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\"));\n    assert_eq!(candidate(String::from(\"a\")), String::from(\"e\"));\n}\n"}
{"name": "HumanEval_95_check_dict_case", "language": "rs", "prompt": "use std::collections::HashMap;\n\n/// Given a HashMap, return true if all keys are strings in lower \n/// case or all keys are strings in upper case, else return false.\n/// The function should return false is the given HashMap is empty.\n/// Examples:\n/// >>> check_dict_case(HashMap::from([(String::from(\"a\"), String::from(\"apple\")), (String::from(\"b\"), String::from(\"banana\"))]))\n/// true\n/// >>> check_dict_case(HashMap::from([(String::from(\"a\"), String::from(\"apple\")), (String::from(\"A\"), String::from(\"banana\")), (String::from(\"B\"), String::from(\"banana\"))]))\n/// false\n/// >>> check_dict_case(HashMap::from([(String::from(\"a\"), String::from(\"apple\")), (8, String::from(\"banana\")), (String::from(\"a\"), String::from(\"apple\"))]))\n/// false\n/// >>> check_dict_case(HashMap::from([(String::from(\"Name\"), String::from(\"John\")), (String::from(\"Age\"), String::from(\"36\")), (String::from(\"City\"), String::from(\"Houston\"))]))\n/// false\n/// >>> check_dict_case(HashMap::from([(String::from(\"STATE\"), String::from(\"NC\")), (String::from(\"ZIP\"), String::from(\"12345\"))]))\n/// true\nfn check_dict_case(dict: HashMap<String, String>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = check_dict_case;\n    assert_eq!(candidate(HashMap::from([(String::from(\"p\"), String::from(\"pineapple\")), (String::from(\"b\"), String::from(\"banana\"))])), true);\n    assert_eq!(candidate(HashMap::from([(String::from(\"p\"), String::from(\"pineapple\")), (String::from(\"A\"), String::from(\"banana\")), (String::from(\"B\"), String::from(\"banana\"))])), false);\n    assert_eq!(candidate(HashMap::from([(String::from(\"p\"), String::from(\"pineapple\")), (String::from(\"5\"), String::from(\"banana\")), (String::from(\"a\"), String::from(\"apple\"))])), false);\n    assert_eq!(candidate(HashMap::from([(String::from(\"Name\"), String::from(\"John\")), (String::from(\"Age\"), String::from(\"36\")), (String::from(\"City\"), String::from(\"Houston\"))])), false);\n    assert_eq!(candidate(HashMap::from([(String::from(\"STATE\"), String::from(\"NC\")), (String::from(\"ZIP\"), String::from(\"12345\"))])), true);\n    assert_eq!(candidate(HashMap::from([(String::from(\"fruit\"), String::from(\"Orange\")), (String::from(\"taste\"), String::from(\"Sweet\"))])), true);\n    assert_eq!(candidate(HashMap::from([])), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_95_check_dict_case", "test": "}\n\nfn main() {\n    let candidate = check_dict_case;\n    assert_eq!(candidate(HashMap::from([(String::from(\"p\"), String::from(\"pineapple\")), (String::from(\"b\"), String::from(\"banana\"))])), true);\n    assert_eq!(candidate(HashMap::from([(String::from(\"p\"), String::from(\"pineapple\")), (String::from(\"A\"), String::from(\"banana\")), (String::from(\"B\"), String::from(\"banana\"))])), false);\n    assert_eq!(candidate(HashMap::from([(String::from(\"p\"), String::from(\"pineapple\")), (String::from(\"5\"), String::from(\"banana\")), (String::from(\"a\"), String::from(\"apple\"))])), false);\n    assert_eq!(candidate(HashMap::from([(String::from(\"Name\"), String::from(\"John\")), (String::from(\"Age\"), String::from(\"36\")), (String::from(\"City\"), String::from(\"Houston\"))])), false);\n    assert_eq!(candidate(HashMap::from([(String::from(\"STATE\"), String::from(\"NC\")), (String::from(\"ZIP\"), String::from(\"12345\"))])), true);\n    assert_eq!(candidate(HashMap::from([(String::from(\"fruit\"), String::from(\"Orange\")), (String::from(\"taste\"), String::from(\"Sweet\"))])), true);\n    assert_eq!(candidate(HashMap::from([])), false);\n}\n"}
{"name": "HumanEval_85_add", "language": "rs", "prompt": "/// Given a non-empty vector of integers lst. add the even elements that are at odd indices..\n/// Examples:\n/// >>> add(vec![4, 2, 6, 7])\n/// 2\nfn add(lst: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = add;\n    assert_eq!(candidate(vec![4, 88]), 88);\n    assert_eq!(candidate(vec![4, 5, 6, 7, 2, 122]), 122);\n    assert_eq!(candidate(vec![4, 0, 6, 7]), 0);\n    assert_eq!(candidate(vec![4, 4, 6, 8]), 12);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_85_add", "test": "}\n\nfn main() {\n    let candidate = add;\n    assert_eq!(candidate(vec![4, 88]), 88);\n    assert_eq!(candidate(vec![4, 5, 6, 7, 2, 122]), 122);\n    assert_eq!(candidate(vec![4, 0, 6, 7]), 0);\n    assert_eq!(candidate(vec![4, 4, 6, 8]), 12);\n}\n"}
{"name": "HumanEval_140_fix_spaces", "language": "rs", "prompt": "/// Given a string text, replace all spaces in it with underscores, \n/// and if a string has more than 2 consecutive spaces, \n/// then replace all consecutive spaces with - \n/// >>> fix_spaces(String::from(\" Example\"))\n/// String::from(\"Example\")\n/// >>> fix_spaces(String::from(\" Example 1\"))\n/// String::from(\"Example_1\")\n/// >>> fix_spaces(String::from(\" Example 2\"))\n/// String::from(\"_Example_2\")\n/// >>> fix_spaces(String::from(\" Example 3\"))\n/// String::from(\"_Example-3\")\nfn fix_spaces(text: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = fix_spaces;\n    assert_eq!(candidate(String::from(\"Example\")), String::from(\"Example\"));\n    assert_eq!(candidate(String::from(\"Mudasir Hanif \")), String::from(\"Mudasir_Hanif_\"));\n    assert_eq!(candidate(String::from(\"Yellow Yellow  Dirty  Fellow\")), String::from(\"Yellow_Yellow__Dirty__Fellow\"));\n    assert_eq!(candidate(String::from(\"Exa   mple\")), String::from(\"Exa-mple\"));\n    assert_eq!(candidate(String::from(\"   Exa 1 2 2 mple\")), String::from(\"-Exa_1_2_2_mple\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_140_fix_spaces", "test": "}\n\nfn main() {\n    let candidate = fix_spaces;\n    assert_eq!(candidate(String::from(\"Example\")), String::from(\"Example\"));\n    assert_eq!(candidate(String::from(\"Mudasir Hanif \")), String::from(\"Mudasir_Hanif_\"));\n    assert_eq!(candidate(String::from(\"Yellow Yellow  Dirty  Fellow\")), String::from(\"Yellow_Yellow__Dirty__Fellow\"));\n    assert_eq!(candidate(String::from(\"Exa   mple\")), String::from(\"Exa-mple\"));\n    assert_eq!(candidate(String::from(\"   Exa 1 2 2 mple\")), String::from(\"-Exa_1_2_2_mple\"));\n}\n"}
{"name": "HumanEval_63_fibfib", "language": "rs", "prompt": "/// The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n/// fibfib(0) == 0\n/// fibfib(1) == 0\n/// fibfib(2) == 1\n/// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n/// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n/// >>> fibfib(1)\n/// 0\n/// >>> fibfib(5)\n/// 4\n/// >>> fibfib(8)\n/// 24\nfn fibfib(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = fibfib;\n    assert_eq!(candidate(2), 1);\n    assert_eq!(candidate(1), 0);\n    assert_eq!(candidate(5), 4);\n    assert_eq!(candidate(8), 24);\n    assert_eq!(candidate(10), 81);\n    assert_eq!(candidate(12), 274);\n    assert_eq!(candidate(14), 927);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_63_fibfib", "test": "}\n\nfn main() {\n    let candidate = fibfib;\n    assert_eq!(candidate(2), 1);\n    assert_eq!(candidate(1), 0);\n    assert_eq!(candidate(5), 4);\n    assert_eq!(candidate(8), 24);\n    assert_eq!(candidate(10), 81);\n    assert_eq!(candidate(12), 274);\n    assert_eq!(candidate(14), 927);\n}\n"}
{"name": "HumanEval_151_double_the_difference", "language": "rs", "prompt": "/// Given a vector of numbers, return the sum of squares of the numbers\n/// in the vector that are odd. Ignore numbers that are negative or not integers.\n/// >>> double_the_difference(vec![1, 3, 2, 0])\n/// 10\n/// >>> double_the_difference(vec![-1, -2, 0])\n/// 0\n/// >>> double_the_difference(vec![9, -2])\n/// 81\n/// >>> double_the_difference(vec![0])\n/// 0\n/// If the input vector is empty, return 0.\nfn double_the_difference(lst: Vec<f64>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = double_the_difference;\n    assert_eq!(candidate(Vec::<f64>::new()), 0);\n    assert_eq!(candidate(vec![5.0, 4.0]), 25);\n    assert_eq!(candidate(vec![0.1, 0.2, 0.3]), 0);\n    assert_eq!(candidate(vec![-10.0, -20.0, -30.0]), 0);\n    assert_eq!(candidate(vec![-1.0, -2.0, 8.0]), 0);\n    assert_eq!(candidate(vec![0.2, 3.0, 5.0]), 34);\n    assert_eq!(candidate(vec![-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]), 165);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_151_double_the_difference", "test": "}\n\nfn main() {\n    let candidate = double_the_difference;\n    assert_eq!(candidate(Vec::<f64>::new()), 0);\n    assert_eq!(candidate(vec![5.0, 4.0]), 25);\n    assert_eq!(candidate(vec![0.1, 0.2, 0.3]), 0);\n    assert_eq!(candidate(vec![-10.0, -20.0, -30.0]), 0);\n    assert_eq!(candidate(vec![-1.0, -2.0, 8.0]), 0);\n    assert_eq!(candidate(vec![0.2, 3.0, 5.0]), 34);\n    assert_eq!(candidate(vec![-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]), 165);\n}\n"}
{"name": "HumanEval_41_car_race_collision", "language": "rs", "prompt": "/// Imagine a road that's a perfectly straight infinitely long line.\n/// n cars are driving left to right;  simultaneously, a different set of n cars\n/// are driving right to left.   The two sets of cars start out being very far from\n/// each other.  All cars move in the same speed.  Two cars are said to collide\n/// when a car that's moving left to right hits a car that's moving right to left.\n/// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n/// in their trajectory as if they did not collide.\n/// This function outputs the number of such collisions.\nfn car_race_collision(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = car_race_collision;\n    assert_eq!(candidate(2), 4);\n    assert_eq!(candidate(3), 9);\n    assert_eq!(candidate(4), 16);\n    assert_eq!(candidate(8), 64);\n    assert_eq!(candidate(10), 100);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_41_car_race_collision", "test": "}\n\nfn main() {\n    let candidate = car_race_collision;\n    assert_eq!(candidate(2), 4);\n    assert_eq!(candidate(3), 9);\n    assert_eq!(candidate(4), 16);\n    assert_eq!(candidate(8), 64);\n    assert_eq!(candidate(10), 100);\n}\n"}
{"name": "HumanEval_17_parse_music", "language": "rs", "prompt": "/// Input to this function is a string representing musical notes in a special ASCII format.\n/// Your task is to parse this string and return vector of integers corresponding to how many beats does each\n/// not last.\n/// Here is a legend:\n/// 'o' - whole note, lasts four beats\n/// 'o|' - half note, lasts two beats\n/// '.|' - quater note, lasts one beat\n/// >>> parse_music(String::from(\"o o| .| o| o| .| .| .| .| o o\"))\n/// vec![4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\nfn parse_music(music_string: String) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = parse_music;\n    assert_eq!(candidate(String::from(\"\")), Vec::<isize>::new());\n    assert_eq!(candidate(String::from(\"o o o o\")), vec![4, 4, 4, 4]);\n    assert_eq!(candidate(String::from(\".| .| .| .|\")), vec![1, 1, 1, 1]);\n    assert_eq!(candidate(String::from(\"o| o| .| .| o o o o\")), vec![2, 2, 1, 1, 4, 4, 4, 4]);\n    assert_eq!(candidate(String::from(\"o| .| o| .| o o| o o|\")), vec![2, 1, 2, 1, 4, 2, 4, 2]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_17_parse_music", "test": "}\n\nfn main() {\n    let candidate = parse_music;\n    assert_eq!(candidate(String::from(\"\")), Vec::<isize>::new());\n    assert_eq!(candidate(String::from(\"o o o o\")), vec![4, 4, 4, 4]);\n    assert_eq!(candidate(String::from(\".| .| .| .|\")), vec![1, 1, 1, 1]);\n    assert_eq!(candidate(String::from(\"o| o| .| .| o o o o\")), vec![2, 2, 1, 1, 4, 4, 4, 4]);\n    assert_eq!(candidate(String::from(\"o| .| o| .| o o| o o|\")), vec![2, 1, 2, 1, 4, 2, 4, 2]);\n}\n"}
{"name": "HumanEval_79_decimal_to_binary", "language": "rs", "prompt": "/// You will be given a number in decimal form and your task is to convert it to\n/// binary format. The function should return a string, with each character representing a binary\n/// number. Each character in the string will be '0' or '1'.\n/// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n/// The extra characters are there to help with the format.\n/// Examples:\n/// >>> decimal_to_binary(15)\n/// String::from(\"db1111db\")\n/// >>> decimal_to_binary(32)\n/// String::from(\"db100000db\")\nfn decimal_to_binary(decimal: isize) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = decimal_to_binary;\n    assert_eq!(candidate(0), String::from(\"db0db\"));\n    assert_eq!(candidate(32), String::from(\"db100000db\"));\n    assert_eq!(candidate(103), String::from(\"db1100111db\"));\n    assert_eq!(candidate(15), String::from(\"db1111db\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_79_decimal_to_binary", "test": "}\n\nfn main() {\n    let candidate = decimal_to_binary;\n    assert_eq!(candidate(0), String::from(\"db0db\"));\n    assert_eq!(candidate(32), String::from(\"db100000db\"));\n    assert_eq!(candidate(103), String::from(\"db1100111db\"));\n    assert_eq!(candidate(15), String::from(\"db1111db\"));\n}\n"}
{"name": "HumanEval_14_all_prefixes", "language": "rs", "prompt": "/// Return vector of all prefixes from shortest to longest of the input string\n/// >>> all_prefixes(String::from(\"abc\"))\n/// vec![String::from(\"a\"), String::from(\"ab\"), String::from(\"abc\")]\nfn all_prefixes(string: String) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = all_prefixes;\n    assert_eq!(candidate(String::from(\"\")), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"asdfgh\")), vec![String::from(\"a\"), String::from(\"as\"), String::from(\"asd\"), String::from(\"asdf\"), String::from(\"asdfg\"), String::from(\"asdfgh\")]);\n    assert_eq!(candidate(String::from(\"WWW\")), vec![String::from(\"W\"), String::from(\"WW\"), String::from(\"WWW\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_14_all_prefixes", "test": "}\n\nfn main() {\n    let candidate = all_prefixes;\n    assert_eq!(candidate(String::from(\"\")), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"asdfgh\")), vec![String::from(\"a\"), String::from(\"as\"), String::from(\"asd\"), String::from(\"asdf\"), String::from(\"asdfg\"), String::from(\"asdfgh\")]);\n    assert_eq!(candidate(String::from(\"WWW\")), vec![String::from(\"W\"), String::from(\"WW\"), String::from(\"WWW\")]);\n}\n"}
{"name": "HumanEval_53_add", "language": "rs", "prompt": "/// Add two numbers x and y\n/// >>> add(2, 3)\n/// 5\n/// >>> add(5, 7)\n/// 12\nfn add(x: isize, y: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = add;\n    assert_eq!(candidate(0, 1), 1);\n    assert_eq!(candidate(1, 0), 1);\n    assert_eq!(candidate(2, 3), 5);\n    assert_eq!(candidate(5, 7), 12);\n    assert_eq!(candidate(7, 5), 12);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_53_add", "test": "}\n\nfn main() {\n    let candidate = add;\n    assert_eq!(candidate(0, 1), 1);\n    assert_eq!(candidate(1, 0), 1);\n    assert_eq!(candidate(2, 3), 5);\n    assert_eq!(candidate(5, 7), 12);\n    assert_eq!(candidate(7, 5), 12);\n}\n"}
{"name": "HumanEval_159_eat", "language": "rs", "prompt": "/// You're a hungry rabbit, and you already have eaten a certain number of carrots,\n/// but now you need to eat more carrots to complete the day's meals.\n/// you should return a vector of [ total number of eaten carrots after your meals,\n/// the number of carrots left after your meals ]\n/// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n/// Example:\n/// >>> eat(5, 6, 10)\n/// vec![11, 4]\n/// >>> eat(4, 8, 9)\n/// vec![12, 1]\n/// >>> eat(1, 10, 10)\n/// vec![11, 0]\n/// >>> eat(2, 11, 5)\n/// vec![7, 0]\n/// Variables:\n/// @number : integer\n/// the number of carrots that you have eaten.\n/// @need : integer\n/// the number of carrots that you need to eat.\n/// @remaining : integer\n/// the number of remaining carrots thet exist in stock\n/// Constrain:\n/// * 0 <= number <= 1000\n/// * 0 <= need <= 1000\n/// * 0 <= remaining <= 1000\n/// Have fun :)\nfn eat(number: isize, need: isize, remaining: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = eat;\n    assert_eq!(candidate(5, 6, 10), vec![11, 4]);\n    assert_eq!(candidate(4, 8, 9), vec![12, 1]);\n    assert_eq!(candidate(1, 10, 10), vec![11, 0]);\n    assert_eq!(candidate(2, 11, 5), vec![7, 0]);\n    assert_eq!(candidate(4, 5, 7), vec![9, 2]);\n    assert_eq!(candidate(4, 5, 1), vec![5, 0]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_159_eat", "test": "}\n\nfn main() {\n    let candidate = eat;\n    assert_eq!(candidate(5, 6, 10), vec![11, 4]);\n    assert_eq!(candidate(4, 8, 9), vec![12, 1]);\n    assert_eq!(candidate(1, 10, 10), vec![11, 0]);\n    assert_eq!(candidate(2, 11, 5), vec![7, 0]);\n    assert_eq!(candidate(4, 5, 7), vec![9, 2]);\n    assert_eq!(candidate(4, 5, 1), vec![5, 0]);\n}\n"}
{"name": "HumanEval_115_max_fill", "language": "rs", "prompt": "/// You are given a rectangular grid of wells. Each row represents a single well,\n/// and each 1 in a row represents a single unit of water.\n/// Each well has a corresponding bucket that can be used to extract water from it, \n/// and all buckets have the same capacity.\n/// Your task is to use the buckets to empty the wells.\n/// Output the number of times you need to lower the buckets.\n/// Example 1:\n/// >>> max_fill(vec![vec![0, 0, 1, 0], vec![0, 1, 0, 0], vec![1, 1, 1, 1]], 1)\n/// 6\n/// Example 2:\n/// >>> max_fill(vec![vec![0, 0, 1, 1], vec![0, 0, 0, 0], vec![1, 1, 1, 1], vec![0, 1, 1, 1]], 2)\n/// 5\n/// Example 3:\n/// >>> max_fill(vec![vec![0, 0, 0], vec![0, 0, 0]], 5)\n/// 0\n/// Constraints:\n/// * all wells have the same length\n/// * 1 <= grid.length <= 10^2\n/// * 1 <= grid[:,1].length <= 10^2\n/// * grid[i][j] -> 0 | 1\n/// * 1 <= capacity <= 10\nfn max_fill(grid: Vec<Vec<isize>>, capacity: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = max_fill;\n    assert_eq!(candidate(vec![vec![0, 0, 1, 0], vec![0, 1, 0, 0], vec![1, 1, 1, 1]], 1), 6);\n    assert_eq!(candidate(vec![vec![0, 0, 1, 1], vec![0, 0, 0, 0], vec![1, 1, 1, 1], vec![0, 1, 1, 1]], 2), 5);\n    assert_eq!(candidate(vec![vec![0, 0, 0], vec![0, 0, 0]], 5), 0);\n    assert_eq!(candidate(vec![vec![1, 1, 1, 1], vec![1, 1, 1, 1]], 2), 4);\n    assert_eq!(candidate(vec![vec![1, 1, 1, 1], vec![1, 1, 1, 1]], 9), 2);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_115_max_fill", "test": "}\n\nfn main() {\n    let candidate = max_fill;\n    assert_eq!(candidate(vec![vec![0, 0, 1, 0], vec![0, 1, 0, 0], vec![1, 1, 1, 1]], 1), 6);\n    assert_eq!(candidate(vec![vec![0, 0, 1, 1], vec![0, 0, 0, 0], vec![1, 1, 1, 1], vec![0, 1, 1, 1]], 2), 5);\n    assert_eq!(candidate(vec![vec![0, 0, 0], vec![0, 0, 0]], 5), 0);\n    assert_eq!(candidate(vec![vec![1, 1, 1, 1], vec![1, 1, 1, 1]], 2), 4);\n    assert_eq!(candidate(vec![vec![1, 1, 1, 1], vec![1, 1, 1, 1]], 9), 2);\n}\n"}
{"name": "HumanEval_160_do_algebra", "language": "rs", "prompt": "/// Given two vectors operator, and operand. The first vector has basic algebra operations, and \n/// the second vector is a vector of integers. Use the two given vectors to build the algebric \n/// expression and return the evaluation of this expression.\n/// The basic algebra operations:\n/// Addition ( + ) \n/// Subtraction ( - ) \n/// Multiplication ( * ) \n/// Floor division ( // ) \n/// Exponentiation ( ** ) \n/// Example:\n/// operator['+', '*', '-']\n/// vector = [2, 3, 4, 5]\n/// result = 2 + 3 * 4 - 5\n/// => result = 9\n/// Note:\n/// The length of operator vector is equal to the length of operand vector minus one.\n/// Operand is a vector of of non-negative integers.\n/// Operator vector has at least one operator, and operand vector has at least two operands.\nfn do_algebra(operator: Vec<String>, operand: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = do_algebra;\n    assert_eq!(candidate(vec![String::from(\"**\"), String::from(\"*\"), String::from(\"+\")], vec![2, 3, 4, 5]), 37);\n    assert_eq!(candidate(vec![String::from(\"+\"), String::from(\"*\"), String::from(\"-\")], vec![2, 3, 4, 5]), 9);\n    assert_eq!(candidate(vec![String::from(\"//\"), String::from(\"*\")], vec![7, 3, 4]), 8);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_160_do_algebra", "test": "}\n\nfn main() {\n    let candidate = do_algebra;\n    assert_eq!(candidate(vec![String::from(\"**\"), String::from(\"*\"), String::from(\"+\")], vec![2, 3, 4, 5]), 37);\n    assert_eq!(candidate(vec![String::from(\"+\"), String::from(\"*\"), String::from(\"-\")], vec![2, 3, 4, 5]), 9);\n    assert_eq!(candidate(vec![String::from(\"//\"), String::from(\"*\")], vec![7, 3, 4]), 8);\n}\n"}
{"name": "HumanEval_27_flip_case", "language": "rs", "prompt": "/// For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n/// >>> flip_case(String::from(\"Hello\"))\n/// String::from(\"hELLO\")\nfn flip_case(string: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = flip_case;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"Hello!\")), String::from(\"hELLO!\"));\n    assert_eq!(candidate(String::from(\"These violent delights have violent ends\")), String::from(\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_27_flip_case", "test": "}\n\nfn main() {\n    let candidate = flip_case;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"Hello!\")), String::from(\"hELLO!\"));\n    assert_eq!(candidate(String::from(\"These violent delights have violent ends\")), String::from(\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\"));\n}\n"}
{"name": "HumanEval_105_by_length", "language": "rs", "prompt": "/// Given a vector of integers, sort the integers that are between 1 and 9 inclusive,\n/// reverse the resulting vector, and then replace each digit by its corresponding name from\n/// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n/// For example:\n/// >>> by_length(vec![2, 1, 1, 4, 5, 8, 2, 3])\n/// vec![String::from(\"Eight\"), String::from(\"Five\"), String::from(\"Four\"), String::from(\"Three\"), String::from(\"Two\"), String::from(\"Two\"), String::from(\"One\"), String::from(\"One\")]\n/// If the vector is empty, return an empty vector:\n/// >>> by_length(vec![])\n/// Vec::<String>::new()\n/// If the vector has any strange number ignore it:\n/// >>> by_length(vec![1, -1, 55])\n/// vec![String::from(\"One\")]\nfn by_length(arr: Vec<isize>) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = by_length;\n    assert_eq!(candidate(vec![2, 1, 1, 4, 5, 8, 2, 3]), vec![String::from(\"Eight\"), String::from(\"Five\"), String::from(\"Four\"), String::from(\"Three\"), String::from(\"Two\"), String::from(\"Two\"), String::from(\"One\"), String::from(\"One\")]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<String>::new());\n    assert_eq!(candidate(vec![1, -1, 55]), vec![String::from(\"One\")]);\n    assert_eq!(candidate(vec![1, -1, 3, 2]), vec![String::from(\"Three\"), String::from(\"Two\"), String::from(\"One\")]);\n    assert_eq!(candidate(vec![9, 4, 8]), vec![String::from(\"Nine\"), String::from(\"Eight\"), String::from(\"Four\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_105_by_length", "test": "}\n\nfn main() {\n    let candidate = by_length;\n    assert_eq!(candidate(vec![2, 1, 1, 4, 5, 8, 2, 3]), vec![String::from(\"Eight\"), String::from(\"Five\"), String::from(\"Four\"), String::from(\"Three\"), String::from(\"Two\"), String::from(\"Two\"), String::from(\"One\"), String::from(\"One\")]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<String>::new());\n    assert_eq!(candidate(vec![1, -1, 55]), vec![String::from(\"One\")]);\n    assert_eq!(candidate(vec![1, -1, 3, 2]), vec![String::from(\"Three\"), String::from(\"Two\"), String::from(\"One\")]);\n    assert_eq!(candidate(vec![9, 4, 8]), vec![String::from(\"Nine\"), String::from(\"Eight\"), String::from(\"Four\")]);\n}\n"}
{"name": "HumanEval_25_factorize", "language": "rs", "prompt": "/// Return vector of prime factors of given integer in the order from smallest to largest.\n/// Each of the factors should be vectored number of times corresponding to how many times it appeares in factorization.\n/// Input number should be equal to the product of all factors\n/// >>> factorize(8)\n/// vec![2, 2, 2]\n/// >>> factorize(25)\n/// vec![5, 5]\n/// >>> factorize(70)\n/// vec![2, 5, 7]\nfn factorize(n: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = factorize;\n    assert_eq!(candidate(2), vec![2]);\n    assert_eq!(candidate(4), vec![2, 2]);\n    assert_eq!(candidate(8), vec![2, 2, 2]);\n    assert_eq!(candidate(57), vec![3, 19]);\n    assert_eq!(candidate(3249), vec![3, 3, 19, 19]);\n    assert_eq!(candidate(185193), vec![3, 3, 3, 19, 19, 19]);\n    assert_eq!(candidate(20577), vec![3, 19, 19, 19]);\n    assert_eq!(candidate(18), vec![2, 3, 3]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_25_factorize", "test": "}\n\nfn main() {\n    let candidate = factorize;\n    assert_eq!(candidate(2), vec![2]);\n    assert_eq!(candidate(4), vec![2, 2]);\n    assert_eq!(candidate(8), vec![2, 2, 2]);\n    assert_eq!(candidate(57), vec![3, 19]);\n    assert_eq!(candidate(3249), vec![3, 3, 19, 19]);\n    assert_eq!(candidate(185193), vec![3, 3, 3, 19, 19, 19]);\n    assert_eq!(candidate(20577), vec![3, 19, 19, 19]);\n    assert_eq!(candidate(18), vec![2, 3, 3]);\n}\n"}
{"name": "HumanEval_96_count_up_to", "language": "rs", "prompt": "/// Implement a function that takes an non-negative integer and returns a vector of the first n\n/// integers that are prime numbers and less than n.\n/// for example:\n/// >>> count_up_to(5)\n/// vec![2, 3]\n/// >>> count_up_to(11)\n/// vec![2, 3, 5, 7]\n/// >>> count_up_to(0)\n/// Vec::<isize>::new()\n/// >>> count_up_to(20)\n/// vec![2, 3, 5, 7, 11, 13, 17, 19]\n/// >>> count_up_to(1)\n/// Vec::<isize>::new()\n/// >>> count_up_to(18)\n/// vec![2, 3, 5, 7, 11, 13, 17]\nfn count_up_to(n: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = count_up_to;\n    assert_eq!(candidate(5), vec![2, 3]);\n    assert_eq!(candidate(6), vec![2, 3, 5]);\n    assert_eq!(candidate(7), vec![2, 3, 5]);\n    assert_eq!(candidate(10), vec![2, 3, 5, 7]);\n    assert_eq!(candidate(0), Vec::<isize>::new());\n    assert_eq!(candidate(22), vec![2, 3, 5, 7, 11, 13, 17, 19]);\n    assert_eq!(candidate(1), Vec::<isize>::new());\n    assert_eq!(candidate(18), vec![2, 3, 5, 7, 11, 13, 17]);\n    assert_eq!(candidate(47), vec![2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43]);\n    assert_eq!(candidate(101), vec![2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_96_count_up_to", "test": "}\n\nfn main() {\n    let candidate = count_up_to;\n    assert_eq!(candidate(5), vec![2, 3]);\n    assert_eq!(candidate(6), vec![2, 3, 5]);\n    assert_eq!(candidate(7), vec![2, 3, 5]);\n    assert_eq!(candidate(10), vec![2, 3, 5, 7]);\n    assert_eq!(candidate(0), Vec::<isize>::new());\n    assert_eq!(candidate(22), vec![2, 3, 5, 7, 11, 13, 17, 19]);\n    assert_eq!(candidate(1), Vec::<isize>::new());\n    assert_eq!(candidate(18), vec![2, 3, 5, 7, 11, 13, 17]);\n    assert_eq!(candidate(47), vec![2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43]);\n    assert_eq!(candidate(101), vec![2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97]);\n}\n"}
{"name": "HumanEval_34_unique", "language": "rs", "prompt": "/// Return sorted unique elements in a vector\n/// >>> unique(vec![5, 3, 5, 2, 3, 3, 9, 0, 123])\n/// vec![0, 2, 3, 5, 9, 123]\nfn unique(l: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = unique;\n    assert_eq!(candidate(vec![5, 3, 5, 2, 3, 3, 9, 0, 123]), vec![0, 2, 3, 5, 9, 123]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_34_unique", "test": "}\n\nfn main() {\n    let candidate = unique;\n    assert_eq!(candidate(vec![5, 3, 5, 2, 3, 3, 9, 0, 123]), vec![0, 2, 3, 5, 9, 123]);\n}\n"}
{"name": "HumanEval_74_total_match", "language": "rs", "prompt": "/// Write a function that accepts two vectors of strings and returns the vector that has \n/// total number of chars in the all strings of the vector less than the other vector.\n/// if the two vectors have the same number of chars, return the first vector.\n/// Examples\n/// >>> total_match(vec![], vec![])\n/// Vec::<String>::new()\n/// >>> total_match(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"Hi\")])\n/// vec![String::from(\"hI\"), String::from(\"Hi\")]\n/// >>> total_match(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hi\"), String::from(\"hi\"), String::from(\"admin\"), String::from(\"project\")])\n/// vec![String::from(\"hi\"), String::from(\"admin\")]\n/// >>> total_match(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hi\")])\n/// vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hi\")]\n/// >>> total_match(vec![String::from(\"4\")], vec![String::from(\"1\"), String::from(\"2\"), String::from(\"3\"), String::from(\"4\"), String::from(\"5\")])\n/// vec![String::from(\"4\")]\nfn total_match(lst1: Vec<String>, lst2: Vec<String>) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = total_match;\n    assert_eq!(candidate(Vec::<String>::new(), Vec::<String>::new()), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hi\"), String::from(\"hi\")]), vec![String::from(\"hi\"), String::from(\"hi\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hi\"), String::from(\"hi\"), String::from(\"admin\"), String::from(\"project\")]), vec![String::from(\"hi\"), String::from(\"admin\")]);\n    assert_eq!(candidate(vec![String::from(\"4\")], vec![String::from(\"1\"), String::from(\"2\"), String::from(\"3\"), String::from(\"4\"), String::from(\"5\")]), vec![String::from(\"4\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"Hi\")]), vec![String::from(\"hI\"), String::from(\"Hi\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hi\")]), vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hi\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hii\")]), vec![String::from(\"hi\"), String::from(\"admin\")]);\n    assert_eq!(candidate(Vec::<String>::new(), vec![String::from(\"this\")]), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"this\")], Vec::<String>::new()), Vec::<String>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_74_total_match", "test": "}\n\nfn main() {\n    let candidate = total_match;\n    assert_eq!(candidate(Vec::<String>::new(), Vec::<String>::new()), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hi\"), String::from(\"hi\")]), vec![String::from(\"hi\"), String::from(\"hi\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hi\"), String::from(\"hi\"), String::from(\"admin\"), String::from(\"project\")]), vec![String::from(\"hi\"), String::from(\"admin\")]);\n    assert_eq!(candidate(vec![String::from(\"4\")], vec![String::from(\"1\"), String::from(\"2\"), String::from(\"3\"), String::from(\"4\"), String::from(\"5\")]), vec![String::from(\"4\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"Hi\")]), vec![String::from(\"hI\"), String::from(\"Hi\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hi\")]), vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hi\")]);\n    assert_eq!(candidate(vec![String::from(\"hi\"), String::from(\"admin\")], vec![String::from(\"hI\"), String::from(\"hi\"), String::from(\"hii\")]), vec![String::from(\"hi\"), String::from(\"admin\")]);\n    assert_eq!(candidate(Vec::<String>::new(), vec![String::from(\"this\")]), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"this\")], Vec::<String>::new()), Vec::<String>::new());\n}\n"}
{"name": "HumanEval_35_max_element", "language": "rs", "prompt": "/// Return maximum element in the vector.\n/// >>> max_element(vec![1, 2, 3])\n/// 3\n/// >>> max_element(vec![5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n/// 123\nfn max_element(l: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = max_element;\n    assert_eq!(candidate(vec![1, 2, 3]), 3);\n    assert_eq!(candidate(vec![5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10]), 124);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_35_max_element", "test": "}\n\nfn main() {\n    let candidate = max_element;\n    assert_eq!(candidate(vec![1, 2, 3]), 3);\n    assert_eq!(candidate(vec![5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10]), 124);\n}\n"}
{"name": "HumanEval_132_is_nested", "language": "rs", "prompt": "/// Create a function that takes a string as input which contains only square brackets.\n/// The function should return true if and only if there is a valid subsequence of brackets \n/// where at least one bracket in the subsequence is nested.\n/// >>> is_nested(String::from(\"[[]]\"))\n/// true\n/// >>> is_nested(String::from(\"[]]]]]]][[[[[]\"))\n/// false\n/// >>> is_nested(String::from(\"[][]\"))\n/// false\n/// >>> is_nested(String::from(\"[]\"))\n/// false\n/// >>> is_nested(String::from(\"[[][]]\"))\n/// true\n/// >>> is_nested(String::from(\"[[]][[\"))\n/// true\nfn is_nested(string: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_nested;\n    assert_eq!(candidate(String::from(\"[[]]\")), true);\n    assert_eq!(candidate(String::from(\"[]]]]]]][[[[[]\")), false);\n    assert_eq!(candidate(String::from(\"[][]\")), false);\n    assert_eq!(candidate(String::from(\"[]\")), false);\n    assert_eq!(candidate(String::from(\"[[[[]]]]\")), true);\n    assert_eq!(candidate(String::from(\"[]]]]]]]]]]\")), false);\n    assert_eq!(candidate(String::from(\"[][][[]]\")), true);\n    assert_eq!(candidate(String::from(\"[[]\")), false);\n    assert_eq!(candidate(String::from(\"[]]\")), false);\n    assert_eq!(candidate(String::from(\"[[]][[\")), true);\n    assert_eq!(candidate(String::from(\"[[][]]\")), true);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"[[[[[[[[\")), false);\n    assert_eq!(candidate(String::from(\"]]]]]]]]\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_132_is_nested", "test": "}\n\nfn main() {\n    let candidate = is_nested;\n    assert_eq!(candidate(String::from(\"[[]]\")), true);\n    assert_eq!(candidate(String::from(\"[]]]]]]][[[[[]\")), false);\n    assert_eq!(candidate(String::from(\"[][]\")), false);\n    assert_eq!(candidate(String::from(\"[]\")), false);\n    assert_eq!(candidate(String::from(\"[[[[]]]]\")), true);\n    assert_eq!(candidate(String::from(\"[]]]]]]]]]]\")), false);\n    assert_eq!(candidate(String::from(\"[][][[]]\")), true);\n    assert_eq!(candidate(String::from(\"[[]\")), false);\n    assert_eq!(candidate(String::from(\"[]]\")), false);\n    assert_eq!(candidate(String::from(\"[[]][[\")), true);\n    assert_eq!(candidate(String::from(\"[[][]]\")), true);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"[[[[[[[[\")), false);\n    assert_eq!(candidate(String::from(\"]]]]]]]]\")), false);\n}\n"}
{"name": "HumanEval_113_odd_count", "language": "rs", "prompt": "/// Given a vector of strings, where each string consists of only digits, return a vector.\n/// Each element i of the output should be \"the number of odd elements in the\n/// string i of the input.\" where all the i's should be replaced by the number\n/// of odd digits in the i'th string of the input.\n/// >>> odd_count(vec![String::from(\"1234567\")])\n/// vec![String::from(\"the number of odd elements 4n the str4ng 4 of the 4nput.\")]\n/// >>> odd_count(vec![String::from(\"3\"), String::from(\"11111111\")])\n/// vec![String::from(\"the number of odd elements 1n the str1ng 1 of the 1nput.\"), String::from(\"the number of odd elements 8n the str8ng 8 of the 8nput.\")]\nfn odd_count(lst: Vec<String>) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = odd_count;\n    assert_eq!(candidate(vec![String::from(\"1234567\")]), vec![String::from(\"the number of odd elements 4n the str4ng 4 of the 4nput.\")]);\n    assert_eq!(candidate(vec![String::from(\"3\"), String::from(\"11111111\")]), vec![String::from(\"the number of odd elements 1n the str1ng 1 of the 1nput.\"), String::from(\"the number of odd elements 8n the str8ng 8 of the 8nput.\")]);\n    assert_eq!(candidate(vec![String::from(\"271\"), String::from(\"137\"), String::from(\"314\")]), vec![String::from(\"the number of odd elements 2n the str2ng 2 of the 2nput.\"), String::from(\"the number of odd elements 3n the str3ng 3 of the 3nput.\"), String::from(\"the number of odd elements 2n the str2ng 2 of the 2nput.\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_113_odd_count", "test": "}\n\nfn main() {\n    let candidate = odd_count;\n    assert_eq!(candidate(vec![String::from(\"1234567\")]), vec![String::from(\"the number of odd elements 4n the str4ng 4 of the 4nput.\")]);\n    assert_eq!(candidate(vec![String::from(\"3\"), String::from(\"11111111\")]), vec![String::from(\"the number of odd elements 1n the str1ng 1 of the 1nput.\"), String::from(\"the number of odd elements 8n the str8ng 8 of the 8nput.\")]);\n    assert_eq!(candidate(vec![String::from(\"271\"), String::from(\"137\"), String::from(\"314\")]), vec![String::from(\"the number of odd elements 2n the str2ng 2 of the 2nput.\"), String::from(\"the number of odd elements 3n the str3ng 3 of the 3nput.\"), String::from(\"the number of odd elements 2n the str2ng 2 of the 2nput.\")]);\n}\n"}
{"name": "HumanEval_109_move_one_ball", "language": "rs", "prompt": "/// We have a vector 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n/// numbers in the vector will be randomly ordered. Your task is to determine if\n/// it is possible to get a vector sorted in non-decreasing order by performing \n/// the following operation on the given vector:\n/// You are allowed to perform right shift operation any number of times.\n/// One right shift operation means shifting all elements of the vector by one\n/// position in the right direction. The last element of the vector will be moved to\n/// the starting position in the vector i.e. 0th index. \n/// If it is possible to obtain the sorted vector by performing the above operation\n/// then return true else return false.\n/// If the given vector is empty then return true.\n/// Note: The given vector is guaranteed to have unique elements.\n/// For Example:\n/// >>> move_one_ball(vec![3, 4, 5, 1, 2])\n/// true\n/// Explanation: By performin 2 right shift operations, non-decreasing order can\n/// be achieved for the given vector.\n/// >>> move_one_ball(vec![3, 5, 4, 1, 2])\n/// false\n/// Explanation:It is not possible to get non-decreasing order for the given\n/// vector by performing any number of right shift operations.\nfn move_one_ball(arr: Vec<isize>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = move_one_ball;\n    assert_eq!(candidate(vec![3, 4, 5, 1, 2]), true);\n    assert_eq!(candidate(vec![3, 5, 10, 1, 2]), true);\n    assert_eq!(candidate(vec![4, 3, 1, 2]), false);\n    assert_eq!(candidate(vec![3, 5, 4, 1, 2]), false);\n    assert_eq!(candidate(Vec::<isize>::new()), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_109_move_one_ball", "test": "}\n\nfn main() {\n    let candidate = move_one_ball;\n    assert_eq!(candidate(vec![3, 4, 5, 1, 2]), true);\n    assert_eq!(candidate(vec![3, 5, 10, 1, 2]), true);\n    assert_eq!(candidate(vec![4, 3, 1, 2]), false);\n    assert_eq!(candidate(vec![3, 5, 4, 1, 2]), false);\n    assert_eq!(candidate(Vec::<isize>::new()), true);\n}\n"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "rs", "prompt": "/// Given a positive integer n, return a tuple that has the number of even and odd\n/// integer palindromes that fall within the range(1, n), inclusive.\n/// Example 1:\n/// >>> even_odd_palindrome(3)\n/// (1, 2)\n/// Explanation:\n/// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n/// Example 2:\n/// >>> even_odd_palindrome(12)\n/// (4, 6)\n/// Explanation:\n/// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n/// Note:\n/// 1. 1 <= n <= 10^3\n/// 2. returned tuple has the number of even and odd integer palindromes respectively.\nfn even_odd_palindrome(n: isize) -> (isize, isize) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = even_odd_palindrome;\n    assert_eq!(candidate(123), (8, 13));\n    assert_eq!(candidate(12), (4, 6));\n    assert_eq!(candidate(3), (1, 2));\n    assert_eq!(candidate(63), (6, 8));\n    assert_eq!(candidate(25), (5, 6));\n    assert_eq!(candidate(19), (4, 6));\n    assert_eq!(candidate(9), (4, 5));\n    assert_eq!(candidate(1), (0, 1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "}\n\nfn main() {\n    let candidate = even_odd_palindrome;\n    assert_eq!(candidate(123), (8, 13));\n    assert_eq!(candidate(12), (4, 6));\n    assert_eq!(candidate(3), (1, 2));\n    assert_eq!(candidate(63), (6, 8));\n    assert_eq!(candidate(25), (5, 6));\n    assert_eq!(candidate(19), (4, 6));\n    assert_eq!(candidate(9), (4, 5));\n    assert_eq!(candidate(1), (0, 1));\n}\n"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "rs", "prompt": "/// Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n/// Example\n/// >>> is_equal_to_sum_even(4)\n/// false\n/// >>> is_equal_to_sum_even(6)\n/// false\n/// >>> is_equal_to_sum_even(8)\n/// true\nfn is_equal_to_sum_even(n: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_equal_to_sum_even;\n    assert_eq!(candidate(4), false);\n    assert_eq!(candidate(6), false);\n    assert_eq!(candidate(8), true);\n    assert_eq!(candidate(10), true);\n    assert_eq!(candidate(11), false);\n    assert_eq!(candidate(12), true);\n    assert_eq!(candidate(13), false);\n    assert_eq!(candidate(16), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "}\n\nfn main() {\n    let candidate = is_equal_to_sum_even;\n    assert_eq!(candidate(4), false);\n    assert_eq!(candidate(6), false);\n    assert_eq!(candidate(8), true);\n    assert_eq!(candidate(10), true);\n    assert_eq!(candidate(11), false);\n    assert_eq!(candidate(12), true);\n    assert_eq!(candidate(13), false);\n    assert_eq!(candidate(16), true);\n}\n"}
{"name": "HumanEval_62_derivative", "language": "rs", "prompt": "/// xs represent coefficients of a polynomial.\n/// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n/// Return derivative of this polynomial in the same form.\n/// >>> derivative(vec![3, 1, 2, 4, 5])\n/// vec![1, 4, 12, 20]\n/// >>> derivative(vec![1, 2, 3])\n/// vec![2, 6]\nfn derivative(xs: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = derivative;\n    assert_eq!(candidate(vec![3, 1, 2, 4, 5]), vec![1, 4, 12, 20]);\n    assert_eq!(candidate(vec![1, 2, 3]), vec![2, 6]);\n    assert_eq!(candidate(vec![3, 2, 1]), vec![2, 2]);\n    assert_eq!(candidate(vec![3, 2, 1, 0, 4]), vec![2, 2, 0, 16]);\n    assert_eq!(candidate(vec![1]), Vec::<isize>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_62_derivative", "test": "}\n\nfn main() {\n    let candidate = derivative;\n    assert_eq!(candidate(vec![3, 1, 2, 4, 5]), vec![1, 4, 12, 20]);\n    assert_eq!(candidate(vec![1, 2, 3]), vec![2, 6]);\n    assert_eq!(candidate(vec![3, 2, 1]), vec![2, 2]);\n    assert_eq!(candidate(vec![3, 2, 1, 0, 4]), vec![2, 2, 0, 16]);\n    assert_eq!(candidate(vec![1]), Vec::<isize>::new());\n}\n"}
{"name": "HumanEval_126_is_sorted", "language": "rs", "prompt": "/// Given a vector of numbers, return whether or not they are sorted\n/// in ascending order. If vector has more than 1 duplicate of the same\n/// number, return false. Assume no negative numbers and only integers.\n/// Examples\n/// >>> is_sorted(vec![5])\n/// true\n/// >>> is_sorted(vec![1, 2, 3, 4, 5])\n/// true\n/// >>> is_sorted(vec![1, 3, 2, 4, 5])\n/// false\n/// >>> is_sorted(vec![1, 2, 3, 4, 5, 6])\n/// true\n/// >>> is_sorted(vec![1, 2, 3, 4, 5, 6, 7])\n/// true\n/// >>> is_sorted(vec![1, 3, 2, 4, 5, 6, 7])\n/// false\n/// >>> is_sorted(vec![1, 2, 2, 3, 3, 4])\n/// true\n/// >>> is_sorted(vec![1, 2, 2, 2, 3, 4])\n/// false\nfn is_sorted(lst: Vec<isize>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_sorted;\n    assert_eq!(candidate(vec![5]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5]), true);\n    assert_eq!(candidate(vec![1, 3, 2, 4, 5]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6, 7]), true);\n    assert_eq!(candidate(vec![1, 3, 2, 4, 5, 6, 7]), false);\n    assert_eq!(candidate(Vec::<isize>::new()), true);\n    assert_eq!(candidate(vec![1]), true);\n    assert_eq!(candidate(vec![3, 2, 1]), false);\n    assert_eq!(candidate(vec![1, 2, 2, 2, 3, 4]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 3, 3, 4]), false);\n    assert_eq!(candidate(vec![1, 2, 2, 3, 3, 4]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 4]), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_126_is_sorted", "test": "}\n\nfn main() {\n    let candidate = is_sorted;\n    assert_eq!(candidate(vec![5]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5]), true);\n    assert_eq!(candidate(vec![1, 3, 2, 4, 5]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6, 7]), true);\n    assert_eq!(candidate(vec![1, 3, 2, 4, 5, 6, 7]), false);\n    assert_eq!(candidate(Vec::<isize>::new()), true);\n    assert_eq!(candidate(vec![1]), true);\n    assert_eq!(candidate(vec![3, 2, 1]), false);\n    assert_eq!(candidate(vec![1, 2, 2, 2, 3, 4]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 3, 3, 4]), false);\n    assert_eq!(candidate(vec![1, 2, 2, 3, 3, 4]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 4]), true);\n}\n"}
{"name": "HumanEval_161_solve", "language": "rs", "prompt": "/// You are given a string s.\n/// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n/// otherwise keep it as it is.\n/// If the string contains no letters, reverse the string.\n/// The function should return the resulted string.\n/// Examples\n/// >>> solve(String::from(\"1234\"))\n/// String::from(\"4321\")\n/// >>> solve(String::from(\"ab\"))\n/// String::from(\"AB\")\n/// >>> solve(String::from(\"#a@C\"))\n/// String::from(\"#A@c\")\nfn solve(s: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = solve;\n    assert_eq!(candidate(String::from(\"AsDf\")), String::from(\"aSdF\"));\n    assert_eq!(candidate(String::from(\"1234\")), String::from(\"4321\"));\n    assert_eq!(candidate(String::from(\"ab\")), String::from(\"AB\"));\n    assert_eq!(candidate(String::from(\"#a@C\")), String::from(\"#A@c\"));\n    assert_eq!(candidate(String::from(\"#AsdfW^45\")), String::from(\"#aSDFw^45\"));\n    assert_eq!(candidate(String::from(\"#6@2\")), String::from(\"2@6#\"));\n    assert_eq!(candidate(String::from(\"#$a^D\")), String::from(\"#$A^d\"));\n    assert_eq!(candidate(String::from(\"#ccc\")), String::from(\"#CCC\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_161_solve", "test": "}\n\nfn main() {\n    let candidate = solve;\n    assert_eq!(candidate(String::from(\"AsDf\")), String::from(\"aSdF\"));\n    assert_eq!(candidate(String::from(\"1234\")), String::from(\"4321\"));\n    assert_eq!(candidate(String::from(\"ab\")), String::from(\"AB\"));\n    assert_eq!(candidate(String::from(\"#a@C\")), String::from(\"#A@c\"));\n    assert_eq!(candidate(String::from(\"#AsdfW^45\")), String::from(\"#aSDFw^45\"));\n    assert_eq!(candidate(String::from(\"#6@2\")), String::from(\"2@6#\"));\n    assert_eq!(candidate(String::from(\"#$a^D\")), String::from(\"#$A^d\"));\n    assert_eq!(candidate(String::from(\"#ccc\")), String::from(\"#CCC\"));\n}\n"}
{"name": "HumanEval_130_tri", "language": "rs", "prompt": "/// Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n/// the last couple centuries. However, what people don't know is Tribonacci sequence.\n/// Tribonacci sequence is defined by the recurrence:\n/// tri(1) = 3\n/// tri(n) = 1 + n / 2, if n is even.\n/// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n/// For example:\n/// tri(2) = 1 + (2 / 2) = 2\n/// tri(4) = 3\n/// tri(3) = tri(2) + tri(1) + tri(4)\n/// = 2 + 3 + 3 = 8 \n/// You are given a non-negative integer number n, you have to a return a vector of the \n/// first n + 1 numbers of the Tribonacci sequence.\n/// Examples:\n/// >>> tri(3)\n/// vec![1, 3, 2, 8]\nfn tri(n: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = tri;\n    assert_eq!(candidate(3), vec![1, 3, 2, 8]);\n    assert_eq!(candidate(4), vec![1, 3, 2, 8, 3]);\n    assert_eq!(candidate(5), vec![1, 3, 2, 8, 3, 15]);\n    assert_eq!(candidate(6), vec![1, 3, 2, 8, 3, 15, 4]);\n    assert_eq!(candidate(7), vec![1, 3, 2, 8, 3, 15, 4, 24]);\n    assert_eq!(candidate(8), vec![1, 3, 2, 8, 3, 15, 4, 24, 5]);\n    assert_eq!(candidate(9), vec![1, 3, 2, 8, 3, 15, 4, 24, 5, 35]);\n    assert_eq!(candidate(20), vec![1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11]);\n    assert_eq!(candidate(0), vec![1]);\n    assert_eq!(candidate(1), vec![1, 3]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_130_tri", "test": "}\n\nfn main() {\n    let candidate = tri;\n    assert_eq!(candidate(3), vec![1, 3, 2, 8]);\n    assert_eq!(candidate(4), vec![1, 3, 2, 8, 3]);\n    assert_eq!(candidate(5), vec![1, 3, 2, 8, 3, 15]);\n    assert_eq!(candidate(6), vec![1, 3, 2, 8, 3, 15, 4]);\n    assert_eq!(candidate(7), vec![1, 3, 2, 8, 3, 15, 4, 24]);\n    assert_eq!(candidate(8), vec![1, 3, 2, 8, 3, 15, 4, 24, 5]);\n    assert_eq!(candidate(9), vec![1, 3, 2, 8, 3, 15, 4, 24, 5, 35]);\n    assert_eq!(candidate(20), vec![1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11]);\n    assert_eq!(candidate(0), vec![1]);\n    assert_eq!(candidate(1), vec![1, 3]);\n}\n"}
{"name": "HumanEval_36_fizz_buzz", "language": "rs", "prompt": "/// Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n/// >>> fizz_buzz(50)\n/// 0\n/// >>> fizz_buzz(78)\n/// 2\n/// >>> fizz_buzz(79)\n/// 3\nfn fizz_buzz(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = fizz_buzz;\n    assert_eq!(candidate(50), 0);\n    assert_eq!(candidate(78), 2);\n    assert_eq!(candidate(79), 3);\n    assert_eq!(candidate(100), 3);\n    assert_eq!(candidate(200), 6);\n    assert_eq!(candidate(4000), 192);\n    assert_eq!(candidate(10000), 639);\n    assert_eq!(candidate(100000), 8026);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_36_fizz_buzz", "test": "}\n\nfn main() {\n    let candidate = fizz_buzz;\n    assert_eq!(candidate(50), 0);\n    assert_eq!(candidate(78), 2);\n    assert_eq!(candidate(79), 3);\n    assert_eq!(candidate(100), 3);\n    assert_eq!(candidate(200), 6);\n    assert_eq!(candidate(4000), 192);\n    assert_eq!(candidate(10000), 639);\n    assert_eq!(candidate(100000), 8026);\n}\n"}
{"name": "HumanEval_29_filter_by_prefix", "language": "rs", "prompt": "/// Filter an input vector of strings only for ones that start with a given prefix.\n/// >>> filter_by_prefix(vec![], String::from(\"a\"))\n/// Vec::<String>::new()\n/// >>> filter_by_prefix(vec![String::from(\"abc\"), String::from(\"bcd\"), String::from(\"cde\"), String::from(\"array\")], String::from(\"a\"))\n/// vec![String::from(\"abc\"), String::from(\"array\")]\nfn filter_by_prefix(strings: Vec<String>, prefix: String) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = filter_by_prefix;\n    assert_eq!(candidate(Vec::<String>::new(), String::from(\"john\")), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"xxx\"), String::from(\"asd\"), String::from(\"xxy\"), String::from(\"john doe\"), String::from(\"xxxAAA\"), String::from(\"xxx\")], String::from(\"xxx\")), vec![String::from(\"xxx\"), String::from(\"xxxAAA\"), String::from(\"xxx\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_29_filter_by_prefix", "test": "}\n\nfn main() {\n    let candidate = filter_by_prefix;\n    assert_eq!(candidate(Vec::<String>::new(), String::from(\"john\")), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"xxx\"), String::from(\"asd\"), String::from(\"xxy\"), String::from(\"john doe\"), String::from(\"xxxAAA\"), String::from(\"xxx\")], String::from(\"xxx\")), vec![String::from(\"xxx\"), String::from(\"xxxAAA\"), String::from(\"xxx\")]);\n}\n"}
{"name": "HumanEval_84_solve", "language": "rs", "prompt": "/// Given a positive integer N, return the total sum of its digits in binary.\n/// Example\n/// >>> solve(1000)\n/// String::from(\"1\")\n/// >>> solve(150)\n/// String::from(\"110\")\n/// >>> solve(147)\n/// String::from(\"1100\")\n/// Variables:\n/// @N integer\n/// Constraints: 0 \u2264 N \u2264 10000.\n/// Output:\n/// a string of binary number\nfn solve(N: isize) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = solve;\n    assert_eq!(candidate(1000), String::from(\"1\"));\n    assert_eq!(candidate(150), String::from(\"110\"));\n    assert_eq!(candidate(147), String::from(\"1100\"));\n    assert_eq!(candidate(333), String::from(\"1001\"));\n    assert_eq!(candidate(963), String::from(\"10010\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_84_solve", "test": "}\n\nfn main() {\n    let candidate = solve;\n    assert_eq!(candidate(1000), String::from(\"1\"));\n    assert_eq!(candidate(150), String::from(\"110\"));\n    assert_eq!(candidate(147), String::from(\"1100\"));\n    assert_eq!(candidate(333), String::from(\"1001\"));\n    assert_eq!(candidate(963), String::from(\"10010\"));\n}\n"}
{"name": "HumanEval_129_minPath", "language": "rs", "prompt": "/// Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n/// each cell of the grid contains a value. Every integer in the range [1, N * N]\n/// inclusive appears exactly once on the cells of the grid.\n/// You have to find the minimum path of length k in the grid. You can start\n/// from any cell, and in each step you can move to any of the neighbor cells,\n/// in other words, you can go to cells which share an edge with you current\n/// cell.\n/// Please note that a path of length k means visiting exactly k cells (not\n/// necessarily distinct).\n/// You CANNOT go off the grid.\n/// A path A (of length k) is considered less than a path B (of length k) if\n/// after making the ordered vectors of the values on the cells that A and B go\n/// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n/// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n/// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n/// lst_A[j] = lst_B[j].\n/// It is guaranteed that the answer is unique.\n/// Return an ordered vector of the values on the cells that the minimum path go through.\n/// Examples:    \n/// >>> minPath(vec![vec![1, 2, 3], vec![4, 5, 6], vec![7, 8, 9]], 3)\n/// vec![1, 2, 1]\n/// >>> minPath(vec![vec![5, 9, 3], vec![4, 1, 6], vec![7, 8, 2]], 1)\n/// vec![1]\nfn minPath(grid: Vec<Vec<isize>>, k: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = minPath;\n    assert_eq!(candidate(vec![vec![1, 2, 3], vec![4, 5, 6], vec![7, 8, 9]], 3), vec![1, 2, 1]);\n    assert_eq!(candidate(vec![vec![5, 9, 3], vec![4, 1, 6], vec![7, 8, 2]], 1), vec![1]);\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4], vec![5, 6, 7, 8], vec![9, 10, 11, 12], vec![13, 14, 15, 16]], 4), vec![1, 2, 1, 2]);\n    assert_eq!(candidate(vec![vec![6, 4, 13, 10], vec![5, 7, 12, 1], vec![3, 16, 11, 15], vec![8, 14, 9, 2]], 7), vec![1, 10, 1, 10, 1, 10, 1]);\n    assert_eq!(candidate(vec![vec![8, 14, 9, 2], vec![6, 4, 13, 15], vec![5, 7, 1, 12], vec![3, 10, 11, 16]], 5), vec![1, 7, 1, 7, 1]);\n    assert_eq!(candidate(vec![vec![11, 8, 7, 2], vec![5, 16, 14, 4], vec![9, 3, 15, 6], vec![12, 13, 10, 1]], 9), vec![1, 6, 1, 6, 1, 6, 1, 6, 1]);\n    assert_eq!(candidate(vec![vec![12, 13, 10, 1], vec![9, 3, 15, 6], vec![5, 16, 14, 4], vec![11, 8, 7, 2]], 12), vec![1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6]);\n    assert_eq!(candidate(vec![vec![2, 7, 4], vec![3, 1, 5], vec![6, 8, 9]], 8), vec![1, 3, 1, 3, 1, 3, 1, 3]);\n    assert_eq!(candidate(vec![vec![6, 1, 5], vec![3, 8, 9], vec![2, 7, 4]], 8), vec![1, 5, 1, 5, 1, 5, 1, 5]);\n    assert_eq!(candidate(vec![vec![1, 2], vec![3, 4]], 10), vec![1, 2, 1, 2, 1, 2, 1, 2, 1, 2]);\n    assert_eq!(candidate(vec![vec![1, 3], vec![3, 2]], 10), vec![1, 3, 1, 3, 1, 3, 1, 3, 1, 3]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_129_minPath", "test": "}\n\nfn main() {\n    let candidate = minPath;\n    assert_eq!(candidate(vec![vec![1, 2, 3], vec![4, 5, 6], vec![7, 8, 9]], 3), vec![1, 2, 1]);\n    assert_eq!(candidate(vec![vec![5, 9, 3], vec![4, 1, 6], vec![7, 8, 2]], 1), vec![1]);\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4], vec![5, 6, 7, 8], vec![9, 10, 11, 12], vec![13, 14, 15, 16]], 4), vec![1, 2, 1, 2]);\n    assert_eq!(candidate(vec![vec![6, 4, 13, 10], vec![5, 7, 12, 1], vec![3, 16, 11, 15], vec![8, 14, 9, 2]], 7), vec![1, 10, 1, 10, 1, 10, 1]);\n    assert_eq!(candidate(vec![vec![8, 14, 9, 2], vec![6, 4, 13, 15], vec![5, 7, 1, 12], vec![3, 10, 11, 16]], 5), vec![1, 7, 1, 7, 1]);\n    assert_eq!(candidate(vec![vec![11, 8, 7, 2], vec![5, 16, 14, 4], vec![9, 3, 15, 6], vec![12, 13, 10, 1]], 9), vec![1, 6, 1, 6, 1, 6, 1, 6, 1]);\n    assert_eq!(candidate(vec![vec![12, 13, 10, 1], vec![9, 3, 15, 6], vec![5, 16, 14, 4], vec![11, 8, 7, 2]], 12), vec![1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6]);\n    assert_eq!(candidate(vec![vec![2, 7, 4], vec![3, 1, 5], vec![6, 8, 9]], 8), vec![1, 3, 1, 3, 1, 3, 1, 3]);\n    assert_eq!(candidate(vec![vec![6, 1, 5], vec![3, 8, 9], vec![2, 7, 4]], 8), vec![1, 5, 1, 5, 1, 5, 1, 5]);\n    assert_eq!(candidate(vec![vec![1, 2], vec![3, 4]], 10), vec![1, 2, 1, 2, 1, 2, 1, 2, 1, 2]);\n    assert_eq!(candidate(vec![vec![1, 3], vec![3, 2]], 10), vec![1, 3, 1, 3, 1, 3, 1, 3, 1, 3]);\n}\n"}
{"name": "HumanEval_98_count_upper", "language": "rs", "prompt": "/// Given a string s, count the number of uppercase vowels in even indices.\n/// For example:\n/// >>> count_upper(String::from(\"aBCdEf\"))\n/// 1\n/// >>> count_upper(String::from(\"abcdefg\"))\n/// 0\n/// >>> count_upper(String::from(\"dBBE\"))\n/// 0\nfn count_upper(s: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = count_upper;\n    assert_eq!(candidate(String::from(\"aBCdEf\")), 1);\n    assert_eq!(candidate(String::from(\"abcdefg\")), 0);\n    assert_eq!(candidate(String::from(\"dBBE\")), 0);\n    assert_eq!(candidate(String::from(\"B\")), 0);\n    assert_eq!(candidate(String::from(\"U\")), 1);\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"EEEE\")), 2);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_98_count_upper", "test": "}\n\nfn main() {\n    let candidate = count_upper;\n    assert_eq!(candidate(String::from(\"aBCdEf\")), 1);\n    assert_eq!(candidate(String::from(\"abcdefg\")), 0);\n    assert_eq!(candidate(String::from(\"dBBE\")), 0);\n    assert_eq!(candidate(String::from(\"B\")), 0);\n    assert_eq!(candidate(String::from(\"U\")), 1);\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"EEEE\")), 2);\n}\n"}
{"name": "HumanEval_120_maximum", "language": "rs", "prompt": "/// Given a vector arr of integers and a positive integer k, return a sorted vector \n/// of length k with the maximum k numbers in arr.\n/// Example 1:\n/// >>> maximum(vec![-3, -4, 5], 3)\n/// vec![-4, -3, 5]\n/// Example 2:\n/// >>> maximum(vec![4, -4, 4], 2)\n/// vec![4, 4]\n/// Example 3:\n/// >>> maximum(vec![-3, 2, 1, 2, -1, -2, 1], 1)\n/// vec![2]\n/// Note:\n/// 1. The length of the vector will be in the range of [1, 1000].\n/// 2. The elements in the vector will be in the range of [-1000, 1000].\n/// 3. 0 <= k <= len(arr)\nfn maximum(arr: Vec<isize>, k: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = maximum;\n    assert_eq!(candidate(vec![-3, -4, 5], 3), vec![-4, -3, 5]);\n    assert_eq!(candidate(vec![4, -4, 4], 2), vec![4, 4]);\n    assert_eq!(candidate(vec![-3, 2, 1, 2, -1, -2, 1], 1), vec![2]);\n    assert_eq!(candidate(vec![123, -123, 20, 0, 1, 2, -3], 3), vec![2, 20, 123]);\n    assert_eq!(candidate(vec![-123, 20, 0, 1, 2, -3], 4), vec![0, 1, 2, 20]);\n    assert_eq!(candidate(vec![5, 15, 0, 3, -13, -8, 0], 7), vec![-13, -8, 0, 0, 3, 5, 15]);\n    assert_eq!(candidate(vec![-1, 0, 2, 5, 3, -10], 2), vec![3, 5]);\n    assert_eq!(candidate(vec![1, 0, 5, -7], 1), vec![5]);\n    assert_eq!(candidate(vec![4, -4], 2), vec![-4, 4]);\n    assert_eq!(candidate(vec![-10, 10], 2), vec![-10, 10]);\n    assert_eq!(candidate(vec![1, 2, 3, -23, 243, -400, 0], 0), Vec::<isize>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_120_maximum", "test": "}\n\nfn main() {\n    let candidate = maximum;\n    assert_eq!(candidate(vec![-3, -4, 5], 3), vec![-4, -3, 5]);\n    assert_eq!(candidate(vec![4, -4, 4], 2), vec![4, 4]);\n    assert_eq!(candidate(vec![-3, 2, 1, 2, -1, -2, 1], 1), vec![2]);\n    assert_eq!(candidate(vec![123, -123, 20, 0, 1, 2, -3], 3), vec![2, 20, 123]);\n    assert_eq!(candidate(vec![-123, 20, 0, 1, 2, -3], 4), vec![0, 1, 2, 20]);\n    assert_eq!(candidate(vec![5, 15, 0, 3, -13, -8, 0], 7), vec![-13, -8, 0, 0, 3, 5, 15]);\n    assert_eq!(candidate(vec![-1, 0, 2, 5, 3, -10], 2), vec![3, 5]);\n    assert_eq!(candidate(vec![1, 0, 5, -7], 1), vec![5]);\n    assert_eq!(candidate(vec![4, -4], 2), vec![-4, 4]);\n    assert_eq!(candidate(vec![-10, 10], 2), vec![-10, 10]);\n    assert_eq!(candidate(vec![1, 2, 3, -23, 243, -400, 0], 0), Vec::<isize>::new());\n}\n"}
{"name": "HumanEval_24_largest_divisor", "language": "rs", "prompt": "/// For a given number n, find the largest number that divides n evenly, smaller than n\n/// >>> largest_divisor(15)\n/// 5\nfn largest_divisor(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = largest_divisor;\n    assert_eq!(candidate(3), 1);\n    assert_eq!(candidate(7), 1);\n    assert_eq!(candidate(10), 5);\n    assert_eq!(candidate(100), 50);\n    assert_eq!(candidate(49), 7);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_24_largest_divisor", "test": "}\n\nfn main() {\n    let candidate = largest_divisor;\n    assert_eq!(candidate(3), 1);\n    assert_eq!(candidate(7), 1);\n    assert_eq!(candidate(10), 5);\n    assert_eq!(candidate(100), 50);\n    assert_eq!(candidate(49), 7);\n}\n"}
{"name": "HumanEval_88_sort_array", "language": "rs", "prompt": "/// Given a vector of non-negative integers, return a cors of the given vector after sorting,\n/// you will sort the given vector in ascending order if the sum( first index value, last index value) is odd,\n/// or sort it in descending order if the sum( first index value, last index value) is even.\n/// Note:\n/// * don't change the given vector.\n/// Examples:\n/// >>> sort_array(vec![])\n/// Vec::<isize>::new()\n/// >>> sort_array(vec![5])\n/// vec![5]\n/// >>> sort_array(vec![2, 4, 3, 0, 1, 5])\n/// vec![0, 1, 2, 3, 4, 5]\n/// >>> sort_array(vec![2, 4, 3, 0, 1, 5, 6])\n/// vec![6, 5, 4, 3, 2, 1, 0]\nfn sort_array(array: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sort_array;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![5]), vec![5]);\n    assert_eq!(candidate(vec![2, 4, 3, 0, 1, 5]), vec![0, 1, 2, 3, 4, 5]);\n    assert_eq!(candidate(vec![2, 4, 3, 0, 1, 5, 6]), vec![6, 5, 4, 3, 2, 1, 0]);\n    assert_eq!(candidate(vec![2, 1]), vec![1, 2]);\n    assert_eq!(candidate(vec![15, 42, 87, 32, 11, 0]), vec![0, 11, 15, 32, 42, 87]);\n    assert_eq!(candidate(vec![21, 14, 23, 11]), vec![23, 21, 14, 11]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_88_sort_array", "test": "}\n\nfn main() {\n    let candidate = sort_array;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![5]), vec![5]);\n    assert_eq!(candidate(vec![2, 4, 3, 0, 1, 5]), vec![0, 1, 2, 3, 4, 5]);\n    assert_eq!(candidate(vec![2, 4, 3, 0, 1, 5, 6]), vec![6, 5, 4, 3, 2, 1, 0]);\n    assert_eq!(candidate(vec![2, 1]), vec![1, 2]);\n    assert_eq!(candidate(vec![15, 42, 87, 32, 11, 0]), vec![0, 11, 15, 32, 42, 87]);\n    assert_eq!(candidate(vec![21, 14, 23, 11]), vec![23, 21, 14, 11]);\n}\n"}
{"name": "HumanEval_106_f", "language": "rs", "prompt": "/// Implement the function f that takes n as a parameter,\n/// and returns a vector of size n, such that the value of the element at index i is the factorial of i if i is even\n/// or the sum of numbers from 1 to i otherwise.\n/// i starts from 1.\n/// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n/// Example:\n/// >>> f(5)\n/// vec![1, 2, 6, 24, 15]\nfn f(n: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = f;\n    assert_eq!(candidate(5), vec![1, 2, 6, 24, 15]);\n    assert_eq!(candidate(7), vec![1, 2, 6, 24, 15, 720, 28]);\n    assert_eq!(candidate(1), vec![1]);\n    assert_eq!(candidate(3), vec![1, 2, 6]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_106_f", "test": "}\n\nfn main() {\n    let candidate = f;\n    assert_eq!(candidate(5), vec![1, 2, 6, 24, 15]);\n    assert_eq!(candidate(7), vec![1, 2, 6, 24, 15, 720, 28]);\n    assert_eq!(candidate(1), vec![1]);\n    assert_eq!(candidate(3), vec![1, 2, 6]);\n}\n"}
{"name": "HumanEval_77_iscube", "language": "rs", "prompt": "/// Write a function that takes an integer a and returns true \n/// if this ingeger is a cube of some integer number.\n/// Note: you may assume the input is always valid.\n/// Examples:\n/// >>> iscube(1)\n/// true\n/// >>> iscube(2)\n/// false\n/// >>> iscube(-1)\n/// true\n/// >>> iscube(64)\n/// true\n/// >>> iscube(0)\n/// true\n/// >>> iscube(180)\n/// false\nfn iscube(a: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = iscube;\n    assert_eq!(candidate(1), true);\n    assert_eq!(candidate(2), false);\n    assert_eq!(candidate(-1), true);\n    assert_eq!(candidate(64), true);\n    assert_eq!(candidate(180), false);\n    assert_eq!(candidate(1000), true);\n    assert_eq!(candidate(0), true);\n    assert_eq!(candidate(1729), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_77_iscube", "test": "}\n\nfn main() {\n    let candidate = iscube;\n    assert_eq!(candidate(1), true);\n    assert_eq!(candidate(2), false);\n    assert_eq!(candidate(-1), true);\n    assert_eq!(candidate(64), true);\n    assert_eq!(candidate(180), false);\n    assert_eq!(candidate(1000), true);\n    assert_eq!(candidate(0), true);\n    assert_eq!(candidate(1729), false);\n}\n"}
{"name": "HumanEval_93_encode", "language": "rs", "prompt": "/// Write a function that takes a message, and encodes in such a \n/// way that it swaps case of all letters, replaces all vowels in \n/// the message with the letter that appears 2 places ahead of that \n/// vowel in the english alphabet. \n/// Assume only letters. \n/// Examples:\n/// >>> encode(String::from(\"test\"))\n/// String::from(\"TGST\")\n/// >>> encode(String::from(\"This is a message\"))\n/// String::from(\"tHKS KS C MGSSCGG\")\nfn encode(message: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = encode;\n    assert_eq!(candidate(String::from(\"TEST\")), String::from(\"tgst\"));\n    assert_eq!(candidate(String::from(\"Mudasir\")), String::from(\"mWDCSKR\"));\n    assert_eq!(candidate(String::from(\"YES\")), String::from(\"ygs\"));\n    assert_eq!(candidate(String::from(\"This is a message\")), String::from(\"tHKS KS C MGSSCGG\"));\n    assert_eq!(candidate(String::from(\"I DoNt KnOw WhAt tO WrItE\")), String::from(\"k dQnT kNqW wHcT Tq wRkTg\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_93_encode", "test": "}\n\nfn main() {\n    let candidate = encode;\n    assert_eq!(candidate(String::from(\"TEST\")), String::from(\"tgst\"));\n    assert_eq!(candidate(String::from(\"Mudasir\")), String::from(\"mWDCSKR\"));\n    assert_eq!(candidate(String::from(\"YES\")), String::from(\"ygs\"));\n    assert_eq!(candidate(String::from(\"This is a message\")), String::from(\"tHKS KS C MGSSCGG\"));\n    assert_eq!(candidate(String::from(\"I DoNt KnOw WhAt tO WrItE\")), String::from(\"k dQnT kNqW wHcT Tq wRkTg\"));\n}\n"}
{"name": "HumanEval_91_is_bored", "language": "rs", "prompt": "/// You'll be given a string of words, and your task is to count the number\n/// of boredoms. A boredom is a sentence that starts with the word \"I\".\n/// Sentences are delimited by '.', '?' or '!'.\n/// For example:\n/// >>> is_bored(String::from(\"Hello world\"))\n/// 0\n/// >>> is_bored(String::from(\"The sky is blue. The sun is shining. I love this weather\"))\n/// 1\nfn is_bored(S: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_bored;\n    assert_eq!(candidate(String::from(\"Hello world\")), 0);\n    assert_eq!(candidate(String::from(\"Is the sky blue?\")), 0);\n    assert_eq!(candidate(String::from(\"I love It !\")), 1);\n    assert_eq!(candidate(String::from(\"bIt\")), 0);\n    assert_eq!(candidate(String::from(\"I feel good today. I will be productive. will kill It\")), 2);\n    assert_eq!(candidate(String::from(\"You and I are going for a walk\")), 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_91_is_bored", "test": "}\n\nfn main() {\n    let candidate = is_bored;\n    assert_eq!(candidate(String::from(\"Hello world\")), 0);\n    assert_eq!(candidate(String::from(\"Is the sky blue?\")), 0);\n    assert_eq!(candidate(String::from(\"I love It !\")), 1);\n    assert_eq!(candidate(String::from(\"bIt\")), 0);\n    assert_eq!(candidate(String::from(\"I feel good today. I will be productive. will kill It\")), 2);\n    assert_eq!(candidate(String::from(\"You and I are going for a walk\")), 0);\n}\n"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "rs", "prompt": "/// pairs_sum_to_zero takes a vector of integers as an input.\n/// it returns true if there are two distinct elements in the vector that\n/// sum to zero, and false otherwise.\n/// >>> pairs_sum_to_zero(vec![1, 3, 5, 0])\n/// false\n/// >>> pairs_sum_to_zero(vec![1, 3, -2, 1])\n/// false\n/// >>> pairs_sum_to_zero(vec![1, 2, 3, 7])\n/// false\n/// >>> pairs_sum_to_zero(vec![2, 4, -5, 3, 5, 7])\n/// true\n/// >>> pairs_sum_to_zero(vec![1])\n/// false\nfn pairs_sum_to_zero(l: Vec<isize>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = pairs_sum_to_zero;\n    assert_eq!(candidate(vec![1, 3, 5, 0]), false);\n    assert_eq!(candidate(vec![1, 3, -2, 1]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 7]), false);\n    assert_eq!(candidate(vec![2, 4, -5, 3, 5, 7]), true);\n    assert_eq!(candidate(vec![1]), false);\n    assert_eq!(candidate(vec![-3, 9, -1, 3, 2, 30]), true);\n    assert_eq!(candidate(vec![-3, 9, -1, 3, 2, 31]), true);\n    assert_eq!(candidate(vec![-3, 9, -1, 4, 2, 30]), false);\n    assert_eq!(candidate(vec![-3, 9, -1, 4, 2, 31]), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "}\n\nfn main() {\n    let candidate = pairs_sum_to_zero;\n    assert_eq!(candidate(vec![1, 3, 5, 0]), false);\n    assert_eq!(candidate(vec![1, 3, -2, 1]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 7]), false);\n    assert_eq!(candidate(vec![2, 4, -5, 3, 5, 7]), true);\n    assert_eq!(candidate(vec![1]), false);\n    assert_eq!(candidate(vec![-3, 9, -1, 3, 2, 30]), true);\n    assert_eq!(candidate(vec![-3, 9, -1, 3, 2, 31]), true);\n    assert_eq!(candidate(vec![-3, 9, -1, 4, 2, 30]), false);\n    assert_eq!(candidate(vec![-3, 9, -1, 4, 2, 31]), false);\n}\n"}
{"name": "HumanEval_71_triangle_area", "language": "rs", "prompt": "/// Given the lengths of the three sides of a triangle. Return the area of\n/// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n/// Otherwise return -1\n/// Three sides make a valid triangle when the sum of any two sides is greater \n/// than the third side.\n/// Example:\n/// >>> triangle_area(3, 4, 5)\n/// 6.0\n/// >>> triangle_area(1, 2, 10)\n/// -1.0\nfn triangle_area(a: isize, b: isize, c: isize) -> f64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = triangle_area;\n    assert_eq!(candidate(3, 4, 5), 6.0);\n    assert_eq!(candidate(1, 2, 10), -1.0);\n    assert_eq!(candidate(4, 8, 5), 8.18);\n    assert_eq!(candidate(2, 2, 2), 1.73);\n    assert_eq!(candidate(1, 2, 3), -1.0);\n    assert_eq!(candidate(10, 5, 7), 16.25);\n    assert_eq!(candidate(2, 6, 3), -1.0);\n    assert_eq!(candidate(1, 1, 1), 0.43);\n    assert_eq!(candidate(2, 2, 10), -1.0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_71_triangle_area", "test": "}\n\nfn main() {\n    let candidate = triangle_area;\n    assert_eq!(candidate(3, 4, 5), 6.0);\n    assert_eq!(candidate(1, 2, 10), -1.0);\n    assert_eq!(candidate(4, 8, 5), 8.18);\n    assert_eq!(candidate(2, 2, 2), 1.73);\n    assert_eq!(candidate(1, 2, 3), -1.0);\n    assert_eq!(candidate(10, 5, 7), 16.25);\n    assert_eq!(candidate(2, 6, 3), -1.0);\n    assert_eq!(candidate(1, 1, 1), 0.43);\n    assert_eq!(candidate(2, 2, 10), -1.0);\n}\n"}
{"name": "HumanEval_131_digits", "language": "rs", "prompt": "/// Given a positive integer n, return the product of the odd digits.\n/// Return 0 if all digits are even.\n/// For example:\n/// >>> digits(1)\n/// 1\n/// >>> digits(4)\n/// 0\n/// >>> digits(235)\n/// 15\nfn digits(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = digits;\n    assert_eq!(candidate(5), 5);\n    assert_eq!(candidate(54), 5);\n    assert_eq!(candidate(120), 1);\n    assert_eq!(candidate(5014), 5);\n    assert_eq!(candidate(98765), 315);\n    assert_eq!(candidate(5576543), 2625);\n    assert_eq!(candidate(2468), 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_131_digits", "test": "}\n\nfn main() {\n    let candidate = digits;\n    assert_eq!(candidate(5), 5);\n    assert_eq!(candidate(54), 5);\n    assert_eq!(candidate(120), 1);\n    assert_eq!(candidate(5014), 5);\n    assert_eq!(candidate(98765), 315);\n    assert_eq!(candidate(5576543), 2625);\n    assert_eq!(candidate(2468), 0);\n}\n"}
{"name": "HumanEval_101_words_string", "language": "rs", "prompt": "/// You will be given a string of words separated by commas or spaces. Your task is\n/// to split the string into words and return a vector of the words.\n/// For example:\n/// >>> words_string(String::from(\"Hi, my name is John\"))\n/// vec![String::from(\"Hi\"), String::from(\"my\"), String::from(\"name\"), String::from(\"is\"), String::from(\"John\")]\n/// >>> words_string(String::from(\"One, two, three, four, five, six\"))\n/// vec![String::from(\"One\"), String::from(\"two\"), String::from(\"three\"), String::from(\"four\"), String::from(\"five\"), String::from(\"six\")]\nfn words_string(s: String) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = words_string;\n    assert_eq!(candidate(String::from(\"Hi, my name is John\")), vec![String::from(\"Hi\"), String::from(\"my\"), String::from(\"name\"), String::from(\"is\"), String::from(\"John\")]);\n    assert_eq!(candidate(String::from(\"One, two, three, four, five, six\")), vec![String::from(\"One\"), String::from(\"two\"), String::from(\"three\"), String::from(\"four\"), String::from(\"five\"), String::from(\"six\")]);\n    assert_eq!(candidate(String::from(\"Hi, my name\")), vec![String::from(\"Hi\"), String::from(\"my\"), String::from(\"name\")]);\n    assert_eq!(candidate(String::from(\"One,, two, three, four, five, six,\")), vec![String::from(\"One\"), String::from(\"two\"), String::from(\"three\"), String::from(\"four\"), String::from(\"five\"), String::from(\"six\")]);\n    assert_eq!(candidate(String::from(\"\")), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"ahmed     , gamal\")), vec![String::from(\"ahmed\"), String::from(\"gamal\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_101_words_string", "test": "}\n\nfn main() {\n    let candidate = words_string;\n    assert_eq!(candidate(String::from(\"Hi, my name is John\")), vec![String::from(\"Hi\"), String::from(\"my\"), String::from(\"name\"), String::from(\"is\"), String::from(\"John\")]);\n    assert_eq!(candidate(String::from(\"One, two, three, four, five, six\")), vec![String::from(\"One\"), String::from(\"two\"), String::from(\"three\"), String::from(\"four\"), String::from(\"five\"), String::from(\"six\")]);\n    assert_eq!(candidate(String::from(\"Hi, my name\")), vec![String::from(\"Hi\"), String::from(\"my\"), String::from(\"name\")]);\n    assert_eq!(candidate(String::from(\"One,, two, three, four, five, six,\")), vec![String::from(\"One\"), String::from(\"two\"), String::from(\"three\"), String::from(\"four\"), String::from(\"five\"), String::from(\"six\")]);\n    assert_eq!(candidate(String::from(\"\")), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"ahmed     , gamal\")), vec![String::from(\"ahmed\"), String::from(\"gamal\")]);\n}\n"}
{"name": "HumanEval_18_how_many_times", "language": "rs", "prompt": "/// Find how many times a given substring can be found in the original string. Count overlaping cases.\n/// >>> how_many_times(String::from(\"\"), String::from(\"a\"))\n/// 0\n/// >>> how_many_times(String::from(\"aaa\"), String::from(\"a\"))\n/// 3\n/// >>> how_many_times(String::from(\"aaaa\"), String::from(\"aa\"))\n/// 3\nfn how_many_times(string: String, substring: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = how_many_times;\n    assert_eq!(candidate(String::from(\"\"), String::from(\"x\")), 0);\n    assert_eq!(candidate(String::from(\"xyxyxyx\"), String::from(\"x\")), 4);\n    assert_eq!(candidate(String::from(\"cacacacac\"), String::from(\"cac\")), 4);\n    assert_eq!(candidate(String::from(\"john doe\"), String::from(\"john\")), 1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_18_how_many_times", "test": "}\n\nfn main() {\n    let candidate = how_many_times;\n    assert_eq!(candidate(String::from(\"\"), String::from(\"x\")), 0);\n    assert_eq!(candidate(String::from(\"xyxyxyx\"), String::from(\"x\")), 4);\n    assert_eq!(candidate(String::from(\"cacacacac\"), String::from(\"cac\")), 4);\n    assert_eq!(candidate(String::from(\"john doe\"), String::from(\"john\")), 1);\n}\n"}
{"name": "HumanEval_51_remove_vowels", "language": "rs", "prompt": "/// remove_vowels is a function that takes string and returns string without vowels.\n/// >>> remove_vowels(String::from(\"\"))\n/// String::from(\"\")\n/// >>> remove_vowels(String::from(\"abcdef\"))\n/// String::from(\"bcdf\")\n/// >>> remove_vowels(String::from(\"aaaaa\"))\n/// String::from(\"\")\n/// >>> remove_vowels(String::from(\"aaBAA\"))\n/// String::from(\"B\")\n/// >>> remove_vowels(String::from(\"zbcd\"))\n/// String::from(\"zbcd\")\nfn remove_vowels(text: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = remove_vowels;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"abcdef\nghijklm\")), String::from(\"bcdf\nghjklm\"));\n    assert_eq!(candidate(String::from(\"fedcba\")), String::from(\"fdcb\"));\n    assert_eq!(candidate(String::from(\"eeeee\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"acBAA\")), String::from(\"cB\"));\n    assert_eq!(candidate(String::from(\"EcBOO\")), String::from(\"cB\"));\n    assert_eq!(candidate(String::from(\"ybcd\")), String::from(\"ybcd\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_51_remove_vowels", "test": "}\n\nfn main() {\n    let candidate = remove_vowels;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"abcdef\nghijklm\")), String::from(\"bcdf\nghjklm\"));\n    assert_eq!(candidate(String::from(\"fedcba\")), String::from(\"fdcb\"));\n    assert_eq!(candidate(String::from(\"eeeee\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"acBAA\")), String::from(\"cB\"));\n    assert_eq!(candidate(String::from(\"EcBOO\")), String::from(\"cB\"));\n    assert_eq!(candidate(String::from(\"ybcd\")), String::from(\"ybcd\"));\n}\n"}
{"name": "HumanEval_70_strange_sort_list", "language": "rs", "prompt": "/// Given vector of integers, return vector in strange order.\n/// Strange sorting, is when you start with the minimum value,\n/// then maximum of the remaining integers, then minimum and so on.\n/// Examples:\n/// >>> strange_sort_list(vec![1, 2, 3, 4])\n/// vec![1, 4, 2, 3]\n/// >>> strange_sort_list(vec![5, 5, 5, 5])\n/// vec![5, 5, 5, 5]\n/// >>> strange_sort_list(vec![])\n/// Vec::<isize>::new()\nfn strange_sort_list(lst: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = strange_sort_list;\n    assert_eq!(candidate(vec![1, 2, 3, 4]), vec![1, 4, 2, 3]);\n    assert_eq!(candidate(vec![5, 6, 7, 8, 9]), vec![5, 9, 6, 8, 7]);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5]), vec![1, 5, 2, 4, 3]);\n    assert_eq!(candidate(vec![5, 6, 7, 8, 9, 1]), vec![1, 9, 5, 8, 6, 7]);\n    assert_eq!(candidate(vec![5, 5, 5, 5]), vec![5, 5, 5, 5]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6, 7, 8]), vec![1, 8, 2, 7, 3, 6, 4, 5]);\n    assert_eq!(candidate(vec![0, 2, 2, 2, 5, 5, -5, -5]), vec![-5, 5, -5, 5, 0, 2, 2, 2]);\n    assert_eq!(candidate(vec![111111]), vec![111111]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_70_strange_sort_list", "test": "}\n\nfn main() {\n    let candidate = strange_sort_list;\n    assert_eq!(candidate(vec![1, 2, 3, 4]), vec![1, 4, 2, 3]);\n    assert_eq!(candidate(vec![5, 6, 7, 8, 9]), vec![5, 9, 6, 8, 7]);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5]), vec![1, 5, 2, 4, 3]);\n    assert_eq!(candidate(vec![5, 6, 7, 8, 9, 1]), vec![1, 9, 5, 8, 6, 7]);\n    assert_eq!(candidate(vec![5, 5, 5, 5]), vec![5, 5, 5, 5]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6, 7, 8]), vec![1, 8, 2, 7, 3, 6, 4, 5]);\n    assert_eq!(candidate(vec![0, 2, 2, 2, 5, 5, -5, -5]), vec![-5, 5, -5, 5, 0, 2, 2, 2]);\n    assert_eq!(candidate(vec![111111]), vec![111111]);\n}\n"}
{"name": "HumanEval_20_find_closest_elements", "language": "rs", "prompt": "/// From a supplied vector of numbers (of length at least two) select and return two that are the closest to each\n/// other and return them in order (smaller number, larger number).\n/// >>> find_closest_elements(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n/// (2.0, 2.2)\n/// >>> find_closest_elements(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n/// (2.0, 2.0)\nfn find_closest_elements(numbers: Vec<f64>) -> (f64, f64) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = find_closest_elements;\n    assert_eq!(candidate(vec![1.0, 2.0, 3.9, 4.0, 5.0, 2.2]), (3.9, 4.0));\n    assert_eq!(candidate(vec![1.0, 2.0, 5.9, 4.0, 5.0]), (5.0, 5.9));\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.2]), (2.0, 2.2));\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.0]), (2.0, 2.0));\n    assert_eq!(candidate(vec![1.1, 2.2, 3.1, 4.1, 5.1]), (2.2, 3.1));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_20_find_closest_elements", "test": "}\n\nfn main() {\n    let candidate = find_closest_elements;\n    assert_eq!(candidate(vec![1.0, 2.0, 3.9, 4.0, 5.0, 2.2]), (3.9, 4.0));\n    assert_eq!(candidate(vec![1.0, 2.0, 5.9, 4.0, 5.0]), (5.0, 5.9));\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.2]), (2.0, 2.2));\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.0]), (2.0, 2.0));\n    assert_eq!(candidate(vec![1.1, 2.2, 3.1, 4.1, 5.1]), (2.2, 3.1));\n}\n"}
{"name": "HumanEval_76_is_simple_power", "language": "rs", "prompt": "/// Your task is to write a function that returns true if a number x is a simple\n/// power of n and false in other cases.\n/// x is a simple power of n if n**int=x\n/// For example:\n/// >>> is_simple_power(1, 4)\n/// true\n/// >>> is_simple_power(2, 2)\n/// true\n/// >>> is_simple_power(8, 2)\n/// true\n/// >>> is_simple_power(3, 2)\n/// false\n/// >>> is_simple_power(3, 1)\n/// false\n/// >>> is_simple_power(5, 3)\n/// false\nfn is_simple_power(x: isize, n: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_simple_power;\n    assert_eq!(candidate(16, 2), true);\n    assert_eq!(candidate(143214, 16), false);\n    assert_eq!(candidate(4, 2), true);\n    assert_eq!(candidate(9, 3), true);\n    assert_eq!(candidate(16, 4), true);\n    assert_eq!(candidate(24, 2), false);\n    assert_eq!(candidate(128, 4), false);\n    assert_eq!(candidate(12, 6), false);\n    assert_eq!(candidate(1, 1), true);\n    assert_eq!(candidate(1, 12), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_76_is_simple_power", "test": "}\n\nfn main() {\n    let candidate = is_simple_power;\n    assert_eq!(candidate(16, 2), true);\n    assert_eq!(candidate(143214, 16), false);\n    assert_eq!(candidate(4, 2), true);\n    assert_eq!(candidate(9, 3), true);\n    assert_eq!(candidate(16, 4), true);\n    assert_eq!(candidate(24, 2), false);\n    assert_eq!(candidate(128, 4), false);\n    assert_eq!(candidate(12, 6), false);\n    assert_eq!(candidate(1, 1), true);\n    assert_eq!(candidate(1, 12), true);\n}\n"}
{"name": "HumanEval_39_prime_fib", "language": "rs", "prompt": "/// prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n/// >>> prime_fib(1)\n/// 2\n/// >>> prime_fib(2)\n/// 3\n/// >>> prime_fib(3)\n/// 5\n/// >>> prime_fib(4)\n/// 13\n/// >>> prime_fib(5)\n/// 89\nfn prime_fib(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = prime_fib;\n    assert_eq!(candidate(1), 2);\n    assert_eq!(candidate(2), 3);\n    assert_eq!(candidate(3), 5);\n    assert_eq!(candidate(4), 13);\n    assert_eq!(candidate(5), 89);\n    assert_eq!(candidate(6), 233);\n    assert_eq!(candidate(7), 1597);\n    assert_eq!(candidate(8), 28657);\n    assert_eq!(candidate(9), 514229);\n    assert_eq!(candidate(10), 433494437);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_39_prime_fib", "test": "}\n\nfn main() {\n    let candidate = prime_fib;\n    assert_eq!(candidate(1), 2);\n    assert_eq!(candidate(2), 3);\n    assert_eq!(candidate(3), 5);\n    assert_eq!(candidate(4), 13);\n    assert_eq!(candidate(5), 89);\n    assert_eq!(candidate(6), 233);\n    assert_eq!(candidate(7), 1597);\n    assert_eq!(candidate(8), 28657);\n    assert_eq!(candidate(9), 514229);\n    assert_eq!(candidate(10), 433494437);\n}\n"}
{"name": "HumanEval_145_order_by_points", "language": "rs", "prompt": "/// Write a function which sorts the given vector of integers\n/// in ascending order according to the sum of their digits.\n/// Note: if there are several items with similar sum of their digits,\n/// order them based on their index in original vector.\n/// For example:\n/// >>> order_by_points(vec![1, 11, -1, -11, -12])\n/// vec![-1, -11, 1, -12, 11]\n/// >>> order_by_points(vec![])\n/// Vec::<isize>::new()\nfn order_by_points(nums: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = order_by_points;\n    assert_eq!(candidate(vec![1, 11, -1, -11, -12]), vec![-1, -11, 1, -12, 11]);\n    assert_eq!(candidate(vec![1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46]), vec![0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, -11, -32, 43, 54, -98, 2, -3]), vec![-3, -32, -98, -11, 1, 2, 43, 54]);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]), vec![1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9]);\n    assert_eq!(candidate(vec![0, 6, 6, -76, -21, 23, 4]), vec![-76, -21, 0, 4, 23, 6, 6]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_145_order_by_points", "test": "}\n\nfn main() {\n    let candidate = order_by_points;\n    assert_eq!(candidate(vec![1, 11, -1, -11, -12]), vec![-1, -11, 1, -12, 11]);\n    assert_eq!(candidate(vec![1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46]), vec![0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, -11, -32, 43, 54, -98, 2, -3]), vec![-3, -32, -98, -11, 1, 2, 43, 54]);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]), vec![1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9]);\n    assert_eq!(candidate(vec![0, 6, 6, -76, -21, 23, 4]), vec![-76, -21, 0, 4, 23, 6, 6]);\n}\n"}
{"name": "HumanEval_0_has_close_elements", "language": "rs", "prompt": "/// Check if in given vector of numbers, are any two numbers closer to each other than\n/// given threshold.\n/// >>> has_close_elements(vec![1.0, 2.0, 3.0], 0.5)\n/// false\n/// >>> has_close_elements(vec![1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n/// true\nfn has_close_elements(numbers: Vec<f64>, threshold: f64) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = has_close_elements;\n    assert_eq!(candidate(vec![1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3), true);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05), false);\n    assert_eq!(candidate(vec![1.0, 2.0, 5.9, 4.0, 5.0], 0.95), true);\n    assert_eq!(candidate(vec![1.0, 2.0, 5.9, 4.0, 5.0], 0.8), false);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1), true);\n    assert_eq!(candidate(vec![1.1, 2.2, 3.1, 4.1, 5.1], 1.0), true);\n    assert_eq!(candidate(vec![1.1, 2.2, 3.1, 4.1, 5.1], 0.5), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_0_has_close_elements", "test": "}\n\nfn main() {\n    let candidate = has_close_elements;\n    assert_eq!(candidate(vec![1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3), true);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05), false);\n    assert_eq!(candidate(vec![1.0, 2.0, 5.9, 4.0, 5.0], 0.95), true);\n    assert_eq!(candidate(vec![1.0, 2.0, 5.9, 4.0, 5.0], 0.8), false);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1), true);\n    assert_eq!(candidate(vec![1.1, 2.2, 3.1, 4.1, 5.1], 1.0), true);\n    assert_eq!(candidate(vec![1.1, 2.2, 3.1, 4.1, 5.1], 0.5), false);\n}\n"}
{"name": "HumanEval_10_make_palindrome", "language": "rs", "prompt": "/// Find the shortest palindrome that begins with a supplied string.\n/// Algorithm idea is simple:\n/// - Find the longest postfix of supplied string that is a palindrome.\n/// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n/// >>> make_palindrome(String::from(\"\"))\n/// String::from(\"\")\n/// >>> make_palindrome(String::from(\"cat\"))\n/// String::from(\"catac\")\n/// >>> make_palindrome(String::from(\"cata\"))\n/// String::from(\"catac\")\nfn make_palindrome(string: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = make_palindrome;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"x\")), String::from(\"x\"));\n    assert_eq!(candidate(String::from(\"xyz\")), String::from(\"xyzyx\"));\n    assert_eq!(candidate(String::from(\"xyx\")), String::from(\"xyx\"));\n    assert_eq!(candidate(String::from(\"jerry\")), String::from(\"jerryrrej\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_10_make_palindrome", "test": "}\n\nfn main() {\n    let candidate = make_palindrome;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"x\")), String::from(\"x\"));\n    assert_eq!(candidate(String::from(\"xyz\")), String::from(\"xyzyx\"));\n    assert_eq!(candidate(String::from(\"xyx\")), String::from(\"xyx\"));\n    assert_eq!(candidate(String::from(\"jerry\")), String::from(\"jerryrrej\"));\n}\n"}
{"name": "HumanEval_11_string_xor", "language": "rs", "prompt": "/// Input are two strings a and b consisting only of 1s and 0s.\n/// Perform binary XOR on these inputs and return result also as a string.\n/// >>> string_xor(String::from(\"010\"), String::from(\"110\"))\n/// String::from(\"100\")\nfn string_xor(a: String, b: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = string_xor;\n    assert_eq!(candidate(String::from(\"111000\"), String::from(\"101010\")), String::from(\"010010\"));\n    assert_eq!(candidate(String::from(\"1\"), String::from(\"1\")), String::from(\"0\"));\n    assert_eq!(candidate(String::from(\"0101\"), String::from(\"0000\")), String::from(\"0101\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_11_string_xor", "test": "}\n\nfn main() {\n    let candidate = string_xor;\n    assert_eq!(candidate(String::from(\"111000\"), String::from(\"101010\")), String::from(\"010010\"));\n    assert_eq!(candidate(String::from(\"1\"), String::from(\"1\")), String::from(\"0\"));\n    assert_eq!(candidate(String::from(\"0101\"), String::from(\"0000\")), String::from(\"0101\"));\n}\n"}
{"name": "HumanEval_139_special_factorial", "language": "rs", "prompt": "/// The Brazilian factorial is defined as:\n/// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n/// where n > 0\n/// For example:\n/// >>> special_factorial(4)\n/// 288\n/// The function will receive an integer as input and should return the special\n/// factorial of this integer.\nfn special_factorial(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = special_factorial;\n    assert_eq!(candidate(4), 288);\n    assert_eq!(candidate(5), 34560);\n    assert_eq!(candidate(7), 125411328000);\n    assert_eq!(candidate(1), 1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_139_special_factorial", "test": "}\n\nfn main() {\n    let candidate = special_factorial;\n    assert_eq!(candidate(4), 288);\n    assert_eq!(candidate(5), 34560);\n    assert_eq!(candidate(7), 125411328000);\n    assert_eq!(candidate(1), 1);\n}\n"}
{"name": "HumanEval_122_add_elements", "language": "rs", "prompt": "/// Given a non-empty vector of integers arr and an integer k, return\n/// the sum of the elements with at most two digits from the first k elements of arr.\n/// Example:\n/// >>> add_elements(vec![111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n/// 24\n/// Constraints:\n/// 1. 1 <= len(arr) <= 100\n/// 2. 1 <= k <= len(arr)\nfn add_elements(arr: Vec<isize>, k: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = add_elements;\n    assert_eq!(candidate(vec![1, -2, -3, 41, 57, 76, 87, 88, 99], 3), -4);\n    assert_eq!(candidate(vec![111, 121, 3, 4000, 5, 6], 2), 0);\n    assert_eq!(candidate(vec![11, 21, 3, 90, 5, 6, 7, 8, 9], 4), 125);\n    assert_eq!(candidate(vec![111, 21, 3, 4000, 5, 6, 7, 8, 9], 4), 24);\n    assert_eq!(candidate(vec![1], 1), 1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_122_add_elements", "test": "}\n\nfn main() {\n    let candidate = add_elements;\n    assert_eq!(candidate(vec![1, -2, -3, 41, 57, 76, 87, 88, 99], 3), -4);\n    assert_eq!(candidate(vec![111, 121, 3, 4000, 5, 6], 2), 0);\n    assert_eq!(candidate(vec![11, 21, 3, 90, 5, 6, 7, 8, 9], 4), 125);\n    assert_eq!(candidate(vec![111, 21, 3, 4000, 5, 6, 7, 8, 9], 4), 24);\n    assert_eq!(candidate(vec![1], 1), 1);\n}\n"}
{"name": "HumanEval_46_fib4", "language": "rs", "prompt": "/// The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n/// fib4(0) -> 0\n/// fib4(1) -> 0\n/// fib4(2) -> 2\n/// fib4(3) -> 0\n/// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n/// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n/// >>> fib4(5)\n/// 4\n/// >>> fib4(6)\n/// 8\n/// >>> fib4(7)\n/// 14\nfn fib4(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = fib4;\n    assert_eq!(candidate(5), 4);\n    assert_eq!(candidate(8), 28);\n    assert_eq!(candidate(10), 104);\n    assert_eq!(candidate(12), 386);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_46_fib4", "test": "}\n\nfn main() {\n    let candidate = fib4;\n    assert_eq!(candidate(5), 4);\n    assert_eq!(candidate(8), 28);\n    assert_eq!(candidate(10), 104);\n    assert_eq!(candidate(12), 386);\n}\n"}
{"name": "HumanEval_104_unique_digits", "language": "rs", "prompt": "/// Given a vector of positive integers x. return a sorted vector of all \n/// elements that hasn't any even digit.\n/// Note: Returned vector should be sorted in increasing order.\n/// For example:\n/// >>> unique_digits(vec![15, 33, 1422, 1])\n/// vec![1, 15, 33]\n/// >>> unique_digits(vec![152, 323, 1422, 10])\n/// Vec::<isize>::new()\nfn unique_digits(x: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = unique_digits;\n    assert_eq!(candidate(vec![15, 33, 1422, 1]), vec![1, 15, 33]);\n    assert_eq!(candidate(vec![152, 323, 1422, 10]), Vec::<isize>::new());\n    assert_eq!(candidate(vec![12345, 2033, 111, 151]), vec![111, 151]);\n    assert_eq!(candidate(vec![135, 103, 31]), vec![31, 135]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_104_unique_digits", "test": "}\n\nfn main() {\n    let candidate = unique_digits;\n    assert_eq!(candidate(vec![15, 33, 1422, 1]), vec![1, 15, 33]);\n    assert_eq!(candidate(vec![152, 323, 1422, 10]), Vec::<isize>::new());\n    assert_eq!(candidate(vec![12345, 2033, 111, 151]), vec![111, 151]);\n    assert_eq!(candidate(vec![135, 103, 31]), vec![31, 135]);\n}\n"}
{"name": "HumanEval_117_select_words", "language": "rs", "prompt": "/// Given a string s and a natural number n, you have been tasked to implement \n/// a function that returns a vector of all words from string s that contain exactly \n/// n consonants, in order these words appear in the string s.\n/// If the string s is empty then the function should return an empty vector.\n/// Note: you may assume the input string contains only letters and spaces.\n/// Examples:\n/// >>> select_words(String::from(\"Mary had a little lamb\"), 4)\n/// vec![String::from(\"little\")]\n/// >>> select_words(String::from(\"Mary had a little lamb\"), 3)\n/// vec![String::from(\"Mary\"), String::from(\"lamb\")]\n/// >>> select_words(String::from(\"simple white space\"), 2)\n/// Vec::<String>::new()\n/// >>> select_words(String::from(\"Hello world\"), 4)\n/// vec![String::from(\"world\")]\n/// >>> select_words(String::from(\"Uncle sam\"), 3)\n/// vec![String::from(\"Uncle\")]\nfn select_words(s: String, n: isize) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = select_words;\n    assert_eq!(candidate(String::from(\"Mary had a little lamb\"), 4), vec![String::from(\"little\")]);\n    assert_eq!(candidate(String::from(\"Mary had a little lamb\"), 3), vec![String::from(\"Mary\"), String::from(\"lamb\")]);\n    assert_eq!(candidate(String::from(\"simple white space\"), 2), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"Hello world\"), 4), vec![String::from(\"world\")]);\n    assert_eq!(candidate(String::from(\"Uncle sam\"), 3), vec![String::from(\"Uncle\")]);\n    assert_eq!(candidate(String::from(\"\"), 4), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"a b c d e f\"), 1), vec![String::from(\"b\"), String::from(\"c\"), String::from(\"d\"), String::from(\"f\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_117_select_words", "test": "}\n\nfn main() {\n    let candidate = select_words;\n    assert_eq!(candidate(String::from(\"Mary had a little lamb\"), 4), vec![String::from(\"little\")]);\n    assert_eq!(candidate(String::from(\"Mary had a little lamb\"), 3), vec![String::from(\"Mary\"), String::from(\"lamb\")]);\n    assert_eq!(candidate(String::from(\"simple white space\"), 2), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"Hello world\"), 4), vec![String::from(\"world\")]);\n    assert_eq!(candidate(String::from(\"Uncle sam\"), 3), vec![String::from(\"Uncle\")]);\n    assert_eq!(candidate(String::from(\"\"), 4), Vec::<String>::new());\n    assert_eq!(candidate(String::from(\"a b c d e f\"), 1), vec![String::from(\"b\"), String::from(\"c\"), String::from(\"d\"), String::from(\"f\")]);\n}\n"}
{"name": "HumanEval_72_will_it_fly", "language": "rs", "prompt": "/// Write a function that returns true if the object q will fly, and false otherwise.\n/// The object q will fly if it's balanced (it is a palindromic vector) and the sum of its elements is less than or equal the maximum possible weight w.\n/// Example:\n/// >>> will_it_fly(vec![1, 2], 5)\n/// false\n/// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n/// >>> will_it_fly(vec![3, 2, 3], 1)\n/// false\n/// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n/// >>> will_it_fly(vec![3, 2, 3], 9)\n/// true\n/// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n/// >>> will_it_fly(vec![3], 5)\n/// true\n/// # 3 is less than the maximum possible weight, and it's balanced.\nfn will_it_fly(q: Vec<isize>, w: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = will_it_fly;\n    assert_eq!(candidate(vec![3, 2, 3], 9), true);\n    assert_eq!(candidate(vec![1, 2], 5), false);\n    assert_eq!(candidate(vec![3], 5), true);\n    assert_eq!(candidate(vec![3, 2, 3], 1), false);\n    assert_eq!(candidate(vec![1, 2, 3], 6), false);\n    assert_eq!(candidate(vec![5], 5), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_72_will_it_fly", "test": "}\n\nfn main() {\n    let candidate = will_it_fly;\n    assert_eq!(candidate(vec![3, 2, 3], 9), true);\n    assert_eq!(candidate(vec![1, 2], 5), false);\n    assert_eq!(candidate(vec![3], 5), true);\n    assert_eq!(candidate(vec![3, 2, 3], 1), false);\n    assert_eq!(candidate(vec![1, 2, 3], 6), false);\n    assert_eq!(candidate(vec![5], 5), true);\n}\n"}
{"name": "HumanEval_55_fib", "language": "rs", "prompt": "/// Return n-th Fibonacci number.\n/// >>> fib(10)\n/// 55\n/// >>> fib(1)\n/// 1\n/// >>> fib(8)\n/// 21\nfn fib(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = fib;\n    assert_eq!(candidate(10), 55);\n    assert_eq!(candidate(1), 1);\n    assert_eq!(candidate(8), 21);\n    assert_eq!(candidate(11), 89);\n    assert_eq!(candidate(12), 144);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_55_fib", "test": "}\n\nfn main() {\n    let candidate = fib;\n    assert_eq!(candidate(10), 55);\n    assert_eq!(candidate(1), 1);\n    assert_eq!(candidate(8), 21);\n    assert_eq!(candidate(11), 89);\n    assert_eq!(candidate(12), 144);\n}\n"}
{"name": "HumanEval_153_Strongest_Extension", "language": "rs", "prompt": "/// You will be given the name of a class (a string) and a vector of extensions.\n/// The extensions are to be used to load additional classes to the class. The\n/// strength of the extension is as follows: Let CAP be the number of the uppercase\n/// letters in the extension's name, and let SM be the number of lowercase letters \n/// in the extension's name, the strength is given by the fraction CAP - SM. \n/// You should find the strongest extension and return a string in this \n/// format: ClassName.StrongestExtensionName.\n/// If there are two or more extensions with the same strength, you should\n/// choose the one that comes first in the vector.\n/// For example, if you are given \"Slices\" as the class and a vector of the\n/// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n/// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n/// (its strength is -1).\n/// Example:\n/// >>> Strongest_Extension(String::from(\"my_class\"), vec![String::from(\"AA\"), String::from(\"Be\"), String::from(\"CC\")])\n/// String::from(\"my_class.AA\")\nfn Strongest_Extension(class_name: String, extensions: Vec<String>) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = Strongest_Extension;\n    assert_eq!(candidate(String::from(\"Watashi\"), vec![String::from(\"tEN\"), String::from(\"niNE\"), String::from(\"eIGHt8OKe\")]), String::from(\"Watashi.eIGHt8OKe\"));\n    assert_eq!(candidate(String::from(\"Boku123\"), vec![String::from(\"nani\"), String::from(\"NazeDa\"), String::from(\"YEs.WeCaNe\"), String::from(\"32145tggg\")]), String::from(\"Boku123.YEs.WeCaNe\"));\n    assert_eq!(candidate(String::from(\"__YESIMHERE\"), vec![String::from(\"t\"), String::from(\"eMptY\"), String::from(\"nothing\"), String::from(\"zeR00\"), String::from(\"NuLl__\"), String::from(\"123NoooneB321\")]), String::from(\"__YESIMHERE.NuLl__\"));\n    assert_eq!(candidate(String::from(\"K\"), vec![String::from(\"Ta\"), String::from(\"TAR\"), String::from(\"t234An\"), String::from(\"cosSo\")]), String::from(\"K.TAR\"));\n    assert_eq!(candidate(String::from(\"__HAHA\"), vec![String::from(\"Tab\"), String::from(\"123\"), String::from(\"781345\"), String::from(\"-_-\")]), String::from(\"__HAHA.123\"));\n    assert_eq!(candidate(String::from(\"YameRore\"), vec![String::from(\"HhAas\"), String::from(\"okIWILL123\"), String::from(\"WorkOut\"), String::from(\"Fails\"), String::from(\"-_-\")]), String::from(\"YameRore.okIWILL123\"));\n    assert_eq!(candidate(String::from(\"finNNalLLly\"), vec![String::from(\"Die\"), String::from(\"NowW\"), String::from(\"Wow\"), String::from(\"WoW\")]), String::from(\"finNNalLLly.WoW\"));\n    assert_eq!(candidate(String::from(\"_\"), vec![String::from(\"Bb\"), String::from(\"91245\")]), String::from(\"_.Bb\"));\n    assert_eq!(candidate(String::from(\"Sp\"), vec![String::from(\"671235\"), String::from(\"Bb\")]), String::from(\"Sp.671235\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_153_Strongest_Extension", "test": "}\n\nfn main() {\n    let candidate = Strongest_Extension;\n    assert_eq!(candidate(String::from(\"Watashi\"), vec![String::from(\"tEN\"), String::from(\"niNE\"), String::from(\"eIGHt8OKe\")]), String::from(\"Watashi.eIGHt8OKe\"));\n    assert_eq!(candidate(String::from(\"Boku123\"), vec![String::from(\"nani\"), String::from(\"NazeDa\"), String::from(\"YEs.WeCaNe\"), String::from(\"32145tggg\")]), String::from(\"Boku123.YEs.WeCaNe\"));\n    assert_eq!(candidate(String::from(\"__YESIMHERE\"), vec![String::from(\"t\"), String::from(\"eMptY\"), String::from(\"nothing\"), String::from(\"zeR00\"), String::from(\"NuLl__\"), String::from(\"123NoooneB321\")]), String::from(\"__YESIMHERE.NuLl__\"));\n    assert_eq!(candidate(String::from(\"K\"), vec![String::from(\"Ta\"), String::from(\"TAR\"), String::from(\"t234An\"), String::from(\"cosSo\")]), String::from(\"K.TAR\"));\n    assert_eq!(candidate(String::from(\"__HAHA\"), vec![String::from(\"Tab\"), String::from(\"123\"), String::from(\"781345\"), String::from(\"-_-\")]), String::from(\"__HAHA.123\"));\n    assert_eq!(candidate(String::from(\"YameRore\"), vec![String::from(\"HhAas\"), String::from(\"okIWILL123\"), String::from(\"WorkOut\"), String::from(\"Fails\"), String::from(\"-_-\")]), String::from(\"YameRore.okIWILL123\"));\n    assert_eq!(candidate(String::from(\"finNNalLLly\"), vec![String::from(\"Die\"), String::from(\"NowW\"), String::from(\"Wow\"), String::from(\"WoW\")]), String::from(\"finNNalLLly.WoW\"));\n    assert_eq!(candidate(String::from(\"_\"), vec![String::from(\"Bb\"), String::from(\"91245\")]), String::from(\"_.Bb\"));\n    assert_eq!(candidate(String::from(\"Sp\"), vec![String::from(\"671235\"), String::from(\"Bb\")]), String::from(\"Sp.671235\"));\n}\n"}
{"name": "HumanEval_119_match_parens", "language": "rs", "prompt": "/// You are given a vector of two strings, both strings consist of open\n/// parentheses '(' or close parentheses ')' only.\n/// Your job is to check if it is possible to concatenate the two strings in\n/// some order, that the resulting string will be good.\n/// A string S is considered to be good if and only if all parentheses in S\n/// are balanced. For example: the string '(())()' is good, while the string\n/// '())' is not.\n/// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n/// Examples:\n/// >>> match_parens(vec![String::from(\"()(\"), String::from(\")\")])\n/// String::from(\"Yes\")\n/// >>> match_parens(vec![String::from(\")\"), String::from(\")\")])\n/// String::from(\"No\")\nfn match_parens(lst: Vec<String>) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = match_parens;\n    assert_eq!(candidate(vec![String::from(\"()(\"), String::from(\")\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\")\"), String::from(\")\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\"(()(())\"), String::from(\"())())\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\")())\"), String::from(\"(()()(\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\"(())))\"), String::from(\"(()())((\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\"()\"), String::from(\"())\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\"(()(\"), String::from(\"()))()\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\"((((\"), String::from(\"((())\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\")(()\"), String::from(\"(()(\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\")(\"), String::from(\")(\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\"(\"), String::from(\")\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\")\"), String::from(\"(\")]), String::from(\"Yes\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_119_match_parens", "test": "}\n\nfn main() {\n    let candidate = match_parens;\n    assert_eq!(candidate(vec![String::from(\"()(\"), String::from(\")\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\")\"), String::from(\")\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\"(()(())\"), String::from(\"())())\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\")())\"), String::from(\"(()()(\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\"(())))\"), String::from(\"(()())((\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\"()\"), String::from(\"())\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\"(()(\"), String::from(\"()))()\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\"((((\"), String::from(\"((())\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\")(()\"), String::from(\"(()(\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\")(\"), String::from(\")(\")]), String::from(\"No\"));\n    assert_eq!(candidate(vec![String::from(\"(\"), String::from(\")\")]), String::from(\"Yes\"));\n    assert_eq!(candidate(vec![String::from(\")\"), String::from(\"(\")]), String::from(\"Yes\"));\n}\n"}
{"name": "HumanEval_90_next_smallest", "language": "rs", "prompt": "/// You are given a vector of integers.\n/// Write a function next_smallest() that returns the 2nd smallest element of the vector.\n/// Return None if there is no such element.\n/// >>> next_smallest(vec![1, 2, 3, 4, 5])\n/// Some(2)\n/// >>> next_smallest(vec![5, 1, 4, 3, 2])\n/// Some(2)\n/// >>> next_smallest(vec![])\n/// None\n/// >>> next_smallest(vec![1, 1])\n/// None\nfn next_smallest(lst: Vec<isize>) -> Option<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = next_smallest;\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5]), Some(2));\n    assert_eq!(candidate(vec![5, 1, 4, 3, 2]), Some(2));\n    assert_eq!(candidate(Vec::<isize>::new()), None);\n    assert_eq!(candidate(vec![1, 1]), None);\n    assert_eq!(candidate(vec![1, 1, 1, 1, 0]), Some(1));\n    assert_eq!(candidate(vec![1, 1]), None);\n    assert_eq!(candidate(vec![-35, 34, 12, -45]), Some(-35));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_90_next_smallest", "test": "}\n\nfn main() {\n    let candidate = next_smallest;\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5]), Some(2));\n    assert_eq!(candidate(vec![5, 1, 4, 3, 2]), Some(2));\n    assert_eq!(candidate(Vec::<isize>::new()), None);\n    assert_eq!(candidate(vec![1, 1]), None);\n    assert_eq!(candidate(vec![1, 1, 1, 1, 0]), Some(1));\n    assert_eq!(candidate(vec![1, 1]), None);\n    assert_eq!(candidate(vec![-35, 34, 12, -45]), Some(-35));\n}\n"}
{"name": "HumanEval_92_any_int", "language": "rs", "prompt": "/// Create a function that takes 3 numbers.\n/// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n/// Returns false in any other cases.\n/// Examples\n/// >>> any_int(5, 2, 7)\n/// true\n/// >>> any_int(3, 2, 2)\n/// false\n/// >>> any_int(3, -2, 1)\n/// true\n/// >>> any_int(3.6, -2.2, 2)\n/// false\nfn any_int(x: f64, y: f64, z: f64) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = any_int;\n    assert_eq!(candidate(2.0, 3.0, 1.0), true);\n    assert_eq!(candidate(2.5, 2.0, 3.0), false);\n    assert_eq!(candidate(1.5, 5.0, 3.5), false);\n    assert_eq!(candidate(2.0, 6.0, 2.0), false);\n    assert_eq!(candidate(4.0, 2.0, 2.0), true);\n    assert_eq!(candidate(2.2, 2.2, 2.2), false);\n    assert_eq!(candidate(-4.0, 6.0, 2.0), true);\n    assert_eq!(candidate(2.0, 1.0, 1.0), true);\n    assert_eq!(candidate(3.0, 4.0, 7.0), true);\n    assert_eq!(candidate(3.0, 4.0, 7.0), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_92_any_int", "test": "}\n\nfn main() {\n    let candidate = any_int;\n    assert_eq!(candidate(2.0, 3.0, 1.0), true);\n    assert_eq!(candidate(2.5, 2.0, 3.0), false);\n    assert_eq!(candidate(1.5, 5.0, 3.5), false);\n    assert_eq!(candidate(2.0, 6.0, 2.0), false);\n    assert_eq!(candidate(4.0, 2.0, 2.0), true);\n    assert_eq!(candidate(2.2, 2.2, 2.2), false);\n    assert_eq!(candidate(-4.0, 6.0, 2.0), true);\n    assert_eq!(candidate(2.0, 1.0, 1.0), true);\n    assert_eq!(candidate(3.0, 4.0, 7.0), true);\n    assert_eq!(candidate(3.0, 4.0, 7.0), false);\n}\n"}
{"name": "HumanEval_2_truncate_number", "language": "rs", "prompt": "/// Given a positive floating point number, it can be decomposed into\n/// and integer part (largest integer smaller than given number) and decimals\n/// (leftover part always smaller than 1).\n/// Return the decimal part of the number.\n/// >>> truncate_number(3.5)\n/// 0.5\nfn truncate_number(number: f64) -> f64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = truncate_number;\n    assert_eq!(candidate(3.5), 0.5);\n    assert_eq!(candidate(1.25), 0.25);\n    assert_eq!(candidate(123.0), 0.0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_2_truncate_number", "test": "}\n\nfn main() {\n    let candidate = truncate_number;\n    assert_eq!(candidate(3.5), 0.5);\n    assert_eq!(candidate(1.25), 0.25);\n    assert_eq!(candidate(123.0), 0.0);\n}\n"}
{"name": "HumanEval_42_incr_list", "language": "rs", "prompt": "/// Return vector with elements incremented by 1.\n/// >>> incr_list(vec![1, 2, 3])\n/// vec![2, 3, 4]\n/// >>> incr_list(vec![5, 3, 5, 2, 3, 3, 9, 0, 123])\n/// vec![6, 4, 6, 3, 4, 4, 10, 1, 124]\nfn incr_list(l: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = incr_list;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![3, 2, 1]), vec![4, 3, 2]);\n    assert_eq!(candidate(vec![5, 2, 5, 2, 3, 3, 9, 0, 123]), vec![6, 3, 6, 3, 4, 4, 10, 1, 124]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_42_incr_list", "test": "}\n\nfn main() {\n    let candidate = incr_list;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![3, 2, 1]), vec![4, 3, 2]);\n    assert_eq!(candidate(vec![5, 2, 5, 2, 3, 3, 9, 0, 123]), vec![6, 3, 6, 3, 4, 4, 10, 1, 124]);\n}\n"}
{"name": "HumanEval_150_x_or_y", "language": "rs", "prompt": "/// A simple program which should return the value of x if n is \n/// a prime number and should return the value of y otherwise.\n/// Examples:\n/// >>> x_or_y(7, 34, 12)\n/// 34\n/// >>> x_or_y(15, 8, 5)\n/// 5\nfn x_or_y(n: isize, x: isize, y: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = x_or_y;\n    assert_eq!(candidate(7, 34, 12), 34);\n    assert_eq!(candidate(15, 8, 5), 5);\n    assert_eq!(candidate(3, 33, 5212), 33);\n    assert_eq!(candidate(1259, 3, 52), 3);\n    assert_eq!(candidate(7919, -1, 12), -1);\n    assert_eq!(candidate(3609, 1245, 583), 583);\n    assert_eq!(candidate(91, 56, 129), 129);\n    assert_eq!(candidate(6, 34, 1234), 1234);\n    assert_eq!(candidate(1, 2, 0), 0);\n    assert_eq!(candidate(2, 2, 0), 2);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_150_x_or_y", "test": "}\n\nfn main() {\n    let candidate = x_or_y;\n    assert_eq!(candidate(7, 34, 12), 34);\n    assert_eq!(candidate(15, 8, 5), 5);\n    assert_eq!(candidate(3, 33, 5212), 33);\n    assert_eq!(candidate(1259, 3, 52), 3);\n    assert_eq!(candidate(7919, -1, 12), -1);\n    assert_eq!(candidate(3609, 1245, 583), 583);\n    assert_eq!(candidate(91, 56, 129), 129);\n    assert_eq!(candidate(6, 34, 1234), 1234);\n    assert_eq!(candidate(1, 2, 0), 0);\n    assert_eq!(candidate(2, 2, 0), 2);\n}\n"}
{"name": "HumanEval_49_modp", "language": "rs", "prompt": "/// Return 2^n modulo p (be aware of numerics).\n/// >>> modp(3, 5)\n/// 3\n/// >>> modp(1101, 101)\n/// 2\n/// >>> modp(0, 101)\n/// 1\n/// >>> modp(3, 11)\n/// 8\n/// >>> modp(100, 101)\n/// 1\nfn modp(n: isize, p: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = modp;\n    assert_eq!(candidate(3, 5), 3);\n    assert_eq!(candidate(1101, 101), 2);\n    assert_eq!(candidate(0, 101), 1);\n    assert_eq!(candidate(3, 11), 8);\n    assert_eq!(candidate(100, 101), 1);\n    assert_eq!(candidate(30, 5), 4);\n    assert_eq!(candidate(31, 5), 3);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_49_modp", "test": "}\n\nfn main() {\n    let candidate = modp;\n    assert_eq!(candidate(3, 5), 3);\n    assert_eq!(candidate(1101, 101), 2);\n    assert_eq!(candidate(0, 101), 1);\n    assert_eq!(candidate(3, 11), 8);\n    assert_eq!(candidate(100, 101), 1);\n    assert_eq!(candidate(30, 5), 4);\n    assert_eq!(candidate(31, 5), 3);\n}\n"}
{"name": "HumanEval_155_even_odd_count", "language": "rs", "prompt": "/// Given an integer. return a tuple that has the number of even and odd digits respectively.\n/// Example:\n/// >>> even_odd_count(-12)\n/// (1, 1)\n/// >>> even_odd_count(123)\n/// (1, 2)\nfn even_odd_count(num: isize) -> (isize, isize) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = even_odd_count;\n    assert_eq!(candidate(7), (0, 1));\n    assert_eq!(candidate(-78), (1, 1));\n    assert_eq!(candidate(3452), (2, 2));\n    assert_eq!(candidate(346211), (3, 3));\n    assert_eq!(candidate(-345821), (3, 3));\n    assert_eq!(candidate(-2), (1, 0));\n    assert_eq!(candidate(-45347), (2, 3));\n    assert_eq!(candidate(0), (1, 0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_155_even_odd_count", "test": "}\n\nfn main() {\n    let candidate = even_odd_count;\n    assert_eq!(candidate(7), (0, 1));\n    assert_eq!(candidate(-78), (1, 1));\n    assert_eq!(candidate(3452), (2, 2));\n    assert_eq!(candidate(346211), (3, 3));\n    assert_eq!(candidate(-345821), (3, 3));\n    assert_eq!(candidate(-2), (1, 0));\n    assert_eq!(candidate(-45347), (2, 3));\n    assert_eq!(candidate(0), (1, 0));\n}\n"}
{"name": "HumanEval_80_is_happy", "language": "rs", "prompt": "/// You are given a string s.\n/// Your task is to check if the string is haprs or not.\n/// A string is haprs if its length is at least 3 and every 3 consecutive letters are distinct\n/// For example:\n/// >>> is_happy(String::from(\"a\"))\n/// false\n/// >>> is_happy(String::from(\"aa\"))\n/// false\n/// >>> is_happy(String::from(\"abcd\"))\n/// true\n/// >>> is_happy(String::from(\"aabb\"))\n/// false\n/// >>> is_happy(String::from(\"adb\"))\n/// true\n/// >>> is_happy(String::from(\"xyy\"))\n/// false\nfn is_happy(s: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_happy;\n    assert_eq!(candidate(String::from(\"a\")), false);\n    assert_eq!(candidate(String::from(\"aa\")), false);\n    assert_eq!(candidate(String::from(\"abcd\")), true);\n    assert_eq!(candidate(String::from(\"aabb\")), false);\n    assert_eq!(candidate(String::from(\"adb\")), true);\n    assert_eq!(candidate(String::from(\"xyy\")), false);\n    assert_eq!(candidate(String::from(\"iopaxpoi\")), true);\n    assert_eq!(candidate(String::from(\"iopaxioi\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_80_is_happy", "test": "}\n\nfn main() {\n    let candidate = is_happy;\n    assert_eq!(candidate(String::from(\"a\")), false);\n    assert_eq!(candidate(String::from(\"aa\")), false);\n    assert_eq!(candidate(String::from(\"abcd\")), true);\n    assert_eq!(candidate(String::from(\"aabb\")), false);\n    assert_eq!(candidate(String::from(\"adb\")), true);\n    assert_eq!(candidate(String::from(\"xyy\")), false);\n    assert_eq!(candidate(String::from(\"iopaxpoi\")), true);\n    assert_eq!(candidate(String::from(\"iopaxioi\")), false);\n}\n"}
{"name": "HumanEval_59_largest_prime_factor", "language": "rs", "prompt": "/// Return the largest prime factor of n. Assume n > 1 and is not a prime.\n/// >>> largest_prime_factor(13195)\n/// 29\n/// >>> largest_prime_factor(2048)\n/// 2\nfn largest_prime_factor(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = largest_prime_factor;\n    assert_eq!(candidate(15), 5);\n    assert_eq!(candidate(27), 3);\n    assert_eq!(candidate(63), 7);\n    assert_eq!(candidate(330), 11);\n    assert_eq!(candidate(13195), 29);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_59_largest_prime_factor", "test": "}\n\nfn main() {\n    let candidate = largest_prime_factor;\n    assert_eq!(candidate(15), 5);\n    assert_eq!(candidate(27), 3);\n    assert_eq!(candidate(63), 7);\n    assert_eq!(candidate(330), 11);\n    assert_eq!(candidate(13195), 29);\n}\n"}
{"name": "HumanEval_66_digitSum", "language": "rs", "prompt": "/// Task\n/// Write a function that takes a string as input and returns the sum of the upper characters only'\n/// ASCII codes.\n/// Examples:\n/// >>> digitSum(String::from(\"\"))\n/// 0\n/// >>> digitSum(String::from(\"abAB\"))\n/// 131\n/// >>> digitSum(String::from(\"abcCd\"))\n/// 67\n/// >>> digitSum(String::from(\"helloE\"))\n/// 69\n/// >>> digitSum(String::from(\"woArBld\"))\n/// 131\n/// >>> digitSum(String::from(\"aAaaaXa\"))\n/// 153\nfn digitSum(s: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = digitSum;\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"abAB\")), 131);\n    assert_eq!(candidate(String::from(\"abcCd\")), 67);\n    assert_eq!(candidate(String::from(\"helloE\")), 69);\n    assert_eq!(candidate(String::from(\"woArBld\")), 131);\n    assert_eq!(candidate(String::from(\"aAaaaXa\")), 153);\n    assert_eq!(candidate(String::from(\" How are yOu?\")), 151);\n    assert_eq!(candidate(String::from(\"You arE Very Smart\")), 327);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_66_digitSum", "test": "}\n\nfn main() {\n    let candidate = digitSum;\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"abAB\")), 131);\n    assert_eq!(candidate(String::from(\"abcCd\")), 67);\n    assert_eq!(candidate(String::from(\"helloE\")), 69);\n    assert_eq!(candidate(String::from(\"woArBld\")), 131);\n    assert_eq!(candidate(String::from(\"aAaaaXa\")), 153);\n    assert_eq!(candidate(String::from(\" How are yOu?\")), 151);\n    assert_eq!(candidate(String::from(\"You arE Very Smart\")), 327);\n}\n"}
{"name": "HumanEval_21_rescale_to_unit", "language": "rs", "prompt": "/// Given vector of numbers (of at least two elements), apply a linear transform to that vector,\n/// such that the smallest number will become 0 and the largest will become 1\n/// >>> rescale_to_unit(vec![1.0, 2.0, 3.0, 4.0, 5.0])\n/// vec![0.0, 0.25, 0.5, 0.75, 1.0]\nfn rescale_to_unit(numbers: Vec<f64>) -> Vec<f64> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = rescale_to_unit;\n    assert_eq!(candidate(vec![2.0, 49.9]), vec![0.0, 1.0]);\n    assert_eq!(candidate(vec![100.0, 49.9]), vec![1.0, 0.0]);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0]), vec![0.0, 0.25, 0.5, 0.75, 1.0]);\n    assert_eq!(candidate(vec![2.0, 1.0, 5.0, 3.0, 4.0]), vec![0.25, 0.0, 1.0, 0.5, 0.75]);\n    assert_eq!(candidate(vec![12.0, 11.0, 15.0, 13.0, 14.0]), vec![0.25, 0.0, 1.0, 0.5, 0.75]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_21_rescale_to_unit", "test": "}\n\nfn main() {\n    let candidate = rescale_to_unit;\n    assert_eq!(candidate(vec![2.0, 49.9]), vec![0.0, 1.0]);\n    assert_eq!(candidate(vec![100.0, 49.9]), vec![1.0, 0.0]);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0]), vec![0.0, 0.25, 0.5, 0.75, 1.0]);\n    assert_eq!(candidate(vec![2.0, 1.0, 5.0, 3.0, 4.0]), vec![0.25, 0.0, 1.0, 0.5, 0.75]);\n    assert_eq!(candidate(vec![12.0, 11.0, 15.0, 13.0, 14.0]), vec![0.25, 0.0, 1.0, 0.5, 0.75]);\n}\n"}
{"name": "HumanEval_121_solution", "language": "rs", "prompt": "/// Given a non-empty vector of integers, return the sum of all of the odd elements that are in even positions.\n/// Examples\n/// >>> solution(vec![5, 8, 7, 1])\n/// 12\n/// >>> solution(vec![3, 3, 3, 3, 3])\n/// 9\n/// >>> solution(vec![30, 13, 24, 321])\n/// 0\nfn solution(lst: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = solution;\n    assert_eq!(candidate(vec![5, 8, 7, 1]), 12);\n    assert_eq!(candidate(vec![3, 3, 3, 3, 3]), 9);\n    assert_eq!(candidate(vec![30, 13, 24, 321]), 0);\n    assert_eq!(candidate(vec![5, 9]), 5);\n    assert_eq!(candidate(vec![2, 4, 8]), 0);\n    assert_eq!(candidate(vec![30, 13, 23, 32]), 23);\n    assert_eq!(candidate(vec![3, 13, 2, 9]), 3);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_121_solution", "test": "}\n\nfn main() {\n    let candidate = solution;\n    assert_eq!(candidate(vec![5, 8, 7, 1]), 12);\n    assert_eq!(candidate(vec![3, 3, 3, 3, 3]), 9);\n    assert_eq!(candidate(vec![30, 13, 24, 321]), 0);\n    assert_eq!(candidate(vec![5, 9]), 5);\n    assert_eq!(candidate(vec![2, 4, 8]), 0);\n    assert_eq!(candidate(vec![30, 13, 23, 32]), 23);\n    assert_eq!(candidate(vec![3, 13, 2, 9]), 3);\n}\n"}
{"name": "HumanEval_68_pluck", "language": "rs", "prompt": "/// \"Given a vector representing a branch of a tree that has non-negative integer nodes\n/// your task is to pluck one of the nodes and return it.\n/// The plucked node should be the node with the smallest even value.\n/// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n/// The plucked node should be returned in a vector, [ smalest_value, its index ],\n/// If there are no even values or the given vector is empty, return [].\n/// Example 1:\n/// >>> pluck(vec![4, 2, 3])\n/// vec![2, 1]\n/// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n/// Example 2:\n/// >>> pluck(vec![1, 2, 3])\n/// vec![2, 1]\n/// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n/// Example 3:\n/// >>> pluck(vec![])\n/// Vec::<isize>::new()\n/// Example 4:\n/// >>> pluck(vec![5, 0, 3, 0, 4, 2])\n/// vec![0, 1]\n/// Explanation: 0 is the smallest value, but  there are two zeros,\n/// so we will choose the first zero, which has the smallest index.\n/// Constraints:\n/// * 1 <= nodes.length <= 10000\n/// * 0 <= node.value\nfn pluck(arr: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = pluck;\n    assert_eq!(candidate(vec![4, 2, 3]), vec![2, 1]);\n    assert_eq!(candidate(vec![1, 2, 3]), vec![2, 1]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![5, 0, 3, 0, 4, 2]), vec![0, 1]);\n    assert_eq!(candidate(vec![1, 2, 3, 0, 5, 3]), vec![0, 3]);\n    assert_eq!(candidate(vec![5, 4, 8, 4, 8]), vec![4, 1]);\n    assert_eq!(candidate(vec![7, 6, 7, 1]), vec![6, 1]);\n    assert_eq!(candidate(vec![7, 9, 7, 1]), Vec::<isize>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_68_pluck", "test": "}\n\nfn main() {\n    let candidate = pluck;\n    assert_eq!(candidate(vec![4, 2, 3]), vec![2, 1]);\n    assert_eq!(candidate(vec![1, 2, 3]), vec![2, 1]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![5, 0, 3, 0, 4, 2]), vec![0, 1]);\n    assert_eq!(candidate(vec![1, 2, 3, 0, 5, 3]), vec![0, 3]);\n    assert_eq!(candidate(vec![5, 4, 8, 4, 8]), vec![4, 1]);\n    assert_eq!(candidate(vec![7, 6, 7, 1]), vec![6, 1]);\n    assert_eq!(candidate(vec![7, 9, 7, 1]), Vec::<isize>::new());\n}\n"}
{"name": "HumanEval_147_get_max_triples", "language": "rs", "prompt": "/// You are given a positive integer n. You have to create an integer vector a of length n.\n/// For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n/// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n/// and a[i] + a[j] + a[k] is a multiple of 3.\n/// Example :\n/// >>> get_max_triples(5)\n/// 1\n/// Explanation: \n/// a = [1, 3, 7, 13, 21]\n/// The only valid triple is (1, 7, 13).\nfn get_max_triples(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = get_max_triples;\n    assert_eq!(candidate(5), 1);\n    assert_eq!(candidate(6), 4);\n    assert_eq!(candidate(10), 36);\n    assert_eq!(candidate(100), 53361);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_147_get_max_triples", "test": "}\n\nfn main() {\n    let candidate = get_max_triples;\n    assert_eq!(candidate(5), 1);\n    assert_eq!(candidate(6), 4);\n    assert_eq!(candidate(10), 36);\n    assert_eq!(candidate(100), 53361);\n}\n"}
{"name": "HumanEval_110_exchange", "language": "rs", "prompt": "/// In this problem, you will implement a function that takes two vectors of numbers,\n/// and determines whether it is possible to perform an exchange of elements\n/// between them to make lst1 a vector of only even numbers.\n/// There is no limit on the number of exchanged elements between lst1 and lst2.\n/// If it is possible to exchange elements between the lst1 and lst2 to make\n/// all the elements of lst1 to be even, return \"YES\".\n/// Otherwise, return \"NO\".\n/// For example:\n/// >>> exchange(vec![1, 2, 3, 4], vec![1, 2, 3, 4])\n/// String::from(\"YES\")\n/// >>> exchange(vec![1, 2, 3, 4], vec![1, 5, 3, 4])\n/// String::from(\"NO\")\n/// It is assumed that the input vectors will be non-empty.\nfn exchange(lst1: Vec<isize>, lst2: Vec<isize>) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = exchange;\n    assert_eq!(candidate(vec![1, 2, 3, 4], vec![1, 2, 3, 4]), String::from(\"YES\"));\n    assert_eq!(candidate(vec![1, 2, 3, 4], vec![1, 5, 3, 4]), String::from(\"NO\"));\n    assert_eq!(candidate(vec![1, 2, 3, 4], vec![2, 1, 4, 3]), String::from(\"YES\"));\n    assert_eq!(candidate(vec![5, 7, 3], vec![2, 6, 4]), String::from(\"YES\"));\n    assert_eq!(candidate(vec![5, 7, 3], vec![2, 6, 3]), String::from(\"NO\"));\n    assert_eq!(candidate(vec![3, 2, 6, 1, 8, 9], vec![3, 5, 5, 1, 1, 1]), String::from(\"NO\"));\n    assert_eq!(candidate(vec![100, 200], vec![200, 200]), String::from(\"YES\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_110_exchange", "test": "}\n\nfn main() {\n    let candidate = exchange;\n    assert_eq!(candidate(vec![1, 2, 3, 4], vec![1, 2, 3, 4]), String::from(\"YES\"));\n    assert_eq!(candidate(vec![1, 2, 3, 4], vec![1, 5, 3, 4]), String::from(\"NO\"));\n    assert_eq!(candidate(vec![1, 2, 3, 4], vec![2, 1, 4, 3]), String::from(\"YES\"));\n    assert_eq!(candidate(vec![5, 7, 3], vec![2, 6, 4]), String::from(\"YES\"));\n    assert_eq!(candidate(vec![5, 7, 3], vec![2, 6, 3]), String::from(\"NO\"));\n    assert_eq!(candidate(vec![3, 2, 6, 1, 8, 9], vec![3, 5, 5, 1, 1, 1]), String::from(\"NO\"));\n    assert_eq!(candidate(vec![100, 200], vec![200, 200]), String::from(\"YES\"));\n}\n"}
{"name": "HumanEval_47_median", "language": "rs", "prompt": "/// Return median of elements in the vector l.\n/// >>> median(vec![3, 1, 2, 4, 5])\n/// 3.0\n/// >>> median(vec![-10, 4, 6, 1000, 10, 20])\n/// 15.0\nfn median(l: Vec<isize>) -> f64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = median;\n    assert_eq!(candidate(vec![3, 1, 2, 4, 5]), 3.0);\n    assert_eq!(candidate(vec![-10, 4, 6, 1000, 10, 20]), 8.0);\n    assert_eq!(candidate(vec![5]), 5.0);\n    assert_eq!(candidate(vec![6, 5]), 5.5);\n    assert_eq!(candidate(vec![8, 1, 3, 9, 9, 2, 7]), 7.0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_47_median", "test": "}\n\nfn main() {\n    let candidate = median;\n    assert_eq!(candidate(vec![3, 1, 2, 4, 5]), 3.0);\n    assert_eq!(candidate(vec![-10, 4, 6, 1000, 10, 20]), 8.0);\n    assert_eq!(candidate(vec![5]), 5.0);\n    assert_eq!(candidate(vec![6, 5]), 5.5);\n    assert_eq!(candidate(vec![8, 1, 3, 9, 9, 2, 7]), 7.0);\n}\n"}
{"name": "HumanEval_82_prime_length", "language": "rs", "prompt": "/// Write a function that takes a string and returns true if the string\n/// length is a prime number or false otherwise\n/// Examples\n/// >>> prime_length(String::from(\"Hello\"))\n/// true\n/// >>> prime_length(String::from(\"abcdcba\"))\n/// true\n/// >>> prime_length(String::from(\"kittens\"))\n/// true\n/// >>> prime_length(String::from(\"orange\"))\n/// false\nfn prime_length(string: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = prime_length;\n    assert_eq!(candidate(String::from(\"Hello\")), true);\n    assert_eq!(candidate(String::from(\"abcdcba\")), true);\n    assert_eq!(candidate(String::from(\"kittens\")), true);\n    assert_eq!(candidate(String::from(\"orange\")), false);\n    assert_eq!(candidate(String::from(\"wow\")), true);\n    assert_eq!(candidate(String::from(\"world\")), true);\n    assert_eq!(candidate(String::from(\"MadaM\")), true);\n    assert_eq!(candidate(String::from(\"Wow\")), true);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"HI\")), true);\n    assert_eq!(candidate(String::from(\"go\")), true);\n    assert_eq!(candidate(String::from(\"gogo\")), false);\n    assert_eq!(candidate(String::from(\"aaaaaaaaaaaaaaa\")), false);\n    assert_eq!(candidate(String::from(\"Madam\")), true);\n    assert_eq!(candidate(String::from(\"M\")), false);\n    assert_eq!(candidate(String::from(\"0\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_82_prime_length", "test": "}\n\nfn main() {\n    let candidate = prime_length;\n    assert_eq!(candidate(String::from(\"Hello\")), true);\n    assert_eq!(candidate(String::from(\"abcdcba\")), true);\n    assert_eq!(candidate(String::from(\"kittens\")), true);\n    assert_eq!(candidate(String::from(\"orange\")), false);\n    assert_eq!(candidate(String::from(\"wow\")), true);\n    assert_eq!(candidate(String::from(\"world\")), true);\n    assert_eq!(candidate(String::from(\"MadaM\")), true);\n    assert_eq!(candidate(String::from(\"Wow\")), true);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"HI\")), true);\n    assert_eq!(candidate(String::from(\"go\")), true);\n    assert_eq!(candidate(String::from(\"gogo\")), false);\n    assert_eq!(candidate(String::from(\"aaaaaaaaaaaaaaa\")), false);\n    assert_eq!(candidate(String::from(\"Madam\")), true);\n    assert_eq!(candidate(String::from(\"M\")), false);\n    assert_eq!(candidate(String::from(\"0\")), false);\n}\n"}
{"name": "HumanEval_73_smallest_change", "language": "rs", "prompt": "/// Given a vector arr of integers, find the minimum number of elements that\n/// need to be changed to make the vector palindromic. A palindromic vector is a vector that\n/// is read the same backwards and forwards. In one change, you can change one element to any other element.\n/// For example:\n/// >>> smallest_change(vec![1, 2, 3, 5, 4, 7, 9, 6])\n/// 4\n/// >>> smallest_change(vec![1, 2, 3, 4, 3, 2, 2])\n/// 1\n/// >>> smallest_change(vec![1, 2, 3, 2, 1])\n/// 0\nfn smallest_change(arr: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = smallest_change;\n    assert_eq!(candidate(vec![1, 2, 3, 5, 4, 7, 9, 6]), 4);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 3, 2, 2]), 1);\n    assert_eq!(candidate(vec![1, 4, 2]), 1);\n    assert_eq!(candidate(vec![1, 4, 4, 2]), 1);\n    assert_eq!(candidate(vec![1, 2, 3, 2, 1]), 0);\n    assert_eq!(candidate(vec![3, 1, 1, 3]), 0);\n    assert_eq!(candidate(vec![1]), 0);\n    assert_eq!(candidate(vec![0, 1]), 1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_73_smallest_change", "test": "}\n\nfn main() {\n    let candidate = smallest_change;\n    assert_eq!(candidate(vec![1, 2, 3, 5, 4, 7, 9, 6]), 4);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 3, 2, 2]), 1);\n    assert_eq!(candidate(vec![1, 4, 2]), 1);\n    assert_eq!(candidate(vec![1, 4, 4, 2]), 1);\n    assert_eq!(candidate(vec![1, 2, 3, 2, 1]), 0);\n    assert_eq!(candidate(vec![3, 1, 1, 3]), 0);\n    assert_eq!(candidate(vec![1]), 0);\n    assert_eq!(candidate(vec![0, 1]), 1);\n}\n"}
{"name": "HumanEval_133_sum_squares", "language": "rs", "prompt": "/// You are given a vector of numbers.\n/// You need to return the sum of squared numbers in the given vector,\n/// round each element in the vector to the upper int(Ceiling) first.\n/// Examples:\n/// >>> lst(vec![1.0, 2.0, 3.0])\n/// 14\n/// >>> lst(vec![1.0, 4.0, 9.0])\n/// 98\n/// >>> lst(vec![1.0, 3.0, 5.0, 7.0])\n/// 84\n/// >>> lst(vec![1.4, 4.2, 0.0])\n/// 29\n/// >>> lst(vec![-2.4, 1.0, 1.0])\n/// 6\nfn sum_squares(lst: Vec<f64>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sum_squares;\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0]), 14);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0]), 14);\n    assert_eq!(candidate(vec![1.0, 3.0, 5.0, 7.0]), 84);\n    assert_eq!(candidate(vec![1.4, 4.2, 0.0]), 29);\n    assert_eq!(candidate(vec![-2.4, 1.0, 1.0]), 6);\n    assert_eq!(candidate(vec![100.0, 1.0, 15.0, 2.0]), 10230);\n    assert_eq!(candidate(vec![10000.0, 10000.0]), 200000000);\n    assert_eq!(candidate(vec![-1.4, 4.6, 6.3]), 75);\n    assert_eq!(candidate(vec![-1.4, 17.9, 18.9, 19.9]), 1086);\n    assert_eq!(candidate(vec![0.0]), 0);\n    assert_eq!(candidate(vec![-1.0]), 1);\n    assert_eq!(candidate(vec![-1.0, 1.0, 0.0]), 2);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_133_sum_squares", "test": "}\n\nfn main() {\n    let candidate = sum_squares;\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0]), 14);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0]), 14);\n    assert_eq!(candidate(vec![1.0, 3.0, 5.0, 7.0]), 84);\n    assert_eq!(candidate(vec![1.4, 4.2, 0.0]), 29);\n    assert_eq!(candidate(vec![-2.4, 1.0, 1.0]), 6);\n    assert_eq!(candidate(vec![100.0, 1.0, 15.0, 2.0]), 10230);\n    assert_eq!(candidate(vec![10000.0, 10000.0]), 200000000);\n    assert_eq!(candidate(vec![-1.4, 4.6, 6.3]), 75);\n    assert_eq!(candidate(vec![-1.4, 17.9, 18.9, 19.9]), 1086);\n    assert_eq!(candidate(vec![0.0]), 0);\n    assert_eq!(candidate(vec![-1.0]), 1);\n    assert_eq!(candidate(vec![-1.0, 1.0, 0.0]), 2);\n}\n"}
{"name": "HumanEval_141_file_name_check", "language": "rs", "prompt": "/// Create a function which takes a string representing a file's name, and returns\n/// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n/// A file's name is considered to be valid if and only if all the following conditions \n/// are met:\n/// - There should not be more than three digits ('0'-'9') in the file's name.\n/// - The file's name contains exactly one dot '.'\n/// - The substring before the dot should not be empty, and it starts with a letter from \n/// the latin alphapet ('a'-'z' and 'A'-'Z').\n/// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n/// Examples:\n/// >>> file_name_check(String::from(\"example.txt\"))\n/// String::from(\"Yes\")\n/// >>> file_name_check(String::from(\"1example.dll\"))\n/// String::from(\"No\")\nfn file_name_check(file_name: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = file_name_check;\n    assert_eq!(candidate(String::from(\"example.txt\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"1example.dll\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"s1sdf3.asd\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"K.dll\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"MY16FILE3.exe\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"His12FILE94.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"_Y.txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"?aREYA.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"/this_is_valid.dll\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"this_is_valid.wow\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"this_is_valid.txt\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"this_is_valid.txtexe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"#this2_i4s_5valid.ten\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"@this1_is6_valid.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"this_is_12valid.6exe4.txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"all.exe.txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"I563_No.exe\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"Is3youfault.txt\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"no_one#knows.dll\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"1I563_Yes3.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"I563_Yes3.txtt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"final..txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"final132\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"_f4indsartal132.\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\".txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"s.\")), String::from(\"No\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_141_file_name_check", "test": "}\n\nfn main() {\n    let candidate = file_name_check;\n    assert_eq!(candidate(String::from(\"example.txt\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"1example.dll\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"s1sdf3.asd\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"K.dll\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"MY16FILE3.exe\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"His12FILE94.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"_Y.txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"?aREYA.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"/this_is_valid.dll\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"this_is_valid.wow\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"this_is_valid.txt\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"this_is_valid.txtexe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"#this2_i4s_5valid.ten\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"@this1_is6_valid.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"this_is_12valid.6exe4.txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"all.exe.txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"I563_No.exe\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"Is3youfault.txt\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"no_one#knows.dll\")), String::from(\"Yes\"));\n    assert_eq!(candidate(String::from(\"1I563_Yes3.exe\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"I563_Yes3.txtt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"final..txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"final132\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"_f4indsartal132.\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\".txt\")), String::from(\"No\"));\n    assert_eq!(candidate(String::from(\"s.\")), String::from(\"No\"));\n}\n"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "rs", "prompt": "/// triples_sum_to_zero takes a vector of integers as an input.\n/// it returns true if there are three distinct elements in the vector that\n/// sum to zero, and false otherwise.\n/// >>> triples_sum_to_zero(vec![1, 3, 5, 0])\n/// false\n/// >>> triples_sum_to_zero(vec![1, 3, -2, 1])\n/// true\n/// >>> triples_sum_to_zero(vec![1, 2, 3, 7])\n/// false\n/// >>> triples_sum_to_zero(vec![2, 4, -5, 3, 9, 7])\n/// true\n/// >>> triples_sum_to_zero(vec![1])\n/// false\nfn triples_sum_to_zero(l: Vec<isize>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = triples_sum_to_zero;\n    assert_eq!(candidate(vec![1, 3, 5, 0]), false);\n    assert_eq!(candidate(vec![1, 3, 5, -1]), false);\n    assert_eq!(candidate(vec![1, 3, -2, 1]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 7]), false);\n    assert_eq!(candidate(vec![1, 2, 5, 7]), false);\n    assert_eq!(candidate(vec![2, 4, -5, 3, 9, 7]), true);\n    assert_eq!(candidate(vec![1]), false);\n    assert_eq!(candidate(vec![1, 3, 5, -100]), false);\n    assert_eq!(candidate(vec![100, 3, 5, -100]), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "}\n\nfn main() {\n    let candidate = triples_sum_to_zero;\n    assert_eq!(candidate(vec![1, 3, 5, 0]), false);\n    assert_eq!(candidate(vec![1, 3, 5, -1]), false);\n    assert_eq!(candidate(vec![1, 3, -2, 1]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 7]), false);\n    assert_eq!(candidate(vec![1, 2, 5, 7]), false);\n    assert_eq!(candidate(vec![2, 4, -5, 3, 9, 7]), true);\n    assert_eq!(candidate(vec![1]), false);\n    assert_eq!(candidate(vec![1, 3, 5, -100]), false);\n    assert_eq!(candidate(vec![100, 3, 5, -100]), false);\n}\n"}
{"name": "HumanEval_127_intersection", "language": "rs", "prompt": "/// You are given two intervals,\n/// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n/// The given intervals are closed which means that the interval (start, end)\n/// includes both start and end.\n/// For each given interval, it is assumed that its start is less or equal its end.\n/// Your task is to determine whether the length of intersection of these two \n/// intervals is a prime number.\n/// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n/// which its length is 1, which not a prime number.\n/// If the length of the intersection is a prime number, return \"YES\",\n/// otherwise, return \"NO\".\n/// If the two intervals don't intersect, return \"NO\".\n/// [input/output] samples:\n/// >>> intersection((1, 2), (2, 3))\n/// String::from(\"NO\")\n/// >>> intersection((-1, 1), (0, 4))\n/// String::from(\"NO\")\n/// >>> intersection((-3, -1), (-5, 5))\n/// String::from(\"YES\")\nfn intersection(interval1: (isize, isize), interval2: (isize, isize)) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = intersection;\n    assert_eq!(candidate((1, 2), (2, 3)), String::from(\"NO\"));\n    assert_eq!(candidate((-1, 1), (0, 4)), String::from(\"NO\"));\n    assert_eq!(candidate((-3, -1), (-5, 5)), String::from(\"YES\"));\n    assert_eq!(candidate((-2, 2), (-4, 0)), String::from(\"YES\"));\n    assert_eq!(candidate((-11, 2), (-1, -1)), String::from(\"NO\"));\n    assert_eq!(candidate((1, 2), (3, 5)), String::from(\"NO\"));\n    assert_eq!(candidate((1, 2), (1, 2)), String::from(\"NO\"));\n    assert_eq!(candidate((-2, -2), (-3, -2)), String::from(\"NO\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_127_intersection", "test": "}\n\nfn main() {\n    let candidate = intersection;\n    assert_eq!(candidate((1, 2), (2, 3)), String::from(\"NO\"));\n    assert_eq!(candidate((-1, 1), (0, 4)), String::from(\"NO\"));\n    assert_eq!(candidate((-3, -1), (-5, 5)), String::from(\"YES\"));\n    assert_eq!(candidate((-2, 2), (-4, 0)), String::from(\"YES\"));\n    assert_eq!(candidate((-11, 2), (-1, -1)), String::from(\"NO\"));\n    assert_eq!(candidate((1, 2), (3, 5)), String::from(\"NO\"));\n    assert_eq!(candidate((1, 2), (1, 2)), String::from(\"NO\"));\n    assert_eq!(candidate((-2, -2), (-3, -2)), String::from(\"NO\"));\n}\n"}
{"name": "HumanEval_1_separate_paren_groups", "language": "rs", "prompt": "/// Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n/// separate those group into separate strings and return the vector of those.\n/// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n/// Ignore any spaces in the input string.\n/// >>> separate_paren_groups(String::from(\"( ) (( )) (( )( ))\"))\n/// vec![String::from(\"()\"), String::from(\"(())\"), String::from(\"(()())\")]\nfn separate_paren_groups(paren_string: String) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = separate_paren_groups;\n    assert_eq!(candidate(String::from(\"(()()) ((())) () ((())()())\")), vec![String::from(\"(()())\"), String::from(\"((()))\"), String::from(\"()\"), String::from(\"((())()())\")]);\n    assert_eq!(candidate(String::from(\"() (()) ((())) (((())))\")), vec![String::from(\"()\"), String::from(\"(())\"), String::from(\"((()))\"), String::from(\"(((())))\")]);\n    assert_eq!(candidate(String::from(\"(()(())((())))\")), vec![String::from(\"(()(())((())))\")]);\n    assert_eq!(candidate(String::from(\"( ) (( )) (( )( ))\")), vec![String::from(\"()\"), String::from(\"(())\"), String::from(\"(()())\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_1_separate_paren_groups", "test": "}\n\nfn main() {\n    let candidate = separate_paren_groups;\n    assert_eq!(candidate(String::from(\"(()()) ((())) () ((())()())\")), vec![String::from(\"(()())\"), String::from(\"((()))\"), String::from(\"()\"), String::from(\"((())()())\")]);\n    assert_eq!(candidate(String::from(\"() (()) ((())) (((())))\")), vec![String::from(\"()\"), String::from(\"(())\"), String::from(\"((()))\"), String::from(\"(((())))\")]);\n    assert_eq!(candidate(String::from(\"(()(())((())))\")), vec![String::from(\"(()(())((())))\")]);\n    assert_eq!(candidate(String::from(\"( ) (( )) (( )( ))\")), vec![String::from(\"()\"), String::from(\"(())\"), String::from(\"(()())\")]);\n}\n"}
{"name": "HumanEval_152_compare", "language": "rs", "prompt": "/// I think we all remember that feeling when the result of some long-awaited\n/// event is finally known. The feelings and thoughts you have at that moment are\n/// definitely worth noting down and comparing.\n/// Your task is to determine if a person correctly guessed the results of a number of matches.\n/// You are given two vectors of scores and guesses of equal length, where each index shows a match. \n/// Return a vector of the same length denoting how far off each guess was. If they have guessed correctly,\n/// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n/// example:\n/// >>> compare(vec![1, 2, 3, 4, 5, 1], vec![1, 2, 3, 4, 2, -2])\n/// vec![0, 0, 0, 0, 3, 3]\n/// >>> compare(vec![0, 5, 0, 0, 0, 4], vec![4, 1, 1, 0, 0, -2])\n/// vec![4, 4, 1, 0, 0, 6]\nfn compare(game: Vec<isize>, guess: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = compare;\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 1], vec![1, 2, 3, 4, 2, -2]), vec![0, 0, 0, 0, 3, 3]);\n    assert_eq!(candidate(vec![0, 0, 0, 0, 0, 0], vec![0, 0, 0, 0, 0, 0]), vec![0, 0, 0, 0, 0, 0]);\n    assert_eq!(candidate(vec![1, 2, 3], vec![-1, -2, -3]), vec![2, 4, 6]);\n    assert_eq!(candidate(vec![1, 2, 3, 5], vec![-1, 2, 3, 4]), vec![2, 0, 0, 1]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_152_compare", "test": "}\n\nfn main() {\n    let candidate = compare;\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 1], vec![1, 2, 3, 4, 2, -2]), vec![0, 0, 0, 0, 3, 3]);\n    assert_eq!(candidate(vec![0, 0, 0, 0, 0, 0], vec![0, 0, 0, 0, 0, 0]), vec![0, 0, 0, 0, 0, 0]);\n    assert_eq!(candidate(vec![1, 2, 3], vec![-1, -2, -3]), vec![2, 4, 6]);\n    assert_eq!(candidate(vec![1, 2, 3, 5], vec![-1, 2, 3, 4]), vec![2, 0, 0, 1]);\n}\n"}
{"name": "HumanEval_83_starts_one_ends", "language": "rs", "prompt": "/// Given a positive integer n, return the count of the numbers of n-digit\n/// positive integers that start or end with 1.\nfn starts_one_ends(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = starts_one_ends;\n    assert_eq!(candidate(1), 1);\n    assert_eq!(candidate(2), 18);\n    assert_eq!(candidate(3), 180);\n    assert_eq!(candidate(4), 1800);\n    assert_eq!(candidate(5), 18000);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_83_starts_one_ends", "test": "}\n\nfn main() {\n    let candidate = starts_one_ends;\n    assert_eq!(candidate(1), 1);\n    assert_eq!(candidate(2), 18);\n    assert_eq!(candidate(3), 180);\n    assert_eq!(candidate(4), 1800);\n    assert_eq!(candidate(5), 18000);\n}\n"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "rs", "prompt": "/// Create a function that returns true if the last character\n/// of a given string is an alphabetical character and is not\n/// a part of a word, and false otherwise.\n/// Note: \"word\" is a group of characters separated by space.\n/// Examples:\n/// >>> check_if_last_char_is_a_letter(String::from(\"apple pie\"))\n/// false\n/// >>> check_if_last_char_is_a_letter(String::from(\"apple pi e\"))\n/// true\n/// >>> check_if_last_char_is_a_letter(String::from(\"apple pi e \"))\n/// false\n/// >>> check_if_last_char_is_a_letter(String::from(\"\"))\n/// false\nfn check_if_last_char_is_a_letter(txt: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = check_if_last_char_is_a_letter;\n    assert_eq!(candidate(String::from(\"apple\")), false);\n    assert_eq!(candidate(String::from(\"apple pi e\")), true);\n    assert_eq!(candidate(String::from(\"eeeee\")), false);\n    assert_eq!(candidate(String::from(\"A\")), true);\n    assert_eq!(candidate(String::from(\"Pumpkin pie \")), false);\n    assert_eq!(candidate(String::from(\"Pumpkin pie 1\")), false);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"eeeee e \")), false);\n    assert_eq!(candidate(String::from(\"apple pie\")), false);\n    assert_eq!(candidate(String::from(\"apple pi e \")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "}\n\nfn main() {\n    let candidate = check_if_last_char_is_a_letter;\n    assert_eq!(candidate(String::from(\"apple\")), false);\n    assert_eq!(candidate(String::from(\"apple pi e\")), true);\n    assert_eq!(candidate(String::from(\"eeeee\")), false);\n    assert_eq!(candidate(String::from(\"A\")), true);\n    assert_eq!(candidate(String::from(\"Pumpkin pie \")), false);\n    assert_eq!(candidate(String::from(\"Pumpkin pie 1\")), false);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"eeeee e \")), false);\n    assert_eq!(candidate(String::from(\"apple pie\")), false);\n    assert_eq!(candidate(String::from(\"apple pi e \")), false);\n}\n"}
{"name": "HumanEval_124_valid_date", "language": "rs", "prompt": "/// You have to write a function which validates a given date string and\n/// returns true if the date is valid otherwise false.\n/// The date is valid if all of the following rules are satisfied:\n/// 1. The date string is not empty.\n/// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n/// 3. The months should not be less than 1 or higher than 12.\n/// 4. The date should be in the format: mm-dd-yyyy\n/// >>> valid_date(String::from(\"03-11-2000\"))\n/// true\n/// >>> valid_date(String::from(\"15-01-2012\"))\n/// false\n/// >>> valid_date(String::from(\"04-0-2040\"))\n/// false\n/// >>> valid_date(String::from(\"06-04-2020\"))\n/// true\n/// >>> valid_date(String::from(\"06/04/2020\"))\n/// false\nfn valid_date(date: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = valid_date;\n    assert_eq!(candidate(String::from(\"03-11-2000\")), true);\n    assert_eq!(candidate(String::from(\"15-01-2012\")), false);\n    assert_eq!(candidate(String::from(\"04-0-2040\")), false);\n    assert_eq!(candidate(String::from(\"06-04-2020\")), true);\n    assert_eq!(candidate(String::from(\"01-01-2007\")), true);\n    assert_eq!(candidate(String::from(\"03-32-2011\")), false);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"04-31-3000\")), false);\n    assert_eq!(candidate(String::from(\"06-06-2005\")), true);\n    assert_eq!(candidate(String::from(\"21-31-2000\")), false);\n    assert_eq!(candidate(String::from(\"04-12-2003\")), true);\n    assert_eq!(candidate(String::from(\"04122003\")), false);\n    assert_eq!(candidate(String::from(\"20030412\")), false);\n    assert_eq!(candidate(String::from(\"2003-04\")), false);\n    assert_eq!(candidate(String::from(\"2003-04-12\")), false);\n    assert_eq!(candidate(String::from(\"04-2003\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_124_valid_date", "test": "}\n\nfn main() {\n    let candidate = valid_date;\n    assert_eq!(candidate(String::from(\"03-11-2000\")), true);\n    assert_eq!(candidate(String::from(\"15-01-2012\")), false);\n    assert_eq!(candidate(String::from(\"04-0-2040\")), false);\n    assert_eq!(candidate(String::from(\"06-04-2020\")), true);\n    assert_eq!(candidate(String::from(\"01-01-2007\")), true);\n    assert_eq!(candidate(String::from(\"03-32-2011\")), false);\n    assert_eq!(candidate(String::from(\"\")), false);\n    assert_eq!(candidate(String::from(\"04-31-3000\")), false);\n    assert_eq!(candidate(String::from(\"06-06-2005\")), true);\n    assert_eq!(candidate(String::from(\"21-31-2000\")), false);\n    assert_eq!(candidate(String::from(\"04-12-2003\")), true);\n    assert_eq!(candidate(String::from(\"04122003\")), false);\n    assert_eq!(candidate(String::from(\"20030412\")), false);\n    assert_eq!(candidate(String::from(\"2003-04\")), false);\n    assert_eq!(candidate(String::from(\"2003-04-12\")), false);\n    assert_eq!(candidate(String::from(\"04-2003\")), false);\n}\n"}
{"name": "HumanEval_108_count_nums", "language": "rs", "prompt": "/// Write a function count_nums which takes a vector of integers and returns\n/// the number of elements which has a sum of digits > 0.\n/// If a number is negative, then its first signed digit will be negative:\n/// e.g. -123 has signed digits -1, 2, and 3.\n/// >>> count_nums(vec![])\n/// 0\n/// >>> count_nums(vec![-1, 11, -11])\n/// 1\n/// >>> count_nums(vec![1, 1, 2])\n/// 3\nfn count_nums(arr: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = count_nums;\n    assert_eq!(candidate(Vec::<isize>::new()), 0);\n    assert_eq!(candidate(vec![-1, -2, 0]), 0);\n    assert_eq!(candidate(vec![1, 1, 2, -2, 3, 4, 5]), 6);\n    assert_eq!(candidate(vec![1, 6, 9, -6, 0, 1, 5]), 5);\n    assert_eq!(candidate(vec![1, 100, 98, -7, 1, -1]), 4);\n    assert_eq!(candidate(vec![12, 23, 34, -45, -56, 0]), 5);\n    assert_eq!(candidate(vec![0, 1]), 1);\n    assert_eq!(candidate(vec![1]), 1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_108_count_nums", "test": "}\n\nfn main() {\n    let candidate = count_nums;\n    assert_eq!(candidate(Vec::<isize>::new()), 0);\n    assert_eq!(candidate(vec![-1, -2, 0]), 0);\n    assert_eq!(candidate(vec![1, 1, 2, -2, 3, 4, 5]), 6);\n    assert_eq!(candidate(vec![1, 6, 9, -6, 0, 1, 5]), 5);\n    assert_eq!(candidate(vec![1, 100, 98, -7, 1, -1]), 4);\n    assert_eq!(candidate(vec![12, 23, 34, -45, -56, 0]), 5);\n    assert_eq!(candidate(vec![0, 1]), 1);\n    assert_eq!(candidate(vec![1]), 1);\n}\n"}
{"name": "HumanEval_86_anti_shuffle", "language": "rs", "prompt": "/// Write a function that takes a string and returns an ordered version of it.\n/// Ordered version of string, is a string where all words (separated by space)\n/// are replaced by a new word where all the characters arranged in\n/// ascending order based on ascii value.\n/// Note: You should keep the order of words and blank spaces in the sentence.\n/// For example:\n/// >>> anti_shuffle(String::from(\"Hi\"))\n/// String::from(\"Hi\")\n/// >>> anti_shuffle(String::from(\"hello\"))\n/// String::from(\"ehllo\")\n/// >>> anti_shuffle(String::from(\"Hello World!!!\"))\n/// String::from(\"Hello !!!Wdlor\")\nfn anti_shuffle(s: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = anti_shuffle;\n    assert_eq!(candidate(String::from(\"Hi\")), String::from(\"Hi\"));\n    assert_eq!(candidate(String::from(\"hello\")), String::from(\"ehllo\"));\n    assert_eq!(candidate(String::from(\"number\")), String::from(\"bemnru\"));\n    assert_eq!(candidate(String::from(\"abcd\")), String::from(\"abcd\"));\n    assert_eq!(candidate(String::from(\"Hello World!!!\")), String::from(\"Hello !!!Wdlor\"));\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"Hi. My name is Mister Robot. How are you?\")), String::from(\".Hi My aemn is Meirst .Rboot How aer ?ouy\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_86_anti_shuffle", "test": "}\n\nfn main() {\n    let candidate = anti_shuffle;\n    assert_eq!(candidate(String::from(\"Hi\")), String::from(\"Hi\"));\n    assert_eq!(candidate(String::from(\"hello\")), String::from(\"ehllo\"));\n    assert_eq!(candidate(String::from(\"number\")), String::from(\"bemnru\"));\n    assert_eq!(candidate(String::from(\"abcd\")), String::from(\"abcd\"));\n    assert_eq!(candidate(String::from(\"Hello World!!!\")), String::from(\"Hello !!!Wdlor\"));\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"Hi. My name is Mister Robot. How are you?\")), String::from(\".Hi My aemn is Meirst .Rboot How aer ?ouy\"));\n}\n"}
{"name": "HumanEval_48_is_palindrome", "language": "rs", "prompt": "/// Checks if given string is a palindrome\n/// >>> is_palindrome(String::from(\"\"))\n/// true\n/// >>> is_palindrome(String::from(\"aba\"))\n/// true\n/// >>> is_palindrome(String::from(\"aaaaa\"))\n/// true\n/// >>> is_palindrome(String::from(\"zbcd\"))\n/// false\nfn is_palindrome(text: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_palindrome;\n    assert_eq!(candidate(String::from(\"\")), true);\n    assert_eq!(candidate(String::from(\"aba\")), true);\n    assert_eq!(candidate(String::from(\"aaaaa\")), true);\n    assert_eq!(candidate(String::from(\"zbcd\")), false);\n    assert_eq!(candidate(String::from(\"xywyx\")), true);\n    assert_eq!(candidate(String::from(\"xywyz\")), false);\n    assert_eq!(candidate(String::from(\"xywzx\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_48_is_palindrome", "test": "}\n\nfn main() {\n    let candidate = is_palindrome;\n    assert_eq!(candidate(String::from(\"\")), true);\n    assert_eq!(candidate(String::from(\"aba\")), true);\n    assert_eq!(candidate(String::from(\"aaaaa\")), true);\n    assert_eq!(candidate(String::from(\"zbcd\")), false);\n    assert_eq!(candidate(String::from(\"xywyx\")), true);\n    assert_eq!(candidate(String::from(\"xywyz\")), false);\n    assert_eq!(candidate(String::from(\"xywzx\")), false);\n}\n"}
{"name": "HumanEval_118_get_closest_vowel", "language": "rs", "prompt": "/// You are given a word. Your task is to find the closest vowel that stands between \n/// two consonants from the right side of the word (case sensitive).\n/// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n/// find any vowel met the above condition. \n/// You may assume that the given string contains English letter only.\n/// Example:\n/// >>> get_closest_vowel(String::from(\"yogurt\"))\n/// String::from(\"u\")\n/// >>> get_closest_vowel(String::from(\"FULL\"))\n/// String::from(\"U\")\n/// >>> get_closest_vowel(String::from(\"quick\"))\n/// String::from(\"\")\n/// >>> get_closest_vowel(String::from(\"ab\"))\n/// String::from(\"\")\nfn get_closest_vowel(word: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = get_closest_vowel;\n    assert_eq!(candidate(String::from(\"yogurt\")), String::from(\"u\"));\n    assert_eq!(candidate(String::from(\"full\")), String::from(\"u\"));\n    assert_eq!(candidate(String::from(\"easy\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"eAsy\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"ali\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"bad\")), String::from(\"a\"));\n    assert_eq!(candidate(String::from(\"most\")), String::from(\"o\"));\n    assert_eq!(candidate(String::from(\"ab\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"ba\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"quick\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"anime\")), String::from(\"i\"));\n    assert_eq!(candidate(String::from(\"Asia\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"Above\")), String::from(\"o\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_118_get_closest_vowel", "test": "}\n\nfn main() {\n    let candidate = get_closest_vowel;\n    assert_eq!(candidate(String::from(\"yogurt\")), String::from(\"u\"));\n    assert_eq!(candidate(String::from(\"full\")), String::from(\"u\"));\n    assert_eq!(candidate(String::from(\"easy\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"eAsy\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"ali\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"bad\")), String::from(\"a\"));\n    assert_eq!(candidate(String::from(\"most\")), String::from(\"o\"));\n    assert_eq!(candidate(String::from(\"ab\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"ba\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"quick\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"anime\")), String::from(\"i\"));\n    assert_eq!(candidate(String::from(\"Asia\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"Above\")), String::from(\"o\"));\n}\n"}
{"name": "HumanEval_31_is_prime", "language": "rs", "prompt": "/// Return true if a given number is prime, and false otherwise.\n/// >>> is_prime(6)\n/// false\n/// >>> is_prime(101)\n/// true\n/// >>> is_prime(11)\n/// true\n/// >>> is_prime(13441)\n/// true\n/// >>> is_prime(61)\n/// true\n/// >>> is_prime(4)\n/// false\n/// >>> is_prime(1)\n/// false\nfn is_prime(n: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_prime;\n    assert_eq!(candidate(6), false);\n    assert_eq!(candidate(101), true);\n    assert_eq!(candidate(11), true);\n    assert_eq!(candidate(13441), true);\n    assert_eq!(candidate(61), true);\n    assert_eq!(candidate(4), false);\n    assert_eq!(candidate(1), false);\n    assert_eq!(candidate(5), true);\n    assert_eq!(candidate(11), true);\n    assert_eq!(candidate(17), true);\n    assert_eq!(candidate(85), false);\n    assert_eq!(candidate(77), false);\n    assert_eq!(candidate(255379), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_31_is_prime", "test": "}\n\nfn main() {\n    let candidate = is_prime;\n    assert_eq!(candidate(6), false);\n    assert_eq!(candidate(101), true);\n    assert_eq!(candidate(11), true);\n    assert_eq!(candidate(13441), true);\n    assert_eq!(candidate(61), true);\n    assert_eq!(candidate(4), false);\n    assert_eq!(candidate(1), false);\n    assert_eq!(candidate(5), true);\n    assert_eq!(candidate(11), true);\n    assert_eq!(candidate(17), true);\n    assert_eq!(candidate(85), false);\n    assert_eq!(candidate(77), false);\n    assert_eq!(candidate(255379), false);\n}\n"}
{"name": "HumanEval_144_simplify", "language": "rs", "prompt": "/// Your task is to implement a function that will simplify the expression\n/// x * n. The function returns true if x * n evaluates to a whole number and false\n/// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n/// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n/// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n/// >>> simplify(String::from(\"1/5\"), String::from(\"5/1\"))\n/// true\n/// >>> simplify(String::from(\"1/6\"), String::from(\"2/1\"))\n/// false\n/// >>> simplify(String::from(\"7/10\"), String::from(\"10/2\"))\n/// false\nfn simplify(x: String, n: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = simplify;\n    assert_eq!(candidate(String::from(\"1/5\"), String::from(\"5/1\")), true);\n    assert_eq!(candidate(String::from(\"1/6\"), String::from(\"2/1\")), false);\n    assert_eq!(candidate(String::from(\"5/1\"), String::from(\"3/1\")), true);\n    assert_eq!(candidate(String::from(\"7/10\"), String::from(\"10/2\")), false);\n    assert_eq!(candidate(String::from(\"2/10\"), String::from(\"50/10\")), true);\n    assert_eq!(candidate(String::from(\"7/2\"), String::from(\"4/2\")), true);\n    assert_eq!(candidate(String::from(\"11/6\"), String::from(\"6/1\")), true);\n    assert_eq!(candidate(String::from(\"2/3\"), String::from(\"5/2\")), false);\n    assert_eq!(candidate(String::from(\"5/2\"), String::from(\"3/5\")), false);\n    assert_eq!(candidate(String::from(\"2/4\"), String::from(\"8/4\")), true);\n    assert_eq!(candidate(String::from(\"2/4\"), String::from(\"4/2\")), true);\n    assert_eq!(candidate(String::from(\"1/5\"), String::from(\"5/1\")), true);\n    assert_eq!(candidate(String::from(\"1/5\"), String::from(\"1/5\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_144_simplify", "test": "}\n\nfn main() {\n    let candidate = simplify;\n    assert_eq!(candidate(String::from(\"1/5\"), String::from(\"5/1\")), true);\n    assert_eq!(candidate(String::from(\"1/6\"), String::from(\"2/1\")), false);\n    assert_eq!(candidate(String::from(\"5/1\"), String::from(\"3/1\")), true);\n    assert_eq!(candidate(String::from(\"7/10\"), String::from(\"10/2\")), false);\n    assert_eq!(candidate(String::from(\"2/10\"), String::from(\"50/10\")), true);\n    assert_eq!(candidate(String::from(\"7/2\"), String::from(\"4/2\")), true);\n    assert_eq!(candidate(String::from(\"11/6\"), String::from(\"6/1\")), true);\n    assert_eq!(candidate(String::from(\"2/3\"), String::from(\"5/2\")), false);\n    assert_eq!(candidate(String::from(\"5/2\"), String::from(\"3/5\")), false);\n    assert_eq!(candidate(String::from(\"2/4\"), String::from(\"8/4\")), true);\n    assert_eq!(candidate(String::from(\"2/4\"), String::from(\"4/2\")), true);\n    assert_eq!(candidate(String::from(\"1/5\"), String::from(\"5/1\")), true);\n    assert_eq!(candidate(String::from(\"1/5\"), String::from(\"1/5\")), false);\n}\n"}
{"name": "HumanEval_78_hex_key", "language": "rs", "prompt": "/// You have been tasked to write a function that receives \n/// a hexadecimal number as a string and counts the number of hexadecimal \n/// digits that are primes (prime number, or a prime, is a natural number \n/// greater than 1 that is not a product of two smaller natural numbers).\n/// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n/// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n/// So you have to determine a number of the following digits: 2, 3, 5, 7, \n/// B (=decimal 11), D (=decimal 13).\n/// Note: you may assume the input is always correct or empty string, \n/// and symbols A,B,C,D,E,F are always uppercase.\n/// Examples:\n/// >>> hex_key(String::from(\"AB\"))\n/// 1\n/// >>> hex_key(String::from(\"1077E\"))\n/// 2\n/// >>> hex_key(String::from(\"ABED1A33\"))\n/// 4\n/// >>> hex_key(String::from(\"123456789ABCDEF0\"))\n/// 6\n/// >>> hex_key(String::from(\"2020\"))\n/// 2\nfn hex_key(num: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = hex_key;\n    assert_eq!(candidate(String::from(\"AB\")), 1);\n    assert_eq!(candidate(String::from(\"1077E\")), 2);\n    assert_eq!(candidate(String::from(\"ABED1A33\")), 4);\n    assert_eq!(candidate(String::from(\"2020\")), 2);\n    assert_eq!(candidate(String::from(\"123456789ABCDEF0\")), 6);\n    assert_eq!(candidate(String::from(\"112233445566778899AABBCCDDEEFF00\")), 12);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_78_hex_key", "test": "}\n\nfn main() {\n    let candidate = hex_key;\n    assert_eq!(candidate(String::from(\"AB\")), 1);\n    assert_eq!(candidate(String::from(\"1077E\")), 2);\n    assert_eq!(candidate(String::from(\"ABED1A33\")), 4);\n    assert_eq!(candidate(String::from(\"2020\")), 2);\n    assert_eq!(candidate(String::from(\"123456789ABCDEF0\")), 6);\n    assert_eq!(candidate(String::from(\"112233445566778899AABBCCDDEEFF00\")), 12);\n}\n"}
{"name": "HumanEval_143_words_in_sentence", "language": "rs", "prompt": "/// You are given a string representing a sentence,\n/// the sentence contains some words separated by a space,\n/// and you have to return a string that contains the words from the original sentence,\n/// whose lengths are prime numbers,\n/// the order of the words in the new string should be the same as the original one.\n/// Example 1:\n/// >>> words_in_sentence(String::from(\"This is a test\"))\n/// String::from(\"is\")\n/// Example 2:\n/// >>> words_in_sentence(String::from(\"lets go for swimming\"))\n/// String::from(\"go for\")\n/// Constraints:\n/// * 1 <= len(sentence) <= 100\n/// * sentence contains only letters\nfn words_in_sentence(sentence: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = words_in_sentence;\n    assert_eq!(candidate(String::from(\"This is a test\")), String::from(\"is\"));\n    assert_eq!(candidate(String::from(\"lets go for swimming\")), String::from(\"go for\"));\n    assert_eq!(candidate(String::from(\"there is no place available here\")), String::from(\"there is no place\"));\n    assert_eq!(candidate(String::from(\"Hi I am Hussein\")), String::from(\"Hi am Hussein\"));\n    assert_eq!(candidate(String::from(\"go for it\")), String::from(\"go for it\"));\n    assert_eq!(candidate(String::from(\"here\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"here is\")), String::from(\"is\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_143_words_in_sentence", "test": "}\n\nfn main() {\n    let candidate = words_in_sentence;\n    assert_eq!(candidate(String::from(\"This is a test\")), String::from(\"is\"));\n    assert_eq!(candidate(String::from(\"lets go for swimming\")), String::from(\"go for\"));\n    assert_eq!(candidate(String::from(\"there is no place available here\")), String::from(\"there is no place\"));\n    assert_eq!(candidate(String::from(\"Hi I am Hussein\")), String::from(\"Hi am Hussein\"));\n    assert_eq!(candidate(String::from(\"go for it\")), String::from(\"go for it\"));\n    assert_eq!(candidate(String::from(\"here\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"here is\")), String::from(\"is\"));\n}\n"}
{"name": "HumanEval_111_histogram", "language": "rs", "prompt": "use std::collections::HashMap;\n\n/// Given a string representing a space separated lowercase letters, return a HashMap\n/// of the letter with the most repetition and containing the corresponding count.\n/// If several letters have the same occurrence, return all of them.\n/// Example:\n/// >>> histogram(String::from(\"a b c\"))\n/// HashMap::from([(String::from(\"a\"), 1), (String::from(\"b\"), 1), (String::from(\"c\"), 1)])\n/// >>> histogram(String::from(\"a b b a\"))\n/// HashMap::from([(String::from(\"a\"), 2), (String::from(\"b\"), 2)])\n/// >>> histogram(String::from(\"a b c a b\"))\n/// HashMap::from([(String::from(\"a\"), 2), (String::from(\"b\"), 2)])\n/// >>> histogram(String::from(\"b b b b a\"))\n/// HashMap::from([(String::from(\"b\"), 4)])\n/// >>> histogram(String::from(\"\"))\n/// HashMap::from([])\nfn histogram(test: String) -> HashMap<String, isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = histogram;\n    assert_eq!(candidate(String::from(\"a b b a\")), HashMap::from([(String::from(\"a\"), 2), (String::from(\"b\"), 2)]));\n    assert_eq!(candidate(String::from(\"a b c a b\")), HashMap::from([(String::from(\"a\"), 2), (String::from(\"b\"), 2)]));\n    assert_eq!(candidate(String::from(\"a b c d g\")), HashMap::from([(String::from(\"a\"), 1), (String::from(\"b\"), 1), (String::from(\"c\"), 1), (String::from(\"d\"), 1), (String::from(\"g\"), 1)]));\n    assert_eq!(candidate(String::from(\"r t g\")), HashMap::from([(String::from(\"r\"), 1), (String::from(\"t\"), 1), (String::from(\"g\"), 1)]));\n    assert_eq!(candidate(String::from(\"b b b b a\")), HashMap::from([(String::from(\"b\"), 4)]));\n    assert_eq!(candidate(String::from(\"r t g\")), HashMap::from([(String::from(\"r\"), 1), (String::from(\"t\"), 1), (String::from(\"g\"), 1)]));\n    assert_eq!(candidate(String::from(\"\")), HashMap::from([]));\n    assert_eq!(candidate(String::from(\"a\")), HashMap::from([(String::from(\"a\"), 1)]));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_111_histogram", "test": "}\n\nfn main() {\n    let candidate = histogram;\n    assert_eq!(candidate(String::from(\"a b b a\")), HashMap::from([(String::from(\"a\"), 2), (String::from(\"b\"), 2)]));\n    assert_eq!(candidate(String::from(\"a b c a b\")), HashMap::from([(String::from(\"a\"), 2), (String::from(\"b\"), 2)]));\n    assert_eq!(candidate(String::from(\"a b c d g\")), HashMap::from([(String::from(\"a\"), 1), (String::from(\"b\"), 1), (String::from(\"c\"), 1), (String::from(\"d\"), 1), (String::from(\"g\"), 1)]));\n    assert_eq!(candidate(String::from(\"r t g\")), HashMap::from([(String::from(\"r\"), 1), (String::from(\"t\"), 1), (String::from(\"g\"), 1)]));\n    assert_eq!(candidate(String::from(\"b b b b a\")), HashMap::from([(String::from(\"b\"), 4)]));\n    assert_eq!(candidate(String::from(\"r t g\")), HashMap::from([(String::from(\"r\"), 1), (String::from(\"t\"), 1), (String::from(\"g\"), 1)]));\n    assert_eq!(candidate(String::from(\"\")), HashMap::from([]));\n    assert_eq!(candidate(String::from(\"a\")), HashMap::from([(String::from(\"a\"), 1)]));\n}\n"}
{"name": "HumanEval_87_get_row", "language": "rs", "prompt": "/// You are given a 2 dimensional data, as a nested vectors,\n/// which is similar to matrix, however, unlike matrices,\n/// each row may contain a different number of columns.\n/// Given lst, and integer x, find integers x in the vector,\n/// and return vector of tuples, [(x1, y1), (x2, y2) ...] such that\n/// each tuple is a coordinate - (row, columns), starting with 0.\n/// Sort coordinates initially by rows in ascending order.\n/// Also, sort coordinates of the row by columns in descending order.\n/// Examples:\n/// >>> get_row(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 1, 6], vec![1, 2, 3, 4, 5, 1]], 1)\n/// vec![(0, 0), (1, 4), (1, 0), (2, 5), (2, 0)]\n/// >>> get_row(vec![], 1)\n/// Vec::<(isize, isize)>::new()\n/// >>> get_row(vec![vec![], vec![1], vec![1, 2, 3]], 3)\n/// vec![(2, 2)]\nfn get_row(lst: Vec<Vec<isize>>, x: isize) -> Vec<(isize, isize)> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = get_row;\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 1, 6], vec![1, 2, 3, 4, 5, 1]], 1), vec![(0, 0), (1, 4), (1, 0), (2, 5), (2, 0)]);\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6]], 2), vec![(0, 1), (1, 1), (2, 1), (3, 1), (4, 1), (5, 1)]);\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 1, 3, 4, 5, 6], vec![1, 2, 1, 4, 5, 6], vec![1, 2, 3, 1, 5, 6], vec![1, 2, 3, 4, 1, 6], vec![1, 2, 3, 4, 5, 1]], 1), vec![(0, 0), (1, 0), (2, 1), (2, 0), (3, 2), (3, 0), (4, 3), (4, 0), (5, 4), (5, 0), (6, 5), (6, 0)]);\n    assert_eq!(candidate(Vec::<Vec<isize>>::new(), 1), Vec::<(isize, isize)>::new());\n    assert_eq!(candidate(vec![vec![1]], 2), Vec::<(isize, isize)>::new());\n    assert_eq!(candidate(vec![vec![], vec![1], vec![1, 2, 3]], 3), vec![(2, 2)]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_87_get_row", "test": "}\n\nfn main() {\n    let candidate = get_row;\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 1, 6], vec![1, 2, 3, 4, 5, 1]], 1), vec![(0, 0), (1, 4), (1, 0), (2, 5), (2, 0)]);\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6]], 2), vec![(0, 1), (1, 1), (2, 1), (3, 1), (4, 1), (5, 1)]);\n    assert_eq!(candidate(vec![vec![1, 2, 3, 4, 5, 6], vec![1, 2, 3, 4, 5, 6], vec![1, 1, 3, 4, 5, 6], vec![1, 2, 1, 4, 5, 6], vec![1, 2, 3, 1, 5, 6], vec![1, 2, 3, 4, 1, 6], vec![1, 2, 3, 4, 5, 1]], 1), vec![(0, 0), (1, 0), (2, 1), (2, 0), (3, 2), (3, 0), (4, 3), (4, 0), (5, 4), (5, 0), (6, 5), (6, 0)]);\n    assert_eq!(candidate(Vec::<Vec<isize>>::new(), 1), Vec::<(isize, isize)>::new());\n    assert_eq!(candidate(vec![vec![1]], 2), Vec::<(isize, isize)>::new());\n    assert_eq!(candidate(vec![vec![], vec![1], vec![1, 2, 3]], 3), vec![(2, 2)]);\n}\n"}
{"name": "HumanEval_123_get_odd_collatz", "language": "rs", "prompt": "/// Given a positive integer n, return a sorted vector that has the odd numbers in collatz sequence.\n/// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n/// as follows: start with any positive integer n. Then each term is obtained from the \n/// previous term as follows: if the previous term is even, the next term is one half of \n/// the previous term. If the previous term is odd, the next term is 3 times the previous\n/// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n/// Note: \n/// 1. Collatz(1) is [1].\n/// 2. returned vector sorted in increasing order.\n/// For example:\n/// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n/// >>> get_odd_collatz(5)\n/// vec![1, 5]\nfn get_odd_collatz(n: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = get_odd_collatz;\n    assert_eq!(candidate(14), vec![1, 5, 7, 11, 13, 17]);\n    assert_eq!(candidate(5), vec![1, 5]);\n    assert_eq!(candidate(12), vec![1, 3, 5]);\n    assert_eq!(candidate(1), vec![1]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_123_get_odd_collatz", "test": "}\n\nfn main() {\n    let candidate = get_odd_collatz;\n    assert_eq!(candidate(14), vec![1, 5, 7, 11, 13, 17]);\n    assert_eq!(candidate(5), vec![1, 5]);\n    assert_eq!(candidate(12), vec![1, 3, 5]);\n    assert_eq!(candidate(1), vec![1]);\n}\n"}
{"name": "HumanEval_135_can_arrange", "language": "rs", "prompt": "/// Create a function which returns the largest index of an element which\n/// is not greater than or equal to the element immediately preceding it. If\n/// no such element exists then return -1. The given vector will not contain\n/// duplicate values.\n/// Examples:\n/// >>> can_arrange(vec![1, 2, 4, 3, 5])\n/// 3\n/// >>> can_arrange(vec![1, 2, 3])\n/// -1\nfn can_arrange(arr: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = can_arrange;\n    assert_eq!(candidate(vec![1, 2, 4, 3, 5]), 3);\n    assert_eq!(candidate(vec![1, 2, 4, 5]), -1);\n    assert_eq!(candidate(vec![1, 4, 2, 5, 6, 7, 8, 9, 10]), 2);\n    assert_eq!(candidate(vec![4, 8, 5, 7, 3]), 4);\n    assert_eq!(candidate(Vec::<isize>::new()), -1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_135_can_arrange", "test": "}\n\nfn main() {\n    let candidate = can_arrange;\n    assert_eq!(candidate(vec![1, 2, 4, 3, 5]), 3);\n    assert_eq!(candidate(vec![1, 2, 4, 5]), -1);\n    assert_eq!(candidate(vec![1, 4, 2, 5, 6, 7, 8, 9, 10]), 2);\n    assert_eq!(candidate(vec![4, 8, 5, 7, 3]), 4);\n    assert_eq!(candidate(Vec::<isize>::new()), -1);\n}\n"}
{"name": "HumanEval_19_sort_numbers", "language": "rs", "prompt": "/// Input is a space-delimited string of numberals from 'zero' to 'nine'.\n/// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n/// Return the string with numbers sorted from smallest to largest\n/// >>> sort_numbers(String::from(\"three one five\"))\n/// String::from(\"one three five\")\nfn sort_numbers(numbers: String) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sort_numbers;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"three\")), String::from(\"three\"));\n    assert_eq!(candidate(String::from(\"three five nine\")), String::from(\"three five nine\"));\n    assert_eq!(candidate(String::from(\"five zero four seven nine eight\")), String::from(\"zero four five seven eight nine\"));\n    assert_eq!(candidate(String::from(\"six five four three two one zero\")), String::from(\"zero one two three four five six\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_19_sort_numbers", "test": "}\n\nfn main() {\n    let candidate = sort_numbers;\n    assert_eq!(candidate(String::from(\"\")), String::from(\"\"));\n    assert_eq!(candidate(String::from(\"three\")), String::from(\"three\"));\n    assert_eq!(candidate(String::from(\"three five nine\")), String::from(\"three five nine\"));\n    assert_eq!(candidate(String::from(\"five zero four seven nine eight\")), String::from(\"zero four five seven eight nine\"));\n    assert_eq!(candidate(String::from(\"six five four three two one zero\")), String::from(\"zero one two three four five six\"));\n}\n"}
{"name": "HumanEval_65_circular_shift", "language": "rs", "prompt": "/// Circular shift the digits of the integer x, shift the digits right by shift\n/// and return the result as a string.\n/// If shift > number of digits, return digits reversed.\n/// >>> circular_shift(12, 1)\n/// String::from(\"21\")\n/// >>> circular_shift(12, 2)\n/// String::from(\"12\")\nfn circular_shift(x: isize, shift: isize) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = circular_shift;\n    assert_eq!(candidate(100, 2), String::from(\"001\"));\n    assert_eq!(candidate(12, 2), String::from(\"12\"));\n    assert_eq!(candidate(97, 8), String::from(\"79\"));\n    assert_eq!(candidate(12, 1), String::from(\"21\"));\n    assert_eq!(candidate(11, 101), String::from(\"11\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_65_circular_shift", "test": "}\n\nfn main() {\n    let candidate = circular_shift;\n    assert_eq!(candidate(100, 2), String::from(\"001\"));\n    assert_eq!(candidate(12, 2), String::from(\"12\"));\n    assert_eq!(candidate(97, 8), String::from(\"79\"));\n    assert_eq!(candidate(12, 1), String::from(\"21\"));\n    assert_eq!(candidate(11, 101), String::from(\"11\"));\n}\n"}
{"name": "HumanEval_142_sum_squares", "language": "rs", "prompt": "/// \"\n/// This function will take a vector of integers. For all entries in the vector, the function shall square the integer entry if its index is a \n/// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n/// change the entries in the vector whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n/// Examples:\n/// >>> lst\n/// vec![1, 2, 3]\n/// >>> lst\n/// vec![]\n/// >>> lst\n/// vec![-1, -5, 2, -1, -5]\nfn sum_squares(lst: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sum_squares;\n    assert_eq!(candidate(vec![1, 2, 3]), 6);\n    assert_eq!(candidate(vec![1, 4, 9]), 14);\n    assert_eq!(candidate(Vec::<isize>::new()), 0);\n    assert_eq!(candidate(vec![1, 1, 1, 1, 1, 1, 1, 1, 1]), 9);\n    assert_eq!(candidate(vec![-1, -1, -1, -1, -1, -1, -1, -1, -1]), -3);\n    assert_eq!(candidate(vec![0]), 0);\n    assert_eq!(candidate(vec![-1, -5, 2, -1, -5]), -126);\n    assert_eq!(candidate(vec![-56, -99, 1, 0, -2]), 3030);\n    assert_eq!(candidate(vec![-1, 0, 0, 0, 0, 0, 0, 0, -1]), 0);\n    assert_eq!(candidate(vec![-16, -9, -2, 36, 36, 26, -20, 25, -40, 20, -4, 12, -26, 35, 37]), -14196);\n    assert_eq!(candidate(vec![-1, -3, 17, -1, -15, 13, -1, 14, -14, -12, -5, 14, -14, 6, 13, 11, 16, 16, 4, 10]), -1448);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_142_sum_squares", "test": "}\n\nfn main() {\n    let candidate = sum_squares;\n    assert_eq!(candidate(vec![1, 2, 3]), 6);\n    assert_eq!(candidate(vec![1, 4, 9]), 14);\n    assert_eq!(candidate(Vec::<isize>::new()), 0);\n    assert_eq!(candidate(vec![1, 1, 1, 1, 1, 1, 1, 1, 1]), 9);\n    assert_eq!(candidate(vec![-1, -1, -1, -1, -1, -1, -1, -1, -1]), -3);\n    assert_eq!(candidate(vec![0]), 0);\n    assert_eq!(candidate(vec![-1, -5, 2, -1, -5]), -126);\n    assert_eq!(candidate(vec![-56, -99, 1, 0, -2]), 3030);\n    assert_eq!(candidate(vec![-1, 0, 0, 0, 0, 0, 0, 0, -1]), 0);\n    assert_eq!(candidate(vec![-16, -9, -2, 36, 36, 26, -20, 25, -40, 20, -4, 12, -26, 35, 37]), -14196);\n    assert_eq!(candidate(vec![-1, -3, 17, -1, -15, 13, -1, 14, -14, -12, -5, 14, -14, 6, 13, 11, 16, 16, 4, 10]), -1448);\n}\n"}
{"name": "HumanEval_94_skjkasdkd", "language": "rs", "prompt": "/// You are given a vector of integers.\n/// You need to find the largest prime value and return the sum of its digits.\n/// Examples:\n/// >>> skjkasdkd(vec![0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n/// 10\n/// >>> skjkasdkd(vec![1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n/// 25\n/// >>> skjkasdkd(vec![1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n/// 13\n/// >>> skjkasdkd(vec![0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n/// 11\n/// >>> skjkasdkd(vec![0, 81, 12, 3, 1, 21])\n/// 3\n/// >>> skjkasdkd(vec![0, 8, 1, 2, 1, 7])\n/// 7\nfn skjkasdkd(lst: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = skjkasdkd;\n    assert_eq!(candidate(vec![0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3]), 10);\n    assert_eq!(candidate(vec![1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1]), 25);\n    assert_eq!(candidate(vec![1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3]), 13);\n    assert_eq!(candidate(vec![0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6]), 11);\n    assert_eq!(candidate(vec![0, 81, 12, 3, 1, 21]), 3);\n    assert_eq!(candidate(vec![0, 8, 1, 2, 1, 7]), 7);\n    assert_eq!(candidate(vec![8191]), 19);\n    assert_eq!(candidate(vec![8191, 123456, 127, 7]), 19);\n    assert_eq!(candidate(vec![127, 97, 8192]), 10);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_94_skjkasdkd", "test": "}\n\nfn main() {\n    let candidate = skjkasdkd;\n    assert_eq!(candidate(vec![0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3]), 10);\n    assert_eq!(candidate(vec![1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1]), 25);\n    assert_eq!(candidate(vec![1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3]), 13);\n    assert_eq!(candidate(vec![0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6]), 11);\n    assert_eq!(candidate(vec![0, 81, 12, 3, 1, 21]), 3);\n    assert_eq!(candidate(vec![0, 8, 1, 2, 1, 7]), 7);\n    assert_eq!(candidate(vec![8191]), 19);\n    assert_eq!(candidate(vec![8191, 123456, 127, 7]), 19);\n    assert_eq!(candidate(vec![127, 97, 8192]), 10);\n}\n"}
{"name": "HumanEval_8_sum_product", "language": "rs", "prompt": "/// For a given vector of integers, return a tuple consisting of a sum and a product of all the integers in a vector.\n/// Empty sum should be equal to 0 and empty product should be equal to 1.\n/// >>> sum_product(vec![])\n/// (0, 1)\n/// >>> sum_product(vec![1, 2, 3, 4])\n/// (10, 24)\nfn sum_product(numbers: Vec<isize>) -> (isize, isize) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sum_product;\n    assert_eq!(candidate(Vec::<isize>::new()), (0, 1));\n    assert_eq!(candidate(vec![1, 1, 1]), (3, 1));\n    assert_eq!(candidate(vec![100, 0]), (100, 0));\n    assert_eq!(candidate(vec![3, 5, 7]), (15, 105));\n    assert_eq!(candidate(vec![10]), (10, 10));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_8_sum_product", "test": "}\n\nfn main() {\n    let candidate = sum_product;\n    assert_eq!(candidate(Vec::<isize>::new()), (0, 1));\n    assert_eq!(candidate(vec![1, 1, 1]), (3, 1));\n    assert_eq!(candidate(vec![100, 0]), (100, 0));\n    assert_eq!(candidate(vec![3, 5, 7]), (15, 105));\n    assert_eq!(candidate(vec![10]), (10, 10));\n}\n"}
{"name": "HumanEval_102_choose_num", "language": "rs", "prompt": "/// This function takes two positive numbers x and y and returns the\n/// biggest even integer number that is in the range [x, y] inclusive. If \n/// there's no such number, then the function should return -1.\n/// For example:\n/// >>> choose_num(12, 15)\n/// 14\n/// >>> choose_num(13, 12)\n/// -1\nfn choose_num(x: isize, y: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = choose_num;\n    assert_eq!(candidate(12, 15), 14);\n    assert_eq!(candidate(13, 12), -1);\n    assert_eq!(candidate(33, 12354), 12354);\n    assert_eq!(candidate(5234, 5233), -1);\n    assert_eq!(candidate(6, 29), 28);\n    assert_eq!(candidate(27, 10), -1);\n    assert_eq!(candidate(7, 7), -1);\n    assert_eq!(candidate(546, 546), 546);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_102_choose_num", "test": "}\n\nfn main() {\n    let candidate = choose_num;\n    assert_eq!(candidate(12, 15), 14);\n    assert_eq!(candidate(13, 12), -1);\n    assert_eq!(candidate(33, 12354), 12354);\n    assert_eq!(candidate(5234, 5233), -1);\n    assert_eq!(candidate(6, 29), 28);\n    assert_eq!(candidate(27, 10), -1);\n    assert_eq!(candidate(7, 7), -1);\n    assert_eq!(candidate(546, 546), 546);\n}\n"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "rs", "prompt": "/// Create a function that returns a tuple (a, b), where 'a' is\n/// the largest of negative integers, and 'b' is the smallest\n/// of positive integers in a vector.\n/// If there is no negative or positive integers, return them as None.\n/// Examples:\n/// >>> largest_smallest_integers(vec![2, 4, 1, 3, 5, 7])\n/// (None, Some(1))\n/// >>> largest_smallest_integers(vec![])\n/// (None, None)\n/// >>> largest_smallest_integers(vec![0])\n/// (None, None)\nfn largest_smallest_integers(lst: Vec<isize>) -> (Option<isize>, Option<isize>) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = largest_smallest_integers;\n    assert_eq!(candidate(vec![2, 4, 1, 3, 5, 7]), (None, Some(1)));\n    assert_eq!(candidate(vec![2, 4, 1, 3, 5, 7, 0]), (None, Some(1)));\n    assert_eq!(candidate(vec![1, 3, 2, 4, 5, 6, -2]), (Some(-2), Some(1)));\n    assert_eq!(candidate(vec![4, 5, 3, 6, 2, 7, -7]), (Some(-7), Some(2)));\n    assert_eq!(candidate(vec![7, 3, 8, 4, 9, 2, 5, -9]), (Some(-9), Some(2)));\n    assert_eq!(candidate(Vec::<isize>::new()), (None, None));\n    assert_eq!(candidate(vec![0]), (None, None));\n    assert_eq!(candidate(vec![-1, -3, -5, -6]), (Some(-1), None));\n    assert_eq!(candidate(vec![-1, -3, -5, -6, 0]), (Some(-1), None));\n    assert_eq!(candidate(vec![-6, -4, -4, -3, 1]), (Some(-3), Some(1)));\n    assert_eq!(candidate(vec![-6, -4, -4, -3, -100, 1]), (Some(-3), Some(1)));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "}\n\nfn main() {\n    let candidate = largest_smallest_integers;\n    assert_eq!(candidate(vec![2, 4, 1, 3, 5, 7]), (None, Some(1)));\n    assert_eq!(candidate(vec![2, 4, 1, 3, 5, 7, 0]), (None, Some(1)));\n    assert_eq!(candidate(vec![1, 3, 2, 4, 5, 6, -2]), (Some(-2), Some(1)));\n    assert_eq!(candidate(vec![4, 5, 3, 6, 2, 7, -7]), (Some(-7), Some(2)));\n    assert_eq!(candidate(vec![7, 3, 8, 4, 9, 2, 5, -9]), (Some(-9), Some(2)));\n    assert_eq!(candidate(Vec::<isize>::new()), (None, None));\n    assert_eq!(candidate(vec![0]), (None, None));\n    assert_eq!(candidate(vec![-1, -3, -5, -6]), (Some(-1), None));\n    assert_eq!(candidate(vec![-1, -3, -5, -6, 0]), (Some(-1), None));\n    assert_eq!(candidate(vec![-6, -4, -4, -3, 1]), (Some(-3), Some(1)));\n    assert_eq!(candidate(vec![-6, -4, -4, -3, -100, 1]), (Some(-3), Some(1)));\n}\n"}
{"name": "HumanEval_16_count_distinct_characters", "language": "rs", "prompt": "/// Given a string, find out how many distinct characters (regardless of case) does it consist of\n/// >>> count_distinct_characters(String::from(\"xyzXYZ\"))\n/// 3\n/// >>> count_distinct_characters(String::from(\"Jerry\"))\n/// 4\nfn count_distinct_characters(string: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = count_distinct_characters;\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"abcde\")), 5);\n    assert_eq!(candidate(String::from(\"abcdecadeCADE\")), 5);\n    assert_eq!(candidate(String::from(\"aaaaAAAAaaaa\")), 1);\n    assert_eq!(candidate(String::from(\"Jerry jERRY JeRRRY\")), 5);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_16_count_distinct_characters", "test": "}\n\nfn main() {\n    let candidate = count_distinct_characters;\n    assert_eq!(candidate(String::from(\"\")), 0);\n    assert_eq!(candidate(String::from(\"abcde\")), 5);\n    assert_eq!(candidate(String::from(\"abcdecadeCADE\")), 5);\n    assert_eq!(candidate(String::from(\"aaaaAAAAaaaa\")), 1);\n    assert_eq!(candidate(String::from(\"Jerry jERRY JeRRRY\")), 5);\n}\n"}
{"name": "HumanEval_100_make_a_pile", "language": "rs", "prompt": "/// Given a positive integer n, you have to make a pile of n levels of stones.\n/// The first level has n stones.\n/// The number of stones in the next level is:\n/// - the next odd number if n is odd.\n/// - the next even number if n is even.\n/// Return the number of stones in each level in a vector, where element at index\n/// i represents the number of stones in the level (i+1).\n/// Examples:\n/// >>> make_a_pile(3)\n/// vec![3, 5, 7]\nfn make_a_pile(n: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = make_a_pile;\n    assert_eq!(candidate(3), vec![3, 5, 7]);\n    assert_eq!(candidate(4), vec![4, 6, 8, 10]);\n    assert_eq!(candidate(5), vec![5, 7, 9, 11, 13]);\n    assert_eq!(candidate(6), vec![6, 8, 10, 12, 14, 16]);\n    assert_eq!(candidate(8), vec![8, 10, 12, 14, 16, 18, 20, 22]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_100_make_a_pile", "test": "}\n\nfn main() {\n    let candidate = make_a_pile;\n    assert_eq!(candidate(3), vec![3, 5, 7]);\n    assert_eq!(candidate(4), vec![4, 6, 8, 10]);\n    assert_eq!(candidate(5), vec![5, 7, 9, 11, 13]);\n    assert_eq!(candidate(6), vec![6, 8, 10, 12, 14, 16]);\n    assert_eq!(candidate(8), vec![8, 10, 12, 14, 16, 18, 20, 22]);\n}\n"}
{"name": "HumanEval_128_prod_signs", "language": "rs", "prompt": "/// You are given a vector arr of integers and you need to return\n/// sum of magnitudes of integers multiplied by product of all signs\n/// of each number in the vector, represented by 1, -1 or 0.\n/// Note: return None for empty arr.\n/// Example:\n/// >>> prod_signs(vec![1, 2, 2, -4])\n/// Some(9)\n/// >>> prod_signs(vec![0, 1])\n/// Some(0)\n/// >>> prod_signs(vec![])\n/// None\nfn prod_signs(arr: Vec<isize>) -> Option<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = prod_signs;\n    assert_eq!(candidate(vec![1, 2, 2, -4]), Some(-9));\n    assert_eq!(candidate(vec![0, 1]), Some(0));\n    assert_eq!(candidate(vec![1, 1, 1, 2, 3, -1, 1]), Some(-10));\n    assert_eq!(candidate(Vec::<isize>::new()), None);\n    assert_eq!(candidate(vec![2, 4, 1, 2, -1, -1, 9]), Some(20));\n    assert_eq!(candidate(vec![-1, 1, -1, 1]), Some(4));\n    assert_eq!(candidate(vec![-1, 1, 1, 1]), Some(-4));\n    assert_eq!(candidate(vec![-1, 1, 1, 0]), Some(0));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_128_prod_signs", "test": "}\n\nfn main() {\n    let candidate = prod_signs;\n    assert_eq!(candidate(vec![1, 2, 2, -4]), Some(-9));\n    assert_eq!(candidate(vec![0, 1]), Some(0));\n    assert_eq!(candidate(vec![1, 1, 1, 2, 3, -1, 1]), Some(-10));\n    assert_eq!(candidate(Vec::<isize>::new()), None);\n    assert_eq!(candidate(vec![2, 4, 1, 2, -1, -1, 9]), Some(20));\n    assert_eq!(candidate(vec![-1, 1, -1, 1]), Some(4));\n    assert_eq!(candidate(vec![-1, 1, 1, 1]), Some(-4));\n    assert_eq!(candidate(vec![-1, 1, 1, 0]), Some(0));\n}\n"}
{"name": "HumanEval_114_minSubArraySum", "language": "rs", "prompt": "/// Given a vector of integers nums, find the minimum sum of any non-empty sub-vector\n/// of nums.\n/// Example\n/// >>> minSubArraySum(vec![2, 3, 4, 1, 2, 4])\n/// 1\n/// >>> minSubArraySum(vec![-1, -2, -3])\n/// -6\nfn minSubArraySum(nums: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = minSubArraySum;\n    assert_eq!(candidate(vec![2, 3, 4, 1, 2, 4]), 1);\n    assert_eq!(candidate(vec![-1, -2, -3]), -6);\n    assert_eq!(candidate(vec![-1, -2, -3, 2, -10]), -14);\n    assert_eq!(candidate(vec![-9999999999999999]), -9999999999999999);\n    assert_eq!(candidate(vec![0, 10, 20, 1000000]), 0);\n    assert_eq!(candidate(vec![-1, -2, -3, 10, -5]), -6);\n    assert_eq!(candidate(vec![100, -1, -2, -3, 10, -5]), -6);\n    assert_eq!(candidate(vec![10, 11, 13, 8, 3, 4]), 3);\n    assert_eq!(candidate(vec![100, -33, 32, -1, 0, -2]), -33);\n    assert_eq!(candidate(vec![-10]), -10);\n    assert_eq!(candidate(vec![7]), 7);\n    assert_eq!(candidate(vec![1, -1]), -1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_114_minSubArraySum", "test": "}\n\nfn main() {\n    let candidate = minSubArraySum;\n    assert_eq!(candidate(vec![2, 3, 4, 1, 2, 4]), 1);\n    assert_eq!(candidate(vec![-1, -2, -3]), -6);\n    assert_eq!(candidate(vec![-1, -2, -3, 2, -10]), -14);\n    assert_eq!(candidate(vec![-9999999999999999]), -9999999999999999);\n    assert_eq!(candidate(vec![0, 10, 20, 1000000]), 0);\n    assert_eq!(candidate(vec![-1, -2, -3, 10, -5]), -6);\n    assert_eq!(candidate(vec![100, -1, -2, -3, 10, -5]), -6);\n    assert_eq!(candidate(vec![10, 11, 13, 8, 3, 4]), 3);\n    assert_eq!(candidate(vec![100, -33, 32, -1, 0, -2]), -33);\n    assert_eq!(candidate(vec![-10]), -10);\n    assert_eq!(candidate(vec![7]), 7);\n    assert_eq!(candidate(vec![1, -1]), -1);\n}\n"}
{"name": "HumanEval_15_string_sequence", "language": "rs", "prompt": "/// Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n/// >>> string_sequence(0)\n/// String::from(\"0\")\n/// >>> string_sequence(5)\n/// String::from(\"0 1 2 3 4 5\")\nfn string_sequence(n: isize) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = string_sequence;\n    assert_eq!(candidate(0), String::from(\"0\"));\n    assert_eq!(candidate(3), String::from(\"0 1 2 3\"));\n    assert_eq!(candidate(10), String::from(\"0 1 2 3 4 5 6 7 8 9 10\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_15_string_sequence", "test": "}\n\nfn main() {\n    let candidate = string_sequence;\n    assert_eq!(candidate(0), String::from(\"0\"));\n    assert_eq!(candidate(3), String::from(\"0 1 2 3\"));\n    assert_eq!(candidate(10), String::from(\"0 1 2 3 4 5 6 7 8 9 10\"));\n}\n"}
{"name": "HumanEval_154_cycpattern_check", "language": "rs", "prompt": "/// You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n/// >>> cycpattern_check(String::from(\"abcd\"), String::from(\"abd\"))\n/// false\n/// >>> cycpattern_check(String::from(\"hello\"), String::from(\"ell\"))\n/// true\n/// >>> cycpattern_check(String::from(\"whassup\"), String::from(\"psus\"))\n/// false\n/// >>> cycpattern_check(String::from(\"abab\"), String::from(\"baa\"))\n/// true\n/// >>> cycpattern_check(String::from(\"efef\"), String::from(\"eeff\"))\n/// false\n/// >>> cycpattern_check(String::from(\"himenss\"), String::from(\"simen\"))\n/// true\nfn cycpattern_check(a: String, b: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = cycpattern_check;\n    assert_eq!(candidate(String::from(\"xyzw\"), String::from(\"xyw\")), false);\n    assert_eq!(candidate(String::from(\"yello\"), String::from(\"ell\")), true);\n    assert_eq!(candidate(String::from(\"whattup\"), String::from(\"ptut\")), false);\n    assert_eq!(candidate(String::from(\"efef\"), String::from(\"fee\")), true);\n    assert_eq!(candidate(String::from(\"abab\"), String::from(\"aabb\")), false);\n    assert_eq!(candidate(String::from(\"winemtt\"), String::from(\"tinem\")), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_154_cycpattern_check", "test": "}\n\nfn main() {\n    let candidate = cycpattern_check;\n    assert_eq!(candidate(String::from(\"xyzw\"), String::from(\"xyw\")), false);\n    assert_eq!(candidate(String::from(\"yello\"), String::from(\"ell\")), true);\n    assert_eq!(candidate(String::from(\"whattup\"), String::from(\"ptut\")), false);\n    assert_eq!(candidate(String::from(\"efef\"), String::from(\"fee\")), true);\n    assert_eq!(candidate(String::from(\"abab\"), String::from(\"aabb\")), false);\n    assert_eq!(candidate(String::from(\"winemtt\"), String::from(\"tinem\")), true);\n}\n"}
{"name": "HumanEval_57_monotonic", "language": "rs", "prompt": "/// Return true is vector elements are monotonically increasing or decreasing.\n/// >>> monotonic(vec![1, 2, 4, 20])\n/// true\n/// >>> monotonic(vec![1, 20, 4, 10])\n/// false\n/// >>> monotonic(vec![4, 1, 0, -10])\n/// true\nfn monotonic(l: Vec<isize>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = monotonic;\n    assert_eq!(candidate(vec![1, 2, 4, 10]), true);\n    assert_eq!(candidate(vec![1, 2, 4, 20]), true);\n    assert_eq!(candidate(vec![1, 20, 4, 10]), false);\n    assert_eq!(candidate(vec![4, 1, 0, -10]), true);\n    assert_eq!(candidate(vec![4, 1, 1, 0]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 2, 5, 60]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 60]), true);\n    assert_eq!(candidate(vec![9, 9, 9, 9]), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_57_monotonic", "test": "}\n\nfn main() {\n    let candidate = monotonic;\n    assert_eq!(candidate(vec![1, 2, 4, 10]), true);\n    assert_eq!(candidate(vec![1, 2, 4, 20]), true);\n    assert_eq!(candidate(vec![1, 20, 4, 10]), false);\n    assert_eq!(candidate(vec![4, 1, 0, -10]), true);\n    assert_eq!(candidate(vec![4, 1, 1, 0]), true);\n    assert_eq!(candidate(vec![1, 2, 3, 2, 5, 60]), false);\n    assert_eq!(candidate(vec![1, 2, 3, 4, 5, 60]), true);\n    assert_eq!(candidate(vec![9, 9, 9, 9]), true);\n}\n"}
{"name": "HumanEval_12_longest", "language": "rs", "prompt": "/// Out of vector of strings, return the longest one. Return the first one in case of multiple\n/// strings of the same length. Return None in case the input vector is empty.\n/// >>> longest(vec![])\n/// None\n/// >>> longest(vec![String::from(\"a\"), String::from(\"b\"), String::from(\"c\")])\n/// Some(String::from(\"a\"))\n/// >>> longest(vec![String::from(\"a\"), String::from(\"bb\"), String::from(\"ccc\")])\n/// Some(String::from(\"ccc\"))\nfn longest(strings: Vec<String>) -> Option<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = longest;\n    assert_eq!(candidate(Vec::<String>::new()), None);\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"y\"), String::from(\"z\")]), Some(String::from(\"x\")));\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"yyy\"), String::from(\"zzzz\"), String::from(\"www\"), String::from(\"kkkk\"), String::from(\"abc\")]), Some(String::from(\"zzzz\")));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_12_longest", "test": "}\n\nfn main() {\n    let candidate = longest;\n    assert_eq!(candidate(Vec::<String>::new()), None);\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"y\"), String::from(\"z\")]), Some(String::from(\"x\")));\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"yyy\"), String::from(\"zzzz\"), String::from(\"www\"), String::from(\"kkkk\"), String::from(\"abc\")]), Some(String::from(\"zzzz\")));\n}\n"}
{"name": "HumanEval_52_below_threshold", "language": "rs", "prompt": "/// Return true if all numbers in the vector l are below threshold t.\n/// >>> below_threshold(vec![1, 2, 4, 10], 100)\n/// true\n/// >>> below_threshold(vec![1, 20, 4, 10], 5)\n/// false\nfn below_threshold(l: Vec<isize>, t: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = below_threshold;\n    assert_eq!(candidate(vec![1, 2, 4, 10], 100), true);\n    assert_eq!(candidate(vec![1, 20, 4, 10], 5), false);\n    assert_eq!(candidate(vec![1, 20, 4, 10], 21), true);\n    assert_eq!(candidate(vec![1, 20, 4, 10], 22), true);\n    assert_eq!(candidate(vec![1, 8, 4, 10], 11), true);\n    assert_eq!(candidate(vec![1, 8, 4, 10], 10), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_52_below_threshold", "test": "}\n\nfn main() {\n    let candidate = below_threshold;\n    assert_eq!(candidate(vec![1, 2, 4, 10], 100), true);\n    assert_eq!(candidate(vec![1, 20, 4, 10], 5), false);\n    assert_eq!(candidate(vec![1, 20, 4, 10], 21), true);\n    assert_eq!(candidate(vec![1, 20, 4, 10], 22), true);\n    assert_eq!(candidate(vec![1, 8, 4, 10], 11), true);\n    assert_eq!(candidate(vec![1, 8, 4, 10], 10), false);\n}\n"}
{"name": "HumanEval_75_is_multiply_prime", "language": "rs", "prompt": "/// Write a function that returns true if the given number is the multiplication of 3 prime numbers\n/// and false otherwise.\n/// Knowing that (a) is less then 100. \n/// Example:\n/// >>> is_multiply_prime(30)\n/// true\n/// 30 = 2 * 3 * 5\nfn is_multiply_prime(a: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = is_multiply_prime;\n    assert_eq!(candidate(5), false);\n    assert_eq!(candidate(30), true);\n    assert_eq!(candidate(8), true);\n    assert_eq!(candidate(10), false);\n    assert_eq!(candidate(125), true);\n    assert_eq!(candidate(105), true);\n    assert_eq!(candidate(126), false);\n    assert_eq!(candidate(729), false);\n    assert_eq!(candidate(891), false);\n    assert_eq!(candidate(1001), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_75_is_multiply_prime", "test": "}\n\nfn main() {\n    let candidate = is_multiply_prime;\n    assert_eq!(candidate(5), false);\n    assert_eq!(candidate(30), true);\n    assert_eq!(candidate(8), true);\n    assert_eq!(candidate(10), false);\n    assert_eq!(candidate(125), true);\n    assert_eq!(candidate(105), true);\n    assert_eq!(candidate(126), false);\n    assert_eq!(candidate(729), false);\n    assert_eq!(candidate(891), false);\n    assert_eq!(candidate(1001), true);\n}\n"}
{"name": "HumanEval_30_get_positive", "language": "rs", "prompt": "/// Return only positive numbers in the vector.\n/// >>> get_positive(vec![-1, 2, -4, 5, 6])\n/// vec![2, 5, 6]\n/// >>> get_positive(vec![5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n/// vec![5, 3, 2, 3, 9, 123, 1]\nfn get_positive(l: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = get_positive;\n    assert_eq!(candidate(vec![-1, -2, 4, 5, 6]), vec![4, 5, 6]);\n    assert_eq!(candidate(vec![5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10]), vec![5, 3, 2, 3, 3, 9, 123, 1]);\n    assert_eq!(candidate(vec![-1, -2]), Vec::<isize>::new());\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_30_get_positive", "test": "}\n\nfn main() {\n    let candidate = get_positive;\n    assert_eq!(candidate(vec![-1, -2, 4, 5, 6]), vec![4, 5, 6]);\n    assert_eq!(candidate(vec![5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10]), vec![5, 3, 2, 3, 3, 9, 123, 1]);\n    assert_eq!(candidate(vec![-1, -2]), Vec::<isize>::new());\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n}\n"}
{"name": "HumanEval_33_sort_third", "language": "rs", "prompt": "/// This function takes a vector l and returns a vector l' such that\n/// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n/// to the values of the corresponding indicies of l, but sorted.\n/// >>> sort_third(vec![1, 2, 3])\n/// vec![1, 2, 3]\n/// >>> sort_third(vec![5, 6, 3, 4, 8, 9, 2])\n/// vec![2, 6, 3, 4, 8, 9, 5]\nfn sort_third(l: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sort_third;\n    assert_eq!(candidate(vec![5, 6, 3, 4, 8, 9, 2]), vec![2, 6, 3, 4, 8, 9, 5]);\n    assert_eq!(candidate(vec![5, 8, 3, 4, 6, 9, 2]), vec![2, 8, 3, 4, 6, 9, 5]);\n    assert_eq!(candidate(vec![5, 6, 9, 4, 8, 3, 2]), vec![2, 6, 9, 4, 8, 3, 5]);\n    assert_eq!(candidate(vec![5, 6, 3, 4, 8, 9, 2, 1]), vec![2, 6, 3, 4, 8, 9, 5, 1]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_33_sort_third", "test": "}\n\nfn main() {\n    let candidate = sort_third;\n    assert_eq!(candidate(vec![5, 6, 3, 4, 8, 9, 2]), vec![2, 6, 3, 4, 8, 9, 5]);\n    assert_eq!(candidate(vec![5, 8, 3, 4, 6, 9, 2]), vec![2, 8, 3, 4, 6, 9, 5]);\n    assert_eq!(candidate(vec![5, 6, 9, 4, 8, 3, 2]), vec![2, 6, 9, 4, 8, 3, 5]);\n    assert_eq!(candidate(vec![5, 6, 3, 4, 8, 9, 2, 1]), vec![2, 6, 3, 4, 8, 9, 5, 1]);\n}\n"}
{"name": "HumanEval_6_parse_nested_parens", "language": "rs", "prompt": "/// Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n/// For each of the group, output the deepest level of nesting of parentheses.\n/// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n/// >>> parse_nested_parens(String::from(\"(()()) ((())) () ((())()())\"))\n/// vec![2, 3, 1, 3]\nfn parse_nested_parens(paren_string: String) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = parse_nested_parens;\n    assert_eq!(candidate(String::from(\"(()()) ((())) () ((())()())\")), vec![2, 3, 1, 3]);\n    assert_eq!(candidate(String::from(\"() (()) ((())) (((())))\")), vec![1, 2, 3, 4]);\n    assert_eq!(candidate(String::from(\"(()(())((())))\")), vec![4]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_6_parse_nested_parens", "test": "}\n\nfn main() {\n    let candidate = parse_nested_parens;\n    assert_eq!(candidate(String::from(\"(()()) ((())) () ((())()())\")), vec![2, 3, 1, 3]);\n    assert_eq!(candidate(String::from(\"() (()) ((())) (((())))\")), vec![1, 2, 3, 4]);\n    assert_eq!(candidate(String::from(\"(()(())((())))\")), vec![4]);\n}\n"}
{"name": "HumanEval_45_triangle_area", "language": "rs", "prompt": "/// Given length of a side and high return area for a triangle.\n/// >>> triangle_area(5, 3)\n/// 7.5\nfn triangle_area(a: isize, h: isize) -> f64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = triangle_area;\n    assert_eq!(candidate(5, 3), 7.5);\n    assert_eq!(candidate(2, 2), 2.0);\n    assert_eq!(candidate(10, 8), 40.0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_45_triangle_area", "test": "}\n\nfn main() {\n    let candidate = triangle_area;\n    assert_eq!(candidate(5, 3), 7.5);\n    assert_eq!(candidate(2, 2), 2.0);\n    assert_eq!(candidate(10, 8), 40.0);\n}\n"}
{"name": "HumanEval_97_multiply", "language": "rs", "prompt": "/// Complete the function that takes two integers and returns \n/// the product of their unit digits.\n/// Assume the input is always valid.\n/// Examples:\n/// >>> multiply(148, 412)\n/// 16\n/// >>> multiply(19, 28)\n/// 72\n/// >>> multiply(2020, 1851)\n/// 0\n/// >>> multiply(14, -15)\n/// 20\nfn multiply(a: isize, b: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = multiply;\n    assert_eq!(candidate(148, 412), 16);\n    assert_eq!(candidate(19, 28), 72);\n    assert_eq!(candidate(2020, 1851), 0);\n    assert_eq!(candidate(14, -15), 20);\n    assert_eq!(candidate(76, 67), 42);\n    assert_eq!(candidate(17, 27), 49);\n    assert_eq!(candidate(0, 1), 0);\n    assert_eq!(candidate(0, 0), 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_97_multiply", "test": "}\n\nfn main() {\n    let candidate = multiply;\n    assert_eq!(candidate(148, 412), 16);\n    assert_eq!(candidate(19, 28), 72);\n    assert_eq!(candidate(2020, 1851), 0);\n    assert_eq!(candidate(14, -15), 20);\n    assert_eq!(candidate(76, 67), 42);\n    assert_eq!(candidate(17, 27), 49);\n    assert_eq!(candidate(0, 1), 0);\n    assert_eq!(candidate(0, 0), 0);\n}\n"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "rs", "prompt": "/// For a given vector of input numbers, calculate Mean Absolute Deviation\n/// around the mean of this dataset.\n/// Mean Absolute Deviation is the average absolute difference between each\n/// element and a centerpoint (mean in this case):\n/// MAD = average | x - x_mean |\n/// >>> mean_absolute_deviation(vec![1.0, 2.0, 3.0, 4.0])\n/// 1.0\nfn mean_absolute_deviation(numbers: Vec<f64>) -> f64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = mean_absolute_deviation;\n    assert_eq!(candidate(vec![1.0, 2.0]), 0.5);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0]), 1.0);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0]), 1.2);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "}\n\nfn main() {\n    let candidate = mean_absolute_deviation;\n    assert_eq!(candidate(vec![1.0, 2.0]), 0.5);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0]), 1.0);\n    assert_eq!(candidate(vec![1.0, 2.0, 3.0, 4.0, 5.0]), 1.2);\n}\n"}
{"name": "HumanEval_58_common", "language": "rs", "prompt": "/// Return sorted unique common elements for two vectors.\n/// >>> common(vec![1, 4, 3, 34, 653, 2, 5], vec![5, 7, 1, 5, 9, 653, 121])\n/// vec![1, 5, 653]\n/// >>> common(vec![5, 3, 2, 8], vec![3, 2])\n/// vec![2, 3]\nfn common(l1: Vec<isize>, l2: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = common;\n    assert_eq!(candidate(vec![1, 4, 3, 34, 653, 2, 5], vec![5, 7, 1, 5, 9, 653, 121]), vec![1, 5, 653]);\n    assert_eq!(candidate(vec![5, 3, 2, 8], vec![3, 2]), vec![2, 3]);\n    assert_eq!(candidate(vec![4, 3, 2, 8], vec![3, 2, 4]), vec![2, 3, 4]);\n    assert_eq!(candidate(vec![4, 3, 2, 8], Vec::<isize>::new()), Vec::<isize>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_58_common", "test": "}\n\nfn main() {\n    let candidate = common;\n    assert_eq!(candidate(vec![1, 4, 3, 34, 653, 2, 5], vec![5, 7, 1, 5, 9, 653, 121]), vec![1, 5, 653]);\n    assert_eq!(candidate(vec![5, 3, 2, 8], vec![3, 2]), vec![2, 3]);\n    assert_eq!(candidate(vec![4, 3, 2, 8], vec![3, 2, 4]), vec![2, 3, 4]);\n    assert_eq!(candidate(vec![4, 3, 2, 8], Vec::<isize>::new()), Vec::<isize>::new());\n}\n"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "rs", "prompt": "/// Given a positive integer, obtain its roman numeral equivalent as a string,\n/// and return it in lowercase.\n/// Restrictions: 1 <= num <= 1000\n/// Examples:\n/// >>> int_to_mini_roman(19)\n/// String::from(\"xix\")\n/// >>> int_to_mini_roman(152)\n/// String::from(\"clii\")\n/// >>> int_to_mini_roman(426)\n/// String::from(\"cdxxvi\")\nfn int_to_mini_roman(number: isize) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = int_to_mini_roman;\n    assert_eq!(candidate(19), String::from(\"xix\"));\n    assert_eq!(candidate(152), String::from(\"clii\"));\n    assert_eq!(candidate(251), String::from(\"ccli\"));\n    assert_eq!(candidate(426), String::from(\"cdxxvi\"));\n    assert_eq!(candidate(500), String::from(\"d\"));\n    assert_eq!(candidate(1), String::from(\"i\"));\n    assert_eq!(candidate(4), String::from(\"iv\"));\n    assert_eq!(candidate(43), String::from(\"xliii\"));\n    assert_eq!(candidate(90), String::from(\"xc\"));\n    assert_eq!(candidate(94), String::from(\"xciv\"));\n    assert_eq!(candidate(532), String::from(\"dxxxii\"));\n    assert_eq!(candidate(900), String::from(\"cm\"));\n    assert_eq!(candidate(994), String::from(\"cmxciv\"));\n    assert_eq!(candidate(1000), String::from(\"m\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "}\n\nfn main() {\n    let candidate = int_to_mini_roman;\n    assert_eq!(candidate(19), String::from(\"xix\"));\n    assert_eq!(candidate(152), String::from(\"clii\"));\n    assert_eq!(candidate(251), String::from(\"ccli\"));\n    assert_eq!(candidate(426), String::from(\"cdxxvi\"));\n    assert_eq!(candidate(500), String::from(\"d\"));\n    assert_eq!(candidate(1), String::from(\"i\"));\n    assert_eq!(candidate(4), String::from(\"iv\"));\n    assert_eq!(candidate(43), String::from(\"xliii\"));\n    assert_eq!(candidate(90), String::from(\"xc\"));\n    assert_eq!(candidate(94), String::from(\"xciv\"));\n    assert_eq!(candidate(532), String::from(\"dxxxii\"));\n    assert_eq!(candidate(900), String::from(\"cm\"));\n    assert_eq!(candidate(994), String::from(\"cmxciv\"));\n    assert_eq!(candidate(1000), String::from(\"m\"));\n}\n"}
{"name": "HumanEval_67_fruit_distribution", "language": "rs", "prompt": "/// In this task, you will be given a string that represents a number of apples and oranges \n/// that are distributed in a basket of fruit this basket contains \n/// apples, oranges, and mango fruits. Given the string that represents the total number of \n/// the oranges and apples and an integer that represent the total number of the fruits \n/// in the basket return the number of the mango fruits in the basket.\n/// for examble:\n/// >>> fruit_distribution(String::from(\"5 apples and 6 oranges\"), 19)\n/// 8\n/// >>> fruit_distribution(String::from(\"0 apples and 1 oranges\"), 3)\n/// 2\n/// >>> fruit_distribution(String::from(\"2 apples and 3 oranges\"), 100)\n/// 95\n/// >>> fruit_distribution(String::from(\"100 apples and 1 oranges\"), 120)\n/// 19\nfn fruit_distribution(s: String, n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = fruit_distribution;\n    assert_eq!(candidate(String::from(\"5 apples and 6 oranges\"), 19), 8);\n    assert_eq!(candidate(String::from(\"5 apples and 6 oranges\"), 21), 10);\n    assert_eq!(candidate(String::from(\"0 apples and 1 oranges\"), 3), 2);\n    assert_eq!(candidate(String::from(\"1 apples and 0 oranges\"), 3), 2);\n    assert_eq!(candidate(String::from(\"2 apples and 3 oranges\"), 100), 95);\n    assert_eq!(candidate(String::from(\"2 apples and 3 oranges\"), 5), 0);\n    assert_eq!(candidate(String::from(\"1 apples and 100 oranges\"), 120), 19);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_67_fruit_distribution", "test": "}\n\nfn main() {\n    let candidate = fruit_distribution;\n    assert_eq!(candidate(String::from(\"5 apples and 6 oranges\"), 19), 8);\n    assert_eq!(candidate(String::from(\"5 apples and 6 oranges\"), 21), 10);\n    assert_eq!(candidate(String::from(\"0 apples and 1 oranges\"), 3), 2);\n    assert_eq!(candidate(String::from(\"1 apples and 0 oranges\"), 3), 2);\n    assert_eq!(candidate(String::from(\"2 apples and 3 oranges\"), 100), 95);\n    assert_eq!(candidate(String::from(\"2 apples and 3 oranges\"), 5), 0);\n    assert_eq!(candidate(String::from(\"1 apples and 100 oranges\"), 120), 19);\n}\n"}
{"name": "HumanEval_112_reverse_delete", "language": "rs", "prompt": "/// Task\n/// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n/// then check if the result string is palindrome.\n/// A string is called palindrome if it reads the same backward as forward.\n/// You should return a tuple containing the result string and true/false for the check.\n/// Example\n/// >>> reverse_delete(String::from(\"abcde\"), String::from(\"ae\"))\n/// (String::from(\"bcd\"), false)\n/// >>> reverse_delete(String::from(\"abcdef\"), String::from(\"b\"))\n/// (String::from(\"acdef\"), false)\n/// >>> reverse_delete(String::from(\"abcdedcba\"), String::from(\"ab\"))\n/// (String::from(\"cdedc\"), true)\nfn reverse_delete(s: String, c: String) -> (String, bool) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = reverse_delete;\n    assert_eq!(candidate(String::from(\"abcde\"), String::from(\"ae\")), (String::from(\"bcd\"), false));\n    assert_eq!(candidate(String::from(\"abcdef\"), String::from(\"b\")), (String::from(\"acdef\"), false));\n    assert_eq!(candidate(String::from(\"abcdedcba\"), String::from(\"ab\")), (String::from(\"cdedc\"), true));\n    assert_eq!(candidate(String::from(\"dwik\"), String::from(\"w\")), (String::from(\"dik\"), false));\n    assert_eq!(candidate(String::from(\"a\"), String::from(\"a\")), (String::from(\"\"), true));\n    assert_eq!(candidate(String::from(\"abcdedcba\"), String::from(\"\")), (String::from(\"abcdedcba\"), true));\n    assert_eq!(candidate(String::from(\"abcdedcba\"), String::from(\"v\")), (String::from(\"abcdedcba\"), true));\n    assert_eq!(candidate(String::from(\"vabba\"), String::from(\"v\")), (String::from(\"abba\"), true));\n    assert_eq!(candidate(String::from(\"mamma\"), String::from(\"mia\")), (String::from(\"\"), true));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_112_reverse_delete", "test": "}\n\nfn main() {\n    let candidate = reverse_delete;\n    assert_eq!(candidate(String::from(\"abcde\"), String::from(\"ae\")), (String::from(\"bcd\"), false));\n    assert_eq!(candidate(String::from(\"abcdef\"), String::from(\"b\")), (String::from(\"acdef\"), false));\n    assert_eq!(candidate(String::from(\"abcdedcba\"), String::from(\"ab\")), (String::from(\"cdedc\"), true));\n    assert_eq!(candidate(String::from(\"dwik\"), String::from(\"w\")), (String::from(\"dik\"), false));\n    assert_eq!(candidate(String::from(\"a\"), String::from(\"a\")), (String::from(\"\"), true));\n    assert_eq!(candidate(String::from(\"abcdedcba\"), String::from(\"\")), (String::from(\"abcdedcba\"), true));\n    assert_eq!(candidate(String::from(\"abcdedcba\"), String::from(\"v\")), (String::from(\"abcdedcba\"), true));\n    assert_eq!(candidate(String::from(\"vabba\"), String::from(\"v\")), (String::from(\"abba\"), true));\n    assert_eq!(candidate(String::from(\"mamma\"), String::from(\"mia\")), (String::from(\"\"), true));\n}\n"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "rs", "prompt": "/// Return a greatest common divisor of two integers a and b\n/// >>> greatest_common_divisor(3, 5)\n/// 1\n/// >>> greatest_common_divisor(25, 15)\n/// 5\nfn greatest_common_divisor(a: isize, b: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = greatest_common_divisor;\n    assert_eq!(candidate(3, 7), 1);\n    assert_eq!(candidate(10, 15), 5);\n    assert_eq!(candidate(49, 14), 7);\n    assert_eq!(candidate(144, 60), 12);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "}\n\nfn main() {\n    let candidate = greatest_common_divisor;\n    assert_eq!(candidate(3, 7), 1);\n    assert_eq!(candidate(10, 15), 5);\n    assert_eq!(candidate(49, 14), 7);\n    assert_eq!(candidate(144, 60), 12);\n}\n"}
{"name": "HumanEval_116_sort_array", "language": "rs", "prompt": "/// In this Kata, you have to sort a vector of non-negative integers according to\n/// number of ones in their binary representation in ascending order.\n/// For similar number of ones, sort based on decimal value.\n/// It must be implemented like this:\n/// >>> sort_array(vec![1, 5, 2, 3, 4])\n/// vec![1, 2, 3, 4, 5]\n/// >>> sort_array(vec![-2, -3, -4, -5, -6])\n/// vec![-6, -5, -4, -3, -2]\n/// >>> sort_array(vec![1, 0, 2, 3, 4])\n/// vec![0, 1, 2, 3, 4]\nfn sort_array(arr: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sort_array;\n    assert_eq!(candidate(vec![1, 5, 2, 3, 4]), vec![1, 2, 4, 3, 5]);\n    assert_eq!(candidate(vec![-2, -3, -4, -5, -6]), vec![-4, -2, -6, -5, -3]);\n    assert_eq!(candidate(vec![1, 0, 2, 3, 4]), vec![0, 1, 2, 4, 3]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]), vec![2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77]);\n    assert_eq!(candidate(vec![3, 6, 44, 12, 32, 5]), vec![32, 3, 5, 6, 12, 44]);\n    assert_eq!(candidate(vec![2, 4, 8, 16, 32]), vec![2, 4, 8, 16, 32]);\n    assert_eq!(candidate(vec![2, 4, 8, 16, 32]), vec![2, 4, 8, 16, 32]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_116_sort_array", "test": "}\n\nfn main() {\n    let candidate = sort_array;\n    assert_eq!(candidate(vec![1, 5, 2, 3, 4]), vec![1, 2, 4, 3, 5]);\n    assert_eq!(candidate(vec![-2, -3, -4, -5, -6]), vec![-4, -2, -6, -5, -3]);\n    assert_eq!(candidate(vec![1, 0, 2, 3, 4]), vec![0, 1, 2, 4, 3]);\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]), vec![2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77]);\n    assert_eq!(candidate(vec![3, 6, 44, 12, 32, 5]), vec![32, 3, 5, 6, 12, 44]);\n    assert_eq!(candidate(vec![2, 4, 8, 16, 32]), vec![2, 4, 8, 16, 32]);\n    assert_eq!(candidate(vec![2, 4, 8, 16, 32]), vec![2, 4, 8, 16, 32]);\n}\n"}
{"name": "HumanEval_28_concatenate", "language": "rs", "prompt": "/// Concatenate vector of strings into a single string\n/// >>> concatenate(vec![])\n/// String::from(\"\")\n/// >>> concatenate(vec![String::from(\"a\"), String::from(\"b\"), String::from(\"c\")])\n/// String::from(\"abc\")\nfn concatenate(strings: Vec<String>) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = concatenate;\n    assert_eq!(candidate(Vec::<String>::new()), String::from(\"\"));\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"y\"), String::from(\"z\")]), String::from(\"xyz\"));\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"y\"), String::from(\"z\"), String::from(\"w\"), String::from(\"k\")]), String::from(\"xyzwk\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_28_concatenate", "test": "}\n\nfn main() {\n    let candidate = concatenate;\n    assert_eq!(candidate(Vec::<String>::new()), String::from(\"\"));\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"y\"), String::from(\"z\")]), String::from(\"xyz\"));\n    assert_eq!(candidate(vec![String::from(\"x\"), String::from(\"y\"), String::from(\"z\"), String::from(\"w\"), String::from(\"k\")]), String::from(\"xyzwk\"));\n}\n"}
{"name": "HumanEval_149_sorted_list_sum", "language": "rs", "prompt": "/// Write a function that accepts a vector of strings as a parameter,\n/// deletes the strings that have odd lengths from it,\n/// and returns the resulted vector with a sorted order,\n/// The vector is always a vector of strings and never a vector of numbers,\n/// and it may contain duplicates.\n/// The order of the vector should be ascending by length of each word, and you\n/// should return the vector sorted by that rule.\n/// If two words have the same length, sort the vector alphabetically.\n/// The function should return a vector of strings in sorted order.\n/// You may assume that all words will have the same length.\n/// For example:\n/// >>> list_sort(vec![String::from(\"aa\"), String::from(\"a\"), String::from(\"aaa\")])\n/// vec![String::from(\"aa\")]\n/// >>> list_sort(vec![String::from(\"ab\"), String::from(\"a\"), String::from(\"aaa\"), String::from(\"cd\")])\n/// vec![String::from(\"ab\"), String::from(\"cd\")]\nfn sorted_list_sum(lst: Vec<String>) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sorted_list_sum;\n    assert_eq!(candidate(vec![String::from(\"aa\"), String::from(\"a\"), String::from(\"aaa\")]), vec![String::from(\"aa\")]);\n    assert_eq!(candidate(vec![String::from(\"school\"), String::from(\"AI\"), String::from(\"asdf\"), String::from(\"b\")]), vec![String::from(\"AI\"), String::from(\"asdf\"), String::from(\"school\")]);\n    assert_eq!(candidate(vec![String::from(\"d\"), String::from(\"b\"), String::from(\"c\"), String::from(\"a\")]), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"d\"), String::from(\"dcba\"), String::from(\"abcd\"), String::from(\"a\")]), vec![String::from(\"abcd\"), String::from(\"dcba\")]);\n    assert_eq!(candidate(vec![String::from(\"AI\"), String::from(\"ai\"), String::from(\"au\")]), vec![String::from(\"AI\"), String::from(\"ai\"), String::from(\"au\")]);\n    assert_eq!(candidate(vec![String::from(\"a\"), String::from(\"b\"), String::from(\"b\"), String::from(\"c\"), String::from(\"c\"), String::from(\"a\")]), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"aaaa\"), String::from(\"bbbb\"), String::from(\"dd\"), String::from(\"cc\")]), vec![String::from(\"cc\"), String::from(\"dd\"), String::from(\"aaaa\"), String::from(\"bbbb\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_149_sorted_list_sum", "test": "}\n\nfn main() {\n    let candidate = sorted_list_sum;\n    assert_eq!(candidate(vec![String::from(\"aa\"), String::from(\"a\"), String::from(\"aaa\")]), vec![String::from(\"aa\")]);\n    assert_eq!(candidate(vec![String::from(\"school\"), String::from(\"AI\"), String::from(\"asdf\"), String::from(\"b\")]), vec![String::from(\"AI\"), String::from(\"asdf\"), String::from(\"school\")]);\n    assert_eq!(candidate(vec![String::from(\"d\"), String::from(\"b\"), String::from(\"c\"), String::from(\"a\")]), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"d\"), String::from(\"dcba\"), String::from(\"abcd\"), String::from(\"a\")]), vec![String::from(\"abcd\"), String::from(\"dcba\")]);\n    assert_eq!(candidate(vec![String::from(\"AI\"), String::from(\"ai\"), String::from(\"au\")]), vec![String::from(\"AI\"), String::from(\"ai\"), String::from(\"au\")]);\n    assert_eq!(candidate(vec![String::from(\"a\"), String::from(\"b\"), String::from(\"b\"), String::from(\"c\"), String::from(\"c\"), String::from(\"a\")]), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"aaaa\"), String::from(\"bbbb\"), String::from(\"dd\"), String::from(\"cc\")]), vec![String::from(\"cc\"), String::from(\"dd\"), String::from(\"aaaa\"), String::from(\"bbbb\")]);\n}\n"}
{"name": "HumanEval_7_filter_by_substring", "language": "rs", "prompt": "/// Filter an input vector of strings only for ones that contain given substring\n/// >>> filter_by_substring(vec![], String::from(\"a\"))\n/// Vec::<String>::new()\n/// >>> filter_by_substring(vec![String::from(\"abc\"), String::from(\"bacd\"), String::from(\"cde\"), String::from(\"array\")], String::from(\"a\"))\n/// vec![String::from(\"abc\"), String::from(\"bacd\"), String::from(\"array\")]\nfn filter_by_substring(strings: Vec<String>, substring: String) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = filter_by_substring;\n    assert_eq!(candidate(Vec::<String>::new(), String::from(\"john\")), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"xxx\"), String::from(\"asd\"), String::from(\"xxy\"), String::from(\"john doe\"), String::from(\"xxxAAA\"), String::from(\"xxx\")], String::from(\"xxx\")), vec![String::from(\"xxx\"), String::from(\"xxxAAA\"), String::from(\"xxx\")]);\n    assert_eq!(candidate(vec![String::from(\"xxx\"), String::from(\"asd\"), String::from(\"aaaxxy\"), String::from(\"john doe\"), String::from(\"xxxAAA\"), String::from(\"xxx\")], String::from(\"xx\")), vec![String::from(\"xxx\"), String::from(\"aaaxxy\"), String::from(\"xxxAAA\"), String::from(\"xxx\")]);\n    assert_eq!(candidate(vec![String::from(\"grunt\"), String::from(\"trumpet\"), String::from(\"prune\"), String::from(\"gruesome\")], String::from(\"run\")), vec![String::from(\"grunt\"), String::from(\"prune\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_7_filter_by_substring", "test": "}\n\nfn main() {\n    let candidate = filter_by_substring;\n    assert_eq!(candidate(Vec::<String>::new(), String::from(\"john\")), Vec::<String>::new());\n    assert_eq!(candidate(vec![String::from(\"xxx\"), String::from(\"asd\"), String::from(\"xxy\"), String::from(\"john doe\"), String::from(\"xxxAAA\"), String::from(\"xxx\")], String::from(\"xxx\")), vec![String::from(\"xxx\"), String::from(\"xxxAAA\"), String::from(\"xxx\")]);\n    assert_eq!(candidate(vec![String::from(\"xxx\"), String::from(\"asd\"), String::from(\"aaaxxy\"), String::from(\"john doe\"), String::from(\"xxxAAA\"), String::from(\"xxx\")], String::from(\"xx\")), vec![String::from(\"xxx\"), String::from(\"aaaxxy\"), String::from(\"xxxAAA\"), String::from(\"xxx\")]);\n    assert_eq!(candidate(vec![String::from(\"grunt\"), String::from(\"trumpet\"), String::from(\"prune\"), String::from(\"gruesome\")], String::from(\"run\")), vec![String::from(\"grunt\"), String::from(\"prune\")]);\n}\n"}
{"name": "HumanEval_99_closest_integer", "language": "rs", "prompt": "/// Create a function that takes a value (string) representing a number\n/// and returns the closest integer to it. If the number is equidistant\n/// from two integers, round it away from zero.\n/// Examples\n/// >>> closest_integer(String::from(\"10\"))\n/// 10\n/// >>> closest_integer(String::from(\"15.3\"))\n/// 15\n/// Note:\n/// Rounding away from zero means that if the given number is equidistant\n/// from two integers, the one you should return is the one that is the\n/// farthest from zero. For example closest_integer(\"14.5\") should\n/// return 15 and closest_integer(\"-14.5\") should return -15.\nfn closest_integer(value: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = closest_integer;\n    assert_eq!(candidate(String::from(\"10\")), 10);\n    assert_eq!(candidate(String::from(\"14.5\")), 15);\n    assert_eq!(candidate(String::from(\"-15.5\")), -16);\n    assert_eq!(candidate(String::from(\"15.3\")), 15);\n    assert_eq!(candidate(String::from(\"0\")), 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_99_closest_integer", "test": "}\n\nfn main() {\n    let candidate = closest_integer;\n    assert_eq!(candidate(String::from(\"10\")), 10);\n    assert_eq!(candidate(String::from(\"14.5\")), 15);\n    assert_eq!(candidate(String::from(\"-15.5\")), -16);\n    assert_eq!(candidate(String::from(\"15.3\")), 15);\n    assert_eq!(candidate(String::from(\"0\")), 0);\n}\n"}
{"name": "HumanEval_64_vowels_count", "language": "rs", "prompt": "/// Write a function vowels_count which takes a string representing\n/// a word as input and returns the number of vowels in the string.\n/// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n/// vowel, but only when it is at the end of the given word.\n/// Example:\n/// >>> vowels_count(String::from(\"abcde\"))\n/// 2\n/// >>> vowels_count(String::from(\"ACEDY\"))\n/// 3\nfn vowels_count(s: String) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = vowels_count;\n    assert_eq!(candidate(String::from(\"abcde\")), 2);\n    assert_eq!(candidate(String::from(\"Alone\")), 3);\n    assert_eq!(candidate(String::from(\"key\")), 2);\n    assert_eq!(candidate(String::from(\"bye\")), 1);\n    assert_eq!(candidate(String::from(\"keY\")), 2);\n    assert_eq!(candidate(String::from(\"bYe\")), 1);\n    assert_eq!(candidate(String::from(\"ACEDY\")), 3);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_64_vowels_count", "test": "}\n\nfn main() {\n    let candidate = vowels_count;\n    assert_eq!(candidate(String::from(\"abcde\")), 2);\n    assert_eq!(candidate(String::from(\"Alone\")), 3);\n    assert_eq!(candidate(String::from(\"key\")), 2);\n    assert_eq!(candidate(String::from(\"bye\")), 1);\n    assert_eq!(candidate(String::from(\"keY\")), 2);\n    assert_eq!(candidate(String::from(\"bYe\")), 1);\n    assert_eq!(candidate(String::from(\"ACEDY\")), 3);\n}\n"}
{"name": "HumanEval_158_find_max", "language": "rs", "prompt": "/// Write a function that accepts a vector of strings.\n/// The vector contains different words. Return the word with maximum number\n/// of unique characters. If multiple strings have maximum number of unique\n/// characters, return the one which comes first in lexicographical order.\n/// >>> find_max(vec![String::from(\"name\"), String::from(\"of\"), String::from(\"string\")])\n/// String::from(\"string\")\n/// >>> find_max(vec![String::from(\"name\"), String::from(\"enam\"), String::from(\"game\")])\n/// String::from(\"enam\")\n/// >>> find_max(vec![String::from(\"aaaaaaa\"), String::from(\"bb\"), String::from(\"cc\")])\n/// String::from(\"aaaaaaa\")\nfn find_max(words: Vec<String>) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = find_max;\n    assert_eq!(candidate(vec![String::from(\"name\"), String::from(\"of\"), String::from(\"string\")]), String::from(\"string\"));\n    assert_eq!(candidate(vec![String::from(\"name\"), String::from(\"enam\"), String::from(\"game\")]), String::from(\"enam\"));\n    assert_eq!(candidate(vec![String::from(\"aaaaaaa\"), String::from(\"bb\"), String::from(\"cc\")]), String::from(\"aaaaaaa\"));\n    assert_eq!(candidate(vec![String::from(\"abc\"), String::from(\"cba\")]), String::from(\"abc\"));\n    assert_eq!(candidate(vec![String::from(\"play\"), String::from(\"this\"), String::from(\"game\"), String::from(\"of\"), String::from(\"footbott\")]), String::from(\"footbott\"));\n    assert_eq!(candidate(vec![String::from(\"we\"), String::from(\"are\"), String::from(\"gonna\"), String::from(\"rock\")]), String::from(\"gonna\"));\n    assert_eq!(candidate(vec![String::from(\"we\"), String::from(\"are\"), String::from(\"a\"), String::from(\"mad\"), String::from(\"nation\")]), String::from(\"nation\"));\n    assert_eq!(candidate(vec![String::from(\"this\"), String::from(\"is\"), String::from(\"a\"), String::from(\"prrk\")]), String::from(\"this\"));\n    assert_eq!(candidate(vec![String::from(\"b\")]), String::from(\"b\"));\n    assert_eq!(candidate(vec![String::from(\"play\"), String::from(\"play\"), String::from(\"play\")]), String::from(\"play\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_158_find_max", "test": "}\n\nfn main() {\n    let candidate = find_max;\n    assert_eq!(candidate(vec![String::from(\"name\"), String::from(\"of\"), String::from(\"string\")]), String::from(\"string\"));\n    assert_eq!(candidate(vec![String::from(\"name\"), String::from(\"enam\"), String::from(\"game\")]), String::from(\"enam\"));\n    assert_eq!(candidate(vec![String::from(\"aaaaaaa\"), String::from(\"bb\"), String::from(\"cc\")]), String::from(\"aaaaaaa\"));\n    assert_eq!(candidate(vec![String::from(\"abc\"), String::from(\"cba\")]), String::from(\"abc\"));\n    assert_eq!(candidate(vec![String::from(\"play\"), String::from(\"this\"), String::from(\"game\"), String::from(\"of\"), String::from(\"footbott\")]), String::from(\"footbott\"));\n    assert_eq!(candidate(vec![String::from(\"we\"), String::from(\"are\"), String::from(\"gonna\"), String::from(\"rock\")]), String::from(\"gonna\"));\n    assert_eq!(candidate(vec![String::from(\"we\"), String::from(\"are\"), String::from(\"a\"), String::from(\"mad\"), String::from(\"nation\")]), String::from(\"nation\"));\n    assert_eq!(candidate(vec![String::from(\"this\"), String::from(\"is\"), String::from(\"a\"), String::from(\"prrk\")]), String::from(\"this\"));\n    assert_eq!(candidate(vec![String::from(\"b\")]), String::from(\"b\"));\n    assert_eq!(candidate(vec![String::from(\"play\"), String::from(\"play\"), String::from(\"play\")]), String::from(\"play\"));\n}\n"}
{"name": "HumanEval_162_string_to_md5", "language": "rs", "prompt": "/// Given a string 'text', return its md5 hash equivalent string.\n/// If 'text' is an empty string, return None.\n/// >>> string_to_md5(String::from(\"Hello world\"))\n/// Some(String::from(\"3e25960a79dbc69b674cd4ec67a72c62\"))\nfn string_to_md5(text: String) -> Option<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = string_to_md5;\n    assert_eq!(candidate(String::from(\"Hello world\")), Some(String::from(\"3e25960a79dbc69b674cd4ec67a72c62\")));\n    assert_eq!(candidate(String::from(\"\")), None);\n    assert_eq!(candidate(String::from(\"A B C\")), Some(String::from(\"0ef78513b0cb8cef12743f5aeb35f888\")));\n    assert_eq!(candidate(String::from(\"password\")), Some(String::from(\"5f4dcc3b5aa765d61d8327deb882cf99\")));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_162_string_to_md5", "test": "}\n\nfn main() {\n    let candidate = string_to_md5;\n    assert_eq!(candidate(String::from(\"Hello world\")), Some(String::from(\"3e25960a79dbc69b674cd4ec67a72c62\")));\n    assert_eq!(candidate(String::from(\"\")), None);\n    assert_eq!(candidate(String::from(\"A B C\")), Some(String::from(\"0ef78513b0cb8cef12743f5aeb35f888\")));\n    assert_eq!(candidate(String::from(\"password\")), Some(String::from(\"5f4dcc3b5aa765d61d8327deb882cf99\")));\n}\n"}
{"name": "HumanEval_44_change_base", "language": "rs", "prompt": "/// Change numerical base of input number x to base.\n/// return string representation after the conversion.\n/// base numbers are less than 10.\n/// >>> change_base(8, 3)\n/// String::from(\"22\")\n/// >>> change_base(8, 2)\n/// String::from(\"1000\")\n/// >>> change_base(7, 2)\n/// String::from(\"111\")\nfn change_base(x: isize, base: isize) -> String {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = change_base;\n    assert_eq!(candidate(8, 3), String::from(\"22\"));\n    assert_eq!(candidate(9, 3), String::from(\"100\"));\n    assert_eq!(candidate(234, 2), String::from(\"11101010\"));\n    assert_eq!(candidate(16, 2), String::from(\"10000\"));\n    assert_eq!(candidate(8, 2), String::from(\"1000\"));\n    assert_eq!(candidate(7, 2), String::from(\"111\"));\n    assert_eq!(candidate(2, 3), String::from(\"2\"));\n    assert_eq!(candidate(3, 4), String::from(\"3\"));\n    assert_eq!(candidate(4, 5), String::from(\"4\"));\n    assert_eq!(candidate(5, 6), String::from(\"5\"));\n    assert_eq!(candidate(6, 7), String::from(\"6\"));\n    assert_eq!(candidate(7, 8), String::from(\"7\"));\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_44_change_base", "test": "}\n\nfn main() {\n    let candidate = change_base;\n    assert_eq!(candidate(8, 3), String::from(\"22\"));\n    assert_eq!(candidate(9, 3), String::from(\"100\"));\n    assert_eq!(candidate(234, 2), String::from(\"11101010\"));\n    assert_eq!(candidate(16, 2), String::from(\"10000\"));\n    assert_eq!(candidate(8, 2), String::from(\"1000\"));\n    assert_eq!(candidate(7, 2), String::from(\"111\"));\n    assert_eq!(candidate(2, 3), String::from(\"2\"));\n    assert_eq!(candidate(3, 4), String::from(\"3\"));\n    assert_eq!(candidate(4, 5), String::from(\"4\"));\n    assert_eq!(candidate(5, 6), String::from(\"5\"));\n    assert_eq!(candidate(6, 7), String::from(\"6\"));\n    assert_eq!(candidate(7, 8), String::from(\"7\"));\n}\n"}
{"name": "HumanEval_157_right_angle_triangle", "language": "rs", "prompt": "/// Given the lengths of the three sides of a triangle. Return true if the three\n/// sides form a right-angled triangle, false otherwise.\n/// A right-angled triangle is a triangle in which one angle is right angle or \n/// 90 degree.\n/// Example:\n/// >>> right_angle_triangle(3, 4, 5)\n/// true\n/// >>> right_angle_triangle(1, 2, 3)\n/// false\nfn right_angle_triangle(a: isize, b: isize, c: isize) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = right_angle_triangle;\n    assert_eq!(candidate(3, 4, 5), true);\n    assert_eq!(candidate(1, 2, 3), false);\n    assert_eq!(candidate(10, 6, 8), true);\n    assert_eq!(candidate(2, 2, 2), false);\n    assert_eq!(candidate(7, 24, 25), true);\n    assert_eq!(candidate(10, 5, 7), false);\n    assert_eq!(candidate(5, 12, 13), true);\n    assert_eq!(candidate(15, 8, 17), true);\n    assert_eq!(candidate(48, 55, 73), true);\n    assert_eq!(candidate(1, 1, 1), false);\n    assert_eq!(candidate(2, 2, 10), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_157_right_angle_triangle", "test": "}\n\nfn main() {\n    let candidate = right_angle_triangle;\n    assert_eq!(candidate(3, 4, 5), true);\n    assert_eq!(candidate(1, 2, 3), false);\n    assert_eq!(candidate(10, 6, 8), true);\n    assert_eq!(candidate(2, 2, 2), false);\n    assert_eq!(candidate(7, 24, 25), true);\n    assert_eq!(candidate(10, 5, 7), false);\n    assert_eq!(candidate(5, 12, 13), true);\n    assert_eq!(candidate(15, 8, 17), true);\n    assert_eq!(candidate(48, 55, 73), true);\n    assert_eq!(candidate(1, 1, 1), false);\n    assert_eq!(candidate(2, 2, 10), false);\n}\n"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "rs", "prompt": "/// It is the last week of the semester and the teacher has to give the grades\n/// to students. The teacher has been making her own algorithm for grading.\n/// The only problem is, she has lost the code she used for grading.\n/// She has given you a vector of GPAs for some students and you have to write \n/// a function that can output a vector of letter grades using the following table:\n/// GPA       |    Letter grade\n/// 4.0                A+\n/// > 3.7                A \n/// > 3.3                A- \n/// > 3.0                B+\n/// > 2.7                B \n/// > 2.3                B-\n/// > 2.0                C+\n/// > 1.7                C\n/// > 1.3                C-\n/// > 1.0                D+ \n/// > 0.7                D \n/// > 0.0                D-\n/// 0.0                E\n/// Example:\n/// >>> grade_equation(vec![4.0, 3, 1.7, 2, 3.5])\n/// vec![String::from(\"A+\"), String::from(\"B\"), String::from(\"C-\"), String::from(\"C\"), String::from(\"A-\")]\nfn numerical_letter_grade(grades: Vec<f64>) -> Vec<String> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = numerical_letter_grade;\n    assert_eq!(candidate(vec![4.0, 3.0, 1.7, 2.0, 3.5]), vec![String::from(\"A+\"), String::from(\"B\"), String::from(\"C-\"), String::from(\"C\"), String::from(\"A-\")]);\n    assert_eq!(candidate(vec![1.2]), vec![String::from(\"D+\")]);\n    assert_eq!(candidate(vec![0.5]), vec![String::from(\"D-\")]);\n    assert_eq!(candidate(vec![0.0]), vec![String::from(\"E\")]);\n    assert_eq!(candidate(vec![1.0, 0.3, 1.5, 2.8, 3.3]), vec![String::from(\"D\"), String::from(\"D-\"), String::from(\"C-\"), String::from(\"B\"), String::from(\"B+\")]);\n    assert_eq!(candidate(vec![0.0, 0.7]), vec![String::from(\"E\"), String::from(\"D-\")]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "}\n\nfn main() {\n    let candidate = numerical_letter_grade;\n    assert_eq!(candidate(vec![4.0, 3.0, 1.7, 2.0, 3.5]), vec![String::from(\"A+\"), String::from(\"B\"), String::from(\"C-\"), String::from(\"C\"), String::from(\"A-\")]);\n    assert_eq!(candidate(vec![1.2]), vec![String::from(\"D+\")]);\n    assert_eq!(candidate(vec![0.5]), vec![String::from(\"D-\")]);\n    assert_eq!(candidate(vec![0.0]), vec![String::from(\"E\")]);\n    assert_eq!(candidate(vec![1.0, 0.3, 1.5, 2.8, 3.3]), vec![String::from(\"D\"), String::from(\"D-\"), String::from(\"C-\"), String::from(\"B\"), String::from(\"B+\")]);\n    assert_eq!(candidate(vec![0.0, 0.7]), vec![String::from(\"E\"), String::from(\"D-\")]);\n}\n"}
{"name": "HumanEval_5_intersperse", "language": "rs", "prompt": "/// Insert a number 'delimeter' between every two consecutive elements of input vector `numbers'\n/// >>> intersperse(vec![], 4)\n/// Vec::<isize>::new()\n/// >>> intersperse(vec![1, 2, 3], 4)\n/// vec![1, 4, 2, 4, 3]\nfn intersperse(numbers: Vec<isize>, delimeter: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = intersperse;\n    assert_eq!(candidate(Vec::<isize>::new(), 7), Vec::<isize>::new());\n    assert_eq!(candidate(vec![5, 6, 3, 2], 8), vec![5, 8, 6, 8, 3, 8, 2]);\n    assert_eq!(candidate(vec![2, 2, 2], 2), vec![2, 2, 2, 2, 2]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_5_intersperse", "test": "}\n\nfn main() {\n    let candidate = intersperse;\n    assert_eq!(candidate(Vec::<isize>::new(), 7), Vec::<isize>::new());\n    assert_eq!(candidate(vec![5, 6, 3, 2], 8), vec![5, 8, 6, 8, 3, 8, 2]);\n    assert_eq!(candidate(vec![2, 2, 2], 2), vec![2, 2, 2, 2, 2]);\n}\n"}
{"name": "HumanEval_146_specialFilter", "language": "rs", "prompt": "/// Write a function that takes a vector of numbers as input and returns \n/// the number of elements in the vector that are greater than 10 and both \n/// first and last digits of a number are odd (1, 3, 5, 7, 9).\n/// For example:\n/// >>> specialFilter(vec![15, -73, 14, -15])\n/// 1\n/// >>> specialFilter(vec![33, -2, -3, 45, 21, 109])\n/// 2\nfn specialFilter(nums: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = specialFilter;\n    assert_eq!(candidate(vec![5, -2, 1, -5]), 0);\n    assert_eq!(candidate(vec![15, -73, 14, -15]), 1);\n    assert_eq!(candidate(vec![33, -2, -3, 45, 21, 109]), 2);\n    assert_eq!(candidate(vec![43, -12, 93, 125, 121, 109]), 4);\n    assert_eq!(candidate(vec![71, -2, -33, 75, 21, 19]), 3);\n    assert_eq!(candidate(vec![1]), 0);\n    assert_eq!(candidate(Vec::<isize>::new()), 0);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_146_specialFilter", "test": "}\n\nfn main() {\n    let candidate = specialFilter;\n    assert_eq!(candidate(vec![5, -2, 1, -5]), 0);\n    assert_eq!(candidate(vec![15, -73, 14, -15]), 1);\n    assert_eq!(candidate(vec![33, -2, -3, 45, 21, 109]), 2);\n    assert_eq!(candidate(vec![43, -12, 93, 125, 121, 109]), 4);\n    assert_eq!(candidate(vec![71, -2, -33, 75, 21, 19]), 3);\n    assert_eq!(candidate(vec![1]), 0);\n    assert_eq!(candidate(Vec::<isize>::new()), 0);\n}\n"}
{"name": "HumanEval_60_sum_to_n", "language": "rs", "prompt": "/// sum_to_n is a function that sums numbers from 1 to n.\n/// >>> sum_to_n(30)\n/// 465\n/// >>> sum_to_n(100)\n/// 5050\n/// >>> sum_to_n(5)\n/// 15\n/// >>> sum_to_n(10)\n/// 55\n/// >>> sum_to_n(1)\n/// 1\nfn sum_to_n(n: isize) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sum_to_n;\n    assert_eq!(candidate(1), 1);\n    assert_eq!(candidate(6), 21);\n    assert_eq!(candidate(11), 66);\n    assert_eq!(candidate(30), 465);\n    assert_eq!(candidate(100), 5050);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_60_sum_to_n", "test": "}\n\nfn main() {\n    let candidate = sum_to_n;\n    assert_eq!(candidate(1), 1);\n    assert_eq!(candidate(6), 21);\n    assert_eq!(candidate(11), 66);\n    assert_eq!(candidate(30), 465);\n    assert_eq!(candidate(100), 5050);\n}\n"}
{"name": "HumanEval_26_remove_duplicates", "language": "rs", "prompt": "/// From a vector of integers, remove all elements that occur more than once.\n/// Keep order of elements left the same as in the input.\n/// >>> remove_duplicates(vec![1, 2, 3, 2, 4])\n/// vec![1, 3, 4]\nfn remove_duplicates(numbers: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = remove_duplicates;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, 2, 3, 4]), vec![1, 2, 3, 4]);\n    assert_eq!(candidate(vec![1, 2, 3, 2, 4, 3, 5]), vec![1, 4, 5]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_26_remove_duplicates", "test": "}\n\nfn main() {\n    let candidate = remove_duplicates;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, 2, 3, 4]), vec![1, 2, 3, 4]);\n    assert_eq!(candidate(vec![1, 2, 3, 2, 4, 3, 5]), vec![1, 4, 5]);\n}\n"}
{"name": "HumanEval_163_generate_integers", "language": "rs", "prompt": "/// Given two positive integers a and b, return the even digits between a\n/// and b, in ascending order.\n/// For example:\n/// >>> generate_integers(2, 8)\n/// vec![2, 4, 6, 8]\n/// >>> generate_integers(8, 2)\n/// vec![2, 4, 6, 8]\n/// >>> generate_integers(10, 14)\n/// Vec::<isize>::new()\nfn generate_integers(a: isize, b: isize) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = generate_integers;\n    assert_eq!(candidate(2, 10), vec![2, 4, 6, 8]);\n    assert_eq!(candidate(10, 2), vec![2, 4, 6, 8]);\n    assert_eq!(candidate(132, 2), vec![2, 4, 6, 8]);\n    assert_eq!(candidate(17, 89), Vec::<isize>::new());\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_163_generate_integers", "test": "}\n\nfn main() {\n    let candidate = generate_integers;\n    assert_eq!(candidate(2, 10), vec![2, 4, 6, 8]);\n    assert_eq!(candidate(10, 2), vec![2, 4, 6, 8]);\n    assert_eq!(candidate(132, 2), vec![2, 4, 6, 8]);\n    assert_eq!(candidate(17, 89), Vec::<isize>::new());\n}\n"}
{"name": "HumanEval_9_rolling_max", "language": "rs", "prompt": "/// From a given vector of integers, generate a vector of rolling maximum element found until given moment\n/// in the sequence.\n/// >>> rolling_max(vec![1, 2, 3, 2, 3, 4, 2])\n/// vec![1, 2, 3, 3, 3, 4, 4]\nfn rolling_max(numbers: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = rolling_max;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, 2, 3, 4]), vec![1, 2, 3, 4]);\n    assert_eq!(candidate(vec![4, 3, 2, 1]), vec![4, 4, 4, 4]);\n    assert_eq!(candidate(vec![3, 2, 3, 100, 3]), vec![3, 3, 3, 100, 100]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_9_rolling_max", "test": "}\n\nfn main() {\n    let candidate = rolling_max;\n    assert_eq!(candidate(Vec::<isize>::new()), Vec::<isize>::new());\n    assert_eq!(candidate(vec![1, 2, 3, 4]), vec![1, 2, 3, 4]);\n    assert_eq!(candidate(vec![4, 3, 2, 1]), vec![4, 4, 4, 4]);\n    assert_eq!(candidate(vec![3, 2, 3, 100, 3]), vec![3, 3, 3, 100, 100]);\n}\n"}
{"name": "HumanEval_3_below_zero", "language": "rs", "prompt": "/// You're given a vector of deposit and withdrawal operations on a bank account that starts with\n/// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n/// at that point function should return true. Otherwise it should return false.\n/// >>> below_zero(vec![1, 2, 3])\n/// false\n/// >>> below_zero(vec![1, 2, -4, 5])\n/// true\nfn below_zero(operations: Vec<isize>) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = below_zero;\n    assert_eq!(candidate(Vec::<isize>::new()), false);\n    assert_eq!(candidate(vec![1, 2, -3, 1, 2, -3]), false);\n    assert_eq!(candidate(vec![1, 2, -4, 5, 6]), true);\n    assert_eq!(candidate(vec![1, -1, 2, -2, 5, -5, 4, -4]), false);\n    assert_eq!(candidate(vec![1, -1, 2, -2, 5, -5, 4, -5]), true);\n    assert_eq!(candidate(vec![1, -2, 2, -2, 5, -5, 4, -4]), true);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_3_below_zero", "test": "}\n\nfn main() {\n    let candidate = below_zero;\n    assert_eq!(candidate(Vec::<isize>::new()), false);\n    assert_eq!(candidate(vec![1, 2, -3, 1, 2, -3]), false);\n    assert_eq!(candidate(vec![1, 2, -4, 5, 6]), true);\n    assert_eq!(candidate(vec![1, -1, 2, -2, 5, -5, 4, -4]), false);\n    assert_eq!(candidate(vec![1, -1, 2, -2, 5, -5, 4, -5]), true);\n    assert_eq!(candidate(vec![1, -2, 2, -2, 5, -5, 4, -4]), true);\n}\n"}
{"name": "HumanEval_69_search", "language": "rs", "prompt": "/// You are given a non-empty vector of positive integers. Return the greatest integer that is greater than \n/// zero, and has a frequency greater than or equal to the value of the integer itself. \n/// The frequency of an integer is the number of times it appears in the vector.\n/// If no such a value exist, return -1.\n/// Examples:\n/// >>> search(vec![4, 1, 2, 2, 3, 1])\n/// 2\n/// >>> search(vec![1, 2, 2, 3, 3, 3, 4, 4, 4])\n/// 3\n/// >>> search(vec![5, 5, 4, 4, 4])\n/// -1\nfn search(lst: Vec<isize>) -> isize {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = search;\n    assert_eq!(candidate(vec![5, 5, 5, 5, 1]), 1);\n    assert_eq!(candidate(vec![4, 1, 4, 1, 4, 4]), 4);\n    assert_eq!(candidate(vec![3, 3]), -1);\n    assert_eq!(candidate(vec![8, 8, 8, 8, 8, 8, 8, 8]), 8);\n    assert_eq!(candidate(vec![2, 3, 3, 2, 2]), 2);\n    assert_eq!(candidate(vec![2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1]), 1);\n    assert_eq!(candidate(vec![3, 2, 8, 2]), 2);\n    assert_eq!(candidate(vec![6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10]), 1);\n    assert_eq!(candidate(vec![8, 8, 3, 6, 5, 6, 4]), -1);\n    assert_eq!(candidate(vec![6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9]), 1);\n    assert_eq!(candidate(vec![1, 9, 10, 1, 3]), 1);\n    assert_eq!(candidate(vec![6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10]), 5);\n    assert_eq!(candidate(vec![1]), 1);\n    assert_eq!(candidate(vec![8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5]), 4);\n    assert_eq!(candidate(vec![2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10]), 2);\n    assert_eq!(candidate(vec![1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3]), 1);\n    assert_eq!(candidate(vec![9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4]), 4);\n    assert_eq!(candidate(vec![2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7]), 4);\n    assert_eq!(candidate(vec![9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1]), 2);\n    assert_eq!(candidate(vec![5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8]), -1);\n    assert_eq!(candidate(vec![10]), -1);\n    assert_eq!(candidate(vec![9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2]), 2);\n    assert_eq!(candidate(vec![5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8]), 1);\n    assert_eq!(candidate(vec![7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6]), 1);\n    assert_eq!(candidate(vec![3, 10, 10, 9, 2]), -1);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_69_search", "test": "}\n\nfn main() {\n    let candidate = search;\n    assert_eq!(candidate(vec![5, 5, 5, 5, 1]), 1);\n    assert_eq!(candidate(vec![4, 1, 4, 1, 4, 4]), 4);\n    assert_eq!(candidate(vec![3, 3]), -1);\n    assert_eq!(candidate(vec![8, 8, 8, 8, 8, 8, 8, 8]), 8);\n    assert_eq!(candidate(vec![2, 3, 3, 2, 2]), 2);\n    assert_eq!(candidate(vec![2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1]), 1);\n    assert_eq!(candidate(vec![3, 2, 8, 2]), 2);\n    assert_eq!(candidate(vec![6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10]), 1);\n    assert_eq!(candidate(vec![8, 8, 3, 6, 5, 6, 4]), -1);\n    assert_eq!(candidate(vec![6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9]), 1);\n    assert_eq!(candidate(vec![1, 9, 10, 1, 3]), 1);\n    assert_eq!(candidate(vec![6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10]), 5);\n    assert_eq!(candidate(vec![1]), 1);\n    assert_eq!(candidate(vec![8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5]), 4);\n    assert_eq!(candidate(vec![2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10]), 2);\n    assert_eq!(candidate(vec![1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3]), 1);\n    assert_eq!(candidate(vec![9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4]), 4);\n    assert_eq!(candidate(vec![2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7]), 4);\n    assert_eq!(candidate(vec![9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1]), 2);\n    assert_eq!(candidate(vec![5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8]), -1);\n    assert_eq!(candidate(vec![10]), -1);\n    assert_eq!(candidate(vec![9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2]), 2);\n    assert_eq!(candidate(vec![5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8]), 1);\n    assert_eq!(candidate(vec![7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6]), 1);\n    assert_eq!(candidate(vec![3, 10, 10, 9, 2]), -1);\n}\n"}
{"name": "HumanEval_61_correct_bracketing", "language": "rs", "prompt": "/// brackets is a string of \"(\" and \")\".\n/// return true if every opening bracket has a corresponding closing bracket.\n/// >>> correct_bracketing(String::from(\"(\"))\n/// false\n/// >>> correct_bracketing(String::from(\"()\"))\n/// true\n/// >>> correct_bracketing(String::from(\"(()())\"))\n/// true\n/// >>> correct_bracketing(String::from(\")(()\"))\n/// false\nfn correct_bracketing(brackets: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = correct_bracketing;\n    assert_eq!(candidate(String::from(\"()\")), true);\n    assert_eq!(candidate(String::from(\"(()())\")), true);\n    assert_eq!(candidate(String::from(\"()()(()())()\")), true);\n    assert_eq!(candidate(String::from(\"()()((()()())())(()()(()))\")), true);\n    assert_eq!(candidate(String::from(\"((()())))\")), false);\n    assert_eq!(candidate(String::from(\")(()\")), false);\n    assert_eq!(candidate(String::from(\"(\")), false);\n    assert_eq!(candidate(String::from(\"((((\")), false);\n    assert_eq!(candidate(String::from(\")\")), false);\n    assert_eq!(candidate(String::from(\"(()\")), false);\n    assert_eq!(candidate(String::from(\"()()(()())())(()\")), false);\n    assert_eq!(candidate(String::from(\"()()(()())()))()\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_61_correct_bracketing", "test": "}\n\nfn main() {\n    let candidate = correct_bracketing;\n    assert_eq!(candidate(String::from(\"()\")), true);\n    assert_eq!(candidate(String::from(\"(()())\")), true);\n    assert_eq!(candidate(String::from(\"()()(()())()\")), true);\n    assert_eq!(candidate(String::from(\"()()((()()())())(()()(()))\")), true);\n    assert_eq!(candidate(String::from(\"((()())))\")), false);\n    assert_eq!(candidate(String::from(\")(()\")), false);\n    assert_eq!(candidate(String::from(\"(\")), false);\n    assert_eq!(candidate(String::from(\"((((\")), false);\n    assert_eq!(candidate(String::from(\")\")), false);\n    assert_eq!(candidate(String::from(\"(()\")), false);\n    assert_eq!(candidate(String::from(\"()()(()())())(()\")), false);\n    assert_eq!(candidate(String::from(\"()()(()())()))()\")), false);\n}\n"}
{"name": "HumanEval_37_sort_even", "language": "rs", "prompt": "/// This function takes a vector l and returns a vector l' such that\n/// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n/// to the values of the even indicies of l, but sorted.\n/// >>> sort_even(vec![1, 2, 3])\n/// vec![1, 2, 3]\n/// >>> sort_even(vec![5, 6, 3, 4])\n/// vec![3, 6, 5, 4]\nfn sort_even(l: Vec<isize>) -> Vec<isize> {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = sort_even;\n    assert_eq!(candidate(vec![1, 2, 3]), vec![1, 2, 3]);\n    assert_eq!(candidate(vec![5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10]), vec![-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123]);\n    assert_eq!(candidate(vec![5, 8, -12, 4, 23, 2, 3, 11, 12, -10]), vec![-12, 8, 3, 4, 5, 2, 12, 11, 23, -10]);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_37_sort_even", "test": "}\n\nfn main() {\n    let candidate = sort_even;\n    assert_eq!(candidate(vec![1, 2, 3]), vec![1, 2, 3]);\n    assert_eq!(candidate(vec![5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10]), vec![-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123]);\n    assert_eq!(candidate(vec![5, 8, -12, 4, 23, 2, 3, 11, 12, -10]), vec![-12, 8, 3, 4, 5, 2, 12, 11, 23, -10]);\n}\n"}
{"name": "HumanEval_54_same_chars", "language": "rs", "prompt": "/// Check if two words have the same characters.\n/// >>> same_chars(String::from(\"eabcdzzzz\"), String::from(\"dddzzzzzzzddeddabc\"))\n/// true\n/// >>> same_chars(String::from(\"abcd\"), String::from(\"dddddddabc\"))\n/// true\n/// >>> same_chars(String::from(\"dddddddabc\"), String::from(\"abcd\"))\n/// true\n/// >>> same_chars(String::from(\"eabcd\"), String::from(\"dddddddabc\"))\n/// false\n/// >>> same_chars(String::from(\"abcd\"), String::from(\"dddddddabce\"))\n/// false\n/// >>> same_chars(String::from(\"eabcdzzzz\"), String::from(\"dddzzzzzzzddddabc\"))\n/// false\nfn same_chars(s0: String, s1: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = same_chars;\n    assert_eq!(candidate(String::from(\"eabcdzzzz\"), String::from(\"dddzzzzzzzddeddabc\")), true);\n    assert_eq!(candidate(String::from(\"abcd\"), String::from(\"dddddddabc\")), true);\n    assert_eq!(candidate(String::from(\"dddddddabc\"), String::from(\"abcd\")), true);\n    assert_eq!(candidate(String::from(\"eabcd\"), String::from(\"dddddddabc\")), false);\n    assert_eq!(candidate(String::from(\"abcd\"), String::from(\"dddddddabcf\")), false);\n    assert_eq!(candidate(String::from(\"eabcdzzzz\"), String::from(\"dddzzzzzzzddddabc\")), false);\n    assert_eq!(candidate(String::from(\"aabb\"), String::from(\"aaccc\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_54_same_chars", "test": "}\n\nfn main() {\n    let candidate = same_chars;\n    assert_eq!(candidate(String::from(\"eabcdzzzz\"), String::from(\"dddzzzzzzzddeddabc\")), true);\n    assert_eq!(candidate(String::from(\"abcd\"), String::from(\"dddddddabc\")), true);\n    assert_eq!(candidate(String::from(\"dddddddabc\"), String::from(\"abcd\")), true);\n    assert_eq!(candidate(String::from(\"eabcd\"), String::from(\"dddddddabc\")), false);\n    assert_eq!(candidate(String::from(\"abcd\"), String::from(\"dddddddabcf\")), false);\n    assert_eq!(candidate(String::from(\"eabcdzzzz\"), String::from(\"dddzzzzzzzddddabc\")), false);\n    assert_eq!(candidate(String::from(\"aabb\"), String::from(\"aaccc\")), false);\n}\n"}
{"name": "HumanEval_56_correct_bracketing", "language": "rs", "prompt": "/// brackets is a string of \"<\" and \">\".\n/// return true if every opening bracket has a corresponding closing bracket.\n/// >>> correct_bracketing(String::from(\"<\"))\n/// false\n/// >>> correct_bracketing(String::from(\"<>\"))\n/// true\n/// >>> correct_bracketing(String::from(\"<<><>>\"))\n/// true\n/// >>> correct_bracketing(String::from(\"><<>\"))\n/// false\nfn correct_bracketing(brackets: String) -> bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\n\nfn main() {\n    let candidate = correct_bracketing;\n    assert_eq!(candidate(String::from(\"<>\")), true);\n    assert_eq!(candidate(String::from(\"<<><>>\")), true);\n    assert_eq!(candidate(String::from(\"<><><<><>><>\")), true);\n    assert_eq!(candidate(String::from(\"<><><<<><><>><>><<><><<>>>\")), true);\n    assert_eq!(candidate(String::from(\"<<<><>>>>\")), false);\n    assert_eq!(candidate(String::from(\"><<>\")), false);\n    assert_eq!(candidate(String::from(\"<\")), false);\n    assert_eq!(candidate(String::from(\"<<<<\")), false);\n    assert_eq!(candidate(String::from(\">\")), false);\n    assert_eq!(candidate(String::from(\"<<>\")), false);\n    assert_eq!(candidate(String::from(\"<><><<><>><>><<>\")), false);\n    assert_eq!(candidate(String::from(\"<><><<><>><>>><>\")), false);\n}\n", "stop_tokens": ["\n}"], "task_id": "HumanEval_56_correct_bracketing", "test": "}\n\nfn main() {\n    let candidate = correct_bracketing;\n    assert_eq!(candidate(String::from(\"<>\")), true);\n    assert_eq!(candidate(String::from(\"<<><>>\")), true);\n    assert_eq!(candidate(String::from(\"<><><<><>><>\")), true);\n    assert_eq!(candidate(String::from(\"<><><<<><><>><>><<><><<>>>\")), true);\n    assert_eq!(candidate(String::from(\"<<<><>>>>\")), false);\n    assert_eq!(candidate(String::from(\"><<>\")), false);\n    assert_eq!(candidate(String::from(\"<\")), false);\n    assert_eq!(candidate(String::from(\"<<<<\")), false);\n    assert_eq!(candidate(String::from(\">\")), false);\n    assert_eq!(candidate(String::from(\"<<>\")), false);\n    assert_eq!(candidate(String::from(\"<><><<><>><>><<>\")), false);\n    assert_eq!(candidate(String::from(\"<><><<><>><>>><>\")), false);\n}\n"}
