---
title: "Quickstart"
description: "Start using Browser Use with this quickstart guide"
icon: "rocket"
---

{/* You can install Browser Use from PyPI or clone it from Github. */}

## Prepare the environment

Browser Use requires Python 3.11 or higher.

First, we recommend using [uv](https://docs.astral.sh/uv/) to setup the Python environment.

```bash
uv venv --python 3.11
```

and activate it with:

```bash
# For Mac/Linux:
source .venv/bin/activate

# For Windows:
.venv\Scripts\activate
```

Install the dependencies:

```bash
uv pip install browser-use
```

Then install playwright:

```bash
uv run playwright install
```

## Create an agent

Then you can use the agent as follows:

```python agent.py
from browser_use.llm import ChatOpenAI
from browser_use import Agent
from dotenv import load_dotenv
load_dotenv()

import asyncio

llm = ChatOpenAI(model="gpt-4.1")

async def main():
    agent = Agent(
        task="Compare the price of gpt-4o and DeepSeek-V3",
        llm=llm,
    )
    result = await agent.run()
    print(result)

asyncio.run(main())
```

## Set up your LLM API keys

`ChatOpenAI` and other chat models require API keys. You should store these in your `.env` file. For example, for OpenAI and Anthropic, you can set the API keys in your `.env` file, such as:

```bash .env
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
```

For other LLM models you can refer to the [Supported Models](/customize/supported-models) page to find how to set them up with their specific API keys.
