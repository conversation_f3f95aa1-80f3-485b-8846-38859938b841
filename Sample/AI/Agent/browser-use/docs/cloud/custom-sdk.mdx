---
title: "Cloud SDK"
description: "Learn how to set up your own Browser Use Cloud SDK"
icon: "code"
---

This guide walks you through setting up your own Browser Use Cloud SDK.

## Building your own client (OpenAPI)

<Note>
  This approach is recommended **only** if you need to run simple tasks and
  **don’t require fine-grained control**.
</Note>

The best way to build your own client is to use our [OpenAPI specification](http://api.browser-use.com/openapi.json) to generate a type-safe client library.

### Python

Use [openapi-python-client](https://github.com/openapi-generators/openapi-python-client) to generate a modern Python client:

```bash
# Install the generator
pipx install openapi-python-client --include-deps

# Generate the client
openapi-python-client generate --url http://api.browser-use.com/openapi.json
```

This will create a Python package with full type hints, modern dataclasses, and async support.

### TypeScript/JavaScript

For TypeScript projects, use [openapi-typescript](https://www.npmjs.com/package/openapi-typescript) to generate type definitions:

```bash
# Install the generator
npm install -D openapi-typescript

# Generate the types
npx openapi-typescript http://api.browser-use.com/openapi.json -o browser-use-api.ts
```

This will create TypeScript definitions you can use with your preferred HTTP client.

<Note>
  Need help? Contact our support <NAME_EMAIL> or join our
  [Discord community](https://link.browser-use.com/discord)
</Note>
