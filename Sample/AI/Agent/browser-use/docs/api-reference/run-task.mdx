---
title: "Run Task"
api: "POST /api/v1/run-task"
description: "Requires an active subscription. Returns the task ID that can be used to track progress."
---

Creates a new browser automation task and returns the task ID that can be used to track progress.

## Request Body

<ParamField body="task" type="string" required>
  Instructions for what the agent should do. You can try it out at https://cloud.browser-use.com/
</ParamField>

<ParamField body="secrets" type="object">
  Dictionary of secrets to be used by the agent. Secrets are safely encrypted before storing in the database.
</ParamField>

<ParamField body="allowed_domains" type="array">
  List of domains that the agent is allowed to visit. If not set, the agent will be allowed to visit all domains (not recommended if you are using secrets).
</ParamField>

<ParamField body="save_browser_data" type="boolean" default="false">
  If set to True, the browser cookies and other data will be saved. Cookies are safely encrypted before storing in the database.
</ParamField>

<Param<PERSON>ield body="structured_output_json" type="string">
  If set, the agent will use this JSON schema as the output model (see example here: https://docs.browser-use.com/cloud/implementation#structured-output-example).
</ParamField>

<ParamField body="llm_model" type="string" default="gpt-4.1">
  LLM model to use. 
  Available options: 
  - gpt-4o
  - gpt-4o-mini
  - gpt-4.1
  - gpt-4.1-mini
  - gemini-2.5-flash
  - claude-sonnet-4-20250514
</ParamField>

<ParamField body="use_adblock" type="boolean" default="true">
  If set to True, the agent will use an adblocker.
</ParamField>

<ParamField body="use_proxy" type="boolean" default="true">
  If set to True, the agent will use a (mobile) proxy. Note that proxy is required for captcha solving, so if you disable proxy, you will not be able to solve captchas.
</ParamField>

<ParamField body="proxy_country_code" type="string" default="us">
  Country code for residential proxy. Must be one of: 'us', 'fr', 'it', 'jp', 'au', 'de', 'fi', 'ca'. Default is 'us'.
</ParamField>

<ParamField body="highlight_elements" type="boolean" default="true">
  If set to True, the agent will highlight the elements on the page.
</ParamField>

<ParamField body="included_file_names" type="array">
  File names to include in the task (note: use uploads/presigned-url endpoint to upload the files first!). E.g. ['file1.txt', 'file2.csv']
</ParamField>

<ParamField body="browser_viewport_width" type="integer" default="1280">
  Width of the browser viewport in pixels. Default is 1280.
</ParamField>

<ParamField body="browser_viewport_height" type="integer" default="960">
  Height of the browser viewport in pixels. Default is 960.
</ParamField>

<ParamField body="max_agent_steps" type="integer" default="75">
  Maximum number of agent steps to take. Default is 75. Maximum is 200.
</ParamField>

<ParamField body="enable_public_share" type="boolean" default="false">
  If set to True, enables public sharing of the task execution. When enabled, a public_share_url will be generated that allows others to view the task results without authentication.
</ParamField>

<ParamField body="metadata" type="object">
  Optional dictionary of string key-value pairs for custom tagging. Max 10 pairs. Keys: strings (max 100 chars, non-empty). Values: strings (max 1000 chars).
</ParamField>

## Response

<ResponseField name="id" type="string">
  The unique identifier for the created task.
</ResponseField>


<RequestExample>

```python python
import requests

url = "https://api.browser-use.com/api/v1/run-task"

payload = {
    "task": "<string>",
    "secrets": {},
    "allowed_domains": ["<string>"],
    "save_browser_data": True,
    "structured_output_json": "<string>",
    "llm_model": "gpt-4o",
    "use_adblock": True,
    "use_proxy": True,
    "proxy_country_code": "us",
    "highlight_elements": True,
    "included_file_names": ["<string>"],
    "browser_viewport_width": 123,
    "browser_viewport_height": 123,
    "max_agent_steps": 123,
    "enable_public_share": True,
    "metadata": {
        "campaign": "q4-automation",
        "team": "marketing"
    }
}
headers = {
    "Authorization": "Bearer <token>",
    "Content-Type": "application/json"
}

response = requests.request("POST", url, json=payload, headers=headers)

print(response.text)
```

```bash cURL
curl --request POST \
  --url https://api.browser-use.com/api/v1/run-task \
  --header 'Authorization: Bearer <token>' \
  --header 'Content-Type: application/json' \
  --data '{
  "task": "<string>",
  "secrets": {},
  "allowed_domains": [
    "<string>"
  ],
  "save_browser_data": true,
  "structured_output_json": "<string>",
  "llm_model": "gpt-4o",
  "use_adblock": true,
  "use_proxy": true,
  "proxy_country_code": "us",
  "highlight_elements": true,
  "included_file_names": [
    "<string>"
  ],
  "browser_viewport_width": 123,
  "browser_viewport_height": 123,
  "max_agent_steps": 123,
  "enable_public_share": true,
  "metadata": {
    "campaign": "q4-automation",
    "team": "marketing"
  }
}'
```

```javascript javascript 
const options = {
  method: 'POST',
  headers: {Authorization: 'Bearer <token>', 'Content-Type': 'application/json'},
  body: '{"task":"<string>","secrets":{},"allowed_domains":["<string>"],"save_browser_data":true,"structured_output_json":"<string>","llm_model":"gpt-4o","use_adblock":true,"use_proxy":true,"proxy_country_code":"us","highlight_elements":true,"included_file_names":["<string>"],"browser_viewport_width":123,"browser_viewport_height":123,"max_agent_steps":123,"enable_public_share":true,"metadata":{"campaign":"q4-automation","team":"marketing"}}'
};

fetch('https://api.browser-use.com/api/v1/run-task', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

```
</RequestExample>

<ResponseExample>
    ```json 200
    {
      "id": "task_1234567890abcdef"
    }
    ```

    ```json 422
    {
    "detail": [
        {
        "loc": [
            "<string>"
        ],
        "msg": "<string>",
        "type": "<string>"
        }
    ]
    }
    ```
</ResponseExample>
