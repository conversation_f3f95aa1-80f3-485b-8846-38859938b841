---
title: "Get Task Output File"
api: "GET /api/v1/task/{task_id}/output-file/{file_name}"
description: "Returns a presigned URL for downloading a file from the task output files"
---

Returns a presigned URL for downloading a file from the task output files. This endpoint is useful for retrieving files that were generated or modified during task execution.

## Path Parameters

<ParamField path="task_id" type="string" required>
  ID of the task
</ParamField>
<ParamField path="file_name" type="string" required>
  Name of the output file
</ParamField>

## Response

<ResponseField name="download_url" type="string">
  A presigned URL for downloading the file.
</ResponseField>

<RequestExample>

```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

task_id = 'task_1234567890abcdef'
file_name = 'results.csv'

response = requests.get(f'{BASE_URL}/task/{task_id}/output-file/{file_name}', headers=HEADERS)
download_url = response.json()['download_url']

# Download the file
file_response = requests.get(download_url)
with open('downloaded_results.csv', 'wb') as file:
    file.write(file_response.content)

print("File downloaded successfully")
```

```bash curl
curl --request GET \
  --url https://api.browser-use.com/api/v1/task/{task_id}/output-file/{file_name} \
  --header 'Authorization: Bearer <token>'
```

</RequestExample>

<ResponseExample>

```json 200
{
  "download_url": "https://storage.browser-use.com/output-files/task_1234567890abcdef/results.csv?signature=..."
}
```

</ResponseExample> 
