---
title: "Get Task"
api: "GET /api/v1/task/{task_id}"
description: "Get comprehensive information about a task"
---

Returns comprehensive information about a task, including its current status, steps completed, output, and other metadata.

## Path Parameters
<ParamField path="task_id" type="string" required>
  ID of the task to retrieve
</ParamField>


## Response

<ResponseField name="id" type="string" required>
  The unique identifier for the task
</ResponseField>

<ResponseField name="task" type="string" required>
  The original task instructions
</ResponseField>

<ResponseField name="output" type="string | null" required>
  The final output or result from the task (if completed)
</ResponseField>

<ResponseField name="status" type="enum<string>" required>
  Enumeration of possible task states.

  - created: Task is initialized but not yet started
  - running: Task is currently executing
  - finished: Task has completed successfully
  - stopped: Task was manually stopped
  - paused: Task execution is temporarily paused
  - failed: Task encountered an error and could not complete
  
  Available options: `created`, `running`, `finished`, `stopped`, `paused`, `failed`
</ResponseField>

<ResponseField name="created_at" type="string" required>
  ISO 8601 timestamp of when the task was created
</ResponseField>

<ResponseField name="steps" type="TaskStepResponse · object[]" required>
  List of task steps with execution details
  
  <Expandable title="Hide child attributes">
    <ResponseField name="id" type="string" required>
      Unique identifier for the step
    </ResponseField>
    <ResponseField name="step" type="integer" required>
      Step number in the execution sequence
    </ResponseField>
    <ResponseField name="evaluation_previous_goal" type="string" required>
      Assessment of the previous goal's completion
    </ResponseField>
    <ResponseField name="next_goal" type="string" required>
      Description of what the next step aims to achieve
    </ResponseField>
    <ResponseField name="url" type="string" required>
      URL of the page where the step was executed
    </ResponseField>
  </Expandable>
</ResponseField>

<ResponseField name="live_url" type="string | null">
  URL to view live task execution. To preview the url you can directly integrate it in `<iframe>` tag. For example:
  `<iframe src={live_url} width="600" height="450"></iframe>`
  Which will display the task execution and allows you to control the agent live. It is pure VNC implementation.
</ResponseField>

<ResponseField name="finished_at" type="string | null">
  ISO 8601 timestamp of when the task finished (if completed)
</ResponseField>

<ResponseField name="browser_data" type="object | null">
  Browser session data (if save_browser_data was enabled)
  
  This field is only available if save_browser_data is set to True in the request.
  
  <Expandable title="Hide child attributes">
    <ResponseField name="browser_data.cookies" type="Cookies · object[]" required>
      List of cookies from the browser session
    </ResponseField>
  </Expandable>
</ResponseField>

<ResponseField name="user_uploaded_files" type="string[] | null">
  List of files uploaded by the user for this task
</ResponseField>

<ResponseField name="output_files" type="string[] | null">
  List of files generated during task execution
</ResponseField>

<ResponseField name="public_share_url" type="string | null">
  Public URL for sharing the task (if public sharing was enabled)
</ResponseField>

<ResponseField name="metadata" type="object | null">
  Custom metadata key-value pairs associated with the task
</ResponseField>

<RequestExample>

```python python
import requests

url = "https://api.browser-use.com/api/v1/task/{task_id}"

headers = {"Authorization": "Bearer <token>"}

response = requests.request("GET", url, headers=headers)

print(response.text)
```

```bash curl
curl --request GET \
  --url https://api.browser-use.com/api/v1/task/{task_id} \
  --header 'Authorization: Bearer <token>'
```

```javascript javascript 
const options = {method: 'GET', headers: {Authorization: 'Bearer <token>'}};

fetch('https://api.browser-use.com/api/v1/task/{task_id}', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));
```
</RequestExample>

<ResponseExample>
    ```json 200
    {
  "id": "<string>",
  "task": "<string>",
  "live_url": "<string>",
  "output": "<string>",
  "status": "created",
  "created_at": "2023-11-07T05:31:56Z",
  "finished_at": "2023-11-07T05:31:56Z",
  "steps": [
    {
      "id": "<string>",
      "step": 123,
      "evaluation_previous_goal": "<string>",
      "next_goal": "<string>",
      "url": "<string>"
    }
  ],
  "browser_data": {
    "cookies": [
      {}
    ]
  },
  "user_uploaded_files": [
    "<string>"
  ],
  "output_files": [
    "<string>"
  ],
  "public_share_url": "<string>",
  "metadata": {
    "campaign": "q4-automation",
    "team": "marketing"
  }
}
    ```

    ```json 422
    {
    "detail": [
        {
        "loc": [
            "<string>"
        ],
        "msg": "<string>",
        "type": "<string>"
        }
    ]
    }
    ```
</ResponseExample>
