---
title: "Create Browser Profile"
api: "POST /api/v1/browser-profiles"
description: "Create a new browser profile with custom settings for ad blocking, proxy usage, and viewport dimensions."
---

Create a new browser profile with custom settings for ad blocking, proxy usage, and viewport dimensions.

<Note>
  Pay-as-you-go users can only have one profile. Subscription users can create
  multiple profiles.
</Note>

### Request Body

<ParamField body="profile_name" type="string" required>
  Name of the browser profile
</ParamField>
<ParamField body="description" type="string" default="">
  Description of the profile
</ParamField>
<ParamField body="persist" type="boolean" default="true">
  Save cookies, local storage, and session data between tasks
</ParamField>
<ParamField body="ad_blocker" type="boolean" default="true">
  Block ads and popups during automated tasks
</ParamField>
<ParamField body="proxy" type="boolean" default="true">
  Route traffic through mobile proxies for better stealth
</ParamField>
<ParamField body="proxy_country_code" type="string" default="US">
  Country code for the proxy
</ParamField>
<ParamField body="browser_viewport_width" type="integer" default="1280">
  Browser viewport width in pixels
</ParamField>
<ParamField body="browser_viewport_height" type="integer" default="960">
  Browser viewport height in pixels
</ParamField>

### Response

<ResponseField name="profile_id" type="string">
  Unique identifier for the created browser profile
</ResponseField>
<ResponseField name="profile_name" type="string">
  Name of the browser profile
</ResponseField>
<ResponseField name="description" type="string">
  Description of the profile
</ResponseField>
<ResponseField name="persist" type="boolean">
  Save cookies, local storage, and session data between tasks
</ResponseField>
<ResponseField name="ad_blocker" type="boolean">
  Block ads and popups during automated tasks
</ResponseField>
<ResponseField name="proxy" type="boolean">
  Route traffic through mobile proxies for better stealth
</ResponseField>
<ResponseField name="proxy_country_code" type="string">
  Country code for the proxy
</ResponseField>
<ResponseField name="browser_viewport_width" type="integer">
  Browser viewport width in pixels
</ResponseField>
<ResponseField name="browser_viewport_height" type="integer">
  Browser viewport height in pixels
</ResponseField>

<RequestExample>
```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

profile_data = {
"profile_name": "Default Profile",
"description": "Main automation profile",
"persist": true,
"ad_blocker": true,
"proxy": true,
"proxy_country_code": "US",
"browser_viewport_width": 1280,
"browser_viewport_height": 960
}

response = requests.post(f'{BASE_URL}/browser-profiles', headers=HEADERS, json=profile_data)
profile = response.json()
print(profile)

````

```bash curl
curl --request POST \
  --url https://api.browser-use.com/api/v1/browser-profiles \
  --header 'Authorization: Bearer <token>' \
  --header 'Content-Type: application/json' \
  --data '{"profile_name": "Default Profile", "description": "Main automation profile", "persist": true, "ad_blocker": true, "proxy": true, "proxy_country_code": "US", "browser_viewport_width": 1280, "browser_viewport_height": 960}'
````

</RequestExample>

<ResponseExample>
```json 200
{
  "profile_id": "profile_1234567890abcdef",
  "profile_name": "Default Profile",
  "description": "Main automation profile",
  "persist": true,
  "ad_blocker": true,
  "proxy": true,
  "proxy_country_code": "US",
  "browser_viewport_width": 1280,
  "browser_viewport_height": 960
}
```
</ResponseExample>
