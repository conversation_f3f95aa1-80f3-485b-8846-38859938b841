---
title: "Update Browser Profile"
api: "PUT /api/v1/browser-profiles/{profile_id}"
description: "Update a browser profile with partial updates. Only the fields you want to change need to be included."
---

Update a browser profile with partial updates. Only the fields you want to change need to be included.

### Path Parameters

<ParamField path="profile_id" type="string" required>
  ID of the browser profile to update
</ParamField>

### Request Body

<ParamField body="profile_name" type="string">
  Name of the browser profile
</ParamField>
<ParamField body="description" type="string">
  Description of the profile
</ParamField>
<ParamField body="persist" type="boolean">
  Save cookies, local storage, and session data between tasks
</ParamField>
<ParamField body="ad_blocker" type="boolean">
  Block ads and popups during automated tasks
</ParamField>
<ParamField body="proxy" type="boolean">
  Route traffic through mobile proxies for better stealth
</ParamField>
<ParamField body="proxy_country_code" type="string">
  Country code for the proxy
</ParamField>
<ParamField body="browser_viewport_width" type="integer">
  Browser viewport width in pixels
</ParamField>
<ParamField body="browser_viewport_height" type="integer">
  Browser viewport height in pixels
</ParamField>

### Response

<ResponseField name="profile_id" type="string">
  Unique identifier for the updated browser profile
</ResponseField>
<ResponseField name="profile_name" type="string">
  Name of the browser profile
</ResponseField>
<ResponseField name="description" type="string">
  Description of the profile
</ResponseField>
<ResponseField name="persist" type="boolean">
  Save cookies, local storage, and session data between tasks
</ResponseField>
<ResponseField name="ad_blocker" type="boolean">
  Block ads and popups during automated tasks
</ResponseField>
<ResponseField name="proxy" type="boolean">
  Route traffic through mobile proxies for better stealth
</ResponseField>
<ResponseField name="proxy_country_code" type="string">
  Country code for the proxy
</ResponseField>
<ResponseField name="browser_viewport_width" type="integer">
  Browser viewport width in pixels
</ResponseField>
<ResponseField name="browser_viewport_height" type="integer">
  Browser viewport height in pixels
</ResponseField>

<RequestExample>
```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

profile_id = 'profile_1234567890abcdef'
update_data = {"ad_blocker": False}
response = requests.put(f'{BASE_URL}/browser-profiles/{profile_id}', headers=HEADERS, json=update_data)
print(response.json())

````

```bash curl
curl --request PUT \
  --url https://api.browser-use.com/api/v1/browser-profiles/profile_1234567890abcdef \
  --header 'Authorization: Bearer <token>' \
  --header 'Content-Type: application/json' \
  --data '{"ad_blocker": false}'
````

</RequestExample>

<ResponseExample>
```json 200
{
  "profile_id": "profile_1234567890abcdef",
  "profile_name": "Default Profile",
  "description": "Main automation profile",
  "persist": true,
  "ad_blocker": false,
  "proxy": true,
  "proxy_country_code": "US",
  "browser_viewport_width": 1280,
  "browser_viewport_height": 960
}
```
</ResponseExample>
