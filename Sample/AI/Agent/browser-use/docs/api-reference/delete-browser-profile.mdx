---
title: "Delete Browser Profile"
api: "DELETE /api/v1/browser-profiles/{profile_id}"
description: "Deletes a browser profile. This will remove the profile and all associated browser data."
---

Deletes a browser profile. This will remove the profile and all associated browser data. This action cannot be undone!

### Path Parameters

<ParamField path="profile_id" type="string" required>
  ID of the browser profile to delete
</ParamField>

### Response

A successful deletion returns an empty object.

<RequestExample>
```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

profile_id = 'profile_1234567890abcdef'
response = requests.delete(f'{BASE_URL}/browser-profiles/{profile_id}', headers=HEADERS)
print(response.json())

````

```bash curl
curl --request DELETE \
  --url https://api.browser-use.com/api/v1/browser-profiles/profile_1234567890abcdef \
  --header 'Authorization: Bearer <token>'
````

</RequestExample>

<ResponseExample>
  ```json 200
  {}
  ```
</ResponseExample>
