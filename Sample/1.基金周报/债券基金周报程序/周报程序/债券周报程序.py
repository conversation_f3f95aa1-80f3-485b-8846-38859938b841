﻿# -*- coding:utf-8 -*-
# <AUTHOR> gyrx-liyx
# @Time : 2023/1/18 14:58

import pandas as pd
import numpy as np
import datetime as dt
import pickle
from dateutil.relativedelta import relativedelta
from scipy import stats
import matplotlib.pyplot as plt
import os
import warnings
from chart import fundDuration
import chart as ct
from WindPy import w
import seaborn as sns
import urllib3
from json import dumps,loads
from plot import *
import copy
import cx_Oracle
cx_Oracle.init_oracle_client(os.path.join('.', 'tools', 'clt64', 'instantclient_11_2'))
from pyecharts.charts import Page
from pyecharts.globals import CurrentConfig
from matplotlib.font_manager import FontProperties
from scipy.stats import spearmanr
# CurrentConfig.ONLINE_HOST = "Z:/基金精选/4.0 定期报告/pyecharts-assets/assets/"
oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind', encoding="UTF-8")
warnings.filterwarnings("ignore")

def printtime(t):
    if t!=1:
        print('-----' * 5+'花费%s秒'%(dt.datetime.now()-t))
        t=dt.datetime.now()
    else:
        print('—' * 25+'共费%s秒'%(dt.datetime.now()-t0))
    return t
t0=dt.datetime.now()
t=t0
#%% 基础变量设定
w.start()
today = dt.datetime.strptime('20241222','%Y%m%d')   #每周日
today_str = today.strftime('%Y-%m-%d')
work_day = w.tdaysoffset(0, today_str, "Period=D").Data[0][0].strftime('%Y-%m-%d')#计算离所选日期最近的工作日
rpt_date = '20240930'    #日期需要定期调整
datei_dict = {'20230430':'20230430','20230531':'20230430','20230630':'20230430','20230731':'20230731','20230831':'20230731','20230930':'20230731','20231031': '20231031', '20231130': '20231031', '20231231': '20231031', '20240131': '20240131',
              '20240229': '20240131','20240331': '20240131','20240430': '20240430','20240531': '20240430','20240630': '20240430','20240731': '20240731','20240831': '20240731','20240930': '20240731','20241031': '20241031','20241130': '20241031','20241231': '20241031'}#定期更新
#%% 基金数据提取
date_i_bf = (dt.datetime.strptime( (pd.to_datetime(today.strftime('%Y%m%d'), format='%Y%m%d') + pd.offsets.MonthEnd(0)).strftime('%Y%m%d'), '%Y%m%d').replace(day=1)- relativedelta(days=1)).strftime('%Y%m%d')
date_i_adjust=datei_dict[date_i_bf]
fund = pd.read_excel('基础数据/季度筛选结果明细_{0}.xlsx'.format(date_i_adjust[:6]), sheet_name = 'ag-grid',index_col = 2) #季度基金池更新之后需要更新
cal_stock_value=pd.read_excel('基础数据/cal_stock_value.xlsx',index_col = 0).set_index('是否关注') #季报更新后需要更新
cal_stock_value['日期']=cal_stock_value['日期'].astype(str)
# fund = fund[~fund['银河证券三级分类'].str.contains('非A类')]
fund = fund[fund['公募FOF基金池级别'] != '禁买']
fund = fund[~fund['资产细分V'].isna()]
fund_pool=fund[fund['公募FOF基金池级别']=='基础|重点|核心'].index.tolist()#基础池和核心池产品列表

last1w = w.tdaysoffset(-4, today, "Period=D").Data[0][0].strftime('%Y-%m-%d') #5个交易日
last1m = (today + relativedelta(months=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')
last3m = (today + relativedelta(months=-3) + relativedelta(days=1)).strftime('%Y-%m-%d')
last6m = (today + relativedelta(months=-6) + relativedelta(days=1)).strftime('%Y-%m-%d')
ytd = str(today.year) +'-01-01'
last1y = (today + relativedelta(years=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')
for i in range(int(np.ceil(len(fund.index)/500))):
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'是否初始基金'] =w.wss(fund.index.tolist()[i*500:(i+1)*500], "fund_initial").Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'基金经理'] = w.wss(fund.index.tolist()[i*500:(i+1)*500],"fund_fundmanager").Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'基金管理人'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "fund_corp_fundmanagementcompany").Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'办公地址'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "fund_corpoffice").Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'去年基金规模'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "netasset_total","unit=1;tradeDate=" + ytd).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'基金规模'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "netasset_total","unit=1;tradeDate=" + today_str).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'近一周'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'近一月'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'近三月'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'年初至今'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'近一年'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
    fund.loc[fund.index.tolist()[i*500:(i+1)*500],'去年'] = w.wss(fund.index.tolist()[i*500:(i+1)*500], "return_y","tradeDate=" + last1y).Data[0]
fund=fund[fund['是否初始基金']=='是']

ts = pd.date_range(start = today + relativedelta(years=-1), periods=13, freq='1M')
ts = pd.Series(ts).sort_values(ascending=False).tolist()
for i in ts:
    for j in range(int(np.ceil(len(fund.index) / 500))):
        fund.loc[fund.index.tolist()[j*500:(j+1)*500],i.strftime('%Y-%m')] = w.wss(fund.index.tolist()[j*500:(j+1)*500], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0]

print('1.0 基金数据提取完毕')
t=printtime(t)
#%% 市场回顾
stock = ['标准股票型基金(A类)','港股通标准股票型基金(A类)']
blend =['港股通偏股型基金(A类)','偏股型基金(股票上下限60%-95%)(A类)','偏股型基金(股票上限80%)(A类)','偏股型基金(股票上限95%)(A类)']
bond_blend=['普通偏债型基金(股票上限高于30%)(A类)','普通偏债型基金(股票上限不高于30%)(A类)','标准偏债型基金(股票上下限10%-30%)(A类)']
long_bond=['长期纯债债券型基金(A类)']
short_bond=['短期纯债债券型基金(A类)','中短期纯债债券型基金(A类)']
absolute_value=['对冲策略绝对收益目标基金(A类)','灵活策略绝对收益目标基金(A类)']
bond_index=['利率债指数债券型基金(A类)','信用债指数债券型基金(A类)','债券ETF基金']
second_bond=['普通债券型基金(二级)(A类)']
convert_bond=['可转换债券型基金(A类)']
others = ['QDII债券型基金(A类)','黄金ETF基金','商品期货ETF基金']
rtn_summary = pd.DataFrame(columns = ['近一周','近一月','近三月','年初至今','近一年'])
rtn_monthly = pd.DataFrame(columns = [i.strftime('%Y-%m') for i in ts])
df1 = fund[fund['银河证券三级分类'].isin(stock)]
#针对不同类型的基金计算表现的分位数
rtn_summary.loc['股票基金 25"'] = df1[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['股票基金 50"'] = df1[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['股票基金 75"'] = df1[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['股票基金'] = df1[rtn_monthly.columns].quantile(0.5)

df6 = fund[fund['银河证券三级分类'].isin(blend)]
rtn_summary.loc['偏股基金 25"'] = df6[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['偏股基金 50"'] = df6[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['偏股基金 75"'] = df6[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['偏股基金'] = df6[rtn_monthly.columns].quantile(0.5)

df3 = fund[fund['银河证券三级分类'].isin(short_bond)]
rtn_summary.loc['短债基金 25"'] = df3[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['短债基金 50"'] = df3[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['短债基金 75"'] = df3[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['短债基金'] = df3[rtn_monthly.columns].quantile(0.5)

df2 = fund[fund['银河证券三级分类'].isin(long_bond)]
rtn_summary.loc['中长期纯债基金 25"'] = df2[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['中长期纯债基金 50"'] = df2[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['中长期纯债基金 75"'] = df2[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['中长期纯债基金'] = df2[rtn_monthly.columns].quantile(0.5)

df5 = fund[fund['银河证券三级分类'].isin(bond_index)]
rtn_summary.loc['债券指数基金 25"'] = df5[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['债券指数基金 50"'] = df5[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['债券指数基金 75"'] = df5[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['债券指数基金'] = df5[rtn_monthly.columns].quantile(0.5)

df4 = fund[fund['银河证券三级分类'].isin(absolute_value)]
rtn_summary.loc['绝对收益基金 25"'] = df4[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['绝对收益基金 50"'] = df4[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['绝对收益基金 75"'] = df4[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['绝对收益基金'] = df4[rtn_monthly.columns].quantile(0.5)

df4 = fund[fund['银河证券三级分类'].isin(second_bond)]
rtn_summary.loc['二级债基 25"'] = df4[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['二级债基 50"'] = df4[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['二级债基 75"'] = df4[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['二级债基'] = df4[rtn_monthly.columns].quantile(0.5)

df4 = fund[fund['银河证券三级分类'].isin(convert_bond)]
rtn_summary.loc['可转债基金 25"'] = df4[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['可转债基金 50"'] = df4[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['可转债基金 75"'] = df4[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['可转债基金'] = df4[rtn_monthly.columns].quantile(0.5)


df4 = fund[fund['银河证券三级分类'].isin(bond_blend)]
rtn_summary.loc['偏债型基金 25"'] = df4[rtn_summary.columns].quantile(0.75)
rtn_summary.loc['偏债型基金 50"'] = df4[rtn_summary.columns].quantile(0.5)
rtn_summary.loc['偏债型基金 75"'] = df4[rtn_summary.columns].quantile(0.25)
rtn_monthly.loc['偏债型基金'] = df4[rtn_monthly.columns].quantile(0.5)

for i in others:
    df7 = fund[fund['银河证券三级分类'] == i]
    if i == '黄金ETF基金' or '商品期货ETF基金':
        rtn_summary.loc[i[:-5]+' 25"'] = df7[rtn_summary.columns].quantile(0.75)
        rtn_summary.loc[i[:-5]+' 50"'] = df7[rtn_summary.columns].quantile(0.5)
        rtn_summary.loc[i[:-5]+' 75"'] = df7[rtn_summary.columns].quantile(0.25)
        rtn_monthly.loc[i[:-5]] = df7[rtn_monthly.columns].quantile(0.5)
    else:
        rtn_summary.loc[i[:-2]+' 25"'] = df7[rtn_summary.columns].quantile(0.75)
        rtn_summary.loc[i[:-2]+' 50"'] = df7[rtn_summary.columns].quantile(0.5)
        rtn_summary.loc[i[:-2]+' 75"'] = df7[rtn_summary.columns].quantile(0.25)
        rtn_monthly.loc[i[:-2]] = df7[rtn_monthly.columns].quantile(0.5).fillna(0)
rtn_summary = rtn_summary.round(2)
rtn_monthly = rtn_monthly.round(2)
rtn_monthly.loc['QDII债券型基','2024-03']=0

df_graph = pd.DataFrame()
df_graph.loc['中证800', '近一周'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近一月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近三月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '年初至今'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近一年'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0][0]

df_graph.loc['中债新综合财富', '近一周'] = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中债新综合财富', '近一月'] = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中债新综合财富', '近三月'] = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中债新综合财富', '年初至今'] = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中债新综合财富', '近一年'] = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0][0]
df_graph = pd.concat([df_graph, rtn_summary[rtn_summary.index.str.contains('50"')][['近一周','近一月','近三月','年初至今','近一年']]]).round(2)
rtn_summary['分位']=rtn_summary.index.str[-4:]
rtn_summary.index = rtn_summary.index.str[:-4]
# 将列'分位'提到最前面成为第一列
rtn_summary.insert(0, '分位', rtn_summary.pop('分位'))
plt_monthly_return(rtn_monthly,'各类基金月度收益情况(' + work_day + ')','1.1.6月度收益')
fund_percentile(rtn_summary.iloc[6:,:],'市场表现分位数(' + work_day + ')','1.1.1市场表现分位数')
new_rtn_summary=rtn_summary[rtn_summary['分位'].str.contains('50')].drop(columns=['分位'])
plt_fund_performance(new_rtn_summary.iloc[:,:2][::-1],'市场表现中位数(' + work_day + ')','1.1.0市场表现中位数')

print('2.0 市场回顾计算完成')
t=printtime(t)
#%% 基金公司收益
fund=fund[fund['管理方式VI'] == '主动管理']
amc_rank = dict()
amc_rank['区域收益'] = pd.DataFrame()
for i in ['北京','上海','广东']:
    df8 = fund[(fund['办公地址'].str.contains(i)) & (fund['资产细分V'].isin(['一级债基','低含权','中含权','高含权','转债','长久期','中短久期']))]
    df8 = df8[~df8['银河证券三级分类'].str.contains('货币市场基金')]
    amc_rank['区域收益'].loc[i, '产品数量'] = df8['年初至今'].count()
    amc_rank['区域收益'].loc[i, '25分位'] = df8['年初至今'].quantile(0.75)
    amc_rank['区域收益'].loc[i, '50分位'] = df8['年初至今'].quantile(0.5)
    amc_rank['区域收益'].loc[i, '75分位'] = df8['年初至今'].quantile(0.25)
    amc_rank['区域收益'].loc[i, '规模加权'] = (df8['基金规模'] * df8['年初至今']).sum() / df8['基金规模'].sum()


for i in ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债']:
    df10 = fund[fund['资产细分V'] == i]
    df10 = df10[~df10['银河证券三级分类'].str.contains('货币市场基金')]
    amc_rank[i] = pd.DataFrame()
    amc_rank[i]['产品数量'] = df10.groupby('基金管理人')['基金规模'].count()
    amc_rank[i]['产品规模'] = df10.groupby('基金管理人')['基金规模'].sum() / 1e8
    amc_rank[i]['中位数收益'] = df10.groupby('基金管理人')['年初至今'].median()
    temp=df10.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
    amc_rank[i]['规模加权收益'] = df10.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
    amc_rank[i]['去年中位数收益'] = df10.groupby('基金管理人')['去年'].median()
    amc_rank[i]['去年规模加权收益'] = df10.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
    amc_rank[i] = amc_rank[i].sort_values(by = '产品规模', ascending=False).round(2)

for i in amc_rank:
    try:
        amc_rank[i].index.name='基金管理人'
        if '工银瑞信基金' in amc_rank[i].iloc[:25,:].index.tolist():
            scatter_manager(amc_rank[i].reset_index().iloc[:25,:], '中位数收益', '去年中位数收益', '中位数收益-'+i+'(' + work_day + ')','1.3.0中位数收益-'+i)
            scatter_manager(amc_rank[i].reset_index().iloc[:25,:], '规模加权收益', '去年规模加权收益', '规模加权收益-' + i + '(' + work_day + ')','1.3.1规模加权收益-'+i)
        else:
            first_24_rows = amc_rank[i].iloc[:24]
            gyrx_rows= amc_rank[i].loc[amc_rank[i].index.str.contains('工银瑞信基金')]
            amc_rank[i] = pd.concat([first_24_rows, gyrx_rows])
            scatter_manager(amc_rank[i].reset_index().iloc[:25, :], '中位数收益', '去年中位数收益',
                            '中位数收益-' + i + '(' + work_day + ')', '1.3.0中位数收益-' + i)
            scatter_manager(amc_rank[i].reset_index().iloc[:25, :], '规模加权收益', '去年规模加权收益',
                            '规模加权收益-' + i + '(' + work_day + ')', '1.3.1规模加权收益-' + i)
    except:
        continue
table_amc(amc_rank,'各公司基金细分(' + work_day + ')','各公司基金细分收益')

print('3.0 基金公司收益计算完成')
t=printtime(t)
#%% FOF基金回顾
#获取FOF基金的银河三级分类
try:
    url = 'http://192.168.105.63/GetFndLevelClassify'
    kw = {'username':'gyrx','passwd':'4E4AB38D17E274B0D2D6A846AE4393E7'}
    http = urllib3.PoolManager()
    r = http.request('post', url, body = dumps(kw))
    fof_class = pd.DataFrame(loads(r.data.decode())['data'])
    fof_class.index = [x+'.OF' for x in fof_class['FUNDCODE']]
    fof_class['FUNDCLOSEDATE'] = [dt.datetime.strptime(x[0:10],"%Y-%m-%d") for x in fof_class['FUNDCLOSEDATE']]
    fof_class['ESTABDATE'] = [dt.datetime.strptime(x[0:10],"%Y-%m-%d") for x in fof_class['ESTABDATE']]
    fof_class = fof_class[(fof_class['FUNDCLOSEDATE']>dt.datetime.strptime(today_str,"%Y-%m-%d"))
                          &(fof_class['ESTABDATE']<=dt.datetime.strptime(today_str,"%Y-%m-%d"))
                          &(fof_class['LEVEL3NAME'].str.contains('FOF'))]
    fof_class = fof_class.loc[:,['FUNDSNAME','F_FNAME','LEVEL1NAME','LEVEL2NAME','LEVEL3NAME']]
    fof_class.columns = ['简称','基金管理人全称','银河证券一级分类','银河证券二级分类','银河证券三级分类']
    fof_class['全称']=w.wss(fof_class.index.tolist(), "fund_fullname").Data[0]
    fof_class=fof_class.reset_index().set_index('全称')
    fof_class.to_excel('基础数据/银河证券分类.xlsx')
except:
    fof_class=pd.read_excel('基础数据/银河证券分类.xlsx',index_col=0)
    fof_class.index.name='全称'

fof_list = w.wset("sectorconstituent","date=" + today_str + ";sectorid=1000041489000000")
fof_fund = pd.DataFrame({'代码':fof_list.Data[1],'简称':fof_list.Data[2],'全称':w.wss(fof_list.Data[1], "fund_fullname").Data[0],
                         '是否初始基金':w.wss(fof_list.Data[1], "fund_initial").Data[0],'成立日':w.wss(fof_list.Data[1],"fund_setupdate").Data[0]}).set_index('全称')
fof_fund = fof_fund[(fof_fund['是否初始基金'] == '是')&(fof_fund['成立日']<ytd)]#只选取今年以前成立的基金
fof_fund=fof_fund.join(fof_class['银河证券三级分类'])
fof_fund=fof_fund.reset_index().set_index('代码')
fof_fund=fof_fund[~fof_fund['银河证券三级分类'].str.contains('非A类').astype(bool)]
fof_all = ct.fof_chart(fof_fund.index.to_list(), today_str, last1w,last1m, last3m, ytd, last1y)
fof=dict()
for i in fof_fund['银河证券三级分类'].drop_duplicates().tolist():
    temp=fof_fund[fof_fund['银河证券三级分类'] == i]
    temp = pd.concat([fof_all.loc[temp[temp['简称'].str.contains('工银')].index],
                        fof_all.loc[temp[~temp['简称'].str.contains('工银')].index].sort_values(by='估算仓位',ascending=False)])
    temp = ct.add_quantile(temp)
    fof[i[:-4]] = temp
cal_day = w.tdaysoffset(-2, today_str, "Period=D").Data[0][0].strftime('%Y-%m-%d')#FOF基金的收益截止日会提前2个工作日
for i in fof.keys():
    df=fof[i]
    correlation, p_value = spearmanr(df['收益_1W'][:-3], df['收益_YTD'][:-3])
    correlation=str(correlation.round(2))
    df=df.reset_index()
    df['简称'].fillna(df.iloc[:, 0], inplace=True)
    df.drop(df.columns[0], axis=1, inplace=True)
    if len(df.iloc[:, 0])<35:
        # 绘制表格
        fof_table(df, df,i + '收益情况(' + cal_day + ')'+ '(相关性' + correlation + ')', '1.4'+i)
    else:
        num=len(df.index)//35
        for num_i in range(0,num):
            df1 = df.iloc[num_i*35:(num_i+1)*35, :]
            fof_table(df, df1, i + '收益情况(' + cal_day + ')' + '(相关性' + correlation + ')', '1.4'+i + str(num_i+1))
        df1 = df.iloc[(num_i + 1) * 35:, :]
        fof_table(df, df1, i + '收益情况(' + cal_day + ')' + '(相关性' + correlation + ')', '1.4'+i + str(num_i + 2))

print('4.0 FOF基金回顾计算完成')
t=printtime(t)

#%% 债券基金池收益计算

start_day = w.tdaysoffset(-4, today, "Period=D").Data[0][0].strftime('%Y%m%d')
end_day = today.strftime('%Y%m%d')

end_day_month = (pd.to_datetime(end_day, format='%Y%m%d') + pd.offsets.MonthEnd(0)).strftime('%Y%m%d')
end_month_date = pd.date_range(start=start_day,end=end_day_month,freq='M')
end_month_date = [i.strftime('%Y%m%d') for i in end_month_date]

def get_fund_nav(startDate, endDate):
    # "获取基金净值"
    sqlcode = """
    SELECT PRICE_DATE as pricedt, F_INFO_WINDCODE as fundcode, F_NAV_ADJUSTED as adjnav
    FROM winddf.ChinaMutualFundNAV
    where PRICE_DATE between '%s' and '%s'
    order by F_INFO_WINDCODE, PRICE_DATE
    """% (startDate, endDate)

    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col=['PRICEDT', 'FUNDCODE'])

    return tDATA

#提取基金净值
try:
    with open('基础数据/基金净值数据.pkl', "rb") as fp:
        fund_value_data = pickle.load(fp)
except:#第一次跑没这个文件
        fund_value_data=pd.DataFrame()

fund_value_data1 = get_fund_nav(start_day, end_day)
fund_value_data1 = fund_value_data1.ADJNAV.unstack()
fund_value_data1.index.name='日期'
fund_value_data1=fund_value_data1.reset_index().melt(id_vars='日期', var_name='基金代码', value_name='单位净值')
fund_value_data_new=pd.DataFrame()
for date_i in end_month_date:
    try:
        date_i_bf = (dt.datetime.strptime(date_i, '%Y%m%d').replace(day=1)- relativedelta(days=1)).strftime('%Y%m%d')
        date_i_adjust=datei_dict[date_i_bf]
        fundpool=pd.read_excel('基础数据/季度筛选结果明细_{0}.xlsx'.format(date_i_adjust[:6]), sheet_name = 'ag-grid',index_col=2).drop(columns=['基金代码'])

        fund_initial=pd.DataFrame()
        for i in range(0,len(fundpool.index.tolist())//500):
            fund0 = pd.DataFrame({'基金代码':fundpool.index.tolist()[500*i:500*(1+i)],'是否初始基金':w.wss(fundpool.index.tolist()[500*i:500*(1+i)], "fund_initial").Data[0]}).set_index('基金代码')
            fund_initial=pd.concat([fund_initial,fund0],axis=0)
        if len(fundpool.index.tolist())%500 > 0:
            fund0 = pd.DataFrame({'基金代码':fundpool.index.tolist()[500*(1+i):],'是否初始基金':w.wss(fundpool.index.tolist()[500*(1+i):], "fund_initial").Data[0]}).set_index('基金代码')
            fund_initial=pd.concat([fund_initial,fund0],axis=0)
        fundpool=fundpool.join(fund_initial)
        fundpool = fundpool[(fundpool['是否初始基金'] == '是') & (fundpool['管理方式VI'] == '主动管理')]
        fund_value_data_i=fund_value_data1[(fund_value_data1['日期']>=(date_i[:6]+'01'))&(fund_value_data1['日期']<date_i)].set_index('基金代码')

        fund_value_data_i_code=list(set(fund_value_data_i.index.tolist()))
        fundpool.index.name='基金代码'
        fundpool=fundpool.reset_index()
        for i in range(len(fundpool)):
            if fundpool.loc[i,'基金代码'][:-3]+'.SZ' in fund_value_data_i_code:
                fundpool.loc[i,'基金代码']=fundpool.loc[i,'基金代码'][:-3]+'.SZ'
            elif fundpool.loc[i,'基金代码'][:-3]+'.SH' in fund_value_data_i_code:
                fundpool.loc[i,'基金代码']=fundpool.loc[i,'基金代码'][:-3]+'.SH'
            else:
                fundpool.loc[i,'基金代码']=fundpool.loc[i,'基金代码'][:-3]+'.OF'

        fundpool = fundpool.drop_duplicates().set_index('基金代码')
        kemaipool_list=list(set(fundpool[fundpool['公募FOF基金池级别']=='可买'].index.tolist())&set(fund_value_data_i.index.tolist()))
        jichupool_list=list(set(fundpool[fundpool['公募FOF基金池级别']=='基础'].index.tolist())&set(fund_value_data_i.index.tolist()))
        zhongdian_list=list(set(fundpool[fundpool['公募FOF基金池级别']=='重点'].index.tolist())&set(fund_value_data_i.index.tolist()))
        fund_value_data_i.loc[kemaipool_list,'基金池类型']='可买池'
        fund_value_data_i.loc[jichupool_list,'基金池类型']='基础池'
        fund_value_data_i.loc[zhongdian_list,'基金池类型']='重点池'
        fund_value_data_i=fund_value_data_i.merge(fundpool[['资产类别I','资产地区II','资产类属III','资产板块IV','资产细分V']],left_index=True,right_index=True)
        fund_value_data_new=pd.concat([fund_value_data_new,fund_value_data_i],axis=0)
    except:
        fund_value_data_new=pd.DataFrame()
fund_value_data=pd.concat([fund_value_data,fund_value_data_new],axis=0).drop_duplicates()

with open('基础数据/基金净值数据.pkl', "wb") as fp:
    pickle.dump(fund_value_data, fp)
#根据基金净值计算基金池的收益表现
label_lst = ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债']
level_lst = ['可买池', '基础池', '重点池']
date_lst = {'1W': last1w, '1M': last1m, '3M': last3m, '6M': last6m, 'YTD': ytd, '1Y': last1y}
dict_graph = dict()
# 计算各只基金的日收益率
def calculate_daily_return(group):
    group.sort_values(by='日期', inplace=True)
    group.dropna(subset=['单位净值'], inplace=True)
    group['日收益率'] = group['单位净值'].pct_change()
    group['日收益率'] = group['日收益率'].fillna(0)
    return group
fund_value_data['日期'] = pd.to_datetime(fund_value_data['日期'])
grouped = fund_value_data.groupby(['基金代码'])
df_fund_returns = grouped.apply(calculate_daily_return)
df_fund_returns.reset_index(level=0, drop=True, inplace=True)
# 针对异常情况的处理
grouped = df_fund_returns.groupby(['日期', '资产细分V'])
df_complete_pools = df_fund_returns
for i_name, i_df in grouped:
    if i_name[1] not in label_lst:
        continue
    num_zhongdian = len(i_df[i_df['基金池类型'] == '重点池'].index)
    num_jichu = len(i_df[i_df['基金池类型'] == '基础池'].index)
    num_kemai = len(i_df[i_df['基金池类型'] == '可买池'].index)
    if num_jichu == 0 and num_zhongdian > 0:
        # 异常情况1：有重点池且无基础池时，用重点池代替（例如202310周期）
        new_df = i_df[i_df['基金池类型'] == '重点池']
        new_df.loc[:, '基金池类型'] = '基础池'
        df_complete_pools = pd.concat([df_complete_pools, new_df], axis=0)
    elif num_kemai == 0 and num_jichu > 0:
        # 异常情况2：有基础池、但没有可买池，可买池用基础池代替
        new_df = i_df[i_df['基金池类型'] == '基础池']
        new_df.loc[:, '基金池类型'] = '可买池'
        df_complete_pools = pd.concat([df_complete_pools, new_df], axis=0)
    elif num_kemai == 0 and num_jichu == 0 and num_zhongdian > 0:
        # 异常情况3：可买池和基础池都没有，但有重点池，用重点池替代基础池和可买池
        new_df = i_df[i_df['基金池类型'] == '重点池']
        new_df.loc[:, '基金池类型'] = '基础池'
        df_complete_pools = pd.concat([df_complete_pools, new_df], axis=0)
        new_df.loc[:, '基金池类型'] = '可买池'
        df_complete_pools = pd.concat([df_complete_pools, new_df], axis=0)

df_complete_pools.reset_index('基金代码', inplace=True)
df_complete_pools = df_complete_pools.apply(pd.to_numeric, errors='coerce')
df_pool_returns = df_complete_pools.groupby(['日期', '基金池类型', '资产细分V']).median().reset_index()

for date in date_lst.keys():
    dict_graph[date] = pd.DataFrame(index=label_lst, columns=level_lst)
    i_date = date_lst[date]
    date1, date2 = dt.datetime.strptime(i_date, '%Y-%m-%d'), dt.datetime.strptime(today_str, '%Y-%m-%d')

    for label in label_lst:
        for level in level_lst:
            i_df = df_pool_returns[(df_pool_returns['基金池类型'] == level) & (df_pool_returns['资产细分V'] == label)]
            i_df = i_df[(i_df['日期'] >= date1) & (i_df['日期'] <= date2)].sort_values(by='日期')
            i_df['累计净值'] = (1 + i_df['日收益率']).cumprod()
            try:
                dict_graph[date].loc[label, level] = i_df.iloc[-1]['累计净值'] - 1
            except:
                pass
# 画债券基金池的图
plt_industry_pool(dict_graph['1W']*100,'近一周基金池收益(' + work_day + ')','2.1.0近一周基金池收益')
plt_industry_pool(dict_graph['1M']*100,'近一月基金池收益(' + work_day + ')','2.1.1近一月基金池收益')
plt_industry_pool(dict_graph['3M']*100,'近三月基金池收益(' + work_day + ')','2.1.2近三月基金池收益')
plt_industry_pool(dict_graph['6M']*100,'近六月基金池收益(' + work_day + ')','2.1.3近六月基金池收益')
plt_industry_pool(dict_graph['YTD']*100,'年初至今基金池收益(' + work_day + ')','2.1.4年初至今基金池收益')
plt_industry_pool(dict_graph['1Y']*100,'近一年基金池收益(' + work_day + ')','2.1.5近一年基金池收益')


print('5.0 债券基金池收益计算完成')
t=printtime(t)

#%% 债券重点池和基础池个基收益排名计算
fund_value_data_i_code=list(set(fund_value_data.index.tolist()))
end_day_month = (pd.to_datetime(today.strftime('%Y%m%d'), format='%Y%m%d') + pd.offsets.MonthEnd(0)).strftime('%Y%m%d')
date_i_bf = (dt.datetime.strptime(end_day_month, '%Y%m%d').replace(day=1)- relativedelta(days=1)).strftime('%Y%m%d')
date_i_adjust=datei_dict[date_i_bf]
fundpool=pd.read_excel('基础数据/季度筛选结果明细_{0}.xlsx'.format(date_i_adjust[:6]), sheet_name = 'ag-grid',index_col=2).drop(columns=['基金代码'])
fund_initial=pd.DataFrame()
for i in range(0,len(fundpool.index.tolist())//500):
    fund0 = pd.DataFrame({'基金代码':fundpool.index.tolist()[500*i:500*(1+i)],'是否初始基金':w.wss(fundpool.index.tolist()[500*i:500*(1+i)], "fund_initial").Data[0]}).set_index('基金代码')
    fund_initial=pd.concat([fund_initial,fund0],axis=0)
if len(fundpool.index.tolist())%500 > 0:
    fund0 = pd.DataFrame({'基金代码':fundpool.index.tolist()[500*(1+i):],'是否初始基金':w.wss(fundpool.index.tolist()[500*(1+i):], "fund_initial").Data[0]}).set_index('基金代码')
    fund_initial=pd.concat([fund_initial,fund0],axis=0)
fundpool=fundpool.join(fund_initial)
fundpool = fundpool[(fundpool['是否初始基金'] == '是') & (fundpool['管理方式VI'] == '主动管理')&(fundpool['资产类别I'] =='债券')]

fundpool.index.name='基金代码'
fundpool=fundpool.reset_index()
for i in range(len(fundpool)):
    if fundpool.loc[i,'基金代码'][:-3]+'.SZ' in fund_value_data_i_code:
        fundpool.loc[i,'基金代码']=fundpool.loc[i,'基金代码'][:-3]+'.SZ'
    elif fundpool.loc[i,'基金代码'][:-3]+'.SH' in fund_value_data_i_code:
        fundpool.loc[i,'基金代码']=fundpool.loc[i,'基金代码'][:-3]+'.SH'
    else:
        fundpool.loc[i,'基金代码']=fundpool.loc[i,'基金代码'][:-3]+'.OF'
fundpool = fundpool.drop_duplicates(subset=['基金代码']).set_index('基金代码')

fund_pool = dict()
bond_summary = pd.DataFrame(columns = ['收益_1W','收益_1M','收益_3M','收益_6M','收益_YTD','收益_1Y'])
item=['收益_1W','收益_1M','收益_3M','收益_6M','收益_YTD','收益_1Y']
df_huizong = pd.DataFrame(index=['中短久期','长久期','一级债基','低含权','中含权','高含权','转债'], columns=item)
df_paiming = pd.DataFrame(index=df_huizong.index, columns=item)
df_shouyi = pd.DataFrame(index=df_huizong.index, columns=item)
for i in ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债','境外债券策略']:
    core_pool = pd.DataFrame()
    core_pool['基金代码']=fundpool[(fundpool['公募FOF基金池级别']=='重点')&(fundpool['资产细分V'] ==i)].index.tolist()
    core_pool['基金简称']=w.wss(core_pool['基金代码'].tolist(), "sec_name").Data[0]
    core_pool.set_index('基金代码',inplace=True)
    basic_pool = pd.DataFrame()
    basic_pool['基金代码']=fundpool[(fundpool['公募FOF基金池级别']=='基础')&(fundpool['资产细分V'] ==i)].index.tolist()
    basic_pool['基金简称']=w.wss(basic_pool['基金代码'].tolist(), "sec_name").Data[0]
    basic_pool.set_index('基金代码',inplace=True)
    fund=fundpool[((fundpool['公募FOF基金池级别']=='可买')|(fundpool['公募FOF基金池级别']=='基础')|(fundpool['公募FOF基金池级别']=='重点'))&(fundpool['资产细分V'] ==i)]
    fund['收益_1W'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
    fund['收益_1M'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
    fund['收益_3M'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
    fund['收益_6M'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0]
    fund['收益_YTD'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
    fund['收益_1Y'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
    if '中短久期' in i or '长久期' in i or '境外债券策略' in i:
        fund_pool[i+'重点池']=ct.fund_chart6(core_pool.index.tolist(),fund,rpt_date,today_str,i)
        fund_pool[i + '基础池'] = ct.fund_chart6(basic_pool.index.tolist(),fund,rpt_date, today_str, i)
        df_shouyi.loc[i, :] = fund_pool[i + '重点池'].iloc[-2, [2, 4, 6, 8, 10,12]]
    else:
        fund_pool[i+'重点池']=ct.fund_chart5(core_pool.index.tolist(),fund,rpt_date,today_str,i)
        fund_pool[i + '基础池'] =ct.fund_chart5(basic_pool.index.tolist(),fund,rpt_date,today_str,i)
        df_shouyi.loc[i, :] =fund_pool[i+'重点池'].iloc[-2,[5,7,9,11,13,15]]
    correlation1, p_value = spearmanr(fund_pool[i+'重点池']['收益_1W'][:-3], fund_pool[i+'重点池']['收益_YTD'][:-3])
    correlation1 = str(round(correlation1,2))
    correlation2, p_value = spearmanr(fund_pool[i + '基础池']['收益_1W'][:-3], fund_pool[i + '基础池']['收益_YTD'][:-3])
    correlation2 = str(round(correlation2,2))
    fund_pool[i + '重点池']=fund_pool[i + '重点池'].reset_index()
    fund_pool[i+'重点池']['简称'].fillna(fund_pool[i+'重点池'].iloc[:, 0], inplace=True)
    fund_pool[i+'重点池'].drop(fund_pool[i+'重点池'].columns[0], axis=1, inplace=True)
    fund_pool[i + '重点池'].fillna(' ', inplace=True)
    fund_pool[i + '重点池'].set_index('简称', inplace=True)
    if len(fund_pool[i + '重点池'].iloc[:, 0]) < 40:
        fundpool_table(fund_pool[i+'重点池'].reset_index(),fund_pool[i+'重点池'].reset_index(),i+'重点池收益情况(' + work_day + ')'+ '(相关性' + correlation1 + ')','2.2'+i+'重点池')
    else:
        df1 = fund_pool[i+'重点池'].reset_index().iloc[:25, :]
        fundpool_table(fund_pool[i + '重点池'].reset_index(), df1,i + '重点池收益情况(' + work_day + ')'+ '(相关性' + correlation1 + ')', '2.2'+i + '重点池1')
        df1 = fund_pool[i+'重点池'].reset_index().iloc[25:, :]
        fundpool_table(fund_pool[i + '重点池'].reset_index(), df1, i + '重点池收益情况(' + work_day + ')'+ '(相关性' + correlation1 + ')', '2.2'+i + '重点池2')
    fund_pool[i + '基础池'] = fund_pool[i + '基础池'].reset_index()
    fund_pool[i + '基础池']['简称'].fillna(fund_pool[i + '基础池'].iloc[:, 0], inplace=True)
    fund_pool[i + '基础池'].drop(fund_pool[i + '基础池'].columns[0], axis=1, inplace=True)
    fund_pool[i + '基础池'].fillna(' ', inplace=True)
    fund_pool[i + '基础池'].set_index('简称', inplace=True)
    if len(fund_pool[i + '基础池'].iloc[:, 0]) < 40:
        fundpool_table(fund_pool[i + '基础池'].reset_index(), fund_pool[i + '基础池'].reset_index(),
                            i + '基础池收益情况(' + work_day + ')'+ '(相关性' + correlation2 + ')', '2.3'+i + '基础池')
    else:
        df1 = fund_pool[i + '基础池'].reset_index().iloc[:25, :]
        fundpool_table(fund_pool[i + '基础池'].reset_index(), df1, i + '基础池收益情况(' + work_day + ')'+ '(相关性' + correlation2 + ')', '2.3'+i + '基础池1')
        df1 = fund_pool[i + '基础池'].reset_index().iloc[25:, :]
        fundpool_table(fund_pool[i + '基础池'].reset_index(), df1, i + '基础池收益情况(' + work_day + ')'+ '(相关性' + correlation2 + ')', '2.3'+i + '基础池2')
    sample = fund
    bond_summary.loc[i+'重点池'] = fund_pool[i+'重点池'][item].iloc[-2,:]
    bond_summary.loc[i + '基础池'] = fund_pool[i + '基础池'][item].iloc[-2,:]
    bond_summary.loc[i + '可买池'] = sample[item].median()
    # sample = fund[fund['基金全称'].str.contains('工银瑞信')]
    # bond_summary.loc[i + '内部池'] = sample[item].median()
    for period in item:
        df_paiming.loc[i,period]=len(list(filter(lambda x: x > df_shouyi.loc[i,period], sample[period]))) / len(sample[period])
        df_huizong.loc[i, period] = "{:.2f}({:.2f})".format(df_shouyi.loc[i,period], df_paiming.loc[i,period])
        df_paiming.loc[i, period] = round(df_paiming.loc[i,period], 2)
        df_shouyi.loc[i, period] = round(df_shouyi.loc[i,period], 2)

for i in ['境外债券策略']:
    core_pool = pd.DataFrame()
    core_pool['基金代码']=fundpool[((fundpool['公募FOF基金池级别']=='可买')|(fundpool['公募FOF基金池级别']=='基础')|(fundpool['公募FOF基金池级别']=='重点'))&(fundpool['资产细分V'] ==i)].index.tolist()
    core_pool['基金简称']=w.wss(core_pool['基金代码'].tolist(), "sec_name").Data[0]
    core_pool.set_index('基金代码',inplace=True)

    fund=fundpool[((fundpool['公募FOF基金池级别']=='可买')|(fundpool['公募FOF基金池级别']=='基础')|(fundpool['公募FOF基金池级别']=='重点'))&(fundpool['资产细分V'] ==i)]
    fund['收益_1W'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
    fund['收益_1M'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
    fund['收益_3M'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
    fund['收益_6M'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0]
    fund['收益_YTD'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
    fund['收益_1Y'] = w.wss(fund.index.tolist(), "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
    fund_pool[i]=ct.fund_chart6(core_pool.index.tolist(),fund,rpt_date,today_str,i)
    correlation1, p_value = spearmanr(fund_pool[i]['收益_1W'][:-3], fund_pool[i]['收益_YTD'][:-3])
    correlation1 = str(round(correlation1,2))

    fund_pool[i]=fund_pool[i ].reset_index()
    fund_pool[i]['简称'].fillna(fund_pool[i].iloc[:, 0], inplace=True)
    fund_pool[i].drop(fund_pool[i].columns[0], axis=1, inplace=True)
    fund_pool[i].fillna(' ', inplace=True)
    fund_pool[i ].set_index('简称', inplace=True)
    if len(fund_pool[i].iloc[:, 0]) < 40:
        fundpool_table(fund_pool[i].reset_index(),fund_pool[i].reset_index(),i+'收益情况(' + work_day + ')'+ '(相关性' + correlation1 + ')','2.3'+i)
    else:
        df1 = fund_pool[i].reset_index().iloc[:25, :]
        fundpool_table(fund_pool[i ].reset_index(), df1,i + '收益情况(' + work_day + ')'+ '(相关性' + correlation1 + ')', '2.3'+i + '1')
        df1 = fund_pool[i].reset_index().iloc[25:, :]
        fundpool_table(fund_pool[i].reset_index(), df1, i + '收益情况(' + work_day + ')'+ '(相关性' + correlation1 + ')', '2.3'+i + '2')

print('6.0 债券重点池和基础池个基收益排名计算')
t=printtime(t)


# %% 重点池的收益排名汇总
bond_summary['基金池类型']=[j[-3:] for j in bond_summary.index.tolist()]
bond_summary.index.name='基金类型'
bond_summary.reset_index(inplace=True)
bond_summary['基金类型']=[j[:-3] for j in bond_summary['基金类型']]
bond_table=bond_summary.pivot(index=['基金类型'],columns=['基金池类型'],values=item)
bond_table.index.name='index'
bond_table.reset_index(inplace=True)
bond_table['index'] = pd.Categorical(bond_table['index'], categories=["中短久期", "长久期", "一级债基", "低含权","中含权","高含权","转债","基金类型"], ordered=True)
bond_table=bond_table.sort_values(by=['index']).set_index(['index'])
df_huizong['收益(排名)'] = df_huizong.index
columns_order = ['收益(排名)','收益_1W','收益_1M','收益_3M','收益_6M','收益_YTD','收益_1Y']
# 重新排列列的顺序
df_huizong = df_huizong.reindex(columns=columns_order)
huizong_table(df_huizong, df_shouyi, df_paiming, '固收重点池收益排名汇总('+work_day+')','2.2.0重点池汇总')
print('7.0 债券重点池汇总')
t=printtime(t)

# %% 对于中含权的brison归因
cal_decomposition(cal_stock_value,rpt_date,last1w, today_str, num=1)
cal_decomposition(cal_stock_value,rpt_date,last3m, today_str, num=2)
print('8.0 brinson收益归因完成')
t=printtime(t)

# # %% 债券久期
# codes={'短期纯债型基金':['885062.WI'],'中长期纯债型基金':['885008.WI']}
# effec_chg={'短期纯债型基金':0.3,'中长期纯债型基金':0.5}
# start_day='20191231'
# page_Dur = Page()
# for wind_type in ['短期纯债型基金','中长期纯债型基金']:
#     fund_bond = fundDuration(codes[wind_type],start_day,today)
#     Df_DurCurve=fund_bond.DurCurve(codes[wind_type][0],effec_chg=effec_chg[wind_type])
#     page_Dur.add(
#         line_chart(title='{0}久期估计{1}'.format(wind_type,today_str),
#             df=Df_DurCurve.round(2)
#         )
#     )
# page_Dur.render('../周报文件/周报程序久期导出.html')
#
# print('9.0 债券久期计算完成')
# t=printtime(t)
# %% 数据底稿汇总输出
a_summary = dict({'1 收益回顾':df_graph, '2 基金分位':rtn_summary, '2 月度收益':rtn_monthly, '3 基金公司收益':amc_rank, '4 FOF':fof,'5 基金池收益':fund_pool})

with pd.ExcelWriter('../周报文件/3.0周报程序数据导出.xlsx') as writer:
    for i in a_summary:
        if isinstance(a_summary[i],dict)==False:
            a_summary[i].to_excel(writer,sheet_name=i)
        else:
            for j in a_summary[i]:
                a_summary[i][j].to_excel(writer,sheet_name=i+' '+j)

print('10.0 数据底稿输出完成')
printtime(1)
