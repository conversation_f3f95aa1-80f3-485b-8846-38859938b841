# %%
# -*- coding: utf-8 -*-
"""
Created on 25/6/2024

@author: gyrx-linyy

用于模拟组合的业绩跟踪
"""

# %%
import numpy as np
import pandas as pd
import datetime as dt
import pickle
import copy
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')
from WindPy import w
import re
w.start()

# %%
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.table import Table
from matplotlib.font_manager import FontProperties
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
import cx_Oracle
import matplotlib.ticker as ticker
import pandas as pd
import numpy as np
import datetime as dt
import pickle
from dateutil.relativedelta import relativedelta
from scipy import stats
import matplotlib.pyplot as plt
import os
import warnings
from chart import fundDuration
import chart as ct
import urllib3
from json import dumps,loads
from plot import *
import copy
import cx_Oracle
from pyecharts.charts import Page
from pyecharts.globals import CurrentConfig
from matplotlib.font_manager import FontProperties
from scipy.stats import spearmanr
import fund_analysis_income
# CurrentConfig.ONLINE_HOST = "Z:/基金精选/4.0 定期报告/pyecharts-assets/assets/"

warnings.filterwarnings("ignore")
oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind', encoding="UTF-8")
fig, ax1 = plt.subplots()#这样后面的设置才能生效

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True

light=0.5
fontsize_subtitle = 18 #16
fontsize_suptitle = 20 #18
fontsize_text = 14 #11
fontsize_legend = 13 #9
plt.rcParams['font.size'] = 16 #12  # 字体大小

def pptsize(ppt_size):
    if ppt_size==43:
        plt.rcParams['figure.figsize']=(16,9)    #(20,7)
    elif ppt_size==207:#蒋总
        plt.rcParams['figure.figsize']=(20,7)
    elif ppt_size==209:#蒋总
        plt.rcParams['figure.figsize']=(20,9)
    elif ppt_size==107:#蒋总
        plt.rcParams['figure.figsize']=(10,7)
    elif ppt_size==109:
        plt.rcParams['figure.figsize']=(10,9)
    elif ppt_size==1045:
        plt.rcParams['figure.figsize']=(10,4.5)
    elif ppt_size==129:
        plt.rcParams['figure.figsize']=(12,9)
    elif ppt_size==75:
        plt.rcParams['figure.figsize']=(7,5)
    else:
        plt.rcParams['figure.figsize']=(20,9)

# %%
#净值
fpath0='../周报文件/'
fpath1="Z:\基金精选\\6.0 其他（临时脚本、数据交换等）\李岳欣\基础数据\\"
fpath2="基础数据/"

start_day='20190101'
end_day='20241221'

# %%

adjust_date=w.tdays(start_day, end_day, "Days=Alldays;Period=M").Data[0]   #季度调池，之后再截断
lm_date=adjust_date[-2].date()
# adjust_date = [dt.datetime(2019,1,1)] + adjust_date[2:-1:3]+[adjust_date[-1]]
adjust_date = adjust_date[0:-1:3]+[adjust_date[-1]]
adjust_date_todate=[i.date() for i in adjust_date]
adjust_date = [i.strftime('%Y%m%d') for i in adjust_date]
adjust_date#可以通过调整adjust_date来调整观察期，以后模拟组合调仓随季度调仓，方便跟踪
today = dt.datetime.strptime(end_day,'%Y%m%d')
today_str = today.strftime('%Y-%m-%d')


#根据基金净值计算基金池的收益表现
label_lst = ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债']
level_lst = ['可买池', '基础池', '重点池']

bond_factor1=pd.read_excel(fpath2+'基金因子暴露.xlsx',sheet_name='非定开基金')
bond_factor2=pd.read_excel(fpath2+'基金因子暴露.xlsx',sheet_name='定开基金')
bond_factor=pd.concat([bond_factor1,bond_factor2]).set_index('季度').reset_index()
bond_factor['季度']=bond_factor['季度'].astype(str)
# %%
fund_benchmark_dict={'A股全市场策略':'930950.CSI','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ','新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI','医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH','金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI','一级债基':'885006.WI','低含权':'885006.WI','中含权':'885007.WI','高含权':'885003.WI','转债':'000832.CSI','中短久期':'885062.WI','长久期':'885008.WI'}
fund_benchmark_name={'A股全市场策略':'偏股基金','A股价值策略':'国证价值','一级债基':'一级债基','低含权':'一级债基','中含权':'二级债基','高含权':'偏债基金','转债':'中证转债','中短久期':'短债基金','长久期':'中长债基金'}

time_dict={'20190131':'18Q4', '20190430':'19Q1', '20190731':'19Q2', '20191031':'19Q3', '20200131':'19Q4', '20200430':'20Q1', '20200731':'20Q2', '20201031':'20Q3', '20210131':'20Q4', '20210430':'21Q1', '20210731':'21Q2', '20211031':'21Q3', '20220131':'21Q4', '20220430':'22Q1', '20220731':'22Q2', '20221031':'22Q3', '20230131':'22Q4', '20230430':'23Q1', '20230731':'23Q2', '20231031':'23Q3', '20240131':'23Q4', '20240430':'24Q1', '20240731':'24Q2', '20241031':'24Q3', '20241221':'24Q4'}
label='一级债基'
factor='rating_spread'

# for label in ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债']:
for label in ['一级债基']:
    for factor in ['alpha', 'duration', 'term_structure', 'credit_spread', 'cbond', 'rating_spread']:
        portfolio_median=pd.DataFrame()
        portfolio_average=pd.DataFrame()
        for i in range(len(adjust_date)-1):
            date_i= adjust_date[i]
            #季度池子
            fpath3='Z:\基金精选\\6.0 其他（临时脚本、数据交换等）\李岳欣\基金池'
            try:
                if i == 0:
                    fund_label=pd.read_excel(fpath3+'\\201810_qfp.xlsx',sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                    fund_label_bf = pd.read_excel(fpath3 + '\\201807_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[
                        ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                elif i==1:
                    fund_label = \
                    pd.read_excel(fpath3 + '\\{0}_qfp.xlsx'.format(date_i[:6]), sheet_name='基金池_简版', index_col=0)[
                        ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                    fund_label_bf = pd.read_excel(fpath3 + '\\201810_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[
                        ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                else:
                    fund_label=pd.read_excel(fpath3+'\\{0}_qfp.xlsx'.format(date_i[:6]),sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                    fund_label_bf = pd.read_excel(fpath3 + '\\{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版', index_col=0)[
                        ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]

                fund_label = fund_label[
                    (fund_label['是否可买池'] == '是') & (fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动') & (
                                fund_label['资产细分(V)'] == label)]
                fund_label_bf = fund_label_bf[
                    (fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (
                            fund_label_bf['资产细分(V)'] == label)]
            except:
                fund_label = pd.read_excel(fpath3 + '\\季度筛选结果明细_{0}.xlsx'.format(date_i[:6]), sheet_name = 'ag-grid',index_col = 2)[
                    ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称':'简称','资产细分V':'资产细分(V)','管理方式VI':'管理方式(VI)'})
                try:
                    fund_label_bf = \
                    pd.read_excel(fpath3 + '\\{0}_qfp.xlsx'.format(adjust_date[i - 1][:6]), sheet_name='基金池_简版',
                                  index_col=0)[
                        ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                except:
                    fund_label_bf = \
                    pd.read_excel(fpath3 + '\\季度筛选结果明细_{0}.xlsx'.format(adjust_date[i-1][:6]), sheet_name='ag-grid', index_col=2)[
                        ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(
                        columns={'基金简称': '简称', '资产细分V': '资产细分(V)', '管理方式VI': '管理方式(VI)'})

                for j in range(int(np.ceil(len(fund_label.index) / 500))):
                    fund_label.loc[fund_label.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = \
                    w.wss(fund_label.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
                    fund_label_bf.loc[fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = \
                        w.wss(fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
                fund_label = fund_label[(fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动管理') & (
                            fund_label['资产细分(V)'] == label)]
                fund_label = fund_label[fund_label['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]
                try:
                    fund_label_bf = fund_label_bf[
                        (fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (
                                    fund_label_bf['管理方式(VI)'] == '主动') & (
                                fund_label_bf['资产细分(V)'] == label)]
                except:
                    fund_label_bf = fund_label_bf[
                        (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动管理') & (
                                fund_label_bf['资产细分(V)'] == label)]
                    fund_label_bf = fund_label_bf[fund_label_bf['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]




#         #转化为wind index
#         fund_label.index.name='基金代码'
#         index_new=[]
#         for index in fund_label.index:
#             if index in fund_chg_data_all.columns:
#                 index_new.append(index)
#             elif index[:-3]+'.SZ' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3]+'.SZ')
#             elif index[:-3]+'.SH' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3]+'.SH')
#             else:
#                 index_new.append(index)
#         fund_label.index=index_new
#         fund_label = fund_label.loc[~fund_label.index.duplicated(keep='first')]
#
#         # 转化为wind index
#         fund_label_bf.index.name = '基金代码'
#         index_new = []
#         for index in fund_label_bf.index:
#             if index in fund_chg_data_all.columns:
#                 index_new.append(index)
#             elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3] + '.SZ')
#             elif index[:-3] + '.SH' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3] + '.SH')
#             else:
#                 index_new.append(index)
#         fund_label_bf.index = index_new
#         fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]
#
            kemai_pool=fund_label.index.tolist()
            kemai_pool_bf = fund_label_bf.index.tolist()
            try:
                jichu_pool=fund_label[fund_label['是否基础池']=='是'].index.tolist()
                zhongdian_pool=fund_label[fund_label['是否重点池']=='是'].index.tolist()
                try:
                    new_in_pool=list((set(jichu_pool)-set(fund_label_bf[fund_label_bf['是否基础池']=='是'].index.tolist()))|(set(zhongdian_pool)-set(fund_label_bf[fund_label_bf['是否重点池']=='是'].index.tolist())))
                    out_pool = list((set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist())-set(jichu_pool))|(set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())-set(
                        zhongdian_pool)))
                except:
                    new_in_pool = list(
                        (set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()))|( set(
                            zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                    out_pool = list(
                        (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool) )|(set(
                            fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                            zhongdian_pool)))
            except:
                jichu_pool = fund_label[fund_label['公募FOF基金池级别'] == '基础'].index.tolist()
                zhongdian_pool = fund_label[fund_label['公募FOF基金池级别'] == '重点'].index.tolist()
                try:
                    new_in_pool = list(
                        (set(jichu_pool) - set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()))|( set(
                            zhongdian_pool) - set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())))
                    out_pool = list(
                        (set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()) - set(jichu_pool) )|( set(
                            fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist()) - set(
                            zhongdian_pool)))
                except:
                    new_in_pool = list(
                        ( set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) )|( set(
                            zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                    out_pool = list(
                        (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool)) | (set(
                            fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                            zhongdian_pool)))
            #可买池
            pd_factor=bond_factor[bond_factor['季度'] == time_dict[date_i]].set_index('基金代码')[[factor]]
            fund_list= list(set(kemai_pool)&set(pd_factor.index.tolist()))
            portfolio_median.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(),'可买池'] =  pd_factor.loc[fund_list].median().values[0]
            portfolio_average.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(),'可买池'] = pd_factor.loc[fund_list].mean().values[0]

            #基础池
            pd_factor = bond_factor[bond_factor['季度'] == time_dict[date_i]].set_index('基金代码')[[factor]]
            fund_list = list(set(jichu_pool) & set(pd_factor.index.tolist()))
            portfolio_median.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '基础池'] = pd_factor.loc[fund_list].median().values[0]
            portfolio_average.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '基础池'] = pd_factor.loc[fund_list].mean().values[0]

            #重点池
            pd_factor = bond_factor[bond_factor['季度'] == time_dict[date_i]].set_index('基金代码')[[factor]]
            fund_list = list(set(zhongdian_pool) & set(pd_factor.index.tolist()))
            portfolio_median.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '重点池'] = pd_factor.loc[fund_list].median().values[0]
            portfolio_average.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '重点池'] = pd_factor.loc[fund_list].mean().values[0]

            # 新进池
            pd_factor = bond_factor[bond_factor['季度'] == time_dict[date_i]].set_index('基金代码')[[factor]]
            fund_list = list(set(new_in_pool) & set(pd_factor.index.tolist()))
            portfolio_median.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '每期新进'] = pd_factor.loc[fund_list].median().values[0]
            portfolio_average.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '每期新进'] = pd_factor.loc[fund_list].mean().values[0]

            # 出池
            pd_factor = bond_factor[bond_factor['季度'] == time_dict[date_i]].set_index('基金代码')[[factor]]
            fund_list = list(set(out_pool) & set(pd_factor.index.tolist()))
            portfolio_median.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '汇总出池'] = pd_factor.loc[fund_list].median().values[0]
            portfolio_average.loc[dt.datetime.strptime(date_i,'%Y%m%d').date(), '汇总出池'] = pd_factor.loc[fund_list].mean().values[0]


        # %%
        color_dict={'可买池':'orange','基础池':(0.5,0.3,0.8),'重点池':'purple','模拟池':'blue','每期新进':'red','汇总出池':'green'}
        line_dict={'可买池':'--','基础池':'--','重点池':'--','模拟池':'-','每期新进':'--','汇总出池':'--'}

        # %%
        #因子暴露图平均值
        df=portfolio_average.copy()
        pptsize(109)
        fig_name = label+factor+'因子暴露平均值'
        fig, ax1 = plt.subplots()
        for column in ['可买池','基础池','重点池','每期新进','汇总出池']:
            ax1.plot(df.index, df[column].tolist(), label = column, color=color_dict[column], alpha = 0.7 ,linestyle=line_dict[column])
            ax1.text(df.index[-1], df[column].tolist()[-1],
                            f'{column}: {df[column].tolist()[-1]:.4f}', color=color_dict[column], verticalalignment='bottom',fontsize=fontsize_text-6)#rotation=90,
        ax1.tick_params(axis='y',labelsize=fontsize_legend)
        ax1.spines['right'].set_color((0,0,1,0.5))
        ax1.spines['top'].set_visible(False)
        plt.xticks(rotation=0)
        #近一月
        # plt.grid(alpha=light)
        # plt.gcf().autofmt_xdate()
        plt.title(fig_name,fontsize=fontsize_suptitle)
        plt.show()
        fig.savefig(fpath0+'{0}.jpg'.format(fig_name))
        plt.close()

        # 因子暴露图中位数
        df = portfolio_median.copy()
        pptsize(109)
        fig_name = label + factor + '因子暴露中位数'
        fig, ax1 = plt.subplots()
        for column in ['可买池', '基础池', '重点池', '每期新进', '汇总出池']:
            ax1.plot(df.index, df[column].tolist(), label=column, color=color_dict[column], alpha=0.7,
                     linestyle=line_dict[column])
            ax1.text(df.index[-1], df[column].tolist()[-1],
                     f'{column}: {df[column].tolist()[-1]:.4f}', color=color_dict[column], verticalalignment='bottom',
                     fontsize=fontsize_text - 6)  # rotation=90,
        ax1.tick_params(axis='y', labelsize=fontsize_legend)
        ax1.spines['right'].set_color((0, 0, 1, 0.5))
        ax1.spines['top'].set_visible(False)
        plt.xticks(rotation=0)
        # 近一月
        # plt.grid(alpha=light)
        # plt.gcf().autofmt_xdate()
        plt.title(fig_name, fontsize=fontsize_suptitle)
        plt.show()
        fig.savefig(fpath0 + '{0}.jpg'.format(fig_name))
        plt.close()







