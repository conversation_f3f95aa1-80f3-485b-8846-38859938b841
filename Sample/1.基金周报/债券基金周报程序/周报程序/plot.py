import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import pandas as pd
import matplotlib.pyplot as plt
import copy
from matplotlib.colors import LinearSegmentedColormap
import numpy as np
from adjustText import adjust_text
from pyecharts.globals import CurrentConfig
from pyecharts import options as opts
from pyecharts.charts import Bar, Line, Pie, Tab, Timeline, Scatter, Page, Grid
from pyecharts.components import Table
from WindPy import w
CurrentConfig.ONLINE_HOST = "Z:/基金精选/4.0 定期报告/pyecharts-assets/assets/"
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True
light=0.5
fontsize_subtitle = 18 #16
fontsize_suptitle = 20 #18
fontsize_text = 13 #11
fontsize_legend = 12 #9
plt.rcParams['font.size'] = 15 #12  # 字体大小
linewidth_table=0.5


w.start()
def pptsize(ppt_size):
    if ppt_size==43:
        plt.rcParams['figure.figsize']=(16,9)    #(20,7)
    elif ppt_size==207:
        plt.rcParams['figure.figsize']=(20,7)
    else:
        plt.rcParams['figure.figsize']=(20,9)
pptsize(209)

def color_map_red_green(df,colortable):
    for j in range(3,len(df.columns)):
        for i in range(len(df)-3):
            if df.iloc[i,j] == df.nsmallest(1,df.columns[j]).values[0][j]:
                colortable.iloc[i,j] = '#90ee90' #淡绿
            if df.iloc[i,j] == df.nlargest(1,df.columns[j]).values[0][j]:
                colortable.iloc[i,j]='#ffa0a0'   #淡红
    return colortable
#%% FOF基金表格
def fof_table(df,df1,title,picture_name):
    pptsize(209)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax.axis('off')
    colortable = copy.deepcopy(df1)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'
    colColours = ['lightsteelblue'] * len(colortable.columns)
    df1.fillna(' ', inplace=True)
    table = ax.table(cellText=df1.values,
                     colLabels=df1.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df1.columns))))
    cmap = plt.cm.get_cmap('RdYlGn')
    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        cell.set_linewidth(linewidth_table)
        if ('-' in str(df1.iloc[row - 1, col])) and (row >= 1 and col >= 2):
            cell.get_text().set_color('red')
        if (row == 0) | (col == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        if (col >2) and (row != 0) and ('中位数' not in str(df1.iloc[row - 1, 0]))and ('25分位' not in str(df1.iloc[row - 1, 0]))and ('75分位' not in str(df1.iloc[row - 1, 0])):
            try:
                value = 1 - (0.25 + (df1.iloc[row - 1, col] - df[df!= ' '].iloc[:, col].min(axis=0)) / (
                        df[df != ' '].iloc[:, col].max(axis=0) -  df[df!= ' '].iloc[:, col].min(
                        axis=0)) / 2)  # float(cell.get_text().get_text())
                color_val = cmap(value)
                cell.set_facecolor(color_val)
            except:
                pass
        if '25分位' in str(df1.iloc[row - 1, 0]):
            ax.axhline(y=(3 / (df1.shape[0] +1)), lw=2, ls='-', c='red')  # 横线
    ax.set_title(title, fontsize=fontsize_subtitle, fontweight='bold', pad=5)
    fig.tight_layout()

    # 保存为图片
    plt.savefig('../周报文件/' + picture_name + '.png', bbox_inches='tight')
    plt.close()
    return


#%% 债券基金池表格
def fundpool_table(df,df1,title,picture_name):
    pptsize(209)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax.axis('off')
    colortable = copy.deepcopy(df1)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'
    colColours = ['lightsteelblue'] * len(colortable.columns)
    table = ax.table(cellText=df1.values,
                     colLabels=df1.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours,
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df.columns))))
    cmap = plt.cm.get_cmap('RdYlGn')
    if len(df.columns) <15:
        for (row, col), cell in table.get_celld().items():
            cell.set_fontsize(fontsize_text)
            cell.set_linewidth(linewidth_table)
            if ('-' in str(df1.iloc[row - 1, col])) and (row >= 1 and col >= 2):
                cell.get_text().set_color('red')
            if (row == 0) | (col == 0):
                cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
            if (col in [3,5,7,9,11,13]) and (row != 0)and ('中位数' not in str(df1.iloc[row - 1, 0]))and ('25分位' not in str(df1.iloc[row - 1, 0]))and ('75分位' not in str(df1.iloc[row - 1, 0])):
                value = 1-(0.25+(df1.iloc[row-1,col]-df.iloc[:,col].min(axis=0))/(df.iloc[:,col].max(axis=0)-df.iloc[:,col].min(axis=0))/2)#float(cell.get_text().get_text())
                color_val = cmap(value)
                cell.set_facecolor(color_val)
            if '25分位' in str(df1.iloc[row - 1, 0]):
                ax.axhline(y=(3 / (df1.shape[0] +1)), lw=2, ls='-', c='red')  # 横线
    else:
        for (row, col), cell in table.get_celld().items():
            cell.set_fontsize(fontsize_text)
            cell.set_linewidth(linewidth_table)
            if ('-' in str(df1.iloc[row - 1, col])) and (row >= 1 and col >= 2):
                cell.get_text().set_color('red')
            if (row == 0) | (col == 0):
                cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
            if (col in [6, 8, 10, 12,14,16]) and (row != 0)and ('中位数' not in str(df1.iloc[row - 1, 0]))and ('25分位' not in str(df1.iloc[row - 1, 0]))and ('75分位' not in str(df1.iloc[row - 1, 0])):

                value = 1-(0.25+(df1.iloc[row-1,col]-df.iloc[:,col].min(axis=0))/(df.iloc[:,col].max(axis=0)-df.iloc[:,col].min(axis=0))/2)#float(cell.get_text().get_text())
                color_val = cmap(value)
                cell.set_facecolor(color_val)
            if '25分位' in str(df1.iloc[row - 1, 0]):
                ax.axhline(y=(3 / (df1.shape[0] +1)), lw=2, ls='-', c='red')  # 横线

    df.fillna(' ', inplace=True)
    ax.set_title(title, fontsize=fontsize_subtitle, fontweight='bold', pad=5)
    fig.tight_layout()
    # 保存为图片
    plt.savefig('../周报文件/' + picture_name + '.png', bbox_inches='tight')
    plt.close()
    return

#%% 月度收益表格
def plt_monthly_return(df,title,picture_name):
    pptsize(209)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax.axis('off')
    colortable = copy.deepcopy(df)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    # rowColours = ['lightsteelblue'] * len(colortable.index)
    colColours = ['lightsteelblue'] * len(colortable.columns)
    df.fillna(0, inplace=True)
    table = ax.table(cellText=df.values,
                     colLabels=df.columns,
                     rowLabels=df.index,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours,
                     rowColours=colortable.iloc[:, 0].values
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df.columns))))
    cmap = plt.cm.get_cmap('RdYlGn')

    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        cell.set_linewidth(linewidth_table)
        if ('-' in str(df.iloc[row - 1, col])) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
        if (row == 0) | (col == -1):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        else:
            try:
                value = 1-(0.25+(df.iloc[row-1,col]-df.iloc[row-1,:].min(axis=0))/(df.iloc[row-1,:].max(axis=0)-df.iloc[row-1,:].min(axis=0))/2)
                color_val = cmap(value)
                cell.set_facecolor(color_val)
            except:
                pass

    ax.set_title(title, fontsize=fontsize_subtitle, fontweight='bold', pad=5)
    fig.tight_layout()
    # 保存为图片
    plt.savefig('../周报文件/' + picture_name + '.png', bbox_inches='tight')
    plt.close()
    return


#%% 基金池收益柱状图
def plt_industry_pool(df,title,picture_name):
    fund_type = df.index.tolist()

    data = df.values.T
    s1='重点>基础>可买:'
    s2='基础>重点>可买:'
    s3='重点>可买>基础:'
    s4='基础>可买>重点:'
    s5='可买>重点>基础:'
    s6='可买>基础>重点:'
    for i in fund_type:
        df_temp=df.loc[i,:]
        if df_temp[0]>df_temp[1]:
            if df_temp[1]>df_temp[2]:
                s6=s6+' '+i
            else:
                if df_temp[0]>df_temp[2]:
                    s5=s5+' '+i
                else:
                    s3=s3+' '+i
        else:
            if df_temp[2] > df_temp[1]:
                s1=s1+' '+i
            else:
                if df_temp[0] > df_temp[2]:
                    s4=s4+' '+i
                else:
                    s2=s2+' '+i
    x = [i * 1.4 for i in range(len(fund_type))]
    # x = range(len(fund_type))
    width = 0.3
    plt.rcParams['figure.figsize'] = (20, 4.5)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    # rects1 = ax.bar(x, data[0], width, label='内部池', alpha=0.8)
    rects2 = ax.bar([i + width for i in x], data[0], width, label='可买池', alpha=light,color='red')
    rects3 = ax.bar([i + 2 * width for i in x], data[1], width, label='基础池', alpha=light, color='orange')
    rects4 = ax.bar([i + 3 * width for i in x], data[2], width, label='重点池', alpha=light, color='blue')
    def autolabel(rects):
        """在每个矩形上方添加数据标签"""
        for rect in rects:
            height = rect.get_height()
            if height >= 0:
                text_color = 'black'
                y_offset = height*0.1
            else:
                text_color = 'red'
                y_offset = height*0.1
            ax.annotate('{:.2f}'.format(height),
                        xy=(rect.get_x() + rect.get_width() / 2, height+y_offset),
                        xytext=(0, 3),  # 调整标签位置
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=fontsize_legend, color=text_color)

    # autolabel(rects1)
    autolabel(rects2)
    autolabel(rects3)
    autolabel(rects4)
    ax.text(0.3, 0.85, s1, ha='center', va='bottom', color='grey',fontweight='bold',transform=plt.gcf().transFigure, fontsize=fontsize_text)
    ax.text(0.3, 0.8, s2, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure, fontsize=fontsize_text)
    ax.text(0.5, 0.85, s3, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_text )
    ax.text(0.5, 0.8, s4, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_text )
    ax.text(0.7, 0.85, s5, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_text)
    ax.text(0.7, 0.8, s6, ha='center', va='bottom', color='grey', fontweight='bold',transform=plt.gcf().transFigure,
            fontsize=fontsize_text )
    ax.set_xticks([i + 2 * width for i in x])
    ax.set_xlabel("基金类型")
    ax.set_ylabel("收益率（%）")
    ax.set_xticklabels(fund_type, rotation=0, fontsize=fontsize_legend)
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.2f}' if y >= 0 else f'-{abs(y):.2f}'))
    # ax.set_ylim(bottom=0)
    # ax.legend(loc='center right', bbox_to_anchor=(0.5, 1.15), ncol=5, fontsize=fontsize_legend)
    ax.legend(loc='best', fontsize=fontsize_legend)
    if abs(data.max())>abs(data.min()):
        plt.ylim(data.min() - abs(data.max())*1.7, data.max() + abs(data.max()) * 0.7)
    if abs(data.max())<=abs(data.min()):
        plt.ylim(data.min() - abs(data.min())*1.7, data.max() + abs(data.min()) * 0.7)

    # plt.ylim(data.min() -1, data.max() + 1)
    # if data.max()>0:
    #     plt.ylim(data.min()-data.max()*0.3, data.max() * 1.5)
    # if data.min() < 0:
    #     plt.ylim(data.min() - abs(data.min()) * 0.5, data.max() + abs(data.min()) * 0.5)

    # elif (data.max()<0)&(data.max()>0):
    #     plt.ylim(data.min() * 0.2, data.max() * 1.2)
    # elif (data.max() > 0) & (data.max() > 0):

    plt.title(title, fontsize=fontsize_subtitle,fontweight='bold', pad=5)
    fig.tight_layout()
    fig.savefig('../周报文件/' + picture_name + '.png')
    plt.close()
    return

#%% 债券基金收益散点图
def scatter_manager(df, namex, namey, title,picture_name):
    plt.rcParams['figure.figsize'] = (10, 9)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    plt.scatter(df[namex], df[namey], c='blue')


    slope, intercept = np.polyfit(df[namex], df[namey], deg=1)
    x = np.array([df[namex].min(), df[namex].max()])
    y = slope * x + intercept
    plt.plot(x, y, '--', c='grey')
    # 计算 x 轴数据的中位数
    median_x = np.median(df[namex]).round(2)
    plt.axvline(x=median_x, color='r', linestyle='--')
    plt.text(median_x, np.max(df[namey]+0.1), f'中位数: {median_x}', va='center', ha='right')
    # plt.axhline(y=0, color='red')
    # plt.axvline(x=0, color='red')

    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}' if x >= 0 else f'-{abs(x):.2f}'))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.2f}' if y >= 0 else f'-{abs(y):.2f}'))
    ax.set_xlabel("今年以来收益率（%）")
    ax.set_ylabel("去年全年收益率（%）")


    for i in range(len(df.index)):
        label = f"{df.iloc[i]['基金管理人']},{df.iloc[i][namex]}"
        if df.iloc[i]['基金管理人'] == '工银瑞信基金':
            plt.scatter(df.iloc[i][namex], df.iloc[i][namey], c='red')
            plt.annotate(label, (df.iloc[i][namex], df.iloc[i][namey]),
                         textcoords='offset points', xytext=(0, -10), ha='center', color='red', fontweight='bold',fontsize=fontsize_legend)
        else:
            plt.annotate(label, (df.iloc[i][namex], df.iloc[i][namey]),
                         textcoords='offset points', xytext=(0, -10), ha='center',fontsize=fontsize_legend)
    plt.title(title, fontsize=fontsize_subtitle,fontweight='bold', pad=5)
    plt.savefig('../周报文件/' + picture_name + '.png')
    plt.close()
    return


def color_map_red_green2(df,colortable):
    for j in [2,3,6,7,10,11,14,15,18,19]:
        for i in range(len(df)-1):
            if df.iloc[i,j] == df.iloc[:,j].nsmallest(2).values[0]:
                colortable.iloc[i,j] = '#90ee90' #淡绿
            if df.iloc[i,j] == df.iloc[:,j].nlargest(2).values[0]:
                colortable.iloc[i,j]='#ffa0a0'   #淡红
            if df.iloc[i,j] == df.iloc[:,j].nsmallest(2).values[1]:
                colortable.iloc[i,j] = '#90ee90' #淡绿
            if df.iloc[i,j] == df.iloc[:,j].nlargest(2).values[1]:
                colortable.iloc[i,j]='#ffa0a0'   #淡红

    return colortable

#%% 债券细分类型收益表
def table_amc(df,title,picture_name, fontsize_legend=11, fontsize_subtitle=14):
    # 合并各列别列表
    df_group = list()
    for i in df:
        if i !='区域收益':
            df_i=df[i].reset_index()

            new_df = df_i[['基金管理人', '产品规模', '中位数收益', '规模加权收益']]
            new_df.columns = [i, '规模', '中位数', '规模加权']
            if len(new_df.index) > 25:
                new_df = new_df.iloc[:25]
            # 确保所有列都是数值类型
            for col in new_df.columns:
                new_df[col] = pd.to_numeric(new_df[col], errors='coerce')
            new_df.loc['中位数']=new_df.median().round(2)

            df_group.append(new_df)
    df_group = pd.concat(df_group, axis=1)
    # df_group.columns = pd.MultiIndex.from_tuples(list(itertools.product(group0, [i+'管理人', '规模', '中位数', '规模加权'])))
    # 生成中位数、调整缺失值和列名
    # df_group.loc['中位数'] = df_median
    # df_group.loc['中位数', df_median.index] = list(df_median)
    df_group = df_group.fillna('中位数')
    # df_group.index = [(i + 1) for i in range(25)] + ['']
    df_group.index = [(i + 1) for i in range(25)]+ ['']
    # 绘制表格
    pptsize(209)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    color_table = df_group.copy()
    for m in range(len(color_table.index)):
        if m in list(range(1, len(color_table.index), 2)):
            color_table.loc[color_table.index[m]] = 'lightgray'
        else:
            color_table.loc[color_table.index[m]] = 'white'

    colColours = list()
    for i in range(len(df)):
        colColours.extend(['lightpink', 'lightsteelblue', 'lightsteelblue', 'lightsteelblue'])


    table0 = ax.table(cellText=df_group.values, colLabels=df_group.columns, rowLabels=df_group.index,
                      bbox=(0, 0, 1, 1), cellLoc='center', rowLoc='center', colLoc='center',
                      cellColours=color_table.values,
                      rowColours=list(color_table.iloc[:, 0]),
                      colColours=colColours)  # [('lightsteelblue','lightsteelblue') for i in range(24) ]) #colColours)

    table0.auto_set_font_size(False)
    cmap = plt.cm.get_cmap('RdYlGn')
    for (row, col), cell in table0.get_celld().items():
        #        if row<1 or col==-1:
        #            print(row,col,df_group.iloc[row-1,col])
        cell.set_fontsize(fontsize_legend)
        if ('-' in str(df_group.iloc[row - 1, col])) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
        if '工银瑞信' in str(df_group.iloc[row - 1, col]):
            cell.get_text().set_color('red')
        if (row == 0) | (row == 26):  # row==0对应中位数那一行
            # cell.get_text().set_weight('bold')
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_legend))
        if (col in [2,3,6,7,10,11,14,15,18,19,22,23,26,27]) and (row != 0)and('中位数' not in str(df_group.iloc[row - 1, 0]))and ('25分位' not in str(df_group.iloc[row - 1, 0]))and ('75分位' not in str(df_group.iloc[row - 1, 0])):
            try:
                value = 1 - (0.25 + (df_group.iloc[row - 1, col] - df_group.iloc[:, col].min(axis=0)) / (
                            df_group.iloc[:, col].max(axis=0) - df_group.iloc[:, col].min(
                        axis=0)) / 2)  # float(cell.get_text().get_text())
                color_val = cmap(value)
                cell.set_facecolor(color_val)
            except:
                pass
    for i in range(df_group.shape[1]):
        table0[0, i].get_text().set_weight('bold')

    for n in np.arange(4, len(df_group.columns), 4):
        ax.axvline(x=(n / len(df_group.columns)), lw=2, ls='-', c='blue')
    for k in np.arange(0, len(df_group.columns)):
        if k % 4 == 1 or k % 4 == 2 or k % 4 == 3:
            ax.axvline(x=(k / len(df_group.columns)), lw=1, ls='-', c='red')

    ax.axis('off')
    plt.title(title, fontsize=fontsize_subtitle,fontweight='bold', pad=5)
    fig.tight_layout()
    fig.savefig('../周报文件/' + picture_name + '.png')
    plt.close()
    return

#%% 债券细分基金分位数表格
def fund_percentile(df,title,picture_name):
    plt.rcParams['figure.figsize'] = (10, 9)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax.axis('off')
    colortable = copy.deepcopy(df)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    colColours = ['lightsteelblue'] * len(colortable.columns)
    df.fillna(' ', inplace=True)
    table = ax.table(cellText=df.values,
                     colLabels=df.columns,
                     rowLabels=df.index,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours,
                     rowColours = colortable.iloc[:, 0].values
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        cell.set_linewidth(linewidth_table)
        if ('-' in str(df.iloc[row - 1, col])) and (row >= 1 and col >= 2):
            cell.get_text().set_color('red')
        if (row == 0) | (col == -1):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))

    ax.set_title(title, fontsize=fontsize_subtitle, fontweight='bold', pad=5)
    fig.tight_layout()
    # 保存为图片
    plt.savefig('../周报文件/' + picture_name + '.png', bbox_inches='tight')
    plt.close()
    return

#%% 债券细分基金横向柱状图
def plt_fund_performance(df,title,picture_name):
    plt.rcParams['figure.figsize'] = (10, 9)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    fund_type = df.index.tolist()
    data = df.values.T# x = range(len(fund_type))
    x = [i * 1.4 for i in range(len(fund_type))]
    width = 0.3
    rects1 = ax.barh(x, data[0], width, label='近一周', alpha=0.8)
    rects2 = ax.barh([i + width for i in x], data[1], width, label='近一月', alpha=0.8)

    def autolabel(rects):
        """在每个柱状图右侧添加数据标签"""
        for rect in rects:
            width = rect.get_width()
            ax.annotate('{:.2f}'.format(width),
                        xy=(width, rect.get_y() + rect.get_height() / 2),  # 将xy位置设置为柱状图右侧顶部
                        xytext=(5, -7),    # 调整标签位置，使其稍微偏离柱形
                        textcoords="offset points",
                        ha='left', va='center', fontsize=fontsize_legend)  # 水平对齐方式设为左对齐

    autolabel(rects1)
    autolabel(rects2)
    ax.set_yticks([i + 2 * width for i in x])
    ax.set_yticklabels(fund_type, rotation=0, fontsize=fontsize_legend)
    ax.set_xlabel("收益率（%）")
    ax.set_ylabel("基金类型")
    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}' if x >= 0 else f'-{abs(x):.2f}'))
    # ax.set_ylim(bottom=0)
    # ax.legend(loc='center right', bbox_to_anchor=(0.5, 1.15), ncol=5, fontsize=fontsize_legend)
    ax.legend(loc='best', fontsize=fontsize_legend)
    plt.title(title, fontsize=fontsize_subtitle,fontweight='bold', pad=5)
    fig.savefig('../周报文件/' + picture_name + '.png')
    plt.close()
    return
def huizong_table(df_huizong, df_shouyi, df_rank, title, picture_name):
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    cmap = plt.cm.get_cmap('RdYlGn')
    ax.axis('off')
    colortable = copy.deepcopy(df_huizong)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    rowColours = ['lightsteelblue'] * len(colortable.index)
    colColours = ['lightsteelblue'] * len(colortable.columns)


    table = ax.table(cellText=df_huizong.values,
                     colLabels=df_huizong.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df_huizong.columns))))

    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        if ('-' in str(df_huizong.iloc[row - 1, col])) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
        if (row == 0) | (col == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        if (row >= 1 and col >= 1):
            try:
                value = (0.25 + df_rank.iloc[row-1 , col-1] / 2)  # float(cell.get_text().get_text())
                color_val = cmap(value)
                cell.set_facecolor(color_val)
            except:
                pass
    ax.set_title(title, fontsize=fontsize_suptitle, fontweight='bold')
    fig.tight_layout()
    # 保存为图片
    fig.savefig('../周报文件/' + picture_name + '.png')
    plt.close()
    return


def line_chart(df, title="带中位数的折线图", percentile=1, id='0') -> Line:
    df = df.sort_values(by=df.index[-1], axis=1, ascending=False)
    line = Line()
    line.add_xaxis(xaxis_data=df.index.tolist())
    is_selected_flag = {i: True if i < 3 else False for i in range(50)}
    label_i = {i: True if i < 4 else False for i in range(50)}
    for i in range(len(df.columns)):
        line.add_yaxis(df.columns[i], df.iloc[:, i].tolist(), is_connect_nones=True, is_selected=is_selected_flag[i],
                       markpoint_opts=opts.MarkPointOpts(symbol_size=0.1,
                                                         data=[opts.MarkPointItem(name=df.columns[i],
                                                                                  coord=[df.index.tolist()[-1],
                                                                                         df.iloc[:, i].tolist()[-1]],
                                                                                  value=df.iloc[:, i].tolist()[-1])],
                                                         label_opts=opts.LabelOpts(position='right')
                                                         )
                       )

    line.set_series_opts(label_opts=opts.LabelOpts(is_show=False))

    if len(df.columns) < 5:
        pos_left_flag = 'center'
    else:
        pos_left_flag = '30%'

    line.set_global_opts(
        title_opts=opts.TitleOpts(title=title),
        yaxis_opts=opts.AxisOpts(
            type_="value",
            axistick_opts=opts.AxisTickOpts(is_show=True),
            splitline_opts=opts.SplitLineOpts(is_show=True),
        ),
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),  # 鼠标放上去会显示X\Y对应数据
        datazoom_opts=[
            # opts.DataZoomOpts(range_start=0,range_end=100,orient='vertical'),
            opts.DataZoomOpts(range_start=0, range_end=100, pos_bottom='bottom', orient='horizontal')],  # 可以选日期
        legend_opts=opts.LegendOpts(type_='scroll', pos_left=pos_left_flag),
    )

    if percentile == 1:
        line.set_series_opts(
            markline_opts=opts.MarkLineOpts(
                data=[
                    opts.MarkLineItem(type_="median", name="中位数"),
                ]
            ),
        )
    if id != '0':
        line.chart_id = id
    return line
#brinson收益归因图
def cal_decomposition(cal_stock_value, rpt_date, day1, day2, num):
    holding = cal_stock_value[cal_stock_value['日期'] == rpt_date].loc['是']
    mf = holding.groupby('股票代码')['持仓市值'].sum()
    mf = pd.DataFrame(mf[~mf.index.str.contains('HK')]).sort_values(by='持仓市值', ascending=False)
    mf['持仓占比'] = mf['持仓市值'] / mf['持仓市值'].sum()
    mf['简称'] = w.wss(mf.index.tolist(), "sec_name").Data[0]
    mf['行业'] = w.wss(mf.index.tolist(), "industry_sw_2021", "tradeDate=" + rpt_date + ";industryType=1").Data[0]
    mf['区间涨跌'] = w.wss(mf.index.tolist(), "pct_chg_per", "startDate=" + day1 + ";endDate=" + day2).Data[0]
    mf['收益'] = mf['持仓占比'] * mf['区间涨跌']
    zz800 = w.wset("indexconstituent", "date=" + rpt_date + ";windcode=000906.SH")
    zz800 = pd.DataFrame(np.array(zz800.Data).T, columns=zz800.Fields)
    zz800['i_weight'] = zz800['i_weight'].astype(float) / 100
    zz800['行业'] = \
    w.wss(zz800['wind_code'].tolist(), "industry_sw_2021", "tradeDate=" + rpt_date + ";industryType=1").Data[0]
    zz800['区间涨跌'] = w.wss(zz800['wind_code'].tolist(), "pct_chg_per", "startDate=" + day1 + ";endDate=" + day2).Data[0]
    zz800['收益'] = zz800['i_weight'] * zz800['区间涨跌']

    allocation = pd.DataFrame()
    allocation['公募配置'] = mf.groupby('行业')['持仓占比'].sum()
    allocation['公募收益'] = mf.groupby('行业')['收益'].sum() / allocation['公募配置']
    allocation['中证800配置'] = zz800.groupby('行业')['i_weight'].sum()
    allocation['中证800收益'] = zz800.groupby('行业')['收益'].sum() / allocation['中证800配置']
    allocation['超额收益'] = allocation['公募配置'] * allocation['公募收益'] - allocation['中证800配置'] * allocation['中证800收益']- (
                allocation['公募配置'] - allocation['中证800配置']) * (zz800['收益'].sum())
    allocation['配置超额'] = (allocation['公募配置'] - allocation['中证800配置']) * (allocation['中证800收益']- (zz800['收益'].sum()))
    allocation['选股超额'] = (allocation['公募收益'] - allocation['中证800收益']) * allocation['公募配置']
    allocation[['公募配置', '中证800配置']] = allocation[['公募配置', '中证800配置']] * 100
    allocation['配置差额'] = allocation['公募配置'] - allocation['中证800配置']
    allocation = allocation.sort_values(by='配置差额', ascending=False)

    sw_idx = ['801010.SI', '801030.SI', '801040.SI', '801050.SI', '801080.SI', '801110.SI', '801120.SI', '801130.SI',
              '801140.SI', '801150.SI',
              '801160.SI', '801170.SI', '801180.SI', '801200.SI', '801210.SI', '801230.SI', '801710.SI', '801720.SI',
              '801730.SI', '801740.SI',
              '801750.SI', '801760.SI', '801770.SI', '801780.SI', '801790.SI', '801880.SI', '801890.SI', '801950.SI',
              '801960.SI', '801970.SI', '801980.SI']
    # citic_idx = ['CI00500'+str(i)+'.WI' for i in range(1,10)] + ['CI0050'+str(i)+'.WI' for i in range(10,31)]
    # citic_idx_rtn = w.wss(citic_idx, "sec_name")
    # citic_idx_rtn = pd.DataFrame(index = [i[:-4] for i in citic_idx_rtn.Data[0]])
    # citic_idx_rtn['区间涨跌'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + day1 + ";endDate=" + day2).Data[0]
    # allocation['区间涨跌'] = citic_idx_rtn['区间涨跌']
    # sw_idx = ['CI00500'+str(i)+'.WI' for i in range(1,10)] + ['CI0050'+str(i)+'.WI' for i in range(10,31)]
    sw_idx_rtn = w.wss(sw_idx, "sec_name")
    sw_idx_rtn = pd.DataFrame(index=[i[:-4] for i in sw_idx_rtn.Data[0]])
    sw_idx_rtn['区间涨跌'] = w.wss(sw_idx, "pct_chg_per", "startDate=" + day1 + ";endDate=" + day2).Data[0]
    allocation['区间涨跌'] = sw_idx_rtn['区间涨跌']
    allocation = allocation.round(2)
    allocation.index.name = '行业'

    # 画超额收益分解柱状图
    pptsize(209)
    filename = '1.1.'+str(int(num+3))+'境内含权超额收益来源分解_{0}.png'.format(num)
    figname = '境内含权超额收益来源分解_{0}-{1}'.format(day1, day2)

    fig, ax1 = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax1.spines['right'].set_visible(False)
    ax1.spines['top'].set_visible(False)
    allocation_bottom = pd.Series()
    for index in allocation.index:
        try:
            if allocation.loc[index, '配置超额'] * allocation.loc[index, '选股超额'] >= 0:
                allocation_bottom.loc[index] = allocation.loc[index, '配置超额']
            else:
                allocation_bottom.loc[index] = 0
        except:
            allocation_bottom.loc[index] = 0
    ax1.bar(allocation.index.tolist(), allocation['配置超额'].tolist(), width=0.4, color='blue', label='配置超额')
    ax1.bar(allocation.index.tolist(), allocation['选股超额'].tolist(), width=0.4, bottom=allocation_bottom,
            color='orange', label='选股超额')
    ax1.plot(allocation.index, allocation['超额收益'].tolist(), linewidth=2.5, color='red', linestyle='-', marker='',
             label='超额收益')

    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.2f}' if y >= 0 else f'-{abs(y):.2f}'))
    ax1.set_ylim(bottom=min(allocation['配置超额'].min(), allocation['选股超额'].min(), allocation['超额收益'].min()) - 0.02,
                 top=max(allocation['配置超额'].max(), allocation['选股超额'].max(), allocation['超额收益'].max()) + 0.02)
    lines, labels = ax1.get_legend_handles_labels()
    plt.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, 0.9), ncol=3, fontsize=fontsize_legend)

    plt.yticks(fontsize=fontsize_legend)
    plt.xticks(rotation=45, ha="center", fontsize=fontsize_legend)

    plt.title(figname, fontsize=fontsize_suptitle, weight='bold')
    plt.savefig('../周报文件/'+ filename)
    plt.close()

    pptsize(107)

    # 画超额收益分解散点图
    filename = '1.1.'+str(int(num+1))+'境内含权涨跌幅{0}.png'.format(num)
    figname = '境内含权涨跌幅_{0}-{1}'.format(day1, day2)

    fig, ax = plt.subplots(nrows=1, ncols=1)

    ret_800 = w.wss("000906.SH", "pct_chg_per", "startDate=" + day1 + ";endDate=" + day2).Data[0][0]

    ret_stock = w.wss("930950.CSI", "pct_chg_per", "startDate=" + day1 + ";endDate=" + day2).Data[0][0]

    ax.scatter(allocation['配置差额'], allocation['区间涨跌'], c='blue')
    ax.scatter(0, ret_800, c='orange', s=80)
    ax.scatter(0, ret_stock, c='green', s=80)

    slope, intercept = np.polyfit(allocation['配置差额'], allocation['区间涨跌'], deg=1)
    x = np.array([allocation['配置差额'].min(), allocation['配置差额'].max()])
    y = slope * x + intercept
    ax.plot(x, y, '--', c='grey')

    ax.spines['bottom'].set_position('zero')
    ax.spines['left'].set_position('zero')
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    # 根据数据范围动态调整顶部和右侧边框位置
    if max(allocation['配置差额']) > 0:
        ax.spines['top'].set_position(('data', max(allocation['区间涨跌'])))
    else:
        ax.spines['top'].set_position(('data', min(allocation['区间涨跌'])))

    if max(allocation['区间涨跌']) > 0:
        ax.spines['right'].set_position(('data', max(allocation['配置差额'])))
    else:
        ax.spines['right'].set_position(('data', min(allocation['配置差额'])))

    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.1f}' if x >= 0 else f'-{abs(x):.1f}'))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1f}' if y >= 0 else f'-{abs(y):.1f}'))
    ax.set_xlabel('{0}超配比例'.format(rpt_date), color='red', weight='bold')
    ax.xaxis.set_label_coords(0.5, -0.1)
    ax.set_ylabel('涨跌幅', color='red', weight='bold')
    ax.yaxis.set_label_coords(0.1, 0.5)

    for j in range(len(allocation.index)):
        label = '{0},{1}'.format(allocation.index[j], allocation.iloc[j]['区间涨跌'])
        ax.annotate(label, (allocation.iloc[j]['配置差额'], allocation.iloc[j]['区间涨跌']),
                    textcoords='offset points', xytext=(0, -10), ha='center')
    label_800 = '中证800,{0}'.format(round(ret_800, 2))
    label_stock = '偏股基金,{0}'.format(round(ret_stock, 2))
    ax.annotate(label_800, (0, ret_800), textcoords='offset points', xytext=(0, -10), ha='center', c='orange')
    ax.annotate(label_stock, (0, ret_stock), textcoords='offset points', xytext=(0, -10), ha='center', c='green')

    fig.tight_layout()
    plt.title(figname, fontsize=fontsize_suptitle, weight='bold', loc='center', y=0.96)
    plt.savefig('../周报文件/'+ filename)
    plt.close()

    return allocation
