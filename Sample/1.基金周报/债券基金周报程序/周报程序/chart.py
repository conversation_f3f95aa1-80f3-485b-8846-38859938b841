﻿# -*- coding: utf-8 -*-
"""
Created on Wed Jan  6 13:25:56 2021

@author: gyrx-<PERSON><PERSON><PERSON><PERSON>
"""

import pandas as pd
import numpy as np
import datetime as dt
from dateutil.relativedelta import relativedelta
from scipy import stats
from WindPy import *
from scipy.optimize import lsq_linear
from sklearn.linear_model import LinearRegression
from sklearn.cluster import KMeans
import warnings
warnings.simplefilter(action='ignore',category=FutureWarning)
warnings.simplefilter(action="ignore", category=UserWarning)
w.start()


def PRD_cal(fund_list, startday, endday, benchmark='000906.SH'):
    b = w.wsd(benchmark, "close", startday, endday, "")
    b = pd.DataFrame({'benchmark': b.Data[0]}, index=b.Times)
    fund_nav = w.wsd(fund_list, "NAV_adj", startday, endday, "")
    fund_nav = pd.DataFrame(np.array(fund_nav.Data).T, index=fund_nav.Times, columns=fund_list)
    fund_rtn = fund_nav / fund_nav.shift(1) - 1
    fund_rtn['benchmark'] = b['benchmark'] / b['benchmark'].shift(1) - 1
    fund_rtn = fund_rtn[1:]

    PRD = pd.DataFrame(index=fund_list)
    for i in fund_list:
        df = fund_rtn[[i, 'benchmark']].dropna()
        PRD.loc[i, 'P+'] = df[df['benchmark'] >= 0][i].mean() / df[df['benchmark'] >= 0]['benchmark'].mean()
        PRD.loc[i, 'P-'] = df[df['benchmark'] < 0][i].mean() / df[df['benchmark'] < 0]['benchmark'].mean()
        PRD.loc[i, 'PRD'] = PRD.loc[i, 'P+'] - PRD.loc[i, 'P-']
    return PRD.round(2)


def exposure_cal(fund_list, cal_day, windows):
    zxfg = w.wsd("CI005917.WI,CI005918.WI,CI005919.WI,CI005920.WI,CI005921.WI", "close", "ED-" + str(windows) + "TD",
                 cal_day, "")
    zxfg = pd.DataFrame({'Time': pd.to_datetime(zxfg.Times), '金融': zxfg.Data[0], '周期': zxfg.Data[1],
                         '消费': zxfg.Data[2], '成长': zxfg.Data[3], '稳定': zxfg.Data[4]})
    zxfg = zxfg.set_index('Time')
    zxfg = (zxfg / zxfg.shift(1) - 1) * 100
    zxfg['cons'] = 1
    zxfg = zxfg.dropna()
    fund_nav = w.wsd(fund_list, "NAV_adj", "ED-" + str(windows) + "TD", cal_day, "")
    fund_nav = pd.DataFrame(np.array(fund_nav.Data).T, index=fund_nav.Times, columns=fund_nav.Codes)
    fund_rtn = (fund_nav / fund_nav.shift(1) - 1) * 100
    fund_rtn = fund_rtn.dropna(how='all')

    lb = [0, 0, 0, 0, 0, -np.inf]
    ub = [1, 1, 1, 1, 1, np.inf]
    position = pd.DataFrame(index=fund_list, columns=zxfg.columns)
    for i in fund_list:
        res = lsq_linear(zxfg.loc[fund_rtn[i].dropna().index], fund_rtn[i].dropna(), bounds=(lb, ub), lsmr_tol='auto')
        position.loc[i] = res.x
    position = position.astype(float)
    position[position < 1e-8] = 0
    position['position'] = position['金融'] + position['周期'] + position['消费'] + position['成长']
    return position['position'] * 100

def fof_chart(code, today_str, last1w,last1m, last3m, ytd, last1y):
    cal_day = w.tdaysoffset(-2, today_str, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last1w = w.tdaysoffset(-2, last1w, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last1m = w.tdaysoffset(-2, last1m, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last3m = w.tdaysoffset(-2, last3m, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last1y = w.tdaysoffset(-2, last1y, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    # cal_day=today_str
    fof_chart = pd.DataFrame(index = code)
    fof_chart['简称'] = w.wss(code, "sec_name").Data[0]
    fof_chart['成立日'] = [x.strftime('%Y-%m-%d') for x in w.wss(code, "fund_setupdate").Data[0]]
    fof_chart['估算仓位'] = exposure_cal(code, cal_day, windows = 60)
    fof_chart['收益_1W'] = w.wss(code, "NAV_adj_return", "startDate=" + last1w + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_1W'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last1w + ";endDate=" + cal_day).Data[
        0]
    fof_chart['收益_1M'] = w.wss(code, "NAV_adj_return", "startDate=" + last1m + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_1M'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last1m + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_3M'] = w.wss(code, "NAV_adj_return", "startDate=" + last3m + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_3M'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last3m + ";endDate=" + cal_day).Data[0]
    fof_chart['最大回撤_3M'] = w.wss(code, "risk_maxdownside", "startDate=" + last3m + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_YTD'] = w.wss(code, "NAV_adj_return", "startDate=" + ytd + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_YTD'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + ytd + ";endDate=" + cal_day).Data[0]
    fof_chart['最大回撤_YTD'] = w.wss(code, "risk_maxdownside", "startDate=" + ytd + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_1Y'] = w.wss(code, "NAV_adj_return", "startDate=" + last1y + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_1Y'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last1y + ";endDate=" + cal_day).Data[0]
    fof_chart['最大回撤_1Y'] = w.wss(code, "risk_maxdownside", "startDate=" + last1y + ";endDate=" + cal_day).Data[0]
    fof_chart = fof_chart.sort_values(by='估算仓位', ascending=False)
    return fof_chart.round(2)
def fund_chart(code, sector, benchmark, today_str, last3m, last1y, fund):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index, '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, '基金标签']
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = [100-stats.percentileofscore(fund[fund['基金标签']==sector]['近一周'], x) for x in fund_chart['收益_1W']]
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = [100-stats.percentileofscore(fund[fund['基金标签']==sector]['近一月'], x) for x in fund_chart['收益_1M']]
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = [100-stats.percentileofscore(fund[fund['基金标签']==sector]['近三月'], x) for x in fund_chart['收益_3M']]
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = [100-stats.percentileofscore(fund[fund['基金标签']==sector]['年初至今'], x) for x in fund_chart['收益_YTD']]
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = [100-stats.percentileofscore(fund[fund['基金标签']==sector]['近一年'], x) for x in fund_chart['收益_1Y']]
    fund_chart[['P+_3M', 'P-_3M', 'PRD_3M']] = PRD_cal(code, last3m, today_str, benchmark[sector])
    fund_chart[['P+_1Y', 'P-_1Y', 'PRD_1Y']] = PRD_cal(code, last1y, today_str, benchmark[sector])
    fund_chart = fund_chart.sort_values(by = '收益_1W', ascending=False)
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_YTD', '收益_1Y', 'P+_3M', 'P-_3M', 'PRD_3M', 'P+_1Y', 'P-_1Y', 'PRD_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)
def fund_chart2(code, fund):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index, '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, '基金标签']
    sample = fund[fund.index.isin(code)]
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = sample['近一周'].rank(ascending = False)[fund_chart.index] / sample['近一周'].count() * 100
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = sample['近一月'].rank(ascending = False)[fund_chart.index] / sample['近一月'].count() * 100
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = sample['近三月'].rank(ascending = False)[fund_chart.index] / sample['近三月'].count() * 100
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = sample['年初至今'].rank(ascending = False)[fund_chart.index] / sample['年初至今'].count() * 100
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = sample['近一年'].rank(ascending = False)[fund_chart.index] / sample['近一年'].count() * 100
    fund_chart = fund_chart.sort_values(by = '收益_1W', ascending=False)
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)
def fund_chart3(code, fund, rpt_date):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index, '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, '基金标签']
    fund_chart['权益仓位'] = w.wss(code,"prt_stocktonav","rptDate=" + rpt_date).Data[0]
    fund_chart['转债仓位'] = w.wss(code,"prt_convertiblebondtonav","rptDate=" + rpt_date).Data[0]
    fund_chart['转债仓位'] = fund_chart['转债仓位'].replace(np.nan, 0)
    sample = fund[fund.index.isin(code)]
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = sample['近一周'].rank(ascending = False)[fund_chart.index] / sample['近一周'].count() * 100
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = sample['近一月'].rank(ascending = False)[fund_chart.index] / sample['近一月'].count() * 100
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = sample['近三月'].rank(ascending = False)[fund_chart.index] / sample['近三月'].count() * 100
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = sample['年初至今'].rank(ascending = False)[fund_chart.index] / sample['年初至今'].count() * 100
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = sample['近一年'].rank(ascending = False)[fund_chart.index] / sample['近一年'].count() * 100
    fund_chart = pd.concat([fund_chart.loc[fund_chart[fund_chart['简称'].str.contains('工银')].index],
                      fund_chart.loc[fund_chart[~fund_chart['简称'].str.contains('工银')].index].sort_values(by='收益_1W',
                                                                                          ascending=False)])
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)
def fund_chart4(code, fund):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index, '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, '基金标签2']
    sample = fund[fund.index.isin(code)]
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = sample['近一周'].rank(ascending = False)[fund_chart.index] / sample['近一周'].count() * 100
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = sample['近一月'].rank(ascending = False)[fund_chart.index] / sample['近一月'].count() * 100
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = sample['近三月'].rank(ascending = False)[fund_chart.index] / sample['近三月'].count() * 100
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = sample['年初至今'].rank(ascending = False)[fund_chart.index] / sample['年初至今'].count() * 100
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = sample['近一年'].rank(ascending = False)[fund_chart.index] / sample['近一年'].count() * 100
    fund_chart = pd.concat([fund_chart.loc[fund_chart[fund_chart['简称'].str.contains('工银')].index],
                            fund_chart.loc[fund_chart[~fund_chart['简称'].str.contains('工银')].index].sort_values(
                                by='收益_1W',
                                ascending=False)])
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)
def fund_chart5(code,fund,rpt_date,today_str,i):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = w.wss(code, "sec_name").Data[0]
    fund_chart['基金经理'] = w.wss(code, "fund_fundmanageroftradedate","tradeDate="+today_str).Data[0]
    fund_chart['股票仓位'] = w.wss(code,"prt_stocktonav","rptDate=" + rpt_date).Data[0]
    fund_chart['转债仓位'] = w.wss(code,"prt_convertiblebondtonav","rptDate=" + rpt_date).Data[0]
    fund_chart['转债仓位'] = fund_chart['转债仓位'].replace(np.nan, 0)
    fund_chart['股票仓位'] = fund_chart['股票仓位'].replace(np.nan, 0)
    fund_chart['权益仓位'] = fund_chart['股票仓位'] + fund_chart['转债仓位'] * 0.5

    sample = fund
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '收益_1W']
    fund_chart['排名_1W'] = sample['收益_1W'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_1W'].count())
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '收益_1M']
    fund_chart['排名_1M'] = sample['收益_1M'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_1M'].count())
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '收益_3M']
    fund_chart['排名_3M'] = sample['收益_3M'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_3M'].count() )
    fund_chart['收益_6M'] = fund.loc[fund_chart.index, '收益_6M']
    fund_chart['排名_6M'] = sample['收益_6M'].rank(ascending=False)[fund_chart.index].astype(float) / float(
        sample['收益_6M'].count())
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '收益_YTD']
    fund_chart['排名_YTD'] = sample['收益_YTD'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_YTD'].count())
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '收益_1Y']
    fund_chart['排名_1Y'] = sample['收益_1Y'].rank(ascending=False)[fund_chart.index].astype(float) /float( sample['收益_1Y'].count() )

    fund_chart = pd.concat([fund_chart.loc[fund_chart[fund_chart['简称'].str.contains('工银')].index],
                      fund_chart.loc[fund_chart[~fund_chart['简称'].str.contains('工银')].index].sort_values(by='权益仓位',
                                                                                          ascending=False)])
    rtn = ['收益_1W', '收益_1M', '收益_3M','收益_6M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_6M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)

def fund_chart6(code,fund,rpt_date,today_str,i):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = w.wss(code, "sec_name").Data[0]
    fund_chart['基金经理'] = w.wss(code, "fund_fundmanageroftradedate", "tradeDate=" + today_str).Data[0]
    sample = fund
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '收益_1W']
    fund_chart['排名_1W'] = sample['收益_1W'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_1W'].count())
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '收益_1M']
    fund_chart['排名_1M'] = sample['收益_1M'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_1M'].count())
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '收益_3M']
    fund_chart['排名_3M'] = sample['收益_3M'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_3M'].count())
    fund_chart['收益_6M'] = fund.loc[fund_chart.index, '收益_6M']
    fund_chart['排名_6M'] = sample['收益_6M'].rank(ascending=False)[fund_chart.index].astype(float) / float(
        sample['收益_6M'].count())
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '收益_YTD']
    fund_chart['排名_YTD'] = sample['收益_YTD'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_YTD'].count())
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '收益_1Y']
    fund_chart['排名_1Y'] = sample['收益_1Y'].rank(ascending=False)[fund_chart.index].astype(float) / float(sample['收益_1Y'].count())
    fund_chart = pd.concat([fund_chart.loc[fund_chart[fund_chart['简称'].str.contains('工银')].index],
                            fund_chart.loc[fund_chart[~fund_chart['简称'].str.contains('工银')].index].sort_values(
                                by='收益_1W',
                                ascending=False)])
    rtn = ['收益_1W', '收益_1M', '收益_3M','收益_6M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M','排名_6M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)


def add_quantile(df):
    col = ['估算仓位', '收益_1W', '超额收益_1W', '收益_1M', '超额收益_1M', '收益_3M', '超额收益_3M', '最大回撤_3M', '收益_YTD',
           '超额收益_YTD', '最大回撤_YTD', '收益_1Y', '超额收益_1Y', '最大回撤_1Y']
    a = df[col].quantile(0.75)
    b = df[col].quantile(0.5)
    c = df[col].quantile(0.25)
    df.loc['25分位', col] = a
    df.loc['中位数', col] = b
    df.loc['75分位', col] = c
    return df.round(2)
def cal_rpt_date(rpt_date):
    if '一季报' in rpt_date:
        rpt_date = rpt_date[:4] + '-03-31'
    if '中报' in rpt_date:
        rpt_date = rpt_date[:4] + '-06-30'
    if '三季报' in rpt_date:
        rpt_date = rpt_date[:4] + '-09-30'
    if '年报' in rpt_date:
        rpt_date = rpt_date[:4] + '-12-31'
    return rpt_date


class fundDuration(object):
    def __init__(self, codes, start, end, n=55,
                 rol=8):  # 基金代码，起始日，结束日，回归窗口区间，滚动长度。中金和华宝的久期测算都用的n=55,rol=8。n是回归长度，rol是滚动加总涨跌幅
        self.codes = codes
        self.start, self.end = start, end
        self.rol = rol
        self.n = n

        self.dfNavRt = self.getNavRt()
        self.dfIndex = self.getBondIndex()

        self.dfNavRtRoll = self.dfNavRt.fillna(0).rolling(rol).sum().dropna()
        self.dfIndexRoll = self.dfIndex.fillna(0).rolling(rol).sum().dropna()

        self.dfDur = self.getBondDur()

    def _bondDict(self):
        bondDict = {'CBA05821.CS': u'1到3年利率债', 'CBA05831.CS': u'3到5年利率债', 'CBA05841.CS': u'5到7年利率债',
                    'CBA05851.CS': u'7到10年利率债', \
                    'CBA02711.CS': u'1年以内信用债', 'CBA02721.CS': u'1到3年信用债', 'CBA02731.CS': u'3到5年信用债',
                    'CBA02741.CS': u'5到7年信用债', 'CBA02751.CS': u'7到10年信用债', \
                    'CBA05801.CS': u'利率债综合', 'CBA02701.CS': u'信用债综合', 'CBA01901.CS': u'高等级信用', 'CBA03801.CS': u'高收益信用'}
        return bondDict

    def _qstart(self):
        n = self.n
        n = n + self.rol
        if not w.isconnected(): w.start()

        return w.tdaysoffset(-n, self.start).Data[0][0].strftime('%Y%m%d')

    def getNavRt(self):
        qstart = self._qstart()
        field_key = {'OF': 'NAV_adj_return1', 'WI': 'pct_chg'}
        _, df = w.wsd(','.join(self.codes), field_key[self.codes[0][-2:]], qstart, self.end, usedf=True)

        if len(self.codes) == 1:
            df.columns = [self.codes[0]]
        df.index = [pd.to_datetime(x).strftime('%Y%m%d') for x in df.index]

        srsCount = df.count()
        codes = list(srsCount[srsCount == srsCount.max()].index)  # 数据不全就剔除
        self.codes = codes

        return df.loc[:, codes]

    def getBondIndex(self):
        qstart = self._qstart()
        indexCodes = self._bondDict().keys()
        _, dfIndex = w.wsd(','.join(indexCodes), 'pct_chg', qstart, self.end, "", usedf=True)

        dfIndex.index = [pd.to_datetime(x).strftime('%Y%m%d') for x in dfIndex.index]
        return dfIndex

    def getBondDur(self):
        qstart = self._qstart()
        indexCodes = self._bondDict().keys()
        _, dfDur = w.wsd(','.join(indexCodes), 'duration', qstart, self.end, usedf=True)
        dfDur.index = [pd.to_datetime(x).strftime('%Y%m%d') for x in dfDur.index]

        return dfDur

    def _getSlice(self, code, date):  # 剔除波动过大的交易日
        n = self.n
        i = self.dfNavRtRoll.index.get_loc(date)
        srsNav = self.dfNavRtRoll[code].iloc[i - n:i + 1]
        dfIndex = self.dfIndexRoll.iloc[i - n:i + 1]

        _t = srsNav.apply(np.abs) <= dfIndex.applymap(np.abs).max(axis=1) * 3  # 当日波动超过波动最大的指数的三倍，就剔除
        ids = _t[_t].index

        if any(srsNav.isnull()):
            print(u'非完整数据基金产品，跳过')
            return None, None
        else:
            return srsNav.loc[ids], dfIndex.loc[ids]

    def findLevel1(self, dfIndex, srsNav):  # 最优指数选择
        L = LinearRegression(fit_intercept=True)
        indexCodes = dfIndex.columns

        score, alpha, beta = 0, None, None
        for idCode in indexCodes:
            srsIndex = dfIndex[idCode]
            L.fit(srsIndex.values.reshape(-1, 1), srsNav.values.reshape(-1, 1))
            if L.score(srsIndex.values.reshape(-1, 1), srsNav.values.reshape(-1, 1)) >= score:
                score = L.score(srsIndex.values.reshape(-1, 1), srsNav.values.reshape(-1, 1))  # R方
                level1, alpha, beta = idCode, L.intercept_[0], L.coef_[0][0]
        return level1, alpha, beta

    def _getGroup(self, dfIndex, level1):  # 备选指数选择
        indexCodes = list(dfIndex.columns)
        indexCodes.remove(level1)

        km = KMeans(n_clusters=2)
        t = dfIndex / dfIndex.std()
        km.fit(t.loc[:, indexCodes].values.transpose())
        srsRet = pd.Series(km.labels_, index=indexCodes)
        lstGroup0, lstGroup1 = list(srsRet[srsRet == 0].index), list(srsRet[srsRet == 1].index)

        srsCorr = dfIndex.corr()[level1]
        select0 = lstGroup0[pd.to_numeric(srsCorr[lstGroup0]).argmin()]
        select1 = lstGroup1[pd.to_numeric(srsCorr[lstGroup1]).argmin()]
        return select0, select1

    # 最终回归
    def regForSingleDaySingleCode(self, code, date):  # date是回归日期最后一日，回归date及之前n天
        srsNav, dfIndex = self._getSlice(code, date)
        if srsNav is None:
            return None

        level1, alpha, beta = self.findLevel1(dfIndex, srsNav)

        select0, select1 = self._getGroup(dfIndex, level1)

        dfX = dfIndex.loc[:, [select0, select1]]
        for sel in (select0, select1):
            dfX[sel] -= dfIndex[level1]

        y = srsNav - beta * dfIndex[level1] - alpha

        LineFinal = LinearRegression(fit_intercept=True)
        LineFinal.fit(dfX.values, y)

        srsRet = pd.Series(index=['alpha', level1, select0, select1])

        srsRet['alpha'] = alpha + LineFinal.intercept_
        srsRet[level1] = beta - sum(LineFinal.coef_)
        srsRet.loc[[select0, select1]] = LineFinal.coef_

        return srsRet

    def CalDur(self, code, date, effec_chg=0.5):
        srsRet = self.regForSingleDaySingleCode(code, date)
        lstIndexes = list(srsRet.index[1:])
        durS = (self.dfDur.loc[date, lstIndexes] * srsRet[lstIndexes]).sum()
        absFenmu = 0.0
        for num in range(1, 60):
            i = self.dfNavRt.index.get_loc(date)
            if i >= num:
                numDaysBefore = self.dfNavRt.index[i - num]
                fenmu = (self.dfIndex.loc[numDaysBefore:date, lstIndexes] * srsRet[lstIndexes]).sum().sum()
                if abs(fenmu) > effec_chg:  # 拟合组合指数的波动有效
                    ratio = (self.dfNavRt.loc[numDaysBefore:date, code].sum() - srsRet.alpha * num / float(
                        self.rol)) / fenmu
                    break
                elif abs(fenmu) > absFenmu:
                    ratio = (self.dfNavRt.loc[numDaysBefore:date, code].sum() - srsRet.alpha * num / float(
                        self.rol)) / fenmu
                    absFenmu = abs(fenmu)
        durRet = durS * ratio
        return durRet

    def DurCurve(self, code, effec_chg=0.5):
        all_date = w.tdays(self.start, self.end, "").Data[0]
        all_date = [i.strftime('%Y%m%d') for i in all_date]
        Df_DurCurve = pd.DataFrame(columns=['估计久期'])
        for date in all_date:
            Df_DurCurve.loc[date, '估计久期'] = self.CalDur(code, date, effec_chg=effec_chg)
        return Df_DurCurve


# %%
# fund_bond = fundDuration(['885062.WI','885008.WI'],'20220101','20220630')#'006150.OF'定开基金换成周度数据
# #Df_DurCurve_short=fund_bond.DurCurve('885062.WI')
# Df_DurCurve_long=fund_bond.DurCurve('885008.WI')



# %% 用于估算债券久期的类
class fundDuration_weekly(object):  # 一些基金净值披露是周度频率
    def __init__(self, codes, start, end, n=11, rol=2):
        self.codes = codes
        self.start, self.end = start, end
        self.rol = rol
        self.n = n

        self.dfNavRt = self.getNavRt()
        self.dfIndex = self.getBondIndex()

        self.dfNavRtRoll = self.dfNavRt.rolling(rol).sum().dropna()
        self.dfIndexRoll = self.dfIndex.rolling(rol).sum().dropna()

        self.dfDur = self.getBondDur()

    def _bondDict(self):
        bondDict = {'CBA05821.CS': u'1到3年利率债', 'CBA05831.CS': u'3到5年利率债', 'CBA05841.CS': u'5到7年利率债',
                    'CBA05851.CS': u'7到10年利率债', \
                    'CBA02711.CS': u'1年以内信用债', 'CBA02721.CS': u'1到3年信用债', 'CBA02731.CS': u'3到5年信用债',
                    'CBA02741.CS': u'5到7年信用债', 'CBA02751.CS': u'7到10年信用债', \
                    'CBA05801.CS': u'利率债综合', 'CBA02701.CS': u'信用债综合', 'CBA01901.CS': u'高等级信用', 'CBA03801.CS': u'高收益信用'}
        return bondDict

    def _qstart(self):
        n = self.n
        n = n + self.rol
        if not w.isconnected(): w.start()
        return w.tdaysoffset(-n, self.start, "Period=W").Data[0][0].strftime('%Y%m%d')

    def getNavRt(self):
        qstart = self._qstart()
        field_key = {'OF': 'NAV_adj_return1', 'WI': 'pct_chg'}
        _, df = w.wsd(','.join(self.codes), field_key[self.codes[0][-2:]], qstart, self.end, "Period=W", usedf=True)
        if len(self.codes) == 1:
            df.columns = [self.codes[0]]
        df.index = [pd.to_datetime(x).strftime('%Y%m%d') for x in df.index]

        srsCount = df.count()
        codes = list(srsCount[srsCount == srsCount.max()].index)  # 数据不全就剔除
        self.codes = codes
        return df.loc[:, codes]

    def getBondIndex(self):
        qstart = self._qstart()
        indexCodes = self._bondDict().keys()
        _, dfIndex = w.wsd(','.join(indexCodes), 'pct_chg', qstart, self.end, "Period=W", usedf=True)
        dfIndex.index = [pd.to_datetime(x).strftime('%Y%m%d') for x in dfIndex.index]
        return dfIndex

    def getBondDur(self):
        qstart = self._qstart()
        indexCodes = self._bondDict().keys()

        _, dfDur = w.wsd(','.join(indexCodes), 'duration', qstart, self.end, "Period=W", usedf=True)
        dfDur.index = [pd.to_datetime(x).strftime('%Y%m%d') for x in dfDur.index]
        return dfDur

    def _getSlice(self, code, date):  # 剔除波动过大的交易日
        n = self.n
        i = self.dfNavRtRoll.index.get_loc(date)
        srsNav = self.dfNavRtRoll[code].iloc[i - n:i + 1]
        dfIndex = self.dfIndexRoll.iloc[i - n:i + 1]

        _t = srsNav.apply(np.abs) <= dfIndex.applymap(np.abs).max(axis=1) * 3  # 当日波动超过波动最大的指数的三倍，就剔除
        ids = _t[_t].index

        if any(srsNav.isnull()):
            print(u'非完整数据基金产品，跳过')
            return None, None
        else:
            return srsNav.loc[ids], dfIndex.loc[ids]

    def findLevel1(self, dfIndex, srsNav):  # 最优指数选择
        L = LinearRegression(fit_intercept=True)
        indexCodes = dfIndex.columns

        score, alpha, beta = 0, None, None
        for idCode in indexCodes:
            srsIndex = dfIndex[idCode]
            L.fit(srsIndex.values.reshape(-1, 1), srsNav.values.reshape(-1, 1))
            if L.score(srsIndex.values.reshape(-1, 1), srsNav.values.reshape(-1, 1)) >= score:
                score = L.score(srsIndex.values.reshape(-1, 1), srsNav.values.reshape(-1, 1))  # R方
                level1, alpha, beta = idCode, L.intercept_[0], L.coef_[0][0]
        return level1, alpha, beta

    def _getGroup(self, dfIndex, level1):  # 备选指数选择
        indexCodes = list(dfIndex.columns)
        indexCodes.remove(level1)

        km = KMeans(n_clusters=2)
        t = dfIndex / dfIndex.std()
        km.fit(t.loc[:, indexCodes].values.transpose())
        srsRet = pd.Series(km.labels_, index=indexCodes)
        lstGroup0, lstGroup1 = list(srsRet[srsRet == 0].index), list(srsRet[srsRet == 1].index)

        srsCorr = dfIndex.corr()[level1]
        select0 = lstGroup0[pd.to_numeric(srsCorr[lstGroup0]).argmin()]
        select1 = lstGroup1[pd.to_numeric(srsCorr[lstGroup1]).argmin()]
        return select0, select1

    # 最终回归
    def regForSingleDaySingleCode(self, code, date):  # date是回归日期最后一日，回归date及之前n天
        srsNav, dfIndex = self._getSlice(code, date)
        if srsNav is None:
            return None

        level1, alpha, beta = self.findLevel1(dfIndex, srsNav)

        select0, select1 = self._getGroup(dfIndex, level1)

        dfX = dfIndex.loc[:, [select0, select1]]
        for sel in (select0, select1):
            dfX[sel] -= dfIndex[level1]

        y = srsNav - beta * dfIndex[level1] - alpha

        LineFinal = LinearRegression(fit_intercept=True)
        LineFinal.fit(dfX.values, y)

        srsRet = pd.Series(index=['alpha', level1, select0, select1])

        srsRet['alpha'] = alpha + LineFinal.intercept_
        srsRet[level1] = beta - sum(LineFinal.coef_)
        srsRet.loc[[select0, select1]] = LineFinal.coef_

        return srsRet

    def CalDur(self, code, date, effec_chg=0.5):
        srsRet = self.regForSingleDaySingleCode(code, date)
        lstIndexes = list(srsRet.index[1:])
        durS = (self.dfDur.loc[date, lstIndexes] * srsRet[lstIndexes]).sum()

        absFenmu = 0.0
        for num in range(1, 13):
            i = self.dfNavRt.index.get_loc(date)
            if i >= num:
                numDaysBefore = self.dfNavRt.index[i - num]
                fenmu = (self.dfIndex.loc[numDaysBefore:date, lstIndexes] * srsRet[lstIndexes]).sum().sum()
                if abs(fenmu) > effec_chg:  # 拟合组合指数的波动有效
                    ratio = (self.dfNavRt.loc[numDaysBefore:date, code].sum() - srsRet.alpha * num / float(
                        self.rol)) / fenmu
                    break
                elif abs(fenmu) > absFenmu:
                    ratio = (self.dfNavRt.loc[numDaysBefore:date, code].sum() - srsRet.alpha * num / float(
                        self.rol)) / fenmu
                    absFenmu = abs(fenmu)
        durRet = durS * ratio
        return durRet

    def DurCurve(self, code, effec_chg=0.5):
        all_date = w.tdays(self.start, self.end, "Period=W").Data[0]
        all_date = [i.strftime('%Y%m%d') for i in all_date]
        Df_DurCurve = pd.DataFrame(columns=['估计久期'])
        for date in all_date:
            Df_DurCurve.loc[date, '估计久期'] = self.CalDur(code, date, effec_chg=effec_chg)
        return Df_DurCurve
