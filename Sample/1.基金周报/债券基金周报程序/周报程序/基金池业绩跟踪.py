# %%
# -*- coding: utf-8 -*-
"""
Created on 25/6/2024

@author: gyrx-linyy

用于模拟组合的业绩跟踪
"""

# %%
import numpy as np
import pandas as pd
import datetime as dt
import pickle
import copy
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')
from WindPy import w
import re
w.start()

# %%
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.table import Table
from matplotlib.font_manager import FontProperties
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
import cx_Oracle
import matplotlib.ticker as ticker
import pandas as pd
import numpy as np
import datetime as dt
import pickle
from dateutil.relativedelta import relativedelta
from scipy import stats
import matplotlib.pyplot as plt
import os
import warnings
from chart import fundDuration
import chart as ct
import urllib3
from json import dumps,loads
from plot import *
import copy
import cx_Oracle
from pyecharts.charts import Page
from pyecharts.globals import CurrentConfig
from matplotlib.font_manager import FontProperties
from scipy.stats import spearmanr
import fund_analysis_income
# CurrentConfig.ONLINE_HOST = "Z:/基金精选/4.0 定期报告/pyecharts-assets/assets/"

warnings.filterwarnings("ignore")
oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind', encoding="UTF-8")
fig, ax1 = plt.subplots()#这样后面的设置才能生效

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True

light=0.5
fontsize_subtitle = 18 #16
fontsize_suptitle = 20 #18
fontsize_text = 14 #11
fontsize_legend = 13 #9
plt.rcParams['font.size'] = 16 #12  # 字体大小

def pptsize(ppt_size):
    if ppt_size==43:
        plt.rcParams['figure.figsize']=(16,9)    #(20,7)
    elif ppt_size==207:#蒋总
        plt.rcParams['figure.figsize']=(20,7)
    elif ppt_size==209:#蒋总
        plt.rcParams['figure.figsize']=(20,9)
    elif ppt_size==107:#蒋总
        plt.rcParams['figure.figsize']=(10,7)
    elif ppt_size==109:
        plt.rcParams['figure.figsize']=(10,9)
    elif ppt_size==1045:
        plt.rcParams['figure.figsize']=(10,4.5)
    elif ppt_size==129:
        plt.rcParams['figure.figsize']=(12,9)
    elif ppt_size==75:
        plt.rcParams['figure.figsize']=(7,5)
    else:
        plt.rcParams['figure.figsize']=(20,9)

# %%
#净值
fpath0='../周报文件/'
fpath1="Z:\基金精选\\6.0 其他（临时脚本、数据交换等）\李岳欣\基础数据\\"
fpath2="N:\基金季报分析\季报分析程序"

def get_fund_nav(startDate, endDate):
    # "获取基金净值"
    sqlcode = """
    SELECT PRICE_DATE as pricedt, F_INFO_WINDCODE as fundcode, F_NAV_ADJUSTED as adjnav
    FROM winddf.ChinaMutualFundNAV
    where PRICE_DATE between '%s' and '%s'
    order by F_INFO_WINDCODE, PRICE_DATE
    """% (startDate, endDate)

    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col=['PRICEDT', 'FUNDCODE'])

    return tDATA

try:
    with open(fpath1+'基金净值数据.pkl', "rb") as fp:
        fund_value_data = pickle.load(fp)
except:#第一次跑没这个文件
        fund_value_data=pd.DataFrame()

start_day='20190101'
end_day='20241221'

fund_value_data1 = get_fund_nav('20190101', '20190201')
fund_value_data1 = fund_value_data1.ADJNAV.unstack()
fund_value_data1.index.name='日期'
fund_value_data1=fund_value_data1.reset_index().melt(id_vars='日期', var_name='基金代码', value_name='单位净值')
fund_value_data=pd.concat([fund_value_data,fund_value_data1],axis=0).drop_duplicates(subset=['日期', '基金代码'])



with open(fpath1+'基金净值数据.pkl', "wb") as fp:
    pickle.dump(fund_value_data, fp)

fund_value_data=fund_value_data.pivot(index='日期', columns='基金代码')['单位净值'].sort_index()
fund_value_data.index=pd.to_datetime(fund_value_data.index,format="%Y%m%d").date
fund_value_data=fund_value_data.fillna(method='ffill')
fund_value_data=fund_value_data.join(w.wsd("000906.SH", "close", fund_value_data.index[0].strftime("%Y-%m-%d"), "",usedf=True)[1].rename({'CLOSE':'中证800'},axis=1))
fund_value_data.dropna(subset=['中证800'], inplace=True)#为了对齐日期，删掉非交易日带来的影响
fund_chg_data_all=fund_value_data.pct_change()

# %%

adjust_date=w.tdays(start_day, end_day, "Days=Alldays;Period=M").Data[0]   #季度调池，之后再截断
lm_date=adjust_date[-2].date()
# adjust_date = [dt.datetime(2019,1,1)] + adjust_date[2:-1:3]+[adjust_date[-1]]
adjust_date = adjust_date[0:-1:3]+[adjust_date[-1]]
adjust_date_todate=[i.date() for i in adjust_date]
adjust_date = [i.strftime('%Y%m%d') for i in adjust_date]
adjust_date#可以通过调整adjust_date来调整观察期，以后模拟组合调仓随季度调仓，方便跟踪
today = dt.datetime.strptime(end_day,'%Y%m%d')
today_str = today.strftime('%Y-%m-%d')
last1w = w.tdaysoffset(-4, today, "Period=D").Data[0][0].date() #5个交易日
last1m = w.tdaysoffset(0,(today + relativedelta(months=-1) + relativedelta(days=1)).strftime('%Y-%m-%d'), "Period=D").Data[0][0].date()
last3m = w.tdaysoffset(0, (today + relativedelta(months=-3) + relativedelta(days=1)).strftime('%Y-%m-%d'), "Period=D").Data[0][0].date()
last6m = w.tdaysoffset(0, (today + relativedelta(months=-6) + relativedelta(days=1)).strftime('%Y-%m-%d'), "Period=D").Data[0][0].date()
ytd = str(today.year) +'-01-01'
ytd = w.tdaysoffset(0, ytd, "Period=D").Data[0][0].date()
last1y = w.tdaysoffset(0, (today + relativedelta(years=-1) + relativedelta(days=1)).strftime('%Y-%m-%d'), "Period=D").Data[0][0].date()


#根据基金净值计算基金池的收益表现
label_lst = ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债']
level_lst = ['可买池', '基础池', '重点池']
date_lst = {'1W': last1w, '1M': last1m, '3M': last3m, '6M': last6m, 'YTD': ytd, '1Y': last1y}
dict_graph = dict()


# %%
fund_benchmark_dict={'A股全市场策略':'930950.CSI','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ','新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI','医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH','金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI','一级债基':'885006.WI','低含权':'885006.WI','中含权':'885007.WI','高含权':'885003.WI','转债':'000832.CSI','中短久期':'885062.WI','长久期':'885008.WI'}
fund_benchmark_name={'A股全市场策略':'偏股基金','A股价值策略':'国证价值','一级债基':'一级债基','低含权':'一级债基','中含权':'二级债基','高含权':'偏债基金','转债':'中证转债','中短久期':'短债基金','长久期':'中长债基金'}


df_index_all = w.wsd(['000832.CSI','930950.CSI','930609.CSI','885006.WI','885007.WI','885062.WI','000300.SH','000905.SH'], "close", start_day,end_day, "",usedf = True)[1]
df_index_all = df_index_all.rename(columns={"000832.CSI": '中证转债',"930950.CSI": '偏股基金指数',"885007.WI": '二级债基指数','885006.WI':'一级债基指数',"930609.CSI": '中长期纯债基金指数',"885062.WI": '短期纯债基金指数',"000300.SH": '沪深300',"000905.SH": '中证500'})



#先计算基准
df_benchmark = pd.DataFrame()
#高含权基准中枢
eq_level = 50
benchmark = df_index_all[['中长期纯债基金指数','偏股基金指数']]
benchmark_rtn = (benchmark/benchmark.shift(1)-1)[0:]
benchmark_rtn['benchmark'] = benchmark_rtn.iloc[:,0]*(1-eq_level/100)+benchmark_rtn.iloc[:,1]*(eq_level/100)
df_benchmark['高含权'] = (benchmark_rtn['benchmark']+1).cumprod(axis=0)
#中含权基准中枢
eq_level = 22.5
benchmark = df_index_all[['中长期纯债基金指数','偏股基金指数']]
benchmark_rtn = (benchmark/benchmark.shift(1)-1)[0:]
benchmark_rtn['benchmark'] = benchmark_rtn.iloc[:,0]*(1-eq_level/100)+benchmark_rtn.iloc[:,1]*(eq_level/100)
df_benchmark['中含权'] = (benchmark_rtn['benchmark']+1).cumprod(axis=0)
#低含权基准中枢
eq_level = 7.5
benchmark = df_index_all[['中长期纯债基金指数','偏股基金指数']]
benchmark_rtn = (benchmark/benchmark.shift(1)-1)[0:]
benchmark_rtn['benchmark'] = benchmark_rtn.iloc[:,0]*(1-eq_level/100)+benchmark_rtn.iloc[:,1]*(eq_level/100)
df_benchmark['低含权'] = (benchmark_rtn['benchmark']+1).cumprod(axis=0)


cb_level = 10
benchmark = df_index_all[['中长期纯债基金指数','中证转债']]
benchmark_rtn = (benchmark/benchmark.shift(1)-1)[0:]
benchmark_rtn['benchmark'] = benchmark_rtn.iloc[:,0]*(1-cb_level/100)+benchmark_rtn.iloc[:,1]*(cb_level/100)
df_benchmark['一级债基'] = (benchmark_rtn['benchmark']+1).cumprod(axis=0)

benchmark = df_index_all[['中证转债','中长期纯债基金指数','短期纯债基金指数']]
benchmark.columns = ['转债','长久期','中短久期']
df_benchmark[['转债','长久期','中短久期']] = (benchmark/benchmark.iloc[0])[['转债','长久期','中短久期']]

df_benchmark.iloc[0,:]=1




# %%
#业绩跟踪
for date in date_lst.keys():
    dict_graph[date] = pd.DataFrame(index=label_lst, columns=level_lst)

# for label in ['中短久期','长久期','一级债基','低含权','中含权','高含权','转债']:
for label in ['中含权','高含权','转债']:
    benchmark=fund_benchmark_dict[label]
    portfolio_nav_all=pd.DataFrame()
    performance_month=pd.DataFrame()
    rank_month=pd.DataFrame()

    portfolio_chg_kemai=pd.Series()
    portfolio_chg_jichu=pd.Series()
    portfolio_chg_zhongdian=pd.Series()
    portfolio_chg_newin = pd.Series()
    portfolio_chg_out = pd.Series()
    portfolio_chg_invest=pd.Series()

    for i in range(len(adjust_date)-1):
        date_i= adjust_date[i]
        #季度池子
        fpath3='Z:\基金精选\\6.0 其他（临时脚本、数据交换等）\李岳欣\基金池'
        try:
            if i == 0:
                fund_label=pd.read_excel(fpath3+'\\201810_qfp.xlsx',sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                fund_label_bf = pd.read_excel(fpath3 + '\\201807_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[
                    ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            elif i==1:
                fund_label = \
                pd.read_excel(fpath3 + '\\{0}_qfp.xlsx'.format(date_i[:6]), sheet_name='基金池_简版', index_col=0)[
                    ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                fund_label_bf = pd.read_excel(fpath3 + '\\201810_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[
                    ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            else:
                fund_label=pd.read_excel(fpath3+'\\{0}_qfp.xlsx'.format(date_i[:6]),sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                fund_label_bf = pd.read_excel(fpath3 + '\\{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版', index_col=0)[
                    ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]

            fund_label = fund_label[
                (fund_label['是否可买池'] == '是') & (fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动') & (
                            fund_label['资产细分(V)'] == label)]
            fund_label_bf = fund_label_bf[
                (fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (
                        fund_label_bf['资产细分(V)'] == label)]
        except:
            fund_label = pd.read_excel(fpath3 + '\\季度筛选结果明细_{0}.xlsx'.format(date_i[:6]), sheet_name = 'ag-grid',index_col = 2)[
                ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称':'简称','资产细分V':'资产细分(V)','管理方式VI':'管理方式(VI)'})
            try:
                fund_label_bf = \
                pd.read_excel(fpath3 + '\\{0}_qfp.xlsx'.format(adjust_date[i - 1][:6]), sheet_name='基金池_简版',
                              index_col=0)[
                    ['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            except:
                fund_label_bf = \
                pd.read_excel(fpath3 + '\\季度筛选结果明细_{0}.xlsx'.format(adjust_date[i-1][:6]), sheet_name='ag-grid', index_col=2)[
                    ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(
                    columns={'基金简称': '简称', '资产细分V': '资产细分(V)', '管理方式VI': '管理方式(VI)'})

            for j in range(int(np.ceil(len(fund_label.index) / 500))):
                fund_label.loc[fund_label.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = \
                w.wss(fund_label.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
                fund_label_bf.loc[fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = \
                    w.wss(fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
            fund_label = fund_label[(fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动管理') & (
                        fund_label['资产细分(V)'] == label)]
            fund_label = fund_label[fund_label['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]
            try:
                fund_label_bf = fund_label_bf[
                    (fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (
                                fund_label_bf['管理方式(VI)'] == '主动') & (
                            fund_label_bf['资产细分(V)'] == label)]
            except:
                fund_label_bf = fund_label_bf[
                    (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动管理') & (
                            fund_label_bf['资产细分(V)'] == label)]
                fund_label_bf = fund_label_bf[fund_label_bf['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]


        #转化为wind index
        fund_label.index.name='基金代码'
        index_new=[]
        for index in fund_label.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3]+'.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3]+'.SZ')
            elif index[:-3]+'.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3]+'.SH')
            else:
                index_new.append(index)
        fund_label.index=index_new
        fund_label = fund_label.loc[~fund_label.index.duplicated(keep='first')]

        # 转化为wind index
        fund_label_bf.index.name = '基金代码'
        index_new = []
        for index in fund_label_bf.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SZ')
            elif index[:-3] + '.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SH')
            else:
                index_new.append(index)
        fund_label_bf.index = index_new
        fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]

        kemai_pool=fund_label.index.tolist()
        kemai_pool_bf = fund_label_bf.index.tolist()
        try:
            jichu_pool=fund_label[fund_label['是否基础池']=='是'].index.tolist()
            zhongdian_pool=fund_label[fund_label['是否重点池']=='是'].index.tolist()
            try:
                new_in_pool=list((set(jichu_pool)-set(fund_label_bf[fund_label_bf['是否基础池']=='是'].index.tolist()))|(set(zhongdian_pool)-set(fund_label_bf[fund_label_bf['是否重点池']=='是'].index.tolist())))
                out_pool = list((set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist())-set(jichu_pool))|(set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())-set(
                    zhongdian_pool)))
            except:
                new_in_pool = list(
                    (set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()))|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool) )|(set(
                        fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                        zhongdian_pool)))
        except:
            jichu_pool = fund_label[fund_label['公募FOF基金池级别'] == '基础'].index.tolist()
            zhongdian_pool = fund_label[fund_label['公募FOF基金池级别'] == '重点'].index.tolist()
            try:
                new_in_pool = list(
                    (set(jichu_pool) - set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()))|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()) - set(jichu_pool) )|( set(
                        fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist()) - set(
                        zhongdian_pool)))
            except:
                new_in_pool = list(
                    ( set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) )|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool)) | (set(
                        fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                        zhongdian_pool)))


        #可买池
        fund_list=kemai_pool
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
        fund_nav=fund_nav.median(axis=1)
        portfolio_chg_i=fund_nav.pct_change().dropna()
        portfolio_chg_kemai=portfolio_chg_kemai.append(portfolio_chg_i)

        #基础池
        fund_list=jichu_pool
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
        fund_nav=fund_nav.median(axis=1)
        portfolio_chg_i=fund_nav.pct_change().dropna()
        portfolio_chg_jichu=portfolio_chg_jichu.append(portfolio_chg_i)

        #重点池
        fund_list=zhongdian_pool
        if not zhongdian_pool:
            fund_list=jichu_pool
            if not jichu_pool:
                fund_list=kemai_pool
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
        fund_nav=fund_nav.median(axis=1)
        portfolio_chg_i=fund_nav.pct_change().dropna()
        portfolio_chg_zhongdian=portfolio_chg_zhongdian.append(portfolio_chg_i)

        # 新进池
        fund_list = new_in_pool
        fund_nav = fund_value_data.loc[
                   w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
        fund_nav = fund_nav.median(axis=1)
        portfolio_chg_i = fund_nav.pct_change().dropna()
        portfolio_chg_newin = portfolio_chg_newin.append(portfolio_chg_i)

        # 出池
        fund_list = out_pool
        fund_nav = fund_value_data.loc[
                   w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
        fund_nav = fund_nav.median(axis=1)
        portfolio_chg_i = fund_nav.pct_change().dropna()
        portfolio_chg_out = portfolio_chg_out.append(portfolio_chg_i)

    # %%
    #汇总不同日期
    portfolio_nav_all=pd.DataFrame()
    #可买池
    portfolio_nav = pd.Series()
    portfolio_nav=portfolio_nav.append(portfolio_chg_kemai+1)
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
    portfolio_nav=portfolio_nav.sort_index().cumprod()
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'可买池'},axis=1)],axis=1)

    #基础池
    portfolio_nav = pd.Series()
    portfolio_nav=portfolio_nav.append(portfolio_chg_jichu+1)
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
    portfolio_nav=portfolio_nav.sort_index().cumprod()
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'基础池'},axis=1)],axis=1)

    #重点池
    portfolio_nav = pd.Series()
    portfolio_nav=portfolio_nav.append(portfolio_chg_zhongdian+1)
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
    portfolio_nav=portfolio_nav.sort_index().cumprod()
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'重点池'},axis=1)],axis=1)

    # 新进池
    portfolio_nav = pd.Series()
    portfolio_nav = portfolio_nav.append(portfolio_chg_newin + 1)
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
    portfolio_nav = portfolio_nav.sort_index().cumprod()
    portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '每期新进'}, axis=1)], axis=1)

    # 出池
    portfolio_nav = pd.Series()
    portfolio_nav = portfolio_nav.append(portfolio_chg_out + 1)
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
    portfolio_nav = portfolio_nav.sort_index().cumprod()
    portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '汇总出池'}, axis=1)], axis=1)

    portfolio_nav_all=portfolio_nav_all.join(w.wsd(benchmark, "close", portfolio_nav_all.index[0].strftime("%Y-%m-%d"), "","Days=Alldays;Fill=Previous",usedf=True)[1].rename({'CLOSE':benchmark},axis=1))
    portfolio_nav_all=portfolio_nav_all.fillna(method='ffill')/portfolio_nav_all.iloc[0]
    portfolio_nav_all['重点池/可买池']=(portfolio_nav_all['重点池']/portfolio_nav_all['可买池']-1)*100
    portfolio_nav_all=portfolio_nav_all.rename({benchmark:fund_benchmark_name[label]},axis=1)


    # %%
    color_dict={fund_benchmark_name[label]:(0.7,0.4,0),'可买池':'orange','基础池':(0.5,0.3,0.8),'重点池':'purple','模拟池':'blue','每期新进':'red','汇总出池':'green'}
    line_dict={fund_benchmark_name[label]:'--','可买池':'--','基础池':'--','重点池':'--','模拟池':'-','每期新进':'--','汇总出池':'--'}

    # %%
    #超额图
    df=portfolio_nav_all.copy()
    pptsize(209)
    fig_name = label+'_基金池业绩走势跟踪'+end_day
    jc_date=adjust_date_todate[0]
    lm_date = w.tdaysoffset(0, lm_date, "").Data[0][0].date()
    start_day_date=dt.datetime.strptime(dt.datetime.strptime(start_day,'%Y%m%d').strftime('%Y-%m-%d'),'%Y-%m-%d').date()
    text_mark3 = df.iloc[-1][[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']] / df.loc[start_day_date][[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']]
    text_mark3 = ['区间收益：','{0}：'.format(fund_benchmark_name[label]),str(round((text_mark3[fund_benchmark_name[label]]-1)*100,2)),'，可买池：',str(round((text_mark3['可买池']-1)*100,2)),'，基础池：',str(round((text_mark3['基础池']-1)*100,2)),'，重点池：',str(round((text_mark3['重点池']-1)*100,2)),'，每期新进：',str(round((text_mark3['每期新进']-1)*100,2)),'，汇总出池：',str(round((text_mark3['汇总出池']-1)*100,2)),'，超额：',str(round((text_mark3['重点池']-text_mark3['可买池'])*100,2))]
    text_mark3 = ''.join(text_mark3)
    text_mark=text_mark3

    fig, ax1 = plt.subplots()
    for column in [fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']:
        ax1.plot(df.index, df[column].tolist(), label = column, color=color_dict[column], alpha = 0.7 ,linestyle=line_dict[column])
        ax1.text(df.index[-1], df[column].tolist()[-1],
                        f'{column}: {df[column].tolist()[-1]:.4f}', color=color_dict[column], verticalalignment='bottom',fontsize=fontsize_text-6)#rotation=90,
    ax1.tick_params(axis='y',labelsize=fontsize_legend)
    ax1.spines['right'].set_color((0,0,1,0.5))
    ax1.spines['top'].set_visible(False)
    plt.xticks(rotation=0)

    #近一月
    ax1.axvline(x=lm_date,color=(0.7,0.7,0.7),linestyle='--',linewidth=0.5)
    plt.grid(alpha=light)

    #超额
    ax2 = plt.twinx()
    column='重点池/可买池'
    ax2.fill_between(df.index, 0, df[column].tolist(), facecolor = 'blue', alpha = 0.3) #light)
    ax2.text(df.index[-1], df[column].tolist()[-1],
                        f'{column}: {df[column].tolist()[-1]:.2f}', color=(0,0,1,0.5), verticalalignment='bottom',fontsize=fontsize_text-6)
    ax2.set_ylabel('重点池相对可买池收益(%)',fontsize=fontsize_legend,color=(0,0,1,0.5))
    ax2.tick_params(axis='y',labelsize=fontsize_legend,color=(0,0,1,0.5),labelcolor=(0,0,1,0.5))
    ax2.spines['right'].set_color((0,0,1,0.5))
    ax2.spines['top'].set_visible(False)
    ax2.text(1,0.03, text_mark, transform=ax2.transAxes, horizontalalignment='right',verticalalignment='bottom',fontsize=fontsize_text-2)

    plt.gcf().autofmt_xdate()
    plt.title(fig_name,fontsize=fontsize_suptitle)

    plt.show()
    fig.savefig(fpath0+'{0}.jpg'.format(fig_name))
    plt.close()

    for date in date_lst.keys():
        dict_graph[date].loc[label,'重点池'] =(df.iloc[-1][['重点池']] / df.loc[date_lst[date]][['重点池']]).values[0]-1
        dict_graph[date].loc[label, '可买池'] = (df.iloc[-1][['可买池']] / df.loc[date_lst[date]][['可买池']]).values[0]-1
        dict_graph[date].loc[label, '基础池'] = (df.iloc[-1][['基础池']] / df.loc[date_lst[date]][['基础池']]).values[0]-1

    # output=fund_analysis_income.cal_PRD(portfolio_nav_all['重点池'], df_benchmark[label], 60, label)
    pptsize(109)

    fund_analysis_income.fund_ability(portfolio_nav_all['重点池'], df_benchmark[label], label,'重点池','PRD(重点相对基准)','Alpha(重点相对基准)' ,windows=60)
    fund_analysis_income.fund_ability(portfolio_nav_all['可买池'], df_benchmark[label], label, '可买池','PRD(可买相对基准)','Alpha(可买相对基准)',windows=60)

    print(label)

#
#
# work_day = w.tdaysoffset(0, today_str, "Period=D").Data[0][0].strftime('%Y-%m-%d')#计算离所选日期最近的工作日
# # 画债券基金池的图
# plt_industry_pool(dict_graph['1W']*100,'近一周基金池收益(' + work_day + ')','近一周基金池收益')
# plt_industry_pool(dict_graph['1M']*100,'近一月基金池收益(' + work_day + ')','近一月基金池收益')
# plt_industry_pool(dict_graph['3M']*100,'近三月基金池收益(' + work_day + ')','近三月基金池收益')
# plt_industry_pool(dict_graph['6M']*100,'近六月基金池收益(' + work_day + ')','近六月基金池收益')
# plt_industry_pool(dict_graph['YTD']*100,'年初至今基金池收益(' + work_day + ')','年初至今基金池收益')
# plt_industry_pool(dict_graph['1Y']*100,'近一年基金池收益(' + work_day + ')','近一年基金池收益')

