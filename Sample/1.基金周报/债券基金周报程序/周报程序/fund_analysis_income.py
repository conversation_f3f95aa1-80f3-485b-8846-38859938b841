# -*- coding: utf-8 -*-
"""
Created on Wed Jan  6 13:25:56 2021

@author: gyrx-liuzhao
"""

import pandas as pd
import numpy as np
import datetime as dt
import statsmodels.api as sm
from dateutil.relativedelta import relativedelta
today = dt.datetime.today()+ relativedelta(days=-1)
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
from WindPy import *

w.start()
def pptsize(ppt_size):
    if ppt_size==43:
        plt.rcParams['figure.figsize']=(16,9)    #(20,7)
    elif ppt_size==207:#蒋总
        plt.rcParams['figure.figsize']=(20,7)
    elif ppt_size==209:#蒋总
        plt.rcParams['figure.figsize']=(20,9)
    elif ppt_size==107:#蒋总
        plt.rcParams['figure.figsize']=(10,7)
    elif ppt_size==109:
        plt.rcParams['figure.figsize']=(10,9)
    elif ppt_size==1045:
        plt.rcParams['figure.figsize']=(10,4.5)
    elif ppt_size==129:
        plt.rcParams['figure.figsize']=(12,9)
    elif ppt_size==75:
        plt.rcParams['figure.figsize']=(7,5)
    else:
        plt.rcParams['figure.figsize']=(20,9)
#直接改成输入基金的日涨跌幅
def cal_PRD(fund, benchmark, windows,fundtype,fundpooltype):
    pptsize(109)
    fund = fund / fund.shift(1) - 1
    benchmark = benchmark / benchmark.shift(1) - 1
    rtn = pd.concat([fund, benchmark], axis=1, sort=False)
    rtn = rtn.dropna()
    output = pd.DataFrame(index = rtn.index[(windows-1): len(rtn.index)])
    for i in range(windows, len(rtn.index)+1):
        temp = rtn.iloc[(i-windows): i]
        temp1 = temp[temp[fundtype] > 0]
        output.loc[temp.index[-1], 'r+_fund'] = temp1[fundpooltype].mean()
        output.loc[temp.index[-1], 'r+_benchmark'] = temp1[fundtype].mean()
        output.loc[temp.index[-1], 'P+'] = output.loc[temp.index[-1], 'r+_fund'] / output.loc[temp.index[-1], 'r+_benchmark']
        temp2 = temp[temp[fundtype] <= 0]
        output.loc[temp.index[-1], 'r-_fund'] = temp2[fundpooltype].mean()
        output.loc[temp.index[-1], 'r-_benchmark'] = temp2[fundtype].mean()
        output.loc[temp.index[-1], 'P-'] = output.loc[temp.index[-1], 'r-_fund'] / output.loc[temp.index[-1], 'r-_benchmark']
        output.loc[temp.index[-1], 'PRD'] = output.loc[temp.index[-1], 'P+'] - output.loc[temp.index[-1], 'P-']
        output.loc[temp.index[-1], '上涨天数'] = str(len(temp1.index)) + '/' +  str(windows)
        x = sm.add_constant(temp[fundtype])
        y = temp[fundpooltype]
        model = sm.OLS(y, x).fit()
        output.loc[temp.index[-1],'IR'] = model.params[0]/np.sqrt(model.mse_resid) * np.sqrt(250)
        output.loc[temp.index[-1],'alpha'] = model.params[0] * 250 * 100
        output.loc[temp.index[-1],'alpha_vol'] = np.sqrt(model.mse_resid) * np.sqrt(250) * 100
    return output

def fund_ability(fund ,benchmark,fundtype,fundpooltype,pic_name1,pic_name2,windows = 250):
    pptsize(109)
    output = cal_PRD(fund, benchmark, windows,fundtype,fundpooltype)
    fig = plt.figure()
    ax1=fig.subplots()
    ax1.plot(output.index, output['P+'], label='P+', color = 'red')
    ax1.plot(output.index, output['P-'], label='P-', color = 'blue')
    ax1.axhline(1, linestyle='--', color='gray', alpha=0.5)
    ax1.legend(loc='upper left')
    ax2 = ax1.twinx()
    ax2.plot(output.index, output['PRD'], label='PRD', color = 'green')
    ax2.axhline(0, linestyle='--', color='gray', alpha=0.5)
    ax2.legend(loc='upper right')
    plt.title(fundtype)
    fig.savefig('../周报文件/' + fundtype + pic_name1 + '.png')
    fig2 = plt.figure()
    ax1=fig2.subplots()
    ax1.plot(output.index, output['alpha'], label='alpha', color = 'red')
    ax1.axhline(0, linestyle='--', color='gray', alpha=0.5)
    ax1.legend(loc='upper left')
    ax2 = ax1.twinx()
    ax2.plot(output.index, output['alpha_vol'], label='alpha_vol', color = 'blue')
    ax2.legend(loc='upper right')
    plt.title(fundtype)
    fig2.savefig('../周报文件/' + fundtype+ pic_name2 + '.png')
    return output


# def cal_PRD_two(fund, benchmark1, benchmark2,eq_level,start, end, windows):
#     rtn = w.wsd(fund, "NAV_adj", start, end)
#     rtn = pd.DataFrame({fund: rtn.Data[0]}, index = rtn.Times)
#     rtn[fund] = rtn[fund]/rtn[fund].shift(1) - 1
#     rtn[benchmark1] = w.wsd(benchmark1,"close", start, end).Data[0]
#     rtn[benchmark1] = rtn[benchmark1]/rtn[benchmark1].shift(1) - 1
#     rtn[benchmark2] = w.wsd(benchmark2, "close", start, end).Data[0]
#     rtn[benchmark2] = rtn[benchmark2] / rtn[benchmark2].shift(1) - 1
#     rtn[fundtype]=rtn[benchmark1]*eq_level+(1-eq_level)*rtn[benchmark2]
#     rtn = rtn.dropna()
#     output = pd.DataFrame(index = rtn.index[(windows-1): len(rtn.index)])
#     for i in range(windows, len(rtn.index)+1):
#         temp = rtn.iloc[(i-windows): i]
#         temp1 = temp[temp[fundtype] > 0]
#         output.loc[temp.index[-1], 'r+_fund'] = temp1[fund].mean()
#         output.loc[temp.index[-1], 'r+_benchmark'] = temp1[fundtype].mean()
#         output.loc[temp.index[-1], 'P+'] = output.loc[temp.index[-1], 'r+_fund'] / output.loc[temp.index[-1], 'r+_benchmark']
#         temp2 = temp[temp[fundtype] <= 0]
#         output.loc[temp.index[-1], 'r-_fund'] = temp2[fund].mean()
#         output.loc[temp.index[-1], 'r-_benchmark'] = temp2[fundtype].mean()
#         output.loc[temp.index[-1], 'P-'] = output.loc[temp.index[-1], 'r-_fund'] / output.loc[temp.index[-1], 'r-_benchmark']
#         output.loc[temp.index[-1], 'PRD'] = output.loc[temp.index[-1], 'P+'] - output.loc[temp.index[-1], 'P-']
#         output.loc[temp.index[-1], '上涨天数'] = str(len(temp1.index)) + '/' +  str(windows)
#         x = sm.add_constant(temp[fundtype])
#         y = temp[fund]
#         model = sm.OLS(y, x).fit()
#         output.loc[temp.index[-1],'IR'] = model.params[0]/np.sqrt(model.mse_resid) * np.sqrt(250)
#         output.loc[temp.index[-1],'alpha'] = model.params[0] * 250 * 100
#         output.loc[temp.index[-1],'alpha_vol'] = np.sqrt(model.mse_resid) * np.sqrt(250) * 100
#     return output
#
# def fund_ability_two(fund ,benchmark1, benchmark2, eq_level,start,windows ):
#     output = cal_PRD_two(fund, benchmark1,benchmark2,eq_level,start, today, windows)
#     fig = plt.figure()
#     ax1=fig.subplots()
#     ax1.plot(output.index, output['P+'], fundtype='P+', color = 'red')
#     ax1.plot(output.index, output['P-'], fundtype='P-', color = 'blue')
#     ax1.axhline(1, linestyle='--', color='gray', alpha=0.5)
#     ax1.legend(loc='upper left')
#     ax2 = ax1.twinx()
#     ax2.plot(output.index, output['PRD'], fundtype='PRD', color = 'green')
#     ax2.legend(loc='upper right')
#     plt.title(w.wss(fund,"sec_name").Data[0][0])
#     fig2 = plt.figure()
#     ax1=fig2.subplots()
#     ax1.plot(output.index, output['alpha'], fundtype='alpha', color = 'red')
#     ax1.axhline(0, linestyle='--', color='gray', alpha=0.5)
#     ax1.legend(loc='upper left')
#     ax2 = ax1.twinx()
#     ax2.plot(output.index, output['alpha_vol'], fundtype='alpha_vol', color = 'blue')
#     ax2.legend(loc='upper right')
#     plt.title(w.wss(fund,"sec_name").Data[0][0])
#     return output