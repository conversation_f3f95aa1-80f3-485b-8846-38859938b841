# -*- coding: utf-8 -*-
"""
Created on Thu Nov  2 14:11:19 2023

@author: gyrx-zhaomh
"""

import pandas as pd
import os
import cx_Oracle

os.environ['NLS_LANG']='SIMPLIFIED CHINESE_CHINA.UTF8'
engine = cx_Oracle.connect('wind_read/Wind_read_100010@192.168.105.38:1521/wind', encoding="UTF-8")

#%%
def Fund_StockHolding_Citic(WindCode,str_date):
    
    ##中信行业分类下的基金组合持仓信息
    ##输入参数中的基金代码列表、定期报告日期列表，先处理成SQL语句支持的形式

    WindCode=WindCode.str.extract('([0-9]+)',expand=True)  ##将代码中的.OF去掉
    WindCode='\''+ WindCode[0].str.cat(sep='\',\'') + '\''
    str_date='\''+str_date.str.cat(sep='\',\'') + '\''
    
    sql='''
    select substr(a.s_info_windcode,1,6) as 基金代码, 
    a.S_INFO_STOCKWINDCODE as 股票代码, 
    b.S_INFO_NAME as 股票简称,
    d.industriesname as 中信行业,
    a.F_PRT_STKVALUETONAV as 持股比例,
    a.f_prt_enddate as 报告日期
    from winddf.ChinaMutualFundStockPortfolio a 
    inner join winddf.AShareDescription b
    on substr(a.S_INFO_STOCKWINDCODE,1,6)=substr(b.s_info_windcode,1,6)
    inner join winddf.AShareIndustriesClassCITICS c 
    on a.s_info_stockwindcode=c.s_info_windcode
    inner join winddf.AShareIndustriesCode d
    --查询对应的最新中信一级行业
    on substr(c.citics_ind_code,1,4)=substr(d.industriescode,1,4) and d.levelnum='2' and c.cur_sign='1'
    where substr(a.S_INFO_WINDCODE,1,6) in 
     ('''
    
    sql=sql+WindCode
    sql=sql+ ') and a.F_PRT_ENDDATE in ('
    sql=sql+str_date
    
    sql= sql+') order by 报告日期,基金代码,持股比例 Desc'
    
#    print(sql)
    results=pd.read_sql(sql,engine)
    return results

def Fund_StockHolding_SW(WindCode,str_date):
    
    ##申万行业分类下的基金组合持仓信息
    ##输入参数中的基金代码列表、定期报告日期列表，先处理成SQL语句支持的形式

    WindCode=WindCode.str.extract('([0-9]+)',expand=True)  ##将代码中的.OF去掉
    WindCode='\''+ WindCode[0].str.cat(sep='\',\'') + '\''
    str_date='\''+str_date.str.cat(sep='\',\'') + '\''
    
    sql='''
    select substr(a.s_info_windcode,1,6) as 基金代码, 
    a.S_INFO_STOCKWINDCODE as 股票代码, 
    b.S_INFO_NAME as 股票简称,
    d.industriesname as 申万行业,
    a.F_PRT_STKVALUETONAV as 持股比例,
    a.f_prt_enddate as 报告日期
    from winddf.ChinaMutualFundStockPortfolio a 
    inner join winddf.AShareDescription b
    on substr(a.S_INFO_STOCKWINDCODE,1,6)=substr(b.s_info_windcode,1,6)
    inner join winddf.AShareSWIndustriesClass c 
    on a.s_info_stockwindcode=c.s_info_windcode
    inner join winddf.AShareIndustriesCode d
    --查询对应的最新申万一级行业
    on substr(c.SW_IND_CODE,1,4)=substr(d.industriescode,1,4) and d.levelnum='2' and c.cur_sign='1'
    where substr(a.S_INFO_WINDCODE,1,6) in 
     ('''
    
    sql=sql+WindCode
    sql=sql+ ') and a.F_PRT_ENDDATE in ('
    sql=sql+str_date
    
    sql= sql+') order by 报告日期,基金代码,持股比例 Desc'
    
#    print(sql)
    results=pd.read_sql(sql,engine)
    return results


def Fund_StockHolding_SW_HK(WindCode,str_date):
    
    ##申万行业分类下的基金组合持仓信息，涵盖沪港通基金
    ##输入参数中的基金代码列表、定期报告日期列表，先处理成SQL语句支持的形式

    if isinstance(WindCode,str):
        WindCode='\''+WindCode+'\''
    else:
        WindCode='\''+WindCode.str.cat(sep='\',\'')+'\'' 
    if isinstance(str_date,str):
        str_date='\''+str_date+'\''
    else:
        str_date='\''+str_date.str.cat(sep='\',\'')+'\'' 
        
    sql='''
    select substr(a.s_info_windcode,1,6) as 基金代码, 
    a.S_INFO_STOCKWINDCODE as 股票代码, 
    b.S_INFO_NAME as 股票简称,
    d.industriesname as 申万行业,
    a.F_PRT_STKVALUETONAV as 持股比例,
    a.f_prt_enddate as 报告日期
    from winddf.ChinaMutualFundStockPortfolio a 
    inner join winddf.AShareDescription b
    on substr(a.S_INFO_STOCKWINDCODE,1,6)=substr(b.s_info_windcode,1,6)
    inner join winddf.AShareSWIndustriesClass c 
    on a.s_info_stockwindcode=c.s_info_windcode
    inner join winddf.AShareIndustriesCode d
    --查询对应的最新申万一级行业
    on substr(c.SW_IND_CODE,1,4)=substr(d.industriescode,1,4) and d.levelnum='2' and c.cur_sign='1'
    where substr(a.S_INFO_WINDCODE,1,6) in 
     ('''
    
    sql=sql+WindCode
    sql=sql+ ') and a.F_PRT_ENDDATE in ('
    sql=sql+str_date
    
    sql= sql+') union '
    
    sql=sql+'''
    
    select substr(a.s_info_windcode,1,6) as 基金代码,
    a.S_INFO_STOCKWINDCODE as 股票代码, b.S_INFO_NAME as 股票简称,
    d.industriesname as 申万行业,a.F_PRT_STKVALUETONAV as 持股比例,
    a.f_prt_enddate as 报告日期
    from winddf.ChinaMutualFundStockPortfolio a 
    inner join winddf.HKShareDescription b
    on substr(a.S_INFO_STOCKWINDCODE,1,6)=substr(b.s_info_windcode,1,6)
    inner join winddf.HKShareSWIndustriesClass c 
    on a.s_info_stockwindcode=c.s_info_windcode
    inner join winddf.AShareIndustriesCode d
    --查询对应的最新申万一级行业
    on substr(c.SW_IND_CODE,1,4)=substr(d.industriescode,1,4) 
    and d.levelnum='2' and c.cur_sign='1'
    where substr(a.S_INFO_WINDCODE,1,6) in 
    ('''
    sql=sql+WindCode
    sql=sql+') and a.F_PRT_ENDDATE in ('
    sql=sql+str_date
    sql=sql+') order by 报告日期,基金代码,持股比例 Desc'

    
#    print(sql)
    results=pd.read_sql(sql,engine)
    return results


def Fund_StockHolding_SW_HK2(WindCode,str_date):
    ##新增申万二级分类
    ##申万行业分类下的基金组合持仓信息，涵盖沪港通基金
    ##输入参数中的基金代码列表、定期报告日期列表，先处理成SQL语句支持的形式

    if isinstance(WindCode,str):
        WindCode='\''+WindCode+'\''
    else:
        WindCode='\''+WindCode.str.cat(sep='\',\'')+'\'' 
    if isinstance(str_date,str):
        str_date='\''+str_date+'\''
    else:
        str_date='\''+str_date.str.cat(sep='\',\'')+'\'' 
        
    sql='''
    select substr(a.s_info_windcode,1,6) as 基金代码, 
    a.S_INFO_STOCKWINDCODE as 股票代码, 
    b.S_INFO_NAME as 股票简称,
    d.industriesname as 申万一级行业,
    e.industriesname as 申万二级行业,
    a.F_PRT_STKVALUETONAV as 持股比例,
    a.F_PRT_STKVALUE as 持股市值,
    a.f_prt_enddate as 报告日期 
    from winddf.ChinaMutualFundStockPortfolio a 
    inner join winddf.AShareDescription b on substr(a.S_INFO_STOCKWINDCODE,1,6)=substr(b.s_info_windcode,1,6) 
    inner join winddf.AShareSWNIndustriesClass c on a.s_info_stockwindcode=c.s_info_windcode 
    inner join winddf.AShareIndustriesCode d on substr(c.SW_IND_CODE,1,4)=substr(d.industriescode,1,4) and d.levelnum='2' and c.cur_sign='1' 
    inner join winddf.AShareIndustriesCode e on substr(c.SW_IND_CODE,1,6)=substr(e.industriescode,1,6) and e.levelnum='3' and c.cur_sign='1' 
    where substr(a.S_INFO_WINDCODE,1,6) in 
     ('''
    
    sql=sql+WindCode
    sql=sql+ ') and a.F_PRT_ENDDATE in ('
    sql=sql+str_date
    
    sql= sql+') union '
    
    sql=sql+'''
    
    select substr(a.s_info_windcode,1,6) as 基金代码,
    a.S_INFO_STOCKWINDCODE as 股票代码, 
    b.S_INFO_NAME as 股票简称,
    d.industriesname as 申万一级行业,
    e.industriesname as 申万二级行业,
    a.F_PRT_STKVALUETONAV as 持股比例,
    a.F_PRT_STKVALUE as 持股市值,
    a.f_prt_enddate as 报告日期 
    from winddf.ChinaMutualFundStockPortfolio a 
    inner join winddf.HKShareDescription b on substr(a.S_INFO_STOCKWINDCODE,1,6)=substr(b.s_info_windcode,1,6) 
    inner join winddf.HKShareSWNIndustriesClass c on a.s_info_stockwindcode=c.s_info_windcode 
    inner join winddf.AShareIndustriesCode d on substr(c.SW_IND_CODE,1,4)=substr(d.industriescode,1,4) and d.levelnum='2' and c.cur_sign='1' 
    inner join winddf.AShareIndustriesCode e on substr(c.SW_IND_CODE,1,6)=substr(e.industriescode,1,6) and e.levelnum='3' and c.cur_sign='1' 
    where substr(a.S_INFO_WINDCODE,1,6) in 
    ('''
    sql=sql+WindCode
    sql=sql+') and a.F_PRT_ENDDATE in ('
    sql=sql+str_date
    sql=sql+') order by 报告日期,基金代码,持股比例 Desc'

    results=pd.read_sql(sql,engine)
    return results

def  Index_StockHolding_800_SW(strdate):
    
    ##查询某一时点中证800申万行业分类
    
    sql='''
    
    select d.industriesname as 申万行业,
    sum(a.weight)/100 as 行业权重
    from winddf.AIndexCSI800Weight a
    inner join winddf.asharedescription b
    on a.s_con_windcode = b.s_info_windcode
    inner join winddf.AShareSWIndustriesClass c
    on a.s_con_windcode = c.s_info_windcode
    inner join winddf.ashareindustriescode d
    on substr(c.SW_IND_CODE,1,4)=substr(d.industriescode,1,4) 
    and d.levelnum='2' and c.cur_sign='1'
    where a.trade_dt in ('''
    sql=sql+strdate
    sql=sql+' ) group by d.industriesname order by 行业权重 desc'

    
    results=pd.read_sql(sql,engine)
    return results

def Fund_CBondHolding_SW(WindCode,strdate):
    
    if isinstance(WindCode,str):
        WindCode='\''+WindCode+'\''
    else:
        WindCode='\''+WindCode.str.cat(sep='\',\'')+'\''  
    
    if isinstance(strdate,str):
        strdate='\''+strdate+'\''
    else:
        strdate='\''+strdate.str.cat(sep='\',\'') + '\''    
    
    
    sql='''
    
    --查询基金持仓可转债债券信息，并对应到正股信息
    --输入：基金代码、报告期
    --输出：基金组合所持转债信息、转债对应正股信息


    select substr(a.s_info_windcode,1,6) as 基金交易代码,
    a.s_info_bondwindcode as 债券代码,
    b.s_info_name as 债券简称,
    c.s_info_industryname as 债券类型,
    a.f_prt_bdvalue as 持券市值,
    a.f_prt_bdvaluetonav as 持券比例,
    e.s_info_windcode as 正股代码,
    e.s_info_name as 股票简称,
    x.industriesname as 申万行业,
    a.f_prt_enddate as 报告期
    from  winddf.ChinaMutualFundBondPortfolio a
    --b表给出债券简称
    inner join winddf.CBondDescription b
    on a.s_info_bondwindcode=b.s_info_windcode
    --c表给出债券类型
    inner join winddf.CBondIndustryWind c
    on a.s_info_bondwindcode=c.s_info_windcode
    --d表作为过渡表，给出转债公司代码，用于连接股票信息表
    inner join winddf.CCBondIssuance d
    on a.s_info_bondwindcode=d.s_info_windcode
    --e表给出正股信息
    inner join winddf.AShareDescription e
    on d.S_INFO_COMPCODE=e.s_info_compcode
    --s、x表给出股票所属申万行业
    inner join winddf.AShareSWIndustriesClass s 
    on e.s_info_windcode=s.s_info_windcode
    inner join winddf.AShareIndustriesCode x
    --查询对应的最新申万一级行业
    on substr(s.SW_IND_CODE,1,4)=substr(x.industriescode,1,4) and x.levelnum='2' and s.cur_sign='1'

    where substr(a.s_info_windcode,1,6) in ('164808')
    and a.f_prt_enddate in ('20190630')
    and c.s_info_industryname in ('可转债','可交换债')
    order by a.s_info_windcode asc,a.f_prt_enddate asc,a.f_prt_bdvaluetonav desc
        
    '''
    
    
    
    results=pd.read_sql(sql,engine)
    return results


def  Fund_FOF_Holding(WindCode,strdate):
    
    ##根据提供的FOF基金代码及报告期，提取FOF基金的持仓信息（仅限持有的基金）

#    输入：FOF基金代码（6位）、定期报告日期
#    输出：FOF基金持仓基金信息
    
    if isinstance(WindCode,str):
        WindCode='\''+WindCode+'\''
    else:
        WindCode='\''+WindCode.str.cat(sep='\',\'')+'\''    

    if isinstance(strdate,str):
        strdate='\''+strdate+'\''
    else:
        strdate='\''+strdate.str.cat(sep='\',\'')+'\''       
    
    sql='''
    --查询FOF基金持仓基金情况
    --输入：FOF基金代码（6位）、定期报告日期
    --输出：FOF基金持仓基金信息
    
    select substr(a.s_info_windcode,1,6) as FOF代码,
    b.f_info_name as FOF简称,
    b.F_INFO_FULLNAME as FOF全称,
    substr(a.s_info_holdwindcode,1,6) as 基金交易代码,
    c.f_info_name as WIND简称,
    a.valuetonav/100 as 持仓占比,
    a.value as 持仓市值,
    a.end_dt as 报告日期
    from winddf.CMFOtherPortfolio a
    inner join winddf.chinamutualfunddescription b
    on a.s_info_windcode=b.f_info_windcode
    inner join winddf.chinamutualfunddescription c
    on a.s_info_holdwindcode=c.f_info_windcode
    where 
    substr(a.s_info_windcode,1,6) in ('''
    sql=sql+WindCode
    sql=sql+') and a.end_dt in ('
    sql=sql+strdate
    sql=sql+') order by 报告日期,FOF代码,持仓占比 desc'
    
    result=pd.read_sql(sql,engine)
    
    return result