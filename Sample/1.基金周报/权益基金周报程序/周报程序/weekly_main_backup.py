#%%导入所需的库和模块
import os  # 操作系统相关功能
import chart as ct  # 自定义图表模块
import copy  # 深拷贝对象
import cx_Oracle  # Oracle数据库连接
# cx_Oracle.init_oracle_client(os.path.join('.', 'tools', 'clt64', 'instantclient_11_2'))  # 初始化Oracle客户端
import datetime as dt  # 日期时间处理
import matplotlib.pyplot as plt  # 绘图库
import matplotlib.dates as mdates  # 处理日期格式
import numpy as np  # 数值计算
import pandas as pd  # 数据分析
import pickle  # 序列化和反序列化Python对象
import seaborn as sns  # 统计数据可视化
import urllib3  # HTTP客户端
import warnings
warnings.filterwarnings("ignore")  # 忽略警告信息
from datetime import timedelta  # 时间差
from dateutil.relativedelta import relativedelta  # 处理相对日期
from json import dumps,loads  # JSON数据处理
from matplotlib.colors import LinearSegmentedColormap  # 自定义颜色映射
from matplotlib.font_manager import FontProperties  # 字体属性
from matplotlib.table import Table  # 表格绘制
from openpyxl import load_workbook  # Excel文件处理
from plotFunc import *  # 导入自定义绘图函数
from scipy import stats  # 统计函数
from WindPy import w,datetime  # Wind金融终端API
w.start()  # 启动Wind API连接
fpath_draw = '图表'  # 图表保存路径
# 设置matplotlib绘图参数
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 设置字体为微软雅黑，支持中文显示
plt.rcParams['axes.unicode_minus']=False  # 正确显示负号
plt.rcParams['figure.dpi']=400  # 设置图像分辨率
plt.rcParams['lines.linewidth']=1  # 设置线宽
plt.rcParams['figure.autolayout']=True  # 自动调整布局

# 定义计时函数，用于记录代码执行时间并输出
def printtime(t):
    if t!=1:
        print('-----' * 5+'花费%s秒'%(dt.datetime.now()-t))  # 输出每个部分的执行时间
        t=dt.datetime.now()  # 更新时间点
    else:
        print('—' * 25+'共费%s秒'%(dt.datetime.now()-t0))  # 输出总执行时间
    return t
t0=dt.datetime.now()  # 记录程序开始时间
t=t0  # 初始化时间点


###1、每次运行需更改：today
###2、需检查：last1w和fof_date的偏移量（没有假期不涉及）、pool_dates（近一年涉及的四个基金池+前移两个基金池
###3、季度调整：rpt_date、pool_date、所有基金重仓或持仓数据的pkl文件

# 设置基准日期和时间范围
today = dt.datetime.strptime('20250222','%Y%m%d')           # 上周六，程序运行的基准日期
today_str = today.strftime('%Y-%m-%d')  # 格式化日期字符串
rpt_date = '20241231'                                       # 季报日期，等对应季报出来再更新，目前对应的是24年三季报
pool_date = '20250131'                                      # 季度基金池调池日（日历日，0131，0430，0731，1031）
                                                            # 更新季度基金池记得同步更新datei_dict
# 近一年的基金池调整日期列表
pool_dates = ['20231031', '20240131', '20240430', '20240731', '20241031', '20250131']
# 计算各个时间段的起始日期
last1w = w.tdaysoffset(-4, today, "Period=D").Data[0][0].strftime('%Y-%m-%d')  # 近一周（5个交易日,-4,节假日不足5个交易日需对应修改参数）
last1m = (today + relativedelta(months=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近一月
last3m = (today + relativedelta(months=-3) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近三月
last6m = (today + relativedelta(months=-6) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近半年
ytd = str(today.year) +'-01-01'  # 年初至今
last1y = (today + relativedelta(years=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近一年
data_date = (w.tdaysoffset(0, today, "Period=D").Data[0][0]).strftime('%Y-%m-%d')  # 数据截止日期，需要手动算一下
fof_date = (w.tdaysoffset(-2, today, "Period=D").Data[0][0]).strftime('%Y-%m-%d')  # FOF基金数据日期
#%% 一、基金数据提取
# 读取基金筛选结果明细表，使用季度基金池调整日期作为文件名前缀
fund = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(pool_date[:6]),sheet_name='ag-grid',index_col=2)
fund = fund.drop('基金代码',axis=1)  # 删除重复的基金代码列
fund.index.name = '基金代码'  # 设置索引名称为基金代码
fund = fund.dropna(subset=['银河证券三级分类'])  # 删除银河证券三级分类为空的记录
fund = fund[~fund['银河证券三级分类'].str.contains('非A类')]  # 过滤掉非A类基金
all_df = copy.deepcopy(fund)  # 复制一份完整的基金数据，用于后续分析
fund = fund[fund['公募FOF基金池级别']!='禁买']  # 过滤掉禁买的基金

# 使用Wind API获取基金的各项信息
# 由于Wind API一次最多查询5000个代码，所以分批查询
# 获取基金经理信息
fund['基金经理'] = w.wss(fund.index.tolist()[:5000],"fund_fundmanager").Data[0] + \
                   w.wss(fund.index.tolist()[5000:],"fund_fundmanager").Data[0]
# 获取基金管理人信息
fund['基金管理人'] = w.wss(fund.index.tolist()[:5000], "fund_corp_fundmanagementcompany").Data[0] + \
                     w.wss(fund.index.tolist()[5000:], "fund_corp_fundmanagementcompany").Data[0]
# 获取基金公司办公地址
fund['办公地址'] = w.wss(fund.index.tolist()[:5000], "fund_corpoffice").Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "fund_corpoffice").Data[0]
# 获取去年年初的基金规模
fund['去年基金规模'] = w.wss(fund.index.tolist()[:5000], "netasset_total","unit=1;tradeDate=" + ytd).Data[0] + \
                       w.wss(fund.index.tolist()[5000:], "netasset_total","unit=1;tradeDate=" + ytd).Data[0]
# 获取当前基金规模
fund['基金规模'] = w.wss(fund.index.tolist()[:5000], "netasset_total","unit=1;tradeDate=" + today_str).Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "netasset_total","unit=1;tradeDate=" + today_str).Data[0]

# 获取各个时间段的收益率数据
# 近一周收益率
fund['近一周'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
# 近一月收益率
fund['近一月'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
# 近三月收益率
fund['近三月'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
# 近半年收益率
fund['近半年'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0]
# 年初至今收益率
fund['年初至今'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
# 近一年收益率
fund['近一年'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
# 去年全年收益率
fund['去年'] = w.wss(fund.index.tolist()[:5000], "return_y","tradeDate=" + last1y).Data[0] + \
               w.wss(fund.index.tolist()[5000:], "return_y","tradeDate=" + last1y).Data[0]

# 获取过去12个月的月度收益率
# 生成过去12个月的日期序列
ts = pd.date_range(start = today + relativedelta(years=-1), periods=13, freq='1M')
ts = pd.Series(ts).sort_values(ascending=False).tolist()  # 按降序排列
# 循环获取每个月的收益率
for i in ts:
    fund[i.strftime('%Y-%m')] = w.wss(fund.index.tolist()[:5000], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0] + \
                                w.wss(fund.index.tolist()[5000:], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0]

# 标记基础池基金
fund['my是否基础池'] = '否'  # 默认设置为"否"
# 判断基金是否属于基础池、重点池或核心池
temp1 = (fund['公募FOF基金池级别']=='基础') | (fund['公募FOF基金池级别']=='重点') | (fund['公募FOF基金池级别']=='核心')
temp1 = temp1[temp1].index  # 获取满足条件的基金代码
fund.loc[fund.index.isin(temp1),'my是否基础池'] = '是'  # 将满足条件的基金标记为"是"
fund = fund.dropna(subset = ['基金管理人', '近一周', '近一年'])  # 删除关键信息缺失的基金

print('1.0 基金数据提取完毕')  # 输出进度信息
printtime(t)  # 记录执行时间
#%% 二、各类基金池的动态跟踪
#%% #提取基金净值
end_day = dt.datetime.strptime(today_str, '%Y-%m-%d').strftime('%Y%m%d')    #'%Y%m%d'日期字符串，表示提取数据的时间区间（可有重叠），通常不用再改
start_day = last1w #(dt.datetime.strptime(today_str, '%Y-%m-%d') + relativedelta(months=-1)).strftime('%Y-%m-%d')
# datei_dict = {'20230430':'20230430','20230531':'20230430','20230630':'20230430','20230731':'20230731','20230831':'20230731','20230930':'20230731','20231031': '20231031', '20231130': '20231031', '20231231': '20231031', '20240131': '20240131',
#               '20240229': '20240131','20240331': '20240131','20240430': '20240430','20240531': '20240430','20240630': '20240430','20240731':'20240731','20240831':'20240731','20240930':'20240731', '20241031':'20241031', '20241130':'20241031',
#               '20241231':'20241031'}        #月度末到季度末的调池映射
end_day_month = (pd.to_datetime(end_day, format='%Y%m%d') + pd.offsets.MonthEnd(0)).strftime('%Y%m%d')
end_month_date = pd.date_range(start=start_day,end=end_day_month,freq='M')
end_month_date = [i.strftime('%Y%m%d') for i in end_month_date]

# oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@192.168.105.38:1521/wind')
def get_fund_nav(startDate, endDate):
    # "获取基金净值"
    sqlcode = """
    SELECT PRICE_DATE as pricedt, F_INFO_WINDCODE as fundcode, F_NAV_ADJUSTED as adjnav
    FROM winddf.ChinaMutualFundNAV
    where PRICE_DATE between '%s' and '%s'
    order by F_INFO_WINDCODE, PRICE_DATE
    """% (startDate, endDate)
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col=['PRICEDT', 'FUNDCODE'])
    return tDATA
try:
    with open('基础数据/基金净值数据.pkl', "rb") as fp:
        fund_value_data = pickle.load(fp)
except:#第一次跑没这个文件
        fund_value_data=pd.DataFrame()

fund_value_data1 = get_fund_nav(start_day, end_day)
fund_value_data1 = fund_value_data1.ADJNAV.unstack()
fund_value_data1.index.name='日期'
fund_value_data1=fund_value_data1.reset_index().melt(id_vars='日期', var_name='基金代码', value_name='单位净值')
fund_value_data=pd.concat([fund_value_data,fund_value_data1],axis=0).drop_duplicates(subset=['日期', '基金代码'])
fund_value_data=fund_value_data.reset_index(drop=True)
with open('基础数据/基金净值数据.pkl', "wb") as fp:
    pickle.dump(fund_value_data, fp)


fund_value_data=fund_value_data.pivot(index='日期', columns='基金代码')['单位净值'].sort_index()
fund_value_data.index=pd.to_datetime(fund_value_data.index,format="%Y%m%d").date
#fund_value_data=fund_value_data.fillna(method='ffill')
fund_value_data=fund_value_data.join(w.wsd("000906.SH", "close", fund_value_data.index[0].strftime("%Y-%m-%d"), "",usedf=True)[1].rename({'CLOSE':'中证800'},axis=1))
fund_value_data.dropna(subset=['中证800'], inplace=True)#为了对齐日期，删掉非交易日带来的影响
fund_chg_data_all=fund_value_data.pct_change()

start_day='20231101'
adjust_date=w.tdays(start_day, end_day, "Days=Alldays;Period=M").Data[0]   #季度调池，之后再截断
lm_date=adjust_date[-2].date()
adjust_date = [dt.datetime(2023,11,1)] + adjust_date[2:-1:3]+[adjust_date[-1]]
adjust_date_todate=[i.date() for i in adjust_date]
adjust_date = [i.strftime('%Y%m%d') for i in adjust_date]
adjust_date#可以通过调整adjust_date来调整观察期，以后模拟组合调仓随季度调仓，方便跟踪

#根据基金净值计算基金池的收益表现
label_lst = ['A股价值策略', 'A股全市场策略', 'A股小盘策略', 'TMT', '新能源', '军工', '其他成长', '医药', '白酒', '其他消费', '金融', '周期', '其他金融周期']
level_lst = ['可买池', '基础池', '重点池']
date_lst = {'1W':last1w, '1M':last1m, '3M':last3m, '6M':last6m, 'YTD':ytd, '1Y':last1y}
dict_graph = dict()
fund_benchmark_dict={'A股全市场策略':'930950.CSI','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ','新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI','医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH','金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI','一级债基':'885006.WI','低含权':'885006.WI','中含权':'885007.WI','高含权':'885003.WI','转债':'000832.CSI','中短久期':'885062.WI','长久期':'885008.WI'}
fund_benchmark_name={'A股全市场策略':'偏股混合', 'A股价值策略':'国证价值', 'A股小盘策略':'中证1000','新能源':'中证新能','TMT':'中证TMT','军工':'中证军工','其他成长':'中证成长风格','医药':'中证医药','白酒':'中证白酒','其他消费':'中证消费','金融':'800金地','周期':'中证周期100','其他金融周期':'中证周期50',}

for date in date_lst.keys():
    dict_graph[date] = pd.DataFrame(index=label_lst, columns=level_lst)

for label in ['A股全市场策略', 'A股价值策略', 'A股小盘策略', '新能源', 'TMT', '军工', '其他成长', '医药', '白酒', '其他消费', '金融', '周期', '其他金融周期',]:
    print(label)
    benchmark=fund_benchmark_dict[label]
    portfolio_nav_all=pd.DataFrame()
    performance_month=pd.DataFrame()
    rank_month=pd.DataFrame()

    portfolio_chg_kemai=pd.Series()
    portfolio_chg_jichu=pd.Series()
    portfolio_chg_zhongdian=pd.Series()
    portfolio_chg_newin = pd.Series()
    portfolio_chg_out = pd.Series()
    portfolio_chg_invest=pd.Series()

    for i in range(len(adjust_date)-1):
        date_i= adjust_date[i]
        #季度池子
        try:
            if i == 0:
                fund_label=pd.read_excel('基础数据/202310_qfp.xlsx',sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                fund_label_bf = pd.read_excel('基础数据/202307_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            elif i==1:
                fund_label = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date_i[:6]), sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                fund_label_bf = pd.read_excel('基础数据/202310_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            else:
                fund_label=pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date_i[:6]),sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                fund_label_bf = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]

            fund_label = fund_label[(fund_label['是否可买池'] == '是') & (fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动') & (fund_label['资产细分(V)'] == label)]
            fund_label_bf = fund_label_bf[(fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (fund_label_bf['资产细分(V)'] == label)]
        except:
            fund_label = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(date_i[:6]), sheet_name = 'ag-grid',index_col = 2)[
                ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称':'简称','资产细分V':'资产细分(V)','管理方式VI':'管理方式(VI)'})
            try:
                fund_label_bf = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版',
                              index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            except:
                fund_label_bf = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(adjust_date[i-1][:6]), sheet_name='ag-grid', index_col=2)[
                    ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称': '简称', '资产细分V': '资产细分(V)', '管理方式VI': '管理方式(VI)'})

            for j in range(int(np.ceil(len(fund_label.index) / 500))):
                fund_label.loc[fund_label.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = w.wss(fund_label.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
                fund_label_bf.loc[fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = w.wss(fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
            fund_label = fund_label[(fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动管理') & (fund_label['资产细分(V)'] == label)]
            fund_label = fund_label[fund_label['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]
            try:
                fund_label_bf = fund_label_bf[(fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (fund_label_bf['资产细分(V)'] == label)]
            except:
                fund_label_bf = fund_label_bf[(fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动管理') & (fund_label_bf['资产细分(V)'] == label)]
                fund_label_bf = fund_label_bf[fund_label_bf['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]


        #转化为wind index
        fund_label.index.name='基金代码'
        index_new=[]
        for index in fund_label.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3]+'.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3]+'.SZ')
            elif index[:-3]+'.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3]+'.SH')
            else:
                index_new.append(index)
        fund_label.index=index_new
        fund_label = fund_label.loc[~fund_label.index.duplicated(keep='first')]

        # 转化为wind index
        fund_label_bf.index.name = '基金代码'
        index_new = []
        for index in fund_label_bf.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SZ')
            elif index[:-3] + '.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SH')
            else:
                index_new.append(index)
        fund_label_bf.index = index_new
        fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]

        # 将基金代码转换为Wind格式
        fund_label_bf.index.name = '基金代码'
        index_new = []
        for index in fund_label_bf.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SZ')
            elif index[:-3] + '.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SH')
            else:
                index_new.append(index)
        fund_label_bf.index = index_new
        fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]  # 去除重复的基金代码

        # 获取各类基金池的基金列表
        kemai_pool=fund_label.index.tolist()  # 当前可买池基金列表
        kemai_pool_bf = fund_label_bf.index.tolist()  # 上期可买池基金列表
        try:
            # 尝试从"是否基础池"和"是否重点池"字段获取基础池和重点池基金
            jichu_pool=fund_label[fund_label['是否基础池']=='是'].index.tolist()  # 当前基础池基金列表
            zhongdian_pool=fund_label[fund_label['是否重点池']=='是'].index.tolist()  # 当前重点池基金列表
            try:
                # 计算新进池基金：当前基础池或重点池中有，但上期基础池或重点池中没有的基金
                new_in_pool=list((set(jichu_pool)-set(fund_label_bf[fund_label_bf['是否基础池']=='是'].index.tolist()))|(set(zhongdian_pool)-set(fund_label_bf[fund_label_bf['是否重点池']=='是'].index.tolist())))
                # 计算出池基金：上期基础池或重点池中有，但当前基础池或重点池中没有的基金
                out_pool = list((set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist())-set(jichu_pool))|(set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())-set(
                    zhongdian_pool)))
            except:
                # 如果上面的方式失败，尝试从"公募FOF基金池级别"字段获取基础池和重点池基金
                new_in_pool = list(
                    (set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()))|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool) )|(set(
                        fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                        zhongdian_pool)))
        except:
            # 如果上面的方式都失败，直接从"公募FOF基金池级别"字段获取基础池和重点池基金
            jichu_pool = fund_label[fund_label['公募FOF基金池级别'] == '基础'].index.tolist()  # 当前基础池基金列表
            zhongdian_pool = fund_label[fund_label['公募FOF基金池级别'] == '重点'].index.tolist()  # 当前重点池基金列表
            try:
                # 计算新进池基金：当前基础池或重点池中有，但上期基础池或重点池中没有的基金
                new_in_pool = list(
                    (set(jichu_pool) - set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()))|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())))
                # 计算出池基金：上期基础池或重点池中有，但当前基础池或重点池中没有的基金
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()) - set(jichu_pool) )|( set(
                        fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist()) - set(
                        zhongdian_pool)))
            except:
                # 如果上面的方式都失败，尝试另一种方式计算新进池和出池基金
                new_in_pool = list(
                    ( set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) )|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool)) | (set(
                        fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                        zhongdian_pool)))


        # 计算可买池基金的收益表现
        fund_list=kemai_pool  # 使用当前可买池基金列表
        # 获取从调整日期开始到下一个调整日期的基金净值数据
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值，计算相对收益
        fund_nav=fund_nav.median(axis=1)  # 计算所有基金的中位数收益，作为可买池整体表现
        portfolio_chg_i=fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_kemai = pd.concat([portfolio_chg_kemai,portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_kemai=portfolio_chg_kemai.append(portfolio_chg_i)

        # 计算基础池基金的收益表现
        fund_list=jichu_pool  # 使用当前基础池基金列表
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],fund_list]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav=fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i=fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_jichu = pd.concat([portfolio_chg_jichu, portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_jichu=portfolio_chg_jichu.append(portfolio_chg_i)

        # 计算重点池基金的收益表现
        fund_list=zhongdian_pool  # 使用当前重点池基金列表
        if len(zhongdian_pool)==0:
            fund_list = jichu_pool  # 如果重点池为空，则使用基础池基金
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],fund_list]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav=fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i=fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_zhongdian = pd.concat([portfolio_chg_zhongdian, portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_zhongdian=portfolio_chg_zhongdian.append(portfolio_chg_i)

        # 计算新进池基金的收益表现
        fund_list = new_in_pool  # 使用新进池基金列表
        fund_nav = fund_value_data.loc[
                   w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], fund_list]
        fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav = fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i = fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_newin = pd.concat([portfolio_chg_newin,portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_newin = portfolio_chg_newin.append(portfolio_chg_i)

        # 计算出池基金的收益表现
        fund_list = out_pool  # 使用出池基金列表
        fund_nav = fund_value_data.loc[
                   w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], fund_list]
        fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav = fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i = fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_out = pd.concat([portfolio_chg_out, portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_out = portfolio_chg_out.append(portfolio_chg_i)

    # %%
    # 汇总不同日期的收益率数据，计算各类基金池的累计净值
    portfolio_nav_all=pd.DataFrame()  # 创建空DataFrame用于存储各类基金池的净值数据

    # 计算可买池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储可买池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_kemai+1], axis=0)  # 将日收益率+1（转为日净值）并合并到净值序列
    #portfolio_nav=portfolio_nav.append(portfolio_chg_kemai+1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav=portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'可买池'},axis=1)],axis=1)  # 将可买池净值添加到总表

    # 计算基础池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储基础池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_jichu+1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav=portfolio_nav.append(portfolio_chg_jichu+1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav=portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'基础池'},axis=1)],axis=1)  # 将基础池净值添加到总表

    # 计算重点池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储重点池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_zhongdian + 1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav=portfolio_nav.append(portfolio_chg_zhongdian+1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav=portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'重点池'},axis=1)],axis=1)  # 将重点池净值添加到总表

    # 计算新进池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储新进池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_newin + 1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav = portfolio_nav.append(portfolio_chg_newin + 1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav = portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '每期新进'}, axis=1)], axis=1)  # 将新进池净值添加到总表

    # 计算出池基金的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储出池基金的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_out + 1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav = portfolio_nav.append(portfolio_chg_out + 1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav = portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '汇总出池'}, axis=1)], axis=1)  # 将出池基金净值添加到总表

    # 添加基准指数数据并进行数据处理
    portfolio_nav_all=portfolio_nav_all.join(w.wsd(benchmark, "close", portfolio_nav_all.index[0].strftime("%Y-%m-%d"), "","Days=Alldays;Fill=Previous",usedf=True)[1].rename({'CLOSE':benchmark},axis=1))  # 获取基准指数数据并合并
    portfolio_nav_all=portfolio_nav_all.fillna(method='ffill')/portfolio_nav_all.iloc[0]  # 前向填充缺失值并归一化净值
    portfolio_nav_all['重点池/可买池']=(portfolio_nav_all['重点池']/portfolio_nav_all['可买池']-1)*100  # 计算重点池相对可买池的超额收益（百分比）
    portfolio_nav_all=portfolio_nav_all.rename({benchmark:fund_benchmark_name[label]},axis=1)  # 将基准指数代码替换为可读名称


    # %%
    # 设置绘图参数
    # 定义各曲线的颜色和线型
    color_dict={fund_benchmark_name[label]:(0.7,0.4,0),'可买池':'orange','基础池':(0.5,0.3,0.8),'重点池':'purple','模拟池':'blue','每期新进':'red','汇总出池':'green'}  # 颜色字典
    line_dict={fund_benchmark_name[label]:'--','可买池':'--','基础池':'--','重点池':'--','模拟池':'-','每期新进':'--','汇总出池':'--'}  # 线型字典

    # %%
    # 绘制基金池业绩走势图
    # 准备绘图数据
    df=portfolio_nav_all.copy()  # 复制净值数据
    pptsize(209)  # 设置图表大小（自定义函数）
    fig_name = label+'_基金池业绩走势跟踪'+end_day  # 设置图表名称
    jc_date=adjust_date_todate[0]  # 获取基准日期
    lm_date = w.tdaysoffset(0, lm_date, "").Data[0][0].date()  # 获取上月日期

    # 计算年初至今（YTD）的收益率数据，用于图表标注
    text_mark3 = df.iloc[-1][[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']] / df.loc[w.tdaysoffset(0, ytd, "").Data[0][0].date(),[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']]
    text_mark3 = ['YTD：','{0}：'.format(fund_benchmark_name[label]),str(round((text_mark3[fund_benchmark_name[label]]-1)*100,2)),'，可买池：',str(round((text_mark3['可买池']-1)*100,2)),'，基础池：',str(round((text_mark3['基础池']-1)*100,2)),'，重点池：',str(round((text_mark3['重点池']-1)*100,2)),'，每期新进：',str(round((text_mark3['每期新进']-1)*100,2)),'，汇总出池：',str(round((text_mark3['汇总出池']-1)*100,2)),'，超额：',str(round((text_mark3['重点池']-text_mark3['可买池'])*100,2))]
    text_mark3 = ''.join(text_mark3)  # 将列表转为字符串
    text_mark=text_mark3  # 设置图表标注文本

    # 创建图表和坐标轴
    fig, ax1 = plt.subplots()  # 创建图表和主坐标轴

    # 绘制各基金池的净值曲线
    for column in [fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']:
        ax1.plot(df.index, df[column].tolist(), label = column, color=color_dict[column], alpha = 0.7 ,linestyle=line_dict[column])  # 绘制净值曲线
        ax1.text(df.index[-1], df[column].tolist()[-1],
                        f'{column}: {df[column].tolist()[-1]:.4f}', color=color_dict[column], verticalalignment='bottom',fontsize=fontsize_text-6)  # 添加曲线末端标签
    ax1.tick_params(axis='y',labelsize=fontsize_legend)  # 设置y轴刻度标签大小
    ax1.spines['right'].set_color((0,0,1,0.5))  # 设置右侧坐标轴颜色
    ax1.spines['top'].set_visible(False)  # 隐藏顶部坐标轴
    plt.xticks(rotation=0)  # 设置x轴刻度标签旋转角度

    #近一月
    ax1.axvline(x=lm_date,color=(0.7,0.7,0.7),linestyle='--',linewidth=0.5)
    plt.grid(alpha=light)

    #超额
    ax2 = plt.twinx()
    column='重点池/可买池'
    ax2.fill_between(df.index, 0, df[column].tolist(), facecolor = 'blue', alpha = 0.3) #light)
    ax2.text(df.index[-1], df[column].tolist()[-1],
                        f'{column}: {df[column].tolist()[-1]:.2f}', color=(0,0,1,0.5), verticalalignment='bottom',fontsize=fontsize_text-6)
    ax2.set_ylabel('重点池相对可买池收益(%)',fontsize=fontsize_legend,color=(0,0,1,0.5))
    ax2.tick_params(axis='y',labelsize=fontsize_legend,color=(0,0,1,0.5),labelcolor=(0,0,1,0.5))
    ax2.spines['right'].set_color((0,0,1,0.5))
    ax2.spines['top'].set_visible(False)
    ax2.text(1,0.03, text_mark, transform=ax2.transAxes, horizontalalignment='right',verticalalignment='bottom',fontsize=fontsize_text-2)
    ax2.set_xlim(df.index[0], df.index[-1])
    plt.gcf().autofmt_xdate()
    plt.title(fig_name,fontsize=fontsize_suptitle)

    plt.show()
    fig.savefig('图表/{0}.jpg'.format(fig_name))
    plt.close()

    for date in date_lst.keys():
        temp = w.tdaysoffset(0, date_lst[date], "").Data[0][0].date()
        dict_graph[date].loc[label,'重点池'] =(df.iloc[-1][['重点池']] / df.loc[temp][['重点池']]).values[0]-1
        dict_graph[date].loc[label, '可买池'] = (df.iloc[-1][['可买池']] / df.loc[temp][['可买池']]).values[0]-1
        dict_graph[date].loc[label, '基础池'] = (df.iloc[-1][['基础池']] / df.loc[temp][['基础池']]).values[0]-1

plt_industry_pool('1W', dict_graph['1W'], '2-1-1(上) 各行业基金池收益'+'1W', fpath_draw, data_date)
plt_industry_pool('1M', dict_graph['1M'], '2-1-1(下) 各行业基金池收益'+'1M', fpath_draw, data_date)
plt_industry_pool('3M', dict_graph['3M'], '2-1-2(上) 各行业基金池收益'+'3M', fpath_draw, data_date)
plt_industry_pool('6M', dict_graph['6M'], '2-1-2(下) 各行业基金池收益'+'6M', fpath_draw, data_date)
plt_industry_pool('YTD', dict_graph['YTD'], '2-1-3(上) 各行业基金池收益'+'YTD', fpath_draw, data_date)
plt_industry_pool('1Y', dict_graph['1Y'], '2-1-3(下) 各行业基金池收益'+'1Y', fpath_draw, data_date)

print('2.0 基金池动态跟踪完毕')
printtime(t)

#%% 三、等权重全市场组合跟踪
# 格式化日期，用于后续数据处理
today_str2 = today.strftime('%Y%m%d')  # 将今天的日期转换为'YYYYMMDD'格式的字符串
last1y2 = (today + relativedelta(years=-1)).strftime('%Y%m%d')  # 计算一年前的日期并格式化

# 生成月末日期序列，用于后续基金池调整的时间点
end_month_date = pd.date_range(start=last1y2, end=today_str2, freq='M')  # 从一年前到今天，按月生成日期序列
end_month_date = [(i+relativedelta(months=-1))for i in end_month_date]  # 将每个日期向前移动一个月
end_month_date = [(i + pd.offsets.MonthEnd(0)).strftime('%Y%m%d') for i in end_month_date]  # 将每个日期调整到月末并格式化为字符串

# 创建字典用于存储不同时间点的基金池数据
POOL = dict()
for i in range(len(pool_dates)):
    date = pool_dates[i][:6]  # 提取年月部分
    # 处理特定月份的基金池数据（使用qfp格式文件）
    if date in ['202301','202304','202307','202310','202401','202404']:
        # 读取基金池数据
        fund0 = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date), sheet_name='基金池', index_col=0)
        # 筛选出初始基金、主动管理且为A股全市场策略的基金
        fund0 = fund0[(fund0['是否为初始基金'] == '是') & (fund0['管理方式(VI)'] == '主动') & (fund0['资产细分(V)'] == 'A股全市场策略')]
        # 如果没有基金池级别列，则根据其他列创建该列
        if '基金池级别' not in fund0.columns:
            fund0['基金池级别'] = '禁买'  # 默认设置为禁买
            fund0.loc[fund0['是否重点池'] == '是', '基金池级别'] = '重点'  # 重点池基金
            fund0.loc[(fund0['是否重点池'] != '是') & (fund0['是否基础池'] == '是'), '基金池级别'] = '基础'  # 基础池基金
            fund0.loc[(fund0['是否重点池'] != '是') & (fund0['是否基础池'] != '是') & (fund0['是否可买池'] == '是'), '基金池级别'] = '可买'  # 可买池基金
        # 选择需要的列并重命名
        fund0 = fund0[['简称', '资产类别(I)', '资产地区(II)', '资产类属(III)', '资产板块(IV)', '资产细分(V)', '基金池级别']]
        fund0.columns = ['基金简称', '资产类别I', '资产地区II', '资产类属III', '资产板块IV', '资产细分V', '基金池级别']
        POOL[pool_dates[i]] = fund0  # 将处理后的数据存入字典
    else:
        # 处理其他月份的基金池数据（使用季度筛选结果明细文件）
        fund0 = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(date), sheet_name='ag-grid', index_col=2)
        fund0 = fund0.drop('基金代码',axis=1)
        fund0.index.name = '基金代码'  # 用wind代码替换6位数字的基金代码
        # 筛选出非A类、主动管理且为A股全市场策略的基金
        fund0 = fund0[(~fund0['银河证券三级分类'].str.contains('非A类')) & (fund0['管理方式VI'] == '主动管理') & (fund0['资产细分V'] == 'A股全市场策略')]
        fund0['基金池级别'] = fund0['公募FOF基金池级别']  # 复制公募FOF基金池级别列
        fund0.loc[fund0['公募FOF基金池级别']=='核心', '基金池级别'] = '重点'  # 将核心级别改为重点
        # 选择需要的列
        fund0 = fund0[['基金简称', '资产类别I', '资产地区II', '资产类属III', '资产板块IV', '资产细分V', '基金池级别']]
        POOL[pool_dates[i]] = fund0  # 将处理后的数据存入字典

# 1、季度重点池-出入分析
s=0  # 初始化计数器
# 创建字典用于存储重点池基金、出池基金和入池基金
dict_key, dict_keyout, dict_keyin = dict(), dict(), dict()
out_of_pool, in_to_pool = set(), set()  # 创建集合用于存储出池和入池基金
for i in range(5):
    old_date, new_date = pool_dates[i], pool_dates[i + 1]  # 获取相邻两个时间点
    fund_old = POOL[old_date]  # 获取旧时间点的基金池数据
    fund_new = POOL[new_date]  # 获取新时间点的基金池数据
    old_pool = fund_old[fund_old['基金池级别'] == '重点'].index.tolist()  # 获取旧时间点的重点池基金列表
    new_pool = fund_new[fund_new['基金池级别'] == '重点'].index.tolist()  # 获取新时间点的重点池基金列表
    out_of_pool.update(set(old_pool) - set(new_pool))  # 更新出池基金集合（在旧池中但不在新池中的基金）
    in_to_pool = set(new_pool) - set(old_pool)  # 获取入池基金集合（在新池中但不在旧池中的基金）

    # 将各类基金列表存入相应的字典
    dict_key[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] == '重点'].index.tolist()})  # 当前重点池基金
    dict_keyout[new_date] = pd.DataFrame({'基金代码': list(out_of_pool)})  # 出池基金
    dict_keyin[new_date] = pd.DataFrame({'基金代码': list(in_to_pool)})  # 入池基金
    # 累计基金数量
    s += len(dict_key[new_date].index)+len(dict_keyin[new_date].index)+len(dict_keyout[new_date].index)

# 2、季度基础池-出入分析
# 创建字典用于存储基础池基金、出池基金和入池基金
dict_basic, dict_basicout, dict_basicin = dict(), dict(), dict()
out_of_pool, in_to_pool = set(), set()  # 重置集合
for i in range(5):
    old_date, new_date = pool_dates[i], pool_dates[i + 1]  # 获取相邻两个时间点
    fund_old = POOL[old_date]  # 获取旧时间点的基金池数据
    fund_new = POOL[new_date]  # 获取新时间点的基金池数据
    # 获取旧时间点的基础池和重点池基金列表（合并考虑）
    old_pool = fund_old[(fund_old['基金池级别'] == '基础') | (fund_old['基金池级别'] == '重点')].index.tolist()
    # 获取新时间点的基础池和重点池基金列表（合并考虑）
    new_pool = fund_new[(fund_new['基金池级别'] == '基础') | (fund_old['基金池级别'] == '重点')].index.tolist()
    out_of_pool.update(set(old_pool) - set(new_pool))  # 更新出池基金集合
    in_to_pool = set(new_pool) - set(old_pool)  # 获取入池基金集合

    # 将各类基金列表存入相应的字典
    dict_basic[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] == '基础'].index.tolist()})  # 当前基础池基金
    dict_basicout[new_date] = pd.DataFrame({'基金代码': list(out_of_pool)})  # 出池基金
    dict_basicin[new_date] = pd.DataFrame({'基金代码': list(in_to_pool)})  # 入池基金
    # 累计基金数量
    s += len(dict_basic[new_date].index) + len(dict_basicin[new_date].index) + len(dict_basicout[new_date].index)

# 3、季度可买池分析
dict_kemai = dict()  # 创建字典用于存储可买池基金
for i in range(5):
    new_date = pool_dates[i + 1]  # 获取时间点
    fund_new = POOL[new_date]  # 获取该时间点的基金池数据
    # 获取非禁买的基金列表（即可买池基金）
    dict_kemai[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] != '禁买'].index.tolist()})
    s += len(dict_basic[new_date].index)  # 累计基金数量

# 4、提取基金净值数据
dict_nav = list()  # 创建列表用于存储基金净值数据
for i in range(6):
    if i==0:
        continue  # 跳过第一个时间点
    elif i==1:
        # 第一个区间：从一年前到第二个时间点
        old_date, new_date = last1y2, pool_dates[i + 1]
        pool_date = pool_dates[1]  # 使用第二个时间点的基金池
    elif i==5:
        # 最后一个区间：从最后一个时间点到今天
        old_date, new_date = pool_dates[i], today_str2
        pool_date = pool_dates[i]  # 使用最后一个时间点的基金池
    else:
        # 中间区间：相邻两个时间点
        old_date, new_date = pool_dates[i], pool_dates[i + 1]
        pool_date = pool_dates[i]  # 使用当前时间点的基金池

    # 合并所有类型的基金代码集合（重点池、基础池、可买池及其出入池基金）
    set_all = set(dict_key[pool_date]['基金代码'])|set(dict_keyout[pool_date]['基金代码'])|set(dict_keyin[pool_date]['基金代码'])
    set_all = set_all|set(dict_basic[pool_date]['基金代码'])|set(dict_basicout[pool_date]['基金代码'])|set(dict_basicin[pool_date]['基金代码'])
    set_all = set_all|set(dict_kemai[pool_date]['基金代码'])

    fund_list = list(set_all)  # 转换为列表
    # 使用Wind API获取基金净值数据
    api_i = w.wsd(fund_list, 'NAV_adj', old_date, new_date)
    fundNav_i = api_i.Data  # 获取净值数据
    date_i = api_i.Times  # 获取日期数据
    fundNav_np_i = np.array(fundNav_i)  # 转换为numpy数组
    # 创建DataFrame存储净值数据，行索引为日期，列索引为基金代码
    df_new = pd.DataFrame(np.transpose(fundNav_np_i), index=list(date_i), columns=fund_list)
    # 将数据添加到列表中
    dict_nav.append({'基金池': pool_date, '时间区间':(old_date, new_date),'基金净值':df_new})

# 5、与各基金池名单匹配，计算净值
portPoint_df = pd.DataFrame()  # 创建DataFrame用于存储各基金池的净值数据
fundNavList = list()  # 创建列表用于存储可买池的收益率数据

# 计算可买池的净值
for temp_dict in dict_nav:
    pool_date = temp_dict['基金池']  # 获取基金池日期
    df_new = temp_dict['基金净值']  # 获取净值数据
    i_kemai = dict_kemai[pool_date]  # 获取该日期的可买池基金列表
    i_kemai = df_new[list(i_kemai['基金代码'])]  # 筛选出可买池基金的净值数据
    i_kemai = i_kemai.median(axis=1)  # 计算每日的中位数净值（横截面）
    i_kemai = i_kemai.pct_change(axis=0)  # 计算日收益率
    # 处理第一个时间点的收益率
    if temp_dict['时间区间'][0] == last1y2:
        i_kemai.iloc[0] = 0  # 如果是第一个区间，将第一天的收益率设为0
    else:
        i_kemai = i_kemai[1:]  # 否则删除第一天的收益率（因为是NaN）
    fundNavList.append(i_kemai)  # 将收益率数据添加到列表中

# 合并所有时间区间的收益率数据
fundNavList = pd.concat(fundNavList, axis=0)
# 将索引（日期）格式化为字符串
fundNavList.index = [datetime.strftime(x, "%Y-%m-%d") for x in fundNavList.index]
# 计算累计净值（从100开始）
portPoint_df['可买池'] = 100 * (1+fundNavList).cumprod()

# 定义函数用于计算各类基金池的净值
def concat_nav(dict_pool, dict_nav, begin_date):
    """
    计算指定基金池的累计净值

    参数:
    dict_pool: 包含各时间点基金池成分的字典
    dict_nav: 包含各时间区间基金净值的列表
    begin_date: 起始日期

    返回:
    fundNavList: 累计净值序列
    """
    fundNavList = list()  # 创建列表用于存储收益率数据
    for temp_dict in dict_nav:
        pool_date = temp_dict['基金池']  # 获取基金池日期
        df_new = temp_dict['基金净值']  # 获取净值数据

        i_pool = dict_pool[pool_date]  # 获取该日期的基金池列表
        i_pool = df_new[list(i_pool['基金代码'])]  # 筛选出基金池中基金的净值数据
        i_pool = i_pool.mean(axis=1)  # 计算每日的平均净值（横截面）
        i_pool = i_pool.pct_change(axis=0)  # 计算日收益率
        # 处理第一个时间点的收益率
        if begin_date==temp_dict['时间区间'][0]:
            i_pool.iloc[0] = 0  # 如果是第一个区间，将第一天的收益率设为0
        else:
            i_pool = i_pool[1:]  # 否则删除第一天的收益率（因为是NaN）
        fundNavList.append(i_pool)  # 将收益率数据添加到列表中

    # 合并所有时间区间的收益率数据
    fundNavList = pd.concat(fundNavList, axis=0)
    # 计算累计净值（从100开始）
    fundNavList = 100 * (1 + fundNavList).cumprod()
    # 将索引（日期）格式化为字符串
    fundNavList.index = [datetime.strftime(x, "%Y-%m-%d") for x in fundNavList.index]
    return fundNavList

# 计算各类基金池的累计净值
portPoint_df['重点池'] = concat_nav(dict_key, dict_nav, last1y2)  # 重点池净值
portPoint_df['重点池_每期新进'] = concat_nav(dict_keyin, dict_nav, last1y2)  # 重点池每期新进基金净值
portPoint_df['重点池_汇总出池'] = concat_nav(dict_keyout, dict_nav, last1y2)  # 重点池汇总出池基金净值
portPoint_df['基础池'] = concat_nav(dict_basic, dict_nav, last1y2)  # 基础池净值
portPoint_df['基础池_每期新进'] = concat_nav(dict_basicin, dict_nav, last1y2)  # 基础池每期新进基金净值
portPoint_df['基础池_汇总出池'] = concat_nav(dict_basicout, dict_nav, last1y2)  # 基础池汇总出池基金净值

# 绘制重点池和基础池的业绩走势图
plt_portfolio_median(portPoint_df, '重点池', fpath_draw, data_date, '2-2-1 等权重基金池业绩走势跟踪-重点池', last_year='2024-12-31')
plt_portfolio_median(portPoint_df, '基础池', fpath_draw, data_date, '2-2-2 等权重基金池业绩走势跟踪-基础池', last_year='2024-12-31')

print('3.0 等权重全市场组合跟踪完毕')  # 输出进度信息
printtime(t)  # 记录执行时间

#%% 四、市场回顾
# 定义不同类型的基金分类列表，用于后续分析
standard = ['标准股票型基金(A类)','港股通标准股票型基金(A类)']  # 标准股票型基金分类，银河分类每季度需对应更改，可通过基础池透视得到
blend = ['港股通偏股型基金(A类)','偏股型基金(股票上下限60%-95%)(A类)','偏股型基金(股票上限80%)(A类)','偏股型基金(股票上限95%)(A类)','北交所主题偏股型基金(A类)']  # 偏股型基金分类
others = ['普通偏债型基金(股票上限不高于30%)(A类)','普通债券型基金(二级)(A类)','长期纯债债券型基金(A类)','QDII股票型基金(A类)',\
           'QDII债券型基金(A类)','商品期货ETF基金','黄金ETF基金']  # 其他类型基金分类

# 创建用于存储各类基金收益率分位数的DataFrame
rtn_summary = pd.DataFrame(columns = ['近一周','近一月','近三月','年初至今','近一年'])  # 用于存储各时间段收益率分位数
rtn_monthly = pd.DataFrame(columns = [i.strftime('%Y-%m') for i in ts])  # 用于存储月度收益率，列名为月份

# 计算标准股票型基金的收益率分位数
df1 = fund[fund['银河证券三级分类'].isin(standard)]  # 筛选出标准股票型基金
rtn_summary.loc['标准股票型基金 25"'] = df1[rtn_summary.columns].quantile(0.75)  # 计算上四分位数（表现较好的基金）
rtn_summary.loc['标准股票型基金 50"'] = df1[rtn_summary.columns].quantile(0.5)   # 计算中位数
rtn_summary.loc['标准股票型基金 75"'] = df1[rtn_summary.columns].quantile(0.25)  # 计算下四分位数（表现较差的基金）
rtn_monthly.loc['标准股票型基金'] = df1[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数

# 计算偏股型基金的收益率分位数
df2 = fund[fund['银河证券三级分类'].isin(blend)]  # 筛选出偏股型基金
rtn_summary.loc['偏股型基金 25"'] = df2[rtn_summary.columns].quantile(0.75)  # 计算上四分位数
rtn_summary.loc['偏股型基金 50"'] = df2[rtn_summary.columns].quantile(0.5)   # 计算中位数
rtn_summary.loc['偏股型基金 75"'] = df2[rtn_summary.columns].quantile(0.25)  # 计算下四分位数
rtn_monthly.loc['偏股型基金'] = df2[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数

# 计算其他类型基金的收益率分位数
for i in others:
    df3 = fund[fund['银河证券三级分类'] == i]  # 筛选出特定类型的基金
    if i == '黄金ETF基金':  # 黄金ETF基金特殊处理，去掉后缀"基金"
        rtn_summary.loc[i[:-5]+' 25"'] = df3[rtn_summary.columns].quantile(0.75)  # 计算上四分位数
        rtn_summary.loc[i[:-5]+' 50"'] = df3[rtn_summary.columns].quantile(0.5)   # 计算中位数
        rtn_summary.loc[i[:-5]+' 75"'] = df3[rtn_summary.columns].quantile(0.25)  # 计算下四分位数
        rtn_monthly.loc[i[:-5]] = df3[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数
    else:  # 其他类型基金处理，去掉后缀"(A类)"
        rtn_summary.loc[i[:-4]+' 25"'] = df3[rtn_summary.columns].quantile(0.75)  # 计算上四分位数
        rtn_summary.loc[i[:-4]+' 50"'] = df3[rtn_summary.columns].quantile(0.5)   # 计算中位数
        rtn_summary.loc[i[:-4]+' 75"'] = df3[rtn_summary.columns].quantile(0.25)  # 计算下四分位数
        rtn_monthly.loc[i[:-4]] = df3[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数

# 对收益率数据进行四舍五入，保留两位小数
rtn_summary = rtn_summary.round(2)
rtn_monthly = rtn_monthly.round(2)

# 创建用于绘图的DataFrame，添加中证800指数的收益率数据
df_graph = pd.DataFrame()
# 使用Wind API获取中证800指数在不同时间段的收益率
df_graph.loc['中证800', '近一周'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近一月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近三月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '年初至今'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近一年'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0][0]

# 合并中证800指数和各类基金中位数收益率数据，用于后续绘图
df_graph = pd.concat([df_graph, rtn_summary[rtn_summary.index.str.contains('50"')][['近一周','近一月','近三月','年初至今','近一年']]]).round(2)

# 调用自定义函数绘制月度收益率图表和板块暴露线图
plt_monthly_return(rtn_monthly, '各类资产月度收益率('+data_date+')', fpath_draw)  # 绘制各类资产月度收益率图表
plate_exposure_line(today, data_date, fpath_draw)  # 绘制板块暴露线图

print('4.0 市场回顾完毕')  # 输出进度信息
printtime(t)  # 记录执行时间并更新时间点

#%% 五、收益归因
# 读取基金重仓股数据，用于分析基金持仓情况
holding = pd.read_pickle('基础数据/所有基金重仓数据_20241231-20241231.pkl')  # 从pickle文件加载基金重仓数据
holding = holding[holding['报告日期']==rpt_date]  # 筛选出特定报告日期的数据

# 筛选出全A基金（境内股市、主动管理、非非A类）
q_fund=pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(pool_date[:6]),sheet_name='ag-grid',index_col=2)  # 读取季度筛选结果明细
q_fund=q_fund.drop('基金代码',axis=1)  # 删除重复的基金代码列
q_fund.index.name='基金代码'  # 设置索引名称为基金代码
# 筛选出境内股市、主动管理且非非A类的基金
A_fund=q_fund[(q_fund['资产地区II']=='境内股市') & (q_fund['管理方式VI']=='主动管理') & (~q_fund['银河证券三级分类'].str.contains('非A类'))].index.tolist()
A_fund=[A_fund_i[:-3] for A_fund_i in A_fund]  # 处理基金代码格式，去掉后缀
holding = holding[holding['基金代码'].isin(A_fund)]  # 只保留全A基金的持仓数据

# 排除指数基金，只保留主动管理基金的持仓数据
# 获取申万一级和二级行业分类的指数基金代码
index_list = [i[0:6] for i in w.wset("sectorconstituent","date=" + rpt_date + ";sectorid=2001010102000000").Data[1]]  # 申万一级行业分类
index_list2 = [i[0:6] for i in w.wset("sectorconstituent","date=" + rpt_date + ";sectorid=2001010103000000").Data[1]]  # 申万二级行业分类
index_list = index_list + index_list2  # 合并两个列表
holding = holding[~holding['基金代码'].isin(index_list)]  # 排除指数基金

# 计算各股票的持仓市值总和，并计算持仓占比
mf = holding.groupby('股票代码')['持股市值'].sum()  # 按股票代码分组，计算持股市值总和
#%%
# 导入所需的库和模块
import os  # 操作系统相关功能
import chart as ct  # 自定义图表模块
import copy  # 深拷贝对象
import cx_Oracle  # Oracle数据库连接
cx_Oracle.init_oracle_client(os.path.join('.', 'tools', 'clt64', 'instantclient_11_2'))  # 初始化Oracle客户端
import datetime as dt  # 日期时间处理
import matplotlib.pyplot as plt  # 绘图库
import matplotlib.dates as mdates  # 处理日期格式
import numpy as np  # 数值计算
import pandas as pd  # 数据分析
import pickle  # 序列化和反序列化Python对象
import seaborn as sns  # 统计数据可视化
import urllib3  # HTTP客户端
import warnings
warnings.filterwarnings("ignore")  # 忽略警告信息
from datetime import timedelta  # 时间差
from dateutil.relativedelta import relativedelta  # 处理相对日期
from json import dumps,loads  # JSON数据处理
from matplotlib.colors import LinearSegmentedColormap  # 自定义颜色映射
from matplotlib.font_manager import FontProperties  # 字体属性
from matplotlib.table import Table  # 表格绘制
from openpyxl import load_workbook  # Excel文件处理
from plotFunc import *  # 导入自定义绘图函数
from scipy import stats  # 统计函数
from WindPy import w,datetime  # Wind金融终端API
w.start()  # 启动Wind API连接
fpath_draw = '图表'  # 图表保存路径
# 设置matplotlib绘图参数
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 设置字体为微软雅黑，支持中文显示
plt.rcParams['axes.unicode_minus']=False  # 正确显示负号
plt.rcParams['figure.dpi']=400  # 设置图像分辨率
plt.rcParams['lines.linewidth']=1  # 设置线宽
plt.rcParams['figure.autolayout']=True  # 自动调整布局

# 定义计时函数，用于记录代码执行时间并输出
def printtime(t):
    if t!=1:
        print('-----' * 5+'花费%s秒'%(dt.datetime.now()-t))  # 输出每个部分的执行时间
        t=dt.datetime.now()  # 更新时间点
    else:
        print('—' * 25+'共费%s秒'%(dt.datetime.now()-t0))  # 输出总执行时间
    return t
t0=dt.datetime.now()  # 记录程序开始时间
t=t0  # 初始化时间点


###1、每次运行需更改：today
###2、需检查：last1w和fof_date的偏移量（没有假期不涉及）、pool_dates（近一年涉及的四个基金池+前移两个基金池
###3、季度调整：rpt_date、pool_date、所有基金重仓或持仓数据的pkl文件

# 设置基准日期和时间范围
today = dt.datetime.strptime('20250222','%Y%m%d')           # 上周六，程序运行的基准日期
today_str = today.strftime('%Y-%m-%d')  # 格式化日期字符串
rpt_date = '20241231'                                       # 季报日期，等对应季报出来再更新，目前对应的是24年三季报
pool_date = '20250131'                                      # 季度基金池调池日（日历日，0131，0430，0731，1031）
                                                            # 更新季度基金池记得同步更新datei_dict
# 近一年的基金池调整日期列表
pool_dates = ['20231031', '20240131', '20240430', '20240731', '20241031', '20250131']
# 计算各个时间段的起始日期
last1w = w.tdaysoffset(-4, today, "Period=D").Data[0][0].strftime('%Y-%m-%d')  # 近一周（5个交易日,-4,节假日不足5个交易日需对应修改参数）
last1m = (today + relativedelta(months=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近一月
last3m = (today + relativedelta(months=-3) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近三月
last6m = (today + relativedelta(months=-6) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近半年
ytd = str(today.year) +'-01-01'  # 年初至今
last1y = (today + relativedelta(years=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')  # 近一年
data_date = (w.tdaysoffset(0, today, "Period=D").Data[0][0]).strftime('%Y-%m-%d')  # 数据截止日期，需要手动算一下
fof_date = (w.tdaysoffset(-2, today, "Period=D").Data[0][0]).strftime('%Y-%m-%d')  # FOF基金数据日期


#%% 一、基金数据提取
# 读取基金筛选结果明细表，使用季度基金池调整日期作为文件名前缀
fund = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(pool_date[:6]),sheet_name='ag-grid',index_col=2)
fund = fund.drop('基金代码',axis=1)  # 删除重复的基金代码列
fund.index.name = '基金代码'  # 设置索引名称为基金代码
fund = fund.dropna(subset=['银河证券三级分类'])  # 删除银河证券三级分类为空的记录
fund = fund[~fund['银河证券三级分类'].str.contains('非A类')]  # 过滤掉非A类基金
all_df = copy.deepcopy(fund)  # 复制一份完整的基金数据，用于后续分析
fund = fund[fund['公募FOF基金池级别']!='禁买']  # 过滤掉禁买的基金

# 使用Wind API获取基金的各项信息
# 由于Wind API一次最多查询5000个代码，所以分批查询
# 获取基金经理信息
fund['基金经理'] = w.wss(fund.index.tolist()[:5000],"fund_fundmanager").Data[0] + \
                   w.wss(fund.index.tolist()[5000:],"fund_fundmanager").Data[0]
# 获取基金管理人信息
fund['基金管理人'] = w.wss(fund.index.tolist()[:5000], "fund_corp_fundmanagementcompany").Data[0] + \
                     w.wss(fund.index.tolist()[5000:], "fund_corp_fundmanagementcompany").Data[0]
# 获取基金公司办公地址
fund['办公地址'] = w.wss(fund.index.tolist()[:5000], "fund_corpoffice").Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "fund_corpoffice").Data[0]
# 获取去年年初的基金规模
fund['去年基金规模'] = w.wss(fund.index.tolist()[:5000], "netasset_total","unit=1;tradeDate=" + ytd).Data[0] + \
                       w.wss(fund.index.tolist()[5000:], "netasset_total","unit=1;tradeDate=" + ytd).Data[0]
# 获取当前基金规模
fund['基金规模'] = w.wss(fund.index.tolist()[:5000], "netasset_total","unit=1;tradeDate=" + today_str).Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "netasset_total","unit=1;tradeDate=" + today_str).Data[0]

# 获取各个时间段的收益率数据
# 近一周收益率
fund['近一周'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
# 近一月收益率
fund['近一月'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
# 近三月收益率
fund['近三月'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
# 近半年收益率
fund['近半年'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0]
# 年初至今收益率
fund['年初至今'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
# 近一年收益率
fund['近一年'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
# 去年全年收益率
fund['去年'] = w.wss(fund.index.tolist()[:5000], "return_y","tradeDate=" + last1y).Data[0] + \
               w.wss(fund.index.tolist()[5000:], "return_y","tradeDate=" + last1y).Data[0]

# 获取过去12个月的月度收益率
# 生成过去12个月的日期序列
ts = pd.date_range(start = today + relativedelta(years=-1), periods=13, freq='1M')
ts = pd.Series(ts).sort_values(ascending=False).tolist()  # 按降序排列
# 循环获取每个月的收益率
for i in ts:
    fund[i.strftime('%Y-%m')] = w.wss(fund.index.tolist()[:5000], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0] + \
                                w.wss(fund.index.tolist()[5000:], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0]

# 标记基础池基金
fund['my是否基础池'] = '否'  # 默认设置为"否"
# 判断基金是否属于基础池、重点池或核心池
temp1 = (fund['公募FOF基金池级别']=='基础') | (fund['公募FOF基金池级别']=='重点') | (fund['公募FOF基金池级别']=='核心')
temp1 = temp1[temp1].index  # 获取满足条件的基金代码
fund.loc[fund.index.isin(temp1),'my是否基础池'] = '是'  # 将满足条件的基金标记为"是"
fund = fund.dropna(subset = ['基金管理人', '近一周', '近一年'])  # 删除关键信息缺失的基金

print('1.0 基金数据提取完毕')  # 输出进度信息
printtime(t)  # 记录执行时间

#%% 二、各类基金池的动态跟踪

#%% #提取基金净值
end_day = dt.datetime.strptime(today_str, '%Y-%m-%d').strftime('%Y%m%d')    #'%Y%m%d'日期字符串，表示提取数据的时间区间（可有重叠），通常不用再改
start_day = last1w #(dt.datetime.strptime(today_str, '%Y-%m-%d') + relativedelta(months=-1)).strftime('%Y-%m-%d')
# datei_dict = {'20230430':'20230430','20230531':'20230430','20230630':'20230430','20230731':'20230731','20230831':'20230731','20230930':'20230731','20231031': '20231031', '20231130': '20231031', '20231231': '20231031', '20240131': '20240131',
#               '20240229': '20240131','20240331': '20240131','20240430': '20240430','20240531': '20240430','20240630': '20240430','20240731':'20240731','20240831':'20240731','20240930':'20240731', '20241031':'20241031', '20241130':'20241031',
#               '20241231':'20241031'}        #月度末到季度末的调池映射
end_day_month = (pd.to_datetime(end_day, format='%Y%m%d') + pd.offsets.MonthEnd(0)).strftime('%Y%m%d')
end_month_date = pd.date_range(start=start_day,end=end_day_month,freq='M')
end_month_date = [i.strftime('%Y%m%d') for i in end_month_date]

oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@192.168.105.38:1521/wind')
def get_fund_nav(startDate, endDate):
    # "获取基金净值"
    sqlcode = """
    SELECT PRICE_DATE as pricedt, F_INFO_WINDCODE as fundcode, F_NAV_ADJUSTED as adjnav
    FROM winddf.ChinaMutualFundNAV
    where PRICE_DATE between '%s' and '%s'
    order by F_INFO_WINDCODE, PRICE_DATE
    """% (startDate, endDate)
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col=['PRICEDT', 'FUNDCODE'])
    return tDATA
try:
    with open('基础数据/基金净值数据.pkl', "rb") as fp:
        fund_value_data = pickle.load(fp)
except:#第一次跑没这个文件
        fund_value_data=pd.DataFrame()

fund_value_data1 = get_fund_nav(start_day, end_day)
fund_value_data1 = fund_value_data1.ADJNAV.unstack()
fund_value_data1.index.name='日期'
fund_value_data1=fund_value_data1.reset_index().melt(id_vars='日期', var_name='基金代码', value_name='单位净值')
fund_value_data=pd.concat([fund_value_data,fund_value_data1],axis=0).drop_duplicates(subset=['日期', '基金代码'])
fund_value_data=fund_value_data.reset_index(drop=True)
with open('基础数据/基金净值数据.pkl', "wb") as fp:
    pickle.dump(fund_value_data, fp)


fund_value_data=fund_value_data.pivot(index='日期', columns='基金代码')['单位净值'].sort_index()
fund_value_data.index=pd.to_datetime(fund_value_data.index,format="%Y%m%d").date
#fund_value_data=fund_value_data.fillna(method='ffill')
fund_value_data=fund_value_data.join(w.wsd("000906.SH", "close", fund_value_data.index[0].strftime("%Y-%m-%d"), "",usedf=True)[1].rename({'CLOSE':'中证800'},axis=1))
fund_value_data.dropna(subset=['中证800'], inplace=True)#为了对齐日期，删掉非交易日带来的影响
fund_chg_data_all=fund_value_data.pct_change()

start_day='20231101'
adjust_date=w.tdays(start_day, end_day, "Days=Alldays;Period=M").Data[0]   #季度调池，之后再截断
lm_date=adjust_date[-2].date()
adjust_date = [dt.datetime(2023,11,1)] + adjust_date[2:-1:3]+[adjust_date[-1]]
adjust_date_todate=[i.date() for i in adjust_date]
adjust_date = [i.strftime('%Y%m%d') for i in adjust_date]
adjust_date#可以通过调整adjust_date来调整观察期，以后模拟组合调仓随季度调仓，方便跟踪

#根据基金净值计算基金池的收益表现
label_lst = ['A股价值策略', 'A股全市场策略', 'A股小盘策略', 'TMT', '新能源', '军工', '其他成长', '医药', '白酒', '其他消费', '金融', '周期', '其他金融周期']
level_lst = ['可买池', '基础池', '重点池']
date_lst = {'1W':last1w, '1M':last1m, '3M':last3m, '6M':last6m, 'YTD':ytd, '1Y':last1y}
dict_graph = dict()
fund_benchmark_dict={'A股全市场策略':'930950.CSI','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ','新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI','医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH','金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI','一级债基':'885006.WI','低含权':'885006.WI','中含权':'885007.WI','高含权':'885003.WI','转债':'000832.CSI','中短久期':'885062.WI','长久期':'885008.WI'}
fund_benchmark_name={'A股全市场策略':'偏股混合', 'A股价值策略':'国证价值', 'A股小盘策略':'中证1000','新能源':'中证新能','TMT':'中证TMT','军工':'中证军工','其他成长':'中证成长风格','医药':'中证医药','白酒':'中证白酒','其他消费':'中证消费','金融':'800金地','周期':'中证周期100','其他金融周期':'中证周期50',}

for date in date_lst.keys():
    dict_graph[date] = pd.DataFrame(index=label_lst, columns=level_lst)

for label in ['A股全市场策略', 'A股价值策略', 'A股小盘策略', '新能源', 'TMT', '军工', '其他成长', '医药', '白酒', '其他消费', '金融', '周期', '其他金融周期',]:
    print(label)
    benchmark=fund_benchmark_dict[label]
    portfolio_nav_all=pd.DataFrame()
    performance_month=pd.DataFrame()
    rank_month=pd.DataFrame()

    portfolio_chg_kemai=pd.Series()
    portfolio_chg_jichu=pd.Series()
    portfolio_chg_zhongdian=pd.Series()
    portfolio_chg_newin = pd.Series()
    portfolio_chg_out = pd.Series()
    portfolio_chg_invest=pd.Series()

    for i in range(len(adjust_date)-1):
        date_i= adjust_date[i]
        #季度池子
        try:
            if i == 0:
                fund_label=pd.read_excel('基础数据/202310_qfp.xlsx',sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                fund_label_bf = pd.read_excel('基础数据/202307_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            elif i==1:
                fund_label = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date_i[:6]), sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
                fund_label_bf = pd.read_excel('基础数据/202310_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            else:
                fund_label=pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date_i[:6]),sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
                fund_label_bf = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]

            fund_label = fund_label[(fund_label['是否可买池'] == '是') & (fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动') & (fund_label['资产细分(V)'] == label)]
            fund_label_bf = fund_label_bf[(fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (fund_label_bf['资产细分(V)'] == label)]
        except:
            fund_label = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(date_i[:6]), sheet_name = 'ag-grid',index_col = 2)[
                ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称':'简称','资产细分V':'资产细分(V)','管理方式VI':'管理方式(VI)'})
            try:
                fund_label_bf = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版',
                              index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
            except:
                fund_label_bf = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(adjust_date[i-1][:6]), sheet_name='ag-grid', index_col=2)[
                    ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称': '简称', '资产细分V': '资产细分(V)', '管理方式VI': '管理方式(VI)'})

            for j in range(int(np.ceil(len(fund_label.index) / 500))):
                fund_label.loc[fund_label.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = w.wss(fund_label.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
                fund_label_bf.loc[fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = w.wss(fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
            fund_label = fund_label[(fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动管理') & (fund_label['资产细分(V)'] == label)]
            fund_label = fund_label[fund_label['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]
            try:
                fund_label_bf = fund_label_bf[(fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (fund_label_bf['资产细分(V)'] == label)]
            except:
                fund_label_bf = fund_label_bf[(fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动管理') & (fund_label_bf['资产细分(V)'] == label)]
                fund_label_bf = fund_label_bf[fund_label_bf['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]


        #转化为wind index
        fund_label.index.name='基金代码'
        index_new=[]
        for index in fund_label.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3]+'.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3]+'.SZ')
            elif index[:-3]+'.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3]+'.SH')
            else:
                index_new.append(index)
        fund_label.index=index_new
        fund_label = fund_label.loc[~fund_label.index.duplicated(keep='first')]

        # 转化为wind index
        fund_label_bf.index.name = '基金代码'
        index_new = []
        for index in fund_label_bf.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SZ')
            elif index[:-3] + '.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SH')
            else:
                index_new.append(index)
        fund_label_bf.index = index_new
        fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]

        # 将基金代码转换为Wind格式
        fund_label_bf.index.name = '基金代码'
        index_new = []
        for index in fund_label_bf.index:
            if index in fund_chg_data_all.columns:
                index_new.append(index)
            elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SZ')
            elif index[:-3] + '.SH' in fund_chg_data_all.columns:
                index_new.append(index[:-3] + '.SH')
            else:
                index_new.append(index)
        fund_label_bf.index = index_new
        fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]  # 去除重复的基金代码

        # 获取各类基金池的基金列表
        kemai_pool=fund_label.index.tolist()  # 当前可买池基金列表
        kemai_pool_bf = fund_label_bf.index.tolist()  # 上期可买池基金列表
        try:
            # 尝试从"是否基础池"和"是否重点池"字段获取基础池和重点池基金
            jichu_pool=fund_label[fund_label['是否基础池']=='是'].index.tolist()  # 当前基础池基金列表
            zhongdian_pool=fund_label[fund_label['是否重点池']=='是'].index.tolist()  # 当前重点池基金列表
            try:
                # 计算新进池基金：当前基础池或重点池中有，但上期基础池或重点池中没有的基金
                new_in_pool=list((set(jichu_pool)-set(fund_label_bf[fund_label_bf['是否基础池']=='是'].index.tolist()))|(set(zhongdian_pool)-set(fund_label_bf[fund_label_bf['是否重点池']=='是'].index.tolist())))
                # 计算出池基金：上期基础池或重点池中有，但当前基础池或重点池中没有的基金
                out_pool = list((set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist())-set(jichu_pool))|(set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())-set(
                    zhongdian_pool)))
            except:
                # 如果上面的方式失败，尝试从"公募FOF基金池级别"字段获取基础池和重点池基金
                new_in_pool = list(
                    (set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()))|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool) )|(set(
                        fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                        zhongdian_pool)))
        except:
            # 如果上面的方式都失败，直接从"公募FOF基金池级别"字段获取基础池和重点池基金
            jichu_pool = fund_label[fund_label['公募FOF基金池级别'] == '基础'].index.tolist()  # 当前基础池基金列表
            zhongdian_pool = fund_label[fund_label['公募FOF基金池级别'] == '重点'].index.tolist()  # 当前重点池基金列表
            try:
                # 计算新进池基金：当前基础池或重点池中有，但上期基础池或重点池中没有的基金
                new_in_pool = list(
                    (set(jichu_pool) - set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()))|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())))
                # 计算出池基金：上期基础池或重点池中有，但当前基础池或重点池中没有的基金
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()) - set(jichu_pool) )|( set(
                        fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist()) - set(
                        zhongdian_pool)))
            except:
                # 如果上面的方式都失败，尝试另一种方式计算新进池和出池基金
                new_in_pool = list(
                    ( set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) )|( set(
                        zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
                out_pool = list(
                    (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool)) | (set(
                        fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
                        zhongdian_pool)))


        # 计算可买池基金的收益表现
        fund_list=kemai_pool  # 使用当前可买池基金列表
        # 获取从调整日期开始到下一个调整日期的基金净值数据
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],list(set(list(fund_value_data.columns))&set(fund_list))]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值，计算相对收益
        fund_nav=fund_nav.median(axis=1)  # 计算所有基金的中位数收益，作为可买池整体表现
        portfolio_chg_i=fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_kemai = pd.concat([portfolio_chg_kemai,portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_kemai=portfolio_chg_kemai.append(portfolio_chg_i)

        # 计算基础池基金的收益表现
        fund_list=jichu_pool  # 使用当前基础池基金列表
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],fund_list]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav=fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i=fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_jichu = pd.concat([portfolio_chg_jichu, portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_jichu=portfolio_chg_jichu.append(portfolio_chg_i)

        # 计算重点池基金的收益表现
        fund_list=zhongdian_pool  # 使用当前重点池基金列表
        if len(zhongdian_pool)==0:
            fund_list = jichu_pool  # 如果重点池为空，则使用基础池基金
        fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],fund_list]
        fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav=fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i=fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_zhongdian = pd.concat([portfolio_chg_zhongdian, portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_zhongdian=portfolio_chg_zhongdian.append(portfolio_chg_i)

        # 计算新进池基金的收益表现
        fund_list = new_in_pool  # 使用新进池基金列表
        fund_nav = fund_value_data.loc[
                   w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], fund_list]
        fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav = fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i = fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_newin = pd.concat([portfolio_chg_newin,portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_newin = portfolio_chg_newin.append(portfolio_chg_i)

        # 计算出池基金的收益表现
        fund_list = out_pool  # 使用出池基金列表
        fund_nav = fund_value_data.loc[
                   w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], fund_list]
        fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]  # 归一化净值
        fund_nav = fund_nav.median(axis=1)  # 计算中位数收益
        portfolio_chg_i = fund_nav.pct_change().dropna()  # 计算日收益率变化
        portfolio_chg_out = pd.concat([portfolio_chg_out, portfolio_chg_i], axis=0)  # 将当期收益率添加到总收益率序列中
        #portfolio_chg_out = portfolio_chg_out.append(portfolio_chg_i)

    # %%
    # 汇总不同日期的收益率数据，计算各类基金池的累计净值
    portfolio_nav_all=pd.DataFrame()  # 创建空DataFrame用于存储各类基金池的净值数据

    # 计算可买池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储可买池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_kemai+1], axis=0)  # 将日收益率+1（转为日净值）并合并到净值序列
    #portfolio_nav=portfolio_nav.append(portfolio_chg_kemai+1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav=portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'可买池'},axis=1)],axis=1)  # 将可买池净值添加到总表

    # 计算基础池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储基础池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_jichu+1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav=portfolio_nav.append(portfolio_chg_jichu+1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav=portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'基础池'},axis=1)],axis=1)  # 将基础池净值添加到总表

    # 计算重点池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储重点池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_zhongdian + 1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav=portfolio_nav.append(portfolio_chg_zhongdian+1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav=portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'重点池'},axis=1)],axis=1)  # 将重点池净值添加到总表

    # 计算新进池的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储新进池的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_newin + 1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav = portfolio_nav.append(portfolio_chg_newin + 1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav = portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '每期新进'}, axis=1)], axis=1)  # 将新进池净值添加到总表

    # 计算出池基金的累计净值
    portfolio_nav = pd.Series()  # 创建空Series用于存储出池基金的净值数据
    portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_out + 1], axis=0)  # 将日收益率+1并合并到净值序列
    #portfolio_nav = portfolio_nav.append(portfolio_chg_out + 1)  # 旧版pandas的合并方法（已注释）
    portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1  # 设置起始日净值为1
    portfolio_nav = portfolio_nav.sort_index().cumprod()  # 按日期排序并计算累计净值
    portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '汇总出池'}, axis=1)], axis=1)  # 将出池基金净值添加到总表

    # 添加基准指数数据并进行数据处理
    portfolio_nav_all=portfolio_nav_all.join(w.wsd(benchmark, "close", portfolio_nav_all.index[0].strftime("%Y-%m-%d"), "","Days=Alldays;Fill=Previous",usedf=True)[1].rename({'CLOSE':benchmark},axis=1))  # 获取基准指数数据并合并
    portfolio_nav_all=portfolio_nav_all.fillna(method='ffill')/portfolio_nav_all.iloc[0]  # 前向填充缺失值并归一化净值
    portfolio_nav_all['重点池/可买池']=(portfolio_nav_all['重点池']/portfolio_nav_all['可买池']-1)*100  # 计算重点池相对可买池的超额收益（百分比）
    portfolio_nav_all=portfolio_nav_all.rename({benchmark:fund_benchmark_name[label]},axis=1)  # 将基准指数代码替换为可读名称


    # %%
    # 设置绘图参数
    # 定义各曲线的颜色和线型
    color_dict={fund_benchmark_name[label]:(0.7,0.4,0),'可买池':'orange','基础池':(0.5,0.3,0.8),'重点池':'purple','模拟池':'blue','每期新进':'red','汇总出池':'green'}  # 颜色字典
    line_dict={fund_benchmark_name[label]:'--','可买池':'--','基础池':'--','重点池':'--','模拟池':'-','每期新进':'--','汇总出池':'--'}  # 线型字典

    # %%
    # 绘制基金池业绩走势图
    # 准备绘图数据
    df=portfolio_nav_all.copy()  # 复制净值数据
    pptsize(209)  # 设置图表大小（自定义函数）
    fig_name = label+'_基金池业绩走势跟踪'+end_day  # 设置图表名称
    jc_date=adjust_date_todate[0]  # 获取基准日期
    lm_date = w.tdaysoffset(0, lm_date, "").Data[0][0].date()  # 获取上月日期

    # 计算年初至今（YTD）的收益率数据，用于图表标注
    text_mark3 = df.iloc[-1][[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']] / df.loc[w.tdaysoffset(0, ytd, "").Data[0][0].date(),[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']]
    text_mark3 = ['YTD：','{0}：'.format(fund_benchmark_name[label]),str(round((text_mark3[fund_benchmark_name[label]]-1)*100,2)),'，可买池：',str(round((text_mark3['可买池']-1)*100,2)),'，基础池：',str(round((text_mark3['基础池']-1)*100,2)),'，重点池：',str(round((text_mark3['重点池']-1)*100,2)),'，每期新进：',str(round((text_mark3['每期新进']-1)*100,2)),'，汇总出池：',str(round((text_mark3['汇总出池']-1)*100,2)),'，超额：',str(round((text_mark3['重点池']-text_mark3['可买池'])*100,2))]
    text_mark3 = ''.join(text_mark3)  # 将列表转为字符串
    text_mark=text_mark3  # 设置图表标注文本

    # 创建图表和坐标轴
    fig, ax1 = plt.subplots()  # 创建图表和主坐标轴

    # 绘制各基金池的净值曲线
    for column in [fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']:
        ax1.plot(df.index, df[column].tolist(), label = column, color=color_dict[column], alpha = 0.7 ,linestyle=line_dict[column])  # 绘制净值曲线
        ax1.text(df.index[-1], df[column].tolist()[-1],
                        f'{column}: {df[column].tolist()[-1]:.4f}', color=color_dict[column], verticalalignment='bottom',fontsize=fontsize_text-6)  # 添加曲线末端标签
    ax1.tick_params(axis='y',labelsize=fontsize_legend)  # 设置y轴刻度标签大小
    ax1.spines['right'].set_color((0,0,1,0.5))  # 设置右侧坐标轴颜色
    ax1.spines['top'].set_visible(False)  # 隐藏顶部坐标轴
    plt.xticks(rotation=0)  # 设置x轴刻度标签旋转角度

    #近一月
    ax1.axvline(x=lm_date,color=(0.7,0.7,0.7),linestyle='--',linewidth=0.5)
    plt.grid(alpha=light)

    #超额
    ax2 = plt.twinx()
    column='重点池/可买池'
    ax2.fill_between(df.index, 0, df[column].tolist(), facecolor = 'blue', alpha = 0.3) #light)
    ax2.text(df.index[-1], df[column].tolist()[-1],
                        f'{column}: {df[column].tolist()[-1]:.2f}', color=(0,0,1,0.5), verticalalignment='bottom',fontsize=fontsize_text-6)
    ax2.set_ylabel('重点池相对可买池收益(%)',fontsize=fontsize_legend,color=(0,0,1,0.5))
    ax2.tick_params(axis='y',labelsize=fontsize_legend,color=(0,0,1,0.5),labelcolor=(0,0,1,0.5))
    ax2.spines['right'].set_color((0,0,1,0.5))
    ax2.spines['top'].set_visible(False)
    ax2.text(1,0.03, text_mark, transform=ax2.transAxes, horizontalalignment='right',verticalalignment='bottom',fontsize=fontsize_text-2)
    ax2.set_xlim(df.index[0], df.index[-1])
    plt.gcf().autofmt_xdate()
    plt.title(fig_name,fontsize=fontsize_suptitle)

    plt.show()
    fig.savefig('图表/{0}.jpg'.format(fig_name))
    plt.close()

    for date in date_lst.keys():
        temp = w.tdaysoffset(0, date_lst[date], "").Data[0][0].date()
        dict_graph[date].loc[label,'重点池'] =(df.iloc[-1][['重点池']] / df.loc[temp][['重点池']]).values[0]-1
        dict_graph[date].loc[label, '可买池'] = (df.iloc[-1][['可买池']] / df.loc[temp][['可买池']]).values[0]-1
        dict_graph[date].loc[label, '基础池'] = (df.iloc[-1][['基础池']] / df.loc[temp][['基础池']]).values[0]-1

plt_industry_pool('1W', dict_graph['1W'], '2-1-1(上) 各行业基金池收益'+'1W', fpath_draw, data_date)
plt_industry_pool('1M', dict_graph['1M'], '2-1-1(下) 各行业基金池收益'+'1M', fpath_draw, data_date)
plt_industry_pool('3M', dict_graph['3M'], '2-1-2(上) 各行业基金池收益'+'3M', fpath_draw, data_date)
plt_industry_pool('6M', dict_graph['6M'], '2-1-2(下) 各行业基金池收益'+'6M', fpath_draw, data_date)
plt_industry_pool('YTD', dict_graph['YTD'], '2-1-3(上) 各行业基金池收益'+'YTD', fpath_draw, data_date)
plt_industry_pool('1Y', dict_graph['1Y'], '2-1-3(下) 各行业基金池收益'+'1Y', fpath_draw, data_date)

print('2.0 基金池动态跟踪完毕')
printtime(t)

#%% 三、等权重全市场组合跟踪
# 格式化日期，用于后续数据处理
today_str2 = today.strftime('%Y%m%d')  # 将今天的日期转换为'YYYYMMDD'格式的字符串
last1y2 = (today + relativedelta(years=-1)).strftime('%Y%m%d')  # 计算一年前的日期并格式化

# 生成月末日期序列，用于后续基金池调整的时间点
end_month_date = pd.date_range(start=last1y2, end=today_str2, freq='M')  # 从一年前到今天，按月生成日期序列
end_month_date = [(i+relativedelta(months=-1))for i in end_month_date]  # 将每个日期向前移动一个月
end_month_date = [(i + pd.offsets.MonthEnd(0)).strftime('%Y%m%d') for i in end_month_date]  # 将每个日期调整到月末并格式化为字符串

# 创建字典用于存储不同时间点的基金池数据
POOL = dict()
for i in range(len(pool_dates)):
    date = pool_dates[i][:6]  # 提取年月部分
    # 处理特定月份的基金池数据（使用qfp格式文件）
    if date in ['202301','202304','202307','202310','202401','202404']:
        # 读取基金池数据
        fund0 = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date), sheet_name='基金池', index_col=0)
        # 筛选出初始基金、主动管理且为A股全市场策略的基金
        fund0 = fund0[(fund0['是否为初始基金'] == '是') & (fund0['管理方式(VI)'] == '主动') & (fund0['资产细分(V)'] == 'A股全市场策略')]
        # 如果没有基金池级别列，则根据其他列创建该列
        if '基金池级别' not in fund0.columns:
            fund0['基金池级别'] = '禁买'  # 默认设置为禁买
            fund0.loc[fund0['是否重点池'] == '是', '基金池级别'] = '重点'  # 重点池基金
            fund0.loc[(fund0['是否重点池'] != '是') & (fund0['是否基础池'] == '是'), '基金池级别'] = '基础'  # 基础池基金
            fund0.loc[(fund0['是否重点池'] != '是') & (fund0['是否基础池'] != '是') & (fund0['是否可买池'] == '是'), '基金池级别'] = '可买'  # 可买池基金
        # 选择需要的列并重命名
        fund0 = fund0[['简称', '资产类别(I)', '资产地区(II)', '资产类属(III)', '资产板块(IV)', '资产细分(V)', '基金池级别']]
        fund0.columns = ['基金简称', '资产类别I', '资产地区II', '资产类属III', '资产板块IV', '资产细分V', '基金池级别']
        POOL[pool_dates[i]] = fund0  # 将处理后的数据存入字典
    else:
        # 处理其他月份的基金池数据（使用季度筛选结果明细文件）
        fund0 = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(date), sheet_name='ag-grid', index_col=2)
        fund0 = fund0.drop('基金代码',axis=1)
        fund0.index.name = '基金代码'  # 用wind代码替换6位数字的基金代码
        # 筛选出非A类、主动管理且为A股全市场策略的基金
        fund0 = fund0[(~fund0['银河证券三级分类'].str.contains('非A类')) & (fund0['管理方式VI'] == '主动管理') & (fund0['资产细分V'] == 'A股全市场策略')]
        fund0['基金池级别'] = fund0['公募FOF基金池级别']  # 复制公募FOF基金池级别列
        fund0.loc[fund0['公募FOF基金池级别']=='核心', '基金池级别'] = '重点'  # 将核心级别改为重点
        # 选择需要的列
        fund0 = fund0[['基金简称', '资产类别I', '资产地区II', '资产类属III', '资产板块IV', '资产细分V', '基金池级别']]
        POOL[pool_dates[i]] = fund0  # 将处理后的数据存入字典

# 1、季度重点池-出入分析
s=0  # 初始化计数器
# 创建字典用于存储重点池基金、出池基金和入池基金
dict_key, dict_keyout, dict_keyin = dict(), dict(), dict()
out_of_pool, in_to_pool = set(), set()  # 创建集合用于存储出池和入池基金
for i in range(5):
    old_date, new_date = pool_dates[i], pool_dates[i + 1]  # 获取相邻两个时间点
    fund_old = POOL[old_date]  # 获取旧时间点的基金池数据
    fund_new = POOL[new_date]  # 获取新时间点的基金池数据
    old_pool = fund_old[fund_old['基金池级别'] == '重点'].index.tolist()  # 获取旧时间点的重点池基金列表
    new_pool = fund_new[fund_new['基金池级别'] == '重点'].index.tolist()  # 获取新时间点的重点池基金列表
    out_of_pool.update(set(old_pool) - set(new_pool))  # 更新出池基金集合（在旧池中但不在新池中的基金）
    in_to_pool = set(new_pool) - set(old_pool)  # 获取入池基金集合（在新池中但不在旧池中的基金）

    # 将各类基金列表存入相应的字典
    dict_key[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] == '重点'].index.tolist()})  # 当前重点池基金
    dict_keyout[new_date] = pd.DataFrame({'基金代码': list(out_of_pool)})  # 出池基金
    dict_keyin[new_date] = pd.DataFrame({'基金代码': list(in_to_pool)})  # 入池基金
    # 累计基金数量
    s += len(dict_key[new_date].index)+len(dict_keyin[new_date].index)+len(dict_keyout[new_date].index)

# 2、季度基础池-出入分析
# 创建字典用于存储基础池基金、出池基金和入池基金
dict_basic, dict_basicout, dict_basicin = dict(), dict(), dict()
out_of_pool, in_to_pool = set(), set()  # 重置集合
for i in range(5):
    old_date, new_date = pool_dates[i], pool_dates[i + 1]  # 获取相邻两个时间点
    fund_old = POOL[old_date]  # 获取旧时间点的基金池数据
    fund_new = POOL[new_date]  # 获取新时间点的基金池数据
    # 获取旧时间点的基础池和重点池基金列表（合并考虑）
    old_pool = fund_old[(fund_old['基金池级别'] == '基础') | (fund_old['基金池级别'] == '重点')].index.tolist()
    # 获取新时间点的基础池和重点池基金列表（合并考虑）
    new_pool = fund_new[(fund_new['基金池级别'] == '基础') | (fund_old['基金池级别'] == '重点')].index.tolist()
    out_of_pool.update(set(old_pool) - set(new_pool))  # 更新出池基金集合
    in_to_pool = set(new_pool) - set(old_pool)  # 获取入池基金集合

    # 将各类基金列表存入相应的字典
    dict_basic[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] == '基础'].index.tolist()})  # 当前基础池基金
    dict_basicout[new_date] = pd.DataFrame({'基金代码': list(out_of_pool)})  # 出池基金
    dict_basicin[new_date] = pd.DataFrame({'基金代码': list(in_to_pool)})  # 入池基金
    # 累计基金数量
    s += len(dict_basic[new_date].index) + len(dict_basicin[new_date].index) + len(dict_basicout[new_date].index)

# 3、季度可买池分析
dict_kemai = dict()  # 创建字典用于存储可买池基金
for i in range(5):
    new_date = pool_dates[i + 1]  # 获取时间点
    fund_new = POOL[new_date]  # 获取该时间点的基金池数据
    # 获取非禁买的基金列表（即可买池基金）
    dict_kemai[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] != '禁买'].index.tolist()})
    s += len(dict_basic[new_date].index)  # 累计基金数量

# 4、提取基金净值数据
dict_nav = list()  # 创建列表用于存储基金净值数据
for i in range(6):
    if i==0:
        continue  # 跳过第一个时间点
    elif i==1:
        # 第一个区间：从一年前到第二个时间点
        old_date, new_date = last1y2, pool_dates[i + 1]
        pool_date = pool_dates[1]  # 使用第二个时间点的基金池
    elif i==5:
        # 最后一个区间：从最后一个时间点到今天
        old_date, new_date = pool_dates[i], today_str2
        pool_date = pool_dates[i]  # 使用最后一个时间点的基金池
    else:
        # 中间区间：相邻两个时间点
        old_date, new_date = pool_dates[i], pool_dates[i + 1]
        pool_date = pool_dates[i]  # 使用当前时间点的基金池

    # 合并所有类型的基金代码集合（重点池、基础池、可买池及其出入池基金）
    set_all = set(dict_key[pool_date]['基金代码'])|set(dict_keyout[pool_date]['基金代码'])|set(dict_keyin[pool_date]['基金代码'])
    set_all = set_all|set(dict_basic[pool_date]['基金代码'])|set(dict_basicout[pool_date]['基金代码'])|set(dict_basicin[pool_date]['基金代码'])
    set_all = set_all|set(dict_kemai[pool_date]['基金代码'])

    fund_list = list(set_all)  # 转换为列表
    # 使用Wind API获取基金净值数据
    api_i = w.wsd(fund_list, 'NAV_adj', old_date, new_date)
    fundNav_i = api_i.Data  # 获取净值数据
    date_i = api_i.Times  # 获取日期数据
    fundNav_np_i = np.array(fundNav_i)  # 转换为numpy数组
    # 创建DataFrame存储净值数据，行索引为日期，列索引为基金代码
    df_new = pd.DataFrame(np.transpose(fundNav_np_i), index=list(date_i), columns=fund_list)
    # 将数据添加到列表中
    dict_nav.append({'基金池': pool_date, '时间区间':(old_date, new_date),'基金净值':df_new})

# 5、与各基金池名单匹配，计算净值
portPoint_df = pd.DataFrame()  # 创建DataFrame用于存储各基金池的净值数据
fundNavList = list()  # 创建列表用于存储可买池的收益率数据

# 计算可买池的净值
for temp_dict in dict_nav:
    pool_date = temp_dict['基金池']  # 获取基金池日期
    df_new = temp_dict['基金净值']  # 获取净值数据
    i_kemai = dict_kemai[pool_date]  # 获取该日期的可买池基金列表
    i_kemai = df_new[list(i_kemai['基金代码'])]  # 筛选出可买池基金的净值数据
    i_kemai = i_kemai.median(axis=1)  # 计算每日的中位数净值（横截面）
    i_kemai = i_kemai.pct_change(axis=0)  # 计算日收益率
    # 处理第一个时间点的收益率
    if temp_dict['时间区间'][0] == last1y2:
        i_kemai.iloc[0] = 0  # 如果是第一个区间，将第一天的收益率设为0
    else:
        i_kemai = i_kemai[1:]  # 否则删除第一天的收益率（因为是NaN）
    fundNavList.append(i_kemai)  # 将收益率数据添加到列表中

# 合并所有时间区间的收益率数据
fundNavList = pd.concat(fundNavList, axis=0)
# 将索引（日期）格式化为字符串
fundNavList.index = [datetime.strftime(x, "%Y-%m-%d") for x in fundNavList.index]
# 计算累计净值（从100开始）
portPoint_df['可买池'] = 100 * (1+fundNavList).cumprod()

# 定义函数用于计算各类基金池的净值
def concat_nav(dict_pool, dict_nav, begin_date):
    """
    计算指定基金池的累计净值

    参数:
    dict_pool: 包含各时间点基金池成分的字典
    dict_nav: 包含各时间区间基金净值的列表
    begin_date: 起始日期

    返回:
    fundNavList: 累计净值序列
    """
    fundNavList = list()  # 创建列表用于存储收益率数据
    for temp_dict in dict_nav:
        pool_date = temp_dict['基金池']  # 获取基金池日期
        df_new = temp_dict['基金净值']  # 获取净值数据

        i_pool = dict_pool[pool_date]  # 获取该日期的基金池列表
        i_pool = df_new[list(i_pool['基金代码'])]  # 筛选出基金池中基金的净值数据
        i_pool = i_pool.mean(axis=1)  # 计算每日的平均净值（横截面）
        i_pool = i_pool.pct_change(axis=0)  # 计算日收益率
        # 处理第一个时间点的收益率
        if begin_date==temp_dict['时间区间'][0]:
            i_pool.iloc[0] = 0  # 如果是第一个区间，将第一天的收益率设为0
        else:
            i_pool = i_pool[1:]  # 否则删除第一天的收益率（因为是NaN）
        fundNavList.append(i_pool)  # 将收益率数据添加到列表中

    # 合并所有时间区间的收益率数据
    fundNavList = pd.concat(fundNavList, axis=0)
    # 计算累计净值（从100开始）
    fundNavList = 100 * (1 + fundNavList).cumprod()
    # 将索引（日期）格式化为字符串
    fundNavList.index = [datetime.strftime(x, "%Y-%m-%d") for x in fundNavList.index]
    return fundNavList

# 计算各类基金池的累计净值
portPoint_df['重点池'] = concat_nav(dict_key, dict_nav, last1y2)  # 重点池净值
portPoint_df['重点池_每期新进'] = concat_nav(dict_keyin, dict_nav, last1y2)  # 重点池每期新进基金净值
portPoint_df['重点池_汇总出池'] = concat_nav(dict_keyout, dict_nav, last1y2)  # 重点池汇总出池基金净值
portPoint_df['基础池'] = concat_nav(dict_basic, dict_nav, last1y2)  # 基础池净值
portPoint_df['基础池_每期新进'] = concat_nav(dict_basicin, dict_nav, last1y2)  # 基础池每期新进基金净值
portPoint_df['基础池_汇总出池'] = concat_nav(dict_basicout, dict_nav, last1y2)  # 基础池汇总出池基金净值

# 绘制重点池和基础池的业绩走势图
plt_portfolio_median(portPoint_df, '重点池', fpath_draw, data_date, '2-2-1 等权重基金池业绩走势跟踪-重点池', last_year='2024-12-31')
plt_portfolio_median(portPoint_df, '基础池', fpath_draw, data_date, '2-2-2 等权重基金池业绩走势跟踪-基础池', last_year='2024-12-31')

print('3.0 等权重全市场组合跟踪完毕')  # 输出进度信息
printtime(t)  # 记录执行时间

#%% 四、市场回顾
# 定义不同类型的基金分类列表，用于后续分析
standard = ['标准股票型基金(A类)','港股通标准股票型基金(A类)']  # 标准股票型基金分类，银河分类每季度需对应更改，可通过基础池透视得到
blend = ['港股通偏股型基金(A类)','偏股型基金(股票上下限60%-95%)(A类)','偏股型基金(股票上限80%)(A类)','偏股型基金(股票上限95%)(A类)','北交所主题偏股型基金(A类)']  # 偏股型基金分类
others = ['普通偏债型基金(股票上限不高于30%)(A类)','普通债券型基金(二级)(A类)','长期纯债债券型基金(A类)','QDII股票型基金(A类)',\
           'QDII债券型基金(A类)','商品期货ETF基金','黄金ETF基金']  # 其他类型基金分类

# 创建用于存储各类基金收益率分位数的DataFrame
rtn_summary = pd.DataFrame(columns = ['近一周','近一月','近三月','年初至今','近一年'])  # 用于存储各时间段收益率分位数
rtn_monthly = pd.DataFrame(columns = [i.strftime('%Y-%m') for i in ts])  # 用于存储月度收益率，列名为月份

# 计算标准股票型基金的收益率分位数
df1 = fund[fund['银河证券三级分类'].isin(standard)]  # 筛选出标准股票型基金
rtn_summary.loc['标准股票型基金 25"'] = df1[rtn_summary.columns].quantile(0.75)  # 计算上四分位数（表现较好的基金）
rtn_summary.loc['标准股票型基金 50"'] = df1[rtn_summary.columns].quantile(0.5)   # 计算中位数
rtn_summary.loc['标准股票型基金 75"'] = df1[rtn_summary.columns].quantile(0.25)  # 计算下四分位数（表现较差的基金）
rtn_monthly.loc['标准股票型基金'] = df1[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数

# 计算偏股型基金的收益率分位数
df2 = fund[fund['银河证券三级分类'].isin(blend)]  # 筛选出偏股型基金
rtn_summary.loc['偏股型基金 25"'] = df2[rtn_summary.columns].quantile(0.75)  # 计算上四分位数
rtn_summary.loc['偏股型基金 50"'] = df2[rtn_summary.columns].quantile(0.5)   # 计算中位数
rtn_summary.loc['偏股型基金 75"'] = df2[rtn_summary.columns].quantile(0.25)  # 计算下四分位数
rtn_monthly.loc['偏股型基金'] = df2[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数

# 计算其他类型基金的收益率分位数
for i in others:
    df3 = fund[fund['银河证券三级分类'] == i]  # 筛选出特定类型的基金
    if i == '黄金ETF基金':  # 黄金ETF基金特殊处理，去掉后缀"基金"
        rtn_summary.loc[i[:-5]+' 25"'] = df3[rtn_summary.columns].quantile(0.75)  # 计算上四分位数
        rtn_summary.loc[i[:-5]+' 50"'] = df3[rtn_summary.columns].quantile(0.5)   # 计算中位数
        rtn_summary.loc[i[:-5]+' 75"'] = df3[rtn_summary.columns].quantile(0.25)  # 计算下四分位数
        rtn_monthly.loc[i[:-5]] = df3[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数
    else:  # 其他类型基金处理，去掉后缀"(A类)"
        rtn_summary.loc[i[:-4]+' 25"'] = df3[rtn_summary.columns].quantile(0.75)  # 计算上四分位数
        rtn_summary.loc[i[:-4]+' 50"'] = df3[rtn_summary.columns].quantile(0.5)   # 计算中位数
        rtn_summary.loc[i[:-4]+' 75"'] = df3[rtn_summary.columns].quantile(0.25)  # 计算下四分位数
        rtn_monthly.loc[i[:-4]] = df3[rtn_monthly.columns].quantile(0.5)  # 计算月度收益率的中位数

# 对收益率数据进行四舍五入，保留两位小数
rtn_summary = rtn_summary.round(2)
rtn_monthly = rtn_monthly.round(2)

# 创建用于绘图的DataFrame，添加中证800指数的收益率数据
df_graph = pd.DataFrame()
# 使用Wind API获取中证800指数在不同时间段的收益率
df_graph.loc['中证800', '近一周'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近一月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近三月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '年初至今'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0][0]
df_graph.loc['中证800', '近一年'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0][0]

# 合并中证800指数和各类基金中位数收益率数据，用于后续绘图
df_graph = pd.concat([df_graph, rtn_summary[rtn_summary.index.str.contains('50"')][['近一周','近一月','近三月','年初至今','近一年']]]).round(2)

# 调用自定义函数绘制月度收益率图表和板块暴露线图
plt_monthly_return(rtn_monthly, '各类资产月度收益率('+data_date+')', fpath_draw)  # 绘制各类资产月度收益率图表
plate_exposure_line(today, data_date, fpath_draw)  # 绘制板块暴露线图

print('4.0 市场回顾完毕')  # 输出进度信息
printtime(t)  # 记录执行时间并更新时间点

#%% 五、收益归因
holding = pd.read_pickle('基础数据/所有基金重仓数据_20241231-20241231.pkl')
holding = holding[holding['报告日期']==rpt_date]

#筛选出全A基金
q_fund=pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(pool_date[:6]),sheet_name='ag-grid',index_col=2)
q_fund=q_fund.drop('基金代码',axis=1)
q_fund.index.name='基金代码'  #用wind代码替换6位数字的基金代码
A_fund=q_fund[(q_fund['资产地区II']=='境内股市') & (q_fund['管理方式VI']=='主动管理') & (~q_fund['银河证券三级分类'].str.contains('非A类'))].index.tolist()
A_fund=[A_fund_i[:-3] for A_fund_i in A_fund]
holding = holding[holding['基金代码'].isin(A_fund)]

index_list = [i[0:6] for i in w.wset("sectorconstituent","date=" + rpt_date + ";sectorid=2001010102000000").Data[1]] #申万一级行业分类
index_list2 = [i[0:6] for i in w.wset("sectorconstituent","date=" + rpt_date + ";sectorid=2001010103000000").Data[1]] #申万二级行业分类
index_list = index_list + index_list2
holding = holding[~holding['基金代码'].isin(index_list)]
mf = holding.groupby('股票代码')['持股市值'].sum()
mf = pd.DataFrame(mf[~mf.index.str.contains('HK')]).sort_values(by = '持股市值', ascending=False)
mf['持仓占比'] = mf['持股市值'] / mf['持股市值'].sum()
mf['简称'] = w.wss(mf.index.tolist(),"sec_name").Data[0]
mf['行业'] = w.wss(mf.index.tolist(),"industry_citic","tradeDate="+today_str+";industryType=1").Data[0]
mf['近一周'] = w.wss(mf.index.tolist(), "pct_chg_per","startDate=" + last1w + ";endDate=" + today_str).Data[0]
mf['收益'] = mf['持仓占比'] * mf['近一周']

zz800 = w.wset("indexconstituent","date=" + rpt_date + ";windcode=000906.SH")
zz800 = pd.DataFrame(np.array(zz800.Data).T, columns = zz800.Fields)
zz800['i_weight'] = zz800['i_weight'].astype(float) / 100
zz800['行业'] = w.wss(zz800['wind_code'].tolist(),"industry_citic","tradeDate="+today_str+";industryType=1").Data[0]
zz800['近一周'] = w.wss(zz800['wind_code'].tolist(), "pct_chg_per","startDate=" + last1w + ";endDate=" + today_str).Data[0]
zz800['收益'] = zz800['i_weight'] * zz800['近一周']

allocation = pd.DataFrame()
allocation['公募配置'] = mf.groupby('行业')['持仓占比'].sum()
allocation['公募收益'] = mf.groupby('行业')['收益'].sum() / allocation['公募配置']
allocation['中证800配置'] = zz800.groupby('行业')['i_weight'].sum()
allocation['中证800收益'] = zz800.groupby('行业')['收益'].sum() / allocation['中证800配置']
allocation['超额收益'] = allocation['公募配置'] * allocation['公募收益'] - allocation['中证800配置'] * allocation['中证800收益']
allocation['配置超额'] = (allocation['公募配置'] - allocation['中证800配置']) * allocation['中证800收益']
allocation['选股超额'] = (allocation['公募收益'] - allocation['中证800收益']) * allocation['公募配置']
allocation[['公募配置', '中证800配置']] = allocation[['公募配置', '中证800配置']] *100
allocation['配置差额'] = allocation['公募配置'] - allocation['中证800配置']
allocation = allocation.sort_values(by = '配置差额', ascending=False)
#allocation.index = [x[2:] for x in list(allocation.index)]

citic_idx = ['CI00500'+str(i)+'.WI' for i in range(1,10)] + ['CI0050'+str(i)+'.WI' for i in range(10,31)]
citic_idx_rtn = w.wss(citic_idx, "sec_name")
citic_idx_rtn = pd.DataFrame(index = [i[:-4] for i in citic_idx_rtn.Data[0]])
citic_idx_rtn['近一周'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + last1w + ";endDate=" + today_str).Data[0]
citic_idx_rtn['近一月'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + last1m + ";endDate=" + today_str).Data[0]
citic_idx_rtn['近三月'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + last3m + ";endDate=" + today_str).Data[0]
allocation[['近一周', '近一月', '近三月']] = citic_idx_rtn[['近一周', '近一月', '近三月']]
allocation = allocation.round(4)
allocation.index.name='行业'

plt_excess_return(allocation, fpath_draw, '本周超额收益来源分解('+data_date+')')
scatter_industry(allocation, df_graph, ['近一周', '近三月'], fpath_draw, data_date)

print('5.0 收益归因计算完毕')
printtime(t)
#%% 六、热门权益基金
pop_fund = dict()
item = ['基金简称','基金经理','近一周','近一月','近三月','年初至今','近一年']
A_level5 = ['A股全市场策略','A股价值策略','A股小盘策略','新能源','TMT','军工','其他成长','医药','白酒','其他消费','金融','周期','其他金融周期']
sample = fund[(fund['资产细分V'].isin(A_level5) & (fund['资产类别I']=='股票') & (fund['管理方式VI']=='主动管理'))]

pop_fund['近一月'] = sample.sort_values(by = '近一月', ascending = False).head(100)[item].round(2)
pop_fund['近三月'] = sample.sort_values(by = '近三月', ascending = False).head(100)[item].round(2)
pop_fund['年初至今'] = sample.sort_values(by = '年初至今', ascending = False).head(100)[item].round(2)
pop_seperate = list()
for i_name,i_group in sample.groupby(['资产细分V']):
    if len(i_group.index)<5:
        num = len(i_group.index)
    else:
        num = 5
    i_df = i_group.sort_values(by = '年初至今', ascending = False).head(num)[item].round(2)
    i_df['资产细分V'] = i_name[0]
    pop_seperate.append(i_df)
pop_fund['分类热门'] = pd.concat(pop_seperate, axis=0)

print('6.0 热门权益基金统计完毕')
printtime(t)
#%% 七、基金公司收益
# 创建字典用于存储各类基金公司收益分析结果
amc_rank = dict()

# 1. 按地区分析基金公司收益
amc_rank['1区域收益'] = pd.DataFrame()  # 创建空DataFrame用于存储按地区分析的结果
# 遍历三个主要地区：北京、上海、广东
for i in ['北京','上海','广东']:
    # 筛选出办公地址在当前地区、资产细分属于A_level5列表中、资产类别为股票、且非指数非ETF的基金
    df5 = fund[(fund['办公地址'].str.contains(i)) & (fund['资产细分V'].isin(A_level5)) & (fund['资产类别I']=='股票')&((fund['银河证券二级分类'].str.contains('指数')==False) & (fund['银河证券二级分类'].str.contains('ETF')==False))]
    # 统计该地区的产品数量（基金数量）
    amc_rank['1区域收益'].loc[i, '产品数量'] = df5['年初至今'].count()
    # 计算该地区基金年初至今收益的25分位数（上四分位数，表现较好的基金）
    amc_rank['1区域收益'].loc[i, '25分位'] = df5['年初至今'].quantile(0.75)
    # 计算该地区基金年初至今收益的中位数（50分位数）
    amc_rank['1区域收益'].loc[i, '50分位'] = df5['年初至今'].quantile(0.5)
    # 计算该地区基金年初至今收益的75分位数（下四分位数，表现较差的基金）
    amc_rank['1区域收益'].loc[i, '75分位'] = df5['年初至今'].quantile(0.25)
    # 计算该地区基金的规模加权收益率（用基金规模作为权重计算加权平均收益率）
    amc_rank['1区域收益'].loc[i, '规模加权'] = (df5['基金规模'] * df5['年初至今']).sum() / df5['基金规模'].sum()

# 2. 分析所有权益基金的公司收益情况
amc_rank['2权益基金'] = pd.DataFrame()  # 创建空DataFrame用于存储权益基金分析结果

# 筛选出资产细分属于A_level5列表中、资产类别为股票、管理方式为主动管理的基金，并删除基金管理人为空的记录
df4 = (fund[fund['资产细分V'].isin(A_level5) & (fund['资产类别I']=='股票') & (fund['管理方式VI']=='主动管理')]).dropna(subset=['基金管理人'])
# 处理基金管理人名称，去掉末尾的"（XX）"部分
df4['基金管理人'] = [s[:-2] for s in list(df4['基金管理人'])]

# 保存一份基金详细信息，用于后续分析
df4_save=df4[['基金简称','资产细分V','基金经理','基金成立日期','基金规模','年初至今','my是否基础池']].sort_values(by=['资产细分V','基金简称','年初至今'],ascending=False)

# 按基金管理人分组统计产品数量
amc_rank['2权益基金']['产品数量'] = df4.groupby('基金管理人')['基金规模'].count()
# 按基金管理人分组计算产品总规模（单位：亿元）
amc_rank['2权益基金']['产品规模'] = df4.groupby('基金管理人')['基金规模'].sum() / 1e8
# 按基金管理人分组计算年初至今收益率的中位数
amc_rank['2权益基金']['中位数收益'] = df4.groupby('基金管理人')['年初至今'].median()
# 按基金管理人分组计算规模加权收益率
amc_rank['2权益基金']['规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
# 按基金管理人分组计算去年收益率的中位数
amc_rank['2权益基金']['去年中位数收益'] = df4.groupby('基金管理人')['去年'].median()
# 按基金管理人分组计算去年的规模加权收益率（排除去年收益率为空的基金）
amc_rank['2权益基金']['去年规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
# 按产品规模降序排列，并将数值四舍五入到小数点后两位
amc_rank['2权益基金'] = amc_rank['2权益基金'].sort_values(by = '产品规模', ascending=False).round(2)

# 3. 分析A股全市场基金的公司收益情况
i = 'A股全市场基金'
# 筛选出资产板块为"A股全市场基金"且管理方式为主动管理的基金，并删除基金管理人为空的记录
df6 = (fund[(fund['资产板块IV'] == i)& (fund['管理方式VI']=='主动管理')]).dropna(subset=['基金管理人'])
# 处理基金管理人名称，去掉末尾的"（XX）"部分
df6['基金管理人'] = [s[:-2] for s in list(df6['基金管理人'])]
# 创建空DataFrame用于存储A股全市场基金分析结果
amc_rank[i] = pd.DataFrame()
# 按基金管理人分组统计产品数量
amc_rank[i]['产品数量'] = df6.groupby('基金管理人')['基金规模'].count()
# 按基金管理人分组计算产品总规模（单位：亿元）
amc_rank[i]['产品规模'] = df6.groupby('基金管理人')['基金规模'].sum() / 1e8
# 按基金管理人分组计算年初至今收益率的中位数
amc_rank[i]['ytd_中位数收益'] = df6.groupby('基金管理人')['年初至今'].median()
# 按基金管理人分组计算近一周收益率的中位数
amc_rank[i]['近一周_中位数收益'] = df6.groupby('基金管理人')['近一周'].median()
# 按基金管理人分组计算规模加权收益率
amc_rank[i]['规模加权收益'] = df6.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
# 按基金管理人分组计算去年收益率的中位数
amc_rank[i]['去年中位数收益'] = df6.groupby('基金管理人')['去年'].median()
# 按基金管理人分组计算去年的规模加权收益率（排除去年收益率为空的基金）
amc_rank[i]['去年规模加权收益'] = df6.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
# 按产品规模降序排列，并将数值四舍五入到小数点后两位
amc_rank[i] = amc_rank[i].sort_values(by = '产品规模', ascending=False).round(2)

# 4. 按资产细分分析各类基金的公司收益情况
# 遍历A_level5列表中的每种资产细分类型
for i in A_level5:
    # 筛选出资产细分为当前类型且管理方式为主动管理的基金，并删除基金管理人为空的记录
    df4 = fund[(fund['资产细分V']==i)&(fund['管理方式VI']=='主动管理')].dropna(subset=['基金管理人'])
    # 处理基金管理人名称，去掉末尾的"（XX）"部分
    df4['基金管理人'] = [s[:-2] for s in list(df4['基金管理人'])]
    # 创建空DataFrame用于存储当前资产细分类型的分析结果
    amc_rank[i] = pd.DataFrame()
    # 按基金管理人分组统计产品数量
    amc_rank[i]['产品数量'] = df4.groupby('基金管理人')['基金规模'].count()
    # 按基金管理人分组计算产品总规模（单位：亿元）
    amc_rank[i]['产品规模'] = df4.groupby('基金管理人')['基金规模'].sum() / 1e8
    # 按基金管理人分组计算年初至今收益率的中位数
    amc_rank[i]['ytd_中位数收益'] = df4.groupby('基金管理人')['年初至今'].median()
    # 按基金管理人分组计算近一周收益率的中位数
    amc_rank[i]['近一周_中位数收益'] = df4.groupby('基金管理人')['近一周'].median()
    # 按基金管理人分组计算规模加权收益率
    amc_rank[i]['规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
    # 按基金管理人分组计算去年收益率的中位数
    amc_rank[i]['去年中位数收益'] = df4.groupby('基金管理人')['去年'].median()
    # 按基金管理人分组计算去年的规模加权收益率（排除去年收益率为空的基金）
    amc_rank[i]['去年规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
    # 按产品规模降序排列，并将数值四舍五入到小数点后两位
    amc_rank[i] = amc_rank[i].sort_values(by = '产品规模', ascending=False).round(2)

# 5. 按资产板块分析各类基金的公司收益情况
# 遍历四个主要资产板块
for j in ['A股消费行业', 'A股医药行业', 'A股成长行业', 'A股金融周期行业']:
    # 筛选出资产板块为当前类型且管理方式为主动管理的基金，并删除基金管理人为空的记录
    df4 = fund[(fund['资产板块IV'] == j)&(fund['管理方式VI']=='主动管理')].dropna(subset=['基金管理人'])
    # 处理基金管理人名称，去掉末尾的"（XX）"部分
    df4['基金管理人'] = [s[:-2] for s in list(df4['基金管理人'])]
    # 创建空DataFrame用于存储当前资产板块的分析结果，使用'板块-'前缀区分
    amc_rank['板块-'+j] = pd.DataFrame()
    # 按基金管理人分组统计产品数量
    amc_rank['板块-'+j]['产品数量'] = df4.groupby('基金管理人')['基金规模'].count()
    # 按基金管理人分组计算产品总规模（单位：亿元）
    amc_rank['板块-'+j]['产品规模'] = df4.groupby('基金管理人')['基金规模'].sum() / 1e8
    # 按基金管理人分组计算年初至今收益率的中位数
    amc_rank['板块-'+j]['ytd_中位数收益'] = df4.groupby('基金管理人')['年初至今'].median()
    # 按基金管理人分组计算近一周收益率的中位数
    amc_rank['板块-'+j]['近一周_中位数收益'] = df4.groupby('基金管理人')['近一周'].median()
    # 按基金管理人分组计算规模加权收益率
    amc_rank['板块-'+j]['规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
    # 按基金管理人分组计算去年收益率的中位数
    amc_rank['板块-'+j]['去年中位数收益'] = df4.groupby('基金管理人')['去年'].median()
    # 按基金管理人分组计算去年的规模加权收益率（排除去年收益率为空的基金）
    amc_rank['板块-'+j]['去年规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
    # 按产品规模降序排列，并将数值四舍五入到小数点后两位
    amc_rank['板块-'+j] = amc_rank['板块-'+j].sort_values(by = '产品规模', ascending=False).round(2)

# 6. 绘制各类图表展示基金公司收益情况
# 绘制各公司权益基金散点图
scatter_manager(amc_rank['2权益基金'],'1-3-1 各公司权益基金',fpath_draw, data_date)
# 绘制各公司全市场策略基金散点图
scatter_manager(amc_rank['A股全市场策略'],'1-3-2 各公司全市场策略基金',fpath_draw, data_date)

# 定义三组基金类别，用于生成表格
# 全市场和金融周期类基金
group1 = ['A股全市场策略','A股价值策略','A股小盘策略','金融','周期','其他金融周期']
# 成长、医药和消费类基金
group2 = ['白酒','其他消费','医药','TMT','新能源','军工','其他成长']
# 四级资产板块基金
group3 = ['A股全市场基金', '板块-A股金融周期行业', '板块-A股成长行业', '板块-A股医药行业','板块-A股消费行业']

# 绘制三张表格，展示不同分组的基金公司收益情况
table_amc(amc_rank, group1, '1-3-4 各公司-全市场&金融周期', fpath_draw, data_date)
table_amc(amc_rank, group2, '1-3-5 各公司-成长&医药&消费', fpath_draw, data_date)
table_amc(amc_rank, group3, '1-3-3 各公司-四级资产板块', fpath_draw, data_date)

print('7.0 基金公司收益计算完毕')  # 输出进度信息
printtime(t)  # 记录执行时间

# %%
#%% 八、FOF基金
# 设置API请求参数，从内部服务器获取基金分类数据
url = 'http://192.168.105.63/GetFndLevelClassify'  # 内部服务器API地址
kw = {'username':'gyrx','passwd':'4E4AB38D17E274B0D2D6A846AE4393E7'}  # 访问API的用户名和密码
http = urllib3.PoolManager()  # 创建HTTP连接池管理器
r = http.request('post', url, body = dumps(kw))  # 发送POST请求并获取响应

# 定义FOF基金分析所需的指数代码和名称
fof_index = ['CI005917.WI','CI005918.WI','CI005919.WI','CI005920.WI','CI005921.WI','HSI.HI','SPX.GI']  # 用于对比的指数代码列表
fof_index_name = ['金融','周期','消费','成长','稳定','恒生指数','标普500']  # 对应的指数名称列表

# 处理从API获取的基金分类数据
fof_class = pd.DataFrame(loads(r.data.decode())['data'])  # 解析JSON响应并转换为DataFrame
fof_class.index = [x+'.OF' for x in fof_class['FUNDCODE']]  # 将基金代码设置为索引，并添加.OF后缀
# 将日期字符串转换为datetime对象
fof_class['FUNDCLOSEDATE'] = [dt.datetime.strptime(x[0:10],"%Y-%m-%d") for x in fof_class['FUNDCLOSEDATE']]  # 转换基金结束日期
fof_class['ESTABDATE'] = [dt.datetime.strptime(x[0:10],"%Y-%m-%d") for x in fof_class['ESTABDATE']]  # 转换基金成立日期
# 筛选出符合条件的FOF基金：未到期、已成立且名称中包含FOF
fof_class = fof_class[(fof_class['FUNDCLOSEDATE']>dt.datetime.strptime(today_str,"%Y-%m-%d"))  # 基金未到期
                      &(fof_class['ESTABDATE']<=dt.datetime.strptime(today_str,"%Y-%m-%d"))  # 基金已成立
                      &(fof_class['LEVEL3NAME'].str.contains('FOF'))]  # 三级分类名称包含FOF
# 选择需要的列并重命名
fof_class = fof_class.loc[:,['FUNDSNAME','F_FNAME','LEVEL1NAME','LEVEL2NAME','LEVEL3NAME']]  # 选择需要的列
fof_class.columns = ['简称','基金管理人全称','银河证券一级分类','银河证券二级分类','银河证券三级分类']  # 重命名列
fof_class['全称']=w.wss(fof_class.index.tolist(), "fund_fullname").Data[0]  # 通过Wind API获取基金全称
fof_class=fof_class.reset_index().set_index('全称')  # 重置索引，将全称设为新索引

# 计算6个月前的日期，用于后续分析
last6m = (today + relativedelta(months=-6) + relativedelta(days=1))
# 从Wind获取FOF基金列表
fof_list = w.wset("sectorconstituent","date=" + today_str + ";sectorid=1000041489000000")  # 获取FOF基金列表
# 创建包含基金基本信息的DataFrame
fof_fund = pd.DataFrame({'代码':fof_list.Data[1],'简称':fof_list.Data[2],'全称':w.wss(fof_list.Data[1], "fund_fullname").Data[0],
                         '是否初始基金':w.wss(fof_list.Data[1], "fund_initial").Data[0],'成立日':w.wss(fof_list.Data[1],"fund_setupdate").Data[0]}).set_index('全称')
# 筛选出初始基金且在今年之前成立的基金
fof_fund = fof_fund[(fof_fund['是否初始基金'] == '是')&(fof_fund['成立日']<ytd)]  # 只选取今年以前成立的初始基金
fof_fund=fof_fund.join(fof_class['银河证券三级分类'])  # 合并基金分类信息
fof_fund=fof_fund.reset_index().set_index('代码')  # 重置索引，将代码设为新索引
fof_fund=fof_fund[~fof_fund['银河证券三级分类'].str.contains('非A类')]  # 过滤掉非A类基金
# 调用自定义函数计算FOF基金的各项指标
fof_all = ct.fof_chart(fof_fund.index.to_list(), today_str, last1w, last1m, last3m, ytd, last1y, fof_index, fof_index_name)
fof=dict()  # 创建字典用于存储不同类型FOF基金的分析结果

# 定义需要分析的FOF基金类别列表
class_list = ['股票型FOF(A类)','混合型FOF(权益资产60%-95%)(A类)','养老目标日期FOF(2050)(A类)','养老目标日期FOF(2055)(A类)','养老目标日期FOF(2060)(A类)', '养老目标风险FOF(权益资产60%-80%)(A类)']
# 遍历每个基金类别，进行分析
for i in class_list:  # 对每个FOF类别进行处理
    temp=fof_fund[fof_fund['银河证券三级分类'] == i]  # 筛选出当前类别的基金
    # 将工银基金放在前面，其他基金按年初至今收益率降序排列
    temp = pd.concat([fof_all.loc[temp[temp['简称'].str.contains('工银')].index],  # 工银基金
                        fof_all.loc[temp[~temp['简称'].str.contains('工银')].index].sort_values(by='收益_YTD',
                                                                                            ascending=False)])  # 其他基金按YTD收益降序
    temp = ct.add_quantile(temp)  # 添加分位数统计信息
    fof[i[:-4]] = temp  # 存储到字典中，去掉类别名称末尾的"(A类)"

# 设置每页显示的基金数量
ngroup = 40
# 遍历每个FOF类别，生成表格和图表
for i in range(len(class_list)):
    class_name = class_list[i][:-4]  # 获取当前类别名称（去掉"(A类)"）
    df = fof[class_name]  # 获取当前类别的基金数据
    # 将基金按估算仓位降序排列，并保留最后3行（通常是统计行）
    df1, df2 = df.iloc[:-3].sort_values('估算仓位', ascending=False), df.iloc[-3:]  # 分离基金数据和统计行
    df = pd.concat([df1, df2], axis=0)  # 重新组合数据
    df.iloc[-3:, 0] = df.index[-3:]  # 设置统计行的名称
    # 为统计行计算分位数值
    for m in [-3, -2, -1]:  # 处理最后三行（75%、50%、25%分位数）
        df.iloc[m, 2] = df.iloc[:-3, 2].quantile(-0.25 * m).round(2)  # 计算估算仓位的分位数
        df.iloc[m, 3] = df.iloc[:-3, 3].quantile(-0.25 * m).round(2)  # 计算R^2的分位数
        df.iloc[m, 4] = df.iloc[:-3, 4].quantile(-0.25 * m).round(2)  # 计算收益_1W的分位数
    # 计算近一周收益与年初至今收益的相关性
    rho = df.iloc[:-3][['收益_1W', '收益_YTD']].corr('spearman').iloc[0, 1]  # 使用Spearman相关系数

    nrows = len(df.index)  # 获取行数
    # 为表格创建颜色映射，用于可视化展示
    df_color = color_map(df, ['估算仓位', 'R^2', '收益_1W', '超额收益_1W', '收益_1M', '超额收益_1M', '收益_3M',
                           '超额收益_3M', '最大回撤_3M', '收益_YTD', '超额收益_YTD', '最大回撤_YTD', '收益_1Y','超额收益_1Y', '最大回撤_1Y'])
    df['简称'] = [cut_name(s, 7) for s in list(df['简称'])]  # 截断基金简称，使其不超过7个字符

    # 根据基金数量决定是生成单个表格还是分页表格
    if nrows <= 40:  # 如果基金数量不超过40个，生成单个表格
        red_green_table(df, df_color, class_list[i] + '(' + fof_date + ')(相关性=' + str(round(rho, 2)) + ')',
                        '1-4 FOF ' + class_name, fpath_draw, True)  # 生成红绿配色的表格
    else:  # 如果基金数量超过40个，分页生成表格
        num_splits = (len(df) // ngroup) + 1  # 计算需要分成几页
        for i in range(num_splits):  # 遍历每一页
            start_index = i * ngroup  # 当前页的起始索引
            end_index = min((i + 1) * ngroup, len(df.index))  # 当前页的结束索引
            subset_df = df.iloc[start_index: end_index]  # 获取当前页的数据
            subset_df_color = df_color.iloc[start_index: end_index]  # 获取当前页的颜色映射
            # 判断是否为最后一页，最后一页需要添加分隔线
            if i == num_splits - 1:
                add_line = True  # 最后一页添加分隔线
            else:
                add_line = False  # 非最后一页不添加分隔线
            # 生成表格并保存
            red_green_table(subset_df, subset_df_color,class_name+'('+fof_date+')(相关性='+str(round(rho, 2))+')-'+str(i+1),
                            '1-4 FOF '+class_name+str(i+1), fpath_draw, add_line)  # 生成红绿配色的表格

print('8.0 FOF基金回顾计算完毕')  # 输出进度信息
printtime(t)  # 记录执行时间并更新时间点
#%% 九、权益基金池回顾
# 定义收益率和排名的权重字典，用于后续颜色映射
# 正值表示数值越大越好（如收益率），负值表示数值越小越好（如排名）
fund_col = {'收益_1W':1, '排名_1W':-1, '收益_1M':1, '排名_1M':-1, '收益_3M':1,
            '排名_3M':-1, '收益_YTD':1, '排名_YTD':-1, '收益_1Y':1, '排名_1Y':-1}

# 定义各类资产细分对应的基准指数代码字典
benchmark = {'A股全市场策略':'000906.SH','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ',
             '新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI',
             '医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH',
             '金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI',
             '偏股混合型基金指数':'885001.WI',
             '恒生指数':'HSI.HI', '恒生科技':'HSTECH.HI',
             '标普500':'SPX.GI', '纳斯达克':'IXIC.GI'}  #'工银股混':'930994.CSI',

# 定义资产板块(IV级)到资产细分(V级)的映射关系
level_4to5 = {'A股全市场基金':['A股全市场策略','A股价值策略','A股小盘策略'],
              'A股成长行业':['TMT','新能源','军工','其他成长'],
               'A股消费行业':['白酒','其他消费'],
               'A股医药行业':['医药'],
               'A股金融周期行业':['金融','周期','其他金融周期']}

# 定义各资产细分的核心池基金代码列表
core_dict = {'A股全市场策略':['000893.OF','008515.OF','000991.OF','001718.OF','481008.OF','001714.OF','000763.OF','002350.OF','008269.OF','166301.OF','002871.OF','002258.OF','005443.OF','519702.OF','008261.OF'], #'540003.OF',
             'A股价值策略':['000628.OF',],
             'A股小盘策略':['001917.OF',], #'007130.OF'
             '新能源':[],
             'TMT':['004616.OF', '001167.OF','000404.OF'],
             '军工':[],
             '其他成长':['003567.OF','213003.OF'],
             '医药':['001717.OF', '000831.OF', '006002.OF', '000727.OF','001230.OF'],
             '白酒':[],
             '其他消费':['481013.OF', '519915.OF', '009476.OF', '000083.OF'],
             '金融':['005576.OF'],
             '周期':[],
             '其他金融周期':[]}

# 从全部基金数据中筛选出核心池基金，并添加到核心池字典中
# 筛选条件：公募FOF基金池级别为核心、境内股市、主动管理、非非A类
core_sys = all_df[(all_df['公募FOF基金池级别']=='核心') & (all_df['资产地区II']=='境内股市') & (all_df['管理方式VI']=='主动管理') & (~all_df['银河证券三级分类'].str.contains('非A类'))]
# 将系统中标记为核心池但未在核心池字典中的基金添加到对应资产细分的核心池列表中
for i in core_sys.index:
    if i not in core_dict[core_sys.loc[i,'资产细分V']]:
        core_dict[core_sys.loc[i,'资产细分V']].append(i)

# 准备一：生成各个基准指数的收益率数据
# 创建DataFrame用于存储各基准指数在不同时间段的收益率
index_fund = pd.DataFrame(index=list(benchmark.values()), columns=['收益_1W','收益_1M','收益_3M','收益_YTD','收益_1Y'])
# 使用Wind API获取各基准指数在不同时间段的收益率数据
index_fund['收益_1W'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
index_fund['收益_1M'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
index_fund['收益_3M'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
index_fund['收益_YTD'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
index_fund['收益_1Y'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
# 添加指数名称列，用于后续展示
index_fund['指数'] = ['中证800','国证价值','中证1000',
                    '中证新能','中证TMT','中证军工','中证成长风格',
                    '中证医药','中证白酒','中证消费',
                    '800金地','中证周期100','中证周期50',
                    '偏股混合型基金指数',
                    '恒生指数', '恒生科技', '标普500', '纳斯达克']

# 筛选出股票型主动管理基金
fund_rank = fund[(fund['资产类别I']=='股票')& (fund['管理方式VI']=='主动管理')]
# 进一步筛选出境内股市的基金
fund_rank_A = fund_rank[(fund_rank['资产地区II']=='境内股市') ]
# 创建三个字典用于存储不同类型的基金池数据
core_pool, key_pool, fund_pool = dict(), dict(), dict()

#%% 生成汇总表格
# 设置PPT图表大小
pptsize(209)
# 创建汇总表格，用于展示各资产细分的收益和排名情况
df_huizong = pd.DataFrame(index=list(benchmark.keys())[:13], columns=['收益(排名)','近一周','近一月','近三月','年初至今','近一年'])
df_huizong['收益(排名)'] = df_huizong.index
# 创建两个辅助DataFrame，分别用于存储排名和收益数据
df_paiming = pd.DataFrame(index=df_huizong.index, columns=df_huizong.columns)
df_shouyi = pd.DataFrame(index=df_huizong.index, columns=df_huizong.columns)

# 遍历每个资产细分，计算其重点池基金在各时间段的中位数收益和排名
for label in df_huizong.index:
    # 筛选出当前资产细分的非禁买基金
    fund_i = fund_rank_A[(fund_rank_A['资产细分V']==label) & (fund_rank_A['公募FOF基金池级别']!='禁买')]
    # 计算各时间段的收益和排名
    for period in ['近一周','近一月','近三月','年初至今','近一年']:
        # 获取所有基金在当前时间段的收益率列表
        pool_performance = fund_i[period].tolist()
        # 计算重点池基金在当前时间段的中位数收益率
        ret = fund_i[fund_i['公募FOF基金池级别']=='重点'][period].median()
        # 计算重点池基金收益率的排名百分比（越小越好）
        ret_rank = len(list(filter(lambda x: x>ret, pool_performance))) / len(pool_performance)
        # 格式化收益和排名数据
        df_huizong.loc[label,period] = "{:.2f}({:.2f})".format(ret, ret_rank)
        # 存储排名和收益数据到辅助DataFrame
        df_paiming.loc[label,period] = round(ret_rank,2)
        df_shouyi.loc[label,period] = round(ret,2)

# 处理缺失值，将NaN值设为None
for label in df_huizong.index:
    for period in ['近一周','近一月','近三月','年初至今','近一年']:
        if str(df_shouyi.loc[label,period]) == 'nan':
            df_shouyi.loc[label,period] = None
            df_paiming.loc[label,period] = None
            df_huizong.loc[label,period] = None

# 生成汇总表格图表
huizong_table(df_huizong, df_shouyi, df_paiming, '权益重点池收益排名汇总('+today_str+')','2-3-0 汇总重点池', fpath_draw,)

# 以下提取顺序依次为：
# 全A核心池、全A内部基础池、赛道内部基础池、全A外部基础池、V级资产细分的基础池、全A外部重点池、V级资产细分的重点池、港股重点池、QD重点池

# 1. 提取全A核心池基金数据
# 为A股全市场策略、A股价值策略和A股小盘策略的核心池基金生成分析数据
core_pool['A股全市场策略'] = ct.fund_chart(core_dict['A股全市场策略'], 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
core_pool['A股价值策略'] = ct.fund_chart(core_dict['A股价值策略'], 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
core_pool['A股小盘策略'] = ct.fund_chart(core_dict['A股小盘策略'], 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)

# 2. 提取全A内部基础池基金数据（工银旗下基金）
# 筛选出工银旗下的A股全市场策略、A股价值策略和A股小盘策略的基础池基金
icbccs_list1 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股全市场策略')&(fund['基金简称'].str.contains('工银'))].index.tolist()
icbccs_list2 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股价值策略')&(fund['基金简称'].str.contains('工银'))].index.tolist()
icbccs_list3 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股小盘策略')&(fund['基金简称'].str.contains('工银'))].index.tolist()
# 为工银旗下的基础池基金生成分析数据
if len(icbccs_list1)>0:
    icbccs_df1 = (ct.fund_chart(icbccs_list1, 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)).iloc[:-3]
else:
    icbccs_df1 = pd.DataFrame()
if len(icbccs_list2)>0:
    icbccs_df2 = (ct.fund_chart(icbccs_list2, 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)).iloc[:-3]
else:
    icbccs_df2 = pd.DataFrame()
if len(icbccs_list3)>0:
    icbccs_df3 = (ct.fund_chart(icbccs_list3, 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)).iloc[:-3]
else:
    icbccs_df3 = pd.DataFrame()
# 合并工银旗下的全A基础池基金数据
fund_pool['全A内部'] = pd.concat([icbccs_df1,icbccs_df2,icbccs_df3], axis=0)

# 3. 提取赛道内部基础池基金数据（工银旗下基金）
gyrx_lst = list()
# 遍历各资产板块和资产细分，筛选出工银旗下的基础池基金
for u in list(level_4to5.keys()):
    for i in level_4to5[u]:
        gyrx_pool = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']==i)&(fund['基金简称'].str.contains('工银'))].index.tolist()
        if len(gyrx_pool)>0:
            gyrx_lst.append(ct.fund_chart(gyrx_pool, i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A).iloc[:-3])
# 合并工银旗下的赛道基础池基金数据
if len(gyrx_lst)>0:
    fund_pool['赛道内部'] = pd.concat(gyrx_lst, axis=0)

# 4. 提取全A外部基础池基金数据（非工银旗下基金）
# 筛选出非工银旗下的A股全市场策略、A股价值策略和A股小盘策略的基础池基金
out_list1 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股全市场策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
out_list2 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股价值策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
out_list3 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股小盘策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
# 为非工银旗下的基础池基金生成分析数据
out_df1 = ct.fund_chart(out_list1, 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
out_df2 = ct.fund_chart(out_list2, 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
out_df3 = ct.fund_chart(out_list3, 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
# 合并非工银旗下的全A基础池基金数据
fund_pool['全A外部'] = pd.concat([out_df1.iloc[:-3],out_df2.iloc[:-3],out_df3.iloc[:-3]], axis=0)

# 5. 提取V级资产细分的基础池基金数据
# 遍历各资产板块和资产细分，筛选出基础池基金
for u in list(level_4to5.keys()):
    for i in level_4to5[u]:
        pool = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']==i)&(fund['资产地区II']!='香港股市')].index.tolist()
        if len(pool)>0:
            # 为每个资产细分的基础池基金生成分析数据
            fund_pool[i] = ct.fund_chart(pool, i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
            # 添加资产板块信息
            fund_pool[i]['资产板块IV'] = u

#%% 6. 提取全A外部重点池基金数据（非工银旗下基金）
# 筛选出非工银旗下的A股全市场策略、A股价值策略和A股小盘策略的重点池基金
key_out_list1 = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']=='A股全市场策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
key_out_df1 = ct.fund_chart(key_out_list1, 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
key_out_list2 = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']=='A股价值策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
key_out_df2 = ct.fund_chart(key_out_list2, 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
key_out_list3 = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']=='A股小盘策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
key_out_df3 = ct.fund_chart(key_out_list3, 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
# 合并非工银旗下的全A重点池基金数据
key_pool['全A外部'] = pd.concat([key_out_df1.iloc[:-3],key_out_df2.iloc[:-3],key_out_df3.iloc[:-3]], axis=0)

# 7. 提取V级资产细分的重点池和核心池基金数据
# 遍历各资产板块和资产细分，筛选出重点池基金和核心池基金
for u in list(level_4to5.keys()):
    for i in level_4to5[u]:
        # 筛选出重点池基金
        pool = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']==i)&(fund['资产地区II']!='香港股市')].index.tolist()
        if len(pool)>0:
            # 为每个资产细分的重点池基金生成分析数据
            key_pool[i] = ct.fund_chart(pool, i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
            # 添加资产板块信息
            key_pool[i]['资产板块IV'] = u
        # 处理核心池基金
        if len(core_dict[i])>0:
            # 为每个资产细分的核心池基金生成分析数据
            core_pool[i] = ct.fund_chart(core_dict[i], i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
            # 添加资产板块信息
            core_pool[i]['资产板块IV'] = u

#%% 8. 提取港股和海外重点池基金数据
# 筛选出QDII和港股基金
# 条件：资产地区为海外市场、非债券类、非指数非ETF、非非A类
qdahk_fund_rank = fund[(fund['资产地区II'].isin(['美国股市','欧洲股市','日本股市','越南股市','印度股市','全球股市','香港股市'])==True) & (fund['银河证券二级分类'].str.contains('债券')==False) & ((fund['银河证券二级分类'].str.contains('指数')==False) & (fund['银河证券二级分类'].str.contains('ETF')==False) & (fund['基金简称'].str.contains('指数')==False) & (fund['基金简称'].str.contains('ETF')==False)) & (fund['银河证券三级分类'].str.contains('非A类')==False)]
# 筛选出香港股市的重点池基金
hkf_list = qdahk_fund_rank[(qdahk_fund_rank['公募FOF基金池级别'] == '重点')&(qdahk_fund_rank['资产地区II'] == '香港股市')].index.tolist()
# 为香港股市的重点池基金生成分析数据
fund_pool['QD港股'] = ct.fund_chart2(hkf_list, '香港股市', '资产地区II',qdahk_fund_rank)
# 筛选出除境内股市和香港股市外的其他海外市场的重点池基金
qd_list = qdahk_fund_rank[(qdahk_fund_rank['公募FOF基金池级别'] == '重点')&(qdahk_fund_rank['资产地区II']!='境内股市')&(qdahk_fund_rank['资产地区II']!='香港股市')].index.tolist()
# 为其他海外市场的重点池基金生成分析数据
fund_pool['QD海外'] = ct.fund_chart2(qd_list, ['全球股市','美国股市','日本股市','越南股市','欧洲股市','印度股市'], '资产地区II', qdahk_fund_rank)

print('9.0 基金池数据提取完毕')  # 输出进度信息
printtime(t)  # 记录执行时间

#%% 十、热门股基未入池原因
# 定义需要分析的资产细分类别列表，包括全市场策略、行业主题策略等
A_level = ['A股全市场策略','A股价值策略','A股小盘策略','新能源','TMT','军工','其他成长','医药','白酒','其他消费','金融','周期','其他金融周期']

# 创建空列表，用于存储各资产细分类别的基金评价指标数据
df_list = list()

# 遍历每个资产细分类别，提取相应的基金评价指标数据
for i in A_level:
    # 读取月度筛选结果明细表，使用基金池调整日期作为文件名前缀
    i_df = pd.read_excel('基础数据/{0}月度筛选结果明细.xlsx'.format(pool_date[:6]),sheet_name='ag-grid',index_col=2)
    # 删除重复的基金代码列并设置索引名称
    i_df = i_df.drop('基金代码',axis=1)
    i_df.index.name = '基金代码'

    # 筛选出当前资产细分类别的基金
    i_df = i_df[i_df['资产细分V']==i]
    # 进一步筛选：排除非A类基金，只保留主动管理的基金
    i_df = i_df[(~i_df['银河证券三级分类'].str.contains('非A类')) & (i_df['管理方式VI']=='主动管理')]

    # 由于A股价值策略的评价指标列名与其他策略不同，需要特殊处理
    if i!='A股价值策略':
        # 添加日期列，用于后续数据合并
        i_df['日期'] = pool_date
        # 选择需要的评价指标列：P+_1Y（正收益能力）、P-_1Y（负收益控制能力）等
        i_df = i_df[['日期','P+_1Y','P-_1Y','PRD_1Y','Calmar比率_1Y','最大回撤_1Y','α波动率_1Y','未入基础池原因']]
    else:
        # A股价值策略使用"汇总P+"而非"P+_1Y"
        i_df['日期'] = pool_date
        i_df = i_df[['日期', '汇总P+', 'P-_1Y', 'PRD_1Y', 'Calmar比率_1Y', '最大回撤_1Y', 'α波动率_1Y','未入基础池原因']]
    # 统一列名，便于后续合并处理
    i_df.columns = ['日期', 'P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率','当月未入基础池原因']
    # 添加标签列，标识资产细分类别
    i_df['标签'] = i
    print('正在看',i)
    # 将当前资产细分类别的数据添加到列表中
    df_list.append(i_df)

# 合并所有资产细分类别的数据
df_evaluate = pd.concat(df_list, axis=0)
# 确保列名一致性
df_evaluate.rename(columns={'未入基础池原因': '当月未入基础池原因'}, inplace=True)
# 复制基金详细信息数据，用于后续分析
basic_df = copy.deepcopy(df4_save)

# 分析四个时间段的热门基金未入池原因
for period in ['近一月', '近三月', '年初至今', '分类热门']:
    # 获取当前时间段的热门基金数据
    x_df = pop_fund[period]
    # 将热门基金数据与评价指标数据合并
    # 使用left_index=True和right_index=True表示按索引（基金代码）合并
    # how='left'表示保留左侧DataFrame的所有行
    reason_df = pd.merge(x_df, df_evaluate[df_evaluate['标签'].isin(A_level)][['标签', 'P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']], left_index=True, right_index=True,
                    how='left')
    # 注释掉的代码是另一种合并方式，不考虑标签筛选
    # reason_df = pd.merge(x_df, df_evaluate[['P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']], left_index=True, right_index=True,
    #                 how='left')

    # 添加基金经理、成立日期和是否基础池等信息
    reason_df = pd.merge(reason_df, basic_df[['基金经理', '基金成立日期', 'my是否基础池']], left_index=True, right_index=True,how='left')
    # 添加基金池级别信息
    reason_df = pd.merge(reason_df, all_df['公募FOF基金池级别'], left_index=True, right_index=True, how='left')
    # 对于已经在基础池的基金，将未入池原因设为"\"（表示不适用）
    reason_df.loc[reason_df['my是否基础池']=='是','当月未入基础池原因'] = '\\'
    # 选择需要的列并重新排序
    reason_df = reason_df[['基金简称', '基金经理_x', '标签', '近一周', '近一月', '近三月', '年初至今', '近一年', '公募FOF基金池级别', 'P+', 'P-',
                        'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']]
    # 重命名列，确保列名一致性
    reason_df.columns = ['基金简称', '基金经理', '分类', '近一周', '近一月', '近三月', '年初至今', '近一年', '基金池级别',
                         'P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']

    # 根据时间段类型进行不同处理
    if period != '分类热门':
        # 对于近一月、近三月、年初至今：取收益率排名前25的基金
        reason_df = reason_df.round(2).sort_values(period, ascending=False).iloc[:25]
        # 生成表格并保存
        reason_table(reason_df, period, '热门基金-' + period, '2-4 热门基金 ' + period, fpath_draw)
        # 将结果保存到pop_fund字典中
        pop_fund['未入池原因0' + period] = reason_df
    else:
        # 对于分类热门：按资产细分类别分组处理
        reason_df = reason_df.round(2)
        # 将结果保存到pop_fund字典中
        pop_fund['未入池原因0' + period] = reason_df
        # 按资产细分类别分组，生成四个子表格
        # 全市场策略类
        df1 = reason_df[reason_df['分类'].str.contains('|'.join(['A股全市场策略', 'A股价值策略', 'A股小盘策略']))]
        # 成长类
        df2 = reason_df[reason_df['分类'].str.contains('|'.join(['新能源', 'TMT', '军工', '其他成长']))]
        # 消费医药类
        df3 = reason_df[reason_df['分类'].str.contains('|'.join(['医药', '白酒', '其他消费', ]))]
        # 金融周期类
        df4 = reason_df[reason_df['分类'].str.contains('|'.join(['金融', '周期', '其他金融周期']))]
        # 生成各类表格并保存
        reason_table(df1, period, '热门基金-全市场-' + period, '2-4 热门局部1 ' + period, fpath_draw, [5, 10])
        reason_table(df2, period, '热门基金-成长-' + period, '2-4 热门局部2 ' + period, fpath_draw, [5, 10, 15])
        reason_table(df3, period, '热门基金-医药&消费-' + period, '2-4 热门局部3 ' + period, fpath_draw, [2, 7])
        reason_table(df4, period, '热门基金-金融周期-' + period, '2-4 热门局部4 ' + period, fpath_draw, [5, 10])

print('10.0 分析绩优基金未入池原因完毕')
printtime(t)
#%% 十一、总结与导出
# 创建汇总字典，包含所有分析结果
# 该字典将所有前面章节生成的数据结构整合在一起，便于统一导出
# 字典的键是带有序号前缀的分类名称，值是对应的数据结构（DataFrame或嵌套字典）
a_summary = dict({'0 各类别收益汇总':df_huizong, '1 收益回顾':df_graph, '2 基金分位':rtn_summary, '2 月度收益':rtn_monthly, #'2 行业基金各池子收益':dict_graph,
                  '3 收益归因':allocation, '4 热门基金':pop_fund,
                   '4 基金公司收益':amc_rank, '5 权益核心池':core_pool, '6 权益基础池':fund_pool, '7 权益重点池':key_pool, '8 主动基金评价指标':df_evaluate, '9 FOF基金':fof,
                  '10 等权重全市场组合净值':portPoint_df})

# 合并所有核心池和基础池的基金数据，创建一个综合的基金池数据框
fund_all=pd.DataFrame()
# 添加核心池基金数据（去除最后3行，通常是统计行）
# 遍历所有核心池类别，将其数据合并到fund_all中
for i in core_pool:
    fund_all=pd.concat([fund_all,core_pool[i][:-3]],axis=0)
# 添加基础池基金数据（去除最后3行，通常是统计行）
# 遍历所有基础池类别，将其数据合并到fund_all中
for i in fund_pool:
    fund_all=pd.concat([fund_all,fund_pool[i][:-3]],axis=0)

# 将所有分析结果导出到Excel文件
# 使用ExcelWriter创建一个Excel工作簿，文件名包含"-zmh"后缀
with pd.ExcelWriter('../周报文件/周报程序数据导出 -zmh.xlsx') as writer:
    # 遍历汇总字典中的所有项目
    for i in a_summary:
        # 如果项目不是字典类型（即为DataFrame），直接导出到对应名称的sheet
        if isinstance(a_summary[i],dict)==False:
            a_summary[i].to_excel(writer,sheet_name=i)
        # 如果项目是字典类型，遍历其中的每个子项目并导出到对应的sheet
        # sheet名称为"父类别 子类别"的格式
        else:
            for j in a_summary[i]:
                a_summary[i][j].to_excel(writer,sheet_name=i+' '+j)
    # 额外导出基金池汇总数据到单独的sheet
    df4_save.to_excel(writer,sheet_name='基金池汇总')

print('11.0 数据导出excel完毕')  # 输出进度信息
printtime(t)  # 记录执行时间并更新时间点t

#%% 【画图】1-1市场总体表现-左图右表
# 复制基金分位数据，用于后续处理
df_percentile = copy.deepcopy(rtn_summary)
# 将基金分类名称拆分为银河分类和分位数两部分
# 例如："标准股票型基金 50"" 拆分为 "标准股票型基金" 和 "50""
temp = np.array(df_percentile.index.str.split(' ', expand = True))
df_percentile['银河分类'] = [s[0] for s in temp]  # 提取银河分类名称
df_percentile['分位'] = [s[1] for s in temp]  # 提取分位数信息

# 获取中证800和债券指数近一周的收益率，用于计算超额收益
index_1w = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]  # 中证800近一周收益率
bond_1w = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]  # 债券指数近一周收益率

# 筛选出50%分位数的基金数据，用于左侧柱状图
df_market = df_percentile[df_percentile['分位'] == '50"']
df_market.set_index(['银河分类'], inplace = True)  # 设置银河分类为索引

# 添加工银配置和工银股混的近期收益率数据
df_market.loc['工银配置', '近一周'] = w.wss("930995.CSI", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_market.loc['工银股混', '近一周'] = w.wss("930994.CSI", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_market.loc['工银配置', '近一月'] = w.wss("930995.CSI", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_market.loc['工银股混', '近一月'] = w.wss("930994.CSI", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]

# 计算各类基金相对于基准指数的超额收益
df_market.loc['标准股票型基金','近一周超额'] = df_market.loc['标准股票型基金','近一周'] - index_1w  # 标准股票型基金相对中证800的超额收益
df_market.loc['偏股型基金','近一周超额'] = df_market.loc['偏股型基金','近一周'] - index_1w  # 偏股型基金相对中证800的超额收益
df_market.loc['长期纯债债券型基金','近一周超额'] = df_market.loc['长期纯债债券型基金','近一周'] - bond_1w  # 债券型基金相对债券指数的超额收益

# 选择需要展示的列并按索引逆序排列（使图表从上到下按基金类型排列）
df_market = df_market[['近一周','近一月','近一周超额']].reindex(df_market.index[::-1])

# 准备右侧表格数据
df_percentile.index = df_percentile['银河分类']  # 设置银河分类为索引
df_percentile = df_percentile[['分位','近一周', '近一月', '近三月', '年初至今', '近一年']]  # 选择需要展示的列

# 创建一个1行2列的图表，左侧为柱状图，右侧为表格
fig, ax = plt.subplots(nrows=1, ncols=2, sharex=False)

#---- 绘制左侧柱状图 ----
# 准备柱状图数据
data = df_market.values.T  # 转置数据，使每行代表一个时间段
fund_type = df_market.index.tolist()  # 基金类型列表
x = [i*1.3 for i in range(len(fund_type))]  # 计算y轴位置，乘以1.3是为了增加柱状图之间的间距
width = 0.3  # 设置柱状图宽度

# 绘制近一周和近一月的水平柱状图
rects1 = ax[0].barh(x, data[0], width, label='近一周', alpha=light)  # 绘制近一周收益率柱状图
rects2 = ax[0].barh([i + width for i in x], data[1], width, label='近一月', alpha=light)  # 绘制近一月收益率柱状图

# 定义函数用于在柱状图上添加数据标签
def autolabel(rects):
    """在每个柱状图右侧添加数据标签"""
    for rect in rects:
        width = rect.get_width()  # 获取柱状图宽度（即收益率值）
        ax[0].annotate('{:.2f}'.format(width),  # 格式化为两位小数
                    xy=(width, rect.get_y() + rect.get_height() / 2),  # 标签位置设为柱状图右侧中部
                    xytext=(5, -7),  # 微调标签位置
                    textcoords="offset points",
                    ha='left', va='center', fontsize=fontsize_text)  # 设置对齐方式和字体大小

# 为两组柱状图添加数据标签
autolabel(rects1)
autolabel(rects2)

# 设置y轴刻度位置和标签
ax[0].set_yticks([i + 2 * width for i in x])  # 设置y轴刻度位置在两个柱状图之间
ax[0].set_yticklabels(['\n'.join(wrap(label, width=6)) for label in fund_type], rotation=0, fontsize=fontsize_legend)  # 设置y轴标签，过长的标签自动换行

# 设置x轴格式，使正负值显示更直观
ax[0].xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}' if x >= 0 else f'-{abs(x):.2f}'))

# 添加图例
ax[0].legend(loc='upper center', bbox_to_anchor=(0.5, 1.05), ncol=5, fontsize=fontsize_legend)

#---- 绘制右侧表格 ----
# 关闭右侧坐标轴显示
ax[1].axis('off')

# 创建表格背景色交替的效果
colortable = copy.deepcopy(df_percentile)
for aa in list(range(len(colortable.index))):
    if aa in list(range(1, len(colortable.index), 2)):  # 奇数行
        colortable.loc[colortable.index[aa]] = 'lightgray'  # 设置为浅灰色
    else:  # 偶数行
        colortable.loc[colortable.index[aa]] = 'white'  # 设置为白色

# 设置列标题背景色
colColours = ['lightsteelblue'] * len(colortable.columns)

# 处理缺失值，将NaN替换为空格
df_percentile.fillna(' ', inplace=True)

# 创建表格
table = ax[1].table(cellText=df_percentile.values,  # 表格内容
                 colLabels=df_percentile.columns,  # 列标签
                 rowLabels=df_percentile.index,  # 行标签
                 bbox=(0, 0, 1, 1),  # 表格位置和大小
                 cellLoc='center',  # 单元格内容居中
                 loc='center',  # 表格位置居中
                 cellColours=colortable.values,  # 单元格背景色
                 colColours=colColours,  # 列标题背景色
                 rowColours=colortable.iloc[:, 0].values  # 行标题背景色
                 )

# 设置表格样式
table.auto_set_font_size(False)  # 禁用自动字体大小调整
# 遍历表格中的每个单元格，设置字体和颜色
for (row, col), cell in table.get_celld().items():
    cell.set_fontsize(fontsize_text)  # 设置字体大小
    # 如果单元格内容为负值（包含'-'），则设置为红色
    if ('-' in str(df_percentile.iloc[row - 1, col])) and (row >= 1 and col >= 1):
        cell.get_text().set_color('red')
    # 对于表头（第0行或第0列），设置为粗体
    if (row == 0) | (col == 0):
        cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_legend))

# 设置图表标题和布局
fig.suptitle('市场总览('+data_date+')', fontsize=fontsize_suptitle, fontweight='bold')  # 添加总标题
fig.tight_layout()  # 自动调整布局，避免元素重叠

# 保存图表到文件并关闭
fig.savefig('图表/1-1-1 市场总览')
plt.close()

print('12.0 补充绘图-市场总览组合表格完毕')  # 输出进度信息
printtime(t)  # 记录执行时间并更新时间点

#【各类基金池画图】
# %% 绘制表格：重点池&核心池-非全市场的各个子类别
ngroup = 42  # 设置每页显示的基金数量上限为42个
# 遍历基准指数字典中的第4到第13个键（对应非全市场的各个子类别）
for i in list(benchmark.keys())[3:13]:
    try:
        # 调用concat_table函数获取当前资产类别的基金池数据和核心池基金数量
        x = concat_table(i, today_str, benchmark, index_fund)
        i_pool, num = x[0], x[1]  # i_pool为基金池数据，num为核心池基金数量
    except:
        continue  # 如果获取数据失败，则跳过当前资产类别

    # 计算近一周收益与年初至今收益的Spearman相关系数（排除最后5行，通常是统计行或基准指数）
    rho = spearmanr(i_pool['收益_1W'][:-5], i_pool['收益_YTD'][:-5]).correlation
    # 截断基金简称，使其不超过一定长度，便于表格展示
    i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]
    nrows = len(i_pool.index)  # 获取基金数量
    if len(i_pool.index)>0:
        pass  # 如果基金池不为空，继续处理

    # 创建颜色映射，用于表格中的条件格式化显示
    # 对收益率和排名列应用颜色映射，收益率越高颜色越深，排名越靠前颜色越深
    df_color = color_map(i_pool,
                         ['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD',
                          '收益_1Y', '排名_1Y'], fund_col)

    # 根据基金数量决定是生成单个表格还是分页表格
    if nrows <= ngroup:  # 如果基金数量不超过每页上限
        # 绘制单个表格，包含所有基金数据
        draw_table(i_pool, df_color, i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')', '2-3 {0}重点池'.format(i),
                   fpath_draw, num, include_benchmark=True, multi_group=False)
    else:  # 如果基金数量超过每页上限，需要分页显示
        # 计算需要分成几页
        num_splits = (len(i_pool) // ngroup) + 1
        # 遍历每一页
        for q in range(num_splits):
            # 计算当前页的起始和结束索引
            start_index = q * ngroup
            end_index = min((q + 1) * ngroup, len(i_pool.index))
            # 获取当前页的数据和颜色映射
            subset_df = i_pool.iloc[start_index: end_index]
            subset_df_color = df_color.iloc[start_index: end_index]

            # 判断是否为最后一页，最后一页需要包含基准指数数据
            if q != num_splits - 1:  # 非最后一页
                draw_table(subset_df, subset_df_color,
                           i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                           '2-3 {0}重点池-子图{1}'.format(i,q+1), fpath_draw, num, include_benchmark=False, multi_group=False)
                num = 0  # 第一次画完核心池后将num归零，后续页面不再显示核心池标记
            else:  # 最后一页
                draw_table(subset_df, subset_df_color,
                           i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                           '2-3 {0}重点池-子图{1}'.format(i,q+1), fpath_draw, num, include_benchmark=True, multi_group=False)


# %% 绘制表格：重点池&核心池-全市场
# 遍历基准指数字典中的前3个键（对应全市场的三个策略：A股全市场策略、A股价值策略、A股小盘策略）
for i in list(benchmark.keys())[:3]:
    # 分别处理核心池和重点池
    for level in ['5 权益核心池 ', '7 权益重点池 ']:
        # 从Excel文件中读取对应的基金池数据
        i_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name=level + i, index_col=0)
        # 调用drop_fund函数处理基金数据，可能是过滤掉不符合条件的基金
        i_pool = drop_fund(i_pool,today_str)

        # 添加基准指数数据行
        i_pool.loc[benchmark[i]] = None  # 添加当前策略的基准指数行
        i_pool.loc['偏股混合型基金指数'] = None  # 添加偏股混合型基金指数行
        # 填充基准指数的收益率数据
        i_pool.loc[benchmark[i], list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark[i], list(index_fund.columns[:-1])].values]
        i_pool.loc['偏股混合型基金指数', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['偏股混合型基金指数'], list(index_fund.columns[:-1])].values]
        # 将基准指数代码替换为可读名称
        i_pool.rename(index={benchmark[i]: index_fund.loc[benchmark[i], '指数']}, inplace=True)
        # 设置最后5行的简称列为行索引值
        i_pool.iloc[-5:, i_pool.columns.get_loc('简称')] = list(i_pool.iloc[-5:].index)
        # 计算近一周收益与年初至今收益的Spearman相关系数
        rho = spearmanr(i_pool['收益_1W'][:-5], i_pool['收益_YTD'][:-5]).correlation
        # 截断基金简称
        i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]

        # 创建颜色映射
        df_color = color_map(i_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
        nrows = len(i_pool.index)  # 获取基金数量

        # 根据基金数量决定是生成单个表格还是分页表格
        if nrows <= ngroup:  # 如果基金数量不超过每页上限
            # 绘制单个表格
            draw_table(i_pool, df_color, level[-4:-1] + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3 {0}{1}'.format(i,level[-4:-1]),
                       fpath_draw, 0, include_benchmark=True, multi_group=False)
        else:  # 如果基金数量超过每页上限，需要分页显示
            # 计算需要分成几页
            num_splits = (len(i_pool) // ngroup) + 1
            # 遍历每一页
            for q in range(num_splits):
                # 计算当前页的起始和结束索引
                start_index = q * ngroup
                end_index = min((q + 1) * ngroup, len(i_pool.index))
                # 获取当前页的数据和颜色映射
                subset_df = i_pool.iloc[start_index: end_index]
                subset_df_color = df_color.iloc[start_index: end_index]

                # 判断是否为最后一页
                if q != num_splits - 1:  # 非最后一页
                    draw_table(subset_df, subset_df_color,
                               level[-4:-1] + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                               '2-3 {0}{1}-子图{2}'.format(i,level[-4:-1],str(q + 1)), fpath_draw, 0, include_benchmark=False,multi_group=False)
                else:  # 最后一页
                    draw_table(subset_df, subset_df_color,
                               level[-4:-1] + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                               '2-3 {0}{1}-子图{2}'.format(i,level[-4:-1],str(q + 1)), fpath_draw, 0, include_benchmark=True,multi_group=False)

# %% 绘制表格：内部基础池
# 处理内部基础池（工银旗下基金），包括全A内部和赛道内部两类
for i in ['全A内部', '赛道内部']:
    # 从Excel文件中读取对应的基金池数据
    i_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name='6 权益基础池 ' + i, index_col=0)
    # 对于赛道内部，过滤掉A股全市场策略的基金
    if i == '赛道内部':
        i_pool = i_pool[~i_pool['标签'].str.contains('A股')]

    # 添加分隔行，用于表格中的视觉分隔
    i_pool.loc['line1'] = None
    i_pool.loc['line2'] = None
    i_pool.loc['line3'] = None
    # 处理基金数据
    i_pool = drop_fund(i_pool,today_str)

    # 添加基准指数数据行
    j = 'A股全市场策略'  # 使用A股全市场策略作为基准
    i_pool.loc[benchmark[j]] = None  # 添加中证800指数行
    i_pool.loc['偏股混合型基金指数'] = None  # 添加偏股混合型基金指数行
    # 填充基准指数的收益率数据
    i_pool.loc[benchmark[j], list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark[j], list(index_fund.columns[:-1])].values]
    i_pool.loc['偏股混合型基金指数', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['偏股混合型基金指数'], list(index_fund.columns[:-1])].values]
    # 将基准指数代码替换为可读名称
    i_pool.rename(index={benchmark[j]: '中证800'}, inplace=True)

    # 设置最后5行的简称列为行索引值
    i_pool.iloc[-5:, i_pool.columns.get_loc('简称')] = list(i_pool.iloc[-5:].index)
    # 计算近一周收益与年初至今收益的Spearman相关系数
    rho = spearmanr(i_pool['收益_1W'][:-5], i_pool['收益_YTD'][:-5]).correlation
    # 截断基金简称
    i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]

    # 创建颜色映射
    df_color = color_map(i_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
    # 根据基金池类型绘制不同的表格
    if i=='赛道内部':
        # 绘制赛道内部基础池表格，multi_group=True表示数据中有分组
        draw_table(i_pool, df_color, '基础池 ' + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-18 内部赛道基础池', fpath_draw, 0, include_benchmark=True, multi_group=True)
    else:
        # 绘制全A内部基础池表格
        draw_table(i_pool, df_color, '基础池 ' + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-17 内部全市场基础池', fpath_draw, 0, include_benchmark=True, multi_group=True)

# %% 绘制表格：香港部分
# 从Excel文件中读取港股基金池数据
hk_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name='6 权益基础池 QD港股',index_col=0)
# 处理基金数据
hk_pool = drop_fund(hk_pool,today_str)

# 添加香港市场基准指数数据行
hk_pool.loc['恒生指数'] = None  # 添加恒生指数行
hk_pool.loc['恒生科技'] = None  # 添加恒生科技指数行
# 填充基准指数的收益率数据
hk_pool.loc['恒生指数', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['恒生指数'], list(index_fund.columns[:-1])].values]
hk_pool.loc['恒生科技', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['恒生科技'], list(index_fund.columns[:-1])].values]

# 设置最后5行的简称列为行索引值
hk_pool.iloc[-5:, hk_pool.columns.get_loc('简称')] = list(hk_pool.iloc[-5:].index)
# 计算近一周收益与年初至今收益的Spearman相关系数
rho = spearmanr(hk_pool['收益_1W'][:-5], hk_pool['收益_YTD'][:-5]).correlation
# 截断基金简称
hk_pool['简称'] = [cut_name(s) for s in list(hk_pool['简称'])]
# 创建颜色映射
df_color = color_map(hk_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
# 绘制港股重点池表格
draw_table(hk_pool, df_color, '重点池 港股(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-19 港股重点池', fpath_draw, 0, include_benchmark=True, multi_group=False)

# %% 绘制表格：海外部分
# 从Excel文件中读取海外基金池数据
qd_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name='6 权益基础池 QD海外',index_col=0)
# 处理基金数据
qd_pool = drop_fund(qd_pool,today_str)

# 添加美国市场基准指数数据行
qd_pool.loc['纳斯达克'] = None  # 添加纳斯达克指数行
qd_pool.loc['标普500'] = None  # 添加标普500指数行
# 填充基准指数的收益率数据
qd_pool.loc['纳斯达克', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['纳斯达克'], list(index_fund.columns[:-1])].values]
qd_pool.loc['标普500', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['标普500'], list(index_fund.columns[:-1])].values]
# 设置最后5行的简称列为行索引值
qd_pool.iloc[-5:, qd_pool.columns.get_loc('简称')] = list(qd_pool.iloc[-5:].index)
# 计算近一周收益与年初至今收益的Spearman相关系数
rho = spearmanr(qd_pool['收益_1W'][:-5], qd_pool['收益_YTD'][:-5]).correlation
# 截断基金简称
qd_pool['简称'] = [cut_name(s) for s in list(qd_pool['简称'])]
# 创建颜色映射
df_color_2 = color_map(qd_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
# 绘制海外重点池表格，multi_group=True表示数据中有分组
draw_table(qd_pool, df_color_2, '重点池 海外 (' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-20 QD重点池', fpath_draw, 0, include_benchmark=True, multi_group=True)

print('13.0 补充绘图-各基金池细分表格绘制完毕')  # 输出进度信息
printtime(t)  # 记录执行时间并更新时间点
