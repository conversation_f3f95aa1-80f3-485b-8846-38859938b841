# -*- coding: utf-8 -*-
"""
Created on Thu Nov  2 14:07:22 2023

@author: gyrx-zhaomh
"""

#%%
import pandas as pd
import numpy as np
import os 
import time
import pickle
import sys
from sqlalchemy import create_engine
os.environ['NLS_LANG']='SIMPLIFIED CHINESE_CHINA.UTF8'
os.chdir('C:/Users/<USER>/Documents/pythonProject/0、基金持仓提取')
sys.path.append('C:/Users/<USER>/Documents/pythonProject/0、基金持仓提取')
#engine=create_engine('oracle+cx_oracle://wind_read:Wind_read_100010@192.168.105.38:1521/wind')
import datetime as dt
today = dt.datetime.today()
from WindPy import w
w.start()
import Fund_StockHolding

#%%
io = pd.io.excel.ExcelFile('所有股基.xlsx')#所有股基是用下面注释掉的代码跑的
fund_df = pd.read_excel(io,sheet_name='最新')
dateQ = pd.read_excel(io,sheet_name='日期')
io.close()
fund_df['基金代码']=[x[0:6] for x in list(fund_df['基金代码'])]#省略.OF
fund_df_list = [fund_df.iloc[0:int(np.ceil(fund_df.shape[0]/10)),:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10)):int(np.ceil(fund_df.shape[0]/10))*2,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*2:int(np.ceil(fund_df.shape[0]/10))*3,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*3:int(np.ceil(fund_df.shape[0]/10))*4,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*4:int(np.ceil(fund_df.shape[0]/10))*5,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*5:int(np.ceil(fund_df.shape[0]/10))*6,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*6:int(np.ceil(fund_df.shape[0]/10))*7,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*7:int(np.ceil(fund_df.shape[0]/10))*8,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*8:int(np.ceil(fund_df.shape[0]/10))*9,:],\
                             fund_df.iloc[int(np.ceil(fund_df.shape[0]/10))*9:,:]]
datet = pd.DataFrame(dateQ['季报日期'].dt.strftime("%Y%m%d").values,columns=['日期'])#更改日期格式

#%%
'''查询持仓数据'''
fundhold = []
for i in range(10):
    print("查询第%d批"%(i+1))
    fundhold.append(Fund_StockHolding.Fund_StockHolding_SW_HK2(fund_df_list[i].loc[:,'基金代码'],datet.loc[:,'日期']))
fundholdall = pd.concat(fundhold,axis=0)
f = open('所有基金重仓数据_%s-%s.pkl'%(datet.iloc[0,0],datet.iloc[-1,0]),'wb')
pickle.dump(fundholdall,f,0)
f.close()


# %%
# 导出基金名单
#%%
# index_list = [i[0:6] for i in w.wset("sectorconstituent","date=" + today.strftime('%Y-%m-%d') + ";sectorid=2001010102000000").Data[1]]
# index_list2 = [i[0:6] for i in w.wset("sectorconstituent","date=" + today.strftime('%Y-%m-%d') + ";sectorid=2001010103000000").Data[1]]
# index_list = index_list + index_list2
# #fundhold_active = fundholdall[~fundholdall['基金代码'].isin(index_list)]

# def fundinitial(code):
#     fund_list = w.wset("sectorconstituent","date=" + today.strftime('%Y-%m-%d') + ";sectorid=" + code)
#     fund = pd.DataFrame({'代码':fund_list.Data[1],'简称':fund_list.Data[2],'是否初始基金':w.wss(fund_list.Data[1], "fund_initial").Data[0]}).set_index('代码')
#     fund = fund[(fund['是否初始基金'] == '是')]
#     return fund
# fundlist = pd.DataFrame()
# fundlist = pd.concat([fundlist,fundinitial('2001010101000000')],axis=0,sort=False) #主动股票
# fundlist = pd.concat([fundlist,fundinitial('2001010102000000')],axis=0,sort=False) #被动指数
# fundlist = pd.concat([fundlist,fundinitial('2001010103000000')],axis=0,sort=False) #增强指数
# fundlist = pd.concat([fundlist,fundinitial('2001010201000000')],axis=0,sort=False) #偏股混合
# fundlist = pd.concat([fundlist,fundinitial('1000011486000000')],axis=0,sort=False) #灵活配置
# fundlist = pd.concat([fundlist,fundinitial('2001010202000000')],axis=0,sort=False) #平衡混合
# fundlist.to_excel('股票基金.xlsx')

# %%
# #注意重仓和持仓pkl的区别，重仓是所有季报的重仓，持仓是半年报和年报的全持仓
# f1 = open('所有基金重仓数据_20040331-20221231.pkl','rb')
# stkFundHoldAll = pickle.load(f1)
# f1.close()
# f2 = open('所有基金重仓数据_20230331-20230331.pkl','rb')
# stkFundHoldAll2 = pickle.load(f2)
# f2.close()
# stkFundHoldAll = pd.concat([stkFundHoldAll,stkFundHoldAll2],axis=0,sort=False)
# f = open('所有基金重仓数据_20040331-20230331.pkl','wb')
# pickle.dump(stkFundHoldAll,f,0)
# f.close()

# f1 = open('所有基金持仓数据_20040630-20220630_lyy.pkl','rb')
# stkFundHoldAll = pickle.load(f1)
# f1.close()
# f2 = open('所有基金持仓数据_20221231-20221231.pkl','rb')
# stkFundHoldAll2 = pickle.load(f2)
# f2.close()
# # stkFundHoldAll = pd.concat([stkFundHoldAll,fundholdall],axis=0,sort=False)
# # stkFundHoldAll = stkFundHoldAll.reset_index(drop=True)
# stkFundHoldAll = pd.concat([stkFundHoldAll,stkFundHoldAll2],axis=0,sort=False)
# f = open('所有基金持仓数据_20040630-20221231_lyy.pkl','wb')
# pickle.dump(stkFundHoldAll,f,0)
# f.close()

# %%
# f3 = open('所有基金持仓数据_20221231-20221231.pkl','rb')
# stkFundHoldAll3 = pickle.load(f3)