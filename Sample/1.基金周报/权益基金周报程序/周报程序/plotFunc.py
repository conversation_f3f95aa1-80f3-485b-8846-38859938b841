# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
# %%
import os
from WindPy import w,datetime
print('wind已连接') if w.isconnected() else w.start()
import chart as ct
import copy
import datetime as dt
from dateutil.relativedelta import relativedelta
import itertools
import imgkit
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.table import Table
from matplotlib.font_manager import FontProperties
from matplotlib.colors import LinearSegmentedColormap
import numpy as np
import pandas as pd
import pickle
import statsmodels.api as sm
from scipy.optimize import nnls
from sklearn import linear_model
from sklearn.metrics import mean_squared_error, r2_score
import seaborn as sns
from scipy.stats import spearmanr
from textwrap import wrap

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True
light=0.5
fontsize_subtitle = 18 #16
fontsize_suptitle = 20 #18
fontsize_text = 13 #11
fontsize_legend = 12 #9
plt.rcParams['font.size'] = 15 #12  # 字体大小
def pptsize(ppt_size):
    if ppt_size==43:
        plt.rcParams['figure.figsize']=(16,9)    #(20,7)
    elif ppt_size==207:
        plt.rcParams['figure.figsize']=(20,7)
    else:
        plt.rcParams['figure.figsize']=(20,9)
pptsize(209)


# %% 1.1市场表现-行业超额分解
def plt_excess_return(df_excess, fpath_draw, fig_name):
    '''df_excess是以行业名为index的dataframe，其余参数分别为绘图存储路径和图片名称'''
    pptsize(209)
    fig, ax1 = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax1.spines['right'].set_visible(False)
    ax1.spines['top'].set_visible(False)
    # 重新定义bottom，看图更清晰
    ret_dec_bottom1 = pd.Series()
    for index in df_excess.index:
        if df_excess.loc[index, '配置超额'] * df_excess.loc[index, '选股超额'] >= 0:
            ret_dec_bottom1.loc[index] = df_excess.loc[index, '配置超额']
        else:
            ret_dec_bottom1.loc[index] = 0

    ax1.bar(df_excess.index.tolist(), df_excess['配置超额'].tolist(), width=0.4, color='blue', label='配置超额')
    ax1.bar(df_excess.index.tolist(), df_excess['选股超额'].tolist(), width=0.4, bottom=ret_dec_bottom1, color='orange',
            label='选股超额')
    ax1.plot(df_excess.index, df_excess['超额收益'].tolist(), linewidth=2.5, color='red', linestyle='-', marker='',
             label='超额收益')

    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.2f}' if y >= 0 else f'-{abs(y):.2f}'))
    ax1.set_ylim(
        bottom=min(df_excess['配置超额'].min(), df_excess['选股超额'].min(), df_excess['超额收益'].min()) - 0.02,
        top=max(df_excess['配置超额'].max(), df_excess['选股超额'].max(), df_excess['超额收益'].max()) + 0.02)
    lines, labels = ax1.get_legend_handles_labels()
    plt.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, 0.9), ncol=3, fontsize=fontsize_legend)

    plt.yticks(fontsize=fontsize_legend)
    plt.xticks(rotation=45, ha="center", fontsize=fontsize_legend)

    plt.title(fig_name, fontsize=fontsize_suptitle)
    plt.savefig(fpath_draw + '/1-1-3 超额收益来源分解.png')
    return

#%% 1.1市场表现-月度收益率
def plt_monthly_return(df, fig_name, fpath_draw):
    fig,ax=plt.subplots(nrows=1,ncols=1,sharex=False)
    ax.axis('off')
    colortable = copy.deepcopy(df)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    colColours = ['lightsteelblue'] * len(colortable.columns)
    table = ax.table(cellText=df.values,
                     colLabels=df.columns,
                     rowLabels=df.index,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours,
                     rowColours=colortable.iloc[:, 0].values
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df.columns))))
    table.auto_set_column_width(col=[0])
    cmap = plt.cm.get_cmap('RdYlGn')

    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        if ('-' in str(df.iloc[row - 1, col])) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
        if (row == 0) | (col == -1):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        else:
            value = 1-(0.25+(df.iloc[row-1,col]-df.iloc[row-1,:].min(axis=0))/(df.iloc[row-1,:].max(axis=0)-df.iloc[row-1,:].min(axis=0))/2)
            color_val = cmap(value)
            cell.set_facecolor(color_val)

    fig.suptitle(fig_name, fontsize=fontsize_subtitle, fontweight='bold')#, pad=5)
    fig.tight_layout()
    plt.savefig(fpath_draw+'/1-1-4 月初至今收益率.png', bbox_inches='tight')
    return

# %% 1.1行业市场表现
def scatter_industry(df_ind_weekly, df_market, periods, fpath_draw, data_date):
    fig, ax = plt.subplots(nrows=1, ncols=2, sharex=False)
    for i in range(2):
        ret_800 = df_market.loc['中证800', periods[i]]
        ret_stock = df_market.loc['偏股型基金 50"', periods[i]]

        ax[i].scatter(df_ind_weekly['配置差额'], df_ind_weekly[periods[i]], c='blue')
        ax[i].scatter(0, ret_800, c='orange', s=80)
        ax[i].scatter(0, ret_stock, c='green', s=80)

        slope, intercept = np.polyfit(df_ind_weekly['配置差额'], df_ind_weekly[periods[i]], deg=1)
        x = np.array([df_ind_weekly['配置差额'].min(), df_ind_weekly['配置差额'].max()])
        y = slope * x + intercept
        ax[i].plot(x, y, '--', c='grey')

        ax[i].spines['bottom'].set_position('zero')
        ax[i].spines['left'].set_position('zero')
        ax[i].spines['top'].set_visible(False)
        ax[i].spines['right'].set_visible(False)
        # 根据数据范围动态调整顶部和右侧边框位置
        if max(df_ind_weekly['配置差额']) > 0:
            ax[i].spines['top'].set_position(('data', max(df_ind_weekly[periods[i]])))
        else:
            ax[i].spines['top'].set_position(('data', min(df_ind_weekly[periods[i]])))

        if max(df_ind_weekly[periods[i]]) > 0:
            ax[i].spines['right'].set_position(('data', max(df_ind_weekly['配置差额'])))
        else:
            ax[i].spines['right'].set_position(('data', min(df_ind_weekly['配置差额'])))

        ax[i].xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.1f}' if x >= 0 else f'-{abs(x):.1f}'))
        ax[i].yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1f}' if y >= 0 else f'-{abs(y):.1f}'))
        ax[i].set_xlabel('超配比例', color='red', weight='bold')
        ax[i].xaxis.set_label_coords(0.5, -0.1)
        ax[i].set_ylabel('涨跌幅', color='red', weight='bold')
        ax[i].yaxis.set_label_coords(0.1, 0.5)

        for j in range(len(df_ind_weekly.index)):
            label = f"{df_ind_weekly.index[j]},{df_ind_weekly.iloc[j][periods[i]]}"
            ax[i].annotate(label, (df_ind_weekly.iloc[j]['配置差额'], df_ind_weekly.iloc[j][periods[i]]),
                           textcoords='offset points', xytext=(0, -10), ha='center')

    fig.suptitle('行业涨跌(左:' + periods[0] + '，右:' + periods[1] + ')(' + data_date + ')', fontsize=fontsize_suptitle,
                 fontweight='bold')
    fig.tight_layout()
    fig.savefig(fpath_draw + '/1-1-2 行业涨跌幅.png')
    plt.close()
    return


# %% 1.3各公司权益基金与全市场基金-散点图
def scatter_manager(df_manager_all, picture_name, fpath_draw, data_date, name_lst=['中位数收益', '规模加权收益']):
    df_manager = df_manager_all.iloc[:25]
    fig, ax = plt.subplots(nrows=1, ncols=2, sharex=False)
    for i in range(2):
        if ('权益' in picture_name) or (i == 1):
            namex = name_lst[i]
            namey = '去年' + namex
        elif ('权益' not in picture_name) and (i == 0):
            namex = 'ytd_中位数收益'
            namey = '去年中位数收益'
        else:
            pass
        df_my = df_manager[df_manager.index == '工银瑞信']
        df_others = df_manager[df_manager.index != '工银瑞信']
        ax[i].scatter(df_my[namex], df_my[namey], c='red')
        ax[i].scatter(df_others[namex], df_others[namey], c='blue')

        slope, intercept = np.polyfit(df_manager[namex], df_manager[namey], deg=1)
        x = np.array([df_manager[namex].min(), df_manager[namex].max()])
        y = slope * x + intercept
        ax[i].plot(x, y, '--', c='grey')

        ax[i].axhline(y=0, color='red')
        ax[i].axvline(x=0, color='red')

        ax[i].xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.1f}' if x >= 0 else f'-{abs(x):.1f}'))
        ax[i].yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1f}' if y >= 0 else f'-{abs(y):.1f}'))

        ax[i].set_xlabel('今年', color='red', weight='bold')
        ax[i].set_ylabel('去年', color='red', weight='bold')
        for q in range(len(df_manager.index)):
            label = f"{df_manager.index[q]},{df_manager.iloc[q][namex]}"
            if df_manager.index[q] == '工银瑞信':
                ax[i].annotate(label, (df_manager.iloc[q][namex], df_manager.iloc[q][namey]),
                               textcoords='offset points', xytext=(0, -10), ha='center', color='red', fontweight='bold',
                               fontsize=fontsize_text)
            else:
                ax[i].annotate(label, (df_manager.iloc[q][namex], df_manager.iloc[q][namey]),
                               textcoords='offset points', xytext=(0, -10), ha='center', fontsize=fontsize_text)

    fig.suptitle('基金公司收益(左:中位数收益，右:规模加权收益)(ytd, ' + data_date + ')', fontsize=fontsize_suptitle,
                 fontweight='bold')
    fig.tight_layout()
    fig.savefig(fpath_draw + '/' + picture_name)
    plt.close()
    return


# %% 1.3各公司分资产类别的表现
def table_amc(amc_rank, group0, name, fpath_draw, data_date):
    # 合并各列别列表
    df_group = list()
    for i_level in group0:
        df_i = amc_rank[i_level].reset_index()
        new_df = df_i[['基金管理人', '产品规模', 'ytd_中位数收益', '近一周_中位数收益']]
        new_df.columns = ['管理人', '规模', 'ytd', '近一周']
        if len(new_df.index) > 25:
            new_df = new_df.iloc[:25]
        else:
            pass
        df_group.append(new_df)
    df_group = pd.concat(df_group, axis=1)
    df_group.columns = pd.MultiIndex.from_tuples(list(itertools.product(group0, ['管理人', '规模', 'ytd', '近一周'])))
    # 生成中位数、调整缺失值和列名
    medians = dict()
    for col in df_group.columns:
        try:
            medians[col] = df_group[col].median().round(2)
        except:
            medians[col] = None
    df_group.loc['中位数'] = '中位数'
    df_group.loc['中位数', medians.keys()] = medians.values()
    df_group.index = [(i + 1) for i in range(25)] + ['']

    color_table = df_group.copy()
    for m in range(len(color_table.index)):
        if m in list(range(1, len(color_table.index), 2)):
            color_table.loc[color_table.index[m]] = 'lightgray'
        else:
            color_table.loc[color_table.index[m]] = 'white'

    cell_table = df_group.copy()
    for i in range(len(cell_table.index)):
        for j in range(len(cell_table.columns)):
            if j % 4 == 0 or df_group.iloc[i, j] == None:
                cell_table.iloc[i, j] = None
            else:
                cell_table.iloc[i, j] = 1 - (0.25 + (df_group.iloc[i, j] - df_group.iloc[:, j].min(axis=0)) / (
                            df_group.iloc[:, j].max(axis=0) - df_group.iloc[:, j].min(axis=0)) / 2)

    colColours = list()
    for i in range(len(group0)):
        colColours.extend(['lightpink', 'lightsteelblue', 'lightsteelblue', 'lightsteelblue'])

    if group0[0] == 'A股全市场基金':
        colNames = list()
        for i in ['全市场', '金融周期', '成长', '医药', '消费']:
            colNames.extend([i, '规模', 'ytd', '近一周'])
    else:
        colNames = list()
        for i in group0:
            colNames.extend([i, '规模', 'ytd', '近一周'])

    df_group = df_group.fillna('')
    fig, ax = plt.subplots(ncols=1, nrows=1, sharex=False)
    cmap = plt.cm.get_cmap('RdYlGn')

    table0 = ax.table(cellText=df_group.values, colLabels=colNames, rowLabels=df_group.index,
                      bbox=(0, 0, 1, 1), cellLoc='center', rowLoc='center', colLoc='center',
                      cellColours=color_table.values,
                      rowColours=list(color_table.iloc[:, 0]),
                      colColours=colColours)  # [('lightsteelblue','lightsteelblue') for i in range(24) ]) #colColours)
    table0.auto_set_font_size(False)

    for (row, col), cell in table0.get_celld().items():
        cell.set_fontsize(fontsize_legend)
        if ('-' in str(df_group.iloc[row - 1, col])) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
        if '工银瑞信' in str(df_group.iloc[row - 1, col]):
            cell.get_text().set_color('red')
        if (row == 0) | (row == 26):  # row==0对应中位数那一行
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_legend))

        if cell_table.iloc[row - 1, col] != None and row > 0 and col > -1:
            color_val = cmap(cell_table.iloc[row - 1, col])
            cell.set_facecolor(color_val)
        else:
            pass

    for i in range(df_group.shape[1]):
        table0[0, i].get_text().set_weight('bold')

    for n in np.arange(4, len(df_group.columns), 4):
        ax.axvline(x=(n / len(df_group.columns)), lw=2, ls='-', c='blue')
    for k in np.arange(0, len(df_group.columns)):
        if k % 4 == 1 or k % 4 == 2 or k % 4 == 3:
            ax.axvline(x=(k / len(df_group.columns)), lw=1, ls='-', c='red')

    ax.axis('off')
    fig.suptitle('各公司基金细分(' + data_date + ')', fontsize=fontsize_suptitle, fontweight='bold')
    fig.tight_layout()
    fig.savefig(fpath_draw + '/' + name)
    return

# %% 2.1基金池表现回顾
def plt_industry_pool(period, df_industry, picture_name, fpath_draw, data_date):
    df = df_industry[['可买池', '基础池', '重点池']] * 100
    industries = df.index.tolist()
    data = (df.values.T).astype(float).round(1)

    s1 = '重点>基础>可买:'
    s2 = '基础>重点>可买:'
    s3 = '重点>可买>基础:'
    s4 = '基础>可买>重点:'
    s5 = '可买>重点>基础:'
    s6 = '可买>基础>重点:'
    for i in industries:
        df_temp = df.loc[i, :]
        if df_temp[0] > df_temp[1]:
            if df_temp[1] > df_temp[2]:
                s6 = s6 + ' ' + i  # 可买>基础>重点
            else:
                if df_temp[0] > df_temp[2]:
                    s5 = s5 + ' ' + i  # 可买>重点>基础
                else:
                    s3 = s3 + ' ' + i  # 重点>可买>基础
        else:
            if df_temp[2] > df_temp[1]:
                s1 = s1 + ' ' + i  # 重点>基础>可买
            else:
                if df_temp[0] > df_temp[2]:
                    s4 = s4 + ' ' + i  # 基础>可买>重点
                else:
                    s2 = s2 + ' ' + i  # 基础>重点>可买
    x = [i * 1.4 for i in range(len(industries))]
    width = 0.3
    plt.rcParams['figure.figsize'] = (20, 4.5)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)

    rects1 = ax.bar(x, data[0], width, label='可买池', alpha=light, color='red')
    rects2 = ax.bar([i + width for i in x], data[1], width, label='基础池', alpha=light, color='orange')
    rects3 = ax.bar([i + 2 * width for i in x], data[2], width, label='重点池', alpha=light, color='blue')
    def autolabel(rects):
        """在每个矩形上方添加数据标签"""
        for rect in rects:
            height = rect.get_height()
            if height >= 0:
                text_color = 'black'
                y_offset = 0.1
            else:
                text_color = 'red'
                y_offset = -1
            ax.annotate('{:.1f}'.format(height),
                        xy=(rect.get_x() + rect.get_width() / 2, height + y_offset),
                        xytext=(0, 3),  # 调整标签位置
                        textcoords="offset points",
                        ha='center', fontsize=fontsize_text - 2, color=text_color)

    autolabel(rects1)
    autolabel(rects2)
    autolabel(rects3)
    ax.text(0.3, 0.75, s1, ha='center', va='bottom', color='grey', fontweight='bold', transform=plt.gcf().transFigure,
            fontsize=fontsize_legend)
    ax.text(0.3, 0.7, s2, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_legend)
    ax.text(0.55, 0.75, s3, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_legend)
    ax.text(0.55, 0.7, s4, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_legend)
    ax.text(0.8, 0.75, s5, ha='center', va='bottom', color='grey', transform=plt.gcf().transFigure,
            fontsize=fontsize_legend)
    ax.text(0.8, 0.7, s6, ha='center', va='bottom', color='grey', fontweight='bold', transform=plt.gcf().transFigure,
            fontsize=fontsize_legend)
    ax.set_xticks([i + width for i in x])
    ax.set_xticklabels(industries, rotation=0, fontsize=fontsize_legend)
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1f}' if y >= 0 else f'-{abs(y):.1f}'))
    ax.legend(loc='best', fontsize=fontsize_legend)
    ax.set_xlabel('基金类型')
    ax.set_ylabel('收益率(%)')

    fig.suptitle('各行业基金池收益(' + period + '_' + data_date + ')', fontsize=fontsize_suptitle, fontweight='bold')
    fig.tight_layout()
    fig.savefig(fpath_draw + '/' + picture_name)
    plt.close()
    return


# %% 2.2等权重全市场股基组合
def plt_portfolio_median(df, pool, fpath_draw, data_date, name, last_year='2023-12-29'):
    pptsize(209)
    df_portfolio = pd.concat([df[pool], df[pool + '_每期新进'], df[pool + '_汇总出池'], df['可买池']], axis=1)
    df_portfolio.columns = [pool, '每期新进', '汇总出池', '可买池']

    # fund_list = list(set_all)
    # api_i = w.wsd(fund_list, 'NAV_adj', old_date, new_date)
    # fundNav_i = api_i.Data
    # date_i = api_i.Times
    # fundNav_np_i = np.array(fundNav_i)
    # df_new = pd.DataFrame(np.transpose(fundNav_np_i), index=list(date_i), columns=fund_list)

    index = '930950.CSI'
    api_i = w.wsd(index, "close", df_portfolio.index[0], df_portfolio.index[-1], "")
    df_index = pd.DataFrame(np.array(api_i.Data[0]), index=np.array(api_i.Times), columns=['偏股基金'])
    df_index.index = [datetime.strftime(x, "%Y-%m-%d") for x in df_index.index]
    df_portfolio['偏股基金'] = df_index.loc[df_portfolio.index, '偏股基金']
    df_portfolio['偏股基金'] = (df_portfolio['偏股基金'] / df_portfolio['偏股基金'].iloc[0]) * 100
    # fundNav_i = api_i.Data
    # date_i = api_i.Times
    # ts = np.array(w.wsd(index, "close", df_portfolio.index[0], df_portfolio.index[-1], "").Data[0])
    # ts = ts / ts[0] * 100
    # df_portfolio['偏股基金'] = ts
    df_portfolio['相对收益'] = df_portfolio[pool] / df_portfolio['可买池']

    text_mark = df_portfolio.iloc[-1][[pool, '可买池', '偏股基金']] / df_portfolio.loc[last_year][[pool, '可买池', '偏股基金']]
    text_mark = ['基金池：', str(round((text_mark[pool] - 1) * 100, 2)),
                 '，可买池：',str(round((text_mark['可买池'] - 1) * 100, 2)),
                 '，偏股基金：', str(round((text_mark['偏股基金'] - 1) * 100,2))]
    text_mark = 'YTD口径： ' + (' '.join(text_mark))

    color_list = ['red', 'purple', 'green', 'orange', 'grey', 'blue']
    labelname_list = [pool, '每期新进', '汇总出池', '可买池', '偏股基金', '{0}/可买池'.format(pool)]

    fig, ax1 = plt.subplots()
    for i in range(4):
        ax1.plot(np.array(df_portfolio.index), df_portfolio[labelname_list[i]].tolist(), label=labelname_list[i],
                 color=color_list[i])
    ax1.plot(np.array(df_portfolio.index), df_portfolio[labelname_list[-2]].tolist(), label=labelname_list[-2],
             color=color_list[-2], alpha=0.8, linestyle='--')

    ax1.grid(alpha=0.3)  # 添加网格线，并设置透明度为0.5
    ax1.spines['left'].set_position(('axes', 0))
    ax1.spines['left'].set_color('red')
    ax1.spines['right'].set_visible(False)
    ax1.tick_params(axis='y', direction='out', color=color_list[0], labelcolor=color_list[0])
    ax1.set_ylabel('净值表现', color=color_list[0])
    # ax1.set_ylim(
    #     bottom=min(df_portfolio[pool].min(), df_portfolio['偏股基金'].min()) - 3,
    #     top=max(df_portfolio[pool].max(), df_portfolio['偏股基金'].max()) + 3)
    ax2 = plt.twinx()
    ax2.plot(np.array(df_portfolio.index), df_portfolio['相对收益'].tolist(), label=labelname_list[-1],
             color=color_list[-1])
    ax2.fill_between(df_portfolio.index, 1, df_portfolio['相对收益'].tolist(), facecolor=color_list[-1],
                     alpha=0.3)  # light
    ax2.spines['left'].set_visible(False)
    ax2.spines['right'].set_position(('axes', 1))
    ax2.spines['right'].set_color(color_list[-1])
    ax2.tick_params(axis='y', direction='out', color=color_list[-1], labelcolor=color_list[-1], left=False, right=True)
    ax2.set_ylabel(labelname_list[-1], color=color_list[-1])

    df_portfolio['timestamp'] = pd.to_datetime(df_portfolio.index)
    df_portfolio['datestr'] = df_portfolio.index
    df_portfolio.set_index('timestamp', inplace=True)
    last_day_of_month = df_portfolio.groupby([pd.Grouper(freq='Y'), pd.Grouper(freq='M')]).max()
    plt.xticks(np.array(last_day_of_month['datestr']), rotation=45, fontsize=fontsize_legend)
    ax1.set_xlim(last_day_of_month['datestr'][0], last_day_of_month['datestr'][-1])

    lines = ax1.lines + ax2.lines
    labels = [line.get_label() for line in lines]
    plt.legend(lines, labels, loc="upper left", bbox_to_anchor=(0.01, 0.98), ncol=6, fontsize=fontsize_legend)
    for ax_i in [ax1, ax2]:
        ax_i.spines['top'].set_visible(False)

    ax2.text(0.98, 0.05, text_mark, transform=ax2.transAxes, horizontalalignment='right', verticalalignment='bottom',
             fontsize=fontsize_text)
    plt.gcf().autofmt_xdate()
    plt.title(name + '(' + data_date + ')', fontsize=fontsize_suptitle)

    plt.savefig(fpath_draw + '/' + name + '.png')
    print('近两周相对收益')
    print(df_portfolio['相对收益'].iloc[-10:])
    return



# %% 1.2板块轮动分析——暴露跟踪
def rolling_analysis(df, windows):
    df = df.dropna()
    output = pd.DataFrame(index=df.index[(windows - 1): len(df.index)])
    for i in range(windows, len(df.index) + 1):
        temp = df.iloc[(i - windows): i]
        x = sm.add_constant(temp.iloc[:, 1:])
        y = temp.iloc[:, 0]
        model = sm.OLS(y, x).fit()
        for i in range(1, len(df.columns)):
            output.loc[temp.index[-1], df.columns[i]] = model.params[i]
        output.loc[temp.index[-1], 'alpha'] = model.params[0] * 250
        output.loc[temp.index[-1], 'IR'] = model.params[0] / np.sqrt(model.mse_resid) * np.sqrt(250)
        output.loc[temp.index[-1], 'rsquared'] = model.rsquared * 100
    return output

def rolling_ridge(df, windows):
    df = df.dropna()
    output = pd.DataFrame(index=df.index[(windows - 1): len(df.index)])
    for i in range(windows, len(df.index) + 1):
        temp = df.iloc[(i - windows): i]
        x = temp.iloc[:, 1:]
        y = temp.iloc[:, 0]
        model = linear_model.Ridge(alpha=5)
        model.fit(x, y)
        for i in range(1, len(df.columns)):
            output.loc[temp.index[-1], df.columns[i]] = model.coef_[i - 1]
        output.loc[temp.index[-1], 'alpha'] = model.intercept_ * 250
        output.loc[temp.index[-1], 'IR'] = model.intercept_ / np.sqrt(
            mean_squared_error(y, model.predict(x))) * np.sqrt(250)
        output.loc[temp.index[-1], 'rsquared'] = r2_score(y, model.predict(x)) * 100
    return output

def rolling_nnls(df, windows):
    df = df.dropna()
    output = pd.DataFrame(index=df.index[(windows - 1):len(df.index)])
    for i in range(windows, len(df.index) + 1):
        temp = df.iloc[(i - windows): i]
        #x = sm.add_constant(temp.iloc[:, 1:])
        x = temp.iloc[:, 1:].values
        y = temp.iloc[:, 0].values
        model = nnls(x, y)
        model_coef, _ = model
        for j in range(1, len(df.columns)):
            output.loc[temp.index[-1], df.columns[j]] = model_coef[j - 1]
        output.loc[temp.index[-1], 'alpha'] = model_coef[0] * 250
        pred_y = np.dot(x, model_coef)
        output.loc[temp.index[-1], 'IR'] = model_coef[0] / np.sqrt(mean_squared_error(y, pred_y)) * np.sqrt(250)
        output.loc[temp.index[-1], 'rsquared'] = r2_score(y, pred_y) * 100
    return output

def do_rolling_analysis(fund, benchmark, startdate, windows, today, ridge=False):
    nav = w.wsd(fund, "close", startdate, today)
    nav = pd.DataFrame(np.array(nav.Data).T, index=nav.Times, columns=nav.Codes)
    nav = (nav / nav.shift(1) - 1) * 100
    nav = nav.iloc[1:, :]
    benchmark = w.wsd(benchmark, "pct_chg", startdate, today)
    benchmark = pd.DataFrame(np.array(benchmark.Data).T, index=benchmark.Times, columns=benchmark.Codes)
    for i in benchmark.columns:
        nav[i] = benchmark[i]
    if ridge == True:
        analysis = rolling_nnls(nav, windows)  #
    else:
        analysis = rolling_analysis(nav, windows)
    return analysis

def plate_exposure_line(today, data_date, fpath_draw, startdate='2021-01-01',
                        fund='930950.CSI', benchmark='000932.SH,000933.SH,000934.SH,CI005920.WI,CI005918.WI',
                        windows=20):
    analysis = do_rolling_analysis(fund, benchmark, startdate, windows, today, ridge=True)
    analysis.index = pd.to_datetime(analysis.index)

    sector = pd.read_excel('基础数据/板块配置.xlsx', index_col=0)
    sector.index = [pd.to_datetime(str(x), format='%Y%m%d') for x in sector.index]
    sector = sector.loc[pd.to_datetime(startdate):]
    #sector.index = sector.index.strftime('%Y%m%d')
    #sector.index = [dt.datetime.strptime(str(x), '%Y%m%d') for x in sector.index]
    b = w.wsd(benchmark, "close", analysis.index[0], analysis.index[-1])
    b = pd.DataFrame(np.array(b.Data).T, index=b.Times, columns=['消费', '医药', '金融', '成长', '周期'])
    b = b / b.iloc[0]

    graph2 = analysis.copy().iloc[:, :-2]
    graph2.columns = ['消费', '医药', '金融', '成长', '周期', 'alpha']
    temp0 = pd.DataFrame({'date': graph2.index, 'year': graph2.index.year, 'month': graph2.index.month})
    ts0 = [i for i in temp0.groupby([temp0['year'], temp0['month']])['date'].max() if (i.month == 6) | (i.month == 12)]
    ts0 = [i for i in ts0 if i <= sector.index[-1]]

    fig, ax = plt.subplots(nrows=1, ncols=2, sharex=False)
    graph2['消费'] = graph2['消费'] + 0.05
    graphma1 = graph2.rolling(20).mean()
    graphma2 = graph2.rolling(120).mean()

    corr = np.corrcoef(graphma1[graphma1.index.isin(ts0)]['消费'], sector['消费'])[0, 1]
    ax[0].plot(graphma1.index.tolist(), graphma1['消费'].values, color='green')
    ax[0].plot(graphma2.index.tolist(), graphma2['消费'].values, color='darkgreen', alpha=light, linestyle='--')
    ax[0].scatter(sector.index.tolist(), sector['消费'].values, marker='^', color='darkgreen')
    ax[0].annotate(str(round(graph2['消费'].iloc[-1], 3)), xy=(graph2.index[-1], graphma1['消费'].iloc[-1]),
                   xytext=(graph2.index[-1], graphma1['消费'].iloc[-1] + 0.01), fontsize=fontsize_legend)
    ax[0].legend(labels=['消费暴露20ma', '消费暴露120ma', '消费持仓'])
    ax[0].set_ylabel('暴露持仓跟踪', color='black', weight='bold')
    ax12 = ax[0].twinx()
    ax12.plot(b.index.tolist(), b['消费'].values, color='grey', alpha=light)
    ax12.set_ylabel('基准走势', color='black', weight='bold')
    plt.title('消费板块暴露与持仓_相关度' + str(corr)[0:4] + '(' + data_date + ')', size=fontsize_suptitle)

    graphma1 = graph2.rolling(20).mean()
    graphma2 = graph2.rolling(120).mean()
    corr = np.corrcoef(graphma1[graphma1.index.isin(ts0)]['医药'], sector['医药'])[0, 1]
    ax[1].plot(graphma1.index.tolist(), graphma1['医药'].values, color='blue')
    ax[1].plot(graphma2.index.tolist(), graphma2['医药'].values, color='darkblue', alpha=light, linestyle='--')
    ax[1].scatter(sector.index.tolist(), sector['医药'].values, marker='^', color='darkblue')
    ax[1].annotate(str(round(graph2['医药'].iloc[-1], 3)), xy=(graph2.index[-1], graphma1['医药'].iloc[-1]),
                   xytext=(graph2.index[-1], graphma1['医药'].iloc[-1] + 0.01), fontsize=fontsize_legend)
    ax[1].legend(labels=['医药暴露20ma', '医药暴露120ma', '医药持仓'])
    ax[1].set_ylabel('暴露持仓跟踪', color='black', weight='bold')
    ax12 = ax[1].twinx()
    ax12.plot(b.index.tolist(), b['医药'].values, color='grey', alpha=light)
    ax12.set_ylabel('基准走势', color='black', weight='bold')
    plt.title('医药板块暴露与持仓_相关度' + str(corr)[0:4] + '(' + data_date + ')', size=fontsize_suptitle)
    plt.savefig(fpath_draw + '/1-2-1 消费和医药.jpg')

    # 成长与金融
    fig, ax = plt.subplots(nrows=1, ncols=2, sharex=False)
    graph2['成长'] = graph2['成长'] + 0.05
    graphma1 = graph2.rolling(20).mean()
    graphma2 = graph2.rolling(120).mean()
    corr = np.corrcoef(graphma1[graphma1.index.isin(ts0)]['成长'], sector['成长'])[0, 1]
    ax[0].plot(graphma1.index.tolist(), graphma1['成长'].values, color='orange')
    ax[0].plot(graphma2.index.tolist(), graphma2['成长'].values, color='#f47920', alpha=light, linestyle='--')
    ax[0].scatter(sector.index.tolist(), sector['成长'].values, marker='^', color='#f47920')
    ax[0].annotate(str(round(graph2['成长'].iloc[-1], 3)), xy=(graph2.index[-1], graphma1['成长'].iloc[-1]),
                   xytext=(graph2.index[-1], graphma1['成长'].iloc[-1] + 0.01), fontsize=fontsize_legend)
    ax[0].legend(labels=['成长暴露20ma', '成长暴露120ma', '成长持仓'])
    ax[0].set_ylabel('暴露持仓跟踪', color='black', weight='bold')
    ax12 = ax[0].twinx()
    ax12.plot(b.index.tolist(), b['成长'].values, color='grey', alpha=light)
    ax12.set_ylabel('基准走势', color='black', weight='bold')
    plt.title('成长板块暴露与持仓_相关度' + str(corr)[0:4] + '(' + data_date + ')', size=fontsize_suptitle)

    graphma1 = graph2.rolling(20).mean()
    graphma2 = graph2.rolling(120).mean()
    corr = np.corrcoef(graphma1[graphma1.index.isin(ts0)]['金融'], sector['金融'])[0, 1]
    ax[1].plot(graphma1.index.tolist(), graphma1['金融'].values, color='red')
    ax[1].plot(graphma2.index.tolist(), graphma2['金融'].values, color='darkred', alpha=light, linestyle='--')
    ax[1].scatter(sector.index.tolist(), sector['金融'].values, marker='^', color='darkred')
    ax[1].annotate(str(round(graph2['金融'].iloc[-1], 3)), xy=(graph2.index[-1], graphma1['金融'].iloc[-1]),
                   xytext=(graph2.index[-1], graphma1['金融'].iloc[-1] + 0.01), fontsize=fontsize_legend)
    ax[1].legend(labels=['金融暴露20ma', '金融暴露120ma', '金融持仓'])
    ax[1].set_ylabel('暴露持仓跟踪', color='black', weight='bold')
    ax12 = ax[1].twinx()
    ax12.plot(b.index.tolist(), b['金融'].values, color='grey', alpha=light)
    ax12.set_ylabel('基准走势', color='black', weight='bold')
    plt.title('金融板块暴露与持仓_相关度' + str(corr)[0:4] + '(' + data_date + ')', size=fontsize_suptitle)
    plt.savefig(fpath_draw + '/1-2-2 成长和金融.jpg')
    return

# #每半年度更新，注意是读入之前的，加上新的，最后几行check再运行
# %%
# import pickle
# f1 = open('C:/Users/<USER>/Documents/pythonProject/基金持仓提取/所有基金持仓数据_20231231-20231231.pkl','rb')
# fundholdall = pickle.load(f1)
# f1.close()
# today = dt.datetime.today().strftime('%Y-%m-%d')
# index_list = [i[0:6] for i in w.wset("sectorconstituent","date=" + today + ";sectorid=2001010102000000").Data[1]]
# index_list2 = [i[0:6] for i in w.wset("sectorconstituent","date=" + today + ";sectorid=2001010103000000").Data[1]]
# index_list = index_list + index_list2
# fundhold_active = fundholdall[~fundholdall['基金代码'].isin(index_list)]
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CI005917.WI").Data[1]), '板块'] = '金融'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CI005918.WI").Data[1]), '板块'] = '周期'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CI005919.WI").Data[1]), '板块'] = '消费'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CI005920.WI").Data[1]), '板块'] = '成长'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CI005921.WI").Data[1]), '板块'] = '稳定'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=000933.SH").Data[1]), '板块'] = '医药'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CI005018.WI").Data[1]), '板块'] = '医药'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=801150.SI").Data[1]), '板块'] = '医药'
# fundhold_active.loc[fundhold_active['股票代码'].isin(w.wset("sectorconstituent","date=" + today + ";windcode=CJ001032.WI").Data[1]), '板块'] = '医药'
# def sector(t, fundhold_active):
#     fundhold_active = fundhold_active[fundhold_active['报告日期'] == t]
#     sector = fundhold_active.groupby('板块')['持股市值'].sum()
#     sector0 = sector / fundhold_active['持股市值'].sum()
#     return sector0
# holding = pd.DataFrame(index = ['医药', '周期', '成长', '消费', '稳定', '金融'])
# ts = pd.date_range(start = '2023-06-30', end = '2023-12-31', freq='6M')
# ts = [i.strftime('%Y%m%d') for i in ts]
# for i in ts:
#     print(i)
#     holding[i] = sector(i, fundhold_active)

# sector = pd.read_excel('基础数据/板块配置.xlsx', index_col=0)
# pd.concat([sector,holding.T]).to_excel('基础数据/板块配置.xlsx')

def huizong_table(df_huizong, df_shouyi, df_rank, title, picture_name, fpath_draw):
    fund_col = {'近一周': -1, '近一月': -1, '近三月': -1, '年初至今': -1, '近一年': -1}
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    cmap = plt.cm.get_cmap('RdYlGn')
    ax.axis('off')
    colortable = copy.deepcopy(df_huizong)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    colColours = ['lightsteelblue'] * len(colortable.columns)

    df_color = color_map(df_rank, ['近一周', '近一月', '近三月', '年初至今', '近一年'], fund_col)
    table = ax.table(cellText=df_huizong.values,
                     colLabels=df_huizong.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df_huizong.columns))))

    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        if ('-' in str(df_huizong.iloc[row - 1, col])) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
        if (row == 0) | (col == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        if df_color.iloc[row - 1, col] != None and row > 0 and col > -1:
            color_val = cmap(df_color.iloc[row - 1, col])
            cell.set_facecolor(color_val)

    ax.set_title(title, fontsize=fontsize_suptitle, fontweight='bold')
    fig.tight_layout()
    # 保存为图片
    plt.savefig(fpath_draw + '/' + picture_name + '.png', bbox_inches='tight')
    return


# %%准备一：热力图需要，根据数据df生成对应的颜色df：
def color_map(df, columns, direction=None):
    cell_table = df.copy()
    if not direction:
        direction = {col: 1 for col in columns}
    for i in range(len(cell_table.index)):
        for j in range(len(cell_table.columns)):
            if (df.iloc[i, j] == None) or (df.columns[j] not in columns):
                cell_table.iloc[i, j] = None
            else:
                if direction[df.columns[j]] > 0:
                    cell_table.iloc[i, j] = 1 - (0.25 + (df.iloc[i, j] - df.iloc[:, j].min(axis=0)) / (
                                df.iloc[:, j].max(axis=0) - df.iloc[:, j].min(axis=0)) / 2)
                else:  # 排名指标，越小颜色越红
                    cell_table.iloc[i, j] = 0.25 + (df.iloc[i, j] - df.iloc[:, j].min(axis=0)) / (
                                df.iloc[:, j].max(axis=0) - df.iloc[:, j].min(axis=0)) / 2
    return cell_table

# %%准备二：对FOF基金的名字进行切割
def cut_name(s, num=8):
    if len(s) <= num:
        return s
    else:
        return s[:num]

# %% 准备三：按照管理规模对基金经理去重
def drop_fund(df, today_str, according=['标签', '收益_1W'], ascending=[True, False]):
    '''按照管理规模对基金经理去重，并重新生成各个分位数'''
    df['基金规模'] = None
    df.iloc[:-3, df.columns.get_loc('基金规模')] = w.wss(df.index[:-3].tolist(), "netasset_total", "unit=1;tradeDate=" + today_str).Data[0]

    temp_lst = list()
    for i_name, i_df in df.iloc[:-3].groupby('基金经理'):
        temp_lst.append(i_df.sort_values('基金规模', ascending=False).iloc[0])
    temp = pd.concat(temp_lst, axis=1).transpose().sort_values(by=according, ascending=ascending)
    temp = temp[['简称', '基金经理', '标签', '收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD',
                 '排名_YTD', '收益_1Y', '排名_1Y']]

    new_row = {'25分位': [], '中位数': [], '75分位': []}
    for column in temp.columns:
        if column in ['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD',
                      '收益_1Y', '排名_1Y']:
            if '收益' in column:
                new_row['25分位'].append(np.percentile(temp[column], q=75).round(2))
                new_row['中位数'].append(np.percentile(temp[column], q=50).round(2))
                new_row['75分位'].append(np.percentile(temp[column], q=25).round(2))
            else:
                new_row['25分位'].append(np.percentile(temp[column], q=25).round(2))
                new_row['中位数'].append(np.percentile(temp[column], q=50).round(2))
                new_row['75分位'].append(np.percentile(temp[column], q=75).round(2))
        else:
            new_row['25分位'].append('')
            new_row['中位数'].append('')
            new_row['75分位'].append('')

    temp.loc['25分位'] = new_row['25分位']
    temp.loc['中位数'] = new_row['中位数']
    temp.loc['75分位'] = new_row['75分位']
    return temp


# %% 1.4FOF收益情况-函数
def red_green_table(df, df_color, title, picture_name, fpath_draw, add_line=False):
    pptsize(209)
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    cmap = plt.cm.get_cmap('RdYlGn')
    ax.axis('off')

    colortable = copy.deepcopy(df)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    colColours = ['lightsteelblue'] * len(colortable.columns)
    df.fillna(' ', inplace=True)
    table = ax.table(cellText=df.values,
                     colLabels=df.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=[1 / df.shape[1]] * df.shape[1])  # (col=list(range(len(df.columns))))

    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text - 1)
        if ('-' in str(df.iloc[row - 1, col])) and (row >= 1 and col >= 2):
            cell.get_text().set_color('red')
        if (row == 0) | (col == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        if df_color.iloc[row - 1, col] != None and row > 0 and col > -1:
            color_val = cmap(df_color.iloc[row - 1, col])
            cell.set_facecolor(color_val)
    if add_line:
        ax.axhline(y=(3 / (len(df.index) + 1)), lw=2, ls='-', c='red')

    for k in np.arange(0, len(df.columns)):
        if k in [4, 6, 8, 11, 14]:
            ax.axvline(x=(k / len(df.columns)), lw=3, ls='-', c='purple')

    ax.set_title(title, fontsize=fontsize_suptitle, fontweight='bold')
    fig.tight_layout()
    # 保存为图片
    plt.savefig(fpath_draw + '/' + picture_name + '.png')
    return


#%% 生成每个资产细分的重点池与核心池合并表格
def concat_table(i, today_str, benchmark, index_fund):
    '''给定V级资产细分的标签，合并核心池和重点池后输出表格'''
    try:
        core_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx',sheet_name='5 权益核心池 '+i, index_col=0)
        core_pool = drop_fund(core_pool, today_str).iloc[:-3]
        core_pool['级别'] = '核心池'
    except:
        core_pool = pd.DataFrame()
        
    try:
        key_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx',sheet_name='7 权益重点池 '+i, index_col=0)
        key_pool = drop_fund(key_pool, today_str)
        key_pool['级别'] = '重点池'
    except:
        key_pool = pd.DataFrame()
        
    try:
        i_pool = pd.concat([core_pool, key_pool], axis=0)
    except:
        i_pool = key_pool
        
    if len(i_pool.index)==0:
        return None

    i_pool.loc[benchmark[i]] = None
    i_pool.loc['偏股混合型基金指数'] = None
    i_pool.loc[benchmark[i], list(index_fund.columns[:-1])] = [round(x,2) for x in index_fund.loc[benchmark[i], list(index_fund.columns[:-1])].values]
    i_pool.loc['偏股混合型基金指数', list(index_fund.columns[:-1])] = [round(x,2) for x in index_fund.loc[benchmark['偏股混合型基金指数'], list(index_fund.columns[:-1])].values]
    i_pool.rename(index={benchmark[i]: index_fund.loc[benchmark[i],'指数']}, inplace=True)
    i_pool.iloc[-5:, i_pool.columns.get_loc('简称')] = list(i_pool.iloc[-5:].index)
    i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]
    return i_pool, len(core_pool.index)

#%% 绘制表格（带基准与不带基准两种类型）
def draw_table(df, df_color, title, picture_name, fpath_draw, num=0, include_benchmark=False, multi_group=False):
    '''muliti_group：如果是多种资产细分合并为一个表格，在不同组之间添加横线；
    num：表示核心池的只数，在核心池和重点池之间添加横线；
    include_benchmark：表示最下面的汇总行，如果False表示没有，如果有，添加分界线（2个基准指数+3个分位数）'''
    fig,ax = plt.subplots(nrows=1,ncols=1,sharex=False)
    cmap = plt.cm.get_cmap('RdYlGn')
    ax.axis('off')
    colortable = copy.deepcopy(df)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    rowColours = ['lightsteelblue'] * len(colortable.index)
    colColours = ['lightsteelblue'] * len(colortable.columns)
    df.fillna(' ', inplace=True)
    table = ax.table(cellText=df.values,
                     colLabels=df.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours,
                     fontsize=fontsize_text
                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=[1/df.shape[1]] * df.shape[1])  #list(range(len(df.columns))))  #()

    l = len(df.index)
    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        if not cell.get_text().get_text():
            continue
        if ('-' in str(df.iloc[row - 1, col])) and (row >= 1 and col >= 2):
            cell.get_text().set_color('red')
        if (row == 0) | (col == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        if df_color.iloc[row-1, col]!=None and col>-1 and row>0: # not in [0, l, l-1, l-2]:
            color_val = cmap(df_color.iloc[row-1,col])
            cell.set_facecolor(color_val)

    if multi_group==True:
        lines = []
        for q in range(len(df.index)):
            if q>0:
                if df['标签'][q]!=df['标签'][q-1]:
                    lines.append(len(df.index)-q)
        for i_line in lines:
            ax.axhline(y=(i_line/(len(df.index)+1)), lw=2, ls='-', c='blue')
    if include_benchmark:
        ax.axhline(y=(5/(len(df.index)+1)), lw=2, ls='-', c='red')
        ax.axhline(y=(2/(len(df.index)+1)), lw=2, ls='-', c='red')
    if num>0:
        ax.axhline(y=1-((num+1)/(len(df.index)+1)), lw=2, ls='-', c='blue')
        
    for k in np.arange(0,len(df.columns)):
        if k in [3,5,7,9,11,13,15]:
            ax.axvline(x=(k/len(df.columns)), lw=3, ls='-', c='purple')

    ax.set_title(title, fontsize=fontsize_suptitle, fontweight='bold', pad=5)
    fig.tight_layout()
    # 保存为图片
    plt.savefig(fpath_draw+'/'+picture_name+'.png', bbox_inches='tight')
    return

#%%
#生成未入池原因
def reason_table(df, period, title, picture_name, fpath_draw, lines=[]):
    fig, ax = plt.subplots(nrows=1,ncols=1,sharex=False)
    cmap = plt.cm.get_cmap('RdYlGn')
    ax.axis('off')
    colortable = copy.deepcopy(df)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'

    rowColours = ['lightsteelblue'] * len(colortable.index)
    colColours = ['lightsteelblue'] * len(colortable.columns)
    if period=='近一周':
        colColours[3] == ['lightsteelred']
    elif period=='近一月':
        colColours[4] == ['lightsteelred']
    elif period=='近三月':
        colColours[5] == ['lightsteelred']
    elif period=='年初至今':
        colColours[6] == ['lightsteelred']
      
    df_color = color_map(df, ['近一周','近一月','近三月','年初至今','近一年','P+','P-','PRD','Calmar比率','最大回撤','α波动率'])
    df.fillna(' ', inplace=True)
    table = ax.table(cellText=df.values,
                     colLabels=df.columns,
                     bbox=(0, 0, 1, 1),
                     cellLoc='center',
                     loc='center',
                     cellColours=colortable.values,
                     colColours=colColours

                     )
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df.columns))))

    for (row, col), cell in table.get_celld().items():
        if col==len(df.columns)-1 or col==len(df.columns)-2:
            cell.set_fontsize(fontsize_text-2)
        else:
            cell.set_fontsize(fontsize_text)
        if ('-' in str(df.iloc[row - 1, col])) and (row >= 1 and col >= 2 and col<14):
            cell.get_text().set_color('red')
        if (row == 0) | (col == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
        if df_color.iloc[row-1,col]!=None and row>0 and col>-1:
            color_val = cmap(df_color.iloc[row-1,col])
            cell.set_facecolor(color_val)
    
    for i_line in lines:
        ax.axhline(y=(i_line/(len(df.index)+1)), lw=2, ls='-', c='red')
    ax.set_title(title, fontsize=fontsize_suptitle, fontweight='bold')
    fig.tight_layout()
    # 保存为图片
    plt.savefig(fpath_draw+'/' + picture_name + '.png', bbox_inches='tight')
    return


        