# -*- coding: utf-8 -*-
"""
Created on Mon Oct 16 14:56:15 2023

@author: gyrx-zhaomh
"""
import os
import chart as ct
import copy
import cx_Oracle
# cx_Oracle.init_oracle_client(os.path.join('.', 'tools', 'clt64', 'instantclient_11_2'))
import datetime as dt
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import pandas as pd
import pickle
import seaborn as sns
import urllib3
import warnings
warnings.filterwarnings("ignore")
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from json import dumps,loads
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.font_manager import FontProperties
from matplotlib.table import Table
from openpyxl import load_workbook
from plotFunc import *
from scipy import stats
from WindPy import w,datetime
w.start()

# 获取基础数据文件夹路径的辅助函数
def get_base_data_path(filename):
    """
    获取基础数据文件夹中文件的完整路径

    参数:
    filename (str): 文件名

    返回:
    str: 文件的完整路径
    """
    # 获取当前文件的绝对路径
    current_file_path = os.path.abspath(__file__)
    # 获取当前文件所在的目录
    current_dir = os.path.dirname(current_file_path)
    # 构建基础数据文件夹的路径
    base_data_dir = os.path.join(current_dir, '基础数据')
    # 返回完整的文件路径
    return os.path.join(base_data_dir, filename)

fpath_draw = '图表'
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True

def printtime(t):
    if t!=1:
        print('-----' * 5+'花费%s秒'%(dt.datetime.now()-t))
        t=dt.datetime.now()
    else:
        print('—' * 25+'共费%s秒'%(dt.datetime.now()-t0))
    return t
t0=dt.datetime.now()
t=t0


###1、每次运行需更改：today
###2、需检查：last1w和fof_date的偏移量（没有假期不涉及）、pool_dates（近一年涉及的四个基金池+前移两个基金池
###3、季度调整：rpt_date、pool_date、所有基金重仓或持仓数据的pkl文件

today = dt.datetime.strptime('20250308','%Y%m%d')           #上周六
today_str = today.strftime('%Y-%m-%d')
rpt_date = '20241231'                                       #等对应季报出来再更新，目前对应的是24年三季报
pool_date = '20250131'                                      #季度基金池调池日（日历日，0131，0430，0731，1031）
                                                            #更新季度基金池记得同步更新datei_dict
pool_dates = ['20231031', '20240131', '20240430', '20240731', '20241031', '20250131']
last1w = w.tdaysoffset(-4, today, "Period=D").Data[0][0].strftime('%Y-%m-%d') #5个交易日,-4,节假日不足5个交易日需对应修改参数
last1m = (today + relativedelta(months=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')
last3m = (today + relativedelta(months=-3) + relativedelta(days=1)).strftime('%Y-%m-%d')
last6m = (today + relativedelta(months=-6) + relativedelta(days=1))
ytd = str(today.year) +'-01-01'
last1y = (today + relativedelta(years=-1) + relativedelta(days=1)).strftime('%Y-%m-%d')
data_date = (w.tdaysoffset(0, today, "Period=D").Data[0][0]).strftime('%Y-%m-%d') #需要手动算一下，表示数据的截止日期
fof_date = (w.tdaysoffset(-2, today, "Period=D").Data[0][0]).strftime('%Y-%m-%d')


#%% 一、基金数据提取
# 获取当前文件的绝对路径
current_file_path = os.path.abspath(__file__)
# 获取当前文件所在的目录
current_dir = os.path.dirname(current_file_path)
# 构建基础数据文件夹的路径
base_data_dir = os.path.join(current_dir, '基础数据')
# 构建文件路径
fund_file_path = get_base_data_path('{0}季度筛选结果明细.xlsx'.format(pool_date[:6]))

# 打印文件路径以便调试
print(f"尝试读取文件: {fund_file_path}")
fund = pd.read_excel(fund_file_path, sheet_name='ag-grid', index_col=2)
fund = fund.drop('基金代码',axis=1)
fund.index.name = '基金代码'
fund = fund.dropna(subset=['银河证券三级分类'])
fund = fund[~fund['银河证券三级分类'].str.contains('非A类')]
all_df = copy.deepcopy(fund)
fund = fund[fund['公募FOF基金池级别']!='禁买']

fund['基金经理'] = w.wss(fund.index.tolist()[:5000],"fund_fundmanager").Data[0] + \
                   w.wss(fund.index.tolist()[5000:],"fund_fundmanager").Data[0]
fund['基金管理人'] = w.wss(fund.index.tolist()[:5000], "fund_corp_fundmanagementcompany").Data[0] + \
                     w.wss(fund.index.tolist()[5000:], "fund_corp_fundmanagementcompany").Data[0]
fund['办公地址'] = w.wss(fund.index.tolist()[:5000], "fund_corpoffice").Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "fund_corpoffice").Data[0]
fund['去年基金规模'] = w.wss(fund.index.tolist()[:5000], "netasset_total","unit=1;tradeDate=" + ytd).Data[0] + \
                       w.wss(fund.index.tolist()[5000:], "netasset_total","unit=1;tradeDate=" + ytd).Data[0]
fund['基金规模'] = w.wss(fund.index.tolist()[:5000], "netasset_total","unit=1;tradeDate=" + today_str).Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "netasset_total","unit=1;tradeDate=" + today_str).Data[0]
fund['近一周'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
fund['近一月'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
fund['近三月'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
fund['近半年'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last6m + ";endDate=" + today_str).Data[0]
fund['年初至今'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0] + \
                   w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
fund['近一年'] = w.wss(fund.index.tolist()[:5000], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0] + \
                 w.wss(fund.index.tolist()[5000:], "NAV_adj_return", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
fund['去年'] = w.wss(fund.index.tolist()[:5000], "return_y","tradeDate=" + last1y).Data[0] + \
               w.wss(fund.index.tolist()[5000:], "return_y","tradeDate=" + last1y).Data[0]
ts = pd.date_range(start = today + relativedelta(years=-1), periods=13, freq='1M')
ts = pd.Series(ts).sort_values(ascending=False).tolist()
for i in ts:
    fund[i.strftime('%Y-%m')] = w.wss(fund.index.tolist()[:5000], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0] + \
                                w.wss(fund.index.tolist()[5000:], "return_m", "tradeDate=" + i.strftime('%Y-%m-%d')).Data[0]
fund['my是否基础池'] = '否'
temp1 = (fund['公募FOF基金池级别']=='基础') | (fund['公募FOF基金池级别']=='重点') | (fund['公募FOF基金池级别']=='核心')
temp1 = temp1[temp1].index
fund.loc[fund.index.isin(temp1),'my是否基础池'] = '是'
fund = fund.dropna(subset = ['基金管理人', '近一周', '近一年'])

print('1.0 基金数据提取完毕')
printtime(t)

# #%% 二、各类基金池的动态跟踪

# #%% #提取基金净值
# end_day = dt.datetime.strptime(today_str, '%Y-%m-%d').strftime('%Y%m%d')    #'%Y%m%d'日期字符串，表示提取数据的时间区间（可有重叠），通常不用再改
# start_day = last1w #(dt.datetime.strptime(today_str, '%Y-%m-%d') + relativedelta(months=-1)).strftime('%Y-%m-%d')
# # datei_dict = {'20230430':'20230430','20230531':'20230430','20230630':'20230430','20230731':'20230731','20230831':'20230731','20230930':'20230731','20231031': '20231031', '20231130': '20231031', '20231231': '20231031', '20240131': '20240131',
# #               '20240229': '20240131','20240331': '20240131','20240430': '20240430','20240531': '20240430','20240630': '20240430','20240731':'20240731','20240831':'20240731','20240930':'20240731', '20241031':'20241031', '20241130':'20241031',
# #               '20241231':'20241031'}        #月度末到季度末的调池映射
# end_day_month = (pd.to_datetime(end_day, format='%Y%m%d') + pd.offsets.MonthEnd(0)).strftime('%Y%m%d')
# end_month_date = pd.date_range(start=start_day,end=end_day_month,freq='M')
# end_month_date = [i.strftime('%Y%m%d') for i in end_month_date]

# oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@192.168.105.38:1521/wind')
# def get_fund_nav(startDate, endDate):
#     # "获取基金净值"
#     sqlcode = """
#     SELECT PRICE_DATE as pricedt, F_INFO_WINDCODE as fundcode, F_NAV_ADJUSTED as adjnav
#     FROM winddf.ChinaMutualFundNAV
#     where PRICE_DATE between '%s' and '%s'
#     order by F_INFO_WINDCODE, PRICE_DATE
#     """% (startDate, endDate)
#     tDATA = pd.read_sql(sqlcode, oracle_conn, index_col=['PRICEDT', 'FUNDCODE'])
#     return tDATA
# try:
#     with open('基础数据/基金净值数据.pkl', "rb") as fp:
#         fund_value_data = pickle.load(fp)
# except:#第一次跑没这个文件
#         fund_value_data=pd.DataFrame()

# fund_value_data1 = get_fund_nav(start_day, end_day)
# fund_value_data1 = fund_value_data1.ADJNAV.unstack()
# fund_value_data1.index.name='日期'
# fund_value_data1=fund_value_data1.reset_index().melt(id_vars='日期', var_name='基金代码', value_name='单位净值')
# fund_value_data=pd.concat([fund_value_data,fund_value_data1],axis=0).drop_duplicates(subset=['日期', '基金代码'])
# fund_value_data=fund_value_data.reset_index(drop=True)
# with open('基础数据/基金净值数据.pkl', "wb") as fp:
#     pickle.dump(fund_value_data, fp)


# fund_value_data=fund_value_data.pivot(index='日期', columns='基金代码')['单位净值'].sort_index()
# fund_value_data.index=pd.to_datetime(fund_value_data.index,format="%Y%m%d").date
# #fund_value_data=fund_value_data.fillna(method='ffill')
# fund_value_data=fund_value_data.join(w.wsd("000906.SH", "close", fund_value_data.index[0].strftime("%Y-%m-%d"), "",usedf=True)[1].rename({'CLOSE':'中证800'},axis=1))
# fund_value_data.dropna(subset=['中证800'], inplace=True)#为了对齐日期，删掉非交易日带来的影响
# fund_chg_data_all=fund_value_data.pct_change()

# start_day='20231101'
# adjust_date=w.tdays(start_day, end_day, "Days=Alldays;Period=M").Data[0]   #季度调池，之后再截断
# lm_date=adjust_date[-2].date()
# adjust_date = [dt.datetime(2023,11,1)] + adjust_date[2:-1:3]+[adjust_date[-1]]
# adjust_date_todate=[i.date() for i in adjust_date]
# adjust_date = [i.strftime('%Y%m%d') for i in adjust_date]
# adjust_date#可以通过调整adjust_date来调整观察期，以后模拟组合调仓随季度调仓，方便跟踪

# #根据基金净值计算基金池的收益表现
# label_lst = ['A股价值策略', 'A股全市场策略', 'A股小盘策略', 'TMT', '新能源', '军工', '其他成长', '医药', '白酒', '其他消费', '金融', '周期', '其他金融周期']
# level_lst = ['可买池', '基础池', '重点池']
# date_lst = {'1W':last1w, '1M':last1m, '3M':last3m, '6M':last6m, 'YTD':ytd, '1Y':last1y}
# dict_graph = dict()
# fund_benchmark_dict={'A股全市场策略':'930950.CSI','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ','新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI','医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH','金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI','一级债基':'885006.WI','低含权':'885006.WI','中含权':'885007.WI','高含权':'885003.WI','转债':'000832.CSI','中短久期':'885062.WI','长久期':'885008.WI'}
# fund_benchmark_name={'A股全市场策略':'偏股混合', 'A股价值策略':'国证价值', 'A股小盘策略':'中证1000','新能源':'中证新能','TMT':'中证TMT','军工':'中证军工','其他成长':'中证成长风格','医药':'中证医药','白酒':'中证白酒','其他消费':'中证消费','金融':'800金地','周期':'中证周期100','其他金融周期':'中证周期50',}

# for date in date_lst.keys():
#     dict_graph[date] = pd.DataFrame(index=label_lst, columns=level_lst)

# for label in ['A股全市场策略', 'A股价值策略', 'A股小盘策略', '新能源', 'TMT', '军工', '其他成长', '医药', '白酒', '其他消费', '金融', '周期', '其他金融周期',]:
#     print(label)
#     benchmark=fund_benchmark_dict[label]
#     portfolio_nav_all=pd.DataFrame()
#     performance_month=pd.DataFrame()
#     rank_month=pd.DataFrame()

#     portfolio_chg_kemai=pd.Series()
#     portfolio_chg_jichu=pd.Series()
#     portfolio_chg_zhongdian=pd.Series()
#     portfolio_chg_newin = pd.Series()
#     portfolio_chg_out = pd.Series()
#     portfolio_chg_invest=pd.Series()

#     for i in range(len(adjust_date)-1):
#         date_i= adjust_date[i]
#         #季度池子
#         try:
#             if i == 0:
#                 fund_label=pd.read_excel('基础数据/202310_qfp.xlsx',sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
#                 fund_label_bf = pd.read_excel('基础数据/202307_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
#             elif i==1:
#                 fund_label = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date_i[:6]), sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
#                 fund_label_bf = pd.read_excel('基础数据/202310_qfp.xlsx', sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
#             else:
#                 fund_label=pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date_i[:6]),sheet_name='基金池_简版',index_col=0)[['简称','资产细分(V)','管理方式(VI)','是否为初始基金','是否可买池','是否重点池','是否基础池']]
#                 fund_label_bf = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版', index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]

#             fund_label = fund_label[(fund_label['是否可买池'] == '是') & (fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动') & (fund_label['资产细分(V)'] == label)]
#             fund_label_bf = fund_label_bf[(fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (fund_label_bf['资产细分(V)'] == label)]
#         except:
#             fund_label = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(date_i[:6]), sheet_name = 'ag-grid',index_col = 2)[
#                 ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称':'简称','资产细分V':'资产细分(V)','管理方式VI':'管理方式(VI)'})
#             try:
#                 fund_label_bf = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(adjust_date[i-1][:6]), sheet_name='基金池_简版',
#                               index_col=0)[['简称', '资产细分(V)', '管理方式(VI)', '是否为初始基金', '是否可买池', '是否重点池', '是否基础池']]
#             except:
#                 fund_label_bf = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(adjust_date[i-1][:6]), sheet_name='ag-grid', index_col=2)[
#                     ['基金简称', '资产细分V', '管理方式VI', '公募FOF基金池级别']].rename(columns={'基金简称': '简称', '资产细分V': '资产细分(V)', '管理方式VI': '管理方式(VI)'})

#             for j in range(int(np.ceil(len(fund_label.index) / 500))):
#                 fund_label.loc[fund_label.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = w.wss(fund_label.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
#                 fund_label_bf.loc[fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], '是否为初始基金'] = w.wss(fund_label_bf.index.tolist()[j * 500:(j + 1) * 500], "fund_initial").Data[0]
#             fund_label = fund_label[(fund_label['是否为初始基金'] == '是') & (fund_label['管理方式(VI)'] == '主动管理') & (fund_label['资产细分(V)'] == label)]
#             fund_label = fund_label[fund_label['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]
#             try:
#                 fund_label_bf = fund_label_bf[(fund_label_bf['是否可买池'] == '是') & (fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动') & (fund_label_bf['资产细分(V)'] == label)]
#             except:
#                 fund_label_bf = fund_label_bf[(fund_label_bf['是否为初始基金'] == '是') & (fund_label_bf['管理方式(VI)'] == '主动管理') & (fund_label_bf['资产细分(V)'] == label)]
#                 fund_label_bf = fund_label_bf[fund_label_bf['公募FOF基金池级别'].str.contains(r'(可买|重点|基础)')]


#         #转化为wind index
#         fund_label.index.name='基金代码'
#         index_new=[]
#         for index in fund_label.index:
#             if index in fund_chg_data_all.columns:
#                 index_new.append(index)
#             elif index[:-3]+'.SZ' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3]+'.SZ')
#             elif index[:-3]+'.SH' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3]+'.SH')
#             else:
#                 index_new.append(index)
#         fund_label.index=index_new
#         fund_label = fund_label.loc[~fund_label.index.duplicated(keep='first')]

#         # 转化为wind index
#         fund_label_bf.index.name = '基金代码'
#         index_new = []
#         for index in fund_label_bf.index:
#             if index in fund_chg_data_all.columns:
#                 index_new.append(index)
#             elif index[:-3] + '.SZ' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3] + '.SZ')
#             elif index[:-3] + '.SH' in fund_chg_data_all.columns:
#                 index_new.append(index[:-3] + '.SH')
#             else:
#                 index_new.append(index)
#         fund_label_bf.index = index_new
#         fund_label_bf = fund_label_bf.loc[~fund_label_bf.index.duplicated(keep='first')]

#         kemai_pool=fund_label.index.tolist()
#         kemai_pool_bf = fund_label_bf.index.tolist()
#         try:
#             jichu_pool=fund_label[fund_label['是否基础池']=='是'].index.tolist()
#             zhongdian_pool=fund_label[fund_label['是否重点池']=='是'].index.tolist()
#             try:
#                 new_in_pool=list((set(jichu_pool)-set(fund_label_bf[fund_label_bf['是否基础池']=='是'].index.tolist()))|(set(zhongdian_pool)-set(fund_label_bf[fund_label_bf['是否重点池']=='是'].index.tolist())))
#                 out_pool = list((set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist())-set(jichu_pool))|(set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())-set(
#                     zhongdian_pool)))
#             except:
#                 new_in_pool = list(
#                     (set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()))|( set(
#                         zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
#                 out_pool = list(
#                     (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool) )|(set(
#                         fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
#                         zhongdian_pool)))
#         except:
#             jichu_pool = fund_label[fund_label['公募FOF基金池级别'] == '基础'].index.tolist()
#             zhongdian_pool = fund_label[fund_label['公募FOF基金池级别'] == '重点'].index.tolist()
#             try:
#                 new_in_pool = list(
#                     (set(jichu_pool) - set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()))|( set(
#                         zhongdian_pool) - set(fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist())))
#                 out_pool = list(
#                     (set(fund_label_bf[fund_label_bf['是否基础池'] == '是'].index.tolist()) - set(jichu_pool) )|( set(
#                         fund_label_bf[fund_label_bf['是否重点池'] == '是'].index.tolist()) - set(
#                         zhongdian_pool)))
#             except:
#                 new_in_pool = list(
#                     ( set(jichu_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) )|( set(
#                         zhongdian_pool) - set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist())))
#                 out_pool = list(
#                     (set(fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '基础'].index.tolist()) - set(jichu_pool)) | (set(
#                         fund_label_bf[fund_label_bf['公募FOF基金池级别'] == '重点'].index.tolist()) - set(
#                         zhongdian_pool)))


#         #可买池
#         fund_list=kemai_pool
#         fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],list(set(list(fund_value_data.columns))&set(fund_list))]
#         fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
#         fund_nav=fund_nav.median(axis=1)
#         portfolio_chg_i=fund_nav.pct_change().dropna()
#         portfolio_chg_kemai = pd.concat([portfolio_chg_kemai,portfolio_chg_i], axis=0)
#         #portfolio_chg_kemai=portfolio_chg_kemai.append(portfolio_chg_i)

#         #基础池
#         fund_list=jichu_pool
#         fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],fund_list]
#         fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
#         fund_nav=fund_nav.median(axis=1)
#         portfolio_chg_i=fund_nav.pct_change().dropna()
#         portfolio_chg_jichu = pd.concat([portfolio_chg_jichu, portfolio_chg_i], axis=0)
#         #portfolio_chg_jichu=portfolio_chg_jichu.append(portfolio_chg_i)

#         #重点池
#         fund_list=zhongdian_pool
#         if len(zhongdian_pool)==0:
#             fund_list = jichu_pool
#         fund_nav=fund_value_data.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i+1],fund_list]
#         fund_nav=fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
#         fund_nav=fund_nav.median(axis=1)
#         portfolio_chg_i=fund_nav.pct_change().dropna()
#         portfolio_chg_zhongdian = pd.concat([portfolio_chg_zhongdian, portfolio_chg_i], axis=0)
#         #portfolio_chg_zhongdian=portfolio_chg_zhongdian.append(portfolio_chg_i)

#         # 新进池
#         fund_list = new_in_pool
#         fund_nav = fund_value_data.loc[
#                    w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], fund_list]
#         fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
#         fund_nav = fund_nav.median(axis=1)
#         portfolio_chg_i = fund_nav.pct_change().dropna()
#         portfolio_chg_newin = pd.concat([portfolio_chg_newin,portfolio_chg_i], axis=0)
#         #portfolio_chg_newin = portfolio_chg_newin.append(portfolio_chg_i)

#         # 出池
#         fund_list = out_pool
#         fund_nav = fund_value_data.loc[
#                    w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date():adjust_date_todate[i + 1], fund_list]
#         fund_nav = fund_nav / fund_nav.loc[w.tdaysoffset(0, adjust_date_todate[i], "").Data[0][0].date()]
#         fund_nav = fund_nav.median(axis=1)
#         portfolio_chg_i = fund_nav.pct_change().dropna()
#         portfolio_chg_out = pd.concat([portfolio_chg_out, portfolio_chg_i], axis=0)
#         #portfolio_chg_out = portfolio_chg_out.append(portfolio_chg_i)

#     # %%
#     #汇总不同日期
#     portfolio_nav_all=pd.DataFrame()
#     #可买池
#     portfolio_nav = pd.Series()
#     portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_kemai+1], axis=0)
#     #portfolio_nav=portfolio_nav.append(portfolio_chg_kemai+1)
#     portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
#     portfolio_nav=portfolio_nav.sort_index().cumprod()
#     portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'可买池'},axis=1)],axis=1)

#     #基础池
#     portfolio_nav = pd.Series()
#     portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_jichu+1], axis=0)
#     #portfolio_nav=portfolio_nav.append(portfolio_chg_jichu+1)
#     portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
#     portfolio_nav=portfolio_nav.sort_index().cumprod()
#     portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'基础池'},axis=1)],axis=1)

#     #重点池
#     portfolio_nav = pd.Series()
#     portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_zhongdian + 1], axis=0)
#     #portfolio_nav=portfolio_nav.append(portfolio_chg_zhongdian+1)
#     portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
#     portfolio_nav=portfolio_nav.sort_index().cumprod()
#     portfolio_nav_all=pd.concat([portfolio_nav_all,portfolio_nav.to_frame().rename({0:'重点池'},axis=1)],axis=1)

#     # 新进池
#     portfolio_nav = pd.Series()
#     portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_newin + 1], axis=0)
#     #portfolio_nav = portfolio_nav.append(portfolio_chg_newin + 1)
#     portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
#     portfolio_nav = portfolio_nav.sort_index().cumprod()
#     portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '每期新进'}, axis=1)], axis=1)

#     # 出池
#     portfolio_nav = pd.Series()
#     portfolio_nav = pd.concat([portfolio_nav, portfolio_chg_out + 1], axis=0)
#     #portfolio_nav = portfolio_nav.append(portfolio_chg_out + 1)
#     portfolio_nav.loc[dt.datetime.strptime(start_day, '%Y%m%d').date()] = 1
#     portfolio_nav = portfolio_nav.sort_index().cumprod()
#     portfolio_nav_all = pd.concat([portfolio_nav_all, portfolio_nav.to_frame().rename({0: '汇总出池'}, axis=1)], axis=1)

#     portfolio_nav_all=portfolio_nav_all.join(w.wsd(benchmark, "close", portfolio_nav_all.index[0].strftime("%Y-%m-%d"), "","Days=Alldays;Fill=Previous",usedf=True)[1].rename({'CLOSE':benchmark},axis=1))
#     portfolio_nav_all=portfolio_nav_all.fillna(method='ffill')/portfolio_nav_all.iloc[0]
#     portfolio_nav_all['重点池/可买池']=(portfolio_nav_all['重点池']/portfolio_nav_all['可买池']-1)*100
#     portfolio_nav_all=portfolio_nav_all.rename({benchmark:fund_benchmark_name[label]},axis=1)


#     # %%
#     color_dict={fund_benchmark_name[label]:(0.7,0.4,0),'可买池':'orange','基础池':(0.5,0.3,0.8),'重点池':'purple','模拟池':'blue','每期新进':'red','汇总出池':'green'}
#     line_dict={fund_benchmark_name[label]:'--','可买池':'--','基础池':'--','重点池':'--','模拟池':'-','每期新进':'--','汇总出池':'--'}

#     # %%
#     #超额图
#     df=portfolio_nav_all.copy()
#     pptsize(209)
#     fig_name = label+'_基金池业绩走势跟踪'+end_day
#     jc_date=adjust_date_todate[0]
#     lm_date = w.tdaysoffset(0, lm_date, "").Data[0][0].date()
#     text_mark3 = df.iloc[-1][[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']] / df.loc[w.tdaysoffset(0, ytd, "").Data[0][0].date(),[fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']]
#     text_mark3 = ['YTD：','{0}：'.format(fund_benchmark_name[label]),str(round((text_mark3[fund_benchmark_name[label]]-1)*100,2)),'，可买池：',str(round((text_mark3['可买池']-1)*100,2)),'，基础池：',str(round((text_mark3['基础池']-1)*100,2)),'，重点池：',str(round((text_mark3['重点池']-1)*100,2)),'，每期新进：',str(round((text_mark3['每期新进']-1)*100,2)),'，汇总出池：',str(round((text_mark3['汇总出池']-1)*100,2)),'，超额：',str(round((text_mark3['重点池']-text_mark3['可买池'])*100,2))]
#     text_mark3 = ''.join(text_mark3)
#     text_mark=text_mark3

#     fig, ax1 = plt.subplots()
#     for column in [fund_benchmark_name[label],'可买池','基础池','重点池','每期新进','汇总出池']:
#         ax1.plot(df.index, df[column].tolist(), label = column, color=color_dict[column], alpha = 0.7 ,linestyle=line_dict[column])
#         ax1.text(df.index[-1], df[column].tolist()[-1],
#                         f'{column}: {df[column].tolist()[-1]:.4f}', color=color_dict[column], verticalalignment='bottom',fontsize=fontsize_text-6)#rotation=90,
#     ax1.tick_params(axis='y',labelsize=fontsize_legend)
#     ax1.spines['right'].set_color((0,0,1,0.5))
#     ax1.spines['top'].set_visible(False)
#     plt.xticks(rotation=0)

#     #近一月
#     ax1.axvline(x=lm_date,color=(0.7,0.7,0.7),linestyle='--',linewidth=0.5)
#     plt.grid(alpha=light)

#     #超额
#     ax2 = plt.twinx()
#     column='重点池/可买池'
#     ax2.fill_between(df.index, 0, df[column].tolist(), facecolor = 'blue', alpha = 0.3) #light)
#     ax2.text(df.index[-1], df[column].tolist()[-1],
#                         f'{column}: {df[column].tolist()[-1]:.2f}', color=(0,0,1,0.5), verticalalignment='bottom',fontsize=fontsize_text-6)
#     ax2.set_ylabel('重点池相对可买池收益(%)',fontsize=fontsize_legend,color=(0,0,1,0.5))
#     ax2.tick_params(axis='y',labelsize=fontsize_legend,color=(0,0,1,0.5),labelcolor=(0,0,1,0.5))
#     ax2.spines['right'].set_color((0,0,1,0.5))
#     ax2.spines['top'].set_visible(False)
#     ax2.text(1,0.03, text_mark, transform=ax2.transAxes, horizontalalignment='right',verticalalignment='bottom',fontsize=fontsize_text-2)
#     ax2.set_xlim(df.index[0], df.index[-1])
#     plt.gcf().autofmt_xdate()
#     plt.title(fig_name,fontsize=fontsize_suptitle)

#     plt.show()
#     fig.savefig('图表/{0}.jpg'.format(fig_name))
#     plt.close()

#     for date in date_lst.keys():
#         temp = w.tdaysoffset(0, date_lst[date], "").Data[0][0].date()
#         dict_graph[date].loc[label,'重点池'] =(df.iloc[-1][['重点池']] / df.loc[temp][['重点池']]).values[0]-1
#         dict_graph[date].loc[label, '可买池'] = (df.iloc[-1][['可买池']] / df.loc[temp][['可买池']]).values[0]-1
#         dict_graph[date].loc[label, '基础池'] = (df.iloc[-1][['基础池']] / df.loc[temp][['基础池']]).values[0]-1

# plt_industry_pool('1W', dict_graph['1W'], '2-1-1(上) 各行业基金池收益'+'1W', fpath_draw, data_date)
# plt_industry_pool('1M', dict_graph['1M'], '2-1-1(下) 各行业基金池收益'+'1M', fpath_draw, data_date)
# plt_industry_pool('3M', dict_graph['3M'], '2-1-2(上) 各行业基金池收益'+'3M', fpath_draw, data_date)
# plt_industry_pool('6M', dict_graph['6M'], '2-1-2(下) 各行业基金池收益'+'6M', fpath_draw, data_date)
# plt_industry_pool('YTD', dict_graph['YTD'], '2-1-3(上) 各行业基金池收益'+'YTD', fpath_draw, data_date)
# plt_industry_pool('1Y', dict_graph['1Y'], '2-1-3(下) 各行业基金池收益'+'1Y', fpath_draw, data_date)

# print('2.0 基金池动态跟踪完毕')
# printtime(t)

# #%% 三、等权重全市场组合跟踪
# today_str2 = today.strftime('%Y%m%d')
# last1y2 = (today + relativedelta(years=-1)).strftime('%Y%m%d')
# end_month_date = pd.date_range(start=last1y2, end=today_str2, freq='M')
# end_month_date = [(i+relativedelta(months=-1))for i in end_month_date]
# end_month_date = [(i + pd.offsets.MonthEnd(0)).strftime('%Y%m%d') for i in end_month_date]
# POOL = dict()
# for i in range(len(pool_dates)):
#     date = pool_dates[i][:6]
#     if date in ['202301','202304','202307','202310','202401','202404']:
#         fund0 = pd.read_excel('基础数据/{0}_qfp.xlsx'.format(date), sheet_name='基金池', index_col=0)
#         fund0 = fund0[(fund0['是否为初始基金'] == '是') & (fund0['管理方式(VI)'] == '主动') & (fund0['资产细分(V)'] == 'A股全市场策略')]
#         if '基金池级别' not in fund0.columns:
#             fund0['基金池级别'] = '禁买'
#             fund0.loc[fund0['是否重点池'] == '是', '基金池级别'] = '重点'
#             fund0.loc[(fund0['是否重点池'] != '是') & (fund0['是否基础池'] == '是'), '基金池级别'] = '基础'
#             fund0.loc[(fund0['是否重点池'] != '是') & (fund0['是否基础池'] != '是') & (fund0['是否可买池'] == '是'), '基金池级别'] = '可买'
#         fund0 = fund0[['简称', '资产类别(I)', '资产地区(II)', '资产类属(III)', '资产板块(IV)', '资产细分(V)', '基金池级别']]
#         fund0.columns = ['基金简称', '资产类别I', '资产地区II', '资产类属III', '资产板块IV', '资产细分V', '基金池级别']
#         POOL[pool_dates[i]] = fund0
#     else:
#         fund0 = pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(date), sheet_name='ag-grid', index_col=2)
#         fund0 = fund0.drop('基金代码',axis=1)
#         fund0.index.name = '基金代码'  #用wind代码替换6位数字的基金代码
#         fund0 = fund0[(~fund0['银河证券三级分类'].str.contains('非A类')) & (fund0['管理方式VI'] == '主动管理') & (fund0['资产细分V'] == 'A股全市场策略')]
#         fund0['基金池级别'] = fund0['公募FOF基金池级别']
#         fund0.loc[fund0['公募FOF基金池级别']=='核心', '基金池级别'] = '重点'
#         fund0 = fund0[['基金简称', '资产类别I', '资产地区II', '资产类属III', '资产板块IV', '资产细分V', '基金池级别']]
#         POOL[pool_dates[i]] = fund0

# # 1、季度重点池-出入
# s=0
# dict_key, dict_keyout, dict_keyin = dict(), dict(), dict()
# out_of_pool, in_to_pool = set(), set()
# for i in range(5):
#     old_date, new_date = pool_dates[i], pool_dates[i + 1]
#     fund_old = POOL[old_date]
#     fund_new = POOL[new_date]
#     old_pool = fund_old[fund_old['基金池级别'] == '重点'].index.tolist()
#     new_pool = fund_new[fund_new['基金池级别'] == '重点'].index.tolist()
#     out_of_pool.update(set(old_pool) - set(new_pool))
#     in_to_pool = set(new_pool) - set(old_pool)

#     dict_key[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] == '重点'].index.tolist()})
#     dict_keyout[new_date] = pd.DataFrame({'基金代码': list(out_of_pool)})
#     dict_keyin[new_date] = pd.DataFrame({'基金代码': list(in_to_pool)})
#     s += len(dict_key[new_date].index)+len(dict_keyin[new_date].index)+len(dict_keyout[new_date].index)

# # 2、季度基础池-出入
# dict_basic, dict_basicout, dict_basicin = dict(), dict(), dict()
# out_of_pool, in_to_pool = set(), set()
# for i in range(5):
#     old_date, new_date = pool_dates[i], pool_dates[i + 1]
#     fund_old = POOL[old_date]
#     fund_new = POOL[new_date]
#     old_pool = fund_old[(fund_old['基金池级别'] == '基础') | (fund_old['基金池级别'] == '重点')].index.tolist()
#     new_pool = fund_new[(fund_new['基金池级别'] == '基础') | (fund_old['基金池级别'] == '重点')].index.tolist()
#     out_of_pool.update(set(old_pool) - set(new_pool))
#     in_to_pool = set(new_pool) - set(old_pool)

#     dict_basic[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] == '基础'].index.tolist()})
#     dict_basicout[new_date] = pd.DataFrame({'基金代码': list(out_of_pool)})
#     dict_basicin[new_date] = pd.DataFrame({'基金代码': list(in_to_pool)})
#     s += len(dict_basic[new_date].index) + len(dict_basicin[new_date].index) + len(dict_basicout[new_date].index)

# # 3、季度可买池
# dict_kemai = dict()
# for i in range(5):
#     new_date = pool_dates[i + 1]
#     fund_new = POOL[new_date]
#     dict_kemai[new_date] = pd.DataFrame({'基金代码': fund_new[fund_new['基金池级别'] != '禁买'].index.tolist()})
#     s += len(dict_basic[new_date].index)

# # 4、提取净值
# dict_nav = list()
# for i in range(6):
#     if i==0:
#         continue
#     elif i==1:
#         old_date, new_date = last1y2, pool_dates[i + 1]
#         pool_date = pool_dates[1]
#     elif i==5:
#         old_date, new_date = pool_dates[i], today_str
#         pool_date = pool_dates[i]
#     else:
#         old_date, new_date = pool_dates[i], pool_dates[i + 1]
#         pool_date = pool_dates[i]

#     set_all = set(dict_key[pool_date]['基金代码'])|set(dict_keyout[pool_date]['基金代码'])|set(dict_keyin[pool_date]['基金代码'])
#     set_all = set_all|set(dict_basic[pool_date]['基金代码'])|set(dict_basicout[pool_date]['基金代码'])|set(dict_basicin[pool_date]['基金代码'])
#     set_all = set_all|set(dict_kemai[pool_date]['基金代码'])

#     fund_list = list(set_all)
#     api_i = w.wsd(fund_list, 'NAV_adj', old_date, new_date)
#     fundNav_i = api_i.Data
#     date_i = api_i.Times
#     fundNav_np_i = np.array(fundNav_i)
#     df_new = pd.DataFrame(np.transpose(fundNav_np_i), index=list(date_i), columns=fund_list)
#     dict_nav.append({'基金池': pool_date, '时间区间':(old_date, new_date),'基金净值':df_new})

# # 5、与各基金池名单匹配，计算净值
# portPoint_df = pd.DataFrame()
# fundNavList = list()
# for temp_dict in dict_nav:
#     pool_date = temp_dict['基金池']
#     df_new = temp_dict['基金净值']
#     i_kemai = dict_kemai[pool_date]
#     i_kemai = df_new[list(i_kemai['基金代码'])]
#     i_kemai = i_kemai.median(axis=1)
#     i_kemai = i_kemai.pct_change(axis=0)
#     if temp_dict['时间区间'][0] == last1y2:
#         i_kemai.iloc[0] = 0
#     else:
#         i_kemai = i_kemai[1:]
#     fundNavList.append(i_kemai)
# fundNavList = pd.concat(fundNavList, axis=0)
# fundNavList.index = [datetime.strftime(x, "%Y-%m-%d") for x in fundNavList.index]
# portPoint_df['可买池'] = 100 * (1+fundNavList).cumprod()

# def concat_nav(dict_pool, dict_nav, begin_date):
#     fundNavList = list()
#     for temp_dict in dict_nav:
#         pool_date = temp_dict['基金池']
#         df_new = temp_dict['基金净值']

#         i_pool = dict_pool[pool_date]
#         i_pool = df_new[list(i_pool['基金代码'])]
#         i_pool = i_pool.mean(axis=1)
#         i_pool = i_pool.pct_change(axis=0)
#         if begin_date==temp_dict['时间区间'][0]:
#             i_pool.iloc[0] = 0
#         else:
#             i_pool = i_pool[1:]
#         fundNavList.append(i_pool)
#     fundNavList = pd.concat(fundNavList, axis=0)
#     fundNavList = 100 * (1 + fundNavList).cumprod()
#     fundNavList.index = [datetime.strftime(x, "%Y-%m-%d") for x in fundNavList.index]
#     return fundNavList

# portPoint_df['重点池'] = concat_nav(dict_key, dict_nav, last1y2)
# portPoint_df['重点池_每期新进'] = concat_nav(dict_keyin, dict_nav, last1y2)
# portPoint_df['重点池_汇总出池'] = concat_nav(dict_keyout, dict_nav, last1y2)
# portPoint_df['基础池'] = concat_nav(dict_basic, dict_nav, last1y2)
# portPoint_df['基础池_每期新进'] = concat_nav(dict_basicin, dict_nav, last1y2)
# portPoint_df['基础池_汇总出池'] = concat_nav(dict_basicout, dict_nav, last1y2)
# plt_portfolio_median(portPoint_df, '重点池', fpath_draw, data_date, '2-2-1 等权重基金池业绩走势跟踪-重点池', last_year='2024-12-31')
# plt_portfolio_median(portPoint_df, '基础池', fpath_draw, data_date, '2-2-2 等权重基金池业绩走势跟踪-基础池', last_year='2024-12-31')

# print('3.0 等权重全市场组合跟踪完毕')
# printtime(t)

# #%% 四、市场回顾
# standard = ['标准股票型基金(A类)','港股通标准股票型基金(A类)']  #银河分类每季度需对应更改，可通过基础池透视得到
# blend = ['港股通偏股型基金(A类)','偏股型基金(股票上下限60%-95%)(A类)','偏股型基金(股票上限80%)(A类)','偏股型基金(股票上限95%)(A类)','北交所主题偏股型基金(A类)']
# others = ['普通偏债型基金(股票上限不高于30%)(A类)','普通债券型基金(二级)(A类)','长期纯债债券型基金(A类)','QDII股票型基金(A类)',\
#            'QDII债券型基金(A类)','商品期货ETF基金','黄金ETF基金']
# rtn_summary = pd.DataFrame(columns = ['近一周','近一月','近三月','年初至今','近一年'])
# rtn_monthly = pd.DataFrame(columns = [i.strftime('%Y-%m') for i in ts])
# df1 = fund[fund['银河证券三级分类'].isin(standard)]
# rtn_summary.loc['标准股票型基金 25"'] = df1[rtn_summary.columns].quantile(0.75)
# rtn_summary.loc['标准股票型基金 50"'] = df1[rtn_summary.columns].quantile(0.5)
# rtn_summary.loc['标准股票型基金 75"'] = df1[rtn_summary.columns].quantile(0.25)
# rtn_monthly.loc['标准股票型基金'] = df1[rtn_monthly.columns].quantile(0.5)
# df2 = fund[fund['银河证券三级分类'].isin(blend)]
# rtn_summary.loc['偏股型基金 25"'] = df2[rtn_summary.columns].quantile(0.75)
# rtn_summary.loc['偏股型基金 50"'] = df2[rtn_summary.columns].quantile(0.5)
# rtn_summary.loc['偏股型基金 75"'] = df2[rtn_summary.columns].quantile(0.25)
# rtn_monthly.loc['偏股型基金'] = df2[rtn_monthly.columns].quantile(0.5)
# for i in others:
#     df3 = fund[fund['银河证券三级分类'] == i]
#     if i == '黄金ETF基金':
#         rtn_summary.loc[i[:-5]+' 25"'] = df3[rtn_summary.columns].quantile(0.75)
#         rtn_summary.loc[i[:-5]+' 50"'] = df3[rtn_summary.columns].quantile(0.5)
#         rtn_summary.loc[i[:-5]+' 75"'] = df3[rtn_summary.columns].quantile(0.25)
#         rtn_monthly.loc[i[:-5]] = df3[rtn_monthly.columns].quantile(0.5)
#     else:
#         rtn_summary.loc[i[:-4]+' 25"'] = df3[rtn_summary.columns].quantile(0.75)
#         rtn_summary.loc[i[:-4]+' 50"'] = df3[rtn_summary.columns].quantile(0.5)
#         rtn_summary.loc[i[:-4]+' 75"'] = df3[rtn_summary.columns].quantile(0.25)
#         rtn_monthly.loc[i[:-4]] = df3[rtn_monthly.columns].quantile(0.5)
# rtn_summary = rtn_summary.round(2)
# rtn_monthly = rtn_monthly.round(2)

# df_graph = pd.DataFrame()
# df_graph.loc['中证800', '近一周'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
# df_graph.loc['中证800', '近一月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
# df_graph.loc['中证800', '近三月'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0][0]
# df_graph.loc['中证800', '年初至今'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0][0]
# df_graph.loc['中证800', '近一年'] = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0][0]
# df_graph = pd.concat([df_graph, rtn_summary[rtn_summary.index.str.contains('50"')][['近一周','近一月','近三月','年初至今','近一年']]]).round(2)

# plt_monthly_return(rtn_monthly, '各类资产月度收益率('+data_date+')', fpath_draw)
# plate_exposure_line(today, data_date, fpath_draw)

# print('4.0 市场回顾完毕')
# printtime(t)


# #%% 五、收益归因
# holding = pd.read_pickle('基础数据/所有基金重仓数据_20241231-20241231.pkl')
# holding = holding[holding['报告日期']==rpt_date]

# #筛选出全A基金
# q_fund=pd.read_excel('基础数据/{0}季度筛选结果明细.xlsx'.format(pool_date[:6]),sheet_name='ag-grid',index_col=2)
# q_fund=q_fund.drop('基金代码',axis=1)
# q_fund.index.name='基金代码'  #用wind代码替换6位数字的基金代码
# A_fund=q_fund[(q_fund['资产地区II']=='境内股市') & (q_fund['管理方式VI']=='主动管理') & (~q_fund['银河证券三级分类'].str.contains('非A类'))].index.tolist()
# A_fund=[A_fund_i[:-3] for A_fund_i in A_fund]
# holding = holding[holding['基金代码'].isin(A_fund)]

# index_list = [i[0:6] for i in w.wset("sectorconstituent","date=" + rpt_date + ";sectorid=2001010102000000").Data[1]] #申万一级行业分类
# index_list2 = [i[0:6] for i in w.wset("sectorconstituent","date=" + rpt_date + ";sectorid=2001010103000000").Data[1]] #申万二级行业分类
# index_list = index_list + index_list2
# holding = holding[~holding['基金代码'].isin(index_list)]
# mf = holding.groupby('股票代码')['持股市值'].sum()
# mf = pd.DataFrame(mf[~mf.index.str.contains('HK')]).sort_values(by = '持股市值', ascending=False)
# mf['持仓占比'] = mf['持股市值'] / mf['持股市值'].sum()
# mf['简称'] = w.wss(mf.index.tolist(),"sec_name").Data[0]
# mf['行业'] = w.wss(mf.index.tolist(),"industry_citic","tradeDate="+today_str+";industryType=1").Data[0]
# mf['近一周'] = w.wss(mf.index.tolist(), "pct_chg_per","startDate=" + last1w + ";endDate=" + today_str).Data[0]
# mf['收益'] = mf['持仓占比'] * mf['近一周']

# zz800 = w.wset("indexconstituent","date=" + rpt_date + ";windcode=000906.SH")
# zz800 = pd.DataFrame(np.array(zz800.Data).T, columns = zz800.Fields)
# zz800['i_weight'] = zz800['i_weight'].astype(float) / 100
# zz800['行业'] = w.wss(zz800['wind_code'].tolist(),"industry_citic","tradeDate="+today_str+";industryType=1").Data[0]
# zz800['近一周'] = w.wss(zz800['wind_code'].tolist(), "pct_chg_per","startDate=" + last1w + ";endDate=" + today_str).Data[0]
# zz800['收益'] = zz800['i_weight'] * zz800['近一周']

# allocation = pd.DataFrame()
# allocation['公募配置'] = mf.groupby('行业')['持仓占比'].sum()
# allocation['公募收益'] = mf.groupby('行业')['收益'].sum() / allocation['公募配置']
# allocation['中证800配置'] = zz800.groupby('行业')['i_weight'].sum()
# allocation['中证800收益'] = zz800.groupby('行业')['收益'].sum() / allocation['中证800配置']
# allocation['超额收益'] = allocation['公募配置'] * allocation['公募收益'] - allocation['中证800配置'] * allocation['中证800收益']
# allocation['配置超额'] = (allocation['公募配置'] - allocation['中证800配置']) * allocation['中证800收益']
# allocation['选股超额'] = (allocation['公募收益'] - allocation['中证800收益']) * allocation['公募配置']
# allocation[['公募配置', '中证800配置']] = allocation[['公募配置', '中证800配置']] *100
# allocation['配置差额'] = allocation['公募配置'] - allocation['中证800配置']
# allocation = allocation.sort_values(by = '配置差额', ascending=False)
# #allocation.index = [x[2:] for x in list(allocation.index)]

# citic_idx = ['CI00500'+str(i)+'.WI' for i in range(1,10)] + ['CI0050'+str(i)+'.WI' for i in range(10,31)]
# citic_idx_rtn = w.wss(citic_idx, "sec_name")
# citic_idx_rtn = pd.DataFrame(index = [i[:-4] for i in citic_idx_rtn.Data[0]])
# citic_idx_rtn['近一周'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + last1w + ";endDate=" + today_str).Data[0]
# citic_idx_rtn['近一月'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + last1m + ";endDate=" + today_str).Data[0]
# citic_idx_rtn['近三月'] =  w.wss(citic_idx, "pct_chg_per","startDate=" + last3m + ";endDate=" + today_str).Data[0]
# allocation[['近一周', '近一月', '近三月']] = citic_idx_rtn[['近一周', '近一月', '近三月']]
# allocation = allocation.round(4)
# allocation.index.name='行业'

# plt_excess_return(allocation, fpath_draw, '本周超额收益来源分解('+data_date+')')
# scatter_industry(allocation, df_graph, ['近一周', '近三月'], fpath_draw, data_date)

# print('5.0 收益归因计算完毕')
# printtime(t)


# #%% 六、热门权益基金
# pop_fund = dict()
# item = ['基金简称','基金经理','近一周','近一月','近三月','年初至今','近一年']
# A_level5 = ['A股全市场策略','A股价值策略','A股小盘策略','新能源','TMT','军工','其他成长','医药','白酒','其他消费','金融','周期','其他金融周期']
# sample = fund[(fund['资产细分V'].isin(A_level5) & (fund['资产类别I']=='股票') & (fund['管理方式VI']=='主动管理'))]

# pop_fund['近一月'] = sample.sort_values(by = '近一月', ascending = False).head(100)[item].round(2)
# pop_fund['近三月'] = sample.sort_values(by = '近三月', ascending = False).head(100)[item].round(2)
# pop_fund['年初至今'] = sample.sort_values(by = '年初至今', ascending = False).head(100)[item].round(2)
# pop_seperate = list()
# for i_name,i_group in sample.groupby(['资产细分V']):
#     if len(i_group.index)<5:
#         num = len(i_group.index)
#     else:
#         num = 5
#     i_df = i_group.sort_values(by = '年初至今', ascending = False).head(num)[item].round(2)
#     i_df['资产细分V'] = i_name[0]
#     pop_seperate.append(i_df)
# pop_fund['分类热门'] = pd.concat(pop_seperate, axis=0)

# print('6.0 热门权益基金统计完毕')
# printtime(t)


# # %%
# #%% 七、基金公司收益
# amc_rank = dict()
# amc_rank['1区域收益'] = pd.DataFrame()
# for i in ['北京','上海','广东']:
#     df5 = fund[(fund['办公地址'].str.contains(i)) & (fund['资产细分V'].isin(A_level5)) & (fund['资产类别I']=='股票')&((fund['银河证券二级分类'].str.contains('指数')==False) & (fund['银河证券二级分类'].str.contains('ETF')==False))]
#     amc_rank['1区域收益'].loc[i, '产品数量'] = df5['年初至今'].count()
#     amc_rank['1区域收益'].loc[i, '25分位'] = df5['年初至今'].quantile(0.75)
#     amc_rank['1区域收益'].loc[i, '50分位'] = df5['年初至今'].quantile(0.5)
#     amc_rank['1区域收益'].loc[i, '75分位'] = df5['年初至今'].quantile(0.25)
#     amc_rank['1区域收益'].loc[i, '规模加权'] = (df5['基金规模'] * df5['年初至今']).sum() / df5['基金规模'].sum()

# amc_rank['2权益基金'] = pd.DataFrame()

# df4 = (fund[fund['资产细分V'].isin(A_level5) & (fund['资产类别I']=='股票') & (fund['管理方式VI']=='主动管理')]).dropna(subset=['基金管理人'])
# df4['基金管理人'] = [s[:-2] for s in list(df4['基金管理人'])]

# df4_save=df4[['基金简称','资产细分V','基金经理','基金成立日期','基金规模','年初至今','my是否基础池']].sort_values(by=['资产细分V','基金简称','年初至今'],ascending=False)

# amc_rank['2权益基金']['产品数量'] = df4.groupby('基金管理人')['基金规模'].count()
# amc_rank['2权益基金']['产品规模'] = df4.groupby('基金管理人')['基金规模'].sum() / 1e8
# amc_rank['2权益基金']['中位数收益'] = df4.groupby('基金管理人')['年初至今'].median()
# amc_rank['2权益基金']['规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
# amc_rank['2权益基金']['去年中位数收益'] = df4.groupby('基金管理人')['去年'].median()
# amc_rank['2权益基金']['去年规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
# amc_rank['2权益基金'] = amc_rank['2权益基金'].sort_values(by = '产品规模', ascending=False).round(2)

# i = 'A股全市场基金'
# df6 = (fund[(fund['资产板块IV'] == i)& (fund['管理方式VI']=='主动管理')]).dropna(subset=['基金管理人'])
# df6['基金管理人'] = [s[:-2] for s in list(df6['基金管理人'])]
# amc_rank[i] = pd.DataFrame()
# amc_rank[i]['产品数量'] = df6.groupby('基金管理人')['基金规模'].count()
# amc_rank[i]['产品规模'] = df6.groupby('基金管理人')['基金规模'].sum() / 1e8
# amc_rank[i]['ytd_中位数收益'] = df6.groupby('基金管理人')['年初至今'].median()
# amc_rank[i]['近一周_中位数收益'] = df6.groupby('基金管理人')['近一周'].median()
# amc_rank[i]['规模加权收益'] = df6.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
# amc_rank[i]['去年中位数收益'] = df6.groupby('基金管理人')['去年'].median()
# amc_rank[i]['去年规模加权收益'] = df6.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
# amc_rank[i] = amc_rank[i].sort_values(by = '产品规模', ascending=False).round(2)

# for i in A_level5:
#     df4 = fund[(fund['资产细分V']==i)&(fund['管理方式VI']=='主动管理')].dropna(subset=['基金管理人'])
#     df4['基金管理人'] = [s[:-2] for s in list(df4['基金管理人'])]
#     amc_rank[i] = pd.DataFrame()
#     amc_rank[i]['产品数量'] = df4.groupby('基金管理人')['基金规模'].count()
#     amc_rank[i]['产品规模'] = df4.groupby('基金管理人')['基金规模'].sum() / 1e8
#     amc_rank[i]['ytd_中位数收益'] = df4.groupby('基金管理人')['年初至今'].median()
#     amc_rank[i]['近一周_中位数收益'] = df4.groupby('基金管理人')['近一周'].median()
#     amc_rank[i]['规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
#     amc_rank[i]['去年中位数收益'] = df4.groupby('基金管理人')['去年'].median()
#     amc_rank[i]['去年规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
#     amc_rank[i] = amc_rank[i].sort_values(by = '产品规模', ascending=False).round(2)

# for j in ['A股消费行业', 'A股医药行业', 'A股成长行业', 'A股金融周期行业']:
#     df4 = fund[(fund['资产板块IV'] == j)&(fund['管理方式VI']=='主动管理')].dropna(subset=['基金管理人'])
#     df4['基金管理人'] = [s[:-2] for s in list(df4['基金管理人'])]
#     amc_rank['板块-'+j] = pd.DataFrame()
#     amc_rank['板块-'+j]['产品数量'] = df4.groupby('基金管理人')['基金规模'].count()
#     amc_rank['板块-'+j]['产品规模'] = df4.groupby('基金管理人')['基金规模'].sum() / 1e8
#     amc_rank['板块-'+j]['ytd_中位数收益'] = df4.groupby('基金管理人')['年初至今'].median()
#     amc_rank['板块-'+j]['近一周_中位数收益'] = df4.groupby('基金管理人')['近一周'].median()
#     amc_rank['板块-'+j]['规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['基金规模'] * x['年初至今']).sum() / x['基金规模'].sum())
#     amc_rank['板块-'+j]['去年中位数收益'] = df4.groupby('基金管理人')['去年'].median()
#     amc_rank['板块-'+j]['去年规模加权收益'] = df4.groupby('基金管理人').apply(lambda x: (x['去年基金规模'] * x['去年']).sum() / x.loc[~x['去年'].isna(),'去年基金规模'].sum())
#     amc_rank['板块-'+j] = amc_rank['板块-'+j].sort_values(by = '产品规模', ascending=False).round(2)

# scatter_manager(amc_rank['2权益基金'],'1-3-1 各公司权益基金',fpath_draw, data_date)
# scatter_manager(amc_rank['A股全市场策略'],'1-3-2 各公司全市场策略基金',fpath_draw, data_date)
# group1 = ['A股全市场策略','A股价值策略','A股小盘策略','金融','周期','其他金融周期']
# group2 = ['白酒','其他消费','医药','TMT','新能源','军工','其他成长']
# group3 = ['A股全市场基金', '板块-A股金融周期行业', '板块-A股成长行业', '板块-A股医药行业','板块-A股消费行业']
# table_amc(amc_rank, group1, '1-3-4 各公司-全市场&金融周期', fpath_draw, data_date)
# table_amc(amc_rank, group2, '1-3-5 各公司-成长&医药&消费', fpath_draw, data_date)
# table_amc(amc_rank, group3, '1-3-3 各公司-四级资产板块', fpath_draw, data_date)

# print('7.0 基金公司收益计算完毕')
# printtime(t)

# %%
#%% 八、FOF基金
try:
    url = 'http://192.168.105.63/GetFndLevelClassify'
    kw = {'username':'gyrx','passwd':'4E4AB38D17E274B0D2D6A846AE4393E7'}
    http = urllib3.PoolManager()
    r = http.request('post', url, body = dumps(kw))
    data_yh = pd.DataFrame(loads(r.data.decode())['data'])
    data_yh.index = [x+'.OF' for x in data_yh['FUNDCODE']]
    data_yh['FUNDCLOSEDATE'] = [dt.datetime.strptime(x[0:10],datestyle) for x in data_yh['FUNDCLOSEDATE']]
    data_yh['ESTABDATE'] = [dt.datetime.strptime(x[0:10],datestyle) for x in data_yh['ESTABDATE']]
    data_yh = data_yh[(data_yh['FUNDCLOSEDATE']>dt.datetime.strptime(enddateM,datestyle))
                      &(data_yh['ESTABDATE']<=dt.datetime.strptime(enddateM,datestyle))
                      &(~data_yh['LEVEL3NAME'].str.contains('互认'))]
    data_yh = data_yh.loc[:,['FUNDSNAME','LEVEL1NAME','LEVEL2NAME','LEVEL3NAME']]
    data_yh.columns = ['银河简称','银河证券一级分类','银河证券二级分类','银河证券三级分类']
    writer_YH=pd.ExcelWriter(os.path.join('/','Users','bj','Documents','Python','Code','FS','Label_YH.xlsx'),engine='xlsxwriter')
    data_yh.to_excel(writer_YH,sheet_name='银河基金数据')
    excel_format(writer_YH)
except Exception:
    data_yh = pd.read_excel(os.path.join('/','Users','bj','Documents','Python','Code','FS','Label_YH.xlsx'),sheet_name='银河基金数据',index_col=0)
fof_class = data_yh[data_yh['银河证券三级分类'].str.contains('FOF')]

fof_index = ['CI005917.WI','CI005918.WI','CI005919.WI','CI005920.WI','CI005921.WI','HSI.HI','SPX.GI']
fof_index_name = ['金融','周期','消费','成长','稳定','恒生指数','标普500']

last6m = (today + relativedelta(months=-6) + relativedelta(days=1))
fof_list = w.wset("sectorconstituent","date=" + today_str + ";sectorid=1000041489000000")

# 检查fof_list是否有效
if not fof_list or not hasattr(fof_list, 'Data') or len(fof_list.Data) < 3 or not fof_list.Data[1]:
    print("警告: 无法获取FOF基金列表或列表为空")
    # 创建一个空的DataFrame，避免后续代码出错
    fof_fund = pd.DataFrame(columns=['代码', '简称', '全称', '是否初始基金', '成立日', '银河证券三级分类'])
else:
    # 创建FOF基金DataFrame
    try:
        print(f"获取到 {len(fof_list.Data[1])} 个FOF基金")
        fof_fund = pd.DataFrame({
            '代码': fof_list.Data[1],
            '简称': fof_list.Data[2],
            '全称': w.wss(fof_list.Data[1], "fund_fullname").Data[0],
            '是否初始基金': w.wss(fof_list.Data[1], "fund_initial").Data[0],
            '成立日': w.wss(fof_list.Data[1], "fund_setupdate").Data[0]
        }).set_index('全称')

        # 只选取今年以前成立的基金
        fof_fund = fof_fund[(fof_fund['是否初始基金'] == '是') & (fof_fund['成立日'] < ytd)]
        print(f"筛选后剩余 {len(fof_fund)} 个FOF基金")

        # 重置索引并设置代码为索引
        fof_fund = fof_fund.reset_index().set_index('代码')

        # 添加银河证券三级分类
        if '银河证券三级分类' in fof_class.columns:
            fof_fund = fof_fund.join(fof_class['银河证券三级分类'])
            # 过滤掉非A类基金
            fof_fund = fof_fund[~fof_fund['银河证券三级分类'].str.contains('非A类', na=False)]
            print(f"加入分类后剩余 {len(fof_fund)} 个FOF基金")
        else:
            print("警告: fof_class中没有'银河证券三级分类'列")
            fof_fund['银河证券三级分类'] = ''
    except Exception as e:
        print(f"处理FOF基金数据时出错: {str(e)}")
        # 创建一个空的DataFrame，避免后续代码出错
        fof_fund = pd.DataFrame(columns=['代码', '简称', '全称', '是否初始基金', '成立日', '银河证券三级分类'])

# 检查fof_fund是否为空
if fof_fund.empty:
    print("警告: FOF基金列表为空，跳过FOF图表生成")
    fof_all = pd.DataFrame()
    fof = dict()
else:
    # 生成FOF图表
    try:
        print(f"开始生成FOF图表，基金数量: {len(fof_fund)}")
        fof_all = ct.fof_chart(fof_fund.index.to_list(), today_str, last1w, last1m, last3m, ytd, last1y, fof_index, fof_index_name)

        # 检查fof_all是否为空
        if fof_all.empty:
            print("警告: FOF图表生成结果为空")
            fof = dict()
        else:
            # 按分类处理FOF基金
            fof = dict()
            class_list = ['股票型FOF(A类)', '混合型FOF(权益资产60%-95%)(A类)', '养老目标日期FOF(2050)(A类)',
                         '养老目标日期FOF(2055)(A类)', '养老目标日期FOF(2060)(A类)', '养老目标风险FOF(权益资产60%-80%)(A类)']

            for i in class_list:
                try:
                    # 筛选特定分类的基金
                    temp = fof_fund[fof_fund['银河证券三级分类'] == i]
                    if temp.empty:
                        print(f"警告: 没有找到分类为 '{i}' 的基金")
                        continue

                    # 获取工银基金和非工银基金
                    temp_gongyin = temp[temp['简称'].str.contains('工银', na=False)]
                    temp_not_gongyin = temp[~temp['简称'].str.contains('工银', na=False)]

                    # 检查这些基金是否在fof_all中
                    gongyin_in_all = [code for code in temp_gongyin.index if code in fof_all.index]
                    not_gongyin_in_all = [code for code in temp_not_gongyin.index if code in fof_all.index]

                    if not gongyin_in_all and not not_gongyin_in_all:
                        print(f"警告: 分类 '{i}' 的基金都不在fof_all中")
                        continue

                    # 合并工银基金和非工银基金（按YTD收益排序）
                    df_gongyin = fof_all.loc[gongyin_in_all] if gongyin_in_all else pd.DataFrame()
                    df_not_gongyin = fof_all.loc[not_gongyin_in_all].sort_values(by='收益_YTD', ascending=False) if not_gongyin_in_all else pd.DataFrame()

                    temp_result = pd.concat([df_gongyin, df_not_gongyin])
                    if not temp_result.empty:
                        temp_result = ct.add_quantile(temp_result)
                        fof[i[:-4]] = temp_result
                except Exception as e:
                    print(f"处理分类 '{i}' 时出错: {str(e)}")
    except Exception as e:
        print(f"生成FOF图表时出错: {str(e)}")
        fof_all = pd.DataFrame()
        fof = dict()

# 检查fof字典是否为空
if not fof:
    print("警告: 没有有效的FOF分类数据，跳过后续处理")
else:
    # 处理每个分类的FOF基金
    ngroup = 40
    for i in range(len(class_list)):
        class_name = class_list[i][:-4]
        if class_name not in fof:
            print(f"警告: 分类 '{class_name}' 不在fof字典中")
            continue

        try:
            df = fof[class_name]
            if len(df) < 3:
                print(f"警告: 分类 '{class_name}' 的基金数量少于3个，跳过")
                continue

            df1, df2 = df.iloc[:-3].sort_values('估算仓位', ascending=False), df.iloc[-3:]
            df = pd.concat([df1, df2], axis=0)
            df.iloc[-3:, 0] = df.index[-3:]
            for m in [-3, -2, -1]:
                df.iloc[m, 2] = df.iloc[:-3, 2].quantile(-0.25 * m).round(2)
                df.iloc[m, 3] = df.iloc[:-3, 3].quantile(-0.25 * m).round(2)
                df.iloc[m, 4] = df.iloc[:-3, 4].quantile(-0.25 * m).round(2)

            # 计算相关性
            if '收益_1W' in df.columns and '收益_YTD' in df.columns and len(df.iloc[:-3]) > 1:
                rho = df.iloc[:-3][['收益_1W', '收益_YTD']].corr('spearman').iloc[0, 1]
            else:
                rho = 0

            nrows = len(df.index)
            df_color = color_map(df, ['估算仓位', 'R^2', '收益_1W', '超额收益_1W', '收益_1M', '超额收益_1M', '收益_3M',
                               '超额收益_3M', '最大回撤_3M', '收益_YTD', '超额收益_YTD', '最大回撤_YTD', '收益_1Y','超额收益_1Y', '最大回撤_1Y'])
            df['简称'] = [cut_name(s, 7) for s in list(df['简称'])]

            if nrows <= 40:
                red_green_table(df, df_color, class_list[i] + '(' + fof_date + ')(相关性=' + str(round(rho, 2)) + ')',
                            '1-4 FOF ' + class_name, fpath_draw, True)
            else:
                num_splits = (len(df) // ngroup) + 1
                for j in range(num_splits):
                    start_index = j * ngroup
                    end_index = min((j + 1) * ngroup, len(df.index))
                    subset_df = df.iloc[start_index: end_index]
                    subset_df_color = df_color.iloc[start_index: end_index]
                    add_line = (j == num_splits - 1)
                    red_green_table(subset_df, subset_df_color, class_name+'('+fof_date+')(相关性='+str(round(rho, 2))+')-'+str(j+1),
                                '1-4 FOF '+class_name+str(j+1), fpath_draw, add_line)
        except Exception as e:
            print(f"处理分类 '{class_name}' 的图表时出错: {str(e)}")

print('8.0 FOF基金回顾计算完毕')
printtime(t)

# %%

#%% 九、权益基金池回顾
fund_col = {'收益_1W':1, '排名_1W':-1, '收益_1M':1, '排名_1M':-1, '收益_3M':1,
            '排名_3M':-1, '收益_YTD':1, '排名_YTD':-1, '收益_1Y':1, '排名_1Y':-1}
benchmark = {'A股全市场策略':'000906.SH','A股价值策略':'399371.SZ','A股小盘策略':'399852.SZ',
             '新能源':'399808.SZ','TMT':'000998.CSI','军工':'399967.SZ','其他成长':'CI005920.WI',
             '医药':'000933.SH','白酒':'399997.SZ','其他消费':'000932.SH',
             '金融':'000934.SH','周期':'931355.CSI','其他金融周期':'931512.CSI',
             '偏股混合型基金指数':'885001.WI',
             '恒生指数':'HSI.HI', '恒生科技':'HSTECH.HI',
             '标普500':'SPX.GI', '纳斯达克':'IXIC.GI'}  #'工银股混':'930994.CSI',
level_4to5 = {'A股全市场基金':['A股全市场策略','A股价值策略','A股小盘策略'],
              'A股成长行业':['TMT','新能源','军工','其他成长'],
               'A股消费行业':['白酒','其他消费'],
               'A股医药行业':['医药'],
               'A股金融周期行业':['金融','周期','其他金融周期']}
core_dict = {'A股全市场策略':['000893.OF','008515.OF','000991.OF','001718.OF','481008.OF','001714.OF','000763.OF','002350.OF','008269.OF','166301.OF','002871.OF','002258.OF','005443.OF','519702.OF','008261.OF'], #'540003.OF',
             'A股价值策略':['000628.OF',],
             'A股小盘策略':['001917.OF',], #'007130.OF'
             '新能源':[],
             'TMT':['004616.OF', '001167.OF','000404.OF'],
             '军工':[],
             '其他成长':['003567.OF','213003.OF'],
             '医药':['001717.OF', '000831.OF', '006002.OF', '000727.OF','001230.OF'],
             '白酒':[],
             '其他消费':['481013.OF', '519915.OF', '009476.OF', '000083.OF'],
             '金融':['005576.OF'],
             '周期':[],
             '其他金融周期':[]}
core_sys = all_df[(all_df['公募FOF基金池级别']=='核心') & (all_df['资产地区II']=='境内股市') & (all_df['管理方式VI']=='主动管理') & (~all_df['银河证券三级分类'].str.contains('非A类'))]
for i in core_sys.index:
    if i not in core_dict[core_sys.loc[i,'资产细分V']]:
        core_dict[core_sys.loc[i,'资产细分V']].append(i)
#准备一：生成各个基准的收益率
index_fund = pd.DataFrame(index=list(benchmark.values()), columns=['收益_1W','收益_1M','收益_3M','收益_YTD','收益_1Y'])
index_fund['收益_1W'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0]
index_fund['收益_1M'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0]
index_fund['收益_3M'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last3m + ";endDate=" + today_str).Data[0]
index_fund['收益_YTD'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + ytd + ";endDate=" + today_str).Data[0]
index_fund['收益_1Y'] = w.wss(index_fund.index.tolist(), "pct_chg_per", "startDate=" + last1y + ";endDate=" + today_str).Data[0]
index_fund['指数'] = ['中证800','国证价值','中证1000',
                    '中证新能','中证TMT','中证军工','中证成长风格',
                    '中证医药','中证白酒','中证消费',
                    '800金地','中证周期100','中证周期50',
                    '偏股混合型基金指数',
                    '恒生指数', '恒生科技', '标普500', '纳斯达克']


fund_rank = fund[(fund['资产类别I']=='股票')& (fund['管理方式VI']=='主动管理')]
fund_rank_A = fund_rank[(fund_rank['资产地区II']=='境内股市') ]
core_pool, key_pool, fund_pool = dict(), dict(), dict()

#%% 生成汇总表格
pptsize(209)
df_huizong = pd.DataFrame(index=list(benchmark.keys())[:13], columns=['收益(排名)','近一周','近一月','近三月','年初至今','近一年'])
df_huizong['收益(排名)'] = df_huizong.index
df_paiming = pd.DataFrame(index=df_huizong.index, columns=df_huizong.columns)
df_shouyi = pd.DataFrame(index=df_huizong.index, columns=df_huizong.columns)
for label in df_huizong.index:
    fund_i = fund_rank_A[(fund_rank_A['资产细分V']==label) & (fund_rank_A['公募FOF基金池级别']!='禁买')]
    for period in ['近一周','近一月','近三月','年初至今','近一年']:
        pool_performance = fund_i[period].tolist()
        ret = fund_i[fund_i['公募FOF基金池级别']=='重点'][period].median()
        ret_rank = len(list(filter(lambda x: x>ret, pool_performance))) / len(pool_performance)
        df_huizong.loc[label,period] = "{:.2f}({:.2f})".format(ret, ret_rank)
        df_paiming.loc[label,period] = round(ret_rank,2)
        df_shouyi.loc[label,period] = round(ret,2)

for label in df_huizong.index:
    for period in ['近一周','近一月','近三月','年初至今','近一年']:
        if str(df_shouyi.loc[label,period]) == 'nan':
            df_shouyi.loc[label,period] = None
            df_paiming.loc[label,period] = None
            df_huizong.loc[label,period] = None

huizong_table(df_huizong, df_shouyi, df_paiming, '权益重点池收益排名汇总('+today_str+')','2-3-0 汇总重点池', fpath_draw,)

# 以下提取顺序依次为：
#全A核心池、全A内部基础池、赛道内部基础池、全A外部基础池、V级资产细分的基础池、全A外部重点池、V级资产细分的重点池、港股重点池、QD重点池
core_pool['A股全市场策略'] = ct.fund_chart(core_dict['A股全市场策略'], 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
core_pool['A股价值策略'] = ct.fund_chart(core_dict['A股价值策略'], 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
core_pool['A股小盘策略'] = ct.fund_chart(core_dict['A股小盘策略'], 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)

icbccs_list1 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股全市场策略')&(fund['基金简称'].str.contains('工银'))].index.tolist()
icbccs_list2 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股价值策略')&(fund['基金简称'].str.contains('工银'))].index.tolist()
icbccs_list3 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股小盘策略')&(fund['基金简称'].str.contains('工银'))].index.tolist()
if len(icbccs_list1)>0:
    icbccs_df1 = (ct.fund_chart(icbccs_list1, 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)).iloc[:-3]
else:
    icbccs_df1 = pd.DataFrame()
if len(icbccs_list2)>0:
    icbccs_df2 = (ct.fund_chart(icbccs_list2, 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)).iloc[:-3]
else:
    icbccs_df2 = pd.DataFrame()
if len(icbccs_list3)>0:
    icbccs_df3 = (ct.fund_chart(icbccs_list3, 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)).iloc[:-3]
else:
    icbccs_df3 = pd.DataFrame()
fund_pool['全A内部'] = pd.concat([icbccs_df1,icbccs_df2,icbccs_df3], axis=0)

gyrx_lst = list()
for u in list(level_4to5.keys()):
    for i in level_4to5[u]:
        gyrx_pool = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']==i)&(fund['基金简称'].str.contains('工银'))].index.tolist()
        if len(gyrx_pool)>0:
            gyrx_lst.append(ct.fund_chart(gyrx_pool, i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A).iloc[:-3])
if len(gyrx_lst)>0:
    fund_pool['赛道内部'] = pd.concat(gyrx_lst, axis=0)

out_list1 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股全市场策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
out_list2 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股价值策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
out_list3 = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']=='A股小盘策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
out_df1 = ct.fund_chart(out_list1, 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
out_df2 = ct.fund_chart(out_list2, 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
out_df3 = ct.fund_chart(out_list3, 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
fund_pool['全A外部'] = pd.concat([out_df1.iloc[:-3],out_df2.iloc[:-3],out_df3.iloc[:-3]], axis=0)

for u in list(level_4to5.keys()):
    for i in level_4to5[u]:
        pool = fund[(fund_rank_A['my是否基础池']=='是')&(fund['资产细分V']==i)&(fund['资产地区II']!='香港股市')].index.tolist()
        if len(pool)>0:
            fund_pool[i] = ct.fund_chart(pool, i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
            fund_pool[i]['资产板块IV'] = u


#%%
key_out_list1 = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']=='A股全市场策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
key_out_df1 = ct.fund_chart(key_out_list1, 'A股全市场策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
key_out_list2 = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']=='A股价值策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
key_out_df2 = ct.fund_chart(key_out_list2, 'A股价值策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
key_out_list3 = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']=='A股小盘策略')&(~fund['基金简称'].str.contains('工银'))].index.tolist()
key_out_df3 = ct.fund_chart(key_out_list3, 'A股小盘策略', '资产细分V',benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
key_pool['全A外部'] = pd.concat([key_out_df1.iloc[:-3],key_out_df2.iloc[:-3],key_out_df3.iloc[:-3]], axis=0)

for u in list(level_4to5.keys()):
    for i in level_4to5[u]:
        pool = fund[(fund_rank_A['公募FOF基金池级别']=='重点')&(fund['资产细分V']==i)&(fund['资产地区II']!='香港股市')].index.tolist()
        if len(pool)>0:
            key_pool[i] = ct.fund_chart(pool, i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
            key_pool[i]['资产板块IV'] = u
        if len(core_dict[i])>0:
            core_pool[i] = ct.fund_chart(core_dict[i], i, '资产细分V', benchmark, today_str, last3m, last6m, last1y, fund_rank_A)
            core_pool[i]['资产板块IV'] = u

#%%
qdahk_fund_rank = fund[(fund['资产地区II'].isin(['美国股市','欧洲股市','日本股市','越南股市','印度股市','全球股市','香港股市'])==True) & (fund['银河证券二级分类'].str.contains('债券')==False) & ((fund['银河证券二级分类'].str.contains('指数')==False) & (fund['银河证券二级分类'].str.contains('ETF')==False) & (fund['基金简称'].str.contains('指数')==False) & (fund['基金简称'].str.contains('ETF')==False)) & (fund['银河证券三级分类'].str.contains('非A类')==False)]
hkf_list = qdahk_fund_rank[(qdahk_fund_rank['公募FOF基金池级别'] == '重点')&(qdahk_fund_rank['资产地区II'] == '香港股市')].index.tolist()
fund_pool['QD港股'] = ct.fund_chart2(hkf_list, '香港股市', '资产地区II',qdahk_fund_rank)
qd_list = qdahk_fund_rank[(qdahk_fund_rank['公募FOF基金池级别'] == '重点')&(qdahk_fund_rank['资产地区II']!='境内股市')&(qdahk_fund_rank['资产地区II']!='香港股市')].index.tolist()
fund_pool['QD海外'] = ct.fund_chart2(qd_list, ['全球股市','美国股市','日本股市','越南股市','欧洲股市','印度股市'], '资产地区II', qdahk_fund_rank)

print('9.0 基金池数据提取完毕')
printtime(t)

#%% 十、热门股基未入池原因
A_level = ['A股全市场策略','A股价值策略','A股小盘策略','新能源','TMT','军工','其他成长','医药','白酒','其他消费','金融','周期','其他金融周期']
#合并能力指标
df_list = list()
for i in A_level:
    i_df = pd.read_excel(get_base_data_path('{0}月度筛选结果明细.xlsx'.format(pool_date[:6])), sheet_name='ag-grid', index_col=2)
    i_df = i_df.drop('基金代码',axis=1)
    i_df.index.name = '基金代码'
    i_df = i_df[i_df['资产细分V']==i]
    i_df = i_df[(~i_df['银河证券三级分类'].str.contains('非A类')) & (i_df['管理方式VI']=='主动管理')]
    if i!='A股价值策略':
        i_df['日期'] = pool_date
        i_df = i_df[['日期','P+_1Y','P-_1Y','PRD_1Y','Calmar比率_1Y','最大回撤_1Y','α波动率_1Y','未入基础池原因']]
    else:
        i_df['日期'] = pool_date
        i_df = i_df[['日期', '汇总P+', 'P-_1Y', 'PRD_1Y', 'Calmar比率_1Y', '最大回撤_1Y', 'α波动率_1Y','未入基础池原因']]
    i_df.columns = ['日期', 'P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率','当月未入基础池原因']
    i_df['标签'] = i
    print('正在看',i)
    df_list.append(i_df)
df_evaluate = pd.concat(df_list, axis=0)
df_evaluate.rename(columns={'未入基础池原因': '当月未入基础池原因'}, inplace=True)
basic_df = copy.deepcopy(df4_save)
for period in ['近一月', '近三月', '年初至今', '分类热门']:
    x_df = pop_fund[period]
    reason_df = pd.merge(x_df, df_evaluate[df_evaluate['标签'].isin(A_level)][['标签', 'P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']], left_index=True, right_index=True,
                    how='left')
    # reason_df = pd.merge(x_df, df_evaluate[['P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']], left_index=True, right_index=True,
    #                 how='left')
    reason_df = pd.merge(reason_df, basic_df[['基金经理', '基金成立日期', 'my是否基础池']], left_index=True, right_index=True,how='left')
    reason_df = pd.merge(reason_df, all_df['公募FOF基金池级别'], left_index=True, right_index=True, how='left')
    reason_df.loc[reason_df['my是否基础池']=='是','当月未入基础池原因'] = '\\'
    reason_df = reason_df[['基金简称', '基金经理_x', '标签', '近一周', '近一月', '近三月', '年初至今', '近一年', '公募FOF基金池级别', 'P+', 'P-',
                        'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']]
    reason_df.columns = ['基金简称', '基金经理', '分类', '近一周', '近一月', '近三月', '年初至今', '近一年', '基金池级别',
                         'P+', 'P-', 'PRD', 'Calmar比率', '最大回撤', 'α波动率', '当月未入基础池原因']

    if period != '分类热门':
        reason_df = reason_df.round(2).sort_values(period, ascending=False).iloc[:25]
        reason_table(reason_df, period, '热门基金-' + period, '2-4 热门基金 ' + period, fpath_draw)
        pop_fund['未入池原因0' + period] = reason_df
    else:
        reason_df = reason_df.round(2)
        pop_fund['未入池原因0' + period] = reason_df
        df1 = reason_df[reason_df['分类'].str.contains('|'.join(['A股全市场策略', 'A股价值策略', 'A股小盘策略']))]
        df2 = reason_df[reason_df['分类'].str.contains('|'.join(['新能源', 'TMT', '军工', '其他成长']))]
        df3 = reason_df[reason_df['分类'].str.contains('|'.join(['医药', '白酒', '其他消费', ]))]
        df4 = reason_df[reason_df['分类'].str.contains('|'.join(['金融', '周期', '其他金融周期']))]
        reason_table(df1, period, '热门基金-全市场-' + period, '2-4 热门局部1 ' + period, fpath_draw, [5, 10])
        reason_table(df2, period, '热门基金-成长-' + period, '2-4 热门局部2 ' + period, fpath_draw, [5, 10, 15])
        reason_table(df3, period, '热门基金-医药&消费-' + period, '2-4 热门局部3 ' + period, fpath_draw, [2, 7])
        reason_table(df4, period, '热门基金-金融周期-' + period, '2-4 热门局部4 ' + period, fpath_draw, [5, 10])

print('10.0 分析绩优基金未入池原因完毕')
printtime(t)



#%% 十一、总结与导出
a_summary = dict({'0 各类别收益汇总':df_huizong, '1 收益回顾':df_graph, '2 基金分位':rtn_summary, '2 月度收益':rtn_monthly, #'2 行业基金各池子收益':dict_graph,
                  '3 收益归因':allocation, '4 热门基金':pop_fund,
                   '4 基金公司收益':amc_rank, '5 权益核心池':core_pool, '6 权益基础池':fund_pool, '7 权益重点池':key_pool, '8 主动基金评价指标':df_evaluate, '9 FOF基金':fof,
                  '10 等权重全市场组合净值':portPoint_df})

fund_all=pd.DataFrame()
for i in core_pool:
    fund_all=pd.concat([fund_all,core_pool[i][:-3]],axis=0)
for i in fund_pool:
    fund_all=pd.concat([fund_all,fund_pool[i][:-3]],axis=0)

with pd.ExcelWriter('../周报文件/周报程序数据导出 -zmh.xlsx') as writer:
    for i in a_summary:
        if isinstance(a_summary[i],dict)==False:
            a_summary[i].to_excel(writer,sheet_name=i)
        else:
            for j in a_summary[i]:
                a_summary[i][j].to_excel(writer,sheet_name=i+' '+j)
    df4_save.to_excel(writer,sheet_name='基金池汇总')

print('11.0 数据导出excel完毕')
printtime(t)

#%% 【画图】1-1市场总体表现-左图右表
df_percentile = copy.deepcopy(rtn_summary)
temp = np.array(df_percentile.index.str.split(' ', expand = True))
df_percentile['银河分类'] = [s[0] for s in temp]
df_percentile['分位'] = [s[1] for s in temp]

index_1w = w.wss("000906.SH", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
bond_1w = w.wss("CBA00101.CS", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_market = df_percentile[df_percentile['分位'] == '50"']
df_market.set_index(['银河分类'], inplace = True)
df_market.loc['工银配置', '近一周'] = w.wss("930995.CSI", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_market.loc['工银股混', '近一周'] = w.wss("930994.CSI", "pct_chg_per", "startDate=" + last1w + ";endDate=" + today_str).Data[0][0]
df_market.loc['工银配置', '近一月'] = w.wss("930995.CSI", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_market.loc['工银股混', '近一月'] = w.wss("930994.CSI", "pct_chg_per", "startDate=" + last1m + ";endDate=" + today_str).Data[0][0]
df_market.loc['标准股票型基金','近一周超额'] = df_market.loc['标准股票型基金','近一周'] - index_1w
df_market.loc['偏股型基金','近一周超额'] = df_market.loc['偏股型基金','近一周'] - index_1w
df_market.loc['长期纯债债券型基金','近一周超额'] = df_market.loc['长期纯债债券型基金','近一周'] - bond_1w
df_market = df_market[['近一周','近一月','近一周超额']].reindex(df_market.index[::-1])

df_percentile.index=df_percentile['银河分类']
df_percentile = df_percentile[['分位','近一周', '近一月', '近三月', '年初至今', '近一年']]

fig,ax=plt.subplots(nrows=1,ncols=2,sharex=False)
#画左图
data = df_market.values.T
fund_type = df_market.index.tolist()
x = [i*1.3 for i in range(len(fund_type))]
width = 0.3
rects1 = ax[0].barh(x, data[0], width, label='近一周', alpha=light)
rects2 = ax[0].barh([i + width for i in x], data[1], width, label='近一月', alpha=light)
def autolabel(rects):
    """在每个柱状图右侧添加数据标签"""
    for rect in rects:
        width = rect.get_width()
        ax[0].annotate('{:.2f}'.format(width),
                    xy=(width, rect.get_y() + rect.get_height() / 2),  # 将xy位置设置为柱状图右侧顶部
                    xytext=(5, -7),    # 调整标签位置，使其稍微偏离柱形
                    textcoords="offset points",
                    ha='left', va='center', fontsize=fontsize_text)  # 水平对齐方式设为左对齐

autolabel(rects1)
autolabel(rects2)
ax[0].set_yticks([i + 2 * width for i in x])
ax[0].set_yticklabels(['\n'.join(wrap(label, width=6)) for label in fund_type], rotation=0, fontsize=fontsize_legend)
ax[0].xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}' if x >= 0 else f'-{abs(x):.2f}'))
ax[0].legend(loc='upper center', bbox_to_anchor=(0.5, 1.05), ncol=5, fontsize=fontsize_legend)

#画右图
ax[1].axis('off')
colortable = copy.deepcopy(df_percentile)
for aa in list(range(len(colortable.index))):
    if aa in list(range(1, len(colortable.index), 2)):
        colortable.loc[colortable.index[aa]] = 'lightgray'
    else:
        colortable.loc[colortable.index[aa]] = 'white'

colColours = ['lightsteelblue'] * len(colortable.columns)
df_percentile.fillna(' ', inplace=True)
table = ax[1].table(cellText=df_percentile.values,
                 colLabels=df_percentile.columns,
                 rowLabels=df_percentile.index,
                 bbox=(0, 0, 1, 1),
                 cellLoc='center',
                 loc='center',
                 cellColours=colortable.values,
                 colColours=colColours,
                 rowColours = colortable.iloc[:, 0].values
                 )
# 设置表格样式
table.auto_set_font_size(False)
for (row, col), cell in table.get_celld().items():
    cell.set_fontsize(fontsize_text)
    if ('-' in str(df_percentile.iloc[row - 1, col])) and (row >= 1 and col >= 1):
        cell.get_text().set_color('red')
    if (row == 0) | (col == 0):
        cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_legend))

fig.suptitle('市场总览('+data_date+')',fontsize=fontsize_suptitle,fontweight='bold')
fig.tight_layout()
fig.savefig('图表/1-1-1 市场总览')
plt.close()
print('12.0 补充绘图-市场总览组合表格完毕')
printtime(t)

#【各类基金池画图】
# %% 绘制表格：重点池&核心池-非全市场的各个子类别
ngroup = 42
for i in list(benchmark.keys())[3:13]:
    try:
        x = concat_table(i, today_str, benchmark, index_fund)
        i_pool, num = x[0], x[1]
    except:
        continue
    rho = spearmanr(i_pool['收益_1W'][:-5], i_pool['收益_YTD'][:-5]).correlation
    i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]
    nrows = len(i_pool.index)
    if len(i_pool.index)>0:
        pass
    df_color = color_map(i_pool,
                         ['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD',
                          '收益_1Y', '排名_1Y'], fund_col)
    if nrows <= ngroup:
        draw_table(i_pool, df_color, i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')', '2-3 {0}重点池'.format(i),
                   fpath_draw, num, include_benchmark=True, multi_group=False)
    else:
        num_splits = (len(i_pool) // ngroup) + 1
        for q in range(num_splits):
            start_index = q * ngroup
            end_index = min((q + 1) * ngroup, len(i_pool.index))
            subset_df = i_pool.iloc[start_index: end_index]
            subset_df_color = df_color.iloc[start_index: end_index]
            if q != num_splits - 1:
                draw_table(subset_df, subset_df_color,
                           i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                           '2-3 {0}重点池-子图{1}'.format(i,q+1), fpath_draw, num, include_benchmark=False, multi_group=False)
                num = 0  # 第一次画完核心池后归零
            else:
                draw_table(subset_df, subset_df_color,
                           i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                           '2-3 {0}重点池-子图{1}'.format(i,q+1), fpath_draw, num, include_benchmark=True, multi_group=False)


# %% 绘制表格：重点池&核心池-全市场
for i in list(benchmark.keys())[:3]:
    for level in ['5 权益核心池 ', '7 权益重点池 ']:
        i_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name=level + i, index_col=0)
        i_pool = drop_fund(i_pool,today_str)

        i_pool.loc[benchmark[i]] = None
        i_pool.loc['偏股混合型基金指数'] = None
        i_pool.loc[benchmark[i], list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark[i], list(index_fund.columns[:-1])].values]
        i_pool.loc['偏股混合型基金指数', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['偏股混合型基金指数'], list(index_fund.columns[:-1])].values]
        i_pool.rename(index={benchmark[i]: index_fund.loc[benchmark[i], '指数']}, inplace=True)
        i_pool.iloc[-5:, i_pool.columns.get_loc('简称')] = list(i_pool.iloc[-5:].index)
        rho = spearmanr(i_pool['收益_1W'][:-5], i_pool['收益_YTD'][:-5]).correlation
        i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]

        df_color = color_map(i_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
        nrows = len(i_pool.index)
        if nrows <= ngroup:
            draw_table(i_pool, df_color, level[-4:-1] + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3 {0}{1}'.format(i,level[-4:-1]),
                       fpath_draw, 0, include_benchmark=True, multi_group=False)
        else:
            num_splits = (len(i_pool) // ngroup) + 1
            for q in range(num_splits):
                start_index = q * ngroup
                end_index = min((q + 1) * ngroup, len(i_pool.index))
                subset_df = i_pool.iloc[start_index: end_index]
                subset_df_color = df_color.iloc[start_index: end_index]
                if q != num_splits - 1:
                    draw_table(subset_df, subset_df_color,
                               level[-4:-1] + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                               '2-3 {0}{1}-子图{2}'.format(i,level[-4:-1],str(q + 1)), fpath_draw, 0, include_benchmark=False,multi_group=False)
                else:
                    draw_table(subset_df, subset_df_color,
                               level[-4:-1] + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')-' + str(q + 1),
                               '2-3 {0}{1}-子图{2}'.format(i,level[-4:-1],str(q + 1)), fpath_draw, 0, include_benchmark=True,multi_group=False)

# %% 绘制表格：内部基础池
for i in ['全A内部', '赛道内部']:
    i_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name='6 权益基础池 ' + i, index_col=0)
    if i == '赛道内部':
        i_pool = i_pool[~i_pool['标签'].str.contains('A股')]
    i_pool.loc['line1'] = None
    i_pool.loc['line2'] = None
    i_pool.loc['line3'] = None
    i_pool = drop_fund(i_pool,today_str)

    j = 'A股全市场策略'
    i_pool.loc[benchmark[j]] = None
    i_pool.loc['偏股混合型基金指数'] = None
    i_pool.loc[benchmark[j], list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark[j], list(index_fund.columns[:-1])].values]
    i_pool.loc['偏股混合型基金指数', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['偏股混合型基金指数'], list(index_fund.columns[:-1])].values]
    i_pool.rename(index={benchmark[j]: '中证800'}, inplace=True)

    i_pool.iloc[-5:, i_pool.columns.get_loc('简称')] = list(i_pool.iloc[-5:].index)
    rho = spearmanr(i_pool['收益_1W'][:-5], i_pool['收益_YTD'][:-5]).correlation
    i_pool['简称'] = [cut_name(s) for s in list(i_pool['简称'])]

    df_color = color_map(i_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
    if i=='赛道内部':
        draw_table(i_pool, df_color, '基础池 ' + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-18 内部赛道基础池', fpath_draw, 0, include_benchmark=True, multi_group=True)
    else:
        draw_table(i_pool, df_color, '基础池 ' + i + '(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-17 内部全市场基础池', fpath_draw, 0, include_benchmark=True, multi_group=True)

# %% 绘制表格：香港部分
hk_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name='6 权益基础池 QD港股',index_col=0)
hk_pool = drop_fund(hk_pool,today_str)

hk_pool.loc['恒生指数'] = None
hk_pool.loc['恒生科技'] = None
hk_pool.loc['恒生指数', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['恒生指数'], list(index_fund.columns[:-1])].values]
hk_pool.loc['恒生科技', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['恒生科技'], list(index_fund.columns[:-1])].values]

hk_pool.iloc[-5:, hk_pool.columns.get_loc('简称')] = list(hk_pool.iloc[-5:].index)
rho = spearmanr(hk_pool['收益_1W'][:-5], hk_pool['收益_YTD'][:-5]).correlation
hk_pool['简称'] = [cut_name(s) for s in list(hk_pool['简称'])]
df_color = color_map(hk_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
draw_table(hk_pool, df_color, '重点池 港股(' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-19 港股重点池', fpath_draw, 0, include_benchmark=True, multi_group=False)

# %% 绘制表格：海外部分
qd_pool = pd.read_excel('../周报文件/周报程序数据导出 -zmh.xlsx', sheet_name='6 权益基础池 QD海外',index_col=0)
qd_pool = drop_fund(qd_pool,today_str)

qd_pool.loc['纳斯达克'] = None
qd_pool.loc['标普500'] = None
qd_pool.loc['纳斯达克', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['纳斯达克'], list(index_fund.columns[:-1])].values]
qd_pool.loc['标普500', list(index_fund.columns[:-1])] = [round(x, 2) for x in index_fund.loc[benchmark['标普500'], list(index_fund.columns[:-1])].values]
qd_pool.iloc[-5:, qd_pool.columns.get_loc('简称')] = list(qd_pool.iloc[-5:].index)
rho = spearmanr(qd_pool['收益_1W'][:-5], qd_pool['收益_YTD'][:-5]).correlation
qd_pool['简称'] = [cut_name(s) for s in list(qd_pool['简称'])]
df_color_2 = color_map(qd_pool,['收益_1W', '排名_1W', '收益_1M', '排名_1M', '收益_3M', '排名_3M', '收益_YTD', '排名_YTD','收益_1Y', '排名_1Y'], fund_col)
draw_table(qd_pool, df_color_2, '重点池 海外 (' + data_date + ')(相关性' + str(round(rho, 2)) + ')','2-3-20 QD重点池', fpath_draw, 0, include_benchmark=True, multi_group=True)

print('13.0 补充绘图-各基金池细分表格绘制完毕')

printtime(t)

printtime(1)
