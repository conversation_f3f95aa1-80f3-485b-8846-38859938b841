# -*- coding: utf-8 -*-
"""
Created on Mon Oct 16 11:03:18 2023

@author: gyrx-zhaomh
"""
import pandas as pd
import numpy as np
import datetime as dt
from dateutil.relativedelta import relativedelta
from scipy import stats
#from cal_position import *
#import cal_PRD
from WindPy import *
w.start()


def PRD_cal(fund_list, startday, endday, benchmark='000906.SH'):
    '''用于计算prd，源于文件cal_PRD.py'''
    b = w.wsd(benchmark, "close", startday, endday, "")
    b = pd.DataFrame({'benchmark': b.Data[0]}, index=b.Times)
    fund_nav = w.wsd(fund_list, "NAV_adj", startday, endday, "")
    fund_nav = pd.DataFrame(np.array(fund_nav.Data).T, index=fund_nav.Times, columns=fund_list)
    fund_rtn = fund_nav / fund_nav.shift(1) - 1
    fund_rtn['benchmark'] = b['benchmark'] / b['benchmark'].shift(1) - 1
    fund_rtn = fund_rtn[1:]

    PRD = pd.DataFrame(index=fund_list)
    for i in fund_list:
        df = fund_rtn[[i, 'benchmark']].dropna()
        PRD.loc[i, 'P+'] = df[df['benchmark'] >= 0][i].mean() / df[df['benchmark'] >= 0]['benchmark'].mean()
        PRD.loc[i, 'P-'] = df[df['benchmark'] < 0][i].mean() / df[df['benchmark'] < 0]['benchmark'].mean()
        PRD.loc[i, 'PRD'] = PRD.loc[i, 'P+'] - PRD.loc[i, 'P-']
    return PRD.round(2)

def exposure_cal2(fund_list, cal_day, windows, index, index_name):
    '''用于计算FOF基金的仓位，源于文件cal_position.py'''
    from scipy.optimize import lsq_linear
    zxfg_w = w.wsd(",".join(index), "close", "ED-" + str(windows) + "TD", cal_day, "")
    zxfg = pd.DataFrame({'Time': pd.to_datetime(zxfg_w.Times)})
    for col in range(len(index)):
        zxfg[index_name[col]] = zxfg_w.Data[col]

    zxfg = zxfg.set_index('Time')
    zxfg = (zxfg / zxfg.shift(1) - 1) * 100
    zxfg['cons'] = 1
    zxfg = zxfg.dropna()
    fund_nav = w.wsd(fund_list, "NAV_adj", "ED-" + str(windows) + "TD", cal_day, "")
    fund_nav = pd.DataFrame(np.array(fund_nav.Data).T, index=fund_nav.Times, columns=fund_nav.Codes)
    fund_rtn = (fund_nav / fund_nav.shift(1) - 1) * 100
    fund_rtn = fund_rtn.dropna(how='all')

    lb = [0] * len(index) + [-np.inf]  # [0,0,0,0,0,-np.inf]
    ub = [1] * len(index) + [np.inf]  # [1,1,1,1,1,np.inf]
    # lb = [0 for i in zxfg.columns]
    # ub = [1 for i in zxfg.columns]
    position = pd.DataFrame(index=fund_list, columns=zxfg.columns)
    r2 = list()
    for i in fund_list:
        res = lsq_linear(zxfg.loc[fund_rtn[i].dropna().index], fund_rtn[i].dropna(), bounds=(lb, ub), lsmr_tol='auto')
        position.loc[i] = res.x
        r2.append(1 - sum([x ** 2 for x in res.fun]) / sum([x ** 2 for x in fund_rtn[i].dropna()]))
    position = position.astype(float)
    position[position < 1e-8] = 0
    position['position'] = position[index_name].sum(axis=1)  # position['金融'] + position['周期'] + position['消费'] + position['成长']
    return position['position'] * 100, r2

def fof_chart(code, today_str, last1w, last1m, last3m, ytd, last1y, index, index_name):
    cal_day = w.tdaysoffset(-2, today_str, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last1w_fof = w.tdaysoffset(-2, last1w, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last1m_fof = w.tdaysoffset(-2, last1m, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last3m_fof = w.tdaysoffset(-2, last3m, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    ytd_fof = ytd #w.tdaysoffset(-2, ytd, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    last1y_fof = w.tdaysoffset(-2, last1y, "Period=D").Data[0][0].strftime('%Y-%m-%d')
    fof_chart = pd.DataFrame(index = code)
    fof_chart['简称'] = w.wss(code, "sec_name").Data[0]
    fof_chart['成立日'] = [x.strftime('%Y-%m-%d') for x in w.wss(code, "fund_setupdate").Data[0]]
    result = exposure_cal2(code, cal_day, 60, index, index_name)
    fof_chart['估算仓位'] = result[0]#exposure_cal2(code, cal_day, 60, index, index_name)
    fof_chart['R^2'] = result[1]
    fof_chart['收益_1W'] = w.wss(code, "NAV_adj_return", "startDate=" + last1w_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_1W'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last1w_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_1M'] = w.wss(code, "NAV_adj_return", "startDate=" + last1m_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_1M'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last1m_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_3M'] = w.wss(code, "NAV_adj_return", "startDate=" + last3m_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_3M'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last3m_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['最大回撤_3M'] = w.wss(code, "risk_maxdownside", "startDate=" + last3m_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_YTD'] = w.wss(code, "NAV_adj_return", "startDate=" + ytd_fof + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_YTD'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + ytd_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['最大回撤_YTD'] = w.wss(code, "risk_maxdownside", "startDate=" + ytd_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['收益_1Y'] = w.wss(code, "NAV_adj_return", "startDate=" + last1y_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['超额收益_1Y'] = w.wss(code, "NAV_over_bench_return_per", "startDate=" + last1y_fof  + ";endDate=" + cal_day).Data[0]
    fof_chart['最大回撤_1Y'] = w.wss(code, "risk_maxdownside", "startDate=" + last1y_fof  + ";endDate=" + cal_day).Data[0]
    return fof_chart.round(2)

def fund_chart(code, sector, item, benchmark, today_str, last3m, last6m, last1y, fund):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index.tolist(), '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, '资产细分V']
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = [100-stats.percentileofscore(fund[fund[item]==sector]['近一周'], x) for x in fund_chart['收益_1W']]
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = [100-stats.percentileofscore(fund[fund[item]==sector]['近一月'], x) for x in fund_chart['收益_1M']]
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = [100-stats.percentileofscore(fund[fund[item]==sector]['近三月'], x) for x in fund_chart['收益_3M']]
    fund_chart['收益_6M'] = fund.loc[fund_chart.index, '近半年']
    fund_chart['排名_6M'] = [100-stats.percentileofscore(fund[fund[item]==sector]['近半年'], x) for x in fund_chart['收益_6M']]
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = [100-stats.percentileofscore(fund[fund[item]==sector]['年初至今'], x) for x in fund_chart['收益_YTD']]
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = [100-stats.percentileofscore(fund[fund[item]==sector]['近一年'], x) for x in fund_chart['收益_1Y']]
    fund_chart[['P+_3M', 'P-_3M', 'PRD_3M']] = PRD_cal(code, last3m, today_str, benchmark[sector])
    fund_chart[['P+_1Y', 'P-_1Y', 'PRD_1Y']] = PRD_cal(code, last1y, today_str, benchmark[sector])
    fund_chart = fund_chart.sort_values(by = '收益_1W', ascending=False)
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_6M', '收益_YTD', '收益_1Y', 'P+_3M', 'P-_3M', 'PRD_3M', 'P+_1Y', 'P-_1Y', 'PRD_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_6M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)

def fund_chart2(code, sector, item, fund):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index, '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, item]
    if type(sector)==list:
        sample = fund[fund[item].isin(sector)]
    else:
        sample = fund[fund[item] == sector]
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = sample['近一周'].rank(ascending = False)[fund_chart.index] / sample['近一周'].count() * 100
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = sample['近一月'].rank(ascending = False)[fund_chart.index] / sample['近一月'].count() * 100
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = sample['近三月'].rank(ascending = False)[fund_chart.index] / sample['近三月'].count() * 100
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = sample['年初至今'].rank(ascending = False)[fund_chart.index] / sample['年初至今'].count() * 100
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = sample['近一年'].rank(ascending = False)[fund_chart.index] / sample['近一年'].count() * 100
    fund_chart = fund_chart.sort_values(by = '收益_1W', ascending=False)
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)

def fund_chart3(code, sector, item, fund, rpt_date):
    fund_chart = pd.DataFrame(index = code)
    fund_chart['简称'] = fund.loc[fund_chart.index, '基金简称']
    fund_chart['基金经理'] = fund.loc[fund_chart.index, '基金经理']
    fund_chart['标签'] = fund.loc[fund_chart.index, item]
    fund_chart['权益仓位'] = w.wss(code,"prt_stocktonav","rptDate=" + rpt_date).Data[0]
    fund_chart['转债仓位'] = w.wss(code,"prt_convertiblebondtonav","rptDate=" + rpt_date).Data[0]
    fund_chart['转债仓位'] = fund_chart['转债仓位'].replace(np.nan, 0)
    sample = fund[fund[item] == sector]
    fund_chart['收益_1W'] = fund.loc[fund_chart.index, '近一周']
    fund_chart['排名_1W'] = sample['近一周'].rank(ascending = False)[fund_chart.index] / sample['近一周'].count() * 100
    fund_chart['收益_1M'] = fund.loc[fund_chart.index, '近一月']
    fund_chart['排名_1M'] = sample['近一月'].rank(ascending = False)[fund_chart.index] / sample['近一月'].count() * 100
    fund_chart['收益_3M'] = fund.loc[fund_chart.index, '近三月']
    fund_chart['排名_3M'] = sample['近三月'].rank(ascending = False)[fund_chart.index] / sample['近三月'].count() * 100
    fund_chart['收益_YTD'] = fund.loc[fund_chart.index, '年初至今']
    fund_chart['排名_YTD'] = sample['年初至今'].rank(ascending = False)[fund_chart.index] / sample['年初至今'].count() * 100
    fund_chart['收益_1Y'] = fund.loc[fund_chart.index, '近一年']
    fund_chart['排名_1Y'] = sample['近一年'].rank(ascending = False)[fund_chart.index] / sample['近一年'].count() * 100
    fund_chart = fund_chart.sort_values(by = '收益_1W', ascending=False)
    rtn = ['收益_1W', '收益_1M', '收益_3M', '收益_YTD', '收益_1Y']
    rank = ['排名_1W', '排名_1M', '排名_3M', '排名_YTD', '排名_1Y']
    a, x = fund_chart[rtn].quantile(0.75), fund_chart[rank].quantile(0.25)
    b, y = fund_chart[rtn].quantile(0.5), fund_chart[rank].quantile(0.5)
    c, z = fund_chart[rtn].quantile(0.25), fund_chart[rank].quantile(0.75)
    fund_chart.loc['25分位', rtn+rank] = pd.concat([a,x])
    fund_chart.loc['中位数', rtn+rank] = pd.concat([b,y])
    fund_chart.loc['75分位', rtn+rank] = pd.concat([c,z])
    return fund_chart.round(2)

def add_quantile(df):
    col = ['估算仓位', '收益_1W', '超额收益_1W','收益_1M', '超额收益_1M', '收益_3M', '超额收益_3M', '最大回撤_3M', '收益_YTD',
           '超额收益_YTD', '最大回撤_YTD', '收益_1Y', '超额收益_1Y', '最大回撤_1Y']
    a = df[col].quantile(0.75)
    b = df[col].quantile(0.5)
    c = df[col].quantile(0.25)
    df.loc['25分位', col] = a
    df.loc['中位数', col] = b
    df.loc['75分位', col] = c
    return df.round(2)

def cal_rpt_date(rpt_date):
    if '一季报' in rpt_date:
        rpt_date = rpt_date[:4] + '-03-31'
    if '中报' in rpt_date:
        rpt_date = rpt_date[:4] + '-06-30'
    if '三季报' in rpt_date:
        rpt_date = rpt_date[:4] + '-09-30'
    if '年报' in rpt_date:
        rpt_date = rpt_date[:4] + '-12-31'
    return rpt_date

