import math
import os

import pandas as pd
import numpy as np
from scipy import signal#求极值
import scipy.stats as stats
import datetime as dt
from WindPy import w
w.start()

import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import textwrap
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True

fontsize_subtitle = 16
fontsize_suptitle = 18
fontsize = 12
fontsize_text = 12
figsize = [20,9]
width = 0.3
linewidth_table=0.5

# from BasicFun1 import *
import statsmodels.api as sm
import copy as mycopy
def get_date_list(begin_date,end_date,freq,date_format='%Y%m%d'):
    date_list=[x.strftime(date_format) for x in pd.date_range(start=begin_date,end=end_date,freq=freq)]
    return date_list

def get_tdate_list(start_date,end_date,period_type,date_format='%Y%m%d'):
    tdays_df = w.tdays(start_date, end_date, f"Period={period_type}", usedf=True)[1]
    tdays_df.index = pd.to_datetime(tdays_df.index)
    return tdays_df.index.strftime(date_format).tolist()
def read_factor_excel(path,sheet_name,datetrans_flag=False):
    factor_df = pd.read_excel(path,sheet_name=sheet_name,index_col=0)
    factor_df.index = pd.to_datetime(factor_df.index).strftime('%Y-%m-%d')
    if datetrans_flag:
        date_list = get_date_list(factor_df.index[0], factor_df.index[-1], 'D')
        factor_df = factor_df.reindex(date_list)
    factor_df.index = pd.to_datetime(factor_df.index)
    return factor_df

def get_creditdelta(endDate):
    df_creditdelta = w.edb('M5206731,M5206732,M5206733,M5206734,M5206735,M5206736,M5206737,M6179492', "20020101", endDate,usedf = True)[1]
    df_creditdelta = df_creditdelta.rename(columns={"M5206731": '新增人民币贷款',"M5206732":'新增外币贷款',"M5206733":'新增委托贷款','M5206734':'新增信托贷款',"M5206735":'新增未贴现银行承兑汇票',"M5206736":'企业债券融资',"M5206737":'非金融企业境内股票融资',"M6179492":'政府债券'})
    df_creditdelta['当月合计'] = df_creditdelta.sum(axis = 1)
    df_creditdelta['年份'] =  df_creditdelta.index.map(lambda x: x.year)
    df_creditdelta['年月'] =  pd.to_datetime(df_creditdelta.index).strftime('%Y%m')
    df_creditdelta['年'] =  pd.to_datetime(df_creditdelta.index).strftime('%Y')
    for yrx in range(2002,2025):
        #yrx = df_creditdelta.index[0].year
        df_creditdelta_yrx = df_creditdelta[df_creditdelta['年份']==yrx]
        df_creditdelta_yrx['当年累计'] = df_creditdelta_yrx.cumsum()['当月合计']
        df_creditdelta.loc[df_creditdelta_yrx.index,'当年累计'] = df_creditdelta_yrx['当年累计'].values
    df_creditdelta['当年累计同比'] = (df_creditdelta[['当年累计']]/df_creditdelta[['当年累计']].shift(12)-1)['当年累计']
    df_creditdelta['累计'] = df_creditdelta.cumsum()['当月合计']
    df_creditdelta['累计同比'] = (df_creditdelta[['累计']]/df_creditdelta[['累计']].shift(12)-1)['累计']
    df_creditdelta.index = pd.to_datetime(df_creditdelta.index )
    return df_creditdelta['累计同比']

def get_edb(code_list,code_name,start_date,end_date):
    code_list = ','.join(code_list)
    df = w.edb(code_list, start_date, end_date, usedf=True)[1]
    df.columns = code_name
    df.index = pd.to_datetime(df.index)
    return df
def generate_edb_data(input_path,output_path,start_date,end_date):
    edb_info = pd.read_excel(input_path)
    edb_data = {}
    for date_type in edb_info['更新频率'].unique():
        sub_edb_info = edb_info[edb_info['更新频率'] == date_type]
        edb_data[date_type] = get_edb(sub_edb_info['指标代码'], sub_edb_info['指标名称'], start_date, end_date)
    writer = pd.ExcelWriter(output_path)
    for date_type in edb_data.keys():
        edb_data[date_type].to_excel(writer, sheet_name=date_type)
    writer._save()

def macro_table(df1,title,save_path):
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False,figsize=figsize)
    ax.axis('off')
    colortable = mycopy.deepcopy(df1)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'
    colColours = ['lightsteelblue'] * len(colortable.columns)
    table = ax.table(cellText=df1.values,colLabels=df1.columns,bbox=(0, 0, 1, 1),cellLoc='center',loc='center',cellColours=colortable.values,colColours=colColours)
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df1.columns))))
    for (row, col), cell in table.get_celld().items():
        cell.set_fontsize(fontsize_text)
        if ('↑' in str(df1.iloc[row-1, col])):
            cell.get_text().set_color('red')
        if ('↓' in str(df1.iloc[row-1, col])):
            cell.get_text().set_color('green')
        cell.set_fontsize(fontsize_text)
        cell.set_linewidth(linewidth_table)
        if (col == 0)|(col == 1)|(row == 0):
            cell.get_text().set_color('black')
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))


    ax.axhline(y=2/(len(df1)+1), lw=2, ls='-', c='blue')
    ax.axhline(y=4/(len(df1)+1), lw=2, ls='-', c='blue')
    ax.axhline(y=6/(len(df1)+1), lw=2, ls='-', c='blue')
#     ax.axvline(x=(len(df1.columns)-1)/(len(df1.columns)+1), lw=2, ls='-', c='blue')
    ax.set_title(title, fontsize=fontsize_subtitle, fontweight='bold', pad=5)
    fig.tight_layout()
    fig.savefig(save_path, bbox_inches='tight')
    return


def classify_diffUpDown(df, f_list, f_meaning=None):
    f_dic = {1: f"{f_list[0]}(升)_{f_list[1]}(升)", 2: f"{f_list[0]}(升)_{f_list[1]}(降)",
             3: f"{f_list[0]}(降)_{f_list[1]}(升)", 4: f"{f_list[0]}(降)_{f_list[1]}(降)",
             np.nan: np.nan}
    sig = df[f_list]
    sig[[f + '_diff' for f in f_list]] = df[f_list].diff()

    sig = sig.iloc[1:]
    sig['pre_level'] = np.nan
    sig.loc[(sig[f_list[0] + '_diff'] > 0) & (sig[f_list[1] + '_diff'] > 0), 'pre_level'] = 1
    sig.loc[(sig[f_list[0] + '_diff'] > 0) & (sig[f_list[1] + '_diff'] < 0), 'pre_level'] = 2
    sig.loc[(sig[f_list[0] + '_diff'] < 0) & (sig[f_list[1] + '_diff'] > 0), 'pre_level'] = 3
    sig.loc[(sig[f_list[0] + '_diff'] < 0) & (sig[f_list[1] + '_diff'] < 0), 'pre_level'] = 4

    g = sig['pre_level'].values.tolist()
    for i in range(1, len(sig.index) - 1):
        if (g[i - 1] == g[i + 1]) | ((g[i - 1] != g[i]) & (g[i + 1] != g[i])):
            g[i] = g[i - 1]
    if g[i + 1] != [i]:
        g[i + 1] = g[i]
    sig['adj_level'] = g
    if f_meaning is None:
        sig['pre_level'] = sig['pre_level'].apply(lambda x: np.nan if x != x else f_dic[x])
        sig['adj_level'] = sig['adj_level'].apply(lambda x: np.nan if x != x else f_dic[x])
    else:
        sig['pre_level'] = sig['pre_level'].apply(lambda x: np.nan if x != x else f_meaning[int(x) - 1])
        sig['adj_level'] = sig['adj_level'].apply(lambda x: np.nan if x != x else f_meaning[int(x) - 1])
    return sig


def classify_diffUpDown2(df, f_list, f_meaning=None,denghao_flag=[False,False]):
    f_dic = {1: f"{f_list[0]}(升)_{f_list[1]}(升)", 2: f"{f_list[0]}(升)_{f_list[1]}(降)",
             3: f"{f_list[0]}(降)_{f_list[1]}(升)", 4: f"{f_list[0]}(降)_{f_list[1]}(降)",
             np.nan: np.nan}
    sig = df[f_list]
    sig[[f + '_diff' for f in f_list]] = df[f_list].diff()

    sig = sig.iloc[1:]
    sig['pre_level1'] = np.nan
    sig.loc[(sig[f_list[0] + '_diff'] > 0), 'pre_level1'] = 1
    sig.loc[(sig[f_list[0] + '_diff'] < 0), 'pre_level1'] = 0
    sig['pre_level2'] = np.nan
    sig.loc[(sig[f_list[1] + '_diff'] > 0), 'pre_level2'] = 1
    sig.loc[(sig[f_list[1] + '_diff'] < 0), 'pre_level2'] = 0

    # sig.loc[(sig[f_list[0] + '_diff'] == 0), 'pre_level1'] = int(denghao_flag[0])
    # sig.loc[(sig[f_list[1] + '_diff'] == 0), 'pre_level2'] = int(denghao_flag[1])


    g = sig['pre_level1'].values.tolist()
    for i in range(1, len(sig.index) - 1):
        if (g[i - 1] == g[i + 1]) | ((g[i - 1] != g[i]) & (g[i + 1] != g[i])):
            g[i] = g[i - 1]
    if g[i + 1] != [i]:
        g[i + 1] = g[i]
    sig['adj_level1'] = g
    g = sig['pre_level2'].values.tolist()
    for i in range(1, len(sig.index) - 1):
        if (g[i - 1] == g[i + 1]) | ((g[i - 1] != g[i]) & (g[i + 1] != g[i])):
            g[i] = g[i - 1]
    if g[i + 1] != [i]:
        g[i + 1] = g[i]
    sig['adj_level2'] = g

    sig['pre_level'] = np.nan
    sig.loc[(sig['adj_level1'] == 1) & (sig['adj_level2'] == 1), 'pre_level'] = 1
    sig.loc[(sig['adj_level1'] == 1) & (sig['adj_level2'] == 0), 'pre_level'] = 2
    sig.loc[(sig['adj_level1'] == 0) & (sig['adj_level2'] == 1), 'pre_level'] = 3
    sig.loc[(sig['adj_level1'] == 0) & (sig['adj_level2'] == 0), 'pre_level'] = 4

    g = sig['pre_level'].values.tolist()
    for i in range(1, len(sig.index) - 1):
        if (g[i - 1] == g[i + 1]) | ((g[i - 1] != g[i]) & (g[i + 1] != g[i])):
            g[i] = g[i - 1]
    if g[i + 1] != [i]:
        g[i + 1] = g[i]
    sig['adj_level'] = g
    if f_meaning is None:
        sig['pre_level'] = sig['pre_level'].apply(lambda x: np.nan if x != x else f_dic[x])
        sig['adj_level'] = sig['adj_level'].apply(lambda x: np.nan if x != x else f_dic[x])
    else:
        sig['pre_level'] = sig['pre_level'].apply(lambda x: np.nan if x != x else f_meaning[int(x) - 1])
        sig['adj_level'] = sig['adj_level'].apply(lambda x: np.nan if x != x else f_meaning[int(x) - 1])
    return sig


# %%波峰波谷公式
# 定义寻找峰谷位置的函数
# 辅助函数，保证峰谷交替出现
def zip_lists(list1: list, list2: list):
    #    举例测试
    #    list1=[8,38,76,85]
    #    list2=[17,59]
    merged = []
    idx1, idx2 = 0, 0
    while idx1 < len(list1) and idx2 < len(list2):
        if list1[idx1] < list2[idx2]:
            merged.append((list1[idx1], 1))
            idx1 += 1
        else:
            merged.append((list2[idx2], 2))
            idx2 += 1

    if idx1 < len(list1):  # 也就是说idx1<len(list1)且idx2>len(list2)，取的就是list1长出来那一段
        merged.extend([(x, 1) for x in list1[idx1:]])
    else:  # 也就是说idx2<len(list2)且idx1>len(list1)，取的就是list2长出来那一段
        merged.extend([(x, 2) for x in list2[idx2:]])

    ret_lists = [[], []]
    if len(merged) == 0:
        ret_lists[0] = []
        ret_lists[1] = []
    else:
        first_ele = merged[0]
        last_src = first_ele[1]  # 假设等于1
        ret_lists[last_src - 1].append(first_ele[0])
        for i in range(1, len(merged)):
            if merged[i][1] == last_src:
                continue
            else:
                last_src = merged[i][1]
                ret_lists[last_src - 1].append(merged[i][0])
    return ret_lists[0], ret_lists[1]


# 定义寻找峰谷位置的函数
def bb(df, order):
    #    df = df_benchmark[industrylist_benchmark]
    # df格式为单列dataframe
    # 第一步，寻找前后N个月（此处N=6）相对最大最小值
    greater_ind = signal.argrelextrema(df.values, np.greater, order=order)  # 寻找极大值
    less_ind = signal.argrelextrema(df.values, np.less, order=order)  # 寻找极小值
    # 第二步，去掉开头结尾6个月的拐点
    greater_ind = list(greater_ind[0][greater_ind[0] >= order])
    less_ind = list(less_ind[0][less_ind[0] >= order])
    greater_ind_output, less_ind_output = zip_lists(greater_ind, less_ind)
    # greater_ind_output.extend(less_ind_output)
    # greater_ind_output.extend([0,len(df)-1])
    return greater_ind_output, less_ind_output


def classify_bb(df, N=6):
    greater_ind_output, less_ind_output = bb(df, N)
    sig = pd.Series(np.nan, index=df.index)
    sig.loc[df.index[[x + 1 for x in greater_ind_output]]] = 0
    sig.loc[df.index[[x + 1 for x in less_ind_output]]] = 1
    sig = sig.fillna(method='ffill')
    if greater_ind_output[0] < less_ind_output[0]:
        sig = sig.fillna(1)
    else:
        sig = sig.fillna(0)
    return sig

def one_sided_HP_filter(df, lam):
    df = df.dropna()
    df_local = df.copy()
    data_series = np.array(df_local)  # nx1的matrix
    length = len(df)

    list_cycle = [math.nan, math.nan]  # t=1,2时是没有的，用math.nan填充
    for i in range(2, length):
        # t=i+1
        sub_series = data_series[:i + 1]  # 一共有i+1=t项
        sub_A_t = At(i + 1, lam)
        cycle_t = (sub_A_t @ sub_series)[0, 0]
        list_cycle.append(cycle_t)
    df_local['cycle_1sHP'] = list_cycle
    df_local['trend_1sHP'] = df[df.columns[0]] - np.array(list_cycle)
    return df_local['trend_1sHP']

def F_hp(df, lambd=129600):
    df_new = pd.Series(np.nan, index=df.index)
    start_idx = df[~df.isnull()].index[0]

    cycle,trend  = sm.tsa.filters.hpfilter(df.loc[start_idx:].values, lambd)
    print(len(df_new.loc[start_idx:]))
    print(len(cycle))
    df_new.loc[start_idx:] = trend

    return df_new

def At(t: int, lam):
    # e_t:tx1，最后一行是1，其余是0
    e_t = np.zeros(t)
    e_t[-1] = 1

    # I_t:t-2的单位阵
    I_t = np.identity(t - 2)

    # Q_t:二阶差分矩阵，(t-2)xt
    Q_t = np.zeros((t - 2, t))  # 先设置shape
    # 再通过循环设置每一行的值
    for i in range(t - 2):
        Q_t[i, i], Q_t[i, i + 1], Q_t[i, i + 2] = 1, -2, 1

    # 通过矩阵运算，计算常数阵
    # @:矩阵乘法； Matrix.T:矩阵转置； Matrix.I：矩阵求逆
    A_t = np.matrix(e_t) @ (Q_t.T) @ (np.linalg.inv(Q_t @ (Q_t.T) + I_t / lam)) @ Q_t
    # 结果是一个1xt的矩阵
    return A_t
def show_macro_table(data_path, output_path, end_date = None,start_date='2007-01-01'):
    if end_date is None:
        # enddatetime = w.tdaysoffset(-0 if ((dt.datetime.today().isoweekday() in [6, 7]) | (dt.datetime.now().hour >= 21)) else -1,
        #               dt.datetime.today(), "Period=D").Data[0][0].date()  # dt.date(2020,12,31)#
        enddatetime = w.tdaysoffset(-0, dt.datetime.today(), "Period=D").Data[0][0].date()
        enddatetime = dt.datetime.today()
        end_date = enddatetime.strftime('%Y-%m-%d')
    generate_edb_data(f"{data_path}edb.xlsx",f"{data_path}edb_data.xlsx", start_date, end_date)

    edb_df = read_factor_excel(f"{data_path}edb_data.xlsx", 'M')
    date_df = pd.DataFrame({'自然日': pd.to_datetime(
        get_date_list(edb_df.index.strftime('%Y%m%d')[0], edb_df.index.strftime('%Y%m%d')[-1],'M')),
                            '交易日': pd.to_datetime(
                                get_tdate_list(edb_df.index.strftime('%Y%m%d')[0], edb_df.index.strftime('%Y%m%d')[-1],'M'))})
    edb_df['社会融资规模存量（同比）'] = get_creditdelta(edb_df.index.strftime('%Y%m%d')[-1]) * 100
    edb_df.index = date_df.set_index('自然日').loc[edb_df.index]['交易日'].values
    edb_df_D = read_factor_excel(f"{data_path}edb_data.xlsx", 'D')
    edb_df_D = edb_df_D.resample('m').mean()
    date_df = pd.DataFrame({'自然日': pd.to_datetime(
        get_date_list(edb_df_D.index.strftime('%Y%m%d')[0], edb_df_D.index.strftime('%Y%m%d')[-1],'M')),
                            '交易日': pd.to_datetime(get_tdate_list(edb_df_D.index.strftime('%Y%m%d')[0],
                                                                  edb_df_D.index.strftime('%Y%m%d')[-1],'M'))})
    edb_df_D.index = date_df.set_index('自然日').loc[edb_df_D.index]['交易日'].values
    edb_df_D.index = pd.to_datetime(edb_df_D.index)
    edb_df_D['期限利差'] = edb_df_D['国债十年期'] - edb_df_D['国债一年期']

    index_ = list(set(list(edb_df_D.index) + list(edb_df.index)))
    index_.sort()
    edb_df = edb_df.reindex(index_)
    edb_df_D = edb_df_D.reindex(index_)

    macro_df = pd.DataFrame()
    macro_df['PMI'] = edb_df['PMI']
    macro_df['CPI_PPI'] = 0.8 * edb_df['CPI（同比）'] + 0.2 * edb_df['PPI（同比）']
    macro_df['长期利率'] = edb_df_D['国债十年期']
    macro_df['期限利差'] = edb_df_D['期限利差']
    macro_df['DR007'] = edb_df_D['DR007']
    macro_df['社会融资规模存量（同比）'] = edb_df['社会融资规模存量（同比）']

    macro_df.index = macro_df.index.strftime('%Y-%m-%d')
    macro_df_show = macro_df.iloc[-4:].applymap(lambda x: '' if x != x else str(round(x, 2))).T
    macro_df_show['方向'] = macro_df.apply(lambda x: show_single_direction(x))
    macro_df_show['分域方法'] = pd.Series(
        {'PMI': '美林时钟', 'CPI_PPI': '美林时钟', '长期利率': '收益率曲线形态', '期限利差': '收益率曲线形态',
         'DR007': '货币信用', '社会融资规模存量（同比）': '货币信用'})

    tmp_concatML = classify_diffUpDown(macro_df, ['PMI', 'CPI_PPI'], ['过热', '复苏', '滞胀', '衰退'])
    tmp_concatLL = classify_diffUpDown2(macro_df, ['长期利率', '期限利差'],
                                        ['熊陡(复苏)', '熊平(过热)', '牛陡(衰退)', '牛平(滞胀)'])
    tmp_concatHBXY = classify_diffUpDown2(macro_df, ['DR007', '社会融资规模存量（同比）'],
                                           ['紧货币宽信用(过热)', '紧货币紧信用(滞胀)', '宽货币宽信用(复苏)',
                                            '宽货币紧信用(衰退)'])
    tmp_concat = pd.Series({'美林时钟': tmp_concatML.iloc[-1].loc['adj_level'],
                            '收益率曲线形态': tmp_concatLL.iloc[-1].loc['adj_level'],
                            '货币信用': tmp_concatHBXY.iloc[-1].loc['adj_level']
                            })
    tmp_concat.name = '宏观域'
    macro_df_show = pd.merge(macro_df_show, tmp_concat, left_on='分域方法', right_index=True, how='left')
    macro_df_show.index.name = '跟踪指标'
    macro_df_show = macro_df_show.reset_index().set_index(['分域方法', '跟踪指标'])

    macro_table(macro_df_show.reset_index(), f"宏观分域指标跟踪({end_date})",f"{output_path}2.1.1宏观分域指标统计.png")
    return macro_df, macro_df_show
def show_single_direction(df):
    last_index_ = df.dropna().index[-1]
    last_df_diff = df.loc[:last_index_].diff().iloc[-2:]
    if (last_df_diff.iloc[-1] > 0) and (last_df_diff.iloc[-2] > 0):
        return '↑'
    elif (last_df_diff.iloc[-1] < 0) and (last_df_diff.iloc[-2] < 0):
        return '↓'
    elif (last_df_diff.iloc[-1] > 0) and (last_df_diff.iloc[-2] <= 0):
        return '↗'
    elif (last_df_diff.iloc[-1] < 0) and (last_df_diff.iloc[-2] >= 0):
        return '↘'
    elif last_df_diff.iloc[-1] == 0:
        return '→'