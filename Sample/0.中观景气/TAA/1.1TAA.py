import os

import pandas as pd

from BasicFun1 import *
from BasicFun2朝阳2 import * #核心的通用基础函数都在BasicFun2
from ModelFun import * #模型函数都在ModelFun
from MacroFun import * #宏观函数都在MacroFun
plt.ioff()
t0 = dt.datetime.now()
t = t0

if __name__ == '__main__':
    ''' 1.参数设置 '''
    # 业绩比较基准参数
    benchmark = '881001.WI'  # 默认wind全A

    # 日期参数
    startDate_anapre = '20170103'  # 分析师预期模型起点
    startDate_chanye = '20130101'  # 中观产业景气模型起点
    startDate_calendar = '20140101'  # 日历效应模型起点
    startDate_pbroe = '20130331'  # PB-ROE模型起点
    startDate_crowd = '20140101'  # 拥挤度模型起点
    threshDate_chanye = '20141231'  # 中观产业景气模型需要设置一个阈值起点
    endDate = '20241130'  # 截止日，需要修改

    # 地址参数
    fp = './data/'
    fp_input = fp + 'input/'
    fp_output = f"{fp}output/result/{endDate}/"
    os.makedirs(fp_output, exist_ok=True)

    # 行业口径
    hylist = pd.read_excel(fp_input + '\\申万行业指数.xlsx', sheet_name='最终表', index_col=0)

    # 模型结果存储dic
    backtest_dic = {}
    plot_data_dic = {}
    result_all_dic = {}
    rili_table_dic = {}

    ''' 2.分析师预期模型 '''
    model_name = '分析师预期模型'
    fp_anapre = fp_output + '1.分析师预期/'
    os.makedirs(fp_anapre, exist_ok=True)
    # 首先确立分析师预期模型内部的统一时间参数
    catchDtAll = get_tdate_list(startDate_anapre, endDate,'M')
    # ---part1:提取数据：净利润，净资产，自由流通市值
    balance, netprofit, stkfreemv_mean,listedstk = get_data_anapre(fp_input,endDate)

    # ---part2:计算个股分析师预期ROE指标
    stock_ROE_read = pd.read_csv(fp_input + '\\个股ROE因子分解8列_最新.csv', index_col=0) #读取上期已经算好的分析师预期数据
    stock_ROE_read['date'] = stock_ROE_read['date'].apply(lambda x: str(x))
    # 日期和catchDtAll对齐
    history_date = stock_ROE_read['date'].drop_duplicates().values.tolist()
    add_date = list(set(catchDtAll) - set(history_date))
    add_date.sort()
    stock_ROE_add = cal_stock_ROE(add_date, balance, netprofit, stkfreemv_mean,listedstk)
    stock_ROE = stock_ROE_read._append(stock_ROE_add)
    stock_ROE.to_csv(fp_input + '\\个股ROE因子分解8列_最新.csv', index=True, index_label='S_INFO_WINDCODE')

    # ---part3:个股预期ROE因子合成行业景气因子
    # 提取申万123级行业历史成分股
    sqlstr = '''select s_info_windcode, S_CON_WINDCODE,S_CON_INDATE,S_CON_OUTDATE \
        from winddf.SWIndexMembers order by s_info_windcode'''
    indexmember_df = pd.read_sql(sqlstr, windconn)
    # 填补现在日期,日期格式改变成数字
    indexmember_df = indexmember_df.where(indexmember_df.notnull(), datetime.datetime.now().strftime("%Y%m%d"))
    indexmember_df['S_CON_INDATE'] = indexmember_df['S_CON_INDATE'].apply(lambda x: int(x))
    indexmember_df['S_CON_OUTDATE'] = indexmember_df['S_CON_OUTDATE'].apply(lambda x: int(x))
    all_con_indus = pd.concat([con_indus(indexmember_df, tm) for tm in catchDtAll])
    all_con_indus['date'] = all_con_indus['date'].apply(lambda x: str(x))
    all_con_indus = all_con_indus[all_con_indus['S_INFO_WINDCODE'].isin(hylist.index)].set_index(['S_INFO_WINDCODE', 'date'])  # 只提取需要使用的75个行业口径

    # 合并all_con_indus和res,得到行业景气度矩阵
    stock_ROE['ROE'] = (stock_ROE['np'] - stock_ROE['np_last']) / stock_ROE['bv_now']
    stock_ROE['ROE_lag'] = (stock_ROE['np_l1'] - stock_ROE['np_last_l1']) / stock_ROE['bv_now_l1']
    stock_ROE = stock_ROE[['ROE', 'ROE_lag', 'freemv', 'date']].reset_index().set_index(['S_INFO_WINDCODE','date'])
    # 更新行业ROE数据
    hy_ROE_read = read_hy_ROE(fp_input + '\\行业ROE因子当期_最新.xlsx',catchDtAll)
    hy_ROE_lag_read = read_hy_ROE(fp_input + '\\行业ROE因子上期_最新.xlsx', catchDtAll)
    hy_ROE_fugaidu_read = read_hy_ROE(fp_input + '\\行业ROE因子覆盖度_最新.xlsx',catchDtAll)
    history_date2 = hy_ROE_read['date'].drop_duplicates().values.tolist()
    add_date = list(set(catchDtAll) - set(history_date2))
    add_date.sort()
    hy_ROE_all_add = cal_hy_ROE(add_date,hylist.index.tolist(),all_con_indus,stock_ROE)
    hy_ROE_add = hy_ROE_all_add[['S_INFO_WINDCODE','date','最新ROE']].rename(columns={'最新ROE':'景气指标值'})
    hy_ROE_lag_add = hy_ROE_all_add[['S_INFO_WINDCODE', 'date', '上期ROE']].rename(columns={'上期ROE':'景气指标值'})
    hy_ROE_fugaidu_add = hy_ROE_all_add[['S_INFO_WINDCODE', 'date', '总市值']].rename(columns={'总市值':'市值覆盖度'})
    hy_ROE = hy_ROE_read._append(hy_ROE_add)
    hy_ROE_lag = hy_ROE_lag_read._append(hy_ROE_lag_add)
    hy_ROE_fugaidu = hy_ROE_fugaidu_read._append(hy_ROE_fugaidu_add)
    hy_ROE.to_excel(fp_input + '\\行业ROE因子当期_最新.xlsx', index=True)  # 覆盖上一期文件
    hy_ROE_lag.to_excel(fp_input + '\\行业ROE因子上期_最新.xlsx', index=True)  # 覆盖上一期文件
    hy_ROE_fugaidu.to_excel(fp_input + '\\行业ROE因子覆盖度_最新.xlsx', index=True)  # 覆盖上一期文件

    # pivot行业景气原矩阵形成stack格式便于后续生成每期多空行业字典
    hy_ROE = hy_ROE.pivot(index='date', columns='S_INFO_WINDCODE', values='景气指标值').replace(0,np.nan)
    hy_ROE_lag = hy_ROE_lag.pivot(index='date', columns='S_INFO_WINDCODE', values='景气指标值').replace(0,np.nan)
    hy_ROE_fugaidu = hy_ROE_fugaidu.pivot(index='date', columns='S_INFO_WINDCODE', values='市值覆盖度')
    # 调整分析师预期覆盖度（之前的是被覆盖的总市值，这里需要处理成覆盖占比）
    hy_ROE_fugaidu2 = pd.DataFrame(index=all_con_indus.index, columns=['行业流通市值'])
    for date in catchDtAll:
        for code in hylist.index.tolist():
            if (code, date) in all_con_indus.index:
                stocklist = all_con_indus.loc[(code, date)][0].split(',')
                stocklist = list(set(stocklist) & set(stkfreemv_mean.columns))
                hy_ROE_fugaidu2.loc[(code, date), '行业流通市值'] = stkfreemv_mean.loc[int(date), stocklist].sum()
    hy_ROE_fugaidu2 = hy_ROE_fugaidu2.reset_index()
    hy_ROE_fugaidu2 = hy_ROE_fugaidu2.pivot(index='date', columns='S_INFO_WINDCODE', values='行业流通市值')
    hy_ROE_fugaidu = (hy_ROE_fugaidu / hy_ROE_fugaidu2).replace(0,np.nan)
    hy_ROE.index = pd.DatetimeIndex(hy_ROE.index.map(str))
    hy_ROE.columns = hylist.loc[hy_ROE.columns, '行业名称'].tolist()
    hy_ROE_lag.index = pd.DatetimeIndex(hy_ROE_lag.index.map(str))
    hy_ROE_lag.columns = hylist.loc[hy_ROE_lag.columns, '行业名称'].tolist()
    hy_ROE_fugaidu.index = pd.DatetimeIndex(hy_ROE_fugaidu.index.map(str))
    hy_ROE_fugaidu.columns = hylist.loc[hy_ROE_fugaidu.columns, '行业名称'].tolist()

    # 首先形成回测函数的核心输入矩阵ROE_change
    hy_ROE_change = (hy_ROE - hy_ROE_lag).dropna(axis=0,how='all')
    # 特殊输出
    hy_ROE_show = cal_anapre_forshow(hy_ROE,hy_ROE_lag, hy_ROE_fugaidu, catchDtAll, hylist)
    ts = pd.DatetimeIndex(catchDtAll).tolist()
    fundrtn, benchrtn = pred_m_rtndata(hylist, hylist.index.tolist(), benchmark, startDate_anapre, endDate,hy_ROE_change, ts)
    ROEchangeTOP1,ROEchangeTAIL5 = pred_m_group_2x(hy_ROE_change,hy_ROE_fugaidu,a='景气边际变化',b='覆盖度',thresh2=0.5,N=10)
    backtest_dic[model_name], plot_data_dic[model_name], result_all_dic[model_name], rili_table_dic[model_name] = pred_m_outputs(benchmark, fundrtn, benchrtn, ROEchangeTOP1, ROEchangeTAIL5,hy_ROE_change)
    backtest_to_excel([backtest_dic[model_name],plot_data_dic[model_name],result_all_dic[model_name],rili_table_dic[model_name],transDic2Df(ROEchangeTOP1,model_name),transDic2Df(ROEchangeTAIL5,model_name),hy_ROE_show],['回测结果','plot_data','result_all','日历效应','TOP','TAIL','画图用_分析师预期最新结果'],fp_anapre+'分析师预期回测.xlsx')

    t=printtime(t)

    ''' 2.中观产业景气模型 '''
    model_name = '中观产业景气模型'
    fp_chanye = fp_output + '2.中观产业/'
    os.makedirs(fp_chanye, exist_ok=True)
    #原始数据读取
    data_hy = pd.read_excel(fp_input + '\\中观产业输入指标.xlsx', sheet_name='指数', index_col=0)
    data_macro = pd.read_excel(fp_input + '\\中观产业输入指标.xlsx', sheet_name='指标', index_col=0)
    data_macro = data_macro[data_macro['频率'].isin(['日', '月', '周'])]
    data_macro.index.name = '指标代码'
    data_macro = data_macro.reset_index().drop_duplicates(['指标代码', '行业'], keep='first').set_index('指标代码')

    #筛选有效指标-合并指标成景气指数
    if not os.path.exists(fp_chanye + '/行业景气指数.xlsx'):
        os.makedirs(f"{fp_chanye}/有效指标dump/", exist_ok=True)
        use_hy_All = data_hy.loc[data_hy[data_hy['是否完成指标映射修改']==1].index,'行业名称'].tolist()
        jingqi_indicator1_forall = pd.DataFrame()
        error_hy = []
        nocore_hy = []
        core_indicator_df = pd.DataFrame([], index=use_hy_All, columns=['核心指标', '核心指标数量', '正向指标', '负向指标'])
        for indus in use_hy_All:
            print('%s行业景气指标合成' % indus)
            try:
                all_jingqi_yuanzhi = CleanData(indus, startDate_chanye, endDate, threshDate_chanye,fp_chanye,data_macro)[1]
                ex_ret = Clean_nav_data_1(indus, benchmark, startDate_chanye, endDate, all_jingqi_yuanzhi, data_hy)
                core_indicator, timelag, dir_ = pick_indicator(all_jingqi_yuanzhi, ex_ret, thresh1=1, thresh2=7,thresh3=0.05, thresh4=0.1)
                core_indicator_True = [core_indicator[i] for i in range(len(core_indicator)) if dir_[i] == 1]
                core_indicator_False = [core_indicator[i] for i in range(len(core_indicator)) if dir_[i] == -1]
                core_indicator_df.loc[indus, ['核心指标', '核心指标数量', '正向指标', '负向指标']] = [core_indicator,len(core_indicator),core_indicator_True,core_indicator_False]
                if len(core_indicator) == 0:
                    nocore_hy.append(indus)
                else:
                    jingqi_indicator1,jingqi_indicator1_core = GetAggIdx3(all_jingqi_yuanzhi.loc[:, core_indicator], indus, timelag, dir_)
                    writer = pd.ExcelWriter(f"{fp_chanye}/有效指标dump/{indus}.xlsx")
                    jingqi_indicator1.to_excel(writer, sheet_name='合成指数')
                    all_jingqi_yuanzhi.loc[:, core_indicator].to_excel(writer, sheet_name='有效指数')
                    jingqi_indicator1_core.to_excel(writer, sheet_name='有效指数lag后')
                    core_indicator_df.loc[indus].to_excel(writer, sheet_name='核心指标信息')
                    writer._save()
                    jingqi_indicator1_forall = pd.concat([jingqi_indicator1_forall, jingqi_indicator1], axis=1)
            except:
                error_hy.append(indus)
                print('【失败】%s行业景气指标合成' % indus)
                continue
        writer = pd.ExcelWriter(fp_chanye + '/行业景气指数.xlsx')
        jingqi_indicator1_forall.to_excel(writer, sheet_name='行业景气指数')
        core_indicator_df.to_excel(writer, sheet_name='核心指标')
        writer._save()
        print('核心指标为0:', nocore_hy,'错误:', error_hy)

    #本次更新1：有效行业会后续动态选择，而非前期指定（根据景气指数滚动5年相关性筛选有效行业）
    jingqi_indicator1_forall = pd.read_excel(fp_chanye + '/行业景气指数.xlsx', sheet_name='行业景气指数', index_col=0)
    jingqi_indicator1_forall.index = pd.DatetimeIndex(jingqi_indicator1_forall.index)
    jingqi_indicator1_forall = trans_date2tdate(jingqi_indicator1_forall)
    jingqi_indicator1_forall = jingqi_indicator1_forall[(jingqi_indicator1_forall.index >= startDate_chanye)]
    jingqi_change = (jingqi_indicator1_forall.shift(-1) - jingqi_indicator1_forall).dropna(axis=0, how='all')
    jingqi_change = jingqi_change[(jingqi_change.index >= startDate_chanye)&(jingqi_change.index <= endDate)]
    catchDtAll = get_tdate_list(startDate_chanye, endDate, 'M')
    ts = pd.DatetimeIndex(catchDtAll).tolist()
    fundrtn, benchrtn = pred_m_rtndata(hylist, hylist.index.tolist(), benchmark, startDate_chanye, endDate,jingqi_change, ts)
    exrtn = fundrtn.sub(benchrtn[benchmark], axis=0)
    corr_jingqi = pd.DataFrame(np.nan, index=jingqi_change.index, columns=jingqi_change.columns)
    for date_i in corr_jingqi.index[61:]:
        for i in jingqi_change.columns:
            corr_jingqi.loc[date_i, i] = jingqi_change[i].loc[:date_i].iloc[-60 - 1:-1].corr(exrtn.loc[:date_i, i].iloc[-60-1:-1])
            #如果报错尝试下面两行代码
            # a2 = exrtn.loc[:date_i, i].iloc[-60 - 1:-1].astype(float)
            # corr_jingqi.loc[date_i, i] = jingqi_change[i].loc[:date_i].iloc[-60 - 1:-1].corr(a2)
    jingqiTOP1, jingqiTAIL5 = pred_m_group_1x(jingqi_change[corr_jingqi.fillna(method='bfill')>0.15],a='景气边际变化',N=5)
    nav_m = get_nav_m(hylist,hylist.index.tolist(), startDate_chanye, endDate)
    base_m = get_base_m(benchmark, startDate_chanye, endDate)
    backtest_dic[model_name], plot_data_dic[model_name], result_all_dic[model_name], rili_table_dic[model_name] = pred_m_outputs(benchmark, fundrtn, benchrtn, jingqiTOP1, jingqiTAIL5,jingqi_change)
    backtest_to_excel([backtest_dic[model_name],plot_data_dic[model_name],result_all_dic[model_name],rili_table_dic[model_name],transDic2Df(jingqiTOP1,model_name),transDic2Df(jingqiTAIL5,model_name),jingqi_indicator1_forall[jingqiTOP1[jingqi_change.index[-1]]],nav_m[jingqiTOP1[jingqi_change.index[-1]]],nav_m.div(base_m['CLOSE'],axis=0)[jingqiTOP1[jingqi_change.index[-1]]],corr_jingqi,corr_jingqi[corr_jingqi>0.15].iloc[-1].dropna().sort_values(ascending=False),jingqi_change.iloc[-1]],['回测结果','plot_data','result_all','日历效应','TOP','TAIL','画图用_景气向上行业','画图用_景气向上行业走势','画图用_景气向上行业超额走势','滚动60月相关性','最新适用行业','全部行业景气变化'],fp_chanye+'中观产业景气回测.xlsx')

    t=printtime(t)

    ''' 3.日历效应模型 '''
    model_name = '日历效应模型'
    fp_calender = fp_output + '3.日历效应/'
    os.makedirs(fp_calender, exist_ok=True)
    endDate_calendar = get_date_list_m(startDate_calendar, endDate)[-1]
    # 提取收盘数据
    all_close = w.wsd(hylist.index.tolist(), "close", startDate_calendar, endDate_calendar, "Period=M;Fill=Previous", usedf=True)[1]
    bench_close = w.wsd(benchmark, "close", startDate_calendar, endDate_calendar, "Period=M;Fill=Previous", usedf=True)[1]
    all_rtn = (all_close / all_close.shift(1) - 1).iloc[1:, :]
    bench_rtn = (bench_close / bench_close.shift(1) - 1).iloc[1:, :]
    all_rtn.index = pd.DatetimeIndex(all_rtn.index)
    bench_rtn.index = pd.DatetimeIndex(bench_rtn.index)
    # 计算胜率矩阵
    ex_ret = all_rtn.subtract(bench_rtn['CLOSE'], axis=0)
    win = ex_ret.copy()
    for i in win.columns:
        win.loc[win[i] > 0, i] = 1
        win.loc[win[i] < 0, i] = 0
    win['月'] = [x.month for x in win.index]

    win_final,win_latest = cal_win_matrix_all(win, hylist)
    win_final = win_final.dropna()
    riliTOP1, riliTAIL5 = pred_m_group_2x_rili(win_final, 0.6, 0.4, 10)
    ts = pd.DatetimeIndex(win_final.index).tolist()
    fundrtn, benchrtn = pred_m_rtndata(hylist, hylist.index.tolist(), benchmark, startDate_calendar, endDate, win_final,ts)
    backtest_dic[model_name], plot_data_dic[model_name], result_all_dic[model_name], rili_table_dic[model_name] = pred_m_outputs(benchmark, fundrtn, benchrtn, riliTOP1, riliTAIL5, win_final)
    backtest_to_excel([backtest_dic[model_name], plot_data_dic[model_name], result_all_dic[model_name], rili_table_dic[model_name],transDic2Df(riliTOP1,model_name), transDic2Df(riliTAIL5,model_name),win_latest,win_final],['回测结果', 'plot_data', 'result_all', '日历效应', 'TOP', 'TAIL','最新日历效应','历史日历效应'], fp_calender + '日历效应回测.xlsx')

    t = printtime(t)

    ''' 4.pbroe模型 '''
    model_name = 'pbroe模型'
    fp_pbroe = fp_output + '4.pbroe/'
    os.makedirs(fp_pbroe, exist_ok=True)
    # ---数据准备---
    # 获取月度交易日序列
    ts0 = get_tdate_list(startDate_pbroe, endDate,'M')
    ##API提取PB，75个行业(注意观察nan值），交易日,日期格式字符串,列名是代码
    pb = w.wsd(hylist.index.tolist(), "pb_lf", startDate_chanye, endDate, "Fill=Previous", usedf=True)[1]
    pb.index = pd.DatetimeIndex(pb.index)
    pb.columns = hylist.loc[pb.columns, '行业名称'].tolist()
    # 读取ROE-TTM数据
    roe = w.wsd(hylist.index.tolist(), "roe_ttm2", startDate_chanye, endDate, "Period=D;Days=Alldays;Fill=Previous", usedf=True)[1]  # 更新2：修改成Alldays
    roe.index = pd.DatetimeIndex(roe.index)
    roe.columns = hylist.loc[roe.columns, '行业名称'].tolist()
    roe = roe[roe.index.isin(pb.index)]

    # ---计算PB、ROE历史百分位---
    window = 1250  # 超参数：回看X年百分位,3.5年等于875个交易日，42个月 #5年等于1250交易日
    # 滚动PB，百分比数值越大说明pb越低,越好
    result_pb = pb.apply(lambda x: roll_rank(x, window, False)).iloc[window-1:]
    # 滚动ROE，百分比数值越大说明ROE越高，越好
    result_roe = roe.apply(lambda x: roll_rank(x, window, True)).iloc[window-1:]

    # 对齐PB百分位和ROE百分位：针对交集法
    result_pb = result_pb[result_pb.index.isin(result_roe.index)]

    # ---生成历史PB-ROE多空行业矩阵
    ts0 = pd.DatetimeIndex(ts0)
    ts = ts0[ts0 >= result_pb.index[0]]
    pbroeTOP1, pbroeTAIL5 = pred_m_group_2x_quantile(ts, result_pb, result_roe)
    backtest_to_excel([result_pb,result_roe,transDic2Df(pbroeTAIL5,model_name)],['result_pb', 'result_roe', 'PBROE预警名单'], fp_pbroe + 'pbroe回测.xlsx')

    t = printtime(t)

    ''' 5.拥挤度模型 '''
    model_name = '拥挤度模型'
    fp_crowd = fp_output + '5.拥挤度/'
    os.makedirs(fp_crowd, exist_ok=True)
    # ---数据准备---
    # API取行业成交额
    sqlstr = '''select S_INFO_WINDCODE,TRADE_DT,S_DQ_AMOUNT,S_VAL_MV,S_DQ_CLOSE
                from winddf.ASWSIndexEOD
                where (S_INFO_WINDCODE in {} ) and (TRADE_DT>= {})
                order by TRADE_DT'''.format(tuple(hylist.index.tolist()), startDate_crowd)
    class1 = pd.read_sql(sqlstr, windconn)
    hy_vol = get_target_f('S_DQ_AMOUNT', class1, hylist,startDate_crowd, endDate)
    hy_mv = get_target_f('S_VAL_MV', class1, hylist,startDate_crowd, endDate)
    hy_close = get_target_f('S_DQ_CLOSE', class1, hylist,startDate_crowd, endDate)
    print('行业成交额、市值、收盘数据提取完毕')
    trade_pct = hy_vol / hy_mv  # 计算成交占比指标
    trade_pct2 = trade_pct.rolling(window=5).mean()

    crowd_sig = pd.DataFrame()
    crowd_pic = pd.DataFrame()
    for indus in hylist['行业名称'].tolist():
        print(indus)
        crowd_sig_i,crowd_pic_i = cal_crowd(trade_pct2, indus,'是否上穿2倍')
        crowd_sig_i.name = indus
        crowd_pic_i.name = indus
        crowd_sig = pd.concat([crowd_sig, crowd_sig_i], axis=1)
        crowd_pic = pd.concat([crowd_pic, crowd_pic_i], axis=1)

    crowdTAIL5 = crowd_sig.replace(1, -1)  # 对空头信号进行正负向调整
    crowdTAIL5.index = pd.to_datetime(crowdTAIL5.index)
    backtest_to_excel([crowd_pic,crowdTAIL5],['拥挤度数值', '拥挤度预警名单'], fp_crowd + '拥挤度回测.xlsx')

    t = printtime(t)

    ''' 6.模型复合'''
    ts0 = get_tdate_list(startDate_anapre, endDate,'M')
    ts0 = pd.DatetimeIndex(ts0)  # ts0是主要的复合模型对齐时间参数

    anapre_Signal = trans_df01(hylist,transDic2Df(ROEchangeTOP1,'行业',ts0[0]))
    anapre_Weight = generate_model_weight(anapre_Signal,rili_table_dic['分析师预期模型']).loc[ts0[0]:]
    jingqi_Signal = trans_df11(hylist,transDic2Df(jingqiTOP1,'行业',ts0[0]),transDic2Df(jingqiTAIL5,'行业',ts0[0]))
    jingqi_Weight = generate_model_weight(jingqi_Signal,rili_table_dic['中观产业景气模型']).loc[ts0[0]:]
    win_Signal = pd.DataFrame(columns=hylist['行业名称'].tolist(), index=ts0, data=1)
    win_Weight = win_final.copy()
    pbroe_Signal = trans_df01(hylist,transDic2Df(pbroeTAIL5,'行业'),-1).reindex(ts0).fillna(0)
    crowd_Signal = crowdTAIL5.loc[ts0]

    # ---分数加总，并存储
    score1 = anapre_Signal * anapre_Weight + jingqi_Signal * jingqi_Weight + win_Signal * win_Weight
    score2 = score1 + pbroe_Signal * 0.5 + crowd_Signal * 0.5
    writer_score = pd.ExcelWriter(fp_output+'1.1.1子模型打分结果.xlsx', engine='xlsxwriter')
    anapre_Signal.to_excel(writer_score, sheet_name='分析师预期模型')
    jingqi_Signal.to_excel(writer_score, sheet_name='行业景气模型')
    win_Signal.to_excel(writer_score, sheet_name='日历效应模型')
    pbroe_Signal.to_excel(writer_score, sheet_name='pbroe模型')
    crowd_Signal.to_excel(writer_score, sheet_name='拥挤度模型')
    score1.to_excel(writer_score, sheet_name='前三个模型复合')
    score2.to_excel(writer_score, sheet_name='五个模型复合')
    writer_score._save()
    t = printtime(t)

    fundrtn, benchrtn = pred_m_rtndata(hylist, hylist.index.tolist(), benchmark, startDate_anapre, endDate, score1, ts0.tolist())
    score1TOP1, score1TAIL5 = pred_m_group_2x_rili(score1, -np.inf, np.inf, 10)
    model_name = '前三个模型复合'
    backtest_dic[model_name], plot_data_dic[model_name], result_all_dic[model_name], rili_table_dic[model_name] = pred_m_outputs(benchmark, fundrtn, benchrtn, score1TOP1, score1TAIL5, score1)
    score2TOP1, score2TAIL5 = pred_m_group_2x_rili(score2, -np.inf, np.inf, 10)
    model_name = '五个模型复合'
    backtest_dic[model_name], plot_data_dic[model_name], result_all_dic[model_name], rili_table_dic[model_name] = pred_m_outputs(benchmark, fundrtn, benchrtn, score2TOP1, score2TAIL5, score2)


    # 输出最终5模型复合结果表
    final_output = pd.concat([anapre_Signal.iloc[-1, :], anapre_Weight.iloc[-1, :],jingqi_Signal.iloc[-1, :], jingqi_Weight.iloc[-1, :], win_Weight.iloc[-1, :], pbroe_Signal.iloc[-1, :], crowd_Signal.iloc[-1, :],score1.iloc[-1, :], score2.iloc[-1, :]], axis=1)
    final_output.columns = ['分析师预期模型信号', '分析师预期模型权重', '行业景气模型信号', '行业景气模型权重', '日历效应模型胜率', 'PB-ROE模型预警信号*0.5', '拥挤度模型预警信号*0.5', '胜率三模型总分', '短期五模型综合分']
    writer = pd.ExcelWriter(fp_output+'1.1.0TAA.xlsx',engine='xlsxwriter')
    rili_table_dic['分析师预期模型'].to_excel(writer, sheet_name='分析师预期日历效应', startcol=0, startrow=0, freeze_panes=[1, 1])
    hy_ROE_show.to_excel(writer, sheet_name='分析师预期单一模型展示结果', startcol=0, startrow=0, freeze_panes=[1, 1])
    rili_table_dic['中观产业景气模型'].to_excel(writer, sheet_name='产业景气日历效应', startcol=0, startrow=0, freeze_panes=[1, 1])
    win_latest.to_excel(writer, sheet_name='日历效应单一模型展示结果', startcol=0, startrow=0, freeze_panes=[1, 1])
    rili_table_dic['日历效应模型'].to_excel(writer, sheet_name='日历效应日历效应', startcol=0, startrow=0,freeze_panes=[1, 1])
    rili_table_dic['前三个模型复合'].to_excel(writer, sheet_name='胜率模型日历效应', startcol=0, startrow=0,freeze_panes=[1, 1])
    rili_table_dic['五个模型复合'].to_excel(writer, sheet_name='复合5模型日历效应', startcol=0, startrow=0,freeze_panes=[1, 1])
    final_output = final_output.sort_index().sort_values(by='短期五模型综合分', axis=0, ascending=False)
    final_output['综合分排名'] = final_output['短期五模型综合分'].rank(method='first', ascending=False)
    final_output.to_excel(writer, sheet_name='短期五模型最新一期打分表', startcol=0, startrow=0, freeze_panes=[1, 1])
    excel_format(writer)

    # 输出用于月内跟踪的数据

    writer = pd.ExcelWriter(fp_output + '1.1.2月内跟踪结果.xlsx', engine='xlsxwriter')
    pd.Series(ROEchangeTOP1[ts0[-1]]).to_excel(writer, sheet_name='分析师预期模型',index=False)
    pd.Series(jingqiTOP1[ts0[-1]]).to_excel(writer, sheet_name='中观产业景气模型',index=False)
    pd.Series(riliTOP1[ts0[-1]]).to_excel(writer, sheet_name='日历效应模型',index=False)
    pd.Series(pbroeTAIL5[ts0[-1]]).to_excel(writer, sheet_name='pbroe模型',index=False)
    pd.Series(crowdTAIL5[crowdTAIL5==-1].loc[ts0[-1]].dropna().index).to_excel(writer, sheet_name='拥挤度模型',index=False)
    pd.Series([x for x in hylist['行业名称'] if ((x not in pbroeTAIL5[ts0[-1]]) and (x not in crowdTAIL5[crowdTAIL5==-1].loc[ts0[-1]].dropna().index))]).to_excel(writer, sheet_name='赔率模型', index=False)
    pd.Series(score1TOP1[ts0[-1]]).to_excel(writer, sheet_name='前三个模型复合',index=False)
    pd.Series(score2TOP1[ts0[-1]]).to_excel(writer, sheet_name='五个模型复合',index=False)
    writer._save()

    t = printtime(t)

    ''' 7.画图'''
    hy_ROE_show = pd.read_excel(fp_output + '1.分析师预期/分析师预期回测.xlsx', sheet_name='画图用_分析师预期最新结果',index_col=0)
    plot_anapre(hy_ROE_show, hylist, fp_output + '1.分析师预期/',endDate)
    plot_guore(hy_ROE_show, hylist, fp_output ,endDate)

    jingqi_indicator = pd.read_excel(fp_output + '2.中观产业/中观产业景气回测.xlsx', sheet_name='画图用_景气向上行业',index_col=0)
    jingqi_nav = pd.read_excel(fp_output + '2.中观产业/中观产业景气回测.xlsx', sheet_name='画图用_景气向上行业走势',index_col=0)
    jingqi_excess = pd.read_excel(fp_output + '2.中观产业/中观产业景气回测.xlsx', sheet_name='画图用_景气向上行业超额走势',index_col=0)
    plot_jingqi(jingqi_indicator, jingqi_nav, fp_output + '2.中观产业/',endDate)
    plot_jingqi(jingqi_indicator, jingqi_excess, fp_output + '2.中观产业/', endDate,True)
    jingqi_change_latest = pd.read_excel(fp_output + '2.中观产业/中观产业景气回测.xlsx', sheet_name='全部行业景气变化',index_col=0).iloc[:,0]
    jingqi_change_corr = pd.read_excel(fp_output + '2.中观产业/中观产业景气回测.xlsx', sheet_name='最新适用行业',index_col=0).iloc[:,0]
    plot_fusu(jingqi_change_latest,jingqi_change_corr,hylist, fp_output,endDate)

    month_f = str(int(endDate[4:6]) % 12 + 1)
    rili_df = pd.read_excel(fp_output + '3.日历效应/日历效应回测.xlsx', sheet_name='历史日历效应', index_col=0)
    plot_rili(rili_df.iloc[-1].sort_index().sort_values(ascending=False), month_f, fp_output + '3.日历效应/',endDate)

    result_pb = pd.read_excel(fp_output + '4.pbroe/pbroe回测.xlsx', sheet_name='result_pb', index_col=0)
    result_roe = pd.read_excel(fp_output + '4.pbroe/pbroe回测.xlsx', sheet_name='result_roe', index_col=0)
    plot_pbroe(result_pb, result_roe, hylist, fp_output + '4.pbroe/',endDate)
    pbroeTAIL5 = get_toplist(fp_output + '4.pbroe/pbroe回测.xlsx', 'PBROE预警名单').iloc[-1].values[0]

    crowd_pic = pd.read_excel(fp_output + '5.拥挤度/拥挤度回测.xlsx', sheet_name='拥挤度数值', index_col=0)
    plot_crowd(crowd_pic, fp_output + '5.拥挤度/',endDate)
    plot_shuaitui(pbroeTAIL5, crowd_pic, hylist, fp_output, endDate)

    final_output = pd.read_excel(fp_output + '\\1.1.0TAA.xlsx', sheet_name='短期五模型最新一期打分表', index_col=0)
    plot_zhizhang(final_output['胜率三模型总分'], fp_output, endDate)
    plot_unsure(final_output['短期五模型综合分'], fp_output, endDate)

    t = printtime(t)

    ''' 8.宏观观点输出 '''
    macro_df, macro_df_show = show_macro_table(fp_input, fp_output, None, '2007-01-01')
    writer = pd.ExcelWriter(fp_output + '2.1.0宏观分域指标统计.xlsx', engine='xlsxwriter')
    macro_df.to_excel(writer, sheet_name='指标明细')
    macro_df_show.to_excel(writer, sheet_name='指标展示')
    writer._save()

    printtime(1)








