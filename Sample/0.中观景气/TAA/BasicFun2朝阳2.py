# -*- coding: utf-8 -*-
"""
Created on Mon Jun 19 19:31:07 2023

@author: gyrx-liys
"""

#%% 取包、连数据库

#通用基础包
import numpy as np
import pandas as pd
import os
import datetime as dt
from datetime import datetime,timedelta
import time
from dateutil.parser import parse

#计算相关
import statsmodels.api as sm
from scipy import stats
from scipy import signal#求极值
from scipy.stats import spearmanr
from sklearn.metrics import mean_squared_error,r2_score

#取数相关
from WindPy import *
w.start()
import pymssql
import cx_Oracle

windconn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind')
#goalconn = pymssql.connect(host="**************", user="zyyxread", \
#                       password="zyyx2014", database="ZYYX",charset="utf8")
goalconn = cx_Oracle.connect('zxread/Zxre70_#60#d@**************:1523/zxdb')

#画图相关
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True
import matplotlib as mpl
import matplotlib.ticker as ticker
from matplotlib.font_manager import FontProperties
import matplotlib.gridspec as gridspec
from pylab import *
import seaborn as sns

#其他包
import openpyxl        
import warnings
warnings.filterwarnings('ignore')
#%%日期类基础函数

datestyle='%Y-%m-%d'
datestyle2='%Y%m%d'

def get_tdate_list(start_date,end_date,period_type,date_format='%Y%m%d'):
    tdays_df = w.tdays(start_date, end_date, f"Period={period_type}", usedf=True)[1]
    tdays_df.index = pd.to_datetime(tdays_df.index)
    return tdays_df.index.strftime(date_format).tolist()

def trans_date2tdate(df):
    date_df = pd.DataFrame({'自然日': pd.to_datetime(
        get_date_list(df.index.strftime('%Y%m')[0]+'01', df.index.strftime('%Y%m%d')[-1], 'M')),
        '交易日': pd.to_datetime(get_tdate_list(df.index.strftime('%Y%m')[0]+'01',
                                                df.index.strftime('%Y%m%d')[-1], 'M'))})
    df.index = date_df.set_index('自然日').loc[df.index]['交易日'].values
    df.index = pd.to_datetime(df.index)
    return df


#形成日频自然日序列,输出'%Y%m%d'
def get_date_list_d(begin_date,end_date):
    date_list=[x.strftime('%Y%m%d') for x in pd.date_range(start=begin_date,end=end_date,freq='D')]
    return date_list

#形成月频自然日序列,输出'%Y%m%d'
def get_date_list_m(begin_date,end_date):
    date_list=[x.strftime('%Y%m%d') for x in pd.date_range(start=begin_date,end=end_date,freq='M')]
    return date_list

#形成日频交易日序列,输出'%Y%m%d'
#备注API写法：trade_days = w.tdays(startDate1, endDate, "",usedf=True)[1]
def get_t_days_d(begin_date,endDate):
    sqlStr = """select TRADE_DAYS from winddf.AShareCalendar \
             where TRADE_DAYS between '%s' and '%s' order by TRADE_DAYS"""% (begin_date, endDate)
    trade_days = pd.read_sql(sqlStr, windconn)
    trade_days.drop_duplicates(inplace=True)
    ts = trade_days['TRADE_DAYS'].tolist()
    return ts

#形成月频交易日序列,输出'%Y%m%d'
def get_t_days_m(begin_date,endDate):
    sqlStr = """select TRADE_DAYS from winddf.AShareCalendar \
             where TRADE_DAYS between '%s' and '%s' order by TRADE_DAYS"""% (begin_date, endDate)
    trade_days = pd.read_sql(sqlStr, windconn)
    trade_days.drop_duplicates(inplace=True)
    trade_days['TRADE_DAYS'] = trade_days['TRADE_DAYS'].apply(lambda x:dt.datetime.strptime(x,datestyle2))
    trade_days['year'] = trade_days['TRADE_DAYS'].apply(lambda x: x.year)
    trade_days['month'] = trade_days['TRADE_DAYS'].apply(lambda x: x.month)
    trade_days.columns = ['date','year','month']
    ts = pd.DatetimeIndex(trade_days.groupby\
                           ([trade_days['year'], trade_days['month']])['date'].max())
    ts1 = [dt.datetime.strftime(date,datestyle2) for date in ts] 
    return ts1



#获取上一期，输入数据格式能被parse识别出来日期都行，可以是'%Y%m%d'，输出'%Y%m%d'
def getLagT(curr_t_str,N): #N是默认的时间参数，比如90，代表90天
    curr_t = parse(curr_t_str).date()   
    t1 = (curr_t-dt.timedelta(N)).strftime(datestyle2)
    return t1

#T年划断，对于一些需要划断的数据进行处理，比如每年8.31开始看针对下一年的分析师预期
#注意这个831是核心参数
def getThisYear(t_str):
    if int(t_str[4:9])<831:    
        return int(t_str[0:4])
    else:
        return int(t_str[0:4])+1
    
def get_date_list(begin_date,end_date,freq):
    date_list=[x.strftime('%Y%m%d') for x in pd.date_range(start=begin_date,end=end_date,freq=freq)]
    return date_list

def get_quarter_end_date(date):
    quarter_month = (date.month - 1) // 3 * 3 + 1
    last_day_of_quarter = datetime.date(date.year, quarter_month, 1) + \
                          datetime.timedelta(days=90) - datetime.timedelta(seconds=1)
    return last_day_of_quarter

#%%读取数据类基础函数
def fund_code_fill(x):
    #将基金数字代码转为Wind代码
    if str(x).find('.')>=0:
        return x
    else:
        x_str = str(x).zfill(6)   
    return '.'.join([x_str,'OF'])

#读取sql数据的快捷方式函数        
def ReadSql(sqlStr, conn, arraySize=200000):
    """
    从数据库读取数据
    :param sqlStr: sql查询代码
    :param conn: 数据库连接
    :param fieldNames: 列名映射表
    """
    if type(conn) == cx_Oracle.Connection:
        # oracle数据库Date为保留关键字, 取出的数据列名默认全大写, 需要调整
        cursor = conn.cursor()
        cursor.arraysize = arraySize
        cursor.execute(sqlStr)
        title = [i[0] for i in cursor.description]
        data = pd.DataFrame(cursor.fetchall(), columns=title)
        return data.rename(columns={"Date1": "Date", "Level1": "Level"}, copy=False)
    else:
        raise Exception("conn should be one of cx_Oracle/pymssql Connection.")

#用数据库获取任意指数收盘价的函数（核心是遍历了底层库表）
def index_close(WindCode,startDate,endDate):
    #首先尝试股票市场指数('000906.SH'),数据库AIndexEODPrices
    if len(WindCode)==1:
        wcd = WindCode[0]
        sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.AIndexEODPrices
                    where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s') 
                    order by TRADE_DT""" % (wcd, startDate, endDate)
    else:
        wcd = tuple(WindCode)  
        sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.AIndexEODPrices 
        				 where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                     order by TRADE_DT""".format(wcd,startDate,endDate)                                             
    df_data0 = ReadSql(sqlStr, windconn, arraySize=200000)
    df_data_OF = df_data0.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
    df_data_OF.columns = df_data_OF.columns.get_level_values(1)
    otherWindCode = list(set(WindCode)-set(df_data_OF.columns))   
    #接下来尝试基金指数('885001.WI'),数据库CMFIndexEOD。
    #在2019年11月之前，在AIndexWindIndustriesEOD也有数据，所以一定先用CMFIndexEOD
    if len(otherWindCode)==0:       
        output = df_data_OF
    else:
        if len(otherWindCode)==1:
            wcd = otherWindCode[0]
            sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.CMFIndexEOD
                        where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s') 
                        order by TRADE_DT""" % (wcd, startDate, endDate)
        else:
            wcd = tuple(otherWindCode) 
            sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.CMFIndexEOD 
            				 where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                        order by TRADE_DT""".format(wcd,startDate,endDate)          
        df_data1 = ReadSql(sqlStr, windconn, arraySize=200000)
        df_data_WI = df_data1.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
        df_data_WI.columns = df_data_WI.columns.get_level_values(1)
        otherWindCode2 = list(set(otherWindCode)-set(df_data_WI.columns))       
        #接下来尝试债券指数('CBA00101.CS'),数据库CBIndexEODPrices
        if len(otherWindCode2)==0:       
            output = df_data_OF.merge(df_data_WI, left_index=True, right_index=True, how="outer")           
        else:
            if len(otherWindCode2)==1:
                wcd = otherWindCode2[0]
                sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.CBIndexEODPrices
                            where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s')
                            order by TRADE_DT""" % (wcd, startDate, endDate)
            else:
                wcd = tuple(otherWindCode2)                                      
                sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.CBIndexEODPrices 
            				     where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                            order by TRADE_DT""".format(wcd,startDate,endDate)               
            df_data2 = ReadSql(sqlStr, windconn, arraySize=200000)            
            df_data_CS = df_data2.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
            df_data_CS.columns = df_data_CS.columns.get_level_values(1) 
           
            otherWindCode3 = list(set(otherWindCode2)-set(df_data_CS.columns))       
            #接下来尝试万得/中信行业指数('CI005920.WI'),数据库AIndexWindIndustriesEOD
            if len(otherWindCode3)==0:
                output = df_data_OF.merge(df_data_WI, left_index=True, right_index=True, how="outer")\
                                    .merge(df_data_CS, left_index=True, right_index=True, how="outer")            
            else:
                if len(otherWindCode3)==1:
                    wcd = otherWindCode3[0]
                    sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.AIndexWindIndustriesEOD
                                where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s')  
                                order by TRADE_DT""" % (wcd, startDate, endDate)
                else:
                    wcd = tuple(otherWindCode3)                                
                    sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.AIndexWindIndustriesEOD 
                				     where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                                order by TRADE_DT""".format(wcd,startDate,endDate)  
                df_data3 = ReadSql(sqlStr, windconn, arraySize=200000)
                df_data_fundindex = df_data3.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
                df_data_fundindex.columns = df_data_fundindex.columns.get_level_values(1) 
                otherWindCode4 = list(set(otherWindCode3)-set(df_data_fundindex.columns))       
                #接下来尝试申万指数('801710.SI'),数据库ASWSIndexEOD
                if len(otherWindCode4)==0:
                    output = df_data_OF.merge(df_data_WI, left_index=True, right_index=True, how="outer")\
                                        .merge(df_data_CS, left_index=True, right_index=True, how="outer")\
                                        .merge(df_data_fundindex, left_index=True, right_index=True, how="outer")
                else:
                    if len(otherWindCode4)==1:
                        wcd = otherWindCode4[0]
                        sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.ASWSIndexEOD
                                    where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s') 
                                    order by TRADE_DT""" % (wcd, startDate, endDate)
                    else:
                        wcd = tuple(otherWindCode4)                                 
                        sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.ASWSIndexEOD 
                    				     where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                                    order by TRADE_DT""".format(wcd,startDate,endDate)  
                    df_data4 = ReadSql(sqlStr, windconn, arraySize=200000)
                    df_data_swindex = df_data4.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
                    df_data_swindex.columns = df_data_swindex.columns.get_level_values(1) 
                    
                    output = df_data_OF.merge(df_data_WI, left_index=True, right_index=True, how="outer")\
                                        .merge(df_data_CS, left_index=True, right_index=True, how="outer")\
                                        .merge(df_data_fundindex, left_index=True, right_index=True, how="outer")\
                                        .merge(df_data_swindex, left_index=True, right_index=True, how="outer")
    #output = output.dropna(how='any',axis = 0)
    #当前输出dataframe的index：'%Y%m%d'
    #index：'%Y-%m-%d'
    #output.index = [dt.datetime.strftime(dt.datetime.strptime(date,'%Y%m%d'),'%Y-%m-%d') for date in output.index]
    #输出dataframe的index：date格式
    output.index = [dt.datetime.strptime(date,'%Y%m%d') for date in output.index]
    return output

#定义函数：提取上市股票池
def getlistedstkfromwind(t,windconn,listd=180,delistd=90):
    """
    上市股票
    """
    #上市时间
    sqlstr = ("select S_INFO_WINDCODE,S_INFO_NAME, S_INFO_LISTDATE,S_INFO_DELISTDATE from winddf.AShareDescription")
    description = pd.read_sql(sqlstr,windconn).sort_values( 'S_INFO_WINDCODE')
    description = description[description['S_INFO_LISTDATE'].apply(lambda x:x is not None)]
    #起止日期
    t1 = (t-timedelta(listd)).strftime('%Y%m%d') 
    t2 = (t+timedelta(delistd)).strftime('%Y%m%d') 
    #上市股票
    sublist = description[(description['S_INFO_LISTDATE']<=t1) & ((description['S_INFO_DELISTDATE']>=t2) | (description['S_INFO_DELISTDATE'].isnull() ) )]
    listedstock = sublist['S_INFO_WINDCODE'].sort_values().tolist()
    return listedstock

#定义函数：提取ST股票池
def getststkfromwind(t,windconn,window=90):
    """
    特别处理及摘帽90天内股票
    """
    lagt = t-timedelta(90)
    #ST
    sqlstr = ("select S_INFO_WINDCODE,S_TYPE_ST,ENTRY_DT,REMOVE_DT "
              "from winddf.AShareST "
              "where (ENTRY_DT<='%s' and (REMOVE_DT>='%s' or REMOVE_DT is null )) "
              "or (REMOVE_DT>='%s' and REMOVE_DT<='%s' )" 
              %(t.strftime('%Y%m%d'),t.strftime('%Y%m%d'),lagt.strftime('%Y%m%d'),t.strftime('%Y%m%d') ) )
    data = pd.read_sql(sqlstr,windconn)
    data = data.drop_duplicates(subset=['S_INFO_WINDCODE'])
    data['isST'] = True
    ststk = data.set_index('S_INFO_WINDCODE')['isST'].sort_index()
    return ststk

#根据股票读取的excel数据进行最新一期填补，例如自由流通市值、收盘价等等
def stock_data_fill(stock_close_read,listedstk,endDate):
    #从数据库提取新数据_填补横向，即新增股票的过往数据
    data_t0 = list(stock_close_read.index)[0]
    data_t1 = list(stock_close_read.index)[-1]
    stocklist_close = list(set(listedstk)-set(stock_close_read.columns))
    if len(stocklist_close)==0:
        stock_close1 = stock_close_read.copy()
    else:
        sql = """select S_INFO_WINDCODE,TRADE_DT,S_DQ_CLOSE_TODAY,FREE_SHARES_TODAY \
        from winddf.AShareEODDerivativeIndicator \
        where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {}) \
        order by TRADE_DT,S_INFO_WINDCODE""".format(tuple(stocklist_close),data_t0,data_t1)
        df_close1 = pd.read_sql(sql,windconn)
        df_close1['freemv'] = df_close1['FREE_SHARES_TODAY']*df_close1['S_DQ_CLOSE_TODAY']#自由流通市值：万元
        df_close1 = df_close1[['S_INFO_WINDCODE','TRADE_DT','freemv']]
        df_close1.columns = ['stock','date','freemv']
        df_close1['date'] = df_close1['date'].astype('int')
        df_close1 = df_close1.pivot(index='date',columns='stock',values='freemv').ffill()

        stock_close1 = stock_close_read.merge(df_close1, left_on='date', right_on='date', how='left')
    #从数据库提取新数据_填补纵向，即所有股票的近期数据
    output = pd.DataFrame()
    WindCode = stock_close1.columns
    for i in range(int(len(WindCode)/1000)+1):#由于数据库取数限制，故只能按1000条循环读取
        sql = """select S_INFO_WINDCODE,TRADE_DT,S_DQ_CLOSE_TODAY,FREE_SHARES_TODAY \
        from winddf.AShareEODDerivativeIndicator \
        where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {}) \
        order by TRADE_DT,S_INFO_WINDCODE""".format(tuple(WindCode[i*1000:(i+1)*1000]),data_t1,endDate)
        df = pd.read_sql(sql,windconn)
        output = pd.concat([output,df],axis=0)
    stock_close2 = output.copy()
    stock_close2['freemv'] = stock_close2['FREE_SHARES_TODAY']*stock_close2['S_DQ_CLOSE_TODAY']
    stock_close2 = stock_close2[['S_INFO_WINDCODE','TRADE_DT','freemv']]
    stock_close2.columns = ['stock','date','freemv']
    stock_close2['date'] = stock_close2['date'].astype('int')
    stock_close2 = stock_close2.pivot(index='date',columns='stock',values='freemv').ffill()

    stock_close3 = pd.DataFrame(columns=stock_close1.columns)
    stock_close3[stock_close2.columns] = stock_close2

    stock_close_new = stock_close1._append(stock_close3)
    return stock_close_new

def fund_nav(WindCode,startDate,endDate):
    conn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind')
#    startDate = dt.datetime.strftime(dt.datetime.strptime(startDate,'%Y-%m-%d'),'%Y%m%d')
#    endDate = dt.datetime.strftime(dt.datetime.strptime(endDate,'%Y-%m-%d'),'%Y%m%d')
    if len(WindCode)==1:
        wcd = WindCode[0]
        sqlStr = """select F_INFO_WINDCODE,F_NAV_ADJUSTED,PRICE_DATE from winddf.ChinaMutualFundNAV\
                    where (F_INFO_WINDCODE='%s') and (PRICE_DATE between '%s' and '%s')order by PRICE_DATE""" \
                    % (wcd, startDate, endDate)
    else:
        wcd = tuple(WindCode)                                      
        sqlStr = """select F_INFO_WINDCODE,F_NAV_ADJUSTED,PRICE_DATE from winddf.ChinaMutualFundNAV 
        				 where (F_INFO_WINDCODE in {}) and (PRICE_DATE between {} and {})
                    order by PRICE_DATE""".format(wcd,startDate,endDate) 
    df_data0 = ReadSql(sqlStr, conn, arraySize=200000)
    df_data_OF = df_data0.set_index(["PRICE_DATE", "F_INFO_WINDCODE"]).unstack()
    df_data_OF.columns = df_data_OF.columns.get_level_values(1)
    otherWindCode = list(set(WindCode)-set(df_data_OF.columns))
    if len(otherWindCode)==0:       
        output = df_data_OF
    else:
        WindCode_SH = [code[:6]+'.SH' for code in otherWindCode]
        if len(WindCode_SH)==1:
            wcd = WindCode_SH[0]
            sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.ChinaClosedFundEODPrice\
                        where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s')order by TRADE_DT""" \
                        % (wcd, startDate, endDate)
        else:
            wcd = tuple(WindCode_SH) 
            sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.ChinaClosedFundEODPrice 
            				 where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                        order by TRADE_DT""".format(wcd,startDate,endDate)   
        df_data1 = ReadSql(sqlStr, conn, arraySize=200000)
        df_data_SH = df_data1.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
        df_data_SH.columns = df_data_SH.columns.get_level_values(1)
        otherWindCode2 = list(set(WindCode_SH)-set(df_data_SH.columns))
        if len(otherWindCode2)==0:       
            output = df_data_OF.merge(df_data_SH, left_index=True, right_index=True, how="outer")           
        else:
            WindCode_SZ = [code[:6]+'.SZ' for code in otherWindCode2]
            if len(WindCode_SZ)==1:
                wcd = WindCode_SZ[0]
                sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.ChinaClosedFundEODPrice\
                            where (S_INFO_WINDCODE='%s') and (TRADE_DT between '%s' and '%s')order by TRADE_DT""" \
                            % (wcd, startDate, endDate)
            else:
                wcd = tuple(WindCode_SZ)                                      
                sqlStr = """select S_INFO_WINDCODE,S_DQ_CLOSE,TRADE_DT from winddf.ChinaClosedFundEODPrice 
            				     where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {})
                            order by TRADE_DT""".format(wcd,startDate,endDate)   
            df_data2 = ReadSql(sqlStr, conn, arraySize=200000)
            df_data_SZ = df_data2.set_index(["TRADE_DT", "S_INFO_WINDCODE"]).unstack()
            df_data_SZ.columns = df_data_SZ.columns.get_level_values(1)            
            output = df_data_OF.merge(df_data_SH, left_index=True, right_index=True, how="outer").merge(df_data_SZ, left_index=True, right_index=True, how="outer")  
    output.columns = [code[:6]+'.OF' for code in output.columns]   
    #当前输出dataframe的index：'%Y%m%d'
    #index：'%Y-%m-%d'
    #output.index = [dt.datetime.strftime(dt.datetime.strptime(date,'%Y%m%d'),'%Y-%m-%d') for date in output.index]
    #输出dataframe的index：date格式
    output.index = [dt.datetime.strptime(date,'%Y%m%d') for date in output.index]
    return output
   
def fund_nav_ceil(WindCode,startDate,endDate):
    conn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind')  
    #为获取交易日日期
#    t0 = dt.datetime.strftime(dt.datetime.strptime(startDate,'%Y-%m-%d'),'%Y%m%d')
#    t1 = dt.datetime.strftime(dt.datetime.strptime(endDate,'%Y-%m-%d'),'%Y%m%d')
    sqlStr = """select TRADE_DT from winddf.AIndexEODPrices
            where (S_INFO_WINDCODE='000001.SH') and (TRADE_DT between '%s' and '%s') 
            order by TRADE_DT""" % (startDate,endDate)
    d2 = ReadSql(sqlStr, conn, arraySize=200000)
    d2 = d2.set_index(['TRADE_DT'])
    d2.index = [dt.datetime.strptime(date,'%Y%m%d') for date in d2.index]
#    d2.index = [dt.datetime.strftime(dt.datetime.strptime(date,'%Y%m%d'),'%Y-%m-%d') for date in d2.index]   
    output = pd.DataFrame()
    for i in range(int(len(WindCode)/1000)+1):#由于数据库取数限制，故只能按1000条循环读取
        df = fund_nav(WindCode[i*1000:(i+1)*1000],startDate,endDate)
        output = output.merge(df, left_index=True, right_index=True, how="outer")
    output = output.loc[d2.index]
    return output

def fund_rtn(fundCodes,startDate, endDate):
    conn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind')    
    wcd=tuple(fundCodes)                                       
    sqlStr = """select F_INFO_WINDCODE,F_NAV_ADJUSTED,PRICE_DATE from winddf.ChinaMutualFundNAV 
				 where (F_INFO_WINDCODE in {}) and (PRICE_DATE between {} and {})
				 order by PRICE_DATE""".format(wcd,startDate,endDate)   
    nav=ReadSql(sqlStr, conn, arraySize=200000)
    adjNav = nav.set_index(["PRICE_DATE", "F_INFO_WINDCODE"]).unstack()
    adjNav.columns = adjNav.columns.get_level_values(1)
    output = (adjNav/adjNav.shift(1)-1)[1:]    
    #当前输出dataframe的index：'%Y%m%d'
    #index：'%Y-%m-%d'
    #output.index = [dt.datetime.strftime(dt.datetime.strptime(date,'%Y%m%d'),'%Y-%m-%d') for date in output.index]
    #输出dataframe的index：date格式
    output.index = [dt.datetime.strptime(date,'%Y%m%d') for date in output.index]
    return output 

#%%数据清洗和处理类基础函数
    
#将股票数字代码转为Wind代码   
def code_fill(x):
    if str(x).find('.')>=0:
        return x
    else:
        x_str = str(x).zfill(6)
        if x_str.startswith('0') or x_str.startswith('3'):
            return '.'.join([x_str,'SZ'])
        elif x_str.startswith('6'):
            return '.'.join([x_str,'SH'])
        elif x_str.startswith('8') or x_str.startswith('4'):
            return '.'.join([x_str,'BJ'])
        else:
            return np.nan

#计算TTM值
def CalTTM(edbdata,method):#输入：method=0,1分别为：当月值，累计值
    if method == 0:
        print("当月值转换成TTM")
        min_month = np.min([x.month for x in edbdata.dropna().index[-36:]])#确定最小月份，是1or2or3月
        loc_month = [x for x in edbdata.dropna().index if x.month>=min_month]
        edbdata_ttm = edbdata.loc[loc_month,:].dropna().rolling(12-min_month+1).sum().dropna()#移动加总，计算TTM值
        edbdata_ttm.columns = [edbdata_ttm.columns[0].replace("当月","TTM")]
    elif method == 1:
        print("累计值转换成TTM")
        min_month = np.min([x.month for x in edbdata.dropna().index[-36:]])#确定最小月份，是1or2or3月
        edbdata_m = edbdata.dropna().diff(1)#算出当月值
        edbdata_m.loc[[x for x in edbdata_m.index if x.month==min_month],:] = edbdata.loc[[x for x in edbdata_m.index if x.month==min_month],:]#替换最小月份数据
        edbdata_ttm = edbdata_m.dropna().rolling(12-min_month+1).sum().dropna()#移动加总，计算TTM值
        edbdata_ttm.columns = [edbdata_ttm.columns[0].replace("累计","TTM")]
    return edbdata_ttm

#计算同比值，并改名
def Calyoy(data):
    min_month = np.min([x.month for x in data.index[-36:]])#过去所有时间的最小月份
    data_yoy = data.pct_change(12-min_month+1).dropna()
    data_yoy.columns = [data_yoy.columns[0]+"同比"]
    return data_yoy

#离群值的处理
def boxplot_fill(col):
    # 根据iqr计算异常值判断阈值
    u_th = col.quantile(0.9) # 上界
    l_th = col.quantile(0.1) # 下界
    # 定义转换函数：如果数字大于上界则用上界值填充，小于下界则用下界值填充。
    def box_trans(x):
          if x > u_th:
           return u_th
          elif x < l_th:
           return l_th
          else:
           return x
    return col.map(box_trans)

#多个指标合成一个指标：考虑时间lag
def GetAggIdx(df,indus,timelag):#输入所有符合标准的宏观中观指标，行业名称，领先期
    # df = all_jingqi_yuanzhi.loc[:,core_indicator]
#    df = df.fillna(method='ffill').dropna(how='all',axis=0)
    data = pd.DataFrame([],index=df.index)
    for i in range(df.shape[1]):
        df_lag = int(timelag[i])
        df_value = df.iloc[:,i]
        df_value2 = df_value.shift(df_lag)
        df_value3 = (df_value2-df_value2.mean())/df_value2.std()#标准化
        data = pd.concat([data,df_value3],axis=1,sort=True)
        data = data.dropna(axis=0)
        data2 = data.mean(axis=1)
        data2 = data2.to_frame()
        data2.columns=[indus]
    return data2

def GetAggIdx2(df,indus,timelag,dir_):#输入所有符合标准的宏观中观指标，行业名称，领先期
    # df = all_jingqi_yuanzhi.loc[:,core_indicator]
#    df = df.fillna(method='ffill').dropna(how='all',axis=0)
    data = pd.DataFrame([],index=df.index)
    for i in range(df.shape[1]):
        df_lag = int(timelag[i])
        df_value = df.iloc[:,i]
        df_value2 = df_value.shift(df_lag)
        df_value3 = (df_value2-df_value2.mean())/df_value2.std()#标准化
        data = pd.concat([data,df_value3*dir_[i]],axis=1,sort=True)
        data = data.dropna(axis=0)
        data2 = data.mean(axis=1)
        data2 = data2.to_frame()
        data2.columns=[indus]
    return data2

def GetAggIdx3(df,indus,timelag,dir_):#输入所有符合标准的宏观中观指标，行业名称，领先期
    max_timelag = np.max(timelag)
    index_  = list(df.index) + list(pd.to_datetime(get_date_list(df.index.strftime('%Y-%m-%d')[-1],str(int(df.index.strftime('%Y-%m-%d')[-1][:4])+1)+df.index.strftime('%Y-%m-%d')[-1][4:],'M')[1:max_timelag+1],format='%Y%m%d'))
    data = pd.DataFrame([],index=index_)
    for i in range(df.shape[1]):
        df_lag = int(timelag[i])
        df_value = df.iloc[:,i]
        df_value3 = (df_value-df_value.mean())/df_value.std()#标准化
        data = pd.concat([data,df_value3*dir_[i]],axis=1,sort=True)
        data[df.columns[i]] = data[df.columns[i]].shift(df_lag)
    # data = data.dropna(axis=0)
    data2 = data.mean(axis=1)
    data2 = data2.to_frame()
    data2.columns=[indus]
    return data2,data

def toline(df): #辅助函数，多行合并成一行
    return ','.join(df.values)

def str_to_list(x): #字符串格式的伪列表转化成列表的函数
    x = x.replace("[","")
    x = x.replace("]","")
    x = x.replace("'","")
    x = x.replace(" ","")
    lst = []
    rg_str_lst = x.strip().split(',')
    for i in rg_str_lst:
        lst.append(i)
    return lst
#%%基本统计模型类函数
    
#计算领先滞后pearson相关系数
def CalMaxCorrT(data_x,data_y,tflag):#输入:y预测变量，x自变量，tflag是0月，1季
    #根据前推和后推12期，寻找最大相关系数时的领先/滞后期数
    #data_x = df_xy.iloc[:,[2]].dropna()
    #data_y = df_xy.iloc[:,[1]].dropna()
    #tflag = 0
    if tflag==0:
        n = 7
#    elif tflag==1:
#        n,nt = 4,'季'
    data_xy = pd.concat([data_x,data_y],axis=1,sort=False).dropna()
    corr_xy = pd.DataFrame()
    for i in range(1,n+1):
        data_xi_lag = pd.DataFrame(data_x.values[:-i],index=data_x.index[i:],columns=data_x.columns)#后移N期，领先性
        data_xi_fwd = pd.DataFrame(data_x.values[i:],index=data_x.index[:-i],columns=data_x.columns)#提前N期，滞后性
        data_xylag = pd.concat([data_xi_lag,data_y],axis=1,sort=False).dropna().corr()  
        data_xyfwd = pd.concat([data_xi_fwd,data_y],axis=1,sort=False).dropna().corr() 
        corr_xy = pd.concat([corr_xy,pd.DataFrame([data_xylag.iloc[1,0],data_xyfwd.iloc[1,0]],index=[i,-i],columns=['%s与%s领先滞后相关系数'%(data_x.columns[0],data_y.columns[0])])],axis=0)#i代表领先i期，-i代表滞后i期的相关性
    corr_xy = pd.concat([corr_xy,pd.DataFrame(data_xy.corr().iloc[1,0],index=[0],columns=['%s与%s领先滞后相关系数'%(data_x.columns[0],data_y.columns[0])])],axis=0).sort_index(ascending=True)
    result_best = corr_xy[corr_xy==corr_xy.max()].dropna()
    result_best = result_best.iloc[0,:].to_frame().T
    print(result_best)
    return corr_xy,result_best

#计算领先滞后spearman秩相关系数
def CalMaxCorrS(data_x,data_y,tflag):#输入:y预测变量，x自变量，tflag是0月，1季
    #根据前推和后推n期，寻找最大相关系数时的领先/滞后期数
#    data_x=jingqi_indicator2
#    data_y=ex_jibenmian2['净利润TTM同比'].to_frame()
#    tflag=0
    if tflag==0:
        n = 7
#    elif tflag==1:
#        n,nt = 4,'季'
#    data_xy = pd.concat([data_x,data_y],axis=1,sort=False).dropna()
    corr_xy = pd.DataFrame()
    for i in range(1,n+1):
        data_xi_lag = pd.DataFrame(data_x.values[:-i],index=data_x.index[i:],columns=data_x.columns)#后移N期，领先性
        data_xi_fwd = pd.DataFrame(data_x.values[i:],index=data_x.index[:-i],columns=data_x.columns)#提前N期，滞后性
        data_xylag = stats.spearmanr(data_xi_lag,data_y[i:])[0]
        data_xyfwd = stats.spearmanr(data_xi_fwd,data_y[:-i])[0]
        corr_xy = pd.concat([corr_xy,pd.DataFrame([data_xylag,data_xyfwd],index=[i,-i],columns=['%s与%s领先滞后spearmanr'%(data_x.columns[0],data_y.columns[0])])],axis=0)#i代表领先i期，-i代表滞后i期的相关性
    corr_xy = pd.concat([corr_xy,pd.DataFrame(stats.spearmanr(data_x,data_y)[0],index=[0],columns=['%s与%s领先滞后spearmanr'%(data_x.columns[0],data_y.columns[0])])],axis=0).sort_index(ascending=True)
    result_best = corr_xy[corr_xy==corr_xy.max()].dropna()
    result_best = result_best.iloc[0,:].to_frame().T
    print(result_best)
    return corr_xy,result_best

#OLS回归+t检验
def tanalyze(data_x,data_y):
    """相关性分析"""
#    data_x = macro_dz_adj.iloc[:-2,i].to_frame()
#    data_y = data_mkt.loc[macro_dz_adj.index[2]:].to_frame()
    # OLS回归
    ols_res = sm.OLS(data_y, sm.add_constant(data_x)).fit()
    r2 = ols_res.rsquared
    p = ols_res.pvalues[1]
    
    # 因子上/下行期间指数收益率
    up_dates = data_x.diff(1).dropna()[data_x.diff(1).dropna()> 0].dropna().index.tolist()
    down_dates = data_x.diff(1).dropna()[data_x.diff(1).dropna()< 0].dropna().index.tolist()
#    up_ret = (data_y.diff(1).dropna().loc[up_dates] + 1).product() - 1
#    down_ret = (data_y.diff(1).dropna().loc[down_dates] + 1).product() - 1

    # 因子每个上行/下行期间收益率
    up_ret_array = data_y.diff(1).dropna().loc[up_dates]
    down_ret_array = data_y.diff(1).dropna().loc[down_dates]

    # 因子上行下行收益率序列与0比较，单样本t检验
    if len(up_ret_array) <= 1:
        up_ret_tvalues = np.nan
    else:
#        up_ret_tvalues = stats.ttest_1samp(up_ret_array, 0, alternative='two-sided')[0]
        up_ret_tvalues = stats.ttest_1samp(up_ret_array, 0)[0]

    if len(down_ret_array) <= 1:
        down_ret_tvalues = np.nan
    else:
#        down_ret_tvalues = stats.ttest_1samp(down_ret_array, 0, alternative='two-sided')[0]
        down_ret_tvalues = stats.ttest_1samp(down_ret_array, 0)[0]
    # 双独立样本t检验
    if min(len(up_ret_array), len(down_ret_array)) <= 1:
        two_tvalues = np.nan
    else:
        two_tvalues = stats.ttest_ind(up_ret_array, down_ret_array, equal_var=True)[0]

    t_avg = (abs(up_ret_tvalues) + abs(down_ret_tvalues) + abs(two_tvalues))/3
    return r2,t_avg,p


def tanalyze2(data_x, data_y):
    """相关性分析"""
    #    data_x = macro_dz_adj.iloc[:-2,i].to_frame()
    #    data_y = data_mkt.loc[macro_dz_adj.index[2]:].to_frame()
    # OLS回归
    ols_res = sm.OLS(data_y, sm.add_constant(data_x)).fit()
    r2 = ols_res.rsquared
    p = ols_res.pvalues[1]
    dir_ = np.sign(ols_res.params[1])

    # 因子上/下行期间指数收益率
    up_dates = data_x.diff(1).dropna()[data_x.diff(1).dropna() > 0].dropna().index.tolist()
    down_dates = data_x.diff(1).dropna()[data_x.diff(1).dropna() < 0].dropna().index.tolist()
    #    up_ret = (data_y.diff(1).dropna().loc[up_dates] + 1).product() - 1
    #    down_ret = (data_y.diff(1).dropna().loc[down_dates] + 1).product() - 1

    # 因子每个上行/下行期间收益率
    up_ret_array = data_y.diff(1).dropna().loc[up_dates]
    down_ret_array = data_y.diff(1).dropna().loc[down_dates]

    # 因子上行下行收益率序列与0比较，单样本t检验
    if len(up_ret_array) <= 1:
        up_ret_tvalues = np.nan
    else:
        #        up_ret_tvalues = stats.ttest_1samp(up_ret_array, 0, alternative='two-sided')[0]
        up_ret_tvalues = stats.ttest_1samp(up_ret_array, 0)[0]

    if len(down_ret_array) <= 1:
        down_ret_tvalues = np.nan
    else:
        #        down_ret_tvalues = stats.ttest_1samp(down_ret_array, 0, alternative='two-sided')[0]
        down_ret_tvalues = stats.ttest_1samp(down_ret_array, 0)[0]
    # 双独立样本t检验
    if min(len(up_ret_array), len(down_ret_array)) <= 1:
        two_tvalues = np.nan
    else:
        two_tvalues = stats.ttest_ind(up_ret_array, down_ret_array, equal_var=True)[0]

    t_avg = (abs(up_ret_tvalues) + abs(down_ret_tvalues) + abs(two_tvalues)) / 3
    return r2, t_avg, p, dir_
#%%回测类函数
    
#准备回测所需的月度涨跌幅（交易日）数据函数,需要嵌套使用index_close函数
def pred_m_rtndata(hylist,WindCode,benchmark,startDate,endDate,x_change,ts):
    #x_change是自变量边际变化矩阵，也是形成invlist1和invlist5信号的输入矩阵
    
    #ORACLE提取行业指数净值
    nav = index_close(WindCode,startDate,endDate)
    
    #提取基准指数净值
    bench_nav = w.wsd(benchmark, "close", startDate, endDate, "Fill=Previous",usedf=True)[1]
    bench_nav.index = pd.DatetimeIndex(bench_nav.index)
    bench_nav.columns = [benchmark]
    
    #获取每个月涨跌幅，进行错位match，然后取平均，再累乘
    fundrtn = pd.DataFrame(index = x_change.index[:-1], columns = nav.columns)
    for i in x_change.index[:-1]:
        fundrtn.loc[i] = nav.loc[ts[ts.index(i)+1]] / nav.loc[ts[ts.index(i)]] - 1
    
    benchrtn = pd.DataFrame(index = x_change.index[:-1], columns = bench_nav.columns)
    for i in x_change.index[:-1]:
        benchrtn.loc[i] = bench_nav.loc[ts[ts.index(i)+1]] / bench_nav.loc[ts[ts.index(i)]] - 1
    
    fundrtn.columns = hylist.loc[fundrtn.columns,'行业名称'].tolist()
    return fundrtn,benchrtn

#形成进出对象字典--多头组和空头组(排序取前N法，剔除负值)
def pred_m_group_2x(x_change1,x_change2,a,b,thresh2,N): 
    #a和b分别是给x_change1和x_change2起的列名str,thresh2是x_change2的阈值float,N是选取前N个对象
    hyinvlist1 = {}
    hyinvlist5 = {} 
    for date in x_change1.index:
            df1 = x_change1[(x_change1.index == date)].T
            aux = x_change2[(x_change2.index == date)].T
          
            df2 = pd.merge(df1,aux,left_index=True,right_index=True)
            df2.columns = [a,b]

            #设定aux条件阈值
            df2 = df2[df2[b]>=thresh2] 
            
            #按照a进行排序,并剔除负值行业
            df2 = df2.sort_values(by=[a],ascending = False).dropna()
            if len(df2) >=N:
                if df2.iloc[N-1,0] >0:
                    top1 = df2.index[:N].tolist()
                    top1 = df2[df2[a].rank(method='min',ascending=False)<=N].index.tolist()
                else:
                    top1 = df2[df2[a]>0].index.tolist()
                if df2.iloc[-(N-1),0] <0:
                    top5 = df2.index[-N:].tolist()
                    top5 = df2[df2[a].rank(method='min')<=N].index.tolist()
                else:
                    top5 = df2[df2[a]<0].index.tolist()
            else:
                top1 = df2[df2[a]>0].index.tolist()
                top5 = df2[df2[a]<0].index.tolist()
            hyinvlist1[pd.Timestamp(date)] = top1
            hyinvlist5[pd.Timestamp(date)] = top5
    return hyinvlist1,hyinvlist5
def pred_m_group_2x_quantile(ts,x_change1,x_change2):
    fundinvlist1 = dict()
    fundinvlist5 = dict()
    for date in ts:
        df1 = x_change1[x_change1.index == date].T
        df2 = x_change2[x_change2.index == date].T
        top_pb = df1[(df1 >= df1.quantile(0.7))].dropna() #参数0.5 0.7
        top_roe = df2[(df2 >= df2.quantile(0.7))].dropna()
        tail_pb = df1[(df1 < df1.quantile(0.3))].dropna()  #参数0.5 0.3
        tail_roe = df2[(df2 < df2.quantile(0.3))].dropna()
        tail_pb = tail_pb[tail_pb<0.5].dropna()
        tail_roe = tail_roe[tail_roe < 0.5].dropna()
        fundinvlist1[date] =  list(set(top_pb.index).intersection(set(top_roe.index)))
        fundinvlist5[date] =  list(set(tail_pb.index).intersection(set(tail_roe.index)))
    return fundinvlist1,fundinvlist5

#形成进出对象字典--多头组和空头组(排序取前N法，剔除负值)
def pred_m_group_1x(x_change1,a,N): 
    #a是给x_change1起的列名str,N是选取前N个对象
    hyinvlist1 = {}
    hyinvlist5 = {} 
    for date in x_change1.index:
            df2 = x_change1[(x_change1.index == date)].T
            df2.columns = [a]           
            #按照a进行排序,并剔除负值行业
            df2 = df2.sort_values(by=[a],ascending = False).dropna()
            if len(df2) >=N:
                if df2.iloc[N-1,0] >0:
                    top1 = df2.index[:N].tolist()
                    top1 = df2[df2[a].rank(method='min', ascending=False) <= N].index.tolist()
                else:
                    top1 = df2[df2[a]>0].index.tolist()
                if df2.iloc[-(N-1),0] <0:    
                    top5 = df2.index[-N:].tolist()
                    top5 = df2[df2[a].rank(method='min') <= N].index.tolist()
                else:
                    top5 = df2[df2[a]<0].index.tolist()
            else:
                top1 = df2[df2[a]>0].index.tolist()
                top5 = df2[df2[a]<0].index.tolist()
            hyinvlist1[pd.Timestamp(date)] = top1
            hyinvlist5[pd.Timestamp(date)] = top5
    return hyinvlist1,hyinvlist5


def pred_m_group_2x_rili(x_change1, thresh1, thresh2, N):
    # a和b分别是给x_change1和x_change2起的列名str,thresh2是x_change2的阈值float,N是选取前N个对象
    hyinvlist1 = {}
    hyinvlist5 = {}
    for date in x_change1.index:
        df1 = x_change1[x_change1 >= thresh1].loc[date].dropna()
        df1 = df1.sort_values(ascending=False).dropna()
        df2 = x_change1[x_change1 <= thresh2].loc[date].dropna()
        df2 = df2.sort_values(ascending=False).dropna()

        if len(df1) >= N:
            top1 = df1.index[:N].tolist()
            top1 = df1[df1.rank(method='min', ascending=False) <= N].index.tolist()
        else:
            top1 = df1.index.tolist()
        if len(df2) >= N:
            top5 = df2.index[-N:].tolist()
            top5 = df2[df2.rank(method='min') <= N].index.tolist()
        else:
            top5 = df2.index.tolist()
        hyinvlist1[pd.Timestamp(date)] = top1
        hyinvlist5[pd.Timestamp(date)] = top5
    return hyinvlist1, hyinvlist5

#形成backtest矩阵，plot_data数据，回测效果矩阵，日历效应矩阵
def pred_m_outputs(benchmark,fundrtn,benchrtn,hyinvlist1,hyinvlist5,x_change1):
    
    backtest = pd.DataFrame(index = fundrtn.index, columns =\
                            ['数量','基准收益率','1组收益率','5组收益率','多空收益率'])
    for i in fundrtn.index:
    #    print(i)
        backtest.loc[i, '数量'] = len(hyinvlist1[i])
        backtest.loc[i, '基准收益率'] = benchrtn.loc[i][benchmark]
        backtest.loc[i, '1组收益率'] = fundrtn.loc[i, hyinvlist1[i]].mean()
        backtest.loc[i, '5组收益率'] = fundrtn.loc[i, hyinvlist5[i]].mean()
        backtest.loc[i, '多空收益率'] = backtest.loc[i, '1组收益率']-backtest.loc[i, '5组收益率']
    
    #计算PRD
    temp = backtest[backtest['基准收益率']>0]
    p_plus = (temp['1组收益率']/temp['基准收益率']).median()
    temp = backtest[backtest['基准收益率']<0]
    p_minus = (temp['1组收益率']/temp['基准收益率']).median()
    prd = p_plus - p_minus
    
    #形成画图所需的plot_data数据
    plot_data = backtest[['基准收益率','1组收益率','5组收益率','多空收益率']]
    plot_data = (plot_data+1).cumprod()
    add_index = list(set(x_change1.index) - set(plot_data.index))
    data2 = pd.DataFrame(index=list(add_index),columns=plot_data.columns,data=np.nan)
    plot_data = plot_data._append(data2)
    plot_data = plot_data.shift(1)
    plot_data.fillna(1,inplace=True)
    plot_data['多头累计超额'] = plot_data['1组收益率'] - plot_data['基准收益率']

    #计算策略年化收益率
    annual_ret = plot_data.iloc[-1,:]**(1/(((plot_data.index[-1] - plot_data.index[0]).days)/365))-1
    annual_ex_ret = annual_ret['1组收益率'] - annual_ret['基准收益率']
    annual_tail_ret = annual_ret['5组收益率'] - annual_ret['基准收益率']
    annual_duokong_ret = annual_ret['1组收益率'] - annual_ret['5组收益率']
    
    #计算策略月度超额收益和胜率
    backtest['月度超额收益率'] = backtest['1组收益率'] - backtest['基准收益率']
    win_probability = len(backtest[backtest['月度超额收益率']>0])/len(backtest)
    
    #输出整体回测效果矩阵
    result_all = pd.DataFrame(columns=['多头年化超额','多头胜率','空头超额','多空超额','P+',\
                                       'P-','PRD'],index=[0])
    
    result_all.loc[0,'多头年化超额'] = '%.2f%%'%(annual_ex_ret*100)
    result_all.loc[0,'多头胜率'] = '%.2f%%'%(win_probability*100)
    result_all.loc[0,'空头超额'] = '%.2f%%'%(annual_tail_ret*100)
    result_all.loc[0,'多空超额'] = '%.2f%%'%(annual_duokong_ret*100)
    result_all.loc[0,'P+'] = '%.2f'%p_plus
    result_all.loc[0,'P-'] = '%.2f'%p_minus
    result_all.loc[0,'PRD'] = '%.2f'%prd
    
    #输出日历效应矩阵
    rili_table = backtest['月度超额收益率'].to_frame()
    rili_table['年'] =  [x.year for x in rili_table.index]
    rili_table['月'] =  [x.month for x in rili_table.index] 
    
    return backtest,plot_data,result_all,rili_table

#%%存储类函数

#将字典转存成本地EXCEL文件  
def dict_to_excel(hyinvlist,fp,add_path,filename): #记得filename包括格式
#    add_path = 'result'
#    filename = '分析师预期策略每期TOP行业名单.xlsx'
    wb = openpyxl.Workbook()
    wb.create_sheet()
    print(wb.sheetnames) #默认就叫Sheet
    sheet = wb['Sheet']  # 获取表
    row = 1
    for x in hyinvlist:
        sheet.cell(row, column=1).value = x
        sheet.cell(row, column=2).value = str(hyinvlist[x])
        row+=1 #enter the next row
    wb.save(fp+"\\"+add_path+"\\"+filename)
    wb.close()

#将list转存成本地EXCEL文件
def backtest_to_excel(df_list,sheet_list,fp):
    assert len(df_list)==len(sheet_list)
    writer = pd.ExcelWriter(fp)
    for i in range(len(df_list)):
        df_list[i].to_excel(writer,sheet_name=sheet_list[i])
    writer._save()


