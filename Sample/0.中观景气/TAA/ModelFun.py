import os
import itertools
import pandas as pd
import textwrap

from BasicFun1 import *
from BasicFun2朝阳2 import * #核心的通用基础函数都在BasicFun2
plt.ioff()
t0 = dt.datetime.now()
t = t0

colors = ['silver', 'orange', 'darksalmon', 'orange', 'orange', 'darksalmon', \
          'silver', 'silver', 'plum', 'orange', 'silver', 'silver', 'silver', \
          'royalblue', 'royalblue', 'royalblue', 'royalblue', 'royalblue', \
          'orange', 'royalblue', 'orange', 'royalblue', 'plum', \
          'orange', 'orange', 'orange', 'orange', 'orange', 'orange', \
          'royalblue', 'royalblue', 'royalblue', 'orange', 'orange', 'orange', \
          'orange', 'plum', 'plum', 'plum', 'plum', 'plum', 'plum', \
          'silver', 'silver', 'orange', 'royalblue', 'royalblue', 'royalblue', \
          'silver', 'silver', 'silver', 'silver', 'silver', 'silver', \
          'silver', 'silver', 'silver', 'royalblue', 'royalblue', 'royalblue', \
          'plum', 'darksalmon', 'darksalmon', 'royalblue', 'royalblue', \
          'royalblue', 'royalblue', 'royalblue', 'royalblue', 'royalblue', \
          'royalblue', 'royalblue', 'royalblue', 'royalblue', 'royalblue']
plt.rcParams['font.size'] = 8  # 字体大小

#---分析师预期特殊函数
def get_data_anapre(fp,endDate):
    if os.path.exists(f"{fp}{endDate}/balance.csv") and os.path.exists(f"{fp}{endDate}/netprofit.csv"):
        balance = pd.read_csv(f"{fp}{endDate}/balance.csv", index_col=0)
        netprofit = pd.read_csv(f"{fp}{endDate}/netprofit.csv", index_col=0)
    else:
        # ---part1:提取数据：净利润，净资产，自由流通市值
        # 净资产:股东权益合计(不含少数股东权益)
        sql = ('select S_INFO_WINDCODE,REPORT_PERIOD,TOT_SHRHLDR_EQY_EXCL_MIN_INT,ANN_DT   '
               'from winddf.AShareBalanceSheet '
               'where report_period>20160101 '
               'and STATEMENT_TYPE in (408001000,408004000,408005000,408050000) ')
        balance = pd.read_sql(sql, windconn)
        balance = balance.sort_values(by=['S_INFO_WINDCODE', 'REPORT_PERIOD', 'ANN_DT'])
        print('资产负债表提取完毕')

        # 净利润:净利润(不含少数股东损益)
        sql = ('select S_INFO_WINDCODE,REPORT_PERIOD,NET_PROFIT_EXCL_MIN_INT_INC,ANN_DT '
               'from winddf.AShareIncome '
               'where report_period>20160101 '
               'and STATEMENT_TYPE  in (408001000,408004000,408005000,408050000) ')
        income = pd.read_sql(sql, windconn)
        income = income[(income['REPORT_PERIOD'].apply(lambda x: x[4:] in ['0331', '0630', '0930', '1231'])) & (
            ~income['S_INFO_WINDCODE'].apply(lambda x: x.startswith('A')))]
        income = income.sort_values(by=['S_INFO_WINDCODE', 'REPORT_PERIOD', 'ANN_DT'])
        print('利润表提取完毕')

        # 业绩快报
        sql = (
            "select S_INFO_WINDCODE,ANN_DT,REPORT_PERIOD,NET_PROFIT_EXCL_MIN_INT_INC,TOT_SHRHLDR_EQY_EXCL_MIN_INT from winddf.AShareProfitExpress")
        express = pd.read_sql(sql, windconn)
        print('业绩快报提取完毕')

        # 业绩预告
        sql = ("select a.S_INFO_WINDCODE,S_PROFITNOTICE_DATE,S_PROFITNOTICE_PERIOD, "
               "S_PROFITNOTICE_CHANGEMIN,S_PROFITNOTICE_CHANGEMAX,"
               "S_PROFITNOTICE_NETPROFITMIN,S_PROFITNOTICE_NETPROFITMAX, "
               "b.S_INFO_LISTDATE "
               "from winddf.AShareProfitNotice a,  winddf.AShareDescription b "
               "where a.S_INFO_WINDCODE = b.S_INFO_WINDCODE "
               " and a.S_PROFITNOTICE_PERIOD>b.S_INFO_LISTDATE ")
        profitnotice = pd.read_sql(sql, windconn)  # 单位：万元
        profitnotice['lastrpt'] = profitnotice['S_PROFITNOTICE_PERIOD'].apply(lambda x: str(int(x[0:4]) - 1) + x[4:])
        profitnotice = pd.merge(profitnotice, income[['S_INFO_WINDCODE', 'REPORT_PERIOD', 'NET_PROFIT_EXCL_MIN_INT_INC']].
                                rename(columns={'NET_PROFIT_EXCL_MIN_INT_INC': 'lastprofit', 'REPORT_PERIOD': 'lastrpt'}),
                                on=['S_INFO_WINDCODE', 'lastrpt'])
        profitnotice['max_change'] = profitnotice['lastprofit'] * (1 + profitnotice['S_PROFITNOTICE_CHANGEMAX'] / 100)
        profitnotice['min_change'] = profitnotice['lastprofit'] * (1 + profitnotice['S_PROFITNOTICE_CHANGEMIN'] / 100)
        profitnotice['max'] = (profitnotice['S_PROFITNOTICE_NETPROFITMAX'] * 1e4).fillna(profitnotice['max_change'])
        profitnotice['min'] = (profitnotice['S_PROFITNOTICE_NETPROFITMIN'] * 1e4).fillna(profitnotice['min_change'])
        # 平均
        profitnotice['NET_PROFIT_EXCL_MIN_INT_INC'] = (profitnotice['max'] + profitnotice['min']) / 2
        # 预告净利润
        notice = profitnotice[
            ['S_INFO_WINDCODE', 'S_PROFITNOTICE_PERIOD', 'S_PROFITNOTICE_DATE', 'NET_PROFIT_EXCL_MIN_INT_INC']]. \
            rename(columns={'S_PROFITNOTICE_PERIOD': 'REPORT_PERIOD', 'S_PROFITNOTICE_DATE': 'ANN_DT'})
        notice = notice[notice['ANN_DT'] > notice['REPORT_PERIOD']]
        print('业绩预告提取完毕')

        # 季报净利润：正式财报+快报+预告
        notice['mark'] = 1
        express['mark'] = 2
        income['mark'] = 3
        netprofit = income._append(express)._append(notice)[
            ['S_INFO_WINDCODE', 'REPORT_PERIOD', 'NET_PROFIT_EXCL_MIN_INT_INC', 'ANN_DT', 'mark']]
        netprofit.sort_values(by=['S_INFO_WINDCODE', 'REPORT_PERIOD', 'ANN_DT', 'mark'], inplace=True, ignore_index=True)
        netprofit = netprofit.drop(netprofit[netprofit['REPORT_PERIOD'] < '20080101'].index).reset_index(drop=True)

        os.makedirs(f"{fp}{endDate}/", exist_ok=True)
        balance.to_csv(f"{fp}{endDate}/balance.csv")
        netprofit.to_csv(f"{fp}{endDate}/netprofit.csv")


    # 补充个股自由流通市值数据（约3000万数据，自由流通股本*收盘价）
    listedstk = getlistedstkfromwind(parse(endDate).date(), windconn, listd=180, delistd=90)  # 读取最新上市股票数据（横向补充股票）
    stock_close_read = pd.read_csv(fp + '\\自由流通市值_最新.csv', index_col=0)
    stkfreemv_mean = stock_data_fill(stock_close_read, listedstk, endDate)  # 开始进行纵向时间填补
    stkfreemv_mean = stkfreemv_mean[~stkfreemv_mean.index.duplicated()]
    stkfreemv_mean.to_csv(fp + '\\自由流通市值_最新.csv', index=True, index_label='date')  # 存储并覆盖原有流通市值本地文件
    print('流通市值提取完毕')
    return balance, netprofit, stkfreemv_mean, listedstk


def cal_stock_ROE(add_date,balance, netprofit, stkfreemv_mean,listedstk):
    if len(add_date)==0:
        return pd.DataFrame()
    else:
        res_add = pd.concat([cal_single_stock_ROE(EndDate,balance, netprofit, stkfreemv_mean,listedstk) for EndDate in add_date])
        return res_add
def cal_single_stock_ROE(EndDate,balance, netprofit, stkfreemv_mean,listedstk):
    # 净利润
    temp_np = netprofit[netprofit['ANN_DT'] < EndDate].copy()
    temp_np = temp_np.drop_duplicates(subset=['S_INFO_WINDCODE', 'REPORT_PERIOD'], keep='last')

    # 补上最新净资产
    subbalance = balance[balance['ANN_DT'] < EndDate].sort_values \
        (by=['S_INFO_WINDCODE', 'REPORT_PERIOD', 'ANN_DT']).drop_duplicates \
        (subset=['S_INFO_WINDCODE', 'REPORT_PERIOD'], keep='last')
    bv = subbalance.drop_duplicates \
        (subset=['S_INFO_WINDCODE'], keep='last') \
        [['S_INFO_WINDCODE', 'REPORT_PERIOD', 'TOT_SHRHLDR_EQY_EXCL_MIN_INT']]
    bv['lastyr'] = bv['REPORT_PERIOD'].apply(lambda x: str(int(int(x) - 1e4)))
    bv = pd.merge(bv, subbalance[['S_INFO_WINDCODE', \
                                  'REPORT_PERIOD', \
                                  'TOT_SHRHLDR_EQY_EXCL_MIN_INT']].rename \
        (columns={'REPORT_PERIOD': 'lastyr', 'TOT_SHRHLDR_EQY_EXCL_MIN_INT': 'lastbv'}), \
                  on=['S_INFO_WINDCODE', 'lastyr'], how='left')
    # 相对前期
    curr_t = parse(EndDate).date()
    lag_t1 = parse(getLagT(EndDate, N=90)).date()
    resample = [lag_t1, curr_t]
    # 获取分析师预测
    thisyear = getThisYear(EndDate)
    lagyear = thisyear - 1

    sql = (
                "select stock_code,create_date, report_year,report_quarter,organ_id,organ_name,author_name,forecast_np,forecast_or "
                "from ZYYX.rpt_forecast_stk "
                "where report_year =" + str(thisyear) +
                " or report_year = " + str(lagyear))
    rpt_forecast = pd.read_sql(sql, goalconn)
    rpt_forecast = rpt_forecast.reset_index()
    rpt_forecast = rpt_forecast[
        ['index', 'REPORT_YEAR', 'REPORT_QUARTER', 'FORECAST_OR', 'FORECAST_NP', 'STOCK_CODE', 'ORGAN_ID',
         'CREATE_DATE']]
    rpt_forecast.columns = ['ID', 'report_year', 'report_quarter', \
                            'forecast_or', 'forecast_np', 'stock_code', \
                            'organ_id', 'create_date']

    # 每期一致预期
    avgrpt = pd.DataFrame()
    for tm in resample:
        # 股票池
        nonst = getststkfromwind(tm, windconn, window=90).reindex(listedstk).fillna(False)

        # 待选股票:非新股、非ST
        allpool = nonst[nonst == False].index.tolist()
        allpool.sort()

        tlag = tm - dt.timedelta(89)

        # 修改rpt_forecast['create_date']日期类型，统一使用datetimeindex
        rpt_forecast['create_date'] = pd.DatetimeIndex(rpt_forecast['create_date'])

        # 截取需要时期的报告
        sub_rptforecast = rpt_forecast[(rpt_forecast['create_date'] <= pd.DatetimeIndex([tm])[0]) & \
                                       (rpt_forecast['create_date'] >= pd.DatetimeIndex([tlag])[0])].sort_values(
            by=['stock_code', 'organ_id', 'create_date'])

        # 留下每家机构resample时间段内最新的预测报告
        sub_rptforecast = sub_rptforecast.drop_duplicates(subset=['stock_code', 'organ_id', 'report_year'],
                                                          keep='last')

        # 计算最终预期净利润
        avg_rpt_ = sub_rptforecast.groupby(['stock_code', 'report_year'])['forecast_np'].mean()
        avg_count_ = sub_rptforecast.groupby(['stock_code', 'report_year'])['forecast_np'].count()
        avg_rpt_ = pd.concat([avg_rpt_.rename('np'), avg_count_.rename('N')], axis=1)
        avg_rpt_ = avg_rpt_[avg_rpt_['N'] >= 2]  # 【这个可以考虑去掉！！！】
        avg_rpt_['np'] = avg_rpt_['np'] * 1e4
        avg_rpt_ = avg_rpt_.reset_index()

        # 剔除港股预测（股票代码五位数）
        avg_rpt_['ishk'] = avg_rpt_['stock_code'].apply(lambda x: len(x))
        avg_rpt_ = avg_rpt_[avg_rpt_['ishk'] == 6]

        avg_rpt_['stock_code'] = avg_rpt_['stock_code'].apply(code_fill)
        avg_rpt_ = avg_rpt_.dropna()

        avg_rpt_ = avg_rpt_.pivot(index='stock_code', columns='report_year', values='np')

        # 去年盈利-过去平均
        lagyears = [str(thisyear - x) + '1231' for x in range(1, 2)]
        last_np = netprofit[(netprofit['REPORT_PERIOD'].isin(lagyears)) & (
                    netprofit['ANN_DT'] <= curr_t.strftime(datestyle2))].sort_values(
            by=['S_INFO_WINDCODE', 'REPORT_PERIOD', 'ANN_DT']).drop_duplicates(
            subset=['S_INFO_WINDCODE', 'REPORT_PERIOD'], keep='last')
        last_np = last_np.pivot(index='S_INFO_WINDCODE', columns='REPORT_PERIOD',
                                values='NET_PROFIT_EXCL_MIN_INT_INC').rename(columns=lambda x: int(x[0:4]))

        # 年初可能还没有上一年年报数据，需填补列
        if thisyear - 1 not in last_np.columns:
            last_np[thisyear - 1] = np.nan
        last_np = last_np.reindex(avg_rpt_.index)  # 处理去年年报数据过少
        if thisyear - 1 in avg_rpt_.columns:
            last_np[thisyear - 1] = last_np[thisyear - 1].fillna(avg_rpt_[thisyear - 1])
        last_np = last_np.dropna()

        # 最新净资产
        bv_now = subbalance.sort_values(by=['S_INFO_WINDCODE', 'REPORT_PERIOD', 'ANN_DT']).drop_duplicates(subset=['S_INFO_WINDCODE'], keep='last')
        bv_now = bv_now.set_index('S_INFO_WINDCODE')[['TOT_SHRHLDR_EQY_EXCL_MIN_INT']].rename(columns={'TOT_SHRHLDR_EQY_EXCL_MIN_INT': 'bv_now'})

        # 预期与历史平均
        avg_rpt_ = pd.concat([avg_rpt_[thisyear], last_np], axis=1).dropna()
        avg_rpt_['np_last'] = avg_rpt_[[int(x[0:4]) for x in lagyears]].mean(axis=1)
        avg_rpt_ = avg_rpt_.rename(columns={thisyear: 'np'})
        avg_rpt_ = avg_rpt_[['np', 'np_last']]

        # 匹配净资产：最新
        avg_rpt_ = pd.concat([avg_rpt_, bv_now], axis=1).dropna()

        # 保存
        avg_rpt_.index.name = 'S_INFO_WINDCODE'
        avg_rpt_['Date'] = tm
        avg_rpt_ = avg_rpt_.reindex(allpool).dropna()

        avgrpt = avgrpt._append(avg_rpt_.reset_index())

    # 去除前后期缺失
    avg_rpt_n = avgrpt.groupby('S_INFO_WINDCODE')['np'].count().rename('Num').reset_index()
    temp = pd.merge(avgrpt, avg_rpt_n, on=['S_INFO_WINDCODE'], how='left')
    temp = temp[(temp['Num'] == len(resample))]  # 只选取本期和上期都有分析师覆盖的股票
    avgrpt_now = temp.loc[temp.Date == curr_t, ['S_INFO_WINDCODE', 'np', 'np_last', 'bv_now']].set_index('S_INFO_WINDCODE')
    avgrpt_lag = temp.loc[temp.Date == lag_t1, ['S_INFO_WINDCODE', 'np', 'np_last', 'bv_now']].set_index('S_INFO_WINDCODE').rename(columns={'np': 'np_l1', 'np_last': 'np_last_l1', 'bv_now': 'bv_now_l1'})
    avgrpt_use = pd.concat([avgrpt_now, avgrpt_lag], axis=1).dropna()

    t_mv = stkfreemv_mean.loc[int(curr_t.strftime("%Y%m%d"))].rename('freemv').reindex(avgrpt_use.index)
    avgrpt_use = pd.concat([avgrpt_use, t_mv], axis=1)
    avgrpt_use.dropna(how='any', inplace=True)
    avgrpt_use['date'] = EndDate
    return avgrpt_use


def read_hy_ROE(file_name,catchDtAll=None):
    hy_ROE_read = pd.read_excel(file_name,index_col=0)
    hy_ROE_read['date'] = hy_ROE_read['date'].apply(lambda x: str(x))
    if catchDtAll is not None:
        hy_ROE_read = hy_ROE_read[hy_ROE_read['date'].isin(catchDtAll)]
    return hy_ROE_read

def cal_single_hy_ROE(code,date,all_con_indus,stock_ROE):
    if (code,date) in all_con_indus.index:
        stocklist = all_con_indus.loc[(code,date)][0].split(',')
        stock_date_list = [(i,date) for i in stocklist if (i,date) in stock_ROE.index]

        temp = stock_ROE.loc[stock_date_list]
        temp['freemv_w'] = temp['freemv'] / temp['freemv'].sum()
        temp['temp'] = temp['ROE'] * temp['freemv_w']
        temp['temp_lag'] = temp['ROE_lag'] * temp['freemv_w']
        result = pd.Series([code,date]+temp[['temp','temp_lag','freemv']].sum().values.tolist(),index=['S_INFO_WINDCODE','date','最新ROE','上期ROE','总市值'])
        return result.to_frame().T
    else:
        return pd.DataFrame()
def cal_hy_ROE(add_date,codelist,all_con_indus,stock_ROE):
    if len(add_date)==0:
        return pd.DataFrame(columns=['S_INFO_WINDCODE','date','最新ROE','上期ROE','总市值'])
    else:
        hy_ROE_add = pd.concat([cal_single_hy_ROE(code,date,all_con_indus,stock_ROE) for (date,code) in itertools.product(add_date, codelist)])
        return hy_ROE_add

def cal_anapre_forshow(hy_ROE,hy_ROE_lag,hy_ROE_fugaidu,catchDtAll,hylist):
    # 特殊输出：形成分析师预期单一模型输出结果表：最新一期有效行业当前预期增速VS上期预期增速变化表
    jingqi_now = hy_ROE.loc[catchDtAll[-1]].loc[hylist['行业名称'].tolist()]  # 格式是series
    jingqi_now = jingqi_now.to_frame()
    jingqi_lag_now = hy_ROE_lag.loc[catchDtAll[-1]].loc[hylist['行业名称'].tolist()]  # 格式是series
    jingqi_lag_now = jingqi_lag_now.to_frame()
    fugaidu_now = hy_ROE_fugaidu.loc[catchDtAll[-1]].loc[hylist['行业名称'].tolist()]
    fugaidu_now = fugaidu_now.to_frame()
    jingqi_now.columns = ['当前预期ROE增速']
    jingqi_lag_now.columns = ['上期预期ROE增速']
    fugaidu_now.columns = ['分析师覆盖度']
    jingqi_now['当前预期增速排序'] = jingqi_now['当前预期ROE增速'].rank(axis=0, method='average', ascending=False)
    jingqi_lag_now['上期预期增速排序'] = jingqi_lag_now['上期预期ROE增速'].rank(axis=0, method='average',
                                                                                ascending=False)
    jingqi_now = pd.merge(jingqi_now, jingqi_lag_now, left_index=True, right_index=True)
    jingqi_now['排序变化'] = -(jingqi_now['当前预期增速排序'] - jingqi_now['上期预期增速排序'])
    jingqi_now['预期增速变化'] = jingqi_now['当前预期ROE增速'] - jingqi_now['上期预期ROE增速']
    jingqi_now = pd.merge(jingqi_now, fugaidu_now, left_index=True, right_index=True)
    jingqi_now = jingqi_now.sort_values(by=['预期增速变化', '分析师覆盖度'], ascending=False)
    jingqi_now = jingqi_now[['当前预期ROE增速', '当前预期增速排序', \
                             '上期预期ROE增速', '上期预期增速排序', '排序变化', '预期增速变化', '分析师覆盖度']]
    return jingqi_now

#改变行业成分股历史序列的格式，从unstack数据格式变成stack数据格式便于索引
def con_indus(class1,tm):
    df = class1.copy()
    tm = int(tm)
    df.loc[(df['S_CON_INDATE']<tm)&(df['S_CON_OUTDATE']>=tm),'当期标记'] = 1
    df = df[df['当期标记']==1]
    df['date'] = tm
    df = df[['S_INFO_WINDCODE','S_CON_WINDCODE','date']]
    df = df.groupby(['S_INFO_WINDCODE','date']).agg(toline).reset_index()
    return df

#---中观产业景气特殊函数
#把中观产业景气数据统一处理成月频TTM或者TTM同比
def AdjustData_1(data_x,startDate,endDate):#输入数据df，指标列表df
    data_x = data_x[data_x.index<=pd.to_datetime(endDate)]
    data_x = data_x[data_x.index>=pd.to_datetime(startDate)]


    data_mon = data_x.resample('m').last() #变频：把日度、周度、月度数据都按月取均值
    data_mon = data_mon.fillna(data_mon.interpolate()) #对于缺失值线性插值
    data_mon = data_mon.dropna(axis=1, thresh=0.9 * data_mon.shape[0])  # 有的指标开始时间晚
    data_adj = pd.DataFrame()
    for idi in data_mon.columns:
        if '同比' in idi:
            data_adj = pd.concat([data_adj,data_mon.loc[:,[idi]].dropna()],axis=1,sort=True)
        elif '累计值' in idi:
            data_adj = pd.concat([data_adj,Calyoy(CalTTM(data_mon.loc[:,[idi]],1)).dropna()],axis=1,sort=True)
        elif '保有量' in idi:
            data_adj = pd.concat([data_adj,Calyoy(CalTTM(data_mon.loc[:,[idi]],1)).dropna()],axis=1,sort=True)
        elif '总产能' in idi:
            data_adj = pd.concat([data_adj,Calyoy(CalTTM(data_mon.loc[:,[idi]],1)).dropna()],axis=1,sort=True)
        elif '当月值' in idi:
            data_adj = pd.concat([data_adj,Calyoy(CalTTM(data_mon.loc[:,[idi]],0)).dropna()],axis=1,sort=True)
        elif '指数' in idi:
            data_adj = pd.concat([data_adj,Calyoy(data_mon.loc[:,[idi]]).dropna()],axis=1,sort=True)
        elif '价' in idi:
            data_adj = pd.concat([data_adj,Calyoy(data_mon.loc[:,[idi]]).dropna()],axis=1,sort=True)
        else:
            data_adj = pd.concat([data_adj,data_mon.loc[:,[idi]].dropna()],axis=1,sort=True)
    data_adj = data_adj.dropna(axis=1,how='all')
    return data_adj

#把中观产业景气数据清洗后标准化
def CleanData(indus,startDate,endDate,threshDate,fp_chanye,data_macro):
    #首先提取中观产业数据
    try:
        macro_dz = pd.read_excel(f"{fp_chanye}/dump/{indus}.xlsx",index_col=0)
        macro_dz.index = pd.DatetimeIndex(macro_dz.index)
    except:
        sub_data = data_macro[(data_macro['行业']==indus)&(data_macro['频率'].isin(['日','月','周']))]
        macro_dz = w.edb(sub_data.index.values.tolist(), startDate,endDate,usedf=True)[1]
        macro_dz.index = pd.DatetimeIndex(macro_dz.index)
        temp = data_macro[data_macro['行业']==indus]
        if len(temp)==1:
            macro_dz.columns = temp["指标名称"].values.tolist()
        else:
            macro_dz.columns = temp.loc[macro_dz.columns,"指标名称"].tolist()#把wind指标代码改为列名
        os.makedirs(f"{fp_chanye}/dump/",exist_ok=True)
        macro_dz.to_excel(f"{fp_chanye}/dump/{indus}.xlsx")

    #对于长期不更新的指标(近3个月或90天），进行剔除
    if len(macro_dz)>200: #代表日频数据
        for i in range(macro_dz.shape[1]):
            if np.isnan(macro_dz.iloc[-90:,i]).all() == True:
                macro_dz.iloc[:,i] = np.nan
        macro_dz = macro_dz.dropna(how='all',axis=1)
    else: #代表月频数据
        for i in range(macro_dz.shape[1]):
            if np.isnan(macro_dz.iloc[-3:,i]).all() == True:
                macro_dz.iloc[:,i] = np.nan
        macro_dz = macro_dz.dropna(how='all',axis=1)

    #得到某行业对齐后的中观指标数据
    macro_dz_adj = AdjustData_1(macro_dz,startDate,endDate)#调整数据格式
    macro_dz_adj = macro_dz_adj.fillna(macro_dz_adj.interpolate())
    macro_dz_adj = macro_dz_adj.dropna(axis=1,thresh=0.5*macro_dz_adj.shape[0])  #删除发布太晚的指标
    macro_dz_adj = macro_dz_adj[macro_dz_adj.index>=pd.to_datetime(threshDate)] #剔除前面由于计算导致的nan
    macro_dz_adj = macro_dz_adj[macro_dz_adj.index<=pd.to_datetime(endDate)] #wind数据有点乱，有时候引入未来变量，所以加一行这个

    #处理离群值
    for i in macro_dz_adj.columns:
        macro_dz_adj[i] = boxplot_fill(macro_dz_adj[i])

    #对景气数据标准化
    all_jingqi2  = (macro_dz_adj-macro_dz_adj.mean())/macro_dz_adj.std()#标准化
    all_jingqi3 = all_jingqi2.fillna(all_jingqi2.interpolate())
    #某些指标有少数几个nan值也没啥，因为有的指标就是某几个月没数
    all_jingqi_yuanzhi = all_jingqi3.dropna(axis=1)

    return macro_dz_adj,all_jingqi_yuanzhi

#清洗因变量数据：净值处理后的超额收益标准化
def Clean_nav_data_1(indus,benchmark,startDate,endDate,all_jingqi_yuanzhi,data_hy):

    #提取行业净值
    wcd = data_hy[data_hy['行业名称']==indus].index
    try:
        nav = index_close(wcd,startDate,endDate.replace('-',''))
    except:
        nav = w.wsd(wcd.tolist(), "close", startDate, endDate, "",usedf=True)[1]
        nav.index = pd.to_datetime(nav.index)
        nav.columns = wcd.tolist()
    mnav = nav.resample('m').last().reindex(all_jingqi_yuanzhi.index) #提取月末时点计算行业月度收益

    #API提取基准净值
    benchnav = w.wsd(benchmark, "close", startDate, endDate, "",usedf=True)[1]
    benchnav.index = pd.DatetimeIndex(benchnav.index)
    benchmnav = benchnav.resample('m').last().reindex(all_jingqi_yuanzhi.index)

    #得到月度超额收益率（nav比值法）
    data_mkt = (mnav[wcd]/mnav.iloc[0,0]).div(benchmnav['CLOSE']/benchmnav.iloc[0,0],axis=0)
    #最终要不要求求ret？

    #处理离群值
    ex_ret = boxplot_fill(data_mkt.iloc[:,0])
    #超额收益标准化
    ex_ret = (ex_ret - ex_ret.mean())/ex_ret.std()

    return ex_ret

#计算领先滞后R2
def CalMaxR(data_x,data_y):#输入:y预测变量，x自变量
    t_xy = pd.DataFrame()
    n = 7
    for i in range(1,n+1):
        data_xi_lag = pd.DataFrame(data_x.values[:-i],index=data_x.index[i:],columns=data_x.columns)#后移N期，领先性
        data_xi_fwd = pd.DataFrame(data_x.values[i:],index=data_x.index[:-i],columns=data_x.columns)#提前N期，滞后性
        data_xylag,data_tlag,data_plag,data_dirlag = tanalyze2(data_xi_lag,data_y.loc[data_xi_lag.index])
        data_xyfwd,data_tfwd,data_pfwd,data_dirfwd = tanalyze2(data_xi_fwd,data_y.loc[data_xi_fwd.index])
        t_xy = pd.concat([t_xy,pd.DataFrame([[data_xylag,data_tlag[0],data_plag,data_dirlag],[data_xyfwd,data_tfwd[0],data_pfwd,data_dirfwd]],index=[i,-i],columns=['R2','t值','p值','方向'])],axis=0)#i代表领先i期，-i代表滞后i期的t均值
    t_xy = t_xy[t_xy.index>0]
    t_xy = t_xy[t_xy['p值']<=0.1]
    # t_xy = t_xy[t_xy['t值']>=1.64]
    if len(t_xy) == 0:
        result_best = pd.DataFrame(data=np.nan,columns=['R2','t值','p值','方向'],index=[0])
    else:
        result_best = t_xy[t_xy['R2']==t_xy['R2'].max()]
        result_best = result_best.iloc[0,:].to_frame().T
        print(result_best)
    return result_best

#得到该行业各个指标最大R2及其领先滞后期数
def Cal_R_Final(all_jingqi_yuanzhi,ex_ret):
    r_result_roe = pd.DataFrame()
    for i in range(all_jingqi_yuanzhi.shape[1]):
        line = CalMaxR(all_jingqi_yuanzhi.iloc[:,i].to_frame(),ex_ret.to_frame())
        line.reset_index(drop=False,inplace=True)
        line.columns = ['领先滞后期','最大R2','t值','p值','方向']
        line.index = [all_jingqi_yuanzhi.columns[i]]
        r_result_roe = r_result_roe._append(line)
    return r_result_roe

#通过检验的指标筛选
def pick_indicator(all_jingqi_yuanzhi,ex_ret,thresh1,thresh2,thresh3,thresh4):
    r_result = Cal_R_Final(all_jingqi_yuanzhi,ex_ret)
    indicator1 = r_result[(r_result['领先滞后期']>=thresh1) \
                                 &(r_result['领先滞后期']<thresh2)
                                 &(r_result['最大R2']>=thresh3) \
                                 & (r_result['p值']<=thresh4)].index.values.tolist()
    timelag = r_result['领先滞后期'].loc[indicator1].values.tolist()
    dir_ = r_result['方向'].loc[indicator1].values.tolist()
    pick_indicator = indicator1
    return pick_indicator,timelag,dir_

def get_nav_m(hylist,codelist,start_date,end_date):
    nav_m = w.wsd(codelist, "close", start_date, end_date,
              "Fill=Previous;Period=M", usedf=True)[1]
    nav_m.index = pd.DatetimeIndex(nav_m.index)
    nav_m.columns = hylist.loc[nav_m.columns, '行业名称'].tolist()
    return nav_m

def get_base_m(codelist,start_date,end_date):
    nav_m = w.wsd(codelist, "close", start_date, end_date,
              "Fill=Previous;Period=M", usedf=True)[1]
    nav_m.index = pd.DatetimeIndex(nav_m.index)
    return nav_m

#---日历效应特殊函数
def cal_win_matrix(date, win, hylist):
    win_matrix1 = win.loc[:date].groupby(['月']).sum()
    win_matrix2 = win.loc[:date].groupby(['月']).count()
    win_matrix3 = win_matrix1 / win_matrix2
    target_month = date.month % 12 + 1

    return win_matrix3.loc[target_month]

def cal_win_latest(date, win, hylist):
    win_matrix1 = win.loc[:date].groupby(['月']).sum()
    win_matrix2 = win.loc[:date].groupby(['月']).count()
    win_matrix3 = win_matrix1 / win_matrix2
    win_matrix4 = win_matrix3.copy()
    win_matrix4.index = range(1, 13)
    win_matrix4.index = win_matrix4.index.map(lambda x: str(x) + '月')
    win_matrix4.columns = hylist.loc[win_matrix4.columns, '行业名称'].tolist()
    return win_matrix4
def cal_win_matrix_all(win, hylist):
    win_final = pd.DataFrame(np.nan, index=win.index, columns=win.columns[:-1])
    for date in win.index[12 * 3 - 1:]:
        win_final.loc[date] = cal_win_matrix(date, win, hylist)
    win_final.columns = hylist.loc[win_final.columns, '行业名称'].tolist()
    win_matrix4 = cal_win_latest(date, win, hylist)
    return win_final,win_matrix4

#---拥挤度特殊函数
def get_target_f(f_name,class1,hylist,startDate_crowd,endDate):
    hy_mv = class1[class1['TRADE_DT'] >= startDate_crowd]
    hy_mv = hy_mv.pivot(index='TRADE_DT', columns='S_INFO_WINDCODE', values=f_name)
    hy_mv = hy_mv[hylist.index.tolist()]
    hy_mv = hy_mv[hy_mv.index <= endDate]
    # 调整数据格式
    hy_mv.index = pd.DatetimeIndex(hy_mv.index)
    hy_mv.columns = hylist.loc[hy_mv.columns, "行业名称"].tolist()
    return hy_mv

#计算最近一年成交占比的均值，上下1倍标准差、1.5倍标准差、2倍标准差（和成交占比的矩阵合并）
def cal_std(trade_pct2,indus):
    result = pd.DataFrame(columns = ['均值','标准差','加1倍标准差','加1.5倍标准差','加2倍标准差'])
    for i in range(243*3,len(trade_pct2)+1):
        temp = trade_pct2.iloc[i-243*3:i,:]
        temp = temp[indus]
        mean = temp.mean()
        plus1 = temp.mean() + temp.std()
        plus2 = temp.mean() + 1.5*temp.std()
        plus3 = temp.mean() + 2*temp.std()
        result.loc[temp.index[-1],'均值'] = mean
        result.loc[temp.index[-1],'标准差'] = temp.std()
        result.loc[temp.index[-1],'加1倍标准差'] = plus1
        result.loc[temp.index[-1],'加1.5倍标准差'] = plus2
        result.loc[temp.index[-1],'加2倍标准差'] = plus3
    return result

def split_Value(x,indus):
    x[['是否上穿1倍','是否上穿1.5倍','是否上穿2倍']] = 0
    x.loc[x[indus] >= x['加1倍标准差'],'是否上穿1倍'] = 1
    x.loc[x[indus] >= x['加1.5倍标准差'],'是否上穿1.5倍'] = 1
    x.loc[x[indus] >= x['加2倍标准差'],'是否上穿2倍'] = 1
    return x

def cal_crowd(trade_pct2,indus,f_signal):
    result = cal_std(trade_pct2,indus)
    result = pd.merge(trade_pct2[indus].to_frame(), result, left_index=True, right_index=True)
    result = split_Value(result,indus)
    return result[f_signal],result[indus]

#---模型复合特殊函数
def trans_df01(hylist,top1,num=1):
    signal1 = pd.DataFrame(columns=hylist['行业名称'].tolist(), index=top1.index, data=0)
    for i in top1.index:
        df = top1.loc[i]
        for j in df['行业']:
            signal1.loc[i, j] = num
    return signal1

def trans_df11(hylist,top1,tail5):
    signal1 = pd.DataFrame(columns=hylist['行业名称'].tolist(), index=top1.index, data=0)
    for i in top1.index:
        df = top1.loc[i]
        for j in df['行业']:
            signal1.loc[i, j] = 1
    for i in tail5.index:
        df = tail5.loc[i]
        for j in df['行业']:
            signal1.loc[i, j] = -1
    return signal1

def generate_model_weight(signal,rili):
    # 生成weight1_final
    weight1 = pd.DataFrame(columns=signal.columns, index=signal.index, data=np.nan)
    weight1['月'] = weight1.index.month
    weight1_final = pd.DataFrame()
    for i in weight1.index:
        rili_temp = rili.loc[:i, :]
        rili_temp2 = rili_temp.pivot(index='月', columns='年', values='月度超额收益率')
        rili_temp2['跑赢月数'] = rili_temp2[rili_temp2 > 0].count(axis=1)
        rili_temp2['总月数'] = rili_temp2.count(axis=1)
        rili_temp2['月胜率'] = rili_temp2['跑赢月数'] / rili_temp2['总月数']
        rili_temp2 = rili_temp2.reset_index()
        rili_temp2 = rili_temp2[['月', '月胜率']]
        weight1_temp = weight1.loc[i].to_frame().T
        weight1_temp = pd.merge(weight1_temp, rili_temp2, how='left', left_on='月', right_on='月')
        del weight1_temp['月']
        weight1_temp.iloc[0, :-1] = weight1_temp.iloc[0, -1]
        del weight1_temp['月胜率']
        weight1_temp.index = [i]
        weight1_final = weight1_final._append(weight1_temp)
    return weight1_final


#---画图函数
def plot_anapre(hy_ROE_show,hylist,save_path,endDate):
    jingqi_now2 = hy_ROE_show[['分析师覆盖度', '预期增速变化']]  # 注意有空值
    jingqi_now2 = jingqi_now2.loc[hylist['行业名称'].tolist()]
    jingqi_now2.dropna(inplace=True)
    hylist2 = hylist[hylist['行业名称'].isin(jingqi_now2.index)]
    x_plot = jingqi_now2['分析师覆盖度'].tolist()
    y_plot = jingqi_now2['预期增速变化'].tolist()
    labels = hylist2['行业名称'].tolist()
    fig, ax = plt.subplots()
    ax.scatter(x_plot, y_plot, s=100, c=colors, alpha=0.6)
    for i, txt in enumerate(labels):
        ax.annotate(txt, (x_plot[i], y_plot[i]))
    # fig.set_size_inches(11, 6)
    fig.subplots_adjust(left=0.2, right=0.8, bottom=0.2, top=0.8)
    plt.ylim([jingqi_now2['预期增速变化'].min(), jingqi_now2['预期增速变化'].max()])  # 纵坐标轴范围
    # plt.ylim([-0.05, jingqi_now2['预期增速变化'].max()])  # 纵坐标轴范围
    plt.xlim([0.2, 1])  # 横坐标轴范围
    plt.axvline(x=0.5, color='red', linestyle='--', label='分析师覆盖度50%阈值')
    plt.plot([0] * 100, '--', color='silver', label='预期ROE边际变化正负阈值')
    plt.plot([pd.Series(y_plot).quantile(0.87)] * 100, 'c--', color='blue', label='预期ROE边际变化前10阈值')
    plt.xlabel('分析师覆盖度（50%以上代表有效）')  # 横坐标轴标题
    plt.ylabel('预期ROE边际变化（越大越好）')  # 纵坐标轴标题
    plt.legend(loc='best')
    plt.title('分析师预期ROE模型最新结果%s(推荐右上区域)' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "%s分析师预期ROE模型最新结果图.png" % endDate, bbox_inches='tight')
    plt.show(block=False)
    plt.close('all')

def plot_lag_ax(ax,p,q_main,title,end_date,color_list=['red','orange'],loc_list=['upper right','lower right']):
    q_real = q_main.loc[:end_date]
    q_forecast = q_main.loc[q_main.index[0]:]
    ax.plot(p.index, p.values, label=p.name, color='blue')
    ax.legend(loc='upper left', fontsize=fontsize_legend)
    ax.tick_params(axis='y', direction='out', color='blue', labelcolor='blue')
    text_str = ''
    ax1 = {}
    ax1[0] = ax.twinx()
    ax1[0].plot(q_real.index, q_real.values, label=q_main.name, color=color_list[0])
    ax1[0].plot(q_forecast.index, q_forecast.values, label='未来景气走势',color=color_list[0],linestyle='--')
    j, k, l, m = lag_onlyP(p,q_main)
    ax1[0].legend(loc=loc_list[0], fontsize=fontsize_legend)
    text_str+=m+'\n'
    ax.text(0.5, 1,text_str,fontsize=fontsize_text, ha='center', va='center', transform=ax.transAxes,color='grey')
    ax.set_title(f"{title}\n", fontsize=fontsize_subtitle)
    ax.spines['left'].set_color('blue')
    ax.spines['top'].set_visible(False)


def plot_jingqi(jingqi_indicator,jingqi_nav,save_path,endDate,excess_flag=False):
    # #
    fig, ax = plt.subplots(nrows=2, ncols=3)
    xy_list = [[0, 0], [0, 1], [0, 2], [1, 0], [1, 1], [1, 2]]
    excess_name = '' if excess_flag==False else '超额'
    for i, ind_i in enumerate(jingqi_indicator.columns):
        if i % 6 == 0:
            fig, ax = plt.subplots(nrows=2, ncols=3, dpi=100)
            fig.suptitle(f"中观产业景气上行行业{endDate}", fontsize=fontsize_suptitle, fontweight='bold')
        data_x = jingqi_nav[ind_i]
        data_x.name = f"行业指数{excess_name}走势"
        data_y = jingqi_indicator[ind_i]
        data_y.name = '中观产业景气走势'
        plot_lag_ax(ax[xy_list[i][0], xy_list[i][1]], data_x, data_y, ind_i,endDate)
        if i % 6 == 5:
            fig.savefig(f"{save_path}景气向上TOP行业{excess_name}走势-{int(i / 6) + 1}.png")
    if i % 6 != 0:
        for ii in range(i % 6 + 1, 6):
            ax[xy_list[ii % 6][0], xy_list[ii % 6][1]].axis('off')
        fig.savefig(f"{save_path}景气向上TOP行业{excess_name}走势-{int(i / 6) + 1}.png")

def plot_rili(rili_df,month_f,save_path,endDate):
    fig, ax = plt.subplots()
    # 提取大于2的柱子索引和高度
    max_ = rili_df.max()
    colors = ['red' if rili_df.iloc[i] == max_ else 'blue' for i in range(len(rili_df))]
    heights = [rili_df.iloc[i] for i in range(len(rili_df))]
    ax.bar(x=rili_df.index.tolist(), height=heights, color=colors)
    # ax.bar(yongjidu_pic4.index, yongjidu_pic4)
    # ax.axhline(y=2, color='gray', linestyle='--')  # 添加水平分割线
    plt.xticks(rotation=45)
    plt.ylabel('日历效应胜率')  # 纵坐标轴标题
    plt.ylim([0,1])
    plt.title(month_f+'月日历效应胜率', size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "%s日历效应胜率.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def plot_pbroe(result_pb,result_roe,hylist,save_path,endDate):
    pbroehy = hylist['行业名称'].tolist()
    result_pb3 = result_pb[pbroehy]  # 保证列顺序
    result_roe3 = result_roe[pbroehy]  # 保证列顺序
    pb_plot = result_pb3.iloc[-1, :].to_list()
    roe_plot = result_roe3.iloc[-1, :].to_list()
    score = [np.add(pb_plot, roe_plot)]
    newscore = [i * 300 for i in score]
    labels = pbroehy
    size = newscore
    fig, ax = plt.subplots()
    ax.scatter(pb_plot, roe_plot, s=size, c=colors, alpha=0.6)
    for i, txt in enumerate(labels):
        ax.annotate(txt, (pb_plot[i], roe_plot[i]))
    plt.rcParams['axes.unicode_minus'] = False  # 显示中文和特殊符号
    plt.plot([0.5] * 100, 'c--', color='red', linewidth=2, label='自身历史50%百分位')
    plt.plot([0.5] * 100, list(range(0, 100)), 'c--', color='red', linewidth=2)
    plt.plot([pd.Series(roe_plot).quantile(0.3)] * 100, 'c--')
    plt.plot([pd.Series(pb_plot).quantile(0.3)] * 100, list(range(0, 100)), 'c--', label='行业横向30%百分位')
    plt.ylim([0, 1])  # 纵坐标轴范围
    plt.xlim([0, 1])  # 横坐标轴范围
    plt.xlabel('PB历史百分位（越大越好）')  # 横坐标轴标题
    plt.ylabel('一致预期ROE历史百分位（越大越好）')  # 纵坐标轴标题
    font = {'family': 'serif', 'color': 'darkred', 'weight': 'bold', 'size': fontsize_suptitle}
    plt.title('PB-ROE%s' % endDate, fontdict=font)
    plt.legend(loc='best')
    plt.savefig(save_path + "%sPBROE策略四象限图.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def plot_crowd(crowd_pic,save_path,endDate):
    # ---拥挤度画分布图
    crowd_pic2 = crowd_pic.iloc[-243 * 3:, :]
    crowd_pic3 = (crowd_pic2 - crowd_pic2.mean()) / crowd_pic2.std()
    # yongjidu_pic4 = yongjidu_pic3.loc[datetime.strptime(end_month,'%Y-%m-%d')]
    crowd_pic4 = crowd_pic3.iloc[-1, :]
    fig, ax = plt.subplots()
    # 提取大于2的柱子索引和高度
    idx = np.where(np.array(crowd_pic4) > 2)[0]
    colors = ['red' if i in idx else 'blue' for i in range(len(crowd_pic4))]
    heights = [crowd_pic4[i] for i in range(len(crowd_pic4))]
    ax.bar(x=crowd_pic4.index.tolist(), height=heights, color=colors)
    # ax.bar(yongjidu_pic4.index, yongjidu_pic4)
    ax.axhline(y=2, color='gray', linestyle='--')  # 添加水平分割线
    plt.xticks(rotation=45)
    plt.ylabel('拥挤度Z值')  # 纵坐标轴标题
    plt.title('拥挤度Z值%s' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "各行业拥挤度Z值分布%s.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def plot_guore(hy_ROE_show,hylist,save_path,endDate):
    jingqi_now2 = hy_ROE_show[['分析师覆盖度', '预期增速变化']]  # 注意有空值
    jingqi_now2 = jingqi_now2.loc[hylist['行业名称'].tolist()]
    jingqi_now2 = jingqi_now2.reindex(hylist['行业名称'].tolist())

    jingqi_show1 = jingqi_now2[jingqi_now2['分析师覆盖度']>=0.5]['预期增速变化'].sort_values(ascending=False)
    jingqi_top = jingqi_show1[jingqi_show1>0].head(10)
    jingqi_show2 = jingqi_now2[jingqi_now2['分析师覆盖度']<0.5]['预期增速变化'].sort_values(ascending=False)
    jingqi_show3 = jingqi_now2[jingqi_now2['分析师覆盖度'].isnull()|jingqi_now2['预期增速变化'].isnull()]['预期增速变化']
    index_  = jingqi_show1.index.tolist()+jingqi_show2.index.tolist()+jingqi_show3.index.tolist()
    jingqi_show1 = jingqi_show1.reindex(index_)
    jingqi_show2 = jingqi_show2.reindex(index_)
    jingqi_show3 = jingqi_show3.reindex(index_)
    jingqi_top = jingqi_top.reindex(index_)

    show_str = '预期ROE边际变化Top10行业: ' + ', '.join(jingqi_top.dropna().index.tolist())


    fig, ax = plt.subplots()
    ax.bar(x=jingqi_show1.index, height=jingqi_show1, color='blue',label='分析师覆盖度>=50%')
    ax.bar(x=jingqi_show2.index, height=jingqi_show2, color='blue',alpha=0.2,label='分析师覆盖度<50%')
    ax.bar(x=jingqi_show3.index, height=0, color='white')
    ax.bar(x=jingqi_top.index, height=jingqi_top, color='red',label='预期ROE边际变化Top10行业')
    ax.legend(loc='upper right')
    ax.text(0.5, 1, show_str, fontsize=fontsize_text, ha='center', va='center', transform=ax.transAxes, color='grey')
    ax.spines['top'].set_visible(False)
    plt.xticks(rotation=45)
    plt.ylabel('预期ROE边际变化（越大越好）')  # 纵坐标轴标题
    plt.title('【过热】域行业选择-分析师预期模型%s\n' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "3.1.2【过热】域行业选择-分析师预期模型%s.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def plot_fusu(jingqi_change_latest_,jingqi_change_corr,hylist,save_path,endDate):
    jingqi_change_latest = jingqi_change_latest_.reindex(hylist['行业名称'].values.tolist())
    jingqi_change_latest = jingqi_change_latest.loc[jingqi_change_corr.index].sort_values(ascending=False)
    jingqi_other = jingqi_change_latest_.reindex(hylist['行业名称'].values.tolist()).loc[[x for x in hylist['行业名称'] if x not in jingqi_change_latest.index]].sort_values(ascending=False)
    index_ = jingqi_change_latest.index.tolist()+jingqi_other.index.tolist()
    jingqi_top = jingqi_change_latest[(jingqi_change_latest.rank(ascending=False,method='min') <= 5) & (jingqi_change_latest>0)].reindex(index_)
    jingqi_notop = jingqi_change_latest.loc[[x for x in jingqi_change_latest.dropna().index if x not in jingqi_top.dropna().index]].reindex(index_)
    jingqi_other = jingqi_other.reindex(index_).fillna(0)

    show_str = '景气向上Top5行业: ' + ', '.join(jingqi_top.dropna().index.tolist())

    fig, ax = plt.subplots()
    ax.bar(x=jingqi_top.index, height=jingqi_top, color='red', label='景气向上Top5行业')
    ax.bar(x=jingqi_notop.index, height=jingqi_notop, color='blue',  label='其他行业')
    ax.bar(x=jingqi_other.index, height=jingqi_other, color='blue', alpha=0.2, label='不适用行业')
    ax.legend(loc='upper right')
    ax.text(0.5, 1, show_str, fontsize=fontsize_text, ha='center', va='center', transform=ax.transAxes, color='grey')
    ax.spines['top'].set_visible(False)
    plt.xticks(rotation=45)
    plt.ylabel('景气边际变化')  # 纵坐标轴标题
    plt.title('【复苏】域行业选择-中观产业景气模型%s\n' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "3.1.1【复苏】域行业选择-中观产业景气模型%s.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def trans_hylist(df,transDate_flag=False,date_df=None):
    for i in range(len(df)):
        df['行业'][i] = str_to_list(df['行业'][i])
    df.index = pd.to_datetime(df.index)
    if transDate_flag:
        df.index = date_df.loc[df.index].values
    return df

def get_toplist(file_name,sheet_name):
    df = pd.read_excel(file_name,sheet_name=sheet_name,index_col=0)
    df.columns = ['行业']
    return trans_hylist(df)

def get_crowdlist(file_name,sheet_name):
    df = pd.read_excel(file_name, sheet_name=sheet_name, index_col=0)
    df_crowd = pd.DataFrame([],index=df.index,columns=['行业'])
    for date in df.index:
        df_crowd.loc[date,'行业'] = df[df==-1].loc[date].dropna().index.tolist()
    return df_crowd

def plot_shuaitui(pbroeTAIL5,crowd_pic,hylist,save_path,endDate):

    # ---拥挤度画分布图
    crowd_pic2 = crowd_pic.iloc[-243 * 3:, :]
    crowd_pic3 = (crowd_pic2 - crowd_pic2.mean()) / crowd_pic2.std()
    crowd_pic4 = crowd_pic3.iloc[-1, :]
    crowd_pic4 = crowd_pic4.sort_values(ascending=False)
    crowdTAIL5 = crowd_pic4[crowd_pic4 >2].index.tolist()

    yujing_both = crowd_pic4.loc[list(set(pbroeTAIL5) & set(crowdTAIL5))].reindex(crowd_pic4.index)
    yujing_no = crowd_pic4.loc[list(set(hylist['行业名称'].tolist()) - set(pbroeTAIL5+crowdTAIL5))].reindex(crowd_pic4.index)
    yujing_pbroe = crowd_pic4.loc[list(set(pbroeTAIL5) - set(yujing_both.dropna().index.tolist()))].reindex(crowd_pic4.index)
    yujing_crowd = crowd_pic4.loc[list(set(crowdTAIL5) - set(yujing_both.dropna().index.tolist()))].reindex(crowd_pic4.index)

    show_str = 'pbroe+拥挤度预警行业: '+ ', '.join(yujing_both.dropna().index.tolist())+ '   pbroe预警行业: '+ ', '.join(yujing_pbroe.dropna().index.tolist())+'   拥挤度预警行业: '+ ', '.join(yujing_crowd.dropna().index.tolist())

    fig, ax = plt.subplots()
    # 提取大于2的柱子索引和高度
    ax.bar(x=crowd_pic4.index, height=yujing_both, color='red', edgecolor='#00FF00',label='pbroe+拥挤度预警行业',linewidth=3)
    ax.bar(x=crowd_pic4.index, height=yujing_pbroe, color='#00FF00', label='pbroe预警行业')
    ax.bar(x=crowd_pic4.index, height=yujing_crowd, color='red',label='拥挤度预警行业')
    ax.bar(x=crowd_pic4.index, height=yujing_no, color='blue', hatch='',label='无预警行业')
    ax.axhline(y=2, color='gray', linestyle='--')  # 添加水平分割线
    ax.legend(loc='upper right')
    xlim = plt.gca().get_xlim()
    wrapped_text = textwrap.fill(show_str, width=int((xlim[1] - xlim[0])))
    ax.text(0.5, 1, wrapped_text, fontsize=fontsize_text, ha='center', va='center', transform=ax.transAxes, color='grey')
    ax.spines['top'].set_visible(False)
    plt.xticks(rotation=45)
    plt.ylabel('拥挤度Z值')  # 纵坐标轴标题
    plt.title('【衰退】域行业选择-赔率复合模型%s\n' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "3.1.4【衰退】域行业选择-赔率复合模型%s.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def plot_zhizhang(score,save_path,endDate):
    score_show = score.sort_values(ascending=False)
    score_top  = score_show[score_show.rank(ascending=False,method='min')<=10]
    score_other = score_show.loc[[x for x in score_show.index if x not in score_top.index]]
    score_top = score_top.reindex(score_show.index)
    scre_other = score_other.reindex(score_show.index)
    show_str = '三模型复合胜率得分Top10行业: '+ ', '.join(score_top.dropna().index.tolist())

    fig, ax = plt.subplots()
    ax.bar(x=score_top.index, height=score_top, color='red', label='胜率得分Top10行业')
    ax.bar(x=scre_other.index, height=scre_other, color='blue', label='其他行业')
    ax.legend(loc='upper right')
    ax.text(0.5, 1, show_str, fontsize=fontsize_text, ha='center', va='center', transform=ax.transAxes, color='grey')
    ax.spines['top'].set_visible(False)
    plt.xticks(rotation=45)
    plt.ylabel('三模型复合胜率得分')  # 纵坐标轴标题
    plt.title('【滞胀】域行业选择-胜率复合模型%s\n' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "3.1.3【滞胀】域行业选择-胜率复合模型%s.png" % endDate)
    plt.show(block=False)
    plt.close('all')

def plot_unsure(score,save_path,endDate):
    score_show = score.sort_values(ascending=False)
    score_top  = score_show[score_show.rank(ascending=False,method='min')<=10]
    score_other = score_show.loc[[x for x in score_show.index if x not in score_top.index]]
    score_top = score_top.reindex(score_show.index)
    scre_other = score_other.reindex(score_show.index)
    show_str = '五模型复合得分Top10行业: '+ ', '.join(score_top.dropna().index.tolist())

    fig, ax = plt.subplots()
    ax.bar(x=score_top.index, height=score_top, color='red', label='五模型复合得分Top10行业')
    ax.bar(x=scre_other.index, height=scre_other, color='blue', label='其他行业')
    ax.legend(loc='upper right')
    ax.text(0.5, 1, show_str, fontsize=fontsize_text, ha='center', va='center', transform=ax.transAxes, color='grey')
    ax.spines['top'].set_visible(False)
    plt.xticks(rotation=45)
    plt.ylabel('五模型复合得分')  # 纵坐标轴标题
    plt.title('【不确定】域行业选择-总复合模型%s\n' % endDate, size=fontsize_suptitle, weight='bold')
    plt.savefig(save_path + "3.1.5【不确定】域行业选择-总复合模型%s.png" % endDate)
    plt.show(block=False)
    plt.close('all')





