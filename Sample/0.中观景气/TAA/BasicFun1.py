import warnings
warnings.filterwarnings('ignore')
import os
import numpy as np
import numpy_financial as npf
import requests
import random
from bs4 import BeautifulSoup
from json import dumps,loads
import ffn
import datetime as dt
from dateutil.parser import parse
import shutil
import re
import copy
from copy import deepcopy
import pickle
import urllib3
import sqlalchemy
from sqlalchemy import create_engine
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
import pandas as pd
import pandas.io.formats.excel as excel
excel.ExcelFormatter.header_style = None
from dateutil.relativedelta import *
import calendar
import cx_Oracle
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True
import matplotlib as mpl
import matplotlib.ticker as ticker
from matplotlib.font_manager import FontProperties
import matplotlib.gridspec as gridspec
from pylab import *
import seaborn as sns
import statsmodels.api as sm
from sklearn import preprocessing
from sklearn import linear_model
from sklearn.metrics import mean_squared_error, r2_score
from scipy.stats import gaussian_kde,spearmanr
import scipy.signal as signal
from scipy.signal import argrelextrema
from scipy import stats
from WindPy import *
print('wind已连接') if w.isconnected() else w.start()
fp=os.getcwd()
#fp = r'D:\FA'
datestyle='%Y-%m-%d'
datestyle2='%Y%m%d'
datestyle3='%Y%m'
light=0.3
fontsize_subtitle = 16
fontsize_suptitle = 18
fontsize_text = 11
fontsize_legend = 9

def pptsize(ppt_size):
    if ppt_size==43:
        plt.rcParams['figure.figsize']=(16,9)    #(20,7)
    elif ppt_size==207:
        plt.rcParams['figure.figsize']=(20,7)
    else:
        plt.rcParams['figure.figsize']=(20,9)
pptsize(209)

t0=dt.datetime.now()
t=t0

#%%定义类别
#tech_prd=['pplus(1y)','pminus(1y)','prd(1y)','pplus(1/3y)','pminus(1/3y)','prd(1/3y)']
#assettype_name=['资产类别(I)','资产类属(II)','资产板块(III)','资产细分(IV)','管理方式(V)']
#assettype=pd.read_excel(fp+'\\detailtype.xlsx',sheet_name='具体分类',header=0,index_col=0)
#assettype.loc['001320.OF',assettype_name]=['权益','行业','消费行业','其他消费','主动']
#assettype.loc['513050.SH',assettype_name]=['权益','全市场','QD权益全市场','QD权益','被动']
#assetcross=pd.DataFrame(assettype[assettype_name])
#assettype_detail1=pd.read_excel(fp+'\\sp.xlsx',sheet_name=assettype_name[0],header=0)[assettype_name[0]].tolist()
#assettype_detail2=pd.read_excel(fp+'\\sp.xlsx',sheet_name=assettype_name[1],header=0)[assettype_name[1]].tolist()
#assettype_detail3=pd.read_excel(fp+'\\sp.xlsx',sheet_name=assettype_name[2],header=0)[assettype_name[2]].tolist()
#assettype_detail4=pd.read_excel(fp+'\\sp.xlsx',sheet_name=assettype_name[3],header=0)[assettype_name[3]].tolist()
#assettype_detail=[assettype_detail1,assettype_detail2,assettype_detail3,assettype_detail4]

#%%定义时间
enddatetime=pd.bdate_range(end=dt.datetime.today(),periods=10)[-1 if ((dt.datetime.today().isoweekday() in [6,7]) | (dt.datetime.now().hour>=21)) else -2].date()       #dt.date(2020,12,31)#
enddate=enddatetime.strftime(datestyle)
enddate2=enddatetime.strftime(datestyle2)
lastdatetime_n=dt.date(2023,4,17)

startdate_1w=(enddatetime+relativedelta(weeks=-1)).strftime(datestyle)
startdate_2w=(enddatetime+relativedelta(weeks=-2)).strftime(datestyle)
startdate_1m=(enddatetime+relativedelta(months=-1)).strftime(datestyle)
startdate_3m=(enddatetime+relativedelta(months=-3)).strftime(datestyle)
startdate_6m=(enddatetime+relativedelta(months=-6)).strftime(datestyle)
startdate_1y=(enddatetime+relativedelta(years=-1)).strftime(datestyle)
startdate_2y=(enddatetime+relativedelta(years=-2)).strftime(datestyle)
startdate_3y=(enddatetime+relativedelta(years=-3)).strftime(datestyle)
startdate_42m=(enddatetime+relativedelta(years=-3,months=-6)).strftime(datestyle)
startdate_5y=(enddatetime+relativedelta(years=-5)).strftime(datestyle)
startdate_10y=(enddatetime+relativedelta(years=-10)).strftime(datestyle)
startdate_15y=(enddatetime+relativedelta(years=-15)).strftime(datestyle)
startdate_2005=dt.date(2005,1,1).strftime(datestyle)
startdate_mtd=(enddatetime+relativedelta(day=1)).strftime(datestyle)
startdate_qtd=pd.date_range(end=enddatetime,periods=1,freq='QS')[-1].strftime(datestyle)
startdate_ytd=(enddatetime+relativedelta(month=1,day=1)).strftime(datestyle)
startdate_n=(lastdatetime_n+relativedelta(days=1)).strftime(datestyle)

lastdatetime_1d=w.tdaysoffset(-1,enddatetime,"Period=D").Data[0][0].date()
lastdatetime_1w=w.tdaysoffset(-1,enddatetime,"Period=W").Data[0][0].date()
lastdatetime_2w=w.tdaysoffset(-2,enddatetime,"Period=W").Data[0][0].date()
lastdatetime_1m=w.tdaysoffset(-1,enddatetime,"Period=M").Data[0][0].date()
lastdatetime_3m=w.tdaysoffset(-3,enddatetime,"Period=M").Data[0][0].date()
lastdatetime_6m=w.tdaysoffset(-6,enddatetime,"Period=M").Data[0][0].date()
lastdatetime_1y=w.tdaysoffset(-1,enddatetime,"Period=Y").Data[0][0].date()
lastdatetime_2y=w.tdaysoffset(-2,enddatetime,"Period=Y").Data[0][0].date()
lastdatetime_3y=w.tdaysoffset(-3,enddatetime,"Period=Y").Data[0][0].date()
lastdatetime_42m=w.tdaysoffset(-42,enddatetime,"Period=M").Data[0][0].date()
lastdatetime_5y=w.tdaysoffset(-5,enddatetime,"Period=Y").Data[0][0].date()
lastdatetime_15y=w.tdaysoffset(-15,enddatetime,"Period=Y").Data[0][0].date()
lastdatetime_lastend=w.tdaysoffset(-0,enddatetime+relativedelta(month=1,day=1),"Period=D").Data[0][0].date()
lastdatetime_lastq=pd.date_range(end=enddatetime,periods=1,freq='Q')[-1].date()
lastdatetime_lastm=pd.date_range(end=enddatetime,periods=1,freq='M')[-1].date()

lastdatetime_tf=w.tdaysoffset(-2,enddatetime,"Period=D").Data[0][0].date()  #
lastdate_tf=lastdatetime_tf.strftime(datestyle)

date_para="NAV_adj_return1,return_1w,return_1m,return_3m,return_6m,return_1y,return_ytd,return"
datelist_name=['近1日','近1周','近1个月','近3个月','近6个月','近1年','今年以来',startdate_n+'以来']
lastdatetime_list=[lastdatetime_1d,lastdatetime_1w,lastdatetime_1m,lastdatetime_3m,lastdatetime_6m,lastdatetime_1y,lastdatetime_lastend,lastdatetime_n]
startdate_list=[enddate,startdate_1w,startdate_1m,startdate_3m,startdate_6m,startdate_1y,startdate_ytd,startdate_n]

enddateM = pd.date_range(end=enddate,periods=4,freq='M')[-1].strftime(datestyle)#筛选月份的最后一个交易日，供wind函数
rptdateQ_list=pd.date_range(end=enddate,periods=8,freq='Q')
rptdateQ=rptdateQ_list[-2 if dt.datetime.today().month in [1,4,7,10] else -1].strftime(datestyle)
rptdateH_list=rptdateQ_list[rptdateQ_list.month.isin([6,12])].strftime(datestyle).tolist()
rptts2H = rptdateH_list[-3:-1] if dt.datetime.today().month in [1,2,3,7,8] else rptdateH_list[-2:]
rptdateH_end=(rptdateH_list[-1] if dt.datetime.today().month not in [1,2,3,7,8] else rptdateH_list[-2]).replace('-','')
rptdateH_last=(rptdateH_list[-2] if dt.datetime.today().month not in [1,2,3,7,8] else rptdateH_list[-3]).replace('-','')

print('截止日期:%s; 今日日期:%s'%(enddate,dt.datetime.now()))

#%%运行时间
def printtime(t):
    if t!=1:
        print('-----' * 5+'花费%s秒'%(dt.datetime.now()-t))
        t=dt.datetime.now()
    else:
        print('—' * 25+'共费%s秒'%(dt.datetime.now()-t0))
    return t

def cost_time(func):
    def fun(*args, **kwargs):
        t = time.perf_counter()
        result = func(*args, **kwargs)
        print(f'func {func.__name__} cost time:{time.perf_counter() - t:.8f} s')
        return result
    return fun

#%%领先滞后期数及相关系数计算
def lag(p,q):       #x、y均为Series
    z=pd.concat([p,q],axis=1,join='inner')
    x=z.iloc[:,0]
    y=z.iloc[:,1]
    corr=[]
    n = np.linspace(-12, 12, 25)
    for i in range(len(n)):
        corr.append(x.corr(y.shift(int(n[i]))))
    corr_abs=np.abs(corr).tolist()
    j=int(n[corr_abs.index(max(corr_abs))])
    k='%.2f'%corr[corr_abs.index(max(corr_abs))]
    l='%.2f'%corr[12]
    m='相关系数:'+str(l)+'  领先期数:'+str(j)+'  最大相关系数:'+str(k)
    return j,k,l,m      #领先期数，最大相关系数，相关系数

def lag_onlyP(p,q,N=None):       #x、y均为Series
    z=pd.concat([p,q],axis=1,join='inner')
    if N is None:
        x=z.iloc[:,0]
        y=z.iloc[:,1]
    else:
        x=z.iloc[:,0].iloc[-N:]
        y=z.iloc[:,1].iloc[-N:]
    corr=[]
    n = np.linspace(-6, 6, 13)
    for i in range(len(n)):
        corr.append(x.corr(y.shift(int(n[i]))))
    j=int(n[corr.index(max(corr))])
    k='%.2f'%corr[corr.index(max(corr))]
    l='%.2f'%corr[6]
    m='相关系数:'+str(l)+'  领先期数:'+str(j)+'  最大相关系数:'+str(k)
    return j,k,l,m      #领先期数，最大相关系数，相关系数

#%%单图
def fig1(title,name,color,data,path,axnum):
    fig,ax=plt.subplots(nrows=1,ncols=1,sharex=False)  #画多图
    if axnum==1:
        for m in range(len(name)):
            data_m=data[name[m]].dropna(how='any')
            ax.plot(data_m.index,data_m,label=name[m],color=color[m])
            ax.text(data_m.index[-1],data_m[-1],name[m]+','+str('%.2f'%data_m[-1]),color=color[m],fontsize=fontsize_text)
        ax.legend(fontsize=fontsize_legend)
        ax.grid(alpha=light)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.set_xlim(data.index[0],data.index[-1])
    elif axnum==2:
        ax0=ax.twinx()
        axis=[ax,ax0]
        for n in range(2):#左右轴
            for j in range(len(name[n])):
                data_m=data[name[n][j]].dropna(how='any')
                axis[n].plot(data_m.index,data_m,label=name[n][j],color=color[n][j])
                axis[n].text(data_m.index[-1],data_m[-1],name[n][j]+','+str('%.2f'%data_m[-1]),color=color[n][j],fontsize=fontsize_text)
            if n==0:
                axis[n].grid(alpha=light)  # 横线
                axis[n].set_xlim(data.index[0],data.index[-1])
                axis[n].tick_params(axis='x', direction='out',rotation=45)
            axis[n].legend(fontsize=fontsize_legend,loc=2-n)
            axis[n].spines['top'].set_visible(False)
    fig.suptitle(title+'('+data.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
    fig.tight_layout()
    fig.savefig(fp+'\\Result\\'+path+title+'.png')
    plt.close()

#%%双图
def fig11(title,name,color,data,path,axnum):
    fig,ax=plt.subplots(nrows=1,ncols=2,sharex=False)  #画多图
    if axnum==1:
        axis=[ax[0],ax[1]]
        for m in range(2):#左右图
            for j in range(len(name[m])):
                data_m=data[name[m][j]].dropna(how='any')
                axis[m].plot(data_m.index,data_m,label=name[m][j],color=color[m][j])
                axis[m].text(data_m.index[-1],data_m[-1],name[m][j]+','+str('%.2f'%data_m[-1]),color=color[m][j],fontsize=fontsize_text)
            axis[m].grid(alpha=light)  # 横线
            axis[m].set_xlim(data.index[0],data.index[-1])
            axis[m].tick_params(axis='x', direction='out',rotation=45)
            axis[m].legend(fontsize=fontsize_legend,loc=0)
            axis[m].spines['top'].set_visible(False)
            axis[m].tick_params(axis='y', direction='out')
    elif axnum==2:
        ax0=ax[0].twinx()
        ax1=ax[1].twinx()
        axis=[[ax[0],ax0],[ax[1],ax1]]
        for m in range(2):#左右图
            for n in range(2):#左右轴
                for j in range(len(name[m][n])):
                    data_m=data[name[m][n][j]].dropna(how='any')
                    axis[m][n].plot(data_m.index,data_m,label=name[m][n][j],color=color[m][n][j])
                    axis[m][n].text(data_m.index[-1],data_m[-1],name[m][n][j]+','+str('%.2f'%data_m[-1]),color=color[m][n][j],fontsize=fontsize_legend)
                if n==0:
                    axis[m][n].grid(alpha=light)  # 横线
                    axis[m][n].set_xlim(data.index[0],data.index[-1])
                    axis[m][n].tick_params(axis='x', direction='out',rotation=45)
                axis[m][n].legend(fontsize=fontsize_legend,loc=2-n)
                axis[m][n].spines['top'].set_visible(False)
                axis[m][n].tick_params(axis='y', direction='out')
    fig.suptitle(title+'('+data.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
    fig.tight_layout()
    fig.savefig(fp+'\\Result\\'+path+title+'.png')
    plt.close()

#%%设定excel格式
def excel_format(writer_name):
    workbook=writer_name.book
    format = workbook.add_format({
        'bold': False,  # 字体加粗
        'text_wrap': False,  # 是否自动换行
        'valign': 'vcenter',  # 垂直对齐方式
        'align': 'center',  # 水平对齐方式
        'font_name': 'Microsoft YaHei',   # 微软雅黑字体
        'num_format': '_ * #,##0.00_ ;[red]_ * -#,##0.00_ ;_ * "-"??_ ;_ @_ ',
        'font_size': 9})    #字体大小
    format_str_bold = workbook.add_format({
        'bold': True,  # 字体加粗
        'text_wrap': False,  # 是否自动换行
        'valign': 'vcenter',  # 垂直对齐方式
        'align': 'center',  # 水平对齐方式
        'font_name': 'Microsoft YaHei',   # 微软雅黑字体
        'num_format': '_ * #,##0.00_ ;[red]_ * -#,##0.00_ ;_ * "-"??_ ;_ @_ ',
        'font_size': 9})    #字体大小
    format_percent = workbook.add_format({
        'bold': False,  # 字体加粗
        'text_wrap': False,  # 是否自动换行
        'valign': 'vcenter',  # 垂直对齐方式
        'align': 'center',  # 水平对齐方式
        'font_name': 'Microsoft YaHei',   # 微软雅黑字体
        'num_format': '_ * #,##0.00%_ ;[red]_ * -#,##0.00%_ ;_ * "-"??_ ;_ @_ ',
        'font_size': 9})    #字体大小
    format_date = workbook.add_format({
        'bold': False,  # 字体加粗
        'text_wrap': False,  # 是否自动换行
        'valign': 'vcenter',  # 垂直对齐方式
        'align': 'center',  # 水平对齐方式
        'font_name': 'Microsoft YaHei',   # 微软雅黑字体
        'num_format': 'yyyymmdd',   #日期格式
        'font_size': 9})    #字体大小
    format_color = workbook.add_format({
        'bold': True,  # 字体加粗
        'text_wrap': False,  # 是否自动换行
        'valign': 'vcenter',  # 垂直对齐方式
        'align': 'center',  # 水平对齐方式
        'font_name': 'Microsoft YaHei',   # 微软雅黑字体
        'num_format': '_ * #,##0.00_ ;[red]_ * -#,##0.00_ ;_ * "-"??_ ;_ @_ ',
        'font_size': 9,
        'bg_color': '#DCDCDC'})  # 字体大小
    format_color_icbccs = workbook.add_format({
        'bold': True,  # 字体加粗
        'text_wrap': False,  # 是否自动换行
        'valign': 'vcenter',  # 垂直对齐方式
        'align': 'center',  # 水平对齐方式
        'font_name': 'Microsoft YaHei',   # 微软雅黑字体
        'num_format': '_ * #,##0.00_ ;[red]_ * -#,##0.00_ ;_ * "-"??_ ;_ @_ ',
        'font_size': 9,
        'bg_color': '#FFEFD5'})  # 字体大小
    sheet_names=list(writer_name.sheets.keys())
    for i in range(len(sheet_names)):
        [writer_name.sheets[sheet_names[i]].set_row(k, None, format_color) for k in range(1, writer_name.sheets[sheet_names[i]].dim_rowmax+1, 2)]  # 整表格式
        writer_name.sheets[sheet_names[i]].set_column(1,writer_name.sheets[sheet_names[i]].dim_colmax,None,format)   #整表格式
        writer_name.sheets[sheet_names[i]].set_column(0,0,None,format_str_bold)   #整表格式
        writer_name.sheets[sheet_names[i]].set_row(0,None,format_str_bold)   #整表格式
    writer_name._save()
    return writer_name

#%%分散化效果图
def diversify_fig(data_selected):
    ret_selected=data_selected.to_returns().dropna()
    vol_selected=ret_selected.std()*(252**0.5)
    vol_selected_multiply=vol_selected[data_selected.columns[0]]/vol_selected
    dd_selected=data_selected.to_drawdown_series()
    dd_base=dd_selected[data_selected.columns[0]]
    ret_compare=pd.DataFrame(columns=[data_selected.columns[1]],dtype='float32')
    for i in range(len(dd_base)):
        if dd_base[i]==0:
            ret_compare.loc[dd_base.index[i],data_selected.columns[1]]=0
        else:
            ret_compare.loc[dd_base.index[i],:]=(ret_compare.loc[dd_base.index[i-1],:]+1)*(ret_selected.loc[dd_base.index[i],data_selected.columns[1]]+1)-1
    titlename=['两者保持原波动率','两者拉齐波动率']
    type=['回撤','回报(在'+data_selected.columns[0]+'回撤时)']
    data=[pd.concat([dd_base,ret_compare],axis=1,join='outer')*100,pd.concat([dd_base,ret_compare*vol_selected_multiply[1]],axis=1,join='outer')*100]
    color=['red','blue']
    fig,ax=plt.subplots(nrows=1,ncols=2,sharex=False)  #画多图
    for n in range(2):
        for m in range(len(data_selected.columns)):
            data_m=data[n][data_selected.columns[m]].dropna(how='any')
            ax[n].fill_between(data_m.index,data_m,label=data_selected.columns[m]+type[m],color=color[m])
        ax[n].legend(fontsize=fontsize_legend)
        ax[n].grid(alpha=light)
        ax[n].spines['top'].set_visible(False)
        ax[n].spines['right'].set_visible(False)
        ax[n].yaxis.set_major_formatter(ticker.StrMethodFormatter('{x:.0f}' + '%'))  # 百分比显示
        ax[n].set_xlim(data[n].index[0],data[n].index[-1])
        ax[n].set_title(titlename[n],fontsize=fontsize_subtitle)
    fig.suptitle(data_selected.columns[1]+'对'+data_selected.columns[0]+'的分散化效果('+data[n].index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
    fig.tight_layout()
    fig.savefig(fp+'\\Result\\5.2Test\\'+data_selected.columns[1]+'对'+data_selected.columns[0]+'的分散化效果.png')
    plt.close()

#%%基金指标
def indicator(code,startdate,enddate,benchmark):
    nav = pd.DataFrame()
    code_ps=pd.Series(code)
    code_ps=code_ps[code_ps!=benchmark]
    code_nav=code_ps[code_ps.str.contains('OF')].values.tolist()
    code_price=code_ps[~code_ps.str.contains('OF')].values.tolist()
    if len(code_nav)>0:
        for i in range(int(np.ceil(len(code_nav) / 500))):
            nav_temp = w.wsd(code_nav[i * 500:(i + 1) * 500], "NAV_adj", startdate, enddate, usedf=True)[1]
            nav = pd.concat([nav, nav_temp], axis=1, join='outer')
    if len(code_price)>0:
        for j in range(int(np.ceil(len(code_price) / 500))):
            price_temp = w.wsd(code_price[j * 500:(j + 1) * 500], "close", startdate, enddate, 'PriceAdj=F', usedf=True)[1]
            nav = pd.concat([nav, price_temp], axis=1, join='outer')
    nav=nav.loc[:,nav.columns != benchmark]
    nav_all=pd.concat([nav,w.wsd(benchmark,"NAV_adj" if benchmark[-2:]=='OF' else 'close', startdate, enddate, usedf=True)[1]],join='outer',axis=1)
    nav_all.columns=code_nav+code_price+[benchmark]
    nav_all=nav_all.dropna(how='any',axis=1)
    ret=nav_all.to_returns().dropna(how='all',axis=0)
    pplus=ret[ret[benchmark]>0].sum(axis=0)
    pplus=(pplus/pplus[-1])[:-1]
    pminus=ret[ret[benchmark]<0].sum(axis=0)
    pminus=(pminus/pminus[-1])[:-1]
    prd=pplus-pminus
    data_prd=pd.concat([pplus,pminus,prd],join='outer',axis=1)
    data_prd.columns=['pplus','pminus','prd']
    mdd=nav_all.calc_max_drawdown()
    data_mdddiff=pd.DataFrame(mdd[:-1]-mdd[-1],columns=['mdddiff'])
    data_final=pd.concat([data_prd,data_mdddiff],join='outer',axis=1)
    data_final['mdd']=mdd[:-1]
    data_final['dd']=(nav_all.iloc[-1,:].div(np.max(nav_all)) - 1)[:-1]
    data_final['rebound']=(nav_all.iloc[-1,:].div(np.min(nav_all)) - 1)[:-1]
    data_final['ir']=[ffn.calc_information_ratio(ret.iloc[:,i],ret.iloc[:,-1]) for i in range(ret.shape[1]-1)]
    data_final['ret']=nav_all.calc_cagr()[:-1]
    data_final['alpha']=nav_all.calc_cagr()[:-1]-nav_all.calc_cagr()[-1]
    data_final['alphavol']=ret.sub(ret.iloc[:,-1],axis=0).std(axis=0)[:-1]
    data_final['corr']=ret.iloc[:,:-1].corrwith(ret.iloc[:,-1])
    data_final['peak_ratio']=nav_all.apply(lambda x:(x.to_drawdown_series().value_counts()[0]/x.to_drawdown_series().count()),axis=0)
    return data_final

#%%定义基金及股票的穿透比例
def pct_pure(subfundcode,rpt_date):
    pct_pure = w.wss(subfundcode,"prt_stocktonav,prt_convertiblebondtonav,prt_bondtonav,prt_othertonav,prt_SIfutures,prt_fundnetasset_total",rptDate=rpt_date, usedf=True)[1].fillna(0)
    pct_pure['pct_pure_equity'] = pct_pure["PRT_STOCKTONAV"] + pct_pure["PRT_CONVERTIBLEBONDTONAV"] * 0.5 + pct_pure["PRT_SIFUTURES"] * 100 / pct_pure["PRT_FUNDNETASSET_TOTAL"]
    pct_pure['pct_pure_bond']=pct_pure["PRT_BONDTONAV"]-pct_pure["PRT_CONVERTIBLEBONDTONAV"]*0.5
    pct_pure['pct_pure_other']=pct_pure["PRT_OTHERTONAV"]
    #设定股票穿透比例为100
    for i in range(len(pct_pure.index)):
        if (pct_pure.index[i][0] == '6' and pct_pure.index[i][-2:] == 'SH') or (((pct_pure.index[i][0] == '0') or (pct_pure.index[i][0] == '3')) and pct_pure.index[i][-2:] == 'SZ'):
            pct_pure.iloc[i, pct_pure.columns.tolist().index('pct_pure_equity')] = 100
    pct_pure['pct_pure_cash']=100-pct_pure["pct_pure_equity"]-pct_pure["pct_pure_bond"]-pct_pure['pct_pure_other']
    pct_pure=pct_pure[['pct_pure_equity','pct_pure_bond','pct_pure_other','pct_pure_cash']]
    pct_pure.columns=['穿透权益占比','穿透债券占比','穿透其他占比','穿透现金占比']
    return pct_pure

#%%定义单个组合单个日期的穿透比例
def fof_pure(fofcode,rpt_date):
    fund_holding = w.wset("fundshelddetails,", windcode=fofcode, rptdate=rpt_date, field='windcode,fundname,navratio',usedf=True)[1]
    if fund_holding.shape[0] != 0:
        fund_holding.columns = ['代码', '基金名称', '净值占比']
        fund_holding['类别']='基金'
    else:
        fund_holding = pd.DataFrame([], columns=['代码', '基金名称', '净值占比', '类别'])
    fund_holding.columns = ['代码', '基金名称', '净值占比', '类别']

    stock_holding = w.wset("allfundhelddetail", rptdate=rpt_date, windcode=fofcode,field='stock_code,stock_name,proportiontonetvalue', usedf=True)[1]
    if stock_holding.shape[0] != 0:
        stock_holding.columns = ['代码', '基金名称', '净值占比']
        stock_holding['类别'] = '股票'
    else:
        stock_holding = pd.DataFrame([], columns=['代码', '基金名称', '净值占比', '类别'])
    bond_holding = w.wset("bondhelddetails", windcode=fofcode, rptdate=rpt_date, field='bondcode,bondname,fundnet',usedf=True)[1]
    if bond_holding.shape[0] != 0:
        bond_holding.columns = ['代码', '基金名称', '净值占比']
        bond_holding['类别'] = '债券'
    else:
        bond_holding = pd.DataFrame([], columns=['代码', '基金名称', '净值占比', '类别'])
    data_holding = pd.concat([fund_holding, stock_holding, bond_holding], axis=0, join='outer')
    data_holding = data_holding[data_holding['基金名称'] != '合计']
    data_holding.index = data_holding['代码']
    data_holding = data_holding[['代码', '基金名称', '净值占比', '类别']]
    data_holding.loc[data_holding[data_holding['类别']=='基金'].index,'权益占比']=data_holding[data_holding['类别']=='基金']['代码'].apply(lambda x:pct_pure(x,rpt_date).loc[x,'穿透权益占比'])
    data_holding.loc[data_holding[data_holding['类别']=='股票'].index,'权益占比']=100
    data_holding.loc[data_holding[data_holding['类别']=='债券'].index,'权益占比']=0
    data_holding['权益贡献比例']=data_holding['净值占比']*data_holding['权益占比']/100
    sum_pct_equity=data_holding['权益贡献比例'].sum()
    return sum_pct_equity

#%%获取单个基金报告期持仓模拟累计收益率
def cumret_simulation(code_single,rpt_date,startdate,enddate): #startdate往前移一个交易日
    fund_holding = w.wset("fundshelddetails,", windcode=code_single, rptdate=rpt_date, field='windcode,navratio', usedf=True)[1]
    if fund_holding.shape[0] != 0:
        fund_holding.columns = ['代码', '净值占比']
        fund_holding.index = fund_holding['代码']
        nav_fund_holding = w.wsd(fund_holding['代码'].tolist(), "NAV_adj", startDate=startdate, endDate=enddate, usedf=True)[1]
        cumret_fund_holding = (nav_fund_holding / nav_fund_holding.iloc[0, :] - 1).iloc[1:, :]
    else:
        fund_holding = pd.DataFrame([], columns=['代码', '净值占比'])
        cumret_fund_holding=pd.DataFrame()
    stock_holding = w.wset("allfundhelddetail", rptdate=rpt_date, windcode=code_single,field='stock_code,proportiontonetvalue', usedf=True)[1]
    if stock_holding.shape[0] != 0:
        stock_holding.columns = ['代码', '净值占比']
        stock_holding.index = stock_holding['代码']
        nav_stock_holding = w.wsd(stock_holding['代码'].tolist(), "close", startDate=startdate, endDate=enddate, usedf=True)[1]
        cumret_stock_holding = (nav_stock_holding / nav_stock_holding.iloc[0, :] - 1).iloc[1:, :]
    else:
        stock_holding = pd.DataFrame([], columns=['代码', '净值占比'])
        cumret_stock_holding = pd.DataFrame()
    bond_holding = w.wset("bondhelddetails", windcode=code_single, rptdate=rpt_date, field='bondcode,fundnet', usedf=True)[1]
    if bond_holding.shape[0] != 0:
        bond_holding = bond_holding[bond_holding['bondname'] != '合计']
        bond_holding.columns = ['代码', '净值占比']
        bond_holding.index = bond_holding['代码']
        nav_bond_holding = w.wsd(bond_holding['代码'].tolist(), "close", startDate=startdate, endDate=enddate, usedf=True)[1]
        cumret_bond_holding = (nav_bond_holding / nav_bond_holding.iloc[0, :] - 1).iloc[1:, :]
    else:
        bond_holding = pd.DataFrame([], columns=['代码', '净值占比'])
        cumret_bond_holding = pd.DataFrame()
    data_holding = pd.concat([fund_holding, stock_holding, bond_holding], axis=0, join='outer')
    cumret_holding = pd.concat([cumret_fund_holding, cumret_stock_holding, cumret_bond_holding], axis=1, join='outer')
    cumret = (cumret_holding * data_holding['净值占比']).sum(axis=1)
    return cumret

def transDic2Df(top_dic,f,start_date=None):
    df = pd.DataFrame([],index=top_dic.keys(),columns=[f])
    for i in df.index:
        df.loc[i,f] = top_dic[i]
    df = df.sort_index()
    if start_date is not None:
        df = df.loc[start_date:]
    return df

def single_roll_rank(df,asc_flag):
    return df.rank(pct=True,ascending=asc_flag,method='average').iloc[-1]

def roll_rank(df,roll_days,asc_flag):
    return df.rolling(roll_days,min_periods=int(roll_days/2)).apply(lambda x:single_roll_rank(x,asc_flag))
