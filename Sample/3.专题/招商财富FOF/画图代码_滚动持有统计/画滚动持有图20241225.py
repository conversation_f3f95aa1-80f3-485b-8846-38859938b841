import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.axes_grid1 import host_subplot
import mpl_toolkits.axisartist as AA
import datetime as dt
import copy
import os
import matplotlib.dates as mdates
import matplotlib
# matplotlib.use('TkAgg')
from matplotlib.ticker import MultipleLocator, FormatStrFormatter
from matplotlib.font_manager import FontProperties
import pymysql
import cx_Oracle
from dateutil.relativedelta import relativedelta
from scipy.stats import spearmanr
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
import warnings
warnings.filterwarnings("ignore")
from scipy.stats import percentileofscore
from tqdm import tqdm
#画图环境设置
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['figure.dpi']=400
plt.rcParams['lines.linewidth']=1
plt.rcParams['figure.autolayout']=True
# plt.rcParams['font.weight'] = 'normal'

light=0.5
linewidth_table=0.5
fontsize_subtitle = 18 #16
fontsize_suptitle = 20 #18
fontsize_text = 14 #11
fontsize_legend = 13 #9
fontsize_label = 15
figsize = [24,10]

from WindPy import *
w.start()
print("是否已连接wind:", w.isconnected())

import os
fp=os.getcwd()
os.environ['NLS_LANG']='SIMPLIFIED CHINESE_CHINA.UTF8'
from sqlalchemy import create_engine
engine=create_engine('oracle+cx_oracle://wind_read:Wind_read_100010@**************:1521/wind')
def ReadSql(sqlStr, conn, arraySize=20000000):
    """
    从数据库读取数据
    :param sqlStr: sql查询代码
    :param conn: 数据库连接
    :param fieldNames: 列名映射表
    """
    if type(conn) == pymysql.Connection:
        data = pd.read_sql(sqlStr, conn)
        return data.rename(columns={"Date1": "Date", "Level1": "Level"}, copy=False)
    elif type(conn) == cx_Oracle.Connection:
        # oracle数据库Date为保留关键字, 取出的数据列名默认全大写, 需要调整
        cursor = conn.cursor()
        cursor.arraysize = arraySize
        cursor.execute(sqlStr)
        title = [i[0] for i in cursor.description]
        data = pd.DataFrame(cursor.fetchall(), columns=title)
        return data.rename(columns={"Date1": "Date", "Level1": "Level"}, copy=False)
    else:
        raise Exception("conn should be one of cx_Oracle/pymssql Connection.")

functionfilepath = "D:\gyrx_gn\研究类\通用代码/0.封装函数"
sys.path.append(functionfilepath)
import sqlGetData
import calfunctions

def get_tdate_list(start_date,end_date,period_type,date_format='%Y%m%d'):
    tdays_df = w.tdays(start_date, end_date, f"Period={period_type}", usedf=True)[1]
    tdays_df.index = pd.to_datetime(tdays_df.index)
    return tdays_df.index.strftime(date_format).tolist()
def MaxDrawdown(compose_data_array):
    maxdrawdown_stoptime = np.argmax((np.maximum.accumulate(compose_data_array)-compose_data_array))
    if maxdrawdown_stoptime==0:
        return 0
    maxdrawdown_starttime = np.argmax(compose_data_array[:maxdrawdown_stoptime])
    drawdown_max = 100*(compose_data_array[maxdrawdown_starttime]-compose_data_array[maxdrawdown_stoptime])/compose_data_array[maxdrawdown_starttime]
    return drawdown_max[0]
def Allocation_Test_Result(compose_data, account_period, flag):
    # 收益率
    gundong_return = compose_data.pct_change(periods=account_period)
    gundong_return.dropna(axis=0, how='all', inplace=True)
    gundong_return.columns = ['收益率']
    if flag == 1:
        return gundong_return
    else:
        # 最大回撤+波动率+夏普
        gundong_maxdrawdown = []
        gundong_stdev_annual = []
        gundong_stdev_annual_5y = []
        for i in range(0, compose_data.shape[0] - account_period):
            compose_data_period = compose_data.iloc[i:i + account_period, :]
            period_maxdrawdown = MaxDrawdown(compose_data_period.values)
            gundong_maxdrawdown = np.append(gundong_maxdrawdown, period_maxdrawdown)

            compose_data_return = compose_data_period.pct_change()
            compose_data_return.columns = ['return']
            compose_data_return.dropna(axis=0, how='all', inplace=True)
            stdev = compose_data_return['return'].std()
            stdev_annual = stdev * 15.81  # math.sqrt(250)
            stdev_annual_5y = stdev * np.sqrt(account_period)  # math.sqrt(250)
            gundong_stdev_annual = np.append(gundong_stdev_annual, stdev_annual)
            gundong_stdev_annual_5y = np.append(gundong_stdev_annual_5y, stdev_annual_5y)

        gundong_return['年化收益率(%)'] = (pow(1 + gundong_return['收益率'], 250 / account_period) - 1) * 100
        gundong_return['最大回撤(%)'] = gundong_maxdrawdown
        gundong_return['年化波动率(%)'] = gundong_stdev_annual * 100
        gundong_return['区间波动率(%)'] = gundong_stdev_annual_5y * 100
        # 夏普比
        gundong_return['夏普比（年化）'] = gundong_return.apply(
            lambda x: (100 * x['收益率'] - 1.5 * account_period / 250) / x['区间波动率(%)'], axis=1)  # 原参数是4.7，改为1.5
        # Calmar
        gundong_return['Calmar'] = gundong_return.apply(lambda x: x['年化收益率(%)'] / x['最大回撤(%)'], axis=1)
        gundong_return.drop(columns=['收益率'], inplace=True)
    return gundong_return

def show_single_describe(df):
    result = pd.Series()
    result.loc['最大'] = df.max()
    result.loc['90分位'] = df.quantile(0.9)
    df.loc['75分位'] = df.quantile(0.75)
    result.loc['中位数'] = df.median()
    result.loc['25分位'] = df.quantile(0.25)
    result.loc['10分位'] = df.quantile(0.1)
    result.loc['最小'] = df.min()
    return result

def plot_finalfig(df_nav,df_describe,save_path):
    name = df_nav.columns[0]
    df_nav = df_nav.dropna(how='all')
    df_nav = df_nav / df_nav.iloc[0]
    nav = df_nav[name]
    df_alloc_plt = df_nav[[name]]
    performance_list = nav.dropna()
    df_linshi = pd.DataFrame(columns=['data', 'max2now', 'dongtai_mdd'])
    df_linshi['data'] = performance_list
    df_linshi['max2now'] = np.maximum.accumulate(performance_list)
    df_linshi['dongtai_mdd'] = (df_linshi['data'] - df_linshi['max2now']) / df_linshi['max2now']

    df_linshi['日收益率'] = (df_nav[name] / df_nav[name].shift(1) - 1)[1:]
    df_linshi['滚动持有收益率'] = (df_nav[name] / df_nav[name].shift(rollingwindow) - 1)[rollingwindow:]
    # 【连续最大回撤天数】若组合当日收益率为负，则记为回撤天数，考察最大连续回撤天数
    df_linshi['最大连续回撤天数'] = 0
    current_drawdown = 0  # 初始化结果数组和当前回撤天数为0
    for i in range(1, len(df_linshi['日收益率']) - 1):  # 遍历所有收益率数据，统计最大连续回撤天数
        if df_linshi['日收益率'].iloc[i] < 0:
            current_drawdown += 1
            df_linshi.loc[df_linshi.index[i], '最大连续回撤天数'] = current_drawdown
        else:
            current_drawdown = 0

    fig, ax = plt.subplots(figsize=figsize)
    par1 = ax.twinx()
    par2 = ax.twinx()
    ax.set_ylabel("累计净值", color='blue')
    par1.set_ylabel("动态回撤", color='#C0C0C0')
    par2.set_ylabel(rollingwindowname + "滚动持有收益", color='red')

    t0 = df_alloc_plt.index[0]
    t1 = df_alloc_plt.index[-1]
    k1 = round((((df_alloc_plt.iloc[-1])[name]) ** (365 / (t1 - t0).days) - 1) * 100, 2)
    k2 = round((((df_alloc_plt.iloc[-1])[name]) - 1) * 100, 2)
    p1, = ax.plot(df_alloc_plt.index, df_alloc_plt[name],
                  label='组合净值（左轴）：年化' + str(k1) + '%/累计收益' + str(k2) + '%', color='blue', linewidth=2.0,
                  zorder=2)
    ax.tick_params(axis='y', direction='out', color='blue', labelcolor='blue')

    par1.fill_between(x=df_linshi.index, y1=0, y2=df_linshi['dongtai_mdd'], facecolor='gainsboro', alpha=0.2, zorder=1)
    p2, = par1.plot(df_linshi.index, df_linshi['dongtai_mdd'],
                    label='动态回撤（右一轴）：历史最大回撤' + str(abs(round(min(df_linshi['dongtai_mdd']) * 100,2))) + '%',
                    color="#C0C0C0")
    hdrtn_positive = len(df_linshi[df_linshi['滚动持有收益率'] > 0]) / len(df_linshi['滚动持有收益率'].dropna())
    p3, = par2.plot(df_linshi.index, df_linshi['滚动持有收益率'],
                    label=rollingwindowname + '滚动持有收益率（右二轴）：中位数' + str(
                        round(np.median(df_linshi['滚动持有收益率'].dropna()) * 100, 2)) + '%/正收益概率' + str(
                        (round(hdrtn_positive * 100, 2))) + '%', color='red')
    p33, = par2.plot(df_linshi.index, [0] * len(df_linshi['滚动持有收益率']), color='orange', ls='--', alpha=0.5)
    ax.set_xlim(df_alloc_plt.index[0], df_alloc_plt.index[-1])
    par1.set_ylim(df_linshi['dongtai_mdd'].min() - 0.01, 0)
    par2.set_ylim(df_linshi['滚动持有收益率'].min() - 0.01, df_linshi['滚动持有收益率'].max())
    par2.axhline(y=5, c='y', ls='--')

    ax.legend(loc='upper left')
    ax.spines['top'].set_visible(False)
    par1.spines['top'].set_visible(False)
    par2.spines['top'].set_visible(False)
    par1.tick_params(axis='y', direction='out', color='#C0C0C0', labelcolor='#C0C0C0')
    par2.tick_params(axis='y', direction='out', color='orange', labelcolor='red')
    ax.spines['left'].set_color('blue')
    par1.spines['left'].set_color('blue')
    par2.spines['left'].set_color('blue')
    par1.spines['right'].set_color('#C0C0C0')
    par2.spines['right'].set_color('red')

    par1.table(cellText=df_describe.values,
               cellLoc='center',
               colWidths=[0.07] * len(df_describe.columns),
               rowLabels=df_describe.index,
               colLabels=df_describe.columns,
               loc='lower center', bbox=[0.7, 0.001, 0.3, 0.1])
    plt.suptitle(sub_FOFname + f"_累计净值&回撤&滚动持有收益表现({df_nav.dropna().index.strftime('%Y%m%d')[0]}-{df_nav.index.strftime('%Y%m%d')[-1]})", fontsize=20,
                 fontweight='bold')
    par2.spines['right'].set_position(('outward', 60))

    g = [p1, p2, p3]
    f = [l.get_label() for l in g]
    plt.legend(g, f, loc='upper left')

    plt.tight_layout()
    fig.savefig(save_path)

if __name__ == '__main__':
    ''' 1.参数设置 '''
    #日期类
    startDate = '2019-01-01'
    endDate = '2024-12-17'

    #地址类
    output_path = './'

    #产品信息类
    FOFcode = ['015975.OF', '008144.OF']
    FOFcode_dic = {'015975.OF': '工银积极养老目标五年持有混合发起（FOF）', '008144.OF': '工银智远配置三个月持有期混合FOF'}

    #待运行的产品
    sub_FOFname = '工银智远配置三个月持有期混合FOF'

    #滚动窗口
    rollingwindow = 21 * 6
    if rollingwindow == 21 * 3:
        rollingwindowname = '季度'
    elif rollingwindow == 21 * 1:
        rollingwindowname = '月度'
    elif rollingwindow == 21 * 6:
        rollingwindowname = '半年度'
    elif rollingwindow == 21 * 12:
        rollingwindowname = '年度'

    ''' 2.净值读取与处理 '''
    fund_nav = w.wsd(FOFcode, "NAV_adj", startDate,endDate, "",usedf = True)[1]
    fund_nav.index = pd.to_datetime(fund_nav.index)
    tdays_list = get_tdate_list(fund_nav.index.strftime('%Y-%m-%d')[0], fund_nav.index.strftime('%Y-%m-%d')[-1], 'D','%Y-%m-%d')
    fund_nav.columns = [FOFcode_dic[i] for i in fund_nav.columns]

    test_result = Allocation_Test_Result(fund_nav[[sub_FOFname]].reindex(tdays_list).fillna(method='ffill').dropna(), rollingwindow, 0)
    df_describe = test_result[['年化收益率(%)','最大回撤(%)']].apply(lambda x:show_single_describe(x)).T
    df_describe = df_describe.apply(lambda x: round(x, 2))
    df_describe = df_describe.rename({'年化收益率(%)':'半年度滚动年化收益率(%)'})
    df_nav = fund_nav[[sub_FOFname]].reindex(tdays_list).fillna(method='ffill')
    df_nav.index = pd.to_datetime(df_nav.index)

    ''' 3.画图 '''
    plot_finalfig(df_nav,df_describe,f"{output_path}{sub_FOFname}_累计净值&回撤&滚动持有收益表现({df_nav.dropna().index.strftime('%Y%m%d')[0]}-{df_nav.dropna().index.strftime('%Y%m%d')[-1]}).png")
