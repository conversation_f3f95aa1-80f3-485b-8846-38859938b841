import os
fpdata = os.getcwd()

import pandas as pd
import numpy as np
from scipy import stats
import datetime as dt
import math
import time
from scipy import stats
from scipy.optimize import minimize
from dateutil.relativedelta import relativedelta
import statsmodels.api as sm
#import matplotlib
#matplotlib.__version__
import matplotlib.pyplot as plt
from matplotlib import cm
import seaborn as sns
from scipy import stats
import os
today = dt.datetime.today()+ relativedelta(days=-1)
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import FuncFormatter
# 自定义刻度标签格式函数
def percentage_format(x, pos):
    return '{:.0%}'.format(x)

import joypy
from WindPy import *
w.start()
#w.close()
w.isconnected()
plt.rcParams['font.sans-serif'] = ['SimHei']#显示中文
plt.rcParams['axes.unicode_minus'] = False#显示负号
from collections import Counter
import pymysql
import cx_Oracle
import pickle


def Compose_Data(original_data, assetweight, allocation_frequency):
    # 资产配置净值模拟函数
    # assetcode="881001.WI,H11001.CSI"  #资产类别代码
    # assetweight=np.array([80,20])     #资产配置权重，单位是%
    # startdate="2022-01-01"            #测试起始时间
    # enddate="2022-01-12"              #测试结束时间
    # allocation_frequency=3            #调仓频率，单位是交易日
    # original_data                     #原始数据，pd.DataFrame
    compose_data = pd.DataFrame()
    for i in range(0, original_data.shape[0], allocation_frequency):
        period_compose_data = pd.DataFrame()
        period_data = original_data.iloc[i:i + allocation_frequency, :]
        period_data_toone = period_data / period_data.iloc[0]
        if i == 0:
            for j in range(0, period_data_toone.shape[1]):
                temp = period_data_toone.iloc[:, j] * assetweight[j]
                period_compose_data = pd.concat([period_compose_data, temp], axis=1)
            period_sum = pd.DataFrame(period_compose_data.sum(axis=1))
            compose_data = pd.concat([compose_data, period_sum], axis=0)
        else:
            adjust_factor = original_data.iloc[i, :] / original_data.iloc[i - 1, :]
            allocation_weight = assetweight * compose_data.iloc[compose_data.shape[0] - 1, 0] / 100 * adjust_factor
            for j in range(0, period_data_toone.shape[1]):
                temp = period_data_toone.iloc[:, j] * allocation_weight[j]
                period_compose_data = pd.concat([period_compose_data, temp], axis=1)
            period_sum = pd.DataFrame(period_compose_data.sum(axis=1))
            compose_data = pd.concat([compose_data, period_sum], axis=0)
    return compose_data


def MaxDrawdown(compose_data_array):
    ##最大回撤，参数为np.array
    maxdrawdown_stoptime = np.argmax((np.maximum.accumulate(compose_data_array)-compose_data_array))
    if maxdrawdown_stoptime==0:
        return 0
    maxdrawdown_starttime = np.argmax(compose_data_array[:maxdrawdown_stoptime])
    drawdown_max = 100*(compose_data_array[maxdrawdown_starttime]-compose_data_array[maxdrawdown_stoptime])/compose_data_array[maxdrawdown_starttime]
    return drawdown_max[0]

def Allocation_Test_Result(compose_data, account_period, flag):
    # 收益率+最大回撤+波动+夏普比+Calmar
    # compose_data：pd.DataFrame
    # account_period=3                  #计算周期，单位是交易日

    # 收益率
    gundong_return = compose_data.pct_change(periods=account_period)
    gundong_return.dropna(axis=0, how='all', inplace=True)
    gundong_return.columns = ['收益率']
    if flag == 1:
        return gundong_return
    else:
        # 最大回撤+波动率+夏普
        gundong_maxdrawdown = []
        gundong_stdev_annual = []
        gundong_stdev_annual_5y = []
        for i in range(0, compose_data.shape[0] - account_period):
            compose_data_period = compose_data.iloc[i:i + account_period, :]
            period_maxdrawdown = MaxDrawdown(compose_data_period.values)
            gundong_maxdrawdown = np.append(gundong_maxdrawdown, period_maxdrawdown)

            compose_data_return = compose_data_period.pct_change()
            compose_data_return.columns = ['return']
            compose_data_return.dropna(axis=0, how='all', inplace=True)
            stdev = compose_data_return['return'].std()
            stdev_annual = stdev * 15.81  # math.sqrt(250)
            stdev_annual_5y = stdev * np.sqrt(account_period)  # math.sqrt(250)
            gundong_stdev_annual = np.append(gundong_stdev_annual, stdev_annual)
            gundong_stdev_annual_5y = np.append(gundong_stdev_annual_5y, stdev_annual_5y)
            # start_riskfree_rate = w.wss("TB10Y.WI", "close","tradeDate="+str(compose_data_period.index[0])+";priceAdj=U;cycle=D", usedf=True)
            # end_riskfree_rate = w.wss("TB10Y.WI", "close","tradeDate="+str(compose_data_period.index[compose_data_period.shape[0]-1])+";priceAdj=U;cycle=D", usedf=True)
            # start_riskfree_rate[1].fillna(4,inplace=True)
            # end_riskfree_rate[1].fillna(4,inplace=True)
            # period_riskfree_rate_avg = (start_riskfree_rate[1].iloc[0,0]+end_riskfree_rate[1].iloc[0,0])/2
            # compose_data_return_avg = compose_data_return.mean(axis=0)
            # period_sharpe_ratio = (compose_data_return_avg[0]-(pow((1+period_riskfree_rate_avg),1/250)-1))/stdev
            # period_sharpe_ratio = ((pow(1+compose_data_return_avg[0],250)-1))/stdev_annual
            # period_sharpe_ratio = ((pow(1+compose_data_return_avg[0],account_period)-1)-0.035*account_period/250)/(stdev*sqrt(account_period))
            # sharpe_ratio = np.append(sharpe_ratio,period_sharpe_ratio)

        gundong_return['年化收益率(%)'] = (pow(1 + gundong_return['收益率'], 250 / account_period) - 1) * 100
        gundong_return['最大回撤(%)'] = gundong_maxdrawdown
        gundong_return['年化波动率(%)'] = gundong_stdev_annual * 100
        gundong_return['区间波动率(%)'] = gundong_stdev_annual_5y * 100
        # 夏普比
        gundong_return['夏普比（年化）'] = gundong_return.apply(
            lambda x: (100 * x['收益率'] - 4.7 * account_period / 250) / x['区间波动率(%)'], axis=1)
        # 原参数是4.7，改为1.5
        # Calmar
        gundong_return['Calmar'] = gundong_return.apply(lambda x: x['年化收益率(%)'] / x['最大回撤(%)'], axis=1)
        gundong_return.drop(columns=['收益率'], inplace=True)
    return gundong_return


def Allocation_Test_Result_probfacevalue(compose_data, account_period, min_facevalue):
    # 持有期间，面值低于给定金额min_facevalue的概率
    df_min_facevalue = pd.DataFrame()
    for i in range(0, compose_data.shape[0] - account_period):
        # i = 0
        compose_data_period = compose_data.iloc[i:i + account_period, :] / compose_data.iloc[i]
        df_min_facevalue.loc[compose_data_period.index[-1], '最低净值'] = compose_data_period.min()[0]
    probfacevalue = len(df_min_facevalue[df_min_facevalue['最低净值'] < min_facevalue]) / len(df_min_facevalue)
    return probfacevalue


def index_close_api_withname(div_index_list,startDate,endDate):
    df_index_all = w.wsd(div_index_list, "close", startDate,endDate, "",usedf = True)[1]
    data=w.wss(div_index_list, "sec_name")
    data_dict = dict(zip(data.Codes,data.Data[0]))
    df_index_all.columns = [data_dict[x] for x in df_index_all.columns]
    df_index_all.index = [pd.to_datetime(x) for x in df_index_all.index]
    return df_index_all

#%%提取数据,计算,保存
'''
assetcode = ['885001.WI','H20269.CSI','SP500TR.SPI','GC.CMX','885008.WI','930995.CSI']
#默认不修改参数，先提取全量数据
startDate = '********'
endDate = dt.datetime.today().strftime('%Y%m%d')
original_data = index_close_api_withname(assetcode,startDate,endDate)
allocation_frequency = 3*21  #调仓频率，单位是交易日，半年调仓

#可修改参数
account_period_m = 12 #6个月持有期
account_period = account_period_m*21
t_start = '********'
t_end = '********'
#设置权益结构，合计权重100，权重顺序保持一致
#以偏股混合型基金指数*30%+红利低波*30%+标普500全收益*30%+黄金*10%与长期纯债基金指数代表风险资产与稳健资产，模拟不同风险资产比例下组合的中长期风险收益特征，数据区间为2013.01-2024.12
input_asset = ['万得偏股混合型基金指数', '红利低波(全)', '标普500全收益指数', 'COMEX黄金']
input_weight = [30,30,30,10]

#每个权益敞口，滚动计算风险收益表现和达成概率
rtn_result = pd.DataFrame(index=['10%','25%','50%','75%','90%','P_over0','P_over35','P_over4','P_over45','P_over5','P_over55','P_over6'])
mdd_result = pd.DataFrame(index=['10%','25%','50%','75%','90%','P_below15','P_below18','P_below2','P_below22','P_below25','P_below3','P_below35','P_below4'])
facevalue_result = pd.DataFrame(index=['P_below15','P_below18','P_below2','P_below22','P_below25','P_below3','P_below35','P_below4'])
vol_result = pd.DataFrame(index=['10%','25%','50%','75%','90%'])
sharpe_result = pd.DataFrame(index=['10%','25%','50%','75%','90%'])
calmar_result = pd.DataFrame(index=['10%','25%','50%','75%','90%'])
for x in range(0,905,5): #[0%,40%],每0.5%权益仓位计算，比较耗时
    stock_weight = x/10
    print(stock_weight)
    #stock_weight = 20
    original_data_x = original_data[input_asset+['万得中长期纯债型指数']].loc[pd.Timestamp(t_start):pd.Timestamp(t_end)]
    weight_x = [x*stock_weight/100 for x in input_weight]+[100-stock_weight]
    compose_data = Compose_Data(original_data_x,np.array(weight_x),allocation_frequency)
    test_result = Allocation_Test_Result(compose_data,account_period,0)
    rtn_p0 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 0)/100 #给定序列，小于该值的概率
    rtn_p1 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 3.5)/100 #给定序列，小于该值的概率
    rtn_p2 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 4)/100 #给定序列，小于该值的概率
    rtn_p3 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 4.5)/100 #给定序列，小于该值的概率
    rtn_p4 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 5)/100 #给定序列，小于该值的概率
    rtn_p5 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 5.5)/100 #给定序列，小于该值的概率
    rtn_p6 = 1-stats.percentileofscore(test_result['年化收益率(%)'], 6)/100 #给定序列，小于该值的概率

    mdd_p1 = stats.percentileofscore(test_result['最大回撤(%)'], 1.5)/100 #给定序列，小于该值的概率
    mdd_p2 = stats.percentileofscore(test_result['最大回撤(%)'], 1.8)/100 #给定序列，小于该值的概率
    mdd_p3 = stats.percentileofscore(test_result['最大回撤(%)'], 2)/100 #给定序列，小于该值的概率
    mdd_p4 = stats.percentileofscore(test_result['最大回撤(%)'], 2.2)/100 #给定序列，小于该值的概率
    mdd_p5 = stats.percentileofscore(test_result['最大回撤(%)'], 2.5)/100 #给定序列，小于该值的概率
    mdd_p6 = stats.percentileofscore(test_result['最大回撤(%)'], 3)/100 #给定序列，小于该值的概率
    mdd_p7 = stats.percentileofscore(test_result['最大回撤(%)'], 3.5)/100 #给定序列，小于该值的概率
    mdd_p8 = stats.percentileofscore(test_result['最大回撤(%)'], 4)/100 #给定序列，小于该值的概率

    facevalue_p1 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-1.5)/100)
    facevalue_p2 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-1.8)/100)
    facevalue_p3 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-2)/100)
    facevalue_p4 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-2.2)/100)
    facevalue_p5 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-2.5)/100)
    facevalue_p6 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-3)/100)
    facevalue_p7 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-3.5)/100)
    facevalue_p8 = 1- Allocation_Test_Result_probfacevalue(compose_data, account_period,(100-4)/100)

    rtn_result[stock_weight] = np.array([np.percentile(test_result['年化收益率(%)'], 10),np.percentile(test_result['年化收益率(%)'], 25),np.percentile(test_result['年化收益率(%)'], 50),np.percentile(test_result['年化收益率(%)'], 75),np.percentile(test_result['年化收益率(%)'], 90),rtn_p0,rtn_p1,rtn_p2,rtn_p3,rtn_p4,rtn_p5,rtn_p6])
    mdd_result[stock_weight] = np.array([np.percentile(test_result['最大回撤(%)'], 10),np.percentile(test_result['最大回撤(%)'], 25),np.percentile(test_result['最大回撤(%)'], 50),np.percentile(test_result['最大回撤(%)'], 75),np.percentile(test_result['最大回撤(%)'], 90),mdd_p1,mdd_p2,mdd_p3,mdd_p4,mdd_p5,mdd_p6,mdd_p7,mdd_p8])
    facevalue_result[stock_weight] = np.array([facevalue_p1,facevalue_p2,facevalue_p3,facevalue_p4,facevalue_p5,facevalue_p6,facevalue_p7,facevalue_p8])
    vol_result[stock_weight] = np.array([np.percentile(test_result['年化波动率(%)'], 10),np.percentile(test_result['年化波动率(%)'], 25),np.percentile(test_result['年化波动率(%)'], 50),np.percentile(test_result['年化波动率(%)'], 75),np.percentile(test_result['年化波动率(%)'], 90)])
    sharpe_result[stock_weight] = np.array([np.percentile(test_result['夏普比（年化）'], 10),np.percentile(test_result['夏普比（年化）'], 25),np.percentile(test_result['夏普比（年化）'], 50),np.percentile(test_result['夏普比（年化）'], 75),np.percentile(test_result['夏普比（年化）'], 90)])
    calmar_result[stock_weight] = np.array([np.percentile(test_result['Calmar'], 10),np.percentile(test_result['Calmar'], 25),np.percentile(test_result['Calmar'], 50),np.percentile(test_result['Calmar'], 75),np.percentile(test_result['Calmar'], 90)])

# # 保存为pickle
# df_result = {'rtn_result':rtn_result,'mdd_result':mdd_result,'facevalue_result':facevalue_result,'vol_result':vol_result,'sharpe_result':sharpe_result,'calmar_result':calmar_result}
# with open("C:/Users/<USER>/Desktop/代码/5.大类资产配置回测"+'/df_result_'+str(account_period_m)+'M_'+t_start+'_'+t_end, "wb") as fp:
#     pickle.dump(df_result, fp)
    
#保存为excel
writer = pd.ExcelWriter("C:/Users/<USER>/Desktop/代码/5.大类资产配置回测"+'/df_result_'+str(account_period_m)+'M_'+t_start+'_'+t_end + '.xlsx')
rtn_result.to_excel(writer, "rtn_result")
mdd_result.to_excel(writer, "mdd_result")
facevalue_result.to_excel(writer, "facevalue_result")
vol_result.to_excel(writer, "vol_result")
sharpe_result.to_excel(writer, "sharpe_result")
calmar_result.to_excel(writer, "calmar_result")
writer.save()
'''
#%%画图v2-美化要求
#读取pickle数据
account_period_m = 12 #12 6
t_start = '********'#'********' # '********'
t_end = '********' #
# f1 = open(fpdata +'/df_result_'+str(account_period_m)+'M_'+t_start+'_'+t_end,'rb')
# df_result = pickle.load(f1)
# f1.close()
# rtn_result = df_result['rtn_result']
# mdd_result = df_result['mdd_result']
# facevalue_result = df_result['facevalue_result']
# vol_result = df_result['vol_result']
# sharpe_result = df_result['sharpe_result']
# calmar_result = df_result['calmar_result']

#读取excel数据
rtn_result = pd.read_excel(fpdata+'/df_result_'+str(account_period_m)+'M_'+t_start+'_'+t_end+'.xlsx', engine='openpyxl',sheet_name = 'rtn_result',header=0,index_col=0)
mdd_result = pd.read_excel(fpdata+'/df_result_'+str(account_period_m)+'M_'+t_start+'_'+t_end+'.xlsx', engine='openpyxl',sheet_name = 'mdd_result',header=0,index_col=0)
facevalue_result = pd.read_excel(fpdata+'/df_result_'+str(account_period_m)+'M_'+t_start+'_'+t_end+'.xlsx', engine='openpyxl',sheet_name = 'facevalue_result',header=0,index_col=0)

#画图设置
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
fmt = '{:.1%}' # 设置横轴和纵轴标签显示为百分制，并保留一位小数
t_end_style = datetime.strptime(t_end, '%Y%m%d').strftime('%Y-%m-%d')
xticks_step = 4 #x轴间隔4个（2%）显示坐标轴数字

#图上重点显示的风险资产中枢1参数设置
w_left_mid = 12 #中枢风险资产仓位
w_left_min = 9 #战术偏离最低风险资产仓位
w_left_max = 15 #战术偏离最高风险资产仓位
w_left_xticks_step = 6 #间隔6个（3%）在图上标注数字
p_left_rtn = 4 #收益目标
p_left_rtn_name = 'P_over4'
p_left_mdd = 2 #回撤目标
p_left_mdd_name = 'P_below2'

#图上重点显示的风险资产中枢2参数设置
w_right_mid = 20 #中枢风险资产仓位
w_right_min = 15 #战术偏离最低风险资产仓位
w_right_max = 25 #战术偏离最高风险资产仓位
w_right_xticks_step = 5 #间隔5个（2.5%）在图上标注数字
p_right_rtn = 5 #收益目标
p_right_rtn_name = 'P_over5'
p_right_mdd = 3.5 #回撤目标
p_right_mdd_name = 'P_below35'

fontsize_xylabel = 12
fontsize_annotate = 12
fontsize_legend = 12
fontsize_labelsize = 12
fontsize_subtitle = 16
fontsize_suptitle = 20
#%%画图
plt_result1 = (rtn_result.T).iloc[:62,:] #仅展示权益中枢0-30的区域
plt_result2 = (mdd_result.T).iloc[:62,:]
plt_result3 = (facevalue_result.T).iloc[:62,:]
pltname_all = ['最大回撤','面值回撤']
pltname_yr = '2006年以来' #'2006年以来'/'近十年'

for pltname in pltname_all:
    fig,ax=plt.subplots(nrows=1,ncols=2,sharex=False,figsize=(20,9),dpi=400)
    ####################################【左图：收益率】#################################
    #左轴：分位数
    ax[0].plot(plt_result1.index,plt_result1['50%'],label='50%分位数',color="b",marker='.',)
    ax[0].plot(plt_result1.index,plt_result1['10%'],label='10%分位数',color="b",ls = ':', linewidth=1)
    ax[0].plot(plt_result1.index,plt_result1['90%'],label='90%分位数',color="b",ls = ':', linewidth=1)
    ax[0].plot(plt_result1.index,plt_result1['25%'],label='25%分位数',color="b",ls = '--', linewidth=1)
    ax[0].plot(plt_result1.index,plt_result1['75%'],label='75%分位数',color="b",ls = '--', linewidth=1)
    ax[0].fill_between(x=plt_result1.index,y1=plt_result1['25%'],y2=plt_result1['75%'],facecolor='blue',alpha = 0.1)
    for i in range(0,len(plt_result1.index),xticks_step):
        #print(i)
        k = round(plt_result1['50%'].values[i],1)
        ax[0].annotate(str(k)+'%',xy=(plt_result1.index[i],plt_result1['50%'].values[i]+0.1),xytext=(plt_result1.index[i],plt_result1['50%'].values[i]+0.1),fontsize=fontsize_annotate,color='blue')
    ax[0].set_xticks(plt_result1.index[::xticks_step])
    ax[0].set_xticklabels(plt_result1.index[::xticks_step])
    ax[0].set_xlabel('风险资产仓位（%）', color='black',fontsize=fontsize_xylabel)
    ax[0].set_ylabel('年化收益率(%)', color='blue',fontsize=fontsize_xylabel)
    ax[0].tick_params(axis='y', direction='out', color='blue', labelcolor='blue', labelsize=fontsize_labelsize)
    ax[0].tick_params(axis='x', direction='out', color='black', labelcolor='black', labelsize=fontsize_labelsize)
    ax[0].legend(loc=2,fontsize=fontsize_legend)#.get_frame().set_alpha(0.0)
    ax[0].spines['right'].set_visible(False)
    ax[0].spines['top'].set_visible(False)
    ax[0].spines['left'].set_color('blue')
    #右轴：实现概率
    ax1=ax[0].twinx()
    ax1.plot(plt_result1.index,plt_result1[p_left_rtn_name],label='年化收益高于'+str(p_left_rtn)+'%概率（右轴）',color="orangered",ls = '--', linewidth=1)
    ax1.plot(plt_result1.index,plt_result1[p_right_rtn_name],label='年化收益高于'+str(p_right_rtn)+'%概率（右轴）',color="red",ls = '--', linewidth=1)
    #局部加粗
    plt_result1_left = plt_result1.loc[w_left_min:w_left_max]
    plt_result1_right = plt_result1.loc[w_right_min:w_right_max]
    ax1.plot(plt_result1_left.index, plt_result1_left[p_left_rtn_name], color="orangered", ls='--',linewidth=2)
    ax1.plot(plt_result1_right.index, plt_result1_right[p_right_rtn_name], color="red", ls='--',linewidth=2)
    #添加数值标签
    for i in range(plt_result1.index.get_loc(w_left_min),plt_result1.index.get_loc(w_left_max)+1,w_left_xticks_step):
        k3 = round(plt_result1[p_left_rtn_name].values[i]*100,1)
        ax1.annotate(str(k3)+'%',xy=(plt_result1.index[i],plt_result1[p_left_rtn_name].values[i]+0.01),xytext=(plt_result1.index[i],plt_result1[p_left_rtn_name].values[i]+0.01),fontsize=fontsize_annotate,color='orangered')
    for i in range(plt_result1.index.get_loc(w_right_min), plt_result1.index.get_loc(w_right_max) + 1, w_right_xticks_step):
        k1 = round(plt_result1[p_right_rtn_name].values[i]*100,1)
        ax1.annotate(str(k1)+'%',xy=(plt_result1.index[i],plt_result1[p_right_rtn_name].values[i]+0.01),xytext=(plt_result1.index[i],plt_result1[p_right_rtn_name].values[i]+0.01),fontsize=fontsize_annotate,color='red')
    ax1.axvline(x=w_left_mid, color='orangered', linestyle='-', linewidth=3)
    ax1.axvline(x=w_right_mid, color='red', linestyle='-', linewidth=3)
    #添加阴影区域
    h_min = 0.30#plt_result1[[p_left_rtn_name,p_right_rtn_name]].min().min()
    h_max = 0.67#plt_result1[[p_left_rtn_name,p_right_rtn_name]].max().max()
    ax1.fill_between(plt_result1.loc[w_left_min:w_left_max].index,h_min,h_max, color='orangered', alpha=0.1)
    ax1.fill_between(plt_result1.loc[w_right_min:w_right_max].index,h_min,h_max, color='red', alpha=0.3)
    ax1.set_ylim(h_min,h_max)
    ax1.legend(loc='lower right',fontsize=fontsize_legend)#.get_frame().set_alpha(0.0)
    ax1.tick_params(axis='y', direction='out', color='red', labelcolor='red', labelsize=fontsize_labelsize)
    ax1.set_ylabel('实现概率', color='red',fontsize=fontsize_xylabel)
    ax1.spines['top'].set_visible(False)
    ax1.spines['right'].set_color('red')
    ax1.spines['left'].set_color('blue')
    yticks = ax1.get_yticks()
    ax1.set_yticks(yticks)
    ax1.set_yticklabels([fmt.format(ytick) for ytick in yticks])
    f = '风险资产中枢'+str(w_left_mid)+'%，年化收益高于'+str(p_left_rtn)+'%的实现概率：'+str(round(plt_result1.loc[12.5][p_left_rtn_name]*100,1))+'%\n风险资产中枢'+str(w_right_mid)+'%，年化收益高于'+str(p_right_rtn)+'%的实现概率：'+str(round(plt_result1.loc[20][p_right_rtn_name]*100,1))+'%'
    ax[0].set_title(f, fontsize=fontsize_subtitle, color='gray')
    ####################################【右图：回撤】#################################
    #左轴：分位数
    ax[1].plot(plt_result2.index,plt_result2['50%'],label='50%分位数',color="b",marker='.',)
    ax[1].plot(plt_result2.index,plt_result2['10%'],label='10%分位数',color="b",ls = ':', linewidth=1)
    ax[1].plot(plt_result2.index,plt_result2['90%'],label='90%分位数',color="b",ls = ':', linewidth=1)
    ax[1].plot(plt_result2.index,plt_result2['25%'],label='25%分位数',color="b",ls = '--', linewidth=1)
    ax[1].plot(plt_result2.index,plt_result2['75%'],label='75%分位数',color="b",ls = '--', linewidth=1)
    ax[1].fill_between(x=plt_result2.index,y1=plt_result2['25%'],y2=plt_result2['75%'],facecolor='blue',alpha = 0.1)
    for i in range(0,len(plt_result2.index),xticks_step):
        k = round(plt_result2['50%'].values[i],1)
        ax[1].annotate(str(k)+'%',xy=(plt_result2.index[i],plt_result2['50%'].values[i]+0.1),xytext=(plt_result2.index[i],plt_result2['50%'].values[i]+0.1),fontsize=fontsize_annotate,color='blue')
    ax[1].set_xticks(plt_result2.index[::xticks_step])
    ax[1].set_xticklabels(plt_result2.index[::xticks_step])
    ax[1].set_xlabel('风险资产仓位（%）', color='black',fontsize=fontsize_xylabel)
    ax[1].set_ylabel(pltname+'(%)', color='blue',fontsize=fontsize_xylabel)
    ax[1].tick_params(axis='y', direction='out', color='blue', labelcolor='blue', labelsize=fontsize_labelsize)
    ax[1].tick_params(axis='x', direction='out', color='black', labelcolor='black', labelsize=fontsize_labelsize)
    ax[1].spines['right'].set_visible(False)
    ax[1].spines['top'].set_visible(False)
    ax[1].spines['left'].set_color('blue')
    ax[1].legend(loc=2,fontsize=fontsize_legend)#.get_frame().set_alpha(0.0)
    #右轴：实现概率
    if pltname=='最大回撤':
        ####################################以持有期内最大回撤来看#################################
        ax2=ax[1].twinx()
        ax2.plot(plt_result2.index,plt_result2[p_left_mdd_name],label=pltname+'低于'+str(p_left_mdd)+'%概率（右轴）',color="orangered",ls = '--', linewidth=1)
        ax2.plot(plt_result2.index,plt_result2[p_right_mdd_name],label=pltname+'低于'+str(p_right_mdd)+'%概率（右轴）',color="red",ls = '--', linewidth=1)
        #局部加粗
        plt_result2_left = plt_result2.loc[w_left_min:w_left_max]
        plt_result2_right = plt_result2.loc[w_right_min:w_right_max]
        ax2.plot(plt_result2_left.index, plt_result2_left[p_left_mdd_name], color="orangered", ls='--', linewidth=2)
        ax2.plot(plt_result2_right.index, plt_result2_right[p_right_mdd_name], color="red", ls='--', linewidth=2)
        # 添加数值标签
        for i in range(plt_result2.index.get_loc(w_left_min),plt_result2.index.get_loc(w_left_max)+1,w_left_xticks_step):
            k1 = round(plt_result2[p_left_mdd_name].values[i]*100,1)
            ax2.annotate(str(k1)+'%',xy=(plt_result2.index[i],plt_result2[p_left_mdd_name].values[i]+0.005),xytext=(plt_result2.index[i],plt_result2[p_left_mdd_name].values[i]+0.005),fontsize=fontsize_annotate,color='orangered')

        for i in range(plt_result2.index.get_loc(w_right_min), plt_result2.index.get_loc(w_right_max) + 1, w_right_xticks_step):
            k2 = round(plt_result2[p_right_mdd_name].values[i]*100,1)
            ax2.annotate(str(k2)+'%',xy=(plt_result2.index[i],plt_result2[p_right_mdd_name].values[i]+0.005),xytext=(plt_result2.index[i],plt_result2[p_right_mdd_name].values[i]+0.005),fontsize=fontsize_annotate,color='red')
        ax2.axvline(x=w_left_mid, color='orangered', linestyle='-', linewidth=3)
        ax2.axvline(x=w_right_mid, color='red', linestyle='-', linewidth=3)
        # 添加阴影区域
        h_min = 0.40#plt_result2[[p_left_mdd_name,p_right_mdd_name]].min().min()
        h_max = 1#plt_result2[[p_left_mdd_name,p_right_mdd_name]].max().max()
        ax2.fill_between(plt_result2.loc[w_left_min:w_left_max].index,h_min,h_max, color='orangered', alpha=0.1)
        ax2.fill_between(plt_result2.loc[w_right_min:w_right_max].index,h_min,h_max, color='red', alpha=0.3)
        ax2.set_ylim(h_min,h_max)
        ax2.legend(loc='lower right',fontsize=fontsize_legend)#.get_frame().set_alpha(0.0)
        ax2.tick_params(axis='y', direction='out', color='red', labelcolor='red', labelsize=fontsize_labelsize)
        ax2.set_ylabel('实现概率', color='red',fontsize=fontsize_xylabel)
        ax2.spines['top'].set_visible(False)
        ax2.spines['right'].set_color('red')
        ax2.spines['left'].set_color('blue')
        yticks = ax2.get_yticks()
        ax2.set_yticks(yticks)
        ax2.set_yticklabels([fmt.format(ytick) for ytick in yticks])
        f = '风险资产中枢'+str(w_left_mid)+'%，'+pltname+'低于'+str(p_left_mdd)+'%的实现概率：'+str(round(plt_result2.loc[w_left_mid][p_left_mdd_name]*100,1))+'%\n风险资产中枢'+str(w_right_mid)+'%，'+pltname+'低于'+str(p_right_mdd)+'%的实现概率：'+str(round(plt_result2.loc[w_right_mid][p_right_mdd_name]*100,1))+'%'
        ax[1].set_title(f, fontsize=fontsize_subtitle,color = 'gray')
    elif pltname=='面值回撤':
        ####################################以持有期内面值回撤来看#################################
        ax2 = ax[1].twinx()
        ax2.plot(plt_result3.index, plt_result3[p_left_mdd_name], label=pltname + '低于'+str(p_left_mdd)+'%概率（右轴）', color="orangered", ls='--', linewidth=1)
        ax2.plot(plt_result3.index, plt_result3[p_right_mdd_name], label=pltname + '低于'+str(p_right_mdd)+'%概率（右轴）', color="red",
                 ls='--', linewidth=1)
        # 局部加粗
        plt_result3_left = plt_result3.loc[w_left_min:w_left_max]
        plt_result3_right = plt_result3.loc[w_right_min:w_right_max]
        ax2.plot(plt_result3_left.index, plt_result3_left[p_left_mdd_name], color="orangered", ls='--', linewidth=2)
        ax2.plot(plt_result3_right.index, plt_result3_right[p_right_mdd_name], color="red", ls='--', linewidth=2)
        # 添加数值标签
        for i in range(plt_result3.index.get_loc(w_left_min), plt_result3.index.get_loc(w_left_max) + 1, w_left_xticks_step):
            k1 = round(plt_result3[p_left_mdd_name].values[i] * 100, 1)
            ax2.annotate(str(k1) + '%', xy=(plt_result3.index[i], plt_result3[p_left_mdd_name].values[i]+0.005),
                         xytext=(plt_result3.index[i], plt_result3[p_left_mdd_name].values[i]+0.005), fontsize=fontsize_annotate,color='orangered')
        for i in range(plt_result3.index.get_loc(w_right_min), plt_result3.index.get_loc(w_right_max) + 1, w_right_xticks_step):
            #print(plt_result3.index[i])
            k2 = round(plt_result3[p_right_mdd_name].values[i] * 100, 1)
            ax2.annotate(str(k2) + '%', xy=(plt_result3.index[i], plt_result3[p_right_mdd_name].values[i]+0.005),
                         xytext=(plt_result3.index[i], plt_result3[p_right_mdd_name].values[i]+0.005), fontsize=fontsize_annotate,color='red')
        ax2.axvline(x=w_left_mid, color='orangered', linestyle='-', linewidth=3)
        ax2.axvline(x=w_right_mid, color='red', linestyle='-', linewidth=3)
        # 添加阴影区域
        h_min = 0.80  # plt_result3[[p_left_mdd_name,p_right_mdd_name]].min().min()
        h_max = 1  # plt_result3[[p_left_mdd_name,p_right_mdd_name]].max().max()
        ax2.fill_between(plt_result3.loc[w_left_min:w_left_max].index, h_min, h_max, color='orangered', alpha=0.1)
        ax2.fill_between(plt_result3.loc[w_right_min:w_right_max].index, h_min, h_max, color='red', alpha=0.3)
        ax2.set_ylim(h_min, h_max)
        ax2.legend(loc='lower right',fontsize=fontsize_legend)#.get_frame().set_alpha(0.0)
        ax2.tick_params(axis='y', direction='out', color='red', labelcolor='red', labelsize=fontsize_labelsize)
        ax2.set_ylabel('实现概率', color='red',fontsize=fontsize_xylabel)
        ax2.spines['top'].set_visible(False)
        ax2.spines['right'].set_color('red')
        ax2.spines['left'].set_color('blue')
        yticks = ax2.get_yticks()
        ax2.set_yticks(yticks)
        ax2.set_yticklabels([fmt.format(ytick) for ytick in yticks])
        f = '风险资产中枢'+str(w_left_mid)+'%，'+pltname+'低于'+str(p_left_mdd)+'%的实现概率：'+str(round(plt_result3.loc[w_left_mid][p_left_mdd_name]*100,1))+'%\n风险资产中枢'+str(w_right_mid)+'%，'+pltname+'低于'+str(p_right_mdd)+'%的实现概率：'+str(round(plt_result3.loc[w_right_mid][p_right_mdd_name]*100,1))+'%'
        ax[1].set_title(f, fontsize=fontsize_subtitle,color = 'gray')
    else:
        pass
    fig.suptitle('组合风险收益特征及实现概率_'+pltname+'（'+str(account_period_m)+'个月持有期,'+pltname_yr+','+t_end_style+'）',fontsize=fontsize_suptitle,fontweight='bold')
    fig.tight_layout()
    fig.savefig('组合风险收益特征及实现概率_'+pltname+'（'+str(account_period_m)+'个月持有期,'+pltname_yr+','+t_start+'-'+t_end+'）') #, transparent=True
    plt.close()
print('已完成')
