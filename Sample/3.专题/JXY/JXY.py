from Basicfun import *
print(os.path.basename(__file__))

#%%
yearmonth='202312'
qmin=0.00
qmax=1.00
data=pd.read_excel(os.path.join(fp,'Sample','JXY',yearmonth+'_全校分数.xlsx'),sheet_name='全科',index_col=0,header=0).dropna(how='any')
data=data.loc[:,data.columns.str.contains('得分')]
data.columns=[data.columns[i][:-2] if i!=0 else data.columns[i][:2] for i in range(len(data.columns))]
data['理科六科']=data['语文']+data['数学']+data['英语']+data['物理']+data['化学']+data['生物']
data_0=data[['总分','理科六科']]
data_1=data.iloc[:,1:-1]
score=['全科','单科']
data_all=[data_0, data_1]
row_num=[1,3]

t=printtime(t)

#%%
for j in range(len(score)):
    paraname_table_3y=data_all[j].columns
    b=row_num[j]
    c = len(paraname_table_3y) // b
    fig_jxy, ax_jxy = plt.subplots(nrows=b, ncols=c)
    for p in range(b if j==1 else 1):
        for q in range(c):
            data_jxy = data_all[j][paraname_table_3y[p * c + q]].astype('float32')
            density = gaussian_kde(data_jxy)
            xs = np.linspace(data_jxy.quantile(q=qmin), data_jxy.quantile(q=qmax), 200)
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).fill_between(xs, density(xs), label=paraname_table_3y[p * c + q], color='gray',alpha=light)
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).axvline(x=data_jxy.quantile(q=0.10), ls='-', c='green')  # 横线
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).axvline(x=data_jxy.quantile(q=0.25), ls='--', c='green')  # 横线
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).axvline(x=data_jxy.quantile(q=0.5), ls='--', c='orange')  # 横线
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).axvline(x=data_jxy.quantile(q=0.75), ls='--', c='red')  # 横线
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).axvline(x=data_jxy.quantile(q=0.9), ls='-', c='red')  # 横线
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).text(data_jxy.quantile(q=0.1), density(xs).max() * 0.2,"90'," + str('%.0f' % data_jxy.quantile(q=0.10)),fontsize=fontsize_legend - 2,color='green')
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).text(data_jxy.quantile(q=0.25), density(xs).max() * 0.3,"75'," + str('%.0f' % data_jxy.quantile(q=0.25)),fontsize=fontsize_legend - 2,color='green')
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).text(data_jxy.quantile(q=0.5), density(xs).max() * 0.4,"50'," + str('%.0f' % data_jxy.quantile(q=0.5)),fontsize=fontsize_legend - 2,color='orange')
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).text(data_jxy.quantile(q=0.75), density(xs).max() * 0.5,"25'," + str('%.0f' % data_jxy.quantile(q=0.75)),fontsize=fontsize_legend - 2,color='red')
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).text(data_jxy.quantile(q=0.9), density(xs).max() * 0.6,"10'," + str('%.0f' % data_jxy.quantile(q=0.9)),fontsize=fontsize_legend - 2,color='red')
            data_jxy_icbccs = data_jxy[data_jxy.index.str.contains('蒋欣烨')]
            if data_jxy_icbccs.shape[0] > 0:
                for o in range(data_jxy_icbccs.shape[0]):
                    (ax_jxy[p, q] if j==1 else ax_jxy[q]).axvline(x=data_jxy_icbccs[o], lw=1, ls='-', c='blue')  # 横线
                    (ax_jxy[p, q] if j==1 else ax_jxy[q]).text(data_jxy_icbccs[o], density(xs).max() * 0.9 * (1 - 0.1 * o),data_jxy_icbccs.index[o] + ',' + str('%.0f' % data_jxy_icbccs[o]), fontsize=fontsize_legend - 2,color='blue')
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).grid(alpha=light)
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).spines['top'].set_visible(False)
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).spines['right'].set_visible(False)
            (ax_jxy[p, q] if j==1 else ax_jxy[q]).set_title(paraname_table_3y[p * c + q], fontsize=fontsize_subtitle)
    fig_jxy.suptitle(score[j]+'_'+yearmonth,fontsize=fontsize_suptitle,fontweight='bold')
    fig_jxy.tight_layout()
    fig_jxy.savefig(os.path.join(fp,'Sample','JXY',yearmonth+'_'+score[j]+'.png'))
    plt.close()

t=printtime(t)

printtime(1)
