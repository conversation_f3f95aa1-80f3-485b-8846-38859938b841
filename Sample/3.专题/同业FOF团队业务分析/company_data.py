import os

# %%

from c_plot import multi_chart,multi_chart_bar,line_chart,bar_line,line_line,pie_chart,bar_diff,scatter_chart,table_chart,box_scatter
from pyecharts.charts import Page
# import pdfkit
from fund_calDur import fundDuration,fundDuration_weekly
import fund_analysis_exposure
import fund_analysis_income

# %%
#数据导入
from WindPy import w
import numpy as np
import pandas as pd
import datetime as dt
import time
from dateutil.relativedelta import relativedelta
import pickle
from pyecharts.globals import CurrentConfig

CurrentConfig.ONLINE_HOST = "D:/pyecharts-assets/assets/"


w.start()

start_day='20190331'
end_day='20230331'
###如果不是现任，需要处理一下
today = dt.datetime.today()+ relativedelta(days=-1)
today=today.strftime('%Y%m%d')


date= w.tdays(start_day, end_day, "Days=Alldays;Period=Q").Data[0]
date = [i.strftime('%Y%m%d') for i in date]

company_list=['兴证全球基金','交银施罗德基金','南方基金','易方达基金','汇添富基金','广发基金','华夏基金','民生加银基金','中欧基金','平安基金','工银瑞信基金']
with open(r'基金池\FOF数据.pkl', "rb") as fp:
    fund_all = pickle.load(fp)

fund_company=pd.DataFrame()
fund_info=pd.DataFrame()
fund_fund=pd.DataFrame()

for date_i in date:
    fund = fund_all[date_i]
    fund_company1=fund['基金公司明细']
    fund_company=pd.concat([fund_company,fund_company1],axis=0)
    fund_info1 = fund['基金信息']
    fund_info = pd.concat([fund_info, fund_info1], axis=0)
    fund_fund1 =fund['基金持基明细']
    fund_fund = pd.concat([fund_fund, fund_fund1], axis=0)




fund_company=fund_company.reset_index().set_index('日期')
fund_info=fund_info.reset_index().set_index('日期')

# fund_company = fund_company[fund_company.index.str.contains('0630|1231')]
fund_company = fund_company[fund_company.index.str.contains('0630|1231')]
fund_info = fund_info[fund_info.index.str.contains('0630|1231')]
fund_info=fund_info.fillna(0)
half_year=fund_company.index.unique().tolist()
company_name=fund_company['基金管理人'].unique().tolist()

#持基数量
fund_num=pd.DataFrame(index=company_name)
temp=fund_company[['基金管理人','FOF持基数量']]

for i in half_year:
    df1=temp.loc[i].reset_index().set_index('基金管理人').rename(columns={'FOF持基数量':i}).drop('日期', axis=1)
    fund_num=fund_num.join(df1)
fund_num=fund_num.loc[company_list].fillna(0)

fund_num['持基数量 50"']=fund_num.median(axis=1)

fund_num.loc['25"'] = fund_num[fund_num.columns].quantile(0.75)
fund_num.loc['50"'] = fund_num[fund_num.columns].quantile(0.5)
fund_num.loc['75"'] = fund_num[fund_num.columns].quantile(0.25)


# #投内部基金60%以上的基金数量
# fund_self_num=pd.DataFrame(index=company_name)
# temp=fund_company[['基金管理人','内部数量','内部占比']]
#
# for i in half_year:
#     df2=temp.loc[i].reset_index().set_index('基金管理人').rename(columns={'内部数量':i+'内部数量','内部占比':i+'内部占比'}).drop('日期', axis=1)
#     fund_self_num=fund_self_num.join(df2)
# fund_self_num=fund_self_num.loc[company_list].fillna(0)
#
# fund_self_num['内部数量 50"']=fund_self_num[list(filter(lambda x: '内部数量' in x ,fund_self_num.columns.to_list()))].median(axis=1)
# fund_self_num['内部占比 50"']=fund_self_num[list(filter(lambda x: '内部占比' in x ,fund_self_num.columns.to_list()))].median(axis=1)
#
# fund_self_num.loc['25"'] = fund_self_num[fund_self_num.columns].quantile(0.75)
# fund_self_num.loc['50"'] = fund_self_num[fund_self_num.columns].quantile(0.5)
# fund_self_num.loc['75"'] = fund_self_num[fund_self_num.columns].quantile(0.25)

#内部基金占比、区分权益和非权益 相对值
fund_self_num=pd.DataFrame(index=company_name)

fund_info['非股基内部持基规模占比']=fund_info['内部持基规模占比']-fund_info['股票基金内部持基规模占比']
fund_info['股基内部持基规模占比']=(fund_info['股票基金内部持基规模占比']*fund_info['持基占比']).div(fund_info['基金类型_股票基金占比'],axis='index')
fund_info['非股基内部持基规模占比']=(fund_info['非股基内部持基规模占比']*fund_info['持基占比']).div((fund_info['基金类型_纯债基金占比']+fund_info['基金类型_固收+基金占比']+fund_info['基金类型_其他基金占比']),axis='index')
temp=pd.DataFrame(fund_info.groupby(['日期','基金管理人'])['股基内部持基规模占比','非股基内部持基规模占比'].mean().fillna(0)).reset_index().set_index('日期')
for i in half_year:
    df2=temp.loc[i].set_index('基金管理人').rename(columns={'股基内部持基规模占比':i+'权益基金','非股基内部持基规模占比':i+'非权益基金'})
    fund_self_num=fund_self_num.join(df2)
fund_self_num=fund_self_num.loc[company_list].fillna(0)


fund_self_num['权益基金 50"']=fund_self_num[list(filter(lambda x: '权益基金' in x ,fund_self_num.columns.to_list()))].median(axis=1)
fund_self_num['非权益基金 50"']=fund_self_num[list(filter(lambda x: '非权益基金' in x ,fund_self_num.columns.to_list()))].median(axis=1)

fund_self_num.loc['25"'] = fund_self_num[fund_self_num.columns].quantile(0.75)
fund_self_num.loc['50"'] = fund_self_num[fund_self_num.columns].quantile(0.5)
fund_self_num.loc['75"'] = fund_self_num[fund_self_num.columns].quantile(0.25)
fund_info.to_excel('基金分析结果导出/fund_info.xlsx')
fund_fund.to_excel('基金分析结果导出/fund_fund.xlsx')

# #内部基金占比、区分权益和非权益 绝对值
# fund_self_num=pd.DataFrame(index=company_name)
# temp=pd.DataFrame(fund_info.groupby(['日期','基金管理人'])['股票基金内部持基规模占比','内部持基规模占比'].mean().fillna(0)).reset_index().set_index('日期')
# temp['非股基内部持基规模占比']=temp['内部持基规模占比']-temp['股票基金内部持基规模占比']
# for i in half_year:
#     df2=temp.loc[i].set_index('基金管理人').rename(columns={'股票基金内部持基规模占比':i+'权益基金','非股基内部持基规模占比':i+'非权益基金'}).drop('内部持基规模占比', axis=1)
#     fund_self_num=fund_self_num.join(df2)
# fund_self_num=fund_self_num.loc[company_list].fillna(0)
#
#
# fund_self_num['权益基金 50"']=fund_self_num[list(filter(lambda x: '权益基金' in x ,fund_self_num.columns.to_list()))].median(axis=1)
# fund_self_num['非权益基金 50"']=fund_self_num[list(filter(lambda x: '非权益基金' in x ,fund_self_num.columns.to_list()))].median(axis=1)
#
# fund_self_num.loc['25"'] = fund_self_num[fund_self_num.columns].quantile(0.75)
# fund_self_num.loc['50"'] = fund_self_num[fund_self_num.columns].quantile(0.5)
# fund_self_num.loc['75"'] = fund_self_num[fund_self_num.columns].quantile(0.25)


#基金股票占比

fund_asset=pd.DataFrame(index=company_name)
temp=fund_company[['基金管理人','FOF基金占比','FOF股票占比']]

for i in half_year:
    df3=temp.loc[i].reset_index().set_index('基金管理人').rename(columns={'FOF基金占比':i+'FOF基金占比','FOF股票占比':i+'FOF股票占比'}).drop('日期', axis=1)
    fund_asset=fund_asset.join(df3)

fund_asset=fund_asset.loc[company_list].fillna(0)

fund_asset['FOF基金占比 50"']=fund_asset[list(filter(lambda x: 'FOF基金占比' in x ,fund_asset.columns.to_list()))].median(axis=1)
fund_asset['FOF股票占比 50"']=fund_asset[list(filter(lambda x: 'FOF股票占比' in x ,fund_asset.columns.to_list()))].median(axis=1)

fund_asset.loc['25"'] = fund_asset[fund_asset.columns].quantile(0.75)
fund_asset.loc['50"'] = fund_asset[fund_asset.columns].quantile(0.5)
fund_asset.loc['75"'] = fund_asset[fund_asset.columns].quantile(0.25)

#主动、被动股基占比
temp = fund_info[~fund_info['证券全称'].isin(['汇添富积极投资指数优选一年定期开放股票型基金中基金(FOF-LOF)', '民生加银优享进取一年封闭运作股票型基金中基金(FOF-LOF)', '工银瑞信睿智进取股票型基金中基金(FOF-LOF)','华夏行业配置股票型基金中基金(FOF-LOF)','富国智鑫行业精选股票型基金中基金(FOF-LOF)','华夏优选配置股票型基金中基金(FOF-LOF)'])]
temp = temp.groupby(['日期', '基金管理人'])['股基类型_主动股基占比','股基类型_被动股基占比'].mean().fillna(0)

fund_active=pd.DataFrame(index=company_name)
temp['股基占比']=temp['股基类型_主动股基占比']+temp['股基类型_被动股基占比']
temp.loc[temp['股基占比'] != 0, '股基类型_被动股基占比'] = temp[temp['股基占比'] != 0]['股基类型_被动股基占比'] / temp[temp['股基占比'] != 0]['股基占比']*100
temp.loc[temp['股基占比'] != 0, '股基类型_主动股基占比'] = temp[temp['股基占比'] != 0]['股基类型_主动股基占比'] / temp[temp['股基占比'] != 0]['股基占比']*100

# temp[temp['股基占比']!=0]['FOF主动股基占比']=temp[temp['股基占比']!=0]['FOF主动股基占比']/temp[temp['股基占比']!=0]['股基占比']*100
# temp[temp['股基占比']!=0]['FOF被动股基占比']=[temp['股基占比']!=0]['FOF被动股基占比']/temp[temp['股基占比']!=0]['股基占比']*100

for i in half_year:
    df4=temp.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'股基类型_主动股基占比':i+'FOF主动股基占比','股基类型_被动股基占比':i+'FOF被动股基占比'}).drop(['股基占比'], axis=1)
    fund_active=fund_active.join(df4)
fund_active=fund_active.loc[company_list].loc[company_list].fillna(0)

fund_active['FOF主动股基占比 50"']=fund_active[list(filter(lambda x: 'FOF主动股基占比' in x ,fund_active.columns.to_list()))].median(axis=1)
fund_active['FOF被动股基占比 50"']=fund_active[list(filter(lambda x: 'FOF被动股基占比' in x ,fund_active.columns.to_list()))].median(axis=1)

fund_active.loc['25"'] = fund_active[fund_active.columns].quantile(0.75)
fund_active.loc['50"'] = fund_active[fund_active.columns].quantile(0.5)
fund_active.loc['75"'] = fund_active[fund_active.columns].quantile(0.25)

# 全市场、行业
fund_stocklabel=pd.DataFrame(index=company_name)
temp=fund_company[['基金标签_全市场占比','基金标签_成长占比','基金标签_金融占比','基金标签_消费占比','基金标签_医药占比','基金标签_周期上游占比','基金管理人']].reset_index().set_index(['日期','基金管理人'])
temp['基金比例合计']=temp.fillna(0).sum(axis=1)
# temp['基金标签_行业基金占比']=temp[['基金标签_成长占比','基金标签_金融占比','基金标签_消费占比','基金标签_医药占比','基金标签_周期上游占比']].fillna(0).sum(axis=1)

temp[list(filter(lambda x: '基金标签' in x ,temp.columns.to_list()))]=temp[list(filter(lambda x: '基金标签' in x ,temp.columns.to_list()))].div(temp['基金比例合计'],axis='index')
temp[list(filter(lambda x: '基金标签' in x ,temp.columns.to_list()))].rename(lambda x:x[5:-2],inplace=True)
temp=temp.drop('基金比例合计', axis=1)


for i in half_year:
    df5 = temp.loc[i].reset_index().set_index('基金管理人').rename(columns=lambda x: f"{i} {x}" if '占比' in x else x)
    fund_stocklabel=fund_stocklabel.join(df5)

fund_stocklabel=fund_stocklabel.loc[company_list].loc[company_list].fillna(0)

fund_stocklabel['全市场占比 50"']=fund_stocklabel[list(filter(lambda x: '全市场占比' in x ,fund_stocklabel.columns.to_list()))].median(axis=1)
fund_stocklabel['成长占比 50"']=fund_stocklabel[list(filter(lambda x: '成长占比' in x ,fund_stocklabel.columns.to_list()))].median(axis=1)
fund_stocklabel['金融占比 50"']=fund_stocklabel[list(filter(lambda x: '金融占比' in x ,fund_stocklabel.columns.to_list()))].median(axis=1)
fund_stocklabel['消费占比 50"']=fund_stocklabel[list(filter(lambda x: '消费占比' in x ,fund_stocklabel.columns.to_list()))].median(axis=1)
fund_stocklabel['医药占比 50"']=fund_stocklabel[list(filter(lambda x: '医药占比' in x ,fund_stocklabel.columns.to_list()))].median(axis=1)
fund_stocklabel['周期上游占比 50"']=fund_stocklabel[list(filter(lambda x: '周期上游占比' in x ,fund_stocklabel.columns.to_list()))].median(axis=1)



fund_stocklabel.loc['25"'] = fund_stocklabel[fund_stocklabel.columns].quantile(0.75)
fund_stocklabel.loc['50"'] = fund_stocklabel[fund_stocklabel.columns].quantile(0.5)
fund_stocklabel.loc['75"'] = fund_stocklabel[fund_stocklabel.columns].quantile(0.25)

# 债券基金的配置

fund_bondlabel=pd.DataFrame(index=company_name)
temp=fund_company[['基金标签_高含权债基占比','基金标签_低含权债基占比','基金标签_长期纯债占比','基金标签_中短债及货基占比','基金标签_债券指数占比','基金管理人']].reset_index().set_index(['日期','基金管理人'])
temp['基金比例合计']=temp.fillna(0).sum(axis=1)
temp[list(filter(lambda x: '基金标签' in x ,temp.columns.to_list()))]=temp[list(filter(lambda x: '基金标签' in x ,temp.columns.to_list()))].div(temp['基金比例合计'],axis='index')
temp[list(filter(lambda x: '基金标签' in x ,temp.columns.to_list()))].rename(lambda x:x[5:-2],inplace=True)
temp=temp.drop('基金比例合计', axis=1)


for i in half_year:
    df6 = temp.loc[i].reset_index().set_index('基金管理人').rename(columns=lambda x: f"{i} {x}" if '占比' in x else x)
    fund_bondlabel=fund_bondlabel.join(df6)

fund_bondlabel=fund_bondlabel.loc[company_list].fillna(0)

fund_bondlabel['高含权债基占比 50"']=fund_bondlabel[list(filter(lambda x: '高含权债基占比' in x ,fund_bondlabel.columns.to_list()))].median(axis=1)
fund_bondlabel['低含权债基占比 50"']=fund_bondlabel[list(filter(lambda x: '低含权债基占比' in x ,fund_bondlabel.columns.to_list()))].median(axis=1)
fund_bondlabel['长期纯债占比 50"']=fund_bondlabel[list(filter(lambda x: '长期纯债占比' in x ,fund_bondlabel.columns.to_list()))].median(axis=1)
fund_bondlabel['中短债及货基占比 50"']=fund_bondlabel[list(filter(lambda x: '中短债及货基占比' in x ,fund_bondlabel.columns.to_list()))].median(axis=1)
fund_bondlabel['债券指数占比 50"']=fund_bondlabel[list(filter(lambda x: '债券指数占比' in x ,fund_bondlabel.columns.to_list()))].median(axis=1)



fund_bondlabel.loc['25"'] = fund_bondlabel[fund_bondlabel.columns].quantile(0.75)
fund_bondlabel.loc['50"'] = fund_bondlabel[fund_bondlabel.columns].quantile(0.5)
fund_bondlabel.loc['75"'] = fund_bondlabel[fund_bondlabel.columns].quantile(0.25)

# 板块配置

fund_group=pd.DataFrame(index=company_name)
temp=fund_company[['板块_成长占比','板块_金融占比','板块_消费占比','板块_医药占比','板块_上游占比','板块_中游占比','板块_稳定占比','板块_未披露占比','基金管理人']].reset_index().set_index(['日期','基金管理人'])
temp['基金比例合计']=temp.fillna(0).sum(axis=1)
temp['板块_其他占比']=temp[['板块_上游占比','板块_中游占比','板块_稳定占比','板块_未披露占比']].fillna(0).sum(axis=1)
temp[list(filter(lambda x: '板块' in x ,temp.columns.to_list()))]=temp[list(filter(lambda x: '板块' in x ,temp.columns.to_list()))].div(temp['基金比例合计'],axis='index')
temp=temp.drop(['板块_上游占比','板块_中游占比','板块_稳定占比','板块_未披露占比','基金比例合计'], axis=1)
temp[list(filter(lambda x: '板块' in x ,temp.columns.to_list()))].rename(lambda x:x[5:-2],inplace=True)



for i in half_year:
    df7 = temp.loc[i].reset_index().set_index('基金管理人').rename(columns=lambda x: f"{i} {x}" if '占比' in x else x)
    fund_group=fund_group.join(df7)

fund_group=fund_group.loc[company_list].fillna(0)

fund_group['成长占比 50"']=fund_group[list(filter(lambda x: '成长占比' in x ,fund_group.columns.to_list()))].median(axis=1)
fund_group['金融占比 50"']=fund_group[list(filter(lambda x: '金融占比' in x ,fund_group.columns.to_list()))].median(axis=1)
fund_group['消费占比 50"']=fund_group[list(filter(lambda x: '消费占比' in x ,fund_group.columns.to_list()))].median(axis=1)
fund_group['医药占比 50"']=fund_group[list(filter(lambda x: '医药占比' in x ,fund_group.columns.to_list()))].median(axis=1)
fund_group['其他占比 50"']=fund_group[list(filter(lambda x: '其他占比' in x ,fund_group.columns.to_list()))].median(axis=1)



fund_group.loc['25"'] = fund_group[fund_group.columns].quantile(0.75)
fund_group.loc['50"'] = fund_group[fund_group.columns].quantile(0.5)
fund_group.loc['75"'] = fund_group[fund_group.columns].quantile(0.25)

# 风格指数

fund_style=pd.DataFrame(index=company_name)
temp=fund_company[list(filter(lambda x: '风格指数' in x ,fund_company.columns.to_list()))+['基金管理人']].reset_index().set_index(['日期','基金管理人'])
temp[list(filter(lambda x: '风格指数' in x ,temp.columns.to_list()))].rename(lambda x:x[5:-2],inplace=True)

for i in half_year:
    df8 = temp.loc[i].reset_index().set_index('基金管理人').rename(columns=lambda x: f"{i} {x}" if '占比' in x else x)
    fund_style=fund_style.join(df8)

fund_style=fund_style.loc[company_list].fillna(0)

fund_style['沪深300占比 50"']=fund_style[list(filter(lambda x: '沪深300占比' in x ,fund_style.columns.to_list()))].median(axis=1)
fund_style['中证500占比 50"']=fund_style[list(filter(lambda x: '中证500占比' in x ,fund_style.columns.to_list()))].median(axis=1)
fund_style['中证1000占比 50"']=fund_style[list(filter(lambda x: '中证1000占比' in x ,fund_style.columns.to_list()))].median(axis=1)
fund_style['其他占比 50"']=fund_style[list(filter(lambda x: '其他占比' in x ,fund_style.columns.to_list()))].median(axis=1)



fund_style.loc['25"'] = fund_style[fund_style.columns].quantile(0.75)
fund_style.loc['50"'] = fund_style[fund_style.columns].quantile(0.5)
fund_style.loc['75"'] = fund_style[fund_style.columns].quantile(0.25)

# 计算穿透后的权益中枢
fund_stock=pd.DataFrame()
fund_risklabel=pd.read_excel('基金池\风险标签.xlsx',sheet_name='Sheet2').reset_index()
fund_info=fund_info.reset_index()
fund_info=pd.merge(fund_info,fund_risklabel,on='证券全称',how='left')

temp=pd.DataFrame(fund_info.groupby(['日期','基金管理人','风险'])['合计股票占比','合计转债占比'].mean().fillna(0)).reset_index().set_index('日期')

for i in half_year:
    df9=temp.loc[i].reset_index().set_index(['基金管理人','风险']).rename(columns={'合计股票占比':i+'穿透股票占比','合计转债占比':i+'穿透转债占比'}).drop('日期', axis=1)
    fund_stock=pd.concat([fund_stock,df9], axis=1)

# 计算FOF业绩
today = dt.datetime.strptime('20230618','%Y%m%d')
today_str = today.strftime('%Y-%m-%d')

fof_list = w.wset("sectorconstituent","date=" + today_str + ";sectorid=1000041489000000")
fof_fund = pd.DataFrame({'代码':fof_list.Data[1],'简称':fof_list.Data[2],'全称':w.wss(fof_list.Data[1], "fund_fullname").Data[0],'基金管理人':w.wss(fof_list.Data[1], "fund_mgrcomp").Data[0],
                         '是否初始基金':w.wss(fof_list.Data[1], "fund_initial").Data[0],'成立日':w.wss(fof_list.Data[1],"fund_setupdate").Data[0]}).set_index('代码')
fof_fund = fof_fund[(fof_fund['是否初始基金'] == '是')]

def get_fof_type(x):
    if "养老目标日期" in x:
        return "养老目标日期"
    elif "养老目标" in x:
        return "养老目标风险"
    else:
        return "普通"

fof_fund['类型']=fof_fund['全称'].apply(get_fof_type)
# fof_fund.to_excel('D:/FOF工作/基金专题/20230601FOF业务分析/程序/基金池/fof_fund.xlsx')
fund_risklabel=pd.read_excel('基金池\风险标签.xlsx',sheet_name='Sheet1').set_index('代码')
fof_fund['基金管理人']=fof_fund['基金管理人'].str.split('管理').str[0]
fof_fund=fof_fund.join(fund_risklabel).reset_index().set_index('基金管理人')
fof_fund=fof_fund.loc[company_list].reset_index().set_index('代码')
fof_fund2019=fof_fund.loc[fof_fund['成立日']<pd.Timestamp('2019')]
fof_fund2020=fof_fund.loc[fof_fund['成立日']<pd.Timestamp('2020')]
fof_fund2021=fof_fund.loc[fof_fund['成立日']<pd.Timestamp('2021')]
fof_fund2022=fof_fund.loc[fof_fund['成立日']<pd.Timestamp('2022')]
fof_fund2023=fof_fund.loc[fof_fund['成立日']<pd.Timestamp('2023')]
fof_fund2019['收益']=w.wss(fof_fund2019.index.tolist(), "NAV_adj_return","startDate=20190101;endDate=20191231").Data[0]
fof_fund2020['收益']=w.wss(fof_fund2020.index.tolist(), "NAV_adj_return","startDate=20200101;endDate=20201231").Data[0]
fof_fund2021['收益']=w.wss(fof_fund2021.index.tolist(), "NAV_adj_return","startDate=20210101;endDate=20211231").Data[0]
fof_fund2022['收益']=w.wss(fof_fund2022.index.tolist(), "NAV_adj_return","startDate=20220101;endDate=20221231").Data[0]
fof_fund2023['收益']=w.wss(fof_fund2023.index.tolist(), "NAV_adj_return","startDate=20230101;endDate=20230618").Data[0]
fof_fund2019_date=pd.DataFrame(fof_fund2019[fof_fund2019['类型']=='养老目标日期'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2019_risk=pd.DataFrame(fof_fund2019[fof_fund2019['类型']=='养老目标风险'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2019_normal=pd.DataFrame(fof_fund2019[fof_fund2019['类型']=='普通'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())

fof_fund2020_date=pd.DataFrame(fof_fund2020[fof_fund2020['类型']=='养老目标日期'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2020_risk=pd.DataFrame(fof_fund2020[fof_fund2020['类型']=='养老目标风险'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2020_normal=pd.DataFrame(fof_fund2020[fof_fund2020['类型']=='普通'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())

fof_fund2021_date=pd.DataFrame(fof_fund2021[fof_fund2021['类型']=='养老目标日期'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2021_risk=pd.DataFrame(fof_fund2021[fof_fund2021['类型']=='养老目标风险'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2021_normal=pd.DataFrame(fof_fund2021[fof_fund2021['类型']=='普通'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())

fof_fund2022_date=pd.DataFrame(fof_fund2022[fof_fund2022['类型']=='养老目标日期'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2022_risk=pd.DataFrame(fof_fund2022[fof_fund2022['类型']=='养老目标风险'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2022_normal=pd.DataFrame(fof_fund2022[fof_fund2022['类型']=='普通'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())

fof_fund2023_date=pd.DataFrame(fof_fund2023[fof_fund2023['类型']=='养老目标日期'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2023_risk=pd.DataFrame(fof_fund2023[fof_fund2023['类型']=='养老目标风险'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())
fof_fund2023_normal=pd.DataFrame(fof_fund2023[fof_fund2023['类型']=='普通'].reset_index().groupby(['风险','基金管理人'])['收益'].mean())

fund_return_date=pd.DataFrame(index=company_list)
fund_return_risk=pd.DataFrame(index=company_list)
fund_return_normal=pd.DataFrame(index=company_list)

risk_list=['低','中','高']

for i in risk_list:
    try:
        temp = fof_fund2019_date.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2019'+i})
        fund_return_date=fund_return_date.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2020_date.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2020'+i})
        fund_return_date=fund_return_date.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2021_date.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2021'+i})
        fund_return_date=fund_return_date.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2022_date.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2022'+i})
        fund_return_date=fund_return_date.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2023_date.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2023'+i})
        fund_return_date=fund_return_date.join(temp)
    except:
        print(i)

for i in risk_list:
    try:
        temp = fof_fund2019_risk.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2019'+i})
        fund_return_risk=fund_return_risk.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2020_risk.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2020'+i})
        fund_return_risk=fund_return_risk.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2021_risk.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2021'+i})
        fund_return_risk=fund_return_risk.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2022_risk.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2022'+i})
        fund_return_risk=fund_return_risk.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2023_risk.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2023'+i})
        fund_return_risk=fund_return_risk.join(temp)
    except:
        print(i)

for i in risk_list:
    try:
        temp = fof_fund2019_normal.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2019'+i})
        fund_return_normal=fund_return_normal.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2020_normal.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2020'+i})
        fund_return_normal=fund_return_normal.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2021_normal.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2021'+i})
        fund_return_normal=fund_return_normal.join(temp)
    except:
        print(i)
for i in risk_list:
    try:
        temp = fof_fund2022_normal.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2022'+i})
        fund_return_normal=fund_return_normal.join(temp)
    except:
        print(i)
for i in risk_list:
    # print(fof_fund2023_normal.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2023'+i}))
    try:
        temp = fof_fund2023_normal.loc[(i),:].reset_index().set_index('基金管理人').rename(columns={'收益':'2023'+i})
        fund_return_normal=fund_return_normal.join(temp)
    except:
        print(i)

fund_return_date['50"']=fund_return_date.median(axis=1)
fund_return_risk['50"']=fund_return_risk.median(axis=1)
fund_return_normal['50"']=fund_return_normal.median(axis=1)


fund_return_date.loc['25"'] = fund_return_date[fund_return_date.columns].quantile(0.75)
fund_return_date.loc['50"'] = fund_return_date[fund_return_date.columns].quantile(0.5)
fund_return_date.loc['75"'] = fund_return_date[fund_return_date.columns].quantile(0.25)

fund_return_risk.loc['25"'] = fund_return_risk[fund_return_risk.columns].quantile(0.75)
fund_return_risk.loc['50"'] = fund_return_risk[fund_return_risk.columns].quantile(0.5)
fund_return_risk.loc['75"'] = fund_return_risk[fund_return_risk.columns].quantile(0.25)

fund_return_normal.loc['25"'] = fund_return_normal[fund_return_normal.columns].quantile(0.75)
fund_return_normal.loc['50"'] = fund_return_normal[fund_return_normal.columns].quantile(0.5)
fund_return_normal.loc['75"'] = fund_return_normal[fund_return_normal.columns].quantile(0.25)

#看看基金公司的风险收益特征

fof_fund2020['年化收益']=w.wss(fof_fund2020.index.tolist(), "return","annualized=1;startDate=20230101;endDate=20230616").Data[0]
fof_fund2020['年化波动']=w.wss(fof_fund2020.index.tolist(), "risk_stdevyearly","startDate=20230101;endDate=20230616;period=1;returnType=1").Data[0]
fof_fund2020['最大回撤']=w.wss(fof_fund2020.index.tolist(), "risk_maxdownside","startDate=20230101;endDate=20230616").Data[0]

# fof_fund2020=fof_fund2020.reset_index()
return_risk = dict()

type1=['养老目标日期','养老目标风险','普通']
type2=['低','中','高']


for i in type1:
    for j in type2:
        return_risk[i+j]=pd.DataFrame()
        return_risk[i+j]=fof_fund2020[['基金管理人', '年化收益', '年化波动','最大回撤']][(fof_fund2020['类型'] == i)&(fof_fund2020['风险'] == j)].groupby(['基金管理人']).mean()

result_summary = dict({'1 FOF持基数量 ':fund_num, '2 内部数量':fund_self_num, '3 FOF资产配置':fund_asset, '4 主被动股基占比':fund_active, '5 全市场行业基金占比':fund_stocklabel, '6 债券基金占比':fund_bondlabel,
                  '7 板块配置':fund_group, '8 风格指数':fund_style, '9 养老目标日期收益':fund_return_date, '10 养老目标风险收益':fund_return_risk, '11 普通FOF收益':fund_return_normal, '12 风险收益特征':return_risk,'13 穿透仓位':fund_stock})

with pd.ExcelWriter('基金分析结果导出/基金公司分析结果v5.xlsx') as writer:
    for i in result_summary:
        if isinstance(result_summary[i],dict)==False:
            result_summary[i].to_excel(writer,sheet_name=i)
        else:
            for j in result_summary[i]:
                result_summary[i][j].to_excel(writer,sheet_name=i+' '+j)
