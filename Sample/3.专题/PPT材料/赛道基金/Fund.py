from Basicfun import *
print(os.path.basename(__file__))

#%%
startdate=dt.date(enddatetime.year-1,enddatetime.month,enddatetime.day).strftime(datestyle)
vstartdate=dt.date(enddatetime.year-15,enddatetime.month,enddatetime.day).strftime(datestyle)
startdate5=dt.date(enddatetime.year-5,enddatetime.month,enddatetime.day).strftime(datestyle)
freq='M'  #频率
date_list=w.tdays(startdate, enddate, "Period="+freq, usedf=True)[1].index.tolist()
rolling_window=6  #移动平均窗口(月)
daytype='Alldays'

#%%数量规模
data_count=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='数量规模')
name_count=['全市场','医药','成长','消费','金融']
tag_count=['数量','规模']
color_count=['gray','green','orange','red','blue']
fig_count,ax_count=plt.subplots(nrows=1,ncols=2,figsize=(16,9),dpi=400)  #画多图
for m in range(2):
    for n in range(5):
        if n!=3:
            ax_count[m].plot(data_count[name_count[n]+'_'+tag_count[m]], color=color_count[n], ls='-' if n!=0 else '--',label=name_count[n])
            ax_count[m].text(data_count.index[-1], data_count[name_count[n]+'_'+tag_count[m]][-1], '%.1f' % data_count[name_count[n]+'_'+tag_count[m]][-1],color=color_count[n],fontsize=fontsize_legend)
        else:
            ax_count[m].plot(data_count[name_count[n]+'_'+tag_count[m]], color=color_count[n], ls='-' if n!=0 else '--',label=name_count[n])
            ax_count[m].text(data_count.index[-1], data_count[name_count[n]+'_'+tag_count[m]][-1], '%.1f' % data_count[name_count[n]+'_'+tag_count[m]][-1],color=color_count[n],fontsize=fontsize_legend)
    ax_count[m].legend(loc=2,fontsize=fontsize_legend)  #图例
    ax_count[m].grid(alpha=light)
    ax_count[m].set_xlim(data_count.index[0],data_count.index[-1])
    # ax_count[m].set_ylim(0,5)
    ax_count[m].spines['top'].set_visible(False)
    ax_count[m].spines['right'].set_visible(False)
    ax_count[m].set_title(tag_count[m]+'增长倍数',fontsize=fontsize_subtitle)
fig_count.suptitle('各类基金数量与规模累计增长倍数',fontsize=fontsize_suptitle,fontweight='bold')
fig_count.tight_layout()
# fig_count.savefig(fp + '\\Result\\1.3MM\\1.3.106各类基金数量与规模累计增速.png')
fig_count.savefig(fp + '\\Result\\0.0WFR\\5.4.1各类基金数量与规模累计增长倍数.png')
plt.close()


#%%风险收益
data_rr=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='风险收益')*100
name_rr=['成长','全市场','消费','医药','金融']
num_rr=np.linspace(1,len(name_rr),len(name_rr))
color_rr=['gray','green','orange','red','blue']
title_rr=['各类基金风险收益特征','各类基金内部分化程度']
width=0.35
fig_rr,ax_rr=plt.subplots(nrows=1,ncols=2,figsize=(16,9),dpi=400)  #画多图
for m in range(2):
    if m==0:
        ax_rr[m].scatter(data_rr.loc['年化波动率'], data_rr.loc['年化收益率'], color='blue',marker='o',label='过去五年风险收益特征')
        for n in range(len(name_rr)):
            ax_rr[m].text(data_rr.loc['年化波动率',name_rr[n]], data_rr.loc['年化收益率',name_rr[n]]-1, name_rr[n]+'\n('+'%.1f' % data_rr.loc['年化波动率',name_rr[n]]+','+'%.1f' % data_rr.loc['年化收益率',name_rr[n]]+')',color=color_rr[n],ha='center',fontsize=fontsize_legend)
        ax_rr[m].set_xlabel('年化波动率(%)')
        ax_rr[m].set_ylabel('年化收益率(%)')
    else:
        ax_rr[m].bar(num_rr-width/2,data_rr.loc['收益离散程度均值'],width,color='blue', label='收益离散程度均值')
        ax_rr[m].bar(num_rr+width/2,data_rr.loc['1/4分位-3/4分位均值'], width, color='red', label='1/4分位-3/4分位均值')
        ax_rr[m].xaxis.set_major_locator(ticker.FixedLocator(np.linspace(1,len(name_rr),len(name_rr))))
        ax_rr[m].set_xticklabels(name_rr)
        for k in range(len(name_rr)):
            ax_rr[m].text(num_rr[k]-width/2, data_rr.loc['收益离散程度均值'][k], '%.1f' % data_rr.loc['收益离散程度均值'][k],color='blue',ha='center',fontsize=fontsize_legend)
            ax_rr[m].text(num_rr[k]+width/2, data_rr.loc['1/4分位-3/4分位均值'][k], '%.1f' % data_rr.loc['1/4分位-3/4分位均值'][k],color='red',ha='center',fontsize=fontsize_legend)
        ax_rr[m].legend(loc=1,fontsize=fontsize_legend)  #图例
    ax_rr[m].grid(alpha=light)
    # ax_rr[m].set_xlim(data_rr.index[0],data_rr.index[-1])
    # ax_rr[m].set_ylim(0,5)
    ax_rr[m].spines['top'].set_visible(False)
    ax_rr[m].spines['right'].set_visible(False)
    ax_rr[m].set_title(title_rr[m],fontsize=fontsize_subtitle)
fig_rr.suptitle('各类基金风险收益特征及内部分化程度',fontsize=fontsize_suptitle,fontweight='bold')
fig_rr.tight_layout()
# fig_rr.savefig(fp + '\\Result\\1.3MM\\1.3.106各类基金数量与规模累计增速.png')
fig_rr.savefig(fp + '\\Result\\0.0WFR\\5.4.2各类基金风险收益特征及内部分化程度.png')
plt.close()


#%%组合净值(中位数)
data_port_rr=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='组合对比')*100
data_port_pct=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='分位数对比')
name_port=['1/2','1/3','1/4','2/3','3/4']
num_port=np.linspace(1,len(name_port),len(name_port))
color_port=['gray','green','orange','purple','olive']
type_port=['赛道基金组合','全市场基金']
title_port=['赛道基金与全市场基金不同分位数组合风险收益特征','赛道基金与全市场基金累计净值和超额收益(中位数组合)']
width=0.35
fig_port,ax_port=plt.subplots(nrows=1,ncols=2,figsize=(16,9),dpi=400)  #画多图
for m in range(2):
    if m==0:
        ax_port[m].scatter(data_port_rr.loc['年化波动率'][data_port_rr.columns.str.contains(type_port[0])], data_port_rr.loc['年化收益率'][data_port_rr.columns.str.contains(type_port[0])], color='blue',marker='o',label=type_port[0])
        ax_port[m].scatter(data_port_rr.loc['年化波动率'][data_port_rr.columns.str.contains(type_port[1])], data_port_rr.loc['年化收益率'][data_port_rr.columns.str.contains(type_port[1])], color='red',marker='*',label=type_port[1])
        for n in range(len(name_port)):
            ax_port[m].text(data_port_rr.loc['年化波动率',type_port[0]+'_'+name_port[n]], data_port_rr.loc['年化收益率',type_port[0]+'_'+name_port[n]]-2, type_port[0]+'_'+name_port[n]+'\n('+'%.1f' % data_port_rr.loc['年化波动率',type_port[0]+'_'+name_port[n]]+','+'%.1f' % data_port_rr.loc['年化收益率',type_port[0]+'_'+name_port[n]]+')',color=color_port[n],ha='center',fontsize=fontsize_legend)
            ax_port[m].text(data_port_rr.loc['年化波动率',type_port[1]+'_'+name_port[n]], data_port_rr.loc['年化收益率',type_port[1]+'_'+name_port[n]]-2, type_port[1]+'_'+name_port[n]+'\n('+'%.1f' % data_port_rr.loc['年化波动率',type_port[1]+'_'+name_port[n]]+','+'%.1f' % data_port_rr.loc['年化收益率',type_port[1]+'_'+name_port[n]]+')',color=color_port[n],ha='center',fontsize=fontsize_legend)
        ax_port[m].spines['top'].set_visible(False)
        ax_port[m].spines['right'].set_visible(False)
        ax_port[m].set_xlabel('年化波动率(%)')
        ax_port[m].set_ylabel('年化收益率(%)')
    else:
        ax_port[m].plot(data_port_pct.index,data_port_pct[type_port[0]+'_'+name_port[0]],color='blue', label=type_port[0]+'累计净值')
        ax_port[m].plot(data_port_pct.index,data_port_pct[type_port[1]+'_'+name_port[0]],color='red', label=type_port[1]+'累计净值')
        axport=ax_port[m].twinx()
        axport.fill_between(data_port_pct.index,(data_port_pct[type_port[0]+'_'+name_port[0]]-data_port_pct[type_port[1]+'_'+name_port[0]])*100,alpha=light,color=color_port[0],label='超额收益(右轴)')
        ax_port[m].spines['top'].set_visible(False)
        axport.spines['top'].set_visible(False)
        ax_port[m].set_xlim(data_port_pct.index[0],data_port_pct.index[-1])
        axport.legend(loc=1,fontsize=fontsize_legend)  #图例
        axport.yaxis.set_major_formatter(ticker.StrMethodFormatter('{x:.0f}' + '%'))  # 百分比显示
    ax_port[m].legend(loc=2,fontsize=fontsize_legend)  #图例
    ax_port[m].grid(alpha=light)
    ax_port[m].set_title(title_port[m],fontsize=fontsize_subtitle)
fig_port.suptitle('赛道基金与全市场基金对比',fontsize=fontsize_suptitle,fontweight='bold')
fig_port.tight_layout()
# fig_port.savefig(fp + '\\Result\\1.3MM\\1.3.106各类基金数量与规模累计增速.png')
fig_port.savefig(fp + '\\Result\\0.0WFR\\5.4.3赛道基金组合与全市场基金对比_50分位.png')
plt.close()

#%%组合净值(靠前)
title_port_top=['赛道基金与全市场基金累计净值和超额收益(前1/3分位组合)','赛道基金与全市场基金累计净值和超额收益(前1/4分位组合)']
fig_port_top,ax_port_top=plt.subplots(nrows=1,ncols=2,figsize=(16,9),dpi=400)  #画多图
for m in range(2):
    ax_port_top[m].plot(data_port_pct.index,data_port_pct[type_port[0]+'_'+name_port[m+1]],color='blue', label=type_port[0]+'累计净值')
    ax_port_top[m].plot(data_port_pct.index,data_port_pct[type_port[1]+'_'+name_port[m+1]],color='red', label=type_port[1]+'累计净值')
    axport=ax_port_top[m].twinx()
    axport.fill_between(data_port_pct.index,(data_port_pct[type_port[0]+'_'+name_port[m+1]]-data_port_pct[type_port[1]+'_'+name_port[m+1]])*100,alpha=light,color=color_port[0],label='超额收益(右轴)')
    ax_port_top[m].spines['top'].set_visible(False)
    axport.spines['top'].set_visible(False)
    ax_port_top[m].set_xlim(data_port_pct.index[0],data_port_pct.index[-1])
    axport.legend(loc=1,fontsize=fontsize_legend)  #图例
    axport.yaxis.set_major_formatter(ticker.StrMethodFormatter('{x:.0f}' + '%'))  # 百分比显示
    ax_port_top[m].legend(loc=2,fontsize=fontsize_legend)  #图例
    ax_port_top[m].grid(alpha=light)
    ax_port_top[m].set_title(title_port_top[m],fontsize=fontsize_subtitle)
fig_port_top.suptitle('赛道基金与全市场基金对比(排名靠前组合)',fontsize=fontsize_suptitle,fontweight='bold')
fig_port_top.tight_layout()
# fig_port_top.savefig(fp + '\\Result\\1.3MM\\1.3.106各类基金数量与规模累计增速.png')
fig_port_top.savefig(fp + '\\Result\\0.0WFR\\5.4.4赛道基金组合与全市场基金对比_排名靠前组合.png')
plt.close()

#%%组合净值(靠后)
title_port_last=['赛道基金与全市场基金累计净值和超额收益(2/3分位组合)','赛道基金与全市场基金累计净值和超额收益(3/4分位组合)']
fig_port_last,ax_port_last=plt.subplots(nrows=1,ncols=2,figsize=(16,9),dpi=400)  #画多图
for m in range(2):
    ax_port_last[m].plot(data_port_pct.index,data_port_pct[type_port[0]+'_'+name_port[m+3]],color='blue', label=type_port[0]+'累计净值')
    ax_port_last[m].plot(data_port_pct.index,data_port_pct[type_port[1]+'_'+name_port[m+3]],color='red', label=type_port[1]+'累计净值')
    axport=ax_port_last[m].twinx()
    axport.fill_between(data_port_pct.index,(data_port_pct[type_port[0]+'_'+name_port[m+3]]-data_port_pct[type_port[1]+'_'+name_port[m+3]])*100,alpha=light,color=color_port[0],label='超额收益(右轴)')
    ax_port_last[m].spines['top'].set_visible(False)
    axport.spines['top'].set_visible(False)
    ax_port_last[m].set_xlim(data_port_pct.index[0],data_port_pct.index[-1])
    axport.legend(loc=1,fontsize=fontsize_legend)  #图例
    axport.yaxis.set_major_formatter(ticker.StrMethodFormatter('{x:.0f}' + '%'))  # 百分比显示
    ax_port_last[m].legend(loc=2,fontsize=fontsize_legend)  #图例
    ax_port_last[m].grid(alpha=light)
    ax_port_last[m].set_title(title_port_last[m],fontsize=fontsize_subtitle)
fig_port_last.suptitle('赛道基金与全市场基金对比(排名靠后组合)',fontsize=fontsize_suptitle,fontweight='bold')
fig_port_last.tight_layout()
# fig_port_last.savefig(fp + '\\Result\\1.3MM\\1.3.106各类基金数量与规模累计增速.png')
fig_port_last.savefig(fp + '\\Result\\0.0WFR\\5.4.5赛道基金组合与全市场基金对比_排名靠后组合.png')
plt.close()

#%%风险收益
data_duration=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='持续性')*100
name_duration=['全市场','医药','成长','消费']
tag_duration=['前1/4','前1/3','前1/2','前2/3','前3/4']
num_duration=np.linspace(1,len(name_duration),len(name_duration))
numtag_duration=np.linspace(1,len(tag_duration),len(tag_duration))
color_duration=['green','orange','red','blue']
title_duration=['各类基金秩相关性','各类基金表现持续概率']
width=0.4
fig_duration,ax_duration=plt.subplots(nrows=1,ncols=2,figsize=(16,9),dpi=400)  #画多图
for m in range(2):
    if m==0:
        ax_duration[m].bar(num_duration,data_duration.loc['秩相关性']/100,width,color='blue', label='秩相关性')
        ax_duration[m].xaxis.set_major_locator(ticker.FixedLocator(np.linspace(1,len(name_duration),len(name_duration))))
        ax_duration[m].set_xticklabels(name_duration)
        for k in range(len(name_duration)):
            ax_duration[m].text(num_duration[k], data_duration.loc['秩相关性'][k]/100, '%.2f' % (data_duration.loc['秩相关性'][k]/100),color='blue',ha='center',fontsize=fontsize_legend)
    else:
        for n in range(len(name_duration)):
            ax_duration[m].bar(numtag_duration-width*0.75+width/2*(n),data_duration[name_duration[n]][1:],width/2,color=color_duration[n], label=name_duration[n])
            ax_duration[m].xaxis.set_major_locator(ticker.FixedLocator(np.linspace(1,len(tag_duration),len(tag_duration))))
            ax_duration[m].set_xticklabels(tag_duration)
            ax_duration[m].yaxis.set_major_formatter(ticker.StrMethodFormatter('{x:.0f}' + '%'))  # 百分比显示
            for k in range(len(tag_duration)):
                ax_duration[m].text((numtag_duration-width*0.75+width/2*(n))[k], data_duration[name_duration[n]][1:][k], '%.0f%%' % (data_duration[name_duration[n]][1:][k]),color=color_duration[n],ha='center',fontsize=fontsize_legend)
            ax_duration[m].legend(loc=2,fontsize=fontsize_legend)  #图例
    ax_duration[m].grid(alpha=light)
    # ax_duration[m].set_xlim(data_duration.index[0],data_duration.index[-1])
    # ax_duration[m].set_ylim(0,5)
    ax_duration[m].spines['top'].set_visible(False)
    ax_duration[m].spines['right'].set_visible(False)
    ax_duration[m].set_title(title_duration[m],fontsize=fontsize_subtitle)
fig_duration.suptitle('各类基金秩相关性及表现持续概率',fontsize=fontsize_suptitle,fontweight='bold')
fig_duration.tight_layout()
# fig_duration.savefig(fp + '\\Result\\1.3MM\\1.3.106各类基金数量与规模累计增速.png')
fig_duration.savefig(fp + '\\Result\\0.0WFR\\5.4.6各类基金秩相关性及表现持续概率.png')
plt.close()

#%%中国库存周期
data_worst=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='表现不佳')
name_worst=data_worst.columns
color_worst=['blue','red','orange']
date_worst=[['2018-07-13','2018-10-30'],['2020-07-01','2020-11-23']]
fig_worst,ax_worst=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(16,9),dpi=400)  #画多图
for m in range(len(name_worst)):
    ax_worst.plot(data_worst.index,data_worst[name_worst[m]],label=name_worst[m],color=color_worst[m])
ax_worst.legend(loc=2,fontsize=fontsize_legend)
ax_worst.spines['top'].set_visible(False)
ax_worst.spines['right'].set_visible(False)
for n in range(len(date_worst)):
    ax_worst.axvspan(date_worst[n][0], date_worst[n][1], facecolor='gray', alpha=light+0.1)
ax_worst.axhline(y=1, lw=2, ls='--', c='black')  # 横线
ax_worst.set_xlim(data_worst.index[0],data_worst.index[-1])
fig_worst.suptitle('表现不佳情形(赛道基金/全市场基金)',fontsize=fontsize_suptitle,fontweight='bold')
fig_worst.tight_layout()
fig_worst.savefig(fp+'\\Result\\0.0WFR\\5.4.7表现不佳情形.png')
plt.close()

#%%中国库存周期
data_pct=pd.read_excel(fp+'\\Code\\Fig\\Fund.xlsx',header=0,index_col=0,sheet_name='权重占比')*100
name_pct=data_pct.columns
color_pct=['blue','red','orange']
fig_pct,ax_pct=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(16,9),dpi=400)  #画多图
for m in range(len(name_pct)):
    ax_pct.plot(data_pct.index,data_pct[name_pct[m]],label=name_pct[m],color=color_pct[m])
    ax_pct.text(data_pct.index[-1],(data_pct[name_pct[m]][-1] if m!=1 else (data_pct[name_pct[m]][-1]-1)),'%.1f%%'%data_pct[name_pct[m]][-1],color=color_pct[m],fontsize=fontsize_legend)
ax_pct.legend(loc=2,fontsize=fontsize_legend)
ax_pct.spines['top'].set_visible(False)
ax_pct.spines['right'].set_visible(False)
ax_pct.yaxis.set_major_formatter(ticker.StrMethodFormatter('{x:.0f}' + '%'))  # 百分比显示
ax_pct.grid(alpha=light)
ax_pct.set_ylim(0,100)
ax_pct.set_xlim(data_pct.index[0],data_pct.index[-1])
fig_pct.suptitle('核心赛道占比',fontsize=fontsize_suptitle,fontweight='bold')
fig_pct.tight_layout()
fig_pct.savefig(fp+'\\Result\\0.0WFR\\5.4.8核心赛道占比.png')
plt.close()