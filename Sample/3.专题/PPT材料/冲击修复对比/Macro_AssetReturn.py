# -*- coding: utf-8 -*-
"""
Created on Mon Jul  5 11:24:30 2021

@author: zhang
"""

import numpy as np
import pandas as pd
import os
filepath = os.getcwd()
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['font.size']=14 # 字体大小
# import seaborn
# seaborn.set(font='Microsoft YaHei',style='white')
from WindPy import *
w.start()
fontsize_subtitle=16
fontsize_suptitle=18
fontsize_legend=10

macrodata = pd.read_excel(filepath+'\\data.xlsx',index_col=0,sheet_name='ML_C')
zxindex = pd.read_excel(filepath+'\\Market.xlsx',index_col=0,sheet_name='1.2SectorZX')
zxClose = w.wsd(list(zxindex.index), "close", "2004-12-31", "2021-06-30", "Period=M",usedf=True)[1]
styleIndex = pd.read_excel(filepath+'\\Market.xlsx',index_col=0,sheet_name='1.1IndexMarket')
styleClose = w.wsd(list(styleIndex.index), "close", "2004-12-31", "2021-06-30", "Period=M",usedf=True)[1]
style_zx = styleIndex[styleIndex['简称'].str.contains("风格.中信")]
style_zxClose = w.wsd(list(style_zx.index), "close", "2004-12-31", "2021-06-30", "Period=M",usedf=True)[1]
windaClose = w.wsd('881001.WI', "close", "2004-12-31", "2021-06-30", "Period=D",usedf=True)[1]
style_zxClose_d = w.wsd(list(style_zx.index), "close", "2004-12-31", "2021-06-30", "Period=D",usedf=True)[1]

#%%行业阶段收益
#日度
style_zxRet_d = style_zxClose_d.pct_change(periods=1,axis=0).dropna(how="all")
style_zxRet_d['平均收益'] = style_zxRet_d.mean(axis=1)
#月度
zxRet_m = zxClose.pct_change(periods=1,axis=0).dropna(how="all")
style_zxRet_m = style_zxClose.pct_change(periods=1,axis=0).dropna(how="all")
macrodata.index = style_zxRet_m.index
style_zxRet_m['平均收益'] = style_zxRet_m.mean(axis=1)
#%%划分阶段
#经济周期
coreMacro0 = macrodata.loc[:,['宏观经济景气指数:先行指数','PPI','宏观景气flag','PPIflag']]
coreMacro = macrodata.loc[date(2008,10,31):date(2014,2,28),['宏观经济景气指数:先行指数','PPI','宏观景气flag','PPIflag']]
coreMacro2 = macrodata.loc[date(2020,2,28):date(2021,6,30),['宏观经济景气指数:先行指数','PPI','宏观景气flag','PPIflag']]
coreFinance = macrodata.loc[:,['社融TTM同比','DR007-调','社融flag','DR007flag']]

def GetTimeRange(data_crit,allMacdata):
    month2 = pd.concat([data_crit,pd.DataFrame([],index=allMacdata.index)],axis=1,sort=True)
    date_start = []
    date_end = []
    for i in range(month2.shape[0]):
        if i < month2.shape[0]-1:
            if np.isnan(month2.iloc[i,0])==True and np.isnan(month2.iloc[i+1,0])==False:
                date_start.append(month2.index[i])
            elif np.isnan(month2.iloc[i,0])==False and np.isnan(month2.iloc[i+1,0])==True:
                date_end.append(month2.index[i])
        elif np.isnan(month2.iloc[i,0])==False and i==month2.shape[0]-1:
            date_end.append(month2.index[i])
    if len(date_start) != len(date_end):
        date_start = [data_crit.index[0]] + date_start
    return date_start,date_end
t0List = [GetTimeRange(coreMacro0[(coreMacro0['宏观景气flag']==1)&(coreMacro0['PPIflag']==-1)],coreMacro0),
         GetTimeRange(coreMacro0[(coreMacro0['宏观景气flag']==1)&(coreMacro0['PPIflag']==1)],coreMacro0),
         GetTimeRange(coreMacro0[(coreMacro0['宏观景气flag']==-1)&(coreMacro0['PPIflag']==1)],coreMacro0),
         GetTimeRange(coreMacro0[(coreMacro0['宏观景气flag']==-1)&(coreMacro0['PPIflag']==-1)],coreMacro0)]#经济周期全时间区间
tList = [GetTimeRange(coreMacro[(coreMacro['宏观景气flag']==1)&(coreMacro['PPIflag']==-1)],coreMacro),
         GetTimeRange(coreMacro[(coreMacro['宏观景气flag']==1)&(coreMacro['PPIflag']==1)],coreMacro),
         GetTimeRange(coreMacro[(coreMacro['宏观景气flag']==-1)&(coreMacro['PPIflag']==1)],coreMacro),
         GetTimeRange(coreMacro[(coreMacro['宏观景气flag']==-1)&(coreMacro['PPIflag']==-1)],coreMacro)]#金融危机后时间区间
t2List = [GetTimeRange(coreMacro2[(coreMacro2['宏观景气flag']==1)&(coreMacro2['PPIflag']==-1)],coreMacro2),
          GetTimeRange(coreMacro2[(coreMacro2['宏观景气flag']==1)&(coreMacro2['PPIflag']==1)],coreMacro2),
          GetTimeRange(coreMacro2[(coreMacro2['宏观景气flag']==-1)&(coreMacro2['PPIflag']==1)],coreMacro2),
          GetTimeRange(coreMacro2[(coreMacro2['宏观景气flag']==-1)&(coreMacro2['PPIflag']==-1)],coreMacro2)]#本轮疫情后时间区间
tfList = [GetTimeRange(coreFinance[(coreFinance['社融flag']==1)&(coreFinance['DR007flag']==-1)],coreFinance),
         GetTimeRange(coreFinance[(coreFinance['社融flag']==1)&(coreFinance['DR007flag']==1)],coreFinance),
         GetTimeRange(coreFinance[(coreFinance['社融flag']==-1)&(coreFinance['DR007flag']==1)],coreFinance),
         GetTimeRange(coreFinance[(coreFinance['社融flag']==-1)&(coreFinance['DR007flag']==-1)],coreFinance)]#金融周期时间划分
#市场阶段-上一轮
tm1_xf = [date(2008,10,31),date(2008,12,31)]
tm2_zq = [date(2008,12,31),date(2009,7,31)]
tm3_cz = [date(2009,7,31),date(2010,11,30)]
tm4_zq = [date(2010,11,30),date(2011,3,31)]
tm5_jr = [date(2011,3,31),date(2013,1,31)]
tm6_cz = [date(2013,1,31),date(2014,1,30)]
tmList1 = [tm1_xf,tm2_zq,tm3_cz,tm4_zq,tm5_jr,tm6_cz]
  #最好的行业：消费-周期-成长-周期-金融-成长
ret1_adj = pd.concat([style_zxRet_d.loc[tm1_xf[0]:tm1_xf[1],'CI005919.WI'],style_zxRet_d.loc[tm2_zq[0]:tm2_zq[1],'CI005918.WI'],\
                      style_zxRet_d.loc[tm3_cz[0]:tm3_cz[1],'CI005920.WI'],style_zxRet_d.loc[tm4_zq[0]:tm4_zq[1],'CI005918.WI'],\
                          style_zxRet_d.loc[tm5_jr[0]:tm5_jr[1],'CI005917.WI'],style_zxRet_d.loc[tm6_cz[0]:tm6_cz[1],'CI005920.WI']],axis=0,sort=False)
ret1_mean = style_zxRet_d.loc[ret1_adj.index,'平均收益']
nav1_adj = np.cumprod(1+ret1_adj)
nav1_adj = nav1_adj/nav1_adj.iloc[0]
nav1_mean = np.cumprod(1+ret1_mean)
nav1_mean = nav1_mean/nav1_mean.iloc[0]
winda1 = windaClose.loc[nav1_adj.index,:]
# winda1 = winda1/winda1.iloc[0,0]
  #最差的行业：周期-消费-金融-消费-周期-金融
ret1_worst = pd.concat([style_zxRet_d.loc[tm1_xf[0]:tm1_xf[1],'CI005918.WI'],style_zxRet_d.loc[tm2_zq[0]:tm2_zq[1],'CI005919.WI'],
                        style_zxRet_d.loc[tm3_cz[0]:tm3_cz[1],'CI005917.WI'],style_zxRet_d.loc[tm4_zq[0]:tm4_zq[1],'CI005919.WI'],
                        style_zxRet_d.loc[tm5_jr[0]:tm5_jr[1],'CI005918.WI'],style_zxRet_d.loc[tm6_cz[0]:tm6_cz[1],'CI005917.WI']],axis=0,sort=False)
nav1_worst = np.cumprod(1+ret1_worst)
nav1_worst = nav1_worst/nav1_worst.iloc[0]
#市场阶段-本轮
tm1_xf = [date(2020,2,28),date(2020,7,31)]
tm2_zq = [date(2020,7,31),date(2021,3,31)]
tm3_cz = [date(2021,3,31),date(2021,6,30)]
tmList2 = [tm1_xf,tm2_zq,tm3_cz]
ret2_adj = pd.concat([style_zxRet_d.loc[tm1_xf[0]:tm1_xf[1],'CI005919.WI'],
                      style_zxRet_d.loc[tm2_zq[0]:tm2_zq[1],'CI005918.WI'],
                      style_zxRet_d.loc[tm3_cz[0]:tm3_cz[1],'CI005920.WI']],axis=0,sort=False)
ret2_mean = style_zxRet_d.loc[ret2_adj.index,'平均收益']
nav2_adj0 = np.cumprod(1+ret2_adj)
nav2_adj0 = nav2_adj0/nav2_adj0.iloc[0]
nav2_mean = np.cumprod(1+ret2_mean)
nav2_mean = nav2_mean/nav2_mean.iloc[0]
winda2 = windaClose.loc[nav2_adj0.index,:]
# winda2 = winda2/winda2.iloc[0,0]
  #最差的行业：金融-成长-金融
ret2_worst = pd.concat([style_zxRet_d.loc[tm1_xf[0]:tm1_xf[1],'CI005917.WI'],
                        style_zxRet_d.loc[tm2_zq[0]:tm2_zq[1],'CI005920.WI'],
                        style_zxRet_d.loc[tm3_cz[0]:tm3_cz[1],'CI005917.WI']],axis=0,sort=False)
nav2_worst = np.cumprod(1+ret2_worst)
nav2_worst = nav2_worst/nav2_worst.iloc[0]

#%%绘图
#经济周期
colorList = ['green','red','yellow','blue']#分别对应复苏、过热、滞胀、衰退
fig,ax_ft = plt.subplots(nrows=2,ncols=1,sharex=False,figsize=(16,9),dpi=400)
axft0=ax_ft[0].twinx()
axft1=ax_ft[1].twinx()
axft=[[ax_ft[0],axft0],[ax_ft[1],axft1]]
color_ft=['blue','red']
List_ft=[t0List,tfList]
loc_tf=[[2,1],[2,1]]
label_ft=[['宏观经济景气指数','PPI'],['新增社融同比_TTM','DR007']]
title_ft=["经济周期(增长/通胀)：复苏-绿|过热-红|滞胀-黄|衰退-蓝","金融周期(货币/信用)：复苏-绿|过热-红|滞胀-黄|衰退-蓝"]
data_ft=[[coreMacro0[coreMacro0.columns[0]],coreMacro0[coreMacro0.columns[1]]],[coreFinance[coreFinance.columns[0]]*100,coreFinance[coreFinance.columns[1]]]]
for m in range(2):  #左右图
    for n in range(2):  #左右轴
        if m==1 and n==1:
            # axft[m][n].plot(data_ft[m][n],color=color_ft[n],label=label_ft[m][n],alpha=0.2)
            axft[m][n].plot(data_ft[m][n].rolling(3).mean(),color=color_ft[n],label=label_ft[m][n]+'_MA3M')
            axft[m][n].set_ylim(0.9,6)
        else:
            axft[m][n].plot(data_ft[m][n],color=color_ft[n],label=label_ft[m][n])
        axft[m][n].spines['top'].set_visible(False)
        axft[m][n].spines['left'].set_color(color_ft[0])
        axft[m][n].spines['right'].set_color(color_ft[1])
        axft[m][n].tick_params(axis='y', direction='out', color=color_ft[n],
                                       labelcolor=color_ft[n])
        axft[m][n].legend(loc=loc_tf[m][n],fontsize=fontsize_legend)
        if n==0:
            # axft[m][n].grid(alpha=light)  # 横线
            axft[m][n].set_xlim(data_ft[m][n].index[0],data_ft[m][n].index[-1])
            axft[m][n].tick_params(axis='x', direction='out',rotation=45)
            axft[m][n].set_title(title_ft[m],fontsize=fontsize_subtitle)
            for x in range(len(List_ft[m])):
                for y in range(len(List_ft[m][x][0])):
                    axft[m][n].axvspan(xmin=List_ft[m][x][0][y],xmax=List_ft[m][x][1][y],facecolor=colorList[x],alpha=0.2)
fig.suptitle('中国经济及金融周期',fontsize=fontsize_suptitle,fontweight='bold')
fig.tight_layout()
fig.savefig(filepath+'\\中国经济及金融周期.png')
plt.close()

#%%行业表现结合宏观背景图
t_gap = nav1_adj.shape[0]-nav2_adj0.shape[0]
#t_gap = 360
t_gap_list = [date(x.year,x.month,x.day) for x in pd.date_range(start='7/1/2021',periods =t_gap,freq='B')]
nav2_adj = pd.concat([nav2_adj0,pd.Series([1.0]*len(t_gap_list),index=t_gap_list)],axis=0,sort=False)

fig,ax_ft = plt.subplots(nrows=2,ncols=1,sharex=False,figsize=(16,9),dpi=400)
colorList1 = ['green','purple','orange','purple','blue','orange']#分别对应消费、周期、成长、周期、金融、成长
nameList1=['消费','周期','成长','周期','金融','成长']
colorList2 = ['green','red','yellow','blue']#分别对应复苏、过热、滞胀、衰退
p1=ax_ft[0].twinx()
p1.plot(winda1,color='red',label='万得全A')
p2, = ax_ft[0].plot((nav1_adj/nav1_mean).loc[tmList1[0][0]:tmList1[0][1]],color=colorList1[0],label=nameList1[0])
p3, = ax_ft[0].plot((nav1_adj/nav1_mean).loc[tmList1[1][0]:tmList1[1][1]],color=colorList1[1],label=nameList1[1])
p4, = ax_ft[0].plot((nav1_adj/nav1_mean).loc[tmList1[2][0]:tmList1[2][1]],color=colorList1[2],label=nameList1[2])
p5, = ax_ft[0].plot((nav1_adj/nav1_mean).loc[tmList1[3][0]:tmList1[3][1]],color=colorList1[3],label=nameList1[3])
p6, = ax_ft[0].plot((nav1_adj/nav1_mean).loc[tmList1[4][0]:tmList1[4][1]],color=colorList1[4],label=nameList1[4])
p7, = ax_ft[0].plot((nav1_adj/nav1_mean).loc[tmList1[5][0]:tmList1[5][1]],color=colorList1[5],label=nameList1[5])
# p8, = ax_ft[0].plot((nav1_worst/nav1_mean).loc[tmList1[0][0]:tmList1[0][1]],color=colorList1[1],linewidth=2,label='周期')#周期-消费-金融-消费-周期-金融
# p9, = ax_ft[0].plot((nav1_worst/nav1_mean).loc[tmList1[1][0]:tmList1[1][1]],color=colorList1[0],linewidth=2,label='消费')#周期-消费-金融-消费-周期-金融
# p10, = ax_ft[0].plot((nav1_worst/nav1_mean).loc[tmList1[2][0]:tmList1[2][1]],color=colorList1[4],linewidth=2,label='金融')#周期-消费-金融-消费-周期-金融
# p11, = ax_ft[0].plot((nav1_worst/nav1_mean).loc[tmList1[3][0]:tmList1[3][1]],color=colorList1[0],linewidth=2,label='消费')#周期-消费-金融-消费-周期-金融
# p12, = ax_ft[0].plot((nav1_worst/nav1_mean).loc[tmList1[4][0]:tmList1[4][1]],color=colorList1[1],linewidth=2,label='周期')#周期-消费-金融-消费-周期-金融
# p13, = ax_ft[0].plot((nav1_worst/nav1_mean).loc[tmList1[5][0]:tmList1[5][1]],color=colorList1[4],linewidth=2,label='金融')#周期-消费-金融-消费-周期-金融
for k in range(len(nameList1)):
    if k==0 or k==3:
        ax_ft[0].text(tmList1[k][1]+(tmList1[k][0]-tmList1[k][1])/2.1,(nav1_adj/nav1_mean).loc[tmList1[k][1]+(tmList1[k][0]-tmList1[k][1])/2.1]-0.18,nameList1[k],color=colorList1[k])
    else:
        ax_ft[0].text(tmList1[k][1]+(tmList1[k][0]-tmList1[k][1])/2,(nav1_adj/nav1_mean).loc[tmList1[k][1]+(tmList1[k][0]-tmList1[k][1])/2]-0.2,nameList1[k],color=colorList1[k])
ax_ft[0].legend([p2,p3,p4,p6],[l.get_label() for l in [p2,p3,p4,p6]],loc=2,fontsize=fontsize_legend)
ax_ft[0].spines['top'].set_visible(False)
p1.spines['top'].set_visible(False)
ax_ft[0].spines['right'].set_color('red')
p1.spines['right'].set_color('red')
ax_ft[0].set_ylabel('超额收益累计净值', color='blue')
p1.set_ylabel('万得全A', color='red')
p1.legend(loc=1,fontsize=fontsize_legend)
ax_ft[0].tick_params(axis='x', direction='out',rotation=45)
ax_ft[0].tick_params(axis='y', direction='out',color='blue', labelcolor='blue')
p1.tick_params(axis='y', direction='out',color='red', labelcolor='red')
ax_ft[0].set_xlim(nav1_adj.index[0],nav1_adj.index[-1])
for x in range(len(tList)):
    for y in range(len(tList[x][0])):
        ax_ft[0].axvspan(xmin=tList[x][0][y],xmax=tList[x][1][y],facecolor=colorList2[x],alpha=0.2)
ax_ft[0].set_title("金融危机后，最好板块依次为：消费-周期-成长-周期-金融-成长",fontsize=fontsize_subtitle)

colorList1 = ['green','purple','orange','blue']#分别对应消费、周期、成长
nameList2=['消费','周期','成长']
colorList2 = ['green','red','yellow','blue']#分别对应复苏、过热、滞胀、衰退
p1= ax_ft[1].twinx()
p1.plot(winda2,color='red',label='万得全A')
p2, = ax_ft[1].plot((nav2_adj/nav2_mean).loc[tmList2[0][0]:tmList2[0][1]],color=colorList1[0],label='消费')
p3, = ax_ft[1].plot((nav2_adj/nav2_mean).loc[tmList2[1][0]:tmList2[1][1]],color=colorList1[1],label='周期')
p4, = ax_ft[1].plot((nav2_adj/nav2_mean).loc[tmList2[2][0]:tmList2[2][1]],color=colorList1[2],label='成长')
p5, = ax_ft[1].plot(nav2_adj[nav2_adj==1.0],color='white',label='',alpha=0)
# p6, = ax_ft[1].plot((nav2_worst/nav2_mean).loc[tmList2[0][0]:tmList2[0][1]],color=colorList1[3],linewidth=2,label='金融')
# p7, = ax_ft[1].plot((nav2_worst/nav2_mean).loc[tmList2[1][0]:tmList2[1][1]],color=colorList1[2],linewidth=2,label='成长')
# p8, = ax_ft[1].plot((nav2_worst/nav2_mean).loc[tmList2[2][0]:tmList2[2][1]],color=colorList1[3],linewidth=2,label='金融')
for g in range(len(nameList2)):
    ax_ft[1].text(tmList2[g][1]+(tmList2[g][0]-tmList2[g][1])/2,(nav2_adj/nav2_mean).loc[tmList2[g][1]+(tmList2[g][0]-tmList2[g][1])/2.2]-0.05,nameList2[g],color=colorList1[g])
ax_ft[1].legend([p2,p3,p4],[l.get_label() for l in [p2,p3,p4]],loc=2,fontsize=10)
ax_ft[1].spines['top'].set_visible(False)
p1.spines['top'].set_visible(False)
ax_ft[1].spines['right'].set_color('red')
p1.spines['right'].set_color('red')
ax_ft[1].set_ylabel('超额收益累计净值', color='blue')
p1.set_ylabel('万得全A', color='red')
p1.legend(loc=1,fontsize=fontsize_legend)
ax_ft[1].tick_params(axis='x', direction='out',rotation=45)
ax_ft[1].tick_params(axis='y', direction='out',color='blue', labelcolor='blue')
p1.tick_params(axis='y', direction='out',color='red', labelcolor='red')
ax_ft[1].set_xlim(nav2_adj.index[0],nav2_adj.index[-1])
for x in range(len(t2List)):
    for y in range(len(t2List[x][0])):
        ax_ft[1].axvspan(xmin=t2List[x][0][y],xmax=t2List[x][1][y],facecolor=colorList2[x],alpha=0.2)
ax_ft[1].set_title("疫情冲击后，最好板块依次为：消费-周期-成长",fontsize=fontsize_subtitle)
fig.suptitle('两轮冲击修复后的板块轮动对比\n【经济周期(增长/通胀)：复苏-绿|过热-红|滞胀-黄|衰退-蓝】',fontweight='bold',fontsize=fontsize_suptitle)
fig.tight_layout()
fig.savefig(filepath+'\\两轮冲击修复后的板块轮动对比.png')
plt.close()





