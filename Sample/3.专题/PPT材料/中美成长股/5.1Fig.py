import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import statsmodels.api as sm
import os
import datetime as dt
from dateutil.relativedelta import relativedelta
import matplotlib.ticker as ticker
import pandas.io.formats.excel as excel
excel.ExcelFormatter.header_style = None
from WindPy import *
w.start()
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['font.size']=14 # 字体大小
fp='D:\\New\\Python'
startdate='2005-01-01'            #历史回测起点
startdate_new='2018-01-01'            #历史回测起点
enddate=w.tdaysoffset(-0 if (w.tdaysoffset(0, dt.datetime.today(), "").Data[0][0].date()!=dt.datetime.today().date())  else -1, dt.datetime.today(), "").Data[0][0].strftime('%Y-%m-%d')     #dt.date(2020,11,24).strftime('%Y-%m-%d')    #历史回测终点
freq='M'  #频率
rolling_window=6  #移动平均窗口(月)
light=0.4  #图像明暗度
daytype='Alldays'
fontsize_subtitle=14
fontsize_suptitle=18
fontsize_legend=10

#%%收益分解
data_tech_roe_cn=pd.read_excel(fp+'\\Code\\Fig\\Tech.xlsx',header=0,index_col=0,sheet_name='A股').fillna(method='ffill')
data_tech_roe_us=pd.read_excel(fp+'\\Code\\Fig\\Tech.xlsx',header=0,index_col=0,sheet_name='美股').fillna(method='ffill')
data_tech_sector_cn=pd.read_excel(fp+'\\Code\\Fig\\Tech.xlsx',header=0,index_col=0,sheet_name='A股节奏').fillna(method='ffill')
data_tech_sector_us=pd.read_excel(fp+'\\Code\\Fig\\Tech.xlsx',header=0,index_col=0,sheet_name='美股节奏').fillna(method='ffill')
data_tech_rr_cn=pd.read_excel(fp+'\\Code\\Fig\\Tech.xlsx',header=0,index_col=0,sheet_name='中国战略配置')
data_tech_rr_us=pd.read_excel(fp+'\\Code\\Fig\\Tech.xlsx',header=0,index_col=0,sheet_name='美国战略配置')

#%%roe
fig_roe,ax_roe=plt.subplots(nrows=1,ncols=2,sharex=False,figsize=(16,7),dpi=400)  #画多图
ax_roe[0].plot(data_tech_roe_us.index,data_tech_roe_us.iloc[:,0],label=data_tech_roe_us.columns[0],color='blue')
ax_roe11=ax_roe[0].twinx()
ax_roe11.plot(data_tech_roe_us.index,data_tech_roe_us.iloc[:,1],label=data_tech_roe_us.columns[1],color='red')
# ax_roe[0].axhline(y=0, lw=2, ls='--', c='black')  # 横线
ax_roe[0].set_xlim(data_tech_roe_us.index[0],data_tech_roe_us.index[-1])
ax_roe[0].legend(loc=2,fontsize=fontsize_legend)
ax_roe11.legend(loc=1,fontsize=fontsize_legend)
ax_roe[0].spines['top'].set_visible(False)
ax_roe11.spines['top'].set_visible(False)
ax_roe[0].spines['left'].set_color('blue')
ax_roe11.spines['left'].set_color('blue')
ax_roe[0].spines['right'].set_color('red')
ax_roe11.spines['right'].set_color('red')
ax_roe[0].tick_params(axis='y', direction='out', color='blue', labelcolor='blue')
ax_roe11.tick_params(axis='y', direction='out', color='red', labelcolor='red')
ax_roe[0].set_title('美股成长板块表现',fontsize=fontsize_subtitle)

ax_roe[1].plot(data_tech_roe_cn.index,data_tech_roe_cn.iloc[:,0],label=data_tech_roe_cn.columns[0],color='blue')
ax_roe22=ax_roe[1].twinx()
ax_roe22.plot(data_tech_roe_cn.index,data_tech_roe_cn.iloc[:,1],label=data_tech_roe_cn.columns[1],color='red')
# ax_roe[1].axhline(y=0, lw=2, ls='--', c='black')  # 横线
ax_roe[1].set_xlim(data_tech_roe_cn.index[0],data_tech_roe_cn.index[-1])
ax_roe[1].legend(loc=2,fontsize=fontsize_legend)
ax_roe22.legend(loc=1,fontsize=fontsize_legend)
ax_roe[1].spines['top'].set_visible(False)
ax_roe22.spines['top'].set_visible(False)
ax_roe[1].spines['left'].set_color('blue')
ax_roe22.spines['left'].set_color('blue')
ax_roe[1].spines['right'].set_color('red')
ax_roe22.spines['right'].set_color('red')
ax_roe[1].tick_params(axis='y', direction='out', color='blue', labelcolor='blue')
ax_roe22.tick_params(axis='y', direction='out', color='red', labelcolor='red')
ax_roe[1].set_title('A股成长板块表现',fontsize=fontsize_subtitle)

fig_roe.suptitle('美股和A股成长板块相对价格与相对基本面表现对比',fontsize=18,fontweight='bold')
fig_roe.tight_layout()
fig_roe.savefig(fp+'\\Result\\5.1Fig\\5.1.1roe.png')
fig_roe.show()

#%%sector
# fig_sector,ax_sector=plt.subplots(nrows=1,ncols=2,sharex=False,figsize=(16,9),dpi=400)  #画多图
# ax_sector[0].plot(data_tech_sector_us.index,data_tech_sector_us.iloc[:,0],label=data_tech_sector_us.columns[0],color='blue')
# ax_sector[0].plot(data_tech_sector_us.index,data_tech_sector_us.iloc[:,1],label=data_tech_sector_us.columns[1],color='red')
# ax_sector[0].plot(data_tech_sector_us.index,data_tech_sector_us.iloc[:,2],label=data_tech_sector_us.columns[2],color='orange')
# ax_sector[0].plot(data_tech_sector_us.index,data_tech_sector_us.iloc[:,3],label=data_tech_sector_us.columns[3],color='green')
# ax_sector[0].plot(data_tech_sector_us.index,data_tech_sector_us.iloc[:,4],label=data_tech_sector_us.columns[4],color='gray')
# ax_sector[0].axhline(y=1, lw=2, ls='--', c='black')  # 横线
# # ax_sector[0].axvline(x=data_tech_sector_us.index[41], lw=2, ls='--', c='black')  # 横线
# ax_sector[0].axvline(x=data_tech_sector_us.index[42], lw=2, ls='--', c='black')  # 横线
# # ax_sector[0].axvline(x=data_tech_sector_us.index[59], lw=2, ls='--', c='black')  # 横线
# ax_sector[0].axvline(x=data_tech_sector_us.index[60], lw=2, ls='--', c='black')  # 横线
# ax_sector[0].set_xlim(data_tech_sector_us.index[0],data_tech_sector_us.index[-1])
# ax_sector[0].legend(loc=2,fontsize=fontsize_legend)
# ax_sector[0].spines['top'].set_visible(False)
# ax_sector[0].spines['right'].set_visible(False)
# # ax_sector[0].tick_params(axis='y', direction='out', color='blue', labelcolor='blue')
# ax_sector[0].set_title('美股成长子板块表现',fontsize=fontsize_subtitle)
#
# ax_sector[1].plot(data_tech_sector_cn.index,data_tech_sector_cn.iloc[:,0],label=data_tech_sector_cn.columns[0],color='blue')
# ax_sector[1].plot(data_tech_sector_cn.index,data_tech_sector_cn.iloc[:,1],label=data_tech_sector_cn.columns[1],color='red')
# ax_sector[1].plot(data_tech_sector_cn.index,data_tech_sector_cn.iloc[:,2],label=data_tech_sector_cn.columns[2],color='orange')
# ax_sector[1].axhline(y=1, lw=2, ls='--', c='black')  # 横线
# # ax_sector[1].axvline(x=data_tech_sector_cn.index[176], lw=2, ls='--', c='black')  # 横线
# ax_sector[1].axvline(x=data_tech_sector_cn.index[177], lw=2, ls='--', c='black')  # 横线
# # ax_sector[1].axvline(x=data_tech_sector_cn.index[323], lw=2, ls='--', c='black')  # 横线
# ax_sector[1].axvline(x=data_tech_sector_cn.index[324], lw=2, ls='--', c='black')  # 横线
# ax_sector[1].set_xlim(data_tech_sector_cn.index[0],data_tech_sector_cn.index[-1])
# ax_sector[1].legend(loc=2,fontsize=fontsize_legend)
# ax_sector[1].spines['top'].set_visible(False)
# ax_sector[1].spines['right'].set_visible(False)
# ax_sector[1].tick_params(axis='x', direction='out', rotation=45)
# ax_sector[1].set_title('A股成长子板块表现',fontsize=fontsize_subtitle)
#
# fig_sector.suptitle('美股和A股成长子板块表现对比',fontsize=18,fontweight='bold')
# fig_sector.tight_layout()
# fig_sector.savefig(fp+'\\Result\\5.1Fig\\5.1.2sector.png')
# fig_sector.show()

#%%画图
# fig_rr,ax_rr=plt.subplots(nrows=1,ncols=2,figsize=[16,9],dpi=400)
# ax_rr[0].scatter(data_tech_rr_us.iloc[:6,0].values,data_tech_rr_us.iloc[:6,1].values,c='b',marker='^',label='历史均值')
# ax_rr[0].scatter(data_tech_rr_us.iloc[6:,0].values,data_tech_rr_us.iloc[6:,1].values,c='r',marker='o',label='5年中位数')
# ax_rr[0].set_xlabel('波动率倍数(相对成长风格)')
# ax_rr[0].set_ylabel('年化超额收益(相对成长风格)')
# for i in range(len(data_tech_rr_us.iloc[:6,0])):
#     ax_rr[0].text(data_tech_rr_us.iloc[:6,0].values[i]-0.02,data_tech_rr_us.iloc[:6,1].values[i]+0.003,data_tech_rr_us.iloc[:6,0].index[i],color='b',fontsize=fontsize_legend)
#     ax_rr[0].text(data_tech_rr_us.iloc[6:,0].values[i]-0.05,data_tech_rr_us.iloc[6:,1].values[i]+0.003,data_tech_rr_us.iloc[6:,0].index[i],color='r',fontsize=fontsize_legend)
# ax_rr[0].axhline(y=0, lw=2, ls='--', c='black')  # 横线
# ax_rr[0].axvline(x=1, lw=2, ls='--', c='black')  # 横线
# ax_rr[0].legend(fontsize=fontsize_legend,loc=2)
# # ax_rr.grid(alpha=0.5)
# ax_rr[0].spines['top'].set_visible(False)
# ax_rr[0].spines['right'].set_visible(False)
# ax_rr[0].yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos:'{:.0%}'.format(x)))
# ax_rr[0].set_title('美股成长子板块风险收益特征',fontsize=fontsize_subtitle)
#
# ax_rr[1].scatter(data_tech_rr_cn.iloc[:8,0].values,data_tech_rr_cn.iloc[:8,1].values,c='b',marker='^',label='历史均值')
# ax_rr[1].scatter(data_tech_rr_cn.iloc[8:,0].values,data_tech_rr_cn.iloc[8:,1].values,c='r',marker='o',label='5年中位数')
# ax_rr[1].set_xlabel('波动率倍数(相对成长风格)')
# ax_rr[1].set_ylabel('年化超额收益(相对成长风格)')
# for i in range(len(data_tech_rr_cn.iloc[:8,0])):
#     ax_rr[1].text(data_tech_rr_cn.iloc[:8,0].values[i]-0.01,data_tech_rr_cn.iloc[:8,1].values[i]+0.003,data_tech_rr_cn.iloc[:8,0].index[i],color='b',fontsize=fontsize_legend)
#     ax_rr[1].text(data_tech_rr_cn.iloc[8:,0].values[i]-0.02,data_tech_rr_cn.iloc[8:,1].values[i]+0.003,data_tech_rr_cn.iloc[8:,0].index[i],color='r',fontsize=fontsize_legend)
# ax_rr[1].axhline(y=0, lw=2, ls='--', c='black')  # 横线
# ax_rr[1].axvline(x=1, lw=2, ls='--', c='black')  # 横线
# ax_rr[1].legend(fontsize=fontsize_legend,loc=2)
# # ax_rr.grid(alpha=0.5)
# ax_rr[1].spines['top'].set_visible(False)
# ax_rr[1].spines['right'].set_visible(False)
# ax_rr[1].yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos:'{:.0%}'.format(x)))
# ax_rr[1].set_title('A股成长子板块风险收益特征',fontsize=fontsize_subtitle)
#
# fig_rr.suptitle('美股和A股成长子板块风险收益特征',fontsize=18,fontweight='bold')
# fig_rr.tight_layout()
# fig_rr.savefig(fp+'\\Result\\5.1Fig\\5.1.3rr.png')
# fig_rr.show()