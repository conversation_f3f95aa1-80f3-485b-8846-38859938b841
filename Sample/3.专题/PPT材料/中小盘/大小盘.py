import numpy as np
import pandas as pd
import pandas.io.formats.excel as excel
excel.ExcelFormatter.header_style = None
from dateutil.relativedelta import relativedelta
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 设置字体
plt.rcParams['axes.unicode_minus']=False # 显示中文和特殊符号
plt.rcParams['font.size']=14 # 字体大小
plt.rcParams['figure.dpi']=400
plt.rcParams['figure.figsize']=[16, 9]
plt.rcParams['lines.linewidth']=1
import matplotlib as mpl
import statsmodels.api as sm
from matplotlib.font_manager import FontProperties
import os
import datetime as dt
from WindPy import *
w.start()
import warnings
warnings.filterwarnings('ignore')
import requests
import random
from bs4 import BeautifulSoup
import ffn
import bt
import shutil
import re
fp='D:\\New\\Python'
fp_target=os.getcwd()
fontsize_subtitle=16
fontsize_suptitle=18
fontsize_legend=10
print(os.path.basename(__file__))
t0=dt.datetime.now()
t=t0

#%%
startdate_new='2018-01-01'            #历史回测起点
datestyle='%Y-%m-%d'
enddatetime=w.tdaysoffset(-0 if (w.tdaysoffset(0, dt.datetime.today(), "").Data[0][0].date()!=dt.datetime.today().date()) else -1, dt.datetime.today(), "").Data[0][0]       #dt.date(2020,12,31)
startdate_senario=dt.date(enddatetime.year-15,enddatetime.month,enddatetime.day).strftime(datestyle)#'2004-12-31'
startdate=dt.date(enddatetime.year-15,enddatetime.month,enddatetime.day).strftime(datestyle)    #'2002-01-01'
startdate_18=dt.date(enddatetime.year-18,enddatetime.month,enddatetime.day).strftime(datestyle)    #'2002-01-01'
enddate=enddatetime.strftime(datestyle)     #历史回测终点
freq='M'  #频率
rolling_window=6  #移动平均窗口(月)
light=0.2  #图像明暗度
daytype='Alldays'
light_stage=0.2

#%%运行时间
def printtime(t):
    print('花费%s秒'%str(dt.datetime.now()-t))
    t=dt.datetime.now()
    return t

#%%导入所有宏观、基本面、市场指标
code_macro_growth=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='1.1Growth')[0].tolist()
name_macro_growth=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='1.1Growth')[1].tolist()
code_macro_inflation=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='2.1Inflation')[0].tolist()
name_macro_inflation=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='2.1Inflation')[1].tolist()
code_macro_liquidity=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='3.1Liquidity')[0].tolist()
name_macro_liquidity=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='3.1Liquidity')[1].tolist()
code_macro_sf=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='3.2SF')[0].tolist()
name_macro_sf=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='3.2SF')[1].tolist()
code_macro_global=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='4.1Global')[0].tolist()
name_macro_global=pd.read_excel(fp+'\\Code\\Macro.xlsx',header=None,sheet_name='4.1Global')[1].tolist()
code_macro=code_macro_growth+code_macro_inflation+code_macro_liquidity+code_macro_sf+code_macro_global
name_macro=name_macro_growth+name_macro_inflation+name_macro_liquidity+name_macro_sf+name_macro_global
macro=dict(zip(name_macro,code_macro))

code_fundamental_finance=pd.read_excel(fp+'\\Code\\Fundamental.xlsx',header=None,sheet_name='1.1Finance')[0].tolist()
name_fundamental_finance=pd.read_excel(fp+'\\Code\\Fundamental.xlsx',header=None,sheet_name='1.1Finance')[1].tolist()
code_fundamental_price=pd.read_excel(fp+'\\Code\\Fundamental.xlsx',header=None,sheet_name='1.2Price')[0].tolist()
name_fundamental_price=pd.read_excel(fp+'\\Code\\Fundamental.xlsx',header=None,sheet_name='1.2Price')[1].tolist()
code_fundamental=code_fundamental_finance+code_fundamental_price
name_fundamental=name_fundamental_finance+name_fundamental_price
fundamental=dict(zip(name_fundamental,code_fundamental))

code_market_index_IndexMarket=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.1IndexMarket')[0].tolist()
name_market_index_IndexMarket=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.1IndexMarket')[1].tolist()
code_market_index_SectorZX=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.2SectorZX')[0].tolist()
name_market_index_SectorZX=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.2SectorZX')[1].tolist()
code_market_index_Sector2SW=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.3Sector2SW')[0].tolist()
name_market_index_Sector2SW=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.3Sector2SW')[1].tolist()
code_market_index_StockZX=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.4StockZX')[0].tolist()
name_market_index_StockZX=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.4StockZX')[1].tolist()
code_market_index_ETF=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.5ETF')[0].tolist()
name_market_index_ETF=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='1.5ETF')[1].tolist()
code_market_index=code_market_index_IndexMarket+code_market_index_SectorZX+code_market_index_Sector2SW+code_market_index_StockZX+code_market_index_ETF
name_market_index=name_market_index_IndexMarket+name_market_index_SectorZX+name_market_index_Sector2SW+name_market_index_StockZX+name_market_index_ETF
market_index=dict(zip(name_market_index,code_market_index))

code_market_fund_FundZX=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='2.1FundZX')[0].tolist()
name_market_fund_FundZX=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='2.1FundZX')[1].tolist()
code_market_fund_FundBase=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='2.2FundBase')[0].tolist()
name_market_fund_FundBase=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='2.2FundBase')[1].tolist()
code_market_fund=code_market_fund_FundZX+code_market_fund_FundBase
name_market_fund=name_market_fund_FundZX+name_market_fund_FundBase
market_fund=dict(zip(name_market_fund,code_market_fund))

code_market_index_yield=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='3.3Yield')[0].tolist()
name_market_index_yield=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='3.3Yield')[1].tolist()
market_yield=dict(zip(name_market_index_yield,code_market_index_yield))

#%%宏观数据处理
data_growth=w.edb(code_macro_growth, startdate_senario, enddate,usedf=True)[1]  #增长数据
data_growth.columns=name_macro_growth  #增长名称
growth_freq_d=['花旗中国意外']  #增长日频名单
growth_freq_w=['高炉开工率','螺纹钢库存']  #增长周频名单
growth_freq_q=['实际GDP','产能利用率','人均可支配收入','GDP同比贡献_第一产业','GDP同比贡献_第二产业','GDP同比贡献_第三产业','GDP同比贡献_资本形成','GDP同比贡献_最终消费','GDP同比贡献_净出口']  #增长季频名单
data_growth_d=data_growth[growth_freq_d].dropna(how='all')  #增长日频数据
data_growth_w=data_growth[growth_freq_w].dropna(how='all')  #增长周频数据
data_growth_q=data_growth[growth_freq_q].dropna(how='all')  #增长季频数据
data_growth_m=data_growth[[i for i in name_macro_growth if i not in growth_freq_d+growth_freq_w+growth_freq_q]].dropna(how='all').fillna(method='bfill')  #增长月频数据

data_inflation=w.edb(code_macro_inflation, startdate_senario, enddate,usedf=True)[1]  #通胀数据
data_inflation.columns=name_macro_inflation  #通胀名称
inflation_freq_d=[]  #通胀日频名单
inflation_freq_w=[]  #通胀周频名单
inflation_freq_q=['GDP平减指数']  #通胀季频名单
# data_inflation_d=data_inflation[inflation_freq_d].dropna(how='all')       #通胀日频数据
data_inflation_w=data_inflation[inflation_freq_w].dropna(how='all')       #通胀周频数据
data_inflation_q=data_inflation[inflation_freq_q].dropna(how='all')       #通胀季频数据
data_inflation_m=data_inflation[[i for i in name_macro_inflation if i not in inflation_freq_d+inflation_freq_w+inflation_freq_q]].dropna(how='all').fillna(method='bfill')  #通胀月频数据

data_liquidity=w.edb(code_macro_liquidity, startdate_senario, enddate,usedf=True)[1]  #流动性数据
data_liquidity.columns=name_macro_liquidity  #流动性名称
liquidity_freq_d=['DR007','SHIBOR_7天','融资余额']  #流动性日频名单
liquidity_freq_w=[]  #流动性周频名单
liquidity_freq_q=['基础货币同比','贷款加权平均利率','超储率']  #流动性季频名单
data_liquidity_d=data_liquidity[liquidity_freq_d].dropna(how='all')  #流动性日频数据
# data_liquidity_w=data_liquidity[liquidity_freq_w].dropna(how='all')       #流动性周频数据
data_liquidity_q=data_liquidity[liquidity_freq_q].dropna(how='all')   #流动性季频数据
data_liquidity_m=data_liquidity[[i for i in name_macro_liquidity if i not in liquidity_freq_d+liquidity_freq_w+liquidity_freq_q]].dropna(how='all').fillna(method='bfill')  #流动性月频数据

data_sf=w.edb(code_macro_sf, startdate_senario, enddate,usedf=True)[1]  #社融数据
data_sf.columns=name_macro_sf  #社融名称
data_sf_m=data_sf  #社融月频数据

#%%市场指数价格及收益率
name_market_selected=pd.read_excel(fp+'\\Code\\Market.xlsx',header=None,sheet_name='0.1Selected')[1].tolist()
nav_index=w.wsd([market_index[i] for i in name_market_selected], "close", startdate_senario, enddate, "Period="+freq, "Fill=Previous", "Days=Alldays", usedf=True)[1]   #境内市场指数净值
nav_index.columns=name_market_selected        #境内市场指数名称
ret_index=nav_index.pct_change().dropna(axis=0,how='all')*100       #境内市场指数收益率
ret_index.columns=name_market_selected        #境内市场指数名称

#%%领先滞后期数及相关系数计算
def lag(x,y):       #x、y均为Series
    corr=[]
    n = np.linspace(-12, 12, 25)
    for i in range(len(n)):
        corr.append(x.corr(y.shift(int(n[i]))))
    corr_abs=np.abs(corr).tolist()
    j=int(np.linspace(-12,12,25)[corr_abs.index(max(corr_abs))])
    k='%.2f'%corr[corr_abs.index(max(corr_abs))]
    l='%.2f'%corr[12]
    return j,k,l      #领先期数，最大相关系数，相关系数

#%%自定义宏观指标
macroname_self1=['中采PMI','财新PMI','社融同比','M2同比','M1同比','实际GDP','GDP平减指数','CPI','PPI']        #按月宏观指标
macroname_self2=['R007','DR007','国债收益率_10年','国债收益率_1年','美元兑人民币_中间价','美债实_10Y','国债收益率(10Y)_美国','伦敦金现']     #其它频率宏观指标
macrocode_self1=[macro[i] for i in macroname_self1]     #获取代码
macrocode_self2=[macro[i] for i in macroname_self2]     #获取代码
data_macro_self1=w.edb(macrocode_self1, startdate_senario, enddate,Period=freq,usedf=True)[1]       #获取数据
data_macro_self1.columns=macroname_self1
data_macro_self2=w.wsd(macrocode_self2, 'close', startdate_senario, enddate, Period=freq, usedf=True)[1]        #获取数据
data_macro_self2.columns=macroname_self2
data_macro_self = pd.concat([data_macro_self1, data_macro_self2], axis=1, join='outer').sort_index(axis=0,ascending=True).fillna(method='ffill')        #合并宏观数据
data_macro_self = data_macro_self.drop(index=[data_macro_self.index[i] for i in range(len(data_macro_self) - 1) if
                                          data_macro_self.index[i].month == data_macro_self.index[i + 1].month])        #处理宏观数据

data_sf_self0=pd.read_excel(fp+'\\Code\\Data.xlsx',header=0,index_col=0,sheet_name='1.1SF')
data_sf_self0.index=data_sf_self0.index.date
data_sf_self1=data_macro_self['社融同比']
data_sf_self2=pd.concat([data_sf_self1,data_sf_self0],axis=1,join='outer').sort_index(axis=0,ascending=True)
data_sf_self=data_sf_self2.drop(index=[data_sf_self2.index[i] for i in range(len(data_sf_self2) - 1) if data_sf_self2.index[i].month == data_sf_self2.index[i + 1].month])
data_sf_self.loc[dt.date(2019,9,30):,'广义社融同比']=data_sf_self.loc[dt.date(2019,9,30):,'社融同比']/data_sf_self.loc[dt.date(2019,8,31),'社融同比']*data_sf_self.loc[dt.date(2019,8,31),'广义社融同比']
data_macro_self=pd.concat([data_macro_self,data_sf_self['广义社融同比']],axis=1,join='inner')

data_macro_self['名义GDP']=((data_macro_self['实际GDP']/100+1)*(data_macro_self['GDP平减指数']/100+1)-1)*100
data_macro_self['M1-名义GDP']=data_macro_self['M1同比']-data_macro_self['名义GDP']
data_macro_self['M2-名义GDP']=data_macro_self['M2同比']-data_macro_self['名义GDP']
data_macro_self['M2-社融']=data_macro_self['M2同比']-data_macro_self['广义社融同比']
data_macro_self['0.3PPI+0.7CPI']=data_macro_self['PPI']*0.3+data_macro_self['CPI']*0.7      #添加综合价格指标
data_macro_self['PPI-CPI']=data_macro_self['PPI']-data_macro_self['CPI']      #添加剪刀差指标
data_macro_self['M1-M2']=data_macro_self['M1同比']-data_macro_self['M2同比']      #添加剪刀差指标
data_macro_self['通胀预期_美国BEI']=data_macro_self['国债收益率(10Y)_美国']-data_macro_self['美债实_10Y']      #添加剪刀差指标
data_macro_self['DR007']=pd.concat([data_macro_self['R007'][data_macro_self.index<dt.date(2015,1,1)],
                                        data_macro_self['DR007'][data_macro_self.index>=dt.date(2015,1,1)]],axis=0)      #合并流动性指标
data_macro_self['DR007']=data_macro_self['DR007'].rolling(window=rolling_window).mean()
chg_macro_self=(data_macro_self-data_macro_self.shift(1)).dropna(how='all')  #增长指标月变动
chg_m_self=pd.concat([chg_macro_self,ret_index],axis=1,join='outer')  #月变动汇总

#%%宏观指标边际变动
chg_growth_m=(data_growth_m-data_growth_m.shift(1)).dropna(how='all')  #增长指标月变动
chg_inflation_m=(data_inflation_m-data_inflation_m.shift(1)).dropna(how='all')  #通胀指标月变动
chg_liquidity_m=(data_liquidity_m-data_liquidity_m.shift(1)).dropna(how='all')  #流动性指标月变动
chg_sf_m=(data_sf_m-data_sf_m.shift(1)).dropna(how='all')  #社融指标月变动
chg_m=pd.concat([chg_growth_m,chg_inflation_m,chg_liquidity_m,chg_sf_m,ret_index],axis=1,join='outer')  #月变动汇总

t=printtime(t)

#%%剩余流动性与估值差
name_valuation=['沪深300','中证500']
code_valuation=[market_index[i] for i in name_valuation]
data_valuation=w.wsd(code_valuation, 'pe_ttm',startDate=startdate,endDate=enddate,Period=freq,usedf=True)[1]
data_valuation.columns=name_valuation
color_liqval=['blue','red']
loc_liqval=[2,1]
name_liqval=[['剩余流动性(M2-名义GDP)','估值差(中证500-沪深300)(右轴)'],['剩余流动性(M2-社融)','估值差(成长-价值)']]
data_liqval=[[data_macro_self['M2-名义GDP'],data_valuation[name_valuation[1]]-data_valuation[name_valuation[0]]],
            [data_macro_self['M2-社融'],data_valuation[name_valuation[1]]-data_valuation[name_valuation[0]]]]
m=0
fig_liqval,ax_liqval=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(7,7),dpi=400)  #画多图
axliqval=[ax_liqval,ax_liqval.twinx()]
for n in range(2):
    axliqval[n].plot(data_liqval[m][n],color=color_liqval[n],label=name_liqval[m][n])  #市场指数图
    axliqval[n].legend(loc=loc_liqval[n],fontsize=fontsize_legend)
    axliqval[n].spines['top'].set_visible(False)
    axliqval[n].spines['left'].set_color(color_liqval[0])
    axliqval[n].spines['right'].set_color(color_liqval[1])
    axliqval[n].tick_params(axis='y', direction='out', color=color_liqval[n],labelcolor=color_liqval[n])
    axliqval[n].tick_params(axis='x', direction='out', labelrotation=45)
    axliqval[n].set_xlim(data_liqval[m][n].index[0],data_liqval[m][n].index[-1])
# axliqval[0].set_title('领先期数:'+str(lag(data_liqval[m][0],data_liqval[m][1])[0])+'|最大相关系数:'+lag(data_liqval[m][0],data_liqval[m][1])[1]+'|相关系数:'+lag(data_liqval[m][0],data_liqval[m][1])[2])
# axliqval[0].grid(alpha=light)
fig_liqval.suptitle('剩余流动性与估值差('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_liqval.tight_layout()
fig_liqval.savefig(fp_target+'\\剩余流动性与估值差.png')
plt.close()


#%%中小盘与产业周期
name_size=['中证500','沪深300']
name_tech=['移动通信基站设备产量累计同比','新能源汽车产量累计同比']
code_size_tech=[market_index[i] for i in name_size]
data_size_tech=w.wsd(code_size_tech,'close',startDate=startdate,endDate=enddate,Period='M',Days='Alldays',usedf=True)[1]
data_size_tech.columns=name_size
data_size_tech=data_size_tech/data_size_tech.iloc[0,:]
data_size_tech[name_size[0]+'/'+name_size[1]]=data_size_tech[name_size[0]]/data_size_tech[name_size[1]]
pe_size_tech=w.wsd(code_size_tech,'pe_ttm',startDate=startdate,endDate=enddate,Period='M',Days='Alldays',usedf=True)[1].dropna(how='any')
pe_size_tech.columns=name_size
pe_size_tech=pe_size_tech/pe_size_tech.iloc[0,:]
pe_size_tech[name_size[0]+'/'+name_size[1]]=pe_size_tech[name_size[0]]/pe_size_tech[name_size[1]]
data_tech=w.edb("S0028240,S0243314",startdate,enddate,"Fill=Previous",usedf=True)[1]
data_tech.columns=name_tech
color_size=['orange','red','blue']
loc_size=[2,1]
startpoint_tech=[dt.date(2008,6,1),dt.date(2013,6,1),dt.date(2020,1,1)]
endpoint_tech=[dt.date(2010,10,1),dt.date(2015,1,1),enddatetime]
fig_size_tech,ax_size_tech=plt.subplots(nrows=1,ncols=1,figsize=[16,7],dpi=400)
axis_size_tech=ax_size_tech.twinx()
plot_size_tech=[ax_size_tech,axis_size_tech]
for m in range(2):
    if m==0:
        plot_size_tech[m].plot(data_tech.index,data_tech[name_tech[0]],label=name_tech[0],color=color_size[0])
        plot_size_tech[m].plot(data_tech[data_tech.index>dt.date(2017,12,31)].index,data_tech[data_tech.index>dt.date(2017,12,31)][name_tech[1]],label=name_tech[1],color=color_size[1])
        for i in range(len(startpoint_tech)):
            plot_size_tech[m].axvspan(startpoint_tech[i], endpoint_tech[i], facecolor='gray', alpha=light)  # 垂直带
    if m==1:
        plot_size_tech[m].plot(data_size_tech.index,data_size_tech[name_size[0]+'/'+name_size[1]],label=name_size[0]+'/'+name_size[1],color=color_size[-1])
        plot_size_tech[m].tick_params(axis='y', direction='out', color=color_size[-1],labelcolor=color_size[-1])
    plot_size_tech[m].tick_params(axis='x', direction='out', labelrotation=45)
    plot_size_tech[m].legend(fontsize=fontsize_legend,loc=loc_size[m])
    plot_size_tech[m].spines['top'].set_visible(False)
    plot_size_tech[m].spines['right'].set_color(color_size[-1])
    plot_size_tech[m].set_xlim(data_size_tech.index[0],data_size_tech.index[-1])
fig_size_tech.suptitle('中小盘与新兴产业('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_size_tech.tight_layout()
fig_size_tech.savefig(fp_target+'\\中小盘与新兴产业.png')
plt.close()

fig_size_pe,ax_size_pe=plt.subplots(nrows=1,ncols=1,figsize=[7,7],dpi=400)
axis_size_pe=ax_size_pe.twinx()
plot_size_pe=[ax_size_pe,axis_size_pe]
for m in range(2):
    if m==0:
        plot_size_pe[m].plot(data_size_tech.index,data_size_tech[name_size[0]+'/'+name_size[1]],label=name_size[0]+'/'+name_size[1]+'价格',color=color_size[-m-1])
    if m==1:
        plot_size_pe[m].plot(pe_size_tech.index,pe_size_tech[name_size[0]+'/'+name_size[1]],label=name_size[0]+'/'+name_size[1]+'PE(右轴)',color=color_size[-m-1])
        plot_size_pe[m].axhline(y=pe_size_tech[name_size[0]+'/'+name_size[1]][-1],color='gray',ls='--')
    plot_size_pe[m].tick_params(axis='y', direction='out', color=color_size[-m-1],labelcolor=color_size[-m-1])
    plot_size_pe[m].tick_params(axis='x', direction='out', labelrotation=45)
    plot_size_pe[m].legend(fontsize=fontsize_legend,loc=loc_size[m])
    plot_size_pe[m].spines['top'].set_visible(False)
    plot_size_pe[m].spines['right'].set_color(color_size[-2])
    plot_size_pe[m].spines['left'].set_color(color_size[-1])
    plot_size_pe[m].set_xlim(data_size_tech.index[0],data_size_tech.index[-1])
fig_size_pe.suptitle('中小盘价格与估值('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_size_pe.tight_layout()
fig_size_pe.savefig(fp_target+'\\中小盘价格与估值.png')
plt.close()
t=printtime(t)


#%%大小盘指数
name_size=[['中证500','沪深300'],['中证1000','中证800'],['小盘指数(申万)','大盘指数(申万)']]
color_size=['blue','red','orange']
fig_size,ax_size=plt.subplots(nrows=1,ncols=1,figsize=[16,4],dpi=400)
for m in range(len(name_size)):
    code_size = [market_index[i] for i in name_size[m]]
    data_size = w.wsd(code_size, 'close', startDate=startdate, endDate=enddate, Period='M', Days='Alldays', usedf=True)[1]
    data_size.columns = name_size[m]
    data_size = data_size / data_size.iloc[0, :]
    data_size_div=data_size[name_size[m][0]]/data_size[name_size[m][1]]
    ax_size.plot(data_size_div.index,data_size_div,label=(name_size[m][0]+'/'+name_size[m][1]),color=color_size[m])
ax_size.tick_params(axis='x', direction='out', labelrotation=45)
ax_size.legend(fontsize=fontsize_legend,loc=0)
ax_size.spines['top'].set_visible(False)
ax_size.spines['right'].set_visible(False)
ax_size.set_xlim(data_size.index[0],data_size.index[-1])
fig_size.suptitle('大小盘指数相对表现(过去15年，'+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_size.tight_layout()
fig_size.savefig(fp_target+'\\大小盘指数相对表现.png')
plt.close()
t=printtime(t)

#%%中小盘与联邦基金利率、杠杆率
name_size=['中证500','沪深300']
name_liquidity=['美国联邦基金目标利率','实体经济杠杆率年度变化(领先4个季度)']
code_size_liquidity=[market_index[i] for i in name_size]
data_size_liquidity=w.wsd(code_size_liquidity,'close',startDate=startdate,endDate=enddate,Period='M',Days='Alldays',usedf=True)[1]
data_size_liquidity.columns=name_size
data_size_liquidity=data_size_liquidity/data_size_liquidity.iloc[0,:]
data_size_liquidity[name_size[0]+'/'+name_size[1]]=data_size_liquidity[name_size[0]]/data_size_liquidity[name_size[1]]
data_liquidity_0=w.edb("M0000162",startdate_18,enddate,"Fill=Previous",usedf=True)[1]
data_liquidity_0.columns=[name_liquidity[0]]
data_liquidity_0=pd.concat([data_size_liquidity,data_liquidity_0],join='outer',axis=1).sort_index(ascending=True).fillna(method='ffill')[name_liquidity[0]]

data_liquidity_1=w.edb("M6404532",startdate_18,enddate,"Fill=Previous",usedf=True)[1]
data_liquidity_1.columns=[name_liquidity[1]]
data_liquidity_1[name_liquidity[1]]=data_liquidity_1[name_liquidity[1]]-data_liquidity_1[name_liquidity[1]].shift(4)
data_liquidity_1.index=data_liquidity_1.index.to_series().apply(lambda x:x+relativedelta(months=12))
# data_size_liquidity=pd.concat([data_size_liquidity,data_liquidity_1],join='outer',axis=1)
color_size=['blue','red','orange']
loc_size=[2,1]
startpoint_liquidity=[[dt.date(2015,11,30)],[dt.date(2010,12,31),dt.date(2015,6,30)]]
endpoint_liquidity=[[dt.date(2019,7,31)],[dt.date(2012,12,31),dt.date(2019,12,31)]]
fig_size_liquidity,ax_size_liquidity=plt.subplots(nrows=1,ncols=2,figsize=[16,7],dpi=400)
for n in range(2):  #左右图
    axis_size_liquidityn=ax_size_liquidity[n].twinx()
    plot_size_liquidity=[ax_size_liquidity[n],axis_size_liquidityn]
    for m in range(2):  #左右轴
        if m==0:
            plot_size_liquidity[m].plot(data_size_liquidity[name_size[0]+'/'+name_size[1]].index,data_size_liquidity[name_size[0]+'/'+name_size[1]],label=name_size[0]+'/'+name_size[1],color=color_size[0])
            plot_size_liquidity[m].tick_params(axis='y', direction='out', color=color_size[0],labelcolor=color_size[0])
            for i in range(len(startpoint_liquidity[n])):
                plot_size_liquidity[m].axvspan(startpoint_liquidity[n][i], endpoint_liquidity[n][i], facecolor='gray', alpha=light)  # 垂直带
        if m==1 and n==0:
            plot_size_liquidity[m].plot(data_liquidity_0.index,data_liquidity_0,label=name_liquidity[n],color=color_size[1+n])
            plot_size_liquidity[m].tick_params(axis='y', direction='out', color=color_size[1+n],labelcolor=color_size[1+n])
        if m==1 and n==1:
            plot_size_liquidity[m].plot(data_liquidity_1.index,data_liquidity_1,label=name_liquidity[n],color=color_size[1+n])
            plot_size_liquidity[m].tick_params(axis='y', direction='out', color=color_size[1 + n],labelcolor=color_size[1 + n])
        plot_size_liquidity[m].spines['right'].set_color(color_size[1+n])
        plot_size_liquidity[m].spines['left'].set_color(color_size[0])
        plot_size_liquidity[m].tick_params(axis='x', direction='out', labelrotation=45)
        plot_size_liquidity[m].legend(fontsize=fontsize_legend,loc=loc_size[m])
        plot_size_liquidity[m].spines['top'].set_visible(False)
        plot_size_liquidity[m].set_xlim(data_size_liquidity.index[0],)
        plot_size_liquidity[m].set_title('中小盘与'+name_liquidity[n])
fig_size_liquidity.suptitle('中小盘与宏观流动性('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_size_liquidity.tight_layout()
fig_size_liquidity.savefig(fp_target+'\\中小盘与宏观流动性.png')
plt.close()

t=printtime(t)

#%%中小盘与机构资金占比
name_fund=['中证500/沪深300','机构资金占比(右轴逆序)']
data_fund=pd.read_excel(fp_target+'\\画图-市值-202112.xlsx',sheet_name='p10',header=0,index_col=0)
data_fund=data_fund.iloc[:,::-1]
data_fund.columns=name_fund
name_mfund=['基金持股占比','公募基金赚钱效应(右轴,领先2个季度)']
name_allfund=['中小盘与机构资金占比','基金赚钱效应与市值占比']
data_mfund=pd.read_excel(fp_target+'\\画图-市值-202112.xlsx',sheet_name='p11',header=0,index_col=0)
data_mfund.columns=name_mfund
color_size=['blue','red']
loc_size=[2,1]
startpoint_fund=[[dt.date(2015,6,30)],[dt.date(2010,12,31),dt.date(2015,6,30)]]
endpoint_fund=[[dt.date(2020,12,31)],[dt.date(2012,12,31),dt.date(2019,12,31)]]
fig_size_fund,ax_size_fund=plt.subplots(nrows=1,ncols=2,figsize=[16,7],dpi=400)
for n in range(2):  #左右图
    axis_size_fundn=ax_size_fund[n].twinx()
    plot_size_fund=[ax_size_fund[n],axis_size_fundn]
    for m in range(2):  #左右轴
        if n==0:
            plot_size_fund[m].plot(data_fund[name_fund[m]].index,data_fund[name_fund[m]],label=name_fund[m],color=color_size[m])
            if m==1:
                plot_size_fund[m].invert_yaxis()
            if m==0:
                for i in range(len(startpoint_fund[n])):
                    plot_size_fund[m].axvspan(startpoint_fund[n][i], endpoint_fund[n][i], facecolor='gray', alpha=light)  # 垂直带
        if n==1:
            plot_size_fund[m].plot(data_mfund[name_mfund[m]].index,data_mfund[name_mfund[m]],label=name_mfund[m],color=color_size[m])
        plot_size_fund[m].tick_params(axis='y', direction='out', color=color_size[m],labelcolor=color_size[m])
        plot_size_fund[m].spines['right'].set_color(color_size[1])
        plot_size_fund[m].spines['left'].set_color(color_size[0])
        plot_size_fund[m].tick_params(axis='x', direction='out', labelrotation=45)
        plot_size_fund[m].legend(fontsize=fontsize_legend,loc=loc_size[m])
        plot_size_fund[m].spines['top'].set_visible(False)
        # plot_size_fund[m].set_xlim(data_fund.index[0],)
        plot_size_fund[m].set_title(name_allfund[n],fontsize=fontsize_subtitle)
fig_size_fund.suptitle('中小盘与微观流动性('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_size_fund.tight_layout()
fig_size_fund.savefig(fp_target+'\\中小盘与微观流动性.png')
plt.close()

t=printtime(t)

#%%大小盘指数
name_length=['中证500','沪深300']
color_length=['blue','red','green']
labelname=['大盘占优','中小盘占优']
code_length = [market_index[i] for i in name_length]
data_length = w.wsd(code_length, 'close', startDate='2005-01-01', endDate=enddate, Period='M', Days='Alldays', usedf=True)[1]
data_length.columns = name_length
data_length = data_length / data_length.iloc[0, :]
data_length_div=data_length[name_length[0]]/data_length[name_length[1]]
startpoint_length=[[dt.date(2005,1,1),dt.date(2010,11,30),dt.date(2015,11,30)],[dt.date(2007,10,31),dt.date(2013,1,31),dt.date(2021,2,26)]]
endpoint_length=[[dt.date(2007,10,31),dt.date(2013,1,31),dt.date(2021,2,26)],[dt.date(2010,11,30),dt.date(2015,11,30),enddatetime]]
fig_length,ax_length=plt.subplots(nrows=1,ncols=1,figsize=[16,9],dpi=400)
ax_length.plot(data_length_div.index,data_length_div,label=(name_length[0]+'/'+name_length[1]),color=color_length[0])
for n in range(len(startpoint_length)):
    for i in range(len(startpoint_length[n])):
        if i==0:
            ax_length.axvspan(startpoint_length[n][i], endpoint_length[n][i], facecolor=color_length[1+n], alpha=light,label=labelname[n])  # 垂直带
        else:
            ax_length.axvspan(startpoint_length[n][i], endpoint_length[n][i], facecolor=color_length[1+n], alpha=light)  # 垂直带
ax_length.axvline(x=dt.date(2020,1,1),ls='--',color='gray')
ax_length.tick_params(axis='x', direction='out', labelrotation=45)
ax_length.legend(fontsize=fontsize_legend,loc=0)
ax_length.spines['top'].set_visible(False)
ax_length.spines['right'].set_visible(False)
ax_length.set_xlim(data_length.index[0],data_length.index[-1])
fig_length.suptitle('大小盘占优持续时间',fontsize=fontsize_suptitle,fontweight='bold')
fig_length.tight_layout()
fig_length.savefig(fp_target+'\\大小盘占优持续时间.png')
plt.close()
t=printtime(t)
