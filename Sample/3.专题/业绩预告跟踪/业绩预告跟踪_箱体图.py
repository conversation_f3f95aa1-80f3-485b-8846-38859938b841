# fp = r'D:\工作文件\中观研究\业绩预告跟踪'
# import sys
# sys.path.append(fp)
# from BasicFun1 import *
# from BasicFun2 import *
from Basicfun import *
print(os.path.basename(__file__))
#%%读取基础文件：聚类行业口径
windconn = cx_Oracle.connect('wind_read/Wind_read_100010@**************:1521/wind')
#goalconn = pymssql.connect(host="**************", user="zyyxread", \
#                       password="zyyx2014", database="ZYYX",charset="utf8")
goalconn = cx_Oracle.connect('zxread/Zxre70_#60#d@**************:1523/zxdb')
hylist = pd.read_excel(fp+'\\Sample\\业绩预告跟踪\\申万行业指数.xlsx',sheet_name='最终表',index_col=0)

#%%日期设定
rpt_date = '20240331' #选取想要的当前财报日（季末）
startDate = '20060101' #起始日，画箱体图用(不能太早，否则申万二级三级行业全是NAN值)
endDate = '20240425' #结束日，一般选当天（不用T-1，因为想要当天的业绩预告数据）

#%%提取和预处理业绩预告数据

#业绩预告
sql = ("select a.S_INFO_WINDCODE,S_PROFITNOTICE_DATE,S_PROFITNOTICE_PERIOD, "
       "S_PROFITNOTICE_CHANGEMIN,S_PROFITNOTICE_CHANGEMAX,S_PROFITNOTICE_NET_PARENT_FIRM, "
       "b.S_INFO_LISTDATE "
       "from winddf.AShareProfitNotice a,  winddf.AShareDescription b "
       "where a.S_INFO_WINDCODE = b.S_INFO_WINDCODE "
       " and a.S_PROFITNOTICE_PERIOD>b.S_INFO_LISTDATE " )
profitnotice = pd.read_sql(sql,windconn)#单位：万元

profitnotice.columns = ['股票代码','发布日','财报期','预告净利润变动幅度下限','预告净利润变动幅度上限','上年同期归母净利润','股票上市日期']

#平均
profitnotice['归母净利润预告同比变化均值'] = (profitnotice['预告净利润变动幅度下限']+profitnotice['预告净利润变动幅度上限'])/200

#只保留当期数据
profitnotice = profitnotice[profitnotice['财报期']==rpt_date]

#profitnotice = profitnotice[profitnotice['发布日']>=rpt_date]
profitnotice = profitnotice[profitnotice['发布日']<=endDate]
profitnotice = profitnotice.dropna()

profitnotice.sort_values(by=['股票代码','财报期','发布日'],inplace=True,ignore_index=True)
profitnotice.set_index('股票代码',inplace=True)

t = printtime(t)

#%%提取自由流通市值数据

#---补充自由流通市值数据（约3000万数据，自由流通股本*收盘价）
print('开始补充流通市值数据')
try: #读取当期已有数据
    stock_close_read = pd.read_csv(fp+'\\Sample\\业绩预告跟踪\\自由流通市值_最新.csv',index_col=0)
except Exception:
    stock_close_read = pd.read_csv(fp+'\\Sample\\业绩预告跟踪\\自由流通市值20230531.csv',index_col=0)

#读取最新上市股票数据（横向补充股票）
listedstk = getlistedstkfromwind(parse(endDate).date(),windconn,listd=180,delistd=90)
len(listedstk)
#自由流通市值填充函数
def stock_data_fill(stock_close_read,listedstk,endDate):
    #从数据库提取新数据_填补横向，即新增股票的过往数据
    data_t0 = list(stock_close_read.index)[0]
    data_t1 = list(stock_close_read.index)[-1]
    stocklist_close = list(set(listedstk)-set(stock_close_read.columns))
    if len(stocklist_close)==0:
        stock_close1 = stock_close_read.copy()
    else:
        sql = """select S_INFO_WINDCODE,TRADE_DT,S_DQ_CLOSE_TODAY,FREE_SHARES_TODAY \
        from winddf.AShareEODDerivativeIndicator \
        where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {}) \
        order by TRADE_DT,S_INFO_WINDCODE""".format(tuple(stocklist_close),data_t0,data_t1)
        df_close1 = pd.read_sql(sql,windconn)
        df_close1['freemv'] = df_close1['FREE_SHARES_TODAY']*df_close1['S_DQ_CLOSE_TODAY']#自由流通市值：万元
        df_close1 = df_close1[['S_INFO_WINDCODE','TRADE_DT','freemv']]
        df_close1.columns = ['stock','date','freemv']
        df_close1['date'] = df_close1['date'].astype('int')
        df_close1 = df_close1.pivot(index='date',columns='stock',values='freemv').ffill()

        stock_close1 = stock_close_read.merge(df_close1, left_on='date', right_on='date', how='left')
    #从数据库提取新数据_填补纵向，即所有股票的近期数据
    output = pd.DataFrame()
    WindCode = stock_close1.columns
    for i in range(int(len(WindCode)/1000)+1):#由于数据库取数限制，故只能按1000条循环读取
        print(i+'组自由流通市值补充')
        sql = """select S_INFO_WINDCODE,TRADE_DT,S_DQ_CLOSE_TODAY,FREE_SHARES_TODAY \
        from winddf.AShareEODDerivativeIndicator \
        where (S_INFO_WINDCODE in {}) and (TRADE_DT between {} and {}) \
        order by TRADE_DT,S_INFO_WINDCODE""".format(tuple(WindCode[i*1000:(i+1)*1000]),data_t1,endDate)
        df = pd.read_sql(sql,windconn)
        output = pd.concat([output,df],axis=0)
    stock_close2 = output.copy()
    stock_close2['freemv'] = stock_close2['FREE_SHARES_TODAY']*stock_close2['S_DQ_CLOSE_TODAY']
    stock_close2 = stock_close2[['S_INFO_WINDCODE','TRADE_DT','freemv']]
    stock_close2.columns = ['stock','date','freemv']
    stock_close2['date'] = stock_close2['date'].astype('int')
    stock_close2 = stock_close2.pivot(index='date',columns='stock',values='freemv').ffill()

    stock_close3 = pd.DataFrame(columns=stock_close1.columns)
    stock_close3[stock_close2.columns] = stock_close2

    stock_close_new = stock_close1.append(stock_close3)
    return stock_close_new

stkfreemv_mean = stock_data_fill(stock_close_read,listedstk,endDate)
stkfreemv_mean = stkfreemv_mean[~stkfreemv_mean.index.duplicated()]
stkfreemv_mean.to_csv(fp+'\\Sample\\业绩预告跟踪\\自由流通市值_最新.csv',encoding='utf-8',index=True,index_label='date')
print('流通市值提取完毕')
t = printtime(t)
#%%计算75个行业的披露数量和披露率、预告增速等核心指标
print('开始合并行业数据')
sqlstr = '''select s_info_windcode, S_CON_WINDCODE,S_CON_INDATE,S_CON_OUTDATE \
from winddf.SWIndexMembers order by s_info_windcode''' #包含了申万一二三级所有行业
class1 = pd.read_sql(sqlstr,windconn)

#填补现在日期,日期格式改变成数字
class1 = class1.where(class1.notnull(),datetime.now().strftime("%Y%m%d"))
class1['S_CON_INDATE'] = class1['S_CON_INDATE'].apply(lambda x:int(x))
class1['S_CON_OUTDATE'] = class1['S_CON_OUTDATE'].apply(lambda x:int(x))

#定义成分股筛选函数
def toline(df): #辅助函数，多行合并成一行
    return ','.join(df.values)

def con_indus(class1,tm):
    df = class1.copy()
    tm = int(tm)
    df.loc[(df['S_CON_INDATE']<tm)&(df['S_CON_OUTDATE']>=tm),'当期标记'] = 1
    df = df[df['当期标记']==1]
    df['date'] = tm
    df = df[['S_INFO_WINDCODE','S_CON_WINDCODE','date']]
    df = df.groupby(['S_INFO_WINDCODE','date']).agg(toline).reset_index()
    return df

all_con_indus = pd.DataFrame()
temp = con_indus(class1,endDate)
all_con_indus = all_con_indus.append(temp)

all_con_indus['date'] = all_con_indus['date'].apply(lambda x:str(x))   

#只提取新的细分行业口径
all_con_indus = all_con_indus[all_con_indus['S_INFO_WINDCODE'].isin(hylist['行业代码'])]
all_con_indus = all_con_indus.set_index(['S_INFO_WINDCODE','date']) #调整index便于索引行业的披露个数和披露率

#计算每个行业数据
res_hy = pd.DataFrame(index=all_con_indus.index,columns=['披露数量','行业股票总数','披露率','中位数增速','自由流通市值加权增速'])
ann_date = endDate
for code in hylist['行业代码'].tolist():
    if (code,ann_date) in all_con_indus.index:
        stocklist = all_con_indus.loc[(code,ann_date)][0].split(',')
        temp = pd.DataFrame()
        for i in stocklist:
            if i in profitnotice.index:
                line0 = profitnotice.loc[i]
                if type(line0) == pd.core.frame.DataFrame:
                    line = line0
                else:
                    line = line0.to_frame().T
                line = line.loc[line['财报期']==rpt_date]
                line = line.loc[line['发布日']<=ann_date]
                if len(line) == 0:
                    pass
                else:
                    temp = temp.append(line)
                if len(temp) == 0 :
                    print('该行业当期没有股票适当披露')
                else:
                    temp['披露数量'] = len(temp)
                    temp['行业股票总数'] = len(stocklist)
                    temp['披露率'] = temp['披露数量']/temp['行业股票总数']                   
                    stocklist2 = list(set(stocklist)&set(stkfreemv_mean.columns))
                    temp = temp[temp.index.isin(stocklist2)]
                    temp['自由流通市值'] = stkfreemv_mean.loc[int(ann_date),temp.index.tolist()]
                    temp['freemv_w'] = temp['自由流通市值']/temp['自由流通市值'].sum()
                    temp['自由流通市值加权增速'] = temp['归母净利润预告同比变化均值']*temp['freemv_w']
                    median_value = temp['归母净利润预告同比变化均值'].median()
                    jiaquan_value = temp['自由流通市值加权增速'].sum()
                    pilulv = temp['披露率'].mean()
                    res_hy.loc[(code,ann_date),'披露数量'] = temp['披露数量'].mean()
                    res_hy.loc[(code,ann_date),'行业股票总数'] = temp['行业股票总数'].mean()
                    res_hy.loc[(code,ann_date),'披露率'] = pilulv
                    res_hy.loc[(code,ann_date),'中位数增速'] = median_value
                    res_hy.loc[(code,ann_date),'自由流通市值加权增速'] = jiaquan_value
t=printtime(t)                       
#%%整理和存储算好的业绩预告行业整合数据
res_hy2 = res_hy.reset_index()

res_hy2 = pd.merge(res_hy2,hylist[['行业代码','行业名称']],left_on='S_INFO_WINDCODE',right_on='行业代码',how='left')

res_hy2 = res_hy2[['行业代码','行业名称','date','披露数量','行业股票总数','披露率','中位数增速','自由流通市值加权增速']]

res_hy2 = res_hy2.sort_values(by=['中位数增速','披露率'],ignore_index=True,ascending=False)
res_hy3 = res_hy2.sort_values(by=['自由流通市值加权增速','披露率'],ignore_index=True,ascending=False) #重新排序，画图用

res_hy2.to_excel(fp+'\\Sample\\业绩预告跟踪\\result\\行业业绩预告%s期截至%s增速跟踪.xlsx'%(rpt_date,endDate),encoding='utf-8',index=True)

#%%补充观察底表（每个行业哪些个股披露了，数据如何）

class2 = pd.merge(class1,hylist,left_on='S_INFO_WINDCODE',right_on='行业代码',how='left')
class2 = class2[['S_CON_WINDCODE','S_INFO_WINDCODE','行业名称','S_CON_INDATE','S_CON_OUTDATE']]
class2.columns = ['股票代码','行业代码','行业名称','调入日','调出日']
class2 = class2.sort_values(by=['股票代码','调入日','调出日'])
class2 = class2.dropna()
class2 = class2[class2['调出日'] == int(endDate)]
class2.set_index('股票代码',inplace=True)

profitnotice2 = pd.merge(profitnotice,class2,left_index=True,right_index=True)
profitnotice2 = profitnotice2.sort_values(by='行业名称')

fabu_date = profitnotice2.groupby('发布日').count()
fabu_date.to_excel(fp+'\\Sample\\业绩预告跟踪\\result\\行业业绩预告%s期截至%s发布情况跟踪.xlsx'%(rpt_date,endDate),encoding='utf-8')
profitnotice2.to_excel(fp+'\\Sample\\业绩预告跟踪\\result\\个股业绩预告%s期截至%s披露详情.xlsx'%(rpt_date,endDate),encoding='utf-8')

#%%补充75个行业startDate以来的季度归母净利润同比数据

hisyoy = w.wsd(hylist.index.tolist(), "yoynetprofit", startDate, endDate, "Period=Q;Days=Alldays",usedf=True)[1]
hisyoy = hisyoy.dropna(how='any',axis=0)
hisyoy.columns = hylist.loc[hisyoy.columns,'行业名称'].tolist()
hisyoy = hisyoy/100
hisyoy.index = pd.DatetimeIndex(hisyoy.index)
append_data1 = res_hy2[['行业名称','披露率','中位数增速','自由流通市值加权增速']].T
append_data1.columns = np.array(append_data1.loc['行业名称']).tolist()
append_data1 = append_data1.iloc[1:,:]
append_data1 = append_data1[hisyoy.columns]

#%%画箱体图
fig_fundamental_quant,ax_fundamental_quant=plt.subplots(nrows=2,ncols=1)   

ax_fundamental_quant[0].boxplot(hisyoy, showfliers=False, vert=True, patch_artist=False,showmeans=False,labels=hisyoy.columns.tolist())
ax_fundamental_quant[0].scatter(append_data1.columns,append_data1.loc['中位数增速'],marker='*',color='red')
ax_fundamental_quant[0].spines['top'].set_visible(False)
ax_fundamental_quant[0].spines['right'].set_visible(False)
ax_fundamental_quant[0].tick_params(axis='x', direction='out', rotation=45)
ax_fundamental_quant[0].grid(axis='y',alpha=0.3)
ax_fundamental_quant[0].set_ylabel('归母净利润增速')
ax2 = ax_fundamental_quant[0].twinx()
ax2.scatter(append_data1.columns,append_data1.loc['披露率'],marker='o',color='green',label='披露率')
plt.legend()
ax2.set_ylabel('业绩预告披露率')
title_text1 = '中位数法：'+\
    '\n预告利润增速前5行业：'+'、'.join(res_hy2.iloc[0:5,1])
ax_fundamental_quant[0].set_title(title_text1,color='gray',fontsize=11)
ax_fundamental_quant[1].boxplot(hisyoy, showfliers=False, vert=True, patch_artist=False,showmeans=False,labels=hisyoy.columns.tolist())
ax_fundamental_quant[1].scatter(append_data1.columns,append_data1.loc['自由流通市值加权增速'],marker='*',color='red')
ax_fundamental_quant[1].spines['top'].set_visible(False)
ax_fundamental_quant[1].spines['right'].set_visible(False)
ax_fundamental_quant[1].tick_params(axis='x', direction='out', rotation=45)
ax_fundamental_quant[1].grid(axis='y',alpha=0.3)
ax_fundamental_quant[1].set_ylabel('归母净利润增速')
ax2 = ax_fundamental_quant[1].twinx()
ax2.scatter(append_data1.columns,append_data1.loc['披露率'],marker='o',color='green',label='披露率')
plt.legend()
ax2.set_ylabel('业绩预告披露率')
title_text2 = '自由流通市值加权法：'+\
    '\n预告利润增速前5行业：'+'、'.join(res_hy3.iloc[0:5,1])  
ax_fundamental_quant[1].set_title(title_text2,color='gray',fontsize=11)      
fig_fundamental_quant.suptitle('申万75个行业%s业绩预告跟踪（净利润同比,截至%s）'%(rpt_date,endDate)+'\n(★代表本期)',fontsize=18,fontweight='bold')
fig_fundamental_quant.tight_layout()
fig_fundamental_quant.savefig(fp+'\\Sample\\业绩预告跟踪\\result\\申万75个行业%s业绩预告跟踪（净利润同比,截至%s）'%(rpt_date,endDate)+'.png')    
