<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="D:\New\Python\Code\assets/echarts.min.js"></script>
        <script type="text/javascript" src="D:\New\Python\Code\assets/maps/world.js"></script>

</head>
<body>
    <div id="6e9356bdcbd74961ae06ba40abe39cfb" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_6e9356bdcbd74961ae06ba40abe39cfb = echarts.init(
            document.getElementById('6e9356bdcbd74961ae06ba40abe39cfb'), 'white', {renderer: 'canvas'});
        var option_6e9356bdcbd74961ae06ba40abe39cfb = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "United States",
                    "value": 3.4
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Japan",
                    "value": 2.6
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Korea",
                    "value": 3.16
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "India",
                    "value": 5.6915
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Australia",
                    "value": 4.1
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "United Kingdom",
                    "value": 4.0
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "France",
                    "value": 3.7
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Germany",
                    "value": 3.7
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Italy",
                    "value": 0.6
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Russia",
                    "value": 7.42
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Turkey",
                    "value": 64.77
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "South Africa",
                    "value": 5.21
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Canada",
                    "value": 3.4
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Brazil",
                    "value": 4.62
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Argentina",
                    "value": 210.1
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Saudi Arabia",
                    "value": 1.492675
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Vietnam",
                    "value": 3.581011
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u901a\u80c0\u6c34\u5e73",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "China",
                    "value": -0.3
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        }
    ],
    "legend": [
        {
            "data": [
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73",
                "\u901a\u80c0\u6c34\u5e73"
            ],
            "selected": {
                "\u901a\u80c0\u6c34\u5e73": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "\u5168\u74032023\u5e74CPI(%)",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 0,
        "max": 3,
        "inRange": {
            "color": [
                "green",
                "white",
                "red"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "orient": "vertical",
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    }
};
        chart_6e9356bdcbd74961ae06ba40abe39cfb.setOption(option_6e9356bdcbd74961ae06ba40abe39cfb);
    </script>
</body>
</html>
