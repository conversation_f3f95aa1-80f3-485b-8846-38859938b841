<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="D:\New\Python\Code\assets/echarts.min.js"></script>
        <script type="text/javascript" src="D:\New\Python\Code\assets/maps/world.js"></script>

</head>
<body>
    <div id="93c3e9134dae431f8b9b51f67ca08f37" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_93c3e9134dae431f8b9b51f67ca08f37 = echarts.init(
            document.getElementById('93c3e9134dae431f8b9b51f67ca08f37'), 'white', {renderer: 'canvas'});
        var option_93c3e9134dae431f8b9b51f67ca08f37 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Italy",
                    "value": 26.28
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Russia",
                    "value": 43.87
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "United States",
                    "value": 24.23
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Canada",
                    "value": 8.12
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Brazil",
                    "value": 22.28
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Argentina",
                    "value": 360.06
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Mexico",
                    "value": 18.41
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "United Kingdom",
                    "value": 3.78
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "France",
                    "value": 16.52
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Germany",
                    "value": 20.31
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Spain",
                    "value": 22.76
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Switzerland",
                    "value": 3.81
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Portugal",
                    "value": 7.05
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Ireland",
                    "value": 23.23
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Netherlands",
                    "value": 14.2
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Belgium",
                    "value": 0.18
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Denmark",
                    "value": 24.42
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Norway",
                    "value": 11.49
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Sweden",
                    "value": 17.26
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Austria",
                    "value": 9.87
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Greece",
                    "value": 39.08
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Ukraine",
                    "value": 7.65
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Egypt",
                    "value": 70.1
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Nigeria",
                    "value": 45.9
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Israel",
                    "value": 4.07
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Saudi Arabia",
                    "value": 14.21
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "United Arab Emirates",
                    "value": 21.69
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Japan",
                    "value": 28.24
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Korea",
                    "value": 18.73
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Singapore Rep.",
                    "value": -0.34
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "India",
                    "value": 18.74
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Australia",
                    "value": 7.84
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "New Zealand",
                    "value": 2.59
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Indonesia",
                    "value": 6.16
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Malaysia",
                    "value": -1.71
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Vietnam",
                    "value": 12.2
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "Korea",
                    "value": 22.98
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        },
        {
            "type": "map",
            "name": "\u6da8\u8dcc\u5e45",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}"
            },
            "mapType": "world",
            "data": [
                {
                    "name": "China",
                    "value": -10.37
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "mapValueCalculation": "sum",
            "showLegendSymbol": false,
            "emphasis": {}
        }
    ],
    "legend": [
        {
            "data": [
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45",
                "\u6da8\u8dcc\u5e45"
            ],
            "selected": {
                "\u6da8\u8dcc\u5e45": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "\u5168\u7403\u80a1\u7968\u6307\u65702023\u5e74\u6da8\u8dcc\u5e45(%)",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": -16,
        "max": 16,
        "inRange": {
            "color": [
                "green",
                "white",
                "red"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "orient": "vertical",
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    }
};
        chart_93c3e9134dae431f8b9b51f67ca08f37.setOption(option_93c3e9134dae431f8b9b51f67ca08f37);
    </script>
</body>
</html>
