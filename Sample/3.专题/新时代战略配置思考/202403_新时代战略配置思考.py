# import sys
# import os
# parent_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# if parent_directory not in sys.path:
#     sys.path.append(parent_directory)
from Basicfun import *
print(os.path.basename(__file__))

#%%
data_pct_insure=pd.read_excel(os.path.join(fp,'Sample','新时代战略配置思考','(野村东方国际证券)定制课题：日本寿险资管投资策略复盘.xlsx'),sheet_name='2 12 13',index_col=0,header=0,usecols='L:S').dropna(how='all',axis=0)
data_pct_insure.columns=[i.replace('.1','') for i in data_pct_insure.columns]
data_pct_insure.index=[dt.date(int(i),12,31) for i in data_pct_insure.index]
data_pct_insure.index=data_pct_insure.index.astype('datetime64[ns]')
data_pct_insure['国内债券']=data_pct_insure['国债']+data_pct_insure['其他债券']
data_pct_insure['国内股票']=data_pct_insure['股票']
data_pct_insure['国内贷款']=data_pct_insure['贷款']
data_pct_insure['国外证券(股+债)']=data_pct_insure['海外证券']
data_pct_insure=data_pct_insure.T.reindex(['国内股票','国内债券','国内贷款','国外证券(股+债)','现金存款','其他']).T
data_pct_insure=data_pct_insure.divide(data_pct_insure.sum(axis=1), axis=0)
data_index=w.wsd("N225.GI","close","1980-01-01",enddate,"Period=Y;Days=Alldays",usedf=True)[1]
data_index.columns=['日经225指数']
data_index.index=data_index.index.astype('datetime64[ns]')
data_gby=w.edb("G1217160","1980-01-01",enddate,"Fill=Previous",usedf=True)[1]
data_gby.columns=['日本10年期国债收益率']
data_gby.index=data_gby.index.astype('datetime64[ns]')
data_gby=data_gby.resample('Y').last()
data_exchange=w.wsd("USDJPY.FX",'close',"1980-01-01",enddate,"Fill=Previous",period='Y',usedf=True)[1]
data_exchange.columns=['美元兑日元汇率']
data_exchange.index=data_exchange.index.astype('datetime64[ns]')
'''
#%%日本寿险行业资产配置演变
fig_insure,ax_insure=plt.subplots(figsize=(20,7))
ax1_insure=ax_insure.twinx()
ax2_insure=ax_insure.twinx()
ax3_insure=ax_insure.twinx()
ax_insure.stackplot(data_pct_insure.index, data_pct_insure.T.values, labels=data_pct_insure.columns.tolist(),colors=code_color[:data_pct_insure.shape[1]], alpha=light)
ax_insure.tick_params(axis='x', direction='out')
ax1_insure.plot(data_index.index,data_index,label=data_index.columns[0]+'(右1轴)',color='blue',lw=2)
ax1_insure.spines['right'].set_color('blue')
ax1_insure.tick_params(axis='y', direction='out',color='blue',labelcolor='blue')
ax2_insure.plot(data_gby.index,data_gby,label=data_gby.columns[0]+'(右2轴)',color='red',lw=2)
ax2_insure.spines['right'].set_color('red')
ax2_insure.tick_params(axis='y', direction='out',color='red',labelcolor='red')
ax2_insure.spines['right'].set_position(('axes',1.05))
ax3_insure.plot(data_exchange.index,data_exchange,label=data_exchange.columns[0]+'(右3轴)',color='orange',lw=2)
ax3_insure.spines['right'].set_color('orange')
ax3_insure.tick_params(axis='y', direction='out',color='orange',labelcolor='orange')
ax3_insure.spines['right'].set_position(('axes',1.1))
ax_insure.set_xlim(data_pct_insure.index[0],)
ax_insure.set_ylim(0,1)
ax_insure.yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos: '{:.1%}'.format(x)))
ax_insure.legend(loc=9,ncols=data_pct_insure.shape[1],fontsize=fontsize_legend)
ax1_insure.legend(loc=1,fontsize=fontsize_legend)
ax2_insure.legend(loc=4,fontsize=fontsize_legend)
ax3_insure.legend(loc=5,fontsize=fontsize_legend)
ax_insure.set_xlabel('日期(年份)')
ax1_insure.set_ylabel(data_index.columns[0],color='blue')
ax2_insure.set_ylabel(data_gby.columns[0]+'(%)',color='red')
ax3_insure.set_ylabel(data_exchange.columns[0],color='orange')
ax_insure.set_ylabel('资产配置比例',va='center')
fig_insure.suptitle('日本寿险行业资产配置演变(1980-2022)',fontsize=fontsize_suptitle,fontweight='bold')
fig_insure.tight_layout()
fig_insure.savefig(os.path.join(fp,'Sample','新时代战略配置思考','日本寿险行业资产配置演变.png'))
plt.close()

#%%日本政府养老投资基金(GPIF)资产配置演变
data_pct_gpif=pd.read_excel(os.path.join(fp,'Sample','新时代战略配置思考','GPIF大类资产配置.xlsx'),sheet_name='2',index_col=0,header=0,usecols='A:F').dropna(how='all',axis=0)
data_pct_gpif.index=[dt.date(int(str(i)[:4]),int(str(i)[5:]),30) for i in data_pct_gpif.index]
data_pct_gpif.index=data_pct_gpif.index.astype('datetime64[ns]')
data_pct_gpif['国外股票']=data_pct_gpif['外国股票']
data_pct_gpif['国外债券']=data_pct_gpif['外国债券']
data_pct_gpif=data_pct_gpif.T.reindex(['国内股票','国内债券','国外股票','国外债券','短期资产']).T

fig_gpif,ax_gpif=plt.subplots(figsize=(20,7))
ax1_gpif=ax_gpif.twinx()
ax2_gpif=ax_gpif.twinx()
ax3_gpif=ax_gpif.twinx()
ax_gpif.stackplot(data_pct_gpif.index, data_pct_gpif.T.values, labels=data_pct_gpif.columns.tolist(),colors=code_color[:data_pct_gpif.shape[1]], alpha=light)
ax_gpif.tick_params(axis='x', direction='out')
ax1_gpif.plot(data_index.index,data_index,label=data_index.columns[0]+'(右1轴)',color='blue',lw=2)
ax1_gpif.spines['right'].set_color('blue')
ax1_gpif.tick_params(axis='y', direction='out',color='blue',labelcolor='blue')
ax2_gpif.plot(data_gby.index,data_gby,label=data_gby.columns[0]+'(右2轴)',color='red',lw=2)
ax2_gpif.spines['right'].set_color('red')
ax2_gpif.tick_params(axis='y', direction='out',color='red',labelcolor='red')
ax2_gpif.spines['right'].set_position(('axes',1.05))
ax3_gpif.plot(data_exchange.index,data_exchange,label=data_exchange.columns[0]+'(右3轴)',color='orange',lw=2)
ax3_gpif.spines['right'].set_color('orange')
ax3_gpif.tick_params(axis='y', direction='out',color='orange',labelcolor='orange')
ax3_gpif.spines['right'].set_position(('axes',1.1))
ax_gpif.set_xlim(data_pct_gpif.index[0],)
ax_gpif.set_ylim(0,1)
ax_gpif.yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos: '{:.1%}'.format(x)))
ax_gpif.legend(loc=9,ncols=data_pct_gpif.shape[1],fontsize=fontsize_legend)
ax1_gpif.legend(loc=1,fontsize=fontsize_legend)
ax2_gpif.legend(loc=4,fontsize=fontsize_legend)
ax3_gpif.legend(loc=5,fontsize=fontsize_legend)
ax_gpif.set_xlabel('日期(年份)')
ax1_gpif.set_ylabel(data_index.columns[0],color='blue')
ax2_gpif.set_ylabel(data_gby.columns[0]+'(%)',color='red')
ax3_gpif.set_ylabel(data_exchange.columns[0],color='orange')
ax_gpif.set_ylabel('资产配置比例')
fig_gpif.suptitle('日本政府养老投资基金(GPIF)资产配置演变(2002-2023)',fontsize=fontsize_suptitle,fontweight='bold')
fig_gpif.tight_layout()
fig_gpif.savefig(os.path.join(fp,'Sample','新时代战略配置思考','日本政府养老投资基金(GPIF)资产配置演变.png'))
plt.close()

t=printtime(t)

#%%挪威主权财富基金(NBIM)资产配置演变
data_us=w.wsd("SPX.GI,UST10Y.GBM,USDX.FX","close","1990-01-01",enddate,"Period=Y;Days=Alldays",usedf=True)[1]
data_us.columns=['标普500指数','美国10年期国债收益率','美元指数']
data_us.index=data_us.index.astype('datetime64[ns]')
data_pct_nbim=pd.read_excel(os.path.join(fp,'Sample','新时代战略配置思考','挪威主权财富基金资产配置.xlsx'),sheet_name='历史大类资产配置权益细分占比',index_col=0,header=0,usecols='A:G').dropna(how='all',axis=0)
data_pct_nbim.index=[dt.date(i,12,31) for i in data_pct_nbim.index]
data_pct_nbim.index=data_pct_nbim.index.astype('datetime64[ns]')

fig_nbim,ax_nbim=plt.subplots(figsize=(20,7))
ax1_nbim=ax_nbim.twinx()
ax2_nbim=ax_nbim.twinx()
ax3_nbim=ax_nbim.twinx()
ax_nbim.stackplot(data_pct_nbim.index, data_pct_nbim.T.values, labels=data_pct_nbim.columns.tolist(),colors=code_color[:data_pct_nbim.shape[1]], alpha=light)
ax_nbim.tick_params(axis='x', direction='out')
ax1_nbim.plot(data_us.index,data_us.iloc[:,0],label=data_us.columns[0]+'(右1轴)',color='blue',lw=2)
ax1_nbim.spines['right'].set_color('blue')
ax1_nbim.tick_params(axis='y', direction='out',color='blue',labelcolor='blue')
ax2_nbim.plot(data_us.index,data_us.iloc[:,1],label=data_us.columns[1]+'(右2轴)',color='red',lw=2)
ax2_nbim.spines['right'].set_color('red')
ax2_nbim.tick_params(axis='y', direction='out',color='red',labelcolor='red')
ax2_nbim.spines['right'].set_position(('axes',1.05))
ax3_nbim.plot(data_us.index,data_us.iloc[:,2],label=data_us.columns[2]+'(右3轴)',color='orange',lw=2)
ax3_nbim.spines['right'].set_color('orange')
ax3_nbim.tick_params(axis='y', direction='out',color='orange',labelcolor='orange')
ax3_nbim.spines['right'].set_position(('axes',1.1))
ax_nbim.set_xlim(data_pct_nbim.index[0],)
ax_nbim.set_ylim(0,1)
ax_nbim.yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos: '{:.1%}'.format(x)))
ax_nbim.legend(loc=9,ncols=data_pct_nbim.shape[1],fontsize=fontsize_legend)
ax1_nbim.legend(loc=1,fontsize=fontsize_legend)
ax2_nbim.legend(loc=4,fontsize=fontsize_legend)
ax3_nbim.legend(loc=5,fontsize=fontsize_legend)
ax_nbim.set_xlabel('日期(年份)')
ax1_nbim.set_ylabel(data_us.columns[0],color='blue')
ax2_nbim.set_ylabel(data_us.columns[1]+'(%)',color='red')
ax3_nbim.set_ylabel(data_us.columns[2],color='orange')
ax_nbim.set_ylabel('资产配置比例')
fig_nbim.suptitle('挪威主权财富基金(NBIM)资产配置演变(1998-2022)',fontsize=fontsize_suptitle,fontweight='bold')
fig_nbim.tight_layout()
fig_nbim.savefig(os.path.join(fp,'Sample','新时代战略配置思考','挪威主权财富基金(NBIM)资产配置演变.png'))
plt.close()

t=printtime(t)

#%%全球通胀水平
from pyecharts.globals import CurrentConfig
CurrentConfig.ONLINE_HOST=os.path.join(fp,'Code','assets/')
from pyecharts.charts import Map
import pyecharts.options as opts
from pyecharts.render import make_snapshot
from snapshot_selenium import snapshot

# 创建示例数据框
data_stock=pd.read_excel(os.path.join(fp,'Sample','新时代战略配置思考','地图.xlsx'),sheet_name='Oversea',index_col=0,header=0).dropna(how='all',axis=0)
pct_stock=w.wss(data_stock.index.tolist(),"pct_chg_per","startDate=20230101;endDate=20231231",usedf=True)[1].round(2)
pct_stock['地区名称']=data_stock['英文国别']
pct_stock['涨跌幅']=pct_stock['PCT_CHG_PER']
pct_stock = pct_stock[['地区名称','涨跌幅']]

data_cpi=pd.read_excel(os.path.join(fp,'Sample','新时代战略配置思考','地图.xlsx'),sheet_name='CPI',index_col=0,header=0).dropna(how='all',axis=0)
pct_cpi=w.edb(data_cpi.index.tolist(),"2023-01-01","2023-12-31","Fill=Previous",usedf=True)[1].T
pct_cpi['通胀水平']=pct_cpi.iloc[:,-1]
pct_cpi['地区名称']=data_cpi['英文国别']
pct_cpi = pct_cpi[['地区名称','通胀水平']]

# 初始化地图
map_ = Map()

# 设置全局配置
map_.set_global_opts(
    title_opts=opts.TitleOpts(title="全球股票指数2023年涨跌幅(%)"),
    visualmap_opts=opts.VisualMapOpts(min_=-16,max_=16,range_color=["green","white", "red"],),
)

# 添加数据，并且仅为DataFrame中的国家设置标签
for country, change in zip(pct_stock["地区名称"], pct_stock["涨跌幅"]):
    map_.add(
        series_name="涨跌幅",
        data_pair=[(country, change)],
        maptype="world",
        is_map_symbol_show=False,
        label_opts=opts.LabelOpts(is_show=False,formatter='{c}')
    )

# 渲染地图到HTML文件
map_.render(os.path.join(fp,'Sample','新时代战略配置思考','全球股市涨跌幅.html'))
make_snapshot(snapshot,os.path.join(fp,'Sample','新时代战略配置思考','全球股市涨跌幅.html'), os.path.join(fp,fp,'Sample','新时代战略配置思考','全球股市涨跌幅.png'))

# 初始化地图
map_ = Map()

# 设置全局配置
map_.set_global_opts(
    title_opts=opts.TitleOpts(title="全球2023年CPI(%)"),
    visualmap_opts=opts.VisualMapOpts(min_=0,max_=3,range_color=["green","white", "red"],),
)

# 添加数据，并且仅为DataFrame中的国家设置标签
for country, change in zip(pct_cpi["地区名称"], pct_cpi["通胀水平"]):
    map_.add(
        series_name="通胀水平",
        data_pair=[(country, change)],
        maptype="world",
        is_map_symbol_show=False,
        label_opts=opts.LabelOpts(is_show=False,formatter='{c}')
    )

# 渲染地图到HTML文件
map_.render(os.path.join(fp,'Sample','新时代战略配置思考','全球通胀水平.html'))
make_snapshot(snapshot,os.path.join(fp,'Sample','新时代战略配置思考','全球通胀水平.html'), os.path.join(fp,'Sample','新时代战略配置思考','全球通胀水平.png'))

t=printtime(t)

#%%主要国家股市相关系数
name_equity=['沪深300','标普500','欧洲斯托克50','日经225','印度SS30','越南指数','巴西指数']
code_equity=[market_index[i] for i in name_equity]
data_equity=w.wsd(code_equity,"close","2015-01-01",enddate,"Period=M;Days=Alldays",usedf=True)[1]
data_equity.columns=name_equity
corr_1521=data_equity[data_equity.index<=dt.date(2021,9,30)].corr()
corr_1521=pd.concat([corr_1521.replace(1,np.nan).median().rename('中位数'),corr_1521],axis=1).round(2)
corr_2124=data_equity[data_equity.index>=dt.date(2021,9,30)].corr()
corr_2124=pd.concat([corr_2124.replace(1,np.nan).median().rename('中位数'),corr_2124],axis=1).round(2)
corr_all=[corr_1521,corr_2124]
corr_title_name=['主要国家股市相关系数(2015-2021)','主要国家股市相关系数(2021-2024)']
for g in range(2):
    fig_table_corr, ax_table_corr = plt.subplots(ncols=1, nrows=1,figsize=(10,4.5),dpi=200)
    color_corr = deepcopy(corr_all[g])
    color_corr.loc[:, :] = np.nan
    colColours = ['lightsteelblue'] * len(color_corr.columns)
    for m in range(len(color_corr.index)):
        if m in list(range(1, len(corr_all[g].index), 2)):
            color_corr.loc[color_corr.index[m], :] = 'lightgray'
        else:
            color_corr.loc[color_corr.index[m], :] = 'white'
    table_corr = ax_table_corr.table(cellText=corr_all[g].values, rowLabels=corr_all[g].index, colLabels=corr_all[g].columns,
                                bbox=(0, 0, 1, 1), cellLoc='center', rowLoc='center', colLoc='center',
                                cellColours=color_corr.values, rowColours=color_corr.iloc[:,0].values, colColours=colColours)
    table_corr.auto_set_font_size(False)
    for (row, col), cell in table_corr.get_celld().items():
        if (row in [0]) | (col in [-1]):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text + 2))
        else:
            cell.set_text_props(fontproperties=FontProperties(size=fontsize_text + 2))
        if (corr_all[g].iloc[row - 1, col]<0) and (row >= 1 and col >= 0):
            cell.get_text().set_color('red')
    ax_table_corr.axvline(x=(1/corr_all[g].shape[1]), lw=1.5, ls='-', c='blue')  # 横线
    ax_table_corr.set_axis_off()
    fig_table_corr.suptitle(corr_title_name[g],fontsize=fontsize_suptitle, fontweight='bold')
    fig_table_corr.tight_layout()
    fig_table_corr.savefig(os.path.join(fp,'Sample','新时代战略配置思考',corr_title_name[g]+'.png'))
    plt.close()

#%%中国股市与全球主要国家股市相关系数变化趋势
corr_list=data_equity.iloc[:,0].rolling(window=12).corr(data_equity.iloc[:,1:]).dropna().median(axis=1)
fig_corr,ax_corr=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(10,9))  #画多图
ax_corr.plot(corr_list.index,corr_list,label='滚动相关系数(年，中位数)',color='blue')
ax_corr.plot(corr_list.index[corr_list.index<=dt.date(2021,9,30)],[corr_list.loc[corr_list.index[corr_list.index<=dt.date(2021,9,30)]].mean()]*len(corr_list.index[corr_list.index<=dt.date(2021,9,30)]),label='2021年下半年之前平均数',color='orange')
ax_corr.text(corr_list.index[corr_list.index<=dt.date(2021,9,30)][-1],corr_list.loc[corr_list.index[corr_list.index<=dt.date(2021,9,30)]].mean(),'%.2f'%(corr_list.loc[corr_list.index[corr_list.index<=dt.date(2021,9,30)]].mean()),color='orange')
ax_corr.plot(corr_list.index[corr_list.index>=dt.date(2021,9,30)],[corr_list.loc[corr_list.index[corr_list.index>=dt.date(2021,9,30)]].mean()]*len(corr_list.index[corr_list.index>=dt.date(2021,9,30)]),label='2021年下半年之后平均数',color='red')
ax_corr.text(corr_list.index[corr_list.index>=dt.date(2021,9,30)][-1],corr_list.loc[corr_list.index[corr_list.index>=dt.date(2021,9,30)]].mean(),'%.2f'%(corr_list.loc[corr_list.index[corr_list.index>=dt.date(2021,9,30)]].mean()),color='red')
ax_corr.axvline(x=dt.date(2021,9,30),color='gray',lw=3)
ax_corr.legend(fontsize=fontsize_legend)
ax_corr.spines['top'].set_visible(False)
ax_corr.spines['right'].set_visible(False)
ax_corr.tick_params(axis='x', direction='out', rotation=45)
ax_corr.set_xlim(corr_list.index[0],corr_list.index[-1])
fig_corr.suptitle('中国股市与全球主要国家股市相关系数变化趋势(北上开通以来)',fontsize=fontsize_suptitle,fontweight='bold',ha='center')
fig_corr.tight_layout()
fig_corr.savefig(os.path.join(fp,'Sample','新时代战略配置思考','中国股市与全球主要国家股市相关系数变化趋势'))
plt.close()

t=printtime(t)

#%%人口结构
data_pop=pd.read_excel(os.path.join(fp,'Sample','新时代战略配置思考','地图.xlsx'),sheet_name='中日人口',index_col=0,header=0).dropna(how='all',axis=0)
pct_pop=w.edb(data_pop.index.tolist(),"1900-01-01","2023-12-31","Fill=Previous",usedf=True)[1]
pct_pop.columns=data_pop['名称']
pop_jp=pct_pop.loc[:,pct_pop.columns.str.contains('日本')]
pop_jp=pop_jp[(pop_jp.index>=dt.date(1960,1,1)) & (pop_jp.index<=dt.date(2020,12,31))]
pop_cn=pct_pop.loc[:,pct_pop.columns.str.contains('中国')]
pop_cn=pd.concat([pop_cn,pd.DataFrame(index=pd.date_range(start='2024-12-31',end='2049-12-31',freq='Y').date)],axis=0)
pop_cn.loc[dt.date(2050,12,31),:]=[60,10,30]
pop_cn=pop_cn.interpolate()
pop_cn=pop_cn[pop_cn.index>=dt.date(1990,1,1)]
color_pop=['orange','red','blue']
title_name=['日本人口结构(1960-2020)','中国人口结构(1990-2050)']
pop_all=[pop_jp,pop_cn]
vspan_date=[[dt.date(1995, 12, 31),dt.date(1997, 12, 31)],[dt.date(2024, 12, 31),dt.date(2026, 12, 31)]]
for k in range(2):
    fig_pop,ax_pop=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(10,9))  #画多图
    for m in range(pop_all[k].shape[1]):
        ax_pop.plot(pop_all[k].index,pop_all[k].loc[:,pop_all[k].columns[m]],label=pop_all[k].columns[m],color=color_pop[m])
        if k==1:
            ax_pop.plot(pop_all[k].index[pop_all[k].index>dt.date(2023, 12, 31)],pop_all[k].loc[pop_all[k].index[pop_all[k].index>dt.date(2023, 12, 31)],pop_all[k].columns[m]],ls='--',lw=3,color='gray')
    ax_pop.axvspan(vspan_date[k][0],vspan_date[k][1], color='gray',alpha=light)  # 横线
    ax_pop.legend(fontsize=fontsize_legend)
    ax_pop.spines['top'].set_visible(False)
    ax_pop.spines['right'].set_visible(False)
    ax_pop.tick_params(axis='x', direction='out', rotation=45)
    ax_pop.set_xlim(pop_all[k].index[0],pop_all[k].index[-1])
    fig_pop.suptitle(title_name[k],fontsize=fontsize_suptitle,fontweight='bold',ha='center')
    fig_pop.tight_layout()
    fig_pop.savefig(os.path.join(fp,'Sample','新时代战略配置思考',title_name[k]))
    plt.close()
t=printtime(t)

#%%全球杠杆率
name_lever_global=['杠杆率_居民部门_美国','杠杆率_居民部门_欧元区','杠杆率_居民部门_日本','杠杆率_居民部门_中国','杠杆率_非金融企业部门_美国','杠杆率_非金融企业部门_欧元区','杠杆率_非金融企业部门_日本','杠杆率_非金融企业部门_中国',
                   '杠杆率_政府部门_美国','杠杆率_政府部门_欧元区','杠杆率_政府部门_日本','杠杆率_政府部门_中国']
code_lever_global=[macro[i] for i in name_lever_global]     #获取代码
data_lever_global=w.edb(code_lever_global, '1992-12-31', enddate,usedf=True)[1]#.fillna(method='ffill')       #获取数据
data_lever_global.columns=name_lever_global
data_lever_global['杠杆率_总体_美国']=data_lever_global['杠杆率_居民部门_美国']+data_lever_global['杠杆率_非金融企业部门_美国']+data_lever_global['杠杆率_政府部门_美国']
data_lever_global['杠杆率_总体_欧元区']=data_lever_global['杠杆率_居民部门_欧元区']+data_lever_global['杠杆率_非金融企业部门_欧元区']+data_lever_global['杠杆率_政府部门_欧元区']
data_lever_global['杠杆率_总体_日本']=data_lever_global['杠杆率_居民部门_日本']+data_lever_global['杠杆率_非金融企业部门_日本']+data_lever_global['杠杆率_政府部门_日本']
data_lever_global['杠杆率_总体_中国']=data_lever_global['杠杆率_居民部门_中国']+data_lever_global['杠杆率_非金融企业部门_中国']+data_lever_global['杠杆率_政府部门_中国']
name_totallever_global=['杠杆率_总体_美国','杠杆率_总体_欧元区','杠杆率_总体_日本','杠杆率_总体_中国']
color_totallever_global=['green','orange','red','blue']
fig_totallever_global,ax_totallever_global=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(10,9))  #画多图
for m in range(len(name_totallever_global)):
    data_m_totallever_global=data_lever_global[name_totallever_global[m]].dropna(how='any')
    ax_totallever_global.plot(data_m_totallever_global.index,data_m_totallever_global,label=name_totallever_global[m],color=color_totallever_global[m])
    ax_totallever_global.text(data_m_totallever_global.index[-1],data_m_totallever_global[-1] if m!=0 else data_m_totallever_global[-1],name_totallever_global[m][7:]+','+str('%.0f'%data_m_totallever_global[-1]),color=color_totallever_global[m],fontsize=fontsize_legend)
# ax_totallever_global.grid(alpha=light)
ax_totallever_global.spines['top'].set_visible(False)
ax_totallever_global.spines['right'].set_visible(False)
ax_totallever_global.tick_params(axis='x', direction='out', rotation=45)
ax_totallever_global.set_xlim(data_lever_global.index[0],data_lever_global.index[-1])
fig_totallever_global.suptitle('主要经济体总杠杆率('+data_lever_global.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_totallever_global.tight_layout()
fig_totallever_global.savefig(os.path.join(fp,'Sample','新时代战略配置思考','主要经济体总杠杆率.png'))
plt.close()
t=printtime(t)

#%%中国杠杆率
name_lever_china=['实体经济杠杆率','居民杠杆率','非金融企业杠杆率','政府杠杆率','中央政府杠杆率','地方政府杠杆率']
color_lever_china=['green','orange','red','navy','dodgerblue','deepskyblue']
code_lever_china=[macro[i] for i in name_lever_china]     #获取代码
data_lever_china=w.edb(code_lever_china, startdate_2005, enddate,Period='Q',usedf=True)[1].fillna(method='ffill')       #获取数据
data_lever_china.columns=name_lever_china
fig_lever_china,ax_lever_china=plt.subplots(nrows=1,ncols=1,figsize=(10,9))  #画多图
for n in range(len(name_lever_china)):
    ax_lever_china.plot(data_lever_china.index,data_lever_china[name_lever_china[n]],label=name_lever_china[n],color=color_lever_china[n],ls=('-' if n<=3 else '--'))
    ax_lever_china.text(data_lever_china.index[-1],data_lever_china[name_lever_china[n]][-1],name_lever_china[n][:-3]+','+str('%.0f'%data_lever_china[name_lever_china[n]][-1]),color=color_lever_china[n],fontsize=fontsize_legend)
# ax_lever_china.grid(alpha=light)  # 横线
ax_lever_china.spines['top'].set_visible(False)
ax_lever_china.spines['right'].set_visible(False)
ax_lever_china.tick_params(axis='x', direction='out', rotation=45)
ax_lever_china.set_xlim(data_lever_china.index[0],data_lever_china.index[-1])
fig_lever_china.suptitle('中国杠杆率_社科院版('+data_lever_china.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_lever_china.tight_layout()
fig_lever_china.savefig(os.path.join(fp,'Sample','新时代战略配置思考','中国杠杆率.png'))
plt.close()
t=printtime(t)

#%%中国FDI
data_fdi=w.edb('M5530708', '1998-01-01', enddate,Period='Q',usedf=True)[1].fillna(method='ffill')       #获取数据
data_fdi.columns=['FDI']
fig_fdi,ax_fdi=plt.subplots(nrows=1,ncols=1,figsize=(20,9))  #画多图
ax_fdi.plot(data_fdi.index,data_fdi['FDI'],label='FDI',color='blue')
ax_fdi.text(data_fdi.index[-1],data_fdi['FDI'][-1],'%.0f'%(data_fdi['FDI'][-1]),color='blue',fontsize=fontsize_legend)
ax_fdi.text(data_fdi.index[-2],data_fdi['FDI'][-2],'%.0f'%(data_fdi['FDI'][-2]),color='red',fontsize=fontsize_legend)
# ax_fdi.grid(alpha=light)  # 横线
ax_fdi.axhline(y=0,color='gray',ls='--')
ax_fdi.spines['top'].set_visible(False)
ax_fdi.spines['right'].set_visible(False)
ax_fdi.tick_params(axis='x', direction='out', rotation=45)
ax_fdi.set_xlim(data_fdi.index[0],data_fdi.index[-1])
fig_fdi.suptitle('外商直接投资(亿美元，'+data_fdi.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_fdi.tight_layout()
fig_fdi.savefig(os.path.join(fp,'Sample','新时代战略配置思考','FDI.png'))
plt.close()
t=printtime(t)

#%%中国费城半导体
data_sox=w.wsd('SOX.GI','close', '1990-01-01', enddate,Period='M',usedf=True)[1].dropna()       #获取数据
data_sox.columns=['SOX']
fig_sox,ax_sox=plt.subplots(nrows=1,ncols=1,figsize=(4,3),dpi=400)  #画多图
ax_sox.plot(data_sox.index,data_sox['SOX'],label='费城半导体指数',color='blue')
ax_sox.text(data_sox.index[-1],data_sox['SOX'][-1],'%.0f'%(data_sox['SOX'][-1]),color='blue',fontsize=fontsize_legend)
ax_sox.text(dt.date(1998,8,31),2000,'x'+'%.1f'%(data_sox.loc[dt.date(2000,3,31),'SOX']/data_sox.loc[dt.date(1998,8,31),'SOX']),color='red',fontsize=fontsize_legend)
ax_sox.text(dt.date(2019,1,1),3600,'x'+'%.1f'%(data_sox.loc[dt.date(2021,12,31),'SOX']/data_sox.loc[dt.date(2018,12,28),'SOX']),color='red',fontsize=fontsize_legend)
ax_sox.text(dt.date(2022,10,31),5200,'x'+'%.1f'%(data_sox.iloc[-1,0]/data_sox.loc[dt.date(2022,10,31),'SOX']),color='red',fontsize=fontsize_legend)
# ax_sox.grid(alpha=light)  # 横线
# ax_sox.axhline(y=0,color='gray',ls='--')
ax_sox.legend(fontsize=fontsize_legend)
ax_sox.axvspan(xmin=dt.date(1998,8,31),xmax=dt.date(2000,3,31),color='gray',alpha=light)
ax_sox.axvspan(xmin=dt.date(2018,12,28),xmax=dt.date(2021,12,31),color='gray',alpha=light)
ax_sox.axvspan(xmin=dt.date(2022,10,31),xmax=enddate,color='gray',alpha=light)
ax_sox.spines['top'].set_visible(False)
ax_sox.spines['right'].set_visible(False)
ax_sox.tick_params(axis='x', direction='out', rotation=45)
ax_sox.set_xlim(data_sox.index[0],data_sox.index[-1])
# fig_sox.suptitle('费城半导体指数('+data_sox.index[-1].strftime(datestyle)+')',fontsize=fontsize_text,fontweight='bold')
fig_sox.tight_layout()
fig_sox.savefig(os.path.join(fp,'Sample','新时代战略配置思考','SOX.png'))
plt.close()

t=printtime(t)
'''
printtime(1)
