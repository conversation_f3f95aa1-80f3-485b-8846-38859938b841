# -*- coding: utf-8 -*-
"""
Created on Mon Apr 15 16:17:36 2019

@author: gyrx-zhangr

绘制基金收益分布图
"""

import pandas as pd
import numpy as np
import os
filepath = os.getcwd()
from WindPy import *
w.start()
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif']=['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
import seaborn
seaborn.set(font='SimHei')
import copy
import pickle

calDate = '2020-02-14'
period = 250
indexcode = '885001.WI'#
io = pd.io.excel.ExcelFile(filepath+'\\data_stock.xlsx')
navData = pd.read_excel(io,sheet_name='data',index_col=0)
fundname = pd.read_excel(io,sheet_name='fundname',index_col=0)
manager = pd.read_excel(io,sheet_name='manager',index_col=0)
io.close()
startDate = str(navData.columns[0])[0:10]
#monthdate = pd.read_excel(filepath+'\\计算数据集\\data_stock.xlsx',sheet_name='monthdate')['月末日期'].tolist()
def SimpDate(date):
    return str(date)[0:10]
def StrDate2Date(strdate):
    return datetime.strptime(strdate,"%Y-%m-%d")
dateList = list(map(SimpDate,list(navData.columns)))
dateList = list(map(StrDate2Date,dateList))
navData.columns = dateList
#monthDateList = list(map(SimpDate,monthdate))
#monthDateList = list(map(StrDate2Date,monthDateList))
indexData = pd.DataFrame(w.wsd(indexcode, "close", startDate, calDate, "").Data,index=[indexcode],columns=dateList)
indexData = pd.Series(indexData.values[0],index=dateList,name=indexcode)
#月度净值、年度净值
#navData_m = navData.loc[:,monthdate]
#del(monthdate,dateList)
#%%计算并绘图
def CalDrawDown(ser):#输入基金净值，series格式
    maxpoint = np.max(ser)
    dd = ser.iloc[-1]/maxpoint-1
    return dd
def CalRetRolling(ser,t):#滚动计算收益率
    result = []
    for i in range(ser.shape[0]):
        try :
            result.append(ser.iloc[i+t]/ser.iloc[i]-1)
        except IndexError:
            break
    return result
def CalDrawDownRolling(ser,t):#滚动计算回撤
    result = []
    for i in range(ser.shape[0]-t):
        try :
            result.append(CalDrawDown(ser.iloc[i:i+t]))
        except IndexError:
            break
    return result
    
def CalRet_plot(fundcode,t_len,startdate,enddate,\
                fundnavdata=navData,fundnamedata=fundname,benchmark=indexData,managerMsg=manager):
    #fundcode = '000831.OF'
    #t_len = 250
    #startdate = SimpDate(startDate)
    #enddate = SimpDate(calDate)
    fname = fundnamedata.loc[fundcode,'基金名称']
    calData = fundnavdata.loc[fundcode,:].loc[startdate:enddate].dropna()#剔除缺失值
    #calData_m = fundnav_m.loc[fundcode,:].loc[startdate:enddate].dropna()#月度净值数据，剔除缺失值
    calBenchmark = benchmark.loc[calData.index]#基准与产品时间轴统一
    #calBenchmark_m = benchmark.loc[calData_m.index]
    
    managerdf = managerMsg.loc[fundcode,:].dropna()#基金经理
    managername = managerdf.iloc[0].split(',')
    managerDate = []
    for i in range(len(managername)):#不同基金经理任职日期
        managerDate.append(managerdf.iloc[1+i])
    managerDate = list(map(StrDate2Date,managerDate))

    retResult = [CalRetRolling(calData,t_len),CalRetRolling(calBenchmark,t_len)]#基金、基准按日滚动收益
    retResult.append(list(np.array(retResult[0])-np.array(retResult[1])))
    
    retResult_df = pd.DataFrame(retResult,index=[calData.name,benchmark.name,'超额收益:%s' %SimpDate(list(calData.index)[0])],\
                                                 columns=list(calData.index)[:-t_len]).T#收益汇总df
    #retResult_m_df = pd.DataFrame(list(np.array(CalRetRolling(calData_m,1))-np.array(CalRetRolling(calBenchmark_m,1))),\
    #                           index=list(calData_m.index)[:-1],columns=['月超额收益'])#月度超额收益df
    
    ddResult_df = pd.DataFrame([CalDrawDownRolling(calData,t_len),CalDrawDownRolling(calBenchmark,t_len)],\
                             index=[calData.name,benchmark.name],columns=list(calData.index)[t_len:]).T#年回撤

    for m_d in range(len(managerDate)):#添加当前管理基金经理的业绩图
        m_d_i = retResult_df.loc[managerDate[m_d]:,['超额收益:%s' %SimpDate(list(calData.index)[0])]]#基金经理管理以来的超额收益
        m_d_i.columns = [managername[m_d]+":"+SimpDate(managerDate[m_d])]
        retResult_df = pd.concat([retResult_df,m_d_i],axis=1,sort=False)
        m_d_i_mdd = ddResult_df.loc[managerDate[m_d]:,calData.name]
        ddResult_df = pd.concat([ddResult_df,m_d_i_mdd],axis=1,sort=False)
    
    colorList = ["r","g","b","orange","c","slategrey","orchid","k"]#颜色参数
    if retResult_df.shape[0] != 0:#绘图并保存
        fig = plt.figure(dpi=125,figsize=(10,10))
        plt.suptitle("%s"%fname)#总标题，下接第一个子图
        ax1 = fig.add_subplot(221)
        seaborn.kdeplot(retResult_df.iloc[:,0],color="r",linewidth=2,shade=True,ax=ax1)
        seaborn.kdeplot(retResult_df.iloc[:,1],color="g",linestyle='--',linewidth=1,shade=True,ax=ax1)
        ax1.legend(loc="upper left",fontsize=10)
        ax1.set_title("全周期收益分布",fontsize=10)
        ax2 = fig.add_subplot(222)
        try:#第二个子图，超额收益
            for i in range(2,retResult_df.shape[1]):
                seaborn.kdeplot(retResult_df.iloc[:,i],color=colorList[i],linewidth=2,ax=ax2)
        except ZeroDivisionError:
            print("现任基金经理管理基金%s:%s未满一年"%(calData.name[0:6],fname))
        ax2.legend(loc="upper left",fontsize=10)
        ax2.set_title("产品和现任基金经理超额收益分布",fontsize=10)
        ax3 = fig.add_subplot(212)
        ax3.plot(ddResult_df.iloc[:,0],color="darkviolet",linewidth=0.8)
        ax3.plot(ddResult_df.iloc[:,1],color="darkgreen",linestyle='--',linewidth=0.8)
        ax3.legend(loc="upper left",fontsize=10)
        ax3.set_title("滚动年回撤",fontsize=10)
        plt.savefig(filepath+'\\收益分布图\\%s_%s_%s.png' %(benchmark.name,calData.name[0:6],fname))
        plt.close()
    return retResult_df
icbccsFund = list(fundname[fundname['基金名称'].str.contains('工银')].index)
fundCode = list(fundname.index)

allFundResult = []
test = []
for fund_i in fundCode:
    print(fundname.loc[fund_i,'基金名称'])
    allFundResult.append(CalRet_plot(fund_i,period,startDate,calDate))

