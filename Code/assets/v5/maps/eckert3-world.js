!function(e,t){"function"==typeof define&&define.amd?define(["exports","echarts"],t):"object"==typeof exports&&"string"!=typeof exports.nodeName?t(exports,require("echarts")):t({},e.echarts)}(this,function(e,t){var o=function(e){"undefined"!=typeof console&&console&&console.error&&console.error(e)};t?t.registerMap?t.registerMap("eckert3-world",{type:"FeatureCollection",features:[{type:"Feature",properties:{name:"Somalia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ᖃ@㺻ᗐ੭ࠦ׵૪܃ܤΗࢴڄࣄߪ૪ఀཿଐߋఔd፤ࣂဂȩኺफ़഼ûెМ߬Ƒᦈ؄཮ৰ৔΁ҟ્xີ˙Ӆϒʘт̏ਣțΧ᎕ᄇᩭוՍຉᦁҷഝᡏ⏹ᠽᨑሳᆅᢋ๫ᨍᒉ╃❅ণྣˠɍ˜БԢؓ޶v仈ࡶࢀغࢊظࢀòŤʢʆመиخܔਤҼࣲɊᑾđ䎞䇾"],encodeOffsets:[[213773,-234185]]}},{type:"Feature",properties:{name:"Liechtenstein",childNum:1},geometry:{type:"Polygon",coordinates:["@@ϿđȂƾŜʐɆǈţӃ"],encodeOffsets:[[136745,-127632]]}},{type:"Feature",properties:{name:"Morocco",childNum:1},geometry:{type:"Polygon",coordinates:["@@媗ú˂ɬῄࢲᬸၖ࠼۸ဢ࿈˔ݶ́؄ڌཎ๼๜מજᑜຼ⚾྆ᆪᅴჸᗄ๨ʾȇ٣৐ࣳ؂ĳาˆזƳ੦ѪÎسʈhȂ¹Ҳzш§ق߁࡟ịĤչËѫÖȹƲǻĴ×ȂƭŲűȋʹƷҳۛℕǛïʕϕğę৙̃υ¡ȡƷyˡЅɍ۳̲ѱ౏ѓғʛӿǏףǏҿș̵̙οΩ͋ʋПɩ಑˭਩{ȣΫɝǷėwʣȵ¢ؓ¢ݫѥƩó᳥ນוᆯý˛"],encodeOffsets:[[69227,-180226]]}},{type:"Feature",properties:{name:"W. Sahara",childNum:2},geometry:{type:"Polygon",coordinates:["@@媘ùý˗̋@@@̌@ɻࠣɥࠅŧѷƍԍƉԉįλåʭ䗉AƷ֯ʑࣳǅ؍ḁ̹̄௛ɭࣳ̉૵ࡕͯыÓơ©ਿ؇ηГIʽmXkӃɿ෩¹́¹ͅɽ¯̓⻋M⒫Kѥ५YȹēŔࣔᙼ྾༌׸ૼೀ༎ө˛ˤ϶᧜የ٪໖നቚᒘંุᏞДΖ","@@ʵݽ@@ʶݾ"],encodeOffsets:[[57663,-180133],[68738,-181092]]}},{type:"Feature",properties:{name:"Serbia",childNum:1},geometry:{type:"Polygon",coordinates:["@@GBϩ×͇ˋйǂڷɡ˧ѵͯBŶ޴؋ܦǒӊڞƲƲƱ૩ݨ۝٘cĀvĄhܲʖɷܜ޲Ǭۅ܀॰୚ܥς׶ޠǚ٩мɼʶǉܖɴɌல̔ԄɼҜ¿ȬҒ©Ē¸æŻ²ǙβʃÜȩøı¤Lºrxmɩف੶؝¾¯ϿЛ˲ȫ©«ǝiŭQ×kÿ̬ćǎǱՎıȰĻæǩʐ¦кʶͮäʦūŮķIāĉǋfǁ·ɕɻʂнޟաÿčǓʉÙǹĶܵήɣÚţ¤ŇȒƝճҏ˵ɫաġǝ߱¿ěɹǽ÷åǭɭ{ࣇ@ׁů@@"],encodeOffsets:[[161665,-140756]]}},{type:"Feature",properties:{name:"Afghanistan",childNum:1},geometry:{type:"Polygon",coordinates:["@@େѯዝĪቷ̛ខ൏ƍǱب્±߿့ษɌέɡ܏ࣇɓྐྵŬԤྍს׹߉ಿŉ૕্ڳԻϚु@൙ڑ˴ηڇʓഅÞ௡਑׷ᐷᣗۅ໗ۥͽૡʂᜭȩফ˚ྉӬ޻ʔాଂךՆ׈ԶĜǼîΤ¥ӈǕɒڑĖӿâիîãNφÎǔx͊¾ʘǀКǼŃ޺ŝࢪ͌ठࠔ۱ȆƇˢǦࡨѤӈ՞Ɠɚ×Ÿ·UÐ¢ÒƖvņôǠļĖ¬ψҎʄठɀĚȢδĚĘĘ[ƪǼvԄѳঘşϊҳࡨȎࢠюŚ˜ɾʘOϖ࿴ϊݮ̨߄јǂȚƒ˴شଂņǌ͈Ɛ̈́ĲҤpդX׺܈ගԉŊVڒ¼ӾćɢɝʪË˴ňǔn´çŮȨZĶÕ@Õ@ïŐǝɜȵɤ¹ѶƚÖ¾ÆŜɀŌچ˔ŤtŨuĨ°î¨è~Ò`ĲoЀύǂŏĔnŪôƼżìȆeʎĎȞȀƪ̦ĠъͦĭѶǢǾÄɨÙ˒ºĒԊ͸࠾ަ੊ʷĸ̩ɱ֝ĄưXɚÌƐHÄ½ڟჭĐكɢͩᗔݔؖՖ݄̀ϸŦɊtʦËłžXĩÇĻəƱ°¹ȬiदɖĪĪƒĪࡌ±ƌċǾǃņŧőcơÄ­Ć{\\ƃÁʝãѝǯA­ɨǵºÏm"],encodeOffsets:[[294794,-155009]]}},{type:"Feature",properties:{name:"Angola",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ƘlࠌௌϪࡶƥѶ@Ѫþ׊GŚɐЦiњĄ̢ݴΞ˘vѐäٮæʌUtŗйNƍÎƏŘõڴSشSఴ¯Ų½иᵓąჇफ़౯ζฝȱ̯ņٻƂǉĆWǖĔȖǎǊè֮Åאgࡄˆ࠰ıˎ͆ȺቭᎫʇHůɨΝIë˃⋟@ԣ@ȒᄿĄܕĊܗа₉ڨຓ᱄ᗓᗱѱ⃿С౛Δ᪕ƈോࣜ⾻r⴫¥೗ࠜڹƶ೓գсfĕiɻTҕĢɫŽ΋¹§ࡎϐяዖӰ܈؎Ȿߖ๜ְࣜΐԾ̲੖ĉጒಧᐖࣻᒄݖ߰S܄ฃṨಹᇲጤ׸δgපb㈀m˄ûʐƥϠѻƼ৭Ǭ·ʴίȌܣจሯ"],["@@ނΠöɂʅǲƯÊʋįʹġࠁݣN§ǆŽ}ౣ׹ęŵॾϭܖਬढ"]],encodeOffsets:[[[131033,-278092]],[[116692,-268513]]]}},{type:"Feature",properties:{name:"Albania",childNum:1},geometry:{type:"Polygon",coordinates:["@@،ܥŵ޳ڙഏǺޕӆǫÿǗͿѱ΋ݵ޷ҵśŗýïśÍãuÛØʑĕӭͭƷnũČé~¯E¹ݞࠡࠀ˾I¯ׂ೐ᔐŸͤяǼžڸஂ৸ƬͿٞǄ"],encodeOffsets:[[158132,-139938]]}},{type:"Feature",properties:{name:"Aland",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@̫ĥǦɚƆų"],["@@ЀȩୁГºжʔĿȨϜˌē"]],encodeOffsets:[[[181581,-91819]],[[182651,-91372]]]}},{type:"Feature",properties:{name:"Andorra",childNum:1},geometry:{type:"Polygon",coordinates:["@@хƏǿWqÄÎȚøńȪľƄiˌåÛk§đé¹"],encodeOffsets:[[110976,-140058]]}},{type:"Feature",properties:{name:"United Arab Emirates",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@٩iҴȨǶǽ"],["@@̙ðՆːɫͿ"],["@@ຏ߉՝áӒӍȑ˵ඓάÕøͻୱ˙Ĵ˙঑ჷƅ݋ȱȁ㓃ۢ჏᢬jҬѐíČկעę௬Ъ᠞ȏߐɪ׌ʈॄ஘ᱬᔶЎ׺Ȅǫߙ͒ę"]],encodeOffsets:[[[234269,-190048]],[[232707,-189826]],[[241218,-186029]]]}},{type:"Feature",properties:{name:"Argentina",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@สo቏ЇƇǞ֮ʚ"],["@@ᰢ⼨ʊ॥ΙÒϡҡ׾ķֽߜઙ๒ઍኒ͕࣯Չᖧˁ੓˼ᲿŒ"],["@@ѦҋዬՅನ਻᧔౻Ϟݯୋಗڝ֭˻ދଷઅėƝX˃঒Ğ⊤ع݊ԴҦU̔ȯǔJɊźŶƸ¦ǸłźǞþżŀĖƂۀǪׂԠߔࢤ˦ڴ˴२ΒŉۘɬӴ͛Ű൱࢑ᇿఱڱୱ˵ߋۻ༽ݯЉѧᶏᩙݛн֗֝еʁŽŅǍʇțȧͫʛƹɉ·׳ɕǭûϏŝȯҝӷĳǩæėjģşʑƋȉΉɵĉŗjƓƣġƯsƥ¼ƝÑǍ˥ͱŇȵȃࢭڋͽڹఃïஇνɳɔࡿೈݥՔޑۇߟɅۇͦ؁ڄōʿଁ᱇᪏ဋ޵┛࣑ᮓ΍໣êࣃδ̫঱І̇˗eƢǹѻ߿ϽƽԱګŏ੯ԩӛඑөฉŏᖣैە͕࠯ᩇߞͿɥί̚¡܄Ĕȼ̩̀ń৤ɺȆϗϓऍਖ਼гקࠖ୕΁Ȼȷ౶ҹ಻ӯఛࡩ݋ཕ஑ࡡ͹ޡૉĪሏٻᔷᄱɗ࢏࣢ిາȟˀ֙ӝٿਖ਼х׺@Ÿʯ⮹ᗇ߃ډ੽ທઽЯѓ߀ȫЧܵƭޠcȆͩွރ౟அδüʿ્঳ɉࣜȹɌູᥱެ◭¦ғ৐ସ኎ʹǖี̉S൐ِ๚ΨͲۆՄٚঐϮլࠎמʖִͪƓԺފ࡮௢ٴ̈́ࠤۊЖƚશܜҨƘ࠼઀քǍ߄ࡥǚĎ̒၎pЬҒPˊ௏żܸ৪Ċನєʚ׷քހ࿼शҞlࢲߤᒰݎۚÅҠҸΒȦࡶЪô࡚๸ේ݌xሲڠᏔբܒფਘϢ஼ĥآфɢഘቈӞǢܼᑊԩȀΠ܂ȧᅒ͊ˮҥ࢈аस՞ˮݰ๚јPΦЦȑɐ̼ၔ֢Ӵި໲೶બଶႮ֜۔ώ͑૸֒܀G࿢زܾɋޘࡴߒᒤߢీᙂ͙Дצۦݢڀ߸ɰŨƊƌʂźʾòØİQƺßæıϐ̻ǆǳǼyϖ଺ߚˉŖ͉¶ʭzɟöϿøƩÄðǼјࢰஎƖİᜄì΄ٿǂƁͤȩˊϯٖౣ೴ਜ਼"]],encodeOffsets:[[[847412,-405256]],[[837145,-405631]],[[890594,-320530]]]}},{type:"Feature",properties:{name:"Armenia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ࠉÝÛࠎη̞ƠϐܯŕכȠƕФ׳ƏߙݮోȔߖӚՎ͵ࢄඔǚ྄Ǥшì̘͏̓ǅशԟ։ϱấࢲڝ̝ҳڹŁ઀ࡩ֪ĕ΃Ϳ΢ЏЫëdٕ"],encodeOffsets:[[223038,-149869]]}},{type:"Feature",properties:{name:"American Samoa",childNum:1},geometry:{type:"Polygon",coordinates:["@@ȷĖ֢ĶΩȋ"],encodeOffsets:[[597967,-295147]]}},{type:"Feature",properties:{name:"Fr. S. Antarctic Lands",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ΙЀрgåϗ"],["@@ݢ̴Ȩ҇͝ƟͦǑไѲּ͋ȡԳԟȰޥș֠ͻԌŖƓϱᑕϸɃϻσ^ɉத̗ɜɌ࣠Мҁӳ̆×"],["@@ЭƪƎŀˠʩ"]],encodeOffsets:[[[289131,-389824]],[[288927,-389962]],[[242943,-382680]]]}},{type:"Feature",properties:{name:"Antigua and Barb.",childNum:1},geometry:{type:"Polygon",coordinates:["@@̳ÁĝȎżǂ˖̍"],encodeOffsets:[[892357,-209525]]}},{type:"Feature",properties:{name:"Australia",childNum:30},geometry:{type:"MultiPolygon",coordinates:[["@@īɵѕƼό;ƶ˃"],["@@ԔᔰࢡࡨȒˈȫ̒ϾঞqΖΘՒ×ȪȼԺǛ˴ᴽ˛ռ׻ഗÂ഻ͯďȁɔŇɬ͈ȸӋȂʥп͡ժĻ्ԹƄǢˋпݫۅɶ੕đŅϦ҂Ɔ۳Âࠏࠌؿ༔ּוĮ̴ЇҬȩőBЈ৭ඬˋࣸƜ۔۔ʱ"],["@@ĻǥƭǄʪb"],["@@óȁêΊJǇ"],["@@ǖГࠁȂ٬ɒ"],["@@وԝпѯȣܡ࢚Ӳʨ"],["@@ȡӄͪܨ̸࠷ѿγ"],["@@ĘƧӛàЄĈ"],["@@܈ю˯ॷĹҭЃԯȸॽŕһ͌Ȩ̈́๜ΔզÕ˅"],["@@ʝ؁xڪɦç"],["@@ԩ؂ï֜ךଢ଼"],["@@ɕҀƒߢкоǹђʠȔȨݝ؃໧"],["@@ȶēMտ҇ռʠĘ"],["@@ˡƕʆФˍ"],["@@ɵjĢ̆Ɣ̯"],["@@´֧ѽԸЊ°"],["@@î˦Ǯɷʛ­"],["@@ޏϗؚ̀ٸǈʘˋпŽ"],["@@͵ƪƢʾȔЧ"],["@@Ǉ«oΈǸ̛"],["@@ʅƤ̤Ģˡ"],["@@Ϻ̓ٗҢʡūʑఙǼȌަԚцƈ˹"],["@@ʭjȮͼÀΥ"],["@@ΗǕڎҾ̵̧"],["@@̫ͪవàͼ̀ĿК̺̀ϖލ"],["@@࣒Ģժ͖؎փಓયধ۔॒̓ՆЩ"],["@@ǃƇ֎ॖЉࠍ"],["@@ʣתɀƴ¤ݝ"],["@@ƩޯٶݫфᅟąڗҺളҜʭ଎Ԯϼ׍ු৛͒Ὥܾ੕ΔóӔྥȩ෗ڠػŘॅဲझؤܘ࢙ᖮ୑ˤѓ΁ķʥ଺௛վ৩ւኝʼŔͰέïլ̒͐ਂࢩÒϼɮùώᙩھܱ๪ݹٶਜ਼܎ҝњࣃ֤ԧƴ࡯ϼͥţ᩷নᚋČॷಧ⢇ȧዑࡁ൅ȟࢡܥ՗Ðǯݽ͏ଁຽޝͱǫɰÅީਿơଳ಻ᆻ֛⑟७ի⃗ηᓗ࿋റƛʲύˠŦƉہԳظӃą̻Ղࡁ̦̎̈́ơͲϷbۯכԳάЮSѪ֢ӱєਿՋ־Ǐᛣൃᙣং༡ʴ˛ʻγÆࣇٚೱИ೭พŬಲ̅ٲဵᄘѾżʔȡՈྦྷױܩâږೊÕא࣍೦ߏᓻႷɏʶܦ߶NǤ၈ࣦನșࡐ̔ʄәୀչܳࠣгউႍࣇૣඳȖܕϵˈ̩ǡିߌȚɘԘȿխൾ݅ݜɷښН̲ڑÈ̃жǫԪ˘¢DТషކছÀোԘ୥œᛁଦޙʏℝů☩ടᴛóᝯ಻ય̑ๅ፳᯹ǛɁˀ෇ń῟ͧ߱ࢡ୷ʡ༅ଋ୧ʝᘹʘ୯ќঁࠞ௱ϞǍဈߴ˥خ৆ɇ‮໇ᡬۣṶရᛢЛ࿢፟ᤔ̸Ə¸μ֔੭͸ĉɄɄ`ϖઙ൰ņʰ؂ԯÜڟ˒ʶΆۋɦǉʺɸ×খእṬĒ౐Ւৠxૼ˩ئڎၞɒŠŔำԌ͆ڨ୊྘ݒ୴ਔຄࡆ༆ƭ႞ޠ௄ʪݴ׮੪ĩᶢॾِܰਘሶ૊ࠈӋీɠऎাެՒ޺൬᝙઴ՒȭČҎ࢝ॠ̒ǖÁјˎì̞ыИ˂ֺˡ਺ࠏɶfφ΢¬Ƣհ̽ɭ˵ӒŖפ˔O˨Ԁࠬϗd̢εQǏͰѢΖ܈ʷб࣢ࢠ԰ĪԔǤýċՑΐƈǔͫ˲ˢ¸୆̆ÇǤί͔̀ϘͅҠҢmώ˰Ĳఞٟ࿘ຽɓ൩ȆɶɢǍȍְ࢞׺ંʕδءĺ٨ࠢ׷Q٨Ҹ¸սРȲȨܓτܚऌ˖࣬ऒ؄˱ΘâтԔ׾ӚĠJӚպ˳TѐआڠͬҳආSмɄ΢ʑɐΪҊ~ȅȢĔࡺ҃Μ࣋֛Ϭ̪˘Ѩ̅ЄȞ޺كآɘԖڛྈ˯ଲӳ۠ƴڦҵр×༜ࢰӉ࡯ڲ¤ǜԫЌ}ƨ˼Ǳ̔юӸت؛ЈŖʃࣛঁƄӭ̅ӷύȜ߻ωüද஧ᇋŐσ၎੻ȞύआʳȀҳӘ༬ߍ௒ગၴЛդ॥ჶ࠷੄Ǫ۲Һ৊ᒄܐḼʃೀƶ܂ˡߒϔ஺÷ۺ۬Җ֟֌v̾ЎࡀȰǭ҂ៜ٘՚ͪğř˯ԘաɌཋۄ̕"],["@@ȃŷƉ˖ƪňǤʥ"]],encodeOffsets:[[[484755,-374379]],[[478733,-367259]],[[486969,-366518]],[[478054,-366495]],[[487197,-365950]],[[486346,-364451]],[[475825,-365429]],[[479353,-360996]],[[459077,-353488]],[[500944,-330841]],[[393240,-327068]],[[499740,-326243]],[[494594,-320078]],[[398538,-312705]],[[488946,-311351]],[[481474,-305731]],[[463029,-302685]],[[463144,-301208]],[[456587,-299039]],[[422737,-298013]],[[424314,-295770]],[[455495,-293654]],[[454201,-293710]],[[454410,-287649]],[[438442,-287859]],[[438864,-287031]],[[455110,-287039]],[[444226,-286832]],[[472996,-288609]],[[470522,-285200]]]}},{type:"Feature",properties:{name:"Austria",childNum:1},geometry:{type:"Polygon",coordinates:["@@ЙӃǼ࡟ӉڛേóϪ̱ر̩ϝ࢝ࢿ͏ҷгᗻǇउң࿣˒ЍĀ˷Lη೥ɠƿäÕŸ·ǠƃɞJŎŞĬňòP¹fࣻɋԉ~ȿЍg˓ėɩǣơƑē­ơgȡ^ů¤ěŠɕhȷH­v¢ü_Ƣŕ°כ̗कҤŤӌжѮƫŸԶŘǊ½ɠĿĨíÅ±ůʖáŵȍӐȜŶŖɔ̞Ăuτ^Ʀ½ˎUɈ˳ᤖԒäÖ¾ƔrŀǮ@ːϤƢájñ·{Ï·łāƲçİLääŊǄĤǞʧƬ̒ϚǕӜଲѨʪàʚƖ̀Ϣh¤v̰C̼dÞúƤˮ®ª̦ο೐Q஼ࢾᄪխ਴Ģوѳ"],encodeOffsets:[[157322,-123430]]}},{type:"Feature",properties:{name:"Azerbaijan",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ౙ˾৅෺éŢ״ƐƖУלȟܰŖƟϏθ̝Üࠍ"],["@@ஔᚃդӗߺǯʜԍऍĊࡥ˛ࡇ಻ù׃ؕ܍ʻɶԩډɁࣗ׽Å্রܲτˁӼ֔Ξԩْ⍏჏cٖЬìΡА΄΀֩Ė੿ࡪںł̞Ҵࢱڞá͂֊ϲवԠ̔ǆ̗͐հ̒ƴÐໄשڂʅɨ~ʌǄƆȔ¶Ģ÷ŶփξůѦ٦цتǫࡐ஋ूȅᐄ฀"]],encodeOffsets:[[[222041,-149948]],[[230706,-141855]]]}},{type:"Feature",properties:{name:"Burundi",childNum:1},geometry:{type:"Polygon",coordinates:["@@©Íȟн×ŒşĿ̃zœȂĩȨʄĥǂiø̍Ν࡛ࡕॻหԇͩ۳dһ൞gრӡݚȐ˨ټӉێϖǬŬࡈфǥ٬ɶ͞ț"],encodeOffsets:[[165749,-262547]]}},{type:"Feature",properties:{name:"Belgium",childNum:1},geometry:{type:"Polygon",coordinates:["@@אÃΌڝ࡫݋eøëAơ͙ȋȃƿŧŗƕġΫࡡؕÓছبͼࠈߍѝߗ~Ϭ؜ःƆlϦסŨֈݙÝ©ޤᎨغD̏ࣨďࢰАლȂƊыਐ˯گࡏ"],encodeOffsets:[[132191,-117495]]}},{type:"Feature",properties:{name:"Benin",childNum:1},geometry:{type:"Polygon",coordinates:["@@ŇƟǣ̓ɵ˺ӧ¹Ăùĝt۟ƐίǷы˭oǽࣝڑԑݹ་؍ā˫ຫť૛ֽÐǕâơQõ«±ÕܷğȟL˵ǻ¢ƅÁŏ·ƩħԑខͿΦǨҗ༠ɀZ˶⯾пۦH්஁ࢎѲബ౬৬௰Ûਆੴܬ৴βཨ໥"],encodeOffsets:[[93848,-224094]]}},{type:"Feature",properties:{name:"Burkina Faso",childNum:1},geometry:{type:"Polygon",coordinates:["@@ʙओЪ֙xؕ΂׹மޟҽhĝܟ୸ࣩ௚ƦɀՁϡΗ׼इਅੳ௯Ü౫৫ࣽď௏άԉŘ޻ՙࣣȂ☹˯ࣥʊΩŭ໡dȝņ˥`ŝ¿ÃŝñŏHŗǘ»øěǠóǢŕöőìůɐŽľƭٛĦҵe࣑̣ȧđǓɓǏNʩǚɟuȳĬઑీ֓ʄɒൢ؞ݜৢ൜֊ڤג}ݢײ˖Ɖܰ҆оӔɊࢌӫՎɖɪࡌڈuʺڲܠئਜƁЎ٢ᓠ߾܎Ԝୢ०ͧ"],encodeOffsets:[[85763,-215324]]}},{type:"Feature",properties:{name:"Bangladesh",childNum:6},geometry:{type:"MultiPolygon",coordinates:[["@@ȗÈƖьÂӓ"],["@@ĥǿÔІɅ"],["@@ʙƽƸऔĢޕ"],["@@̡ȾŰ̀ǲԽ"],["@@׍Ãаט̋଒ɄÚՂࡑ˛ࢭ"],["@@â^GΰƤIԒ࠯֞ñĀ˶ɺǼ˔؍ă࡟ĹԅvçŒբǏੴśṂHࢌԋȭɇ˳ļÁʙ̑إƗˋԱѽʱâࡥ͕ӽࣃ͐೟Ț̎ɔұƜ̤ǒ¾ԼĤǒΞ͐AȾlȠň`ƘѾ`ņথDۭȎѥ¼ŋීjƽZčÓ੓Êίٙ̚Ϋ͡ɸ૭Ձࡪɇ᥎ƻšחೄعթ؁ķօॢG౐ƕϟحüڠщѷଵ͖ࢍोஙͽź΢׾ח̙×ȾɌ޶Ӄઉֽӛğଔ߱έѝխͼo࣐ɋߞIӂçѲȨ͞ѣƴWՒŭŸů˦άܾ®ƮÀʼ~Ĵ_İǗŘ̵°ͥʈʕfǝÈſƞĿǪxĈ¨ň˞ҌĔbȐēô@ƪǲɸԖʴA߄þ°ƲȅĪƳΘַľ܏ؚćâƶվѲϠʸʤĥɬñ|ÐǾ̾Ľ̬ԏֆĽ"]],encodeOffsets:[[[335462,-197329]],[[335330,-196445]],[[333465,-195509]],[[334602,-194945]],[[332443,-195744]],[[328581,-184407]]]}},{type:"Feature",properties:{name:"Bulgaria",childNum:1},geometry:{type:"Polygon",coordinates:["@@ճ߅ەb׿ҵѻ঍ઉؿ܌ૡୁ¹˗̔ĩԯýříȳýןÓȗƳƍƽ͹ÇģÝ¥×gÙɵÔɁ¡Ûµßç¦ͻ­ȗąŋǥÿͫē˥¦֫çͯā̉¯ࣿʦ̧ȘÆȒ×Nथ×ĩŽÁqࡡǏȿCȵƍq˱YɭƕӷԬࣚƗ֨ޓܔǮøæɺǾÀĜD¼ý˪ʘӌ͎࠘մҐȑƞ£ňÙŤέɤĵܶÚǺǔʊĀĎłĮʮƖр̀ֈЁ҉̅Nʫ㉠λᅚݬደΨ׎͟ढõՄӑ݂å"],encodeOffsets:[[181197,-136678]]}},{type:"Feature",properties:{name:"Bahrain",childNum:1},geometry:{type:"Polygon",coordinates:["@@įǟǷ΢ņמʶēߟ"],encodeOffsets:[[226121,-185395]]}},{type:"Feature",properties:{name:"Bahamas",childNum:14},geometry:{type:"MultiPolygon",coordinates:[["@@ɍӋଗS»˺̸Ȝٰą͖ΤŤ˓"],["@@ۖɩफŢʖň"],["@@ƙãښޒȉфɠŖћګܩ"],["@@ҿŷ˺׸ʹ"],["@@५؈ǂɮכ࣊ආႿ"],["@@ࢵ΂ƶࡴӷ"],["@@əɛY̦ɴĉ"],["@@ǩȕ˲ŢƸǱƸࢹѱ܍ਸ਼݈â"],["@@ЍƋƨ˲࣋ैଲભ"],["@@Ӎ؞ÌƏģ"],["@@ŎձֽЁ̓ҤԕŖȏȆЪÐȃழ֪ʃۊࡋ"],["@@ݖΏ֬ѕƘવЧϖɴZڔտў؍ˊΑYḚ̄Ǩǥ"],["@@౒ďၱэرцҬʇͦӘˀƷ"],["@@҅ːˢ̎ȇ࣢ݍْ঍ź࣊Aௐ࠳ǊѯɽȻ¢ݩ"]],encodeOffsets:[[[859906,-198190]],[[859227,-194817]],[[856211,-195404]],[[856341,-194014]],[[854148,-193548]],[[851636,-192032]],[[854599,-190346]],[[845852,-189851]],[[852170,-189986]],[[846245,-187766]],[[845362,-188602]],[[847834,-186474]],[[842156,-183087]],[[846043,-185337]]]}},{type:"Feature",properties:{name:"Bosnia and Herz.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ܦ९୙ۆۿޱǫɸܛܱʕÓLřiûwĩĽtÛϛÿơŅe©ŦǝfˑƗȳŝ˻ғ̕ƃĭũѓ¶̡̣ǽ್ݰƍĢȤƴЁজරຸࢲՕոԐऎԠzϜЙ۞ҮࢂƆံѹᄢCǸѣ΄"],encodeOffsets:[[157981,-133604]]}},{type:"Feature",properties:{name:"Belarus",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǘțᎢʽɧΉ¶ƅĞěϐƬž¦ŤBȌÈǄĊŲȪoϺpִБҊɋɅϭʁƭ³¿}ÙÚŭÜȓmŏ§ĉҋ̣œťęūW»˘ȝɞŁ½̩̗ǲē¸ίļɉΪȏˎū¢ÉUãĭƥǥǳŃĳŖ£ΘZДξ˹ǜۿŹiƱ˅ģࡡБֹ˃ɄԟYí}ĭĹȽǩƍį®ÓÊűŀǇŢƙlēȣԕʝӱpÕö¹ ģɃgÇϗfүMͅūӡWωȻʓŹ˃ǻ̛˩ȥȹYơvƇµơWŕҙǹϗҖჽɇʋոđIűÑĳÓősǻ·Λʅý^å SȖř¬ഁäѹʱĀɄޟÈœ̴ŷpʫe΍ᇋ΄ᱧųˍȹѝʗϥǏ×aƅĔƅǙV̓ʷ}iڪ୤ۛվᏀઐӖ᤬Ŵ­΄gॶĐÈåƂÓ܆Ț̆[êÀɌ˔ŢÎ΀ƾ¸ȸƄڄǬÌ×cŏŃęŎÿѠĚNʨƱšDJ²ĦĸȰȆɖǜŔÚ¬ÐƂ˶ԤҌʔØˠɈĠ̐˖Ķ؊sǾǔͶƸQ®ƙΩ~¯ÆÐàȒǠɆɚƖǞÒĶ૚κ৚í༎ޖ"],encodeOffsets:[[195430,-102851]]}},{type:"Feature",properties:{name:"Belize",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@Ź̹Ʈלsˡ"],["@@ݧIϽ⥌bΔܬ]ӌ੾ӸÐĉʫӤkƒ˧ɟൡǶȻbጕએထ"]],encodeOffsets:[[[821565,-208473]],[[819281,-212653]]]}},{type:"Feature",properties:{name:"Bermuda",childNum:1},geometry:{type:"Polygon",coordinates:["@@˙©Ίʎïȣ"],encodeOffsets:[[874597,-167909]]}},{type:"Feature",properties:{name:"Bolivia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ľޤᅋஔ᎛}♳ީ໛Ꮙəૻഩᤡ̽Պᜃëƕįࢯ஍ǻїÃï÷ƪõЀyɠµʮŕ͊ߙˊହϕǻzǅǴϏ̼åĲƹàįRñ×ŹʽƋʁŧƉ߷ɯݡٿץۥϧõӹćүǉŴƦ݈˭ዤŘ܈ػࢾˤؔԑϞňސԀͪƠyĺɉʸF¬Ͳή̖˶O¼Úۥ٦ȟˬΎ³ΖS࠸ã̴ƨȾϯȔÓ¶˵ঐBFfĴ@Ǆɧ̾Úň໊దϊҲ̲ޗц̇ଖڮࣜοؔĊϒౌඖ͇ઠфᅜݠࡎ࿏⅊ޮU࠴ŭΦɩ٦ɀߔۺȀÞΚ~ʞ­ݎڀጒરᦨрƀঁÃʃɇ̷ĽŵŽǞࡵȌȟӷ͸ૹࠔۙ܌࡛ྮď܂ҏز|זܽඎεǿ̀οƒƝǤŭӊDƒɐ٨ÚŠpஆڥŒඥ`ງ˻UЙ]ļĳˤϻʮίÓिԧq̫⊪ƁĢHǚ¦ŚÆàCćҧ͓ΣœҹϝU·ĚŃ۸ԫǾ©ǚ°ôƍkəǀ؟İϹǒȷȣƟԧ৫ȫЛĤ̃௃༭ӾՁûğǛ­ƗȫɉƱ"],encodeOffsets:[[900449,-311005]]}},{type:"Feature",properties:{name:"Brazil",childNum:17},geometry:{type:"MultiPolygon",coordinates:[["@@ǵĵǺՎϸϼϻࠓ"],["@@ŉʢΒˊʇԫ"],["@@щcцϖDα"],["@@Խã̸ɖɆƱ"],["@@ǷŏõǨ˔Ĵeǋ"],["@@ʀӠŖʥΕɹ"],["@@ɧɗˬܒÃӹ"],["@@̳ũĮɾɆœ"],["@@ʃ¥ךࠐ̰ঌټʐȸ˫˅ߑళइ"],["@@ନƦྮ̙ৗជҥ˥̕ɪƛл؇Ƞȍ҇ӧǧׅȘ঱ȋձ௬ìʬ΂À̓ÌȍϦΊවࢸΖ൴ʧ"],["@@ؗѩʟɨѮЀъǽ"],["@@ࡹ¤ܶЊ̌ȃǇʩ"],["@@ЍٵिƬƪфதĆ"],["@@͟Еƒ֐மĤݑȽˍ"],["@@ʍù±ΚӼ˘Ȼշ"],["@@Ε×ėӦͬłҕ"],["@@ù˳˰ҕƢƗjÏٳޟʭ፡ʦ̨ܷȣͿ[ΣQƑȶΉ̎ĻۘܧऄϹühŴĔĸøϮTTΈʖĪŊĔŸۊƭŐ˺ʔ¨ǤòҊVň¸ԆՐĦWȒºƾðÐkŴƹƶ­જÌਦɟŴôædȦ\\ƘŃŰƃƘŭǬƟºhĜĞĤĦ¾ÄÜÆɺ¦¢XԦʯޖüĜņź|ɒņǒmĮrƚĴÔX~ŋĪŕŚĿΈoÒǽĆïĺEÆĤ¯ĆŃȄďۀȗߜԼኼ͍۬܄Ռส෺ዾɈ֒ݨӡౌ⨁גڽଂϝƢ૕࢛ܹ৿൏୽ۏཏᮕ֋^ওч྄ÿᛚถѢᇏقࣰ֩ЬجɃࣸѦލ᏷Ц;׆ೊӸȘٞފո˯ʴ̐ʽȠ¶֌ݶࣲదư̰ɋ¶̸⥬࿋ˤֺ߱֘޺ׯſƣ˼ŠʊۉԁӇȲʋ԰ҞŌѳ̷ęǍϓ΁ඵڂ׎Ӽ੔˜·ɉ۽תԒ੺ǼǪ˰নȥາܷ࠼ĸ࢖ϝᗀ˔଒Ƨὖችࣦૡ࣠ࡁ܊˓ʼѿ౸бᙤíͲ˕ঠ☋ǧᑭඃᬕ੅੍˧Ϋಡා࢏п਋ࢳӉݥͺঁ቏ࢥષॳ࠽աތУϋǆʽɹ՝ԍӣȫԁɌÑ˛ॣʊӅඋ­ᨅఅ᰿ő୵೽௯ࣁᱝ֛ϟ౟ᇡਝݭՑݑ΁׳ƹૃႝ۝ݧ؇ͳࠍᎍ­̙ը̗׫໷ʗƕİڼņηɜᄩ͗ǋͷȔƯᇋܯлғ૗fᕗণᰕᅉÄ̑ݙ։ĲʆҍĎʿϥԫǖd˝ֺʁܭցƖȱʍձͳȧƷᐁ܏ዿܟ૥௯ڍോ઻ዋᛥዓᇽᵛቿǆِࢊŰ૆ژԈߢЬÆɠѐےҀƖخ˰ǢвઑʳΩݞô݅չࡿŋǤؽ३௏سࡓ૱åكሙᓫᎁടÃZťÖĻŨϖઔĜƔĤĚɺžʜȆ̀ʂʮȌ}ŶݧبėޠˉɺֿɒՃࠢࠗҦƟÿƷüյࣚąÊËgõùÅñϱʡǓóǱ}œJȜƮΌ؝ࢎۙހٱĉ̵̕ܙ¸ñǐžņжʂ֘֞ݜоᶐᩚѨЊ༾ݰߌۼ୲˶లڲ࢒ሀů൲ӳۗ͜ɫΑŊĤ͎фӈƌɜߊᆠbЊƦƔÞ¦Χ͚έȦࢋγɱ½ʳ«ȝǗĕĘ_ȖǌpʂĳƄoľ̂Ꭲȱ΢ɮǇɰ̛Ðѩ@řʓϬŇ߹гᡑ;Ǯ۠ؤᅐµێųôіŐÑâ·ŠÇĘKŴiǌƲȬɊƘǜ®üĠ̵͠ȇȢ௄༮t˴ŗPȬМԨ৬ȤƠǑȸįϺƿؠlɚóƎǙ¯ǽªпͲ˷ǺęńVΈϞҺŔ͔ΤĈҨßDřÅǙ¥ġG⊩Ƃr̬ԨÔीʭΰˣϼĻĴۖt_ຈőඦஅڦşo٧Ùɏ؛ǣŮƑƞ˿πȀඍζԏіą̨ر{܁ҐྭĐ܋࡜ࠓۚͷૺȰ߈ǝࡶžľŶɈ̸Äʄſংᦧп጑યݍٿʝ®Ι}ǿÝߓ۹٥ȿΥɪ࠳ŮԳjʹSɇKι°ˋTʛşΥƣǅřMōnūøǗǨƧáŉ÷ϐᷞǎШƵÏãॵށǟ÷Ձ̧ራΏૄᆹɜ͚͚̈̈b˶ȝʚˏμɁƤĵϸą°͉ʪͳ੆ ȺıÞŧƫȌōĬóÐ¥Þ¶®ÊàL˸ӠĮèÎą́ïĎňԤ෾ঠǭࠔäƼŖɈѶڞʺପŲÞ̜Ðȼź͐ʐ˒ʴɔĊ़նዬΈʤ˔ƶŲǬѐo˲ ƄĭÈţǜťʜǒĻŐ`òÎnƤ¨Āܬ⃬ڀᾢԝໜইࠦྊಢͺ܎ǃα࢘௃rUถ⏠|ǻˮƆ̈́גՕஊ࠘٢ऋĮୋҤĎச৷ٞ¤ࡊѸΦ̻íΏȂRࠎढፌ़ю࣒౰ԆĎֶ๋Ȯҙ᝞ਙौ́עӢ̧ࡰЌѷ່ૄ޷̆ौڐ̴׈Ž݀ɚ୾ܒؤ¤࡜औǩӔġÔåzȪ¨ўóŎ¸ǔ{Ȯƀɤ§΢Α̕௷৸Г"]],encodeOffsets:[[[921357,-331743]],[[922148,-328018]],[[932842,-321165]],[[936380,-319127]],[[955612,-292753]],[[956191,-291726]],[[942857,-264018]],[[941907,-259594]],[[922947,-259911]],[[928985,-256625]],[[926192,-256359]],[[929489,-256306]],[[928686,-255268]],[[926810,-255620]],[[927553,-254927]],[[927107,-250712]],[[901274,-244124]]]}},{type:"Feature",properties:{name:"Barbados",childNum:1},geometry:{type:"Polygon",coordinates:["@@ˍ®ƅ҈Պͳŵǁ"],encodeOffsets:[[899896,-220314]]}},{type:"Feature",properties:{name:"Brunei",childNum:2},geometry:{type:"Polygon",coordinates:["@@ʪ@͘௧ЧĎǙଚ","@@ؿНȬܭчߓವఖ߲ư౼߸ä˝"],encodeOffsets:[[396190,-242634],[396190,-242634]]}},{type:"Feature",properties:{name:"Bhutan",childNum:1},geometry:{type:"Polygon",coordinates:["@@Źҏ̖ʳɾUɞ¬ĎkĲȑâǓſǡýƫŃX¯ÖđâƥCƇÕěś«΁Iϩŕͱ\\ŧƆÙ@͗ǣ஭£࠯ˈ́ĩȯȻЏåЇ¹ͩ ƆաƼЧñɟŌϗɎȻӐÌĨ˴ǰ¦ǂͨ಺ೲƈńˆŔɖäǎƴȆĎȄ޸ƭ~Õħ˛ ˒qͰjȄeٜɩŲXΌɐľfìŪĥּəÓι"],encodeOffsets:[[336184,-180275]]}},{type:"Feature",properties:{name:"Botswana",childNum:1},geometry:{type:"Polygon",coordinates:["@@nϽᇰᓩ۾ഇᝠ఻άࡍࢸÃǸవस઱ᙚիŦӷҐƍȬǩŬąԛ§ɳƣĽʻȧȓ಩Ӊ܁ओăğೡॣ˓๟Щ΋˱нŃç֑ŻȱçʏĵҋᏁرȽኻΛʘӯfǱÊథۘՇNԣϙ̓ມďĹƭěҿϟǽѓƧŭ҉Ǖ×ɏûùᐙiˏʄóᇞ୍Ⴂୋࠤ௑㭬ᔤĐഥ䷤⺜܌঴਍౼঎٤ßފԮފØ"],encodeOffsets:[[154606,-304537]]}},{type:"Feature",properties:{name:"Central African Rep.",childNum:1},geometry:{type:"Polygon",coordinates:["@@༊ឝƅઇϡԏņڝഀͳˈ࠹௾·ܴ݅ù֗Ȫ̃ᙢᄡƓЕмࡹൔޟԼൕܻȰ̭֯ᮥ֒੕ޗမÒᎽݍ௷Ɗਛೋ➵ܼ܅ࠒࣹ׈ڷĐ঩Շૉ௝}ኽ͔̍܁˟๣рጟПΧປړషʃො᎝Ꮢōਂܓ܄̻ະǺ಺Ѓϲܒդ୐᏾ټ܄੢ॴЀЊРӪ۳ጼॸᏊƔಶൂҵЖǨɰḀф৚ذᔐ጖˄߆ြ߈ߢǡ"],encodeOffsets:[[145986,-226213]]}},{type:"Feature",properties:{name:"Canada",childNum:110},geometry:{type:"MultiPolygon",coordinates:[["@@۹ތŎÑƙ"],["@@ɫšnЄȾˡ"],["@@͕Ʋ΂tkǥ"],["@@͛ӂ˔ƥʑ"],["@@ߏÕҊˎ͎̔²Յ"],["@@ҚĒīطؠņ͸ӢօԠߎˤʵߊă̂ӥഋ޹ఱԅ͈Ы৮оකܤҨҞ׭Ȋ਱ࣩܯ"],["@@ǘȼಢ̹ᘢAߥөɾԍ૥ȦȰŸѿɢÔȸͅΓॱǀёϐށØʏ϶֩ĢΞशŨ࣮ٗۉ"],["@@͐ÅҟË«ђ࡜ӈڟ߇"],["@@ǏóŨՖ¨ҡ"],["@@ጮऺʒಸ࿄ை໺Ҟఠؤୈᐬᅶ์∴ೀ᫄³࿴܇ַïܪͳĲ׳ශ࣑༭ϒ඿ϋڞĒ௤ͱޚա੆Єݰ˗ߗձ׳ੌěƾՑഌ಩൨ǕʄſϏȓ࣮λᕖʿIȷגÏࠬր؀Ӈ֐ìмʩ˼̓̋Ǖ঎Ǘ⛯ตړĞȦҕণτ·ΙʉɜɃąʔӛၹᆣׇĪձԾԯ¦ҽ݈ʠॎҁ̓²Ƹᢲᇾজ۱řѢബͦḉwٶ઀ͅßҩ̾~ۑላङ֥ȾæɨՇܛ࢏ÛمɮԙX׉०࣏˦ณᴢ୭לಱ̝ҳӖѫĆާၑʆᅛ՟෉টªŕم㻃Sħ@"],["@@Ťȭγîʐƀ"],["@@ΩȹʹǴؤ"],["@@͸ȁէƫſʺͰô"],["@@ۋʦÇˊϠ`δՏ"],["@@ᨇ̌␇๚چŬᯖՉ᥸ऽц͓ǉĵ"],["@@ҫ܀׼ѝƏˡ"],["@@⏔ܥሼᄃኞۅๆ෭ǆȠ΂ׅӓȟݫƜᩍ࠸ϒ״»ЮıѹઽǷߩΠ̀ɀګƔï΄ணYǳѢްǾ࠳łछԈϱƝҕ׀ơȓͅʠ߁ḁ̆܄ࢰěćЌହȋݷռߎʺ྾ӯ"],["@@Çͥ࠭Ɖࡶډֽᘙ̀ࢧڼ૰ʼϷಒĪࡇࠁӬŖǟӥපĎɈʽÜ̢˸ॡஆ৊βѵࡾʤࡔˏ͔Зއ߇؄Àķɹ״ŗԩדኺդæԷ݅ѡɲˍՙƳӚ[ȇԙ̳֞Ϝ޲ܘʴņ഍дĞðӞ՘ӯŜፋԑƖҟǱءଐӫۇϳĞJమڣۼկɶ×৷ƛ͎ױγϥ੷धđҁͰܖ͒ࡴ૾னSȧѭஇĎќȨʧԖཧ⃥֩ψෑΩ؝όȯׂೄ୸ᄙĈѼрiɋҔśÀજॸɍͫȨÌ;ҷàȱ˺ɄΒՄŝեҾYᅨϘʒΕȊźۦ̔ܜਂՆ߼ɑħʌӒħ"],["@@֡΂ǶǾϬԿ"],["@@קžڞǢõ̟"],["@@έڌ͚Ƞ࡫"],["@@᳍ڲɮ̨඼¬തਅ"],["@@Ѥؓٵĥ୨Ћȉˡ޺ε©ʧςÛ̯ᚩඬŇӚࣱ̲༢ž"],["@@Ђहǭԟӓ੘ʗȧޗٴÊ۶तϑ̂ϭ"],["@@ͫÄт̘Ӆ"],["@@྽ࡆèȚଡ଼ѥԾѕƃǣ"],["@@ɅʱڵňςѲԺ̇"],["@@ܴÛϠ͏Կڷڴôƣ޴ࡴʈlᇿ۱ˋࣅ˖وÐॻϤۻ݆Ǚܜ߾ë"],["@@ó΂̺£ʅ̝"],["@@ՊֶŋӁнĳ"],["@@ˁǶܘАҕׅ"],["@@f࡟ѧܺȉݱҋƱ~ܚ׃ՇɊฌ̾ߩwश૎ݟ"],["@@ц̱ह̒Դ`"],["@@ċǏğψǬȷ"],["@@ʳǱǽЬѲɹ"],["@@®Ʒ۳ǨЇˬ੎̛"],["@@ϙǔŊؒݚ̳Ӊұ"],["@@ʟ}ڶɪѕȫ"],["@@࿙ٌ௢иڏ"],["@@Ԕ͛थςђ¥"],["@@ݮο˖ӷ̃ॱுԄѣٲȮٌ۸¨"],["@@ܴ»Ǹ˃ࠍPĝ̰"],["@@ࣟņ೽بႸ̽ԦЯ"],["@@̚ԇէܵ֫џఓōࡗڄͼ৚ᢪŎ"],["@@ਲՋסˉ࿛ۖ஌ŀ"],["@@࿏Ѥ˼ɀௐЍƄʕ"],["@@ҮѶ஄աⷮ౷යॳċѭ቎ǒ໤ܻऱڧᰳպԵӰኻӠ௫ሉୗщྗಠឹʱßۼजՄ࣡।যᓠΆࡴূϧ˔Ջ২֏"],["@@ભМՑ׆ৄΕؼً"],["@@੤ǽ­ȉ႗̾ଭߨขσЎϙ"],["@@հcÑ̿ӝͤ"],["@@οdמˮɝ̑"],["@@ȇ՗ʞƥՆپכ"],["@@ᇡĴۿՖᛞƫ֢ҫϝq"],["@@ܭɦםמŐҠ݆˙Ҷ৉"],["@@ඊͭଢཷݕϽᑍó๟঄ǑהΈݔྡྷĬ"],["@@ӫǆʎȤʞΩ"],["@@୵΄ࠜLΚΏ"],["@@ಯЄŏζٖěުڝ"],["@@্Ɋ͎̀قǇÌϏ"],["@@هěη׎ীӱ"],["@@ͫ·Ƨ̲Ӕʹ"],["@@ґҘ৚ݮউםɻ"],["@@ÔϏԕ˂҂Ŏ"],["@@५̆pϊશǓƹӻ"],["@@փАݼʭȷơ"],["@@ӌѧәçԑӈȦұЛ֬ܖñ"],["@@ܣǞњƢ̊̿"],["@@Țʅ௳˫ਚԲ"],["@@ĘΗ࣭Tώ˧ᇕДҨʎᆶu"],["@@᣶ڛᄒ৅ৠǅ๻ߑᐱǎᏑڞٗė׉̂Չƹ׷ɎɁϨ๾Җ֣Ք૪ʹࢅނْƠ৮ө"],["@@ࢌˁ༏Ȏۄô"],["@@ŅYҟÝٽѷ׏ڹօԏݻ൓࿁@ᅽPխcɣģ׃ʻ̡̬ĮšƔϫǌȣŸćȪǅГΫኛࡥ໱ˉࢳ͏ߥӑߥӏՋBளڨǇ؜ࢌ࠸^ቚ|ɐᐛ⁾ϟמएϤဿڠĜЈн˚ܟí۝नٻYԛĶޝߚӍǶ෫՜ᠡठ಻ӆ಻ӄ໯լኣۉᯗ̠֥Ɩ׍̡ຍհۅÉƋ@͗΢ࠇ̒ऍƎɿשȱϋȀႇ̢̝˒̺̓ΡֲļڷƢ͸ࡍӡHჵ@࢛@ჳ@᥏@男@电@电@⨅@࢛@࢙@᥏@⎫@ĻĂɷļÿ@Ɠɑեوج̲ާūÇݬԋهଢ଼Ңq̖ҰͯвɄٓŰȏ࣮˟ĚƘۓ՗ɳள֊ࡋࢾø৬ƧߣࡅʁͼýУॉê޻Ȭਊˀ௳ËǦΞ͍ÜŵђƁͅ࠵Ńٌ໋ʾৼබɈ೑VԓԥԛѬʽ୊؂ττĔ೎ݟ౏ࢶ˚ڀȵȴ²ыஷ߽ˍ૭޹ࠦՖΤԘաС¢௬֩ȏ։۶಍Ԕ̫όԠϤN͗࠺̳ࠤOԸ͋ܝҰুǆ͑њϦʞࡕ˷ेӷϋባ੒˴ࣺގɐࢭŉ࢕՜ीᙲկɐş૳طፊ̉΢ٕɅʎσôላپܭöʏҒԻƞNɎ٥˰Üƨ¼ŢȥǨϥ̈˿ȼఫ৒ݿؖށؼ྅ޢ̇Ȫǹƺ܉ю׫Ȧٰ҃ݱςٝ૓ˣȆ˧Ι±~ۉҝKՇʓʭşʡȏǡϫ۠Ⴅࡴഅݢܫ̰zȦʆ½Ơ̙U࡛}ҭ͗ਅʼÿǻӳĞӿĠҙ಴۩ደ௃ὢݩዮಥὤ߹ዮඡὢ࢛ዮࣛደ⅐̡❴௿ᔄǿᅲѷ༱ٮ؎Š۽ȎǶҀܾQİΪఐtƣҚ୞ϵʛ֭࿪МɦѬ࣒Þʛǲنó࿎ҌמӲപąζɮ̈ѩჍޟራʽ௯݁ۓ߳ضɕүȤ൪Կ԰ಖ߄ढǁϊȨҚեैତኌվˉω̶ɛ̪Ő๰ଜકΞŲΐტثᩒဿ༾џպǠףѸ׾ࠂґĈؾ˨ޞ࡭ؠǥܭฒÆμश჈bᖄΑሰص㤸ਵᖴǟՉຸ̬õᅀѥၤޥÏ̑఑Tӑ̱ʱԥڤȅも̫ᦊʒධҎ஠׉ୈģܴ׻ȔЀ࿸އɤίफMᾈ༉ೕވϋմாɯᭇᐸuӦᘦξٸ࢘கǝΡᆧÏrΙ੗«व͎˴ۮⅸۖ৺ȳலओྨЫ֘ύᅚŚᨤࡓ๦ħᗐ͘ᣄʥࠂǼࠧר᯴৵ĄՒஉˠݧū༏ঢࢎĹڢƾҫ̦সÓதգࣾŮĥ؋ਸҼҦခߨՑ܊Ķļ̑ܞÝɚ͇ኧĒሌঃॹܔ୦ʁ۬๻੠őٌఘŎไ௮ඍࡠsӗ৭¦Вܐ١ѐজɴĮё¹֮ಯË৑ѦɕǇ៛ԊᄷࣰïڪۚĎ˝ׅ͘śࠝό׭ॲόϴਤǣEΌভǾ౔ࣼ୼ӥੂ¹ᚘ੽۾ࣻ⒖ປ๙Ŷŉ̋પƣ঻ࠥਲZঞ̓મʪɛˉፈƵՓщړ¢ᑖࣝբࣧ੔ӡÛᖄᢒݿᄢౡɓ࣫ĚٸݳᷚႹ०ۀҿᨲಐǒ֭ǂxԦషΞ൛൒㕎·עȫҥʅ঎ȋҢίှƗӧگ૶÷ոћߑѹଽଡ଼ګ̘ŊڲݓኢࠃҀढ़᜝༡ṏഒڡŰ́˗ڀÒඊุ֙਽गǪӟȉ᝽߮ጵĵ͂ηᄒգໝჟ঳v⤧ೄƻƱ᳽ǲࣴ˃Ȩᕘ̵ៜॵ▐ͱ҃ᗡ੟ёࣵ˘Κ͙৭Ɣ׷ͲϙėنЫػ¶Ҡ٧ફʑᣫզػýᷭߪڨفךÅηʌᨈֹϳ̧চ͈Ꭾ֭੼ࡑ੡׉ᄣଜԕ૧ĹƋѱ܈НཉѰ޼ͯѡكӱrހϹޝٗRዯΛʃຸ᩷ċѽჸ߃юफŻজሒ{Ẹḳʙࢻ⁐ݢ⧎ࣣᕄ࡯Ⴊি⩠ઁࣶё׿ΰҦᗘÞ‒ցڈ֫ˢ౟઄ॗئᄅᐨ೽ɧұݶ«ᜌற؊د՟ם֐Т߈Åᑶ୕౑ै̼পߐο׀םbږࠝبԸઞ੝ࢦቻᖖȿޠؕɎłǀჹ૶᷎൲ୂ᎜ޗༀྗ႔஋߲⊕๤Ӥ༞ίڎ࢞ɏсʀŏݰڱƪǨźࡷؾǦʸ۳ɐƎϚ୙zǠ࿴ඥآँ৘৔ٮ₪ַ˒̥ٶ̒၄ѯǳɊ൴הᡘޫُ̔ϢȂۊȱ؂ͷȏȻԤɨ֊ǅƁЅ౾ؽᛂȫ࣊ӅټЬԊџ§ࣩ݌ࠃᇁōዼʏমಇܠđ̆ॽƻŉһ̠ѡѧ࠘ǃܬѦમ°ࢂέ۔ׅɲܗ७׏ৰͰŰॼնщŏΗȼתޞएşӾనتʜڦూ߫ڵࢌ͆Ҕ؆Ćࡳ΄Оɪσΐতȁ५ͼѓՌߢ]ԃ̬Pॄܠƕ̾ɏчΥࡸŜوܡȬǼલۙǟ̓ӺɱϐŰҮ̱ࢃ̿ඊʦ҉Ѯ|ڤҡޗލќ̬லfGؓ׵гೄ̸ٔПެƉѸڣࡧ˝ఒƵƲϭ࿖Սт݇ᝅ˔ኊ؟ʣɯ૨ƕϡ̃ܒϔϙəŗࣨ́׆ǚڲӑΦźÜ̟ڴϗʋषര֐̦ʧՒ¯˧߷׺ࡺ״őࢰۡࡰƝܬɪ໚ّ̕ҙఱΡơЍᐳӕ͇Փ׏Ÿࠚωǅӵᜒཬ౶ʪߡ¾ĞʮဂƥપࠧՋЗך͇؎؂ܞÌ෎ڃ۸ׅ޴ౝइəౖƽĚʙԭæশֹၙຽ᯻ε༏ᔛ↓Ӆ֏˾႓Ƹ䣥ȩٵӥųกၣͷვᖅᵛָ᧠ىːǓÓߡࠝഉߡޓୁʓᅙ੩։੍णՇ؇Þࣆе஫ޅ"],["@@ڝƈјَ֟"],["@@པƄfژࣆĆᶴ؍ԯߟӞů፲߲඗ޤ஬Å᲌ॷ᜼ᇟ࠸ɕҜ׺ⒹᬀfѼ᳨Ϳ᤼ࣱ☂ᨏٞଭ᜶ॻ૶˙ț͞Ḇࡇ҄͋দÃࠨ९னϢµͻ௙ͤձ˹ɦϋၟȼਦϯ̘ׯߖ؊ݪ}੖থཡ҅ṕŊ੓ņğή៉ʪ઩պݷওཷƭ࿽ֿ㷩У൹ޖٵߘ⌋Ƞᇅ͆ᒝඪ⧂ׂ⍖țၲȈ⪁ࡤ╍ɹᱩĬཱྀׄɟ͞Ⲗਈ⯣ɩًǘ׊њ๻w࣍ʴʭӸࢮبܷτ̠Ҟ⿤ငࢶǗٴԕ»੯"],["@@╥రƬӘᒸǆ࿚хѢׯҹࢗ"],["@@ᓾ~ᘔωῸ฽ᵛŤᙝ̵ਯƶ໡ࢾಗʔ৛ࢀាƫ"],["@@ු̈́রA݄ˡ঻ച౹Ʀྩ٪ծւᖬх༁נʚƴ൙ĶஅڨȘ԰ᑸΙ᫑ۖੴ޺ாėʻΤ᫠Έୠɇ◂ካۗਅߞ̸ؒ௓ۀڬ׶í݇ΎʘʮሂϵӤԡૐεউӲʵ֘᠆ϳᛡپ֪ܺ♈΃ዄޛщଗঈࡪະ˗Ŗ̥ಛ̗঒÷Ҿ৿६पˠŴࠓҮШ࿎ڽݏְζ̺᥌αଘЇ¨Թޣ{ڱכ଎ӚࠨǇی̚Жųд׋఻߭ॲƶՀ࠯Ǐ২౺נṌԿՄ˿ᑳগጚήìԉ֜ċъࡂሼهऊڷ໳Ėၡֿ௄Ƥᣢʽ౞ͩ͘͏Ἱƞࢡ̛রǔಔʃԄف᫛ǎᚼ·࢐Ѝᚒƫʽ̍ݎ׋ɬƚ͈Ƀ̧ЀؼƾӲ৅ѴՎҊͧ֠ĸߪٿҁҚװ΀͔ճ׼Š᎒ؗƾөಓRᄾȗɠʬ೐͉Ёऋ૴˴୶ĭȌ͂ᓪߝÑ̋౿Ȑཎ؁׫ƙೃɔІЕඒѕଵ§ࠆХƙѝহकѬΞ͓ͿŃڲįۦࡿŘڭᇻն̇ԜFٍᬽฌܚఊׅՙ൳ǷďҮرǉ᧥ૌɭԭĲڂׇ໣ΚᒴॹŕɻॱĂႷَ҃ŋ௶˕Ҏҭ޾ɑʼҳծǮంҕɿȽᔮஓɥвೀثɘμႾ֫ʝ˳বƱi̗ڻs౐שब@yʣ૪ҷΆ਑኷୎ଊ૫ൂޅॣzٜۅᲩ੾ȜʹпĆ࢛ՐʼѩÅ๨࢖۟፛ڴݷ¹᜜ஏ਌ǭȉⶎᅏʼۻ㉣ߺළϖຏޔஹWೳ݉͢ȶŀˆڱũᆫ޶ࢴπጳͤÛψӽƽᬏಆЦஹW΃ѷЦەĊݏжͷơࣂ׻ጩØź̑ᅻΉኗ˜ޡ̶ࡅঢॆֺϣָỸԏ൰ࡷōϐഅ٨௚༂Ӯଞ£ᮉ່༄ఴřڲلԆ➓ើ৉b٥ܢֹզͿԉg္װƱՐᩩޣ֗۞੨¶ȢО፭ֺќݟȧݿƔƒ̾๙ƾଉࢠࣟsᔝ࠼ȥғಸΡӢסॣȩ⥁ҨᏆ߿ཥӄ૯ʁቑϬறĻṅͪөǗ஡ʒݧܒᔱөᔑֈᘯౘḢˁଠư㙧৚ጭᑾGᕠੂܐᬼ˶Ꮚ˽३ࣷĤ೉៺ᇓὂ௡ᒡދ"],["@@ቨӑᘸΔ৒ƕ؜Οޟο࡞ʵଢ଼ি௮ȘူࣅߒȂ֢Λțԧసધളҙ଑ò׻ьঈःד̵୑Ȑⓙ႐ඍςࣹKᣃ৔͆۬ᙈߓರŖɥҚ؊ƿ౥ٶ௲ఁ͘މȇຽӀݠʪત͵ෳތၸT́Ͷ"],["@@૷ųऻȆᣬӌԷ՝"],["@@Ꮴх᭚¯۲˳࡛ᜓ⎿R।ȣ࢈փı࢝ၽᩧዔိᆈ႐í௏Ӥ³Ϊ᯺ˌ"],["@@߆ȒҒУؾԢᆞ⮐൏ЬЕ〉ၕЉ࡟ੋȫʲཏുʽ׃Űኡ޹ឫರᨷڌভ`৸♢៍ಜ㉦Ѵ╰ۧŇˏ"],["@@ٿ΂ʺ̾ɣ"],["@@૕ǦӒŨل̍"],["@@༉ʠόقႂկՃͱ"],["@@ܨߧྏí⩽ࢪɲईฎиᒔҫຒࢧ"],["@@୕̇Љ̆ݜ͖ࠄ͓"],["@@੃Оӊƨֺօ"],["@@ݕŹ୓Ɇᒪ૜ɿ஧"],["@@ޫȃ̪ڴӂӯ"],["@@՜ǛٯΣ௽ƪΌ࿻Ķɶޫvҳ̤ᠸ˨๖ٹ"],["@@ዄ̗ɧɭᄉ९،ݘƄˆȃ"],["@@ఄཋ਀ȟڢ֡݇Ƞȓȳॸ٧ߛǐɠϓ⊵Ç߫ИҰżೃΪፔٺ㑑ЯեȔւ؞ᔀǡࡳńಅՎ̞̜௷ʮ঎Ⱦ⏞૩ࡃќࣾÞၿɎתƾኇӀ๺ʐዎύȳԞᖊԗ"],["@@ܑÊፚ̂ಇ΋"],["@@઼ƏſяொȘीӃ˵ޖዜǫࠦ۫Đ௑ໍγᙽ¬ࡅ˂㈓ญᐉĄདזᑆ׎ዂĮ֚Վ⠳пףѐϢΘঝ¢ĢѥڑĄҀп޿ʡङ̶˽͡ဇҘፁƐƆԒጜö౬дᎻȻ൥ƆЄ˲ᯂ˪ᲷƎΈՌ᠂·ጅɔঔԘะĹ઼յኺ[✘ห⌂e̟ΚᎵԨɺӘᇿԊ஀ম௲Ǖြ຿"],["@@ድԘҰɮঠɉԦԻ"],["@@ᗟŘථɢࡲ͹"],["@@ᒸ͇ʆܝԨϮᩖƈპЯඋę♸ΉÒʥ↛êⲘॏĳԥິʝQҜ૎͟ږƜլȯǨ̖ᫀӿΟ̚ᬘٮാƱဢǴ⌚ҋᙢࠟ॥ԍጮʻݍȉࢅƺࣲ܉ᦝʡ໅źᖙࠪܤۻ൥ǥ೽ɚĄȧܳȆНɩ⮥Y॑ݰୗإᇿż৻Ҟñϓ݁`᫯ഴॳಮᨻଐ⇝ȧᆍӼѰɖផΆںƂয়ϸೖɄ᳚·"],["@@࢓ԑొѷڹčࢬыฟЕÚ֓ໍ˦ջࣂৡٷȾࢉ௝Ґھ݃у̳ߓó൥ߜ˥ѽᕏóҭӀӝÓ˨Өฮɢᥨቦ⏐üֽˮܴȂጨЭ"],["@@Սūཙʾࠩ۴ᛞͿִԅ"],["@@யĜᤗఢނSᥰࢽЖѫ"],["@@⑴ŝ˄ؑ⟯ ࢋӜ঄ȴ"],["@@௩ìҵ΀ᕲœժɅ੻đ"],["@@ۣõ༝Ѣݴδຎܟ"],["@@ฬÛ༱աജǃђՓ‍Ϋဵόࡣ࠴⡀҂"],["@@ಔݝᱽƾͻʡ௡ȴ္ƳʦϘᨂ׸ዚȍ"],["@@᠜̿˛υਚͯ᫋׽ᆃِ਴Ǝᗫ϶ཕ৊༌t᣸֝"],["@@៎ঋक़Ҁ࿖اૂ¦ዤՏ᠄ྷᅯÃᔣ߲᧧Ŭӷ˚ᄙʝགྷȴ܅҈ᗆইɆ࠺ɪมĴѻҲߙП˟Ӭ್\\ҵҺ`Ǻἶi"],["@@ўֹᗱ϶ԝѨ൬èআ͋"],["@@⋐஽ᡐȃ࿜ޗ௼ũ෹ۈະĹмؙᒪҕЄ۱බŬসΏȴ̐ዌܷᗻۙˍڛཏ܌ഢਃൽŀघࢻḳसᎨেႽϘÝЋ῭θ௅ͤහȂ᠑Ʀঝ͊ܤÈጩ΄࿨ߊᅸȪ᨝ǦඏΣןʜ೷ʟ༏ՎຌҴጥǝᐝֶ࣑Ӭ⎎șɊ͜ၛƅᑙЖ௰Śྭߊ⍲ί⁙زʄӂᢲŔᔟԪᇖ៾Ӌ"],["@@ㄮȩᏉؓ⦎͜Äɲౢˣபúᒰ߹ࣾƼᎄǧܨֽᧃ঩Ჷ˯ᏻܡ㑾Ҁ⊱៥᝗ΖᄶۅᡳʙᡆáɯݑກƯ⑓Дቀˁ়֟ἕȩᤅɖᲨ؏ሪuͺѡ㽳Ė༎̑᠌ƞ⎢ַҟឯõᣜЩș܅⃋ƛનख़׃ЅᶓÓᜣҼપЛଵŃݲȷⓦkၦڵाȐ૪ӿyЩὡࢹਖ਼ڒೱÆግԞୠֻ᫗ଛӾܔ܋ഡś᰿غŸүೣǄ̰֯ӊѿད¨ੱࠖޤ߁໥Ŧઇ߂஄ب៦Ǻᑡ֚ืޮቺǮᴊ࣓ၬɡཊŢՖআȡ͌ݟ਋ᤫÐඵࠜ࠶׶܆«೛Ɯ׋٢șधঙÛȿהઁәறŐϭ೼ᤒ̰ῂአҢझǉ⇩Ⱥ̣˚ເơ䋑ᄞഷऎⰐß⾼௓ᕾǯϦƘໃÀㅻඬ䷀௎ὯȚ಄ੲ♙Ⴥ᫉ǥѾѦ᪗չᶣĔ࢓ɘ݂׎ᵒԂ⋗ʧകࠇૅ^᧥ת⚚Ʉ࿾֖ᶏҟ⊱Ňख़̼ऴǨ঱Ǭ᧊Ѵ໓ĔᣏΥɐԤᒗƙइʦ⥨ऊᑤ̭̪ȶᩄƳᾏՆቆԬ⺮࠭҉Ϡ㯚ࢱ㾏௘ݪˢગʘᓰŷ৑Ҭ╬ɝᶃ԰♞╨߯Ьǐ☽ࣆ⠞Z╠ٵ዗ЦˀЄ஬®᝴ѿॵКཞö୎ɝ"]],encodeOffsets:[[[875198,-136140]],[[856471,-134115]],[[870194,-131911]],[[837725,-131650]],[[838193,-131968]],[[869224,-130670]],[[861665,-129241]],[[865351,-127016]],[[884527,-126588]],[[835845,-133237]],[[701664,-121370]],[[880464,-120730]],[[881373,-120305]],[[700080,-120683]],[[863060,-122080]],[[703305,-119343]],[[697818,-117861]],[[875103,-115417]],[[695292,-115588]],[[815140,-114284]],[[693379,-113059]],[[810688,-112114]],[[684326,-110891]],[[692444,-111589]],[[690767,-111103]],[[689045,-110967]],[[687381,-108810]],[[681292,-108349]],[[685214,-107390]],[[685809,-106597]],[[807363,-102676]],[[809800,-102515]],[[849186,-99001]],[[805891,-99107]],[[828598,-94948]],[[837378,-91329]],[[828532,-91673]],[[803170,-90343]],[[834349,-88604]],[[832823,-87204]],[[797646,-85751]],[[831879,-85350]],[[818974,-85377]],[[790863,-84271]],[[799487,-82863]],[[802436,-83072]],[[779597,-77979]],[[779530,-77122]],[[781257,-76517]],[[723308,-73221]],[[826467,-73082]],[[723057,-72139]],[[799893,-71097]],[[770660,-71228]],[[793981,-69628]],[[786835,-69981]],[[728830,-69400]],[[795709,-69556]],[[734547,-68908]],[[737774,-68307]],[[737862,-67743]],[[784802,-68201]],[[758911,-66636]],[[789052,-67387]],[[734466,-66696]],[[746724,-66215]],[[808331,-66304]],[[785354,-65829]],[[782057,-65631]],[[742288,-66026]],[[764658,-64743]],[[835845,-133237]],[[736409,-57041]],[[698722,-57979]],[[718046,-57087]],[[771717,-55083]],[[763256,-62294]],[[727069,-54288]],[[730966,-54499]],[[741146,-53701]],[[684480,-53833]],[[731291,-52705]],[[735705,-52762]],[[715670,-51313]],[[738085,-51336]],[[731446,-50021]],[[734065,-49369]],[[684438,-49831]],[[765997,-48887]],[[717210,-48644]],[[711991,-47094]],[[725464,-47412]],[[717820,-47104]],[[704406,-48529]],[[741956,-47301]],[[691769,-46657]],[[731286,-46196]],[[686165,-44966]],[[739458,-45227]],[[709412,-45570]],[[726255,-43796]],[[713796,-44057]],[[688738,-43898]],[[694748,-42947]],[[694587,-41455]],[[722545,-41780]],[[705572,-39640]],[[712748,-37825]],[[722346,-34683]],[[757182,-29544]]]}},{type:"Feature",properties:{name:"Switzerland",childNum:1},geometry:{type:"Polygon",coordinates:["@@ɅǇśʏȁƽЀĒ@Eखңל̘Ŗ¯`ơ¡ûɹ̅ĝƻ¿ʳЯǄŷeţŉšŗƙuā´ÓGŻĝ˧ŋwíxĉˢŅbɡ·έŏů@ħx{ЀÓGǹԃءϵқԋѷʯҢȨŪֱϘҀșģȱƓЕȏѧ՝๷ɵ҃íŷÀµ¸«Ȳăþƕʪʆ֘Ε¶ͅeʿĉʗƋɟ̹˳ĹǡYÙpÞƚǌÀǚĢϠ٠޼фŪǤǾʸЪŲŐºĀì϶ɼϬɸĤþæ°nƧŬȶƂϰĈξŵ݊Φ჎TʞØTufɏgã|RÊŘĬͨĲၞ֛Ƭŷеѭ@A"],encodeOffsets:[[136891,-127054]]}},{type:"Feature",properties:{name:"Chile",childNum:26},geometry:{type:"MultiPolygon",coordinates:[["@@ҵŜԲ̺»ѕ"],["@@܇̳Ʊʴ௉ĭƉՠᑴPĚЁ"],["@@์əॴżֿѫՂɟáࢃ്ќʊҴԟƺԗ˽Ţϵпæܽڔպר̾ĉ"],["@@ʎɟࡤƂҝѳƙ໯٢आ´"],["@@٘ǭווӋ͊ਵƻȧ֦٘θਐͧ"],["@@ɏߒs˨ӭډѯઝ˛վהԧî̋υʗŞָࡁШঈ̤ॸǅ"],["@@ಞͣ۞ٍ߿łΉҸߕӡݘ҆ϳ"],["@@ᰡ⼧ျ[˩͠၅ɑ᝱ШßɔϞɄ෨wܠʾcΝܮī֙प˴Ԓ৞ָǅߧՅ¥ṗмկग़ǒ٬֝بͶຕژ¡ӶͶόሢۂҥȀนīƚٸ߾͌˃ѐ߾Ʒ๔ݢ˘НࢠȰɬǝ"],["@@Ȅݡ۟۱̱nÎ઄ހ΢"],["@@˃ˇ࢑MݷԗڼޤढǄ˰ƹ"],["@@͋ȋٌ̂؂áַϝ"],["@@࡯Ŀذݐ߾ɗֽз"],["@@णܟ֋ąѮʨ°Ԅ৒º"],["@@஧ኍΕYĀٖڀڰ؃ʓֳ܍ऋƌࠦږǿ̢ࢤȤŧ˚ݐт΢Ԗۆ÷˗࡝"],["@@ıІۈ઴ҎǠǋങࢗ̿"],["@@ޕûāઔȸϐސ̢ů၉"],["@@ɯǒܾĞԍʯ"],["@@ϥƥύɄౠરԫ୍"],["@@ɣϜ̠ʎûة"],["@@׏ǳˏȔ̘ߊۄ݀ϱ۱؉"],["@@ݝѯiണݝֿͭ༜Ш϶औȌҎ֐Ә̛ǻˏ"],["@@ϿɃΉŐ݊Ĵ"],["@@ܫҗ˰ᎨỜঞɥ­ڑΏؓޫˋф޹ࡻӽɟл"],["@@Х̖ȘŐǃ"],["@@ΑçƊɲɈǉ"],["@@͚ГిᙁᒣߡࡳߑɌޗرܽH࿡֑ۿ͒૷ۓύ֛ଵႭ೵ફާ໱֡ӳ̻ၓȒɏΥХїOݯ๙՝˭ЯषҦࢇ͉˭ȨᅑΟ܁ԪǿܻᑉӝǡഗቇуɡĦءϡ஻უਗաܑڟᏓwሱෙ݋࡙๷ЩóȥࡵҷΑÆҟݍۙߣᒯkࢱवҝݿ࿻׸փѓʙĉಧܷ৩ௐŻOˉЫґ၍oč̑ࡦǙǎ߃੿փƗ࠻ܛҧƙવۉЕ̓ࠣ௡ٳމ࡭ƔԹֳͩםʕիࠍএϭՃٙۅΧͱُ๙T൏ึ̊ʺǕଷኍҔ৏◮¥ᥲޫෛ͸઩ׅ᪍ڹၕᗉݑɷฅ֚ŭق઴ʺ˅٩ʦĔф֞ୖѾКԀˋľᗉࠗॣܿݗӢఴ઀Ӭśࢬπ౺ĴഋȌ֓ɹѻȬ๿ஹއՌ঒̆ԐӪЧ̥ޏƏ߻ļƐதܘ˿ӲΔވǳ஦Ұƴ֕ѱЯҸ̂ǜۖӶФ͗ѐۋˊݘޛᓉեǽպލʴآƖЂ޾໽Җƃࡢ࡬¨јϼʔѐ˵Ċ݇դˑࠞী਷ݟވҎÇ਍۾ࡢӬݚ«ԝӀˎВऔ[ͣДެࣶ૥੹ȉɤतᅦࠂ˒ॅ|˒ਪᘺ́͡ͼƘݸݱؗهĕ׻׮ϼ̞Ӝʟ͈ȢփΪच֊Ēࢦˑ¶ʍ˅࢙͒˦Ԣഋл]Յ͹Ԥᓴ௠[ْፌʌ΂؆Ѧȴɺħ১ഹयίैƘӴیͮĀȯׁॽਝǺóஶ๸żम઼̚טʃ࡟Ӓђ׊໮ղʺ̾ȈΜে״Ά؆ήĄφԨŪېढَƶࠤ̪π̎Wå֞Κż̂уȆ࣮اǊை׈ɠͲ١ѓ܃ϾࡍשߟfȷêϘΰͷƜtࣂ྘ᣞۨ϶ࡰై©ཾӰਾƱވˢߠʜ΀ࡂ¶ּੲᆶᐊº϶௖મଞᐖજମɈఢईঈݴ⮌˒ژ۪оӜૂaྪ֪ؐॼኊӌԎಔᬖƒ௠ࣖൈmஂं᜼̕۞ǂѺ؂Ұொ‌NපӺᗺáᴾଚ̺ΚӘUٜ϶ŦʔǢȐƮ˶এÔµϰȓƧȽä̳T࠷´Ε΍Ƞ˫҂ғƞĳņĝÙP»̕˵ͱέE«ɊʷzĹƟӿͩŇޏԒϝˣؓؼࢽŗ܇ˮዣƥ݇غƵӺĈϨö"]],encodeOffsets:[[[838925,-408149]],[[840490,-406450]],[[834471,-405809]],[[831403,-405670]],[[831761,-403392]],[[828852,-401888]],[[826095,-400363]],[[840714,-399627]],[[826905,-396838]],[[828066,-395874]],[[827057,-394246]],[[828204,-393199]],[[830044,-389217]],[[831211,-390066]],[[829127,-389018]],[[831721,-388550]],[[831330,-386492]],[[836028,-380638]],[[838802,-378265]],[[840497,-378152]],[[839043,-377100]],[[839509,-375553]],[[840154,-374240]],[[836425,-347781]],[[759872,-330034]],[[874702,-318253]]]}},{type:"Feature",properties:{name:"China",childNum:15},geometry:{type:"MultiPolygon",coordinates:[["@@ʆݫࡹࠁӉ௓ࢣڧఉԡᄫ܀LႤඞৼɕhȰʰᎌϦࣰǥǂΰԖ͛"],["@@̚u¿ʵӥöʌȶ"],["@@˃ˬȜgƿ"],["@@DϠǬ©ǯ͵"],["@@Ƚő¤ʄǚű"],["@@ˉÃîҤʞŅÁ̙"],["@@˃Áǲ͆Ē˃"],["@@בŐĹ̄ڮǙ^ɹ"],["@@ܿŸִًൌ۫"],["@@֛ҶP͢۵ʄӹ௷߿ý࢑࠿ᇳœҤ࡙͛ӭᎍ͐ѯՒґı႙ᆻᜓহஓ৵֭ιय़Ğᄓػቻ௫ځŏą̱੓̗ėшబрġʠѢόଽI\\ΪնɂҢبʂૌ೚ࡣॶʗ˹୕ŬཱງᜑੳথౣำʵԩΦϝāשݵǛԙϬۭԐ˻ዊ̽ʲމΥࠡۀћ২సਸŁŲ઄Ύᇄ࠵࣒ƶ౼˯ԻݡƐ˕Џʱ࡟˸ኯޫԫ´ưуЍçȯى՛ǟőϸ̻Íȳȅϰ̝዇ໍٙ৥Òɣᕤਜ਼૦ᬋęߑ਀Չľӧऔ࢏PʧЏpۗ͠ࠉzॉՊੵϝ๔ńƆοᆠ૧о࣓ৣ˵ഥঅߣôַ͝ڈƐ˪ϝൢϤࢎܭࢂʏಥࡹ२˖Ņੇу˖ԙƙϸ՛̫³ʲǷџҹʨޯݗƽˁɺѵ٫ҩÓǤ˅ֵ۟|ϕݱ۷ҷ॑ғƍıՈʿˁʻŲך৙ृصۭƮДϯيŬəउǆ҃५ƀȸӡڵ½ǹع؉Ȼrӏధ^ș͗ΫiѸԛডࣹΡTʕ֝ƹˮӁ̣υƴŽѡԵɛƠǟηɕǧԻӍĂ࣏ЕҡŜ̝͟ٻɘޣ҅ѕ̴ʉԓه։Ýࠧ޲׬ɣÉѫэҊ܅»࣏ࠛͳŝ̖ͩڧ́ČԙϓӉҊƧՉ޵ʎ޽ٱಛŁਇڗͣˤؗࢡҒʇƇωр΃Ͽԃ৥ƺȦȊإਾâӔצްՑİʕϊå΁ࢯʣĉτܡĚΧ؄ʡTb؇ϥƈȏ̡؏œҋͤ࣡Ļߓ۶ڡŬǯୢհتԓ̂דœ˓ʦڷŁ஍ीथұѩ۱܇˅ӧɌӗ׉ڣը̱ѡ͹О߱ܙઅڴƩĩЧէƛŗőāعrǑɬ­\\ëoĉÃÝŵéǩūŕį»Дज़VŇkϫīٱƾɭmŻŅ¿a΋Ȍ׋őïµÐoŪƞėƢÜʲťĸĹфડٍ৕pЯخU׼ཏɖࠢሂǯʘ஫˚Lॼ̉غϬӜ൉yลѿώ܄ͽלǖوΦɆʤࡤѐĞ΄ذфݼئǋҴ̀ʾɸ೴ᄄеȐΧʉϩ෎फݎƉeˇΓȃɝċċé̅äɻۓӻȟƋྏތকǞกࠝᘽΥ੝ץ޿வ௡ńປĠÖĞDƆçƢÑĖW´ŀüƬƆǢåǒıȔčlɕ«ʃX̕ʰŸҒÔκדɨǽŦĽe΋ɏűWٛɪȃfͯiˑrĨ˜}ÖˑøʛÆˉpȃȅčǍƳɕã˅œƇŃ฻မŝŀƁƬ]ȶʂռâɨÛ̴һȖΙǽͽħ̓­õaƓգ՝ǃܡf͏ʍȬǱĚȓæș¾ÅňËÆǍlƋ¡œ˗ʕýǕèɥƖƳȸƉçéͫǁÅċ¢ÅƐƓ͚ƷȒǉŴ׏EѕȍÀÍŲĸʤņȞH®åzūfӕűűdଝ޾ą˨ëˎ¡ƜǍøǵÌߑɋ߱०቗৞̫ךЋĂғÈԋŏяճȃţΥѴɧŤඋجîʴչҠգǠ֥ɜԁݶ߿ǭĲԔéюưհлԲÏȌ{ό¥ŢކĒfƁ̴́מ̶Ŧ଀õŖŐˌŨ̷ːƭȎ£ࡌ·Ҝ׾ӔˤƓĐઅӜɻݾŃфÒޚԩމ­̻òхĮѕƎɛňýȃŔŹĮnʮŉoˇğ̗ÍǁCÉ¦ĽΈ̻ƨˎݚiǞcƚƣ˼ŵĶȳŀȕ ʳûí£͚½â׫ǒɵ¥ƇϭȈȝDƁn¹ÐɧǶB®ўǰʞäƄÂ|[®ąƢÃŒdրΈϯӚȦ኎ੁҀםˍҁƬőֆ˴ʰ̟׎ƔϒbŜьĜɘȨØkʎçňTĊÎÜĊŎǌɦƚƞቶٖcɠĬŨê|˜įɬX˼Ðڌ͢òKň܍ЬM࢜˸ĞǭǾĎȲƆˊ˼ƄȌôǴØČŠĒ͎πȠĤǦఈĻ̪ìɒê̒YθÈ֤ҢøȬȒĺۦ͊౎ԀੀʸȬɒƸ¨ټ ®|ïʄ௚ޜ̶ɷϞࢲˆҕᐰ֦൨ஓϜҐ˜≔ސѤх۪žӠȽͮڞՓϤᘂ∨ኮӗജJǰ̓༤ԶҊђÐ๖ۨੂႨ͎ߘଡ଼ંĄȊqɪĺՊĬŀQε͗˅Ǝȩϖű¾Ûʋͽ٘ȃǺƵȰ§êƭɑȚŏ̂ƵǂɨȗࣜÐƾÇκЅňSǨ´ŢĤƔmĤ̑ǊīʊÍ±Ų͹²өɚЯńɍǐ¥Ɔç¾ǃĪ̛λੱȽìǉoƹङଡ̮݁φ´ੌ́ႬƯ͘LҀÆ๠ȱᗐ೥಴ǍƛՕϮȑ҂ັࣘగწƼⳘӹ૰ʔɊcࣀťஆƥࡌǢǎ¿ٍ֨τŻࡴƧৼȕౖ֫آĸڒŎиèÝӧݾʎŅư®ưńȘǘʴò௺Ԃጬڈ᳈Π઄é൤ºɞ ᐾ܆ූౘኂݸƨ̠॥ఆ௮༘˸ ǌ¥جYǠĿǴȽɚùۢǏԴ¥ьßǈƚ Ԋ̖ͪļŐÜގԮ΂рϠàκğԞ઴ƴପؾľŴ]ŐwĠࠢࣖ٘ɘঔÍÈàXĸĲɦöîɨJϮƽܞͪǸ͖ĐȞδ}ƞvƲuˌƴɮÉЖɋΎÇ̖̘»ǶLĚ²ʸĶłд͋ټஅ૾űê˝ĘʉòŷƠɡ˔ƃĞນVेݧŽЩ̼ɿäĳ˹ɥ[̉É˧ZŧǱIͿƵȋǩȯÅՓтšִʤŮ͈ƒ¸ɘpʔĠ˘ᑦ᫐ᚶۛౠݎ႒Ӻɴӈͽ˴ᓂᦶາ૒ã੄ୱƪĪΊኮ૚㍐ה ऋնƾ૴ίస຿घᬣ۴࢛OߟӮ́ƽ࣭࢜Էดì۰ԍࣀത૙ܴå§ׁӒ׏ȟܝєۛḒ`ԚԲऊΠڞm჌ؤݪˋɷԭΒۋఽऍޅᚹ१ෳ՗ʗȝࠁϣɥᕯڔࢻލভ̹ҸႷď༇қ੣൛ϻȐѓ"],["@@΋êҨȴś˝"],["@@֊ÞyչܛƺȌ͢"],["@@ªʋ̙¸ʰȔ"],["@@ѧ༑౅ᄦ̵ॴǺૺᒚᶨఊժ۶ڭഋ⚡ࣣః"]],encodeOffsets:[[[386165,-201465]],[[384963,-198461]],[[391114,-196970]],[[391502,-197073]],[[406368,-189178]],[[410878,-186557]],[[415057,-179385]],[[418104,-174264]],[[417193,-170094]],[[441555,-139970]],[[394831,-195413]],[[394921,-194591]],[[406972,-189107]],[[413707,-194295]]]}},{type:"Feature",properties:{name:"Côte d'Ivoire",childNum:1},geometry:{type:"Polygon",coordinates:["@@઒ిȴīɠvʪǙǐMμ̦࣒̤ҶfٜĥƮžĽŰɏŒëŖõǐ΁¼÷ŘǗŐGŞòÀÄ\\ōÑǷƱۅŮ­üƳĄΓľ೛ÿċǙÁwÑēāŷÃ˵ȭҗ੫۷ཇϐᙝѼʝâϓǕس޷Ő¿ͼΡԅႁАઽǑ౬_ኵȳࡓǈȟƃھ­ኃˍ⎡ํ«Ӷ^ޘӢෂđٲЉƪȽ~ƙˎãČ¡ǒ×ȎȩŘڳô٭Ϩٶ०ō઼gьǦ}ʾ÷ÜöɲϢOʈ̖ҾëȲȯǏĐăúÆΊ|ńٔ¸˼uǤȷĨBEײӧ͐àўĘČ̂Ɯ¥úąĈǙĈĨԘԵǴÖ੮Цшߐצ׮țަѱȖѾֺfÚĪwˌƪŰÆºΰɅֈΖÒ܃[çŹȭFʲƃǾg̰ʦłŰþ΄Į̰fΰ˯ː`"],encodeOffsets:[[69389,-227849]]}},{type:"Feature",properties:{name:"Cameroon",childNum:1},geometry:{type:"Polygon",coordinates:["@@ٻ܃୏ᏽܑգЄϱǹಹ̼ຯܔ܃Ŏਁ᎞Ꮡʄෛ̙೧۱ՖሗɔܓкᮥċǅɆ⎡ĸһK¹̑Ա@ۃ@ᆇBʑĸñÞ_öSºÉdÎՀ̺ூכ਄ɎȘˡǵивŬɉʤŜȲҝtƙ͏भϜƟਘ֣ˀw΋ʯŰѬਫ਼ܢዐ୒૬ۖވĦÂŪªϦ˄ʔǚ¶ċĽ¦±ȘːFҘв݊ٙǒǭƈ՝ĺ«ȎϘȈΤκĘіǺƖӌτȋΒľǎӼնמ༂њۮКÜǊĮ̈́ͲàƸɨढƚ˞ض̮ʚ੐Β̢Πॾф׸ӜװɌǈǪôȘTƊªࡌԆˬ๊ȏͤ՗žǧȮśපДEҸŭܺ୓ψ฽˗࿗Ɔ਩˂ٳު޳ṉÙ٩٣۸થ࿢ฅ࡜Ⴃȗס"],encodeOffsets:[[125216,-235476]]}},{type:"Feature",properties:{name:"Dem. Rep. Congo",childNum:1},geometry:{type:"Polygon",coordinates:["@@ࠔਥࢤڅԾÍۂК૆ʧ֠ӰҤÇૠണۖ͝ǌӁϢ@Ǩ̗ƨǿίࠑȢѥ˭࢙঺бǌԍᱯᩝuܑӣࣉ̗ΎࡅݓŽ੟֛ҟ¸Կ˚ȑӢݙhჟҼ൝NŃƫݷ@ƙºȋӮୁżͷǇmšɑщkƅò͡ĢͅŤǵ̂ӽȸǙϚʝͲʣʦ˝ʒχʊֹࡼ࿝❅ٷoڧকঙՄ৭ʌዑЁᗧ˒ջජய॒ƻ\\юزǌɲᮓՕшݥͯщâܵলлȨϓ܆ኡٴੋฎڟࡹᅓǚ༟ײɏऀጱқčٞऱ׀ůʟƝå࠯Ĳࡃ˅׏h֭ÆǉçȕǍǕēąXƁǊŅټȲ̰εพढ़౰Ć჈зᵔű¾ళ°سTڳTŗöÍƐMƎŘкsʋV٭åяã˗uݳΝă̡jљɏХHř׉ѩýѵ@ࡵƦோϩࠋƗkงሰȋܤʳΰǫΈƻ৮ϟѼʏƦ˃üㇿnඳaγhྫ͓֙ը׺ĚƕชŐþƸĜ¸¢ԂըʺĢʌİòŌK٠Է՞ʸŀװԊãऀвɐͱǯ̥Ŭջڠœ᪴᠆Ҧ࿮­ቬܐݲݐ൘ዀຬݠ㛚ঐᎬ̚ఎ~ኾ૊௞পՈڸďׇࣺ܆ࠑ➶ܻਜೌ௸ƉᎾݎယÑ੖ޘᮦְ֑̮ܼȯ"],encodeOffsets:[[157368,-242063]]}},{type:"Feature",properties:{name:"Congo",childNum:1},geometry:{type:"Polygon",coordinates:["@@̙఍এᎫݟ㛙኿ຫݏൗ܏ݱ®ቫҥ࿭᪳᠅ڟŔūռǰ̦ɏͲࣿбԉäĿׯ՝ʷٟԸŋLñÉǱưɁʆõށΟਫड׃ঘຉถɐԐפϢиαѠİަӽ̈́ĘѢԑ͞Ŷૈਐƽ࡚Ȅµ࣌ΘɬҜɻҠࡡਫ਼ǿמֺΦܷʊÇҖ̨ę̇ӖܼǢᱎ൛ࠤȪఠࡂ؂ʊتյ਼݁Ŋීϡĉூ˸࠴ᮦČܔйመɓ۲Օ̚೨ڔసΨຜጠР๤п܂ˠ͓̎"],encodeOffsets:[[133246,-246511]]}},{type:"Feature",properties:{name:"Colombia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ൟѫխ੃ࣥ߭ͣޗǟ༵۝೭ڮƤؒ͵ϐઁڞށãႁؖ·ӸݱዾĹࡲɴ൜̧ဲᇭᒎðഢ̘ࡨʕ¶֕܇ํLႅӢ෯ࠆ৏ஏඁ՞@तࣙ߈ᤝңčĭୌ١ऌஉࠗבՖƅ̓Ǽ˭⏟{Vต௄qβࢗ܍Ǆಡ͹ྉঈࠥԞ໛ٿᾡܫ⃫§ÿѥдƭϒŵĒęäࢧòຈᠰƳ¤ǯöŉŨح̜ǇűƜҧˢťƁ§ȗ«ȋƯ҇±ΩʖѕǆœƥƍƩƍŝčՅǓྥdŁŨνƐûৌڕ̬εসѹŜƗǆƅÔƭƼ˯èƣÔĉĪőǢǽ؄ĿƖŇńğĜƍƆǙþƛĈÇŦ³ĴݩͰ͝űઽѬૉ޶ԃҗ፷ΠǋլՉҊੳ̊໑ઞϓϘԤԌӠǙÅಬϬ˴޴Ì֌Ҫཀᧂе««τǡʫƉڶʛÿάдŬܦʡഔϡӌؖծՙࢴǲࣀౝฮʚੂԮ̓ۼࣨ܅୶ȼΨరౙơʿ̀ġ{ࡒϋלൊचӲࡈ࡚ȎƗشɪ੠νƏӶਸ਼ఌૢ୦ʧϯ̛ˤɓҞఘᇪÉᔈ೦ʌڢࢪԮਊȍΌ٧ηЭ"],encodeOffsets:[[868166,-223642]]}},{type:"Feature",properties:{name:"Comoros",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ǚƍѯÊęɌΰƇ"],["@@Ŕ؛ڛвֈȪ"],["@@֓ͮĬࡌɌùʜિ"]],encodeOffsets:[[[203155,-289571]],[[204973,-288956]],[[202190,-288464]]]}},{type:"Feature",properties:{name:"Cape Verde",childNum:8},geometry:{type:"MultiPolygon",coordinates:[["@@̟µï͚ϮĖ]ι"],["@@PкǼūȋ̍"],["@@ǗȵїĶ¢ࠔ֎ܓ"],["@@ӀƳĕ˭ցJǘј"],["@@ĸœҕÖɕʃŝˬےV"],["@@ȉĤǎ̼|П"],["@@ҋϪȨâɷ"],["@@̷yDΎۺȒĖǽӛͧ"]],encodeOffsets:[[[19299,-215474]],[[22474,-214709]],[[21707,-215060]],[[23701,-211707]],[[20724,-210656]],[[23986,-210556]],[[18661,-210123]],[[17961,-209772]]]}},{type:"Feature",properties:{name:"Costa Rica",childNum:1},geometry:{type:"Polygon",coordinates:["@@ޖഓ൨ฃӸȹU£©ūóιȔğ±ƃÏåÿ«ØࢉƂWɰƥĴđāЃϽƶɽ²͛ăΛʷτׯדلũՄ۳ˆѸڧ֫Ę҉ϘʊъͣޢዙनΝݎ௡ގt̝ࠪٛϳө֡Ҵ۽ȴ֫ࢌ͘২֡΀ͬςͬ˶༆ճרɤ࿾ۛҊŔƆ̌"],encodeOffsets:[[834931,-226219]]}},{type:"Feature",properties:{name:"Cuba",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ࠝ̅օ;ҪEͅѾƘ͸قŝҦ܉"],["@@؅̦ٞùɫ"],["@@ϱǒÀɔͲϥ"],["@@౔a̠ɫ੬ň۠Χ௤ų඘ੵೆğᢈౡ̶qɱɨȞࢎԝӉç̢˿̔ʀ؜׃ᤢӫ͞ʻǣطሲƛऄ׫ߞȷþˡᎇױşʈˁʵዝɐ₇̛ଠఄσՈፕ˄ळٸ݅ா൱iᎱٞڍ֚஛ŏХѦƳΡಃɺֵϴވʔΣϾጩ௧ਯૹşȡԕঅϏËΈࠓǍଶӠȣھ׈ٞྒڮᴺѪ"]],encodeOffsets:[[[834083,-197156]],[[847045,-196118]],[[846391,-195639]],[[835237,-192815]]]}},{type:"Feature",properties:{name:"Curaçao",childNum:1},geometry:{type:"Polygon",coordinates:["@@֝ȀхՖতܕ"],encodeOffsets:[[875086,-223103]]}},{type:"Feature",properties:{name:"Cayman Is.",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@׬ĭ٭ÑÂǀ"],["@@ǵq΂Ūǋķ"]],encodeOffsets:[[[838300,-203219]],[[842309,-202229]]]}},{type:"Feature",properties:{name:"N. Cyprus",childNum:1},geometry:{type:"Polygon",coordinates:["@@୳ơįζਯǗϽȆκtˀҸඪąᕴې໻ࠡIԕ"],encodeOffsets:[[187607,-160348]]}},{type:"Feature",properties:{name:"Cyprus",childNum:1},geometry:{type:"Polygon",coordinates:["@@Ͼȅਰǘİε୴Ƣ¼ǡމ¥ႹࣇૅΨƣԆ®̄लȠ"],encodeOffsets:[[184302,-160059]]}},{type:"Feature",properties:{name:"Czech Rep.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǴNȸÈǞ˺ྔࣕؒİȌǽׯͱ԰ܛࢄ̠]юࣚϫ٦ŞιёΒ͑ϨƘऎΑƺࠛ࿷֥έїࡵЅ೯ŁىէهѴਲ਼ġᄩծ஻ࢽ೏R̥πŌŇƌȓȄɡUË¸؁߄ϵȌɯܮО̮Γॾò@È»àītƙĒÄ܎р੪ɐή®ᮤࢆÒ̸ЪįiēąÂUėƁÜ½Œ¡ĢTĞÄª"],encodeOffsets:[[155008,-117265]]}},{type:"Feature",properties:{name:"Germany",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@׳ęŞ֊Ӗү"],["@@ƇɩছĲޢࠬҰ̭ŭЅ"],["@@κͻֻ҉໎ΓٰƖˏѷՙʁŐǳࣞœႦԄ೔ڠ࠺ųӼ؁ְĩڣْˡƍϱŁ؏ēƫ{¡ȩȽʟǭёȿĕũƌɉή̩ðʿɕɓśǃeĹ¤ąĬ×Áϓ׿֑ҟƳǿH±̖ƉÙ͹ݿșȗρ̫{©ĝÃġSő¢Û¾ĘƂÁVĆjĔЩİÑ̷ᮣࢅέ­੩ɏ܍пđÃsƚßĬÇ¼ñ@qõˊ̑ư׃Н̭ɰܭ϶ȋ؂߃Ì·ɢVȔȃňƋŋ­©ƣ˭Ýùث_£ug˿ϡʙƕʩß଱ѧǖӛ̑ϙʨƫģǝŉǃããįKƱèŁĂ¸|Ð¸iòơâडŌq½ƓãÕᤕԑŧZĹǌeäªˍVƥ¾σ]āvɓ̝ŵŕӏțŶȎʕâælÊÆħîɟŀǉ¾Եŗၝ֜ͧıŗīQÉä{ɐhveSǻ}áჍSؾెጼᆨளЎวnҧٺۮޞѱʼáƄbƈŞȘ࡬݌΋ڞ̜Ίɽˬ۬ˀԨڲá߄኶ς[ʨৠڦӃȈŦΈ۠öର඲RѪۚҘๆrĝӿʚ¹ϴή¸Ӄ࢚੾୆§ޢګ֋ھٱʄδڌѳȼ߰ʬpঈᎮǧ"],["@@ٶ׸ΡΏӪk޽ɻ"]],encodeOffsets:[[[157996,-108918]],[[157517,-107653]],[[148507,-106445]],[[144942,-106550]]]}},{type:"Feature",properties:{name:"Djibouti",childNum:1},geometry:{type:"Polygon",coordinates:["@@ߩ૩ߝǼႉɣňཊจტðØĬÔÜkӆ̕ƮĨƼ͞ĶŤGˬĠʰļҪޅXءკ஑ʽŲǙਤȴѬȣ"],encodeOffsets:[[201508,-224630]]}},{type:"Feature",properties:{name:"Dominica",childNum:1},geometry:{type:"Polygon",coordinates:["@@ȩµࣦ̽ѨʃĀث"],encodeOffsets:[[894265,-214403]]}},{type:"Feature",properties:{name:"Denmark",childNum:10,cp:[10.2768332,56.1773879]},geometry:{type:"MultiPolygon",coordinates:[["@@٪ȉř˹ۇœٿ͒ˎϒժŋ"],["@@झřԚͲфɗ"],["@@ӟ¬Ǟθ͂У"],["@@ğɦ੬ھঋࣣ"],["@@۳ǶиѤ՜ͽʟ˛"],["@@ε੥ߛȍݳʰɞ߂ၨɂ"],["@@˱ě˂ˤpȇ"],["@@଍܉ƺЕާɗֳ࠱ѡħƀऔ܇ ČୄܮêࢶҚׅҢ؄ࣨʹ٠ŧЋس"],["@@ͻvھƊ΁ƿ"],["@@ᎭǨצ଺ܓО๖ᖶ࡞ʝޖ՚ѰƀŲȵ֒ۜԥXࡡٳۓƉīрਞׄႂƘ࿒࣪ບΔᖛ᠋ਔЅد҉࣡Ýݧީי¹Ÿǻ௻׷ƫԓݑԕҦǅǭ͉"]],encodeOffsets:[[[152580,-106265]],[[155603,-106062]],[[149393,-106279]],[[150818,-106649]],[[161895,-105909]],[[152008,-104306]],[[156909,-104342]],[[156982,-103828]],[[155785,-99825]],[[148507,-106445]]]}},{type:"Feature",properties:{name:"Dominican Rep.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ʩȪB҆ǳҌÑäȠ̈༆Ƥᅈ؟Ԝøش࠱ଈÓߡƥMȡᓎӋ࢔ٽسࢳأќޅîષT๭ԑСˊࠝƗۉ෗Ӆ̲ЁרµτƬāǀ˕ǚȁɎǏȐ~ˆVɰˈXƾŢɁ̊ˠҴ"],encodeOffsets:[[864581,-203636]]}},{type:"Feature",properties:{name:"Algeria",childNum:1},geometry:{type:"Polygon",coordinates:["@@Ȃͷؙཱș֝fɳFɉˇˋƷɽʝչᏍอɏ߹Ξ௡юʻŰଧ௜ੋ³৛yևęᢡԻʭԐᐿ֧ᷥحৱē༩৯ڕɽ׭ࠤ࿩ȈဉʰɿڠƦၔՋيᄹ榍㢛⡍᷵⍧ݩጯΓӍʼъێįজᲇஂėЈࢅהĈْ㠇⭚㋷⠞䏙ㄐɼࠤƼմזᆰ᳦ບƪôݬѦؔ¡ȶ¡ʤĘxɞǸȤάਪ|ಒˮРɪ͌ʌښڠӀȚפǐԀǐҔʜ౐є̱ѲɎ۴ˢІƸzȢφ¢৚̄ĠĚʖϖǜð№Ҵۜɞ΄űŲȁƮĳØƱǼÕȺÌѬģպࡠỌչӪāƂEǖఆǮỸຶ߀Ýਪ݆࿦ـ⊌Θ४Ъᆌʢᐴ@ौևᯞ঒߶ϋ۶ŌˤೖՇ๰Ǻɳડ۵"],encodeOffsets:[[121372,-156382]]}},{type:"Feature",properties:{name:"Ecuador",childNum:9},geometry:{type:"MultiPolygon",coordinates:[["@@ͅ»Ŭլ͖ȌϢųԝև"],["@@ɇĠĬǪŜˉ"],["@@щ¼ܜѼ̑ӷ"],["@@ҧɆ|ɆטɐǠϵ͋ʥ"],["@@ЯāΨкÜøҟ"],["@@ځƖȞʞҤϳ"],["@@ੈ჏ʇуԑǯ١¾Ṳ̈̀ࡄڮօ،ƃנϓÊՄˤȦɥ"],["@@ȃĜȲƬmʇ"],["@@އ_ûǄħƼŇƨΫςЛhߵəíÒ×čΓ౟஁ഓ೻१ᦟभ࢝વɯǚ݃ቡŋ۱БɯŅō­űtŅaėŵ³ýĝƋĭµʑðǻDƏâƣȠšǒħɊšƇĔƁĸǍȗoĽæʷŮɗƘǙìŽɝʁȵĿěNţêmĆľżɈ˶ɥTõĘQļŐ¶ƄŨì̔ÑĐŴČÆĊ¤ę݄ǛцࠔԨւಔɯନȷ੿ɻmÖҔٓ߅ෛঔІݠ̋ቼ޶ԬɢטϮýѻ֠঎୨ĳඪᦂ़Č֎໒ઝੴ̉Պ҉ǌի፸ΟԄҘ૊޵ાѫ"]],encodeOffsets:[[[845682,-264110]],[[817685,-259655]],[[820436,-258485]],[[817940,-258104]],[[814968,-257257]],[[817293,-256911]],[[815389,-255931]],[[849096,-252582]],[[859001,-256290]]]}},{type:"Feature",properties:{name:"Egypt",childNum:1},geometry:{type:"Polygon",coordinates:["@@ը┡ҳҧ೽᫽ԛ֛ࢉيࢃ୔ޯḸدޏٚᛵર๵ŽٱҞुE஫ᄺ⺓઎றեƉ̓ńᇿੈ܃ஊභ牝EǠ;ǙŪϥ҃ϋg䁥B䁣B໖䳒ᄾ䳔µᗸࠢ੬ƒ฼ࠪܔо͇୤ʎ᫢խդυᎆγચӱᐎࢸٶèӜѾ஺ʔࡹͯঌĺʪ̾࢘͗࠶Ȇю҅ҋ̈́λы،ҁ҆ʎğȺܖԉᚆƐఴј¦ʯ"],encodeOffsets:[[185558,-170869]]}},{type:"Feature",properties:{name:"Eritrea",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@հʹणò£Ȉˎ©˙Ʋð͖δѝ"],["@@¼ɏʅŶȊĚ"],["@@ɽ˾ృ܎ຟʀݯŷśɮűľő@íġѝɹۡŻǉVзӶүȞŗǐŧĤǫ͏̫֛Ǎ̝ɁϱÇ]čӧږ֑ПҩĪ̓g̿ō¡EM٠GјCҖCјκԨ৖ᐨ݀ʞЀZƨĎΈ¤ĐŞNɺ­ը°łŀǬДĢĄ֐̚આ˴ îÈ®ƖľæøƐʆҮМېᏗ˼ᤑࢺᄷʮޞٮ৹ᏺࢣढී௦ࢭˎ࠽ˢpՠߛϔűĊȲǤǗÂʹʯĻ˫ğHĵţƻ͝ƭħӅ̖ÛlīÓï×ǱɶŵȴǹʖϧʜǱ͐ǳΞࠩۼࡇઌ"]],encodeOffsets:[[[194141,-213183]],[[194081,-212129]],[[195826,-217856]]]}},{type:"Feature",properties:{name:"Spain",childNum:12,cp:[-2.9366964,40.3438963]},geometry:{type:"MultiPolygon",coordinates:[["@@͏ι̩ʴغņ"],["@@̧ݭ؛ÏõҼϨΦؒc"],["@@ʉɊȮȒ˞ȵʁȥ"],["@@͹ԝة͓̹ݖለՊשѭ"],["@@ϋʩ̓Ķ٢ʲ࠮શͼìӛઓٟȋ"],["@@ǯצɔȘΤˏЇԭ"],["@@΃ĥǴ͔लԆȑԫ׏ɇ"],["@@БEƮɒʤɋ"],["@@ӟъІӶĜÀǿӟˏ"],["@@לȷ୻ࢱԏȈëΚӝƟȏɂ᎜ࢾȑЗ"],["@@ߟʾÞɺނçя"],["@@لϏ̗уϲŌஜڭរʕ̊Βಈ֏÷ŃʝȀXцƐ˦ĻŔçƬǋᢜǦϿટڿЃᜧ੻ᝫԋ࣏ՙ˦ɣܩʋ᲋នƹ৿רڵኟফૅ௩þ̵ണƻࠏϵ໋ു⺯ſ࠽Ӂ଑ȃٳ٧ڇʑݩζΕ৐״ؤҕǽ؅ৈడɝЪ௲ผਊ͓cʡؤಐഄ֧തࣤ®вʾϨԂƧٞ͞ϼТ๰Ꭸ୦օ̾Ś؀੽Ĕཱྀۙͣ·İܰ౵ѯÓπנϚʥ|̾˲ìԠԩĽѴҞҙЌͬѴᖐרƌ̾ٺʰ઺ĸܬеᥲȲᡞԣᎦɶ৲ͥѼȄী˱௤ǰ"]],encodeOffsets:[[[44757,-180140]],[[51653,-179218]],[[46804,-179560]],[[49363,-178584]],[[54872,-179159]],[[45472,-178275]],[[56767,-177135]],[[106333,-150509]],[[106213,-149836]],[[111565,-147459]],[[114593,-147318]],[[103124,-137592]]]}},{type:"Feature",properties:{name:"Estonia",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@୊ϡัӯܧ૳ٗڰԀҙʎ˪Ղငˤ"],["@@̽ʒюyŏɗ"],["@@फ̩sϲוǴഺ͐Ϟɋȡο"],["@@Ųʕᔋೃ׽౏Ĩ޷֑ǅ҇̑˕̵ډǺढ़ǩϑƾࡕ܎٭ȦʝŦ๳ΏʕģñXਸ਼ઐणȡ֑ɆŇژԞ˾̫̘Ӿ؜ಂɌࠨМᒸň̂˂ᤢсᐊö"]],encodeOffsets:[[[185952,-96091]],[[187577,-96284]],[[187028,-95530]],[[200281,-93737]]]}},{type:"Feature",properties:{name:"Ethiopia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǊUۢżўɺîĢŒ@ŲĽŜɭݰŸຠɿౄ܍ɾ˽ࡈઋࠪۻǴΝǲ͏ϨʛǺʕŶȳǲɵงსŇཉႊɤߞǻڃࣃΘࢳ܄ܣ׶૩੮ࠥ㺼ᗏᖄ@䎝䇽ᑽĒࣱɉਣһحܓሗзʡʅñţ͝HֳʗͻMѱʮ֏Әᐧऻŧɓ̭Ёχ˗ጏ͒ΓZԏ̳äᎻനճΘጇŶօݾK͊ÜΎ¼ŘŅмफ²ſłDĺ`ü׿ࠌՋᅘ൧ౌٌ͝ጁ࢐ۮஶຌbˬɪƢᏈٌ࿊ƲਸմӺф̛Ϙˈּᒈඖᅴਨ̖ெ‸¢F̀Ŏ̔hҪĩ֒РӨڕĎÈ^ɂϲǎ̞̬֜Ǭ͐ŨģŘǏҰȝиӵ"],encodeOffsets:[[189149,-216641]]}},{type:"Feature",properties:{name:"Finland",childNum:5},geometry:{type:"MultiPolygon",coordinates:[["@@όƭ̥̯ϝ˒̸Ȍ"],["@@νŏÐ̮̀ȯ"],["@@̴įϣķųˮȤą"],["@@їŚފͱƝ"],["@@௭ˁհƝஃ޽Żڝ়˹԰ࢡᴏ჋¦ᤗࢻɩߛࡁʤɵۣЃֲҕօܙՆկ́Бඡٕ຀୹ɀܡఝࣛ⡗ᐭゝᗅ᥋ˡδӄٷ҅ݧŒ̓٣ǓŃƦ᫣ۥዻýુϡَҤ̋рՏͿ׷×݊޼ንӚᅢᕶ÷ࢺलှ෸ڢÊτ࿶ʂलݲᦒࢠᴸၲ๘Ƅ૪൲॑єɨфওťįৈຊతĝ܌۔ڀҍČ௲ૈۡऎಗ΄༉ୀࠚZͪϮ઎Ƅ҈఍ༀǁ౒ΘྖӇᕖࢶቜ༲దԄ઎oොΠᅼࢡ]ѹෝڏÑ΍"]],encodeOffsets:[[[187877,-91319]],[[186438,-90885]],[[190898,-83488]],[[202743,-78715]],[[220500,-67720]]]}},{type:"Feature",properties:{name:"Fiji",childNum:7},geometry:{type:"MultiPolygon",coordinates:[["@@஁ͣࡎоʹę"],["@@ȷǈŮȘĊΟ"],["@@ڢصƐ॥ཥЧൿкőӐ֊ܨ۶͸ਆ"],["@@ɭEʌЎ]Ї"],["@@͂ɔˑՉÍđ^Ј"],["@@ǃċɬʐçǃ"],["@@িਹDʛࠎԜsԉଇƓӅɀҁнڣǟҋфȮϊӬs၆ࡢ৊ɐG"]],encodeOffsets:[[[568357,-307758]],[[570762,-305380]],[[567939,-303387]],[[572622,-302272]],[[572637,-301788]],[[572852,-301016]],[[572687,-300105]]]}},{type:"Feature",properties:{name:"Falkland Is.",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@˨m΅ͽʽʆ͜Ŧ"],["@@ዲɬƺƯᎭோڿс΅݋Ňϫ̊ᇐٖحƂ޴ǔ̋ؼѲɷ"],["@@ǿʶϦÕȥȟ"],["@@ߐŧϙΥζȯɶΜئòǔ̙Ѓƣ˚şᕏڥƹηষǼÀΏד»ʹʣ˯ϴѦҴപ՞ǴѢߊӸ"]],encodeOffsets:[[[860908,-397262]],[[863239,-396379]],[[863776,-396199]],[[867110,-395855]]]}},{type:"Feature",properties:{name:"France",childNum:10,cp:[2.8719426,46.8222422]},geometry:{type:"MultiPolygon",coordinates:[["@@এƨΏӖżΊޠKҘԳ҇"],["@@ʽǼNժ̲ɛÁԉ"],["@@෹ዽՋษ۫܃ኻ͎ߛԻڿȘȃĐąńģ°Å|rĆfòDňǌȂĸ̶֠ƀɸ¬Ѐć͔ĞˊϸŔɈçКݓौεხନᐞᙠޓ඼ઉÍϥɤȴϖ͗ǆࠇθيҀ౽"],["@@ԳÕø̠ԽւڔȽ̦׍"],["@@ǵXĮʢĈʹ"],["@@ЃŬóڀҪȋNן"],["@@ѝKlذݠ֗ͭË"],["@@ӛລฑဟؙҖ˔ʦ΍Ԧ͊τ¡זݾ֔ମʀϾں°Ӄ"],["@@ǟȝĻԼ˜͝"],["@@óƚ¨½ĪqƔržèƘˮĻҨٹศmழЍጻᆧؽ౅݉ΥνŶϯćȵƁƨūmå¯ģýϫɷϵɻÿëŏ¹ЩűɥϳࡽҫƵˋɩϓͥơÝƙÚoǢZ˴ĺɠ̺ʘƌˀĊ͆fΖµʅ֗ƖʩĄý¨ŽDóګЯμߧ˿ͅિϧԂաەહڴѷۜÔۡࢹ៿౛ઃ˳೻ΔԥӜߟǅ๵ьჍࠡ܇ثƕઑᢛǥƫǌœè˥ļňɨˋæƃjȩĽಇ֐̉Αយʖ஛ڮϱŋ̘фكϐߺΖ༌ᕺӰ̆ÿǔ̕ɓ౼ኴՎகHਸ਼ֳւϊÐЎ౨ષҖɫڌΌԘɡˬӒʜԼŻωɆણH΄΢څĪĬʢᰇѴӷӞࢢ̈ιˎ܀ࣱƄlȚۈϞ᱖Ӷ݈ڹ٠ʠࠪƩ΂ʴਞĳȱ΂҈ࢶં௤Sřٹቄȯ൞ͺԫƈΨѦᡤڊਆ֪क़඼፠ӆªޣݚÞևעŧkϥऄƅϫ؛ߘ}ߎўͻࠇজاؖÔ"]],encodeOffsets:[[[238216,-314210]],[[207084,-291398]],[[923245,-244921]],[[895785,-216461]],[[894150,-212654]],[[893131,-212335]],[[893750,-211726]],[[131273,-139234]],[[107969,-130781]],[[130617,-120868]]]}},{type:"Feature",properties:{name:"Faeroe Is.",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ȹґÚӮƠ"],["@@̛̯̕ÌėϓʇݠदŻ"],["@@ηşʸͲŀɑ"]],encodeOffsets:[[[122044,-86494]],[[123551,-86253]],[[124149,-86169]]]}},{type:"Feature",properties:{name:"Micronesia",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@̋ŉ͐Φb°̭"],["@@ǝǽˌ϶ĭȷ"]],encodeOffsets:[[[514169,-237413]],[[459266,-230083]]]}},{type:"Feature",properties:{name:"Gabon",childNum:1},geometry:{type:"Polygon",coordinates:["@@˷࠳ĊுුϢ݂ŉն਻ʉةࡁ؁ȩట൜ࠣǡᱍӕܻĘ̧ҕ̧ʉÈΥܸםֹ੝ȀҟࡢқɼΗɫ¶࡙࣋ȃਏƾŵેԒ͝ėѡӾ̓įޥџзβףϡɏԏᅽᎀෙ௠ވǿধФݟਤЮnөƼՉୀٜܡМǢҡĚňюͅŹ̽Ȍࠓདྷղɓްݰż༐ʨЫ݀ʵҞͶ৯ɨӳֶϤʦ˜ȡÃ৶ĸĞØÚ\\Ű[ØŋÑɮđẴĘN஄dෞº̒ҼL⎢ķǆɅ"],encodeOffsets:[[118663,-250103]]}},{type:"Feature",properties:{name:"United Kingdom",childNum:14,cp:[-2.5830348,54.4598409]},geometry:{type:"MultiPolygon",coordinates:[["@@ԙɩաǦٲʊЊȅ"],["@@ˠࣅϧ֢ֈæļɉ"],["@@ࣱãȹު਩ؑցzػ؂৊КǃɴࠢƼ଼ܨ႖ܾͬɍƮܷדϿظ¸eιͣŎ˥اହӷ΋Č"],["@@ЕðόԜ׋"],["@@ɉՁىȡ̄φԽǙɪʞࡤ˚"],["@@ĉǬਦԎज़ڹ"],["@@ଧœټѲĹ˖кøѪ΍ʽ͝"],["@@Ōתːqϛַ"],["@@˽яࠎƏ࢟ҫĂΒ֭Hӗ՜౞Ԧ¶Ϗ"],["@@ȣͱձƸݖǺ"],["@@ଭ؁ƐǇ໹؇ģƜغʘ}ׂܜhĒʤඌҊƹ̹"],["@@ԭѣᑯ߷ʿʇ̚÷ण՟င̈᤮_ɜԝᴍᎱႻѹ࣌úȨ͑႑ԏݟƲా͍৞ǚܐ˥Ӣݕй኷๎಻ϝɍòउ׷˸ߧ۞ë٪ߛåϻঁ֫΂ɑުΠসkसҟȝُޟޱऋ̯ͩϓঝǑúΟગɿሴɭˋН௛ֵ໧ͯᕻy૫̌êǝұǏઅ·Ǘ˛ᆛʬभȋ੯࢝ࡥΆ௩ɱ૷֟৫⩚ᢀᓤᒤபᕙ޵ॉҊܳ»аȰӭǺஹʱϭʰʪҴᜲܼࡊע˘࠸೩ʓಾݶጎѮֈǷ̚ώӂʽΙҒ೘඄ٹg͍ݾپۂ૲Жਥ_੝ϋ࿃ƤлΕƍǈ¾Ԉᄎ଼Ǭ࠲ڨƠ΍ʦʦʊౙ؋ވ۠॥ҍ௏ധѝů୔਼ǲ֤ኢ༺౧ԑہϺਔ͞ǹŎ૦݈ɫۖ͞ϔŵ˘؜ӊࢾ@ȥфъϚےÂӚں੘ťᡦ̮Ϡ̩ˉ"],["@@̈́˝ॏƠ̪Φ͢ɧ"],["@@ͺȹ෯౏Ӑ؜ԗٰ̀Œŉβ׼ʴcǉ"]],encodeOffsets:[[[115228,-117725]],[[111783,-110548]],[[108156,-108455]],[[113338,-104745]],[[111741,-103431]],[[111910,-103747]],[[113374,-102302]],[[111293,-100199]],[[114726,-99136]],[[112520,-98650]],[[116305,-96795]],[[124027,-96379]],[[125181,-94977]],[[132441,-90864]]]}},{type:"Feature",properties:{name:"Georgia",childNum:1},geometry:{type:"Polygon",coordinates:["@@քνøŵµġƅȓʋǃɧ}ځʆໃתƳÏկ̑Ùέ¡ྃǣൣÑҍĮďĊMÜĂ¾¨ǋĺʇʮǉÌuǄÓƥdǏe¡kÉ}̑ˋુŰʹƗĕlƙêʗÌų®՜т͂עĉ໲੻ࣜܩȀ߷ؘƠƤǴƴୠ©Ռʷ˂īնġǦŵȾŷគōіǩ]ĥƜƉࢤ͓Őć̈Ƌr©{ÅŹķ¿ĝ½Ŭ¹˚YŤÞɜ²բȚЖĶҮ̍ʶ̄ºvƆÏʔăǦ©˶͗Үb˚č½γ՟[ß੠ӕϸɛ٥хŰѥ"],encodeOffsets:[[224599,-142774]]}},{type:"Feature",properties:{name:"Ghana",childNum:1},geometry:{type:"Polygon",coordinates:["@@ƲɃ̇ީॠ࡟Ͽฉǖĝ˕ٖ̿ơȅ௧˟ȷؼࣝչᅷ˔ѝͅ૵кࢽॢ߽֡ر໿šㅱᕱលܴڬƞǖشáϔѻʞϏᙞ۸཈Ҙ੬˶ȮŸÄĂĔxÒǚÂĀČĽ೜ăΔûƴŭ®Ʋۆ¶̆_ŞŅ˦cȞŮ໢ʉΪ˰ࣦ☺ࣤȁ޼՚Ԋŗ"],encodeOffsets:[[83727,-225678]]}},{type:"Feature",properties:{name:"Guinea",childNum:1},geometry:{type:"Polygon",coordinates:["@@̯ңϴХࢢъиۇ੄۰૖УऺԎ}ҶގƓϊ੹ó֏ࢀۉۇߛܲ¥ଁզνDΥХчÕ੭ԶǳħԗǚćĆć¦ù́ƛėċßѝӨ͏Fױˋȶ˻vٓ·{ŃÅΉĄùǐďȰìȱ̕ҽPʇɱϡÛõѣĶƃīλ̄׍॑ԫĿݏ҄ʺ٢ɕ෰փіٱʋǛȪȟçőSǝxāʡˇơĽ˽bÁĚƤ҈ٞǿࠪʑ¼¥AÔǘ̴hÚƫɌ֑࣌ˉ̶©ŞƧ¦෋Qթˁم¼ͩѝԣݏӇʙ̧џӝ¯ʏࠨխʶĜ̛࢜ĝѹ֪ࠟʈͱڮĘӞȵǧǅլʃĆʃρǉê¬ʀŐŜƚǴҒݜƲǠĜÒǪPΚĴ̔ǒƦä̈́kϠԜǄÞԆEŐǋĴʻˢƼƸ̀ʪӜıʰǺෘěR͏̢ĦଂїᗖƠ"],encodeOffsets:[[53379,-222162]]}},{type:"Feature",properties:{name:"Gambia",childNum:1},geometry:{type:"Polygon",coordinates:["@@½بϲ̚ԐҟᖼѼཏ­ԥɻϻղᚎEɮ̀݀Ȓุۓ॰Ẹ̌̌থϩቹܔΕΫ஧ǁĩϗᆅK˯ȫ"],encodeOffsets:[[39028,-220363]]}},{type:"Feature",properties:{name:"Guinea-Bissau",childNum:5},geometry:{type:"MultiPolygon",coordinates:[["@@ƿÁĢ̚Þʗ"],["@@ʳŦЊȊƕ̯"],["@@ˋ~ǎ˖ľ̓"],["@@ƫ˛ɝʾϊ^"],["@@˿ƻƷшϕFŏÝԅԛǃۣYƥã̓ǑΙĳǩOěÑƱǟґݛƙǳŏśħǄŦ̊Гʝ¬ˬϗƌƂВ˝ड़о͝ȦяǅǛʐŌˊࣖ˰ጋУĚͺӯ¯ϫ̜ʆдѩó֭ͶŲt̔ʈ¨ЪǞɚڨ©؄Ş࣎ъሲQنGݞEǹĲʯʩӛ"]],encodeOffsets:[[[40707,-225768]],[[40106,-225831]],[[40813,-224723]],[[40720,-223587]],[[46895,-222550]]]}},{type:"Feature",properties:{name:"Eq. Guinea",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@MஃẳėɭĒÒ×Ōů\\Ù[ĝ×ķpºȣƈˣÆॲᄌÇ̀ÌՊÊctůòÝʒķᆈAۄ@Բ@cෝ"],["@@϶@î̇֩ࣛԙĠYжغێ"]],encodeOffsets:[[[113286,-251830]],[[106344,-245748]]]}},{type:"Feature",properties:{name:"Greece",childNum:29},geometry:{type:"MultiPolygon",coordinates:[["@@܌ƆȍɝѤ̧᱔{gԍెјՁڑᲽȉģΊӑǆᅳɐƎتȸ±ɺ͆ǖˏ"],["@@Ɠ̒ӄӾͯߏ"],["@@˵êȤκĒѣ"],["@@ʫÚƠӐ౰זօ࠙؝̥"],["@@ʿ÷ǈʴĸǻ"],["@@ѣđˢȴǂš"],["@@ۆϨ˚ãय़̓"],["@@ʝAފʨԫʥ"],["@@ɝ͆Ȓ˻"],["@@Ё÷Φʬǳ"],["@@ɑďĽ̀Ѫ˰řԟ"],["@@ȵfǺǪ|ȏ"],["@@ƙǀ̮£Ǔś"],["@@ȠʀצĖ߅͕"],["@@ʣũʛ̐Ԁǥ"],["@@Ǳŉ̤Ίűɿ"],["@@рɭӵƟԓǮ׊Ƞ"],["@@ƶɓПī˱юȾǘ͞˥"],["@@ōƭι׼Ԑŵ͗"],["@@Ĉ۫ӑĆÉ˸ʧŧڦڈȱ"],["@@ЭÊ̈ʘƩӴ٤õϓܟ"],["@@̝ÍӾהȟՅ"],["@@̛¯~і˞ϥ"],["@@Ů͗୊σAॡ׮ɋɍ͑ס̮Ǖؐ߅Ţ׉ߠݻƺॄή˪ǋ"],["@@ȄعȻÎŝɫۋʎѬʼԉɫСɸ߾ϴӤı"],["@@ϵÞʽۂ״dʧɿ̨Ճ"],["@@̧ϻǋȖѕģľͤߌ"],["@@ԹîΆ͜ǴЉ"],["@@ᄑ׶ޏ̫׭Ȭ৥ԃڃÀ£݃ڎȭƌЗݛӀгÝͺӕǃͫЉ׀ӁĜǈݿୱ੤ȶђ۟ɇѵਅࣄኝчǏƆΔӁňģ́˴̏஑ϡಢңێەմǥx̡΋ਗࡋߜ્ΕٮকޣʃޱҰưត۷ޞࡉ܇Εఊ̍ḗ֣еޔђۆ߻଀ࠖۀՄ³Ҹ΂ቲ߯ࡄόඍھ˥ǽ܍Ű૥ȯǭψƟʩЇzίરݞ¸Ŝ̚ࡩ\\ٯٸÓӪţɈƷƴ°Fê}ŪċƸmӮͮĖ×ʒvÜäŜÎþðŘҶŜݶ޸ѲΌ΀Āǘಸ¨ƂÖμŦ׶КѐÖبęʢ¶˾ĐʢΆިuɮƖ˲ZƎrȶɀDࡢǐÂrĪž۠UʆîØMϠѯҾpʰŋȒǉ์ƴͬĔǦĀĆŌ®ȘzФÜ¶ɂ¢ɶÓҚʳáҁݧηɟُߕԙ"]],encodeOffsets:[[[161391,-159065]],[[170033,-159256]],[[159848,-157280]],[[172149,-157991]],[[166362,-156727]],[[169077,-156201]],[[170471,-155814]],[[167679,-155642]],[[170847,-155182]],[[166402,-154883]],[[167012,-155158]],[[164481,-154729]],[[167025,-153927]],[[168752,-153626]],[[166799,-153434]],[[164437,-153496]],[[171065,-152856]],[[155616,-152873]],[[166251,-152998]],[[155427,-151296]],[[169520,-151747]],[[155829,-150682]],[[166364,-150135]],[[163234,-149727]],[[171336,-148716]],[[155030,-148434]],[[169422,-146932]],[[168307,-145208]],[[171678,-144904]]]}},{type:"Feature",properties:{name:"Grenada",childNum:1},geometry:{type:"Polygon",coordinates:["@@¥ϬɲĒȋҽ"],encodeOffsets:[[894205,-223231]]}},{type:"Feature",properties:{name:"Greenland",childNum:14},geometry:{type:"MultiPolygon",coordinates:[["@@D˕ঁɘ޾вȀγ"],["@@֩ǪĸʤҲэ"],["@@˥ÕݡҨqјसǛłڍ"],["@@དྷ̯ݪҏᖥީณ͠ࠄƐәǚᜯϲ͏Պࢂk൫ђϺЮᕲʡဒ׍"],["@@ࡃɝɔ੢ʧ"],["@@ە׹ᎍѻёɞ᪹ƫዞग़ၠď኎ϔΤ˙"],["@@কȚʜϜֵܺ"],["@@ϟԕ༗Ύኸǈ"],["@@ťࠥᙷÿے܄ᄌȢ"],["@@Ѩࢄ౨ؔႏ๗"],["@@ၩʆቆċțƹ"],["@@ᒥʁʙ̒ᨌҒܬÏ਷ґ"],["@@⠗פᖭ۔நĀῒӁኌࢵ"],["@@ᨿƧӌɫᬔƘꗤ仯蘀仯팺鶟@@υȉࣁżն̗՗ųℹࡄฆৃँࠃѾᓳࢷࣟႭђୂګۉەۏЄʔӣऩǼǃӓই»˥ຊᆻǼ਺˥Ĵߟॡʂ౮ڗ⁁ΑྎӕɽѿϽȷ၏ŚܺԱӢľᇖঅ׍ׁ၉ǢූЅބz঴ࣩѕӕ«̀ৡࢿn̴຺ࣻՉ±ՃዏᆚͻԘछশڋٟ̹ڀɡऌቛǧձᇑǆᄒқ߈݅Ə̕෇ɔࢶϏНĩѵéȺڭK˩޺˳ֹᇁк̅Ϟ؎ȌҬߩӭྜྷϤӝά̡প஡࣏លŁϒԠ௃fпФ૷Ȭಓۨݏڦ۶ъൣǹ̳Ծካՠѫ֌Άܠ܉ӽ⌭ᆼࢋࠀ᎜Ұн]řɸᄉҷѩɬÎ׀ౄԦෆսָ݉࢝Ɩ჋঄ಀࣙ׷ϙͥ̂ǭ஽ᡫᐴࢭϴञࡲࡲƔઅśৣוړ̂࠭RصࡪۉǄᗠሂና຿߽Ƒ࡫ଚ͞ЌસŪᏱĬ౯Ӏқنൈݐ⥌ӹඩψŤʨ೗Ƶಛɜ኷١Ϲ่ᮮͷҪƄʏںേѽᛁǢćۮּɖᕎϳࣞͦÓ̜مǣؑࢆഎĨ຿Ĕԛᅨ⍕Ĳጳծᐯˎྟتδ˒ᥦƹ⪴ࡷʞŞྵϦէנೳΞ൶qऽ˂ᶽƌӹجุӐ᪍ȹÇԞᚩఔϗĭᎆ਽ӑї؊ѽ੽ȳᐵиࣿఠɈպࡻЅЩ˘ҲŦߥɜࣆˆҧڨই͊Ǒǽࢍǖǽ܎ᏫԬćҌၭࠠჭUچɂ؅ҺՔɔ㳯ᇚĶΈ঳Т㤙ਾ⟻Ѥց̟એɞಁз୯ʌܱɛ૗ǒ௄ՃᪿȨợݔ྾֜ᯡȒݿ֦એϑ๧Ҩ¼͢Ḝ¬ࢨЮἲɝ႟࣊ஷΙᅷǪ̟ࠩ᧏Դ˸͆ᇹÙ⏃ࡠ̅٢द͂⍊ԾѴИ␬̦૯ᇞٶɚ⣑»රےᇴ൜੖źპϓ਱݂ᦼȍብྡྷᚬÜ㳖୛䅙෸㣼ޖᔦйᛄଣ᎟຀㈀ॗᎲ¾⣓ਨ৐Ȩ涰༁ኁೊ⓱ࡒ㧼ƫҊƼ䯹ʒਯҞඊĻ᠀ք⅌νˠԮヶݛᭉ࠰ࣨ˴液Ʈᗤए@@"],["@@䷠ʨҍفὶկ▟ۗ梽λ৩ʃ{ʳỾʔ⚂Ņëڣ‮۞ᵠŸፕਵ䀟ᇋ娊ኞᛦթ⒎ࡔḴČᘎ̍ذԍ⣻ࠝᡓɃਲ਼ԥ⩽ыܴ͛ྟϽᯛǳᇫȔᔟց෕ᇿ㔡୑⮑ᑿᔺҖ྆ѝԟ΁ܣˤଃǁƗ֗੖͛དྷĊᄷઁᮓ͊ᩁԵ׹ʂਟȧߐ̉υڭᔤşԣਕቝ಍௵̜ᱫٳۯɎѠ΍མʖ୻গᇞݼݨ@ƅॣख़ח༓Eଥ͙ዏʈਐܚ๱כ١ۥ᦮͇೛ߓ▍ԃ୷˾ԑࠐලγ܃֐ɰݿᴯׁ༱Ш۾нᒫʙǈǁぴ݀ာᖎષ᫃ᅅᏱࡔ؊ா⯃ܛⓄдډ܋໏ՅੲȮ᭘༁ዏ߳ˁ̝ܨШ૘ļᔣᒉჅƛঠࢸྑु૩صѺՀ౼ඃެፑŪ̫ȿᑸŝڋڑᝉԷᰅĨҚɕۛѭᡗ֩⬘¤ɁʃᝡԳ¥̕௤ҸḦ;᫼ӏ០ʡȹႏ̑̿ʍ࿙űǇͧਅOϧ͝ᑋЏóɇᆫ؝ፋӍ㨫ࡉ૑ͫû팺鶠蘀仰ꗤ仰@@"],["@@ð@@@ⵦڹ䎹ʕᗤऐ@@"]],encodeOffsets:[[[879535,-90198]],[[889139,-77240]],[[845982,-66272]],[[841007,-65202]],[[840610,-62719]],[[104935,-62538]],[[835945,-62212]],[[827218,-57438]],[[137299,-50301]],[[138598,-48569]],[[776737,-45070]],[[157327,-38248]],[[809118,-32090]],[[830960,-29177]],[[151393,-29463]],[[154851,-28049]]]}},{type:"Feature",properties:{name:"Guatemala",childNum:1},geometry:{type:"Polygon",coordinates:["@@ݨJں˱иࢌӷđùȅǭֱٟ֕ҭϣǵűңǾѱMƓɿǣƗʭҧÍĦͫਙݯßϑஃњႳƎᎩఖæपxĐĦÈ¦ł̷ւȌϺɢҬϾࠒڦBড়BؖBЖ@ǔH¿ȪMɜ¸Ȫ[ĘƃŊʉÚƗĪeŨ÷ƨƅǤهΨੵࢄӆG҈Iƿె⛒IϾ⥋"],encodeOffsets:[[818365,-212658]]}},{type:"Feature",properties:{name:"Guam",childNum:1},geometry:{type:"Polygon",coordinates:["@@ȱϜԐжƢıѿ۟"],encodeOffsets:[[477255,-219830]]}},{type:"Feature",properties:{name:"Guyana",childNum:1},geometry:{type:"Polygon",coordinates:["@@ʥӋʶ̹ʥϻࡏßѷϡǨͿЯ౑ॶདޒ˘୩ନᆧԂɃછËƵ®ųƺÏlѵőɁũȑ̡ıŃŇ·҉Uǣñʓ§ŏ˹ۉƮēŷŉʕĩS·ϭSķ÷ųēûgःϺۗܨ̍ļQɌǍîÐa࠶̧Ȥʥܸʮ።ٴޠiÐơƘ˯Җú˴৷Д̖௸ΡΒɣ¨ȭſˡ{ѝôȩ§ງཆìǼʞǒǲƖĶÜĚõϤMƐûƬÏǘŘʄूǒ޶ՀÄȦaĦ¯ˑŵ½ƋţŬʉܬвتਚ՞øʐÀâʞŶźқհቪ੝࿴ຫŪ߫͹ซԢਐঊƣ੄৭ܚ΃ưණ"],encodeOffsets:[[907950,-240864]]}},{type:"Feature",properties:{name:"Heard I. and McDonald Is.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ҙŁׁҲ౶̫ʙÃ"],encodeOffsets:[[303480,-400948]]}},{type:"Feature",properties:{name:"Honduras",childNum:1},geometry:{type:"Polygon",coordinates:["@@ኽխʛÖԧͬʿŷŧϝʿ؅੓১¥eïgҳ˔ƣȎׇطīԽ¨˕}¿aŃƙźԛÚȿÝƑYǱěĩƷӗŃ؟у࠶ܽļª˚Ąٶ§ÜǛŊȩ\\ɏϡʌف̧÷P­ŔƄuฃ࢖ջĺϘђNƔǽѲŲҤϤǶ֖Ү٠ֲȆǮĒúɢ×ԴЎₜɍࢰϢŹǂର˧ૌȬঀпڀ°௘णӯʮ̛ÓÔȱϒeкϳ׌Jσ͞ٲ˻ɒίƺćǐŋֻʽéȗɣ"],encodeOffsets:[[833850,-215419]]}},{type:"Feature",properties:{name:"Croatia",childNum:8},geometry:{type:"MultiPolygon",coordinates:[["@@ɨƵށȪ՚³"],["@@੔Ǳܱµ͡ɨ"],["@@೎ݯÒ˳ଗࠄ෻Ӥழ˃"],["@@؁Lɦࡸǋˁį"],["@@æǓʗʞǲĉ"],["@@Ɖՠ̖פǋଃ"],["@@ٳɮЀ͚ʴև"],["@@ɳɋǊܕɻʵ٪лޟǙρ׵΃ǷѤᄡDဵѺࢁƅ۝ҭϛКԟyԏऍՖշࢱ඼ືЂছȣƳ৽৮छͶ࢝¿੫ฦ½ȎݒۿݎȞੰхӞҙĚ෣૩ċ෼۬ţ઀ŢƞÄѾͺ¦_¤ǱȺȽΨ¶ˢŋˆqɬÌTĀĖtĐɦǶĕǌࣨ̄ǜ˖®ƢċƊtèâîఘҼ״΀ɂwǄÅĬ̠ƷǶǣָޏ࠮ҡ૖ċОĪĆ̢ɂԸÄ"]],encodeOffsets:[[[152069,-139333]],[[149865,-138712]],[[152362,-138984]],[[150511,-137965]],[[147687,-136047]],[[146229,-134175]],[[147422,-133310]],[[158956,-130706]]]}},{type:"Feature",properties:{name:"Haiti",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@aǁ׭ȊӿκૐЁ"],["@@˟ҳɂ̉šWƽɯˇ˅U}ǐȏȂɍ˖ǙĂƿbԯɍǴɱƮˉĨᄷǣ୍ɠޯƍʩͧฝࣲƒϸ;Đ὘ԛࡾ̺ɦ୥ࢲĴࠠႽؾюβ౨জΕ࢈ŃŖ´ÒãǴҋA҅ʪȩ"]],encodeOffsets:[[[861648,-204777]],[[864581,-203636]]]}},{type:"Feature",properties:{name:"Hungary",childNum:1},geometry:{type:"Polygon",coordinates:["@@ʪJЖؑΰÅÒεᖱফᳯᩍ޽ąЭʥȉöԍùĿÃ·đґªȫқÀԃɻႩΗ̡ɁąНĩ૕Č࠭ҢַސǵǤ̟Ƹīũߞϳ`ࣀ͐Ϟ࢞ز̪ϩ̲ൈôӊڜ਄Յ፜ΪӊዒԀפǡाͬ֔Ԯ፞Ìўӑࠒư"],encodeOffsets:[[170091,-123958]]}},{type:"Feature",properties:{name:"Indonesia",childNum:107},geometry:{type:"MultiPolygon",coordinates:[["@@˛x̬ୌٞŒЁਝֿ"],["@@Ѝüٲʒʣ͍"],["@@ȯĒ̈˦ėη"],["@@რ಩Ŏǵ̉ϙַȝډɀ௷क़଻Ƭ̃оݚ;෶ƨǆǏ"],["@@ඥෑມѻ͍Ǥʎі˽ʢʮ߀ܜؒւȕΠՐ੔ֈʝӂ|Ľ̷ͣÁʢڙ"],["@@̇ŨǶĸŒɟ"],["@@ȣĥ˒̶͊ÕϷƹ"],["@@ǫZŰۊʢȧȥӻ"],["@@ٳĝъΪˊ÷ȓ"],["@@˛ҩǐơޝÍ࡫˦ӤĪ±ێݨԸ܌̏Ǐث"],["@@̩ҍӭȂېռň˯"],["@@ࠏڅܡĄժҲȏĤࡨȪͰǽ"],["@@ݏǲ԰̢ɠӓ"],["@@ƷЬϲĀɹӫ"],["@@ੜēȆ͡ဳȣŀԲ͒Ĩ"],["@@ڐǇɃȍғƤHȲ"],["@@ᠵٝՏȊȋɫٹ¥ઙ̚ෛűɷΪƔج࿖Вመݯ୐˜ߤϯଡ଼ܨưȂΫĄΘǶȴԃӧم"],["@@ȗéըׄΔėۣс"],["@@࠘Ċɶ̛ҺˀҴࠥࡡƭȢɕ߅½ɯРгωᠣׁؕȺĎਗ਼ܴτ࣒Ɓհّμט̦ॅ֔ƕњ߲Ô˺ѩ"],["@@׌֟௷঩ɃآহѦͭӄ੆±ӊ˘ڈȧ"],["@@˥ȕʅՎҘÔ̟"],["@@ܭمॳƠ՟əϰܘՖīࢤ̠јɋ"],["@@ŏʚɺ£ũȵ"],["@@Տ̋෽¨॒ᆸӈ˺੖ņӴۃ৕ಏ"],["@@ӁL´۪૦౰Ⱦȋȳƻߧڇ࠵"],["@@ԭȐϦƈɓ"],["@@̽ŊɒƦĬʯ"],["@@̗Roɔ͈ɥ"],["@@ཹˇࣗͺ̸ϰѼ¬ᎎfɢțԑ͗"],["@@Η¤¨Ȳۆǥϕï"],["@@̍Ê̮Ȱ_ʹ"],["@@ҿࣩԯ߸Ȟǽ׼ॆ֛"],["@@ڎү๤ƿߔ૿▄Εࣄ୤̮}ӊ֙ߠĒ௸շৢõʮڇ͠ȅXևӴɽ઒Ʃ౨ȰނΥ}്Җࠅ᲻ઌ౫˛ᤥɸᎁΐᲡ঺௃Ďٙ̃஥ǌා֢ᇱɲž۶ܣ˰ᑙȸʤϤʲʽψۦ̌ǜނҔҐ၈ЫӤѢ݄ɳ"],["@@ēω໲Ŵ୧"],["@@̗hɲǦæȍ"],["@@˩ƑšۂЌկ"],["@@áೇ؍ęٟ،͠ŢĚЄ̱ˮ̤]ԜٖЈص"],["@@ǵȕ٨ຈֿѓܱ"],["@@׃ΒהĭOʣ"],["@@īɋĝɎȊA"],["@@ՅРɴЪ˺ȗXر"],["@@ǳ˃ؽƴʜ֒áڲݐͬƊ۷͑ҝð̉"],["@@¤؉́ǖǵ࢏ӠѥӡǋΉשύlƇϸЈذǾ೜Ӭ԰ʄο"],["@@Řšګт̟֔"],["@@υʭʍѲժòêʵ"],["@@ϡŝƄˠʞǁ"],["@@ړɍţǆࣘЪşΡ"],["@@ýϫƑɖɐǖ"],["@@խ̵B௴ҜҨĐ൥"],["@@ࠖڵRէோя੓ӨΡЮä۸Ⴖ "],["@@͑ǊϒE¿ǃ"],["@@ඐˡє̓ٶঝţٟᓃஐݕGī˧ഩӦޙӏࡷٸԛݹËؘ܌ߘᄢČЪɽۖͦ؆ǳ"],["@@Ģ͙ןވiҊ׀ӛ×Л"],["@@ͳԡѷ̀ի˟š٪ɢߊϦĄࡐϋǢ̕ǿϓ"],["@@уǫßچӤә"],["@@ՉȒąԲؐ܃"],["@@џ࠸Ɔɰ̚੧"],["@@ێóቧʿȷɒිŢ"],["@@ƪػϡǟଣК൜Ђ"],["@@Өg˲οፗ˹ȡ͞ȜҀఄĻ"],["@@Ṻҭ๧ȿၑڮ"],["@@ʳģûɀͰś"],["@@ܬᄳ২ʏҙܝŶЁຉքӟ෶ห̶ܦਸˮņʺқ¦ѴъÞ̨ο"],["@@ʳ¾ĸЎƼҋ"],["@@೙Ƴύ̶ּ֚଎ܽ"],["@@ÎӣѲ΀ʦɥÿʗՋxɩЏ¿ׅۤمǳΎɎ֔ڼz"],["@@տɏ~ٺ״ŧñ́"],["@@ɵJ÷̢ϖūçǿ"],["@@ݭϔԛईǒԘֶݲປɯͱ"],["@@ӭƈʗڌ߬Ĩåࢻ"],["@@ଢǕઆ࠙һˑ֑ĆϕߒɩſיԪ"],["@@͑®Š̈́Ȳα"],["@@ίĦʹƺ|ʟ"],["@@ɋԏ¿Ҵˌ"],["@@Bѡz䮿ʟࢣʬυz⽫ᔉᏮʦ؊ЛЇ௫ȟ̭Ҩߕֱўพރݨफ़ƌݍŨշ҂טǪढ़଺͏ࢨǨɾϹĈ̸̌֟õÓղჭ୔๕˴෡٢ჵǶୃࠪÐ˨Д൵gˋͮϯ³ړݶδॎ́óĻإ̟Ńʵટ͹λݝ^γ߈ʴ΀ˉֶყଊ׆ʎ૊ʑਨ޾ଜлŠϴͺƆʯɔ̆єᒫϡีæُ֞Ɂ८ෑϊڗéѤׄƪ۞௨ͼ೎ެਜƑ൤ۓ૰£̬ɭĕ΍т߱΃ߡȬೇ׬୧¼֒͒ļǞࢵ͂ÆİڧࠈΡՂÀࠪ޴ஈྡྷໆϖɜǲŅа໊ߠ⪊ኯሀɓ˪ίԒM"],["@@ʸ̏ԓїʃѪӠ˾"],["@@ʶͭǥͅؠѹˉṶ̈̌˂έę`κϙ͊Êπ̄ƿʢȜ"],["@@̉Çhզμ]ęҿ"],["@@؛sȜ̆рˑ"],["@@ਢ͙Ƙ̽ݟǁ࡫؜بׁͫɝƏ̯͢ƻ۱Θˮ̪৚ƶ"],["@@ͱEˀۂɹԎӸַȋؑ"],["@@ԔʅథɀɊҨՈѡ"],["@@͕fņͶɐΛ"],["@@யʘԲૌԣÈʥ"],["@@ʍ¾ƠƔĮȑ"],["@@ਣЪÂɾؖȱόѵ"],["@@ʲǓϿ̙Ǿȼʔ"],["@@Ǥύȡӡ̡ӨнāʚόԄþ"],["@@ͣȔ·ܘжηճ"],["@@ৎષʥ̧ࣹîǱՐѹʪܧੲبʰˬǗ"],["@@f˻ਖ਼ب਴ͫ"],["@@ਗ୯ປσ઩²׷ϐ⪇śॽȚࡿ׉ЏਧŐߧЌ۩بϥͶ݋઀è॔ଢࢨɹۚЀഺLƃɌފȢҰǩŪ́űԑ੟ɴฝั࢙̯ҧҏڋᑊᘯʒٿ͏यঞਣΞ¬Òܹ΁ŘഝѿǑٻ৓ǎɵЎ˺ન࿍౲Ϊ߈Wचϯʄԏ·࢓فЖၥsᎥ̥ஃΐ঻ੳԵʷΫȂҁڒԒᄜÌપΧ୞ੑÅ˹̸ȡະێՒԚఊଡ଼Ѹ૰ҒԒ̘Н˟Ⴜ͐˾űѼɸҦߚ୴ݘʻ׬ࢾӐǘܞȉоϽቀŏ࣎ϭԞɎኪȳജ࡚औপξ˜Щޝଋ"],["@@v٩ӫűȧ٦ڞŶ"],["@@ͨȘҼѠʶƔլਰϪਭࣛԥđ͋޸Щ٤ݝᏅִɋϳȮ௥ৌ෻ࡓмߑਤஊϧܢǄՎЏٸҜཀࣦߒ͵ࠍˤ̍@ࠟߩ܅Ǵχ"],["@@ԉ̈́ЦĤ˫"],["@@ΝǣךࡴڤȔˣƧ֏ϝϭ"],["@@วڎȩϠВƘ୊࠷Ķ΍"],["@@ɧ«ɢʪFȽ"],["@@ƫҀ͒ȓǥʫ"],["@@KƷǽʼȊŃ"],["@@ʕͺ̔Ã½˵"],["@@͝Êğ،оڕ"],["@@Ċ̏зőȗτՆ"],["@@ӗ͐ʀ՗Ϯ֌Ծ͆ԫǱڑ"],["@@ʗɃ۔ࡽྟƫ٨ѫZԷּ˿ħ̡˼ģƁƓॎଏدٳᦪᚁৗϗݷƌٍԈŞҵӭΧԁૅƃభɢ৅ซ৥ϷВه੧৿ͬ@Ú̳̫ӝ֠ΧÿҽӅġ޵νïȶїڗ਻ᬋ౿ϵᄨǭɽɫČIҶʇ˯ΉȀࡁЁɑ׈׫œۣ۪Ɖ֥Ӈ̡Ηǩۻǰोԩĕ૊́ϔႁПɧǞƸȘѧʣ̅̂܃Ǘυᓐ̴̫²ೈѯמஅҘʬϜ֟ք̨ा۩ܬÑఞĦӼϲˊ˕ƪֶھक़֘ɼȭ̋ʬؽቜ။ೲфཔē؀̎ʎڎٴˎਲlƘ̕൜ѷؼѺീ ܒஂĩ؂ࢦؐɕʞĒ׎̆̌ҬnԸᣰШΎฮ¼಺÷੐ϋ"],["@@ʡČʘрȯȔĈҖδַʁֽ"],["@@ᚲ¿໬ᄛŪܛṢᎣჰᕭߺաŻچӦग़౅௎֙ࠖഉ෬יÅνਁѻᄒږݜԉʮէߡ՗¿ϡɸɱƿʙي۟෢ͻ΢ქ݌ؓѓ૳܊Ԉ॔qสྐྵѭଏȦӫɇ݇ƪቋ˳ᅻࣕ׺ףѫহԌŒ࠯ǿძᇔᰏᐄटఈఉॶ༕Ꮤą۲ఁៜෟዒଡۊధ⁾ጻ૆ʁદѹ̀छഈ୙դቱቆ߱඀Îؼ࣐Ĳ૞۵ߠơ"]],encodeOffsets:[[[418002,-285758]],[[415089,-284888]],[[419243,-284103]],[[409942,-281572]],[[423701,-281946]],[[397934,-279922]],[[417974,-279309]],[[408421,-279843]],[[418892,-278790]],[[400731,-279497]],[[421529,-278721]],[[420542,-278565]],[[461294,-278882]],[[403212,-278824]],[[422309,-278206]],[[431149,-278092]],[[417447,-279491]],[[439428,-278692]],[[405077,-278689]],[[397462,-278245]],[[436632,-277698]],[[428353,-276916]],[[430035,-276794]],[[460313,-278569]],[[440681,-277821]],[[442449,-275645]],[[433429,-275594]],[[411923,-275419]],[[393049,-275382]],[[397217,-275015]],[[369624,-274113]],[[449394,-273573]],[[375374,-272387]],[[411225,-273181]],[[389932,-271851]],[[444671,-271959]],[[449955,-271567]],[[444996,-272099]],[[361707,-270944]],[[419641,-270379]],[[415328,-270833]],[[416965,-270374]],[[418406,-268414]],[[446729,-267582]],[[418568,-267219]],[[433066,-265780]],[[432285,-266023]],[[399965,-265450]],[[399644,-266551]],[[428420,-264423]],[[373947,-264197]],[[436309,-263817]],[[356331,-264682]],[[377549,-264177]],[[355717,-263477]],[[354724,-262391]],[[426215,-262686]],[[426125,-260882]],[[437932,-260611]],[[423248,-260652]],[[451900,-260342]],[[379561,-260418]],[[371631,-260553]],[[419506,-260649]],[[431931,-260529]],[[418451,-259195]],[[381622,-259220]],[[450510,-259044]],[[352862,-260849]],[[439700,-259588]],[[451648,-257776]],[[429601,-258130]],[[438674,-257442]],[[414772,-257109]],[[466903,-263118]],[[367335,-256912]],[[430327,-256870]],[[429463,-257351]],[[365322,-256949]],[[439182,-256011]],[[350928,-257447]],[[368165,-256480]],[[364090,-254521]],[[363390,-253963]],[[364473,-253140]],[[363770,-253626]],[[366114,-252779]],[[367643,-252682]],[[361754,-253299]],[[348269,-252003]],[[361935,-252019]],[[423024,-253284]],[[359806,-250330]],[[430781,-253686]],[[347876,-250338]],[[432754,-250403]],[[345508,-249562]],[[379402,-248074]],[[370873,-248190]],[[372311,-247387]],[[403328,-247051]],[[425144,-246627]],[[403963,-244580]],[[377862,-245935]],[[403117,-244623]],[[428312,-244997]],[[345685,-241735]]]}},{type:"Feature",properties:{name:"Isle of Man",childNum:1},geometry:{type:"Polygon",coordinates:["@@ࣳʥைݠ̓ӹ"],encodeOffsets:[[112758,-108191]]}},{type:"Feature",properties:{name:"India",childNum:1},geometry:{type:"Polygon",coordinates:["@@پ@мૢ಼ȸߜ঴ॎ£୾ിࣚаܤޛɣ}¦š|ϋÐȋмԱ­ɍŁ͡êэıԓࠀǮԂݵ֦ɛդǟպҟíʳඌثɨţǋĝŻkƓïã­ͭ˩͏ɃωϛɷΟǵͫɽēˡǃƽğÃƁʁŇˣě̮ʅȢŷȌŧże°èÊàƤ୸۵ശ਷İhŒÈĬľƶāΠʍͮȏЂĲÀǣǥ୲ͧȂĖ¤üƞ°ɦoЪƉǨ£׾ʬ˺ƙՆǱ˒ōÄŋnĻʡÊœਢؙɌËظɜǤ{ĶŁ²ͅŜāӊĶึӏܘːմѧ̄Ɩफ़̪ũòèþǊΖƚ̲ZȂï˖ɕΜSńԔ൞ɀćÖbƔö̔®;ĨΚǾҼȕʇગ^ȵƂƫŞĿ¥˳ǯËħȼӏ׸͙Шòբƻƅɖ]ŔЈºАæȰȼ͂Ī࠰ˇம¤͘ǤÚ@ŨƅͲ[ϪŖɢjŠ_Ŝ¬๞ğరŉޤழ੠ײᘮΪสࠒঐǑོޗɃЩzǙϦأaŃ͍ ̩κೳýᘙ൵Ήϑهàɟ¤±BÝγ؅ޟ̛ۣխ̬˃օ਍˹νɉͽ֥৕࠯̞ԟ§ʽǢĝᐙ­ŃͣŅͯ܉Šߡɭ͵˓ҡŭŝ´ǓɆǯÎƯˋŷõiƾු»ŌȍѦCۮŅদѽ_ƗŇ_kȟBȽҁӡ½ԻѿƑɓҲș̍͏ೠӾࣄࡦ͖ʲáԲѾƘˌ̒ئʚÂ˴ĻȮɈࢋԌṁGੳŜաǐőuèĺԆĄࡠ˓؎ɹǻÿ˵֝òԑ࠰ƣJHίá]օľ̫Ԑ̽ľÏǽò{ĦɫʷʣѱϟƵսĈáܐָؙĽƴΗȆĩ¯Ʊ߃ýʳBɷԕƩǱó@ȏĔēa˝ҋ§ŇwćŀǩƀƝǞÇʖeͦʇ̶¯ǘŗ`į}ĳ¿ʻ­ƭΫܽŰ˥ŮŷXՑψĳÜ¿ſޏJӁɌߝŕݱǥDǤًЩȮ˥Ωèसșʼɉਙ̽ķ͑͒ĳ̥сǈЀাԧՂј֡ࠃࢧණУׁГ̑׃ɔ֧ࣧࡿࢥۇ˯ľ¾ཱུ̙ԓǣVºђי̅ǳη׌ì༹૭ྭᇽ⛵ᦿ̱஭ଗԧੋhޙಓܓ̨߻ЫؽෑȔೱȅ׫Ͳ྿̥Ԡɡʉעշ͵຋ࣩ༳ѽຩʆÎĨ̓ŗዡ୯Ƨࣙ໹ƾԳࡴ̿ढ़ǠిνՍӏ͹ଉఇۅ঩Ր઩ೂҝ௴ƍ੠ʸࡿȂSɃࡸ͋π࣫᭮བྷ᧢এ⥤ࢋಜ˥ܞ̈@͝ЀƮȘͧŎϿޜࡋ㝪͈ଠͽЇµɶÞӮϨµчȒǑ਺ۈሰõ঺έ֮ˎɚˑoୢ٦౓ťЮ؞ϹXŔЪ׊ǐ്Ćʨǵħɟח׭ΪɅ¸ѳ؛࠽ᗿडکH௥ߌᖷᦼɔ͢ҘͽᏘ٠ࠒୄσͱ໗ЙःȨ௛ވϭࡠࢂؼృ֯ą׌˦ǂɠ¼ʊɌC̠IĤڪÐ²ºd¢YºĳþLĘÎǴ©ưƠʪAҠhʪMԜʋڶ˜Ѭƶ׎ƒïũśǖóŬkŖhĔȪǾĮ¦ŢGƤĖZâų͆ŀâĀŊøƾř̚ŏϨϹ߬ÚղǉĖơ¯ŵUЃÈ̛Ҙʈ঒c҆೫ֶͦࠤ߼ڠОӶʲΊŬĺŪĖϘȰȂI˨ȓĊϗɈƁǜZ܌ʾߐĒݖȸäȐǠ̄҄ϚҺ޼ŴŪೂئആᎌজΤȒƶŨƈDƆãǀÜȆˊȌࡎ࠸ĘA̤ƪ̊ǚIúkİǫǌÜǞȤҚMžėшÀŜĊŤȦǄʖƂޒ̀ưˀĂɜ̶w`"],encodeOffsets:[[294591,-167931]]}},{type:"Feature",properties:{name:"Br. Indian Ocean Ter.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ƋŻȑϪɮ̑iϼŚΗ"],encodeOffsets:[[280448,-276124]]}},{type:"Feature",properties:{name:"Ireland",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@իƠ׈Îȭ"],["@@Ӈͣ߭ᗝၫಣƞɛౕțȬ᫹ेعƘčͯେΉᎡʝܜҬೝǷขؖྡǛೲܨॣǠǊఢĬÁΆݼ͌ቮ̵̔Ǟյˑ཯ơ౬ؘ֦ܶࣞʖଏê࣫ϸƨ̦ࢦŰǿ˞ސǪŊǖ۩Ҥ԰ϩƊɨɬ᳴DʀැڄஷĜઢҰ׮ֺ൰ʚзٕತࢪԂ˹݁͛଻ܧࠡƻǄɳ৉Йؼ؁ւyਪؒȺީࣲä"]],encodeOffsets:[[[98683,-108934]],[[108156,-108455]]]}},{type:"Feature",properties:{name:"Iran",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@סӯฃʏ੨Ӭ ̤ଠXɁç"],["@@ࠊÞ⍐აԪّ֓Ν˂ӻܱσৎয׾Æų௟˲٫኶ѝٖࢣી؅ᕸ˓⇼۔̈́@ճǥ׾ýǐ୲ტ˴׮ێ୰ؒኂàҌϚཞóɂխᄦۍ஘¢ॊϯȚֽ଺٭ѠܭဦZyৡɯӍǈʓĭև\\Ʃėęėȡγȿęʃटχҍĕ«˥ǯƕu¡ÑVÏ¸ȬΑ՝ѣӇǥࡧƈˡ۲ȅटࠓ͋Şࢩń޹ǻƿЙ½ʗąӝMυäլíԀáڒĕǖɑ¦ӇíΣěǻׇԵיՅఽଁͺѩɂˑ௾ᕻᄮޕı࣍ʣ஧ࣔÔoŢăĄųCÑƵĿ·ѻ{ŽÓīϩኣϹȱ̙ʓڝ͍ĝƉťϛŅͿ«¹Ǚśq¡DſĽʋƯڛēȉũȅȻནЖ˷Ό˱˫፭ϠँƟӽ̮ḅՌ̕ᗂօܸ೥ệ෗ਿє୫Þጵ೾ΥڀࢹҮॉĘ֫ײɯ೬ι͒ĴњГȊÔؔࠣ૮ęتனϝ৛ھӪ̶ҵúΛࡷࢣΟŹĢăøŕֈ؁Жɶ୬܉Ȉࢼʲ҈ʴќǻ͈ȕͺƙƖʓۀē¤Χg৛ࠔρɈǅ²Ѕh¤AŞžǬȠŬǭи੅ఔѼո©֮Ƭʞ̄iȾԊ٪Ԧ˾ŚɢńĊƸw˪ȑҐɦͮ͢ĐȘò àȧł࠻FޭѠÛނ¹ĢĽvĝLĝƔɟຌƮͦϧ͘Ȧ؜ۋѶޖॖ̃Ŝɢ৔ȁொ߮ĴٞޢЮ˃৆෹ౚ˽"]],encodeOffsets:[[[241476,-182563]],[[222041,-149948]]]}},{type:"Feature",properties:{name:"Iraq",childNum:1},geometry:{type:"Polygon",coordinates:["@@̃jƫʝª֭ѻշ੆ఓǮзūǫȟŽBŝ£֌Ùςɇড়ࠓΨhĔ£ʔڿƚƕȖ͹Ǽ͇ʳћʱ҇ȇࢻ܊ɵ୫؂Е͔ܡɁÃȻ¤эȌΫáqÛڱʲ૫ɍܿਟϡэȯȝыλЃñ↵̆ჯุ᷇᩼ᵃᆔɉĸ܁Ųگń୛ȀϢˊ£ʈҹĵA᛺㍈ᛆٖݾࡈዒ̼ဖͶ̌ࡎȂ໎ଦɖ®Қ˒̎ʦþvǶćƐwΪĨ୒̻΀SќȪȾþ¥ÊóRŁĽͷŶǑŔòȔņԐˀƈKŴmÒ»ɠ຋ĞƓĞKľuºġÜށޮџ࠼EȨŁßȗñ͡ďɥͭȒҏx˩ĉƷɡŃ˽ř٩ԥȽԉ"],encodeOffsets:[[217760,-161688]]}},{type:"Feature",properties:{name:"Iceland",childNum:1},geometry:{type:"Polygon",coordinates:["@@ᑬ΄໿ֳֆɿֳ߫࢈¾؝ӃܤƬސ˝ۻρ̋܋ีڃٕR۫֙௩Ϸᎃ͋ᔗۡᢥͿاЅྭ˿ᥣͮ˻̎И˂ׯƱԣҪᷡʽҤֲǐȳܘńᑪހ౉ƣِ҈खʠஅȽŁז᧗ůŕ˦᩾ЦၲLԼ̶ഭrᆘ֘ၟ̖ᗭϯࠅ˂يȪ֞ǡľՒڬɏ޺ǀߟɞٰèƷ̮ڈdͪΘΘѽޤİ˳ΟцĞξβؑ͊সŤ৭ǌ՜ȮংD঄ࣝכֻً£৽ฆࠢԞƣဢஆתݓ࣎ێఴʐ̰৕ڌॄ۬ˊЏ౸ՔиǥࠔǂҤ֘ܘǞӈkȄٙ"],encodeOffsets:[[112850,-75340]]}},{type:"Feature",properties:{name:"Israel",childNum:1},geometry:{type:"Polygon",coordinates:["@@ԩɁo±ǉԇƙbƭŊYĨǙ¬ɩ¶͇Ǔȃ˛ɋЅ×և¹ĽáÉN¡¬cǎ´ƪ³žƫطͥ˥Խಔʠᎉ⥃ȍǥէ┢ˎȎ¤ƾҴθñƄnhגڶའᦒٮk஖ߖǭઅϹӑ"],encodeOffsets:[[190638,-166704]]}},{type:"Feature",properties:{name:"Italy",childNum:6},geometry:{type:"MultiPolygon",coordinates:[["@@ྑၻŐࣗڛࠓ௧ʜՙۀԛO౯ࡒਫʲʵԎऒ߾ʌ॒ͧΖࣆҹ᲌Ϩऀ̰Ōƿ"],["@@ȵɛVͤȠŇ"],["@@ȻħΠ˨ƣǿ"],["@@ࡧճԫૣួ১ǎщ؝۽ċʩشࠐවɯǪҪࡀˣߦ̬הڸƗჴ࣬׾՛ď̡"],["@@ďׁ͕Üڒʺ"],["@@Иāa§ÙÅȇįǳŏ©ãO¹Ȟ`Ëпʂʟ͕ȹQШ࢝Bᗙܯ̧қΖؓࠅչǫ૟ᆤษŪጉ܎঻਼۝Ᏸčࢿ࡯⊲ኁ܌ॿֳࣅһʸǟސिƈ͑ζ֯ĳงལ२ࠑǱࡥ഍ѷҟ࢕ඣਵ܃MĥɆ॒ಜߊ͘ǦРʗ᚜ി֎ȈԨɏѾ॥ƻμ̄߳Ȉ̅ॆധnޏԐയᆮ्ɮȎ͘ס࣌ϙêʨᔤᠧਫ਼ᒑ௟୿ʕۢࢺۛÓڳѸۖ઺ԁբીϨ̀͆λߨڬа¶·Ÿ¿҄î๸ɶѨ՞ЖȐȲƔȚĤѿֲϗ@ǛơȖωǌބ࠶ԄآǺÔH|ϿĨwŰ@ήŐɢ¸ņaĊˡîwŌxĞ˨Hż³ÔvĂŘƚŊŢŤŸfаǃlƂŲĞƼɺ̆®uȸGɖgĜşŰ£Ȣ]ƢhĔ®ƢƒɪǤ˔ĘЎhɀԊ}ࣼɌºeOŇñŝīIōƄɝ¸ǟÖŷǀã೦ɟθ˸KЎÿĵŹĹȉõѯɓ×ɽ"]],encodeOffsets:[[[142190,-151741]],[[124609,-149430]],[[126215,-144050]],[[129513,-144480]],[[133678,-139090]],[[145389,-129940]]]}},{type:"Feature",properties:{name:"Jamaica",childNum:1},geometry:{type:"Polygon",coordinates:["@@ᑺہϴՕڷŁޅʌǅ˩ЋêƇσϭˈࣷĮ܅ڒۗʪɒήܾǔ൞ơ"],encodeOffsets:[[849760,-205651]]}},{type:"Feature",properties:{name:"Jersey",childNum:1},geometry:{type:"Polygon",coordinates:["@@ԥūƜȬϊÿ"],encodeOffsets:[[110611,-121705]]}},{type:"Feature",properties:{name:"Jordan",childNum:1},geometry:{type:"Polygon",coordinates:["@@ϣʿćകͣ؇ƩළΏٓƻןƟႀᕡ˱ŷұɃ½«ʳϗȭ̕Åྣ˿Źû͙υͱϥӧ̹թ΍ᚳ΄ƬҊᎊ⥄ڦၪ¦ΜǊԈp²Ԫɂ੸ޫऔƻⷺᚼB᛹ҺĶ¤ʇϡˉ"],encodeOffsets:[[199088,-168369]]}},{type:"Feature",properties:{name:"Japan",childNum:28},geometry:{type:"MultiPolygon",coordinates:[["@@ҕĎɀɐΠŝŉǿ"],["@@ίϕĽȠӬͺ}ǃ"],["@@ϫy¬̸΀˽"],["@@࢓һǍ٭ͯŻ؈ഄвËβӐYԡ"],["@@ɉfϠǾЅ"],["@@țǻї˚ୠ״իۑ"],["@@ϩHŗˢʔǦ̶ǡÇ˭"],["@@ȏłѾࡰʭॱ"],["@@̕ĔӔŢǽȵ"],["@@ˁƆجΆª¸҅ˁʝ"],["@@Ә̍ѱŁ¥А"],["@@ǇʠʾКƘ̷ʍ΁"],["@@ˉĿ҈вǽ̱"],["@@ʽoļˆǂʕ"],["@@૶@ϋ݁ޞ©ş̺̂Յޣड߉ᛛֹð¬Йࣹգʴ໼ʻɭΏࡱࠩʪĿʼ͒ĨôͶ˝ϐ¬ܞ৪ஂ̧ĎŬϸٍޠʗǋĂ֧͞HfͷؓƺُҁƘΊ˹ݠټү࡙ࡲúʊԈµʴ஄Ѷˠ҂ԚȾԒűҎ׻"],["@@ϤíԡěžǊ"],["@@ȣ¶ĂϾʺăƗί"],["@@׺áȆࣧߕӅ҇߱ҭҊ۩Ɩݻͷ܁୉ϟȫΕzƾ̼ңƃ଴ࠣȭഖ޶ڔࣤժεࡌȊÚԖݬɸ࢒ȳ"],["@@əƉƙɌδā"],["@@ʉȑͣȲݖ֔ǧֳ"],["@@ʫњۼǭݗ"],["@@˳ÇÑʲȞľƨ̧"],["@@ʩhì֎֌Րũ՟ʶuԙկ"],["@@Ӓîěໃݮॵͺኡǯ܃ޙޫȃߥޑƧ͏чᙉ१ጓؤଉࣝӓʁ݁૗׭gࡤ׬٦֑ǌЧ֍ ׇхͮӝ¯ϓঙյӯÃߔɘŒϵɜ୫୿൯ƲਫǻҾьڙƔȱɫúڲȣ؍࠭ܶշÝʓଗͣथ൛ӕǗԯƪڙ৬ؾ۶݄෽ͦ੣ŗؙйᅝԉੁųܻǮГછࡥӌᄭǡٚʼ̈ݲ²⅘ឦᗢUᣔ՜̴ӷݶ¹Ӡɶьά޲ඐඒΪ෤ਘ̼डࣝɰعӞȉטѺეװఢඔਢְঘხ؀နƇԪՓƨצݼǓݴޞֈƼߜײkɼࠅЖʚϤſ˴ܤवƫVʲ̜֞؎̓"],["@@īˈ˒ĂǥΉ"],["@@̷þņɀȲ˽"],["@@ďƛśѮȬ̑"],["@@ᇲч൰ࣄԋ๫Ш߫ਞȈኙࡳፙˋୁਛыफᯃಖभï࣋қ֫ӌӗTͽ׫ႄધ͓Ư܇Ǫ׳ڏٓȑǷ͊ʺݬտࠈòڸ௰࠰Óް༘˟Ԇɠࢤ᧢ͧບȔպ֪ʐኤሣኺఉ"]],encodeOffsets:[[[421614,-189768]],[[422723,-189124]],[[425827,-188504]],[[433544,-183295]],[[435624,-180382]],[[436883,-179050]],[[440202,-173447]],[[441108,-173082]],[[439800,-167553]],[[438991,-168083]],[[435322,-166571]],[[436414,-166417]],[[437553,-165373]],[[438418,-163939]],[[442025,-164337]],[[444932,-163403]],[[437109,-162917]],[[450456,-162554]],[[450464,-161934]],[[451970,-162467]],[[437417,-162289]],[[448074,-157242]],[[461197,-152827]],[[468934,-143142]],[[464492,-141210]],[[469376,-132922]],[[468826,-132339]],[[475756,-135656]]]}},{type:"Feature",properties:{name:"Siachen Glacier",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǙՌÙȦĩ̦ÙȪɜŇіƍцĭ̼ñːU͢pǘࢥѝױ̋˭Ə"],encodeOffsets:[[300405,-160226]]}},{type:"Feature",properties:{name:"Kazakhstan",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@͵ȂΆ͖ʑ̓ʂɃ"],["@@ઁăߗ୛Ⴇ͍ۧੁÏ๕҉ё༣Եǯ̈́ഛIክӘᘁ∧ՔϣͭڝӟȾ۩Žѣц≓ޏҏ˛ஔϛ֥൧Җᐯࢱ˅ɸϝޛ̵ʃ௙ʅʨˇʘࢯĲşÎϱФȼñ⨧Ѹ݋ĳ̋uுtȋrŉY˥ɭಗǒཥݪܽʋݻ̃ϗڣřʕÈЭǓzǱł¹ĊࢍŸǩǢсĚԵǴਿƜյũȃϏŏঃࢡàȱ⡛ᇍ๭௱ƹֽ֧öѫ΄ʨӬ˻ϖᤃKᆢਅxݒᕂۏʭЇॾ৏ࣞ੥λᢃǢᥕ̣ຑ፶⧛ᤂ㜓ఱᚏ亅୓Ë߿຀ಽࠤᛁпසࠓࡔᔢඍʔБ٨ڏɜےԃཤ॓̔å̦ѶζᘶȻࡃ࢔ୈঌ⎆Íܙբ஠൰Ύ๪ૡ̞࠳γᄳـᵵଷ୵ǟϕС௹׺Ʋͺࡌ ࣵᐨഃɆѵƗõ઺ۇЖШࢢ६ޘ́ӞҞ݈ဪ๖׌®ࡒ୓खƾ̬໖࿊՘֊ԲၺҢ๺ঠھǽɚϫጜԾԼԛ༠D౔ধѬଃ֚੤ᅈॡጼ঎઴ĸ݊ЁॼҔਦ×޶ࠟ૆Ǔ´˷ڈȦ۾׸آϥੰਢψېਠዻܶڏقᗲࣚǯݺߊڒᑲôگԂࢩƀȌ΢ڼǦਧɘ࠴ࢺഎǍ㐮ঔ௬į৘و㘜߈ʘЮႪԠᖊԗ௤͊δཉӁࡿᆪcҐˎʘ࡭ӰќલЃਔȎ੓ݥtׇਘϬ̼ࣖ̏ΦႤրֶѮᵦ߄ֱڕҭb˽ᗚ၍᪄㕩঺ϨȒՎٴʘ݈ɭʕӽ٘ӝᐮٚψ౤ɨ઀ӯӈன౒Љʸঽሴʻ୎جɅӑ஌੩"]],encodeOffsets:[[[237354,-133644]],[[334382,-122102]]]}},{type:"Feature",properties:{name:"Kenya",childNum:1},geometry:{type:"Polygon",coordinates:["@@ब±ņл»ŗÛ΍L͉ֆݽገŵմΗ჈଑̴ɕ̴ãԐΔYጐ͑ψ˘̮ЂŨɔᐨ़֐ӗѲʭͼNʘִ͞Gطࡿعࢉࡵࡿu仇ؔ޵ВԡɎ˛˟ו֑ࡇǫտ֑׿भͫɉ఻ցܱޙ፷חӏ⍕ᦺÊৠ傓⯌ĈᥐӪ॰֜זǈϺ܊ҐЦ੠P͈y҆ǛߔÂƒ঵໒ąѢaŮŀѠOĲĭĞ˧ĤɟǴ¸Ŷš¨͝ݐҪѼҸҌ׶֮ւԾӌҎѐКŜ˷"],encodeOffsets:[[178986,-241365]]}},{type:"Feature",properties:{name:"Kyrgyzstan",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ȩ~ƓBJ«ŖǉŊUȾĒÝä"],["@@Ŭΐ̙ìďƺRųƝƀɉWŅǻńuȚGŎr"],["@@ॗЍƯŢōŐçĢ׭Ǔ͘ҡŰàǚgÚÏ´໇ҳեќᣡƕڲŞȮĐŖŒµŬċĲxzǂXäବО௲ԙॸغڼîݴ̝ੰٚզ}ĶͶДŝౌڶঙĢ՝ϲƧSȅËµȺSŬīƟµȇºǑjdʼēɪáŅÎȜÕeěéṆ̃ǁƃīCƫüŽң׋àӯǊǯϾઉϤဖਊЂŀҞȒp¾®ƱĮǵþŻ£ýßȲ঄ࢢϐŐȄնŪ߼ƋʄOԶǳтęǪǡࢎŷºĉǲŁǔyÇЮŚʖϘڤݼܾ̄ʌསݩಘǑ˦ɮŊZȌqூšv݌Ĵ⨨ѷŊʕǔƍŰŹĮƛ৐ƿˈʗʆʧj̇­{ٻƷ§ȫɑᙍݷұɣɳĥȑĹ÷ȫ֣ҡηÇ̑Zɑé̩ëఇļǥȟģ͍οşđ×ċóǳƃȋˉ˻ȱƅǽčĝǮ࢛˷ЫNŇ܎ñLڋ͡˻ÏɫW˛İǕƣdɟ঍ѓϝĵ΁¡ɇéƙƝǋɥĉōÍÛSĉèŇlʍ×ģķųįʉÑȁ͵ȁ¡ūãɷď˗ণÄ"]],encodeOffsets:[[[286295,-147281]],[[287719,-147179]],[[291136,-148561]]]}},{type:"Feature",properties:{name:"Cambodia",childNum:1},geometry:{type:"Polygon",coordinates:["@@௥ղٕɱĩʼрӸЙָгԋ҃ks௨υ۬ƭЧࡪêݬջղ˹ጺҼÊࡨ௾ڒѬ⇈Ɉٔ҅ŤŤĶŮxFÕ¨ƃĄƁɤȯРľҚǃȚʏǂśΰQƔ˺ÇǪṳ̑·ǶâΖÎĎȠĦuȔ}ɀƈŶƘÚÛæıüËƞďǀţĪşĀ­ȾÆKŲƜĖüìuŀFцϾȐĉĀjʖ͆ҵಓֆ໭͏࣭n๯ʷɷЏĮୗܫٗç»בࢋǂΝ˝aރڎֹUթۍƶȿ˲ޱďȿʅ׫ƪ]ҷ҃Ϗॕ˟"],encodeOffsets:[[367621,-227599]]}},{type:"Feature",properties:{name:"Kiribati",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ώʷࣛζ͎ũƾɚǣǮːų©ʧ"],["@@ƨ˳̡ǢƺŒ"]],encodeOffsets:[[[635147,-250938]],[[629649,-245297]]]}},{type:"Feature",properties:{name:"Korea",childNum:7},geometry:{type:"MultiPolygon",coordinates:[["@@ͷȢϴͪ௤Ūąϵஙʿ"],["@@ɯǒӦɦʵϷ"],["@@ͳ̨ǂłǲЩ"],["@@ɕϯÒï̺ӶƵ"],["@@ȿƏ͕˼А͎Ɔҹ"],["@@̏ț¦DѾɜȓ"],["@@Ꭶ↗ƣᖵζGϹఇޥ࠽௙µǝԩ࡯ͺۥƭԻځɮƐѷΙγ̿ȄǾҴ߅ۻ̗ǎԍѵՍߦ֜Æǎ˲ωCʁ݌ݚढʹǠ׬Ԋлچ­୾ځKǶܴՄؼƁǢˣʆʴҟ֪ǋೊਪ૲ᎊÈݰ۶"]],encodeOffsets:[[[429225,-165371]],[[429147,-162243]],[[429039,-161259]],[[434016,-161055]],[[435785,-161075]],[[430424,-153060]],[[435384,-150641]]]}},{type:"Feature",properties:{name:"Kuwait",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ɉŽр̨ЖˌѯɫΟ"],["@@၍u͑৺ኁʆьμȰȞϢю݀ਠ૬Ɏɰ±҂ȿQ͡ǾכΩĲٍԃۨčԊᆏ"]],encodeOffsets:[[[221765,-175190]],[[221640,-178139]]]}},{type:"Feature",properties:{name:"Lao PDR",childNum:1},geometry:{type:"Polygon",coordinates:["@@ठ੝ƘԝǜJúîòȢ¢rˢ˯ˡङØƙѺ۟ɪŕռƋπȁ੠֊Ȯë͠ȹӰϷūȳūÉǋįĥı¾ɮ}ˈēúĻRė|Ɲ°©͢OĒõþȕYơ˃ʭƱƓǻέ̥ģkدǘѧeçQ«ÛôȁdǻđƓɓŧćÍcÿ᧮ฯOùƝˑ¼ȯܸ֙ŰǡƠȿȪսڎܟ֜ѹƆǍȐ̓ǐŇ®ǥ\\ůXϟɮΝǸěŢĎĦĄ¢yĄˉĄīৎۛmȱïãϵȃÃąɼЩŴŽӠϙŤǽÞũµƣĿǅąƏǧǣ¶ƛ¢ıŧHʥůęťɫÿiȏĊхϽĿEëvĕûűƛÅLȽÿ®ĩŠƿŤƝĐûÌåĲÙÜدɫčȟÍΕá¸ǵȘ̣ÈǩƓ˹ίRǁŜșʐҙǄПĽɣȰăƂ§ƄEÖƌ¶ɠŊȀŬŬžÄƊĄ֢ÔȨoʒēȖ^̜̀԰Ôɔ\\ǂĬӗ̆ΚǍŔЉŰɕƬӁۄƅОXՌÆќưԂƱΰڑՎəʾʹϮ̙ԜȹɰřÁɷ´Ιƨ̳Ăˉ\\ǭďýYûÜùÏƛ§şčƍȇĵʕսȃȵţįěRÕ©aħȾīĐȳªɯƐћΜĹfƵĝȯȡǏÿū¤ķëɁ͏˯Ʒqk͗ȿֱӭƕ©чɸנనH͔ǯːGþ״நƭ޺ò΢[Ɛ໳\\ɫҒ़͠гҰɩĽȇɁ¿|ƂzŮƲӠǒ͂ǴƦȀÖΒJƂüSªƭ\\ÃĎ¤ǀО׆ࡼӠƠɜŦķÛʱĘơƝpũ¶ÏðθľɔTΌȋÀbżņnƽɮĬٲlϬUňГड़İ¼ŬŖƈ̠ĊÄìp®[ǒɫШŖƞÓôó"],encodeOffsets:[[362983,-194953]]}},{type:"Feature",properties:{name:"Lebanon",childNum:1},geometry:{type:"Polygon",coordinates:["@@கߕ٭lᆴᤒज࡞ࣄÜȽΟʹƭ¼ҿ࠭ڍƒȕࢁȗVϑѹ΅"],encodeOffsets:[[191330,-164804]]}},{type:"Feature",properties:{name:"Liberia",childNum:1},geometry:{type:"Polygon",coordinates:["@@Ƭ͟Ŏ઻ٵ॥ٮϧڴóȪŗǞѫƚˍȾ}ЊƩĒٱӡශ]ޗ¬ӵ༻Ֆ቟ਨᝩᘌᔇൄ҅ӦטܴऄۂքӸɨۤžŨβƖɨږŒTȠèǜȩٲʌքѕɖ෯ʹ١ݐ҃Ԭŀ׎॒μ̃"],encodeOffsets:[[59961,-235381]]}},{type:"Feature",properties:{name:"Libya",childNum:1},geometry:{type:"Polygon",coordinates:["@@ࠩܓƑ฻ࠡ੫¶ᗷᄽ䳓໕䳑ݗ⪹ᕍcǫૣ䨫⢖佑Ⱀ᜵৑ᇕࡅൃలṹݲىᄺၓՌڟƥʯʀȇညࠣ࿪ɾ׮ৰږĔ༪خ৲֨ᷦԏᑀԼʮࣺΞŀĐώΌҶҔɎເӬӸҶŖдѠְ͸҂ȶФȊҸȤζƴńƆŚģ˂Ĥ̶ĚʸÚƴɬӜ˺Ɨ͎ÉࡔՁঢŹ௠ʬ⏲୧Ֆᓏ⤸૟ຶ଩࣎ͯථӾତ੠Œቲڞ࣎ಮߴᝄߜ୼§ᄒڡƗٟ͈ˁၶқྌÇЄ݃"],encodeOffsets:[[161833,-169650]]}},{type:"Feature",properties:{name:"Saint Lucia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ħɷ̻̞ͦԜņ؉"],encodeOffsets:[[895840,-218296]]}},{type:"Feature",properties:{name:"Sri Lanka",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ÎǛ͵̂˨ť"],["@@׼জ३ંኗ͌ǓĖًॄ኏ÃࡿՍ੥Ꮿু৵èϓ͎ӝສɫᴔĞʤÂءЈጆФ۠Jࠖݮɏ࠹Ϛųθ"]],encodeOffsets:[[[300716,-231311]],[[301094,-229232]]]}},{type:"Feature",properties:{name:"Lesotho",childNum:1},geometry:{type:"Polygon",coordinates:["@@ի±οࡋĭʁڱê৷ܖփݴұۺҤʄڂಘҔƬɠȾ˲ǬӌȔɶ¶ĒႼଓ̦ԥo˙Å˩ʷ̛nӣࢇҭ"],encodeOffsets:[[170039,-338180]]}},{type:"Feature",properties:{name:"Lithuania",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@Ńjࡊ݈݅ݱ"],["@@ōĀńĚdŐËØڃǫȷƃƽ·ͿšÍɋ˓é¿োǽƁÔÇæಹçų®ǲҖĳŸŻĚǭĠƟ¢đZAİÇ¢ƏÄƇ^ǋĕW̨Ĥƾټے˻рণZ෗҄ʐђĴߤʠҶ®łۈ˄࠺˲ঌȶ༞wǐþűǈdȢĆ႐ɯႮ̚Ēѷ੢ɇ೜঑ÑĵƕǝɅəȑǟÏß°ÅΪ}ƚR­Գ͋؉tĵ̏˕ɇğ˟ʓ×ԣҋƁ˵«ÏœÙɕǛȯȅĥķI±˔¡Mʧџę"]],encodeOffsets:[[[176627,-105208]],[[186245,-108315]]]}},{type:"Feature",properties:{name:"Luxembourg",childNum:1},geometry:{type:"Polygon",coordinates:["@@ۭޝ˭ļƗŽçƓqĩr¾§ƙôĤɬˈضƖĢŨŘȄǀ͚ȌɎNéXMŝȗaƇâƃŀ×Ͳȣ"],encodeOffsets:[[132737,-120158]]}},{type:"Feature",properties:{name:"Latvia",childNum:1},geometry:{type:"Polygon",coordinates:["@@།ޕ৙î૙ι೛঒੡ɈđѸႭ̙ՑƢ୽ĎȡąǇcʍǊ༝x߳ǷǗ}࠹˱ۇ˃৾ၐᘈာᆾ΄تළ֚˅ᆎزی൸òWʖĤ๴ΐʞťٮȥࡖ܍ϒƽफ़ǪΎǁʺˮGjɁφʧ¿Ĺĉš߻ޭΦ´æ¿ʥÈ́Ůŧýˣ¥ǃ±ŭʡʿ"],encodeOffsets:[[195430,-102851]]}},{type:"Feature",properties:{name:"Moldova",childNum:1},geometry:{type:"Polygon",coordinates:["@@ƹͬ౺ᙴݰ؏߸Տᆜ٣ɼþüԬȺĪڮCފʄ಺ݙǦN౨Уя੝ӔѱÕ݁ɶǹ˨ťɽĿɛĳȕ͚ʵƻķяğs۝ɲ̭ʟȧLłˮ]¨ăPҏƵħû·ƏǓʡřŉţǧˍǅˋġĩȏǷŽ˓ŻȽƽśͧЧ§ŅƑ÷"],encodeOffsets:[[182043,-132019]]}},{type:"Feature",properties:{name:"Madagascar",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ȳЩΐ࢘ƛҭ"],["@@˹³éˬʢƐłχ"],["@@ৎ෣ঠ⎟یอӗ౿ґ΢ΫޖӱƝ؂ᓏćܿ׻॑ɨๅƙওዹ嚷͟῍ԗᎳ࡙߹௛Ƿಡݕڇg᫉ಀ੟༒ǓႬඥ᢬ĩড়ˤಈ׺Іॄᬌ³ૂ֥ߊŃݠփ৤ύ፜߬໰HઘॎĨ٠Ԇ˸ȕ֐ĲȂЎূǜ׆зȧҾ˜҄ংڈІӕûَӴ࡮ʈŠĢڍׂ࠰јăӡڴЪࣺ˧΂Ĳ͘ȸƨԊ˝૦ৠȚॢα͈࠘îրߨߚ߁"]],encodeOffsets:[[[220993,-302108]],[[215753,-292454]],[[218792,-289912]]]}},{type:"Feature",properties:{name:"Mexico",childNum:10},geometry:{type:"MultiPolygon",coordinates:[["@@̉GפȌ̙ȃ"],["@@ƯкѶȨ̅ء"],["@@ɋōŗ̨ͤș"],["@@ĺʣпь͆ǧ"],["@@؏ղŶ૒ĀষКڋ"],["@@ȱЯfҞȌ­"],["@@Щüǀעʪڝ"],["@@ãՇձǬºߖ˺ĈˢՁ"],["@@ࡑֲ̭רॎ؇Ȳ֑"],["@@ԉ↽͖⡍͈ࣟ୺௉ŉٷ©چ঍਌ၰᰧሼቍՖ୵ಚஷ͉ې˿ɯǨொƯࣞзࡠީኤנึĐऀҦ৺ĞȔнࡶǇԶϔʁآȯyආਚuࡌόӠÙᛦઌצᮢڈ৊þႰΙĴɞСÖܲ¤Պϡɐ؇ȹࠁࣅఓČࠡұԇŨȅӎàѯԫɁͨâ̟ሯɃϫү֘±۴үࠏӷÏӋ੽ܫ^aΓ⛑Jǀ౅҇JӅH੶ࢃوΧƆǣøƧfŧƘĩʊÙƄŉ\\ė·ȩNɛÀȩǓGЕ@ؕA৛AڥAϽࠑɡҫȋϹ̸ց¥ŁĥÇwďåऩ❩⁤ઁԮĵſূҫຩЦƊʮѻÛȅ͇̔͑ԚƱܿóᗡએ೥öࢧЦ౱źᄽݴ׭Ԧᑑв᧡੾ᣋၰჽɲ႕טஃ૸ᜫ઀උ๶ՙࣺࡌѠȩϴчŰԖ۞ÇࠬՏ˶׵ࠢĽݸхڨ᱑᫸᎛೘֚ʡ]̀ঢ়ˮࢿਲԖ໓ۼʱΎԕŹɒ֤ط́ҧ̠ȱۂϺؐȬşқؾԳЄףi֕ࡆৗΈץۢVܐ೯ɲ᧱ᚪᥩ⌈Ƚஂ೛ώϵӄѯǆϣ˙ሙࢢъ֧hળ஺ᡫፒำ݆ণۚ˹άٝԸȝ՚೿ৌڱഎቁЂɓ͓ڞ֒ϫॐᏍဌᏧʖ୵ܦգÌ՘ڬρबଯذНËܓਛש৷ཨỵᒂʵ۟ژʭ࿞ࣣ෪໵ࠞѝ߼ȝ΍ۋƻثոර֞˻ӊૉۀƇʀਨŻֆȦɑնɡ˿մʉˬ઺ᘻᏺᄕ࣠౵ᚔׁόǷԲࢅ߸ȱϞǚʼ֋͞ࡉ஬ヰРƟҩ厢᥹㪚\\ȹ৖Ⓖ{ߘ܃ᰐᅧ๞ᓡবٵᕶࢃԊӦ΢஬ۢˈᇖɁᄞ೫඀ት᐀მ͐઻ਂഽ⚮ಁҬȪ"]],encodeOffsets:[[[810754,-205051]],[[822881,-200616]],[[769714,-197049]],[[757417,-187794]],[[753627,-189043]],[[755541,-185020]],[[743747,-179431]],[[751177,-176878]],[[748624,-176750]],[[792823,-185181]]]}},{type:"Feature",properties:{name:"Macedonia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ޔܓƘ֧ԫࣙ˯Wʡ΅˽ďʡµاĚяÕ׵ЙλťƁÕಷ§ӅǬǹޖښഐͰA˨Ѷڸɢкǁ͈ˌϪØHAׂŰ૶|"],encodeOffsets:[[163741,-140574]]}},{type:"Feature",properties:{name:"Mali",childNum:1},geometry:{type:"Polygon",coordinates:["@@ޱⷽҷෛ࠯฻޽ӧ÷˳९ǂǁȕ⑯Ƶ࢕ٯ࿩ǁ॥ͨୡ܍ԛᓟ߽Ѝ١ਛƂܟإʹڱڇvɩࡋՍɕࢋӬӓɉ҅нƊܯױ˕~ݡڣב൛։ৡ؝ݛɑൡë̯e΃ĭůýŁ̯ʥǽhʱƄE źȮ\\èÑ܄ևΕǯÈǿƾÅ¹ůƩxˋÙĩֹeȕѽޥѲ׭ȜߏץCΦեξଂܱ¦ۈߜࡿۊô֐ω੺ލƔ~ҵहԍ૕Ф੃ۯзۈࢡщϳЦ̰Ҥưழзफ़ғťЋ܂˲ժyݎϥ֠@޸φƛÎ´ƤĒߐൺɒȘє˨ĸ}२ਜ਼ٶ؊တŋ૾ȎʼÜņȊŪoͻĎ»倶cؒᄂ˝Φɓ˲բ֌֎֌ľဦ̎╘ݎ䔤ȺဦƤ૚ø֌èԜڪ@ܖ@۪BᅚB㋸⠝㠈⭙ćّࢆדĘЇᲈ஁İছщۍӎʻጰΔ"],encodeOffsets:[[98428,-203781]]}},{type:"Feature",properties:{name:"Malta",childNum:1},geometry:{type:"Polygon",coordinates:["@@̝éãΖς˫"],encodeOffsets:[[137370,-158199]]}},{type:"Feature",properties:{name:"Myanmar",childNum:15},geometry:{type:"MultiPolygon",coordinates:[["@@ƧŭЎϲʥ˃"],["@@ŦӉб̌͠ƪ"],["@@óңȵÜŏԈϺĿ"],["@@ħВ̰˷ɇř"],["@@ƱʝƉƮ˼İ"],["@@Ĝ˥ͳ̯ފɖǳ"],["@@οï״ĸɳ"],["@@ǁTɢʘßʫ"],["@@ƻɓ͜ԂӀׇ̯"],["@@ȯŠǈѾ¨֝"],["@@хШ֪KƣЛ"],["@@Ӭ̃ŏɍ͕aɛϜǖƘ"],["@@~̯ȏѬǒŻ"],["@@`ɇȣ־Ȅε"],["@@ƟɛࡻӟɥїŃ³óĹ£ƿÄčƮ[T©ƁûΑIǿÕǳƥǑ́ƱӟyŭʩƖĻĄƕrϷɛ֯ČӉࣻœЃܳௗȉީƊӓ٫˧ዓ࠯ƿըԙİુຸᇁв෿ЊȜŌƥǭϥԩ̅ƻໍࠩλ@ԝݪൟத෵ģໝ׊ઉД႕েວ৅ষƽੇ͓Ώǟ෨ӴۢȜത̶˺Ց¦ǪઠɃ®ȀɲƷᆖݱᆰƥݕāƤv৐ϋશʐ@ѩҦŏᗪˁ͒Ϡ௴޳Ĺ͇౴ܽۂĂࠡΩܕލҟԋמʸܧ౭؝ࢫৃȑøƚކЧ؋ԷgÛࠄչحǜലىथӱǧ২⅘ݽᤈǖpλ;Jࢦ̿ࢃӇ˸йތ܎̉аҐߡݤɶ̦ডΞ̛ӃʙӞȖڴ֓҃ǔθ̫Ӿïױߗ૔ɷ૮ά̙ٚ͢ÉΰÔ੔YĎ˨΂ìHńÕРɧ˔ҢɮͶşߢͰ܊ͤņ®ńĞᐚʾǡԠ¨࠰̝֦৖Ɋ;˺ξֆ਎̫˄̜ծޠۤĂ͈ȆǖĬŨAÞ£²ßɠوΊϒᘚ൶೴þ̪ι͎bńϥؤyǚɄЪȰƐێӼáɶî̐ĈĈȄɞˈΔƊfबݍϪ෍Ψʊжȏᄃɷೳ˿ʽǌҳݻإу΃دяĝʣࡣΥɅǕه;כύ܃ฦҀൊzϫӛ̊عKॻ஬˙ǰʗࠡሁཐɕV׻аح৖oઢَĺу"]],encodeOffsets:[[[350603,-228903]],[[350771,-226122]],[[351784,-223962]],[[351698,-223525]],[[350528,-222202]],[[351495,-221634]],[[351285,-220268]],[[342130,-212847]],[[341261,-212502]],[[349690,-211664]],[[339592,-205032]],[[339819,-202648]],[[339282,-201736]],[[337991,-201650]],[[360178,-197167]]]}},{type:"Feature",properties:{name:"Montenegro",childNum:1},geometry:{type:"Polygon",coordinates:["@@۞ٗ૪ݧڝƱǑӉٝǃƫ΀஁৷Žڷ಻໸̢Ǿµ̤ŪєÔàð̖ĊɐȲʄȴŞ˒ƘǞeªťņfĀƢû̸¶äÜľsĪüxŚjÔKyȇ"],encodeOffsets:[[156936,-137476]]}},{type:"Feature",properties:{name:"Mongolia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ኁݷ෕౗ᐽ܅ɝൣ¹ઃê᳇Οጫڇ௹ԁʳñȗǗƯŃƯ­োƘÞӨзçڑōءķౕ֬৻Ȗࡳƨσżَ֧ǍÀǡࡋஅƦࢿŦɉd૯ʓⳗӺძƻࣗఘҁາϭȒƜՖಳǎᗏ೦๟ȲѿÅ͗KԣÐ˵öऑjੋ͂υ³̭݂चଢpƺëǊȾμੲĩ̜½ǄƅèǏ¦͝ؾ±ӪŝȼSž²ʉÎǉĬģ̒Ɠnšģǧ³ŇTιІƽÈࣛÏƉłĝĖǁ́ƶșŐɒéƮȯ¨ǹƶٗȄʌ;ѓȎƍȪ͘ˆ÷ǶĄBļʞLŦºĴòȢĈʰѪऊüϲčłǰĆúí¢Áð{ɜÜǆèĀƚĴê̤ȈŌɺĖǌ îдनђಖ˼ƖĊŞľךʖҐĊƚ|ĐdưKĞŨࠆϨؒIȄpЬμŚƍΞʱŊ[žØŜƲǌxǚyʘϷᙢŁÈݡѴѷŴVĦѤB͐ʍࠬÃ଺ȜҴɓൄ£ϸͻ̀zհϞࣄɲؾ٤ǆԪʷĂţĀĉĞˉނ߆ૠఆ܄ڀۼአ߳ɜ¥ЄeӜUƀྒ׏ϾӬĻ֜Ưôė_šÃĳ}ʏÜŵµӷǌʛŏ̗ኘࡳٮƏȐǢ¸֎µ᫖܎࿨ϙ̼ю~˚јʹǎʗȒţǞĭɶO׆QֶāEڃࠌֻɖũǸ¥౼ѠēƢÏഔɝӞŎ֊ǣʢ¾ࠈ̲˾ŚȆസǼࢰʎФÇɮʀĺ̢ŀİǠǶɼ΀Ǥ˪ư˲Ƥ༞ײࣔĝԐɅԄыǪĉŶ͒wˆ]ǔÊˮļѺƀǚGˊéЀŻʔɍᑥ᫏ğ˗oʓ·ɗ͇ƑʣŭŢֳՔсȰÆȌǪ΀ƶǲJŨ˨Y̊Êɦ\\˺ĴʀãЪ̻žैݨບUƄĝɢ˓ŸƟʊñ˞ėŲéஆ૽͌ٻŁгʷĵę±ǵK̗¼̕΍ÈЕɌɭÊŗƳūƱvƝuγ~ȝ͕ďǷܝͩϭƾɧIíõıɥWķÇßওÎٗɗࠡࣕxğ^ŏĽų଩ؽળƳԝιĠϟß΁п֟ͿȭǭѹǗԉ̕ƙેƠۡǐəúǳȾǟŀثZǋ¦˷௭༗०అƧ̟"],encodeOffsets:[[394065,-136848]]}},{type:"Feature",properties:{name:"N. Mariana Is.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ďȀʎŒƽ̑"],encodeOffsets:[[480015,-214719]]}},{type:"Feature",properties:{name:"Mozambique",childNum:1},geometry:{type:"Polygon",coordinates:["@@Ǉ܎ʝ՘õך˙ƌ֑…ٛ౞ܡࣼথᝆ˘ɲᇂᖚɥάƖ੨࢚ຢڡ೴èِϥμҺತۍᾴŒϦᙋٌဧ࣢ᄳÚȅ߂Ցศ㳤ᔒર఑ྐ͎ͦϱʜ༥ԳகݮયৰধѢ̱ÇЕƦŉǠSƺ¶ĳ٪ǃϖƁƨŠаȪӔՀþɾäĤČÄɠ᛼ÝÚėќЅҠԙע˱͊͏βֻ֨ळҠșӺʊ׿ᑞҨਫ਼Ĳɪφ̔MӺRՄkȞµƄLƢ°͎̀ˬIІȁɚǉ²ƍʼā์̸ࣜ͏φĂϸɂϪ٘ಔ˅ਫ਼վᅲ֮πȴৈߚδѳʿɵʴƥΑ૭РຕSણʀ͍ʧާ̎G̐ᢁ˺ȳĉܧʼˀɚڕЯࢵņ͙డᓍЩƷƺ́໻ற፥؁ᇱ૗׿֧य़჉͛Œiͅߩ̏ჟᆵ۟ʠ˜ˑĺฟܦݙईቁϒဣǜŻݸɀ̑ζཻŽላϰÍqգ࡫ಷ❓ሥי଱ۀןƮЮϗǌ಑F§୉ΓØȇ_"],encodeOffsets:[[177261,-329213]]}},{type:"Feature",properties:{name:"Mauritania",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ȅɑ£ɨπΒƕΧ"],["@@ؑᄁ倵dÝϸąuŃĳÛŅʻ૽ȍဏŌٵ؉१ੜݝҁɍЙǿ֑ЁэƣđÍ³عٟ͐ժڙতԁઢܯʪʱÃߑॶ๻ɎᅡѕਇļֵܵŵʉřЕĻǭǘৎˮ۶๖᮪þ᠄ԍߦՐͶ͝ėࢌၖ˩ौѡ£ͳৢ̉ĽɧԹZȺѦ६⒬LᡘFৢDԒBԒ@ͮB°̔ɾº͆º͂ʀ෪¤ܪθДΌȨЎɲ̦ǮƢªьÔࡖͰ̊૶ɮ̺ࣴ௜Ă̦ǆ؎ʒࣴƸְ≴BᎰ@ࠄ@Ф@Ѿ@æʮİμƊԊƎԎŨѸɦࠆ䏚ㄏᅙA۩Aܕ@ک@ӽⓧݍ䔣̍╗Ľဥ֋֍Åભɔ˱˞Υ"]],encodeOffsets:[[[43189,-202244]],[[71222,-211583]]]}},{type:"Feature",properties:{name:"Montserrat",childNum:1},geometry:{type:"Polygon",coordinates:["@@ƩĝɼŞƝ"],encodeOffsets:[[891315,-210335]]}},{type:"Feature",properties:{name:"Mauritius",childNum:1},geometry:{type:"Polygon",coordinates:["@@ݩŸƴմӔѠΨӽʅ؍"],encodeOffsets:[[242915,-311879]]}},{type:"Feature",properties:{name:"Malawi",childNum:1},geometry:{type:"Polygon",coordinates:["@@̓Nυďëaƽҧ੝؀ᑝʉȚӹऴҟּ֧͐α˲͉ԚסІҟƶӵ᛻ÃɟģċɽãԿýȩӓşЯƂƧǄϕĴ٩µƹǟTƥŊÈЖѡ̲৯নݭરԴ஖ʛ༦ͥϲྏ͍௻ඞΡƵӕܘ̥ɖ׈ࠊģ୨૴ߊϹĘɻѪाȿؒüȰվʝۂޖڠˍδ§ĘʾԫѲªȊfȴʋɰʥΔͳ©ȯӘᔺיɄϺނՇْଛ̲ఁą١иב˒řŜƽîʱ"],encodeOffsets:[[179014,-287583]]}},{type:"Feature",properties:{name:"Malaysia",childNum:8},geometry:{type:"MultiPolygon",coordinates:[["@@ǧºÄ݌Ť߅"],["@@ȷĂŴɺĄ̻"],["@@ՅƆȾЀǟ"],["@@ɗźвȸŻřʳ"],["@@ƶʇЃɇɁΎҐł"],["@@Րƽфۗఒ৭ۜథL⤙ࠢ߃࣌ኇäݟ̟Â̓Ӏtϗڕ[ӑˏ˝Ӟඡۘẙᘲl࠘ಉིǸŸЁ޴ջ⊔ӓਂ͢բǺ࣬̕ȩʴӑВOǵੋ̛̄ূ؆ː˿ѬľՀढ"],["@@੏όಹøอ»Ч΍Էᣯҫm̅̋đ׍ɖʝࢥ؏Ī؁ܑ஁ിػѹ൛ѸƗ̖਱kٳˍʍڍ׿̍ནĔೱуቛ၌ʫؾȮ̌Հי઒ĳዚܧѡδըದŽ՞ՂJǄߴιमᑢᝊ͎޴ಶకшߔȫܮـОǚଙШč͗௨ՂôМή̍ܐмю̦ƿ̬ǶጴᵐɖͳơӳޚߺɦÛƊכ۬οǾηšީ͕ʿ૾ϘʖёНǗĎǻ࡚ɠ՘Ӎ඘ГĤɹŅͧۑ̫รùǡΩ࠸ܳ˟ே˿۟Ⱥ˟ϫ"],["@@Əş͘҈ȲD̉˭ş"]],encodeOffsets:[[[386216,-249411]],[[366674,-248548]],[[403963,-244580]],[[356035,-241556]],[[354891,-238362]],[[361018,-238972]],[[403117,-244623]],[[402031,-236446]]]}},{type:"Feature",properties:{name:"Namibia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ኦΦਆćؔœٖԋމ×މԭ٣à౻঍঳਎⺛܋ദ䷣ᔣď௒㭫ᄌ䮍ǿࣃ˟УΡÁϕǋĉርŞጥт͝ѲĻ԰ѽ̬΅ɪʛēĥŽ{ȯ§ķt̅ŹˁăʯȍÕĳዋฤକಘࣛዸӁьगᮢଋᇸ࢛▚ᮡ⃨ࣵ࿚Ꮑᬐᇋ᏶ǹ֘ƭ૲ΌºɬžҖġɼSĖjтe೔դںƵ೘ࠛ⴬¦⾼qൌࣛ᪖Ƈ౜Γ℀ТᗲѲ"],encodeOffsets:[[149475,-304120]]}},{type:"Feature",properties:{name:"New Caledonia",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ɝLɔɒǽ"],["@@ˮ¹ϣͽË͍֎ϪŪ®ȵ"],["@@ܑμčǖΚˊ̃ȆՖȴय़"],["@@Ԧą࿲ઝ৮૟᪺ᇉ¶Գѣţڛː‥ᓘ࿗ᆀɯݸ̸ɹ"]],encodeOffsets:[[[538610,-317712]],[[539945,-314457]],[[538322,-313722]],[[529766,-311227]]]}},{type:"Feature",properties:{name:"Niger",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǿᾡۼౡƃӵڸࡿڧফྵ䀕ᬋᣯ᝵ᵯÔսŌ३ЏMʁé੡ӹԣ˛ӟԵήجકġ֗ŋĹ{൫ਉँzছȚ؟ͺݑԮ̟îοႛݱՕʊই௾ʯ\\ݏɪݍʊǥų¹˿ǱಉÃ௯؝ط໋ܽչ͋ሃཧ໦৳αܫ׻ईϢΘȿՂ௙ƥ୷࣪ĞܠҾg஭ޠ΁׺wؖЩ֚ʚऔ࿪ǂ࢖ٰ⑰ƶǂȖ॰ǁø˴޾Ө࠰฼Ҹො޲ⷾ⍨ݪ⡎᷶榎㢜Ṻݱൄఱᇖࡆ"],encodeOffsets:[[129320,-193270]]}},{type:"Feature",properties:{name:"Nigeria",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@έ»ȶ̐Ƹʓ"],["@@Ֆʉႜݲπ̠íലࡧজșंy൬ਊĺ|֘ŌખĢὶثӠԶᆈࡀАNؐুʞЕĴਫhχǨȭ՘ŽȐͣ˫้ࡋԅƉ©ȗSǩóɋǇӛׯу׷ΟॽΑ̡ʙ੏ص̭ƙ˝ɧडßƷ̓ͱ֣ǉɻϿȝ̭ם༁ӻյĽǍȌΑӋσǹƕėѕउׅĹ¬Ƈ՞ǑǮ݉ٚҗбˏEȗ¥²ľµČʓǙϥ˃ũ©ĥÁƋʗǉ˃Ͽʫ୑૫ܡዏӏܛؑФĴ޳นïʍ̄֕ʉ҇ϖƾҋՉˣͅݠǬ޽ԝóŃ̤ç̝ٷí˞ɵ΍҃Ǝࠣ۔˳ڞųШɀÆɷĢZ͊т̨ϏǉЕ̎אǪ˥ɌӥƧԫࡲःܞᔅǬآϦٱǍɭ˻ණÕĨԒ¸ƪÂŐ¡ƆǼK˶ĠȠܸÖ¬²Röፒˬຬ؎Ăݺ༌ڒԒǾࣞˮpǸьƏΰs۠ĞœŴ˹ӨɶǤ̔ňƠǞƨƮႜܾպظ໌௰؞ಊÄ̀ǲŴºǦ჎ӏঈ௽"]],encodeOffsets:[[[102496,-243948]],[[102990,-220244]]]}},{type:"Feature",properties:{name:"Nicaragua",childNum:1},geometry:{type:"Polygon",coordinates:["@@ƅ̋҉œ࿽ۜקɣ༅մͫ˵ⳗ⣒ǢǦϬ˙ǸžؠńƸӘĜĪǲƒZÞȓܜƚbńÀ˖~Ծ§Ĭǰ̂ǠɆŶĪłƤȍҴ˓ðh¦fXsŰĮŖং݄ˀ؆ϨԖԨͫʜÕኾծȘɤʾêּ½ćÕÃōˋǕȨƇǣ˞ǟΞࣵЯޓǹວͨ᎛ˏłn̵֪ҿˌ੭ȩþĩѹ˶ąƼУЭމׂࡧ"],encodeOffsets:[[834931,-226219]]}},{type:"Feature",properties:{name:"Niue",childNum:1},geometry:{type:"Polygon",coordinates:["@@͉x˄ʈÆʿ"],encodeOffsets:[[599923,-308055]]}},{type:"Feature",properties:{name:"Netherlands",childNum:6,cp:[5.0752777,52.358465]},geometry:{type:"MultiPolygon",coordinates:[["@@ąʥ͍؀ДΙ"],["@@ࢯЏࣧĐC̐ᅜp"],["@@ƖȯۋƎնâ"],["@@Зûք͢ƫʥ"],["@@൷ΊڤŮई˧Ϲ˦Ϡɣ҆Ⴘૠගමآªബېర̴༮ú֚Σଯන۟õť·ӄȇয়ڥ\\ʧኵρâ߃ԧڱ۫ʿɾ˫̛Ή׏Äڰࡐਏ˰Ɖьკȁ"],["@@ˇ`ࡢļיś"]],encodeOffsets:[[[876542,-222871]],[[129469,-115826]],[[129327,-114863]],[[133747,-111232]],[[129469,-115826]],[[135338,-110372]]]}},{type:"Feature",properties:{name:"Norway",childNum:27},geometry:{type:"MultiPolygon",coordinates:[["@@ȅˇǎׂx̹"],["@@̹I̬ʞNʓ"],["@@ѫǢ؞ŶǱ̗"],["@@́®૨ˀƋɧڙą"],["@@ऋXܮʐȞʧ"],["@@ϗşऺ֔֡ѳ"],["@@͒ĽᎳԁҖτౌɼ"],["@@ثܵඳŀᇀݰȠŹ"],["@@ဠ۶ŷկตԧ≩Ӄᲂॐ঒ड़༐ׂፋဇ"],["@@ܾȱۻۉ࡛GૉХϭŶजٶۤªےӔ"],["@@ЏßϠ̲pʑ"],["@@׵̨˴֤˕̃"],["@@նĿࣇѽ๥գ఍³ࠒҺبᄚਚƍЉ"],["@@Ʋ̽Χьȶō"],["@@ഩׯ÷δ෺ϮWƱ"],["@@֋]ӔдøЕ"],["@@စࣣؗÑϏ͈᰺΢"],["@@ҡչ੫ɔ՝ғໃ̝౟أÒΎෞڐ^ѺᅻࢢෛΟઍpథԃቛ༱ᕕࢵྕӈ౑Η໿ǂ҇ఎઍƃͩϭ࠙YॡĦϫ࿍ઽ᧫Ѿᇇೕ̶࠭ඓҳᑏਿǳࠥᮡోɛЩᐟЃྕᇣᭇྗ؈ˁϟٯءΥ೻ƌ࣯Ǚሓࣛࡡڻયᖯஹ౉ࠀ࠙ߡٻࢹŷ̃ఁܳޕᚷଵɩڱࡗ߿γ˶ࡽͤиಠዹၗ݅ŕ͛Ίm̓⠅ᇙ់˕×˴٥ţϮφ๗אАৠࣞɽܠ̊קŋȅ͜ѬҊಜ֊ᴱࡱޔ࣐یͪԌś૨Μ࢝ĴጼરҊjыҭ๴ߨቭχ᷋ན࠸৒৆ĀٷȂĴ՞ංժগ˅ƕƪҘ࣒⊘ɸȚчɰ̲ಌˊΙȬТ̚ॱ׷ࣣˢڳΑᇹëՁȒɂվ݆ŚбԎ̄ϒዂȯ຺ȂᨛƦƢԈቾֶɠƫੌż͎ƌୱƋޒҘᝊŁʎଦɞᤍœݺҸຖϚূm׆շΏ۸௴КʥΖעɮ੎gȝ̛ೖτɊԹ໠ǀɎμ໴ЖȽɂܘɸȹĺॽ˹ɟ͵ᒁֽ׫ѠṒཬ᜚ܴͦƼݓƃހӶ࿎ҔԬǱ஬՞௝ΏηɈᔄආݜŨȯ͒ᨐѴᇇƭबࣾၐΆεʦరӆᣰȆ၉Ƹ໴ڴݠԍԔϮڟǸвκ਋ɫǰ͞पκࡦÃͫːཤΪĽާِঈ᧼ʔႇʄ➲ඌ࣮۲द̟ʕΎཆּ਄øࡣߟᗴଡ଼ၳ്ரҶрµ֖ࢤरÅ݄ˢَЍ͹ࣀᆒê࢜ʚ҆ࢗϼԆᱦ૊˦ਫ਼Ϻ޶Λचv᪕ရ⮘ᇴୡஉचɨߤԶޜƈ˳͌न͒ெʷ฻ڕڄwषयᙴ൞௬ӟ۪Ųߎॡຯͯᨛïఆʷŋ֍Ըw՞͔Ģ̹ࣼƎ"],["@@ۤ͝੥ÿ͵ǐ۸ʎ"],["@@ᣊݸ΍̧ᕻҏ"],["@@ͥˠ֮Tʇ˳"],["@@ڤ¥֋ޯႊǫ঻ΝᛂՁ㉥યপٜủȿ៶ਢࠓఐᥚ˄"],["@@̂ࡏॡԬѢঞɾٹ"],["@@੐gḩşഘʶ݂ŭ"],["@@൘ʅ༡ऑሜӬףޑർҡ೺Ŕɐࠍỵ̉旻⮃ᓣึƴؔ௲ࡖξ┐ʒĐɤル˳Üֺজ½⃌ܜᓐĨՉ̜਒Ψᭉэ࠾ښਟ́ˏȰৗۇಹ͏ၻõޱ཰๊ăܘ߈ޭૅ·Ӟ࢔਄ذ̔ǽజɴ؄ʉ͖ɲឨĴ͙ͭ៓͛ಖ^Фԩᛤৰ̤ᇇ೜ཊሪࠖъ͋"],["@@Ⴟ}⒄ʖᐃɗ"],["@@࿚҅ാ߆଀ǈܣٝឬϲ√҅ȫ֥⚕ૻₗұྣĆҼМ‍गȴѣɪᘮΜ῿ʅϖ೔ʌகʄᖎÑᅐߴ୎ԛ"]],encodeOffsets:[[[147157,-91491]],[[148415,-89371]],[[160488,-83226]],[[162051,-82327]],[[171049,-79057]],[[181893,-70851]],[[184862,-69784]],[[189499,-67935]],[[189819,-68976]],[[196224,-66153]],[[224425,-65606]],[[204715,-64807]],[[201275,-64871]],[[202781,-64461]],[[212112,-63553]],[[213044,-63504]],[[212374,-62826]],[[226428,-65642]],[[217896,-61936]],[[140995,-62763]],[[212752,-53074]],[[231087,-41604]],[[210134,-41563]],[[247225,-40741]],[[226228,-38028]],[[258307,-37448]],[[235724,-37092]]]}},{type:"Feature",properties:{name:"Nepal",childNum:1},geometry:{type:"Polygon",coordinates:["@@TŃɖΛð˕Yȁƙ̱ǉΕçýñ་·ճѨܗˏีӐӉĵśĂƧшǣ|طɛɋÌਡؚÉŔʢmļÃŌˑŎՅǲ˹ƚ׽ʫǧ¤ЩƊɥpƝ¯£ûȁĕୱͨǦ¿ǤıЁͭȐΟʎƵĂĽīőÇįgവਸ୷۶ƣÉß¯çŻfȋŨȡŸ̭ʆĜňˤƂʂĠÄǄƾĔˢɾǶͬɸΠϊϜپӮä®Ɣðżlж̻ȄŤѐմԌŐҔÇЌā̬יቘঢ়߲॥ߒɌǶËǎ÷¢ƛìˍĆ˧ଞ޽ŲcӖŲŬeæyG­ŅȝķʣÎűȎ¿іאFǊųƸȑƔ͙ÆƏČ¡ǂÆêͬƊèƴȷɦƕǖçʖþŔ˘ƌ¢ǎkÌÅÆŇϮţǲęʎȫ͐ܢe՞Ǆդҗ࿳"],encodeOffsets:[[326288,-181983]]}},{type:"Feature",properties:{name:"New Zealand",childNum:9},geometry:{type:"MultiPolygon",coordinates:[["@@ŗǍȫǘ̈́I"],["@@ȋܓĐҺ׺Ͷ«ūґ"],["@@əǁϺ̫ອхش߂QҨΨ\\τϑ"],["@@øˍ˃ƂȌƌ"],["@@қ˱ʂɻÁ͞՟ЕȱɗǸȤЬ؅Øؚʆ߶½"],["@@̇ŻрҴŷͷ"],["@@ᆂߒρީҶذْÏףӻܠ̲ۉܱOգЦƽᝥ᪕ಣڍɘȁґʗΜ°ɂЃռŭ³ѹ୯êʾП́Էϊ˘ѣᎃࠗ̍ฏ̭Dɘ˹ॡ෽żйݭƿྟുܥƷጷοծܟS׵Ӣνȹ೑Įдՠ֡ȋǈ;ӋǕN̐જˎсƪиʮ՛ǘϖƌĥɎגɟǠ˒ЛώӰĢťˈҶ̤Ĳ̇ØмٺЂʘƻėξೌ੆໊ˀC̦ු՞ࣜ࠴͘£ȃǖٴǧĄ̑ņƎƵ՞ۢÜϋÔцيՈҞ๮ৠִӦ೚૮०ٜC҉ÇýˣּЗȲ९"],["@@ЯƐŒ̼̞ҋ"],["@@ЂȬ¤ɯ೰ѫÜΙϲŤӦ஻Љߤ଩ʏகௌͽƶӍͶ`ïྶـׇהᎣᝮޫ࿜ঘૂ̫܍ኍٯϥɯ୅ߋψঃͧ΍ݙ΄˃٫ઓጿᚽ๩ࢷ˙ў׹cA҈ӻƩ௠๼ʘݔǷϸ˹θᕗলͱюȠВஸЄ҈Њ֞ጴ˲Ȱكཐ̞ʿѤ̈̍̀ٽJփ৖Ѳʕ߶ʱǤ¥ȕ҇˴ɹӎSʕӴۗʑï౟ᆬӦԂ֓ЛŵŶϿՔǺцউశܶÎȃʁ؎ࣃ"]],encodeOffsets:[[[538924,-399203]],[[531914,-394468]],[[537397,-383831]],[[534708,-379242]],[[577968,-375316]],[[553200,-367469]],[[551063,-368602]],[[558242,-354963]],[[552507,-351295]]]}},{type:"Feature",properties:{name:"Oman",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ǹk˲غސļο׏ڕ"],["@@ϬଗࣖભⅨਡஎቿ࠘ͻƇ٧ଭྜྷୁࡹ૫ཋׇŴϼǝuޗޛӃ్¸๗ᖥםୗႏᎧ͕֍ֽiףՑفᕛõੵջ஑ʡሧ㉊䇴ᖎ᎚⬀߉༶Ɔ݌঒ჸĳ˚୲˚÷ͼΫÖ˶ඔӎȒâӑߊ՞"],["@@͑ĚǬߚȃࠨ٦ӽ༭"]],encodeOffsets:[[[245712,-200846]],[[241179,-187861]],[[241218,-186029]]]}},{type:"Feature",properties:{name:"Pakistan",childNum:1},geometry:{type:"Polygon",coordinates:["@@ȡ्ኧ๳ൢᑳɯᐹຐශ½ñפԣÙЛĞģԀĺ ÖĭͱºʭɀăњXۂХɛ̵ʿāƯޑ̿ʕƁȥǃĉţ¿śĘчNŽȣҙÛǝǬǋlįJù̉Ụ̈̌ƩėBࡍ࠷ˉȋÛȅäƿCƅŧƇȑƵছΣഅᎋುإųũҹ޻҃ϙǟ̃ãȏݕȷߏđ܋ʽǛYɇƂĉϘҩȞϗȯũĕūĹʱΉНӵ߻ڟͥࠣ೬ֵd҅ʇ঑̜җŴ¯ːWŶVƢ°ǊĕÙձϺ߫ŐϧŚ̙ ƽŉ÷áÿĿŴͅYáƣĕšHĭ¥ȩǽēŕgūlǕôŜŪð׍ƑѫƵڵ˛ȕhǓŠƱńʩNҟgʩBƟ ƯǳªėÍýKěŎ¹c±ÏģکԫNʉɟ»˥ǁƏɵԳͮѿȳۇࠀƅತিɼĴࠊ۹ࡀҗʏܢŅ⏵̱̑˟టӪૣōȉ͉⢃Å¼ĔȊưڜľʌCƀr¢ǚŜ¬ºņ΀ŦϜĞƊڞ͎ʔȲ̚ኤϺĬϪžÔ|¸ѼƶŀDÒăŴšĄÓp࣓ʤநĲ࣎ᄭޖ௽ᕼɁ˒͹Ѫ޼ʓྊӫব˙ᜮȪૢʁۦ;໘ᣘۆ׸ᐸ௢਒ആÝڈʔ˳θ൚ڒू@ԼϙৎڴŊ૖ߊೀტ׺ԣྎྺūࣈɔɢܐɋήးส²ࠀا૎Ǝǲគ൐ᇦ̄"],encodeOffsets:[[290926,-155440]]}},{type:"Feature",properties:{name:"Panama",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ֱ˒ì̶ǾÞ͈٥"],["@@ǏβǜÆKз"],["@@ȻΧ܆୵ۻࣧԭ̔ʙੁ౭ᇸ̐êˬڄࢺ֡߻ࠀʻɣЧʒAͳ˝ؒ೹ࠜधÓϥˉʩӁƶĭໃࡡt̫ઈಋෙؗՍŘ҃ආлؗգɊ֏ञঅźȅʺ঳zНǛIϽσװΜʸĄ±͜ƵɾèƢ͜ʜƉǔɯƦƁX×ࢊ¬ĀÐæƄĠ²κȓǘǂƮţ̨Ƀłӫ΀ıΩϮɏڌº˵Шౄ߫ଐʆ໠ܢ୐ࢄₚߵးಏ"]],encodeOffsets:[[[841182,-235997]],[[848386,-233429]],[[852462,-232381]]]}},{type:"Feature",properties:{name:"Peru",childNum:1},geometry:{type:"Polygon",coordinates:["@@෽টŇԣðčâ̧ևƻK˷Éßµ­¦ÝôÏŎīƬȋŨĲÝȹʹ੅͊ʩĆ¯`ýoşņșɂƣːλȞʙa˵͙͙̇̇ᆺɛΐૃሬՂ̨ǠøॶނäƶÐóŝÏţǥϏᷝŊøƨâǘǧŬ÷ŎmŚNǆΦƤʜŠˌSκ¯ɈL࿐ⅉݟࡍуᅛ͈ટోඕĉϑπؓڭࣛ̈କޘхұ̱ω້థÙŇɨ̽@ǃeĳAEȏƭʓǡϵťVٛΙӗଙ̹ᅥ๢̛ࢆሩಾᥙଊᬗᄔ߯ମࠅתީႪˢƊȌॲ૏ᘊ٭٦Ûࢶ࠿ࡀƏ৞࡭ီਛᾠషღ఍᛼១཮ОՂüžҢगᓔƨަᅨᇜРʎǜхĚ݃ĉ£ċÅďų̓ÒŧëµƃŏRĻöėɦSɇ˵ĽŻnąŤéĜMȶŀɞʂžǚëɘƗʸŭľåȘpǎƂķƈēt̋ĨɉŢǑƤȟƐáǼCʒïĮ¶Ğƌ´þŶQȞ®ŲņŎВɰŌ۲݄ቢɰǙ࢞શᦠम೼२ஂഔΔౠĎΠÕg߶ρМƧάƻňǃĨ`üٖ§Ґȴݪͯ´ĳÈťƜćǚýƎƅĠěňŃŀƕǾ؃ŒǡĊĩєŻƮƻƆÓƘǅѺśζষږ̫üোξƏłŧྦcՆǔŞĎƪƎƦƎŔіǅΪʕ҈²ȌưȘ¬Ƃ¨׎̡Ųƛǈخ̛Ŋŧǰõƴ£ງᠯҒȍĮOƤªĪêĚXĚãŶđƮϑ͞ʡňǑmƣñÍŏ_ǑļʛǛŦÇŤƃĮ˱яpǫЙЅያ·ऻյɓĉˑʳ͏ʏȻŹ̛ÏűÝʹ଩ѵڝŕɇãƻǮࠓ"],encodeOffsets:[[864376,-273637]]}},{type:"Feature",properties:{name:"Philippines",childNum:37},geometry:{type:"MultiPolygon",coordinates:[["@@५нޠךȌǛ"],["@@̥֔ʿƻडȂَˠ"],["@@̙ˣ֊ԔǢמ̳Գϳ"],["@@̗Ȼ¨ʦʰ©"],["@@şǝŷȜÐΒȈ¦@е"],["@@ƵÁUˤǌɡ"],["@@̽w}Ⱦͼȅ"],["@@ȏ̙ˋȤҜĶ"],["@@иįʆܫϛߗԆʡȪؽ×ࠡ̔Ώf१࢟ࢫk௑ߋᘐ̣ƽ۱౷Ҡԅǘડܩࡕʛ੆ہѣལߢ̓Ϧȗ೎Ԑࣖ஥ऺϭuʅࣇٯپ֯ϥŉўϹĞઋᆕЇ¦ĉԮܾဢຄӂ͌تࡾ׾ࣾ؅ū࡫ࣆИӂࠤܮć͌ࣸ؂ɳǀΠ٤တ୺উ"],["@@ƫǊȔӴɄׅʫ÷"],["@@੓пеÆʳѠ߬܆θÆՔ̕՛"],["@@ѓҜЂܘ୳"],["@@ˡåƔаΌƽȽȋ"],["@@ʡŃNФφ̖ű׵"],["@@̣]ˣאڧκΗހǔ̴ࡴˬéढϮࡨؘʊ֞ǁŨ͇ऱᏉֿτݙљұ"],["@@Äୈຘᰘŋዝۏ؉ݿື"],["@@Ű̑ǫD¼̎"],["@@Ƚǉ̬ޢ⣈◖ȴ૔֪ݾû߭Ͷ৥ܑϯС׭ँ͕ߣ๑ᡳᆥ"],["@@÷ͱɛŴ̔Ⱦ"],["@@ߎäȾί±॓ւ੯˵˃ͻЊøݟխ̠൒ˑКӟđɳൊ՚љ"],["@@˧aˉЖʹ´Ⱦѧ"],["@@ݾ©ŢΉפɂī֋ܗړăωᆹࡕϪ᧊СӂʬĠਊع"],["@@ȽǧלϲćKӏ"],["@@˾ŜǼҫڳUˁٶҼ̏"],["@@ԗ͞КĆľУ"],["@@Ǡңӆ̅ũ೹ǚߗΠу૟ɈѧѮʧӢǼҦிࢄͅठᑄġ"],["@@ۺܓFӑయਤܑٱȴไ੎ڏ"],["@@ȏ֍ȑԎȊײ͐Ũŷڙ"],["@@Lȝ͵ےͪӳ"],["@@ǼͿࢽ࣪܂֩"],["@@ૈĿۢګdુ΃ݱΡǽۇپ֩ไڿծƢɼڨč"],["@@ѲǣʳׁϩˤȬӂ"],["@@Бɥ̎̕ѤखкҧƵՕ"],["@@ʑޯȃࡤіó"],["@@ྶܑіòʦ΢ʼ×̫ᄭޤி࣯ᑇ௑֛XםӿޛٖೃǃΓ˶ऋ঎ՑǢƨȅͼ।Ϝڮ́ΖওѬȖÜԌઢԇʵַǝ׈ࡅċΡߌǗȵ਽Ѝ˞ǘԔඳ˺̱ࢸ఍ਮ˳±мષǇ҉ӓܴಷतةɇέѝտæ։Ҡҙǵy࡬ۤۀ¾;ޯՈ_ࢻͯáЇݾωż׫Ḭƈǆ܆رҴƒĎ⃒׸ዊ૨ʊ"],["@@ɑś˜ϔÉʷ"],["@@fȯͻȐ͖`"]],encodeOffsets:[[[410438,-241660]],[[412938,-239426]],[[415492,-238464]],[[417823,-235789]],[[401893,-234495]],[[402657,-233591]],[[422975,-231060]],[[419962,-230802]],[[426245,-230574]],[[426407,-229359]],[[422422,-229302]],[[425409,-228955]],[[409737,-227396]],[[417167,-227432]],[[418412,-231274]],[[419080,-230223]],[[420217,-225221]],[[402547,-232978]],[[409646,-224560]],[[422441,-225058]],[[422540,-224651]],[[416809,-224316]],[[410138,-224075]],[[410333,-222808]],[[417277,-222423]],[[424307,-221826]],[[420160,-222482]],[[415758,-222298]],[[420328,-222027]],[[419012,-220938]],[[412058,-219230]],[[415345,-219064]],[[421965,-218813]],[[415767,-215068]],[[413543,-205220]],[[415781,-204458]],[[414744,-203183]]]}},{type:"Feature",properties:{name:"Palau",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǫ̪͒иŽ݉"],encodeOffsets:[[449570,-235863]]}},{type:"Feature",properties:{name:"Papua New Guinea",childNum:21},geometry:{type:"MultiPolygon",coordinates:[["@@Ӿʗҟıקʬȁќ݌ͽ"],["@@ΣƟɑǨֶ"],["@@ǩəĚʨĐ"],["@@ӔƚƥՑ؃ɮУ࡬ںۡ"],["@@׈ǅɼ֑৹ĪǊȤǃЀȰ"],["@@Ձ̐ɘΐʠŻÊԣ"],["@@ܘʛ̵ৗѾʶŔ"],["@@ǅˇؾʤɼy۳"],["@@ףʮ׆½^ȯ"],["@@յѾ̠׊ˉÛӓ"],["@@ՓϿ࠹͂̿ऒ঩ࡆßઠࠂϽࠬ୯ߎՃ̜ۇ"],["@@ϡ˪˪ʺĸգ"],["@@ʅܜȺɒɀҙǳӓ"],["@@̣ƀʤ˼Àл"],["@@ҌȌ٤˻ūઃࠓ֟ΤمƧɧӁʃ޳ٗࠕၕޑᆹÓ׿ӆهēၹ৊ɄѶᏼʣ੐ƈ̌Ǡ˶ঊǶſɕ՗ʜӏ྄Ǣ॔ઠޘǤǧ໊ܞɉ"],["@@ÃȝɱˬɊŠìȭ"],["@@y⽬ʫφʠࢤy䯀AѢ⥮ཧഞʙఢ࠷ऄ­ൂ୩ւąॸও±ಗ✊ำՄֿĮޥླƃυʥՌௗᐲྵː৙Ҙ̽̐ߛൔ°Ƣ਱ဦэ֏ˁʨҩᓶԁ࣑ǃ҆ҽ̩ܳگǨדԆ⩭وွ໊ť݀ݓʎ೽ዦᛋؘʗϢ߉ƞࠋыથڔ٤એʍȍ۵ÚɌҁᝩ˕ЃΊ̽Ù׸ϳܞÆۮ́׬١wѽཱྀࠏणΞឋřΙɰ"],["@@Ǔǟ͹Ѳǅ૾ࢅ௴ᨓ၎ܕȬǲϊ᧸ີᔴታʴցύࣛ"],["@@؅HҗиרȪҐȕfѓ"],["@@ࠦɛԯ˝๏ÑʖՀऄ"],["@@ӭǼþˆаҁ"]],encodeOffsets:[[[501127,-287305]],[[503151,-286991]],[[493964,-284820]],[[494460,-283333]],[[492958,-281495]],[[492460,-281897]],[[498679,-280439]],[[494533,-279823]],[[474065,-279136]],[[486141,-271893]],[[507749,-274240]],[[483825,-270817]],[[504186,-270819]],[[480669,-268892]],[[496743,-267720]],[[498803,-264547]],[[466968,-280874]],[[499605,-268974]],[[492711,-263260]],[[483522,-261346]],[[490881,-260236]]]}},{type:"Feature",properties:{name:"Poland",childNum:1},geometry:{type:"Polygon",coordinates:["@@ӕᤫᎿએۜսکୣŃҭРএˏƙŇرοˑ֩ñሻ੯টٽȅƯŭãſǿˣȤȩٟoųpߙ˼֯Ґଣbիɵ७ǂࡅҗש¾ɈϒѳԎ߻ѵՑʜƹࠜऍΒϧƗΑ͒κђ٥ŝࣙϬ^эࢃ̟ԯܜװͲȋǾؑįྒྷࣖǝ˹ȷÇǳM|ς̬ȚȘͺހÚ̕ƊG²ƴȀҠ؀֒ÂϔīØ£ĆfĺŜǄɖɔïˀέ̪ƋɊĖŪђɀʠǮȪȾ|¢ĔƬʐূըȭǶЬա˶⨲ݾઐמ╢ِھ΅ֵƈͶ࠿ဂȪ୚đ└Ƌ૮uüdǌĖƈ]ƐÃÈ¡BįĒYƠ¡ǮğżęĴŷǱҕ"],encodeOffsets:[[180833,-108861]]}},{type:"Feature",properties:{name:"Puerto Rico",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ͳî؜U˧×"],["@@ଈƙê̷گ׵ᨯ®˵ࠤȀ̞ᘤŧ"]],encodeOffsets:[[[881863,-206611]],[[879809,-205685]]]}},{type:"Feature",properties:{name:"Dem. Rep. Korea",childNum:1},geometry:{type:"Polygon",coordinates:["@@̮Էॵˉੵ੉Ƀቷߑ̉ᆽೳᎿޫ҃౻ࠆˣசਏݯ۵ᎉÇ਩૱սɎ̡֑۽ժࣵ׍ċˈګȠӢͲધńࡼफ़੐̂ߧˊאઊåՔு԰̻͋Կࢊஔ৶᜔঺ႚᆼҒĲѰՑᎎ͏͜Ӯң࡚ᇴŔ࢒ࡀࠀþӺ௸۶ʃO֜͡ҵ"],encodeOffsets:[[441555,-139970]]}},{type:"Feature",properties:{name:"Portugal",childNum:7,cp:[-8.7440694,39.9251454]},geometry:{type:"MultiPolygon",coordinates:[["@@ঢʙޫɇιɸǄɪ"],["@@সOġǑ୑GĿ̌μš"],["@@ƈę࠳Uďʖ޼ƥ"],["@@ΕǐΔĜBʫ"],["@@Փ˜־Â©͝"],["@@̑Ûɰήȩ"],["@@੭ϳ໑ʶधȥݤࢾ߆ᗦў¹ȏɀेƽŜҲִȬߦݐਿݥԡĳǕɦ݈ா቎გफ़ි߼ᑨɁƪжЂ౶Ѱįܯྂ¸ۚͤ੾ēř׿ֆ̽Ꭷ୥С๯ٝϻƨ͝ϧԁбʽࣣ­֨ണಏഃʢأ͔dปਉЩ௱"]],encodeOffsets:[[[51204,-166340]],[[34585,-152776]],[[28859,-151107]],[[27669,-150910]],[[31886,-150587]],[[954799,-148504]],[[81319,-154580]]]}},{type:"Feature",properties:{name:"Paraguay",childNum:1},geometry:{type:"Polygon",coordinates:["@@jǋLųÈė¸şÒáŏѕŴó¶ۍأᅏǭ۟ᡒͽߺдňÂȕȒȕŚѪ@̜ÏǈɯɭȲΡ́ᎡpĽĴƃoʁǋ`ȕĖėǘȞʴ¬ɲ¾ࢌδήȥŘƻʐǝɥ׃߉ᆟƋɛуӇģ͍˳१˥ڳߓࢣׁԟڿǩĕƁŻĿǝýŁŹ¥ǷŵƷɉŹǓI̓ȰҥV݉Գ⊣غ঑ĝW˄Ęƞସઆ˼ތڞ֮ୌಘϝݰ᧓౼ಧ਼ያՆѥҌೳੜٕ౤ˉϰͣȪǁƂbÞ§ØപᤢɚૼໜᏊ♴ު᎜~ᅌஓĽޣ"],encodeOffsets:[[900449,-311005]]}},{type:"Feature",properties:{name:"Palestine",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ˍȍȊcæݚטþīKԗԵ"],["@@˦ԾظͦŽƬƩ´Ǎ³«dM¢ŜǈØֈɌІȄ˜͈ǔɪµǚ«ZħƮŉƚa¥Λڥၩಓʟ"]],encodeOffsets:[[[185885,-170638]],[[187340,-170433]]]}},{type:"Feature",properties:{name:"Fr. Polynesia",childNum:5},geometry:{type:"MultiPolygon",coordinates:[["@@̸Ľ˭ͫ̆ՅMįϢִĦĺϓ"],["@@ŔŋΧńʔH"],["@@ѤĽۙȟʶ̞"],["@@ƧƬǖêmɕ"],["@@͗§̐РS§ʓ"]],encodeOffsets:[[[655426,-304255]],[[671522,-301335]],[[684567,-282446]],[[681738,-281712]],[[681802,-280306]]]}},{type:"Feature",properties:{name:"Qatar",childNum:1},geometry:{type:"Polygon",coordinates:["@@ȳ¯ȗ·ƷBŧp÷´ŽȌóʖĞƎĐmଢݸನلϪԸ֗˃ࡍĸ੍ՑࡱϋƋ"],encodeOffsets:[[227328,-188875]]}},{type:"Feature",properties:{name:"Romania",childNum:1},geometry:{type:"Polygon",coordinates:["@@Ÿɱɰūڄŗ²Ƹɒ«޼ʨϸ̘ŉȔʛڡटକȕ˺ӤӃų࿩ᥑ݁æՃӒडö׍͠ዯΧᅙݫ㉟μMʬҊ̆՗Ψxʁоɖɼǂ¸ǌeĊJĂŭĸʥŬͭãйʵʏ¥˕˦ՍĲǍǲ̫ĈĀØlŮRǞjª¬˱ȬЀМ½°ੵ؞ɪقwnq¹£K÷ĲÛȪαʄŗ̖ŀÄԎúȊõЮʦ޾ĆᳰᩎᖲবѐƄʰǺŶOͦɃҤ`ቖơشҧஈкӠ¶ӤĀպÈɜǈƂŊƦȮ̰ÐϾâ٤ɻՐᆛؐ߷ݯ౹ᙳƺͫ"],encodeOffsets:[[182043,-132019]]}},{type:"Feature",properties:{name:"the Russia Federation",childNum:73},geometry:{type:"MultiPolygon",coordinates:[["@@ȑƺռΩȑ"],["@@ݨŷ൛ݫۙ׃ëЗȑҦཌක"],["@@෩ܹ՗ཽ౳݊ৠഄࠠĊΜ˚˓֖®৶Ԛ¼ͣӹĵ"],["@@ԅŉ੺ॢ௲кᅥ౑"],["@@ֿƵୠۮןշ"],["@@Х¡ю߆gݣ"],["@@ਖ਼׏ͅŤÑҔऴɲ࠰ࠨͱੁ"],["@@ӥǔм̈ɬ©ǁб"],["@@਴ẽЅዽˈঢ়খᴃᄒ⌿ഃഺ٩ƖಕʩେᡁÅݟೊᑗֆĩǴࢗ͛ݑ˳஌น͆કჳӕ஢ӄ໰ŵৈӊ॔׿࿲بᆶđᛞ͜ೞ्ଲďரѠ୘{௢ټΔޚŅЀৎܛࡴࡼʪ"],["@@ˍϳڷàॆ͔"],["@@Ղƃ৩ࠣӵҼһȃ݊ࣨܐɷ"],["@@ෘ҃তY˼пٻۑģƽX̧னRᎏî᳝ưೆਘ஌ǒ૲ٸńiયڑ۸ǳѐòԈ݂"],["@@^͗ሳഌ਺ ͟࡬ܳ"],["@@ɽĂϨʂƩ̓"],["@@̯ȿӸଆ༞ҤƐݥስك"],["@@ϓҟɇ϶לê"],["@@ثƘóж৤ŭ̓џ"],["@@ဉܱ๳ʻޟƐӘਦಮԎܞයܯ"],["@@߅ଂܺԼÌ࿽"],["@@དȊॅՊࢀʲቆ̕ɭگ"],["@@̥ҳ้[­͘ॱÒȋ॒୐͈ᆌ୳"],["@@˼ͱۏտࣕۼಪǶ"],["@@ҧȋଣʆീżʌǵ"],["@@ፌĖංʟఴڝᾙևથǔጛϋ҅زᓠॶ"],["@@೥Ìዒ۲ࣺͫ໥ё"],["@@൳̀ఐհƤ࠯"],["@@ܙɬȸ̘ಌĽީх"],["@@ࡣıɩǮ઎û"],["@@ᅆʇஅࢩၙ୍ֻ൓ߺᇥ༔ࡑ੷̹୕͘£ϓ౿˰ጧቯѠࡐӌदĒৡӔ̞ьᩓŭӧϲࠂॄಐƀੰҪ৆ݔЇ˜ྜˬɺ׬ᶪҜޮǯ"],["@@றŻςܞሰΎݖށᅕǭ"],["@@ፆܹƲݯὣǲ࿁͂༩ɏᛢଢ኶ä"],["@@఻®ټƆ؀ǳ"],["@@੫ǧ͓۶๚bęկ"],["@@ేہᏥ׀ږzӢвᓶͩ"],["@@۞HЅͻટƖ߈Ȟ"],["@@֏ϩԓĤ̶ζࢂƞœɍ"],["@@ᬾĪˌЇ┚ǳ̡ӃᆡϩᗍØ⎙ࢺ۪࡬ўҽ"],["@@ࡻ­ւ৶ݘ՛ѝѫ"],["@@౾ѹఎଊጸؓშ@᭨ݗᛓછำǊܿӶмࡀયµɷڝ༊ुୋͯࠍϤᳳ͓িʾ੻ۋႫ̀ᇿ஀ײ¾Ø࣌ݴvȱѬଲΨఎǾሀࠕ"],["@@֛ΕཱུȼȢǖደ{"],["@@ϣࠡЀࡼ["],["@@౳}Ⴂ̚ѭ˛"],["@@矵Ꮹ᛿ۋɔɱኧڅءȄ۟ҫࡳ̫؉ᇑӍщյڕƸňЕ຃ٵ੍ƹᖽΖළɱʓचሢў፰਎ໂǢݍ̎ᓬ݄࣓ϔԦƺݶɍऊ֨ದwഘܜჾі᬴զપÇࠄҾᱞķ᫤̈́㹂ව౾ǩВԏ᪵੅"],["@@Ⴟiᚐк؏Џ"],["@@׍ĐࣨȌ͙˛"],["@@᥋ਥຑʮᄡࠈ৛ý೴ŃА̯ˇǙዿϸ᪭ا᫆ӴضѿΩˋᛪЊŪ܋Ք̹܂ȑ͖͚̚ɳࢢ೧ՕƏپҽ¦͚౲࢟ȡ̵܆ܕ݁ࠑ⊥ذТ؁Uͪӛ⺧࿵ၛʋࡿۗսƖƚΛॗ͓rɿ῕ඛϿਿݑɪལஆᑣ·ბϓჿ௷́ˠͤଆᜣࡻɅ֯࢏ӤީλȘޕİ۳ցŋੳᑏቹ̳ࠥ࣌ߩӪԬ଴ΛࠛॹĒઅˬ΁ڠûɒ৵ջпе²ѹόޮ٬ܩëʱԣࡵϥٵ೥࡚မऱՇ႓Ê൉߁ёࣣюฯࣉʎိ֛݁ˢ̀Ρȟળݣ౹᠟ᅛȩċӟंϡᡐՙߚય⫖ƿ઒ޤᳲ၂೮ʌЂ̻۬௪¬ЦӲஶyᅜଠஔഴ⊞ᘚְ۰Ḋ৚͏ʒࠞ܈УˀٜᇶܠЌ஘ɍ϶ǎዻ־Ꮵ΅ԇ๏Ӵѕ̽˿ҫ̞ࢷĩᴟ᎝ଙʱΞࡾ଍Ƶࣈᐼ෽ϹܥҸᒩϽ୙Ķܽ˟ᣑᕫ෣ہಓ௕ˇڽྜǭϪׇൡÐࢋ́صͮٻu੏٥ঙɺҍˏ๻ÿПҪᖆ͂ᅗݲၝƫάƒፗӐଃ̻˖͇ߙİŁΡўWҳʝࣻΔว͙ቭфॷٙॡլⱛī௅̅⊏ᐵܩય⋇ቑ᎓ᇛ⿿ᦽӁ୪ͥቢĊʷᆧࢦ f߮੪ɨڷХখ΅௙ࣉපźஶߘǽگڇР՜ݘॢೖɽ߂ȊቸແഴڷЩةۅǌࠔև˯ֻࣝۢʹֽ࣡ڧࣝᆷƺྋΉϥĔ۷ߕ᜙ₓᾁ֝଱සླ‛Დֱʵ࿥ቑ᜽಑ᆋ֗कА࡛jÜ४ढ़ӥǲҨႭວवæʖ̗͵ӳ̭Ըȏє൜ϼҜ੤Đ༈ҷႸম̺ࢼގᕰړϤɦȞࠂ՘ʘ२෴ކᚺాऎΑیɸԮݩˌ჋أڝnउΟԙԱḑ_ѓۜȠܞӑא¨ׂܳæണ૚ࢿۯԎณë࢛Ըƾ࣮ӭ͂Pߠ۳࢜गᬤషເ૳ΰյƽᙿऌ㍏דክ૙ĩΉ୲Ʃä੃ັ૑ᓁᦵ;˳ɳӇ႑ӹ౟ݍᚵۜʓɎϿżˉêǙHѹſ˭ĻǓÉ˅^͑xŵǩĊԃьԏɆ࣓Ğ༝ױ˱ƣ˩ƯͿǣǵɻįǟ̡ĿɿĹɭУÈࢯʍഷǻȅ˽řࠇ̱ʡ½։ǤɗU˅ķഓɞơÐџĔ౻Ƿ¦ɕŪࠋּFڄֵĂׅRɵPǝĮȑŤǍʘїʺ˙э}̻࿧Ϛ᫕܍ࣿˑÐơ¸ɹኗࡴŐ̘ǋʜ¶ӸÛŶ~ʐÄĴ`ŢóĘʳÜ̧ĔӫļϽྑאſӛVЃfɛ¦ίʸ༯ռٿۻఅ܃߅૟Ȱ̃ÚҽĊĝŤÿƬ}ŌÃǅԩؽ٣ෳ؏̿yϷͼൃ¤ҳɔହțࠫÄ͏ʎʍXȕYĥųUñƞρ̚ÇݢᙡłʗϸǙzǋwśƱŽ×ŉ\\āČ˛ǦřƎŷ˳ͭߕeࠅϧĝŧƯLďcƙ{ҏĉсȳǗ¡ŝĽƕĉಕ˻धёíгǋɹĕȇŋ̣ĳéÿƙǅçɛÛï|¡ÂùîąŁǯϱĎउûƳƽĻ˫ȡćĳñť¹ʝKĿRՉīɩĹȉr஋੪ɆӒ୍ثሳʼʷা౑ЊӇப੿ӰౣɧٙχᐭӞٗʖӾ݇ɮٳʗȑՍহϧ᪃㕪ᗙ၎˾Үaֲږᵥ߃ֵѭႣտ̻Υࣕ̐ਗϫs׈੔ݦਓȍ઱Єӯћʗ࡮ҏˍᆩdӂࢀγཊ௣͉ᖉԘႩԟʗЭ㘛߇ৗه௫İ㐭ও഍ǎ࠳ࢹਨɗڻǥȋΡࢪſڰԁᑱó߉ڑǰݹᗱࣙڐفዼܵۏਟਡχ੯ءϦ۽׷ڇȥ³˸ૅǔ޵ࠠਥØॻғ݉Ђળķጻ঍ᅇॢ֙੣ѫ଄౓ন༟CԻԜጛԽəϬڽǾ๹টၹҡ։Ա࿉՗̫໕कƽࡑ୔׋­ဩ๕ҝ݇̂ӝ५ޗЧࢡۈЕöહѶƘഄɅࣶᐧࡋƱ͹௺׹೧֗¯щӉĐᎭ޹˯Ɋ~ͯ׿ۧሑཹॲࣇÙ௭آ݆গ቙໐ᦟᐃ෿ुȆࡏ஌ةǬϷɜ੟Ӗ\\àδՠ¾˙Ďҭa˵͘ǥªʓĄƅÐ¹uʵ̃ķȺćÈ˭Еĵաșɛ±ţÝ˙Zūº¾ÀĞźĸJİ̇ƌŏĈࢣ͔ƛƊ^ĦѕǪខŎȽŸǥŶյĢˁĬʝƒ˭ŦୟªǳƳ͕ᅝᆠৱ̎ҵزݝSϧئ੕Вސ̴ϽȀΒŮײ΋মɼĄоਊঐȺļ¦ʧΔΨٔWऱۼœԦ໾ÖGͮ፼پÛκΝR˩ɍߟËՊʠ౯ϟފୌ಼պᇎð۪ࡢ]ؖΗňӺӜҀӥжઘհɺܰا[টَևéଟԚ֍̑ࠇસᆻҍ࠽Ѿ૙ėĐຢΑΨ၃ŚʀैږɨޑైᦽċֹࣙڱŔhÈɄĤõºoÖʞӲȤԖkĔšƚĿǈÉŲ­ÔΌ˚Įĺî~ޤȩֺࡢВˆĤƲźjǛ܀ν˺ГΗYŕ¤˪˨ĮƦVä¡ÊˍŬΩȐĻɊ·ΰǱĔ̐ˊZ¾ɝł˗ȞX¼ĚŬŔŦҌ̤¨ĊnŐÛȔÙŮ~Ú´ÀʂƮɆϮ҉ɌֳВϹoȩpűǃĉȋÇʡ§ϏƫĝĜµƆɨΊᎡʾǗȜʢˀ²Ů¦ǄˤþǵѪĆȆàåΥ³߼ޮĊŢĺÀυʨ®Ǭ×ɫV˖̶҈̒֒ǆħ޸׾౐ᔌೄűʖӦږфȥ֬Κߺ±ئϤ჌˯ȼȈ֛Ք౧UׇЬǆڰཛ́ゞᗆ⡘ᐮఞࣜȿܢ๿୺ජٖ̂ВՅհֆܚֱҖۤЄʣɶߜࡂࢼɪ¥ᤘᴐ჌ԯࢢ঻˺żڞ஄޾կƞ௮˂ౠؤໄ̞՞Ҕ੬ɓҢպઊȗਈʨɾ͐ฐԯϥˏ௟Ǌpѧ૲oɗɯঐƌளߩවզ⑀˅ᤎ૥݌ݫ࿸ٍҊŤȶ֌܋ࣘ˅§ૻࣅ࡫ᬗ୷ᵯԫ㎟ܸ॓Ӗ݁ƋĸɦᑽќgծᅷǖᒈྑܹʧఠɹڐؑũЩ౑࠯ɻᒝಬɻפݕᅀщஞ̦Ϟ͆ŠԢ೻Ȣ֥ފłтৎԺ⯰೯ࡆѢݘlС౬Єֈ╊୴ዦࢲᄌɷռρϻ˷ר¦̙̚ዊᐎ݋܀ᘎᖌय़ल૲˧ᾢƹՔනᘍȱေޕࣆԙǮڅ৐ȯᝒϾ౬ശኈɔǌӒۼË㭢๾ӆл߄̒Ե˦ἔࢂూQඉȏп੍็ϕᗾÒגȷ໘ࠎౌǺሆɅᛰݨᄼʺɐ׳ࢻڃਚȹ޲ߎ૮¼୆־חഠၪֶ㕢ۧऌٝ₠࠷ᇜఫᓸැઅΧମ֩ɺࢯʔӗ̻ᕲᦀųɜࠕ§ɾך⏘ഒ⟨ᯰĞɑṨÜᎠӻಯືጓఓࢾેᤍ⫁೎ટୡڧסओᶹၭोĻzدၱλսȆܸ͊ࠟƃ׏ΦధяěłҵሸՉኜघʥࡖȒтԠᴚংॾबဢ߂Xา܈݌ᵠԈٮNࢴକࡅ໹ࣄұᓌ¨ᙃθ׮ঘۀƸ®෸ᦧݔਜ਼R౥ͥ჋Ş˾๐ᘶ჈௓኶༊ࣆᩘڤӒ๸۰ĞΊఛఱݯٯ൧᤾ӍఌĚਸ਼Տ૾Tͬƴࣟkη؎ᮉ٘˹ߢ࿼˜൴ѯ॔ʸɗ̺१­ņː༴дᒞRᏎٷஎ࠳፾ňݢɋᐗࣳো๙ŋҏᄘపŁ࠹ৣ߻׬˧ǲЪ๠؀ҹఠೈಞϷѐभŔ؁ހፑԀ֬਒Џق۔ݰ佸΄Ṧψជ৏தݑࣻ޴ᗈ΢ৢѪోॼࣳǊࡎܾ̚˧زĒᔏؾࣾϦظ̙ߌɎ৪Հ࡝Ūʀɬ૊ě⻊ਖ䎆ܜᎯOࡊц⚎ĒߎŭऑыཤʪƓʭ✘޼ၒӍةԧڒ̎ǀҢඑޘ⇖Ĵஸȕ਍˂ՠॾὐ୊ᣢӼḆݿ⃃٥⯮ƯܾʋᐯओᖞvॶӘ㏴ŗਤңͯɡୌRǹ݉ழѺʌ߫Հâݝ߅ჃהڼֿԼĖܠΫၙউ㠭༃Ǎ΁ẅ੟ၙǍ౉ٯࢫ»ᗁஏ਺Ƅ཰ڴᗲøᴆ۴ࣶዺ׀Ꭱ୦ؔഄǩɂʬܚÅ؉ƅȴ̿ఀˇ୚ĊɌӚ̼ŅԄ׭ׁсԬʩॽ଻ࠬѭەϦ໎ூ֙Τ“Ѷ㪤ρޗǏǗӥᔨױ⨲əࢮÎȃ˖ढǍखՔ[੖ሼȎႸڹரɦѪӋठӈ௪Ƨᗖࠝॹӟܾ»ΠϏ໯ϟຒɓƎ؇ࢣșቅߎt˭႘ఇעΰϴЇ଍ǝไௗ೦ԕоƼЮә጖૜ಶ່ႨહᆄŃ཮Ԅड़rỼછƷȾࢀɚۭƀ،ӆদò݊Э඘ŀЗࢢٗƪໂ՞ᆃ×ϊ࡚ό᥶Ȱѣںᔸϟ䜎دȷᤁòભˡɔƩ⭺ɲಯ޷ɜњ࠷Ȣwُకȴԧིʦᓆ୒♎λआ׹Ⴧɟƥɽᰪϡࣗځ༪ΈૐȽघם׭ğനͥ䟮׆ធũᜰ֋ె੿ѩঝᔰם̺କԼʷރ঵ఞࠒƇੜૐԲሔɲ㾺ӕᆸԠस৻቞їٌٿၤĖޠӰएஸ޵ĬѼ઀㆚ًʢ˞⮌ŗ㴤෋ภأϴĩᅂзіү଼˕ưΧѼǐୡԚၒ؇ąƿ⩖௡ĸܕٸǴ͠ȡɆྷࠦȑ̚џԬ̀ƼձѾҠ॓ѴŞ۠भིͦŨৢǝʣʭǴʱ®΄౨ğॵʦᙲ̕ᨦ೉ŷɃհĶǪ˧ࢸÏw͑౅̗Ňձູцݠڡຑმв଴ԍÇЕٷÓܸĩɌΙ໫ԫքÑőαੂӕٙGλʬaѹ׋ďɍ֔ڿϹჽ߆ཱྀȎ१Ҳځ਎ሣ͊ޏʗ჏k௑லτ§ʞࢄջӑשтʱӅڳ´čهঔ࡯ਲ਼৓"],["@@ыǍႅǾᒒo"],["@@ఁफᥔ۰ߔ׻ઞƍ¥܋૕Ω₫ͽᦕXᶣә⿒ᶊঞɃ҆ІרЏ"],["@@ዂͭ༗mϩΜ"],["@@ኧʹѨ⚚˯ᒇӫ"],["@@ᏩĈ࿺ʬаͳ"],["@@஝Þೄ̜ťι"],["@@՟ࢷቨٶᏘ؛୉৫ઝå઎ݏ᨝ϳ⨝࣓ۜķࢗ޴ᄏȼ◂ഒϺƩ⛶˸"],["@@׹ȎۼǆŁΓ"],["@@޷سሥज֬Ⴢt"],["@@ᇝØ౴шತ̙ܹȅ"],["@@भþΔݨẦ͵งÅୃѩ"],["@@ॹTд̀ֆ̓"],["@@๸ŃÍϭఏ̐‧܉˞ྗĤⵈժ"],["@@كӣᐳѻઓǞ୉Ɨެࡶ⠪¤"],["@@ၦ՛㒽ݝȭёᕣǹ٦Μྏųˉ˂ᤜԠ቎ਈۂኬʂ"],["@@༙ăҞͨၖwיɫ"],["@@ཱŊហɼ࡭΅"],["@@ᡔˁ௄խᴯ˳ၭʚڻ̖၂մ"],["@@Nҹ஝ƫଓ֠ཞɪ݆ǣ"],["@@उ΄⥒ޖѨׯ⒯ԩ"],["@@ڟǥዝʆ᤾ß"],["@@გۧမԛĲח㰏כᲝް⃶ॸભw͔Ȳ⬬ࣀਤt࢘ҝ"],["@@ࢼ˷డœإѹἣ¨ı˜⥢Ղ"],["@@ᘓƾᢰj˛ǧ"],["@@~ʨᇠÐċɇᅑį"]],encodeOffsets:[[[483122,-136674]],[[481852,-134618]],[[487960,-132381]],[[490727,-131496]],[[496582,-128072]],[[503570,-121485]],[[506293,-118784]],[[507477,-117814]],[[473781,-107622]],[[460206,-105695]],[[462066,-105716]],[[177283,-105249]],[[532237,-106408]],[[492933,-95007]],[[524232,-96140]],[[228396,-78194]],[[308934,-74593]],[[268490,-67273]],[[517223,-68050]],[[534319,-66196]],[[292505,-65229]],[[278399,-61328]],[[465127,-60939]],[[557266,-60857]],[[334024,-58801]],[[338717,-57627]],[[328463,-57213]],[[427292,-56623]],[[287472,-56027]],[[320204,-56609]],[[474777,-54424]],[[349406,-53945]],[[472280,-54142]],[[413485,-53048]],[[357335,-51464]],[[348528,-50269]],[[484761,-50401]],[[461895,-50295]],[[470637,-49151]],[[379891,-47926]],[[413496,-46991]],[[489754,-46885]],[[319751,-48037]],[[380366,-45884]],[[366601,-45442]],[[560707,-78507]],[[405181,-42872]],[[396809,-39808]],[[344099,-38725]],[[377006,-38632]],[[295287,-37926]],[[311797,-37894]],[[387703,-37342]],[[292967,-37571]],[[307692,-36816]],[[300177,-37268]],[[308803,-37437]],[[302774,-36484]],[[290201,-35445]],[[319056,-35496]],[[295996,-35244]],[[354152,-35460]],[[317358,-35181]],[[305266,-34729]],[[312683,-34932]],[[321064,-35864]],[[377343,-34660]],[[386923,-34839]],[[312577,-33556]],[[324092,-33383]],[[314025,-33095]]]}},{type:"Feature",properties:{name:"Rwanda",childNum:1},geometry:{type:"Polygon",coordinates:["@@óaŅĒǣˬΥǰßŜƑŘɷǊಝ«˙ÇǍƥõŅlŻwƕ»ęS͝Ȝ٫ɵуǦūࡇϕǫۍٻӊȏ˧˙Ȓ·Հ֜Ҡž੠ࡆݔֈŜʂ̛क़ࢬŚżOĚG"],encodeOffsets:[[165584,-258911]]}},{type:"Feature",properties:{name:"Saudi Arabia",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ǾȠǀԵ݅ҮȊԄȾٛ"],["@@ኂʅ͒৹၎vՌฉĩϧࢄԯǁūАֹු୉̣̰ࠡȷүˉzޤᘓȜ˟ĨɁƠǵȲѡø³ŨoƸAȘ¸ȴ°Ɣĩǌb̡ԧպŧiҫა᢫㓄ۡȲȂߊ༵᎙⫿䇳ᖍ㼩कᎥ्๻བྷϵݫۯυυfҿۦᥕĆࠟˮ᩽ɱ੍іկΗɫງࢻ܃ࡕᛨઓড়छᄤܟ᜴ഃଠ݉˨߳ರ̃ݪȊҴɧߺаཤࣙ᠜৩੘޻ʄבஂʒ̼ūݎ᷇㪺ॗɊʏƧ೮᮸ᚴ΃ժΎӨ̺ͲϦ͚φźüྤ̀ÆȮ̖ʴϘ¬ ¾ҲɄ˲Ÿࠥ଒فިʗ̨נƠٔƼෆΐ؈ƪഖͤĈϤˀҰó۬ŋڰŃ܂űɊķᵄᆓ᷈᩻ჰื↶̅Єò"]],encodeOffsets:[[[199445,-210402]],[[216855,-176630]]]}},{type:"Feature",properties:{name:"Sudan",childNum:1},geometry:{type:"Polygon",coordinates:["@@sࣽ֨੯ƹɉƠÕ˙ǛኙɄ᯵ᗾᆟҭЛçķÓùå÷ƕĽÇ­íઅ˳֏̙ġăǫГŁĿίYǷɹ®ŝM£ďč·YƧʝϿܿ৕ᐧιԧDїDҕV੷௅‷ਧ̕ඕᅳֻᒇϗˇу̜ճӹƱ਷ً࿉ғĴʺߔñܐ࿗ైኔЎെ੝`UӋ๙O՘ڑæൻᯁᮽ੕Ʊཝ௢उӅ·ڏଁϷ΅׷ᄧNͧؒᅝॉ́໛໚ģՎปǡͣąܗ୏܁ጫࢿӵ೿ʹŅڞϢԐƆઈ༉ឞɠॐۓՂÚऀɥǦƍೢव ȭʦৼ஼ƽ੆੄߸ʡ࢈ێϪۦঞŀࠨђϜኜ¨ர傴Ǭ૤ᕎdݘ⪺䁤A䁦AόhϦ҄ǚũǟͽ牞F"],encodeOffsets:[[187605,-195996]]}},{type:"Feature",properties:{name:"S. Sudan",childNum:1},geometry:{type:"Polygon",coordinates:["@@ơᏇ˫ɩ຋aۭவጂ࢏ً͞൨ోՌᅗ؀ࠋяЙӋҍցԽ׵֭ҷҋҩѻգՁЉϳǷųધ˪๱ϵқз݉ن֩˩࢙ʎܙڇǧ̘ϡ@ǋӂە͞૟തңÈ֟ӯૅʨہЙԽÎࢣچࠓਦԻൖ൓ޠлࡺƔЖᙡᄢȩ̄ú݆֘ܳ௽Έˇ࠺ࣀӶ܂ጬܘ୐ͤĆผǢĤՍໜ໙ॊ̂ᅞͨؑᄨMΆ׸ଂϸΈڐऊӆཞ௡੖Ʋᯂᮾåർ՗ڒ๚PVӌਫ਼_Ѝ൅ና࿘ేò܏ʹߓҔĳ"],encodeOffsets:[[176182,-230190]]}},{type:"Feature",properties:{name:"Senegal",childNum:1},geometry:{type:"Polygon",coordinates:["@@@޷Ϧ֟zݍ˱թЌ܁ҔŦиढ़ƯளᗕƟଁј̡ĥQ͐෗ĜݝFمHሱRћȯұə؃ŝڧªəЩǝʇ§̓űsÍϦܶȈ̻ʂε̅Ĳੴ˰ȬᆆLĪϘநǂΖάቺܓদϪ̤̋९ęื۔ܿȑɭ̿ᚍFΝ܆ϔ̤ЋćدිηȢʛǥǓɴआώ߮࠮ފঢŞɸļǮŚЖŶʊֶܶਈĻᅢі๼ɍߒॵʲÄܰʩԂડښণ٠թʴǳ"],encodeOffsets:[[51864,-215603]]}},{type:"Feature",properties:{name:"Singapore",childNum:1},geometry:{type:"Polygon",coordinates:["@@܏_ϖ˘ͺʷ"],encodeOffsets:[[365965,-252368]]}},{type:"Feature",properties:{name:"S. Geo. and S. Sandw. Is.",childNum:1},geometry:{type:"Polygon",coordinates:["@@݂ħƍΉڒ~ɒଫݝɳ݉୾௝ϪɨˬݝúფŹ"],encodeOffsets:[[915860,-403481]]}},{type:"Feature",properties:{name:"Saint Helena",childNum:1},geometry:{type:"Polygon",coordinates:["@@ȣaƂɖâȳ"],encodeOffsets:[[70201,-299639]]}},{type:"Feature",properties:{name:"Solomon Is.",childNum:16},geometry:{type:"MultiPolygon",coordinates:[["@@̓ৣܔಸڵ"],["@@ԡȽʣƬԺˢʌɏ"],["@@ࢆƫנࠝಭʄփʹŋҞїĀ˚ࣰХ"],["@@Z͑͋ȢīܾО؍"],["@@ӪΫࡨਐথϝƧስτуپΦ˨Ē"],["@@Ւ͕کƆƘȐ"],["@@ͻňɀȀɘĉěȽ"],["@@ϟŝÕϤѬÞJͣ"],["@@Ͻͪϐ͒nٻ"],["@@վڙŧлӊηҘต೏ฌŭौ˛ό΢ƀ"],["@@̚ןǱʁԧώÝϾ֯ƙȍǀؖڔ؆أ"],["@@Ӈɀƨ͞βǏύ"],["@@ϽӖŒ̚քͫ˗҃"],["@@ᐓ૆એ૒ؤƅ༊ोࣸڙþҫ"],["@@γlƐʺɤ˥"],["@@Ĺɇݷɬฃ໚ಈ֧δ׷ٺϝ"]],encodeOffsets:[[[520246,-288182]],[[535371,-285345]],[[523371,-284334]],[[522931,-282257]],[[518047,-281294]],[[519188,-280538]],[[516521,-280887]],[[513586,-279688]],[[511629,-279768]],[[520780,-278679]],[[512653,-278483]],[[511044,-278117]],[[509728,-277612]],[[518408,-279280]],[[507426,-275359]],[[511909,-275995]]]}},{type:"Feature",properties:{name:"Sierra Leone",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@क̜ނƪɚũÅ͛"],["@@ƬɋgÙǗ̳BÓ¦ʒ»Ȁࠩ҇ٝęƣÂĀnńyĺUƢľʢˈĂǞw]éĝ͍ū˝ӯʽɧۣփӷःہחܳᑕ੺öࠒӭċ͗ɲǱܴеƼɽӔмYҮҞٵşI׬ͶĐӡϔӞ°̨ѠӈʚԤݐͪўن»ժ˂෌Rƨ¥ªŝˊ̵֒࣋"]],encodeOffsets:[[[48936,-235714]],[[54194,-230398]]]}},{type:"Feature",properties:{name:"El Salvador",childNum:1},geometry:{type:"Polygon",coordinates:["@@ռĹค࢕vƃ®œøOĎÄ˨ưˌĴϢʋɐȪ[ǜŉ¨ÛăٵʩށၷɦФɓᱱࡄۋЂàϒਚݰĥͬҨÎ"],encodeOffsets:[[818474,-216675]]}},{type:"Feature",properties:{name:"St. Pierre and Miquelon",childNum:1},geometry:{type:"Polygon",coordinates:["@@ɛ§˵شԒ׋"],encodeOffsets:[[880244,-128232]]}},{type:"Feature",properties:{name:"São Tomé and Principe",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@̽ũŗΰӬІƖέȫ˝"],["@@ȹĂȒɊh̋"]],encodeOffsets:[[[100514,-255670]],[[102627,-251723]]]}},{type:"Feature",properties:{name:"Suriname",childNum:1},geometry:{type:"Polygon",coordinates:["@@èЙœɇˉϷĝĈ͓ˇ_ŷſɷ̵֟̉˃ñCąe{qĹFąðÑǾ·přŀĩŖ}ŌÓWƙĳĭqǑnɑŅŹ{ěŅޕûԥʰÿ½ÅɹÃÛĥ½ĝģgěƠ¹ŮǫƄƗńů[ƗcȥȧĵਥɠԁɄଧᆨ˗୪ޑॵདྷа౒ǧ΀ѸϢࡐàʦϼʵ̺ʦӌ҆সဂʉުз»ѠִȼᆖNᄐЙsہࠋ๯ζჭݔो"],encodeOffsets:[[916795,-246099]]}},{type:"Feature",properties:{name:"Slovakia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ïğѯΝϭпџʿȋιࠑƯѝӒ፝Ë֓ԭऽͫףǢዑӿΩӉ፛ਃՆǻࡠКӄيը೰łࡶІήј࿸֦Ւʛ߼ѶѴԍɇϑת½ࡆҘ८ǁլɶତaְҏߚ˻Ŵo"],encodeOffsets:[[171944,-122138]]}},{type:"Feature",properties:{name:"Slovenia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ǃÆɁx׳Ϳగһáísç˫Ǜ˕ࣧ̃ĖǋɥǵsďĕSÿɫË˅rˡŌΧµȹȾ£ǲ¥`íÛūďʣȍƝÃ੿šաȤلǞÎȞʁʠÌрȝ_PºªäǴŐȈİÚÆb¨ЗĂØɾѰɔȊöĺĶź࿤ˑऊҤᗼǈҸдϴ_Ūߝ"],encodeOffsets:[[153582,-129156]]}},{type:"Feature",properties:{name:"Sweden",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ʭĽІܮᗘ჊ᜯᚹ"],["@@۟̃ɵۏᒽ੭ӴθΌ੖ଡ଼ؖ࠲ȐʆǱ"],["@@ɏŹʬʼδ¥Џě"],["@@ᐿƽે˴ܧףྫщǑ͟˫Ų_ѕഥ܁ƶޛᜳ೯㈙໱ࡣПŶɥܹʃΕɔȵٙձɕ߯`M؟੻ࠏŅПқĚΧͫૃዣռχټŪࣺృǇ׹᡹ࢹ௚þृّỡ৳ໍச͑ءŽಧᐥ၁ᄛᆗಳ᎝Ĉɧ˽ޫų֣͑Ʊڽԙ̋ᢙظݮˉ௎جÏ±Єжˬφԯ٦ȝᨔ̀Ӻ͇Þܜ൴ɲȪ̀ģ˵δࡘࠀɪڲᚸଶܴޖ̄ంࢺŸߢټ߿ࠚ஺ొરᖰࡢڼሔࣰࣜǚ೼ƋآΦϠٰ؇˂ᭈ྘ྖᇤᐠЄɜЪᮢౌǴࠦᑐੀඔҴ࠮̵ᇈೖ᧬ѽ࿎ાĥϬॢ༊ିಘ΃ۢऍ௱ેҎċۓٿĞ܋ຉణİে"]],encodeOffsets:[[[167435,-102449]],[[176136,-98233]],[[176473,-97997]],[[202787,-76494]]]}},{type:"Feature",properties:{name:"Swaziland",childNum:1},geometry:{type:"Polygon",coordinates:["@@˚Ƌöיʞ՗ǈ܍ȵÚ×aN͋¾ӉĘ̑ҷU׿ґŸԱ˰λѰǭˢǟf²Ɨ޺єॺȨ̬ʌȠİòSீӻÂ~"],encodeOffsets:[[176338,-326807]]}},{type:"Feature",properties:{name:"Seychelles",childNum:1},geometry:{type:"Polygon",coordinates:["@@\\ȹοЂǄŔȠ̛"],encodeOffsets:[[234026,-268801]]}},{type:"Feature",properties:{name:"Syria",childNum:1},geometry:{type:"Polygon",coordinates:["@@ໍଥࡍȁ͵̻̋ပࡇዑٕݽ㍇ᛅⷹᚻओƼ੷ެϺӒǮઆѺΆUϒࢂȘƑȖ࠮ڎ»ӀͳƮȾΠࣃÛ͂ᄞȱ͸ͨتƼưƾčǭÎNԴϞǎϒ׶ƘàķѢºŊɰѨŚǢĄÆʘsͪûÎűƚō޾sȼĀҦƤњŖڲʈ઼ұ̜HшՒF͒°мĆݼȞ࿠؊ᢦƀАŎˬȂĄpĒ¦_àĿ~˓"],encodeOffsets:[[210955,-154774]]}},{type:"Feature",properties:{name:"Turks and Caicos Is.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ѲſНûȼ"],encodeOffsets:[[861434,-196393]]}},{type:"Feature",properties:{name:"Chad",childNum:1},geometry:{type:"Polygon",coordinates:["@@ய傳ኛ§ёϛĿࠧۥঝۍϩʢࢇ੃߷ƾ੅৻஻ȮʥशƎೡɦǥÙࣿ۔ՁɟॏߡǢျ߇˃߅ᔏጕ৙د᷿уǧɯҶЕವുᏉƓጻॷө۴ЉПॳϿ੡Șע࡛Ⴄ࿡ฆ۷દ٪٤ṊÚީ޴ˁٴƅਪ˘࿘χ฾ܹ୔ҷŮГFʝЖ؏ূŋ४Óվ᝶ᵰᬌᣰྶ䀖ڨবڷࢀƄӶۻౢȀᾢ᜶৒佒Ⰿ䨬⢕"],encodeOffsets:[[151842,-202816]]}},{type:"Feature",properties:{name:"Togo",childNum:1},geometry:{type:"Polygon",coordinates:["@@ѱഫஂࢍG෉рۥ˵⯽ȿYҘ༟Υǧফ˷ॡ߾йࢾ͆૶˓ўպᅸػࣞˠȸȆ௨ٕƢ˖̀ǕĞЀชय़ࡠ̈ުƱɄௐΫࣾĐ"],encodeOffsets:[[86326,-226012]]}},{type:"Feature",properties:{name:"Thailand",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ʷ˯Ą࢒ʚǷåЩ"],["@@ʥρ¥̲̌Ð"],["@@˥k£Ϧ͊ι"],["@@{ƁÀв̀дү͟ऻɬґ໴[\\ƏñΡƮ޹׳஧HýǰˏG͓ןధĲħ͖ƏƖªֲӮ͘ɀrl˰Ƹɂ͐ĸìŬ£ǐĀȰȢƶĞĺeќΛɰƏȴ©Ŷ̍ĨªbࡒҚĶʖƎȈŠĎƜ¨ ÐÛúZüĐþǮˊ[̴āΚƧɸ³ŚÂȺɯ̚ԛʺϭɚʽڒՍƲίƯԁÅћWՋƆНӂۃɖƫЊůǎœΙӘ̅ī[ǁÓɓ̛ԯ]˿ĔȕpʑÓȧă֡ÃƉūŽǿūɟŉƋµwĵŭţţٓ҆⇇ɇڑѫࡧ௽һÉ˺ጹռձéݫШࡩܕ૔ŷȿ໅ஒᐡɊზ޿̦ࠛȇ֧գȮѵ̷ߓZ୑ጡ➱ŲᚹಠÀ̜༧̢έĆǶʔ՛ՠᙳɹŦş׎ɍxµԂɡϽ֦ःಾܷ੸X೔ඇԿडѫĽˏ̀ু؅̜̃ǶੌБPʳӒ࣫Ȫǹ̖͡աऑ঄àԤοʦŀ̪ՏȥւЧɜ¹ϞֻϪǳѲ˗Ȟεωʭņŉௌ૶Ẑƾੈ৆সৈຨГ႖׉ઊĤໞண෶ݩൠ@ԞࠪμƼ໎Ԫ̆ǮϦŋƦЉțб฀ືᇂįૂէԚ࠰ǀ˨ዔӔ٬ުƉ௘ȊЄܴࣼŔӊְċϸɜƖqļăʪƕ"]],encodeOffsets:[[[351061,-234444]],[[355709,-229850]],[[362327,-223296]],[[357219,-200579]]]}},{type:"Feature",properties:{name:"Tajikistan",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ʩƟŕǊI¬AƔȪ}"],["@@ࢻס௱ԚଫНWãūǹūČő¶ďŕŝȭڱᣢƖզћ່ҴÐ³hÙ¥ĉyďҢů͗׮ǔèġŎŏưšक़ЎতÃ˘ɸĐŬäȂ¢ͶaśƓϑ̠׍˳ʯŒօ҂ƫמˎੂѿȥኍϰәտ·xýİǽǄƋČٵÄȕQƑĩĩĩथɕȫj¯ºɚƲÈļWĪŽŁʥÌɉsϷť݃̿ؕՕᗓݓɡͪďلڠხÃ¾ƏGəËƯWăɲ֞ķ̪੉ʸ࠽ޥԉͷ¹đÚˑÃɧǡǽǑēˣºͥĮщ̥ğǿƩčȝfʍëȅƻŻũóēmǁŐϿώıpÑ_ç}§¯íħŧvţsڅ˓ȿŋÅśÕ½ĵnͿǇɣºɛȶŏʎŞǌƨժ๦ྮѥט̈́৬࣏Ʈֻ̨Ȕ˒֎۬Β໦ȍԼڰԸƶÝ͌ϒĘۅǤ൨ê¢ࠚф҈ۤʙ࿴ࡎ׈ۭঁݱԒХږĎ"]],encodeOffsets:[[[286295,-147281]],[[287283,-146235]]]}},{type:"Feature",properties:{name:"Turkmenistan",childNum:1},geometry:{type:"Polygon",coordinates:["@@ŉUඛԊ׹܇գWңo̓ı͇ƏŅǋسଁƑ˳ǁș߃їݭ̧࿳ωPϕΗԳǷą̃ƅУɁࡧȍωҴগŠԃѴǻuĮֈǇʔɰӎzৢဥYџܮହٮș־ॉϰ஗¡ᄥێɁծཝôҋϙኁß୯ؑ׭ۍს˳ߠ≸ȣ׌۹̌яǛτޒݘƏȷ˘Ǩՠ৥Ǭ}Ӌ͑ضÌޘ࠶෠̐ӉরcѦϝ઼Ĵ͂ߖĒǆѸৡৎʁഀുÁӛˁӵסʕଥϳಜහࠔᛂрಾࠣࠀ๿୔ÌᓚſˆȚsଊ࡚ײଦƨوژ࠘сӏ݀ۺþ̬ʸ೴৳ెʟɵՓѾϡ́īǵ߭ڠЉ࿬ïטȘݰСؘ᠓ᘌᆝ੪Чᜤ࿽ގ^௴ֻˏ҉ţзå̿ʗ"],encodeOffsets:[[274074,-154119]]}},{type:"Feature",properties:{name:"Timor-Leste",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@࣢ͺΟՏցȖ"],["@@ʡښ̸ÂľͤӁ{ʞ֚ڈҐǊ᪊ʺޖ͐݊ʑࡅٱ‡ఱݥՇ"],["@@ƝϩǋĈ̢̪"]],encodeOffsets:[[[420887,-281482]],[[423701,-281946]],[[425223,-278204]]]}},{type:"Feature",properties:{name:"Tonga",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ȌĜȇ̝ѫΘѨƕ"],["@@ʯEɲǘ~Ǒ"]],encodeOffsets:[[[585220,-313746]],[[588783,-306844]]]}},{type:"Feature",properties:{name:"Trinidad and Tobago",childNum:1},geometry:{type:"Polygon",coordinates:["@@ጛƣࡾҀ}ݤЩʲྒ˚əϡǎன"],encodeOffsets:[[896641,-228355]]}},{type:"Feature",properties:{name:"Tunisia",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ӷƺͲըƣȩɩ"],["@@ϧǉϤ̴DƩ"],["@@ӫӷɍ຿ҵғύ΋ĿďࣹΝĚᢢzֈ´ড়௛ੌůନэʼΝ௢ɐߺᏎฮʞպƸɾˈˌǮড়ؚིȁ͸ઢ۶ɴ᧬ࣘ̔ëƹϳȐˈ٬Ņűोɐō࿘ނDѹኟ༥١֧ۤ§ࢡ೯హືࢻ̱ԅ͎ܽ࡞ĕĵϷגʢѐ܃ǃƃ؄ƯuÏȵыÙƳǽ֭ĤˁřŃƅεƳҷȣУȉҁȵǁƃЭȳгџҵŕ"]],encodeOffsets:[[[126052,-164012]],[[127787,-161197]],[[122575,-169568]]]}},{type:"Feature",properties:{name:"Turkey",childNum:3},geometry:{type:"MultiPolygon",coordinates:[["@@ٕA؊ɮɫ"],["@@oŇͶࢃәՍߕౌȓߚݭêšЭ˄ٝޡ߭ĳȂ௉ɡ৓̄śޕॕیѵȥ؛Ϩ͗ƭͥÑ¼ųnƇLԏʿȓŅœñó^Áƴľ͸QłÉôý¦ȽћȩͿT୑̼Ωħͅŀýu̍ʥҙˑɕ­}˔ßŀ¥`đăo˫ȁЏōᢥſ׷ȥਧУݻȝлą͑¯ՑEч̛G઻ҲڱʇљŕҥƣȻÿɵR̉rʽOƙŎÍŲͩüʗtăÅřǡɯѧ¹ŉôԁ׵ƗǍϑΛƧǗɵÍM̋ǮƽdèࢢਊްƑ֜ྥݥೕզ᢯ළᎁ͝ߝͦ՗޾఻ת็Ƣࠇೳၧʧࢳϒ˱࢖റɎܱЇɒʾൗĕౄˎٜѲᒋŝȀʹՆĊٹૈϢưȠ֦ᅕڜגࠂ˞ہખʺӝâƑԜڌІρǔȞΌɹЄٺּႽǿ¾ɪӨऺ๾ࣼமŮ̜̣ժp̐Ţǟɠ͞ìɤ¿Ʃˋᕸ¬Ρ͒Ѣɐጨ̂෇Њ̆و⧖ʩנӆᑊࣼ៰٢᭎Ƌبʆ˸ăǡ̧ʞεӐɃઆň҂ࣱ࡜ȦӐБ᣶׉ᚆТီ͗ኸڤ߼ζˎɀόĹƚéĖkʺƘૂů̒ˌÊ~¢lǐfƦcÔvǃǊËʈʭǌĹ§ā½NÛĐĉҎĭ"],["@@ୂºÌक༠ܫϩӷཱྀǌທʋܹٕୱщ໕ु̈՚൨ۦี¹ʶߖԚɠِݨθâ҂ҙʴƲȐͺÈƎƾŀŘĘנÔȴþŚîЊ¦ŦĪ˘̓"]],encodeOffsets:[[[170942,-146515]],[[216912,-143734]],[[176554,-141576]]]}},{type:"Feature",properties:{name:"Tanzania",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ʙĢ٦ٞЋܿ"],["@@ȔӇȥƩ۝ؾʎ৚Ң৥"],["@@ɏளɽǚ|নђ²"],["@@傔⯋Éয়⍖ᦹࠋᶛǸ׏༺༃Եૅ׿͊Ç¾ѽʣॵЄૡҞ֑̪ᅷႠਡেߙοȳᅱ֭੝սಓˆϩٗϷɁυā̷๋ࣛ͐ʻĂ±ƎəǊЅȂ˫J͍˿ơ¯͡ªՃlӹQíʲśƾˑŚзגĆ٢̱ంّଜށՈɃϹᔹךϗȐᇯ׸éʢ÷®ƹ¬͏cˡɴʻͺɩŀؑYʡƌǹǦƫȠ˱ؾ޹෸ʉֺʑψʥ˞ͱʤϙʞȷǚ́ӾţǶġ͆ñ͢lƆɒъnŢǈŻ͸ӭୂ¹Ȍ@ƚƬݸMń۴cԈͪॼฬ࡜ࡖΞ÷̎ǁjʃĦȧ ƇÄ¹¦yŔŀ̄őŠØȠоªÎĚTƖ¼żxņkƦöÈǎ¬˚ɞĽǢࣞŗɸśƒǯà˫ΦđǤbņôvZ΂~ƸĮƮW´O⃊@⃢@"]],encodeOffsets:[[[191264,-277761]],[[190441,-272843]],[[191315,-269383]],[[174838,-258733]]]}},{type:"Feature",properties:{name:"Uganda",childNum:1},geometry:{type:"Polygon",coordinates:["@@ęHŻPřॗࢫʁ̜ևś̘ῬӤ࣊vܒᱰᩞǋԎহвˮ࢚ȡѦΰࠒƧȀܚڈ࢚ʍ֪˪݊مҜи๲϶ન˩ǸŴЊϴդՂ͞ݏŢ§·ŵɠǳ˨ģĮĝPıĿџbŭĆѡশ໑ÁƑǜߓz҅O͇Х੟܉ҏǇϹ֛וө९ć᥏⃡@⃉@³PXƭƷĭǓ¥ǭhuY"],encodeOffsets:[[165584,-258911]]}},{type:"Feature",properties:{name:"Ukraine",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ʒō૽ɴ]ʴ࣊ϙ"],["@@ඡᇷ߭҃ň࿗̱ᒍୋ۪ϲή৕֕Åફאࡱཾ̪ߐƅ٫ܽଟƇݫȼ਷ڍഭǹกݝ࠙ϊڤ௘ንۆᮠ௔mͮϑđχ˄ཡȵୡѸԂͨࠫ˨྆Ǳָ̀ઑÒā౨ˑిۛǔʦүͩ೥ǣᅹ໙ഭ͓ʑਙȓʜ̗ŊϷ޻ʧɑ¬½ÁLĵڃŘɯŬŷɲƒøņШ¨ĂŪȾȾƾ˔żǸžĪȐˌĢˎǆŤǨŚŊʢ ǔ¸ƐĨüҐƶĄO^§Ł˭K֎Ǣ۞ɱĠtѐ Ƽĸ͙ʶĴȖŀɜFƀ£ľԝ̠Ö݂ӓѲѐਫ਼౧ФǥMಹݚމʃڭDĩԫȹýûϽá߳ՏչÇӣÿӟµஇйسҨቕƢң_ͥɄŵPڿ̽ÑζίÆЕؒʩIȌκѠˀϮрѰΞðĠ٠pȣȪˊࠖȆưঠپሼੰ֪òπ˒ĸȘPњːƚПঐƂӚ́ʸǚUƆƆēØbϦǐўʘˎȺᱨŴᇌ΃ΎʬfŸoƪ΋ޠÇÿɃѺʲംãŚ«Tȕæþ]ΜʆǼ¸ŒtĴÔŲÒĒJʌշჾɈϘҕҚǺXŖ¶ƢuƈZƢȦȺ̜˪˄ǼʔźϊȼӢX͆ŬҰNϘeڲœֺࣚᦾČޒేڕɧɿे၄řΒΧďມ૚Ę࠾ѽᆼҎࠈષ֎̒ଠԙֈêঠٍب\\ɹܯગկӦеѿӹӛΘŇ^ؕ۩ࡡᇍï಻չމୋ"]],encodeOffsets:[[[192495,-129963]],[[209125,-127542]]]}},{type:"Feature",properties:{name:"Uruguay",childNum:1},geometry:{type:"Polygon",coordinates:["@@¸״ƺɊͬʜȜȨÊƴńĔòǏܚ·ð¤ɦ˒ٲĊۚݿ؞ࢍƭ΋ț̆tۂӂÌhĆÉնࣙƸûƠĀ࠘ҥՄࠡࡊҋĘޟަݝʭȋ˿ʁʛȅɹŽģęěƓϕઓ˦ȗƇĉǛƿˋϓهԱȟ̉੓ٯᅍו໓Θഅ˫ྵ৐෯Ç߉ొل࿐ࠖ٦C̨êђcɄ˦ͲÒǎ»ƞtƦĢưƤiƔĊŘΊɶƌȊюĴǪҞӸŞȰüϐɖǮ"],encodeOffsets:[[894084,-340176]]}},{type:"Feature",properties:{name:"United States",childNum:76},geometry:{type:"MultiPolygon",coordinates:[["@@ڥƀһ຺Ҭעßג෼ؽा੃࿣ଫ"],["@@˓șΎϖiĘ̏"],["@@Ҟ¦ژѣ࣓ͻȋѮԋͦɼȐɺɩ"],["@@ૠƇ̳əॡŲƶɰ"],["@@Ϭͫਿ|Б׮ښʺЌշ"],["@@ԫ»л̒ѢМԆQ@ٟ"],["@@ȫcќŌɯħ"],["@@ΑсՀࢢǭҟ"],["@@߉๖Žࡄईᙙ"],["@@òǑࣹคࡈ౱"],["@@Աʖ˒èʠ̽"],["@@҅sޚɞ͓ȩ"],["@@ʕ³˸ʀ¡ȋ"],["@@ƜǑ͓\\ͫ͢Ԥǫ"],["@@ӯɆϤŌʋ"],["@@ݐřՓƇȻʢ"],["@@̛£̂Ŷ˭શ̼ܕsұ"],["@@ŭ͹ŲӠCƥ"],["@@ģƟ̸಼ᛥࣩေǍńҼ՚˺ዂǊ׺ΰΙϏ"],["@@֏°̨ʠʨ̏"],["@@څδɘ̒ǹ"],["@@ȫ¹øаŴε"],["@@ѝďôδΪˣ"],["@@ɋċ̊ʬȽ"],["@@ւԷॅڰŬ΢ʊȫ˛ƣ̪ǉ"],["@@̹̞̈́ˑႈ̡όǿתȲʀऎƍࠈ̑͘Ρƌ@ۆÊຎկ׎̢ЮdƸƹᯘ̟ኤۊ໰ի಼Ӄ಼Ӆᠢट෬՛ӎǵޞߙԜĵټZ۞धܠîо˙ěЇ၀ڟऐϣϠםᐜ⁽{ɏ]቙ࢋ࠷ǈ؛ழڧՌAߦӐߦӒࢴ͐໲ˊኜࡦДάȩǆŷĈǋȤխ̞߰Ɗ˦ŲɤĤծdᅾO࿂@ݼൔֆԐאںپѸҠÞȮZ㻄TŖنঠ©ՠ්ʅᅜިၒѬąҴӕಲ̞୮כดᴡ࣐˥׊॥ԚWٔއ˕ͽయɝʿїͅ˂ؿ`ü֏ڕǺŷԮϹಳՋȥǁȒ¥ɭǟʪŋӷХȄαȣӽ༗յ٠ҏٛٹݔɭજੋ࠲Ƹݟצ̄Ϩʽٍ͐ൃ͋ʯИ޵ԍȋָȧƟΟʮĤटᮩȿᄹடmںϕѦϢݧϣࡵܬ˻˼ോƑҞܿᕟ̕śѬྜྷޘڢ׀Ѣ֥ͽʇяࡲ௭࣐۷Óй҂ρए᭩˛ւʚఖх ͏࢔ΉȃׁΘŵμԒÊԹ̖ɻŇʬ͌хɢ̲̝̂ʔøˢդƌ̗þȜɢʏǖңחǹǼ³БΝĲо͋ɱƋࣖ༗ߏծüǕਾ৙റڸɗΥ΄ҩčÀ઄ʿ٩ǖ֋Ӑ\\᎐ਝGӥ͏dၝ઎ཎ૕תɓʪӑ́Þʥ࠙Բന৥ȳƏڕԪඡʊദΓ׸ءਘᅪឍຉሦбٌ߱࠙όĤ̗̅İƦǑٕɥՇӎɆ؇ඒǤص̖؀̀ʽŌѯٯٽ۽Ī˵̤˫ɧ܁Ɍೄֹϓطݍπ̒́քȋהňʊşķȽဵ՝ͷ˺ɂҙىեƫޛȻԤ¨ֻࡗ`߷џҁ੹ǩάä׽ःީ̅¼ô͋߱ϋק|Вȥƛ̡׋άϘԳ۫ݭǀȭˏ֏Ȇi͹Љʪȧ̍ƝîࢻୂᖹᘢᰝkӓӈࠥӑئǱޜģЉŕԺчАЊࡅᚎ⇇̾ᔁӻഝ೩ȳƋϨҊķёɲޙਢࡁ;ޫଌȊѴЁ̟ơࣺѿʥաҾࠋঠӨ߾ٙɦʀйʧƱ̪̃ŷᙲᭁᙠ࡭Њ։ēïΙታԗʫȜ¡ǚʼȍƩ˂࠳րâτ˅ˍ࿫Բ܎dͷȜዷ΅кȴĹ̸ΩëŽѿЉĵLɠ̭́ࠁ÷Ѥƌ۩ࣔěܻၫŊࡻƽӡБ౥ҔЋȱĻֺ̭ʝ࢘͊ȆũˍʗҒɳѐ͈ɚԏۗ҇წटȻзȻƠʫɟʭӈჅސͰ҅ɯԯҟьܩοయΠ̀Þͻրֳqॹؼҙ̩ǖʃλąᑇӼ൷ţǉ֬¢ٯჍڿѐμչ@sҎחŃҜݑࣩٗሇ੟о͌ऩċһʰ׬֛ԇʷʟǶԗڝőðȇɒöʋ܍ͫϰύǩދޥ̂ʎϡӆŏ׿Ҽઃࡨ୫ҫȩ⚭ಂਁാ͏઼᏿ნൿቶᄝ೬ᇕɂۡˇΡ஫ԉӥᕵࢄফٶ๝ᓢᰏᅨߗ܄Ⓕ|Ⱥ৕㪙[厡᥺ƠҪワПઃၾີक़ܟ¶ͫؔලɄକۆቭŤГʐ˳௎ұȜȕӘऽ֚ཝิĻୖ෣ߘϵݤ_ֲ२܏ࣷઆŸŀညň቏Ȧŏ܉঱ԢŇƫĖҮᏉསܹዀ෕ೲVᝲყᴘ̮ዺۗ⡮Տ෰࿞ƥ׍ˀౕnƭܰ̀ϭԚ׫ҒӴɤ؁ǈIǵॕཀߧۺσࣨညӃᒐɇάƠђַƹ͉űŪϥ࣫Ѥ´̋igǲޒঢđۋк܍΅Ǘȱ˄˫ѵۚ×մ׎ϝऌǬԒڡͮž̜ٽϦ͞ĜƧξǯʐˉśƀɻ̼᭒@↪@࢚@࢜@ჶ@ᥐ@甶@甶@甸@ᥐ@ჴ@࢜@ჶ@ӢGͷࡎڸơĻ΢ֱ"],["@@ōſǋʊ˚ŉ"],["@@ϋ¼ŪȴУ̶ચǡѓЃ"],["@@ੑ੺ԚgӃ"],["@@̋͘ি̏ъࡦʞˉ"],["@@ӃǖʂŔʂ˩"],["@@Ϳ®ߨ҄ҧӱ"],["@@રŷ࿡ç΁ǚࢴ"],["@@̿Dˆʴ͸Ž˽Ź"],["@@ఫዐѢ̍ɨԖɲˌšÿԱષɡ"],["@@ڇĸ݊Ȭạ̄"],["@@ಚέ঵ɡङжضǚ"],["@@ᐑଯڊࢬ؀ŲľӠऐjɅη"],["@@҈ɖ̸ƩսңОşࣳӳᆏ΁௎ԒǪϪѬśŌƬޟɤĎ˾܂ƜǄɻ"],["@@яŋʴϖĎ҂ʇ˻ß"],["@@£͖ͦȝ˱Ƈ"],["@@̵Ȍ͂ŜĶ"],["@@ˎσچ͟׉Ƭ᫷ۿȽцۖڠ౺͈ۜƵ"],["@@ǅɃÜϘĪǓ"],["@@ǔїف̰ųՔעЫ"],["@@ؑңd҆ӾͲİ͓"],["@@ӰIyұቩ൶ܶȫ۾܍"],["@@XǭϩǚϒT"],["@@ɸƨɢѭ׭ėl׈Ũȩ"],["@@ӡؗ΃Ԙࠦŀ"],["@@΋nɾɄŎɱ"],["@@ȩمګҐѽխΥ֖ŮजؚˢܘԙɚՕ"],["@@݈©ϰԥଦѧୖओރʪƩˑоtؠׁӂȘ૏ᢷ൚ːƲ̍ѤෳѪޞ̤΋ʴۋǃ˸КɑѠ"],["@@ׅɀǲʨֺĥǥρ"],["@@Ĕὠࡩͬɸशࠒ֫`̟"],["@@ʱŰЖðƣȟ"],["@@ֺğ̏Ň˩Ȩ"],["@@ѝĢԚܔөͅő"],["@@ٮѥwୣѯœûࡖࢁࡦϬȾόț"],["@@ߊǍ˔֥ŃųݛҶ״ݡ؝ĭඵత௢a"],["@@ۋǍĶ̖ތñǵÕ"],["@@୸ලʲ૳ࠓڴðΪঽ͌ľҠڭӼՃ՛ɧ؆մ֨ઠХ"],["@@औJ˛׾˯́˛ੵĬ،΁૑ˇ߳ϵɰȱڅХΈӂগΆÙӻ॥୰ঠҠ਼ޟػఢޤ̍ӺƠTЂڂǙ»Ǎ"],["@@і֡ц̒࡮ʑŮѷۏŉݚį˰կඉвÙտґÒၷ୴̐ւѾˑpɠھļ"],["@@ࢨఀ଻హञಠ໿ॡۙ˯জ༁ዚنѝ"],["@@ࣚőŁ̏ѫǒUˁיÜջϙोȾڪҺа¥ɗ̰ࢊű"],["@@ͧŶђǨĩ̝"],["@@ҭȰݨ˶˹ӥ"],["@@ӑ̬ɚǦȗ"],["@@Ƴ̈ࡨऔͪÍਝ୍"],["@@ࠜƹҴ࣋৫ͫᏋմڭѺ୚xخψڎD"],["@@׮đ̭ࣥʹֲ̠Ġ"],["@@×؏˹˘Β͸"],["@@ਗ਼Οߝâࣗ՚ٜʛ"],["@@ࡊŧരɆགۋ࿖ƕɏ଍ĩӭӓΟϊᒅܞทȻϭ޾ֲƗ"],["@@࢜ይජὡߺይದὣݪይ௄ὡ۪ዯҚಳԀğӴĝĀǼਆʻҮ͘࡜~̚V¾Ɵʅyȥܬ̯ആݡႦࡳϬ۟ǢʢȐʮŠՈʔҞL}ۊΚ²ȅ˨૔ˤٞݲρ҄ٯ׬ȥ܊эǺƹ̈ȩ྆ޡނػހؕబ৑̀ȻϦ̇Ȧǧ»šÛƧϨéˈµIǏMɍԼƝʐґܮõሌٽτóɆʍٖ̊Ρ͊ᏳՉֵ׃Ďڏ޶ӌΪళ෰Ě˺೵ӁƋއϽƠɛΈЮ࣬մľۣþܧАᄥ්ഹʺܹ੆૘ǟਥװٔĴݹľʵǳ߇ԚÃূыٓಓӴኩᓔĩӟགᒡઉϊمإ़κǠΡÃЏ϶øكႍ׺[ΫऒƟОѕԺѮͯࣳұᑍݰᚹಶᩥߴϘϤȡְ۾ދҴѸ৓ЈၩڭᐍјǑӔЇͥᣡ˶ᘅǷǥ̜໓Юǎݞ࢏٧೽ɖ̴ϰᅟǪ˾ǾթȘΔ֦ƂᱫӽˣǬ̺֖਑ΟƘҩϽÏێͻ؟еक़Ȕκȫ֥૑࿙r͕ʌǡݥ͙ͮ¹ПїľɷӣߛQѫ۱ԳŚ৯ʣҩǠ¼˖ࢂ̆Ղֲ܅˗࢏ִ̐ાɗৎགڨ᩼͡ᑇڈॾ݆ࣅӷణ¶၍ܙൗཷ५æࣘڝ঑Ή̼̗੉Ɠܱષ྘ѫĒҟඥৗǚ͗ලౕ͍৉஍үȑ޵ἥ౻Ƀ˟ؒʻϱ̛ɻ˜̑ѵൗЃǹЭͣӖಯڷ၍ωȑ͆ҸæڕǘࢹੵדșֿѤ~֓εĥ֓ʘªͧϑǥ_ن܈ǒᐞཬ࣢ʮ٦×ǉʃҶ̓ƎʸࡰƣףҊ͎ېឺౚՆțıնՠׂࠊԨޚ·֓ϲࠪݖؙ͎Ɔǋϒࣚ۶İࢼԕֹቻכݗՈêͬےµܿʤЫΩ˱Ĳͬ஛̙ąၗଌѵȱষԮྃ֝ƿ̃ໍÉݤͲO۸ڹĈâম໋༒ڨऄ୹ॕψس᏷с॑Ɉ᎕ഞڃĜࢶ޺৚ˑ]Бٌ϶ؠЁԢϒহɎ׬ŌΕƬуɻᕏƴϪАࠟż§ɾӍϭۡԺã҆׵tċɼڦĸֿʈࢀƨ̣Ԯ߆࠲ࠢÀȺӈ΅ȕǩڈ݀ʦƯЮ࢖ðۜ˙ǡʑЈȨ҈ƃ଴ӮӲלَǟྒɼҒאڕॸ੯Фॾ͘ЄعϞ૱ʓႛফअجʢԛ୷πᙣ̝ᗿ̀ܥ͘bѼ઻Ԫ൰˾ᝧ̨෋Մᯞઠดϯрᘨ׾໾ӑF߭ܔ̍ ğݒՖ࡜ȹʳ͚໵Ǧෝࢢʚ͈ᄊણᣀėϋњকžँˉज़ͪ¦ܔ⃱Ȁෟ૾㏃၌۾ƤtਐᒬOᏘΊ᧺ᯘɣϝᇤ͊൘ٜ۬ۧÿӦݰÖഓʚ؄;מȴɖ̅ᚂʢᒐ௪ĶʹჴϡࡩѝϮȓంּ፺ڙྴ̌൸řՐƿŷы஦Ɖɗʋ⸎ňᷦىⅤŷಊ͹ↈʊ৪ǿ᪚࡛ݘMࣜዯ"]],encodeOffsets:[[[638290,-204138]],[[634548,-199335]],[[635494,-198899]],[[633484,-198128]],[[631862,-197470]],[[627541,-196172]],[[836527,-188576]],[[838058,-187416]],[[792655,-184641]],[[837300,-181589]],[[805060,-175527]],[[823230,-175140]],[[811501,-173935]],[[732612,-164929]],[[727859,-163475]],[[728193,-163036]],[[843650,-159871]],[[843158,-147757]],[[845996,-144197]],[[852219,-143434]],[[850727,-143132]],[[848718,-142817]],[[853268,-135068]],[[712584,-126713]],[[711501,-124637]],[[780425,-122328]],[[575108,-114720]],[[571208,-115108]],[[572998,-114925]],[[574321,-114516]],[[565190,-114411]],[[559467,-114472]],[[581750,-113781]],[[584390,-113410]],[[579006,-114057]],[[550193,-113172]],[[547777,-111389]],[[595030,-110483]],[[598081,-108967]],[[599896,-108504]],[[600551,-108324]],[[608166,-106386]],[[605213,-106022]],[[614791,-105555]],[[683654,-105751]],[[613931,-105618]],[[680103,-106257]],[[612694,-105048]],[[611840,-105110]],[[678434,-104486]],[[624001,-103729]],[[684153,-104635]],[[677066,-102316]],[[679061,-102567]],[[680812,-102943]],[[626836,-101837]],[[589071,-101509]],[[678879,-101807]],[[675567,-100936]],[[676926,-100504]],[[629322,-100173]],[[672721,-99555]],[[629083,-98266]],[[670042,-97119]],[[672646,-97345]],[[629847,-96803]],[[629588,-96463]],[[609285,-96211]],[[639006,-92152]],[[639898,-92839]],[[595711,-91284]],[[642545,-91103]],[[639543,-91101]],[[579981,-91082]],[[581248,-82401]],[[646329,-68396]]]}},{type:"Feature",properties:{name:"Uzbekistan",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@ōqșHŃvņǼXſɊŴƞQĐƹ̚ëūΏ"],["@@þ ż¤ǶýƲĭ­o½ᠵ೛ઊϣǰϽӰǉЊAȂÝžҤКÌɄϴțņÍâĔɩcʻǒiȈ¹Ơ¶ĬTūȹ¶ͮà՞ϱচġోڵГŞĵ͵ե~੯ٙݳ̞ڻíûڕčԑЦংݲׇۮ࿳ࡍۣʚу҇¡࠙൧éۆǣϑėÞ͋ԷƵԻگ໥Ȏ۫Αˑ֍ּȓ̧࣐ƭ̓৫Ѧח๥ྭƧթŝǋ@ÖĵÖȧYǡİǓm˳ŇʩÌɡɞӽĈڑ»ʘæ̀ŤиːҊ௳ּލ]ᜣ࿾੩Шᘋᆞؗ᠔ݯТחȗ࿫ðڟЊǶ߮̂ĬѽϢɶՔ౅ʠೳ৴̫ʷ۹ýӐܿࠗтهڗଥƧ࡙ױtଉ˅șᓙƀᚐ了㜔ల⧜ᤁຒ፵ᥖ̤ᢄǡ੦μ৐ࣝЈॽېʮݑᕁਆwLᆡᤄ ˼ϕʧӫѬ΃־õƺ֨๮௲⡜ᇎ"]],encodeOffsets:[[[287719,-147179]],[[288495,-140752]]]}},{type:"Feature",properties:{name:"St. Vin. and Gren.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ʓ̂ːǄ{҅"],encodeOffsets:[[895309,-220106]]}},{type:"Feature",properties:{name:"Venezuela",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ǧǴ͊ˠƞ˓˿ǿ"],["@@ʿñѲɖǱƣ"],["@@ĭկઽɀЈɼўȋφ̀"],["@@æyĢÓǪӓ࡛ओأ£୽ܑܿəׇžڏ̳̅ोૃ޸໇ЋѸ࡯ӡ̨̂סਚोҚ᝝์ȭčֵ౯ԅэ࣑ፋऻࠍडȁQîΐΥ̼ࡉѷٝ£ங৸߇ᤞणࣚ՝@ஐංࠅ৐ӡ෰Kႆ܈๎µ֖ࡧʖഡ̗ᒍïေᇮ൛̨ࡱɳዽĺӷݲؕΈäႂڝނϏંؑͶڭƣ۞೮Ǡ༶ͤޘࣦ߮ծ੄ൠѬണٹΠࡕҸЅț¥ϒݯযሯఎႛࠚʦ̼рTߞ଩ᇢ॰᦬ࣶː͠ڌķïѸࠛŃˑהƎӆъǲর༟ᅪŇৰ؃੍ͤ϶ѯ⠪̸०࠹࿶п؞Øක߬৚ĸେĖƃȸ㍦ʂ੹ѡ஋֒էñԹЂ͔ײथȬɞǎƉΚǪUࠪޓĻФϚǸሰடӅңЋఏࠥO݂лતОථÈΎƯҜկŹʝŵ¿á÷ʏਙ՝бة˴ӓ©ʗŤūƌŶ¾ǌKņ°bĥÃȥĕŹ۟ЅुǑŗʃÐǗüƫNƏöϣÛęƕĵǑǱǻʝëӺԵԌՋԂՃ"]],encodeOffsets:[[[896984,-231811]],[[897403,-231072]],[[888664,-225636]],[[898330,-241809]]]}},{type:"Feature",properties:{name:"U.S. Virgin Is.",childNum:1},geometry:{type:"Polygon",coordinates:["@@ѐįڗŇʈȸ"],encodeOffsets:[[883784,-207460]]}},{type:"Feature",properties:{name:"Vietnam",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@űߵΟފӒ¬"],["@@˗½Κ̈āʉ"],["@@ҕ˟Ǿϼ˘ś"],["@@౗ӿƱԥѝʃ੍ŚŦקӃұ̛ഫ১ँᓻ՚ਡಈఙéԕʁÖ᷂ẛѤpဨᒏा₭ŗಋ̾ܣtߗɷ;ˉʁǤ֭ɍÆß໷ε̑Ƈֵ፳ছɗЃဥܝցטƩ׋ӗ̢˟ǁ̞¯ͩٹL۸Пѭַো߶ऎଇȋΓෟনۺࣩZѵນڷࢵਣޓë̂ͮǟŬłᅤ֐ࢎءҪ̵óԡզॖˠ҄ϐ^Ҹ׬Ʃɀʆ޲Đɀ˱ێƵVժڍֺbބΞ˞ࢌǁ¼ג٘è୘ܬАĭʸɸm๰࣮͐օ໮ҶಔGʦŨ¡ĲµƜǨǤĆƐŀǆ¶ƤÝŪţǾɥǖʹɄųžɻЪÄĆ϶ȄðänȲ্ۜăĬăˊ¡zĥăščǷĜɭΞWϠ[Ű­Ǧӥך֛ѺڍܠȩվƟɀůǢܷ֚»Ȱƞ˒Pú᧭ะdĀĈÎɔŨĒƔcǼóȂ¬ÜèRѨfذǗĤlή̦ƔǼƲ˄ʮZƢýȖđö͡P¯ª{ƞQĘùļˇĔɭ~½ĦĲǌİŬÊŬȴӯϸ͟Ⱥȭì੟։οȂջƌɩŖѹ۠×ƚˢचɝŤÃǌ¡qñȡùíǛIƗԞटਫ਼ŒĂƜŘШըƪĪઆڳ߲ܚͺН̲ѢڤէӘ׊Өɋ܈ˆѪ۲दҲஎिڸł˔ʥהŔԔ́կةǰୡڢūߔ۵࣢ļҌͣ"]],encodeOffsets:[[[366634,-227655]],[[377234,-198915]],[[377498,-198124]],[[378538,-197330]]]}},{type:"Feature",properties:{name:"Vanuatu",childNum:10},geometry:{type:"MultiPolygon",coordinates:[["@@͑ȅ˩͢ô̎Ոѩ"],["@@މƸøԚ˴Оە"],["@@̞΅Ƒɫ߯ȜʾΠϦv"],["@@םÉħϐۆͅ"],["@@߹ʌՆͤ˴֯"],["@@औ߉ࡹɿȵ࣊ѧǔŖѪ̠ëǎБ"],["@@Յ݂ϘȻ΁"],["@@ƀ݉ײӺʸ଑ʍ̭ݝœ˝Ԍŗႊϸб"],["@@ΉùɔɊŮǠˇ"],["@@ɅˈȬǮZѵ"]],encodeOffsets:[[[544055,-309302]],[[543671,-307665]],[[541355,-303852]],[[541399,-301769]],[[541016,-300563]],[[538636,-299906]],[[540018,-298106]],[[536888,-296445]],[[539186,-294901]],[[538941,-293936]]]}},{type:"Feature",properties:{name:"Samoa",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@৩Ĵ˽͖চ¹͎Ϗ"],["@@ͬӭřˋۓԵ؜সŚ"]],encodeOffsets:[[[596023,-294316]],[[593690,-292730]]]}},{type:"Feature",properties:{name:"Yemen",childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@ौÔۮʟࢥїअġࡳӐ԰ФӶǭ"],["@@ƵåʤȸĭƑ"],["@@ȧĈǐƄɋ"],["@@ᄅߏеݫګᐍ॥⬩೉࿏೅ຟK൏ࡻඹѭᡷ˳๏ୣफH඙ҷߡ̪ͯķҟ౜Ȣॐη܊ƑᐄƉѦзɠͰȾĿ݀βࡊOݾࢼ܄ɬຈհΘ੎ѕ᩾ɲࠠ˭ᥖąӀۥφe۰φ϶ݬ๼མᎦॎ㼪खረ㉉"]],encodeOffsets:[[[230303,-221528]],[[200683,-218617]],[[200836,-217888]],[[229436,-210586]]]}},{type:"Feature",properties:{name:"South Africa",childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@քݳ৸ܕڲéĮʂπࡌլ²࢈ҮmӤʸ̜Æ˪p˚̥ԦႻଔđɵµӋȓ˱ǫɟȽғƫځಗңʃҲ۹"],["@@׏ĆΞǀɲʅ"],["@@ٜౝԐ⁣ൡӌʋȟȧ̫ѓॹǲࠫǠeǮˡμѯԲ˯Ғŷ؀ҸVė̒½ӊM͌ØbȶÙȈ`Δ×ऄzʆHƉᴅͷऽၛၟኟ⦭ᢋᰁ᝕ፙధՕയ¼̷ڛౙƐʫѧӷ_ᏅРࢅȡཙǄصƁ޷ڷ᥽ȭ࢟ܟဥϤ]˜׭͈֡ΫـԿÑúՏ΃ΠƓੌᆻጶǞˈϊěˤմܧႀᦏ᯦฿ᙲڿࢦࡥ࢚׈φźs̆¨ĸ|ȰĦžʜĔΆɩѾ̫њॡጦсሮŝǌĊÂϖФ΢ࣄˠ÷͂ᄋ䮎ୌࠣ୎ႡôᇝːʃᐚjüúØɐҊǖƨŮǾєӀϠƮĜĐĺ̔ຢԤϚՈMదۗǲÉӰeΜʗኼزȾҌᏂʐĶȲè֒żńèۜފ˔๠ೢ।ĄĠ܂औಪӊȨȔľʼɴƤԜ¨Fؔľըčڮʇ،čժêߠu˼ċɜŵদᝅܢࣻ"]],encodeOffsets:[[[166067,-337674]],[[208069,-384055]],[[174875,-321173]]]}},{type:"Feature",properties:{name:"Zambia",childNum:1},geometry:{type:"Polygon",coordinates:["@@ʦΓʌɯeȳ©ȉԬѱʽ¨ėˎγޕڟʞہȯսûTʿȬΑऽɼѩϺė૳߉Ĥ୧ׇࠉ̦ɕӖܗ΢ƶƌǋ㳣ᔑՒวፕť஥ٱȉ௙ჁࠁၱᛣԕȅਁʤܿěၓЂٕԌؓŔਅĈእΥ᱃ᗔڧດЯ₊ĉܘăܖȑᅀԤ@⋠@˄ɝъGŰʈᎬȹቮलֿĎٝጲҜɐࣿ༠ױᅔǙڠࡺੌญኢٳϔ܅мȧܶ঱ъáݦͰՖчɱᮔرǋ[э॑ƼඡரˑռЂᗨʋዒՃ৮খচpڨ❆ٸȰїƬȟǺǥʢƋؒZɪĿȦƃÖȵˢɳւõnǥ¼ûᇰ׷ϘȏȰӗʹª"],encodeOffsets:[[173679,-282196]]}},{type:"Feature",properties:{name:"Zimbabwe",childNum:1},geometry:{type:"Polygon",coordinates:["@@ԗɂߟvթé؋ĎڭʈէĎؓĽEūĆȫǪҏƎťӸᙙլषલǷశࢷÄΫࡎ᝟఼۽ഈᇯᓪmϾၔЁ݀ĜਂʣԖȆၲᛤჂࠂȊ௚஦ٲፖŦȆ߁ᄴÙဨ࣡ᙌًőϥێᾳҹಣϦλçُڢೳ࢙ມƕ੧ɦΫᇁᖙ˗ɱ"],encodeOffsets:[[172791,-317108]]}},{type:"Feature",properties:{name:"",childNum:1},geometry:{type:"Polygon",coordinates:["@@ຓ෼ɴ᐀ൡᑴከ๴ȢॎÒXዞĩୈѰ͠Ϯȇƈɶ¦ȎОƅ¾á¤͙îʴüȖȴĿŶĵøŋìǯdƙjǝˍݙ̼Ƨľ·Ê¥ǂD̘ÎˈĠŊpmʭźĭȄœþÚȩĪ̥ÚȥǚՋƧˁԋȵÙ¹ãƍƋʅŕ˽®٥ȡ˯LʏŷǿŝΗŷᰥז̋ıˇěɉÓұ؝^şɶċĊŗEķșȅÜȥؼÛۅ׻̊ӏГ֥"],encodeOffsets:[[291562,-165289]]}},{type:"Feature",properties:{name:"",childNum:1},geometry:{type:"Polygon",coordinates:["@@ܣޜࣙЯ୽ീ्¤ߛ঳಻ȷлૡٽ@ډІљWȿĄ´נÕĹӿĝĤÚМףԤҒ٘̉Ӑۆ׼ػÜÛȦȚȆFĸĉŘɵČ]ŠҲ؞ɊÔˈĜ̌ĲᰦוΘŸȀŞʐŸ˰K٦Ȣ˾­ʆŖƎƌäÚºԌȶĞǀÊłࢠќΐǠ̌ƠʊŞԪÑޙńуɼݽઆӛƔďˣ׽ӓ¸қ¤ࡋƮȍ̸ˏŧŏˋöŕť૿ם̵̳͂eƂաÓ"],encodeOffsets:[[303053,-167260]]}}],UTF8Encoding:!0}):o("ECharts Map is not loaded"):o("ECharts is not Loaded")});