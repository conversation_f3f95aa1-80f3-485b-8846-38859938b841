!function(B,A){"function"==typeof define&&define.amd?define(["exports","echarts"],A):"object"==typeof exports&&"string"!=typeof exports.nodeName?A(exports,require("echarts")):A({},B.echarts)}(this,function(B,A){var C=function(B){"undefined"!=typeof console&&console&&console.error&&console.error(B)}
return A?A.registerMap?void A.registerMap("鄂州",{type:"FeatureCollection",features:[{type:"Feature",id:"420702",properties:{name:"梁子湖区",cp:[114.684731,30.100141],childNum:1},geometry:{type:"Polygon",coordinates:["@@EAACCC@E@EAA@@CAE@EACI@ABC@ABC@ABABGBEAG@C@@@A@MBC@ALEB@BABADCDC@EDC@AHCDEFCNE@@@@@@@AFCDCDEDAN@D@FADAJGB@@@AEAAQGAC@CBCBABABA@AC@C@C@EAEC@@AAAAEK@E@CDCDCDAJEJCBC@ABG@EFAD@DAFCDE@@DMBMACFEDGDCDEDCBC@C@@C@CDCBCBE@ECECCC@@CCEE@EBCDCBG@EDC@@DCFADCACAC@@AEEAECECCA@CCEEAEAG@GBADADAFCFEAEAEAEEACBCJG@CCKAEAGC@E@EBI@GCCCEECBCDEBGA@@AAEAE@ECA@ABAB@@@HCFAD@@@BB@BB@@@BAD@BBD@@@F@B@@EA@@A@@B@B@@B@@B@@@@ABA@@@B@@BDA@B@@A@A@@BA@BBA@EBA@ABABABADADCBAB@@C@C@GCIAIAEAAAA@@@C@AAABCBA@CBAAA@AA@@@C@AAAAACAA@AA@E@AA@C@ABA@C@AAEC@@A@C@CDCD@DBDFBBBDBADEBCD@@EBCFGHKBABCBEDCDAB@@A@@BABB@@B@BBBBB@B@B@@@BABADA@CDCBCBAFAD@B@B@@@B@B@BAD@B@@ABGDCBABA@A@@@@@C@CDABABB@@D@BBB@@AD@@@@A@@@@BADCDCDABEHCBCD@F@@@B@@@BAD@DABBB@BBBBB@@BBB@@B@@@B@B@@@@@B@@@@@@@B@D@B@@CD@B@BBBDDBDFD@BBBBDBDAH@@CD@D@DDBFBDD@FAD@D@D@D@DBDBB@BD@B@HAF@HFHHDHBHBHBHBHAF@D@H@HBHFFJFHFFDHFFDDH@H@H@FBF@FBDHFH@FFFDHDFDFHBFBJAH@HAH@F@F@B@DBFBF@@BJBHBHBH@@AHAHB@@@BAB@B@D@D@B@D@D@FAJ@B@D@D@BBBB@D@B@B@@BB@B@BB@@B@@B@DBDBB@BBDB@@DD@@@@B@B@@@BA@@@ABADA@@DAB@@@DBB@@BBB@@@B@@@B@B@@@B@@@@@B@@B@@@B@@@@@B@@@BADCLU@AB@@G@EFIAGBAHEH@DAD@EW@Q@A[C@K^IFDHFFNZF@AAECCCEAE@EACCE@@@ABABABCBAFEAECCIEGAAAA@GC@@AA"],encodeOffsets:[[117430,30995]]}},{type:"Feature",id:"420703",properties:{name:"华容区",cp:[114.729878,30.534309],childNum:1},geometry:{type:"Polygon",coordinates:["@@CAA@@@@AA@@A@AAA@@@A@A@CAAAAC@C@A@I@EBC@C@A@C@C@A@A@AB@@A@GFGJEP@LFL@HBJ@N@FBLBD@HBB@JEDBJ@B@FAHAFCDCFCD@D@@@B@BBBBBBBBBB@@@@B@@D@@BB@@@@B@B@B@BAB@@A@@BA@@D@DBDBBB@@@DBBAB@B@@@@A@@D@B@@@B@B@FB@@B@B@B@BAB@@@B@@@B@@@BA@@B@@@B@@@@@@A@AB@@@FA@@DAF@D@BB@@ABADAH@D@BBHBBBDB@@BB@F@F@D@BBBB@@BB@@BBBBBD@BAB@BADCD@@AB@DB@@D@B@B@DAFCDAD@@@FDDB@BD@BAHAFAJ@BAF@D@DABADA@@BC@@AA@AAA@@@AB@B@BBFB@F@BBBB@B@BEBED@BAD@H@@AHCJRARHJFPHPFNDHBFBJBZDD@@@@BRDJBNDLDB@JDJFPFBBDBHFJJBBDBLHDBnNDBN@P@TABAJG\\]JQFMDW@@@E@@BIAGBABI@G@K@O@K@AAI@A@ICM@KAIAI@I@G@G@C@IBIBIDINYmUEAEAABABB@ADE@BCC@@BGAAD@BADGAACCBC@@@EIAA@CAAAAABAAC@@CBA@A@@AABECA@AAA@@@ACE@E@E@@@E@ABIDBFKCAA@A@A@CC@@A@@@AAA@A@A@C@CB@@EHA@A@A@E@C@GG@A@ABA@AAC@@ACA@C@AAC@@@C@A@AA@@AA@@A@E@AB@FABEBCBC@A@ABAAC@CAA@CBA@@BA@A@CAA@@AAAAA@A@A@A@C@@@C@E@A@@BA@CAAA@AAC@A@CBMFKBGBKBEFEFAFAF@BE@A@ABA@A@A@QEAAAA@CAA@A@@BA@@@ABA@ADG@EBCBG@A@AAA@@@C@ACQAAA@@BKVCDAB@@A@@@@@A@@@A@@@@A@@@@@A@@@A@A@@@A@@AA@AA@CA@@A@CB@@CBAB@B@@AB@@A@A@@@@@CC@@CAAAA@CA"],encodeOffsets:[[117381,31085]]}},{type:"Feature",id:"420704",properties:{name:"鄂城区",cp:[114.891586,30.400651],childNum:1},geometry:{type:"Polygon",coordinates:["@@AAEEAEAGIAA@@@AA@A@@AAA@@AAAA@@@@AC@A@@@@A@@@@DAB@BAB@@@@A@AA@BCA@DCA@C@A@AAEAAAA@A@@@@CA@@C@AA@@@AA@AA@@@@@AB@@ABEDCD@@@BA@CBACA@C@ECAAC@C@@@A@A@@@ACAAC@A@AAAA@EAA@ACA@@CAC@E@@@C@CBABCHABABA@ABA@E@A@A@CDADABABCBEBMBEBGBGDSFGBGBCBCDEBGBQBGDEDADABAF@F@D@BEHCF@BBDBBDBDDFD@DKB@D@B@B@DDB@BDHEBCGGBG@EBEBEBEDGDADABAB@B@F@DBL@DABCDIJC@I@E@@@I@M@MCK@MAA@@AGG@C@CBG@@DG@@ECG@K@GBIBABIBEDE@A@@@EF@@ABA@E@E@C@CBC@CDABABBF@D@BCBA@ABCF@BAF@L@@A@GBAAC@CAQKIIGIA@YEEMGEEC]J@L\\D@B@RFXC@CBG@GFABBHEJ@F@HBBDR@B@D@@BB@B@BAHAD@FCH@BAB@B@@AB@@@BBB@DBBBBRFB@B@B@BAB@F@@ABEBEFEFELAHALANEDAB@D@BBB@BB@DAB@@@B@F@D@@@D@B@B@BBBBB@BB@DBB@B@@AB@DAB@DBD@BBBAB@D@DAFABA@EBAF@B@@@BB@@BBB@D@@@D@BBD@B@BD@@BD@BAB@B@BHHD@F@B@B@B@FG@@DAD@B@B@B@BB@@B@@@DDB@B@B@DBELCAAJ@B@F@@@F@FDF@B@@BB@BDBAFBB@@@BAB@DD@BBBABBBB@DBBFJ@@D@DABDHBBC@ABCHB@AD@ADF@BCA@BABAFBFBnVJMBCFE@AFCLIHCFALGHCFAFA@AB@B@@@@@BAB@DAB@@@HAJAB@@@DAFAF@BA@@LAL@BAR@D@LAPANAJAB@FAF@LAL@LBL@LBJDB@R@L@B@VCD@NCB@FCHCDCJGHCHIHIHMFKDIDIBCBEBEDIBG@CBGBIBEBM@CBIDMBK@A@E@G@@@AAOAEIQCYIBA@IBA@K@A@CC@AACA@AA@@@AC@ABEAAAECA@GCGAA@G@@@O@"],encodeOffsets:[[117786,30975]]}}],UTF8Encoding:!0}):void C("ECharts Map is not loaded"):void C("ECharts is not Loaded")})
