!function(A,B){"function"==typeof define&&define.amd?define(["exports","echarts"],B):"object"==typeof exports&&"string"!=typeof exports.nodeName?B(exports,require("echarts")):B({},A.echarts)}(this,function(A,B){var C=function(A){"undefined"!=typeof console&&console&&console.error&&console.error(A)}
return B?B.registerMap?void B.registerMap("莱芜",{type:"FeatureCollection",features:[{type:"Feature",id:"371202",properties:{name:"莱城区",cp:[117.659884,36.203179],childNum:1},geometry:{type:"Polygon",coordinates:["@@[@G@]JEDQHQLMHOHIDQ@G@K@GAG@QFIDIHGJA@ET@DADAHEFCFE@I@IC@@@@A@OGMGCAEAAACAIDIAAACAA@CABFCDI@AAEEAABEA@AAACC@A@AACBCBECAAIBE@A@EC@@ABCAEDABADFBBBCBADDBBAFBFDADBDCFBDADBBF@LDBB@BCBIBIAE@CDCB@BDH@@@BBBB@@@@D@B@@BBA@BB@FDD@BBFBF@BBDFHHBFFFBB@BCCA@AJAF@BBPHDB@HEHAHBBBABBBBABC@GAEBCFKA@CABABMCADGFEB@D@BBB@BCAA@CDCF@BAB@HG@C@BBBD@BDBBBFFDBBBBDA@@@@@CBAAA@CJ@@@@D@BFANI@G@@BA@A@CBCDIACCICAAA@AE@@KCQDGDABC@@B@B@BC@CB@FA@A@ABC@ED@BB@L@B@BB@JCHBB@@J@HBBBLFB@H@B@FBABADAL@JAL@H@B@@A@IAC@E@GACBCA@B@FA@AB@DJJ@LGBCBI@BD@BDB@@ABC@C@@@@FBBBBBB@@@BAFADABEFAB@BCNBFDFFFHD@@B@@@@B@JADAJGFCDABIFCFGF@BOLGLADCH@@GN@LAL@@AFIVCLCLALAJ@H@@@PFVJNAPPM@AHGHGPO@@JGB@XCTDBBBDFJPRHJDFDDLHLBD@TBH@H@PAB@F@H@HBB@D@PL@@@@JF@BC@AB@BBBBFBHJ@RNBBBDCF@HFJ@@@BB@B@JELCFAFC@@F@BFD@B@F@N@JHB@NF@BFBP@DAD@BBBF@BBBFBH@DBBFFFBBH@BDBBDDB@DF@DDD@BBBJ@DDF@BADDF@JABB@@FF@BFBDAB@@BA@JHDBDCACBCFCFAHGLIHCF@TCBA@ADANGBAFADDB@L@FCBEFA@@NAD@RANB@@DB@@TJDDJ@@@DAJ@DAL@BAJIFCBBBAHEBA@AAEMCA@A@CBB@F@DDE@I@A@ICCAE@KCDAJKP@@@@A@ABAB@D@BBDGBC@CAGKIEA@@BA@@@@@@B@B@B@@@AAB@DAB@ACB@@B@@B@@@B@@@B@@@@@BCBGB@@@HBBGCEEACBC@A@EEB@FADGBEF@EEECCEHGJBDBBBJ@@AFCB@H@HKDA@EFCFJE@A@A@@D@@FJB@B@JADCBA@IB@DBB@PHHCF@AB@@DLBBA@CB@BDBDBDC@CLBB@HABC@AACGC@A@A@@BAAAA@@A@@@AA@@A@@@@@AAA@AB@@A@AB@@GBEB@@@DDDAFCB@`EBAB@DE@E@AACBE@A@CCCAABCFGJAHCJKD@BA@CBCBADGF@@K@AACBC@EBAD@BBDLDF@@D@B@@@DMAKCCBCBACC@@@ACE@@@GBABAB@B@@@@B@@AB@BHJBBBBJBAF@DBB@BD@DCFAH@BDJCH@B@@B@BA@@@@@@B@@@B@BHAFE@IF@@CC@CBCDBDABA@AAE@DCB@B@HG@AEBAABABBF@BABAGA@@BABADA@AAC@@@@A@A@AA@@@B@B@B@@AAAA@A@GAACAE@EEAG@EFAHGAI@@C@BCAABCAC@ADIDEB@DAB@BOEE@@@AACBEBAAAQEE@ACC@@@ADCB@@UAGAIGEDICCAEGGBEACC@CA@E@CBALMV@@@B@B@B@B@BCDCBABEFAD@BA@CA@A@C@AAA@A@@A@AA@@CCGCGCA@I@@AA@@@AC@A@A@EEAA@@ACACCOBA@BE@E@ABEACDIBA@A@A@AA@@A@@B@@AB@B@@A@@BA@AA@@AA@A@B@@A@A@@FC@AAAA@ABC@CBA@AAC@AACAAACAAAAAA@A@AB@@@@A@@@A@A@@A@@@ABA@@@@@@AAECECCAGE@@IEFK@AC@K@GIGBGAAAJABAB@@E@G@AII@GDML@H@@IJA@ACMMBAMEQCAMNIH@@IFEBAAEAC@IAEBA@CB@@ACCCA@CB@B@@@@A@GA@K@CAAG@GBGAC@AAC@A@GAAC@@BBB@B@FB@AAABABAD@DIBA@EBGA@CCECA@E@ABCGAABG@AJOBCFCFADADCBC@I@E@IEEACBCFIDGBAFA@@@EBIACAAKMEAIA@ACBCBGFEAYC"],encodeOffsets:[[120499,36915]]}},{type:"Feature",id:"371203",properties:{name:"钢城区",cp:[117.811354,36.058572],childNum:1},geometry:{type:"Polygon",coordinates:["@@CACGC@CA@AAACAE@CDC@G@AAAAA@EB@BABKDA@@@DGAAE@@BCDGBAAAACAC@CHABA@AAC@C@A@@DE@CCACC@@B@@BB@BCBA@CAC@G@CBIBAAA@GEGACE@ADABA@ECACC@C@@CAC@A@C@A@@@AAC@AAA@@@A@AACAAAA@A@@@A@@@EBC@@B@@@B@B@B@B@@@D@B@B@DAHAB@BEBAFDDDDFFGLCD@@KH@BGH@@A@@@A@CCAAECMAA@ABED@BDBADA@CD@AA@GDCDE@KFEBGD@@@BJBFBLNBBBDAJ@F@@EBABCHEJADBDFF@J@F@JADCDCBEBEDADIP@BAHBBDHBAF@B@FDDDB@AH@FABCJC@ABABBB@BEAA@A@AA@@BDHBB@D@BBD@HBHAH@BB@D@LHBB@@@@@@ADAB@DDBD@@DAB@FAJBD@FBBBFAJE@@JGNMDBFRBNNADN@BIB@JG@K@CN@HJJ@B@H@FA@ABIBBBHBHAHJL@D@@BELJF@@HFDBFDFDBB@@@@@@AB@B@@@BB@B@@@B@@@@@BAB@B@BBBBDBBBDBBBD@BBB@DAD@BAB@BB@BED@@@B@BA@B@B@@BB@@BAB@@@BA@A@@BA@@@@BB@@B@B@BABCJBDAF@B@FAFB@PADDDB@BB@FB@F@B@BBD@@B@@BJ@B@HDHDDD@@BBB@@@@BBB@B@D@BDBB@@ABCFEBADADC@A@A@A@A@A@@NUBKDAF@B@@DDDFBHAFHDBJDFCJHHBVB@@DABC@@D@BDF@RFBBHCDAJ@DDDBH@HAFCD@J@NCJA@CDGCCCOCCCA@ACO@AAI@ABE@EACA@@ADOFE@@AAAAEACKCAA@@@EFGBG@C@A@@@AAMEECAC@@@GC@E@AFKHE@GE@G@EBAGMGGCKAAA@AC@AD@F@F@DAJO@CC@@@A@AAB@@@BAAEAAABC@AA@@A@CEBGBAD@BC@EB@@@B@@ABACA@IAEFGD@B@@G@CHCDCBAAAAI@A@@@@A@@ADCBA@C@EBA@CD@DCDEB@B@N@FDBBBBBDB@DC@ACC@ALEBA@C@A@A@A@GC@A@A@G@AIAECCFEDA@ADGIAOAEIACBCFGBCHEHAD@@M@@A@C@CECABCB@JBDMDE@@AEE@EAA@BEB@BEHQBABA@CACA@@@A@@AA@@@A@@@A@A@@@A@A@@B@@A@A@A@@@A@A@@@AHAFABQB@BBDGNA@@@EBIHA@@@A@C@EAKB@BA@EAE@AAEA@@GBC@OE"],encodeOffsets:[[120707,36881]]}}],UTF8Encoding:!0}):void C("ECharts Map is not loaded"):void C("ECharts is not Loaded")})
