!function(B,A){"function"==typeof define&&define.amd?define(["exports","echarts"],A):"object"==typeof exports&&"string"!=typeof exports.nodeName?A(exports,require("echarts")):A({},B.echarts)}(this,function(B,A){var D=function(B){"undefined"!=typeof console&&console&&console.error&&console.error(B)}
return A?A.registerMap?void A.registerMap("葫芦岛",{type:"FeatureCollection",features:[{type:"Feature",id:"211402",properties:{name:"连山区",cp:[120.869231,40.774461],childNum:1},geometry:{type:"Polygon",coordinates:["@@JADAB@FAH@N@J@@BBB@B@@BBB@@BBBB@@BDBB@BB@@B@BBB@B@B@BBB@B@BBB@B@D@B@CCEC@@KEEKAI@CBCFUBG@CHS\\ECCGAKGAGBILCZCVKDQAMLAJIBMLCBA@ABA@ABABEBA@ABC@@@@@G@GA@BA@@@@@A@C@@@@@@A@A@@@@A@A@@@@AA@A@@@@AA@@@EA@@@@BB@@@@C@@@C@@@AA@@AA@B@BBB@@A@@@A@@CBAAA@@A@A@@@@A@E@C@@A@A@A@@@@A@@@@@AA@CB@@@AA@@@@@A@@@BA@@@@A@@@@CBG@A@A@@AA@@A@AACA@@BA@@@ABC@@CC@@A@A@@@@A@C@@CA@@@A@@@@@A@@@@B@BB@@@ACAAA@AA@AA@A@@@@AA@@AA@E@@C@@@A@@A@CA@A@@A@@@@@@@A@@A@A@@@@@@A@A@A@A@A@@EA@@@@@A@A@@A@AB@@@@AA@@B@@@@A@AA@@@ABA@@C@@@@A@@A@CA@@A@@B@D@B@@A@A@A@@@AA@@@@AD@BA@@@A@@@AAC@@AACBA@A@NGBAIKQA@AM@MA@@GAA@@@A@@@A@A@@@@@ABA@@@CDAE@@BAC@A@@AA@@@A@@B@@@BE@@BA@@@@@@AAB@@A@@AA@@@AAA@AA@ACA@@AA@@@@AA@@AAAE@AAAA@@@EDCDIGBA@C@AGEEABADA@@@@@A@@@@AC@@@@A@A@A@@B@@A@@ABC@@@@A@A@CA@@@@AB@@BB@@@@BB@@A@A@AB@@B@@@B@@B@@B@@@BBBB@@@BA@@@@@@B@@@B@@@BGAAFFD@B@BAD@@A@AB@@@@@BB@BB@BA@@@GAA@@@@CC@@@@@@@ALEA@@@@@@B@@A@@@@@@A@@@AB@@@B@@A@@A@@A@@@@AAA@@@@CAAA@@CA@@@@@@AA@@AAA@@@AA@@@@@@@@@B@B@@AA@@A@A@A@ABA@A@A@A@A@@@CA@@A@@@AB@B@@A@A@@@@@@@C@@@@AA@@A@AA@A@A@@ACA@AAB@@@@@@A@@@AB@@A@@A@@@@@BA@EB@AA@A@A@@@@DAD@@C@A@@@@@@@B@@@@@B@B@@@B@@BBBBABBBB@@CD@@DB@B@@A@A@ABA@@@@@@@@@CBA@@@@@BBB@B@@@@@@@EBA@@BABAB@B@@@BBBBB@@BBD@@B@@A@@@ABABC@A@A@GBA@A@@A@@A@A@@@A@A@@@A@G@A@@@CBEDCBCBABCDEFOHMDE@K@QCc@K@MAOCOBIJEJEJIBOAOAYECBI@QDUDKBA@QDODA@A@E@A@CAA@CAA@@@A@AB@@@@EAE@CAAAA@@@@@@C@ABA@AA@@@@AAA@@A@@BC@A@@BA@ABC@C@@@A@@@@B@@ABAB@B@BA@AD@@CB@@A@@AA@@@ABA@A@ABA@A@A@@@@A@A@@@A@@@@@A@A@AA@@AB@@AB@@@BA@@@AB@@AC@@A@@@AA@A@AA@@A@C@@AABA@A@@BA@C@AAC@CAA@A@@@AAA@AA@@@AAC@@@A@AA@A@AA@A@@AA@AA@A@@AC@@@A@C@@@A@AAC@AA@@@CAA@@A@@@A@@BCB@@AB@@@@B@@@CB@@A@@@AD@B@@@B@B@@A@A@A@@@A@C@ABE@CAG@@@C@KJIJELIHKDO@Q@@@KHAFBH@JCJELIJIHIJKHIDU@MASECCGEKAOCKCKBKBKFBBHFFF@H@FCFA@EFMFEDULEDCHEP@@CNCJEHEFIDIDCF@FBRDR@JCNCLAHEJAHDLHFFFNFDK@@@A@A@@@A@@A@A@A@AA@@CA@@@@@@B@@@B@@@B@@@@CB@@@@@@B@@B@@AB@@@@@BB@@B@@@@A@AB@BABAB@@@CB@B@@B@D@D@B@@@FDB@@@BAF@HA@@BA@@B@DB@@@B@@@BBB@@@BBB@B@@DBD@@@@B@BDBBD@DB@BD@@@@@D@BAD@BAB@@@@@BB@@@@B@BBB@@@B@@B@FAB@B@PBBBBF@JAFBFHFPH@@B@@B@@BBBB@B@@AB@@BBB@BAB@@@DB@@@BB@D@B@@@@E@@B@@@B@D@@@AABA@@BA@@B@@BBDDB@@@B@B@B@@@@CB@@B@@@B@B@D@@@@AAA@@@@BADB@@DBD@B@BA@@A@@AC@AAAAEAA@@@A@@AFAB@B@@A@@@A@@@A@AB@@@BA@@B@@@B@B@D@@A@C@@D@DA@@BCPCBAJELELCBAL@JSFGHGFAHALBNDLPRLJFBB@BB@DBBBBB@B@@@@@D@BB@@B@@B@B@@@B@@B@@BBB@@B@@B@@@AB@BB@BBDD@BAB@@BBA@A@AB@@@DB@@@AB@B@@BB@@B@@@BA@@@@DBBBDB@@@@AB@@@@@BBBDABBB@B@B@BAD@@@@@@A@@@@BA@@BCB@@@@EBA@A@@@@@@@A@AB@B@@@B@@@@ABB@AB@D@@AB@@A@@@ACE@@BBB@D@B@B@@@DAB@BBBBB@BA@@B@@BD@@AB@@B@B@@DB@@B@BBABB@DBBBBB@@BB@BB@@@@@BA@@BB@B@B@@@@@@B@@@BBB@@@@B@@@B@@@@@B@@@BB@DAB@BBBBFBB@BBB@@B@@@BA@A@@@@BB@@@@BA@@@@BDAD@@@B@FCHG~}DABAHCHBJHLFL@`UBAB@BA@@BA@@B@@@B@@@D@@AD@BABA@BBBB@B@F@@@@A@@@@DAB@@@BA@@BA@@BABA@@A@@@@@A@@A@ABC@@BC@@@@B@@@B@@@B@BA@@@@@@AAA@AAA@@AAA@AB@@AA@BA@@B@@@@B@BB@@@@@@@@@@CCE@A@@B@@B@@@@BDBF@@BB@@@@B@@@@ADABB@@D@AB@B@B@@@D@@BBBBBBB@@@BB@B@BB@B@@@B@B@BB@@@@BBDB@@BBA@@@FDB@@@B@@A@@BA@@@EBABA@@B@HBBA@@D@@B@@BB@@BABBB@@BB@@@@@B@@@BBBB@@B@BBBBNENKPOJEHCPYjIPCVEPBJBZAPAPBXJLHJTJELA^CPAHAH@F@F@H@FBHAJ@FAHEFIDEFAJEJEFCDCJGFOFEBC@C@CBADBDBDBD@BAACACBCBIBAB@D@D@JBJBL@B@H@HANOLO"],encodeOffsets:[[123877,41843]]}},{type:"Feature",id:"211403",properties:{name:"龙港区",cp:[120.893786,40.735519],childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@@@@@B@@AB@@@B@@A@@A@@ABA@@@@@@@@A@A@@@@@@BA@@B@@@B@@@B"],["@@IAA@A@@@A@CAA@CAAB@BA@@BAB@BAB@@@BAB@@AB@BAB@BAB@B@@ABA@ABA@@BA@A@ABA@@@A@A@A@@B@@@@@B@@@@@B@@@@@B@@AB@@@B@@@B@@@@@B@@@B@@@@@B@@@@@@ABA@@@@@A@@@@BA@@@A@A@@@A@A@@BA@A@A@A@A@ABA@A@ABA@A@C@A@A@ABA@ABA@E@AAA@A@A@C@A@A@A@A@A@C@A@A@C@A@ABC@A@CBA@C@ABA@AB@BB@BBB@B@BBB@DD@@AH@@A@A@C@A@@@@B@@@B@@@B@@@D@B@B@@@@@BBBBB@BBBB@@BB@@@BB@@BB@@BB@@@BB@@@@B@@@B@@BB@@@@@B@@@B@@@BB@@@@B@@@B@@@@@@@B@@@@AB@@@B@@@@@B@@B@@@BB@@BB@@@BB@D@@@BC@C@@B@B@B@@BFAB@@A@@@@@BB@@@BA@@B@@@@@@@BA@BDB@BB@B@B@@B@BB@@B@@D@@@@@@@B@B@@@@ABA@@B@@@DB@@B@B@B@B@B@BAB@B@B@@@BB@@@A@A@@@@@@@@BB@@B@BB@@BB@@@@@@DB@@BBDB@@@@BB@B@@B@@@@BB@@@@A@@BA@@B@@@@@@@@BA@@@@@@@FBBK@@@@@@D@@D@@B@HB@@B@@AAAA@@A@@@@BAB@@@BC@A@AECBEHB@A@@@A@@@A@@@@B@@A@@AAAA@@A@@@@AA@@@A@@@BAB@B@@@AA@@@@AA@@BA@@@@DBB@B@@@@@AD@BB@@@@AB@B@B@@@@@BD@@@@@B@@@@CBABFBHF@B@DABJHDCFC@@B@BB@BBFBB@@BB@@@@BB@@DB@BBBB@BB@@B@@BB@@@BA@B@@@@B@@AF@@A@@@AB@@@B@@BB@D@AB@@BFDC@@B@BA@@@@B@B@@@B@@@B@HB@@NBN@@BRBJLABMHB@B@DABB@@BD@B@@@B@@ABC@@B@@B@@B@@@B@B@BA@C@A@@@@BB@@D@BB@@@@@@DB@BA@@B@@B@B@@A@@@BB@@@@BAB@@@@B@B@@@@FB@@@B@B@B@B@B@@@@B@B@@@@B@@@@@@@BB@B@@D@BB@@@D@@@@FBB@@BB@@@@@BBBB@@BBBDB@B@@AAA@@@@@@B@@@@@B@@DB@@@D@B@@B@B@@@DD@@AD@B@@AB@@DBBBB@@@BB@@@B@BAH@D@@B@@@@@AB@@B@@@@@B@@B@@DAB@@B@@@@@B@@B@B@B@@@@D@F@B@@B@B@@@BBAB@DB@@@B@@@AA@A@ABB@@BB@@D@@@D@@@@@AA@@@@FB@@B@@B@@B@B@@B@@B@B@@@@@@B@B@@@@D@B@@@@@B@@AHBH@@@@@D@BAB@FABABAB@BAB@DANKAA@@BA@C@@@AAEAAACIE@A@C@ABCBADADALAFAFAJIBCDG@E@EACGCQE@@ACCGAGAK@MBCBABC@@BAD@B@D@D@D@FF^@b@DCBAB]BCF@FA@AAA@C@E@IAEACA@EAK@OAK@M@E@EDCBEDEBE@EAEAAEACECECAA@EAAAAA@C@IBC@E@CAC@CCEGECAACAG@G@KAI@O@M@M@KAC@C@A@CAA@AAE@A@A@A@A@ICC@C@ABA@A@E@A@A@ABABA@A@ABAASACAICICECA@CCACAA@CBGBEBEBA"]],encodeOffsets:[[[123893,41727]],[[123734,41635]]]}},{type:"Feature",id:"211404",properties:{name:"南票区",cp:[120.749727,41.107107],childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@FA@@@A@AACAACACA@DDB@D@BBD"],["@@@AAOA@@C@ABE@EAGACEECGAAEI@AA@C@A@A@AAA@A@AAA@A@A@AAA@@@AAA@CA@AA@AA@AA@AA@@@AAA@AI@M@G@EBA@CBIBKPMPGBG@A@K@IAIAC@C@A@ABAJADBDBDABC@CACACAAB@D@DADEFEPIHCDEDIFIFEBCFEJGFEBI@GBEAG@E@E@G@GBOB]DKBIFISKGWIOAOBYBIAOAUFODiJOZGDIFOPMLMFAAAAA@@@AAAA@@A@@@@@A@@AA@AAAB@@AA@@@AC@@@ABGAA@@@ABAB@F@@AB@@@BA@@@A@EC@@B@AA@@CAAA@@@@AAA@A@@@A@A@@A@AAA@@A@AAAAAA@@@C@@@A@ABAC@@@AACB@B@@A@@@@@AA@@AEAC@@@@@AA@@@@BDF@D@@@@@@@@A@@A@A@@A@@@ABB@@BA@@BBB@BB@BBB@BB@@@@@@ABA@@@A@@@A@@@@@AD@@AD@B@BB@@@@@B@@@ABAB@@AB@@AB@@A@CB@@@@@B@@E@A@A@AA@AABABC@@BC@@@A@@@A@@@AB@@ABA@AB_VK@KEIGGAGDABCB}~GHEDA@@@C@CB@A@@B@@A@@A@@A@@B@B@@A@@@AA@AAA@EAAAAAA@CBA@@A@@@A@@@@@A@@@A@@A@AA@@A@@@@@@@@A@AAA@@AB@@@@A@@AAA@@AAAACAA@BAAAA@@@CA@@@A@AA@@BC@@AA@@@ABA@AAAAA@CB@@A@A@C@A@AA@@DF@B@@@BA@@BC@A@@BAA@B@@A@@@A@A@@B@B@@@@@@@BAB@F@@A@AD@@AB@@@@@B@@@@C@ABA@A@A@AACB@@@BA@@@ABA@@B@B@BBB@B@BBBAB@@@@ABA@@@@@@D@D@B@@@BA@@BB@@@@@@BBB@@@B@D@BBD@BA@@BAB@@@B@BD@@@@@@B@B@BAB@D@B@@@B@B@B@B@B@@BB@@@BAD@@ABA@@@@B@B@BBBBF@B@@B@DBB@@BBA@@BBBBBB@B@BBBBBDB@@@B@D@@BBBB@@@@BD@@@@B@B@B@@@FD@B@@B@@AB@B@B@DBB@B@BA@@B@FBDBD@B@BBDAHCDIHG\\IHAF@JDFFDH@FAL@J@HBHLHLAXHZDXDTDB@B@BBB@@@@@ABBB@B@BBB@@@B@BBB@DB@@BBB@@@DA@@@@B@B@@@BBB@@@B@B@B@B@@BD@@@B@@AB@@BBBB@@@B@BBB@@F@@@@B@@@B@@B@@@BBA@@B@@BB@@B@BBBBAD@B@BBB@BAB@@@BBBABAD@BFLPF\\HDDDJ@J@HHB\\CFCHEJ@FDBB@@BA@@B@BB@B@D@B@@BB@@BBB@D@@BD@B@@BBA@B@BB@@D@@BBB@@@BB@BBBD@B@DB@@D@@@@@@BB@B@@@DBB@@@@@@A@ABAB@AA@AAAA@AA@CA@@A@A@@AC@ABA@@@@BBB@D@D@B@D@@@B@B@DAB@B@BBB@D@@@BBBBB@F@H@B@@@@B@@DBB@@@BBB@@@@@@B@@BBB@@BBB@B@@BAD@B@F@DB@@@B@BBBB@@@@@B@BA@@BA@@B@@@B@@A@@D@@A@@BA@@B@@AB@B@@@B@@@FA@AB@D@@@BBB@B@BBB@BBDB@@A@FDDFB@BBBBDDD@B@B@B@BC@@BA@ABCBC@ADCBCBC@A@A@@B@@AB@@A@C@@@ACA@AAA@@A@A@@AA@AAA@@A@@AA@AA@@CA@@A@A@A@@@A@@BA@@B@B@@E@@BA@@@AB@DAB@B@D@B@@AB@DAB@@@@A@@@ABBB@D@B@@@@A@@@C@A@AAC@@@ABA@AB@@A@@NCFCHAB@BA@@H@@@@AAAB@@AB@@BB@B@BA@@@AB@@AB@BA@@@A@@@@BABA@AB@B@B@BBB@B@B@D@@@BBBB@@BBDB@@@B@@@@@BFF@@@@B@B@B@DAB@B@DB@B@BA@BB@BB@BB@@BBB@BB@B@@B@B@BBD@@@@BA@@@@BDBBB@@B@FDB@DAHE@A@@B@B@B@@@@@@@@@B@DBB@@ABA@ABAB@@@B@DB@@@BB@@A@A@@@@BA@@AA@A@@@A@@BBB@BBFA@@B@@B@B@@DAB@D@@@@A@A@@BABA@@B@@A@@@A@@BA@@@A@@@AA@@A@AA@@@A@@AC@C@A@@A@AA@@@A@@@A@A@@AA@AC@ABA@@BAB@B@@@@AB@BABAB@BABAB@@@BA@BB@@B@BB@F@@@@@@A@@DC@@B@@BB@B@B@B@@B@@B@@CB@B@@AD@HC@@DBBB@BBB@@@@BB@@@BB@B@B@@@B@BA@@B@@A@A@@BAB@AE@AAC@CBIBA@ABI@AFI@@DG@IBEBGK@BEBEEK@CAG@CBC@@BC@EB@DABAB@BCBCB@BABBD@DBDDBBB@D@DADADAHCD@DBDBB@@AB@B@@A@CACA@AABADCFAHA@A@AB@F@B@FBB@D@B@B@DBD@DBBDBDBBDBBBDAD@BBD@BBBBDBD@B@DFBBH@CG@@BKAA@IA@EGCA@ABC@CCE@ACKEG@CCICKAOCI@@@G@@AI@@A@@AAE@@B@D@D@@G@@AIB@B@@@@@@A@@@A@A@A@AAA@AA@A@EAA@@@AGB@B@B@B@@@AG@A@@DAB@@AB@@ABA@A@AB@B@BBB@BN@@@E@@B@@@BF@@BB@A@@@A@C@@B@AI@@B@@@DBB@BAB@B@@B@@DBB@@@@@@ABA@@@@A@@A@@@@@@@AH@@@G@@@H@@@G@@ADA@A@A@A@A@@CAA@AA@@AE@@@BABA@CAAAA@@@CA@@DGBBBAHDB@@@AC@@BA@@FAB@@@@A@A@@A@AA@@@A@C@A@AA@G@@@AA@@DEFE"]],encodeOffsets:[[[123675,42210]],[[123939,41890]]]}},{type:"Feature",id:"211421",properties:{name:"绥中县",cp:[120.344311,40.32558],childNum:1},geometry:{type:"Polygon",coordinates:["@@AF@@@@A@@@@@@@BB@@@@@@@@@@@BA@ADADAD@@A@@@@@A@A@@@@@@@A@@@@BB@A@@@@@@@@@@B@@@@@@@@@B@@@@B@@@@@B@@@@@@@@BAB@@@@@@@@A@@B@@@@A@@@@@A@@@@@@BA@A@@@@@A@A@@@@B@@B@@@@@@@@@@B@@@@@BB@@B@@@@@@@@@@@@@@@B@@@B@@B@@@@@@@@@@@B@@@@@@@@B@@B@@@@BB@@@@@@B@@@@@@BB@@@@@@@B@@B@@B@@B@@@@@@@@@@@B@@@@B@@@@@B@@B@@@@@@B@@@@A@@@@@@@@@@@@@@@A@@A@@@@@@@@@@@B@@@BB@@@@@@B@@B@@@@@@@B@A@@B@@@@@@@B@@@@@@@@@B@@A@@@@@@@@@@@@B@@ABHFDB@B@@@@@@@BA@@@A@@@AB@@A@@@A@@BA@@@ABA@A@A@@BA@@BAB@@@@@@A@@B@@A@@BA@@BA@@@@BABAB@BA@@@@BB@@@@@A@@@@B@@@@@@B@@@@@@@@B@@@@@@A@@B@@@@@@A@A@CAA@A@@@@AC@@@@BA@@A@@A@@@@@A@@@@A@@A@@@@B@@A@@BA@@@A@@@@@@@AB@@AB@@A@@BAB@@@@@A@A@@@ABA@@@@A@@@@BA@@@AB@@AA@@@@@@AA@@@@@@@@A@@AAA@A@AA@A@A@@@A@@@@@@@@A@@@@B@GCC@@@@B@@@@@@@@A@@@A@@@@@@AA@@@@@A@@@@@BA@@EAA@@@@B@@AB@@@@@@@@@@@@@@@@@A@@@@A@@@@@@@A@@@@@A@@B@@@@@@A@@@A@@@@@A@AA@@@@A@@@@@@@BB@@@B@@@@A@@@@BA@@BA@@@AB@@@B@@A@@@@AB@@@A@@@@@@AA@@@@B@@@@@@@@@@@@A@@@@@A@@@@@BB@@@@@@@@@@@@@@BBB@@@@@@@@@A@@@@@A@@@@@@@@@@@A@@B@@@@@@@@A@@@@@A@@B@@C@@@A@A@A@AA@@AAAD@B@@ADA@@@A@@@A@A@@@@B@@B@BB@@@@B@B@@B@B@BABA@CDC@ABAD@BAB@@BDBDDDBF@DLHBB@BG@EDEBEDEDSNEFGDCDADBDFHB@FFJNFFDFDDADBDBDFDDD@D@JADCFCDCBBFBD@DADAFCFAF@LAL@LADAHCJAN@BABMA@@A@AAAA@A@A@@AA@AA@@A@@@BA@@@AB@@AB@@@@@B@B@@@B@@@@@@K@KA@@@@@@@AB@@@@A@@@@@A@@@@BA@@@@@A@@AAA@@AAAA@@A@@A@A@@@ABA@ABA@@BA@@B@@@@A@AB@@@@@B@@@@@BA@@@@B@@AB@@@@ABIDIBGFMLQN@B@@@@@@@B@@B@@BB@@B@@@BA@@B@@A@@B@B@@@BA@@B@B@@AB@B@@AB@@AB@@AB@@@@A@@@@@A@C@A@@AA@@@A@@@A@@AA@@A@@A@@@A@@@@A@@@@A@A@@AA@@@A@@@@@A@@@@A@AA@@A@@A@@@@@A@A@A@AA@@AAAAA@@@A@@@A@A@@BA@@@A@A@AA@B@@A@@@@@@B@B@@@@@@AA@@AAA@@@AA@@@@@A@@A@@@A@@@@@@AA@@A@@@@@A@A@@@@EAE@ABABAD@@B@BBB@@@@B@@@BBB@B@@B@@B@@BB@@@@@B@@@B@@@@@BB@@B@@@B@@@B@B@B@@@B@@@BB@@BB@@DBB@B@B@@B@@@B@B@@@B@@BBB@@BB@@@BA@@@@@AB@@@B@BB@@@@@B@BB@@B@@BB@@BB@@@B@@@@B@@@B@@ABAB@@@B@@@@@B@@BBB@B@@@BBB@@B@@@@BB@@BBB@@@BB@@BB@@@@@B@@AB@BA@AB@@@@@BA@@@@@@@@B@@@@@B@@AD@B@D@@@B@@@@@B@@@BA@@@ABA@@@@BA@@B@@@B@@A@@B@@A@A@A@@@A@@BA@A@@BA@AB@@A@A@@BA@A@A@@@@BA@@B@@@@@@@@@BB@@@@BA@@@@B@@C@A@@@A@@@A@@BA@ADAHAFGPEFCFGFIFCDCFAD@F@H@DDJBF@HEFEFGJEJAFDNDHBNHNJN@J@FEFAH@DAFJDD@J@NBP@N@HAB@V@P@Z@L@P@JBL@F@RDPBL@J@FALCJGJMNMFAJEP@T@VDFBHD@@XPRHHBFBD@fDfALBJFNF^VPRDFDP@DDZBPHPJPPZJHFDVDN@NBFBDFHHJJTFPDP@RCFAFCD@XLPHTLTLNBRCV@VHJHBJCHCFEFADIJGJEHENBHFHHFNFLDP@F@B@@BB@B@B@B@B@@@B@@ADA@@@@BA@@AABC@@BAB@@AB@@A@A@@B@@C@@@E@CBA@AB@B@D@B@D@PDDBD@D@DAFADCDAFEHEDEDEDGBEDCBCB@BABAD@@A@@@@@AA@@AAA@A@ABCDGBCDCBCDAB@B@B@B@B@BBDBDFDDBFB@BBB@D@HBD@H@DBB@BBB@BBBDB@B@B@B@BABADGFEBC@@@A@AA@AAICGCGECCCCAEAC@E@IDGFI@@FGFAJARCLAB@J@DABADC@CDCBAHCLETKJEFEDA@A@CACAAIGACAC@EBCBCDAHAFA@A@@BABEBIBKBEBC@EAE@ACG@C@CBABCDAVEHAPEJCRIPIBCDC@GCKAE@C@E@ADCFGBIBGBCBADADADA@CDKBEFETPRJHDHBJ@NCHCDCJAD@LBLBbE@EMOCIAC@KEACAIAEBACCABCBCLCN@FADEFGFEHALAB@L@HBD@FCHCDCDEHIB@A@BCBC@EDCDCDEBGBEDAHADAFADEDADCFADAJ@FDHHDDZFTBBS@QAKMCKKCICQGICKG[KSCEMOQMQG]CwDK@gBGAQA[GaICgcQYaIQiGEAwkUA]DUBKB_GQYGIAcGMCUAcCQCQECACACCCCEKAIAG@I@KAE@CEGGGKIMIGEIAKAM@I@I@mDG@I@IAA@"],encodeOffsets:[[122731,40949]]}},{type:"Feature",id:"211481",properties:{name:"兴城市",cp:[120.756479,40.609731],childNum:6},geometry:{type:"MultiPolygon",coordinates:[["@@AAABAB@@@B@@BB@AB@B@BB@@BAB@BA@@B@@@@A@@@@A@A@@@AA@@A@@@AB@@"],["@@A@A@AA@@AB@@@B@@@@A@@@@@@@@BB@@@@@@B@@@@@@@B@@B@@BB@B@@B@@@BB@BA@@@AB@@AAA@@AA@@@@@A@@BA@@AA@@@B"],["@@ABABABBB@BFFD@B@D@@@B@@AB@@AAAGGA@A@AA"],["@@AA@CAAA@CAC@AB@B@BBJBDBBBB@@@@B@@@B@BA@A@@BA@G"],["@@ECIASBK@G@EAEGGIEEGCEBIBEFADAH@FBFFDBDBFBDDBH@H@F@BBBDABADAD@D@J@BBBD@DCFAD@FBHDBB@DA@CBCB@F@DBBD@D@BCBC@AD@B@BBB@B@DEDCDCD@F@B@BABEBCAG@EBGBGAGCC"],["@@CBEBCDCBCFEBCBGBCBAFAHCFCDCD@FADADB@A@GJCFCDGDEDC@GAK@A@KBGBEFEHCFEBM@KDADADDBBDFAJBDBFB@LBDDJNP@FaFKAKAC@IBCDGDMDI@GAGCQISOEFAFCL@DCBCBCBABADAHAJEHCD@B@F@DBFDL@HCDADOJQJIDOFGBUFCBADAB@D@DDH@BBF@FADAFALAJAFAB@@@BEBGBCBADAD@FBDBDJHBBBD@D@BCBEFIFSLKFGDABCD@DCDABCBI@A@KBQDIBEBEH@@EJCH@J@FBDBFDDDDHFHDJDBBB@@B@B@@ADEFCHABABA@A@A@A@ACAAA@AAA@CAG@C@GAC@A@AAA@AECCCECAAAA@A@A@A@A@CBADCDADCHAD@B@BBB@BB@@B@@@@@BC@ABABA@ADCDAFCHCFCFGFEFCBCDEBCBC@C@CAOCC@A@C@A@A@@BAB@D@F@@@DA@@@@B@BA@@BA@AB@@ADBB@@AB@@@@CB@BA@B@BB@BB@@@BB@@@BD@@BB@@BBBB@@B@BBBBBH@B@B@BABA@AB@AAA@AABEB@@@B@B@@BBB@BB@B@@@@@BBB@BBDFBF@HAJCJCFCFCLEJAFAJDJHL@@PPFJHHFFFDLDPBXAF@J@LCH@F@DFHFPHVFF@FBFADAFEFGHEL@L@TFFDBH@ZALDHHJ@@FLHLBJDDDLBH@H@D@H@FA@ADC@@@A@A@@DB@@BA@@@BB@@@@@@A@@@A@A@@BABCD@@AB@BA@@@ABBB@BD@BB@@B@@@B@B@BB@@BAB@BDBB@DB@@@@HBF@FFFJHJLLELALALDPDLBHFDDTFNBV@JCLGJIJGJIFKDI@IAGBELG@@R@P@LCJGFKJILID@@@H@DBF@BAD@B@@@B@B@B@@@@A@A@@@ABC@@B@@@DA@@A@@@@@BA@@DA@AB@@@B@@@BB@D@@BBD@BBB@@@D@B@@@D@@BB@B@@BBB@@@BBBB@B@@B@B@@BD@B@@BBB@BB@@B@B@DBD@BBD@B@@AB@B@BA@BD@B@@@BBB@B@@B@@@BD@@BA@@B@@AB@@A@@BA@@BB@@B@B@B@@@@@B@@@B@B@@B@B@B@BAB@B@BA@@B@@BB@@@DA@@BCB@@A@ABABA@@@A@@B@@@D@D@BAB@@AB@D@@AB@@@BB@B@@B@@BAB@B@D@@@@B@BBDBF@FB@@@@BAB@@@B@DBB@DBB@F@B@B@PCRCB@LAVCRCJ@DAZFPBPBJAFIFIJIPAPDNBL@d@RDL@F@NCPGFEDCBADADAFCDA@@B@H@B@@@B@B@@@B@B@@@@BB@B@HAB@B@D@BABA@@B@@@@AC@AA@@AAAA@A@@@ABABA@AB@FA@@@@@@A@A@AA@@@@B@DA@@@@@@@@B@BAB@B@@@@ACA@@DC@@AAAAABAA@AA@@@A@A@@@@@A@@@@@@@@A@@AA@@AA@@A@@@@A@@@@@A@@BA@@@@@A@@@@@@@A@@@A@@A@@A@@@A@@@A@@@@AA@@@A@@@A@@A@@A@@AA@@AA@@AA@@A@@AA@AA@AAAAA@A@@@@@A@A@C@@@A@@@A@@@A@@B@D@B@B@@@BG@@CCA@AAA@A@AAA@@ABAB@BAD@B@DAB@D@BAB@D@B@B@D@B@B@B@B@B@D@B@B@B@BBF@B@BAB@BAB@B@D@B@B@BAB@B@BAB@B@B@B@B@@AB@B@@@B@B@@@B@@A@@B@@@@@B@BA@@@@@@@A@@@@@A@@@A@@@@@A@@@A@@BA@@@A@@@@@A@@@@@A@@@@@AB@B@B@@@B@BAB@B@@AB@BAB@BA@@@ABA@ABA@ABA@@BA@A@@BA@ABA@AB@@ABADBB@DBB@@@B@B@JBDCDI@G@GEGEICGAS@AAO@KCMEKMMQKOKSKcSUIMGaOSGGQ@MCQYMOCKACBO_KSGMCEECIC_BW@OCMEKECGAEBQ@IAE@CBIBUCKQSUIAE@E@CDCJMBKBOCOEIGGKIOIWKoOQEKIIMISAUFYSAYECCGGECI@"]],encodeOffsets:[[[123664,41442]],[[123669,41413]],[[123724,41503]],[[123518,41370]],[[123731,41462]],[[123380,41236]]]}},{type:"Feature",id:"211422",properties:{name:"建昌县",cp:[119.837124,40.824367],childNum:1},geometry:{type:"Polygon",coordinates:["@@OCSEIIGGCEEAMAM@UCECIGOYIOGOAOCY@CCOCEOQ]UMEIEKAeBeCC@EAGAQGWO@@GCEAUCS@O@IFEBMNINIHKDEBI@K@OAQCE@K@IAO@K@Y@O@U@A@GBM@O@MAI@C@IC@HADAHDFFDHHDJBD@FGLEJABCFIDI@GBGFCDAHAFCHGJIFGDKHAJAFBJFFNDNDFFBF@FBF@D@BAFADA@IHMFQDIBC@A@CBABA@A@A@AB@BA@@@A@A@ACCCA@@AA@AAE@@@AAC@A@ABA@AB@@A@A@A@CBC@@@@B@@@B@@B@@@@B@B@@A@@@@AC@@AAA@@A@@@@@@B@@@B@@A@ABA@@@@A@@@@@@@A@@@AA@AA@@@AC@@@@A@AAAAA@AA@EC@@AA@A@@@A@@@AA@A@AABA@@AAEACAG@E@G@A@MBK@MDKDKBK@MBGAKCI@KAK@KAEBKBGB@@E@IAI@I@I@IAA@GAGACAEAIAGAE@GBG@CAE@IAK@KBOBQDQBMHMDMFOJWPQNKHCDALBHBDFDJFHHDHBHHLFFB@B@BB@@@@BB@@@B@BB@BBBB@@@@@B@B@BDBAD@@BBB@@BDB@@D@@@D@@@BAB@BA@@BADA@ABA@@B@@ABA@A@A@AAAB@D@FADAD@@@B@@ABB@@B@BDB@BBDBD@D@FB@@@B@B@@B@@@DA@@B@BBB@@@@B@@@B@@AB@BB@DB@@BBBBA@AAA@CB@@DBBBBB@@BBBBB@@DLFHFJJFDHFJNJPLLHFFFHDHDJHJDLBJBH@H@HCH@HBJFLDFDFDJHHFBDHLDJ@N@HDLHLJHHJHDJDHJJFBDC@EB@B@@@@BDBBB@B@B@DDFD@B@@@B@@BBBAB@B@BADBHA@@DCBAB@@@BBB@D@DBD@BAD@JDDFD@DBDDFDFH@JDLFLDLLNFDFDDFBBDHAFCHAJ@F@HDDLBJCRCLINIJEHCDAH@HDJFHDHDFDLLBBB@@BABB@DBB@BB@@@B@@B@B@DADAFCB@FAB@DAF@@A@@@@B@FAFABAHAB@BA@@BC@A@AB@@A@@DEBA@@@@BABA@AB@@@B@D@@@DA@@@@BA@@@A@AB@@@@A@@BAA@@@@@@@@@B@@A@@@A@AB@B@@@@@@@B@B@@A@A@ABA@A@@AABABA@C@@BA@A@@AAA@@@@A@A@@DC@AB@B@@A@A@@@A@@B@BA@@@ABA@@B@BA@@@AB@DAB@@@B@D@BBB@B@BA@AB@D@B@B@@@BB@B@B@@B@@@@BBB@BBB@BB@BB@B@B@B@B@@@@@BBD@BAB@BB@DD@@@BH@BB@@BDBB@@@@@@@@@@BBBBDBBB@B@@@D@@@@@B@@BB@BB@BBB@@BB@BB@B@B@@BB@B@@@D@D@@CAC@CAA@I@C@C@@@AA@@A@BBAB@BAB@F@BBB@@@B@B@@@BBBB@D@B@@@BB@B@@BB@@B@B@@@B@@@BB@B@BAB@@BD@@@B@B@BBBBDBBBB@B@@BD@D@@BB@BB@DBB@DAB@D@B@DBD@BAD@B@BBDBB@BB@@BB@B@@@@DB@@B@B@BBDB@@BA@@B@BC@@@A@A@AB@BC@AB@JAF@B@B@BBD@D@@@B@B@BAHABA@AB@@@@@BCAABABA@@@@B@D@B@@AB@BA@A@AB@@@B@DDBDAB@@@D@B@@FD@@@@B@BAB@BBBAB@@@BAB@D@BBBBBDB@BBBD@B@B@B@@@@@B@@BB@B@D@B@@@@BBD@@@B@@@B@D@@@BBB@DBBAB@BB@BDB@B@@@B@@@B@@@@@B@@@B@@BB@BBB@@B@@BBBB@B@@BD@@BB@A@@BAB@BB@@B@BB@AB@B@@@B@@@B@B@@ABAB@@ABAB@@@B@@BDBB@@@D@@@B@BA@@D@@AB@BA@@B@D@B@B@@@@@B@B@@BB@BAB@@@B@@@@@B@@@@@B@@AB@@@B@@@B@@@B@@AB@BB@BD@@BBB@B@B@BB@@B@@@B@@BB@FBB@B@@E@A@A@@@A@@@ABAB@B@DBD@D@B@B@BA@@B@D@@@BBB@@AB@@@BADA@@@AB@B@D@BAB@@AHC@A@@BABA@A@@B@@A@@@@@A@A@@BCB@@@D@@@B@@@B@BA@AB@B@BBD@B@B@B@B@BBDB@@D@FADBB@D@@@@AAA@@AA@AB@@@@@BDDB@@@@DABA@@D@D@@ABA@@FB@@@@@B@DAD@BFBBJFNHH@@HBJBFBHFDJHHHDF@D@HCJGDKDGDCDCFAF@DBDBLFDFDDHFBBFDJJBDDFDH@@BB@BB@@@@B@@DD@B@@BB@BABABB@@B@@BDAB@@B@@B@@@BBD@F@BB@@@DBBB@@B@B@B@@BBB@@BB@B@@@@AB@BAB@@@@B@@BBB@@BBB@@@DB@BD@B@BB@@@BABBBBB@@@B@BBB@@BBB@B@@BB@@B@@@@C@AB@@@BBBA@A@@@AD@@@@A@A@BBAB@@AB@@BB@B@B@@@@ABA@BDABB@@BBB@@@@AB@@AB@B@BA@AB@@BBAB@@@B@BBB@@@B@@@BBB@BCD@@BB@@CD@B@@A@A@@@A@A@@BABAB@@A@A@A@@@@B@B@B@B@@@B@B@@@B@@@@AB@@@BAD@B@@AB@@@BAB@BBB@@@B@@@@@BA@@@A@@B@@@@EBC@G@@@@B@@@BA@AB@B@@@B@BB@@BB@BBDD@@@BBD@@@@B@@@D@B@B@@DB@BBB@B@@@B@B@B@BB@@BB@@BBBAB@B@@A@@@@BBB@D@@@B@B@@@@BDB@@@BB@@BBBDBB@D@F@H@JBBBBBB@@@B@@B@@HFB@DAB@B@@AB@BB@@@BBBBBB@@B@B@BBB@B@DDAD@D@D@D@@@BA@@@@B@@@B@B@BBDA@B@@BA@@BA@ABAB@@@@ABABA@AB@BAB@DAD@FADAB@BAB@@@B@@BB@B@B@@AB@DBB@B@@@BA@@B@BBB@BC@@C@C@CB@@A@@@@A@@@@@@B@@@B@@@@A@@@@@@BB@@@@B@@@@AB@@@@@@BBABB@@@@@C@ABC@@B@@@@B@@@@B@@@B@@@@CBC@@@@@AA@@@BAB@@@@@@AB@@@AA@@A@@@@A@@C@AAAA@ACAA@@@@AA@@A@@BAB@CA@@AA@@BA@@AA@@@@@@@ABAB@@@BABBB@D@@@BABB@@DA@@BEHIFIHMFMDIDIDI@ADIBEFGH@HFHHJJJJFFFBJ@FKDKDKDIBE@AB@B@B@B@DBHAB@@@BC@A@A@ADC@ABABAB@@C@@A@@A@A@AA@@ABA@A@ADCAAAA@AAA@@BA@@@A@@@AB@@@@@AAA@A@AA@@AAEAC@@AC@@A@@BABA@@@C@A@@AAACA@A@@A@@@CA@@AA@A@AAC@A@C@A@ABA@@@AAA@E@A@@A@@@A@A@A@@B@BABC@@CAA@CAA@@AACA@C@@A@@AA@C@A@@BA@A@@AA@ABA@ABA@@@@@A@@@A@@B@@A@ABA@@DA@@@AB@BAD@JCB@B@DB@@B@BAB@D@B@@@B@B@@@B@B@BBD@@BDB@@@B@BBBBBB@@@@BBB@@BB@@@B@@@B@B@D@B@@BBBB@@@@@B@@B@BBBD@@@B@@@D@@BBBBB@@@AB@@BBDBDDBB@@BD@@B@@@F@@AB@@@D@BAB@B@@@BBBAB@BADA@@@@DCH@CKBGFIBGDKDM@ICQAQ@EDEJCJCFEFGDIDM@@FODGFCVKFCNEFEB@DE@E@GEEGEAAIKIGEE@EAE@G@@A@@CAAACA@AB@@AAA@A@@@A@@@AAC@@AAABA@@B@@ABA@@DCBA@AB@B@@@B@@@@@@@AA@@B@@AA@@CB@B@@@D@BCB@@E@G@C@GAGCKCCAIGKEK@@GICGBK@YAGECSEK@K@GFEHEFCBEBEAE@UEOGGECEE@G@KDI@E@WBOAKCECEEGGEIOO@@GKCIBIBEFIDKDEDEDIBI@GAECEAAA@AA@@@@A@A@@AAA@AA@A@@@A@AFBBB@BBA@@BABABA@A@G@AAAA@A@AA@AA@AA@@AC@@A@@AA@@A@@AAAA@@@A@A@A@A@A@@AA@E@O@KCMEGEEGAGFMFGHIJIBCFEDEDGAIIGUGU@QDMASKSKOGWKC@EDEBQDO@"],encodeOffsets:[[122861,41515]]}}],UTF8Encoding:!0}):void D("ECharts Map is not loaded"):void D("ECharts is not Loaded")})
