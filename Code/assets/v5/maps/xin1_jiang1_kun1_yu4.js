!function(A,B){"function"==typeof define&&define.amd?define(["exports","echarts"],B):"object"==typeof exports&&"string"!=typeof exports.nodeName?B(exports,require("echarts")):B({},A.echarts)}(this,function(A,B){var D=function(A){"undefined"!=typeof console&&console&&console.error&&console.error(A)}
return B?B.registerMap?void B.registerMap("昆玉",{type:"FeatureCollection",features:[{type:"Feature",id:"659009",properties:{name:"昆玉市",cp:[79.291083,37.209642],childNum:19},geometry:{type:"MultiPolygon",coordinates:[["@@HAJBn@T@CQCMAKAIAIA@AAEAA@CAC@AA@@A@E@C@E@C@EBCA@@@AC@A@AC@A@AAC@ACABADABCDAD@@BFABC@AAABAB@BAAC@@BBDABABA@@@AAA@AD@FCD@BBD@D@BS@A@A@cBU@CBi@ADuBaBwBmBokLMHOJUHZoPGBEc­TTdHNMFZC¤Al@^@DGúªBB"],["@@dJXHČ@çA@s@@CCUBBBBDCBABBBADB@@BCDBJGBAAA@AEB@BCAC@ABCDAAECA@CEBE@HE@CFAB@@EBABCAEAA@AA@ABAC@AACCBDDED@@ADB@BBAB@BC@CBBDA@C@AAC@E@GBCAA@@B@B@ACBSDD¢SCQLaX[TwiSCEBIAEEMAMAMEIBCEKCA@AAA@EAAAEBAAGB@AGCWBCD@FTJAB@@@FHJCJBJDRB@NJ@JTFST@l@`J¦`"],["@@BA@@@@B@@@FABACGBCA@BAAA@@CEBA@@BAGECE@@CBEBGDBBDDBBBBCBADA@ABA@A@A@EB@@C@DDBDAB@@DD@BB@@BDADAD@BAB@ABBBB@@@@@B@@@B@DCBD"],["@@AV@@Kr@B@"],["@@@A@@BACAA@ACC@AB@B@@BBBBB@BD@@DA"],["@@BDBHH@@DFB@BDB@BF@@CL@@A@A@@@A@A@@@AB@A@BA@@@A@@@@@@BAD@B@@@DABABABA@@@CAA@A@@AA@AAAA@@ACBAI@@@CAGAKC@C@OBCDC@A@@DABBH@@@DIB@BA@@@ABABDHBABN"],["@@MGGB@BBHD@FB@B@DNCCA@E"],["@@DCDAFNF@DAFABJEBEBBHTAHMBBAJD@@FHBFADACCDA@BBBD@DADDJBB@B@@AB@@A@C@@AA@@E@CCA@A@CEAC@AAA@A@CE@AE@@ACA@@E@C@AMJ@@BA@AA@@@BAB@B@@@@A@@AABG@AAEEAAGGDEEA@@E@@@AA@AAA@@AB@A@A@AB@@AAA@@@B@@AA@A@@@AB@@@@@C@@@A@@@@A@@@@A@A@@@A@@A@@H@B@DBF@FELEHCDGF@B@B@DBD@B@DBHBD@@FE"],["@@@BAJADBBBJDRBBBB@@@DBD@F@FA@ABBBBDBBDDBBFHDDA@BDBBBBBBBF@B@@FHB@FFLHF@HFJDB@BAF@B@D@FBBADBBAD@HACQAWASC[ASCkAUAS@KAGAO@@QKGEA@A@AAA@@AA@AAABA@AA@@CAA@@ACAcQECEAIEAA@@ABA@@@BHBBBJ@DBNBBFHB@FDLF@D@FADAD@D@DCD@DADCB@@CDABCDABADA@BJBFBDBHBF"],["@@AHD@BA@@D@@@@A@@@@C@@AA@@AA@@@"],["@@ABAD@@B@B@BA@AAA"],["@@@@D@AA@@AC@@@B@@@B@@@B"],["@@ACAB@DDA"],["@@CA@BD@@@"],["@@B@@@@@@AA@@@@B@@"],["@@@@@@@@@@"],["@@@@BB@@BB@BD@DA@@@A@AAEACA@ACA@@B@B@B@DAB@@@B"],["@@FABCBGBKCIEKKBIB@B@BBHE@@BA@C@BHABB@D@@J@HAF@B@F@@ABB@@@@@@@BJBHBR@B@B@FBF@JKBG@DFEB@@A@@@DJ@@@@@B@@@BB@B@@@BB@@B@@@B@@@@@B@@DFLDD@@JAHA@ABAN@AMAOE@@E@K@O@CCQBKCGAG"],["@@GBD^H@BEAC@C@A@@@A@@A@@@@@@A@@AA@@@C@@B@@@@@@A@@@C@@A@"]],encodeOffsets:[[[81241,38315]],[[80298,38658]],[[81611,38276]],[[81699,38439]],[[81623,38278]],[[81644,38294]],[[81691,38180]],[[81486,38277]],[[81549,38391]],[[81493,38226]],[[81499,38218]],[[82347,37150]],[[82757,37165]],[[82954,37131]],[[82976,37151]],[[83226,37010]],[[82754,37173]],[[82992,37145]],[[83281,37280]]]}}],UTF8Encoding:!0}):void D("ECharts Map is not loaded"):void D("ECharts is not Loaded")})
