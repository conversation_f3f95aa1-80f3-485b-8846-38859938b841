!function(A,B){"function"==typeof define&&define.amd?define(["exports","echarts"],B):"object"==typeof exports&&"string"!=typeof exports.nodeName?B(exports,require("echarts")):B({},A.echarts)}(this,function(A,B){var C=function(A){"undefined"!=typeof console&&console&&console.error&&console.error(A)}
return B?B.registerMap?void B.registerMap("东莞",{type:"FeatureCollection",features:[{type:"Feature",id:"441900",properties:{name:"东莞市",cp:[113.751799,23.020673],childNum:1},geometry:{type:"Polygon",coordinates:["@@CDSVY\\MTELGL@H@B@PDRDN@F@H@HGNMPObIXABGTGVAFABEJGLIX@DCLAJAR@@A@@BAL@FAN@J@F@B@HBHFLHHDHNJ@@DDBBDF@BBDBB@DBBFNBDDB@@HFF@F@LD@@D@BB@@B@^FFD@@FB@@HDFDDBDDBBBD@@BBJFL@N@JAJBF@H@DBBDBB@BBBA@@@ABAB@@@B@@BB@@FDBB@@B@BB@@BB@@@@@@B@@@@@@@@BBA@@@@@@@@@@@@B@@@BA@@B@B@BBB@@@@@B@D@BA@@@AAC@EB@@@JDHFH@HB@@H@LDHFHFBBBBHF@@HDDBFDJFDBHBB@DBJBF@F@D@DAFCLGFEHEF@J@LBD@JBF@FAJCD@HAPBJBNAHALCBAB@FABAHCDAJAFC@@BA@ND@@FB@B@DABE@A@E@@@A@@D@B@B@D@B@@AD@B@@@BAD@B@B@D@BBDD@@BBBB@@BBBBBBBBBBB@B@BABABABABAFCDAB@DAB@F@DA@C@CBBJFJ@B@F@DAFAFAJCD@B@LCHAFBLDD@@@@@BBB@F@DBB@B@B@BBB@@@DBB@B@@@@@B@D@D@B@@@D@B@HEDA@@DABABADADCBADABCLODAHCB@DADADABCB@DAF@BAJ@D@F@BBJBLHDBHFDBNHNFB@PBNC@@DAHA@@H@D@FADCBG@ABE@QBCDI@@DEBGBADCBABAJGB@BARGFCDAF@FB@G@EB@DA@@@@DC@@@@@@B@LIBCBAAABADCBAB@DB@A@@@A@@B@BCBE@ACBEDEDCBA@@BA@BAAA@CAC@AACACACAA@ABAAAA@AADC@@AAA@@AEAC@@@BCA@@AAA@AA@AA@@@@@A@@@@@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@@@@BA@@@@@@@@@@@@@@@@@@@@@A@@DA@@@@@@@@@@@@@@B@@A@@@@@@@@@@@@B@@@@@@@@@@@@@@@@@@BB@@@@@@@BB@@@DBB@@BEDCBCBAB@FAD@@@@@B@D@@@BAB@B@BA@A@A@@@@B@B@@B@@F@B@@@BA@AAAFC@@@BB@FDB@@@BAB@@@B@B@@B@DBB@@D@@@BBDBB@HF@BHLBB@@B@@A@@BEB@@@B@BAHCDDBAB@B@@BDA@A@@AAA@@A@@D@@A@@D@F@@BFAFB@@BADBB@BAB@@@DE@A@AD@@@BA@@B@B@@@BCAA@@@@BAAA@A@@B@BAAC@@BA@@@@@@@@@@@A@@@@@@@@@@@@@AAA@A@@@@@@@ABEA@CA@@@@@@C@@@A@@ACCC@@AAACA@@@A@@I@@@@AB@@@@AB@D@@@B@@@@@@ABA@@@@@@@ABC@@BA@A@AA@@@A@A@@@@@@@@@AA@C@A@ABC@@BI@C@A@@@A@@@ABABABADABAB@@CBADADA@@BABA@ABAB@DAB@B@H@@AB@@ADA@@B@DAB@@@FB@A@AFCBC@@@@@A@@FBB@B@@@@@B@@@@@@B@@@@@@@@B@@@@@@@@@@@B@@@@@@@@@DA@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@@@A@@@@@@@@@A@@@@@@@@B@@A@@@@@@@@@@@@@A@@@@@@@A@@@@@@@@@@@@@A@@@@@@@@@AA@@@@@@@@@@@AA@@@@@A@@@A@@@@@@@@@@@A@@@@@@@@@@@@@A@@@@@@A@@@@A@@@@@@@@A@@@@@@@@A@@A@@@@@@@@@@@@@@A@@A@@AA@@A@C@@AA@AA@@@A@A@CA@@@CA@@AA@A@ABABA@CB@B@BA@A@ABAAA@AAAC@@AAAA@@@C@A@C@@AABE@ABAB@@A@A@@BC@AB@@A@A@@@GAA@CBA@@@@@@BA@@@AAA@AAB@@@@A@@@A@@C@A@A@ABAB@@AAA@@@A@ABAB@BA@@@C@A@A@C@C@A@@AAAA@@@E@@BABAAE@A@A@@@A@@AA@AAA@@@@@@AAA@AA@AA@A@CB@@CA@C@@AACA@@AAABA@CAA@@CAAAC@ACCDA@@AAACAE@AEEC@AAA@A@@@IA@@AC@@A@E@A@C@A@@ACAA@ACCA@A@@CAA@A@@ABA@CAA@A@@BAB@@@B@@@@@@BAB@B@B@@@@@BBAB@@A@A@A@@DA@@B@BA@@BA@@@AA@@@@AA@A@@@@BC@@AA@@@@@@@@A@C@@BADBBA@@B@BAB@@@@BB@F@BBBBB@@AB@@@@@B@@@@BB@@@D@@A@CAE@@B@BABA@@B@@B@@B@BAB@@A@@@AA@CA@@@@A@DG@ABB@BB@FABA@@@AAA@A@AAA@A@@B@BAB@@@BABADC@A@A@@@CAAAAA@@@AA@A@@A@@@@@AA@@AC@@@@@A@C@C@A@@@@@@@A@@@A@A@@@AB@@@BA@@@A@@@@AAAC@@A@A@@@@@ABA@A@@@A@AC@@A@A@AACC@A@A@A@@DA@@@@@A@ABE@AA@@A@@AAACB@@A@@B@@@@A@ACG@CC@A@@BC@@@AADGD@B@BA@@@A@@AA@AIE@A@@@AA@@@A@CC@@@ACABA@A@A@@@AB@BA@@AAAAA@@@C@@AAA@@AC@AACAABAB@@AA@@@CAA@E@@@@@A@@@@@ABCBAB@@BB@B@@@BCB@D@@A@@@CA@@A@@@@@@A@C@@@A@AC@@@@@@@@@A@A@@@@@@@@@@B@@@@@@@@@BBBBBAFAB@BA@@CAAAA@@A@@D@B@@AA@CAAA@@B@@BB@@@B@@A@@@DJ@@@@@@D@@@@@@@@@F@B@BA@@BC@@@@@@BB@BDF@@BD@@@B@@@@@B@BAD@@@B@BDABB@@AB@@A@@BA@A@A@@@A@@BA@BBCD@@A@@@A@ABIFCBBD@@@BAD@BADAD@@C@@@ABCB@@@B@@@DBB@@@B@@@BA@@BABA@BB@@@@@B@@@B@@@B@@BBA@@A@@A@@@@@ABA@AB@@A@@B@@A@A@@@@A@AAAA@A@@@E@@@@BAB@BA@@@CBCA@@@@@@AAA@@@A@AB@@@B@B@@CB@BCF@@C@AAAA@@@@C@AB@@EBA@AL@@@@@@@@AD@@@@AB@@AAA@@BAB@@@@CD@BDB@B@B@@AFABA@@BBD@B@@ABAAA@C@CAABBB@@@@@@@@B@@@@@@@ABCB@@@@@@A@@@A@@@@@@@@A@@A@@@@@@@@@A@@@@@@@@@@B@@@@A@@@@@@@@@@BA@@@@@@@@@@@@@A@@@@A@@@@@@@@A@@@@@@A@@@@A@@AA@@@@@@@@@@@BC@@@@@@@@@AA@@@@@@@A@AA@@@@@@A@@A@@@@AAAA@@@@A@@@@@@@@@@@@@A@CB@B@@B@@B@@@BA@ADCD@@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@@@@@A@@@@@@@@@@A@@@@A@@@@@@@@@@@@@@@A@@@@BCB@@@@@@@@@@@@@@@@@@@@@@@B@@@@@@@@@@@@@@@@@@@BB@@@@@@@@@@@@@@@@@@@@@@@B@@@@@@B@@@@@@@@@@@B@D@@@@@@@@@@@@@@@@@BA@@@@@A@@@@@@@EA@@@@@@@@@A@@@@A@@@@@@@@@@@@@@A@@@@@@@@@@AB@@@@@@@@@@@@@@A@@@@@@@@@@B@@@@@@@@AB@@@@@@A@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@@@@@@@@@@@@@A@@@@A@@@@ABE@@@@@@@AB@@@@@@@@@@@@@@@@@@@@@B@@@@@@@@@@B@@@@B@@@BB@@@@@@@@@@@@@BBB@@@@@@@@@@@@@@@@@@@@B@@@@@@@@@@@B@@@@@@@@CD@@@@@@@@@@@@@@A@@@@B@@@@@@@@@@@@@@@@@B@@@@@@A@@B@@@@@@@@@@B@@B@@@@@@@@@@@@@@B@@@@@AD@@@@@@@@AB@@@@@@A@@@@@@@@A@@A@@@@@@@@@AA@@@@@@@@@@AC@A@@@@@@A@@@@@@@@@@@@@A@@@@@@@@@@@A@@@@A@@@@@@@@@@@@@A@@@@A@@@@@@@@@@@@A@@A@@@@@@@@@@@@@@@A@A@@@@@@@@@A@@@@@@@@B@@@@@@@@A@@@@@@@@@@B@@@@@@@@@@@@@B@@@@@@C@@@@@AAA@@@@@@@@@@@@A@@@@@@@@@AA@@@@@@@@@@@@@A@@B@@@@@@@@A@@@@@@@@@@@@@@@AB@@@@@@@@@@AB@A@@@@@@@@@@@@@@@@A@@@@A@@@@@@@@@@@@@@@@@@@@@A@@@@@@@@@@@@@A@@@@@@@@@@@@A@@@@@@@A@@@@@@@@A@@@@@@A@@@@@@@@@@@@@@@@@A@@@@A@@@@A@@@@@@@@@C@@@@@@@@@@@@@AA@@@@@@@@A@E@@@@@@@@@@B@@@@@@@@A@@@@@@@@@@B@@A@A@@@@@@@@@A@@@@@@@@@@@A@@@@A@@A@@@@@@@@@@@@BA@@@@@@@@@@B@@@@@@@@@@@@AB@@A@@@@@@@@@A@@@AA@@A@@@@BA@@B@BAB@@A@ABA@A@@@@BAB@B@@@BB@BBBB@B@@@DA@A@@B@BA@@DADA@@D@@AF@B@@@BAB@B@@@BAD@B@D@D@B@@K@C@CA@@@@@B@@@BB@B@BA@@BD@B@@AB@A@@A@AA@AC@AA@B@@A@BA@@AAA@@@@@@BB@@BAA@@@@A@@@@B@@A@@@@@BC@A@@@AA@@@@BABABAD@@BD@BB@B@B@@@DBAB@B@B@BA@@@CB@@A@@BA@@@@@@@A@ABA@@@AA@@A@A@ABABBB@@AB@@AACCA@A@@@AB@@@B@@A@@AA@@@CB@@@@@@@B@@@@@@@@BB@@@@@@@@@@AB@@@@@B@@@@@@BB@BA@@@CBCB@@ABCB@D@BA@E@@@@BA@A@A@AB@@AB@@A@AB@@B@@@@B@@A@C@A@EAC@A@A@A@AAA@C@@BC@CAC@@AA@@AAB@@@BA@A@CA@BAB@BA@A@A@C@A@AH@BAB@B@@@B@@@@@@B@BB@A@ADA@@B@@@@@B@B@B@D@@@@@BAB@B@B@BB@@@@BABADAA@@A@@@@DB@@BAB@@@AB@@CBCB@@AB@@@B@BDAB@@B@@A@@@@@@B@@B@@@DB@@B@DA@BB@B@@@B@B@BA@@@@D@@@B@DA@@@BCBA@C@@@A@ABA@A@@@A@A@@@@@A@A@@@AAA@@A@@A@@B@@A@@@AA@@@A@AA@@@A@@@AA@@AA@@AB@B@@B@@@@AB@@B@@@@@D@@@@@B@A@@@C@@@@A@@@AB@@@@A@AAC@@AA@@@@B@@ABAAAB@@@B@@@BB@@@C@@@A@@@BB@BB@@BB@A@AAA@C@@@@B@B@@@CA@AAAAAD@@B@@@@BB@@@@@@@A@A@@@A@@B@@@B@@@@@@@A@@A@@@A@@@AB@B@B@@B@BB@@A@@@@@A@@@@@@B@B@@AA@BA@@B@BB@B@@ADA@@@A@@@A@@BBDFB@@BBBBB@@@@A@@@A@@A@AA@@@A@AAA@@BAB@@@A@@A@@B@@@@AB@@@@A@A@A@@B@@BB@@B@@@@D@B@@@A@AA@@@AA@@AB@@@@@@@AA@@@A@@@@@@@@@BABABA@@AA@@AACB@@@@@@BA@@AA@A@@AB@@@@@A@A@@@@A@@B@@@@A@@@BA@@@A@@A@ABABBB@@@@@@AB@@@@@A@A@@A@A@@B@BBBB@C@@@@@AA@@B@@@@@@A@@@A@@A@@ACC@@A@@ABAAC@AA@@@@@@B@@C@A@@@BCDE@@@@A@@@@@@@@E@@@@A@@@@@@@@@@@@@A@C@A@AA@@A@ABA@@A@@@@@AAAA@@@@@@A@@@@AB@@AA@@CAA@@BADC@@@ABABA@@AC@A@CA@@AAEA@@AB@A@A@A@@CA@@@@@@@@AA@@@@@@@A@@@@@@AA@@@AA@@@@@@@@A@@@@@@@A@A@@@@@@@@@@A@@A@@@@@@A@@@@@@A@@@@@@@@B@B@@@@@DC@@@@@@@@@@@@@@@@@@@A@@@@@@@@@@@A@@B@@@@@AA@@@@@@@@@@@AB@@@@@@@@@@A@@A@@@@@@A@@@@@@@@@@@A@@BA@@@@@@@A@@@@B@@@@@@A@@@A@@@@@@@A@@@AA@@AAAB@@@A@@A@@@A@@AA@A@@@@@@@@AA@@A@@@AA@@@@@@A@@@@@@@A@@@@@@@@AA@@@@AA@@@@@@@@B@@@@@@A@@@@@C@@@@@@@@@@@@A@A@@@@@@B@@@@@@AA@@A@@@@@@A@AB@@@@@A@@A@@A@@@@@A@@@@@@@@A@A@@@A@@@@@A@@@@@A@@@A@E@@@AA@@CA@A@ABCBAA@@A@@CAAA@@@@AAA@A@A@AB@@ABABA@@@A@A@@@AA@A@@@A@ABABA@@@A@AAAA@@AA@A@CAAACAACAA@ABCB@@A@ABC@C@@AC@AAA@ACCCCCAGAAAA@EACCMGMIEMO]ELEJCFIJIFSDK@G@OAQ@UKQKKNMRGHSX@@GJGHUZIJADABCBQVEFABEF"],encodeOffsets:[[116336,23353]]}}],UTF8Encoding:!0}):void C("ECharts Map is not loaded"):void C("ECharts is not Loaded")})
