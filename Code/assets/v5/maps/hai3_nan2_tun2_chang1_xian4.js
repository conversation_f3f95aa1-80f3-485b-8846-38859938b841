!function(B,A){"function"==typeof define&&define.amd?define(["exports","echarts"],A):"object"==typeof exports&&"string"!=typeof exports.nodeName?A(exports,require("echarts")):A({},B.echarts)}(this,function(B,A){var D=function(B){"undefined"!=typeof console&&console&&console.error&&console.error(B)}
return A?A.registerMap?void A.registerMap("屯昌县",{type:"FeatureCollection",features:[{type:"Feature",id:"469022",properties:{name:"屯昌县",cp:[110.103415,19.351765],childNum:1},geometry:{type:"Polygon",coordinates:["@@ABA@A@A@@A@@AACBC@C@A@CB@@A@AAA@ABA@@BA@A@@B@@AA@@A@A@A@@@A@A@A@ABAD@B@@@@ABA@A@@@@B@B@@@@@@@BB@@@@B@@@@@@CBA@@B@BAB@@A@AA@@@@@B@@ABa@GAGAG@OACF@B@@CF@F@JBJ@F@BADAB@BCFKNGHEDOFCBADC@CDKBKAWAG@GHCDCDABCFEHABABABCFCFADCDAB@BCBEFGDMBM@CC@@A@CBABCDA@@AA@CECCGIGDKLCHM@CFFFHDBH@LANDJHB@DDBCF@DBDBDBP@@@DE@GFELGF@FNFNBHDH@FELJBNAHFPFJG@GBCJDJ@ABCB@@@B@BBBD@@BBBB@@BAD@B@B@BB@BA@@BAB@@A@A@ABADEBABA@CBA@@B@B@DBD@BB@DBD@BBD@DBBBBBBBBD@B@@@B@@@B@BA@@BBDB@BBB@BAB@BC@@@AEEAA@@BA@AD@BABBBAB@BADCBABA@C@A@C@A@AAE@A@@BAF@B@@ABCBADCHIBCDCB@B@H@B@B@BBFDHH@@B@D@B@B@BBD@B@BBBBBBDD@B@B@B@@@@@@@@@@C@AA@@@@A@@@@B@BC@@@@@@B@@BBBBB@D@BBB@BB@B@B@BCJ@BA@@@CAC@A@A@A@@BAB@@BBD@H@BBB@DB@@@B@B@@@B@@@D@D@B@D@DBDBBBB@@B@@@DC@@BA@@D@B@HBDBD@B@B@DAD@B@B@BBB@@B@B@BABCFABA@A@CAAAA@A@ABC@ABA@@B@D@B@BBBDDB@B@B@DDDB@BB@@B@B@F@B@@BBBBDDBB@B@B@BA@AB@B@B@@FBF@@@BBB@@D@@ABE@A@ABEFADAB@B@@@BBBBD@D@FABVB@@FBHBLBJAN@HCNCJCJDDHH@JAFCBAHARDJC@@JEBEBCBOBIHKNCTFTHNJDJ@JGNBHBH@@DCHCNFHBFF@FH@RAPDPJAJCHGH@BA@@B@@@B@BBF@B@@@BB@@@BB@@@D@@@B@@AB@B@@CB@@ADA@@A@D@D@HDL@DBD@@B@@@BA@@@@BBB@AB@@BB@@B@@@@@@ABB@@B@@B@@@BB@@@@B@@AB@@BBBA@BB@@@BBB@B@@B@@B@@B@@BBB@@@@@BA@@B@B@DA@@B@@@BB@@@@BA@@@@@@BB@@BABAB@B@@@@@B@@B@B@BDB@@@@@BBB@@B@@@BB@@B@B@B@D@@@@B@@@BAB@@B@B@@@BD@@B@@@AB@@B@@@DBB@@@@BB@@@ABA@@B@@@B@@D@D@@@@B@@@DA@@@@@@B@BBDAB@@A@@B@@@BB@@BD@D@B@@@BBB@@@BA@@@@BBB@@BBB@@B@@B@@BB@B@@B@@@B@@AB@@BB@B@@BB@@AB@@@BA@@BA@AB@DBBBB@@@D@D@@@B@BA@A@A@EACBABEBCDABAD@@A@C@AAC@A@ABEBC@A@ABA@A@@ACAE@C@A@AB@BAB@@CA@AAA@CA@@AA@A@K@ABA@AFAHCFADADAB@FED@DA@BB@@@@@B@D@B@B@B@BABAFCB@BA@@B@@@D@FADAFAPQDC@AFK@KKIQBECAAECAK@C@@@S@QIQA@A@A@AAA@@@A@@@A@@@AA@@@@SDOHQBKGIMFAPCB@LAB@@ADE@ADG@CB@DC@@FC@@HATD@AAC@AAA@A@AAA@ACBA@IB@@KFEB@@ADCFAB@DEF@@ODA@I@GBM@GEKQAI@]@Q@C@@CCAAAC@CBC@CBC@ABA@@BCBC@C@@BCBC@CAE@C@EFEDADGFED@JFBADA@@D@BACCACFG@EBCAEEGGCCEACDIDAHABACCCEDCFG@@JBJDBBHBB@HAB@BB@@BBBD@@DEBADADEBEDAF@FBBABEBABG@ACEAGDCCI@C@@@QAUCS@EDYAMAIBKDME@M@MCGCICYGEACAKE@@QEMHIJEFILI@GKCOBKDG@EGM@M@OACIOIKEGCCGGCCC@KJE@IDO@S@MAKCEAEAGBED@BGBGCEHAF@JGPGHKHCDAB@BAFBFBB"],encodeOffsets:[[112673,19637]]}}],UTF8Encoding:!0}):void D("ECharts Map is not loaded"):void D("ECharts is not Loaded")})
