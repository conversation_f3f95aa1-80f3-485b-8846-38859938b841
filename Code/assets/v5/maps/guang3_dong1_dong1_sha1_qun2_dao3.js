!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(exports,require("echarts")):o({},e.echarts)}(this,function(e,o){var t=function(e){"undefined"!=typeof console&&console&&console.error&&console.error(e)}
return o?o.registerMap?void o.registerMap("东沙群岛",{type:"FeatureCollection",features:[{type:"Feature",id:"442100",properties:{name:"东沙群岛",cp:[116.887613,20.617825],childNum:4},geometry:{type:"MultiPolygon",coordinates:[["@@N\\`ZrDVBrCf]\\mRmBqWY]OoFyZe^a\\SfA^"],["@@JVZNT@B@TSno\\cB_ES@AWCaAaR]dOfId"],["@@IBGBIBEBID@@AB@BB@@@BBA@BD@@DBDBB@B@B@DAFCFAD@B@DCB@B@@ADGBCA@E@"],["@@tJrHtC^BvGj[JK\\{R§CS]{Ycc_q]K{AuLUJSPCJBLFHNFKNlRtn`CMhQJ]BuDUCKCIBIHOFK@[AQAKDCJDHHBFFCN@HDF"]],encodeOffsets:[[[118726,21604]],[[118709,21486]],[[119538,21192]],[[119573,21271]]]}}],UTF8Encoding:!0}):void t("ECharts Map is not loaded"):void t("ECharts is not Loaded")})
