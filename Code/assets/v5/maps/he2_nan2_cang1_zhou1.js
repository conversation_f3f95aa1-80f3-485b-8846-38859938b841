!function(e,t){"function"==typeof define&&define.amd?define(["exports","echarts"],t):"object"==typeof exports&&"string"!=typeof exports.nodeName?t(exports,require("echarts")):t({},e.echarts)}(this,function(e,t){var o=function(e){"undefined"!=typeof console&&console&&console.error&&console.error(e)}
return t?t.registerMap?void t.registerMap("沧州",{type:"FeatureCollection",features:[{type:"Feature",properties:{name:"泊头市"},geometry:{type:"MultiPolygon",coordinates:[["@@RW¬ixHLMIM¡a{i_F_i}UFSEyNUaNIiMDSASBSUÛXCS@gU[¯^UWDÛKbtYRcaHM^JVaj|RcREb`J\\Gj¤[V\\dbhVTG|aĸbmh`G`gPfXVA¼V"]],encodeOffsets:[[[119453,39137]]]}},{type:"Feature",properties:{name:"沧县"},geometry:{type:"MultiPolygon",coordinates:[["@@togd]lspUAgBUUJ]wePtSRQ}Uo]H]OW]KSD¡]tOSyV}Mev[EqbKNwG«jQX»UUBeWO_h_HIVGH`dd®vOzrclTtGfuÒ^hX~W\\jO±×BUUgBUYSpSYBw¦jEJxT@@TdWG[jQxXdG^NxaXOXmP[KJtJú gn^ªfPXPr`J]T}hc"]],encodeOffsets:[[[119923,39405]]]}},{type:"Feature",properties:{name:"东光县"},geometry:{type:"MultiPolygon",coordinates:[["@@QyL³s]µqOvIo^uavÇV{M@~L^CTDVCVtTlªH´ĄLhkVQX[ňifaAgZUC}kOm"]],encodeOffsets:[[[119683,38753]]]}},{type:"Feature",properties:{name:"海兴县"},geometry:{type:"MultiPolygon",coordinates:[["@@aQkQJm}k}L¹WLkF[WeGa]Qg³KGKPOR¿`QlwVf|RU`JpyNHbnVHnLJRjVhChJâSÐ``AtVC`^Ĕ[atIj]"]],encodeOffsets:[[[120636,39157]]]}},{type:"Feature",properties:{name:"河间市"},geometry:{type:"MultiPolygon",coordinates:[["@@Qeg]h[^²UpTwvGL[zbdoemEckSqdyuPc­cG__JYE·J]cŃQiUSSUkDkí^J`VTEx\\n}\\jbWTWtýb`xH`Ojzhbq|R|nT[xfOVDS`fGd\\PRZrRp_`GpYPW~W"]],encodeOffsets:[[[119247,39503]]]}},{type:"Feature",properties:{name:"黄骅市"},geometry:{type:"MultiPolygon",coordinates:[["@@HXRQYE"],["@@EMu[UgO[`WmsJbē\\]D_sU_BÏ_áTIDgUgQiKIN_E¥lIWxbQVKJI^VAVBhoVktc^phs gdS~I^JJ^`J~fHNH|X{\\SSpI\\TYV\\hdof\\T^âp¢QdNje®ąl{Ñ¢­üdxS"]],encodeOffsets:[[[120717,39234]],[[120717,39234]]]}},{type:"Feature",properties:{name:"孟村回族自治县"},geometry:{type:"MultiPolygon",coordinates:[["@@XwJ¦k`FMGmmUGasHZaEYF_µzbHd»©U}h¿ROXs\\J^C`D`k|LPX ^^Gp~VQRsTOxfLIRUa"]],encodeOffsets:[[[120005,39119]]]}},{type:"Feature",properties:{name:"南皮县"},geometry:{type:"MultiPolygon",coordinates:[["@@ra\\Ffu~NzUTPs¢^TC^LKl{C_D_I]}m~mKFgEiLÅNIMS@@VU@¹@PnlD~YVBhebŇjW\\URglăKS@JjMVbzMTFVEj~E``|jb¢JN"]],encodeOffsets:[[[119550,39101]]]}},{type:"Feature",properties:{name:"青县"},geometry:{type:"MultiPolygon",coordinates:[["@@IZìVV[ZSIn{VQRULI]IIq_OOW©em]hIùPeOiP[}XgWÑ]evsHFdfncpyaK\\JJ`\\^dlCpxwnnVAhVTTVTfV`_\\BTAVBTK°ĜGGu\\}"]],encodeOffsets:[[[119680,39612]]]}},{type:"Feature",properties:{name:"任丘市"},geometry:{type:"MultiPolygon",coordinates:[["@@HcAYlQRiÀSÒbgJIE¿I_Ho`QqQYO[Hc_eTUCPweS\\{m{QarM°d\\a´BVUhLrpN~XEVZKvbvKPzRe"]],encodeOffsets:[[[118987,39859]]]}},{type:"Feature",properties:{name:"肃宁县"},geometry:{type:"MultiPolygon",coordinates:[["@@G__wþaXsXSia~[[mFwUSI_MS¯Rå»Wx_J]ÏjUCQXiWqLRXh^a¶h^ZbRRl^UZf~MR"],["@@FWWFRR"]],encodeOffsets:[[[118720,39459]],[[118635,39461]]]}},{type:"Feature",properties:{name:"吴桥县"},geometry:{type:"MultiPolygon",coordinates:[["@@uvbp]JPuu]Qi©Å§waYQW¿a}snSmVY\\Ln\\G|VRR|ZTCVzXdn|NÈU"]],encodeOffsets:[[[119361,38687]]]}},{type:"Feature",properties:{name:"献县"},geometry:{type:"MultiPolygon",coordinates:[["@@ń^d¸IFZ`IHUJgnķa{bHUSagcU[£\\iH[_Ig|`t@|±k±lQXuHUL\\jO]P[lJ`IXwæ¼°QNTî]llCTVVTRj"]],encodeOffsets:[[[118981,39301]]]}},{type:"Feature",properties:{name:"新华区"},geometry:{type:"MultiPolygon",coordinates:[["@@@SS@@TT@"],["@@JL\\nOPWbWMwH]WcwiRH\\cX"]],encodeOffsets:[[[119662,39203]],[[119662,39203]]]}},{type:"Feature",properties:{name:"盐山县"},geometry:{type:"MultiPolygon",coordinates:[["@@YtGzMIoV_Qe{xURkÀ_QPLOeY¯çĝxQY_I{U¯L¯K÷R[KFjEhL}n~nt[PWÀQ~gªVc¼aGy¶`ZEbF"]],encodeOffsets:[[[120063,39036]]]}},{type:"Feature",properties:{name:"运河区"},geometry:{type:"MultiPolygon",coordinates:[["@@IwiF¥AxZoTZTAVVhAV²ØfPOsLIKIS@@S"]],encodeOffsets:[[[119652,39193]]]}}],UTF8Encoding:!0}):void o("ECharts Map is not loaded"):void o("ECharts is not Loaded")})
