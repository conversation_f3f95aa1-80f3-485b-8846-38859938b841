!function(A,B){"function"==typeof define&&define.amd?define(["exports","echarts"],B):"object"==typeof exports&&"string"!=typeof exports.nodeName?B(exports,require("echarts")):B({},A.echarts)}(this,function(A,B){var C=function(A){"undefined"!=typeof console&&console&&console.error&&console.error(A)}
return B?B.registerMap?void B.registerMap("北屯",{type:"FeatureCollection",features:[{type:"Feature",id:"659005",properties:{name:"北屯市",cp:[87.837075,47.332643],childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@@@DADAB@B@@@@C@@AA@@C@@AA@@@A@@@@@A@@@@@A@@@@@A@@@A@@@A@@AA@@@C@A@@@@@AA@@AAA@@@@@AAA@A@@AA@@@A@@@@@A@A@AA@@A@A@@@AA@A@@A@BAA@@@@@@@A@A@@@A@A@@@A@AAA@@@@@@@@@AA@@@@B@@@A@@@@A@@@@@@@@@AB@@@AA@B@@@@A@BA@@@@B@@@@@B@@@@@@@@@@@A@@@@@C@@@@@A@@B@@@@A@@@A@ABAA@@A@@@@@@@@@A@@@@@@B@AA@@@@@@@A@@@B@A@@@@@A@@@A@@@@AA@@@A@@@A@@@@@A@@@EHVF\\JZHVHB@"],["@@A@A@E@EBABOBCD@BB@@BABA@KBABAACB@@E@A@AB@@@@@@B@@B@BBBA@A@@BA@B@ADA@AAC@C@A@A@A@@@A@C@@AA@@AACAAA@AAA@AA@AA@AA@AAAA@A@AAA@@@ABBB@BA@@@AA@@@AAAEB@A@@A@A@A@A@@@@AB@CAEBC@GDA@IBE@EBAB@D@@@BCBC@CBCBC@CBC@C@E@AB@BA@A@A@A@EBA@G@CBC@E@CBCB@A@@A@@BGHDA@BCB@BCBABB@@@AB@@A@@@@BA@A@CBA@ADA@A@@ABAA@A@ABA@@AAA@@A@A@ABA@A@A@ABA@A@A@@@C@@@BA@@@AA@CBA@CBE@CBCBE@A@GBABCBABA@@AA@@@ABCDABABABA@A@@AA@CB@BA@AACAA@ABABA@CBCBEFCB@@CDCBCBADA@@DBBA@CBEDA@ABC@AB@BABCBABBBFFCDGHEHABGDEBCDEBCDCDCHEDCFA@EDGDKJKHCBE@GAGAEBEB_VMFEBcFWJ]VUR]VaZKLRHFDPH@JJFDJDB@BBBLHBBBDBBDDBBBDDDBBDFBDBDBB@BBDBJ@H@FADADCDADAACCEICEACCCCCAA@@@B@BDDBBDFB@BD@BBFBBBBBB@B@B@B@B@B@DCBABABC@EBA@CBADAD@BN@JAHHHHDFDDBAAA@CB@B@BABAB@BA@@B@@@DA@@B@@@@@B@@@@@BA@@@@D@@A@@@AAAAA@@BAD@HAFALDJBB@B@@@@A@AB@@A@ABAB@@AD@D@L@BBD@@@HBB@B@B@@@BBB@@@BABB@@B@B@D@B@B@@@@@BA@AB@@AA@BA@@@@B@BAB@D@B@F@B@B@B@B@@@B@BA@@BA@@B@B@@BB@B@B@BADBB@@AB@@A@@@@@A@@BA@@@@@@BA@@@@B@@@B@@@@@B@@@@@@AB@@@B@@@B@@@BAB@@@@@B@B@@@B@@@B@@@BA@@@@@AB@@@A@B@@AB@@@B@BBB@B@B@@@B@B@@AB@B@B@B@@@@A@@BA@AB@@AB@DAB@D@D@B@BABAB@BABBB@@@B@@@B@BA@@BAB@@@BAB@BABA@@B@@@B@BAB@BA@@B@BA@@B@B@@@B@B@B@B@B@BAB@B@@AA@@@@A@@@@@A@@B@BAB@@@BAB@@@B@@@BA@@B@B@@@B@BB@@B@@@@@B@@@B@@@@@B@@@BAB@@@B@@@@A@@B@@@@AB@@@@@B@@@@@BA@@A@BA@@@@BAB@B@B@BBB@@@B@B@@@BA@@@AB@@B@@@@B@B@@@@AB@BAB@B@D@B@@@BB@@B@@@BA@@BA@@@@BAB@@@@AB@@@@@BAB@@@BA@@BA@@@@B@@@@@@AB@@AB@BA@@@@B@B@B@BA@@@@@A@A@@BA@A@@BA@@B@BA@@B@B@@@D@@@B@@@B@B@B@@@BA@@B@@A@A@@@AB@B@@AB@@@BB@B@@@@@BB@@B@@B@@@B@AA@@@@@A@@@@@A@@@@AA@@@@@AB@@@@@@@B@@@B@@@@@B@@B@@@@B@@@B@@@@@@@B@@@@@@@B@@@@@@@@@B@@@@@@@@@@A@@@@@A@@B@@A@@B@@@B@B@@@B@B@B@B@B@BB@@B@BB@@B@@B@@B@@@B@@@B@B@B@@@B@B@@@@BB@@@@@B@@@@B@@@@@@@@BB@@@@@@B@@@@@B@@@@AB@@@@@B@@@@A@@@AA@@@@A@@@@@A@@@@B@@@B@@@B@@@@@B@B@@@B@@A@@B@@@@AB@@@B@B@@B@@B@@B@@@BBBBB@@B@B@BABA@A@A@AAAAAC@AA@@BA@@DAB@BADABAD@BAB@BBB@B@@AD@BADABABC@GHBDBL@FBDAD@FANC^GB@@@B@@ABA@@@@@@@@B@@AB@VEB@ZEDATCDAD@PG@AD@F@\\@XAJCFAD@B@V@B@BA@@JCBAXMRIFCDADAFALC@@REBAB@HGB@BAB@BANAJCF@HAZGUDAB@LENAPCPCDAVE\\IJ@`EVCZC@BD@DBD@@BBBBBFBDBH@DBF@B@HBDAFAFC^ARBDCB@F@D@DAF@FAD@B@D@@@B@B@F@D@D@F@F@DAFAN@LBBDJBDADAJDFDF@FDVHHBL@H@FAF@@EAKAEAEa@qA}@C@G@E@OA@@@@AA@@@A@@@A@A@A@A@C@C@CAA@AB@BABCBA@ABE@E@AB@@A@@@A@@@C@A@A@@@A@C@A@E@EBI@KBC@@AA@AAA@@AAA@QKYKGAOGSGSIICCGAC@AAE@@AACAE@SAMAI@E@E@A@C@G@C@E@C@A@@@@@G@I@C@@@A@C@IEICCAA@A@AA@@A@EAE@A@IECA@@CA@AA@AACAMEG@@B@@A@@@@@@@@B@@@@A@@@@@A@@@@@@@@@@B@@A@@@C@B@A@@@@@@@@AA@@@@B@A@@AB@@@@@@A@@@@@@BA@@@@@@@CA@@@@@@A@@@@@A@@@@@@@@@ABA@@@A@@@@@@@A@@BAA@B@@A@@@@@A@@@@@@@A@AA@@@A@@@@@A@@@A@@@@@A@@@@@@@@@@@A@@@@A@@@@@@@@@A@@@@B@@@@@@A@@@@BA@@@@@A@@@AB@@@@A@@@@AA@@@@@@@@@@BA@@A@BAA@@@@@A@B@@B@@@@@@A@@A@@@@@@@@@@A@@@@@@@@@@@@@A@@@@A@@@@@@A@@@@A@@@@@@@@B@@@@@@@@@@@@A@@B@@@@A@@A@@@@A@@@@B@@@@A@@A@@ABB@A@@@@@A@@AA@CB@@AB@@A@@B@@A@BBBA@B@@D@@@@B@B@@B@@BB@@@B@@@BB@@@@B@B@@@@BA@@@A@@@AA@@A@A@@@AB@@@@A@@BB@@@A@B@AB@@@AABB@@@BB@@@@A@ABC@@AABA@AA@@CD@@C@A@@@AB@@@B@@@@A@A@@@A@@@A@@BA@@@A@@@A@@@AAA@A@A@@AA@@@AA@@@@AA@@@@A@@A@@A@@@A@A@@@A@AAA@A@C@@@A@A@ABA@C@C@ABA@C@A@A@@@ABA@@B@@AA@@@@A@@@@@A@@B@@@@@B@@AB@BB@BBB@BB@@BBA@@D@B@B@@AB@@AB@@AB@@AB@@ABA@@BA@A@A@A@A@C@CB@@A@A@CBA@@@A@A@@BA@EBA@A@A@C@C@A@CAC@A@A@AAA@@@AAA@@@ABA@@@@@AAAAAA@@A@@AA@C@A@A@@AABA@A@ABA@A@@@AB@@ABC@EBA@AA@@@AA@@AA@A@A@A@ABA@ABA@A@@@ABA@ABA@@BA@ABA@@@ABA@CAC@A@ABAB@@A@ABA@A@ABA@C@A@@@@BA@@@A@A@@@@B@BA@A@C@A@A@ABC@A@A@@@AB@@@@A@A@A@@@AAA@@@C@A@AB@@@@A@"]],encodeOffsets:[[[89650,48565]],[[90064,48289]]]}}],UTF8Encoding:!0}):void C("ECharts Map is not loaded"):void C("ECharts is not Loaded")})
