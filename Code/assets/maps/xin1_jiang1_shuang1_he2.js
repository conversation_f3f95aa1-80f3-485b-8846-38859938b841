!function(A,B){"function"==typeof define&&define.amd?define(["exports","echarts"],B):"object"==typeof exports&&"string"!=typeof exports.nodeName?B(exports,require("echarts")):B({},A.echarts)}(this,function(A,B){var C=function(A){"undefined"!=typeof console&&console&&console.error&&console.error(A)}
return B?B.registerMap?void B.registerMap("双河",{type:"FeatureCollection",features:[{type:"Feature",id:"659007",properties:{name:"双河市",cp:[82.353656,44.840524],childNum:10},geometry:{type:"MultiPolygon",coordinates:[["@@DAB@BADBDABB@@@ADABCBEBABAAAFB@ABA@AB@F@B@D@B@D@FAFBRC@E@@LE@AA@E@AA@A@AC@CB@D@BE@@ACBC@@BEBA@A@@ACAC@A@@@CAM@CAGBEAKDBDE@E@ABEA@AGDBBX`@BB@"],["@@@@`MZABEBQI@BAAA@C@C@A@C@A@G@AAGCGCCA@@AAA@ECGCEACCAA@AA@CCA@AA@ACBAAE@A@A@A@C@A@A@K@ABEBA@EB@@CD@DCBA@@@@@@@AA@@ABA@A@EE@@CCCEU@@@C@AGMgQDA@OFEIKBEMUFCBDB@@BF@BBBUDBF@FBJDV@BHAXPbVLCFAB@lZ@@NNNDPJDJBBjEJHBFQRGACC@@MND@G"],["@@EBAABAAACBABBBE@AB@FYJCCE@CDA@E@WJKJ@BQFEBUD[DS@CH]JKJED_VEDABYEG@UCKCyIEBQDaHeHMDE@@BEB@@BABABAB@@AD@@@@A@A@@@@@@@A@@A@@A@@@AA@@A@AA@@A@@@AAAA@@AAAAEBEA@CBE@EDaUgbe^fVKHJLJAPIDAVCRIFArfAD@@@@BBOBsPIB@Ao@@@@NQD@@AA@C@@C@A@@B@BAB@@@BAB@DABAD@BAD@BA@ABCD@@B@BAB@B@B@@@B@B@@@F@@@@BFAB@@@BB@@@@@BB@B@B@@ABANCTGXAB@VE@@HJB@BDDJLCHAL@@@VAB@FA@@HB@@FBH@FANAHAHAHBDAF@F@D@DAF@BAFAB@PGHAFAPIPID@RMTGFD\\RTLPJ\\FPB@DHDNJVNÌ[nIAVD@hARFàrPDPX^BDCBCBCDE@ABEDEBG@A@CBC@GBA@ABA@A@@BABEBEBA@@@AEGEC@EDADAAABEJCDEBE@I@IDCBABCJANELC`Cn@@@CA@C@AB@@AB@BAB@@AE@@A@AHE@ACA@BABABA@CA@A@GB@B@B@@CE@EA@ABADBB@DA@CAA@A@ABAACAA@AA@CBCG@@DAECAB@@ABEA@AE@@BAA@BAAAAC@C@OCAA@@A@@CDADA@AMGCBABA@CAAADA@ECA@A@ABA@CAA@@G@GA@@A@A@AA@ACAEBC@CACCBC@@BAAA@CACCAC@CA@ADCB@@CCAABAA@@BAD@AAAACA@@@ECACAC@CACACAA@A@A@CAA@CB@@CABAB@@@B@BA@AAACAAAC@A@C@AAB@@A@ACB@BA@CA@CGECBAAACB@D@B@@AAAAA@A@@C@ABCA@@GECDC@EACG@EDABAAAAACACBAA@CCAC@A@AAA@@CA@G@AA@AB@@AAAA@CACBABA@@AAAAAE@A@C@A@@AAAAAC@AB@@C@A@@A@AAAAAC@C@AAB@BCBABBB@DA@AAAA@A@ABCC@AC@EBABAAAAA@CAC@AB@@@BBB@BABA@A@C@CCEBBDCA@BBBC@@ACAAD@DD@B@GZRAE\\C@CAI@ABCAC@AAC@ABE@A@CAMBABC@AAABA@A@A@@@@BC@@@A@ABA@AAAB@@ABA@ABA@AB@BA@A@ABC@A@BBCDAAABADA@A@ABAB@@ABE@A@@@CB@B@@C@A@AB@BA@A@AACBCAIAC@ACMDC@@DABAA@AA@AB@@CAC@CBBBB@B@@BC@AAA@G@@@A@ABBBBB@@@BA@@BCBCAIBBDA@BB@BABC@BBADACCBBDC@CC@@ABC@@BAB@@"],["@@JAAEA@A@@@ACGBDHBB"],["@@`C@GAEAECAKCeKS^fLH@"],["@@FGJKKABA]IIVbL"],["@@ABBBCBLNFDBBBFFB@DBDAHADBBABBDEJEFKH@BBFBFED@DA@@L@FPJJDLHHB@HF@ƀ_½Ən"],["@@g@I@@@FLTf`CGm"],["@@RMBADMTA@GGKEEDEBCDABCB@@A@AB@@AAABABAA@BAB@@A@@@@@A@@@A@@@ABAD@@@B@AADAAA@@@@@@A@@@@@@A@AB@@BB@AAD@AA@@AAA@BABB@AB@A@BAB@C@@AB@@A@@@@@A@@BAB@A@@AAAAAB@BACA@@@A@@AAB@@A@@B@AAB@@ADECCSIAA@ADA@E[BO@G@WDJC@SBEBG@GBO@WBAH]@UBIZ@LMTIP@@CFE@@DA@ABBC@AA@CB@@A@BAEBG@E@CB@@@BIAAAA@ABAFC@BCDEEGACIHA@ECEDABCBAAMHEDAABA@A@AFEBCBGAABAACA@AAA@AB@@GBGB@BABAB@@CB@BCFA@@FA@A@@@@@A@@@@@@B@@@@ABA@AF@@A@ABA@A@AB@@A@A@A@A@A@KJEHED@FEJCHAFCHJ@LBNARCHJJVB@F@HD@D@lPdLPAÊE"],["@@CA@AC@CDAAA@GGUN@BEHGL@BU`AB@BDANApUB@LMDIBEAA@A@C"]],encodeOffsets:[[[84324,45764]],[[84183,45809]],[[84388,45850]],[[84149,45889]],[[84141,45867]],[[84129,45883]],[[84250,46111]],[[84209,46208]],[[84106,46119]],[[84279,45811]]]}}],UTF8Encoding:!0}):void C("ECharts Map is not loaded"):void C("ECharts is not Loaded")})
