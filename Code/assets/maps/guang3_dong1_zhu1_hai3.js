!function(B,A){"function"==typeof define&&define.amd?define(["exports","echarts"],A):"object"==typeof exports&&"string"!=typeof exports.nodeName?A(exports,require("echarts")):A({},B.echarts)}(this,function(B,A){var D=function(B){"undefined"!=typeof console&&console&&console.error&&console.error(B)}
return A?A.registerMap?void A.registerMap("珠海",{type:"FeatureCollection",features:[{type:"Feature",id:"440402",properties:{name:"香洲区",cp:[113.543784,22.265811],childNum:63},geometry:{type:"MultiPolygon",coordinates:[["@@@@@@@@@@@@B@@@@@@@A@@@@@"],["@@@B@@@@@@B@@@@A@@@@A@@@@@@@@@"],["@@@@A@@@@@@@@@@B@@@@@@@@@@B@@@@@@A"],["@@@@@@@B@@B@@A@@@@@@@@@@A@@@@@"],["@@@BB@@@@@@@@A@@@@@@@@@@@@@@@A@@@@@@@@@@AB@@@@@@@@@@"],["@@@B@@B@@@@A@A@@@@A@@@@B@@"],["@@BB@@@@@@@@BA@@@@@@@@@@@@@A@@@@@@@@A@@B@@@@@@@@@@A@"],["@@B@@AAAA@@B@@@BB@"],["@@@@B@@@@@@A@@@@@A@@A@@@A@@@@@@@@B@@@@@@B@@@@B@@"],["@@@BA@@BB@@@B@@AB@@A@@@@AA@@AB@@"],["@@@BB@@@@@@@BA@@@@@A@@@AA@@@@@@BA@@@@@@@@B@@@@"],["@@@@@@@BB@@@B@B@@@@A@@A@@@@@AAAB@@"],["@@@BB@B@@@@A@@AA@@@@A@@@@@A@@B@@B@"],["@@@B@@B@@@@@B@@@B@@@@AB@@A@@@@A@A@ABA@"],["@@@@@B@@B@@@@@@@B@B@@@@@@A@@A@@A@@A@@@@@@@A@@@@@@B@@"],["@@@B@@B@B@@@@@B@@AA@@@@A@A@@@@A@@@@@@@@BA@@B@@@@"],["@@@@B@D@@AB@@@AA@@A@@@A@@@@BA@A@@@@BB@"],["@@B@BA@@@@@@@AA@@@A@@@A@AB@B@@@@@@B@@@@@B@"],["@@BA@@BA@@A@@@A@ABABA@A@@B@@D@BAB@"],["@@@@BBB@@@BA@A@AAAABA@@D@@"],["@@A@AB@B@BBBBBB@BA@AACAA"],["@@D@@BB@B@@@BA@AB@@AAAA@C@ABA@@D@@@@"],["@@BBB@B@B@@A@CAAA@@@A@@@A@@@AB@BBB@@"],["@@B@@ABA@AAA@A@@B@@A@@@A@AA@A@A@@B@B@B@@BD@BAB@BB@@@"],["@@A@@B@B@@B@@@B@@BB@@AD@@@@A@@@AA@@A@@@@@A@@@@AA@@@@@@A@@@A@@@@@@B@B@@@@@B@@"],["@@@B@@DB@@@@AB@@@@@B@@B@B@@@DA@@@@@A@@AA@A@C@@@AA@@@A@@B@@@B@@@@@BA@AA@@@@@B@@@@"],["@@AB@@BBB@@AB@@@@@@A@A@@@A@@@AB@@A@AA@@AA@AB@@@B@@AB@@@B@BB@@B"],["@@ABB@@BB@@@B@@A@A@@@A@@@A@@@A@A@@B@@AA@@@@@@@@A@@A@A@@A@@A@@@@@@B@@@@@@@B@@@B@@B@@@@BA@@@@@@B@@@B@@B@@@@B"],["@@@BABBBD@B@D@@@BA@AAAAA@AABA@CB"],["@@ABB@@@B@B@@A@@@@@@@ABA@@@@BCB@BA@@@A@@A@@@@A@@A@A@@B@B@@AB@B@@A@@@A@@@@@@@@B@B@B"],["@@C@AB@B@BBB@@DDDBB@@A@AAAACAC@@"],["@@DBBBB@@A@@B@@@BAB@@BB@@@B@@A@@A@AC@@A@@AA@@@A@A@ABABAB@@@@"],["@@BB@BB@B@@@B@B@@@BA@A@@@A@A@@@A@@AA@@A@@@@BA@@B@@A@@AA@A@@@AB@@@BB@@B@@"],["@@@@@@@B@@B@B@DB@@@@B@@@@@B@@@@A@@@@@A@A@@@AAA@@AAC@@@@AAA@@@@@BA@@B@@BD@@@@AB@B"],["@@ABA@CB@@BBBBB@@@AFBD@BDD@@@@B@B@@AAAAA@A@CBC@C@ACA"],["@@@@@@@@BB@@DB@@B@B@@@@@BA@@BA@@@C@@@@@A@@B@DBB@BB@@B@@@@@B@@@@A@@AA@@@@AAGA@@A@@@@@@@AB@@A@@BEBA@@@AB@B"],["@@@@@@B@B@B@@@@B@@@@@B@@B@@@@B@@@@@B@@@@@BB@@@B@D@DABA@@@A@@@A@@@@CA@@A@@@AA@@A@A@@@@A@@@@@A@@@@@@@A@@A@@BA@CB@@AB@@@B"],["@@A@@DAD@B@BD@BBDBBDB@B@BAAEAAAE@AEAA@"],["@@@@BBBD@BB@@BB@B@@@FDB@B@@@BA@@@A@@@A@ACAA@@AACA@AA@@AAA@ABA@@@AD"],["@@@@B@BA@@@A@A@@@@@@@@AA@@@A@@A@@@@@AB@@@@AA@AA@@@AB@@AB@@@BA@@@A@@BAD@B@B@B@B@B@@@@D@B@@@B@B@@@@A@A@A@@@@@AB@@@@@B@@A@@@@B@"],["@@D@DAB@@A@CBA@@BAD@@ABA@AAA@AA@A@@BABA@A@AA@@CBABCBAB@DBDBB@@BB"],["@@@BB@@BBBB@@B@B@@BBBB@@@@@FBB@@@BB@@@BAFGBE@@@A@@AA@@ECEAA@@@A@A@A@A@@B"],["@@DFBBJBBBBBFDFBHAB@@A@@@AGEC@@AK@@@A@EEEAC@@@@@@B"],["@@D@BA@A@@BA@@B@@@BA@@@A@@@@B@@@B@@A@A@AAAAAC@A@C@AB@@A@@@AA@@A@@@A@AB@DABAB@@@BB@B@@AB@B@@@@B@@B@@@@A@@B@@B@@@D@BBB@@"],["@@@@@@A@@@@AC@AAA@@@@BAB@B@BBBBB@BB@@A@@@AB@@@@BB@@@B@@@B@@@@@BB@@B@@AB@@ABA@@B@@A@@@AACAA@A@A@AAAAEA@A@@@A@@D@@A@@@@B@D@B@@@@@@@@B@@@@@BBAB@@BB@B"],["@@HBH@@@BA@@@AB@BBBBB@@A@@@A@@B@@@B@@A@@@AA@C@@@AAACAACACAC@E@@D@B@BBB@BA@@@@B@B@B@@"],["@@DBBBBBBDBBB@B@@A@ACC@@BAB@BA@@CE@CB@BAB@@CBA@AAAAA@@@AAACEA@A@A@C@AB@BBDDBBBBB@B@D@BA@EBA@AB@D@BBB"],["@@@@@B@@BB@@@@B@FB@@@@@@BAA@AA@A@@@@B@@AB@@A@@@A@C@@@@@@D@B@B@B@@BB@HDB@B@@@@A@@@@CE@A@@A@AA@@BAA@@@@@C@@@CEA@AAA@A@A@EBA@@BA@@B@B@B@BB@@@@B@@A@@B@@@BB@@B@B@@AB@@@B"],["@@@BKD@B@@@BA@@@A@@@@@BB@@@@CB@BA@@A@@BA@A@@C@CBCB@B@BA@@@@BBB@B@@@B@@BB@@@FBBD@B@BBD@@A@C@AAA@@BC@A@@DB@D@@B@BCBABEBA@@@@A@@@AA@@@A@A@@B@D@B@@@@A@@CE@@"],["@@@DBD@B@BA@AB@BBFBBB@B@@B@@@B@B@@B@B@BBBB@@B@BA@AB@@@B@@DBB@@DDB@B@DBD@BBBA@A@@AAAACCCCAACAC@CAC@AA@@@C@A@AAAAA@A@@AAAA@A@@@A@A@AAAA@A@A@@AA@@@@@A@@B@@BB@@@BBB@@DB"],["@@@DBF@BB@DD@@@BAD@B@@B@@@@@B@@@@@DE@AB@B@@@B@B@B@D@@@B@@@@@B@@I@@B@@A@@B@@@@@B@@@@@@A@@@@@A@@AA@@A@I@A@A@@@AAA@AACAA@A@A@@@ABAD@B"],["@@ACEACAEAIBCB@DBDB@BB@B@BBF@B@B@@DBBBBB@@B@@@BA@@B@B@@AB@B@B@BCBC@AD@@A@@@AAAA@@@A@@@@C"],["@@@BDFFFDDB@VHFBBBDBBLBF@@BBB@BA@@BEB@@A@AAACCAC@ABABAD@F@HFDD@BB@@A@@BA@AAAAAEECAKEE@C@GBA@A@IAAAAA@C@CFE@@@A@@A@@AA@CDA@@BA@CCA@CACBCD@D"],["@@AA@AB@BBBBD@BABC@A@CA@@@@A@ADAB@@AACAC@@@ACA@A@@A@EFA@ABA@AAA@@@AB@B@@@@@@@@C@CAABA@@@A@A@A@@BCBA@ADABAB@BBB@@@@B@@@AB@AA@@B@@B@@@@B@@A@@BA@@B@@B@@@BBB@@AB@@@BB@A@@@@@A@@DBB@BDBDBBBB@@B@D@@AB@@BDB@BB@BABABC@G"],["@@@@@C@@AEBC@AB@D@@AEEAAA@@DAB@@A@MAA@EB@BAAAGAAK@CBAB@BDFBBCF@BBBDFFFDH@ABDFDDAFCDBDCD@BADABAAE"],["@@@B@DA@A@AAA@@@@@A@@@AAC@A@@@@B@BBB@@@BA@A@@BBDBBADA@C@@@C@A@@BC@@@@BAB@@A@@@@BBDDBHHB@@AAA@@D@D@@AB@BDFDBBDADBBAB@@@D@DAB@AC@EA@@@AAA@A@@ACCAAAA@A@@BBBBDBDBDBF@D@BA@EB@BA@ACA@@A@CA@A@C@@CAA@C@ECA@AAA@"],["@@C@ABADABGBGBGAU@E@ABABBDDBNHHBH@HAHBBBBD@HBFBBD@DADABCBAD@B@DBDFDBD@D@BA@ABC@CBCACCCCAAA@C@CCAKCCAAACCCA"],["@@BDB@FBDDHBFCD@@AH@LCHC@AAC@AF@F@@A@EACCCCGCAAGKEE@EBEABACCC@CBAA@@A@CD@BB@BF@B@DB@B@DB@BAB@BCCC@@BABA@@AABA@@@@HBFBBBD@DA@EA@@AB@BDD"],["@@C@CBEDAB@BBD@BCB@D@BABACEAAB@BAH@BBD@BGDGHAF@BD@DAB@@BAFBBD@D@BCDA@C@@BFBBB@FANAADBFB@F@B@DBD@DG@EDAB@@CACAEA@AB@@@ADCBGIGCCC@CB@A@GAGCC"],["@@E@IBABABAFBD@DADAD@FDJBDBBD@PBF@HAD@F@D@F@FAHBF@FAFEBC@AACAAAE@CB@B@D@B@B@B@@AACAAECGAC@GBC@E@G@EBEAEACACCE@"],["@@A@IDEB@B@FB@DDBBBB@B@@BBBBD@BBF@DDBDDBLBLBHBBBADD@T@B@@BBDPHLBTBDBRABBBBFDBBCBCF@BFDLDHBJAFA@ECGE@KA@@BA@CAAECC@AABC@CA@A@BAF@@AAAIAKGCAG@G@KBACA@C@C@@AB@AAAAK@KGA@E@M@GACC@@EAI@EAGEAEA@CA"],["@@ABADUhENAFIRELQbCFiKTM\\@@AFJHPHDFDHDD@@PFFBJDDBLD@@FBFHBDB@@@FB@@DBDBJDB@DBFBHBD@@BABABCBED@BAD@FD@DDB@DBDBB@BABB@@B@B@@BB@B@B@B@BBD@@@BBB@B@B@FBDD@BBDHC@D@B@@@@@BBB@@@B@@@@@D@B@@@B@D@@@@ADAB@B@@EHABAAA@@ACAADCBCFDBAB@@A@@@DF@@CA@@ADABAB@@@B@BABA@@BA@@B@@CDBB@@ADIJBBIJA@@@ABADAAAB@AA@AB@@AB@@@@BBCBCDA@@@ABDDBBDD@AB@@B@@@AB@B@@@BBB@@@B@B@BB@@BB@@@@@BABB@BBDAB@@@B@@AD@F@PHDBBD@BABCBADBBABABCBA@AB@DBDCBEBA@AF@DB@@@B@@B@@AD@D@BBBBB@@BB@F@B@@BB@BA@@FABABC@@B@BBDBDAFBF@@CDCDADAF@DBFFDDDD@DB@HANBDHBDBDBBBFDD@DCFEDADBB@@BBDBBBBD@BABADBBDDBB@DBB@BAPCBBPCB@V@JABAFINGNAP@TFJJH`JZNLR@LBN@LANCNCHEDKAOAKcuEGMYKUKSCGGEECGAEAE@G@CAAAAE@E@EBCDCDEHEHEBC@CACCECAICOAGCECCEAE@EBEBEBEDA@ABA@A@CAAACCEAC@ABCBADCD@BABCBA@CAI@C@E@GAMAGAC@AAAACCAAAA@M@M@C@A@CACAA@GIAACCAA@@CGEBA@C@@@EBA@@@AA@@@@ACAA@@@@A@@@A@A@A@C@A@@@AA@@@@@CBGB@AA@A@A@E@E@@CEAEAAACCEAE@A@GBEDEFGDG@@FKJK@AJEDE@C@G@@@A@CAQ@I@G@K@EBG@ABCHENMDG@QAEAECCEAE@G@]FQBE@EAIGMOSKICG@E@CBE@C@C@"],["@@D@DBBA@@@C@@FEBCDGBAB@ACAAEECAEGAE@CA@EBABC@CBADBB@BA@@D@DA@CA@BADBDHFDB@J@HEJAFA@@DB@BBB@@@F@FBB@B@@E@A@AAG@E"]],encodeOffsets:[[[116828,22426]],[[116331,22776]],[[116460,22482]],[[116828,22427]],[[116677,22335]],[[116779,22406]],[[116724,22390]],[[116804,22457]],[[116440,22661]],[[116679,22597]],[[116757,22397]],[[116577,22520]],[[116749,22367]],[[116957,22541]],[[116367,22556]],[[116531,22709]],[[116342,22863]],[[116355,22774]],[[116338,22866]],[[116748,22582]],[[116425,22560]],[[116712,22344]],[[116696,22345]],[[116572,22656]],[[116431,22645]],[[116706,22333]],[[116750,22374]],[[116344,22777]],[[116710,22357]],[[116703,22351]],[[116494,22644]],[[116456,22697]],[[116327,22782]],[[116522,22511]],[[116388,22560]],[[116537,22532]],[[116752,22569]],[[116425,22566]],[[116642,22563]],[[116721,22587]],[[116432,22633]],[[116744,22556]],[[116866,22489]],[[116610,22649]],[[116736,22575]],[[116437,22675]],[[116394,22569]],[[116557,22530]],[[116547,22689]],[[116747,22378]],[[116654,22566]],[[116636,22645]],[[116772,22412]],[[116786,22634]],[[116436,22481]],[[116444,22536]],[[116894,22503]],[[116447,22477]],[[116496,22490]],[[116931,22515]],[[116973,22545]],[[116207,22584]],[[116560,22677]]]}},{type:"Feature",id:"440404",properties:{name:"金湾区",cp:[113.362656,22.147471],childNum:6},geometry:{type:"MultiPolygon",coordinates:[["@@B@BBBBB@@@@@@A@@AA@A@@A@@A@@A@@AA@@B@B@B"],["@@BABA@A@@@A@A@@A@@@@@A@@BABAB@BA@@BA@@@@@@B@@B@@@B@B@B@@A"],["@@BBB@BAB@@@B@BBB@B@BA@A@A@@A@@@AA@A@@A@@@A@@@@@@@@A@@B@@AA@@@@@@@BCB@@ABA@@@A@AAA@@A@EBA@AFAB@D@@ABAB@DBBBB"],["@@CD@B@FFNB@B@BBDD@BBBB@B@B@@@BBB@B@DCBE@A@ABAB@D@BC@AAICEECGEGAGAAAAAEKEEIGCCA@AB@B@LFNDDJBBDDDB@@B@B"],["@@ABGNANAF@J@B@N@HBFBDDFDBFABAJOJAFF@JBFHDH@HEBGDGHGF@HJNLJBJ@HCBGEMKICACAEBAAAABCACAAGAE@C@AD@HADC@EAA@CBABEAEAECEGOKEEAG@A@CAAA@"],["@@CEES@cCUK[ECQEGCGGS[ACMKQCICIBKDWHGDOLUTKNQVKPanINKJQJONOVCHAHAbIPMTCNA\\tF@HFADLD@BA@AD@B@B@BBD@BB@HF@@@DFlX@BJ@@FFDADADD@@@BBB@DD@BBBB@B@B@BBFADBDB@DA@BBABAB@A@B@@@B@@@@@BA@B@BAB@BBA@@@B@@@@@@@B@@@@@@@@@@A@@@@@A@@@@A@A@@@B@@A@@@@B@BB@@A@B@@@@@@A@@@A@AB@BBBBF@CJHD@DBBBBB@@BB@@AB@@AB@@DBBB@BAFDAFDBD@@@BBBDH@BBFBD@DEHBB@DE@@CAC@@@EBGEDADCF@@@ACACAECGGEACCE@AHGJHJKHIEGABGHIJA@OODCGGAACCBC@A@ARNFGDEBABC@ABAD@FADAD@DAB@DAD@N@D@DADAFALCB@B@F@HBET@@CJ@DBDHR@BFFBB@BFDFF@@@@HFDB@BJDHFFDBBB@@@FDPN@@DBBDJEDA@AZO@ABA@@B@@@DBLHB@ADDBDADDABADJFFBBAAABA@BBABBADB@BCFADBFEDFB@DFBDHIFIPUhZDFJFNLLHJFjDERaFKJQBEFMVgBCBAIAKGGCQGECEECEAE@C@E@ICKMOeWcGAAEAWGGEAC"]],encodeOffsets:[[[115833,22417]],[[115851,22482]],[[115882,22481]],[[115854,22446]],[[115859,22356]],[[116053,22492]]]}},{type:"Feature",id:"440403",properties:{name:"斗门区",cp:[113.296467,22.2092],childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@@@@@BBBA@@@AA@@@A@@@A@BB"],["@@DDBBHHCDPPB@JIHGBAFHGJILIGGH@BDFBDHFDHBFBDBD@@E@CDCBHFFA@@D@DB@@CFA@GACFC@EAAAG@ACAA@@C@CABEECABA@AA@CA@@BA@@BA@@AA@AAAA@CGCDIE@AAAAA@@B@B@@@B@@@@A@B@@@AAA@@@@@@BA@@@B@B@@@@@@B@@@@@B@@@@@@@@A@@@@@@@A@@@B@AAA@ABA@B@@A@@@@@A@@@A@BBABAAAB@@CCACAEBAAA@A@A@AA@ACCA@AA@@C@BCBCEC@EI@@AkWCE@@E@@GAAC@AAA@A@C@@BABC@CKEB@GsEEx@LA^BBHRH@HBH@HDHDFFFBDJJFDAFEBBFLDFGBABARRFBFBJDTFRHBBHFJLBDBLBHBLDNBFBD@F@H@B@HBH@@@@DDDDHJ@DBPBN@JBH@@@J@RBHBJBFCBAB@B@DBF@@B@DBBL@@EDC@ADCLCDCDGDGDCDCBA@CDCDBBB@@@@F@DC@A@@@@@@F@D@JDDBBD@JA@@D@BBBDD@B@D@DCDCBABEDI@ADO@@B@HBPDJHBHBB@B@DBFDBFADCBCD@FBH@FEFAD@DBNHDDD@FATKTKDAFGHILKBADCRU@@LM@@@@FGNKFIHK@Q@I@@@A@@BEBEDKJSLWBCBEBEDIBADIDIDCDGFE@@DCDANITIB@DABAD@BBFCB@@@@ABABCB@BBDBB@B@FEJMDCPSFEDCDEJAHAH@B@BBCGCEOGIGBE@@N[LSIEKGMKIECEgYOVEJGJACCEA@CEEFCAEBADA@BCAAAB@AABBBABEAIEBCBACCCBCABCA@KGCA@@A@@@AB@BYP@BCBIFACCA@@OMEC@@A@AAECGEIC@ACAGE@@@@EEEC@AAAEE@AGQAC@CDI@@FSGAE@A@A@KDEBCBCBC@M@C@CBA@CBC@CBEBC@AB@BADABCFEHQM@B@BAD"]],encodeOffsets:[[[115800,22625]],[[115921,22631]]]}}],UTF8Encoding:!0}):void D("ECharts Map is not loaded"):void D("ECharts is not Loaded")})
