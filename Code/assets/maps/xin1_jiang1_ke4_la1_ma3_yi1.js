!function(B,A){"function"==typeof define&&define.amd?define(["exports","echarts"],A):"object"==typeof exports&&"string"!=typeof exports.nodeName?A(exports,require("echarts")):A({},B.echarts)}(this,function(B,A){var D=function(B){"undefined"!=typeof console&&console&&console.error&&console.error(B)}
return A?A.registerMap?void A.registerMap("克拉玛依",{type:"FeatureCollection",features:[{type:"Feature",id:"650202",properties:{name:"独山子区",cp:[84.886974,44.328095],childNum:1},geometry:{type:"Polygon",coordinates:["@@@C@AC@CAA@AB@@AAA@ABA@CBGDE@E@E@CAC@C@IAECEAAAAAAAAACAGCCASKCACCAAGCECKEGCEAECCAAACAA@A@ABCBA@BD@DFJDJBDBDNLNRLLFFDHJJFLDJFNHJFTDDLT@F@D@J@D@BDPDNBHDJ@FEN@DBD@F@DAD@DCN@J@FAFADIJCDAB@DBDBDBD@DAJABCFCL@DAJAH@L@D@D@D@DAH@J@N@H@H@B@J@F@DP@LAAR@AjAjA@@B@R@V@AJAHALAXGPC~SHAUTEzQ@@BE@G@C@@@A@A@A@A@@AA@A@AA@@@BC@A@A@A@A@@@A@CAA@C@AAE@@@A@AA@@@AAA@ABA@IBEBG@KBC@CBGBI@CBC@EBA@C@A@A@CBEAK@C@E@E@A@E@@BGAK@M@C@EAA@CAG@QEE@E@EAGCAAGCACAACAKEAAA@A@KAA@AA@A@@BC@A@AACEEAAAC@ACE@@AA@G@C@CAA@AAA@A@@AEAACACAAAAAC@ACAAA@EA@@CC@AA@@@@A@C@A@A@AAAAA@@B@@A@@@CB@@A@C@AA@@A@@@@@A@@@@@@@A@@@A@A@@@@@AA@@@@@@A@@@@@A@@@A@A@@@@CE@@@A@A@A@C@A@@@ABAAC@A@A@C@G@@AAA@@C@AAEACAAAA@ACAAEACECCGCC@A@AAA@@A@CAI@E@A@CACAIEAAEAC@C@C@EAE@G@A@EAC@EAC@C@C@A@@BA@ABEF@@A@A@@@C@A@ABA@AAEAAAA@A@C@A@AAA@@AAA@@AA@A@AAAAAC@"],encodeOffsets:[[86869,45224]]}},{type:"Feature",id:"650204",properties:{name:"白碱滩区",cp:[85.131696,45.687854],childNum:1},geometry:{type:"Polygon",coordinates:["@@@@A@A@@@@C@ACU@AA@CGCECEAAAAAEEAKGMGCACCA@ACACMU@@GOIQAE@AAAQMGGEEECQOECSUIGEEIIII@@A@CACAEAEACACAMECCYOGACCEAAAKAeGSCEAyIA@A@C@CAC@A@A@AAGCA@AAA@@@A@CBE@A@@BA@E@CBEBA@A@CAC@AA@A@AA@@@A@@AA@CIQN[TMJSPshIHIH@BIFc^YVADIF@@@BC@EFYRAB@@A@ABIH@@@@@@KHKHA@ADEBCDABA@KJAAEA@@@@GN@@@@@BELA@BDB@@BAD@BA@@BA@C@C@G@CAA@@B@@@@A@CAM@IAE@S@C@@@@A@@BHAD@BGLEJA@CDA@@BBD@JC@KDIBA@EDABAF@@@D@B@BBF@BB@@@RDVBDBFBA@CHAH@FAB@BAD@@@FAHgHB@@DDDB@@BDBBBDBBBB@@@@@@BAB@@B@@@@@B@@CBC@@@AB@@@BD@BABAB@B@@@@DBB@@@DBBAFADA@AB@@@B@BB@@@B@@B@B@@@B@@@@@ABA@AB@@@B@@B@DBDBB@DBB@@@F@@BB@@@B@B@BB@@D@B@@@BBD@B@@BD@@@DBB@B@D@@@B@D@B@@B@@B@@A@@D@@A@@@AA@@@@@@ABA@@BB@@BA@@B@BAD@BB@@@@BBABB@@@@B@@B@@@D@@BB@@@@@BB@A@@B@ADB@B@@@BAB@HAB@@AB@D@B@B@@@B@BAB@B@D@@@DAB@F@B@FA@@@A@@@AB@@@BBB@@A@@B@@@@@@B@@D@@@B@@@B@@A@@@@B@DBB@@AB@@B@B@@B@DBB@BBBBBBD@D@DB@@@B@@@@@BH@@@BBBB@@B@@@@BB@BDB@B@BBDDBB@@DA@@@@@BB@AB@@@B@@B@@AB@F@B@@DB@B@B@B@@@@B@@D@B@B@@B@B@@@@DA@@B@B@@BAB@@BB@@BA@ABBDBB@B@BB@@@@@@B@BB@@A@B@@@B@B@@@@@AB@B@@@@BADB@@@BA@A@@@@@B@BBB@B@@@@@@@@B@@@@@@B@B@B@B@@@B@@B@@@BA@AB@@@@@@D@@@B@B@@BA@@@BB@@B@@@@BB@B@@BBB@@B@B@@@B@@B@@@@CB@@@@BB@@B@@A@@B@BA@@B@B@@@@BABABA@A@AD@@@@@@BAB@B@B@@@@@@BA@@B@@B@DC@@B@B@@B@@@@@D@@@@H@@@@BA@AB@@B@B@@@@BA@@@@@B@D@@B@BB@@@@B@@@B@@B@D@B@@B@@A@@@@BB@A@@@CBA@@@B@D@B@B@B@@B@@BBB@A@@BEBCB@@@BD@BBB@@BB@@B@@B@B@@B@B@@D@@@@@@BCB@@@BD@@@BBB@D@@@B@B@@@AB@@@@B@@@B@@BD@@@@B@@C@AB@@B@D@@AD@FABBB@B@@BB@@@B@B@@B@@@@A@C@E@@@@@@BB@B@B@BB@@BBB@BBBDBBD@@B@@@@@@B@@@BB@@@@ABA@B@D@BB@@B@BB@@@B@@C@@@@@@@BBBBB@@@@A@A@A@@B@@@B@BB@@BAB@@@@B@@CB@@B@B@B@@@@BCB@@B@D@@@@@@BA@AB@@BB@@@@@BA@@@@@@@@BABA@A@@@@B@@@@B@@AD@B@@@@BA@@@@B@@D@BB@@D@@@@BB@BBBBB@BB@@@B@@BB@@@BA@@@@@B@@B@@BB@BC@@@BB@@È¦¯Z@@@@@B@B@@@B@@@B@XA@@@@H@NAD@H@@@@@D@ÌKB@D@B@ED@\\AB@@@NAB@\\AD@LAB@H@B@B@F@DAB@B@DBD@B@DB¨{RGI[Mg@A@@A@@@@A@@@@B@B@@A@@@@@@DA@A@@BA@A@A@E@ADA@A@@AAC@CAACGGOKCEAAAC@AA@@A@@A@@AAAA@AAA@CBA@@@@AAA@@@@BA@@BAA@@AA@@A@CB@BA@AC@A@A@@AB@BA@@@AB@@A@AAA@AAAA@C@GC@@A@A@@@@@@A@@CCA@@A@@@AE@A@A@A@@@@AAA@@AA@A"],encodeOffsets:[[87599,46684]]}},{type:"Feature",id:"650203",properties:{name:"克拉玛依区",cp:[84.867844,45.602525],childNum:1},geometry:{type:"Polygon",coordinates:["@@@E@ABAVkRcHMDG@ABAHMDIDC@@FMBCDGBA@@@ALSHO@A@@FIHOFKwCY@IAG@@@A@C@EA@AAACK@CA@@@A@A@A@EEAAA@E@A@ABA@CDCBADADA@A@AACA@@A@CBAB@B@@A@AAA@@B@@@B@B@BA@EF@BIDA@@D@D@BEDEBABAD@DABAD@@@B@@@@CBA@AF@D@@@@ABA@@@@B@BAB@@CAA@C@@@C@A@CBGDEBA@A@@BAB@@AAAA@@C@E@EFAD@BBFHFCHCF@@CDA@C@@A@AA@A@KBA@@DA@EDGDG@CAG@CBA@ABABBB@@DBDBBB@@@B@BA@AB@@ABA@@@@B@B@@@@@DABaNIDEB@@AB@BELABABKHA@CBEDEDCB@NBB@@@@D@B@TAZ@Ch@LAf@@A^@P@RA@Y@@B@@@@@B@FBJ@B@DC^@B@F@F@H@B@FBlPAJHSFRLBVD^FlBNH@@JBLBN@HCBADEDEDGFEDEFCDGFCBADCB@@EBA@@BABABAB@@@B@BAD@BABCFADAD@BCDAHABAFCDCDAFABABCBCBCDIJA@ABCBCB@BCF@BA@CDGHCVAPE\\C^@@@D@FAJAZB@BB@BD@DB@B@@@@A@A@@BB@DA@@@B@@@B@BB@@@@@@BB@D@@BB@@@AB@@B@B@@BB@BB@@@B@@B@@B@BB@BBB@NHFFB@BB@@BBB@@@@BB@B@@B@@BBB@BBB@BBBDB@BBB@@@BB@@BB@@@B@@@BB@BD@@B@@DB@@BDB@BBDBB@@@@@@DA@B@@@@ABA@@@BBD@@@BDB@@D@@@B@@D@@@BDBB@D@@@D@D@@@BAB@@A@@BB@@@FAB@B@@@@BAB@@@@D@B@@B@@@BA@@B@@@@B@@@BBAB@@@@ABA@@@@BB@@B@@A@@B@B@@BBDBBBB@D@@BB@B@BA@@DAB@@@@@ABABABABADADA@@BB@BA@@@BBB@B@BB@@@BB@@B@BB@@B@@@B@@A@AB@@@@@FBBB@@@B@@AB@B@@B@@B@@@BB@B@B@B@@@B@ABABBB@@@@@BA@ABA@@@@@BB@@BBB@BD@@B@B@@@@@@BCB@@@@B@@@D@D@B@B@D@@B@@A@A@@B@BA@@@@@D@@@BAB@@@@@@B@@BBB@BB@@DBB@@@A@AB@@@@@B@@@BDD@@BBAB@F@BAB@B@@@@B@@@B@BB@D@@B@@@D@@@B@B@BB@B@@@B@B@@BBD@@@B@@A@@B@@@B@B@F@@@@@@BAB@@@B@@B@@@@BAB@@B@@@BA@@BAB@@@B@@B@B@@@BA@@@BB@@@@@BDA@BB@@@BBBB@B@BB@CD@@@BBB@B@B@@@D@@BBB@BB@@@B@@B@@@B@@D@B@B@B@BB@@@@@BAB@B@@@BBDB@BA@CB@@A@@@A@@@ABAA@@@B@@B@BBD@B@B@@@@@B@@@@@B@@@@@@B@@@B@B@@@@ABB@@@DA@BB@BBA@AB@B@@@BB@@@@B@BBBAB@B@@BB@@B@@@@@@D@B@BBD@@AB@@BB@@BAB@@@@B@@@@AB@@ABAB@@@@B@@@@B@@@@@@B@@B@@AB@@AB@@@@@BB@B@@@BB@@D@D@B@@@@@@@CD@B@B@B@@@@B@B@@@B@@@A@AB@@@@D@@@@BAB@@@@B@@BA@@B@@@B@@D@B@@@@B@@AB@@@@@@BB@@@BA@@@AB@BA@C@AB@@@@B@@B@BB@AB@@@@D@B@@B@@A@@BC@@B@@@B@@@BB@BB@@@B@@@@AB@@ABABBB@B@B@BBBB@B@@BB@FBB@@AB@B@@B@@BB@@B@BB@B@@@B@@B@BBB@@@B@BB@BBB@B@@@@B@@@B@@@BB@@@BA@@@@BBAB@@@@@@B@@ABCBCD@@@@@@B@B@@BB@A@@B@@@@D@@B@@A@@B@@B@@@@@AB@B@@@BB@B@@ABABAB@B@@@@@@@@BA@@@@@@@BBFDDDDD@BBDB@@@B@@@@@@@@B@@AB@@@B@@@@@@AB@@@@B@B@BA@@@B@@@BC@@B@BA@@@@@BBB@BB@@@@@@@B@@@@D@@@B@@B@@DB@@@B@@@@@BA@@@A@@@@B@@@@A@@B@@B@BA@@@@BB@@A@@@@BB@B@B@B@@@BB@@AB@@@BB@@@@B@@DDBB@@@@B@F@@@DDB@BA@BB@@@@B@@@@B@B@@BB@DBBBBBBBBBB@@D@@BB@@@@B@BAB@@@@B@@@@@BABB@@@@@@B@@@BAB@@@B@B@BADAB@@@@BB@@ABADC@MPADCBCBSHAB@@CAC@A@@B@@B@@B@B@BAFAF@BB@BBD@FDDBFBBBBDBBAB@B@@BDDBH@@@B@BADABAHADC@ABE@@FCHAD@@@B@F@@BBAD@D@@@B@DADAFC@@CC@@@A@I@CAGHEFCB@BBBDB@BB@B@@@BB@B@@@DA@@B@DABAD@F@F@BBBBB@D@@B@B@BB@B@B@B@@@@@BB@@B@@@B@B@B@@BD@@AB@@B@@B@@@D@DAB@B@DB@@BBDADDB@@@@@@B@@@@@@B@@@@@DBBBFBB@FBB@@BB@B@@AB@BB@@B@B@DB@@@@B@@@@@B@@@B@@@B@@BDB@@A@@B@@A@@@@BB@@@D@@B@@B@@BDB@BBD@B@F@BB@@@DDA@BB@B@@DB@@AB@B@@@BDBBB@B@DB@DD@B@B@@BB@B@BB@B@@@FDDBB@@AB@@@@@BB@B@@B@B@@@@@@BBBDB@BBB@B@B@B@BB@@BB@BB@@BB@@hGBG@E@@BC@ABA@EBGDGB@EACAUAQC@@A@@AAE@A@A@C@@BEBAFCB@JALCD@@IAC@AB@DCB@FIHK@ABCAG@@@B@@D@T@F@JBN@DBB@@@@@@AB@DBH@D@D@B@@AB@@ABC@AA@ACB@FK@A@@@@HM@@@@FBBBLIB@BADCFABCB@LGLG@@@@@@JGBAB@@@BAZQFED@@A@@JEBCZUd]JE@AJGJGtgTONI\\SRMAACAm]ACCECECCAEEICE@@JK@@CCCGCEACBG@ABGBG@CBEBEBEBGBE@E@G@CAGAE@EAG@EAEAGAE@ACECG@@OYCC@@ACAACCCCCAACEAACGEAACCACACEG@A@ADKBE@CBE@A@E@G@M@G@EBAHILOJKX[LI@AB@DCHGFEBAB@@@@CAEAC@A@AAKAAAEACACACAA@GACCQ@A@C@KAOAU@E@A@@@@BABA@@@C@E@@DEDAFCB@@@B@@@BABABABODE@ACG@CDABCHMDMAEAEEEYOA@AAA@]HE@CBKFA@eDoMKkEOAGAGCOEM@AOEWIICGAOEICOE_MMCGCIAUGICQC@AAAMQCECEACAAACAC@AAE@@AC@AAAAGACAA@CACACAC@@BCBEDUBQ@A@ACSGGIOAABAAC@E@I@GBE@ABE@ABA@CBA@ADCBADEBABABEDCB@@AB@B@DCD@@ADC@@B@D@@@@A@@BC@A@G@KBI@E@@A@CCMG@@MEGAOCQE@@A@@@@BA@@BCDA@@@ABABAD@@CDCBGDCB@@A@CAA@@@AA@@GBCBCB@@AB@BCDA@ABCBCDCDCBABA@@@@@ADEFAB@BAHABAD@DA@EFA@A@I@CBC@@BC@@@ABCD@BA@KCUCGAA@GBG@Q@ABEBKFKFIFUJABKFCBA@CDIDC@@@@AA@CAKAC@C@EEA@QGGAOGA@GAKAGAIA@@"],encodeOffsets:[[86828,45884]]}},{type:"Feature",id:"650205",properties:{name:"乌尔禾区",cp:[85.693742,46.089148],childNum:1},geometry:{type:"Polygon",coordinates:["@@H@B@DBD@H@FA@@D@LBB@H@r@V@Z@f@P@B@BAJCLEDAB@DEJI@@B@DBB@H@BBBBB@BAB@BBB@D@F@FAB@B@@@B@@@B@BB@BB@@@B@B@B@DAFAB@D@B@B@@@D@BDB@BBFD@@@@LAJAL@LAF@H@L@J@FA@@FC\\QJEJGp]r_TMFC@AFMBEBABE@ABABADABABABA@AAA@@CA@ABA@AFEFCBCBC@ABABIBA@CDCHEFABEBAAAOGCAGAIEA@C@[GC@AAYIMECACA@KAI@A@C@AAAIKEE@AAABGAI@CBABCBE@@ECCCGCECAAA@A@CAA@A@@@@CBC@@B@@@@ADG@@@AGCECAC@A@ADADABCB@@@@AB@@@DAB@BABA@C@@AACA@@A@GBCBC@A@AB@@ABABC@KD@@A@@@@AAA@@A@A@C@A@C@AAA@@A@ABAB@@AA@@@C@A@IBGBA@A@@@@ABABAFAD@@A@A@AA@C@GBA@@@A@BA@AA@@AA@A@@@AAA@A@A@A@A@A@@ABA@@AA@BC@@BC@@@A@AAA@@A@@@AA@C@A@@@@A@@BA@@@A@@AC@@@@@@D@BABADAD@B@DB@@DA@A@@@AAA@A@@A@A@A@ABC@C@@AB@@A@@B@@AA@@@@@BA@@CAC@A@AA@@@AC@E@@@@@@ABA@@A@C@@@@A@@BA@A@@AAA@@@@CA@A@AAC@C@C@C@A@ABA@ABA@@@@A@A@@@@A@@@A@@ACA@AEAKB@@A@@@@ABA@@@@@@CAA@BC@A@@AA@@AAA@@A@A@A@@A@@@AA@@AAA@A@A@AA@A@@A@A@A@A@@@A@@@@ABA@@@AA@A@C@@AB@B@@@@AGCA@A@A@@@BA@A@@@@A@CA@ABA@@@@@@A@E@@@AAA@AAA@@A@@@AA@@@AA@@@@A@A@@@AA@@BA@@A@A@@@A@@AAA@@@A@AA@@@A@A@AAAAA@ABA@@A@@@ABA@@@@@@G@@BCA@@@@@AC@@@AA@@@C@@C@@@A@@@@@BA@@@@@@A@CB@A@@AAA@@@DA@A@@A@ABA@@@AAB@@@@A@@A@@@@AA@@@E@A@@@@A@@@A@@@@CB@A@@A@AABA@@A@A@@@@@@A@@@A@@AAA@@@CA@@CB@@A@@@@ADA@@@AAAA@AB@@AABAAAA@@@@@BA@@@A@@A@ABA@A@@@@@@A@@@@A@A@@@@A@@AAA@@@@A@@A@@@A@@AB@@@@AA@A@@BA@@@A@A@@@@@@A@@BA@@A@AACAA@A@@@A@@@@B@@A@@@@AA@@@@@BA@@@@@AA@A@@@BA@A@@@@@@C@@@@A@A@@A@A@@BA@@A@@B@@A@@@@A@A@@@A@@@A@A@@@DC@@@@@AABC@A@A@@A@@@ADA@@C@A@A@A@@A@@A@E@@A@@@@@@BA@@@@C@@@A@@@@A@@A@AAA@@@@AB@@@@AA@A@@@AB@@@A@@@@A@@AA@B@@A@@@@A@@@A@@@A@A@@A@@BA@@A@C@AA@@A@@A@@@@@A@@A@C@A@@@@@@A@@@@A@@ABA@@@@A@A@@AA@@@@@BA@@@@AAAB@@A@@@@A@@@AA@A@A@@@@@@A@@C@A@AA@@BA@@@@AAC@@@@@AA@@A@AAC@A@@A@A@@@@A@A@@@A@@@@@A@A@A@A@@@BA@@AAA@C@@A@@@@AA@@C@@@AA@@AAC@@@CCAAA@CCA@AACAAAMI@A@A@@BA@A@AB@BADAB@@@@A@@AAAAC@@AA@AA@@@AB@@AAAAAGCA@MGECECCAAIAIAAAKAG@IE_AE@ICGGWCICKQH§|CAA@C@CAA@A@CBE@A@A@G@A@KBC@[BA@MB@@A@[BC@FA@C@A@ËLC@@@@@G@C@MBG@@@@@WBA@@@A@@@A@A@@@@@Y@¥°Ç@B@@@@@@BBB@@@@@@B@@A@@BBBB@DB@@B@@B@@B@@@BB@B@@A@C@@B@@@@@@FAB@@B@@A@@B@@A@@@A@@@@BB@@@@B@@@@A@@@@@@@D@BB@@@BA@ABCBA@B@@@FA@@D@@@@A@@B@@B@@BA@@B@@B@@@B@@A@@@@@@@B@BB@@@@CB@@@@@@@BG@A@@BB@@@DAD@B@@@@B@@@@A@A@@@@BB@B@@AB@@@B@@BBBBBB@@@B@@BA@@@A@A@A@A@@@A@ABC@A@@@@@D@BB@@BAB@D@B@BD@@@@@B@@@@A@@@A@C@A@@@B@@@@BC@@@@@@@@@B@B@DB@@@@@@@@@BCA@@@B@@D@BB@@B@BA@@@B@@AB@@B@@@BA@@@@@@@B@@@B@@@@@BB@@@ABAA@B@@@@B@@@@B@@@@@@@BBB@@BDB@@@@B@@A@@@@@B@BBD@@@@@@@@B@@C@C@A@A@A@@@@@@@DB@@@@@B@@@@@@@@BA@@B@DB@@AB@@A@@@@@@@B@BB@@@B@@A@A@@@BBB@@@@@@@@B@@B@@AB@@@@B@@@BA@@BA@BDA@@BA@A@@@BBA@@BB@DAB@B@DA@@@@@@@B@@@@B@@A@B@@@BA@@BBBAB@B@@A@AAA@@B@@BB@@BB@@@@@@BBB@@ABB@@@@@@@@D@@@B@B@@@@BAB@@@@@@@@B@B@@@@@@@@@@B@@ABCBA@CBA@E@@@@@@BB@F@@@B@D@@A@@BB@@@@A@@B@@@@B@BA@B@@@@@@@BA@@@@@@B@D@@@B@AB@@@B@@@@@@B@@BB@BD@@@B@@B@@A@AB@B@@@@BADAB@@@@BA@@BA@BB@@@@BA@@D@@@B@@B@BA@@BA@@@B@B@B@BB@@@B@@A@ABA@CBAD@@B@BABADAB@@@B@ABA@@@B@B@@@@B@@ABB@@B@@@@@B@@AB@BCDA@CFA@A@@@A@AA@@A@@BB@@BD@BB@@@@DAB@DA@@@@@BB@@B@@@A@@BA@@@@BBB@BBB@BB@@@B@@@B@@@@B@BBB@BA@@B@BB@@@B@@A@@BB@@@@@B@@@B@@@@@@B@@A@@B@@B@@@B@B@@@B@B@@B@@BB@@@@@@ABC@@@@B@@DB@BCB@@AAA@@B@@A@A@A@ABCA@@ABC@@@@@@@B@F@@B@@B@D@DB@@@@@BA@A@A@ABA@@B@@@@B@@@D@@B@@BB@@@A@@BA@A@AB@B@BBDB@@B@B@B@BA@BBBD@@B@@@@A@C@A@@@@B@B@@@@ABA@@@BB@@@B@@A@A@AAA@AC@@A@AB@@AA@@@@A@ABI@AAA@@B@@@@BBD@D@B@B@@@B@B@@BB@@B@@A@A@CAA@@@@@BBB@@@DB@@@@ABABA@@@AB@B@@@@DA@@@B@@B@@@@@BA@@B@@@@ABAB@B@B@B@@BAB@@ABCBAB@@ABA@@@A@@@A@@B@@@BB@AB@BB@@@B@B@B@@ABABC@A@AB@B@BC@@@@B@DBB@FBBDDB@BA@A@@B@@B@BAB@@B@@BB@@B@@@@BAB@BEB@@C@@BA@C@A@A@EBA@CA@@@A@@@@@A@@@@EDAB@@B@BB@@@@@BA@E@CBA@A@@@AA@@A@A@AB@@@@D@@@@@@BAB@@@@BBB@DD@@@B@B@@B@BAB@@@BB@@@B@BC@@@C@@@AA@@A@AB@BB@@@@@@BB@B@@@B@@@B@@@@B@B@@B@B@@AB@BB@@@B@AD@H@J@@@@@@@BB@@@@DAB@BBBB@B@@CBA@@@B@@B@@B@@@CB@@@BBADAHAB@B@DB@@@B@D@B@@@@@@A@AA@@@@@BBB@@AB@@AAA@A@@@@@@BBBAB@BABA@@B@@@@B@D@@@DB@B@@BBBD@B@@@@C@@@EA@@@@@B@B@B@@@@A@AAAAAA@A@@A@@@@B@@@@@@A@@@C@AAGCA@@@@B@@B@@BBBD@@BB@@@B@B@BBDB@@@B@@I@A@@B@@F@B@DB@BB@@@@B@@A@A@A@@B@@@@@@BBB@@B@@@@A@@@E@A@@@BD@@D@BB@@BBA@A@IAA@A@@@@@DBD@@B@@@@C@A@@B@@BDBB@B@@@@ABABA@G@A@@@@B@@@B@@AB@@@F@BA@@@@B@@@@@B@@A@@B@@B@BB@@@@BDBB@@A@C@A@ABAB@@A@AB@@B@BB@@@B@@@BAB@@ABA@@@@@BBB@@@F@@@B@@@@B@@@@ABA@AB@@@@BBB@@@@B@D@@A@@B@@B@B@@@BB@B@@@@@BA@@@B@B@B@@@@D@D@@B@B@B@@@BB@BB@A@@B@@@@BB@@@@@DA@@@@BB@B@DAB@@@ADABABA@@B@@@@JC@@@@@@@@ABAB@@D@@B@@@B@BAB@B@@DB@@@F@FB@BBB@BBB@FBLFD@FBH@F@DBFBB@DBFB@BBBDJBD@B@BDJ@B@B@@B@@@B@B@FDDBDBBDB@B@H@BBB@BBF@D@HBB@D@D@BBD@JJDBB@FBHBBB@@@DBDBBDBD@DBB@FBB@B@@C@AB@@AFAB@@A@A@A@@FCB@F@HC@@B@DADCBEB@B@B@@@DAFBHFF@BBDAB@B@@@@BBBBB@B@BBBDD@@@@@BCD@BAB@B@B@BFBBDB@DBBBB@F@B@B@BB@@BBB@BAB@DBD@D@DDD@LDFBFBD@JD@@B@B@@@BB@BA@A@@@@B@@B@@B@B@@BBB@H@B@BBB@DBBBD@@@DB@@BBBBD@@BB@D@@@@BBBBBBBFBHBHBB@BBD@DBDBB@DBDDBBBBD@D@D@BBFDHDBBB@B@DBD@HDB@F@DBBAD@B@B@D@@BB@B@BBB@B@D@BBFBB@BB@@@@@DB@@BB@D@B@B@@@D@B@DB@@BBF@D@BAB@B@@@B@B@BBBBB@BD@@BBJ@BB@@H@B@@AB@H@FAD@BA@@B@B@J@FBB@F@DB@@BBB@FC@@@A@AB@BC@ABADCB@DAB@BA@@A@@A@@B@DARGLEFA@@F@LB"],encodeOffsets:[[87746,47301]]}}],UTF8Encoding:!0}):void D("ECharts Map is not loaded"):void D("ECharts is not Loaded")})
