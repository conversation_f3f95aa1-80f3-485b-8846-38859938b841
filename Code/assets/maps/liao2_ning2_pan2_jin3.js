!function(A,B){"function"==typeof define&&define.amd?define(["exports","echarts"],B):"object"==typeof exports&&"string"!=typeof exports.nodeName?B(exports,require("echarts")):B({},A.echarts)}(this,function(A,B){var D=function(A){"undefined"!=typeof console&&console&&console.error&&console.error(A)}
return B?B.registerMap?void B.registerMap("盘锦",{type:"FeatureCollection",features:[{type:"Feature",id:"211102",properties:{name:"双台子区",cp:[122.039787,41.19965],childNum:2},geometry:{type:"MultiPolygon",coordinates:[["@@@@RDJD@B@BBBBDC@@FFDHDGD@@@A@@AC@@GCA@@@GDCGOHE@AB@DD@@DBBBBCDIAMCEAWKCHGHADD@@@B@HJJLLP@@@JHHJBNANGJGFCDA@@B@NCHAH@J@FMFG@@@AFGDKDGAC@AAEAIAGGGGECC@AEAEAGAG@G@E@EBG@EAG@BAGEGEGHAB@@AAA@@@AF@BAD@@AB@BAB@@AHTDDBND@@AJH@B@@CB@CC@@"],["@@F@PGDHHC@@B@HD@@BD@@@B@@HCGCEC@@@B@@AAAA@@ABAAA@BAAA@@CCCCACAAAAA@@@AADDA@@DA@G@BI@@MCCASCBG@@BA@ABA@@BC@ABEBIB@@@BABABAB@BA@ABAB@@@AC@@B@@A@A@@@A@@OEA@G@G@E@g@@F@FALKAUEEAG@@@@DCBCBE@@BA@@@@B@@@@@BA@ACEIECDCHCDCBC@AAACEA@AAC@A@CBGFA@A@@@CAC@CAGAI@G@GAK@IAG@KAAC@@@MAAOAALFFBLBLMB@DCBA@@@AAAAEB@@BD@BBHDLBNnFDBJFFHBBHPFL@@N@N@EJ@BDBB@FBBBLDDBFBF@FBDBB@D@B@@@B@BB@@B@@@D@D@@BB@BB@@@BB@@BB@@@B@@BB@@@BB@@B@BBBB@@@BB@BDBBBCBA@@@@HGBCJKDBB@BCHGDGXLFBNDJBDCAAAA@CC@@CBA"]],encodeOffsets:[[[125015,42194]],[[125009,42209]]]}},{type:"Feature",id:"211104",properties:{name:"大洼区",cp:[122.082574,41.002279],childNum:5},geometry:{type:"MultiPolygon",coordinates:[["@@BB@B@@B@@BD@@AB@@@B@@AB@@@AABAD@@BDD@@FLFJ@AFO@QAODW@A@@WCA@E@QAE@@@@@@DDADD@BC@@B@@BB@@BB@@BD@@@@BDBABD@@@BDA@DA@BB@@BDDABDDA@@B@@B@@@B@B@@BBAB@@A@BDBA@@@@BBBDA@C@ABA@@C@@A@@@A@AAAA@@@@BA@A@@@@@@@AB@AA@@E@@@@@@@AC@@AA@@AC@@CGAAGMB@ACA@@@@A@@@@A@A@DFDHFHDFBDBDBBBB@BFH@@BB@@B@BB@@ABA@CBCBC@@@"],["@@@@@AAAAB@@ABMUDAAAB@@BB@ACB@@@ACHAACCADAEGCGCEeC@@A@A@CB@@@E@@BEHAEG@AWHEN@BHA@BBBEB@BB@cLCBB@[HID@@CBGB@@ABoPZBBBLBH@NFJCPDD|F@FM@ATK@CBODALNE@XL@`@H@AHNBAPFJNCD@DAXCLCNCZK@@DAFCFADCBAHC@AfOHCPEPGBMAY@CDKLIJG@CEIEK@@ID@@A@EB@@A@ABMD@@@@ABEBACA@@AVGB@DA"],["@@BB@BD@BDHAHAAA@@@C@@B@AA@AAA@@@@BAB@B@DABF@@@@@@@A@A@@@@@C@@CB@AAAB@B@@AC@@@ABAA@@B@@@DAAA@CCBC@BD@@@B@@EBALBB@@@@@BB@@@ABE@@BAA@A@AEB"],["@@EBK@EAIBMBM@U@ECGAACCECCACAE@@AAAUaBBIDG@CHAQSW[KM£KJXLVDVENKF[@eBcFiZULKLCBABAFANBDDDFDDBCHEJEDMDEBGFEDCHAJEPCNCPEFOBGAM~ALAJDNFJTNf`RJLJRXh@BB@T\\FNFP@BBHCFEDWBC@a@QAm@C@ABCHCF@FBDDFHFRLRDJBHBF@F@DBFBDDFFDFDDBDDBDDJBF@F@DADCHID@B@D@@BBBDDBB@D@NBLBHBJBD@B@DBDBDBDFH@D@@DAHAHCD@D@F@D@B@H@F@BAB@@C@A@EAA@ABABAF@FAHGDA@@DABBFBD@D@DDB@B@B@@ABAD@B@@@B@BBB@FAFGBEBABGBAPADBGJJ@HABUBUB@F@BBBBADN@@@DDF@AJRGJCD@^EACAED@BAFJD@AEPDFAF@D@FBL@@BX@^B@BJ@B@TBfDVBDOEI@MAGAKAAYpOBACC@AJCA@@CB@@A@@@@AC@A@AbKDFB@BBABBFBDDAdKA@KD@@AACENCFMXGFAFAFA@@BKEI@A@A@@D@BA@DBABBDDDA@DBBJAFA@@@@A@ABCBA@CB@@@BDADLE@@@D@@@BDFNA@@@@BCBAA@@@@CG@@A@@@A@CGA@JN@B@D@@BDDFB@B@BBF@RBF@B@XDBABU@OBIDEFK@A@A@A@@BBD@D@DABADCFEDEB@BAB@B@D@ACAC@ABABA@@B@BADBF@FBJHHDBBB@DA@@@A@ACEEIAEGCGEKAEAE@GBGBEBA@A@AA@A@ABEDA@AFCFATCHCDABA@@@C@AACA@OECCAA@EBEBEFI@A@CACCACAG@A@CBEDEFC@A@C@AA@@AA@A@ABCJEFCBC@AAAAAAAEAE@@@AA@@BAB@B@F@L@B@DABABC@@@I@GACAAA@KBKBCB@@@BBDFJ@B@BCDC@C@G@GE@A@C@A@CAEAEEGOQOKAACCECGICAAAC@CDGHABE@EBGAG@OGEAM@IAEAAACKCEAACAA@CBGBGDOLKNMNEDC@A@A@CAAAACCI@MACBGFKBCFEHGBG@A@AAACACAEBMBEBSJGBE@CAECAAEGEEACCCGIIEAAiSKECHEBI@@LC@MDI@EBGDCBGDKD"],["@@AOl@@DH@@FD@@KJ@FADGCAGGAC@ABCFCBABANGTGFANENG@@XQ\\OVIJCHCDABABCNCBCD@BABBB@D@HAFABA@ADA@ABA@ABC@KACAICCCCEEFCHA\\@B@@@B@@@^@PBF@@@B@B@L@RAHC@ADCBC@A@E@C@@@A@AAAA@AAOGA@MECAMEA@K@G@EJGDA@G@IBADWDDACAURMK@BSRKHGD@@E@E@GCGECIAIDWK[E@YIUQqMC_Jqj¡CuR_~¤LNX\\RTGB@DCHAJbABVBB@@BFBDDDDFBDHBFDV@N@NAJAFBL@FALC@@@@@I"]],encodeOffsets:[[[125229,42045]],[[125230,42047]],[[125200,42001]],[[124991,41806]],[[124997,41799]]]}},{type:"Feature",id:"211122",properties:{name:"盘山县",cp:[121.996411,41.242639],childNum:8},geometry:{type:"MultiPolygon",coordinates:[["@@C@KBDTBDFA@A@CHA@@CC@CBA@@AE"],["@@E@E@IAABBDFFDFLLDLFC@@@ACG@@@A@A@AB@@ABC@@@A@BDGB@@@@A@A@@@@AA@@@@@AA@CCCDCB"],["@@^EF@@CAEAI@@ACCACGAAAA@@C@EBE@C@@@ABA@A@GBSBA@A@@FFX@BBB@FDAB@@@H@D@B@FBB@BBBD@@@@@AAG@@@@B@@@@@"],["@@LA@FJAJA@@@@@@CE@@@@@AAC@@HA@@BB@@JA@@LAB@`GvG@@LC@@BFŠAS[A@@AWgIQIK_QeSMEICMBIBKN}OC]QaWQMACBWAEE@EBMREBAAAC@MCKGCSEQ@UCIE@GCCSC@YFEH@JKFi@KAOEQ@KAACGKCGYOICG@CB@BADBDPRHP@DCFAD@BBBDDJDHLFRDF@@@BFHRTPRJFPHJDHFJDN@F@DDDHDFDH@BDBBHDFFFDFDFBF@FAD@FBB@BDDDBDDHBJDAXDLDTFBFABFV@AJQBBHBF@@@@@LGTABGRCJABEDA\\DDJA@@"],["@@@DBBDA@DC@CA@BD@BB@DAB@DB@H@X@B@@@@@@@B@@@@@BBB@JLBJDA@@BBB@BA@A@ACC@@EE@A@@C@AAFAAAB@@ABABBJA@@B@DFB@DBBCBBDBABCDHEAAACA@@A@@AAAAA@@@AA@@A@@AA@@@A@@AA@@A@@AAA@@AC@C@@@A@@@AAA@@@A@C@A@CAEAE@EACAKCAAAB@B@DAH"],["@@CCPGISAAIQ`@ACAQAMACCEIIEKEU@CCGBW@MKMKCOGMAOCA@KAEAACPKDAHILMDEPEPCLCLAXBHGJMRJNG^LTE`JXGNL@@@@HFSLdFB@JHDHBFVJLDFPPPNPFFDBLKRIBZNCZA@@RAJALPdAPGIISGSSIGCCIKKKWCSAO@E@CK@@@@AEIGKEQ@A@WICAQOCKDSHI\\C@S@CAS@KFGFGLIAE@MBQPOB@LEHMAI@CAMBE@UEQCSBGBKHKRSMUIKEIEACBKPCBE@EAAC@Q@ASDGDEDGHCFEDE@CAGCA@MKKI@AC@A@A@ABA@CFEFCDABCBC@C@AA@@@B@B@BELCFAJ@PAVAB@@@BCXBP@REP@B@DIHKJCL@DBZANOHOFGDeP@BGDABCDEBEDCB@@YLMDKDWDCBC@MDEB@@EDE@KBCBMBKBEBIBKCAAcM@DBDC@C@[D@JB@@DB@@B@B@@B@B@@@@C@AB@B@@@B@@BB@ABEB@B@B@FE@@LK@@B@@@HB@ALD@@L@BDDHFHHBHBJBF@BBDCHCLEH@B@@EHENI@G@GBMDA@@@CBEDIHMHMBIAGG@I@@KOIKGIA@@@C@A@CAILADGH@@@@ABADGFABCBABABIFA@GFACB@@A@@B@AIIKA@ADA@ABCB@CB@@C@@B@@AW@G@@JF@@DCB@BDABBCBG@ADABAAA@FC@@@C@E@@C@@CCA@AAAACH@AGAA@AH@@A@@@A@ABBBGCA@A@AD@BAEAA@CA@AFIM@M@@@EKGOAAEGIECAmEA@C@C@G@EBE@GBBDDPB@DALAJ@@FBFDDBDBDC@DJq@K@C@K@E@@B@@AB@@@@LPA@K@MACA@BBBABA@@BAB@DAB@@CGMWAAECEACAAA@A@ACA@@@AD@F@BAD@@AA@C@AA@@@ABAB@CCKEAAE@ICCCBK@CBAFCD@BA@AGAAADE@AEACAAAACMUAAE@GAE@CC@CCAAAEFA@AB@@AA@@GEAAA@ABA@CDUJA@AA@A@AAGAABAB@FBFBD@DCBA@CAAC@E@A@CACAAACE@A@AB@B@B@FADABA@CBCBAB@@ADGBCBABADABADCBEDAHAFADCDCBA@A@CBADADAFADAD@HBFCBCBABCHGGAIAQCQKGECEAC@EDEDGBAD@n@RBb@D@XAFCDEAG@AEOEMşBC@GB@@@AAAuHBDBA@BBBMD@CKB@@A@DFGBBFGBED@@BBDLJTO@CG@@@@CEGKCG@@ABA@C@AB@@AA@@@CIB@@A@ABDD@BDDMDDHC@@@RbFJK@CNPBB@@DJD@DD@@AHA@ED@DCD@L@BFF@@@@@@EN@@GR@XAVAD@BHTABP@D@@@B@@D@DB@@@BEBABCBGDCBGFIFGDBAFM@@UBK@E@A@@EC@@@CAA@@@A@C@G@MBG@@@@AA@EBEAMEGAA@A@]AWDaBU@SBK@OB@@@@@@EB]FMP@@EJ@@@@CFKRAD@L@@@@@BDBBB@B@BCBFHBB@@@@@@PPDDLJFFPP@@@@HJ\\BFJDB@BA@BR@B@D@LWFKDWCGDMJADAB@F@@@@AH@@AN@FBJFRNH\\HDDJFB@@@@B@@NFLLNHBBADLHJFHCHBHD@JBDJBLARHLHDBNFZRFDHBHBD@LAHDFHDFDBBDLHLFLDFALAHA@EN@D@D@JBBDAJ`@JLFDFDF@B@DC@GPB@B@@@DODVDNBF@RB@@N@F@BAHCFCB@ZM@A@A@@@A@@BABMA@DAB@PIJCHDFHDDDBBBBBB@BBB@DBDD@@@B@BABABAB@B@@BBBD@@DB@BBB@D@@EFA@@B@@N@DBB@DDFBBBBB@@BD@B@@DBDBDBB@BA@@BABBDBB@B@B@@@BBABD@RBB@BB@@@BAB@BHDBB@B@D@DLJXANAL@H@LBVBR@JCJCHEDOFAH@H@HBHFDBTHDBTF`JJDLH@B@@@@@B@B@@BB@B@@@B@@B@F@B@JFBBFBDBDDFHBBDBJDPFTGLEEE@CB@@ABAEGPIGEACJCBABA@A^KB@CEFA@KB@BADCXECGAACGA@@ACGCEJACIH@BBHAACR@DBFAL@@DF@HBfCEI@@JCFLB@PJB@@@@@@BHGGBAE@@A@@CEBEKZE@DB@@B@@B@@DB@@BDAB@@B@@@@B@"],["@@BDDDDD@@BBABB@BBBA@@BBBB@@@A@@@ED@ACAA@A@AICQC@@@@BB@@B@BBBB"],["@@BBC@@A@@@@AB@BB@@@@@@BA@@@@@@@A@@B@@GDGHA@@BA@@@@ABAOIA@IBeDGAE@@CK@EBCAQ@BDGBAAG@DJIBDFDH@BB@DHBBDHWFCDABA@@LEBDFA@]L@BABABIDBDHFOJFHAB@BA@@DFFKFSHD@THKL@BJBRHNFDBDDJJFCBBDFJH@BPEHHJNLCKMCCB@JEB@CCBAFA@AAAEGAA@@BABC@ADCA@LMBABA@@@@@@JOJINOJMHKNMLM\\OTILEHADCBAAAACAA@ABABA@A@C@ABAB@BA@@AAA@AA@G@@CECCEABCBK@CCE@@@@IK_@JRBBJTDAD@B@@@B@@B@@@B@@A@ABAAABAAKFB@A@ABB@ABA@"]],encodeOffsets:[[[124809,42107]],[[124814,42091]],[[124811,42144]],[[124588,42035]],[[124915,42220]],[[125192,42329]],[[125019,42197]],[[125191,42329]]]}},{type:"Feature",id:"211103",properties:{name:"兴隆台区",cp:[122.070769,41.119898],childNum:32},geometry:{type:"MultiPolygon",coordinates:[["@@@@@@A@AB@B@@B@@AB@@A"],["@@@B@BDB@C@AC@"],["@@@@CBA@@@@BA@BDHEAA"],["@@@@EB@B@BBB@AF@BA@@A@@A@@@@AA"],["@@AA@@@@A@@@@@@@A@@BA@@@@DA@@DDABAB@BC"],["@@HAD@AE@@KD@@BB@B@@"],["@@@B@BFJFA@@@A@@ACD@CCAAAB@CABC@@@"],["@@JAEKID@@FJ"],["@@AA@AGB@AMDDFBB@@LC@AFA"],["@@PC@C@@@AOA@HCDD@"],["@@HBBEA@DIB@BEGADIMA@@@@@BE@@FPDGV"],["@@AC@@AA@@AA@@@AD@@ACCCB@C@@@@AAA@@@@@@B@@B@BDA@HNBBDH@@BD@@BB@@BD@@@@@@F@@@BBA@@B@@@@@@@BAB@@@@BBBBB@@@B@@@@DB@BAD@B@ACAA@@@@ABACB@@@BAAA@@@A@A@@@AA@@@CBACCBAC@@AAB@@CCB@A@@ACABAC@@@@"],["@@BB@@B@DDDBHC@@BA@@B@@AB@@@@@@@B@@A@@@@@@B@A@@A@@@@A@@@@@A@@@@@@@AA@@@@@@A@@A@@@@@@@@@@@@@@@@@@@@@@A@@A@@@@@@@@@@A@@AA@@@@@AA@@B@@@@@A@@A@@A@@@A@EB@BA@@@A@@B@@@@@@@@AB@@@@A@@@@@@@@@@B@@@@@@B@@@@@@B@@@@@@@@@@A@B@@@@B@@@@B@@@@@@@@@@@@@@B@@@@@@B@"],["@@AB@BA@BBEBBBD@@@@BFF@@DD@B@BABJEBABADABADCBACAAAADCAA@CEA@@@IBAA"],["@@@AA@@CA@@@@AA@@CYFFLFA@DB@@@BFHAHC@@@AB@@@@@@@B@@A@@@@A@@ABA@@@@@BD@AA@@@@@AA@CB"],["@@@QED@DUAADCDCHN@X@@@@C@@"],["@@DAB@B@@@fDB@CEAC@@@C@AIM@@@D@@BBGBGBACC@@AAAEB@BFHGBAF@@@F@@"],["@@@EKB@@IB@BE@A@OHCJADN@PCD@D@DF@@D@CGNCCC@ACCBAB@@@"],["@@DD@@HADA@@JC\\GA@ACAEBAAAA@CEaL@B@BBD@@@@@BA@@DB@ID@B"],["@@D@EEKK@@OL@BCDBJFAB@B@B@DAD@B@B@DAD@@@@@@A@@A@BA"],["@@FCHAAEHACEB@@@LA@DNCAA@AABAC_HA@KB@@IB@@AA@@GB@@BD@B@@@@DF@@@@@@IB@D@@BB@@BAD@B@BA@@DHHLDF@@@@DHP@ISCKAA@@"],["@@B@TAHAB@B@BA@@D@AEEBACEB@BEB@CIB@BGB@F"],["@@@@A@A@@ACBACC@K@DNC@WB@@BNL^LFFNH^NPPHTNRHF@L@D@L@r@CID@ACACCCAE@EI@KBCBA@COACACIB]D[DIWCSE]@EAA@AEW"],["@@QGSMOGMOG]EMKEK]AM@@XAD@CML@D@BDDA@BB@B@@@@EB@@EHA@AJA@DFA@AACCSLAD@BF@@AB@DDD@@GB@D@BBDFABFF@FAD@@@BBBBDHDBBD@@BJBF@DE@]F@@@@A@@@@@BH@B@@@@ACAAA@EAA@C@G@@@A@CBF^DTJX\\C^CJABDHAF@FAH@D@D@B@AMCKAG@AAC@@FABBBB@@B@DA@CNAAKAKEEBKPBBB@N@@BDLBH@JBL@HBH@J@HBDBD@DB@@B@B@HEDAB@D@BBB@DFBB@BADCDGDCDFDFJBDB@@A@@@@@A@@B@@AF@DADA@C@@H@FBVFLBBK@E@Eh@F@H@H@B@PF@@@B@@@B@BA@@@BD@@A@AB@BABA@ABABAB@@A@AJ@@B@BB@@BAHGHFHFABH@FBH@FAF@H@H@HBFBFB@KC@BKA@@G@@@AL@@KF@@E@A@AFABAA@@AA@@@A@A@@B@D@@A@A@@@@A@AA@@CA@@I\\CD@D@AC@CdNBBLDJAFALANADALAF@FC@@FAEIBOMABGG@_@K@@WMFBKPCDAL@BSN@@E{ECCUAeCSAA@I@@A]AW@@AK@EAC@E@EBOCBFC@EIABC@BFBD]FC@IDQHBIE@CC@@M@BCAAAAE@A@AVAVGBI@HICAOBABAHABAFEHEBA@AAA@@@A@C@AB@BA@A@A@CCC@C@EAAACB@@CBGHEBE@ABAB@BBB@F@B@DA@ABE@G@A@C@E@C@C@GDGBCB@@@CEGACACAC@C@AACAIAGAK@M@CAACCAA@AC@A@C@GJDDB@@B@@@@BB@@@@@B@B@@A@CH@A@B@@AD@BA@@B@B@B@@DH@B@@EDCKKKCEEEACBACCCAACCCCEEECCEACAE@E@GHADABADEDGAC@CBEBCBCBAB@D@BABCDCDEBGBCBAFCDABCBABABADCH@BA@ABAD@DABCBEBA@A@A@@B@BDFBBDBDBB@F@D@BB@DABCDC@EAEAA@ABBBBH@B@BBBB@VIDCB@BAB@BBHF@@BB@@BAB@FEBBDB@DDDF@HBF@BBNVBDBBDBFB@BCFBBHB@BABC@EDAB@DALDDJDF@BBLFDDA@AB@B@@BBD@B@@BC@ABE@C@@B@@DB@B@BBBDBFBFDBBNXDH@@BA@CBA@AB@BAAA@ADBNBL@B@KO@@@@BA@@@A"],["@@CBDBBDGBBD@@A@BDA@@AA@BBCBNVBA@@BA@@D@DADAB@BA@@AAA@@@AA@@EG@AAAAAACACCE"],["@@UH@BB@BDFABA@@@@NCBAB@@@FAB@@@JCCC@AC@ABBB@@A@@BA@@@A@@BC@@AA@CBA@"],["@@BB@BDA@@@D@@@@@B@B@@F@CKCBA@A@"],["@@@@BBBA@@D@@@DAB@DABAB@@@@@EBIBCB@@A@"],["@@@@@@HCDAHCFAJ@NC@EG@@Ck@BP@J"],["@@@CBA@CAAC@@ADBD@@CCBAA@CAA@B@B@@@BG@@BBBBHG@BDBB@BDB@DD@@@@F@D@@EDB@BBBABCH@DAAACB@ADA@CE@@IA@"],["@@BAA@BAB@A@LEBBBABBBAB@@@@A@@@AA@@@A@C@CBOHDD"],["@@@@AECBA@A@AB@@@@BB@BBBDHB@@@B@@@DH@@@@BBDA@A@@B@EMAC@@C@"]],encodeOffsets:[[[125180,42338]],[[124914,42213]],[[124945,42241]],[[125206,41999]],[[124936,42231]],[[124662,42021]],[[125204,41986]],[[125165,42334]],[[125187,42015]],[[124731,42342]],[[124510,41950]],[[125227,42025]],[[125852,42520]],[[124944,42228]],[[125188,42329]],[[124421,41990]],[[125195,42014]],[[124594,42037]],[[125139,42030]],[[126019,42941]],[[124615,42037]],[[124792,42126]],[[124791,42129]],[[124820,42205]],[[125225,42026]],[[125227,42048]],[[125213,41993]],[[125211,41992]],[[124997,41804]],[[124916,42230]],[[125192,42329]],[[125216,41998]]]}}],UTF8Encoding:!0}):void D("ECharts Map is not loaded"):void D("ECharts is not Loaded")})
