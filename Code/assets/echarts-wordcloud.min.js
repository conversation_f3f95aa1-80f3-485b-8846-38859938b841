!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("echarts")):"function"==typeof define&&define.amd?define(["echarts"],e):"object"==typeof exports?exports["echarts-wordcloud"]=e(require("echarts")):t["echarts-wordcloud"]=e(t.echarts)}(this,function(t){return function(t){function e(n){if(r[n])return r[n].exports;var i=r[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,e),i.l=!0,i.exports}var r={};return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:n})},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=25)}([function(t,e){function r(t){if(null==t||"object"!=typeof t)return t;var e=t,n=W.call(t);if("[object Array]"===n){e=[];for(var i=0,a=t.length;i<a;i++)e[i]=r(t[i])}else if(E[n]){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var i=0,a=t.length;i<a;i++)e[i]=r(t[i])}}else if(!N[n]&&!F(t)&&!T(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=r(t[s]))}return e}function n(t,e,i){if(!S(e)||!S(t))return i?r(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!S(s)||!S(o)||w(s)||w(o)||T(s)||T(o)||M(s)||M(o)||F(s)||F(o)?!i&&a in t||(t[a]=r(e[a],!0)):n(o,s,i)}return t}function i(t,e){for(var r=t[0],i=1,a=t.length;i<a;i++)r=n(r,t[i],e);return r}function a(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}function o(t,e,r){for(var n in e)e.hasOwnProperty(n)&&(r?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function s(){return document.createElement("canvas")}function l(){return z||(z=U.createCanvas().getContext("2d")),z}function h(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r}return-1}function u(t,e){function r(){}var n=t.prototype;r.prototype=e.prototype,t.prototype=new r;for(var i in n)t.prototype[i]=n[i];t.prototype.constructor=t,t.superClass=e}function c(t,e,r){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,o(t,e,r)}function f(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function d(t,e,r){if(t&&e)if(t.forEach&&t.forEach===H)t.forEach(e,r);else if(t.length===+t.length)for(var n=0,i=t.length;n<i;n++)e.call(r,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(r,t[a],a,t)}function g(t,e,r){if(t&&e){if(t.map&&t.map===Y)return t.map(e,r);for(var n=[],i=0,a=t.length;i<a;i++)n.push(e.call(r,t[i],i,t));return n}}function p(t,e,r,n){if(t&&e){if(t.reduce&&t.reduce===V)return t.reduce(e,r,n);for(var i=0,a=t.length;i<a;i++)r=e.call(n,r,t[i],i,t);return r}}function v(t,e,r){if(t&&e){if(t.filter&&t.filter===j)return t.filter(e,r);for(var n=[],i=0,a=t.length;i<a;i++)e.call(r,t[i],i,t)&&n.push(t[i]);return n}}function m(t,e,r){if(t&&e)for(var n=0,i=t.length;n<i;n++)if(e.call(r,t[n],n,t))return t[n]}function x(t,e){var r=X.call(arguments,2);return function(){return t.apply(e,r.concat(X.call(arguments)))}}function y(t){var e=X.call(arguments,1);return function(){return t.apply(this,e.concat(X.call(arguments)))}}function w(t){return"[object Array]"===W.call(t)}function _(t){return"function"==typeof t}function b(t){return"[object String]"===W.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"==e}function M(t){return!!N[W.call(t)]}function T(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function k(t){return t!==t}function C(t){for(var e=0,r=arguments.length;e<r;e++)if(null!=arguments[e])return arguments[e]}function P(t,e){return null!=t?t:e}function I(t,e,r){return null!=t?t:null!=e?e:r}function A(){return Function.call.apply(X,arguments)}function O(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function D(t,e){if(!t)throw new Error(e)}function L(t){t[G]=!0}function F(t){return t[G]}function R(t){t&&d(t,function(t,e){this.set(e,t)},this)}function B(t){return new R(t)}var z,N={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},E={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},W=Object.prototype.toString,q=Array.prototype,H=q.forEach,j=q.filter,X=q.slice,Y=q.map,V=q.reduce,G="__ec_primitive__";R.prototype={constructor:R,get:function(t){return this["_ec_"+t]},set:function(t,e){return this["_ec_"+t]=e,e},each:function(t,e){void 0!==e&&(t=x(t,e));for(var r in this)this.hasOwnProperty(r)&&t(this[r],r.slice(4))},removeKey:function(t){delete this["_ec_"+t]}};var U={inherits:u,mixin:c,clone:r,merge:n,mergeAll:i,extend:a,defaults:o,getContext:l,createCanvas:s,indexOf:h,slice:A,find:m,isArrayLike:f,each:d,map:g,reduce:p,filter:v,bind:x,curry:y,isArray:w,isString:b,isObject:S,isFunction:_,isBuiltInObject:M,isDom:T,eqNaN:k,retrieve:C,retrieve2:P,retrieve3:I,assert:D,setAsPrimitive:L,createHashMap:B,normalizeCssArray:O,noop:function(){}};t.exports=U},function(t,e,r){function n(t){i.call(this,t),this.path=null}var i=r(12),a=r(0),o=r(6),s=r(48),l=r(54),h=l.prototype.getCanvasPattern,u=Math.abs,c=new o(!0);n.prototype={constructor:n,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var r=this.style,n=this.path||c,i=r.hasStroke(),a=r.hasFill(),o=r.fill,s=r.stroke,l=a&&!!o.colorStops,u=i&&!!s.colorStops,f=a&&!!o.image,d=i&&!!s.image;if(r.bind(t,this,e),this.setTransform(t),this.__dirty){var g;l&&(g=g||this.getBoundingRect(),this._fillGradient=r.getGradient(t,o,g)),u&&(g=g||this.getBoundingRect(),this._strokeGradient=r.getGradient(t,s,g))}l?t.fillStyle=this._fillGradient:f&&(t.fillStyle=h.call(o,t)),u?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=h.call(s,t));var p=r.lineDash,v=r.lineDashOffset,m=!!t.setLineDash,x=this.getGlobalScale();n.setScale(x[0],x[1]),this.__dirtyPath||p&&!m&&i?(n.beginPath(t),p&&!m&&(n.setLineDash(p),n.setLineDashOffset(v)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a&&n.fill(t),p&&m&&(t.setLineDash(p),t.lineDashOffset=v),i&&n.stroke(t),p&&m&&t.setLineDash([]),this.restoreTransform(t),null!=r.text&&this.drawRectText(t,this.getBoundingRect())},buildPath:function(t,e,r){},createPathProxy:function(){this.path=new o},getBoundingRect:function(){var t=this._rect,e=this.style,r=!t;if(r){var n=this.path;n||(n=this.path=new o),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var i=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||r){i.copy(t);var a=e.lineWidth,s=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),s>1e-10&&(i.width+=a/s,i.height+=a/s,i.x-=a/s/2,i.y-=a/s/2)}return i}return t},contain:function(t,e){var r=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),i=this.style;if(t=r[0],e=r[1],n.contain(t,e)){var a=this.path.data;if(i.hasStroke()){var o=i.lineWidth,l=i.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(i.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),s.containStroke(a,o/l,t,e)))return!0}if(i.hasFill())return s.contain(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):i.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var r=this.shape;if(r){if(a.isObject(t))for(var n in t)t.hasOwnProperty(n)&&(r[n]=t[n]);else r[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&u(t[0]-1)>1e-10&&u(t[3]-1)>1e-10?Math.sqrt(u(t[0]*t[3]-t[2]*t[1])):1}},n.extend=function(t){var e=function(e){n.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var r=t.shape;if(r){this.shape=this.shape||{};var i=this.shape;for(var a in r)!i.hasOwnProperty(a)&&r.hasOwnProperty(a)&&(i[a]=r[a])}t.init&&t.init.call(this,e)};a.inherits(e,n);for(var r in t)"style"!==r&&"shape"!==r&&(e.prototype[r]=t[r]);return e},a.inherits(n,i),t.exports=n},function(t,e){var r="undefined"==typeof Float32Array?Array:Float32Array,n={create:function(t,e){var n=new r(2);return null==t&&(t=0),null==e&&(e=0),n[0]=t,n[1]=e,n},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:function(t){var e=new r(2);return e[0]=t[0],e[1]=t[1],e},set:function(t,e,r){return t[0]=e,t[1]=r,t},add:function(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t},scaleAndAdd:function(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t},sub:function(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t},len:function(t){return Math.sqrt(this.lenSquare(t))},lenSquare:function(t){return t[0]*t[0]+t[1]*t[1]},mul:function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},div:function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:function(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t},normalize:function(t,e){var r=n.len(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t},distance:function(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))},distanceSquare:function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,r,n){return t[0]=e[0]+n*(r[0]-e[0]),t[1]=e[1]+n*(r[1]-e[1]),t},applyTransform:function(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[2]*i+r[4],t[1]=r[1]*n+r[3]*i+r[5],t},min:function(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t},max:function(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}};n.length=n.len,n.lengthSquare=n.lenSquare,n.dist=n.distance,n.distSquare=n.distanceSquare,t.exports=n},function(t,e,r){"use strict";function n(t,e,r,n){r<0&&(t+=r,r=-r),n<0&&(e+=n,n=-n),this.x=t,this.y=e,this.width=r,this.height=n}var i=r(2),a=r(8),o=i.applyTransform,s=Math.min,l=Math.max;n.prototype={constructor:n,union:function(t){var e=s(t.x,this.x),r=s(t.y,this.y);this.width=l(t.x+t.width,this.x+this.width)-e,this.height=l(t.y+t.height,this.y+this.height)-r,this.x=e,this.y=r},applyTransform:function(){var t=[],e=[],r=[],n=[];return function(i){if(i){t[0]=r[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=r[1]=this.y+this.height,o(t,t,i),o(e,e,i),o(r,r,i),o(n,n,i),this.x=s(t[0],e[0],r[0],n[0]),this.y=s(t[1],e[1],r[1],n[1]);var a=l(t[0],e[0],r[0],n[0]),h=l(t[1],e[1],r[1],n[1]);this.width=a-this.x,this.height=h-this.y}}}(),calculateTransform:function(t){var e=this,r=t.width/e.width,n=t.height/e.height,i=a.create();return a.translate(i,i,[-e.x,-e.y]),a.scale(i,i,[r,n]),a.translate(i,i,[t.x,t.y]),i},intersect:function(t){if(!t)return!1;t instanceof n||(t=n.create(t));var e=this,r=e.x,i=e.x+e.width,a=e.y,o=e.y+e.height,s=t.x,l=t.x+t.width,h=t.y,u=t.y+t.height;return!(i<s||l<r||o<h||u<a)},contain:function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},clone:function(){return new n(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},n.create=function(t){return new n(t.x,t.y,t.width,t.height)},t.exports=n},function(t,e,r){"use strict";function n(t){return t>-b&&t<b}function i(t){return t>b||t<-b}function a(t,e,r,n,i){var a=1-i;return a*a*(a*t+3*i*e)+i*i*(i*n+3*a*r)}function o(t,e,r,n,i){var a=1-i;return 3*(((e-t)*a+2*(r-e)*i)*a+(n-r)*i*i)}function s(t,e,r,i,a,o){var s=i+3*(e-r)-t,l=3*(r-2*e+t),h=3*(e-t),u=t-a,c=l*l-3*s*h,f=l*h-9*s*u,d=h*h-3*l*u,g=0;if(n(c)&&n(f))if(n(l))o[0]=0;else{var p=-h/l;p>=0&&p<=1&&(o[g++]=p)}else{var v=f*f-4*c*d;if(n(v)){var m=f/c,p=-l/s+m,x=-m/2;p>=0&&p<=1&&(o[g++]=p),x>=0&&x<=1&&(o[g++]=x)}else if(v>0){var y=_(v),b=c*l+1.5*s*(-f+y),S=c*l+1.5*s*(-f-y);b=b<0?-w(-b,T):w(b,T),S=S<0?-w(-S,T):w(S,T);var p=(-l-(b+S))/(3*s);p>=0&&p<=1&&(o[g++]=p)}else{var k=(2*c*l-3*s*f)/(2*_(c*c*c)),C=Math.acos(k)/3,P=_(c),I=Math.cos(C),p=(-l-2*P*I)/(3*s),x=(-l+P*(I+M*Math.sin(C)))/(3*s),A=(-l+P*(I-M*Math.sin(C)))/(3*s);p>=0&&p<=1&&(o[g++]=p),x>=0&&x<=1&&(o[g++]=x),A>=0&&A<=1&&(o[g++]=A)}}return g}function l(t,e,r,a,o){var s=6*r-12*e+6*t,l=9*e+3*a-3*t-9*r,h=3*e-3*t,u=0;if(n(l)){if(i(s)){var c=-h/s;c>=0&&c<=1&&(o[u++]=c)}}else{var f=s*s-4*l*h;if(n(f))o[0]=-s/(2*l);else if(f>0){var d=_(f),c=(-s+d)/(2*l),g=(-s-d)/(2*l);c>=0&&c<=1&&(o[u++]=c),g>=0&&g<=1&&(o[u++]=g)}}return u}function h(t,e,r,n,i,a){var o=(e-t)*i+t,s=(r-e)*i+e,l=(n-r)*i+r,h=(s-o)*i+o,u=(l-s)*i+s,c=(u-h)*i+h;a[0]=t,a[1]=o,a[2]=h,a[3]=c,a[4]=c,a[5]=u,a[6]=l,a[7]=n}function u(t,e,r,n,i,o,s,l,h,u,c){var f,d,g,p,v,m=.005,x=1/0;k[0]=h,k[1]=u;for(var w=0;w<1;w+=.05)C[0]=a(t,r,i,s,w),C[1]=a(e,n,o,l,w),(p=y(k,C))<x&&(f=w,x=p);x=1/0;for(var b=0;b<32&&!(m<S);b++)d=f-m,g=f+m,C[0]=a(t,r,i,s,d),C[1]=a(e,n,o,l,d),p=y(C,k),d>=0&&p<x?(f=d,x=p):(P[0]=a(t,r,i,s,g),P[1]=a(e,n,o,l,g),v=y(P,k),g<=1&&v<x?(f=g,x=v):m*=.5);return c&&(c[0]=a(t,r,i,s,f),c[1]=a(e,n,o,l,f)),_(x)}function c(t,e,r,n){var i=1-n;return i*(i*t+2*n*e)+n*n*r}function f(t,e,r,n){return 2*((1-n)*(e-t)+n*(r-e))}function d(t,e,r,a,o){var s=t-2*e+r,l=2*(e-t),h=t-a,u=0;if(n(s)){if(i(l)){var c=-h/l;c>=0&&c<=1&&(o[u++]=c)}}else{var f=l*l-4*s*h;if(n(f)){var c=-l/(2*s);c>=0&&c<=1&&(o[u++]=c)}else if(f>0){var d=_(f),c=(-l+d)/(2*s),g=(-l-d)/(2*s);c>=0&&c<=1&&(o[u++]=c),g>=0&&g<=1&&(o[u++]=g)}}return u}function g(t,e,r){var n=t+r-2*e;return 0===n?.5:(t-e)/n}function p(t,e,r,n,i){var a=(e-t)*n+t,o=(r-e)*n+e,s=(o-a)*n+a;i[0]=t,i[1]=a,i[2]=s,i[3]=s,i[4]=o,i[5]=r}function v(t,e,r,n,i,a,o,s,l){var h,u=.005,f=1/0;k[0]=o,k[1]=s;for(var d=0;d<1;d+=.05){C[0]=c(t,r,i,d),C[1]=c(e,n,a,d);var g=y(k,C);g<f&&(h=d,f=g)}f=1/0;for(var p=0;p<32&&!(u<S);p++){var v=h-u,m=h+u;C[0]=c(t,r,i,v),C[1]=c(e,n,a,v);var g=y(C,k);if(v>=0&&g<f)h=v,f=g;else{P[0]=c(t,r,i,m),P[1]=c(e,n,a,m);var x=y(P,k);m<=1&&x<f?(h=m,f=x):u*=.5}}return l&&(l[0]=c(t,r,i,h),l[1]=c(e,n,a,h)),_(f)}var m=r(2),x=m.create,y=m.distSquare,w=Math.pow,_=Math.sqrt,b=1e-8,S=1e-4,M=_(3),T=1/3,k=x(),C=x(),P=x();t.exports={cubicAt:a,cubicDerivativeAt:o,cubicRootAt:s,cubicExtrema:l,cubicSubdivide:h,cubicProjectPoint:u,quadraticAt:c,quadraticDerivativeAt:f,quadraticRootAt:d,quadraticExtremum:g,quadraticSubdivide:p,quadraticProjectPoint:v}},function(t,e,r){function n(t,e){e=e||C;var r=t+":"+e;if(S[r])return S[r];for(var n=(t+"").split("\n"),i=0,a=0,o=n.length;a<o;a++)i=Math.max(A.measureText(n[a],e).width,i);return M>T&&(M=0,S={}),M++,S[r]=i,i}function i(t,e,r,n,i,s,l){return s?o(t,e,r,n,i,s,l):a(t,e,r,n,i,l)}function a(t,e,r,i,a,o){var h=v(t,e,a,o),u=n(t,e);a&&(u+=a[1]+a[3]);var c=h.outerHeight,f=s(0,u,r),d=l(0,c,i),g=new _(f,d,u,c);return g.lineHeight=h.lineHeight,g}function o(t,e,r,n,i,a,o){var h=m(t,{rich:a,truncate:o,font:e,textAlign:r,textPadding:i}),u=h.outerWidth,c=h.outerHeight,f=s(0,u,r),d=l(0,c,n);return new _(f,d,u,c)}function s(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function l(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function h(t,e,r){var n=e.x,i=e.y,a=e.height,o=e.width,s=a/2,l="left",h="top";switch(t){case"left":n-=r,i+=s,l="right",h="middle";break;case"right":n+=r+o,i+=s,h="middle";break;case"top":n+=o/2,i-=r,l="center",h="bottom";break;case"bottom":n+=o/2,i+=a+r,l="center";break;case"inside":n+=o/2,i+=s,l="center",h="middle";break;case"insideLeft":n+=r,i+=s,h="middle";break;case"insideRight":n+=o-r,i+=s,l="right",h="middle";break;case"insideTop":n+=o/2,i+=r,l="center";break;case"insideBottom":n+=o/2,i+=a-r,l="center",h="bottom";break;case"insideTopLeft":n+=r,i+=r;break;case"insideTopRight":n+=o-r,i+=r,l="right";break;case"insideBottomLeft":n+=r,i+=a-r,h="bottom";break;case"insideBottomRight":n+=o-r,i+=a-r,l="right",h="bottom"}return{x:n,y:i,textAlign:l,textVerticalAlign:h}}function u(t,e,r,n,i){if(!e)return"";var a=(t+"").split("\n");i=c(e,r,n,i);for(var o=0,s=a.length;o<s;o++)a[o]=f(a[o],i);return a.join("\n")}function c(t,e,r,i){i=w.extend({},i),i.font=e;var r=P(r,"...");i.maxIterations=P(i.maxIterations,2);var a=i.minChar=P(i.minChar,0);i.cnCharWidth=n("国",e);var o=i.ascCharWidth=n("a",e);i.placeholder=P(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<a&&s>=o;l++)s-=o;var h=n(r);return h>s&&(r="",h=0),s=t-h,i.ellipsis=r,i.ellipsisWidth=h,i.contentWidth=s,i.containerWidth=t,i}function f(t,e){var r=e.containerWidth,i=e.font,a=e.contentWidth;if(!r)return"";var o=n(t,i);if(o<=r)return t;for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var l=0===s?d(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,l),o=n(t,i)}return""===t&&(t=e.placeholder),t}function d(t,e,r,n){for(var i=0,a=0,o=t.length;a<o&&i<e;a++){var s=t.charCodeAt(a);i+=0<=s&&s<=127?r:n}return a}function g(t){return n("国",t)}function p(t,e){var r=w.getContext();return r.font=e||C,r.measureText(t)}function v(t,e,r,n){null!=t&&(t+="");var i=g(e),a=t?t.split("\n"):[],o=a.length*i,s=o;if(r&&(s+=r[0]+r[2]),t&&n){var l=n.outerHeight,h=n.outerWidth;if(null!=l&&s>l)t="",a=[];else if(null!=h)for(var u=c(h-(r?r[1]+r[3]:0),e,n.ellipsis,{minChar:n.minChar,placeholder:n.placeholder}),d=0,p=a.length;d<p;d++)a[d]=f(a[d],u)}return{lines:a,height:o,outerHeight:s,lineHeight:i}}function m(t,e){var r={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return r;for(var n,i=k.lastIndex=0;null!=(n=k.exec(t));){var a=n.index;a>i&&x(r,t.substring(i,a)),x(r,n[2],n[1]),i=k.lastIndex}i<t.length&&x(r,t.substring(i,t.length));var o=r.lines,s=0,l=0,h=[],c=e.textPadding,f=e.truncate,d=f&&f.outerWidth,g=f&&f.outerHeight;c&&(null!=d&&(d-=c[1]+c[3]),null!=g&&(g-=c[0]+c[2]));for(var p=0;p<o.length;p++){for(var v=o[p],m=0,y=0,w=0;w<v.tokens.length;w++){var _=v.tokens[w],S=_.styleName&&e.rich[_.styleName]||{},M=_.textPadding=S.textPadding,T=_.font=S.font||e.font,C=_.textHeight=P(S.textHeight,A.getLineHeight(T));if(M&&(C+=M[0]+M[2]),_.height=C,_.lineHeight=I(S.textLineHeight,e.textLineHeight,C),_.textAlign=S&&S.textAlign||e.textAlign,_.textVerticalAlign=S&&S.textVerticalAlign||"middle",null!=g&&s+_.lineHeight>g)return{lines:[],width:0,height:0};_.textWidth=A.getWidth(_.text,T);var O=S.textWidth,D=null==O||"auto"===O;if("string"==typeof O&&"%"===O.charAt(O.length-1))_.percentWidth=O,h.push(_),O=0;else{if(D){O=_.textWidth;var L=S.textBackgroundColor,F=L&&L.image;F&&(F=b.findExistImage(F),b.isImageReady(F)&&(O=Math.max(O,F.width*C/F.height)))}var R=M?M[1]+M[3]:0;O+=R;var B=null!=d?d-y:null;null!=B&&B<O&&(!D||B<R?(_.text="",_.textWidth=O=0):(_.text=u(_.text,B-R,T,f.ellipsis,{minChar:f.minChar}),_.textWidth=A.getWidth(_.text,T),O=_.textWidth+R))}y+=_.width=O,S&&(m=Math.max(m,_.lineHeight))}v.width=y,v.lineHeight=m,s+=m,l=Math.max(l,y)}r.outerWidth=r.width=P(e.textWidth,l),r.outerHeight=r.height=P(e.textHeight,s),c&&(r.outerWidth+=c[1]+c[3],r.outerHeight+=c[0]+c[2]);for(var p=0;p<h.length;p++){var _=h[p],z=_.percentWidth;_.width=parseInt(z,10)/100*l}return r}function x(t,e,r){for(var n=""===e,i=e.split("\n"),a=t.lines,o=0;o<i.length;o++){var s=i[o],l={styleName:r,text:s,isLineHolder:!s&&!n};if(o)a.push({tokens:[l]});else{var h=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||n)&&h.push(l)}}}function y(t){return(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ")||t.textFont||t.font}var w=r(0),_=r(3),b=r(10),S={},M=0,T=5e3,k=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,C="12px sans-serif",P=w.retrieve2,I=w.retrieve3,A={getWidth:n,getBoundingRect:i,adjustTextPositionOnRect:h,truncateText:u,measureText:p,getLineHeight:g,parsePlainText:v,parseRichText:m,adjustTextX:s,adjustTextY:l,makeFont:y,DEFAULT_FONT:C};t.exports=A},function(t,e,r){"use strict";var n=r(4),i=r(2),a=r(47),o=r(3),s=r(19).devicePixelRatio,l={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},h=[],u=[],c=[],f=[],d=Math.min,g=Math.max,p=Math.cos,v=Math.sin,m=Math.sqrt,x=Math.abs,y="undefined"!=typeof Float32Array,w=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};w.prototype={constructor:w,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=x(1/s/t)||0,this._uy=x(1/s/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(l.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var r=x(t-this._xi)>this._ux||x(e-this._yi)>this._uy||this._len<5;return this.addData(l.L,t,e),this._ctx&&r&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),r&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,r,n,i,a){return this.addData(l.C,t,e,r,n,i,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,r,n,i,a):this._ctx.bezierCurveTo(t,e,r,n,i,a)),this._xi=i,this._yi=a,this},quadraticCurveTo:function(t,e,r,n){return this.addData(l.Q,t,e,r,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,r,n):this._ctx.quadraticCurveTo(t,e,r,n)),this._xi=r,this._yi=n,this},arc:function(t,e,r,n,i,a){return this.addData(l.A,t,e,r,r,n,i-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,r,n,i,a),this._xi=p(i)*r+t,this._yi=v(i)*r+t,this},arcTo:function(t,e,r,n,i){return this._ctx&&this._ctx.arcTo(t,e,r,n,i),this},rect:function(t,e,r,n){return this._ctx&&this._ctx.rect(t,e,r,n),this.addData(l.R,t,e,r,n),this},closePath:function(){this.addData(l.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,r),t.closePath()),this._xi=e,this._yi=r,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,r=0;r<t.length;r++)e+=t[r];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length==e||!y||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,n=this._len,i=0;i<e;i++)r+=t[i].len();y&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+r));for(var i=0;i<e;i++)for(var a=t[i].data,o=0;o<a.length;o++)this.data[n++]=a[o];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var r=0;r<arguments.length;r++)e[this._len++]=arguments[r];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var r,n,i=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,f=m(u*u+c*c),p=l,v=h,x=o.length;for(u/=f,c/=f,a<0&&(a=i+a),a%=i,p-=a*u,v-=a*c;u>0&&p<=t||u<0&&p>=t||0==u&&(c>0&&v<=e||c<0&&v>=e);)n=this._dashIdx,r=o[n],p+=u*r,v+=c*r,this._dashIdx=(n+1)%x,u>0&&p<l||u<0&&p>l||c>0&&v<h||c<0&&v>h||s[n%2?"moveTo":"lineTo"](u>=0?d(p,t):g(p,t),c>=0?d(v,e):g(v,e));u=p-t,c=v-e,this._dashOffset=-m(u*u+c*c)},_dashedBezierTo:function(t,e,r,i,a,o){var s,l,h,u,c,f=this._dashSum,d=this._dashOffset,g=this._lineDash,p=this._ctx,v=this._xi,x=this._yi,y=n.cubicAt,w=0,_=this._dashIdx,b=g.length,S=0;for(d<0&&(d=f+d),d%=f,s=0;s<1;s+=.1)l=y(v,t,r,a,s+.1)-y(v,t,r,a,s),h=y(x,e,i,o,s+.1)-y(x,e,i,o,s),w+=m(l*l+h*h);for(;_<b&&!((S+=g[_])>d);_++);for(s=(S-d)/w;s<=1;)u=y(v,t,r,a,s),c=y(x,e,i,o,s),_%2?p.moveTo(u,c):p.lineTo(u,c),s+=g[_]/w,_=(_+1)%b;_%2!=0&&p.lineTo(a,o),l=a-u,h=o-c,this._dashOffset=-m(l*l+h*h)},_dashedQuadraticTo:function(t,e,r,n){var i=r,a=n;r=(r+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,r,n,i,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,y&&(this.data=new Float32Array(t)))},getBoundingRect:function(){h[0]=h[1]=c[0]=c[1]=Number.MAX_VALUE,u[0]=u[1]=f[0]=f[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,r=0,n=0,s=0,d=0;d<t.length;){var g=t[d++];switch(1==d&&(e=t[d],r=t[d+1],n=e,s=r),g){case l.M:n=t[d++],s=t[d++],e=n,r=s,c[0]=n,c[1]=s,f[0]=n,f[1]=s;break;case l.L:a.fromLine(e,r,t[d],t[d+1],c,f),e=t[d++],r=t[d++];break;case l.C:a.fromCubic(e,r,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],c,f),e=t[d++],r=t[d++];break;case l.Q:a.fromQuadratic(e,r,t[d++],t[d++],t[d],t[d+1],c,f),e=t[d++],r=t[d++];break;case l.A:var m=t[d++],x=t[d++],y=t[d++],w=t[d++],_=t[d++],b=t[d++]+_,S=(t[d++],1-t[d++]);1==d&&(n=p(_)*y+m,s=v(_)*w+x),a.fromArc(m,x,y,w,_,b,S,c,f),e=p(b)*y+m,r=v(b)*w+x;break;case l.R:n=e=t[d++],s=r=t[d++];var M=t[d++],T=t[d++];a.fromLine(n,s,n+M,s+T,c,f);break;case l.Z:e=n,r=s}i.min(h,h,c),i.max(u,u,f)}return 0===d&&(h[0]=h[1]=u[0]=u[1]=0),new o(h[0],h[1],u[0]-h[0],u[1]-h[1])},rebuildPath:function(t){for(var e,r,n,i,a,o,s=this.data,h=this._ux,u=this._uy,c=this._len,f=0;f<c;){var d=s[f++];switch(1==f&&(n=s[f],i=s[f+1],e=n,r=i),d){case l.M:e=n=s[f++],r=i=s[f++],t.moveTo(n,i);break;case l.L:a=s[f++],o=s[f++],(x(a-n)>h||x(o-i)>u||f===c-1)&&(t.lineTo(a,o),n=a,i=o);break;case l.C:t.bezierCurveTo(s[f++],s[f++],s[f++],s[f++],s[f++],s[f++]),n=s[f-2],i=s[f-1];break;case l.Q:t.quadraticCurveTo(s[f++],s[f++],s[f++],s[f++]),n=s[f-2],i=s[f-1];break;case l.A:var g=s[f++],m=s[f++],y=s[f++],w=s[f++],_=s[f++],b=s[f++],S=s[f++],M=s[f++],T=y>w?y:w,k=y>w?1:y/w,C=y>w?w/y:1,P=Math.abs(y-w)>.001,I=_+b;P?(t.translate(g,m),t.rotate(S),t.scale(k,C),t.arc(0,0,T,_,I,1-M),t.scale(1/k,1/C),t.rotate(-S),t.translate(-g,-m)):t.arc(g,m,T,_,I,1-M),1==f&&(e=p(_)*y+g,r=v(_)*w+m),n=p(I)*y+g,i=v(I)*w+m;break;case l.R:e=n=s[f],r=i=s[f+1],t.rect(s[f++],s[f++],s[f++],s[f++]);break;case l.Z:t.closePath(),n=e,i=r}}}},w.CMD=l,t.exports=w},function(e,r){e.exports=t},function(t,e){var r="undefined"==typeof Float32Array?Array:Float32Array,n={create:function(){var t=new r(6);return n.identity(t),t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},mul:function(t,e,r){var n=e[0]*r[0]+e[2]*r[1],i=e[1]*r[0]+e[3]*r[1],a=e[0]*r[2]+e[2]*r[3],o=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],l=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t},translate:function(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t},rotate:function(t,e,r){var n=e[0],i=e[2],a=e[4],o=e[1],s=e[3],l=e[5],h=Math.sin(r),u=Math.cos(r);return t[0]=n*u+o*h,t[1]=-n*h+o*u,t[2]=i*u+s*h,t[3]=-i*h+u*s,t[4]=u*a+h*l,t[5]=u*l-h*a,t},scale:function(t,e,r){var n=r[0],i=r[1];return t[0]=e[0]*n,t[1]=e[1]*i,t[2]=e[2]*n,t[3]=e[3]*i,t[4]=e[4]*n,t[5]=e[5]*i,t},invert:function(t,e){var r=e[0],n=e[2],i=e[4],a=e[1],o=e[3],s=e[5],l=r*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=r*l,t[4]=(n*s-o*i)*l,t[5]=(a*i-r*s)*l,t):null}};t.exports=n},function(t,e,r){function n(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function i(t){return Math.floor(Math.log(t)/Math.LN10)}var a=r(0),o={};o.linearMap=function(t,e,r,n){var i=e[1]-e[0],a=r[1]-r[0];if(0===i)return 0===a?r[0]:(r[0]+r[1])/2;if(n)if(i>0){if(t<=e[0])return r[0];if(t>=e[1])return r[1]}else{if(t>=e[0])return r[0];if(t<=e[1])return r[1]}else{if(t===e[0])return r[0];if(t===e[1])return r[1]}return(t-e[0])/i*a+r[0]},o.parsePercent=function(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?n(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t},o.round=function(t,e,r){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),r?t:+t},o.asc=function(t){return t.sort(function(t,e){return t-e}),t},o.getPrecision=function(t){if(t=+t,isNaN(t))return 0;for(var e=1,r=0;Math.round(t*e)/e!==t;)e*=10,r++;return r},o.getPrecisionSafe=function(t){var e=t.toString(),r=e.indexOf("e");if(r>0){var n=+e.slice(r+1);return n<0?-n:0}var i=e.indexOf(".");return i<0?0:e.length-1-i},o.getPixelPrecision=function(t,e){var r=Math.log,n=Math.LN10,i=Math.floor(r(t[1]-t[0])/n),a=Math.round(r(Math.abs(e[1]-e[0]))/n),o=Math.min(Math.max(-i+a,0),20);return isFinite(o)?o:20},o.getPercentWithPrecision=function(t,e,r){if(!t[e])return 0;var n=a.reduce(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return 0;for(var i=Math.pow(10,r),o=a.map(t,function(t){return(isNaN(t)?0:t)/n*i*100}),s=100*i,l=a.map(o,function(t){return Math.floor(t)}),h=a.reduce(l,function(t,e){return t+e},0),u=a.map(o,function(t,e){return t-l[e]});h<s;){for(var c=Number.NEGATIVE_INFINITY,f=null,d=0,g=u.length;d<g;++d)u[d]>c&&(c=u[d],f=d);++l[f],u[f]=0,++h}return l[e]/i},o.MAX_SAFE_INTEGER=9007199254740991,o.remRadian=function(t){var e=2*Math.PI;return(t%e+e)%e},o.isRadianAroundZero=function(t){return t>-1e-4&&t<1e-4};var s=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;o.parseDate=function(t){if(t instanceof Date)return t;if("string"==typeof t){var e=s.exec(t);if(!e)return new Date(NaN);if(e[8]){var r=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(r-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,r,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return null==t?new Date(NaN):new Date(Math.round(t))},o.quantity=function(t){return Math.pow(10,i(t))},o.nice=function(t,e){var r,n=i(t),a=Math.pow(10,n),o=t/a;return r=e?o<1.5?1:o<2.5?2:o<4?3:o<7?5:10:o<1?1:o<2?2:o<3?3:o<5?5:10,t=r*a,n>=-20?+t.toFixed(n<0?-n:0):t},o.reformIntervals=function(t){function e(t,r,n){return t.interval[n]<r.interval[n]||t.interval[n]===r.interval[n]&&(t.close[n]-r.close[n]==(n?-1:1)||!n&&e(t,r,1))}t.sort(function(t,r){return e(t,r,0)?-1:1});for(var r=-1/0,n=1,i=0;i<t.length;){for(var a=t[i].interval,o=t[i].close,s=0;s<2;s++)a[s]<=r&&(a[s]=r,o[s]=s?1:1-n),r=a[s],n=o[s];a[0]===a[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},o.isNumeric=function(t){return t-parseFloat(t)>=0},t.exports=o},function(t,e,r){function n(){var t=this.__cachedImgObj;this.onload=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],n=r.cb;n&&n(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}var i=r(14),a=new i(50),o={};o.findExistImage=function(t){if("string"==typeof t){var e=a.get(t);return e&&e.image}return t},o.createOrUpdateImage=function(t,e,r,i,o){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!r)return e;var l=a.get(t),h={hostEl:r,cb:i,cbPayload:o};return l?(e=l.image,!s(e)&&l.pending.push(h)):(!e&&(e=new Image),e.onload=n,a.put(t,e.__cachedImgObj={image:e,pending:[h]}),e.src=e.__zrImageSrc=t),e}return t}return e};var s=o.isImageReady=function(t){return t&&t.width&&t.height};t.exports=o},function(t,e,r){var n=r(0);t.exports=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,r){for(var i={},a=0;a<t.length;a++){var o=t[a][1];if(!(e&&n.indexOf(e,o)>=0||r&&n.indexOf(r,o)<0)){var s=this.getShallow(o);null!=s&&(i[t[a][0]]=s)}}return i}}},function(t,e,r){function n(t){t=t||{},o.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new a(t.style,this),this._rect=null,this.__clipPaths=[]}var i=r(0),a=r(38),o=r(16),s=r(46);n.prototype={constructor:n,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var r=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(r[0],r[1])},dirty:function(){this.__dirty=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?o.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new a(t,this),this.dirty(!1),this}},i.inherits(n,o),i.mixin(n,s),t.exports=n},function(t,e,r){var n=r(0),i=r(9),a=r(5),o={};o.addCommas=function(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))},o.toCamelCase=function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},o.normalizeCssArray=n.normalizeCssArray;var s=o.encodeHTML=function(t){return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")},l=["a","b","c","d","e","f","g"],h=function(t,e){return"{"+t+(null==e?"":e)+"}"};o.formatTpl=function(t,e,r){n.isArray(e)||(e=[e]);var i=e.length;if(!i)return"";for(var a=e[0].$vars||[],o=0;o<a.length;o++){var u=l[o],c=h(u,0);t=t.replace(h(u),r?s(c):c)}for(var f=0;f<i;f++)for(var d=0;d<a.length;d++){var c=e[f][a[d]];t=t.replace(h(l[d],f),r?s(c):c)}return t},o.formatTplSimple=function(t,e,r){return n.each(e,function(e,n){t=t.replace("{"+n+"}",r?s(e):e)}),t},o.getTooltipMarker=function(t,e){return t?'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+o.encodeHTML(t)+";"+(e||"")+'"></span>':""};var u=function(t){return t<10?"0"+t:t};o.formatTime=function(t,e,r){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var n=i.parseDate(e),a=r?"UTC":"",o=n["get"+a+"FullYear"](),s=n["get"+a+"Month"]()+1,l=n["get"+a+"Date"](),h=n["get"+a+"Hours"](),c=n["get"+a+"Minutes"](),f=n["get"+a+"Seconds"]();return t=t.replace("MM",u(s)).replace("M",s).replace("yyyy",o).replace("yy",o%100).replace("dd",u(l)).replace("d",l).replace("hh",u(h)).replace("h",h).replace("mm",u(c)).replace("m",c).replace("ss",u(f)).replace("s",f)},o.capitalFirst=function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},o.truncateText=a.truncateText,o.getTextRect=a.getBoundingRect,t.exports=o},function(t,e){var r=function(){this.head=null,this.tail=null,this._len=0},n=r.prototype;n.insert=function(t){var e=new i(t);return this.insertEntry(e),e},n.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},n.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},n.len=function(){return this._len},n.clear=function(){this.head=this.tail=null,this._len=0};var i=function(t){this.value=t,this.next,this.prev},a=function(t){this._list=new r,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},o=a.prototype;o.put=function(t,e){var r=this._list,n=this._map,a=null;if(null==n[t]){var o=r.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=r.head;r.remove(l),delete n[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new i(e),s.key=t,r.insertEntry(s),n[t]=s}return a},o.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},o.clear=function(){this._list.clear(),this._map={}},t.exports=a},function(t,e){var r={};r="undefined"==typeof navigator?{browser:{},os:{},node:!0,canvasSupported:!0}:function(t){var e={},r={},n=t.match(/Firefox\/([\d.]+)/),i=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(r.firefox=!0,r.version=n[1]),i&&(r.ie=!0,r.version=i[1]),a&&(r.edge=!0,r.version=a[1]),o&&(r.weChat=!0),{browser:r,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,touchEventsSupported:"ontouchstart"in window&&!r.ie&&!r.edge,pointerEventsSupported:"onpointerdown"in window&&(r.edge||r.ie&&r.version>=11)}}(navigator.userAgent),t.exports=r},function(t,e,r){"use strict";var n=r(39),i=r(40),a=r(17),o=r(41),s=r(0),l=function(t){a.call(this,t),i.call(this,t),o.call(this,t),this.id=t.id||n()};l.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var r=this.transform;r||(r=this.transform=[1,0,0,1,0,0]),r[4]+=t,r[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var r=this[t];r||(r=this[t]=[]),r[0]=e[0],r[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(s.isObject(t))for(var r in t)t.hasOwnProperty(r)&&this.attrKV(r,t[r]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},s.mixin(l,o),s.mixin(l,a),s.mixin(l,i),t.exports=l},function(t,e,r){"use strict";function n(t){return t>s||t<-s}var i=r(8),a=r(2),o=i.identity,s=5e-5,l=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},h=l.prototype;h.transform=null,h.needLocalTransform=function(){return n(this.rotation)||n(this.position[0])||n(this.position[1])||n(this.scale[0]-1)||n(this.scale[1]-1)},h.updateTransform=function(){var t=this.parent,e=t&&t.transform,r=this.needLocalTransform(),n=this.transform;if(!r&&!e)return void(n&&o(n));n=n||i.create(),r?this.getLocalTransform(n):o(n),e&&(r?i.mul(n,t.transform,n):i.copy(n,t.transform)),this.transform=n,this.invTransform=this.invTransform||i.create(),i.invert(this.invTransform,n)},h.getLocalTransform=function(t){return l.getLocalTransform(this,t)},h.setTransform=function(t){var e=this.transform,r=t.dpr||1;e?t.setTransform(r*e[0],r*e[1],r*e[2],r*e[3],r*e[4],r*e[5]):t.setTransform(r,0,0,r,0,0)},h.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var u=[];h.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(i.mul(u,t.invTransform,e),e=u);var r=e[0]*e[0]+e[1]*e[1],a=e[2]*e[2]+e[3]*e[3],o=this.position,s=this.scale;n(r-1)&&(r=Math.sqrt(r)),n(a-1)&&(a=Math.sqrt(a)),e[0]<0&&(r=-r),e[3]<0&&(a=-a),o[0]=e[4],o[1]=e[5],s[0]=r,s[1]=a,this.rotation=Math.atan2(-e[1]/a,e[0]/r)}},h.getGlobalScale=function(){var t=this.transform;if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),r=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(r=-r),[e,r]},h.transformCoordToLocal=function(t,e){var r=[t,e],n=this.invTransform;return n&&a.applyTransform(r,r,n),r},h.transformCoordToGlobal=function(t,e){var r=[t,e],n=this.transform;return n&&a.applyTransform(r,r,n),r},l.getLocalTransform=function(t,e){e=e||[],o(e);var r=t.origin,n=t.scale||[1,1],a=t.rotation||0,s=t.position||[0,0];return r&&(e[4]-=r[0],e[5]-=r[1]),i.scale(e,e,n),a&&i.rotate(e,e,a),r&&(e[4]+=r[0],e[5]+=r[1]),e[4]+=s[0],e[5]+=s[1],e},t.exports=l},function(t,e,r){function n(t){return t=Math.round(t),t<0?0:t>255?255:t}function i(t){return t=Math.round(t),t<0?0:t>360?360:t}function a(t){return t<0?0:t>1?1:t}function o(t){return n(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function s(t){return a(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function l(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function h(t,e,r){return t+(e-t)*r}function u(t,e,r,n,i){return t[0]=e,t[1]=r,t[2]=n,t[3]=i,t}function c(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function f(t,e){k&&c(k,e),k=T.put(t,k||e.slice())}function d(t,e){if(t){e=e||[];var r=T.get(t);if(r)return c(e,r);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in M)return c(e,M[n]),f(t,e),e;if("#"!==n.charAt(0)){var i=n.indexOf("("),a=n.indexOf(")");if(-1!==i&&a+1===n.length){var l=n.substr(0,i),h=n.substr(i+1,a-(i+1)).split(","),d=1;switch(l){case"rgba":if(4!==h.length)return void u(e,0,0,0,1);d=s(h.pop());case"rgb":return 3!==h.length?void u(e,0,0,0,1):(u(e,o(h[0]),o(h[1]),o(h[2]),d),f(t,e),e);case"hsla":return 4!==h.length?void u(e,0,0,0,1):(h[3]=s(h[3]),g(h,e),f(t,e),e);case"hsl":return 3!==h.length?void u(e,0,0,0,1):(g(h,e),f(t,e),e);default:return}}u(e,0,0,0,1)}else{if(4===n.length){var p=parseInt(n.substr(1),16);return p>=0&&p<=4095?(u(e,(3840&p)>>4|(3840&p)>>8,240&p|(240&p)>>4,15&p|(15&p)<<4,1),f(t,e),e):void u(e,0,0,0,1)}if(7===n.length){var p=parseInt(n.substr(1),16);return p>=0&&p<=16777215?(u(e,(16711680&p)>>16,(65280&p)>>8,255&p,1),f(t,e),e):void u(e,0,0,0,1)}}}}function g(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=s(t[1]),a=s(t[2]),o=a<=.5?a*(i+1):a+i-a*i,h=2*a-o;return e=e||[],u(e,n(255*l(h,o,r+1/3)),n(255*l(h,o,r)),n(255*l(h,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function p(t){if(t){var e,r,n=t[0]/255,i=t[1]/255,a=t[2]/255,o=Math.min(n,i,a),s=Math.max(n,i,a),l=s-o,h=(s+o)/2;if(0===l)e=0,r=0;else{r=h<.5?l/(s+o):l/(2-s-o);var u=((s-n)/6+l/2)/l,c=((s-i)/6+l/2)/l,f=((s-a)/6+l/2)/l;n===s?e=f-c:i===s?e=1/3+u-f:a===s&&(e=2/3+c-u),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,r,h];return null!=t[3]&&d.push(t[3]),d}}function v(t,e){var r=d(t);if(r){for(var n=0;n<3;n++)r[n]=e<0?r[n]*(1-e)|0:(255-r[n])*e+r[n]|0;return b(r,4===r.length?"rgba":"rgb")}}function m(t,e){var r=d(t);if(r)return((1<<24)+(r[0]<<16)+(r[1]<<8)+ +r[2]).toString(16).slice(1)}function x(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var i=t*(e.length-1),o=Math.floor(i),s=Math.ceil(i),l=e[o],u=e[s],c=i-o;return r[0]=n(h(l[0],u[0],c)),r[1]=n(h(l[1],u[1],c)),r[2]=n(h(l[2],u[2],c)),r[3]=a(h(l[3],u[3],c)),r}}function y(t,e,r){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),o=Math.floor(i),s=Math.ceil(i),l=d(e[o]),u=d(e[s]),c=i-o,f=b([n(h(l[0],u[0],c)),n(h(l[1],u[1],c)),n(h(l[2],u[2],c)),a(h(l[3],u[3],c))],"rgba");return r?{color:f,leftIndex:o,rightIndex:s,value:i}:f}}function w(t,e,r,n){if(t=d(t))return t=p(t),null!=e&&(t[0]=i(e)),null!=r&&(t[1]=s(r)),null!=n&&(t[2]=s(n)),b(g(t),"rgba")}function _(t,e){if((t=d(t))&&null!=e)return t[3]=a(e),b(t,"rgba")}function b(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}var S=r(14),M={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},T=new S(20),k=null;t.exports={parse:d,lift:v,toHex:m,fastLerp:x,fastMapToColor:x,lerp:y,mapToColor:y,modifyHSL:w,modifyAlpha:_,stringify:b}},function(t,e){var r=1;"undefined"!=typeof window&&(r=Math.max(window.devicePixelRatio||1,1));var n={debugMode:0,devicePixelRatio:r};t.exports=n},function(t,e,r){function n(t){if(t){t.font=v.makeFont(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||b[e]?e:"left";var r=t.textVerticalAlign||t.textBaseline;"center"===r&&(r="middle"),t.textVerticalAlign=null==r||S[r]?r:"top";t.textPadding&&(t.textPadding=m.normalizeCssArray(t.textPadding))}}function i(t,e,r,n,i){var a=d(e,"font",n.font||v.DEFAULT_FONT),o=n.textPadding,l=t.__textCotentBlock;l&&!t.__dirty||(l=t.__textCotentBlock=v.parsePlainText(r,a,o,n.truncate));var c=l.outerHeight,g=l.lines,m=l.lineHeight,x=f(c,n,i),y=x.baseX,w=x.baseY,_=x.textAlign,b=x.textVerticalAlign;s(e,n,i,y,w);var S=v.adjustTextY(w,c,b),M=y,C=S,P=h(n);if(P||o){var I=v.getWidth(r,a),A=I;o&&(A+=o[1]+o[3]);var O=v.adjustTextX(y,A,_);P&&u(t,e,n,O,S,A,c),o&&(M=p(y,_,o),C+=o[0])}d(e,"textAlign",_||"left"),d(e,"textBaseline","middle"),d(e,"shadowBlur",n.textShadowBlur||0),d(e,"shadowColor",n.textShadowColor||"transparent"),d(e,"shadowOffsetX",n.textShadowOffsetX||0),d(e,"shadowOffsetY",n.textShadowOffsetY||0),C+=m/2;var D=n.textStrokeWidth,L=T(n.textStroke,D),F=k(n.textFill);L&&(d(e,"lineWidth",D),d(e,"strokeStyle",L)),F&&d(e,"fillStyle",F);for(var R=0;R<g.length;R++)L&&e.strokeText(g[R],M,C),F&&e.fillText(g[R],M,C),C+=m}function a(t,e,r,n,i){var a=t.__textCotentBlock;a&&!t.__dirty||(a=t.__textCotentBlock=v.parseRichText(r,n)),o(t,e,a,n,i)}function o(t,e,r,n,i){var a=r.width,o=r.outerWidth,c=r.outerHeight,d=n.textPadding,g=f(c,n,i),p=g.baseX,m=g.baseY,x=g.textAlign,y=g.textVerticalAlign;s(e,n,i,p,m);var w=v.adjustTextX(p,o,x),_=v.adjustTextY(m,c,y),b=w,S=_;d&&(b+=d[3],S+=d[0]);var M=b+a;h(n)&&u(t,e,n,w,_,o,c);for(var T=0;T<r.lines.length;T++){for(var k,C=r.lines[T],P=C.tokens,I=P.length,A=C.lineHeight,O=C.width,D=0,L=b,F=M,R=I-1;D<I&&(k=P[D],!k.textAlign||"left"===k.textAlign);)l(t,e,k,n,A,S,L,"left"),O-=k.width,L+=k.width,D++;for(;R>=0&&(k=P[R],"right"===k.textAlign);)l(t,e,k,n,A,S,F,"right"),O-=k.width,F-=k.width,R--;for(L+=(a-(L-b)-(M-F)-O)/2;D<=R;)k=P[D],l(t,e,k,n,A,S,L+k.width/2,"center"),L+=k.width,D++;S+=A}}function s(t,e,r,n,i){if(r&&e.textRotation){var a=e.textOrigin;"center"===a?(n=r.width/2+r.x,i=r.height/2+r.y):a&&(n=a[0]+r.x,i=a[1]+r.y),t.translate(n,i),t.rotate(-e.textRotation),t.translate(-n,-i)}}function l(t,e,r,n,i,a,o,s){var l=n.rich[r.styleName]||{},c=r.textVerticalAlign,f=a+i/2;"top"===c?f=a+r.height/2:"bottom"===c&&(f=a+i-r.height/2),!r.isLineHolder&&h(l)&&u(t,e,l,"right"===s?o-r.width:"center"===s?o-r.width/2:o,f-r.height/2,r.width,r.height);var g=r.textPadding;g&&(o=p(o,s,g),f-=r.height/2-g[2]-r.textHeight/2),d(e,"shadowBlur",w(l.textShadowBlur,n.textShadowBlur,0)),d(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),d(e,"shadowOffsetX",w(l.textShadowOffsetX,n.textShadowOffsetX,0)),d(e,"shadowOffsetY",w(l.textShadowOffsetY,n.textShadowOffsetY,0)),d(e,"textAlign",s),d(e,"textBaseline","middle"),d(e,"font",r.font||v.DEFAULT_FONT);var m=T(l.textStroke||n.textStroke,y),x=k(l.textFill||n.textFill),y=_(l.textStrokeWidth,n.textStrokeWidth);m&&(d(e,"lineWidth",y),d(e,"strokeStyle",m),e.strokeText(r.text,o,f)),x&&(d(e,"fillStyle",x),e.fillText(r.text,o,f))}function h(t){return t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor}function u(t,e,r,n,i,a,o){var s=r.textBackgroundColor,l=r.textBorderWidth,h=r.textBorderColor,u=m.isString(s);if(d(e,"shadowBlur",r.textBoxShadowBlur||0),d(e,"shadowColor",r.textBoxShadowColor||"transparent"),d(e,"shadowOffsetX",r.textBoxShadowOffsetX||0),d(e,"shadowOffsetY",r.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var f=r.textBorderRadius;f?x.buildPath(e,{x:n,y:i,width:a,height:o,r:f}):e.rect(n,i,a,o),e.closePath()}if(u)d(e,"fillStyle",s),e.fill();else if(m.isObject(s)){var g=s.image;g=y.createOrUpdateImage(g,null,t,c,s),g&&y.isImageReady(g)&&e.drawImage(g,n,i,a,o)}l&&h&&(d(e,"lineWidth",l),d(e,"strokeStyle",h),e.stroke())}function c(t,e){e.image=t}function f(t,e,r){var n=e.x||0,i=e.y||0,a=e.textAlign,o=e.textVerticalAlign;if(r){var s=e.textPosition;if(s instanceof Array)n=r.x+g(s[0],r.width),i=r.y+g(s[1],r.height);else{var l=v.adjustTextPositionOnRect(s,r,e.textDistance);n=l.x,i=l.y,a=a||l.textAlign,o=o||l.textVerticalAlign}var h=e.textOffset;h&&(n+=h[0],i+=h[1])}return{baseX:n,baseY:i,textAlign:a,textVerticalAlign:o}}function d(t,e,r){return t[e]=r,t[e]}function g(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function p(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}var v=r(5),m=r(0),x=r(21),y=r(10),w=m.retrieve3,_=m.retrieve2,b={left:1,right:1,center:1},S={top:1,bottom:1,middle:1},M={};M.normalizeTextStyle=function(t){return n(t),m.each(t.rich,n),t},M.renderText=function(t,e,r,n,o){n.rich?a(t,e,r,n,o):i(t,e,r,n,o)};var T=M.getStroke=function(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t},k=M.getFill=function(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t};M.needDrawText=function(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)},t.exports=M},function(t,e){t.exports={buildPath:function(t,e){var r,n,i,a,o=e.x,s=e.y,l=e.width,h=e.height,u=e.r;l<0&&(o+=l,l=-l),h<0&&(s+=h,h=-h),"number"==typeof u?r=n=i=a=u:u instanceof Array?1===u.length?r=n=i=a=u[0]:2===u.length?(r=i=u[0],n=a=u[1]):3===u.length?(r=u[0],n=a=u[1],i=u[2]):(r=u[0],n=u[1],i=u[2],a=u[3]):r=n=i=a=0;var c;r+n>l&&(c=r+n,r*=l/c,n*=l/c),i+a>l&&(c=i+a,i*=l/c,a*=l/c),n+i>h&&(c=n+i,n*=h/c,i*=h/c),r+a>h&&(c=r+a,r*=h/c,a*=h/c),t.moveTo(o+r,s),t.lineTo(o+l-n,s),0!==n&&t.quadraticCurveTo(o+l,s,o+l,s+n),t.lineTo(o+l,s+h-i),0!==i&&t.quadraticCurveTo(o+l,s+h,o+l-i,s+h),t.lineTo(o+a,s+h),0!==a&&t.quadraticCurveTo(o,s+h,o,s+h-a),t.lineTo(o,s+r),0!==r&&t.quadraticCurveTo(o,s,o+r,s)}}},function(t,e){var r=2*Math.PI;t.exports={normalizeRadian:function(t){return t%=r,t<0&&(t+=r),t}}},function(t,e,r){var n=r(64),i=r(65);t.exports={buildPath:function(t,e,r){var a=e.points,o=e.smooth;if(a&&a.length>=2){if(o&&"spline"!==o){var s=i(a,o,r,e.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var l=a.length,h=0;h<(r?l:l-1);h++){var u=s[2*h],c=s[2*h+1],f=a[(h+1)%l];t.bezierCurveTo(u[0],u[1],c[0],c[1],f[0],f[1])}}else{"spline"===o&&(a=n(a,r)),t.moveTo(a[0][0],a[0][1]);for(var h=1,d=a.length;h<d;h++)t.lineTo(a[h][0],a[h][1])}r&&t.closePath()}}}},function(t,e){var r=function(t){this.colorStops=t||[]};r.prototype={constructor:r,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}},t.exports=r},function(t,e,r){t.exports=r(26)},function(t,e,r){function n(t){for(var e=t.getContext("2d"),r=e.getImageData(0,0,t.width,t.height),n=e.createImageData(r),i=0,a=0,o=0;o<r.data.length;o+=4){var s=r.data[o+3];if(s>128){var l=r.data[o]+r.data[o+1]+r.data[o+2];i+=l,++a}}for(var h=i/a,o=0;o<r.data.length;o+=4){var l=r.data[o]+r.data[o+1]+r.data[o+2],s=r.data[o+3];s<128||l>h?(n.data[o]=0,n.data[o+1]=0,n.data[o+2]=0,n.data[o+3]=0):(n.data[o]=255,n.data[o+1]=255,n.data[o+2]=255,n.data[o+3]=255)}e.putImageData(n,0,0)}var i=r(7),a=r(27);r(28),r(75);var o=r(76);if(!o.isSupported)throw new Error("Sorry your browser not support wordCloud");i.registerLayout(function(t,e){t.eachSeriesByType("wordCloud",function(r){function s(t){var e=t.detail.item;t.detail.drawn&&r.layoutInstance.ondraw&&(t.detail.drawn.gx+=l.x/m,t.detail.drawn.gy+=l.y/m,r.layoutInstance.ondraw(e[0],e[1],e[2],t.detail.drawn))}var l=a.getLayoutRect(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()}),h=r.getData(),u=document.createElement("canvas");u.width=l.width,u.height=l.height;var c=u.getContext("2d"),f=r.get("maskImage");if(f)try{c.drawImage(f,0,0,u.width,u.height),n(u)}catch(t){console.error("Invalid mask image"),console.error(t.toString())}var d=r.get("sizeRange"),g=r.get("rotationRange"),p=h.getDataExtent("value"),v=Math.PI/180,m=r.get("gridSize");o(u,{list:h.mapArray("value",function(t,e){var r=h.getItemModel(e);return[h.getName(e),r.get("textStyle.normal.textSize",!0)||i.number.linearMap(t,p,d),e]}).sort(function(t,e){return e[1]-t[1]}),fontFamily:r.get("textStyle.normal.fontFamily")||r.get("textStyle.emphasis.fontFamily")||t.get("textStyle.fontFamily"),fontWeight:r.get("textStyle.normal.fontWeight")||r.get("textStyle.emphasis.fontWeight")||t.get("textStyle.fontWeight"),gridSize:m,ellipticity:l.height/l.width,minRotation:g[0]*v,maxRotation:g[1]*v,clearCanvas:!f,rotateRatio:1,rotationStep:r.get("rotationStep")*v,drawOutOfBound:r.get("drawOutOfBound"),shuffle:!1,shape:r.get("shape")}),u.addEventListener("wordclouddrawn",s),r.layoutInstance&&r.layoutInstance.dispose(),r.layoutInstance={ondraw:null,dispose:function(){u.removeEventListener("wordclouddrawn",s),u.addEventListener("wordclouddrawn",function(t){t.preventDefault()})}}})}),i.registerPreprocessor(function(t){function e(t){t&&i.util.each(n,function(e){t.hasOwnProperty(e)&&(t["text"+i.format.capitalFirst(e)]=t[e])})}var r=(t||{}).series;!i.util.isArray(r)&&(r=r?[r]:[]);var n=["shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];i.util.each(r,function(t){if(t&&"wordCloud"===t.type){var r=t.textStyle||{};e(r.normal),e(r.emphasis)}})})},function(t,e,r){"use strict";function n(t,e,r,n,i){var a=0,o=0;null==n&&(n=1/0),null==i&&(i=1/0);var s=0;e.eachChild(function(l,h){var u,c,f=l.position,d=l.getBoundingRect(),g=e.childAt(h+1),p=g&&g.getBoundingRect();if("horizontal"===t){var v=d.width+(p?-p.x+d.x:0);u=a+v,u>n||l.newline?(a=0,u=v,o+=s+r,s=d.height):s=Math.max(s,d.height)}else{var m=d.height+(p?-p.y+d.y:0);c=o+m,c>i||l.newline?(a+=s+r,o=0,c=m,s=d.width):s=Math.max(s,d.width)}l.newline||(f[0]=a,f[1]=o,"horizontal"===t?a=u+r:o=c+r)})}var i=r(0),a=r(3),o=r(9),s=r(13),l=o.parsePercent,h=i.each,u={},c=u.LOCATION_PARAMS=["left","right","top","bottom","width","height"],f=u.HV_NAMES=[["width","left","right"],["height","top","bottom"]];u.box=n,u.vbox=i.curry(n,"vertical"),u.hbox=i.curry(n,"horizontal"),u.getAvailableSize=function(t,e,r){var n=e.width,i=e.height,a=l(t.x,n),o=l(t.y,i),h=l(t.x2,n),u=l(t.y2,i);return(isNaN(a)||isNaN(parseFloat(t.x)))&&(a=0),(isNaN(h)||isNaN(parseFloat(t.x2)))&&(h=n),(isNaN(o)||isNaN(parseFloat(t.y)))&&(o=0),(isNaN(u)||isNaN(parseFloat(t.y2)))&&(u=i),r=s.normalizeCssArray(r||0),{width:Math.max(h-a-r[1]-r[3],0),height:Math.max(u-o-r[0]-r[2],0)}},u.getLayoutRect=function(t,e,r){r=s.normalizeCssArray(r||0);var n=e.width,i=e.height,o=l(t.left,n),h=l(t.top,i),u=l(t.right,n),c=l(t.bottom,i),f=l(t.width,n),d=l(t.height,i),g=r[2]+r[0],p=r[1]+r[3],v=t.aspect;switch(isNaN(f)&&(f=n-u-p-o),isNaN(d)&&(d=i-c-g-h),null!=v&&(isNaN(f)&&isNaN(d)&&(v>n/i?f=.8*n:d=.8*i),isNaN(f)&&(f=v*d),isNaN(d)&&(d=f/v)),isNaN(o)&&(o=n-u-f-p),isNaN(h)&&(h=i-c-d-g),t.left||t.right){case"center":o=n/2-f/2-r[3];break;case"right":o=n-f-p}switch(t.top||t.bottom){case"middle":case"center":h=i/2-d/2-r[0];break;case"bottom":h=i-d-g}o=o||0,h=h||0,isNaN(f)&&(f=n-p-o-(u||0)),isNaN(d)&&(d=i-g-h-(c||0));var m=new a(o+r[3],h+r[0],f,d);return m.margin=r,m},u.positionElement=function(t,e,r,n,o){var s=!o||!o.hv||o.hv[0],l=!o||!o.hv||o.hv[1],h=o&&o.boundingMode||"all";if(s||l){var c;if("raw"===h)c="group"===t.type?new a(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(c=t.getBoundingRect(),t.needLocalTransform()){var f=t.getLocalTransform();c=c.clone(),c.applyTransform(f)}e=u.getLayoutRect(i.defaults({width:c.width,height:c.height},e),r,n);var d=t.position,g=s?e.x-c.x:0,p=l?e.y-c.y:0;t.attr("position","raw"===h?[g,p]:[d[0]+g,d[1]+p])}},u.sizeCalculable=function(t,e){return null!=t[f[e][0]]||null!=t[f[e][1]]&&null!=t[f[e][2]]},u.mergeLayoutParam=function(t,e,r){function n(r,n){var i={},s=0,u={},c=0;if(h(r,function(e){u[e]=t[e]}),h(r,function(t){a(e,t)&&(i[t]=u[t]=e[t]),o(i,t)&&s++,o(u,t)&&c++}),l[n])return o(e,r[1])?u[r[2]]=null:o(e,r[2])&&(u[r[1]]=null),u;if(2!==c&&s){if(s>=2)return i;for(var f=0;f<r.length;f++){var d=r[f];if(!a(i,d)&&a(t,d)){i[d]=t[d];break}}return i}return u}function a(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function s(t,e,r){h(t,function(t){e[t]=r[t]})}!i.isObject(r)&&(r={});var l=r.ignoreSize;!i.isArray(l)&&(l=[l,l]);var u=n(f[0],0),c=n(f[1],1);s(f[0],t,u),s(f[1],t,c)},u.getLayoutParams=function(t){return u.copyLayoutParams({},t)},u.copyLayoutParams=function(t,e){return e&&t&&h(c,function(r){e.hasOwnProperty(r)&&(t[r]=e[r])}),t},t.exports=u},function(t,e,r){var n=r(29),i=r(7);i.extendSeriesModel({type:"series.wordCloud",visualColorAccessPath:"textStyle.normal.color",optionUpdated:function(){var t=this.option;t.gridSize=Math.max(Math.floor(t.gridSize),4)},getInitialData:function(t,e){var r=n(["value"],t.data),a=new i.List(r,this);return a.initData(t.data),a},defaultOption:{maskImage:null,shape:"circle",left:"center",top:"center",width:"70%",height:"80%",sizeRange:[12,60],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,textStyle:{normal:{fontWeight:"normal"}}}})},function(t,e,r){function n(t,e,r){function n(t,e,r){c[e]?t.otherDims[e]=r:(t.coordDim=e,t.coordDimIndex=r,v.set(e,!0))}function o(t,e,r){if(r||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}e=e||[],r=r||{},t=(t||[]).slice();var d=(r.dimsDef||[]).slice(),g=a.createHashMap(r.encodeDef),p=a.createHashMap(),v=a.createHashMap(),m=[],x=r.dimCount;if(null==x){var y=i(e[0]);x=Math.max(a.isArray(y)&&y.length||1,t.length,d.length),s(t,function(t){var e=t.dimsDef;e&&(x=Math.max(x,e.length))})}for(var w=0;w<x;w++){var _=l(d[w])?{name:d[w]}:d[w]||{},b=_.name,S=m[w]={otherDims:{}};null!=b&&null==p.get(b)&&(S.name=S.tooltipName=b,p.set(b,w)),null!=_.type&&(S.type=_.type)}g.each(function(t,e){t=g.set(e,u(t).slice()),s(t,function(r,i){l(r)&&(r=p.get(r)),null!=r&&r<x&&(t[i]=r,n(m[r],e,i))})});var M=0;s(t,function(t,e){var r,t,i,o;l(t)?(r=t,t={}):(r=t.name,t=a.clone(t),i=t.dimsDef,o=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null);var c=u(g.get(r));if(!c.length)for(var f=0;f<(i&&i.length||1);f++){for(;M<m.length&&null!=m[M].coordDim;)M++;M<m.length&&c.push(M++)}s(c,function(e,a){var s=m[e];n(h(s,t),r,a),null==s.name&&i&&(s.name=s.tooltipName=i[a]),o&&h(s.otherDims,o)})});for(var T=r.extraPrefix||"value",k=0;k<x;k++){var S=m[k]=m[k]||{};null==S.coordDim&&(S.coordDim=o(T,v,r.extraFromZero),S.coordDimIndex=0,S.isExtraCoord=!0),null==S.name&&(S.name=o(S.coordDim,p)),null==S.type&&f(e,k)&&(S.type="ordinal")}return m}function i(t){return a.isArray(t)?t:a.isObject(t)?t.value:t}var a=r(0),o=r(30),s=a.each,l=a.isString,h=a.defaults,u=o.normalizeToArray,c={tooltip:1,label:1,itemName:1},f=n.guessOrdinal=function(t,e){for(var r=0,n=t.length;r<n;r++){var o=i(t[r]);if(!a.isArray(o))return!1;var o=o[e];if(null!=o&&isFinite(o)&&""!==o)return!1;if(l(o)&&"-"!==o)return!0}return!1};t.exports=n},function(t,e,r){function n(t,e){return t&&t.hasOwnProperty(e)}var i=r(13),a=r(9),o=r(31),s=r(0),l=s.each,h=s.isObject,u={};u.normalizeToArray=function(t){return t instanceof Array?t:null==t?[]:[t]},u.defaultEmphasis=function(t,e){if(t)for(var r=t.emphasis=t.emphasis||{},n=t.normal=t.normal||{},i=0,a=e.length;i<a;i++){var o=e[i];!r.hasOwnProperty(o)&&n.hasOwnProperty(o)&&(r[o]=n[o])}},u.TEXT_STYLE_OPTIONS=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],u.getDataItemValue=function(t){return t&&(null==t.value?t:t.value)},u.isDataItemOption=function(t){return h(t)&&!(t instanceof Array)},u.converDataValue=function(t,e){var r=e&&e.type;return"ordinal"===r?t:("time"===r&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+a.parseDate(t)),null==t||""===t?NaN:+t)},u.createDataFormatModel=function(t,e){var r=new o;return s.mixin(r,u.dataFormatMixin),r.seriesIndex=e.seriesIndex,r.name=e.name||"",r.mainType=e.mainType,r.subType=e.subType,r.getData=function(){return t},r},u.dataFormatMixin={getDataParams:function(t,e){var r=this.getData(e),n=this.getRawValue(t,e),a=r.getRawIndex(t),o=r.getName(t,!0),s=r.getRawDataItem(t),l=r.getItemVisual(t,"color");return{componentType:this.mainType,componentSubType:this.subType,seriesType:"series"===this.mainType?this.subType:null,seriesIndex:this.seriesIndex,seriesId:this.id,seriesName:this.name,name:o,dataIndex:a,data:s,dataType:e,value:n,color:l,marker:i.getTooltipMarker(l),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,r,n,a){e=e||"normal";var o=this.getData(r),s=o.getItemModel(t),l=this.getDataParams(t,r);null!=n&&l.value instanceof Array&&(l.value=l.value[n]);var h=s.get([a||"label",e,"formatter"]);return"function"==typeof h?(l.status=e,h(l)):"string"==typeof h?i.formatTpl(h,l):void 0},getRawValue:function(t,e){var r=this.getData(e),n=r.getRawDataItem(t);if(null!=n)return!h(n)||n instanceof Array?n:n.value},formatTooltip:s.noop},u.mappingToExists=function(t,e){e=(e||[]).slice();var r=s.map(t||[],function(t,e){return{exist:t}});return l(e,function(t,n){if(h(t)){for(var i=0;i<r.length;i++)if(!r[i].option&&null!=t.id&&r[i].exist.id===t.id+"")return r[i].option=t,void(e[n]=null);for(var i=0;i<r.length;i++){var a=r[i].exist;if(!(r[i].option||null!=a.id&&null!=t.id||null==t.name||u.isIdInner(t)||u.isIdInner(a)||a.name!==t.name+""))return r[i].option=t,void(e[n]=null)}}}),l(e,function(t,e){if(h(t)){for(var n=0;n<r.length;n++){var i=r[n].exist;if(!r[n].option&&!u.isIdInner(i)&&null==t.id){r[n].option=t;break}}n>=r.length&&r.push({option:t})}}),r},u.makeIdAndName=function(t){var e=s.createHashMap();l(t,function(t,r){var n=t.exist;n&&e.set(n.id,t)}),l(t,function(t,r){var n=t.option;s.assert(!n||null==n.id||!e.get(n.id)||e.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&e.set(n.id,t),!t.keyInfo&&(t.keyInfo={})}),l(t,function(t,r){var n=t.exist,i=t.option,a=t.keyInfo;if(h(i)){if(a.name=null!=i.name?i.name+"":n?n.name:"\0-",n)a.id=n.id;else if(null!=i.id)a.id=i.id+"";else{var o=0;do{a.id="\0"+a.name+"\0"+o++}while(e.get(a.id))}e.set(a.id,t)}})},u.isIdInner=function(t){return h(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")},u.compressBatches=function(t,e){function r(t,e,r){for(var n=0,i=t.length;n<i;n++)for(var a=t[n].seriesId,o=u.normalizeToArray(t[n].dataIndex),s=r&&r[a],l=0,h=o.length;l<h;l++){var c=o[l];s&&s[c]?s[c]=null:(e[a]||(e[a]={}))[c]=1}}function n(t,e){var r=[];for(var i in t)if(t.hasOwnProperty(i)&&null!=t[i])if(e)r.push(+i);else{var a=n(t[i],!0);a.length&&r.push({seriesId:i,dataIndex:a})}return r}var i={},a={};return r(t||[],i),r(e||[],a,i),[n(i),n(a)]},u.queryDataIndex=function(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?s.isArray(e.dataIndex)?s.map(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?s.isArray(e.name)?s.map(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0},u.makeGetter=function(){var t=0;return function(){var e="\0__ec_prop_getter_"+t++;return function(t){return t[e]||(t[e]={})}}}(),u.parseFinder=function(t,e,r){if(s.isString(e)){var i={};i[e+"Index"]=0,e=i}var a=r&&r.defaultMainType;!a||n(e,a+"Index")||n(e,a+"Id")||n(e,a+"Name")||(e[a+"Index"]=0);var o={};return l(e,function(n,i){var n=e[i];if("dataIndex"===i||"dataIndexInside"===i)return void(o[i]=n);var a=i.match(/^(\w+)(Index|Id|Name)$/)||[],l=a[1],h=(a[2]||"").toLowerCase();if(!(!l||!h||null==n||"index"===h&&"none"===n||r&&r.includeMainTypes&&s.indexOf(r.includeMainTypes,l)<0)){var u={mainType:l};"index"===h&&"all"===n||(u[h]=n);var c=t.queryComponents(u);o[l+"Models"]=c,o[l+"Model"]=c[0]}}),o},u.dataDimToCoordDim=function(t,e){var r=t.dimensions;e=t.getDimension(e);for(var n=0;n<r.length;n++){var i=t.getDimensionInfo(r[n]);if(i.name===e)return i.coordDim}},u.coordDimToDataDim=function(t,e){var r=[];return l(t.dimensions,function(n){var i=t.getDimensionInfo(n);i.coordDim===e&&(r[i.coordDimIndex]=i.name)}),r},u.otherDimToDataDim=function(t,e){var r=[];return l(t.dimensions,function(n){var i=t.getDimensionInfo(n),a=i.otherDims,o=a[e];null!=o&&!1!==o&&(r[o]=i.name)}),r},t.exports=u},function(t,e,r){function n(t,e,r){this.parentModel=e,this.ecModel=r,this.option=t}function i(t,e,r){for(var n=0;n<e.length&&(!e[n]||null!=(t=t&&"object"==typeof t?t[e[n]]:null));n++);return null==t&&r&&(t=r.get(e)),t}function a(t,e){var r=s.get(t,"getParent");return r?r.call(t,e):t.parentModel}var o=r(0),s=r(32),l=r(15);n.prototype={constructor:n,init:null,mergeOption:function(t){o.merge(this.option,t,!0)},get:function(t,e){return null==t?this.option:i(this.option,this.parsePath(t),!e&&a(this,t))},getShallow:function(t,e){var r=this.option,n=null==r?r:r[t],i=!e&&a(this,t);return null==n&&i&&(n=i.getShallow(t)),n},getModel:function(t,e){var r,o=null==t?this.option:i(this.option,t=this.parsePath(t));return e=e||(r=a(this,t))&&r.getModel(t),new n(o,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){return new(0,this.constructor)(o.clone(this.option))},setReadOnly:function(t){s.setReadOnly(this,t)},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){s.set(this,"getParent",t)},isAnimationEnabled:function(){if(!l.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},s.enableClassExtend(n);var h=o.mixin;h(n,r(33)),h(n,r(34)),h(n,r(35)),h(n,r(74)),t.exports=n},function(t,e,r){function n(t){o.assert(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function i(t,e){var r=o.slice(arguments,2);return this.superClass.prototype[e].apply(t,r)}function a(t,e,r){return this.superClass.prototype[e].apply(t,r)}var o=r(0),s={},l="___EC__COMPONENT__CONTAINER___";s.set=function(t,e,r){return t["\0ec_\0"+e]=r},s.get=function(t,e){return t["\0ec_\0"+e]},s.hasOwn=function(t,e){return t.hasOwnProperty("\0ec_\0"+e)};var h=s.parseClassType=function(t){var e={main:"",sub:""};return t&&(t=t.split("."),e.main=t[0]||"",e.sub=t[1]||""),e};s.enableClassExtend=function(t,e){t.$constructor=t,t.extend=function(t){__DEV__&&o.each(e,function(e){t[e]||console.warn("Method `"+e+"` should be implemented"+(t.type?" in "+t.type:"")+".")});var r=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):r.apply(this,arguments)};return o.extend(n.prototype,t),n.extend=this.extend,n.superCall=i,n.superApply=a,o.inherits(n,this),n.superClass=r,n}},s.enableClassManagement=function(t,e){function r(t){var e=i[t.main];return e&&e[l]||(e=i[t.main]={},e[l]=!0),e}e=e||{};var i={};if(t.registerClass=function(t,e){if(e)if(n(e),e=h(e),e.sub){if(e.sub!==l){var a=r(e);a[e.sub]=t}}else __DEV__&&i[e.main]&&console.warn(e.main+" exists."),i[e.main]=t;return t},t.getClass=function(t,e,r){var n=i[t];if(n&&n[l]&&(n=e?n[e]:null),r&&!n)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return n},t.getClassesByMainType=function(t){t=h(t);var e=[],r=i[t.main];return r&&r[l]?o.each(r,function(t,r){r!==l&&e.push(t)}):e.push(r),e},t.hasClass=function(t){return t=h(t),!!i[t.main]},t.getAllClassMainTypes=function(){var t=[];return o.each(i,function(e,r){t.push(r)}),t},t.hasSubTypes=function(t){t=h(t);var e=i[t.main];return e&&e[l]},t.parseClassType=h,e.registerWhenExtend){var a=t.extend;a&&(t.extend=function(e){var r=a.call(this,e);return t.registerClass(r,e.type)})}return t},s.setReadOnly=function(t,e){},t.exports=s},function(t,e,r){var n=r(11)([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);t.exports={getLineStyle:function(t){var e=n.call(this,t),r=this.getLineDash(e.lineWidth);return r&&(e.lineDash=r),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),r=Math.max(t,2),n=4*t;return"solid"===e||null==e?null:"dashed"===e?[n,n]:[r,r]}}},function(t,e,r){t.exports={getAreaStyle:r(11)([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]])}},function(t,e,r){var n=r(5),i=r(36),a=["textStyle","color"];t.exports={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(a):null)},getFont:function(){return i.getFont({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return n.getBoundingRect(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("rich"),this.getShallow("truncateText"))}}},function(t,e,r){"use strict";function n(t){return null!=t&&"none"!=t}function i(t){return"string"==typeof t?T.lift(t,-.1):t}function a(t){if(t.__hoverStlDirty){var e=t.style.stroke,r=t.style.fill,a=t.__hoverStl;a.fill=a.fill||(n(r)?i(r):null),a.stroke=a.stroke||(n(e)?i(e):null);var o={};for(var s in a)null!=a[s]&&(o[s]=t.style[s]);t.__normalStl=o,t.__hoverStlDirty=!1}}function o(t){if(!t.__isHover){if(a(t),t.useHoverLayer)t.__zr&&t.__zr.addHover(t,t.__hoverStl);else{var e=t.style,r=e.insideRollbackOpt;r&&w(e),e.extendFrom(t.__hoverStl),r&&(y(e,e.insideOriginalTextPosition,r),null==e.textFill&&(e.textFill=r.autoColor)),t.dirty(!1),t.z2+=1}t.__isHover=!0}}function s(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t.setStyle(e),t.z2-=1),t.__isHover=!1}}function l(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&o(t)}):o(t)}function h(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&s(t)}):s(t)}function u(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&a(t)}function c(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&l(this)}function f(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&h(this)}function d(){this.__isEmphasis=!0,l(this)}function g(){this.__isEmphasis=!1,h(this)}function p(t,e,r,n){if(r=r||L,r.isRectText){var i=e.getShallow("position")||(n?null:"inside");"outside"===i&&(i="top"),t.textPosition=i,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=b.retrieve2(e.getShallow("distance"),n?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,h=v(e);if(h){o={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);m(o[u]={},c,l,r,n)}}return t.rich=o,m(t,e,l,r,n,!0),r.forceRich&&!r.textStyle&&(r.textStyle={}),t}function v(t){for(var e;t&&t!==t.ecModel;){var r=(t.option||L).rich;if(r){e=e||{};for(var n in r)r.hasOwnProperty(n)&&(e[n]=1)}t=t.parentModel}return e}function m(t,e,r,n,i,a){if(r=!i&&r||L,t.textFill=x(e.getShallow("color"),n)||r.color,t.textStroke=x(e.getShallow("textBorderColor"),n)||r.textBorderColor,t.textStrokeWidth=b.retrieve2(e.getShallow("textBorderWidth"),r.textBorderWidth),!i){if(a){var o=t.textPosition;t.insideRollback=y(t,o,n),t.insideOriginalTextPosition=o,t.insideRollbackOpt=n}null==t.textFill&&(t.textFill=n.autoColor)}t.fontStyle=e.getShallow("fontStyle")||r.fontStyle,t.fontWeight=e.getShallow("fontWeight")||r.fontWeight,t.fontSize=e.getShallow("fontSize")||r.fontSize,t.fontFamily=e.getShallow("fontFamily")||r.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&n.disableBox||(t.textBackgroundColor=x(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=x(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||r.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||r.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||r.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||r.textShadowOffsetY}function x(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function y(t,e,r){var n,i=r.useInsideStyle;return null==t.textFill&&!1!==i&&(!0===i||r.isRectText&&e&&"string"==typeof e&&e.indexOf("inside")>=0)&&(n={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=r.autoColor,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),n}function w(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth)}function _(t,e,r,n,i,a){if("function"==typeof i&&(a=i,i=null),n&&n.isAnimationEnabled()){var o=t?"Update":"",s=n.getShallow("animationDuration"+o),l=n.getShallow("animationEasing"+o),h=n.getShallow("animationDelay"+o);"function"==typeof h&&(h=h(i,n.getAnimationDelayParams?n.getAnimationDelayParams(e,i):null)),"function"==typeof s&&(s=s(i)),s>0?e.animateTo(r,s,h||0,l,a,!!a):(e.stopAnimation(),e.attr(r),a&&a())}else e.stopAnimation(),e.attr(r),a&&a()}var b=r(0),S=r(37),M=r(1),T=r(18),k=r(8),C=r(2),P=r(17),I=r(3),A=Math.round,O=Math.max,D=Math.min,L={},F={};F.Group=r(56),F.Image=r(57),F.Text=r(58),F.Circle=r(59),F.Sector=r(60),F.Ring=r(62),F.Polygon=r(63),F.Polyline=r(66),F.Rect=r(67),F.Line=r(68),F.BezierCurve=r(69),F.Arc=r(70),F.CompoundPath=r(71),F.LinearGradient=r(72),F.RadialGradient=r(73),F.BoundingRect=I,F.extendShape=function(t){return M.extend(t)},F.extendPath=function(t,e){return S.extendFromString(t,e)},F.makePath=function(t,e,r,n){var i=S.createFromString(t,e),a=i.getBoundingRect();if(r){var o=a.width/a.height;if("center"===n){var s,l=r.height*o;l<=r.width?s=r.height:(l=r.width,s=l/o);var h=r.x+r.width/2,u=r.y+r.height/2;r.x=h-l/2,r.y=u-s/2,r.width=l,r.height=s}F.resizePath(i,r)}return i},F.mergePath=S.mergePath,F.resizePath=function(t,e){if(t.applyTransform){var r=t.getBoundingRect(),n=r.calculateTransform(e);t.applyTransform(n)}},F.subPixelOptimizeLine=function(t){var e=t.shape,r=t.style.lineWidth;return A(2*e.x1)===A(2*e.x2)&&(e.x1=e.x2=R(e.x1,r,!0)),A(2*e.y1)===A(2*e.y2)&&(e.y1=e.y2=R(e.y1,r,!0)),t},F.subPixelOptimizeRect=function(t){var e=t.shape,r=t.style.lineWidth,n=e.x,i=e.y,a=e.width,o=e.height;return e.x=R(e.x,r,!0),e.y=R(e.y,r,!0),e.width=Math.max(R(n+a,r,!1)-e.x,0===a?0:1),e.height=Math.max(R(i+o,r,!1)-e.y,0===o?0:1),t};var R=F.subPixelOptimize=function(t,e,r){var n=A(2*t);return(n+A(e))%2==0?n/2:(n+(r?1:-1))/2};F.setHoverStyle=function(t,e,r){t.__hoverSilentOnTouch=r&&r.hoverSilentOnTouch,"group"===t.type?t.traverse(function(t){"group"!==t.type&&u(t,e)}):u(t,e),t.on("mouseover",c).on("mouseout",f),t.on("emphasis",d).on("normal",g)},F.setLabelStyle=function(t,e,r,n,i,a,o){i=i||L;var s=i.labelFetcher,l=i.labelDataIndex,h=i.labelDimIndex,u=r.getShallow("show"),c=n.getShallow("show"),f=u||c?b.retrieve2(s?s.getFormattedLabel(l,"normal",null,h):null,i.defaultText):null,d=u?f:null,g=c?b.retrieve2(s?s.getFormattedLabel(l,"emphasis",null,h):null,f):null;null==d&&null==g||(B(t,r,a,i),B(e,n,o,i,!0)),t.text=d,e.text=g};var B=F.setTextStyle=function(t,e,r,n,i){return p(t,e,n,i),r&&b.extend(t,r),t.host&&t.host.dirty&&t.host.dirty(!1),t};F.setText=function(t,e,r){var n,i={isRectText:!0};!1===r?n=!0:i.autoColor=r,p(t,e,i,n),t.host&&t.host.dirty&&t.host.dirty(!1)},F.getFont=function(t,e){var r=e||e.getModel("textStyle");return[t.fontStyle||r&&r.getShallow("fontStyle")||"",t.fontWeight||r&&r.getShallow("fontWeight")||"",(t.fontSize||r&&r.getShallow("fontSize")||12)+"px",t.fontFamily||r&&r.getShallow("fontFamily")||"sans-serif"].join(" ")},F.updateProps=function(t,e,r,n,i){_(!0,t,e,r,n,i)},F.initProps=function(t,e,r,n,i){_(!1,t,e,r,n,i)},F.getTransform=function(t,e){for(var r=k.identity([]);t&&t!==e;)k.mul(r,t.getLocalTransform(),r),t=t.parent;return r},F.applyTransform=function(t,e,r){return e&&!b.isArrayLike(e)&&(e=P.getLocalTransform(e)),r&&(e=k.invert([],e)),C.applyTransform([],t,e)},F.transformDirection=function(t,e,r){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),i=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-i:"bottom"===t?i:0];return a=F.applyTransform(a,e,r),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"},F.groupTransition=function(t,e,r,n){function i(t){var e={position:C.clone(t.position),rotation:t.rotation};return t.shape&&(e.shape=b.extend({},t.shape)),e}if(t&&e){var a=function(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=i(t);t.attr(i(e)),F.updateProps(t,n,r,t.dataIndex)}}})}},F.clipPointsByRect=function(t,e){return b.map(t,function(t){var r=t[0];r=O(r,e.x),r=D(r,e.x+e.width);var n=t[1];return n=O(n,e.y),n=D(n,e.y+e.height),[r,n]})},F.clipRectByRect=function(t,e){var r=O(t.x,e.x),n=D(t.x+t.width,e.x+e.width),i=O(t.y,e.y),a=D(t.y+t.height,e.y+e.height);if(n>=r&&a>=i)return{x:r,y:i,width:n-r,height:a-i}},F.createIcon=function(t,e,r){e=b.extend({rectHover:!0},e);var n=e.style={strokeNoScale:!0};if(r=r||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(n.image=t.slice(8),b.defaults(n,r),new F.Image(e)):F.makePath(t.replace("path://",""),e,r,"center")},t.exports=F},function(t,e,r){function n(t,e,r,n,i,a,o,s,l,h,g){var m=l*(d/180),x=f(m)*(t-r)/2+c(m)*(e-n)/2,y=-1*c(m)*(t-r)/2+f(m)*(e-n)/2,w=x*x/(o*o)+y*y/(s*s);w>1&&(o*=u(w),s*=u(w));var _=(i===a?-1:1)*u((o*o*(s*s)-o*o*(y*y)-s*s*(x*x))/(o*o*(y*y)+s*s*(x*x)))||0,b=_*o*y/s,S=_*-s*x/o,M=(t+r)/2+f(m)*b-c(m)*S,T=(e+n)/2+c(m)*b+f(m)*S,k=v([1,0],[(x-b)/o,(y-S)/s]),C=[(x-b)/o,(y-S)/s],P=[(-1*x-b)/o,(-1*y-S)/s],I=v(C,P);p(C,P)<=-1&&(I=d),p(C,P)>=1&&(I=0),0===a&&I>0&&(I-=2*d),1===a&&I<0&&(I+=2*d),g.addData(h,M,T,o,s,k,I,m,a)}function i(t){if(!t)return[];var e,r=t.replace(/-/g," -").replace(/  /g," ").replace(/ /g,",").replace(/,,/g,",");for(e=0;e<h.length;e++)r=r.replace(new RegExp(h[e],"g"),"|"+h[e]);var i,a=r.split("|"),o=0,l=0,u=new s,c=s.CMD;for(e=1;e<a.length;e++){var f,d=a[e],g=d.charAt(0),p=0,v=d.slice(1).replace(/e,-/g,"e-").split(",");v.length>0&&""===v[0]&&v.shift();for(var m=0;m<v.length;m++)v[m]=parseFloat(v[m]);for(;p<v.length&&!isNaN(v[p])&&!isNaN(v[0]);){var x,y,w,_,b,S,M,T=o,k=l;switch(g){case"l":o+=v[p++],l+=v[p++],f=c.L,u.addData(f,o,l);break;case"L":o=v[p++],l=v[p++],f=c.L,u.addData(f,o,l);break;case"m":o+=v[p++],l+=v[p++],f=c.M,u.addData(f,o,l),g="l";break;case"M":o=v[p++],l=v[p++],f=c.M,u.addData(f,o,l),g="L";break;case"h":o+=v[p++],f=c.L,u.addData(f,o,l);break;case"H":o=v[p++],f=c.L,u.addData(f,o,l);break;case"v":l+=v[p++],f=c.L,u.addData(f,o,l);break;case"V":l=v[p++],f=c.L,u.addData(f,o,l);break;case"C":f=c.C,u.addData(f,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),o=v[p-2],l=v[p-1];break;case"c":f=c.C,u.addData(f,v[p++]+o,v[p++]+l,v[p++]+o,v[p++]+l,v[p++]+o,v[p++]+l),o+=v[p-2],l+=v[p-1];break;case"S":x=o,y=l;var C=u.len(),P=u.data;i===c.C&&(x+=o-P[C-4],y+=l-P[C-3]),f=c.C,T=v[p++],k=v[p++],o=v[p++],l=v[p++],u.addData(f,x,y,T,k,o,l);break;case"s":x=o,y=l;var C=u.len(),P=u.data;i===c.C&&(x+=o-P[C-4],y+=l-P[C-3]),f=c.C,T=o+v[p++],k=l+v[p++],o+=v[p++],l+=v[p++],u.addData(f,x,y,T,k,o,l);break;case"Q":T=v[p++],k=v[p++],o=v[p++],l=v[p++],f=c.Q,u.addData(f,T,k,o,l);break;case"q":T=v[p++]+o,k=v[p++]+l,o+=v[p++],l+=v[p++],f=c.Q,u.addData(f,T,k,o,l);break;case"T":x=o,y=l;var C=u.len(),P=u.data;i===c.Q&&(x+=o-P[C-4],y+=l-P[C-3]),o=v[p++],l=v[p++],f=c.Q,u.addData(f,x,y,o,l);break;case"t":x=o,y=l;var C=u.len(),P=u.data;i===c.Q&&(x+=o-P[C-4],y+=l-P[C-3]),o+=v[p++],l+=v[p++],f=c.Q,u.addData(f,x,y,o,l);break;case"A":w=v[p++],_=v[p++],b=v[p++],S=v[p++],M=v[p++],T=o,k=l,o=v[p++],l=v[p++],f=c.A,n(T,k,o,l,S,M,w,_,b,f,u);break;case"a":w=v[p++],_=v[p++],b=v[p++],S=v[p++],M=v[p++],T=o,k=l,o+=v[p++],l+=v[p++],f=c.A,n(T,k,o,l,S,M,w,_,b,f,u)}}"z"!==g&&"Z"!==g||(f=c.Z,u.addData(f)),i=f}return u.toStatic(),u}function a(t,e){var r=i(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(r.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;r.rebuildPath(e)}},e.applyTransform=function(t){l(r,t),this.dirty(!0)},e}var o=r(1),s=r(6),l=r(55),h=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],u=Math.sqrt,c=Math.sin,f=Math.cos,d=Math.PI,g=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},p=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(g(t)*g(e))},v=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(p(t,e))};t.exports={createFromString:function(t,e){return new o(a(t,e))},extendFromString:function(t,e){return o.extend(a(t,e))},mergePath:function(t,e){for(var r=[],n=t.length,i=0;i<n;i++){var a=t[i];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),r.push(a.path)}var s=new o(e);return s.createPathProxy(),s.buildPath=function(t){t.appendPath(r);var e=t.getContext();e&&t.rebuildPath(e)},s}}},function(t,e){function r(t,e,r){var n=null==e.x?0:e.x,i=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;return e.global||(n=n*r.width+r.x,i=i*r.width+r.x,a=a*r.height+r.y,o=o*r.height+r.y),t.createLinearGradient(n,a,i,o)}function n(t,e,r){var n=r.width,i=r.height,a=Math.min(n,i),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(o=o*n+r.x,s=s*i+r.y,l*=a),t.createRadialGradient(o,s,0,o,s,l)}var i=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],a=function(t,e){this.extendFrom(t,!1),this.host=e};a.prototype={constructor:a,host:null,fill:"#000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,r){for(var n=this,a=r&&r.style,o=!a,s=0;s<i.length;s++){var l=i[s],h=l[0];(o||n[h]!==a[h])&&(t[h]=n[h]||l[1])}if((o||n.fill!==a.fill)&&(t.fillStyle=n.fill),(o||n.stroke!==a.stroke)&&(t.strokeStyle=n.stroke),(o||n.opacity!==a.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(o||n.blend!==a.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var u=n.lineWidth;t.lineWidth=u/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var r in t)!t.hasOwnProperty(r)||!0!==e&&(!1===e?this.hasOwnProperty(r):null==t[r])||(this[r]=t[r])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var a="radial"===e.type?n:r,o=a(t,e,i),s=e.colorStops,l=0;l<s.length;l++)o.addColorStop(s[l].offset,s[l].color);return o}};for(var o=a.prototype,s=0;s<i.length;s++){var l=i[s];l[0]in o||(o[l[0]]=l[1])}a.getGradient=o.getGradient,t.exports=a},function(t,e){var r=2311;t.exports=function(){return r++}},function(t,e){var r=Array.prototype.slice,n=function(){this._$handlers={}};n.prototype={constructor:n,one:function(t,e,r){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var i=0;i<n[t].length;i++)if(n[t][i].h===e)return this;return n[t].push({h:e,one:!0,ctx:r||this}),this},on:function(t,e,r){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var i=0;i<n[t].length;i++)if(n[t][i].h===e)return this;return n[t].push({h:e,one:!1,ctx:r||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t].length},off:function(t,e){var r=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var n=[],i=0,a=r[t].length;i<a;i++)r[t][i].h!=e&&n.push(r[t][i]);r[t]=n}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},trigger:function(t){if(this._$handlers[t]){var e=arguments,n=e.length;n>3&&(e=r.call(e,1));for(var i=this._$handlers[t],a=i.length,o=0;o<a;){switch(n){case 1:i[o].h.call(i[o].ctx);break;case 2:i[o].h.call(i[o].ctx,e[1]);break;case 3:i[o].h.call(i[o].ctx,e[1],e[2]);break;default:i[o].h.apply(i[o].ctx,e)}i[o].one?(i.splice(o,1),a--):o++}}return this},triggerWithContext:function(t){if(this._$handlers[t]){var e=arguments,n=e.length;n>4&&(e=r.call(e,1,e.length-1));for(var i=e[e.length-1],a=this._$handlers[t],o=a.length,s=0;s<o;){switch(n){case 1:a[s].h.call(i);break;case 2:a[s].h.call(i,e[1]);break;case 3:a[s].h.call(i,e[1],e[2]);break;default:a[s].h.apply(i,e)}a[s].one?(a.splice(s,1),o--):s++}}return this}},t.exports=n},function(t,e,r){"use strict";var n=r(42),i=r(0),a=i.isString,o=i.isFunction,s=i.isObject,l=r(45),h=function(){this.animators=[]};h.prototype={constructor:h,animate:function(t,e){var r,a=!1,o=this,s=this.__zr;if(t){var h=t.split("."),u=o;a="shape"===h[0];for(var c=0,f=h.length;c<f;c++)u&&(u=u[h[c]]);u&&(r=u)}else r=o;if(!r)return void l('Property "'+t+'" is not existed in element '+o.id);var d=o.animators,g=new n(r,e);return g.during(function(t){o.dirty(a)}).done(function(){d.splice(i.indexOf(d,g),1)}),d.push(g),s&&s.animation.addAnimator(g),g},stopAnimation:function(t){for(var e=this.animators,r=e.length,n=0;n<r;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,r,n,i,s){function l(){--u||i&&i()}a(r)?(i=n,n=r,r=0):o(n)?(i=n,n="linear",r=0):o(r)?(i=r,r=0):o(e)?(i=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,r,n,i);var h=this.animators.slice(),u=h.length;u||i&&i();for(var c=0;c<h.length;c++)h[c].done(l).start(n,s)},_animateToShallow:function(t,e,r,n,a){var o={},l=0;for(var h in r)if(r.hasOwnProperty(h))if(null!=e[h])s(r[h])&&!i.isArrayLike(r[h])?this._animateToShallow(t?t+"."+h:h,e[h],r[h],n,a):(o[h]=r[h],l++);else if(null!=r[h])if(t){var u={};u[t]={},u[t][h]=r[h],this.attr(u)}else this.attr(h,r[h]);return l>0&&this.animate(t,!1).when(null==n?500:n,o).delay(a||0),this}},t.exports=h},function(t,e,r){function n(t,e){return t[e]}function i(t,e,r){t[e]=r}function a(t,e,r){return(e-t)*r+t}function o(t,e,r){return r>.5?e:t}function s(t,e,r,n,i){var o=t.length;if(1==i)for(var s=0;s<o;s++)n[s]=a(t[s],e[s],r);else for(var l=o&&t[0].length,s=0;s<o;s++)for(var h=0;h<l;h++)n[s][h]=a(t[s][h],e[s][h],r)}function l(t,e,r){var n=t.length,i=e.length;if(n!==i){if(n>i)t.length=i;else for(var a=n;a<i;a++)t.push(1===r?e[a]:w.call(e[a]))}for(var o=t[0]&&t[0].length,a=0;a<t.length;a++)if(1===r)isNaN(t[a])&&(t[a]=e[a]);else for(var s=0;s<o;s++)isNaN(t[a][s])&&(t[a][s]=e[a][s])}function h(t,e,r){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===r){for(var i=0;i<n;i++)if(t[i]!==e[i])return!1}else for(var a=t[0].length,i=0;i<n;i++)for(var o=0;o<a;o++)if(t[i][o]!==e[i][o])return!1;return!0}function u(t,e,r,n,i,a,o,s,l){var h=t.length;if(1==l)for(var u=0;u<h;u++)s[u]=c(t[u],e[u],r[u],n[u],i,a,o);else for(var f=t[0].length,u=0;u<h;u++)for(var d=0;d<f;d++)s[u][d]=c(t[u][d],e[u][d],r[u][d],n[u][d],i,a,o)}function c(t,e,r,n,i,a,o){var s=.5*(r-t),l=.5*(n-e);return(2*(e-r)+s+l)*o+(-3*(e-r)-2*s-l)*a+s*i+e}function f(t){if(y(t)){var e=t.length;if(y(t[0])){for(var r=[],n=0;n<e;n++)r.push(w.call(t[n]));return r}return w.call(t)}return t}function d(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function g(t){var e=t[t.length-1].value;return y(e&&e[0])?2:1}function p(t,e,r,n,i,f){var p=t._getter,x=t._setter,w="spline"===e,_=n.length;if(_){var b,S=n[0].value,M=y(S),T=!1,k=!1,C=M?g(n):0;n.sort(function(t,e){return t.time-e.time}),b=n[_-1].time;for(var P=[],I=[],A=n[0].value,O=!0,D=0;D<_;D++){P.push(n[D].time/b);var L=n[D].value;if(M&&h(L,A,C)||!M&&L===A||(O=!1),A=L,"string"==typeof L){var F=m.parse(L);F?(L=F,T=!0):k=!0}I.push(L)}if(f||!O){for(var R=I[_-1],D=0;D<_-1;D++)M?l(I[D],R,C):!isNaN(I[D])||isNaN(R)||k||T||(I[D]=R);M&&l(p(t._target,i),R,C);var B,z,N,E,W,q,H=0,j=0;if(T)var X=[0,0,0,0];var Y=function(t,e){var r;if(e<0)r=0;else if(e<j){for(B=Math.min(H+1,_-1),r=B;r>=0&&!(P[r]<=e);r--);r=Math.min(r,_-2)}else{for(r=H;r<_&&!(P[r]>e);r++);r=Math.min(r-1,_-2)}H=r,j=e;var n=P[r+1]-P[r];if(0!==n)if(z=(e-P[r])/n,w)if(E=I[r],N=I[0===r?r:r-1],W=I[r>_-2?_-1:r+1],q=I[r>_-3?_-1:r+2],M)u(N,E,W,q,z,z*z,z*z*z,p(t,i),C);else{var l;if(T)l=u(N,E,W,q,z,z*z,z*z*z,X,1),l=d(X);else{if(k)return o(E,W,z);l=c(N,E,W,q,z,z*z,z*z*z)}x(t,i,l)}else if(M)s(I[r],I[r+1],z,p(t,i),C);else{var l;if(T)s(I[r],I[r+1],z,X,1),l=d(X);else{if(k)return o(I[r],I[r+1],z);l=a(I[r],I[r+1],z)}x(t,i,l)}},V=new v({target:t._target,life:b,loop:t._loop,delay:t._delay,onframe:Y,ondestroy:r});return e&&"spline"!==e&&(V.easing=e),V}}}var v=r(43),m=r(18),x=r(0),y=x.isArrayLike,w=Array.prototype.slice,_=function(t,e,r,a){this._tracks={},this._target=t,this._loop=e||!1,this._getter=r||n,this._setter=a||i,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};_.prototype={when:function(t,e){var r=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!r[n]){r[n]=[];var i=this._getter(this._target,n);if(null==i)continue;0!==t&&r[n].push({time:0,value:f(i)})}r[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,r=0;r<e;r++)t[r].call(this)},start:function(t,e){var r,n=this,i=0,a=function(){--i||n._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=p(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),i++,this.animation&&this.animation.addClip(s),r=s)}if(r){var l=r.onframe;r.onframe=function(t,e){l(t,e);for(var r=0;r<n._onframeList.length;r++)n._onframeList[r](t,e)}}return i||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,r=this.animation,n=0;n<e.length;n++){var i=e[n];t&&i.onframe(this._target,1),r&&r.removeClip(i)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}},t.exports=_},function(t,e,r){function n(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}var i=r(44);n.prototype={constructor:n,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var r=(t-this._startTime-this._pausedTime)/this._life;if(!(r<0)){r=Math.min(r,1);var n=this.easing,a="string"==typeof n?i[n]:n,o="function"==typeof a?a(r):r;return this.fire("frame",o),1==r?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}},t.exports=n},function(t,e){var r={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-r.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*r.bounceIn(2*t):.5*r.bounceOut(2*t-1)+.5}};t.exports=r},function(t,e,r){var n=r(19);t.exports=function(){if(0!==n.debugMode)if(1==n.debugMode)for(var t in arguments)throw new Error(arguments[t]);else if(n.debugMode>1)for(var t in arguments)console.log(arguments[t])}},function(t,e,r){var n=r(20),i=r(3),a=new i,o=function(){};o.prototype={constructor:o,drawRectText:function(t,e){var r=this.style;e=r.textRect||e,this.__dirty&&n.normalizeTextStyle(r,!0);var i=r.text;if(null!=i&&(i+=""),n.needDrawText(i,r)){t.save();var o=this.transform;r.transformText?this.setTransform(t):o&&(a.copy(e),a.applyTransform(o),e=a),n.renderText(this,t,i,r,e),t.restore()}}},t.exports=o},function(t,e,r){var n=r(2),i=r(4),a={},o=Math.min,s=Math.max,l=Math.sin,h=Math.cos,u=n.create(),c=n.create(),f=n.create(),d=2*Math.PI;a.fromPoints=function(t,e,r){if(0!==t.length){var n,i=t[0],a=i[0],l=i[0],h=i[1],u=i[1];for(n=1;n<t.length;n++)i=t[n],a=o(a,i[0]),l=s(l,i[0]),h=o(h,i[1]),u=s(u,i[1]);e[0]=a,e[1]=h,r[0]=l,r[1]=u}},a.fromLine=function(t,e,r,n,i,a){i[0]=o(t,r),i[1]=o(e,n),a[0]=s(t,r),a[1]=s(e,n)};var g=[],p=[];a.fromCubic=function(t,e,r,n,a,l,h,u,c,f){var d,v=i.cubicExtrema,m=i.cubicAt,x=v(t,r,a,h,g);for(c[0]=1/0,c[1]=1/0,f[0]=-1/0,f[1]=-1/0,d=0;d<x;d++){var y=m(t,r,a,h,g[d]);c[0]=o(y,c[0]),f[0]=s(y,f[0])}for(x=v(e,n,l,u,p),d=0;d<x;d++){var w=m(e,n,l,u,p[d]);c[1]=o(w,c[1]),f[1]=s(w,f[1])}c[0]=o(t,c[0]),f[0]=s(t,f[0]),c[0]=o(h,c[0]),f[0]=s(h,f[0]),c[1]=o(e,c[1]),f[1]=s(e,f[1]),c[1]=o(u,c[1]),f[1]=s(u,f[1])},a.fromQuadratic=function(t,e,r,n,a,l,h,u){var c=i.quadraticExtremum,f=i.quadraticAt,d=s(o(c(t,r,a),1),0),g=s(o(c(e,n,l),1),0),p=f(t,r,a,d),v=f(e,n,l,g);h[0]=o(t,a,p),h[1]=o(e,l,v),u[0]=s(t,a,p),u[1]=s(e,l,v)},a.fromArc=function(t,e,r,i,a,o,s,g,p){var v=n.min,m=n.max,x=Math.abs(a-o);if(x%d<1e-4&&x>1e-4)return g[0]=t-r,g[1]=e-i,p[0]=t+r,void(p[1]=e+i);if(u[0]=h(a)*r+t,u[1]=l(a)*i+e,c[0]=h(o)*r+t,c[1]=l(o)*i+e,v(g,u,c),m(p,u,c),a%=d,a<0&&(a+=d),o%=d,o<0&&(o+=d),a>o&&!s?o+=d:a<o&&s&&(a+=d),s){var y=o;o=a,a=y}for(var w=0;w<o;w+=Math.PI/2)w>a&&(f[0]=h(w)*r+t,f[1]=l(w)*i+e,v(g,f,g),m(p,f,p))},t.exports=a},function(t,e,r){"use strict";function n(t,e){return Math.abs(t-e)<y}function i(){var t=_[0];_[0]=_[1],_[1]=t}function a(t,e,r,n,a,o,s,l,h,u){if(u>e&&u>n&&u>o&&u>l||u<e&&u<n&&u<o&&u<l)return 0;var c=p.cubicRootAt(e,n,o,l,u,w);if(0===c)return 0;for(var f,d,g=0,v=-1,m=0;m<c;m++){var x=w[m],y=0===x||1===x?.5:1;p.cubicAt(t,r,a,s,x)<h||(v<0&&(v=p.cubicExtrema(e,n,o,l,_),_[1]<_[0]&&v>1&&i(),f=p.cubicAt(e,n,o,l,_[0]),v>1&&(d=p.cubicAt(e,n,o,l,_[1]))),2==v?x<_[0]?g+=f<e?y:-y:x<_[1]?g+=d<f?y:-y:g+=l<d?y:-y:x<_[0]?g+=f<e?y:-y:g+=l<f?y:-y)}return g}function o(t,e,r,n,i,a,o,s){if(s>e&&s>n&&s>a||s<e&&s<n&&s<a)return 0;var l=p.quadraticRootAt(e,n,a,s,w);if(0===l)return 0;var h=p.quadraticExtremum(e,n,a);if(h>=0&&h<=1){for(var u=0,c=p.quadraticAt(e,n,a,h),f=0;f<l;f++){var d=0===w[f]||1===w[f]?.5:1,g=p.quadraticAt(t,r,i,w[f]);g<o||(w[f]<h?u+=c<e?d:-d:u+=a<c?d:-d)}return u}var d=0===w[0]||1===w[0]?.5:1,g=p.quadraticAt(t,r,i,w[0]);return g<o?0:a<e?d:-d}function s(t,e,r,n,i,a,o,s){if((s-=e)>r||s<-r)return 0;var l=Math.sqrt(r*r-s*s);w[0]=-l,w[1]=l;var h=Math.abs(n-i);if(h<1e-4)return 0;if(h%x<1e-4){n=0,i=x;var u=a?1:-1;return o>=w[0]+t&&o<=w[1]+t?u:0}if(a){var l=n;n=g(i),i=g(l)}else n=g(n),i=g(i);n>i&&(i+=x);for(var c=0,f=0;f<2;f++){var d=w[f];if(d+t>o){var p=Math.atan2(s,d),u=a?1:-1;p<0&&(p=x+p),(p>=n&&p<=i||p+x>=n&&p+x<=i)&&(p>Math.PI/2&&p<1.5*Math.PI&&(u=-u),c+=u)}}return c}function l(t,e,r,i,l){for(var u=0,g=0,p=0,x=0,y=0,w=0;w<t.length;){var _=t[w++];switch(_===h.M&&w>1&&(r||(u+=v(g,p,x,y,i,l))),1==w&&(g=t[w],p=t[w+1],x=g,y=p),_){case h.M:x=t[w++],y=t[w++],g=x,p=y;break;case h.L:if(r){if(m(g,p,t[w],t[w+1],e,i,l))return!0}else u+=v(g,p,t[w],t[w+1],i,l)||0;g=t[w++],p=t[w++];break;case h.C:if(r){if(c.containStroke(g,p,t[w++],t[w++],t[w++],t[w++],t[w],t[w+1],e,i,l))return!0}else u+=a(g,p,t[w++],t[w++],t[w++],t[w++],t[w],t[w+1],i,l)||0;g=t[w++],p=t[w++];break;case h.Q:if(r){if(f.containStroke(g,p,t[w++],t[w++],t[w],t[w+1],e,i,l))return!0}else u+=o(g,p,t[w++],t[w++],t[w],t[w+1],i,l)||0;g=t[w++],p=t[w++];break;case h.A:var b=t[w++],S=t[w++],M=t[w++],T=t[w++],k=t[w++],C=t[w++],P=(t[w++],1-t[w++]),I=Math.cos(k)*M+b,A=Math.sin(k)*T+S;w>1?u+=v(g,p,I,A,i,l):(x=I,y=A);var O=(i-b)*T/M+b;if(r){if(d.containStroke(b,S,T,k,k+C,P,e,O,l))return!0}else u+=s(b,S,T,k,k+C,P,O,l);g=Math.cos(k+C)*M+b,p=Math.sin(k+C)*T+S;break;case h.R:x=g=t[w++],y=p=t[w++];var D=t[w++],L=t[w++],I=x+D,A=y+L;if(r){if(m(x,y,I,y,e,i,l)||m(I,y,I,A,e,i,l)||m(I,A,x,A,e,i,l)||m(x,A,x,y,e,i,l))return!0}else u+=v(I,y,I,A,i,l),u+=v(x,A,x,y,i,l);break;case h.Z:if(r){if(m(g,p,x,y,e,i,l))return!0}else u+=v(g,p,x,y,i,l);g=x,p=y}}return r||n(p,y)||(u+=v(g,p,x,y,i,l)||0),0!==u}var h=r(6).CMD,u=r(49),c=r(50),f=r(51),d=r(52),g=r(22).normalizeRadian,p=r(4),v=r(53),m=u.containStroke,x=2*Math.PI,y=1e-4,w=[-1,-1,-1],_=[-1,-1];t.exports={contain:function(t,e,r){return l(t,0,!1,e,r)},containStroke:function(t,e,r,n){return l(t,e,!0,r,n)}}},function(t,e){t.exports={containStroke:function(t,e,r,n,i,a,o){if(0===i)return!1;var s=i,l=0,h=t;if(o>e+s&&o>n+s||o<e-s&&o<n-s||a>t+s&&a>r+s||a<t-s&&a<r-s)return!1;if(t===r)return Math.abs(a-t)<=s/2;l=(e-n)/(t-r),h=(t*n-r*e)/(t-r);var u=l*a-o+h;return u*u/(l*l+1)<=s/2*s/2}}},function(t,e,r){var n=r(4);t.exports={containStroke:function(t,e,r,i,a,o,s,l,h,u,c){if(0===h)return!1;var f=h;return!(c>e+f&&c>i+f&&c>o+f&&c>l+f||c<e-f&&c<i-f&&c<o-f&&c<l-f||u>t+f&&u>r+f&&u>a+f&&u>s+f||u<t-f&&u<r-f&&u<a-f&&u<s-f)&&n.cubicProjectPoint(t,e,r,i,a,o,s,l,u,c,null)<=f/2}}},function(t,e,r){var n=r(4);t.exports={containStroke:function(t,e,r,i,a,o,s,l,h){if(0===s)return!1;var u=s;return!(h>e+u&&h>i+u&&h>o+u||h<e-u&&h<i-u&&h<o-u||l>t+u&&l>r+u&&l>a+u||l<t-u&&l<r-u&&l<a-u)&&n.quadraticProjectPoint(t,e,r,i,a,o,l,h,null)<=u/2}}},function(t,e,r){var n=r(22).normalizeRadian,i=2*Math.PI;t.exports={containStroke:function(t,e,r,a,o,s,l,h,u){if(0===l)return!1;var c=l;h-=t,u-=e;var f=Math.sqrt(h*h+u*u);if(f-c>r||f+c<r)return!1;if(Math.abs(a-o)%i<1e-4)return!0;if(s){var d=a;a=n(o),o=n(d)}else a=n(a),o=n(o);a>o&&(o+=i);var g=Math.atan2(u,h);return g<0&&(g+=i),g>=a&&g<=o||g+i>=a&&g+i<=o}}},function(t,e){t.exports=function(t,e,r,n,i,a){if(a>e&&a>n||a<e&&a<n)return 0;if(n===e)return 0;var o=n<e?1:-1,s=(a-e)/(n-e);return 1!==s&&0!==s||(o=n<e?.5:-.5),s*(r-t)+t>i?o:0}},function(t,e){var r=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};r.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")},t.exports=r},function(t,e,r){function n(t,e){var r,n,a,u,c,f,d=t.data,g=i.M,p=i.C,v=i.L,m=i.R,x=i.A,y=i.Q;for(a=0,u=0;a<d.length;){switch(r=d[a++],u=a,n=0,r){case g:case v:n=1;break;case p:n=3;break;case y:n=2;break;case x:var w=e[4],_=e[5],b=l(e[0]*e[0]+e[1]*e[1]),S=l(e[2]*e[2]+e[3]*e[3]),M=h(-e[1]/S,e[0]/b);d[a]*=b,d[a++]+=w,d[a]*=S,d[a++]+=_,d[a++]*=b,d[a++]*=S,d[a++]+=M,d[a++]+=M,a+=2,u=a;break;case m:f[0]=d[a++],f[1]=d[a++],o(f,f,e),d[u++]=f[0],d[u++]=f[1],f[0]+=d[a++],f[1]+=d[a++],o(f,f,e),d[u++]=f[0],d[u++]=f[1]}for(c=0;c<n;c++){var f=s[c];f[0]=d[a++],f[1]=d[a++],o(f,f,e),d[u++]=f[0],d[u++]=f[1]}}}var i=r(6).CMD,a=r(2),o=a.applyTransform,s=[[],[],[]],l=Math.sqrt,h=Math.atan2;t.exports=n},function(t,e,r){var n=r(0),i=r(16),a=r(3),o=function(t){t=t||{},i.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};o.prototype={constructor:o,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var r=this._children,n=r.indexOf(e);n>=0&&(r.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,r=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof o&&t.addChildrenToStorage(e)),r&&r.refresh()},remove:function(t){var e=this.__zr,r=this.__storage,i=this._children,a=n.indexOf(i,t);return a<0?this:(i.splice(a,1),t.parent=null,r&&(r.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(r)),e&&e.refresh(),this)},removeAll:function(){var t,e,r=this._children,n=this.__storage;for(e=0;e<r.length;e++)t=r[e],n&&(n.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(n)),t.parent=null;return r.length=0,this},eachChild:function(t,e){for(var r=this._children,n=0;n<r.length;n++){var i=r[n];t.call(e,i,n)}return this},traverse:function(t,e){for(var r=0;r<this._children.length;r++){var n=this._children[r];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var r=this._children[e];t.addToStorage(r),r instanceof o&&r.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var r=this._children[e];t.delFromStorage(r),r instanceof o&&r.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,r=new a(0,0,0,0),n=t||this._children,i=[],o=0;o<n.length;o++){var s=n[o];if(!s.ignore&&!s.invisible){var l=s.getBoundingRect(),h=s.getLocalTransform(i);h?(r.copy(l),r.applyTransform(h),e=e||r.clone(),e.union(r)):(e=e||l.clone(),e.union(l))}}return e||r}},n.inherits(o,i),t.exports=o},function(t,e,r){function n(t){i.call(this,t)}var i=r(12),a=r(3),o=r(0),s=r(10);n.prototype={constructor:n,type:"image",brush:function(t,e){var r=this.style,n=r.image;r.bind(t,this,e);var i=this._image=s.createOrUpdateImage(n,this._image,this);if(i&&s.isImageReady(i)){var a=r.x||0,o=r.y||0,l=r.width,h=r.height,u=i.width/i.height;if(null==l&&null!=h?l=h*u:null==h&&null!=l?h=l/u:null==l&&null==h&&(l=i.width,h=i.height),this.setTransform(t),r.sWidth&&r.sHeight){var c=r.sx||0,f=r.sy||0;t.drawImage(i,c,f,r.sWidth,r.sHeight,a,o,l,h)}else if(r.sx&&r.sy){var c=r.sx,f=r.sy,d=l-c,g=h-f;t.drawImage(i,c,f,d,g,a,o,l,h)}else t.drawImage(i,a,o,l,h);this.restoreTransform(t),null!=r.text&&this.drawRectText(t,this.getBoundingRect())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new a(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},o.inherits(n,i),t.exports=n},function(t,e,r){var n=r(12),i=r(0),a=r(5),o=r(20),s=function(t){n.call(this,t)};s.prototype={constructor:s,type:"text",brush:function(t,e){var r=this.style;this.__dirty&&o.normalizeTextStyle(r,!0),r.fill=r.stroke=r.shadowBlur=r.shadowColor=r.shadowOffsetX=r.shadowOffsetY=null;var n=r.text;null!=n&&(n+=""),r.bind(t,this,e),o.needDrawText(n,r)&&(this.setTransform(t),o.renderText(this,t,n,r),this.restoreTransform(t))},getBoundingRect:function(){var t=this.style;if(this.__dirty&&o.normalizeTextStyle(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var r=a.getBoundingRect(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.rich);if(r.x+=t.x||0,r.y+=t.y||0,o.getStroke(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;r.x-=n/2,r.y-=n/2,r.width+=n,r.height+=n}this._rect=r}return this._rect}},i.inherits(s,n),t.exports=s},function(t,e,r){"use strict";t.exports=r(1).extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,r){r&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}})},function(t,e,r){var n=r(1),i=r(61);t.exports=n.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:i(n.prototype.brush),buildPath:function(t,e){var r=e.cx,n=e.cy,i=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(o),u=Math.sin(o);t.moveTo(h*i+r,u*i+n),t.lineTo(h*a+r,u*a+n),t.arc(r,n,a,o,s,!l),t.lineTo(Math.cos(s)*i+r,Math.sin(s)*i+n),0!==i&&t.arc(r,n,i,s,o,l),t.closePath()}})},function(t,e,r){var n=r(15),i=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];t.exports=function(t){return n.browser.ie&&n.browser.version>=11?function(){var e,r=this.__clipPaths,n=this.style;if(r)for(var a=0;a<r.length;a++){var o=r[a],s=o&&o.shape,l=o&&o.type;if(s&&("sector"===l&&s.startAngle===s.endAngle||"rect"===l&&(!s.width||!s.height))){for(var h=0;h<i.length;h++)i[h][2]=n[i[h][0]],n[i[h][0]]=i[h][1];e=!0;break}}if(t.apply(this,arguments),e)for(var h=0;h<i.length;h++)n[i[h][0]]=i[h][2]}:t}},function(t,e,r){t.exports=r(1).extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var r=e.cx,n=e.cy,i=2*Math.PI;t.moveTo(r+e.r,n),t.arc(r,n,e.r,0,i,!1),t.moveTo(r+e.r0,n),t.arc(r,n,e.r0,0,i,!0)}})},function(t,e,r){var n=r(23);t.exports=r(1).extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){n.buildPath(t,e,!0)}})},function(t,e,r){function n(t,e,r,n,i,a,o){var s=.5*(r-t),l=.5*(n-e);return(2*(e-r)+s+l)*o+(-3*(e-r)-2*s-l)*a+s*i+e}var i=r(2);t.exports=function(t,e){for(var r=t.length,a=[],o=0,s=1;s<r;s++)o+=i.distance(t[s-1],t[s]);var l=o/2;l=l<r?r:l;for(var s=0;s<l;s++){var h,u,c,f=s/(l-1)*(e?r:r-1),d=Math.floor(f),g=f-d,p=t[d%r];e?(h=t[(d-1+r)%r],u=t[(d+1)%r],c=t[(d+2)%r]):(h=t[0===d?d:d-1],u=t[d>r-2?r-1:d+1],c=t[d>r-3?r-1:d+2]);var v=g*g,m=g*v;a.push([n(h[0],p[0],u[0],c[0],g,v,m),n(h[1],p[1],u[1],c[1],g,v,m)])}return a}},function(t,e,r){var n=r(2),i=n.min,a=n.max,o=n.scale,s=n.distance,l=n.add;t.exports=function(t,e,r,h){var u,c,f,d,g=[],p=[],v=[],m=[];if(h){f=[1/0,1/0],d=[-1/0,-1/0];for(var x=0,y=t.length;x<y;x++)i(f,f,t[x]),a(d,d,t[x]);i(f,f,h[0]),a(d,d,h[1])}for(var x=0,y=t.length;x<y;x++){var w=t[x];if(r)u=t[x?x-1:y-1],c=t[(x+1)%y];else{if(0===x||x===y-1){g.push(n.clone(t[x]));continue}u=t[x-1],c=t[x+1]}n.sub(p,c,u),o(p,p,e);var _=s(w,u),b=s(w,c),S=_+b;0!==S&&(_/=S,b/=S),o(v,p,-_),o(m,p,b);var M=l([],w,v),T=l([],w,m);h&&(a(M,M,f),i(M,M,d),a(T,T,f),i(T,T,d)),g.push(M),g.push(T)}return r&&g.push(g.shift()),g}},function(t,e,r){var n=r(23);t.exports=r(1).extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){n.buildPath(t,e,!1)}})},function(t,e,r){var n=r(21);t.exports=r(1).extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var r=e.x,i=e.y,a=e.width,o=e.height;e.r?n.buildPath(t,e):t.rect(r,i,a,o),t.closePath()}})},function(t,e,r){t.exports=r(1).extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var r=e.x1,n=e.y1,i=e.x2,a=e.y2,o=e.percent;0!==o&&(t.moveTo(r,n),o<1&&(i=r*(1-o)+i*o,a=n*(1-o)+a*o),t.lineTo(i,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}})},function(t,e,r){"use strict";function n(t,e,r){var n=t.cpx2,i=t.cpy2;return null===n||null===i?[(r?c:h)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?c:h)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?u:l)(t.x1,t.cpx1,t.x2,e),(r?u:l)(t.y1,t.cpy1,t.y2,e)]}var i=r(4),a=r(2),o=i.quadraticSubdivide,s=i.cubicSubdivide,l=i.quadraticAt,h=i.cubicAt,u=i.quadraticDerivativeAt,c=i.cubicDerivativeAt,f=[];t.exports=r(1).extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var r=e.x1,n=e.y1,i=e.x2,a=e.y2,l=e.cpx1,h=e.cpy1,u=e.cpx2,c=e.cpy2,d=e.percent;0!==d&&(t.moveTo(r,n),null==u||null==c?(d<1&&(o(r,l,i,d,f),l=f[1],i=f[2],o(n,h,a,d,f),h=f[1],a=f[2]),t.quadraticCurveTo(l,h,i,a)):(d<1&&(s(r,l,u,i,d,f),l=f[1],u=f[2],i=f[3],s(n,h,c,a,d,f),h=f[1],c=f[2],a=f[3]),t.bezierCurveTo(l,h,u,c,i,a)))},pointAt:function(t){return n(this.shape,t,!1)},tangentAt:function(t){var e=n(this.shape,t,!0);return a.normalize(e,e)}})},function(t,e,r){t.exports=r(1).extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var r=e.cx,n=e.cy,i=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),h=Math.sin(a);t.moveTo(l*i+r,h*i+n),t.arc(r,n,i,a,o,!s)}})},function(t,e,r){var n=r(1);t.exports=n.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,r=0;r<e.length;r++)t=t||e[r].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var r=e.paths||[],n=0;n<r.length;n++)r[n].buildPath(t,r[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths,e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),n.prototype.getBoundingRect.call(this)}})},function(t,e,r){"use strict";var n=r(0),i=r(24),a=function(t,e,r,n,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==r?1:r,this.y2=null==n?0:n,this.type="linear",this.global=o||!1,i.call(this,a)};a.prototype={constructor:a},n.inherits(a,i),t.exports=a},function(t,e,r){"use strict";var n=r(0),i=r(24),a=function(t,e,r,n,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==r?.5:r,this.type="radial",this.global=a||!1,i.call(this,n)};a.prototype={constructor:a},n.inherits(a,i),t.exports=a},function(t,e,r){var n=r(11)([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]);t.exports={getItemStyle:function(t,e){var r=n.call(this,t,e),i=this.getBorderLineDash();return i&&(r.lineDash=i),r},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}}},function(t,e,r){var n=r(7);n.extendChartView({type:"wordCloud",render:function(t,e,r){var i=this.group;i.removeAll();var a=t.getData(),o=t.get("gridSize");t.layoutInstance.ondraw=function(t,e,r,s){var l=a.getItemModel(r),h=l.getModel("textStyle.normal"),u=l.getModel("textStyle.emphasis"),c=new n.graphic.Text({style:n.graphic.setTextStyle({},h,{x:s.info.fillTextOffsetX,y:s.info.fillTextOffsetY+.5*e,text:t,textBaseline:"middle",textFill:a.getItemVisual(r,"color"),fontSize:e}),scale:[1/s.info.mu,1/s.info.mu],position:[(s.gx+s.info.gw/2)*o,(s.gy+s.info.gh/2)*o],rotation:s.rot});i.add(c),a.setItemGraphicEl(r,c),n.graphic.setHoverStyle(c,n.graphic.setTextStyle({},u,null,{forMerge:!0},!0))},this._model=t},remove:function(){this.group.removeAll(),this._model.layoutInstance.dispose()},dispose:function(){this._model.layoutInstance.dispose()}})},function(t,e,r){"use strict";var n,i;/*!
 * wordcloud2.js
 * http://timdream.org/wordcloud2.js/
 *
 * Copyright 2011 - 2013 Tim Chien
 * Released under the MIT license
 */
window.setImmediate||(window.setImmediate=function(){return window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var t=[void 0],e="zero-timeout-message",r=function(r){var n=t.length;return t.push(r),window.postMessage(e+n.toString(36),"*"),n};return window.addEventListener("message",function(r){if("string"==typeof r.data&&r.data.substr(0,e.length)===e){r.stopImmediatePropagation();var n=parseInt(r.data.substr(e.length),36);t[n]&&(t[n](),t[n]=void 0)}},!0),window.clearImmediate=function(e){t[e]&&(t[e]=void 0)},r}()||function(t){window.setTimeout(t,0)}}()),window.clearImmediate||(window.clearImmediate=function(){return window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(t){window.clearTimeout(t)}}()),function(r){var a=function(){var t=document.createElement("canvas");if(!t||!t.getContext)return!1;var e=t.getContext("2d");return!!e.getImageData&&(!!e.fillText&&(!!Array.prototype.some&&!!Array.prototype.push))}(),o=function(){if(a){for(var t,e,r=document.createElement("canvas").getContext("2d"),n=20;n;){if(r.font=n.toString(10)+"px sans-serif",r.measureText("Ｗ").width===t&&r.measureText("m").width===e)return n+1;t=r.measureText("Ｗ").width,e=r.measureText("m").width,n--}return 0}}(),s=function(t){for(var e,r,n=t.length;n;e=Math.floor(Math.random()*n),r=t[--n],t[n]=t[e],t[e]=r);return t},l=function(t,e){function r(t,e){return"hsl("+(360*Math.random()).toFixed()+","+(30*Math.random()+70).toFixed()+"%,"+(Math.random()*(e-t)+t).toFixed()+"%)"}if(a){Array.isArray(t)||(t=[t]),t.forEach(function(e,r){if("string"==typeof e){if(t[r]=document.getElementById(e),!t[r])throw"The element id specified is not found."}else if(!e.tagName&&!e.appendChild)throw"You must pass valid HTML elements, or ID of the element."});var n={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,rotationStep:.1,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(e)for(var i in e)i in n&&(n[i]=e[i]);if("function"!=typeof n.weightFactor){var l=n.weightFactor;n.weightFactor=function(t){return t*l}}if("function"!=typeof n.shape)switch(n.shape){case"circle":default:n.shape="circle";break;case"cardioid":n.shape=function(t){return 1-Math.sin(t)};break;case"diamond":case"square":n.shape=function(t){var e=t%(2*Math.PI/4);return 1/(Math.cos(e)+Math.sin(e))};break;case"triangle-forward":n.shape=function(t){var e=t%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"triangle":case"triangle-upright":n.shape=function(t){var e=(t+3*Math.PI/2)%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"pentagon":n.shape=function(t){var e=(t+.955)%(2*Math.PI/5);return 1/(Math.cos(e)+.726543*Math.sin(e))};break;case"star":n.shape=function(t){var e=(t+.955)%(2*Math.PI/10);return(t+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-e)+3.07768*Math.sin(2*Math.PI/10-e)):1/(Math.cos(e)+3.07768*Math.sin(e))}}n.gridSize=Math.max(Math.floor(n.gridSize),4);var h,u,c,f,d,g,p,v=n.gridSize,m=v-n.maskGapWidth,x=Math.abs(n.maxRotation-n.minRotation),y=Math.min(n.maxRotation,n.minRotation),w=n.rotationStep;switch(n.color){case"random-dark":p=function(){return r(10,50)};break;case"random-light":p=function(){return r(50,90)};break;default:"function"==typeof n.color&&(p=n.color)}var _=null;"function"==typeof n.classes&&(_=n.classes);var b,S=!1,M=[],T=function(t){var e,r,n=t.currentTarget,i=n.getBoundingClientRect();t.touches?(e=t.touches[0].clientX,r=t.touches[0].clientY):(e=t.clientX,r=t.clientY);var a=e-i.left,o=r-i.top,s=Math.floor(a*(n.width/i.width||1)/v),l=Math.floor(o*(n.height/i.height||1)/v);return M[s][l]},k=function(t){var e=T(t);if(b!==e){if(b=e,!e)return void n.hover(void 0,void 0,t);n.hover(e.item,e.dimension,t)}},C=function(t){var e=T(t);e&&(n.click(e.item,e.dimension,t),t.preventDefault())},P=[],I=function(t){if(P[t])return P[t];var e=8*t,r=e,i=[];for(0===t&&i.push([f[0],f[1],0]);r--;){var a=1;"circle"!==n.shape&&(a=n.shape(r/e*2*Math.PI)),i.push([f[0]+t*a*Math.cos(-r/e*2*Math.PI),f[1]+t*a*Math.sin(-r/e*2*Math.PI)*n.ellipticity,r/e*2*Math.PI])}return P[t]=i,i},A=function(){return n.abortThreshold>0&&(new Date).getTime()-g>n.abortThreshold},O=function(){return 0===n.rotateRatio?0:Math.random()>n.rotateRatio?0:0===x?y:y+Math.round(Math.random()*x/w)*w},D=function(t,e,r){var i=n.weightFactor(e);if(i<=n.minSize)return!1;var a=1;i<o&&(a=function(){for(var t=2;t*i<o;)t+=2;return t}());var s=document.createElement("canvas"),l=s.getContext("2d",{willReadFrequently:!0});l.font=n.fontWeight+" "+(i*a).toString(10)+"px "+n.fontFamily;var h=l.measureText(t).width/a,u=Math.max(i*a,l.measureText("m").width,l.measureText("Ｗ").width)/a,c=h+2*u,f=3*u,d=Math.ceil(c/v),g=Math.ceil(f/v);c=d*v,f=g*v;var p=-h/2,m=.4*-u,x=Math.ceil((c*Math.abs(Math.sin(r))+f*Math.abs(Math.cos(r)))/v),y=Math.ceil((c*Math.abs(Math.cos(r))+f*Math.abs(Math.sin(r)))/v),w=y*v,_=x*v;s.setAttribute("width",w),s.setAttribute("height",_),l.scale(1/a,1/a),l.translate(w*a/2,_*a/2),l.rotate(-r),l.font=n.fontWeight+" "+(i*a).toString(10)+"px "+n.fontFamily,l.fillStyle="#000",l.textBaseline="middle",l.fillText(t,p*a,(m+.5*i)*a);var b=l.getImageData(0,0,w,_).data;if(A())return!1;for(var S,M,T,k=[],C=y,P=[x/2,y/2,x/2,y/2];C--;)for(S=x;S--;){T=v;t:for(;T--;)for(M=v;M--;)if(b[4*((S*v+T)*w+(C*v+M))+3]){k.push([C,S]),C<P[3]&&(P[3]=C),C>P[1]&&(P[1]=C),S<P[0]&&(P[0]=S),S>P[2]&&(P[2]=S);break t}}return{mu:a,occupied:k,bounds:P,gw:y,gh:x,fillTextOffsetX:p,fillTextOffsetY:m,fillTextWidth:h,fillTextHeight:u,fontSize:i}},L=function(t,e,r,i,a){for(var o=a.length;o--;){var s=t+a[o][0],l=e+a[o][1];if(s>=u||l>=c||s<0||l<0){if(!n.drawOutOfBound)return!1}else if(!h[s][l])return!1}return!0},F=function(e,r,i,a,o,s,l,h,u){var c,f=i.fontSize;c=p?p(a,o,f,s,l):n.color;var d;d=_?_(a,o,f,s,l):n.classes;var g=i.bounds;g[3],g[0],g[1],g[3],g[2],g[0],t.forEach(function(t){if(t.getContext){var o=t.getContext("2d"),s=i.mu;o.save(),o.scale(1/s,1/s),o.font=n.fontWeight+" "+(f*s).toString(10)+"px "+n.fontFamily,o.fillStyle=c,o.translate((e+i.gw/2)*v*s,(r+i.gh/2)*v*s),0!==h&&o.rotate(-h),o.textBaseline="middle",o.fillText(a,i.fillTextOffsetX*s,(i.fillTextOffsetY+.5*f)*s),o.restore()}else{var l=document.createElement("span"),g="";g="rotate("+-h/Math.PI*180+"deg) ",1!==i.mu&&(g+="translateX(-"+i.fillTextWidth/4+"px) scale("+1/i.mu+")");var p={position:"absolute",display:"block",font:n.fontWeight+" "+f*i.mu+"px "+n.fontFamily,left:(e+i.gw/2)*v+i.fillTextOffsetX+"px",top:(r+i.gh/2)*v+i.fillTextOffsetY+"px",width:i.fillTextWidth+"px",height:i.fillTextHeight+"px",lineHeight:f+"px",whiteSpace:"nowrap",transform:g,webkitTransform:g,msTransform:g,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};c&&(p.color=c),l.textContent=a;for(var m in p)l.style[m]=p[m];if(u)for(var x in u)l.setAttribute(x,u[x]);d&&(l.className+=d),t.appendChild(l)}})},R=function(e,r,n,i,a){if(!(e>=u||r>=c||e<0||r<0)){if(h[e][r]=!1,n){t[0].getContext("2d").fillRect(e*v,r*v,m,m)}S&&(M[e][r]={item:a,dimension:i})}},B=function(e,r,i,a,o,s){var l,h=o.occupied,f=n.drawMask;f&&(l=t[0].getContext("2d"),l.save(),l.fillStyle=n.maskColor);var d;if(S){var g=o.bounds;d={x:(e+g[3])*v,y:(r+g[0])*v,w:(g[1]-g[3]+1)*v,h:(g[2]-g[0]+1)*v}}for(var p=h.length;p--;){var m=e+h[p][0],x=r+h[p][1];m>=u||x>=c||m<0||x<0||R(m,x,f,d,s)}f&&l.restore()},z=function(t){var e,r,i;Array.isArray(t)?(e=t[0],r=t[1]):(e=t.word,r=t.weight,i=t.attributes);var a=O(),o=D(e,r,a);if(!o)return!1;if(A())return!1;if(!n.drawOutOfBound){var l=o.bounds;if(l[1]-l[3]+1>u||l[2]-l[0]+1>c)return!1}for(var h=d+1;h--;){var f=I(d-h);n.shuffle&&(f=[].concat(f),s(f));for(var g=0;g<f.length;g++){var p=function(n){var s=Math.floor(n[0]-o.gw/2),l=Math.floor(n[1]-o.gh/2);o.gw,o.gh;return!!L(s,l,0,0,o.occupied)&&(F(s,l,o,e,r,d-h,n[2],a,i),B(s,l,0,0,o,t),{gx:s,gy:l,rot:a,info:o})}(f[g]);if(p)return p}}return null},N=function(e,r,n){if(r)return!t.some(function(t){var i=document.createEvent("CustomEvent");return i.initCustomEvent(e,!0,r,n||{}),!t.dispatchEvent(i)},this);t.forEach(function(t){var i=document.createEvent("CustomEvent");i.initCustomEvent(e,!0,r,n||{}),t.dispatchEvent(i)},this)};!function(){var e=t[0];if(e.getContext)u=Math.ceil(e.width/v),c=Math.ceil(e.height/v);else{var r=e.getBoundingClientRect();u=Math.ceil(r.width/v),c=Math.ceil(r.height/v)}if(N("wordcloudstart",!0)){f=n.origin?[n.origin[0]/v,n.origin[1]/v]:[u/2,c/2],d=Math.floor(Math.sqrt(u*u+c*c)),h=[];var i,a,o;if(!e.getContext||n.clearCanvas)for(t.forEach(function(t){if(t.getContext){var e=t.getContext("2d");e.fillStyle=n.backgroundColor,e.clearRect(0,0,u*(v+1),c*(v+1)),e.fillRect(0,0,u*(v+1),c*(v+1))}else t.textContent="",t.style.backgroundColor=n.backgroundColor,t.style.position="relative"}),i=u;i--;)for(h[i]=[],a=c;a--;)h[i][a]=!0;else{var s=document.createElement("canvas").getContext("2d");s.fillStyle=n.backgroundColor,s.fillRect(0,0,1,1);var l=s.getImageData(0,0,1,1).data,p=e.getContext("2d").getImageData(0,0,u*v,c*v).data;i=u;for(var m,x;i--;)for(h[i]=[],a=c;a--;){x=v;t:for(;x--;)for(m=v;m--;)for(o=4;o--;)if(p[4*((a*v+x)*u*v+(i*v+m))+o]!==l[o]){h[i][a]=!1;break t}!1!==h[i][a]&&(h[i][a]=!0)}p=s=l=void 0}if(n.hover||n.click){for(S=!0,i=u+1;i--;)M[i]=[];n.hover&&e.addEventListener("mousemove",k),n.click&&(e.addEventListener("click",C),e.addEventListener("touchstart",C),e.addEventListener("touchend",function(t){t.preventDefault()}),e.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),e.addEventListener("wordcloudstart",function t(){e.removeEventListener("wordcloudstart",t),e.removeEventListener("mousemove",k),e.removeEventListener("click",C),b=void 0})}o=0;var y,w;0!==n.wait?(y=window.setTimeout,w=window.clearTimeout):(y=window.setImmediate,w=window.clearImmediate);var _=function(e,r){t.forEach(function(t){t.removeEventListener(e,r)},this)},T=function t(){_("wordcloudstart",t),w(P)};!function(e,r){t.forEach(function(t){t.addEventListener(e,r)},this)}("wordcloudstart",T);var P=y(function t(){if(o>=n.list.length)return w(P),N("wordcloudstop",!1),void _("wordcloudstart",T);g=(new Date).getTime();var e=z(n.list[o]),r=!N("wordclouddrawn",!0,{item:n.list[o],drawn:e});if(A()||r)return w(P),n.abort(),N("wordcloudabort",!1),N("wordcloudstop",!1),void _("wordcloudstart",T);o++,P=y(t,n.wait)},n.wait)}}()}};l.isSupported=a,l.minFontSize=o,n=[],void 0!==(i=function(){return l}.apply(e,n))&&(t.exports=i)}()}])});