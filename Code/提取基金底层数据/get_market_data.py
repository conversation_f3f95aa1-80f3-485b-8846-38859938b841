# -*- coding: utf-8 -*-
"""
Created on Mon May 10 16:45:47 2021

@author: qiurui
"""


import pandas as pd 
from sqlalchemy import create_engine
import numpy as np
import cx_Oracle

oracle_conn = cx_Oracle.connect('wind_read/Wind_read_100010@192.168.105.38:1521/wind', encoding="UTF-8")
# oracle_url = "oracle://yjsread:Y?IACIEJ123456@*************:1521/wdzx"

def get_fund_info():
    "获取基金基本信息"
    
    # engine = create_engine(oracle_url)
    # con = engine.connect()
        
    sqlcode = """
    SELECT F_INFO_WINDCODE as fundcode, F_INFO_NAME as fundname, F_INFO_FULLNAME as fullname, F_INFO_CORP_FUNDMANAGEMENTCOMP as company,\
    F_INFO_SETUPDATE as setupdate, F_INFO_MATURITYDATE as enddate, F_INFO_ISINITIAL as isinitial
    FROM winddf.ChinaMutualFundDescription
    where F_INFO_SETUPDATE is not null
    order by F_INFO_WINDCODE 
    """

    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['FUNDCODE'])
    # con.close()
    
    return tDATA


def get_fundsector():
    "获取wind基金分类"
         
    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    select F_INFO_WINDCODE as fundcode,S_INFO_SECTOR as sectorcode,S_INFO_SECTORENTRYDT as entrydate,S_INFO_SECTOREXITDT as exitdate
    from winddf.ChinaMutualFundSector
    where S_INFO_SECTOR like '200101%%'
    order by F_INFO_WINDCODE
    """    
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['FUNDCODE'])
    # con.close()
    
    return tDATA



def get_windsector():
    "获得wind基金分类"
    
    # mysql_url = "mysql+pymysql://qiurui:QRe@der!@#@************:3306/fund?charset=utf8"
    # engine = create_engine(mysql_url)
    # con = engine.connect()
    #
    # sqlcode = 'SELECT * FROM windsector order by sectorcode'
    # tDATA = pd.read_sql(sqlcode, con, index_col = ['sectorcode'])
    # con.close()
    sqlcode = """
       select INDUSTRIESCODE as sectorcode,INDUSTRIESNAME as fundtype
       from winddf.AShareIndustriesCode       
       order by sectorcode
       """

    tDATA = pd.read_sql(sqlcode, oracle_conn,index_col = ['SECTORCODE'])


    
    return tDATA


def get_fund_nav():
    "获取基金净值"
    
    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    SELECT PRICE_DATE as pricedt, F_INFO_WINDCODE as fundcode, F_NAV_ADJUSTED as adjnav
    FROM winddf.ChinaMutualFundNAV  
    order by F_INFO_WINDCODE, PRICE_DATE
    """
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['PRICEDT','FUNDCODE'])
    # con.close()
    
    return tDATA


def get_fund_assetport():
    "获取基金资产组合"
    
    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    SELECT F_PRT_ENDDATE as reportdt, S_INFO_WINDCODE as fundcode, F_PRT_STOCKVALUE as stockvalue, F_PRT_STOCKTOTOT as stockratio,\
    F_PRT_BONDTOTOT as bondratio, F_PRT_FUNDVALUE as fundvalue, F_PRT_TOTALASSET as totasset
    FROM winddf.ChinaMutualFundAssetPortfolio
    order by F_PRT_ENDDATE, S_INFO_WINDCODE
    """
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['REPORTDT','FUNDCODE'])
    # con.close()
    
    return tDATA  

    

def get_fund_stockport():
    "获取基金股票组合"

    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    SELECT F_PRT_ENDDATE as reportdt, S_INFO_WINDCODE as fundcode, S_INFO_STOCKWINDCODE as stockcode, F_PRT_STKVALUE as stkvalue, \
    F_PRT_STKVALUETONAV as stkratio2nav, STOCK_PER as stkratio
    FROM winddf.ChinaMutualFundStockPortfolio
    order by F_PRT_ENDDATE, S_INFO_WINDCODE
    """
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['REPORTDT','FUNDCODE'])
    # con.close()
    
    return tDATA 



def get_qdfund_scope():
    "获取QDII基金地区配置数据"

    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    SELECT ENDDATE as reportdt, S_INFO_WINDCODE as fundcode, SCOPE as scope, VALUE as value, POSSTKTONAV as ratio
    FROM winddf.QDIIScopePortfolio
    order by ENDDATE, S_INFO_WINDCODE
    """
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['REPORTDT','FUNDCODE','SCOPE'])
    # con.close()
    
    return tDATA 


def get_fund_fundport():
    "获取基金的基金持仓"
    
    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    SELECT a.END_DT AS reportdt, a.S_INFO_WINDCODE AS fundcode, a.S_INFO_HOLDWINDCODE AS fcode,\
    a.VALUE AS value, a.VALUETONAV as ratio, a.QUANTITY as quantity, b.f_info_name AS fname
    from winddf.CMFOtherPortfolio a LEFT JOIN winddf.ChinaMutualFundDescription b
    on a.S_INFO_HOLDWINDCODE = b.F_INFO_WINDCODE
    WHERE b.f_info_windcode IS NOT null
    order by a.END_DT, a.S_INFO_WINDCODE, a.VALUE
    """    
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['REPORTDT','FUNDCODE'])
    # con.close()
    
    return tDATA


def get_fund_asset():
    
    # engine = create_engine(oracle_url)
    # con = engine.connect()
    
    sqlcode = """
    SELECT PRICE_DATE as reportdt, F_INFO_WINDCODE as fundcode, NETASSET_TOTAL as totasset
    FROM winddf.ChinaMutualFundNAV
    where NETASSET_TOTAL is not null
    order by PRICE_DATE,F_INFO_WINDCODE
    """
    
    tDATA = pd.read_sql(sqlcode, oracle_conn, index_col = ['REPORTDT','FUNDCODE'])
    # con.close()
    
    return tDATA 




#%%

if __name__ == "__main__":

    fundinfo = get_fund_info()
    print('1')
    fundsector = get_fundsector()
    print('2')
    windsector = get_windsector()
    fundsector = fundsector.join(windsector,on='SECTORCODE')
    print('3')
    fundassetport = get_fund_assetport()
    print('4')
    fundstockport = get_fund_stockport()
    print('5')
    fundscope = get_qdfund_scope()
    print('6')
    fundfundport = get_fund_fundport()
    print('7')
    fundasset = get_fund_asset()
    print('8')
    rawfundnav=get_fund_nav()
    rawfundnav=rawfundnav.ADJNAV.unstack()

        
    
    fundinfo.to_pickle('fundinfo.pkl')
    funfundnav.to_pickle('rawfundnav.pkl')
    fundassetport.to_pickle('fundassetport.pkl')
    fundsector.to_pickle('fundsector.pkl')
    rawdstockport.to_pickle('fundstockport.pkl')
    fundscope.to_pickle('fundscope.pkl')
    fundfundport.to_pickle('fundfundport.pkl')
    fundasset.to_pickle('fundasset.pkl')
    
    
    
    
    
    
    