# -*- coding: utf-8 -*-
"""
Created on Thu Feb 22 14:35:29 2024

@author: qiurui
"""

import pandas as pd
from sqlalchemy import create_engine
import numpy as np

import seaborn as sns
import matplotlib.pyplot as plt


from WindPy import *
w.start();


oracle_url = "oracle://yjsread:Y?IACIEJ123456@192.168.1.100:1521/wdzx"


def get_tradingdate(begindt, enddt, freq):
    # 获得交易日
    mysql_url = "mysql+pymysql://qiurui:QRe@der!@#@192.168.1.83:3306/factor?charset=utf8"
    engine = create_engine(mysql_url)  
    con = engine.connect()  
    
    sqlcode = """
    SELECT tradingdate 
    FROM acalendar 
    WHERE freq = '{0}' and tradingdate >= '{1}' and tradingdate <= '{2}'
    order by tradingdate
    """.format(freq, begindt, enddt)
    
    tDATA = pd.read_sql(sqlcode, con)
    con.close()
    
    return tDATA


def get_calendar(begindt, enddt, freq):
    "获取自然日"
    
    mysql_url = "mysql+pymysql://qiurui:QRe@der!@#@192.168.1.83:3306/fund?charset=utf8"
    engine = create_engine(mysql_url)
    
    sqlcode = """
    SELECT date FROM calendar WHERE freq = '{0}' and date >= '{1}' and date <= '{2}' order by date
    """.format(freq, begindt, enddt)
    
    tDATA = pd.read_sql(sqlcode, engine)
    
    return tDATA


def process_fundport(dt, fundfundport, fundinfo, fundsector, foflist):
  
    tport = fundfundport.loc[dt]
    d = [x for x in foflist if x in tport.index]
    tport = tport.loc[d].sort_values(by='value',ascending=False).groupby(level=0).head(10)
    tport = tport.join(fundinfo['company'])
    tport = tport.reset_index().set_index(keys='fcode').join(fundinfo, lsuffix='_mother', rsuffix='_son')
    tport['isinternal'] = 0
    tport.loc[tport['company_mother']==tport['company_son'],'isinternal'] = 1
    
    #tdata = tport.groupby(['fundcode','isinternal'])['quantity'].sum()

    
    d1 = tport.groupby('fullname')['fname'].count()
    d2 = tport.groupby('fullname')[['value','quantity']].sum()
    d3 = tport.groupby(['fullname','isinternal'])['quantity'].sum()
    tDATA = []
    keylist = []
    for name,group in d3.groupby(level=0):
        group = group.reset_index()
        d = group[group['isinternal'] == 1]
        if d.index.size > 0 :
            ratio = d['quantity'].values[0]/group['quantity'].sum()            
        else:
            ratio = 0
        tDATA.append(ratio)
        keylist.append(name)
    d4 = pd.Series(tDATA, index = keylist)
                    
    d = pd.concat([d1,d2,d4],axis=1)
    d.columns = ['num','value','quantity','internalratio']


    fundpool = fundinfo[fundinfo['isinitial']==1]
    tsector = fundsector[(fundsector.entrydate <= dt) & ((fundsector.exitdate.isnull()) | (fundsector.exitdate >= dt))]
    
    d = d.join(fundpool.reset_index().set_index(keys='fullname')).reset_index().set_index(keys='fundcode').join(tsector)
    d = d[['fundname','sectorname1','sectorname2','num','value','quantity','internalratio']]
    d['wei'] = d['value']/d['value'].sum()
    d = d.sort_values(by='value',ascending=False)
   
    return d


def analyze_fundport(fport,tasset,ftype,typelist):  
    

    d = fport
    stratio = tasset['stockratio']
    bondratio = tasset['bondratio']
    
    tdata = d[d['sectorname2'].isin(typelist)]
    if ftype == '主动权益基金':
        t = stratio[stratio > 60]
        flist = [x for x in tdata.index if x in t.index]
        tdata = tdata.loc[flist]
    elif ftype == '固收+基金':
        t1 = bondratio[bondratio > 60]
        t2 = stratio[stratio > 0]
        flist = [x for x in tdata.index if (x in t1.index) & (x in t2.index)]        
        tdata = tdata.loc[flist]
        
    out = tdata[['fundname','sectorname2','num','value','deltanum','deltashare','internalratio']]

    
    return out




def get_fund_assetport():
    "获取基金资产组合"
    
    engine = create_engine(oracle_url) 
    con = engine.connect() 
    
    sqlcode = """
    SELECT F_PRT_ENDDATE as reportdt, S_INFO_WINDCODE as fundcode, F_PRT_NETASSET as netasset, \
    F_PRT_STOCKVALUE as stockvalue,  F_PRT_COVERTBOND as ctbondvalue, F_PRT_FUNDVALUE as fundvalue, F_PRT_HKSTOCKVALUE as hkvalue 
    FROM wind_filesync.ChinaMutualFundAssetPortfolio
    order by F_PRT_ENDDATE, S_INFO_WINDCODE
    """
    
    tDATA = pd.read_sql(sqlcode, con, index_col = ['reportdt','fundcode'])
    con.close()
    
    return tDATA  



def get_fund_universe(utype):
    "获取基金范围"
    
    mysql_url = "mysql+pymysql://qiurui:QRe@der!@#@192.168.1.83:3306/fund?charset=utf8"
    engine = create_engine(mysql_url)  
    
    sqlcode = """
    SELECT tradingdate, fundcode
    FROM universe 
    WHERE utype = '{0}'
    order by tradingdate, fundcode
    """.format(utype)
    
    tDATA = pd.read_sql(sqlcode, engine, index_col = ['tradingdate'])
    
    return tDATA


def newindus(x):
    if x in ['银行','非银行金融','综合金融','房地产']:
        indus = '金融地产'
    elif x in ['电子','计算机','通信','传媒']:
        indus = '科技'
    elif x in ['电力设备及新能源']:
        indus = '新能源'
    elif x in ['家电','农林牧渔','食品饮料','消费者服务','商贸零售','纺织服装']:
        indus = '消费'
    elif x in ['基础化工','机械','汽车','建材','建筑','钢铁','石油石化','煤炭','有色金属','电力及公用事业','交通运输','轻工制造']:        
        indus = '周期'
    elif x in ['国防军工']:
        indus = '军工'    
    elif x in ['医药']:
        indus = '医药'    
    else:
        indus = '综合'
    return indus



def get_ashare_sector():
    "获取A股行业分类"
    
    engine = create_engine(oracle_url) 
    con = engine.connect() 
    
    sqlcode = """ 
    select a.s_info_windcode as stockcode, a.ENTRY_DT as entrydt, a.REMOVE_DT as removedt, b.Industriesname as indusname 
    from wind_filesync.AShareIndustriesClassCITICS a, wind_filesync.AShareIndustriesCode  b 
    where substr(a.CITICS_IND_CODE, 1, 4) = substr(b.IndustriesCode, 1, 4) and b.levelnum = 2
    ORDER BY a.s_info_windcode, a.ENTRY_DT
    """
    
    tDATA = pd.read_sql(sqlcode, con, index_col = ['stockcode'])
    con.close()
    
    return tDATA



def get_risk_model(dt, pool, con = None):
    "提取风险指定日期风险模型相关数据"
    if con is None:
        mysql_url = "mysql+pymysql://qiurui:QRe@der!@#@192.168.1.83:3306/dfrisk2020?charset=utf8"
        engine = create_engine(mysql_url)
        con = engine.connect()
        flag = True
    else:
        flag = False

    # 日期
    dt = pd.to_datetime(dt).strftime('%Y%m%d')

    # 风险因子暴露
    res = con.execute("SELECT fvjson FROM dfrisk2020.factorexposure WHERE tradingdate ='{0}' and universe='{1}'" .format(dt,pool))
    risk_factors = pd.read_json(res.fetchone()[0], orient='split', convert_axes=False)

    # 因子收益
    res = con.execute("SELECT fretjson FROM dfrisk2020.factorreturnwls WHERE tradingdate ='{0}' and universe='{1}'" .format(dt,pool))
    factor_return = pd.read_json(res.fetchone()[0], orient='split', convert_axes=False, typ='series')

    if flag:
        con.close()
        engine.dispose()

    return risk_factors,factor_return * 0.01






#%%

if __name__ == "__main__":
    
    updatedt = '20240401'
    
    
    fundinfo = pd.read_pickle('fundinfo.pkl')
    fundsector = pd.read_pickle('fundsector.pkl')
    fundfundport = pd.read_pickle('fundfundport.pkl')
    fundstockport = pd.read_pickle('fundstockport.pkl')
    #fundassetport = pd.read_pickle('fundassetport.pkl')
    rawfundnav = pd.read_pickle('rawfundnav.pkl')
    fundscope = pd.read_pickle('fundscope.pkl')
    fundasset = pd.read_pickle('fundasset.pkl')
    
    fundassetport = get_fund_assetport()
    asharesector = get_ashare_sector()
    tdata = asharesector['indusname'].apply(newindus)
    asharesector['newindus'] = tdata
    
    
      
    
    #%%
    
    tradingdate = get_tradingdate('19980101', updatedt, 'D').tradingdate.tolist()
    mondate = get_tradingdate('19980101', updatedt, 'M').tradingdate.tolist()
    quardate = get_tradingdate('19980101', updatedt, 'Q').tradingdate.tolist()
    
    monlist = get_calendar('19980101', updatedt, 'M').date.tolist()
    quarlist = get_calendar('19980101', updatedt, 'Q').date.tolist()
    annlist = [x for x in quarlist if (x[4:6]=='06')|(x[4:6]=='12')]

    
    
    #%% FOF基金分类，按照底层资产属性(基于半年报、年报)

    tDATA1 = []
    tDATA2 = []
    tDATA3 = []
    tDATA4 = []
    samplenum = pd.DataFrame(columns=['active','balance','stable'],dtype='float')
    newfofstratio = pd.DataFrame(columns=['active','balance','stable'],dtype='float')
    for q in annlist[38:]:
        
    
        #fof基金筛选      
        tdata = fundinfo[fundinfo['fullname'].str.contains('FOF').astype(bool)]
        fofpool = tdata[(tdata['setupdate'] <= q) & (tdata['enddate'].isnull()|(tdata['enddate']>= q))]
        fofpool = fofpool[fofpool['isinitial']==1]
        foflist = fofpool.index.tolist()   
        
        
        fport0 = fundassetport.loc[q].fillna(0)
        fport1 = fundfundport.loc[q].fillna(0)
        tscope = fundscope.loc[q,'value'].unstack().fillna(0)
        

        stratio = pd.Series(index=foflist,dtype='float')

        for f in foflist:           
            if (f in fport0.index) & (f in fport1.index):
                d0 = fport0.loc[f] #母基金资产配置
                d1 = fport1.loc[f] #子基金明细
                d2 = fport0.reindex(d1['fcode']) #子基金资产配置
                ratio = d1['value'].values/d2['netasset'] #母基金在子基金中的占比
                newd2 = d2.mul(ratio,axis=0)
                d3 = tscope.reindex(d1['fcode']).mul(ratio,axis=0).sum()
                
                
                v1 = d0.loc['stockvalue'] + d0.loc['ctbondvalue']*0.5 #母基金权益市值
                v2 = newd2['stockvalue'].sum() + newd2['ctbondvalue'].sum()*0.5 #子基金权益市值
                v = v1+v2
                stratio.loc[f] = v/d0.loc['netasset']
                

        
        pool1 = stratio[stratio >= 0.6]
        pool2 = stratio[(stratio > 0.4)&(stratio < 0.6)]
        pool3 = stratio[stratio <= 0.4]
        pool = pd.concat([pool1,pool2,pool3])
        tDATA1.append(pool1)
        tDATA2.append(pool2)
        tDATA3.append(pool3)
        tDATA4.append(pool)
        samplenum.loc[q,'active'] = pool1.index.size
        samplenum.loc[q,'balance'] = pool2.index.size
        samplenum.loc[q,'stable'] = pool3.index.size
        

    activefof = pd.concat(tDATA1,keys=annlist[38:])
    balancefof = pd.concat(tDATA2,keys=annlist[38:])
    stablefof = pd.concat(tDATA3,keys=annlist[38:])
    totfof = pd.concat(tDATA4,keys=annlist[38:])
    
    
    # 直接输出分类标签
    def g(data):
        
        if data >= 0.6:
            d = '积极型'
        elif data <= 0.4:
            d = '稳健型'
        else:
            d = '平衡型'
        
        return d
    
    totfof = totfof.to_frame()
    totfof.columns = ['ratio']
    totfof['flag'] = totfof['ratio'].apply(g)
    
    out1 = totfof['flag'].unstack(level=0)
    out2 = activefof.groupby(level=0).median()

    
       
   
#%%
    # 基准指数
    tDATA = w.wsd("000985.CSI", "pct_chg", "20100101", "20231231", "")  # 中证全指
    aindex = pd.Series(tDATA.Data[0], index = [x.strftime('%Y%m%d') for x in tDATA.Times]) / 100
    
    tDATA = w.wsd("HSCI.HI", "pct_chg", "20100101", "20231231", "")  # 恒生综合指数
    hkindex = pd.Series(tDATA.Data[0], index = [x.strftime('%Y%m%d') for x in tDATA.Times]) / 100
    
    tDATA = w.wsd("SPX.GI", "pct_chg", "20100101", "20231231", "")  # 标普500
    usindex = pd.Series(tDATA.Data[0], index = [x.strftime('%Y%m%d') for x in tDATA.Times]) / 100
    
    
   #%% 地区配置
   
    tDATA0 = []     
    tempdtlist = [x for x in annlist if x >= '20190630']
    for q in tempdtlist:
        
        pool = totfof.loc[q].index.tolist()
        
        fport0 = fundassetport.loc[q].fillna(0)
        fport1 = fundfundport.loc[q].fillna(0)
        tscope = fundscope.loc[q,'value'].unstack().fillna(0)
        
        tDATA1 = []
        tDATA = []
        validkey = []
        for f in pool:           
            if (f in fport0.index) & (f in fport1.index):

                d0 = fport0.loc[f] #母基金资产配置
                d1 = fport1.loc[f] #子基金明细
                d2 = fport0.reindex(d1['fcode']) #子基金资产配置
                ratio = d1['value'].values/d2['netasset'] #母基金在子基金中的占比
                newd2 = d2.mul(ratio,axis=0)
                d3 = tscope.reindex(d1['fcode']).mul(ratio,axis=0).sum()
                test = tscope.reindex(d1['fcode']).mul(ratio,axis=0)
                
                
                v1 = d0.loc['stockvalue'] + d0.loc['ctbondvalue']*0.5 #母基金权益市值
                v2 = newd2['stockvalue'].sum() + newd2['ctbondvalue'].sum()*0.5 #子基金权益市值
                v = v1+v2

                                
                # 对于港股通和QDII港股重复市值的处理
                #子基金港股通市值
                if newd2['hkvalue'].sum() > 0:
                    #查找基金是否属于QDII，若为QDII，则选QDII市值，否则取港股通市值
                    temp0 = newd2[newd2['hkvalue']>0].index.tolist()
                    temp1 = [x for x in temp0 if x not in tscope.index] #不在qdii中的列表
                    hkv1 = newd2.loc[temp1,'hkvalue'].sum()  
                else:
                    hkv1 = 0
                
                #母基金港股市值
                if d0['hkvalue'].sum() > 0:
                    hkv2 = d0['hkvalue'].sum()
                else:
                    hkv2 = 0
                
                #子基金QDII港股市值
                d4 = d3.copy() 
                hkv3 = d3.loc['HKG']
                
                hkv = hkv1+hkv2+hkv3
                d4.loc['newHKG'] = hkv
                d5 = d4.drop('HKG')
                
                # 仅考虑A股市值的权益仓位修正，剔除海外配置
                d5 = d5.drop('CHN')
                v3 = d5.sum() 
                newv = v - v3 
                d5.loc['newCHN'] = newv
                d5 = d5.to_frame()
                d5['ratio'] = d5/d0.loc['netasset']
                d5.columns = ['value','ratio']
                
       
                tDATA1.append(d5)
                validkey.append(f)
                tDATA.append(test)
        
        res = pd.concat(tDATA1,keys=validkey)
        tDATA0.append(res)
        outdata = pd.concat(tDATA,keys=validkey)
    
    scoperes = pd.concat(tDATA0,keys=tempdtlist)
    scopevalue = scoperes['value'].unstack().groupby(level=0).sum().T
    scoperatio = scoperes['ratio'].unstack().groupby(level=0).median()
    
    out1 = scoperes['ratio'].groupby(level=[0,2]).median().unstack()['newCHN']
    out2 = scoperes['ratio'].unstack()['newCHN'].unstack(level=0)
    
    

    
    tdata = scoperes['ratio'].unstack()
    d1 = tdata['USA'].unstack()
    d2 = d1 - d1.shift()
    indexret = pd.Series(index=tempdtlist[1:],dtype='float')
    for q in tempdtlist[1:]:
        dtlist = [x for x in tradingdate if (x<=q) & (x>tempdtlist[tempdtlist.index(q)-1])]
        aret = (usindex.loc[dtlist]+1).product()-1
        indexret.loc[q] = aret
    
    d3 = d2.mul(indexret,axis=0)
    
    num=8
    d4 = d3.iloc[-num:].dropna(axis=1)
    temp=((d4>0).sum()/num).sort_values(ascending=False)
    
    
    
    
    
    
    #%% 主动权益基金股票持仓解析

    fundpool = fundsector[fundsector['sectorname2'].isin(['普通股票型基金','偏股混合型基金','灵活配置型基金'])]
    fundpool = fundpool.join(fundinfo)
    fundpool = fundpool[fundpool.isinitial == 1]

    
    tDATA = []
    tDATA1 = []
    tDATA2 = []
    tDATA3 = []

    for q in tempdtlist:

        pool1 = fundpool[(fundpool['entrydate'] <= q) & (fundpool['exitdate'].isnull() | (fundpool['exitdate'] >= q))]
        
        # 选择成立至少12个月的产品  
        begindt = monlist[monlist.index(q) - 12]      
        pool2 = fundpool[(fundpool.setupdate <= begindt) & ((fundpool.enddate.isnull()) | (fundpool.enddate >= q))]
        
        
        # 最新规模不低于2亿元
        temp = fundasset.loc[q,'totasset'] 
        pool3 = temp[temp >= 200000000]
        

        # 最近4个季度股票仓位不低于70%
        templist = [x for x in quarlist if x <= q]
        reportdt = templist[-4:]     
        temp = fundassetport.loc[reportdt]
        ratio = temp['stockvalue']/temp['netasset']
        ratio = ratio.unstack()
        pool4 = ratio[ratio>0.7].dropna(axis=1).T
        
                
        flist0 = [x for x in pool1.index if (x in pool2.index) & (x in pool3.index) & (x in pool4.index)]

        tport = fundstockport.loc[q]
        temp = list(set(tport.index.tolist()))
        flist = [x for x in flist0 if x in temp]
        

        tport = tport.loc[flist]
        tport = tport.reset_index(drop=True)
        temp = tport['stockcode'].apply(lambda x:x.split('.')[1])
        tport = tport[(temp=='SH')|(temp=='SZ')]
        #tport['stockcode'] = tport['stockcode'].apply(lambda x:x.split('.')[0])
        tport = tport.set_index('stockcode')['stkvalue']
        
        # 个股配置
        stvalue = tport.groupby(level=0).sum()  # 持股市值
        stnum = tport.groupby(level=0).count()  # 持基个数
        
        # 行业板块配置
        tsector = asharesector[(asharesector['entrydt'] <= q) & (asharesector['removedt'].isnull() | (asharesector['removedt'] >= q))]
        indusvalue = stvalue.to_frame().join(tsector)
        indus1 = indusvalue.groupby(['indusname']).sum()['stkvalue']
        indus2 = indusvalue.groupby(['newindus']).sum()['stkvalue']
        
        # 风格配置
        dt = [x for x in tradingdate if x <= q][-1]
        facexp,_ = get_risk_model(dt, '000000')
        wei = stvalue / stvalue.sum()
        idx = [x.split('.')[0] for x in wei.index]
        wei.index = idx
        tdata = facexp.mul(wei,axis=0)
        texp = tdata.sum()
        

       
        tDATA.append(stvalue)
        tDATA1.append(indus1)
        tDATA2.append(indus2)
        tDATA3.append(texp)


    # 风格因子暴露
    res_exp = pd.concat(tDATA3,keys=tempdtlist)
    index_exp = res_exp.unstack()
    #分位数水平res_exp.apply(lambda x:x.rank(method='max',pct=True),axis=0)
    
    # 行业板块
    res_sector = pd.concat(tDATA2,keys=tempdtlist).unstack()
    res_sector = res_sector[['周期','消费','医药','科技','新能源','军工','金融地产','综合']]
    index_sector = res_sector.div(res_sector.sum(axis=1),axis=0)    
    
    # 中信一级行业
    res_indus = pd.concat(tDATA1,keys=tempdtlist).unstack()
    index_indus = res_indus.div(res_indus.sum(axis=1),axis=0)
    
    
    #%% fof持仓股票解析
  
    tDATA1 = []
    tDATA2 = []
    tDATA3 = []

    for q in tempdtlist:

        # 基金筛选    
        foflist = stablefof.loc[q].index.tolist()
    
        fport = process_fundport(q, fundfundport, fundinfo, fundsector, foflist)
        flist0 = fport.index.tolist()

        tport = fundstockport.loc[q][['stockcode','stkvalue']]
        temp = list(set(tport.index.tolist()))
        flist = [x for x in flist0 if x in temp]
        
        tvalue = fport.loc[flist]['value']
        wei = tvalue/tvalue.sum()
        tport = tport.loc[flist]
        tport = tport.sort_values(by='stkvalue',ascending=False)
        tport = tport.join(wei)
        tport['newstkvalue'] = tport['stkvalue']*tport['value']

        
        tport = tport.reset_index(drop=True)
        temp = tport['stockcode'].apply(lambda x:x.split('.')[1])
        tport = tport[(temp=='SH')|(temp=='SZ')]
        #tport['stockcode'] = tport['stockcode'].apply(lambda x:x.split('.')[0])
        tport = tport.set_index('stockcode')['newstkvalue']
        
        stvalue = tport.groupby(level=0).sum()
        
        
        # 行业板块配置
        tsector = asharesector[(asharesector['entrydt'] <= q) & (asharesector['removedt'].isnull() | (asharesector['removedt'] >= q))]
        indusvalue = stvalue.to_frame().join(tsector)
        indus1 = indusvalue.groupby(['indusname']).sum()['newstkvalue']
        indus2 = indusvalue.groupby(['newindus']).sum()['newstkvalue']
        
        # 风格配置
        dt = [x for x in tradingdate if x <= q][-1]
        facexp,_ = get_risk_model(dt, '000000')
        wei = stvalue / stvalue.sum()
        idx = [x.split('.')[0] for x in wei.index]
        wei.index = idx
        tdata = facexp.mul(wei,axis=0)
        texp = tdata.sum()


        tDATA1.append(indus1)
        tDATA2.append(indus2)
        tDATA3.append(texp)
        
    
     
    # 风格因子暴露
    res_exp = pd.concat(tDATA3,keys=tempdtlist)
    res_exp = res_exp.unstack()
    
    # 行业板块
    res_sector = pd.concat(tDATA2,keys=tempdtlist).unstack()
    res_sector = res_sector[['周期','消费','医药','科技','新能源','军工','金融地产','综合']]
    ratio_sector = res_sector.div(res_sector.sum(axis=1),axis=0)    
    
    # 中信一级行业
    res_indus = pd.concat(tDATA1,keys=tempdtlist).unstack()
    ratio_indus = res_indus.div(res_indus.sum(axis=1),axis=0)
    
    active_exp = res_exp - index_exp
    active_exp_mean = active_exp.mean()
    
        
    #%%
    # 提取风险模型数据，因子收益率从20070105开始    
    facret_list = []
    tempdt = [x for x in tradingdate if x >= '20070105']
    for dt in tempdt:
        _,tdata = get_risk_model(dt, '000000') 
        facret_list.append(tdata)
        
    facret = pd.concat(facret_list, keys = tempdt)
    facret = facret.unstack()
    indusret = facret.iloc[:,11:]
    styleret = facret.iloc[:,1:11]
    
    
    

    tDATA1 = []
    tDATA2 = []
    for q in tempdtlist:
        
        texp = active_exp.loc[q]
        
        dtlist = [x for x in tradingdate if (x > monlist[monlist.index(q)-2]) & (x <= monlist[monlist.index(q)+2])]        
        tret1 = (styleret.loc[dtlist]+1).product()-1       
        out1 = texp.iloc[1:11] * tret1
        tDATA1.append(out1)
        
        tret2 = (indusret.loc[dtlist]+1).product()-1       
        out2 = texp.iloc[11:] * tret2
        tDATA2.append(out2)
    
    stylecon = pd.concat(tDATA1,keys=tempdtlist)
    stylecon = stylecon.unstack()

    induscon = pd.concat(tDATA2,keys=tempdtlist)
    induscon = induscon.unstack()        
    induscon_mean = 12*induscon.mean()/4
    
    
    
    #%% fof持仓股票解析
  
    tDATA0 = []

    for q in tempdtlist:
        
        
        foflist = totfof.loc[q].index.tolist()
        
        dtlist = [x for x in tradingdate if (x > monlist[monlist.index(q)-2]) & (x <= monlist[monlist.index(q)+2])]        
        tret1 = (styleret.loc[dtlist]+1).product()-1            
        tret2 = (indusret.loc[dtlist]+1).product()-1 
        
        tDATA = []
        for f in foflist:
            
            print(q,f)
    
            fport = process_fundport(q, fundfundport, fundinfo, fundsector, [f])
            flist0 = fport.index.tolist()
    
            tport = fundstockport.loc[q][['stockcode','stkvalue']]
            temp = list(set(tport.index.tolist()))
            flist = [x for x in flist0 if x in temp]
            
            tvalue = fport.loc[flist]['value']
            wei = tvalue/tvalue.sum()
            tport = tport.loc[flist]
            tport = tport.sort_values(by='stkvalue',ascending=False)
            tport = tport.join(wei)
            tport['newstkvalue'] = tport['stkvalue']*tport['value']
    
            
            tport = tport.reset_index(drop=True)
            temp = tport['stockcode'].apply(lambda x:x.split('.')[1])
            tport = tport[(temp=='SH')|(temp=='SZ')]
            #tport['stockcode'] = tport['stockcode'].apply(lambda x:x.split('.')[0])
            tport = tport.set_index('stockcode')['newstkvalue']
            
            stvalue = tport.groupby(level=0).sum()
            
            
            # 行业板块配置
            tsector = asharesector[(asharesector['entrydt'] <= q) & (asharesector['removedt'].isnull() | (asharesector['removedt'] >= q))]
            indusvalue = stvalue.to_frame().join(tsector)
            indus1 = indusvalue.groupby(['indusname']).sum()['newstkvalue']
            indus2 = indusvalue.groupby(['newindus']).sum()['newstkvalue']
            
            # 风格配置
            dt = [x for x in tradingdate if x <= q][-1]
            facexp,_ = get_risk_model(dt, '000000')
            wei = stvalue / stvalue.sum()
            idx = [x.split('.')[0] for x in wei.index]
            wei.index = idx
            tdata = facexp.mul(wei,axis=0)
            texp = tdata.sum()           
            aexp = texp - index_exp.loc[q]
            
            
            # 收益贡献     
            con1 = aexp.iloc[1:11] * tret1
            con2 = aexp.iloc[11:] * tret2
            con = pd.concat([con1,con2])
            
            out = pd.concat([aexp,con],axis=1)
            out.columns = ['exp','retcon']
            out = out.iloc[1:]
            
            tDATA.append(out)
            
        
        res = pd.concat(tDATA,keys=foflist)
        tDATA0.append(res)
    
    
    result = pd.concat(tDATA0,keys=tempdtlist)



    

    
    
    
    
    
    
    
    
    
    