#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WindPy连接测试脚本
用于诊断Wind数据库连接问题
"""

import sys

def test_windpy_connection():
    """测试WindPy连接"""
    print("=" * 50)
    print("WindPy连接诊断测试")
    print("=" * 50)
    
    # 步骤1：测试WindPy导入
    print("步骤1: 测试WindPy模块导入...")
    try:
        from WindPy import w
        print("✓ WindPy模块导入成功")
    except ImportError as e:
        print(f"✗ WindPy模块导入失败: {e}")
        print("解决方案:")
        print("1. 从万得官网下载WindPy: https://www.wind.com.cn/")
        print("2. 安装WindPy到当前Python环境")
        return False
    except Exception as e:
        print(f"✗ WindPy导入异常: {e}")
        return False
    
    # 步骤2：检查w对象属性
    print("\n步骤2: 检查WindPy对象属性...")
    try:
        print(f"w对象类型: {type(w)}")
        print(f"w对象可用方法: {[attr for attr in dir(w) if not attr.startswith('_')][:10]}...")
        
        # 检查关键方法是否存在
        if hasattr(w, 'start'):
            print("✓ w.start() 方法存在")
        else:
            print("✗ w.start() 方法不存在")
            print("可能的方法:", [attr for attr in dir(w) if 'start' in attr.lower()])
            
    except Exception as e:
        print(f"✗ 检查w对象时出错: {e}")
        return False
    
    # 步骤3：尝试启动连接
    print("\n步骤3: 尝试启动Wind连接...")
    try:
        if hasattr(w, 'start'):
            result = w.start()
            print(f"w.start() 返回值: {result}")
            if hasattr(result, 'ErrorCode'):
                if result.ErrorCode == 0:
                    print("✓ Wind数据库连接成功")
                else:
                    print(f"✗ Wind连接失败，错误代码: {result.ErrorCode}")
                    print(f"错误信息: {getattr(result, 'Data', 'N/A')}")
            else:
                print("✓ Wind连接命令执行完成（无错误代码返回）")
        else:
            print("✗ 无法找到启动方法")
            return False
            
    except Exception as e:
        print(f"✗ 启动Wind连接时出错: {e}")
        print("可能的原因:")
        print("1. Wind终端未启动")
        print("2. Wind账户未登录")
        print("3. 网络连接问题")
        print("4. WindPy版本不兼容")
        return False
    
    # 步骤4：测试简单数据获取
    print("\n步骤4: 测试简单数据获取...")
    try:
        # 尝试获取一个简单的数据
        test_data = w.wsd("000001.SZ", "close", "2025-07-30", "2025-07-31", "")
        if hasattr(test_data, 'ErrorCode'):
            if test_data.ErrorCode == 0:
                print("✓ 数据获取测试成功")
                print(f"测试数据: {test_data.Data[0] if test_data.Data else 'N/A'}")
            else:
                print(f"✗ 数据获取失败，错误代码: {test_data.ErrorCode}")
                print(f"错误信息: {test_data.Data}")
        else:
            print("✓ 数据获取命令执行完成")
            
    except Exception as e:
        print(f"✗ 数据获取测试失败: {e}")
        return False
    
    # 步骤5：关闭连接
    print("\n步骤5: 关闭Wind连接...")
    try:
        if hasattr(w, 'stop'):
            w.stop()
            print("✓ Wind连接已关闭")
        else:
            print("⚠ 未找到关闭连接的方法")
    except Exception as e:
        print(f"⚠ 关闭连接时出错: {e}")
    
    print("\n" + "=" * 50)
    print("✓ WindPy连接测试完成")
    print("=" * 50)
    return True

def print_troubleshooting_guide():
    """打印故障排除指南"""
    print("\n🔧 常见问题解决方案:")
    print("-" * 30)
    print("1. WindPy模块导入失败:")
    print("   - 下载地址: https://www.wind.com.cn/")
    print("   - 确保安装到正确的Python环境")
    print("   - 尝试: pip uninstall WindPy && 重新安装")
    
    print("\n2. Wind连接失败:")
    print("   - 确保Wind终端已启动并登录")
    print("   - 检查网络连接")
    print("   - 确认Wind账户权限")
    print("   - 尝试重启Wind终端")
    
    print("\n3. 数据获取失败:")
    print("   - 检查股票代码格式")
    print("   - 确认日期范围有效")
    print("   - 验证数据权限")
    
    print("\n4. WindPy版本问题:")
    print("   - 检查WindPy版本与Wind终端版本匹配")
    print("   - 尝试更新到最新版本")

if __name__ == "__main__":
    success = test_windpy_connection()
    
    if not success:
        print_troubleshooting_guide()
        sys.exit(1)
    else:
        print("\n🎉 WindPy连接正常，可以运行主分析程序！")
