#%%
import ssl
ssl._create_default_https_context = ssl._create_unverified_context

from Basicfun import *
print(os.path.basename(__file__))

#%%
from fredapi import Fred
api_key = '0af49173e74acdfbc4a5b1ad1a5239a7'   
fred = Fred(api_key)

#%%1流动性
os.makedirs(os.path.join(fp, 'Result', '1.19Oversea'), exist_ok=True)
os.makedirs(os.path.join(fp, 'Result', '0.0WFR'), exist_ok=True)
# writer_liquidity = pd.ExcelWriter(os.path.join(fp,'Result','1.19Oversea','1.19.1.0美元流动性跟踪.xlsx'))

#%%1.1美联储资产端
indicators_asset = ['WLCFLL', 'H41RESPPALDKNWW', 'RPONTTLD', 'SWPT', 'H41RESPPALGTRFNWW']
indicator_name_asset= ['贴现窗口工具','银行定期融资计划','常备回购便利工具','央行货币互换','对海外国际货币当局的回购便利工具']
limits = [1500, 20, None, 1500, 200]
units = ['百万美金', '百万美金', '十亿美金', '百万美金', '百万美金']
for i in range(len(indicators_asset)):
    data_asset = fred.get_series(indicators_asset[i])
    df_asset = pd.DataFrame({'日期': data_asset.index, indicator_name_asset[i]: data_asset.values})
    # df_asset.to_excel(writer_liquidity, indicator_name_asset[i])
    if limits[i] is not None:
        df_asset = df_asset[-limits[i]:]
    else:
        df_asset = df_asset
    fig_asset, ax_asset = plt.subplots(nrows=1, ncols=1)
    ax_asset.plot(df_asset['日期'], df_asset[indicator_name_asset[i]], label=f'{indicator_name_asset[i]} ({units[i]})', color='red')
    ax_asset.text(list(df_asset['日期'])[-1], df_asset[indicator_name_asset[i]].iloc[-1], round(df_asset[indicator_name_asset[i]].iloc[-1], 1), color='red', fontsize=fontsize_legend)
    ax_asset.legend(loc=1, fontsize=fontsize_legend)
    ax_asset.spines['top'].set_visible(False)
    ax_asset.spines['right'].set_visible(False)
    ax_asset.tick_params(axis='y', direction='out')
    ax_asset.set_xlim(df_asset['日期'].iloc[0], df_asset['日期'].iloc[-1])
    fig_asset.suptitle(f'美联储资产端-{indicator_name_asset[i]}' + '(' + enddate + ')', fontsize=fontsize_suptitle, fontweight='bold')
    fig_asset.tight_layout()
    fig_asset.savefig(os.path.join(fp, 'Result', '1.19Oversea', f'1.19.1.1.{i+1}流动性-美联储资产端-{indicator_name_asset[i]}.png'))
    plt.close()

#%%1.2美联储负债端
indicators_liability = ['WRBWFRBL', 'WDTGAL', 'RRPONTSYD', 'WLFN']
indicator_name_liability= ['准备金','财政存款','隔夜逆回购','联邦储备票据']
for i in range(len(indicators_liability)):
    data_liability = fred.get_series(indicators_liability[i])
    df_liability = pd.DataFrame({'日期': data_liability.index, indicator_name_liability[i]: data_liability.values})
    # df_liability.to_excel(writer_liquidity, indicator_name_liability[i])
    df_liability = df_liability[-1500:]
    fig_liability, ax_liability = plt.subplots(nrows=1, ncols=1)
    ax_liability.fill_between(list(df_liability['日期']), df_liability[indicator_name_liability[i]], label=f'{indicator_name_liability[i]}（百万美金）' if i < 2 else f'{indicator_name_liability[i]}（十亿美金）', color='red', alpha=0.5)
    ax_liability.text(list(df_liability['日期'])[-1], df_liability[indicator_name_liability[i]].iloc[-1], round(df_liability[indicator_name_liability[i]].iloc[-1], 1), color='red', fontsize=fontsize_legend)
    ax_liability.legend(loc=1, fontsize=fontsize_legend)
    ax_liability.spines['top'].set_visible(False)
    ax_liability.spines['right'].set_visible(False)
    ax_liability.tick_params(axis='y', direction='out')
    ax_liability.set_xlim(df_liability['日期'].iloc[0], df_liability['日期'].iloc[-1])
    fig_liability.suptitle(f'美联储负债端-{indicator_name_liability[i]}' + '(' + enddate + ')', fontsize=fontsize_suptitle, fontweight='bold')
    fig_liability.tight_layout()
    fig_liability.savefig(os.path.join(fp, 'Result', '1.19Oversea', f'1.19.1.2.{i+1}流动性-美联储负债端-{indicator_name_liability[i]}.png'))
    plt.close()

#%%1.3"准央行"及银行流动性指标
#银行准备金存款占总资产比率
data_asset = fred.get_series('TLAACBW027SBOG')
data_cash = fred.get_series('CASACBW027SBOG')
data_asset_small = fred.get_series('TLASCBW027SBOG')
data_cash_small = fred.get_series('CASSCBW027SBOG')
data_asset_big = fred.get_series('TLALCBW027SBOG')
data_cash_big = fred.get_series('CASLCBW027SBOG')
df_percent = pd.DataFrame({'日期':data_asset.index,'总资产':data_asset.values,'准备金存款':data_cash.values})
df_percent['银行准备金存款占总资产比率'] = df_percent['准备金存款'] /df_percent['总资产']
df_percent = df_percent.iloc[-1500:,:]
df_percent['小型商业银行'] = data_cash_small.values[-1500:]/data_asset_small.values[-1500:]
df_percent['大型商业银行'] = data_cash_big.values[-1500:]/data_asset_big.values[-1500:]
# df_percent.to_excel(writer_liquidity,'银行准备金存款占总资产比率')
#联邦住房贷款银行贷款
data = fred.get_series('QBPBSTLKFHLB')
df_fhlb = pd.DataFrame({'日期':data.index,'联邦住房贷款银行贷款':data.values})
# df_fhlb.to_excel(writer_liquidity,'联邦住房贷款银行贷款')

#%%1.4利率以及利差指标
#有效联邦基金利率及交易量
data = fred.get_series('DFF')
df_dff = pd.DataFrame({'日期':data.index,'有效联邦基金利率':data.values})
data_1 = fred.get_series('EFFRVOL')
df_dff_1 = pd.DataFrame({'日期':data_1.index,'成交量':data_1.values})
df_dff_all = pd.merge(df_dff,df_dff_1,how='outer',left_on = '日期',right_on='日期')
# df_dff_all.to_excel(writer_liquidity,'有效联邦基金利率及交易量')
#99th EFFR - EFFR
data = fred.get_series('EFFR99')
df_EFFR99= pd.DataFrame({'日期':data.index,'99分位':data.values})
df_dff_final = pd.merge(df_dff,df_EFFR99,how='outer',left_on = '日期',right_on='日期')
df_dff_final = df_dff_final.iloc[-1800:,].dropna()
df_dff_final['分位差'] = df_dff_final['99分位'] - df_dff_final['有效联邦基金利率']
# df_dff_final.to_excel(writer_liquidity,'联邦基金利率99分位')
#隔夜银行融资利率
data = fred.get_series('OBFR')
df_obfr= pd.DataFrame({'日期':data.index,'隔夜银行融资利率':data.values})
data_1 = fred.get_series('OBFRVOL')
df_obfr['成交量'] = data_1.values
# df_obfr.to_excel(writer_liquidity,'隔夜银行融资利率')
#广义一般抵押品融资回购利率
data = fred.get_series('SOFR')
df_sofr= pd.DataFrame({'日期':data.index,'广义一般抵押品融资回购利率':data.values})
data_1 = fred.get_series('SOFRVOL')
df_sofr['成交量'] = data_1.values
# df_sofr.to_excel(writer_liquidity,'广义一般抵押品融资回购利率')

# excel_format(writer_liquidity)

t=printtime(t)

#%%1.5"准央行"及银行流动性指标
df_percent_1 = df_percent[-1000:]
fig_asset,ax_asset = plt.subplots(nrows=1,ncols=1)
ax_asset.plot(df_percent_1['日期'],df_percent_1['银行准备金存款占总资产比率'],label='银行准备金存款占总资产比率',color='red')
ax_asset.text(list(df_percent_1['日期'])[-1],df_percent_1['银行准备金存款占总资产比率'].iloc[-1],round(df_percent_1['银行准备金存款占总资产比率'].iloc[-1],3),color='red',fontsize=fontsize_legend)
ax_asset.plot(df_percent_1['日期'],df_percent_1['大型商业银行'],label='大型商业银行',color='blue')
ax_asset.text(list(df_percent_1['日期'])[-1],df_percent_1['大型商业银行'].iloc[-1],round(df_percent_1['大型商业银行'].iloc[-1],3),color='blue',fontsize=fontsize_legend)
ax_asset.plot(df_percent_1['日期'],df_percent_1['小型商业银行'],label='小型商业银行',color='green')
ax_asset.text(list(df_percent_1['日期'])[-1],df_percent_1['小型商业银行'].iloc[-1],round(df_percent_1['小型商业银行'].iloc[-1],3),color='green',fontsize=fontsize_legend)
ax_asset.legend(loc=1,fontsize=fontsize_legend)
ax_asset.spines['top'].set_visible(False)
ax_asset.spines['right'].set_visible(False)
ax_asset.tick_params(axis='y', direction='out')
ax_asset.set_xlim(df_percent_1['日期'].iloc[0], df_percent_1['日期'].iloc[-1])
fig_asset.suptitle('"准央行"及银行流动性指标-银行准备金存款占总资产比率'+'('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_asset.tight_layout()
fig_asset.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.1.3.1流动性-"准央行"及银行流动性指标-银行准备金存款占总资产比率.png'))
plt.close()

df_fhlb_1 = df_fhlb[-100:]
fig_asset,ax_asset = plt.subplots(nrows=1,ncols=1)
ax_asset.plot(df_fhlb_1['日期'],df_fhlb_1['联邦住房贷款银行贷款'],label='联邦住房贷款银行贷款',color='red')
ax_asset.text(list(df_fhlb_1['日期'])[-1],df_fhlb_1['联邦住房贷款银行贷款'].iloc[-1],round(df_fhlb_1['联邦住房贷款银行贷款'].iloc[-1],1),color='red',fontsize=fontsize_legend)
ax_asset.legend(loc=1,fontsize=fontsize_legend)
ax_asset.spines['top'].set_visible(False)
ax_asset.spines['right'].set_visible(False)
ax_asset.tick_params(axis='y', direction='out')
ax_asset.set_xlim(df_fhlb_1['日期'].iloc[0], df_fhlb_1['日期'].iloc[-1])
fig_asset.suptitle('"准央行"及银行流动性指标-联邦住房贷款银行贷款'+'('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_asset.tight_layout()
fig_asset.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.1.3.2流动性-"准央行"及银行流动性指标-联邦住房贷款银行贷款.png'))
plt.close()

t=printtime(t)

#%%1.6利率及利差指标
df_EFFR99_1 = df_dff_final[-1500:]
fig_asset,ax_asset = plt.subplots(nrows=1,ncols=1)
ax_asset.plot(df_dff_final['日期'],df_dff_final['分位差'],label='联邦基金利率99分位 - 联邦基金利率（%）',color='red')
ax_asset.text(list(df_dff_final['日期'])[-1],df_dff_final['分位差'].iloc[-1],round(df_dff_final['分位差'].iloc[-1],2),color='red',fontsize=fontsize_legend)
ax_asset.legend(loc=1,fontsize=fontsize_legend)
ax_asset.spines['top'].set_visible(False)
ax_asset.spines['right'].set_visible(False)
ax_asset.tick_params(axis='y', direction='out')
ax_asset.set_xlim(df_EFFR99_1['日期'].iloc[0], df_EFFR99_1['日期'].iloc[-1])
ax_asset.set_ylim(0, 1)
fig_asset.suptitle('利率及利差指标-联邦基金利率99分位-联邦基金利率'+'('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_asset.tight_layout()
fig_asset.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.1.4.1流动性-利率及利差指标-联邦基金利率99分位.png'))
plt.close()

df_obfr_1 = df_obfr[-1500:]
fig_asset,ax_asset = plt.subplots(nrows=1,ncols=1)
ax_asset1=ax_asset.twinx()
ax_asset.plot(df_obfr_1['日期'],df_obfr_1['隔夜银行融资利率'],label='隔夜银行融资利率',color='red')
ax_asset.text(list(df_obfr_1['日期'])[-1],df_obfr_1['隔夜银行融资利率'].iloc[-1],round(df_obfr_1['隔夜银行融资利率'].iloc[-1],2),color='red',fontsize=fontsize_legend)
ax_asset1.plot(df_obfr_1['日期'],df_obfr_1['成交量'],label='成交量',color='blue',alpha=0.2)
ax_asset1.text(list(df_obfr_1['日期'])[-1],df_obfr_1['成交量'].iloc[-1],round(df_obfr_1['成交量'].iloc[-1],1),color='blue',fontsize=fontsize_legend)
df_obfr_2 = df_obfr_1['成交量'].rolling(5).mean()
df_obfr_3= pd.DataFrame({'日期':df_obfr_1['日期'],'成交量MA5':df_obfr_2}).dropna()
ax_asset1.plot(df_obfr_3['日期'],df_obfr_3['成交量MA5'],label='成交量MA5',color='blue')
ax_asset.legend(loc=2,fontsize=fontsize_legend)
ax_asset1.legend(loc=1,fontsize=fontsize_legend)
ax_asset.spines['top'].set_visible(False)
ax_asset1.spines['top'].set_visible(False)
ax_asset.tick_params(axis='y', direction='out')
ax_asset.spines['left'].set_color('red')
ax_asset.spines['right'].set_color('blue')
ax_asset1.spines['left'].set_color('red')
ax_asset1.spines['right'].set_color('blue')
ax_asset.set_xlim(df_obfr_1['日期'].iloc[0],df_obfr_1['日期'].iloc[-1])
fig_asset.suptitle('利率及利差指标-隔夜银行融资利率及成交量'+'('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_asset.tight_layout()
fig_asset.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.1.4.2流动性-利率及利差指标-隔夜银行融资利率及成交量.png'))
plt.close()

df_sofr_1 = df_sofr[-1500:]
fig_asset,ax_asset = plt.subplots(nrows=1,ncols=1)
ax_asset1=ax_asset.twinx()
ax_asset.plot(df_sofr_1['日期'],df_sofr_1['广义一般抵押品融资回购利率'],label='有担保隔夜融资利率',color='red')
ax_asset.text(list(df_sofr_1['日期'])[-1],df_sofr_1['广义一般抵押品融资回购利率'].iloc[-1],round(df_sofr_1['广义一般抵押品融资回购利率'].iloc[-1],2),color='red',fontsize=fontsize_legend)
ax_asset1.plot(df_sofr_1['日期'],df_sofr_1['成交量'],label='成交量',color='blue',alpha=0.2)
ax_asset1.text(list(df_sofr_1['日期'])[-1],df_sofr_1['成交量'].iloc[-1],round(df_sofr_1['成交量'].iloc[-1],1),color='blue',fontsize=fontsize_legend)
df_sofr_2 = df_sofr_1['成交量'].rolling(5).mean()
df_sofr_3= pd.DataFrame({'日期':df_sofr_1['日期'],'成交量MA5':df_sofr_2}).dropna()
ax_asset1.plot(df_sofr_3['日期'],df_sofr_3['成交量MA5'],label='成交量MA5',color='blue')
ax_asset.legend(loc=2,fontsize=fontsize_legend)
ax_asset1.legend(loc=1,fontsize=fontsize_legend)
ax_asset.spines['top'].set_visible(False)
ax_asset1.spines['top'].set_visible(False)
ax_asset.spines['left'].set_color('red')
ax_asset.spines['right'].set_color('blue')
ax_asset1.spines['left'].set_color('red')
ax_asset1.spines['right'].set_color('blue')
ax_asset.tick_params(axis='y', direction='out')
ax_asset.set_xlim(df_sofr_1['日期'].iloc[0],df_sofr_1['日期'].iloc[-1])
fig_asset.suptitle('利率及利差指标-有担保隔夜融资利率及成交量'+'('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_asset.tight_layout()
fig_asset.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.1.4.3流动性-利率及利差指标-有担保隔夜融资利率及成交量.png'))
plt.close()

df_dff_all_1 = df_dff_all[-1500:]
fig_asset,ax_asset = plt.subplots(nrows=1,ncols=1)
ax_asset1=ax_asset.twinx()
ax_asset.plot(df_dff_all_1['日期'],df_dff_all_1['有效联邦基金利率'],label='联邦基金利率',color='red')
ax_asset.text(list(df_dff_all_1['日期'])[-1],df_dff_all_1['有效联邦基金利率'].iloc[-1],round(df_dff_all_1['有效联邦基金利率'].iloc[-1],2),color='red',fontsize=fontsize_legend)
ax_asset1.plot(df_dff_all_1['日期'],df_dff_all_1['成交量'],label='成交量',color='blue',alpha=0.2)
ax_asset1.text(list(df_dff_all_1['日期'])[-1],df_dff_all_1['成交量'].iloc[-1],round(df_dff_all_1['成交量'].iloc[-1],1),color='blue',fontsize=fontsize_legend)
df_dff_all_2 = df_dff_all_1['成交量'].rolling(5).mean()
df_dff_all_3= pd.DataFrame({'日期':df_dff_all_1['日期'],'成交量MA5':df_dff_all_2}).dropna()
ax_asset1.plot(df_dff_all_3['日期'],df_dff_all_3['成交量MA5'],label='成交量MA5',color='blue')
ax_asset.legend(loc=2,fontsize=fontsize_legend)
ax_asset1.legend(loc=1,fontsize=fontsize_legend)
ax_asset.spines['top'].set_visible(False)
ax_asset1.spines['top'].set_visible(False)
ax_asset.tick_params(axis='y', direction='out')
ax_asset.spines['left'].set_color('red')
ax_asset.spines['right'].set_color('blue')
ax_asset1.spines['left'].set_color('red')
ax_asset1.spines['right'].set_color('blue')
ax_asset1.set_xlim(df_dff_all_1['日期'].iloc[0],df_dff_all_1['日期'].iloc[-1])
fig_asset.suptitle('利率及利差指标-联邦基金利率及成交量'+'('+enddate+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_asset.tight_layout()
fig_asset.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.1.4.4流动性-利率及利差指标-联邦基金利率及成交量.png'))
plt.close()

t=printtime(t)

print('流动性数据完成!')

#%%2通胀
#%%2.1.1CPI及核心CPI同比和环比
df_data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='2.1.1CPI')
us_cpi = w.edb(df_data.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_cpi_df = pd.DataFrame(us_cpi.Data,index=df_data.iloc[:,0].tolist(),columns=us_cpi.Times).T
fig_uscpi,ax_uscpi=plt.subplots(nrows=2,ncols=1,sharex=False,figsize=(20,9),dpi=400)
ax_uscpi1=ax_uscpi[0].twinx()
axis_uscpi=[ax_uscpi[0],ax_uscpi1]
axis_uscpi[0].bar(us_cpi_df.index,us_cpi_df['CPI季调环比'],color='blue',width=3,label = 'CPI环比')
axis_uscpi[0].text(us_cpi_df.index[-1],us_cpi_df['CPI季调环比'][-1],us_cpi_df['CPI季调环比'][-1],color='blue',alpha=0.7,fontsize=10)
axis_uscpi[0].legend(loc=2,fontsize=fontsize_legend)
axis_uscpi[1].plot(us_cpi_df.index,us_cpi_df['CPI同比'],color='red',alpha=0.7,label='CPI同比')
axis_uscpi[1].text(us_cpi_df.index[-1],us_cpi_df['CPI同比'][-1],us_cpi_df['CPI同比'][-1],color='red',alpha=0.7,fontsize=10)
axis_uscpi[1].legend(loc=1,fontsize=fontsize_legend)
axis_uscpi[0].spines['top'].set_visible(False)
axis_uscpi[1].spines['right'].set_color('red')
axis_uscpi[1].spines['left'].set_color('blue')
axis_uscpi[1].tick_params(axis='x', direction='in', rotation=25)
axis_uscpi[1].set_title('美国CPI'+'('+us_cpi_df['CPI同比'].index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_uscpi[1].spines['top'].set_visible(False)
axis_uscpi[0].set_xlim(us_cpi_df.index[0],us_cpi_df.index[-1])
ax_uscpi1=ax_uscpi[1].twinx()
axis_uscpi=[ax_uscpi[1],ax_uscpi1]
axis_uscpi[0].bar(us_cpi_df.index,us_cpi_df['核心CPI季调环比'],color='blue',width=3,label = '核心CPI环比')
axis_uscpi[0].text(us_cpi_df.index[-1],us_cpi_df['核心CPI季调环比'][-1],us_cpi_df['核心CPI季调环比'][-1],color='blue',alpha=0.7,fontsize=10)
axis_uscpi[0].legend(loc=2,fontsize=fontsize_legend)
axis_uscpi[1].plot(us_cpi_df.index,us_cpi_df['核心CPI同比'],color='red',alpha=0.7,label='核心CPI同比')
axis_uscpi[1].text(us_cpi_df.index[-1],us_cpi_df['核心CPI同比'][-1],us_cpi_df['核心CPI同比'][-1],color='red',alpha=0.7,fontsize=10)
axis_uscpi[1].legend(loc=1,fontsize=fontsize_legend)
axis_uscpi[0].spines['top'].set_visible(False)
axis_uscpi[1].spines['right'].set_color('red')
axis_uscpi[1].spines['left'].set_color('blue')
axis_uscpi[1].tick_params(axis='x', direction='in', rotation=25)
axis_uscpi[1].set_title('美国核心CPI'+'('+us_cpi_df['CPI同比'].index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_uscpi[1].spines['top'].set_visible(False)
axis_uscpi[0].set_xlim(us_cpi_df.index[0],us_cpi_df.index[-1])
axis_uscpi[1].set_xlim(us_cpi_df.index[0],us_cpi_df.index[-1])
fig_uscpi.tight_layout()
fig_uscpi.savefig(os.path.join(fp,'Result','1.19Oversea','********.1CPI_同比和环比时间序列.png'))
plt.close()

t=printtime(t)

#%%2.1.2核心cpi3个月/6个月环比年化/核心cpi
df_3 = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),header=0,sheet_name="2.1.3CPI3个月年化静态数据")
df_6 = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),header=0,sheet_name="2.1.5CPI6个月年化静态数据")
df_3.drop(df_3.columns[[0,2]], axis=1, inplace=True)
df_3 = df_3.T
col_name = df_3.iloc[0]
col_name = col_name.tolist()
df_3.drop(df_3.index[0], inplace=True)
df_3.columns = col_name
df_3 = df_3.sort_index(ascending=True)
df_3 = df_3.tail(60)         #近五年
df_6.drop(df_6.columns[[0,2]], axis=1, inplace=True)
df_6 = df_6.T
col_name = df_6.iloc[0]
col_name = col_name.tolist()
df_6.drop(df_6.index[0], inplace=True)
df_6.columns = col_name
df_6 = df_6.sort_index(ascending=True)
df_6 = df_6.tail(60)         #近五年
start_date = df_6.index[0].strftime(datestyle)    #开始日期
end_date = df_6.index[-1].strftime(datestyle)   #结束日期
corecpi = w.edb(df_data.index[df_data.iloc[:,0] == "核心CPI同比"].tolist(), start_date,end_date,"Fill=Previous")
df_corecpi = pd.DataFrame(corecpi.Data,index=["核心CPI同比"],columns=corecpi.Times).T

fig_cpi,ax_cpi=plt.subplots(nrows=1,ncols=1)
ax_cpi.plot(df_3.index,df_3["All Items Less Food And Energy"],label='核心CPI近3个月环比年化',color='red')
ax_cpi.text(df_3['All Items Less Food And Energy'].index[-1],df_3['All Items Less Food And Energy'][-1],round(df_3['All Items Less Food And Energy'][-1],1),color='red',fontsize=fontsize_legend)
ax_cpi.plot(df_6.index,df_6["All Items Less Food And Energy"],label='核心CPI近6个月环比年化',color='blue')
ax_cpi.text(df_6['All Items Less Food And Energy'].index[-1],df_6['All Items Less Food And Energy'][-1],round(df_6['All Items Less Food And Energy'][-1],1),color='blue',fontsize=fontsize_legend)
ax_cpi.plot(df_corecpi.index,df_corecpi["核心CPI同比"],label='核心CPI同比',color='orange')
ax_cpi.text(df_corecpi['核心CPI同比'].index[-1],df_corecpi['核心CPI同比'][-1],round(df_corecpi['核心CPI同比'][-1],1),color='orange',fontsize=fontsize_legend)
ax_cpi.spines['top'].set_visible(False)
ax_cpi.spines['right'].set_visible(False)
ax_cpi.legend(loc=2,fontsize=fontsize_legend)
ax_cpi.set_xlim(df_corecpi['核心CPI同比'].index[0],df_corecpi['核心CPI同比'].index[-1])
ax_cpi.set_ylim(-2,8)
fig_cpi.suptitle('美国核心CPI'+'('+df_corecpi.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_cpi.tight_layout()
fig_cpi.savefig(os.path.join(fp,'Result','1.19Oversea','********.2CPI_具体核心cpi时间序列.png'))
plt.close()

t=printtime(t)

#%%2.1.3CPI同比分项
#定义CPI细分项的series ID  （无季调）
series_ids = [
    'CPIAUCNS',  # 总体CPI
    'CPIUFDNS',  # 食品CPI
    'CUUR0000SAF11',  # Food at Home
    'CUUR0000SEFV',  # Food Away from Home
    'CPIENGNS',  # 能源CPI
    'CUUR0000SACE',  # Energy Commodities
    'CUUR0000SEHF',  # Energy Services
    'CPILFENS',  # 核心CPI（不包含食品和能源）
    'CUUR0000SACL1E',  # Commodities Less Food and Energy Commodities
    'CUUR0000SETA01',  # New Vehicles
    'CPIAPPNS',  # Apparel
    'CUUR0000SETA02',  # Used cars and trucks
    'CUUR0000SASLE',  # Services Less Energy Services
    'CUUR0000SAH1',  # Shelter
    'CUUR0000SEHA',  # Rent of Primary Residence
    'CUUR0000SEHC',  # Owners' Equivalent Rent of Residences
    'CUUR0000SAM2',  # Medical Care Services
    'CUUR0000SAS4',  # Transportation Services
]
name_list =  ['CPI','食品CPI','家用食品','非家内食品','能源CPI','能源商品', '能源服务','核心CPI','核心商品','新车','服装','二手车', '核心服务','居住','租金','OER','医疗','运输服务']

# 获取数据
data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)
    # 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_20 = df.tail(20)
df = df_20
df = df.pct_change(periods=12)*100
df = df.applymap(lambda x: round(x, 2))   #近三个月各项同比
name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'red', 'orange', 'orange', 'red', 'orange', 'orange', 'blue', 'red', 'orange', 'orange',
                   'orange', 'red', 'orange', 'green', 'green', 'orange', 'orange']
weights = ['(100%)', '(13%)', '(8%)', '(5%)', '(7%)', '(4%)', '(3%)', '(80%)', '(19%)', '(4%)', '(3%)', '(2%)', '(61%)',
               '(36%)', '(8%)', '(27%)', '(7%)', '(6%)']
label_name = []
for i in range(len(df.columns)):
    label_name.append(df.columns[i] + '\n' + weights[i])
name_cpi = df.columns.tolist()
subtitle_cpi = '↑持续上行：' + '、'.join(
    df.iloc[-1, :][(df.iloc[-1, :] > df.iloc[-2, :]) & (df.iloc[-2, :] > df.iloc[-3, :])].index.tolist()) + \
                   '\n↓持续下行：' + '、'.join(
    df.iloc[-1, :][(df.iloc[-1, :] < df.iloc[-2, :]) & (df.iloc[-2, :] < df.iloc[-3, :])].index.tolist()) + \
                   '\n↗边际上行：' + '、'.join(
    df.iloc[-1, :][(df.iloc[-1, :] > df.iloc[-2, :]) & (df.iloc[-2, :] <= df.iloc[-3, :])].index.tolist()) + \
                   '\n↘边际下行：' + '、'.join(
    df.iloc[-1, :][(df.iloc[-1, :] < df.iloc[-2, :]) & (df.iloc[-2, :] >= df.iloc[-3, :])].index.tolist())

dim_cpi = len(name_cpi)
radians_cpi = np.linspace(0, 2 * np.pi, dim_cpi, endpoint=False)
radians_cpi = np.concatenate((radians_cpi, [radians_cpi[0]]))

fig_cpi, ax_cpi = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = df.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_cpi.plot(radians_cpi, temp, color=name_color[j - 1], label=df.index[-j].strftime(datestyle1))
    ax_cpi.fill(radians_cpi, temp, color=name_color[j - 1], alpha=0.3)
    ax_cpi.set_xticks(radians_cpi[:-1])
    ax_cpi.set_xticklabels(label_name)
    ax_cpi.set_yticks(np.linspace(-10, 10, 11))
    ax_cpi.set_yticklabels(np.linspace(-10, 10, 11), fontsize=fontsize_legend)
    ax_cpi.set_ylim(df.iloc[-3:, :].min().min() - 1, df.iloc[-3:, :].max().max() + 1)
    ax_cpi.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_cpi.grid(alpha=0.3)
    ax_cpi.spines['polar'].set_visible(False)

temp = df.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_cpi)):
    ax_cpi.text(radians_cpi[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_cpi.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    elif color == 'red':
        label.set_fontsize(12)
    elif color == 'orange':
        label.set_fontsize(10)
    else:
        label.set_fontsize(8)

ax_cpi.set_title(subtitle_cpi, color='gray', fontsize=fontsize_legend)
fig_cpi.suptitle('美国CPI同比分项(' + df.index[-1].strftime(datestyle1) + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_cpi.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_cpi.savefig(os.path.join(fp,'Result','1.19Oversea','********.3CPI_同比分项.png'))
plt.close()

t=printtime(t)

#%%2.1.4CPI季调环比分项
# 定义CPI细分项的series ID  （季调）
series_ids = [
        'CPIAUCSL',  # 总体CPI
        'CPIUFDSL',  # 食品CPI
        'CUSR0000SAF11',  # Food at Home
        'CUSR0000SEFV',  # Food Away from Home
        'CPIENGSL',  # 能源CPI
        'CUSR0000SACE',  # Energy Commodities
        'CUSR0000SEHF',  # Energy Services
        'CPILFESL',  # 核心CPI（不包含食品和能源）
        'CUSR0000SACL1E',  # Commodities Less Food and Energy Commodities
        'CUSR0000SETA01',  # New Vehicles
        'CPIAPPSL',  # Apparel
        'CUSR0000SETA02',  # Used cars and trucks
        'CUSR0000SASLE',  # Services Less Energy Services
        'CUSR0000SAH1',  # Shelter
        'CUSR0000SEHA',  # Rent of Primary Residence
        'CUSR0000SEHC',  # Owners' Equivalent Rent of Residences
        'CUSR0000SAM2',  # Medical Care Services
        'CUSR0000SAS4',  # Transportation Services
    ]

name_list =  ['CPI','食品CPI','家用食品','非家内食品','能源CPI','能源商品','能源服务','核心CPI','核心商品','新车','服装','二手车','核心服务','居住','租金','OER','医疗','运输服务']

data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)

# 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_20 = df.tail(20)
df_adj = df_20
df_adj = df_adj.pct_change(periods=1) * 100
df_adj = df_adj.applymap(lambda x: round(x, 2))  # 近三个月各项同比

subtitle_cpi = '↑持续上行：' + '、'.join(df_adj.iloc[-1, :][(df_adj.iloc[-1, :] > df_adj.iloc[-2, :]) & (
                df_adj.iloc[-2, :] > df_adj.iloc[-3, :])].index.tolist()) + \
                   '\n↓持续下行：' + '、'.join(df_adj.iloc[-1, :][(df_adj.iloc[-1, :] < df_adj.iloc[-2, :]) & (
                df_adj.iloc[-2, :] < df_adj.iloc[-3, :])].index.tolist()) + \
                   '\n↗边际上行：' + '、'.join(df_adj.iloc[-1, :][(df_adj.iloc[-1, :] > df_adj.iloc[-2, :]) & (
                df_adj.iloc[-2, :] <= df_adj.iloc[-3, :])].index.tolist()) + \
                   '\n↘边际下行：' + '、'.join(df_adj.iloc[-1, :][(df_adj.iloc[-1, :] < df_adj.iloc[-2, :]) & (
                df_adj.iloc[-2, :] >= df_adj.iloc[-3, :])].index.tolist())

fig_cpi, ax_cpi = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = df_adj.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_cpi.plot(radians_cpi, temp, color=name_color[j - 1], label=df_adj.index[-j].strftime(datestyle1))
    ax_cpi.fill(radians_cpi, temp, color=name_color[j - 1], alpha=0.3)
    ax_cpi.set_xticks(radians_cpi[:-1])
    ax_cpi.set_xticklabels(label_name)
    ax_cpi.set_yticks(np.linspace(-4, 4, 9))
    ax_cpi.set_yticklabels(np.linspace(-4, 4, 9), fontsize=fontsize_legend)
    ax_cpi.set_ylim(df_adj.iloc[-3:, :].min().min() - 1, df_adj.iloc[-3:, :].max().max() + 1)
    ax_cpi.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_cpi.grid(alpha=0.3)
    ax_cpi.spines['polar'].set_visible(False)

temp = df_adj.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_cpi)):
    ax_cpi.text(radians_cpi[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_cpi.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    elif color == 'red':
        label.set_fontsize(12)
    elif color == 'orange':
        label.set_fontsize(10)
    else:
        label.set_fontsize(8)

ax_cpi.set_title(subtitle_cpi, color='gray', fontsize=fontsize_legend)
fig_cpi.suptitle('美国CPI季调环比分项(' + df_adj.index[-1].strftime(datestyle1) + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_cpi.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_cpi.savefig(os.path.join(fp,'Result','1.19Oversea','********.4CPI_季调环比分项.png'))
plt.close()

t=printtime(t)

#%%2.1.5剔除房租的核心服务通胀~supercore及分项
df_supercore = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),header=0,sheet_name="2.1.7CPI_supercore静态数据")
df_supercore.drop(df_supercore.columns[[2,4]], axis=1, inplace=True)
df_supercore = df_supercore[df_supercore['Level'].isin([0,1])]   #1级分类
df_supercore = df_supercore.drop(df_supercore.index[-1])
data = { '层级':df_supercore['Level'],'名称':df_supercore['Series'],'权重':df_supercore['Weight Value']}
name_supercore = pd.DataFrame(data)
df_supercore_data = df_supercore.drop(df_supercore.columns[[0,2]], axis=1)
df_supercore_data = df_supercore_data.T
col_name = df_supercore_data.iloc[0]
col_name = col_name.tolist()
df_supercore_data.columns = col_name
df_supercore_data.drop(df_supercore_data.index[0], inplace=True)
df_supercore_data = df_supercore_data.sort_index(ascending=True)

fig_supercore,ax_supercore=plt.subplots(nrows=1,ncols=1)
ax_supercore.plot(df_supercore_data.index,df_supercore_data["超核心"],label='剔除房租的核心服务通胀环比增速',color='red')
ax_supercore.text(df_supercore_data['超核心'].index[-1],df_supercore_data['超核心'][-1],round(df_supercore_data['超核心'][-1],1),color='red',fontsize=fontsize_legend)
ax_supercore.spines['top'].set_visible(False)
ax_supercore.spines['right'].set_visible(False)
ax_supercore.legend(loc=2,fontsize=fontsize_legend)
ax_supercore.set_xlim(df_supercore_data['超核心'].index[0],df_supercore_data['超核心'].index[-1])
ax_supercore.set_ylim(-0.5,1)
fig_supercore.suptitle('美国核心服务通胀"Supercore"'+'('+df_supercore_data.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_supercore.tight_layout()
fig_supercore.savefig(os.path.join(fp,'Result','1.19Oversea','********.5CPI_超级核心服务通胀Supercore环比时间序列.png'))
plt.close()

t=printtime(t)

#%%2.1.6Supercore分项画图
name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'red', 'red', 'red', 'red', 'red', 'red', 'red']
name_supercore['权重'][0] = 100
weights = [round(num, 1) for num in name_supercore['权重']]
label_name = []
for i in range(len(df_supercore_data.columns)):
    label_name.append(df_supercore_data.columns[i] + '\n' + '(' + str(weights[i]) + '%)')
name_cpi = df_supercore_data.columns.tolist()
subtitle_cpi = '↑持续上行：' + '、'.join(df_supercore_data.iloc[-1, :][(df_supercore_data.iloc[-1, :] > df_supercore_data.iloc[-2, :]) & (df_supercore_data.iloc[-2, :] > df_supercore_data.iloc[-3,:])].index.tolist()) + \
               '\n↓持续下行：' + '、'.join(df_supercore_data.iloc[-1, :][(df_supercore_data.iloc[-1, :] < df_supercore_data.iloc[-2, :]) & (df_supercore_data.iloc[-2, :] < df_supercore_data.iloc[-3,:])].index.tolist()) + \
               '\n↗边际上行：' + '、'.join(df_supercore_data.iloc[-1, :][(df_supercore_data.iloc[-1, :] > df_supercore_data.iloc[-2, :]) & (df_supercore_data.iloc[-2, :] <= df_supercore_data.iloc[-3,:])].index.tolist()) + \
               '\n↘边际下行：' + '、'.join(df_supercore_data.iloc[-1, :][(df_supercore_data.iloc[-1, :] < df_supercore_data.iloc[-2, :]) & (df_supercore_data.iloc[-2, :] >= df_supercore_data.iloc[-3,:])].index.tolist())

dim_cpi = len(name_cpi)
radians_cpi = np.linspace(0, 2 * np.pi, dim_cpi, endpoint=False)
radians_cpi = np.concatenate((radians_cpi, [radians_cpi[0]]))

fig_cpi, ax_cpi = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = df_supercore_data.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_cpi.plot(radians_cpi, temp, color=name_color[j - 1], label=df_supercore_data.index[-j].strftime(datestyle1))
    ax_cpi.fill(radians_cpi, temp, color=name_color[j - 1], alpha=0.3)
    ax_cpi.set_xticks(radians_cpi[:-1])
    ax_cpi.set_xticklabels(label_name)
    ax_cpi.set_yticks(np.around(np.linspace(-0.2, 0.8, 6), decimals=1))
    ax_cpi.set_yticklabels(np.around(np.linspace(-0.2, 0.8, 6), decimals=1), fontsize=fontsize_legend)
    ax_cpi.set_ylim(df_supercore_data.iloc[-3:, :].min().min() - 1, df_supercore_data.iloc[-3:, :].max().max() + 1)
    ax_cpi.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.2, 1))
    ax_cpi.grid(alpha=0.3)
    ax_cpi.spines['polar'].set_visible(False)

temp = df_supercore_data.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_cpi)):
    ax_cpi.text(radians_cpi[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_cpi.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    else:
        label.set_fontsize(12)

ax_cpi.set_title(subtitle_cpi, color='gray', fontsize=fontsize_legend)
fig_cpi.suptitle('美国超级核心服务通胀环比分项贡献(' + df_supercore_data.index[-1].strftime(datestyle1) + ')', fontsize=fontsize_suptitle,fontweight='bold')
fig_cpi.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_cpi.savefig(os.path.join(fp,'Result','1.19Oversea','********.6CPI_超级核心服务通胀Supercore环比分项贡献.png'))
plt.close()

t=printtime(t)

#%%2.2美国PPI（ppi+核心ppi 同比和环比）-季调后
df_ppi = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='2.2PPI')
us_ppi = w.edb(df_ppi.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_ppi_df = pd.DataFrame(us_ppi.Data,index=df_ppi.iloc[:,0].tolist(),columns=us_ppi.Times).T

fig_usppi,ax_usppi=plt.subplots(nrows=2,ncols=1,sharex=False,figsize=(20,9),dpi=400)
ax_usppi1=ax_usppi[0].twinx()
axis_ppi=[ax_usppi[0],ax_usppi1]
axis_ppi[0].bar(us_ppi_df.index,us_ppi_df['PPI环比'],color='blue',width=3,label = 'PPI环比')
axis_ppi[0].text(us_ppi_df.index[-1],us_ppi_df['PPI环比'][-1],us_ppi_df['PPI环比'][-1],color='blue',alpha=1,fontsize=fontsize_legend)
axis_ppi[0].legend(loc=2,fontsize=fontsize_legend)
axis_ppi[1].plot(us_ppi_df.index,us_ppi_df['PPI同比'],color='red',alpha=0.7,label='PPI同比')
axis_ppi[1].text(us_ppi_df.index[-1],us_ppi_df['PPI同比'][-1],us_ppi_df['PPI同比'][-1],color='red',alpha=1,fontsize=fontsize_legend)
axis_ppi[1].legend(loc=1,fontsize=fontsize_legend)
axis_ppi[0].set_xlim(us_ppi_df.index[0],us_ppi_df.index[-1])
axis_ppi[0].spines['top'].set_visible(False)
axis_ppi[1].spines['right'].set_color('red')
axis_ppi[1].spines['left'].set_color('blue')
axis_ppi[1].tick_params(axis='x', direction='in', rotation=25)
axis_ppi[1].set_title('美国PPI'+'('+us_ppi_df['PPI同比'].index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_ppi[1].spines['top'].set_visible(False)

ax_usppi1=ax_usppi[1].twinx()
axis_ppi=[ax_usppi[1],ax_usppi1]
axis_ppi[0].bar(us_ppi_df.index,us_ppi_df['核心PPI环比'],color='blue',width=3,label = '核心PPI环比')
axis_ppi[0].text(us_ppi_df.index[-1],us_ppi_df['核心PPI环比'][-1],us_ppi_df['核心PPI环比'][-1],color='blue',alpha=1,fontsize=fontsize_legend)
axis_ppi[0].legend(loc=2,fontsize=fontsize_legend)
axis_ppi[1].plot(us_ppi_df.index,us_ppi_df['核心PPI同比'],color='red',alpha=0.7,label='核心PPI同比')
axis_ppi[1].text(us_ppi_df.index[-1],us_ppi_df['核心PPI同比'][-1],us_ppi_df['核心PPI同比'][-1],color='red',alpha=1,fontsize=fontsize_legend)
axis_ppi[1].legend(loc=1,fontsize=fontsize_legend)
axis_ppi[0].spines['top'].set_visible(False)
axis_ppi[1].spines['right'].set_color('red')
axis_ppi[1].spines['left'].set_color('blue')
axis_ppi[1].tick_params(axis='x', direction='in', rotation=25)
axis_ppi[1].set_title('美国核心PPI'+'('+us_ppi_df['PPI同比'].index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_ppi[1].spines['top'].set_visible(False)
axis_ppi[1].set_xlim(us_ppi_df.index[0],us_ppi_df.index[-1])
fig_usppi.tight_layout()
fig_usppi.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.2.2PPI_同比和环比时间序列.png'))
plt.close()

t=printtime(t)

#%%2.3.1PCE及核心PCE时间序列同比环比
df_pce = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='2.3.1PCE')
us_cpi = w.edb(df_pce.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_cpi_df = pd.DataFrame(us_cpi.Data,index=df_pce.iloc[:,0].tolist(),columns=us_cpi.Times).T

fig_uscpi,ax_uscpi=plt.subplots(nrows=2,ncols=1,sharex=False,figsize=(20,9),dpi=400)
ax_uscpi1=ax_uscpi[0].twinx()
axis_uscpi=[ax_uscpi[0],ax_uscpi1]
axis_uscpi[0].bar(us_cpi_df.index,us_cpi_df['PCE环比'],color='blue',width=3,label = 'PCE环比')
axis_uscpi[0].text(us_cpi_df.index[-1],us_cpi_df['PCE环比'][-1],round(us_cpi_df['PCE环比'][-1],1),color='blue',alpha=1,fontsize=fontsize_legend)
axis_uscpi[0].legend(loc=2,fontsize=fontsize_legend)
axis_uscpi[1].plot(us_cpi_df.index,us_cpi_df['PCE同比'],color='red',alpha=0.7,label='PCE同比')
axis_uscpi[1].text(us_cpi_df.index[-1],us_cpi_df['PCE同比'][-1],round(us_cpi_df['PCE同比'][-1],1),color='red',alpha=1,fontsize=fontsize_legend)
axis_uscpi[1].legend(loc=1,fontsize=fontsize_legend)
axis_uscpi[0].spines['top'].set_visible(False)
axis_uscpi[1].spines['right'].set_color('red')
axis_uscpi[1].spines['left'].set_color('blue')
axis_uscpi[1].tick_params(axis='x', direction='in', rotation=25)
axis_uscpi[1].set_title('美国PCE'+'('+us_cpi_df['PCE同比'].index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_uscpi[0].set_xlim(us_cpi_df.index[0],us_cpi_df.index[-1])
axis_uscpi[1].spines['top'].set_visible(False)

ax_uscpi1=ax_uscpi[1].twinx()
axis_uscpi=[ax_uscpi[1],ax_uscpi1]
axis_uscpi[0].bar(us_cpi_df.index,us_cpi_df['核心PCE环比'],color='blue',width=3,label = '核心PCE环比')
axis_uscpi[0].text(us_cpi_df.index[-1],us_cpi_df['核心PCE环比'][-1],round(us_cpi_df['核心PCE环比'][-1],1),color='blue',alpha=1,fontsize=fontsize_legend)
axis_uscpi[0].legend(loc=2,fontsize=fontsize_legend)
axis_uscpi[1].plot(us_cpi_df.index,us_cpi_df['核心PCE同比'],color='red',alpha=0.7,label='核心PCE同比')
axis_uscpi[1].text(us_cpi_df.index[-1],us_cpi_df['核心PCE同比'][-1],round(us_cpi_df['核心PCE同比'][-1],1),color='red',alpha=1,fontsize=fontsize_legend)
axis_uscpi[1].legend(loc=1,fontsize=fontsize_legend)
axis_uscpi[0].spines['top'].set_visible(False)
axis_uscpi[1].spines['right'].set_color('red')
axis_uscpi[1].spines['left'].set_color('blue')
axis_uscpi[1].tick_params(axis='x', direction='in', rotation=25)
axis_uscpi[1].set_title('美国核心PCE'+'('+us_cpi_df['PCE同比'].index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_uscpi[1].spines['top'].set_visible(False)
axis_uscpi[1].set_xlim(us_cpi_df.index[0],us_cpi_df.index[-1])
fig_uscpi.tight_layout()
fig_uscpi.savefig(os.path.join(fp,'Result','1.19Oversea','********.1PCE_同比和环比时间序列.png'))
plt.close()

t=printtime(t)

#%%2.3.2PCE环比分项
data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='2.3.2PCE指数')
df = data.iloc[9:]
df.columns = data.iloc[8,:]
df.dropna(how='all', inplace=True)
df = df.drop(df.columns[:2], axis=1)
df.columns = pd.to_datetime(df.columns, format='%m月 %Y').strftime('%Y-%m')
data_weights = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='2.3.3PCE数值')
df_weights = data_weights.iloc[8:]
df_weights.columns = data_weights.iloc[7,:]
df_weights.dropna(how='all', inplace=True)
df_significant_weights = df_weights.drop(df_weights.columns[:2], axis=1)
#df_significant_weights = df_weights.iloc[:-34]

first_col = df_significant_weights.iloc[:,0] / df_significant_weights.iloc[0,0]*100
df_significant_weights['权重'] = first_col
result_df = df_significant_weights[df_significant_weights['权重'] > 7]
result_df = result_df[~result_df.index.str.contains('非营利机构')]

keywords_to_extract = ['个人消费平减指数(每月)','除食品与能源','商品', '服务','耐用品','非耐用品','家庭消费支出','服务家庭的非营利机构最终消费支出','场外消费用食品与饮料购买',
                       '其他非耐用品','住房与公用事业','医疗保健','运输服务','休闲服务','食宿','金融服务与保险','其他服务','住宅','门诊服务','医院和护理中心服务']
df.index = df.index.str.lower().str.replace(' ', '') #删除空格
df_final = df[df.index.isin(keywords_to_extract)]
df_final = df_final.T     #用来算环比和同比的
df_right = df_final.iloc[::-1]
# df_right.index = df_right.index.str.replace(' ', '') #删除空
df_right.rename(columns={df_right.columns[0]: 'PCE'}, inplace=True)
last_column = df_right.pop('除食品与能源')
df_right.insert(1, '除食品与能源', last_column)
df_right.rename(columns={'除食品与能源': '核心PCE'}, inplace=True)

df_significant_weights.index = df_significant_weights.index.str.lower().str.replace(' ', '')
df_weights = df_significant_weights[df_significant_weights.index.isin(keywords_to_extract)]
last_row = df_weights.iloc[-1:]
df_weights = df_weights.iloc[:-1]
df_weights = pd.concat([last_row, df_weights], ignore_index=True)
weights_list = [round(num,1) for num in df_weights['权重']]
weights_list.insert(0, 100)    #加入第一个100%
str_num_list = ['(' + str(num) + '%)' for num in weights_list]

df_pct_change_mom = df_right.pct_change() * 100
name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'blue','red', 'orange','orange','green','green','red','orange','green','purple','green','purple','purple','green',
               'green','green','green','green','orange']
weights = str_num_list
label_name = []
for i in range(len(df_pct_change_mom.columns)):
    label_name.append(df_pct_change_mom.columns[i] + '\n' + weights[i])
name_pce = df_pct_change_mom.columns.tolist()
subtitle_pce = '↑持续上行：' + '、'.join(
    df_pct_change_mom.iloc[-1, :][(df_pct_change_mom.iloc[-1, :] > df_pct_change_mom.iloc[-2, :]) & (df_pct_change_mom.iloc[-2, :] > df_pct_change_mom.iloc[-3, :])].index.tolist()) + \
               '\n↓持续下行：' + '、'.join(
    df_pct_change_mom.iloc[-1, :][(df_pct_change_mom.iloc[-1, :] < df_pct_change_mom.iloc[-2, :]) & (df_pct_change_mom.iloc[-2, :] < df_pct_change_mom.iloc[-3, :])].index.tolist()) + \
               '\n↗边际上行：' + '、'.join(
    df_pct_change_mom.iloc[-1, :][(df_pct_change_mom.iloc[-1, :] > df_pct_change_mom.iloc[-2, :]) & (df_pct_change_mom.iloc[-2, :] <= df_pct_change_mom.iloc[-3, :])].index.tolist()) + \
               '\n↘边际下行：' + '、'.join(
    df_pct_change_mom.iloc[-1, :][(df_pct_change_mom.iloc[-1, :] < df_pct_change_mom.iloc[-2, :]) & (df_pct_change_mom.iloc[-2, :] >= df_pct_change_mom.iloc[-3, :])].index.tolist())

dim_pce = len(name_pce)
radians_pce = np.linspace(0, 2 * np.pi, dim_pce, endpoint=False)
radians_pce = np.concatenate((radians_pce, [radians_pce[0]]))

fig_pce, ax_pce = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = df_pct_change_mom.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_pce.plot(radians_pce, temp, color=name_color[j - 1], label=df_pct_change_mom.index[-j])
    ax_pce.fill(radians_pce, temp, color=name_color[j - 1], alpha=0.3)
    ax_pce.set_xticks(radians_pce[:-1])
    ax_pce.set_xticklabels(label_name)
    ax_pce.set_ylim(df_pct_change_mom.iloc[-3:, :].min().min() - 1, df_pct_change_mom.iloc[-3:, :].max().max() + 1)
    ax_pce.set_yticklabels(np.linspace(-2, 2, 9),fontsize=fontsize_legend)
    ax_pce.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_pce.grid(alpha=0.3)
    ax_pce.spines['polar'].set_visible(False)

temp = df_pct_change_mom.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_pce)):
    ax_pce.text(radians_pce[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_pce.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    elif color == 'red':
        label.set_fontsize(12)
    elif color == 'orange':
        label.set_fontsize(10)
    elif color == 'green':
        label.set_fontsize(8)
    else:
        label.set_fontsize(6)

ax_pce.set_title(subtitle_pce, color='gray', fontsize=8)
fig_pce.suptitle('美国PCE环比分项(' + df_pct_change_mom.index[-1] + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_pce.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_pce.savefig(os.path.join(fp,'Result','1.19Oversea','********.2PCE_环比分项.png'))
plt.close()

t=printtime(t)

#%%2.3.3PCE同比分项
df_pct_change_yoy = df_right.pct_change(12) * 100
name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'blue','red', 'orange','orange','green','green','red','orange','green','purple','green','purple','purple','green',
               'green','green','green','green','orange']
weights = str_num_list
label_name = []
for i in range(len(df_pct_change_yoy.columns)):
    label_name.append(df_pct_change_yoy.columns[i] + '\n' + weights[i])
name_pce = df_pct_change_yoy.columns.tolist()
subtitle_pce = '↑持续上行：' + '、'.join(
    df_pct_change_yoy.iloc[-1, :][(df_pct_change_yoy.iloc[-1, :] > df_pct_change_yoy.iloc[-2, :]) & (df_pct_change_yoy.iloc[-2, :] > df_pct_change_yoy.iloc[-3, :])].index.tolist()) + \
               '\n↓持续下行：' + '、'.join(
    df_pct_change_yoy.iloc[-1, :][(df_pct_change_yoy.iloc[-1, :] < df_pct_change_yoy.iloc[-2, :]) & (df_pct_change_yoy.iloc[-2, :] < df_pct_change_yoy.iloc[-3, :])].index.tolist()) + \
               '\n↗边际上行：' + '、'.join(
    df_pct_change_yoy.iloc[-1, :][(df_pct_change_yoy.iloc[-1, :] > df_pct_change_yoy.iloc[-2, :]) & (df_pct_change_yoy.iloc[-2, :] <= df_pct_change_yoy.iloc[-3, :])].index.tolist()) + \
               '\n↘边际下行：' + '、'.join(
    df_pct_change_yoy.iloc[-1, :][(df_pct_change_yoy.iloc[-1, :] < df_pct_change_yoy.iloc[-2, :]) & (df_pct_change_yoy.iloc[-2, :] >= df_pct_change_yoy.iloc[-3, :])].index.tolist())

dim_pce = len(name_pce)
radians_pce = np.linspace(0, 2 * np.pi, dim_pce, endpoint=False)
radians_pce = np.concatenate((radians_pce, [radians_pce[0]]))

fig_pce, ax_pce = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = df_pct_change_yoy.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_pce.plot(radians_pce, temp, color=name_color[j - 1], label=df_pct_change_yoy.index[-j])
    ax_pce.fill(radians_pce, temp, color=name_color[j - 1], alpha=0.3)
    ax_pce.set_xticks(radians_pce[:-1])
    ax_pce.set_xticklabels(label_name)
    ax_pce.set_ylim(df_pct_change_yoy.iloc[-3:, :].min().min() - 1, df_pct_change_yoy.iloc[-3:, :].max().max() + 1)
    ax_pce.set_yticklabels(np.around(np.linspace(-4, 6, 6), decimals=1), fontsize=fontsize_legend)
    ax_pce.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_pce.grid(alpha=0.3)
    ax_pce.spines['polar'].set_visible(False)

temp = df_pct_change_yoy.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_pce)):
    ax_pce.text(radians_pce[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_pce.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    elif color == 'red':
        label.set_fontsize(12)
    elif color == 'orange':
        label.set_fontsize(10)
    elif color == 'green':
        label.set_fontsize(8)
    else:
        label.set_fontsize(6)

ax_pce.set_title(subtitle_pce, color='gray', fontsize=8)
fig_pce.suptitle('美国PCE同比分项(' + df_pct_change_yoy.index[-1] + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_pce.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_pce.savefig(os.path.join(fp,'Result','1.19Oversea','********.3PCE_同比分项.png'))
plt.close()

t=printtime(t)

#%%2.3.4PCE Supercore同比和环比时间序列
supercore_yoy = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,sheet_name='2.3.4supercore同比贡献')
supercore_yoy.drop(supercore_yoy.columns[[1, 2]], axis=1, inplace=True)
supercore_yoy = supercore_yoy.T
supercore_yoy.columns=supercore_yoy.iloc[0,:]
supercore_yoy = supercore_yoy.iloc[1:]
supercore_yoy = supercore_yoy.sort_index(ascending=True)
supercore_mom = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,sheet_name='2.3.5supercore环比贡献')
supercore_mom.drop(supercore_mom.columns[[1, 2]], axis=1, inplace=True)
supercore_mom = supercore_mom.T
supercore_mom.columns=supercore_mom.iloc[0,:]
supercore_mom = supercore_mom.iloc[1:]
supercore_mom = supercore_mom.sort_index(ascending=True)

fig_pce,ax_pce=plt.subplots(nrows=1,ncols=1,sharex=False,figsize=(20,9),dpi=400)
ax_pce1=ax_pce.twinx()
axis_pce=[ax_pce,ax_pce1]
axis_pce[0].bar(supercore_mom.index,supercore_mom.iloc[:,0],color='blue',width=3,label = '超级核心服务PCE环比')
axis_pce[0].text(supercore_mom.index[-1],supercore_mom.iloc[:,0][-1],round(supercore_mom.iloc[:,0][-1],1),color='blue',fontsize=fontsize_legend)
axis_pce[0].legend(loc=2,fontsize=fontsize_legend)
axis_pce[1].plot(supercore_yoy.index,supercore_yoy.iloc[:,0],color='red',alpha=0.7,label='超级核心服务PCE同比')
axis_pce[1].text(supercore_yoy.index[-1],supercore_yoy.iloc[:,0][-1],round(supercore_yoy.iloc[:,0][-1],1),color='red',fontsize=fontsize_legend)
axis_pce[1].legend(loc=1,fontsize=fontsize_legend)
axis_pce[0].spines['top'].set_visible(False)
axis_pce[1].spines['right'].set_color('red')
axis_pce[1].spines['left'].set_color('blue')
axis_pce[1].tick_params(axis='x', direction='in', rotation=25)
axis_pce[1].set_title('超级核心服务PCE'+'('+supercore_mom.index[-1].strftime(datestyle)+')',fontsize=fontsize_subtitle)
axis_pce[1].spines['top'].set_visible(False)
axis_pce[1].set_xlim(supercore_mom.index[0],supercore_mom.index[-1])
fig_pce.tight_layout()
fig_pce.savefig(os.path.join(fp,'Result','1.19Oversea','********.4PCE_supercore同比和环比时间序列.png'))
plt.close()

t=printtime(t)

#%%2.3.5PCE-supercore分项同比贡献
supercore_yoy = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),sheet_name='2.3.4supercore同比贡献')
supercore_yoy.drop(supercore_yoy.columns[[2, 3]], axis=1, inplace=True)
supercore_yoy = supercore_yoy[(supercore_yoy['Level'] == 0) | (supercore_yoy['Level'] == 1)]
supercore_yoy = supercore_yoy.drop(columns=['Level'])
supercore_yoy = supercore_yoy.T
supercore_yoy.columns=['超级核心服务通胀','非营利组织','餐饮服务和住宿设施','金融服务和保险业','住房和公用事业','医疗保健','其他服务','娱乐服务','交通运输服务']
supercore_yoy = supercore_yoy.iloc[1:]
supercore_yoy = supercore_yoy.sort_index(ascending=True)

name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'red', 'red','red','red','red','red','red','red']
label_name = []
for i in range(len(supercore_yoy.columns)):
    label_name.append(supercore_yoy.columns[i] + '\n')
name_pce = supercore_yoy.columns.tolist()
subtitle_pce = '↑持续上行：' + '、'.join(
    supercore_yoy.iloc[-1, :][(supercore_yoy.iloc[-1, :] > supercore_yoy.iloc[-2, :]) & (supercore_yoy.iloc[-2, :] > supercore_yoy.iloc[-3, :])].index.tolist()) + \
               '\n↓持续下行：' + '、'.join(
    supercore_yoy.iloc[-1, :][(supercore_yoy.iloc[-1, :] < supercore_yoy.iloc[-2, :]) & (supercore_yoy.iloc[-2, :] < supercore_yoy.iloc[-3, :])].index.tolist()) + \
               '\n↗边际上行：' + '、'.join(
    supercore_yoy.iloc[-1, :][(supercore_yoy.iloc[-1, :] > supercore_yoy.iloc[-2, :]) & (supercore_yoy.iloc[-2, :] <= supercore_yoy.iloc[-3, :])].index.tolist()) + \
               '\n↘边际下行：' + '、'.join(
    supercore_yoy.iloc[-1, :][(supercore_yoy.iloc[-1, :] < supercore_yoy.iloc[-2, :]) & (supercore_yoy.iloc[-2, :] >= supercore_yoy.iloc[-3, :])].index.tolist())

dim_pce = len(name_pce)
radians_pce = np.linspace(0, 2 * np.pi, dim_pce, endpoint=False)
radians_pce = np.concatenate((radians_pce, [radians_pce[0]]))

fig_pce, ax_pce = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = supercore_yoy.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_pce.plot(radians_pce, temp, color=name_color[j - 1], label=supercore_yoy.index[-j].strftime(datestyle1))
    ax_pce.fill(radians_pce, temp, color=name_color[j - 1], alpha=0.3)
    ax_pce.set_xticks(radians_pce[:-1])
    ax_pce.set_xticklabels(label_name)
    ax_pce.set_ylim(supercore_yoy.iloc[-3:, :].min().min() - 1, supercore_yoy.iloc[-3:, :].max().max() + 1)
    ax_pce.set_yticklabels(np.around(np.linspace(0, 4, 6), decimals=1), fontsize=fontsize_legend)
    ax_pce.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_pce.grid(alpha=0.3)
    ax_pce.spines['polar'].set_visible(False)

temp = supercore_yoy.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_pce)):
    ax_pce.text(radians_pce[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_pce.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    else:
        label.set_fontsize(12)

ax_pce.set_title(subtitle_pce, color='gray', fontsize=8)
fig_pce.suptitle('超级核心服务PCE同比分项贡献(' + supercore_yoy.index[-1].strftime(datestyle) + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_pce.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_pce.savefig(os.path.join(fp,'Result','1.19Oversea','********.5PCE_supercore同比分项贡献.png'))
plt.close()

t=printtime(t)

#%%2.3.6PCE-supercore分项环比贡献
supercore_mom= pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),sheet_name='2.3.5supercore环比贡献')
supercore_mom.drop(supercore_mom.columns[[2, 3]], axis=1, inplace=True)
supercore_mom = supercore_mom[(supercore_mom['Level'] == 0) | (supercore_mom['Level'] == 1)]
supercore_mom = supercore_mom.drop(columns=['Level'])
supercore_mom = supercore_mom.T
supercore_mom.columns=['超级核心服务通胀','非营利组织','餐饮服务和住宿设施','金融服务和保险业','住房和公用事业','医疗保健','其他服务','娱乐服务','交通运输服务']
supercore_mom = supercore_mom.iloc[1:]
supercore_mom = supercore_mom.sort_index(ascending=True)

name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'red', 'red','red','red','red','red','red','red']
label_name = []
for i in range(len(supercore_mom.columns)):
    label_name.append(supercore_mom.columns[i] + '\n')
name_pce = supercore_mom.columns.tolist()
subtitle_pce = '↑持续上行：' + '、'.join(
    supercore_mom.iloc[-1, :][(supercore_mom.iloc[-1, :] > supercore_mom.iloc[-2, :]) & (supercore_mom.iloc[-2, :] > supercore_mom.iloc[-3, :])].index.tolist()) + \
               '\n↓持续下行：' + '、'.join(
    supercore_mom.iloc[-1, :][(supercore_mom.iloc[-1, :] < supercore_mom.iloc[-2, :]) & (supercore_mom.iloc[-2, :] < supercore_mom.iloc[-3, :])].index.tolist()) + \
               '\n↗边际上行：' + '、'.join(
    supercore_mom.iloc[-1, :][(supercore_mom.iloc[-1, :] > supercore_mom.iloc[-2, :]) & (supercore_mom.iloc[-2, :] <= supercore_mom.iloc[-3, :])].index.tolist()) + \
               '\n↘边际下行：' + '、'.join(
    supercore_mom.iloc[-1, :][(supercore_mom.iloc[-1, :] < supercore_mom.iloc[-2, :]) & (supercore_mom.iloc[-2, :] >= supercore_mom.iloc[-3, :])].index.tolist())

dim_pce = len(name_pce)
radians_pce = np.linspace(0, 2 * np.pi, dim_pce, endpoint=False)
radians_pce = np.concatenate((radians_pce, [radians_pce[0]]))

fig_pce, ax_pce = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = supercore_mom.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_pce.plot(radians_pce, temp, color=name_color[j - 1], label=supercore_mom.index[-j].strftime(datestyle1))
    ax_pce.fill(radians_pce, temp, color=name_color[j - 1], alpha=0.3)
    ax_pce.set_xticks(radians_pce[:-1])
    ax_pce.set_xticklabels(label_name)
    ax_pce.set_ylim(supercore_mom.iloc[-3:, :].min().min() - 1, supercore_mom.iloc[-3:, :].max().max() + 1)
    ax_pce.set_yticklabels(np.around(np.linspace(-1, 1, 6), decimals=1), fontsize=fontsize_legend)
    ax_pce.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_pce.grid(alpha=0.3)
    ax_pce.spines['polar'].set_visible(False)

temp = supercore_mom.iloc[-1,:]
temp = np.concatenate((temp, [temp[0]]))
for i in range(len(radians_pce)):
    ax_pce.text(radians_pce[i], temp[i], round(temp[i],1),color='blue', fontsize=fontsize_legend)

for label, color in zip(ax_pce.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    else:
        label.set_fontsize(12)

ax_pce.set_title(subtitle_pce, color='gray', fontsize=8)
fig_pce.suptitle('超级核心服务PCE环比分项贡献(' + supercore_mom.index[-1].strftime(datestyle) + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_pce.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_pce.savefig(os.path.join(fp,'Result','1.19Oversea','********.6PCE_supercore环比分项贡献.png'))
plt.close()

t=printtime(t)

#%%2.4.1通胀预期（基于市场：名义利率-tips）
df_inflation = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='2.4通胀预期')
inflation = w.edb(df_inflation.index.tolist()[:4], "2018-01-01",enddate,"Fill=Previous")
data_USAbond = pd.DataFrame(inflation.Data,index=df_inflation.iloc[:,0].tolist()[:4],columns=inflation.Times).T
data_USAbond['10年通胀预期'] = data_USAbond['美债名义利率-10Y']-data_USAbond['美债实际利率-10Y']
data_USAbond['5年通胀预期'] = data_USAbond['美债名义利率-5Y']-data_USAbond['美债实际收益率-5Y']

fig_usbond,ax_usbond=plt.subplots(nrows=1,ncols=2)
ax_usbond[0].plot(data_USAbond.index,data_USAbond['10年通胀预期'],label='10年盈亏平衡通胀预期（%）',color='red')
ax_usbond[0].legend(loc=2,fontsize=fontsize_legend)
ax_usbond[0].spines['top'].set_visible(False)
ax_usbond[0].spines['right'].set_visible(False)
ax_usbond[0].text(data_USAbond.index[-1],data_USAbond['10年通胀预期'][-1],round(data_USAbond['10年通胀预期'][-1],2),color='red',fontsize=fontsize_legend)
ax_usbond[1].plot(data_USAbond.index,data_USAbond['5年通胀预期'],label='5年盈亏平衡通胀预期（%）',color='red')
ax_usbond[1].text(data_USAbond.index[-18],data_USAbond['5年通胀预期'][-1],round(data_USAbond['5年通胀预期'][-1],2),color='red',fontsize=fontsize_legend)
ax_usbond[1].set_xlim(data_USAbond.index[0],data_USAbond.index[-1])
ax_usbond[1].legend(loc=1,fontsize=fontsize_legend)
ax_usbond[1].spines['top'].set_visible(False)
ax_usbond[1].spines['right'].set_visible(False)
fig_usbond.suptitle('基于市场的通胀预期'+'('+data_USAbond.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_usbond.tight_layout()
fig_usbond.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.2.4.1通胀预期_基于市场.png'))
plt.close()

t=printtime(t)

#%%2.4.2通胀预期（基于调研-纽约联储）
# median point inflation：直接取具体预测值的中位数
# median expected inflation：基于受访者的概率分布预期的中位数
inflation_expectation = w.edb(df_inflation.index.tolist()[-2:], "2015-01-01",enddate,"Fill=Previous")
inflation_expectation_df = pd.DataFrame(inflation_expectation.Data,index=df_inflation.iloc[:,0].tolist()[-2:],columns=inflation_expectation.Times).T

fig_inflation,ax_inflation=plt.subplots(nrows=1,ncols=1)
ax_inflation.plot(inflation_expectation_df.index,inflation_expectation_df['未来1年通货膨胀率：中位数预测点'],label='未来1年通货膨胀率：中位数预测点',color='red')
ax_inflation.text(inflation_expectation_df.index[-1],inflation_expectation_df['未来1年通货膨胀率：中位数预测点'][-1],round(inflation_expectation_df['未来1年通货膨胀率：中位数预测点'][-1],1),color='red',fontsize=fontsize_legend)
ax_inflation.plot(inflation_expectation_df.index,inflation_expectation_df['未来1年通货膨胀率：中位数'],label='未来1年通货膨胀率：中位数',color='blue')
ax_inflation.text(inflation_expectation_df.index[-1],inflation_expectation_df['未来1年通货膨胀率：中位数'][-1],round(inflation_expectation_df['未来1年通货膨胀率：中位数'][-1],1),color='blue',fontsize=fontsize_legend)
ax_inflation.legend(loc=2,fontsize=fontsize_legend)
ax_inflation.spines['top'].set_visible(False)
ax_inflation.spines['right'].set_visible(False)
ax_inflation.set_xlim(inflation_expectation_df.index[0],inflation_expectation_df.index[-1])
fig_inflation.suptitle('基于纽约联储调研的通胀预期'+'('+inflation_expectation_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_inflation.tight_layout()
fig_inflation.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.2.4.2通胀预期_基于纽约联储调研.png'))
plt.close()

t=printtime(t)

print('通胀数据完成！')

#%%3就业
writer_job = pd.ExcelWriter(os.path.join(fp,'Result','1.19Oversea','1.19.3.0美国就业数据.xlsx'))

#%%3.1失业率+新增非农就业
df_data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="3.1失业率与新增非农")
us_work = w.edb(df_data.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_work_df = pd.DataFrame(us_work.Data,index=df_data.iloc[:,0].tolist(),columns=us_work.Times).T
us_work_df.to_excel(writer_job,"美国失业率+非农")

us_work_dat=[us_work_df.iloc[:,0],us_work_df.iloc[:,1]]
title_us_work=us_work_df.columns.tolist()
color_us_work=['blue','red']
loc_us_work=[2,1]
fig_us_work,ax_us_work=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_us_work1=ax_us_work.twinx()
axis_us_work=[ax_us_work,ax_us_work1]
axis_us_work[0].plot(us_work_df.iloc[:,0].index,us_work_df.iloc[:,0],label=title_us_work[0],color=color_us_work[1])
axis_us_work[0].legend(loc=loc_us_work[0],fontsize=fontsize_legend)
axis_us_work[0].text(us_work_df.index[-1],us_work_df.iloc[:,0][-1],us_work_df.iloc[:,0][-1],color='red',alpha=0.7,fontsize=10)
axis_us_work[0].spines['top'].set_visible(False)
axis_us_work[0].set_ylim(2,10)
axis_us_work[0].tick_params(axis='y', direction='out', color='black', labelcolor='black')
axis_us_work[1].plot(us_work_df.iloc[:,1].index,us_work_df.iloc[:,1],label=title_us_work[1],color=color_us_work[0])
axis_us_work[1].legend(loc=loc_us_work[1],fontsize=fontsize_legend)
axis_us_work[1].text(us_work_df.index[-1],us_work_df.iloc[:,1][-1],us_work_df.iloc[:,1][-1],color='blue',alpha=0.7,fontsize=10)
axis_us_work[1].spines['top'].set_visible(False)
axis_us_work[1].spines['right'].set_color('blue')
axis_us_work[1].spines['left'].set_color('red')
axis_us_work[1].set_ylim(-1000,3000)
ax_us_work.set_xlim(us_work_df.index[0],us_work_df.index[-1])
fig_us_work.suptitle('美国失业率及新增非农就业人数'+'('+us_work_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_us_work.tight_layout()
fig_us_work.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.1美国就业（失业率+非农）.png'))
plt.close()

t=printtime(t)

#%%3.2劳动参与率
df_part = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="3.2劳动参与率")
us_part = w.edb(df_part.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_part_df = pd.DataFrame(us_part.Data,index=df_part.iloc[:,0].tolist(),columns=us_part.Times).T
us_part_df.to_excel(writer_job,"美国劳动参与率")

fig_part,ax_part=plt.subplots(nrows=1,ncols=2)
ax_part[0].plot(us_part_df.index,us_part_df.iloc[:,0],label=us_part_df.columns[0],color='red')
ax_part[0].text(us_part_df.index[-1],us_part_df.iloc[:,0][-1],us_part_df.iloc[:,0][-1],color='red',alpha=0.7,fontsize=10)
ax_part[0].spines['top'].set_visible(False)
ax_part[0].spines['right'].set_visible(False)
ax_part[0].legend(loc=2,fontsize=fontsize_legend)
ax_part[0].set_xlim(us_part_df.index[0],us_part_df.index[-1])
ax_part1=ax_part[1].twinx()
axis_part=[ax_part[1],ax_part1]
axis_part[0].plot(us_part_df.index,us_part_df.iloc[:,1],label=us_part_df.columns[1],color='red')
axis_part[0].text(us_part_df.index[-1],us_part_df.iloc[:,1][-1],us_part_df.iloc[:,1][-1],color='red',alpha=0.7,fontsize=10)
axis_part[0].legend(loc=2,fontsize=fontsize_legend)
axis_part[0].spines['top'].set_visible(False)
axis_part[0].spines['left'].set_color('red')
axis_part[1].plot(us_part_df.index,us_part_df.iloc[:,2],label=us_part_df.columns[2],color='blue')
axis_part[1].text(us_part_df.index[-1],us_part_df.iloc[:,2][-1],us_part_df.iloc[:,2][-1],color='blue',alpha=0.7,fontsize=10)
axis_part[1].legend(loc=1,fontsize=fontsize_legend)
axis_part[1].spines['top'].set_visible(False)
axis_part[1].spines['right'].set_color('blue')
axis_part[1].spines['left'].set_color('red')
axis_part[1].set_xlim(us_part_df.index[0],us_part_df.index[-1])
fig_part.suptitle('美国劳动参与率'+'('+us_part_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_part.tight_layout()
fig_part.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.2美国就业（劳动参与率）.png'))
plt.close()

t=printtime(t)

#%%3.3时薪
df_wage = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="3.3时薪")
us_wage = w.edb(df_wage.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_wage_df = pd.DataFrame(us_wage.Data,index=df_wage.iloc[:,0].tolist(),columns=us_wage.Times).T
us_wage_df['同比'] = us_wage_df.iloc[:,0].pct_change(periods=12)*100
us_wage_df['环比'] = us_wage_df.iloc[:,0].pct_change(periods=1)*100
us_wage_df = us_wage_df.dropna()
us_wage_df.to_excel(writer_job,"美国时薪增长")

fig_wage,ax_wage=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_wage1=ax_wage.twinx()
axis_wage=[ax_wage,ax_wage1]
axis_wage[0].plot(us_wage_df.index,us_wage_df['同比'],label='美国私人非农员工平均时薪同比',color='red')
axis_wage[0].text(us_wage_df.index[-1],us_wage_df['同比'][-1],round(us_wage_df['同比'][-1],1),color='red',alpha=0.7,fontsize=10)
axis_wage[0].spines['top'].set_visible(False)
axis_wage[0].set_xlim(us_wage_df.index[0],us_wage_df.index[-1])
axis_wage[1].bar(us_wage_df.index,us_wage_df['环比'],label='美国私人非农员工平均时薪环比',color='blue',width=3)
axis_wage[1].text(us_wage_df.index[-1],us_wage_df['环比'][-1],round(us_wage_df['环比'][-1],1),color='blue',alpha=0.7,fontsize=10)
axis_wage[1].spines['top'].set_visible(False)
axis_wage[1].set_xlim(us_wage_df.index[0],us_wage_df.index[-1])
axis_wage[1].spines['right'].set_color('blue')
axis_wage[1].spines['left'].set_color('red')
fig_wage.suptitle('美国私人非农企业员工平均时薪同比增速'+'('+us_wage_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_wage.tight_layout()
fig_wage.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.3美国就业（时薪增速）.png'))
plt.close()

t=printtime(t)

#%%3.4职位空缺
df_gap = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="3.4职位空缺")
us_gap = w.edb(df_gap.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_gap_df = pd.DataFrame(us_gap.Data,index=df_gap.iloc[:,0].tolist(),columns=us_gap.Times).T
us_gap_df['空缺职位数失业人数'] = us_gap_df.iloc[:,0]/us_gap_df.iloc[:,1]
us_gap_df.to_excel(writer_job,'职位空缺数')

fig_gap,ax_gap=plt.subplots(nrows=1,ncols=2)
ax_gap[0].plot(us_gap_df.index,us_gap_df.iloc[:,0],label=us_gap_df.columns[0],color='red')
ax_gap[0].text(us_gap_df.index[-1],us_gap_df.iloc[:,0][-1],round(us_gap_df.iloc[:,0][-1],0),color='red',alpha=0.7,fontsize=10)
ax_gap[0].spines['top'].set_visible(False)
ax_gap[0].spines['right'].set_visible(False)
ax_gap[0].legend(loc=2,fontsize=fontsize_legend)
ax_gap[0].set_xlim(us_gap_df.index[0],us_gap_df.index[-1])
ax_gap[1].plot(us_gap_df.index,us_gap_df.iloc[:,-1],label='空缺职位数/失业人数',color='red')
ax_gap[1].text(us_gap_df.index[-1],us_gap_df.iloc[:,-1][-1],round(us_gap_df.iloc[:,-1][-1],1),color='red',alpha=0.7,fontsize=10)
ax_gap[1].spines['top'].set_visible(False)
ax_gap[1].spines['right'].set_visible(False)
ax_gap[1].legend(loc=1,fontsize=fontsize_legend)
ax_gap[1].set_xlim(us_gap_df.index[0],us_gap_df.index[-1])
fig_gap.suptitle('美国空缺职位数与劳动力市场"紧度"'+'('+us_gap_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_gap.tight_layout()
fig_gap.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.4美国劳动力市场空缺职位数.png'))
plt.close()

t=printtime(t)

#%%3.5首申失业救济金+持续申领失业救济金(周度）
df_u = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="3.5失业金领取")
us_u = w.edb(df_u.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_u_df = pd.DataFrame(us_u.Data,index=df_u.iloc[:,0].tolist(),columns=us_u.Times).T
us_u_df = us_u_df/1000
us_u_df.to_excel(writer_job,'失业金领取情况')

fig_u,ax_u=plt.subplots(nrows=1,ncols=1)
ax_u1=ax_u.twinx()
axis_u=[ax_u,ax_u1]
axis_u[0].plot(us_u_df.index,us_u_df.iloc[:,0],label=us_u_df.columns[0],color='red')
axis_u[0].text(us_u_df.index[-1],us_u_df.iloc[:,0][-1],us_u_df.iloc[:,0][-1],color='red',alpha=0.7,fontsize=10)
axis_u[0].spines['top'].set_visible(False)
axis_u[0].legend(loc=2,fontsize=fontsize_legend)
axis_u[1].plot(us_u_df.index,us_u_df.iloc[:,1],label=us_u_df.columns[1],color='blue')
axis_u[1].text(us_u_df.index[-1],us_u_df.iloc[:,1][-1],us_u_df.iloc[:,1][-1],color='blue',alpha=0.7,fontsize=10)
axis_u[1].spines['top'].set_visible(False)
axis_u[1].legend(loc=1,fontsize=fontsize_legend)
axis_u[1].set_xlim(us_u_df.index[0],us_u_df.index[-1])
axis_u[0].set_ylim(100,1000)
axis_u[1].set_ylim(200,3000)
axis_u[1].spines['right'].set_color('blue')
axis_u[1].spines['left'].set_color('red')
fig_u.suptitle('美国失业金领取情况（千人）'+'('+us_u_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_u.tight_layout()
fig_u.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.5美国失业金领取情况（周度）.png'))
plt.close()

t=printtime(t)

#%%3.6贝弗里奇曲线（横轴失业率，纵轴职位空缺率）
df_b= pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="3.6贝弗里奇曲线")
us_b = w.edb(df_b.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
b_df = pd.DataFrame(us_b.Data,index=df_b.iloc[:,0].tolist(),columns=us_b.Times).T
us_u_df.to_excel(writer_job,'贝弗里奇曲线')

fig_b,ax_b=plt.subplots(nrows=1,ncols=1,sharex=True)
#2018/1-2020/3
#2020/4-2020/9
#2020/10-2021/6
#2021/7-
ax_b.scatter(b_df.iloc[:27,0], b_df.iloc[:27,1],color='blue',label='2018/1-2020/3')
ax_b.scatter(b_df.iloc[27:33,0], b_df.iloc[27:33,1],color='green',label='2020/4-2020/9')
ax_b.scatter(b_df.iloc[33:42,0], b_df.iloc[33:42,1],color='orange',label='2020/10-2021/6')
ax_b.scatter(b_df.iloc[42:,0], b_df.iloc[42:,1],color='red',label='2021/7-')
ax_b.scatter(b_df.iloc[-1,0], b_df.iloc[-1,1], s=150, color='red', edgecolors='black', linewidths=2)
for i in range(len(b_df) - 1):
    x1, y1 = b_df.iloc[i,0], b_df.iloc[i,1]
    x2, y2 = b_df.iloc[i+1,0], b_df.iloc[i+1,1]
    ax_b.annotate('', xy=(x2, y2), xytext=(x1, y1),
                  arrowprops=dict(arrowstyle="->", color="red", lw=0.5))
ax_b.set_xlabel('失业率')
ax_b.set_ylabel('职位空缺率')
ax_b.legend(loc=1,fontsize=fontsize_legend)
ax_b.spines['top'].set_visible(False)
ax_b.spines['right'].set_visible(False)
fig_b.suptitle('美国贝弗里奇曲线'+'('+b_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold')
fig_b.tight_layout()
fig_b.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.6贝弗里奇曲线.png'))
plt.close()

t=printtime(t)

#%%3.7就业人口比（就业人口占16岁及以上非机构居民人口的比重）
series_ids = ['EMRATIO']  #employment-population ratio
name_list =  ['就业人口比']
# 获取数据
data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)
    # 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_emratio = df.tail(75)
df_emratio.to_excel(writer_job, '就业人口比')
#画图
fig_em,ax_em=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_em.plot(df_emratio.index,df_emratio['就业人口比'],label='美国就业人口比',color='red')
ax_em.text(df_emratio.index[-1],df_emratio['就业人口比'][-1],round(df_emratio['就业人口比'][-1],1),color='red',alpha=0.7,fontsize=10)
ax_em.spines['top'].set_visible(False)
ax_em.spines['right'].set_visible(False)
ax_em.set_ylim(54,62)
ax_em.set_xlim(df_emratio.index[0],df_emratio.index[-1])
fig_em.suptitle('美国就业人口比'+'('+df_emratio.index[-1].strftime(datestyle1)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_em.tight_layout()
fig_em.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.7美国就业（就业人口比）.png'))
plt.close()

t=printtime(t)

#%%3.8离职率quit rate
series_ids = ['JTSQUR']
name_list =  ['离职率']
# 获取数据
data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)
    # 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_quit = df.tail(75)
df_quit.to_excel(writer_job, '离职率')
#画图
fig_quit,ax_quit=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_quit.plot(df_quit.index,df_quit['离职率'],label='离职率',color='red')
ax_quit.text(df_quit.index[-1],df_quit['离职率'][-1],round(df_quit['离职率'][-1],1),color='red',alpha=0.7,fontsize=10)
ax_quit.spines['top'].set_visible(False)
ax_quit.spines['right'].set_visible(False)
ax_quit.set_xlim(df_quit.index[0],df_quit.index[-1])
fig_quit.suptitle('美国劳动力市场离职率'+'('+df_quit.index[-1].strftime(datestyle1)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_quit.tight_layout()
fig_quit.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.8美国就业（离职率）.png'))
plt.close()

t=printtime(t)

#%%3.9雇佣率hiring rate
series_ids = ['JTSHIR']
name_list =  ['雇佣率']
# 获取数据
data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)
    # 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_employed = df.tail(75)
df_employed.to_excel(writer_job, '雇佣率')
#画图
fig_employed,ax_employed=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_employed.plot(df_employed.index,df_employed['雇佣率'],label='雇佣率',color='red')
ax_employed.text(df_employed.index[-1],df_employed['雇佣率'][-1],round(df_employed['雇佣率'][-1],1),color='red',alpha=0.7,fontsize=10)
ax_employed.spines['top'].set_visible(False)
ax_employed.spines['right'].set_visible(False)
ax_employed.set_xlim(df_employed.index[0],df_employed.index[-1])
fig_employed.suptitle('美国劳动力市场雇佣率'+'('+df_employed.index[-1].strftime(datestyle1)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_employed.tight_layout()
fig_employed.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.9美国就业（雇佣率）.png'))
plt.close()

t=printtime(t)

#%%3.10平民劳动力civilian labor force
series_ids = ['CLF16OV']
name_list =  ['平民劳动力']
# 获取数据
data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)
    # 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_civilian = df.tail(75)
df_civilian.to_excel(writer_job, '平民劳动力')
writer_job._save()
#画图
fig_civilian,ax_civilian=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_civilian.plot(df_civilian.index,df_civilian['平民劳动力'],label='平民劳动力',color='red')
ax_civilian.text(df_civilian.index[-1],df_civilian['平民劳动力'][-1],round(df_civilian['平民劳动力'][-1],1),color='red',alpha=0.7,fontsize=10)
ax_civilian.spines['top'].set_visible(False)
ax_civilian.spines['right'].set_visible(False)
ax_civilian.set_xlim(df_civilian.index[0],df_civilian.index[-1])
fig_civilian.suptitle('美国劳动力市场平民劳动力'+'('+df_civilian.index[-1].strftime(datestyle1)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_civilian.tight_layout()
fig_civilian.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.3.10美国就业（平民劳动力）.png'))
plt.close()

t=printtime(t)

print('就业数据完成!')

#%%4增长
writer_growth = pd.ExcelWriter(os.path.join(fp,'Result','1.19Oversea','1.19.4.0美国经济增长数据.xlsx'))

#%%4.1Markit PMI
df_data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="4.1Markit PMI")
us_markit = w.edb(df_data.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_markit_df = pd.DataFrame(us_markit.Data,index=df_data.iloc[:,0].tolist(),columns=us_markit.Times).T
us_markit_df.to_excel(writer_growth,"Markit PMI")

fig_pmi,ax_pmi=plt.subplots(nrows=1,ncols=1)
ax_pmi.plot(us_markit_df.index,us_markit_df["Markit制造业PMI"],label='制造业PMI',color='red')
ax_pmi.plot(us_markit_df.index,us_markit_df["Markit服务业PMI"],label='服务业PMI',color='blue')
ax_pmi.plot(us_markit_df.index,[50]*len(us_markit_df.index),color='grey',ls='--')
ax_pmi.text(us_markit_df.index[-1],us_markit_df["Markit制造业PMI"][-1],round(us_markit_df["Markit制造业PMI"][-1],1),color='red',fontsize=fontsize_legend)
ax_pmi.text(us_markit_df.index[-1],us_markit_df["Markit服务业PMI"][-1],round(us_markit_df["Markit服务业PMI"][-1],1),color='blue',fontsize=fontsize_legend)
ax_pmi.legend(loc=2,fontsize=fontsize_legend)
ax_pmi.spines['top'].set_visible(False)
ax_pmi.spines['right'].set_visible(False)
ax_pmi.set_xlim(us_markit_df.index[0],us_markit_df.index[-1])
fig_pmi.suptitle('美国Markit PMI'+'('+us_markit_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_pmi.tight_layout()
fig_pmi.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.1美国Markit PMI.png'))
plt.close()

t=printtime(t)

#%%4.2.1ISM PMI
df_data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="4.2ISM PMI")
us_ism = w.edb(df_data.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_ism_df = pd.DataFrame(us_ism.Data,index=df_data.iloc[:,0].tolist(),columns=us_ism.Times).T
us_ism_df.to_excel(writer_growth,"ISM PMI")

fig_pmi,ax_pmi=plt.subplots(nrows=1,ncols=1)
ax_pmi.plot(us_ism_df.index,us_ism_df["ISM制造业PMI"],label='制造业PMI',color='red')
ax_pmi.plot(us_ism_df.index,us_ism_df["ISM非制造业PMI"],label='非制造业PMI',color='blue')
ax_pmi.plot(us_ism_df.index,[50]*len(us_ism_df.index),color='grey',ls='--')
ax_pmi.text(us_ism_df.index[-1],us_ism_df["ISM制造业PMI"][-1],round(us_ism_df["ISM制造业PMI"][-1],1),color='red',fontsize=fontsize_legend)
ax_pmi.text(us_ism_df.index[-1],us_ism_df["ISM非制造业PMI"][-1],round(us_ism_df["ISM非制造业PMI"][-1],1),color='blue',fontsize=fontsize_legend)
ax_pmi.legend(loc=2,fontsize=fontsize_legend)
ax_pmi.spines['top'].set_visible(False)
ax_pmi.spines['right'].set_visible(False)
ax_pmi.set_xlim(us_markit_df.index[0],us_markit_df.index[-1])
fig_pmi.suptitle('美国ISM PMI'+'('+us_ism_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_pmi.tight_layout()
fig_pmi.savefig(os.path.join(fp,'Result','1.19Oversea','********.1美国ISM PMI.png'))
plt.close()

t=printtime(t)

#%%4.2.2ISM PMI制造业分项时间序列
us_pmi = w.edb("G0002323,G0008345,G0008346,G0008347,G0008348,G0008349","2015-01-01", enddate)
us_pmi_df = pd.DataFrame(us_pmi.Data,index=["ISM制造业PMI","新订单",'产出','就业','供应商交付','库存'],columns=us_pmi.Times).T
us_pmi_df.to_excel(writer_growth,"ISM PMI制造业分项")

fig_pmi,ax_pmi=plt.subplots(nrows=1,ncols=1)
ax_pmi.plot(us_pmi_df.index,us_pmi_df["新订单"],label='新订单',color='red')
ax_pmi.plot(us_pmi_df.index,us_pmi_df["产出"],label='产出',color='blue')
ax_pmi.plot(us_pmi_df.index,us_pmi_df["就业"],label='就业',color='green')
ax_pmi.plot(us_pmi_df.index,us_pmi_df["供应商交付"],label='供应商交付',color='purple')
ax_pmi.plot(us_pmi_df.index,us_pmi_df["库存"],label='库存',color='orange')
ax_pmi.plot(us_pmi_df.index,[50]*len(us_pmi_df.index),color='grey',ls='--')
ax_pmi.spines['top'].set_visible(False)
ax_pmi.spines['right'].set_visible(False)
ax_pmi.legend(loc=2,fontsize=fontsize_legend)
ax_pmi.set_xlim(us_pmi_df.index[0],us_pmi_df.index[-1])
fig_pmi.suptitle('美国ISM制造业PMI及分项'+'('+us_pmi_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_pmi.tight_layout()
fig_pmi.savefig(os.path.join(fp,'Result','1.19Oversea','********.2美国ISM制造业PMI分项时间序列.png'))
plt.close()

t=printtime(t)

#%%4.2.3ISM PMI制造业分项
name_color = ['blue', 'red', 'orange']
label_color = ['blue', 'red', 'red', 'red', 'red', 'red']
weights = ['(100%)', '(30%)', '(25%)', '(20%)', '(15%)', '(10%)']
label_name = []
for i in range(len(us_pmi_df.columns)):
    label_name.append(us_pmi_df.columns[i] + '\n' + weights[i])
name_pmi = us_pmi_df.columns.tolist()
subtitle_pmi = '↑持续上行：' + '、'.join(
    us_pmi_df.iloc[-1, :][(us_pmi_df.iloc[-1, :] > us_pmi_df.iloc[-2, :]) & (us_pmi_df.iloc[-2, :] > us_pmi_df.iloc[-3, :])].index.tolist()) + \
               '    ↓持续下行：' + '、'.join(
    us_pmi_df.iloc[-1, :][(us_pmi_df.iloc[-1, :] < us_pmi_df.iloc[-2, :]) & (us_pmi_df.iloc[-2, :] < us_pmi_df.iloc[-3, :])].index.tolist()) + \
               '\n↗边际上行：' + '、'.join(
    us_pmi_df.iloc[-1, :][(us_pmi_df.iloc[-1, :] > us_pmi_df.iloc[-2, :]) & (us_pmi_df.iloc[-2, :] <= us_pmi_df.iloc[-3, :])].index.tolist()) + \
               '    ↘边际下行：' + '、'.join(
    us_pmi_df.iloc[-1, :][(us_pmi_df.iloc[-1, :] < us_pmi_df.iloc[-2, :]) & (us_pmi_df.iloc[-2, :] >= us_pmi_df.iloc[-3, :])].index.tolist())

dim_pmi = len(name_pmi)
radians_pmi = np.linspace(0, 2 * np.pi, dim_pmi, endpoint=False)
radians_pmi = np.concatenate((radians_pmi, [radians_pmi[0]]))

fig_pmi, ax_pmi = plt.subplots(subplot_kw=dict(polar=True), figsize=[10, 9], dpi=400)
for j in range(1, 4):
    temp = us_pmi_df.iloc[-j, :]
    temp = np.concatenate((temp, [temp[0]]))
    ax_pmi.plot(radians_pmi, temp, color=name_color[j - 1], label=us_pmi_df.index[-j].strftime(datestyle1))
    ax_pmi.fill(radians_pmi, temp, color=name_color[j - 1], alpha=0.3)
    ax_pmi.set_xticks(radians_pmi[:-1])
    ax_pmi.set_xticklabels(label_name)
    ax_pmi.set_ylim(us_pmi_df.iloc[-3:, :].min().min() - 1, us_pmi_df.iloc[-3:, :].max().max() + 1)
    ax_pmi.legend(ncol=1, fontsize=fontsize_legend, bbox_to_anchor=(1.1, 1))
    ax_pmi.grid(alpha=0.3)
    ax_pmi.spines['polar'].set_visible(False)
for label, color in zip(ax_pmi.get_xticklabels(), label_color):
    label.set_color(color)
    if color == 'blue':
        label.set_fontsize(14)
    else:
        label.set_fontsize(12)
ax_pmi.set_title(subtitle_pmi, color='gray', fontsize=fontsize_legend)
fig_pmi.suptitle('美国ISM制造业PMI分项(' + us_pmi_df.index[-1].strftime(datestyle1) + ')', fontsize=fontsize_suptitle, fontweight='bold')
fig_pmi.tight_layout(rect=[0, 0.03, 1, 0.95])
fig_pmi.savefig(os.path.join(fp,'Result','1.19Oversea','********.3美国ISM制造业PMI分项网状图.png'))
plt.close()

t=printtime(t)

#%%4.3.1密歇根大学消费者信心
df_data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="4.3消费")
us_consumption = w.edb(df_data.index.tolist(), "2018-01-01",enddate,"Fill=Previous")
us_consumption_df = pd.DataFrame(us_consumption.Data,index=df_data.iloc[:,0].tolist(),columns=us_consumption.Times).T
us_consumption_df['消费支出同比'] = us_consumption_df["美国个人消费支出季调折年数"].pct_change(periods=12)*100
us_consumption_df['消费支出环比'] = us_consumption_df["美国个人消费支出季调折年数"].pct_change(periods=1)*100
us_consumption_df['可支配收入同比'] = us_consumption_df["美国个人可支配收入季调折年数"].pct_change(periods=12)*100
us_consumption_df['可支配收入环比'] = us_consumption_df["美国个人可支配收入季调折年数"].pct_change(periods=1)*100
us_consumption_df = us_consumption_df.dropna()
us_consumption_df.to_excel(writer_growth,"消费数据")

fig_confience,ax_confidence=plt.subplots(nrows=1,ncols=1,sharex=True)
ax_confidence.plot(us_consumption_df.index,us_consumption_df['密歇根大学消费者信心指数'],label='1966=100',color='red')
ax_confidence.text(us_consumption_df.index[-1],us_consumption_df["密歇根大学消费者信心指数"][-1],round(us_consumption_df["密歇根大学消费者信心指数"][-1],1),color='red',fontsize=fontsize_legend)
ax_confidence.legend(loc=2,fontsize=fontsize_legend)
ax_confidence.spines['top'].set_visible(False)
ax_confidence.spines['right'].set_visible(False)
ax_confidence.set_xlim(us_consumption_df.index[0],us_consumption_df.index[-1])
fig_confience.suptitle('美国密歇根大学消费者信心指数'+'('+us_consumption_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_confience.tight_layout()
fig_confience.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.3.1美国消费者信心指数.png'))
plt.close()

t=printtime(t)

#%%4.3.2美国个人收入与消费支出
fig_spending,ax_spending=plt.subplots(nrows=1,ncols=2)
ax_spending1=ax_spending[0].twinx()
axis_spending=[ax_spending[0],ax_spending1]
axis_spending[0].plot(us_consumption_df.index,us_consumption_df["可支配收入同比"],label='美国个人可支配收入同比',color='red')
axis_spending[0].text(us_consumption_df.index[-1],us_consumption_df["可支配收入同比"][-1],round(us_consumption_df["可支配收入同比"][-1],1),color='red',fontsize=fontsize_legend)
axis_spending[0].legend(loc=2,fontsize=fontsize_legend)
axis_spending[0].spines['top'].set_visible(False)
axis_spending[0].set_ylim(-10,20)
axis_spending[1].bar(us_consumption_df.index,us_consumption_df["可支配收入环比"],label='美国个人可支配收入环比',color='blue',width = 3)
axis_spending[1].text(us_consumption_df.index[-1],us_consumption_df["可支配收入环比"][-1],round(us_consumption_df["可支配收入环比"][-1],1),color='blue',fontsize=fontsize_legend)
axis_spending[1].spines['top'].set_visible(False)
axis_spending[1].spines['right'].set_color('blue')
axis_spending[1].spines['left'].set_color('red')
axis_spending[1].legend(loc=2,fontsize=fontsize_legend)
axis_spending[1].legend(loc=1,fontsize=fontsize_legend)
axis_spending[1].set_xlim(us_consumption_df.index[0],us_consumption_df.index[-1])
axis_spending[1].set_ylim(-10,15)

ax_spending1=ax_spending[1].twinx()
axis_spending=[ax_spending[1],ax_spending1]
axis_spending[0].plot(us_consumption_df.index,us_consumption_df["消费支出同比"],label='美国个人消费支出同比',color='red')
axis_spending[0].text(us_consumption_df.index[-1],us_consumption_df["消费支出同比"][-1],round(us_consumption_df["消费支出同比"][-1],1),color='red',fontsize=fontsize_legend)
axis_spending[0].legend(loc=2,fontsize=fontsize_legend)
axis_spending[0].spines['top'].set_visible(False)
axis_spending[0].set_ylim(-10,20)
axis_spending[1].bar(us_consumption_df.index,us_consumption_df["消费支出环比"],label='美国个人消费支出环比',color='blue',width = 3)
axis_spending[1].text(us_consumption_df.index[-1],us_consumption_df["消费支出环比"][-1],round(us_consumption_df["消费支出环比"][-1],1),color='blue',fontsize=fontsize_legend)
axis_spending[1].spines['top'].set_visible(False)
axis_spending[1].spines['right'].set_color('blue')
axis_spending[1].spines['left'].set_color('red')
axis_spending[1].legend(loc=2,fontsize=fontsize_legend)
axis_spending[1].legend(loc=1,fontsize=fontsize_legend)
axis_spending[1].set_xlim(us_consumption_df.index[0],us_consumption_df.index[-1])
axis_spending[1].set_ylim(-5,5)
fig_spending.suptitle('美国个人收入与消费支出'+'('+us_consumption_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_spending.tight_layout()
fig_spending.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.3.2美国个人收入与消费支出.png'))
plt.close()

t=printtime(t)

#%%4.3.3美国消费（零售销售）
fig_sales,ax_sales=plt.subplots(nrows=1,ncols=1)
ax_sales1=ax_sales.twinx()
axis_sales=[ax_sales,ax_sales1]
axis_sales[0].bar(us_consumption_df.index,us_consumption_df['美国零售和食品服务销售额环比'],color='red',label = '环比增速',width=3)
axis_sales[0].text(us_consumption_df.index[-1],us_consumption_df["美国零售和食品服务销售额环比"][-1],round(us_consumption_df["美国零售和食品服务销售额环比"][-1],1),color='red',fontsize=fontsize_legend)
axis_sales[0].legend(loc=2,fontsize=fontsize_legend)
axis_sales[0].spines['top'].set_visible(False)
axis_sales[0].set_ylim(-10,10)
axis_sales[1].plot(us_consumption_df.index,us_consumption_df['美国零售和食品服务销售额同比'],color='blue',label = '同比增速')
axis_sales[1].text(us_consumption_df.index[-1],us_consumption_df["美国零售和食品服务销售额同比"][-1],round(us_consumption_df["美国零售和食品服务销售额同比"][-1],1),color='blue',fontsize=fontsize_legend)
axis_sales[1].legend(loc=1,fontsize=fontsize_legend)
axis_sales[1].spines['top'].set_visible(False)
axis_sales[1].spines['right'].set_color('blue')
axis_sales[1].spines['left'].set_color('red')
axis_sales[1].set_xlim(us_consumption_df.index[0],us_consumption_df.index[-1])
axis_sales[1].set_ylim(-15,30)
fig_sales.suptitle('美国零售销售'+'('+us_consumption_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_sales.tight_layout()
fig_sales.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.3.3美国零售销售.png'))
plt.close()

t=printtime(t)

#%%4.4.1房地产销售
df_data = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="4.4地产")
us_housing = w.edb(df_data.index.tolist()[:2], "2018-01-01",enddate,"Fill=Previous")
us_housing_df = pd.DataFrame(us_housing.Data,index=df_data.iloc[:,0].tolist()[:2],columns=us_housing.Times).T
us_housing_df = us_housing_df.dropna()
us_housing_df.to_excel(writer_growth,"美国房地产销售")

fig_housing,ax_housing=plt.subplots(nrows=1,ncols=1)
ax_housing1=ax_housing.twinx()
axis_housing=[ax_housing,ax_housing1]
axis_housing[0].plot(us_housing_df.index,us_housing_df["美国成屋销售季调折年数"],label='成屋销售折年数（万套）',color='red')
axis_housing[0].text(us_housing_df.index[-1],us_housing_df["美国成屋销售季调折年数"][-1],round(us_housing_df["美国成屋销售季调折年数"][-1],1),color='red',fontsize=fontsize_legend)
axis_housing[0].legend(loc=2,fontsize=fontsize_legend)
axis_housing[0].spines['top'].set_visible(False)
axis_housing[1].plot(us_housing_df.index,us_housing_df["美国新建住房销售季调折年数"],label='新建住房销售折年数（千套）',color='blue')
axis_housing[1].text(us_housing_df.index[-1],us_housing_df["美国新建住房销售季调折年数"][-1],round(us_housing_df["美国新建住房销售季调折年数"][-1],1),color='blue',fontsize=fontsize_legend)
axis_housing[1].spines['top'].set_visible(False)
axis_housing[1].spines['right'].set_color('blue')
axis_housing[1].spines['left'].set_color('red')
axis_housing[1].legend(loc=2,fontsize=fontsize_legend)
axis_housing[1].legend(loc=1,fontsize=fontsize_legend)
axis_housing[1].set_xlim(us_housing_df.index[0],us_housing_df.index[-1])
fig_housing.suptitle('美国房地产销售情况'+'('+us_housing_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_housing.tight_layout()
fig_housing.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.4.1美国房地产销售.png'))
plt.close()

t=printtime(t)

#%%4.4.2房地产景气指数
#NAHB：反映了开发商对新屋独栋市场的前景预期（当下的新屋销售情况、未来6个月的新屋销售预期、以及潜在买家的看房情况），高于50表示新屋独栋市场景气度高，领先新屋开工数
us_housing = w.edb(df_data.index[2], "2018-01-01",enddate,"Fill=Previous")
us_housing_df = pd.DataFrame(us_housing.Data,index=[df_data.iloc[:,0][2]],columns=us_housing.Times).T
us_housing_df.to_excel(writer_growth,"美国NAHB住房指数")

fig_housing,ax_housing=plt.subplots(nrows=1,ncols=1)
ax_housing.plot(us_housing_df.index,us_housing_df["美国住房市场指数"],label='美国住房市场指数（NAHB）',color='red')
ax_housing.text(us_housing_df.index[-1],us_housing_df["美国住房市场指数"][-1],round(us_housing_df["美国住房市场指数"][-1],1),color='red',fontsize=fontsize_legend)
ax_housing.legend(loc=2,fontsize=fontsize_legend)
ax_housing.spines['top'].set_visible(False)
ax_housing.spines['right'].set_visible(False)
ax_housing.legend(loc=2,fontsize=fontsize_legend)
ax_housing.set_xlim(us_housing_df.index[0],us_housing_df.index[-1])
fig_housing.suptitle('美国房地产景气度情况（NAHB）'+'('+us_housing_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_housing.tight_layout()
fig_housing.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.4.2美国房地产景气度.png'))
plt.close()

t=printtime(t)

#%%4.4.3房地产抵押贷款利率
us_housing = w.edb(df_data.index[3], "2018-01-01",enddate,"Fill=Previous")
us_housing_df = pd.DataFrame(us_housing.Data,index=[df_data.iloc[:,0][3]],columns=us_housing.Times).T
us_housing_df.to_excel(writer_growth,"30年抵押贷款利率")
writer_growth._save()

fig_housing,ax_housing=plt.subplots(nrows=1,ncols=1)
ax_housing.plot(us_housing_df.index,us_housing_df["30年抵押贷款固定利率"],label='美国30年抵押贷款固定利率',color='red')
ax_housing.text(us_housing_df.index[-1],us_housing_df["30年抵押贷款固定利率"][-1],round(us_housing_df["30年抵押贷款固定利率"][-1],1),color='red',fontsize=fontsize_legend)
ax_housing.legend(loc=2,fontsize=fontsize_legend)
ax_housing.spines['top'].set_visible(False)
ax_housing.spines['right'].set_visible(False)
ax_housing.set_xlim(us_housing_df.index[0],us_housing_df.index[-1])
fig_housing.suptitle('美国房地产抵押贷款固定利率情况'+'('+us_housing_df.index[-1].strftime(datestyle)+')',fontsize=fontsize_suptitle,fontweight='bold',verticalalignment = 'center')
fig_housing.tight_layout()
fig_housing.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.4.4.3美国抵押贷款利率.png'))
plt.close()

t=printtime(t)

print('增长数据完成！')

#%%5总表
#%%5.1通胀数据
inflation = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='0.1总表_通胀')
us_inflation = w.edb(inflation.index.tolist(), startdate_3y,enddate)
inflation_df = pd.DataFrame(us_inflation.Data,index=inflation.iloc[:,0].tolist(),columns=us_inflation.Times).T
df = round(inflation_df,1)
arrow = []
for i in df.columns:
    if df[i][-1] > df[i][-2] and df[i][-2] > df[i][-3]:
        arrow.append('↑')
    elif df[i][-1] < df[i][-2] and df[i][-2] < df[i][-3]:
        arrow.append('↓')
    elif df[i][-1] > df[i][-2] and df[i][-2] <= df[i][-3]:
        arrow.append('↗')
    elif df[i][-1] < df[i][-2] and df[i][-2] >= df[i][-3]:
        arrow.append('↘')
    elif df[i][-1] == df[i][-2]:
        arrow.append('→')
    else:
        temp = df[i].shift(1)
        if temp[-1] > temp[-2] and temp[-2] > temp[-3]:
            arrow.append('↑')
        elif temp[-1] < temp[-2] and temp[-2] < temp[-3]:
            arrow.append('↓')
        elif temp[-1] > temp[-2] and temp[-2] <= temp[-3]:
            arrow.append('↗')
        elif temp[-1] < temp[-2] and temp[-2] >= temp[-3]:
            arrow.append('↘')
        elif temp[-1] == temp[-2]:
            arrow.append('→')

percentile_df = inflation_df.rank()/inflation_df.count()
percentile_df.iloc[-1] = percentile_df.iloc[-1].fillna(percentile_df.iloc[-2])
percentile = round(percentile_df.iloc[-1:,:]*100,1)  #当前近三年分位
inflation_df = pd.concat([inflation_df, percentile], axis=0)
inflation_df = inflation_df.T
index_list = inflation_df.columns.tolist()[:-1]
index_list.append('近三年分位')
inflation_df.columns = index_list
inflation_df = inflation_df.iloc[:, -13:]
inflation_df = round(inflation_df,1)
inflation_df = inflation_df.iloc[:, -4:]
diff = []
for i in range(len(inflation_df.iloc[:,-2])):
    if pd.isnull(inflation_df.iloc[:,-2][i]):
        diff.append(inflation_df.iloc[i,-3]-inflation_df.iloc[i,-4])
    else:
        diff.append(inflation_df.iloc[i,-2]-inflation_df.iloc[i,-3])
diff_mark = []
for i in diff:
    if i>0:
        diff_mark.append('↑')
    elif i<0:
        diff_mark.append('↓')
    else:
        diff_mark.append('→')
inflation_df['与上期变化'] = diff_mark
inflation_df = inflation_df.reset_index()
old_name = inflation_df.columns[0]
inflation_df.rename(columns={old_name: '指标名称'}, inplace=True)
inflation_df.fillna(' ', inplace=True)
inflation_df = inflation_df.iloc[:, :-1]   #先不要（与上期变化）这一列
inflation_df['方向'] = arrow

t=printtime(t)

#%%5.2就业数据
df_all= pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="0.2总表_就业")
all = w.edb(df_all.index.tolist(), "2021-01-01",enddate,"Fill=Previous")
df_all = pd.DataFrame(all.Data,index=df_all.iloc[:,0].tolist(),columns=all.Times).T
df_all['职位空缺与失业比'] = round(df_all['美国非农职位空缺数（季调）']/df_all['美国登记失业人数（季调）'],1)
df_all = df_all.drop(['美国非农职位空缺数（季调）','美国登记失业人数（季调）'],axis=1)
df_all =df_all.tail(36)

#FRED提取数据
series_ids = ['EMRATIO','JTSQUR','JTSHIR','CLF16OV']  #employment-population ratio,quit rate,hiring rate,civilian labor force
name_list =  ['就业人口比','离职率','雇佣率','平民劳动力']
# 获取数据
data = {}
for series_id in series_ids:
    data[series_id] = fred.get_series(series_id)
    # 转换为DataFrame
df = pd.DataFrame(data)
df.columns = name_list
df_emratio = df.tail(75)

df_emratio = df_emratio.tail(36)
df_all['就业人口比'] = df_emratio.iloc[:,0].tolist()
df_all['离职率'] = df_emratio.iloc[:,1].tolist()
df_all['雇佣率'] = df_emratio.iloc[:,2].tolist()
df_all['平民劳动力（百万）'] = round(df_emratio.iloc[:,3]/1000,1).tolist()


df_1 = df_all
arrow = []
for i in df_1.columns:
    if df_1[i][-1] > df_1[i][-2] and df_1[i][-2] > df_1[i][-3]:
        arrow.append('↑')
    elif df_1[i][-1] < df_1[i][-2] and df_1[i][-2] < df_1[i][-3]:
        arrow.append('↓')
    elif df_1[i][-1] > df_1[i][-2] and df_1[i][-2] <= df_1[i][-3]:
        arrow.append('↗')
    elif df_1[i][-1] < df_1[i][-2] and df_1[i][-2] >= df_1[i][-3]:
        arrow.append('↘')
    elif df_1[i][-1] == df_1[i][-2]:
        arrow.append('→')
    else:
        temp = df_1[i].shift(1)
        if temp[-1] > temp[-2] and temp[-2] > temp[-3]:
            arrow.append('↑')
        elif temp[-1] < temp[-2] and temp[-2] < temp[-3]:
            arrow.append('↓')
        elif temp[-1] > temp[-2] and temp[-2] <= temp[-3]:
            arrow.append('↗')
        elif temp[-1] < temp[-2] and temp[-2] >= temp[-3]:
            arrow.append('↘')
        elif temp[-1] == temp[-2]:
            arrow.append('→')

percentile_df = df_all.rank()/df_all.count()
percentile_df = percentile_df.fillna(method='ffill')
percentile = round(percentile_df.iloc[-1:,:]*100,1)  #当前近三年分位
employment_df = pd.concat([df_all, percentile], axis=0)
employment_df = employment_df.T
index_list = employment_df.columns.tolist()[:-1]
index_list.append('近三年分位')
employment_df.columns = index_list
employment_df = round(employment_df,1)
employment_df = employment_df.iloc[:, -5:]
employment_df = employment_df.reset_index()
old_name = employment_df.columns[0]
employment_df.rename(columns={old_name: '指标名称'}, inplace=True)
employment_df['方向'] = arrow
#就业+通胀数据
result = pd.concat([employment_df,inflation_df], ignore_index=True)

#%%5.3经济数据
df_all= pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name="0.3总表_经济")
all = w.edb(df_all.index.tolist(), "2021-01-01",enddate)
df_all = pd.DataFrame(all.Data,index=df_all.iloc[:,0].tolist(),columns=all.Times).T
df_all =df_all.tail(36)

df = df_all
arrow = []
for i in df.columns:
    temp = df[i].dropna()
    if temp[-1] > temp[-2] and temp[-2] > temp[-3]:
        arrow.append('↑')
    elif temp[-1] < temp[-2] and temp[-2] < temp[-3]:
        arrow.append('↓')
    elif temp[-1] > temp[-2] and temp[-2] <= temp[-3]:
        arrow.append('↗')
    elif temp[-1] < temp[-2] and temp[-2] >= temp[-3]:
        arrow.append('↘')
    else:
        arrow.append('→')

percentile_df = df_all.rank()/df_all.count()
percentile_df = percentile_df.fillna(method='ffill')
percentile = round(percentile_df.iloc[-1:,:]*100,1)  #当前近三年分位
econ_df = pd.concat([df_all, percentile], axis=0)
econ_df = econ_df.T
index_list = econ_df.columns.tolist()[:-1]
index_list.append('近三年分位')
econ_df.columns = index_list
econ_df = round(econ_df,1)
econ_df = econ_df.iloc[:, -6:]
econ_df = econ_df.reset_index()
old_name = econ_df.columns[0]
econ_df.rename(columns={old_name: '指标名称'}, inplace=True)
econ_df.fillna(' ', inplace=True)
econ_df['方向'] = arrow

final = pd.concat([econ_df, result], ignore_index=True)
sign = []
for i in final['方向']:
    if i == '↑':
        sign.append('++')
    elif i == '↓':
        sign.append('--')
    elif i == '↗':
        sign.append('+')
    elif i == '↘':
        sign.append('-')
    else:
        sign.append('=')

for i in range(len(final['指标名称'])):
    if final['指标名称'][i] in ['失业率','职位空缺与失业比','离职率']:
        if sign[i] == '+':
            sign[i] = '-'
        elif sign[i] == '++':
            sign[i] = '--'
        elif sign[i] == '-':
            sign[i] = '+'
        elif sign[i] == '--':
            sign[i] = '++'

#判断通胀对经济影响（以2.5%为界）
inflation = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='0.1总表_通胀')
us_inflation = w.edb(inflation.index.tolist(), startdate_3y,enddate)
inflation_df = pd.DataFrame(us_inflation.Data,index=inflation.iloc[:,0].tolist(),columns=us_inflation.Times).T

notes = [0]*8
for i in range(len(inflation_df.columns)):
    if inflation_df.columns[i] == 'PCE同比' and inflation_df['PCE同比'].dropna()[-1]>2.5:
        notes[0] = 1
        notes[2] = 1
    elif inflation_df.columns[i] == '核心PCE同比' and inflation_df['核心PCE同比'].dropna()[-1]>2.5:
        notes[1] = 1
        notes[3] = 1
    elif inflation_df.columns[i] == 'CPI同比' and inflation_df['CPI同比'].dropna()[-1]>2.5:
        notes[4] = 1
        notes[6] = 1
    elif inflation_df.columns[i] == '核心CPI同比' and inflation_df['核心CPI同比'].dropna()[-1]>2.5:
        notes[5] = 1
        notes[7] = 1

for i in range(len(notes)):
    if notes[i] == 1:
        if sign[i+19] == '+':
            sign[i+19] = '-'
        elif sign[i+19] == '++':
            sign[i+19] = '--'
        elif sign[i+19] == '-':
            sign[i+19] = '+'
        elif sign[i+19] == '--':
            sign[i+19] = '++'

final['对经济影响'] = sign

name_edited =[]
for i in final['指标名称']:
    if i in ['失业率','新增非农就业人数','PCE同比','核心PCE同比','PCE环比','核心PCE环比','CPI同比','CPI环比','核心CPI同比','核心CPI环比']:
        name_edited.append(i+'***')
    else:
        name_edited.append(i)
final['指标名称'] = name_edited
final = final.fillna('')

#%%5.4加入流动性数据
df_rate = pd.read_excel(os.path.join(fp,'Code','Oversea.xlsx'),index_col=0,header=None,sheet_name='0.4总表_流动性')
us_rate = w.edb(df_rate.index.tolist(), startdate_3y,enddate)
rate_df = pd.DataFrame(us_rate.Data,index=df_rate.iloc[:,0].tolist(),columns=us_rate.Times).T
#取每月最后一天
rate_df['date'] = pd.to_datetime(rate_df.index)
rate_df['month'] = rate_df['date'].dt.to_period('M')
df_month_end = rate_df.groupby('month').last().reset_index(drop=True)
df_month_end.set_index('date', inplace=True)

df_month_end =df_month_end.tail(36)
df_month_end = df_month_end.iloc[:-1]
df = df_month_end

arrow = []
for i in df.columns:
    temp = df[i].dropna()
    if temp[-1] > temp[-2] and temp[-2] > temp[-3]:
        arrow.append('↑')
    elif temp[-1] < temp[-2] and temp[-2] < temp[-3]:
        arrow.append('↓')
    elif temp[-1] > temp[-2] and temp[-2] <= temp[-3]:
        arrow.append('↗')
    elif temp[-1] < temp[-2] and temp[-2] >= temp[-3]:
        arrow.append('↘')
    else:
        arrow.append('→')

#df_month_end.index = df_month_end.index.strftime(datestyle)
percentile_df = df_month_end.rank()/df_month_end.count()
percentile_df = percentile_df.fillna(method='ffill')
percentile = round(percentile_df.iloc[-1:,:]*100,1)  #当前近三年分位
rate_df = pd.concat([df_month_end, percentile], axis=0)
rate_df = rate_df.T
index_list = rate_df.columns.tolist()[:-1]
index_list.append('近三年分位')
rate_df.columns = index_list
rate_df = rate_df.iloc[:, -6:]
rate_df = rate_df.reset_index()
old_name = rate_df.columns[0]
rate_df.rename(columns={old_name: '指标名称'}, inplace=True)
rate_df.fillna(' ', inplace=True)
rate_df['方向'] = arrow

sign = []
for i in rate_df['方向']:
    if i == '↑':
        sign.append('--')
    elif i == '↓':
        sign.append('++')
    elif i == '↗':
        sign.append('-')
    elif i == '↘':
        sign.append('+')
    else:
        sign.append('=')

rate_df['对经济影响'] = sign

rate_df.columns = final.columns
final1 = pd.concat([final,rate_df], ignore_index=True)    #与经济数据拼接
final2 = final1.drop(final1.columns[1],axis=1)
#改最后一列的名字
last_column = final2.columns[-1]
final2 = final2.rename(columns={last_column: '对资产定价影响'})

def macro_table(df,df1,title):
    fig, ax = plt.subplots(nrows=1, ncols=1, sharex=False)
    ax.axis('off')
    colortable = deepcopy(df1)
    for aa in list(range(len(colortable.index))):
        if aa in list(range(1, len(colortable.index), 2)):
            colortable.loc[colortable.index[aa]] = 'lightgray'
        else:
            colortable.loc[colortable.index[aa]] = 'white'
    colColours = ['lightsteelblue'] * len(colortable.columns)
    table = ax.table(cellText=df1.values,colLabels=df1.columns,bbox=(0, 0, 1, 1),cellLoc='center',loc='center',cellColours=colortable.values,colColours=colColours)
    # 设置表格样式
    table.auto_set_font_size(False)
    table.auto_set_column_width(col=list(range(len(df.columns))))
    cmap = plt.cm.get_cmap('RdYlGn')
    ax.set_ylim(0, 11)
    ax.set_xlim(0, 6)
    for (row, col), cell in table.get_celld().items():
        if ('↑' in str(df1.iloc[row-1, col])):
            cell.get_text().set_color('red')
        if ('↓' in str(df1.iloc[row-1, col])):
            cell.get_text().set_color('green')
        if ('+' in str(df1.iloc[row - 1, col])):
            cell.get_text().set_color('red')
        if ('-' in str(df1.iloc[row - 1, col])):
            cell.get_text().set_color('green')
        cell.set_fontsize(fontsize_text)
        if (col == 0)|(row == 0):
            cell.set_text_props(fontproperties=FontProperties(weight='bold', size=fontsize_text))
            cell.get_text().set_color('black')
        if (col in [5]) and (row != 0) :
            value = 1 - (0.25 + (df1.iloc[row - 1, col] - df.iloc[:, col].min(axis=0)) / (df.iloc[:, col].max(axis=0) - df.iloc[:, col].min(axis=0)) / 2)  # float(cell.get_text().get_text())
            color_val = cmap(value)
            cell.set_facecolor(color_val)

    df.fillna(' ', inplace=True)
    ax.axvline(4.36, lw=2, ls='-', c='blue')
    ax.axhline(3.35, lw=2, ls='-', c='blue')
    ax.axhline(5.87, lw=2, ls='-', c='blue')
    ax.axhline(7.92, lw=1, ls='-', c='orange')
    ax.set_title(title+'('+df.columns[-4].strftime(datestyle1)+')', fontsize=fontsize_subtitle, fontweight='bold', pad=5)
    fig.tight_layout()
    fig.savefig(os.path.join(fp,'Result','1.19Oversea','1.19.0美国经济总览表格图.png'))
    fig.savefig(os.path.join(fp,'Result','0.0WFR','1.19.0美国经济总览表格图.png'))
    return

macro_table(final2,final2,'美国经济总览')
plt.close()

t=printtime(t)

print('总表完成！')

printtime(1)
