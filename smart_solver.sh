#!/bin/bash

# 沪深300分析工具 - 智能问题解决脚本
# 自动检测问题并提供解决方案

echo "========================================"
echo "🔧 沪深300分析工具 - 智能问题诊断"
echo "========================================"

# 设置脚本目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        echo "请先安装Python3: https://www.python.org/downloads/"
        return 1
    fi
    
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_success "Python版本: $PYTHON_VERSION"
    return 0
}

# 检查依赖包
check_dependencies() {
    log_info "检查Python依赖包..."
    
    local missing_packages=()
    local packages=("pandas" "numpy" "matplotlib" "seaborn")
    
    for package in "${packages[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            log_success "$package 已安装"
        else
            log_warning "$package 未安装"
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -ne 0 ]; then
        log_info "正在安装缺失的包: ${missing_packages[*]}"
        pip3 install "${missing_packages[@]}" || {
            log_error "依赖包安装失败"
            return 1
        }
        log_success "依赖包安装完成"
    fi
    
    return 0
}

# 检查WindPy
check_windpy() {
    log_info "检查WindPy状态..."
    
    local windpy_status=$(python3 -c "
try:
    from WindPy import w
    print('imported')
except ImportError:
    print('not_installed')
except Exception as e:
    print(f'error:{e}')
" 2>/dev/null)
    
    case $windpy_status in
        "imported")
            log_success "WindPy导入成功"
            
            # 测试连接
            local connection_test=$(python3 -c "
try:
    from WindPy import w
    result = w.start()
    if hasattr(result, 'ErrorCode'):
        if result.ErrorCode == 0:
            print('connected')
        else:
            print(f'connection_failed:{result.ErrorCode}')
    else:
        print('connected')
    w.stop()
except Exception as e:
    print(f'connection_error:{e}')
" 2>/dev/null)
            
            case $connection_test in
                "connected")
                    log_success "Wind数据库连接正常"
                    return 0
                    ;;
                connection_failed:*)
                    log_error "Wind连接失败: ${connection_test#connection_failed:}"
                    return 2
                    ;;
                *)
                    log_error "Wind连接测试异常: $connection_test"
                    return 2
                    ;;
            esac
            ;;
        "not_installed")
            log_error "WindPy未安装"
            return 1
            ;;
        error:*)
            log_error "WindPy导入异常: ${windpy_status#error:}"
            return 1
            ;;
        *)
            log_error "WindPy状态检查异常"
            return 1
            ;;
    esac
}

# 修复WindPy问题
fix_windpy() {
    log_info "启动WindPy修复流程..."
    
    echo "请按以下步骤操作："
    echo "1. 确保Wind数据终端已启动并登录"
    echo "2. 访问 https://www.wind.com.cn/ 下载最新WindPy"
    echo "3. 卸载旧版本: pip3 uninstall WindPy"
    echo "4. 安装新版本WindPy"
    echo ""
    
    read -p "是否已完成WindPy重新安装？(y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        check_windpy
        return $?
    else
        log_warning "请完成WindPy安装后重新运行此脚本"
        return 1
    fi
}

# 运行主程序
run_main_analysis() {
    log_info "运行主分析程序..."
    
    if [ -f "hs300_analysis.py" ]; then
        python3 hs300_analysis.py
        local exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            log_success "分析完成！"
            return 0
        else
            log_error "分析过程中出现错误 (退出代码: $exit_code)"
            return $exit_code
        fi
    else
        log_error "主程序文件不存在: hs300_analysis.py"
        return 1
    fi
}

# 运行备用程序
run_backup_analysis() {
    log_info "运行备用分析程序（使用模拟数据）..."
    
    if [ -f "hs300_analysis_backup.py" ]; then
        python3 hs300_analysis_backup.py
        local exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            log_success "备用分析完成！"
            return 0
        else
            log_error "备用分析失败 (退出代码: $exit_code)"
            return $exit_code
        fi
    else
        log_error "备用程序文件不存在: hs300_analysis_backup.py"
        return 1
    fi
}

# 显示选项菜单
show_options() {
    echo ""
    echo "请选择操作："
    echo "1) 运行WindPy连接测试"
    echo "2) 重新安装WindPy"
    echo "3) 运行主分析程序（需要WindPy）"
    echo "4) 运行备用分析程序（使用模拟数据）"
    echo "5) 查看帮助信息"
    echo "6) 退出"
    echo ""
    read -p "请输入选项 (1-6): " choice
}

# 显示帮助信息
show_help() {
    echo ""
    echo "🔧 故障排除指南："
    echo "===================="
    echo ""
    echo "常见问题及解决方案："
    echo ""
    echo "1. WindPy导入失败"
    echo "   - 从官网重新下载安装WindPy"
    echo "   - 确保安装到正确的Python环境"
    echo ""
    echo "2. Wind连接失败"
    echo "   - 启动Wind数据终端并登录"
    echo "   - 检查网络连接"
    echo "   - 验证数据权限"
    echo ""
    echo "3. 依赖包问题"
    echo "   - 运行: pip3 install pandas numpy matplotlib seaborn"
    echo ""
    echo "4. 权限问题"
    echo "   - 运行: chmod +x *.sh"
    echo ""
    echo "5. 如果WindPy无法正常工作"
    echo "   - 可以使用备用程序查看分析功能演示"
    echo "   - 备用程序使用模拟数据，不需要WindPy"
    echo ""
    echo "技术支持："
    echo "- 确保Wind终端版本与WindPy版本兼容"
    echo "- 检查防火墙和网络设置"
    echo "- 联系Wind技术支持获取帮助"
    echo ""
}

# 主执行流程
main() {
    # 检查基础环境
    check_python || {
        log_error "Python环境检查失败，程序退出"
        exit 1
    }
    
    check_dependencies || {
        log_error "依赖包检查失败，程序退出"
        exit 1
    }
    
    # 检查WindPy状态
    check_windpy
    windpy_status=$?
    
    if [ $windpy_status -eq 0 ]; then
        log_success "所有检查通过，可以运行主分析程序"
        echo ""
        read -p "是否立即运行分析？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            run_main_analysis
            exit $?
        fi
    elif [ $windpy_status -eq 1 ]; then
        log_warning "WindPy未正确安装或配置"
    elif [ $windpy_status -eq 2 ]; then
        log_warning "WindPy已安装但连接失败"
    fi
    
    # 进入交互模式
    while true; do
        show_options
        
        case $choice in
            1)
                python3 test_windpy.py 2>/dev/null || log_error "测试脚本运行失败"
                ;;
            2)
                fix_windpy
                ;;
            3)
                check_windpy
                if [ $? -eq 0 ]; then
                    run_main_analysis
                else
                    log_error "WindPy连接失败，无法运行主程序"
                fi
                ;;
            4)
                run_backup_analysis
                ;;
            5)
                show_help
                ;;
            6)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选项，请重新选择"
                ;;
        esac
        
        echo ""
        read -p "按Enter键继续..." 
    done
}

# 运行主程序
main "$@"
