# -*- coding: utf-8 -*-
"""
沪深300指数个股超额收益率分布分析
适用于Wind数据终端
作者：基金经理
日期：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from WindPy import w
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

print("正在初始化Wind连接...")
# 初始化Wind连接
w.start()
print("Wind连接成功！")

# 设置时间范围 - 2025年至今
start_date = "2025-01-01"
end_date = "2025-07-31"  # 当前日期

print(f"数据时间范围：{start_date} 至 {end_date}")

# 获取沪深300成分股代码
print("正在获取沪深300成分股...")
constituents_data = w.wset("sectorconstituent", "date=" + end_date + ";sectorid=a001010100000000")
if constituents_data.ErrorCode != 0:
    print(f"获取成分股失败：{constituents_data.Data}")
    exit()

stock_codes = constituents_data.Data[1]  # 股票代码列表
stock_names = constituents_data.Data[2]  # 股票名称列表
print(f"成功获取{len(stock_codes)}只沪深300成分股")

# 验证成分股数量
if len(stock_codes) != 300:
    print(f"警告：成分股数量为{len(stock_codes)}只，不是预期的300只")
    print("这可能是由于：")
    print("1. 指数成分股调整期间")
    print("2. 部分股票停牌或退市")
    print("3. 数据获取问题")
else:
    print("✓ 确认获取到300只沪深300成分股")

# 获取期初价格数据（年初第一个交易日）
print("正在获取期初价格数据...")
start_prices_data = w.wss(stock_codes, "close", "tradeDate=" + start_date + ";priceAdj=U;cycle=D")
if start_prices_data.ErrorCode != 0:
    print("获取期初价格失败")
    exit()

# 获取期末价格数据（最新交易日）
print("正在获取期末价格数据...")
end_prices_data = w.wss(stock_codes, "close", "tradeDate=" + end_date + ";priceAdj=U;cycle=D")
if end_prices_data.ErrorCode != 0:
    print("获取期末价格失败")
    exit()

# 获取沪深300指数期初价格
print("正在获取沪深300指数期初价格...")
index_start_data = w.wss("000300.SH", "close", "tradeDate=" + start_date + ";priceAdj=U;cycle=D")
if index_start_data.ErrorCode != 0:
    print("获取指数期初价格失败")
    exit()

# 获取沪深300指数期末价格
print("正在获取沪深300指数期末价格...")
index_end_data = w.wss("000300.SH", "close", "tradeDate=" + end_date + ";priceAdj=U;cycle=D")
if index_end_data.ErrorCode != 0:
    print("获取指数期末价格失败")
    exit()

# 整理数据
print("正在整理数据...")
start_prices = start_prices_data.Data[0]  # 期初价格列表
end_prices = end_prices_data.Data[0]      # 期末价格列表
index_start_price = index_start_data.Data[0][0]  # 指数期初价格
index_end_price = index_end_data.Data[0][0]      # 指数期末价格

# 筛选有效数据的股票
valid_stocks = []
valid_names = []
valid_start_prices = []
valid_end_prices = []

for i, code in enumerate(stock_codes):
    if (start_prices[i] is not None and end_prices[i] is not None and 
        not pd.isna(start_prices[i]) and not pd.isna(end_prices[i]) and
        start_prices[i] > 0 and end_prices[i] > 0):
        valid_stocks.append(code)
        valid_names.append(stock_names[i])
        valid_start_prices.append(start_prices[i])
        valid_end_prices.append(end_prices[i])

print(f"成功获取{len(valid_stocks)}只股票的有效价格数据")
print(f"数据期间：{start_date} 至 {end_date}")

# 检查数据完整性
if len(valid_stocks) < 280:
    print(f"警告：有效股票数量({len(valid_stocks)})少于280只，可能存在数据问题")
elif len(valid_stocks) < 300:
    print(f"注意：有{300 - len(valid_stocks)}只股票数据缺失")
else:
    print("✓ 所有300只成分股数据完整")

# 计算期间总收益率
print("正在计算期间总收益率...")
stock_total_returns = [(end_price / start_price - 1) * 100 
                      for start_price, end_price in zip(valid_start_prices, valid_end_prices)]
stock_total_returns = pd.Series(stock_total_returns, index=valid_stocks)

# 计算指数收益率
index_total_return = (index_end_price / index_start_price - 1) * 100

# 计算超额收益率
excess_returns = stock_total_returns - index_total_return

print(f"沪深300指数期间收益率：{index_total_return:.2f}%")
print(f"个股超额收益率统计：")
print(f"  平均值：{excess_returns.mean():.2f}%")
print(f"  中位数：{excess_returns.median():.2f}%")
print(f"  标准差：{excess_returns.std():.2f}%")
print(f"  最大值：{excess_returns.max():.2f}%")
print(f"  最小值：{excess_returns.min():.2f}%")

# 找出表现最好和最差的股票
best_stock_idx = excess_returns.idxmax()
worst_stock_idx = excess_returns.idxmin()
best_stock_name = valid_names[valid_stocks.index(best_stock_idx)]
worst_stock_name = valid_names[valid_stocks.index(worst_stock_idx)]

print(f"\n表现最好：{best_stock_name}({best_stock_idx}) 超额收益率：{excess_returns[best_stock_idx]:.2f}%")
print(f"表现最差：{worst_stock_name}({worst_stock_idx}) 超额收益率：{excess_returns[worst_stock_idx]:.2f}%")

# 创建图表
print("正在生成图表...")
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle(f'沪深300个股超额收益率分布分析 ({start_date} 至 {end_date})', fontsize=16, fontweight='bold')

# 1. 直方图
axes[0, 0].hist(excess_returns, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
axes[0, 0].axvline(excess_returns.mean(), color='red', linestyle='--', linewidth=2, label=f'均值: {excess_returns.mean():.2f}%')
axes[0, 0].axvline(excess_returns.median(), color='orange', linestyle='--', linewidth=2, label=f'中位数: {excess_returns.median():.2f}%')
axes[0, 0].set_xlabel('超额收益率 (%)')
axes[0, 0].set_ylabel('频数')
axes[0, 0].set_title('超额收益率分布直方图')
axes[0, 0].legend()
axes[0, 0].grid(True, alpha=0.3)

# 2. 箱线图
box_plot = axes[0, 1].boxplot(excess_returns, patch_artist=True)
box_plot['boxes'][0].set_facecolor('lightblue')
axes[0, 1].set_ylabel('超额收益率 (%)')
axes[0, 1].set_title('超额收益率箱线图')
axes[0, 1].grid(True, alpha=0.3)

# 3. 分位数图
sorted_returns = np.sort(excess_returns)
quantiles = np.linspace(0, 100, len(sorted_returns))
axes[1, 0].plot(quantiles, sorted_returns, 'b-', linewidth=2)
axes[1, 0].axhline(0, color='red', linestyle='--', alpha=0.7, label='零超额收益率')
axes[1, 0].set_xlabel('分位数 (%)')
axes[1, 0].set_ylabel('超额收益率 (%)')
axes[1, 0].set_title('超额收益率分位数图')
axes[1, 0].legend()
axes[1, 0].grid(True, alpha=0.3)

# 4. 正负收益分布饼图
positive_count = (excess_returns > 0).sum()
negative_count = (excess_returns <= 0).sum()
labels = [f'正超额收益\n({positive_count}只)', f'负超额收益\n({negative_count}只)']
sizes = [positive_count, negative_count]
colors = ['lightgreen', 'lightcoral']

axes[1, 1].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
axes[1, 1].set_title('正负超额收益股票占比')

plt.tight_layout()
plt.show()

# 输出详细统计信息
print("\n" + "="*60)
print("详细统计信息")
print("="*60)

# 分位数统计
percentiles = [5, 10, 25, 50, 75, 90, 95]
print("\n分位数统计：")
for p in percentiles:
    value = np.percentile(excess_returns, p)
    print(f"  {p:2d}%分位数: {value:6.2f}%")

# 区间分布统计
print("\n区间分布统计：")
ranges = [(-float('inf'), -20), (-20, -10), (-10, -5), (-5, 0), (0, 5), (5, 10), (10, 20), (20, float('inf'))]
range_labels = ['<-20%', '-20%~-10%', '-10%~-5%', '-5%~0%', '0%~5%', '5%~10%', '10%~20%', '>20%']

for i, (low, high) in enumerate(ranges):
    if low == -float('inf'):
        count = (excess_returns < high).sum()
    elif high == float('inf'):
        count = (excess_returns >= low).sum()
    else:
        count = ((excess_returns >= low) & (excess_returns < high)).sum()
    
    percentage = count / len(excess_returns) * 100
    print(f"  {range_labels[i]:>10s}: {count:3d}只 ({percentage:5.1f}%)")

# 保存结果到CSV
print("\n正在保存结果...")
results_df = pd.DataFrame({
    '股票代码': valid_stocks,
    '股票名称': valid_names,
    '期初价格': valid_start_prices,
    '期末价格': valid_end_prices,
    '期间收益率(%)': stock_total_returns.values,
    '超额收益率(%)': excess_returns.values
})

results_df = results_df.sort_values('超额收益率(%)', ascending=False)
output_file = f'/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Python/沪深300超额收益率分析_{start_date}_{end_date}.csv'
results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
print(f"结果已保存至：{output_file}")

print("\n分析完成！")
w.stop()
