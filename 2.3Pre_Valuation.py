#%%
from Basicfun import *
print(os.path.basename(__file__))
#%%
numday=0
date_fof_gzb=(enddatetime-dt.timed<PERSON>ta(0)).strftime(datestyle2)        #'20210205'
code_nalist=['204','131','019']
startdate_m=(enddatetime-dt.timedelta(5)).strftime(datestyle)

#%%
code_name=['基金编号','缺省组合','基金_直销','基金_天天代销','基金_盈米代销','基金_好买代销']
str_name=['产品代码']+code_name
data_product=pd.read_excel(os.path.join(fp,'code','sp.xlsx'),sheet_name='产品列表',header=0,dtype=dict(zip(str_name,[str]*len(str_name))))
fullname_origin = data_product[data_product['产品形态'] == 'FOF']['产品全称'].tolist()
fullname_origin1 = data_product[data_product['产品形态'] == 'FOF']['产品全称1'].tolist()
simplename_origin = data_product[data_product['产品形态'] == 'FOF']['产品简称'].tolist()
fullname_zip=dict(zip(simplename_origin,fullname_origin))
fullname_zip1=dict(zip(simplename_origin,fullname_origin1))
def fullname(simplename,date):
    date=dt.datetime.strptime(date,datestyle2).date()
    if date <= dt.date(2022, 2, 10):
        fullname_FU = fullname_zip[simplename]
    elif date >= dt.date(2022, 11, 24):
        fullname_FU = fullname_zip1[simplename]
    else:
        fullname_FU = fullname_zip1[simplename]
        if simplename == '睿智进取':
            fullname_FU = '睿智进取一年封闭运作股票（FOF-LOF）'
    return fullname_FU

fofcode=data_product[data_product['终止日期']=='长期']['产品代码'].tolist()
fofoverdate=data_product[data_product['终止日期']=='长期']['终止日期'].tolist()

#%%标准组合预估值(按比例)
Array_kv=dict()
try:
    writer=pd.ExcelWriter(os.path.join(fp,'Result','2.3Pre_Valuation','2.3.0Pre_Valuation.xlsx'),engine='xlsxwriter')
except:
    writer=pd.ExcelWriter(os.path.join(fp,'Result','2.3Pre_Valuation','2.3.0Pre_Valuation1.xlsx'),engine='xlsxwriter')
#%%
fund_index=w.wsq('8841141.WI', "rt_pct_chg",usedf=True)[1]*100*100
fund_index.columns=['实时预估值']
fund_index[str(numday)+'日实际涨跌幅']=w.wss('8841141.WI',"pct_chg",tradeDate=enddate,usedf=True)[1]*100
fund_index.index=['基金重仓指数']
print(fund_index.round(2))
t=printtime(t)

name_fund=['万得全A','偏股基金','偏股混合型基金','偏债混合型基金','混合债券型二级基金']
para_est_fund=["rt_pct_chg"]+["rt_estimated_chg"]*4
para_real_fund=["pct_chg"]+["NAV_return_nd"]*4
for m in range(len(name_fund)):
    est_fund = pd.DataFrame()
    fund_details=w.wset("sectorconstituent",date=enddate,windcode=market[name_fund[m]],usedf=True)[1]
    for i in range(int(np.ceil(len(fund_details.index)/500))):
        est_fund_temp = w.wsq(fund_details['wind_code'].tolist()[i*500:(i+1)*500],para_est_fund[m],usedf=True)[1]*100*100
        est_fund_temp.columns=['实时预估值']
        est_fund_temp[str(numday)+'日实际涨跌幅'] = w.wss(fund_details['wind_code'].tolist()[i*500:(i+1)*500],para_real_fund[m],ndays=-numday,tradeDate=enddate,usedf=True)[1]*100
        est_fund = pd.concat([est_fund,est_fund_temp],axis=0,join='outer')
    est_fund_pct=est_fund.describe().iloc[4:7,:].iloc[::-1,:].round(2)
    est_fund_pct.index=[name_fund[m]+'_'+str(100-int(n[:2])) for n in est_fund_pct.index]
    print(est_fund_pct)
t=printtime(t)

est_standport=pd.DataFrame()
name_standport=['标准权益组合','标准固收组合']
for j in range(6):
    if j==5:
        data_standport_sub = pd.read_excel(os.path.join(fp,'Result','2.7Alloc','2.7.3Standport.xlsx'),sheet_name=name_standport[j//5],header=0,index_col=0).dropna(how='all')
    else:
        data_standport_sub = pd.read_excel(os.path.join(fp,'Result','2.7Alloc','2.7.3Standport.xlsx'),sheet_name=name_standport[j//5],header=0,index_col=0,usecols=list(range(4*j,4*j+4,1))).dropna(how='all')
        if j>0:
            data_standport_sub.columns=[i[:-2] for i in data_standport_sub.columns]
    code_fund_standport_sub=data_standport_sub.index[data_standport_sub.index.str.contains('OF')].tolist()
    code_stock_standport_sub=data_standport_sub.index[~data_standport_sub.index.str.contains('OF')].tolist()

    data_fund_standport_pct = pd.Series((w.wsq(code_fund_standport_sub,"rt_estimated_chg",usedf=True)[1]*100).iloc[:,0], index=code_fund_standport_sub, name='实时预估值')

    data_fund_standport_realpct = w.wss(code_fund_standport_sub,"NAV_return_nd",ndays=-numday,annualized=0,tradeDate=enddate,usedf=True)[1].fillna(0)
    data_fund_standport_realpct.columns=[str(numday)+'日实际涨跌幅']
    data_fund_standport_realpct['区间实际涨跌幅']=w.wss(code_fund_standport_sub,"NAV_adj_return",startDate=startdate_m,endDate=enddate).Data[0]
    data_fund_standport=pd.concat([data_standport_sub.loc[code_fund_standport_sub],data_fund_standport_pct,data_fund_standport_realpct],axis=1)

    if len(code_stock_standport_sub)!=0:
        data_stock_standport_pct=w.wsq(code_stock_standport_sub, "rt_pct_chg",usedf=True)[1]*100
        data_stock_standport_pct.columns=['实时预估值']
        data_stock_standport_realpct = w.wss(code_stock_standport_sub,"pct_chg_nd",days=-numday,tradeDate=enddate,usedf=True)[1].fillna(0)
        data_stock_standport_realpct.columns=[str(numday)+'日实际涨跌幅']
        data_stock_standport_realpct['区间实际涨跌幅']=w.wss(code_stock_standport_sub,"pct_chg_per",startDate=startdate_m,endDate=enddate).Data[0]
        data_stock_standport=pd.concat([data_standport_sub.loc[code_stock_standport_sub],data_stock_standport_pct,data_stock_standport_realpct],axis=1)

    data_standport=pd.DataFrame(pd.concat([data_fund_standport,data_stock_standport],axis=0,join='outer'),index=data_standport_sub.index)

    data_standport['实时贡献'] = data_standport['目标比例'].mul(data_standport['实时预估值'])
    data_standport[str(numday) + '日实际贡献'] = data_standport['目标比例'].mul(data_standport[str(numday) + '日实际涨跌幅'])
    data_standport['区间实际贡献'] = data_standport['目标比例'].mul(data_standport['区间实际涨跌幅'])
    pre_standport = data_standport['实时贡献'].sum()
    nd_standport = data_standport[str(numday) + '日实际贡献'].sum()
    est_standport.loc[name_standport[j//5]+'_'+str(25*j) if j!=5 else name_standport[j//5]+'_'+str(50),['实时预估值',str(numday)+'日实际涨跌幅']]=[pre_standport,nd_standport]
    data_standport=data_standport.round(2).sort_values(by='区间实际贡献' if dt.datetime.today().weekday() in [5, 6] else '实时贡献',ascending=False)
    Array_kv[name_standport[j//5]] = data_standport
    data_standport.to_excel(writer, sheet_name=name_standport[j//5]+'_'+str(25*j) if j!=5 else name_standport[j//5]+'_'+str(50), startcol=0, startrow=0, freeze_panes=[1, 1])
print(est_standport.applymap(lambda x:x.round(2)))

t=printtime(t)
#%%
#养老目标基金实时预估值(按估值表)
# est_fof=pd.DataFrame()
# for j in range(len(simplename_origin)):
#     if fofoverdate[j]=='长期':
#         data_fof_origin=pd.read_excel(os.path.join(fp_source,'基金估值-'+date_fof_gzb+'-'+'工银'+fullname(simplename_origin[j],date_fof_gzb)+'-'+fofcode[j]+'.xls'),sheet_name='Sheet1',header=4,index_col=0)
#         data_fof_pct=data_fof_origin.iloc[2:,:][['科目名称','市值占比']].dropna()
#         data_fof_pct['市值占比']=data_fof_pct['市值占比'].apply(lambda x:float(x.replace('%','')))

#         data_fof_fund = data_fof_pct.loc[[i for i in data_fof_pct.index if (len(str(i)) ==21 and i[-3:]=='OTC')]]   # 筛选基金代码
#         data_fof_fund.index=[i[-10:-4]+'.OF' for i in data_fof_fund.index]
#         data_fof_fund=pd.concat([data_fof_fund['科目名称'].drop_duplicates(),data_fof_fund['市值占比'].groupby(data_fof_fund.index).sum()],join='outer',axis=1)
#         if data_fof_fund.shape[0]!=0:
#             data_fof_fund_pct=pd.Series((w.wsq(data_fof_fund.index.tolist(),"rt_estimated_chg",usedf=True)[1]*100).iloc[:,0],index=data_fof_fund.index,name='实时预估值')

#             data_fof_fund_realpct = w.wss(data_fof_fund.index.tolist(),"NAV_return_nd",ndays=-numday,annualized=0,tradeDate=enddate,usedf=True)[1].fillna(0)
#             data_fof_fund_realpct.columns=[str(numday)+'日实际涨跌幅']
#             data_fof_fund_realpct['区间实际涨跌幅'] =w.wss(data_fof_fund.index.tolist(), "NAV_adj_return", startDate=startdate_m, endDate=enddate).Data[0]
#             data_fof_fund=pd.concat([data_fof_fund,data_fof_fund_pct,data_fof_fund_realpct],axis=1)
#         else:
#             data_fof_fund = pd.DataFrame([], columns=['科目名称','市值占比', '实时预估值',str(numday) + '日实际涨跌幅','区间实际涨跌幅'], dtype='float64')

#         data_fof_stock = data_fof_pct.loc[[i for i in data_fof_pct.index if len(str(i)) == 20]]  # 筛选股票代码
#         data_fof_stock.index=[i[-9:-3]+'.'+i[-2:] for i in data_fof_stock.index]
#         data_fof_stock=pd.concat([data_fof_stock['科目名称'].drop_duplicates(),data_fof_stock['市值占比'].groupby(data_fof_stock.index).sum()],join='outer',axis=1)
#         if data_fof_stock.shape[0]!=0:
#             data_fof_stock_pct=w.wsq(data_fof_stock.index.tolist(), "rt_pct_chg",usedf=True)[1].drop_duplicates()*100
#             data_fof_stock_pct.columns=['实时预估值']
#             for i in range(len(data_fof_stock_pct)):
#                 if data_fof_stock_pct.index[i][:3] in code_nalist:
#                     data_fof_stock_pct.loc[data_fof_stock_pct.index[i]] = 0
#             data_fof_stock_realpct = w.wss(data_fof_stock.index.tolist(),"pct_chg_nd",days=-numday,tradeDate=enddate,usedf=True)[1].fillna(0)
#             data_fof_stock_realpct.columns=[str(numday)+'日实际涨跌幅']
#             data_fof_stock_realpct['区间实际涨跌幅']=w.wss(data_fof_stock.index.tolist(),"pct_chg_per",startDate=startdate_m,endDate=enddate).Data[0]
#             data_fof_stock=pd.concat([data_fof_stock,data_fof_stock_pct,data_fof_stock_realpct],axis=1)
#         else:
#             data_fof_stock = pd.DataFrame([], columns=['科目名称','市值占比', '实时预估值',str(numday) + '日实际涨跌幅','区间实际涨跌幅'], dtype='float64')

#         data_fof=pd.concat([data_fof_fund,data_fof_stock],axis=0,join='outer').fillna(0)
#         data_fof['实时贡献']=data_fof['市值占比'].mul(data_fof['实时预估值'])
#         data_fof[str(numday)+'日实际贡献']=data_fof['市值占比'].div(data_fof[str(numday)+'日实际涨跌幅']/100+1).mul(data_fof[str(numday)+'日实际涨跌幅'])
#         data_fof['区间实际贡献']=data_fof['市值占比'].div(data_fof['区间实际涨跌幅']/100+1).mul(data_fof['区间实际涨跌幅'])
#         pre_fof=data_fof['实时贡献'].sum()
#         nd_fof=data_fof[str(numday)+'日实际贡献'].sum()

#         est_fof.loc[simplename_origin[j],['实时预估值',str(numday)+'日实际涨跌幅']]=[pre_fof,nd_fof]
#         data_fof=data_fof.round(2).sort_values(by='区间实际贡献' if dt.datetime.today().weekday() in [5,6] else '实时贡献',ascending=False)    #'实时贡献'
#         Array_kv[simplename_origin[j]]=data_fof
#         data_fof.to_excel(writer, sheet_name=simplename_origin[j], startcol=0, startrow=0, freeze_panes=[1, 1])
# print(est_fof.applymap(lambda x:x.round(2)))

# t=printtime(t)
# #%%
# #账户预估值(按份额)
# name_self=data_product[data_product['产品形态']=='Self']['产品简称'].tolist()
# for j in range(len(name_self)):
#     data_self = pd.read_excel(os.path.join(fp,'Code','Holding.xlsx'),sheet_name=name_self[j],header=0,index_col=0).dropna(how='all')
#     nav_self=w.wss(data_self.index.tolist(), "nav",tradeDate=enddate,usedf=True)[1]
#     data_self_pct = pd.Series((w.wsq(data_self.index.tolist(),"rt_estimated_chg",usedf=True)[1]*100).iloc[:,0], index=data_self.index, name='实时预估值')

#     data_self_realpct = w.wss(data_self.index.tolist(),"NAV_return_nd,return_1w",ndays=-numday,annualized=0,tradeDate=enddate,usedf=True)[1]
#     data_self_realpct.columns=[str(numday)+'日实际涨跌幅','5日实际涨跌幅']
#     data_self_realpct['区间实际涨跌幅'] = w.wss(data_self.index.tolist(), "NAV_adj_return", startDate=startdate_m, endDate=enddate).Data[0]

#     data_self=pd.concat([data_self,nav_self,data_self_pct,data_self_realpct],axis=1)
#     data_self['市值']=data_self['数量'].mul(data_self['NAV'])
#     data_self['市值占比']=data_self['市值']/(data_self['市值'].sum())*100
#     data_self['实时贡献']=data_self['市值占比'].mul(data_self['实时预估值'])
#     data_self[str(numday)+'日市值']=data_self['市值'].div(data_self[str(numday) + '日实际涨跌幅']/100+1)
#     data_self[str(numday)+'日市值占比']=data_self[str(numday)+'日市值']/data_self[str(numday)+'日市值'].sum()*100
#     data_self[str(numday)+'日实际贡献']=data_self[str(numday)+'日市值占比'].mul(data_self[str(numday) + '日实际涨跌幅'])
#     data_self['区间市值']=data_self['市值'].div(data_self['区间实际涨跌幅']/100+1)
#     data_self['区间市值占比']=data_self['区间市值']/data_self['区间市值'].sum()*100
#     data_self['区间实际贡献']=data_self['区间市值占比'].mul(data_self['区间实际涨跌幅'])
#     pre_self=data_self['市值占比'].mul(data_self['实时预估值']).sum()
#     nd_self=data_self[str(numday)+'日市值占比'].mul(data_self[str(numday)+'日实际涨跌幅']).sum()
#     navchg_self=data_self[str(numday)+'日市值'].mul(data_self[str(numday)+'日实际涨跌幅']).sum()/100
#     print([name_self[j],'实时预估值','%.2f'%pre_self,str(numday)+'日实际涨跌幅','%.2f'%nd_self,'市值',format(data_self['市值'].sum(),',.2f'),'变动',format(navchg_self,',.2f')])
#     data_self=data_self[['科目名称', '市值占比', '实时预估值', str(numday) + '日实际涨跌幅', '区间实际涨跌幅', '实时贡献', str(numday) + '日实际贡献', '区间实际贡献']].round(2).sort_values(by='区间实际贡献' if dt.datetime.today().weekday() in [5, 6] else '实时贡献', ascending=False)
#     Array_kv[name_self[j]]=data_self
#     data_self.to_excel(writer, sheet_name=name_self[j], startcol=0, startrow=0, freeze_panes=[1, 1])

excel_format(writer)

t=printtime(t)

#%%筛选>MA20的ETF
writer_ma20=pd.ExcelWriter(os.path.join(fp,'Result','2.3Pre_Valuation','2.3.1MA20.xlsx'),engine='xlsxwriter')
list_etf_stock=w.wset("sectorconstituent","date=%s;sectorid=1000009165000000;field=wind_code,sec_name"%enddate,usedf=True)[1]
list_etf_oversea=w.wset("sectorconstituent","date=%s;sectorid=1000009717000000;field=wind_code,sec_name"%enddate,usedf=True)[1]
list_etf_commodity=w.wset("sectorconstituent","date=%s;sectorid=1000010087000000;field=wind_code,sec_name"%enddate,usedf=True)[1]
list_etf=pd.concat([list_etf_stock,list_etf_oversea,list_etf_commodity],axis=0,join='outer')
code_etf=list_etf['wind_code'].tolist()
code_ma20=code_etf+code_market_index_IndexMarket[1:]+code_market_index_SectorZX+code_market_index_Sector2SW+code_market_fund_StockZX
data_ma20_0=w.wss(code_ma20, "sec_name,close,EXPMA,avg_amt_per",tradeDate=enddate,unit=1,priceAdj="F",cycle="D",EXPMA_N=20,startDate=startdate_1w,usedf=True)[1]       #获取所有FOF基金横截面数据
data_ma20_1=w.wss(code_ma20, "EXPMA",tradeDate=enddate,unit=1,priceAdj="F",cycle="D",EXPMA_N=5,startDate=startdate_1w,usedf=True)[1]       #获取所有FOF基金横截面数据
data_ma20=pd.concat([data_ma20_0,data_ma20_1],axis=1)
data_ma20.columns=['简称','收盘价','EXPMA20','日均成交额','EXPMA5']
data_ma20=data_ma20[['简称','日均成交额','EXPMA20','EXPMA5','收盘价']]
data_ma20['最新价']=w.wsq(code_ma20,"rt_latest").Data[0]
data_ma20_up=data_ma20[(data_ma20['收盘价']>data_ma20['EXPMA5'])  & (data_ma20['EXPMA5']>data_ma20['EXPMA20']) & (data_ma20['日均成交额']>2*(10**7))]
data_ma20_down=data_ma20[(data_ma20['收盘价']<data_ma20['EXPMA5'])  & (data_ma20['EXPMA5']<data_ma20['EXPMA20']) & (data_ma20['日均成交额']>2*(10**7))]
data_ma20_new_wait=data_ma20[(data_ma20['收盘价']>data_ma20['EXPMA5']) & (data_ma20['收盘价']<data_ma20['EXPMA20']) & (data_ma20['日均成交额']>2*(10**7))]
data_ma20_lost_wait=data_ma20[(data_ma20['收盘价']<data_ma20['EXPMA5'])  & (data_ma20['收盘价']>data_ma20['EXPMA20']) & (data_ma20['日均成交额']>2*(10**7))]
data_ma20_new_rt=data_ma20[(data_ma20['收盘价']<data_ma20['EXPMA20']) & (data_ma20['最新价']>data_ma20['EXPMA20']) & (data_ma20['日均成交额']>2*(10**7))]
data_ma20_lost_rt=data_ma20[(data_ma20['收盘价']>data_ma20['EXPMA20']) & (data_ma20['最新价']<data_ma20['EXPMA20']) & (data_ma20['日均成交额']>2*(10**7))]
data_ma20_up.to_excel(writer_ma20, sheet_name='上行趋势名单', startcol=0, startrow=0, freeze_panes=[1, 2])
data_ma20_down.to_excel(writer_ma20, sheet_name='下行趋势名单', startcol=0, startrow=0, freeze_panes=[1, 2])
data_ma20_new_wait.to_excel(writer_ma20, sheet_name='上行关注名单', startcol=0, startrow=0, freeze_panes=[1, 2])
data_ma20_lost_wait.to_excel(writer_ma20, sheet_name='下行关注名单', startcol=0, startrow=0, freeze_panes=[1, 2])
data_ma20_new_rt.to_excel(writer_ma20, sheet_name='新进MA20名单', startcol=0, startrow=0, freeze_panes=[1, 2])
data_ma20_lost_rt.to_excel(writer_ma20, sheet_name='跌落MA20名单', startcol=0, startrow=0, freeze_panes=[1, 2])

excel_format(writer_ma20)

print('跌破MA20:')
print(data_ma20_lost_rt)

print('站上MA20:')
print(data_ma20_new_rt)

t=printtime(t)

#%%重点池筛选
# pool_backup=pd.read_excel(os.path.join(fp,'Result','2.9FS','%s_qfp.xlsx'%lastpoolQ), sheet_name='基金池_简版', index_col=0,header=0)
# criterion_pool_basic=(pool_backup['是否为初始基金'] == '是')# & (pool_backup[assettype_name[1]]=='境内股市')
# pool_backup=pd.read_excel(os.path.join(fp,'Code','FS','2.8.0data_label_fs.xlsx'), sheet_name='基金池筛选', index_col=0,header=0)
# criterion_pool_basic=(pool_backup['是否为初始基金'] == '是') & (pool_backup[assettype_name[1]]=='境内股市')
# pool_backup=pool_backup[criterion_pool_basic]
# pool_selected=[pool_backup[assettype_name[1]].str.contains('|'.join(['行业','全市场']))]

# fund_pool=indicator(pool_selected.index.tolist(),'2024-01-01',enddate,'000852.SH',1.5)#'885001.WI')
# col_name=['简称']+assettype_name
# col_name=col_name[::-1]
# [fund_pool.insert(loc=0,column=i,value=pool_selected[i][fund_pool.index]) for i in col_name]
# group=fund_pool.groupby(by=assettype_name[3:5])[['ret','rebound','prd']].median(numeric_only=True).sort_values(by='ret',ascending=False)
# group0=fund_pool.groupby(by=assettype_name[3:5]).get_group(('A股金融周期行业','周期'))[['ret','rebound','prd']].sort_values(by='ret',ascending=False)
t=printtime(t)

printtime(1)