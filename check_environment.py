import sys
print("Python版本:", sys.version)

required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
missing_packages = []

for package in required_packages:
    try:
        __import__(package)
        print(f"✓ {package} 已安装")
    except ImportError:
        print(f"✗ {package} 未安装")
        missing_packages.append(package)

if missing_packages:
    print(f"需要安装: pip install {' '.join(missing_packages)}")
else:
    print("基础包检查完成！")

# 检查WindPy
try:
    import WindPy
    print("✓ WindPy 可导入")
except ImportError:
    print("✗ WindPy 未安装 - 需要Wind客户端支持")
