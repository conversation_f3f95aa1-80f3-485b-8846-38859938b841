# 沪深300超额收益率分析工具 - 使用说明

## 📁 文件说明

已为您创建了以下文件：
1. `hs300_analysis.py` - 主分析程序
2. `run_hs300_analysis.sh` - 一键运行脚本
3. `README.md` - 本使用说明

## 🚀 快速开始

### 方法1：使用一键脚本（推荐）

打开终端，执行以下命令：

```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Python"
./run_hs300_analysis.sh
```

### 方法2：直接运行Python脚本

```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Python"
python3 hs300_analysis.py
```

## 📋 前置条件

### 1. Wind数据终端
- 确保已安装Wind数据终端
- 确保Wind终端已登录且有有效权限
- 确保可以访问沪深300成分股数据

### 2. Python环境
- Python 3.7或更高版本
- 自动安装依赖包：pandas, numpy, matplotlib, seaborn

### 3. WindPy SDK
- 需要从万得官网下载安装：https://www.wind.com.cn/
- 脚本会自动检测是否已安装

## 📊 功能特性

### 数据获取
- 自动获取最新沪深300成分股列表（约300只股票）
- 获取2025年初至今的价格数据
- 自动计算个股和指数的日收益率

### 超额收益率计算
- 计算每只股票相对沪深300指数的超额收益率
- 生成年初至今累计超额收益率
- 提供详细的统计分析

### 可视化图表
生成6个分析图表：
1. **分布直方图** - 展示超额收益率整体分布
2. **箱线图** - 显示分位数和异常值
3. **前20名排行** - 表现最佳的股票
4. **后20名排行** - 表现最差的股票
5. **分位数分析** - 25%、50%、75%分位数对比
6. **密度分布** - 与正态分布对比

### 统计报告
- 样本基本信息（数量、时间范围等）
- 核心统计指标（均值、中位数、标准差等）
- 跑赢指数股票比例
- 表现最佳和最差的股票列表

## 📁 输出文件

所有结果将保存在：`~/Documents/沪深300超额收益率分析/`

包含文件：
- `沪深300超额收益率分析_YYYYMMDD.png` - 综合图表
- `统计报告_YYYYMMDD.txt` - 详细统计报告

## 🔧 使用步骤

1. **启动Wind终端**
   ```bash
   # 确保Wind数据终端已启动并登录
   ```

2. **运行分析**
   ```bash
   cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Python"
   ./run_hs300_analysis.sh
   ```

3. **查看进度**
   - 脚本会显示实时进度信息
   - 包括数据获取、计算和图表生成状态

4. **查看结果**
   - 图表会自动显示
   - 文件自动保存到Documents目录
   - 控制台显示统计摘要

## ⚠️ 故障排除

### WindPy连接问题诊断

**步骤1：运行连接测试**
```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Python"
python3 test_windpy.py
```

**常见错误及解决方案：**

#### 错误1: `module 'WindPy' has no attribute 'start'`
```
原因: WindPy导入方式错误或版本问题
解决方案:
1. 确认使用正确导入: from WindPy import w
2. 重新安装WindPy:
   - 卸载旧版本: pip3 uninstall WindPy
   - 从万得官网重新下载安装最新版本
3. 检查Wind终端版本匹配性
```

#### 错误2: `Wind数据库连接失败`
```
原因: Wind终端未启动或登录失败
解决方案:
1. 启动Wind终端并确保已登录
2. 检查Wind账户权限和数据订阅
3. 确认网络连接正常
4. 重启Wind终端后重试
5. 检查防火墙设置
```

#### 错误3: `ImportError: No module named 'WindPy'`
```
原因: WindPy未正确安装
解决方案:
1. 访问万得官网: https://www.wind.com.cn/
2. 下载最新版WindPy SDK
3. 确保安装到正确的Python环境
4. 验证安装: python3 -c "from WindPy import w; print('安装成功')"
```

### 其他常见问题

#### 依赖包问题
```bash
# 安装缺失的包
pip3 install pandas numpy matplotlib seaborn

# 如果pip3不可用，尝试
python3 -m pip install pandas numpy matplotlib seaborn
```

#### 权限问题
```bash
# 给脚本执行权限
chmod +x run_hs300_analysis.sh

# 检查文件权限
ls -la run_hs300_analysis.sh
```

#### 中文字体问题
```bash
# macOS安装中文字体
brew install --cask font-simsun

# 或者手动下载字体文件到 ~/Library/Fonts/
```

#### Python环境问题
```bash
# 检查Python版本
python3 --version

# 检查pip版本
pip3 --version

# 如果有多个Python版本，确保使用正确版本
which python3
```

## 🎯 自定义选项

可以修改 `hs300_analysis.py` 中的参数：

```python
# 修改分析时间范围
start_date = "2025-01-01"  # 开始日期
end_date = datetime.now().strftime("%Y-%m-%d")  # 结束日期

# 修改图表样式
plt.rcParams['font.size'] = 12  # 字体大小
colors = ['skyblue', 'lightgreen', 'lightcoral']  # 颜色方案

# 修改保存路径
save_dir = os.path.expanduser("~/Desktop/分析结果")
```

## 📈 结果解读

### 超额收益率含义
- 正值：跑赢沪深300指数
- 负值：跑输沪深300指数
- 数值大小：相对指数的超额/落后幅度

### 关键指标
- **平均超额收益率**：所有成分股的平均表现
- **中位数**：排除极值影响的中位表现
- **标准差**：收益率分散程度
- **跑赢比例**：超额收益为正的股票占比

### 投资启示
- 前20名：可关注的强势股票
- 后20名：需要规避的弱势股票
- 分布特征：判断市场分化程度
- 分位数分析：了解不同水平的收益区间

## 🛠️ 详细诊断流程

### 完整故障排除步骤

1. **运行连接测试**
   ```bash
   python3 test_windpy.py
   ```

2. **检查Wind终端状态**
   - 确保Wind终端已启动
   - 验证登录状态
   - 测试手动查询数据

3. **验证Python环境**
   ```bash
   python3 --version
   pip3 list | grep -E "pandas|numpy|matplotlib|seaborn"
   python3 -c "from WindPy import w; print('WindPy导入成功')"
   ```

4. **检查文件权限**
   ```bash
   ls -la *.py *.sh
   chmod +x run_hs300_analysis.sh
   ```

5. **如果仍有问题，收集以下信息**
   - Python版本: `python3 --version`
   - Wind终端版本
   - WindPy版本
   - macOS版本: `sw_vers`
   - 错误的完整输出

## 📞 技术支持

遇到问题时，请按以下顺序检查：
1. **运行测试脚本**: `python3 test_windpy.py`
2. **Wind数据权限和连接状态**
3. **Python环境和依赖包版本**
4. **文件路径和权限设置**
5. **网络连接稳定性**

### 应急处理方案
如果WindPy始终无法连接，可以考虑：
1. 使用其他数据源（如tushare、akshare）
2. 手动导入CSV数据文件
3. 联系Wind技术支持获取帮助

## 🔄 更新日志

- **v1.0** (2025-07-31)
  - 初始版本发布
  - 支持沪深300成分股分析
  - 完整的可视化和报告功能
  - 自动化数据获取和处理

---

**注意**: 本工具仅供研究和分析使用，不构成投资建议。投资有风险，决策需谨慎。
