#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪深300超额收益率分析 - 备用版本
不依赖WindPy，使用模拟数据进行演示
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import warnings

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

def generate_sample_data():
    """生成沪深300样本数据用于演示"""
    print("正在生成沪深300样本数据...")
    
    # 生成300只股票代码
    stock_codes = []
    for i in range(200):
        stock_codes.append(f"{str(i+1).zfill(6)}.SZ")
    for i in range(100):
        stock_codes.append(f"{str(i+600000).zfill(6)}.SH")
    
    # 生成日期序列（2025年初至今）
    start_date = datetime(2025, 1, 1)
    end_date = datetime.now()
    date_range = pd.date_range(start_date, end_date, freq='B')  # 工作日
    
    # 生成模拟价格数据
    np.random.seed(42)  # 确保结果可重现
    
    # 指数数据（沪深300）
    index_returns = np.random.normal(0.0003, 0.02, len(date_range))  # 日均收益率0.03%，标准差2%
    index_prices = 100 * np.cumprod(1 + index_returns)
    
    # 个股数据
    stock_data = {}
    for code in stock_codes:
        # 个股相对指数有不同的alpha和beta
        alpha = np.random.normal(0, 0.0005)  # 个股alpha
        beta = np.random.normal(1.0, 0.3)    # 个股beta
        
        # 个股收益率 = alpha + beta * 指数收益率 + 特异性风险
        stock_returns = alpha + beta * index_returns + np.random.normal(0, 0.015, len(date_range))
        stock_prices = 100 * np.cumprod(1 + stock_returns)
        stock_data[code] = stock_prices
    
    # 创建DataFrame
    stock_df = pd.DataFrame(stock_data, index=date_range)
    index_series = pd.Series(index_prices, index=date_range, name='HS300')
    
    print(f"生成数据完成：")
    print(f"- 股票数量: {len(stock_codes)}")
    print(f"- 交易日数量: {len(date_range)}")
    print(f"- 数据期间: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}")
    
    return stock_df, index_series, stock_codes

def calculate_returns_and_excess(stock_df, index_series):
    """计算收益率和超额收益率"""
    print("正在计算超额收益率...")
    
    # 计算收益率
    stock_returns = stock_df.pct_change().dropna()
    index_returns = index_series.pct_change().dropna()
    
    # 确保日期对齐
    common_dates = stock_returns.index.intersection(index_returns.index)
    stock_returns = stock_returns.loc[common_dates]
    index_returns = index_returns.loc[common_dates]
    
    # 计算超额收益率
    excess_returns = stock_returns.sub(index_returns, axis=0)
    
    # 计算累计超额收益率
    cumulative_excess_returns = (1 + excess_returns).cumprod() - 1
    ytd_excess_returns = cumulative_excess_returns.iloc[-1]
    
    print(f"超额收益率计算完成，共{len(ytd_excess_returns)}只股票")
    
    return excess_returns, ytd_excess_returns

def create_enhanced_visualizations(ytd_excess_returns, stock_codes):
    """创建增强版可视化图表"""
    try:
        # 创建保存目录
        save_dir = os.path.expanduser("~/Documents/沪深300超额收益率分析")
        os.makedirs(save_dir, exist_ok=True)
        
        # 创建更大的图表
        fig = plt.figure(figsize=(24, 18))
        
        # 1. 超额收益率分布直方图
        plt.subplot(3, 3, 1)
        n, bins, patches = plt.hist(ytd_excess_returns * 100, bins=50, alpha=0.7, 
                                   color='skyblue', edgecolor='black')
        # 根据收益率给直方图着色
        for i, patch in enumerate(patches):
            if bins[i] < 0:
                patch.set_facecolor('lightcoral')
            elif bins[i] > 0:
                patch.set_facecolor('lightgreen')
        
        plt.title('沪深300成分股年初至今超额收益率分布', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.ylabel('频数', fontsize=12)
        plt.axvline(ytd_excess_returns.mean() * 100, color='red', linestyle='--', 
                   label=f'均值: {ytd_excess_returns.mean()*100:.2f}%')
        plt.axvline(ytd_excess_returns.median() * 100, color='orange', linestyle='--', 
                   label=f'中位数: {ytd_excess_returns.median()*100:.2f}%')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 箱线图
        plt.subplot(3, 3, 2)
        box_plot = plt.boxplot(ytd_excess_returns * 100, patch_artist=True, 
                              labels=['沪深300成分股'])
        box_plot['boxes'][0].set_facecolor('lightblue')
        plt.title('超额收益率箱线图', fontsize=14, fontweight='bold')
        plt.ylabel('超额收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 3. 前20名
        plt.subplot(3, 3, 3)
        top_20 = ytd_excess_returns.nlargest(20)
        colors = ['darkgreen' if x > 0.1 else 'green' for x in top_20]
        y_pos = np.arange(len(top_20))
        bars = plt.barh(y_pos, top_20 * 100, color=colors, alpha=0.8)
        plt.title('超额收益率前20名', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.yticks(y_pos, [code[:6] for code in top_20.index], fontsize=9)
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width + 0.5, bar.get_y() + bar.get_height()/2, 
                    f'{width:.1f}%', ha='left', va='center', fontsize=8)
        plt.grid(True, alpha=0.3)
        
        # 4. 后20名
        plt.subplot(3, 3, 4)
        bottom_20 = ytd_excess_returns.nsmallest(20)
        colors = ['darkred' if x < -0.1 else 'red' for x in bottom_20]
        y_pos = np.arange(len(bottom_20))
        bars = plt.barh(y_pos, bottom_20 * 100, color=colors, alpha=0.8)
        plt.title('超额收益率后20名', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.yticks(y_pos, [code[:6] for code in bottom_20.index], fontsize=9)
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width - 0.5, bar.get_y() + bar.get_height()/2, 
                    f'{width:.1f}%', ha='right', va='center', fontsize=8)
        plt.grid(True, alpha=0.3)
        
        # 5. 分位数分析
        plt.subplot(3, 3, 5)
        quartiles = ytd_excess_returns.quantile([0.1, 0.25, 0.5, 0.75, 0.9])
        quartile_labels = ['10%', '25%', '50%\n(中位数)', '75%', '90%']
        colors = ['red', 'orange', 'gold', 'lightgreen', 'green']
        
        bars = plt.bar(quartile_labels, quartiles * 100, color=colors, alpha=0.8)
        plt.title('超额收益率分位数分析', fontsize=14, fontweight='bold')
        plt.ylabel('超额收益率 (%)', fontsize=12)
        plt.xlabel('分位数', fontsize=12)
        # 添加数值标签
        for bar, value in zip(bars, quartiles * 100):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5 if height > 0 else height - 1,
                    f'{value:.2f}%', ha='center', va='bottom' if height > 0 else 'top', 
                    fontweight='bold', fontsize=10)
        plt.grid(True, alpha=0.3)
        
        # 6. 概率密度分布对比
        plt.subplot(3, 3, 6)
        plt.hist(ytd_excess_returns * 100, bins=50, density=True, alpha=0.7, 
                color='skyblue', label='实际分布')
        
        # 添加正态分布对比
        mu, sigma = ytd_excess_returns.mean() * 100, ytd_excess_returns.std() * 100
        x = np.linspace(ytd_excess_returns.min() * 100, ytd_excess_returns.max() * 100, 100)
        normal_dist = (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu)/sigma)**2)
        plt.plot(x, normal_dist, 'r-', linewidth=2, label='正态分布')
        
        plt.title('概率密度分布对比', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.ylabel('概率密度', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 7. 收益率区间分析
        plt.subplot(3, 3, 7)
        bins = [-float('inf'), -20, -10, -5, 0, 5, 10, 20, float('inf')]
        labels = ['<-20%', '-20%~-10%', '-10%~-5%', '-5%~0%', '0%~5%', '5%~10%', '10%~20%', '>20%']
        counts = pd.cut(ytd_excess_returns * 100, bins=bins, labels=labels).value_counts()
        
        colors = ['darkred', 'red', 'lightcoral', 'orange', 'lightgreen', 'green', 'darkgreen', 'blue']
        wedges, texts, autotexts = plt.pie(counts.values, labels=counts.index, autopct='%1.1f%%',
                                          colors=colors, startangle=90)
        plt.title('超额收益率区间分布', fontsize=14, fontweight='bold')
        
        # 8. 累计超额收益率趋势（模拟前50只股票）
        plt.subplot(3, 3, 8)
        # 选择几只代表性股票显示趋势
        sample_stocks = ytd_excess_returns.nlargest(25).index.tolist() + ytd_excess_returns.nsmallest(25).index.tolist()
        
        # 这里我们用最终收益率反推趋势（简化处理）
        dates = pd.date_range('2025-01-01', datetime.now(), freq='W')
        for i, stock in enumerate(sample_stocks[:10]):  # 只显示10只避免图表过乱
            final_return = ytd_excess_returns[stock]
            # 模拟累计收益率趋势
            trend = np.cumsum(np.random.normal(final_return/len(dates), 0.01, len(dates)))
            plt.plot(dates, trend * 100, alpha=0.7, linewidth=1)
        
        plt.title('代表性股票超额收益率趋势', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('累计超额收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        # 9. 统计摘要表格
        plt.subplot(3, 3, 9)
        plt.axis('off')
        stats_data = [
            ['指标', '数值'],
            ['样本数量', f'{len(ytd_excess_returns)}只'],
            ['平均超额收益率', f'{ytd_excess_returns.mean()*100:.2f}%'],
            ['中位数收益率', f'{ytd_excess_returns.median()*100:.2f}%'],
            ['标准差', f'{ytd_excess_returns.std()*100:.2f}%'],
            ['最大收益率', f'{ytd_excess_returns.max()*100:.2f}%'],
            ['最小收益率', f'{ytd_excess_returns.min()*100:.2f}%'],
            ['正收益股票数', f'{len(ytd_excess_returns[ytd_excess_returns > 0])}只'],
            ['跑赢指数比例', f'{len(ytd_excess_returns[ytd_excess_returns > 0])/len(ytd_excess_returns)*100:.1f}%']
        ]
        
        table = plt.table(cellText=stats_data[1:], colLabels=stats_data[0],
                         cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(11)
        table.scale(1, 2)
        
        # 设置表格样式
        for i in range(len(stats_data)):
            for j in range(2):
                if i == 0:  # 表头
                    table[(i, j)].set_facecolor('#4CAF50')
                    table[(i, j)].set_text_props(weight='bold', color='white')
                else:
                    if i % 2 == 0:
                        table[(i, j)].set_facecolor('#f0f0f0')
        
        plt.title('统计指标摘要', fontsize=14, fontweight='bold', pad=20)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_path = os.path.join(save_dir, f'沪深300超额收益率分析_备用版_{timestamp}.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {chart_path}")
        
        # 显示图表
        plt.show()
        
        return save_dir
        
    except Exception as e:
        print(f"创建图表时出错: {e}")
        return None

def generate_detailed_report(ytd_excess_returns, stock_codes, save_dir):
    """生成详细分析报告"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(save_dir, f'详细分析报告_备用版_{timestamp}.txt')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("沪深300成分股超额收益率分析报告（演示版）\n")
            f.write("="*60 + "\n")
            f.write(f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
            f.write(f"分析期间: 2025年初至{datetime.now().strftime('%Y年%m月%d日')}\n")
            f.write("数据来源: 模拟数据（用于演示分析功能）\n\n")
            
            # 基本统计信息
            f.write("一、基本统计信息\n")
            f.write("-"*40 + "\n")
            f.write(f"样本股票数量: {len(ytd_excess_returns)}只\n")
            f.write(f"平均超额收益率: {ytd_excess_returns.mean()*100:.3f}%\n")
            f.write(f"中位数超额收益率: {ytd_excess_returns.median()*100:.3f}%\n")
            f.write(f"标准差: {ytd_excess_returns.std()*100:.3f}%\n")
            f.write(f"偏度: {ytd_excess_returns.skew():.3f}\n")
            f.write(f"峰度: {ytd_excess_returns.kurtosis():.3f}\n")
            f.write(f"最大超额收益率: {ytd_excess_returns.max()*100:.2f}%\n")
            f.write(f"最小超额收益率: {ytd_excess_returns.min()*100:.2f}%\n")
            f.write(f"收益率范围: {(ytd_excess_returns.max()-ytd_excess_returns.min())*100:.2f}%\n\n")
            
            # 分布特征分析
            f.write("二、分布特征分析\n")
            f.write("-"*40 + "\n")
            positive_count = len(ytd_excess_returns[ytd_excess_returns > 0])
            negative_count = len(ytd_excess_returns[ytd_excess_returns <= 0])
            f.write(f"正超额收益股票数量: {positive_count}只 ({positive_count/len(ytd_excess_returns)*100:.1f}%)\n")
            f.write(f"负超额收益股票数量: {negative_count}只 ({negative_count/len(ytd_excess_returns)*100:.1f}%)\n")
            
            # 分位数分析
            quartiles = ytd_excess_returns.quantile([0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95])
            f.write(f"\n分位数分析:\n")
            for q, value in quartiles.items():
                f.write(f"  {q*100:2.0f}%分位数: {value*100:6.2f}%\n")
            
            # 表现分组分析
            f.write(f"\n三、表现分组分析\n")
            f.write("-"*40 + "\n")
            
            bins = [-float('inf'), -0.2, -0.1, -0.05, 0, 0.05, 0.1, 0.2, float('inf')]
            labels = ['严重跑输(<-20%)', '大幅跑输(-20%~-10%)', '跑输(-10%~-5%)', 
                     '小幅跑输(-5%~0%)', '小幅跑赢(0%~5%)', '跑赢(5%~10%)', 
                     '大幅跑赢(10%~20%)', '严重跑赢(>20%)']
            
            group_counts = pd.cut(ytd_excess_returns, bins=bins, labels=labels).value_counts()
            for label, count in group_counts.items():
                f.write(f"  {label}: {count}只 ({count/len(ytd_excess_returns)*100:.1f}%)\n")
            
            # 前后排名
            f.write(f"\n四、表现排名\n")
            f.write("-"*40 + "\n")
            f.write("表现最佳前20名:\n")
            top_20 = ytd_excess_returns.nlargest(20)
            for i, (code, ret) in enumerate(top_20.items(), 1):
                f.write(f"  {i:2d}. {code}: {ret*100:6.2f}%\n")
            
            f.write(f"\n表现最差后20名:\n")
            bottom_20 = ytd_excess_returns.nsmallest(20)
            for i, (code, ret) in enumerate(bottom_20.items(), 1):
                f.write(f"  {i:2d}. {code}: {ret*100:6.2f}%\n")
            
            # 投资建议
            f.write(f"\n五、分析结论与建议\n")
            f.write("-"*40 + "\n")
            
            if ytd_excess_returns.mean() > 0:
                f.write("• 整体而言，沪深300成分股平均跑赢指数，显示个股分化明显\n")
            else:
                f.write("• 整体而言，沪深300成分股平均跑输指数，市场呈现分化特征\n")
                
            win_rate = positive_count / len(ytd_excess_returns)
            if win_rate > 0.6:
                f.write("• 多数股票跑赢指数，市场情绪相对乐观\n")
            elif win_rate < 0.4:
                f.write("• 多数股票跑输指数，市场面临调整压力\n")
            else:
                f.write("• 跑赢跑输股票数量相当，市场分化明显\n")
                
            if ytd_excess_returns.std() > 0.15:
                f.write("• 个股收益率分散度较高，选股重要性凸显\n")
            else:
                f.write("• 个股收益率相对集中，整体走势较为一致\n")
                
            f.write(f"\n注意: 本报告基于模拟数据生成，仅用于演示分析功能。\n")
            f.write(f"实际投资决策请基于真实市场数据进行分析。\n")
        
        print(f"详细报告已保存至: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        return None

def main():
    """主函数"""
    print("沪深300超额收益率分析工具 - 备用版本")
    print("="*60)
    print("注意: 此版本使用模拟数据演示分析功能")
    print("如需使用真实数据，请解决WindPy连接问题")
    print("="*60)
    
    try:
        # 1. 生成样本数据
        stock_df, index_series, stock_codes = generate_sample_data()
        
        # 2. 计算超额收益率
        excess_returns, ytd_excess_returns = calculate_returns_and_excess(stock_df, index_series)
        
        # 3. 创建可视化图表
        save_dir = create_enhanced_visualizations(ytd_excess_returns, stock_codes)
        if save_dir is None:
            print("图表创建失败")
            return
        
        # 4. 生成详细报告
        report_path = generate_detailed_report(ytd_excess_returns, stock_codes, save_dir)
        
        # 5. 控制台摘要
        print("\n" + "="*60)
        print("分析摘要（基于模拟数据）")
        print("="*60)
        print(f"样本数量: {len(ytd_excess_returns)}只股票")
        print(f"平均超额收益率: {ytd_excess_returns.mean()*100:.2f}%")
        print(f"中位数超额收益率: {ytd_excess_returns.median()*100:.2f}%")
        print(f"标准差: {ytd_excess_returns.std()*100:.2f}%")
        print(f"跑赢指数股票比例: {len(ytd_excess_returns[ytd_excess_returns > 0])/len(ytd_excess_returns)*100:.1f}%")
        print(f"最佳表现: {ytd_excess_returns.max()*100:.2f}%")
        print(f"最差表现: {ytd_excess_returns.min()*100:.2f}%")
        
        print(f"\n所有文件已保存至: {save_dir}")
        print("="*60)
        print("✅ 备用版分析完成！")
        print("如需使用真实Wind数据，请运行: ./fix_windpy.sh")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
