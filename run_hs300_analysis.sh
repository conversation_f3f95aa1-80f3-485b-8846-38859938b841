#!/bin/bash

# 沪深300超额收益率分析运行脚本
# 适用于macOS终端

echo "======================================"
echo "沪深300成分股超额收益率分析工具"
echo "======================================"

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PYTHON_SCRIPT="$SCRIPT_DIR/hs300_analysis.py"

# 检查Python环境
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

echo "Python3 版本: $(python3 --version)"

# 检查并安装必要的包
echo "检查Python依赖包..."
python3 -c "
import sys
required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
missing_packages = []

for package in required_packages:
    try:
        __import__(package)
        print(f'✓ {package} 已安装')
    except ImportError:
        missing_packages.append(package)
        print(f'✗ {package} 未安装')

# 检查WindPy
try:
    from WindPy import w
    print('✓ WindPy 已安装')
except ImportError:
    print('✗ WindPy 未安装')
    print('注意: WindPy需要从万得官网下载安装')
    print('请访问: https://www.wind.com.cn/ 下载WindPy')
    missing_packages.append('WindPy')
except Exception as e:
    print(f'⚠ WindPy 导入异常: {e}')
    print('可能需要重新安装WindPy或检查Wind终端连接')

if missing_packages and 'WindPy' not in missing_packages:
    print('正在安装缺少的包...')
    import subprocess
    for package in missing_packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f'✓ {package} 安装成功')
        except subprocess.CalledProcessError:
            print(f'✗ {package} 安装失败')
"

# 检查Python脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "错误: 未找到分析脚本文件 $PYTHON_SCRIPT"
    echo "请确保hs300_analysis.py文件在同一目录下"
    exit 1
fi

# 创建输出目录
OUTPUT_DIR="$HOME/Documents/沪深300超额收益率分析"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

# 运行分析
echo "======================================"
echo "开始运行分析..."
echo "======================================"

if python3 "$PYTHON_SCRIPT"; then
    echo "======================================"
    echo "✓ 分析完成！"
    echo "结果文件保存在: $OUTPUT_DIR"
    echo "======================================"
    
    # 询问是否打开结果目录
    read -p "是否打开结果目录？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open "$OUTPUT_DIR"
    fi
else
    echo "======================================"
    echo "✗ 分析过程中出现错误"
    echo "请检查Wind数据库连接和权限"
    echo "======================================"
    exit 1
fi
