#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪深300指数成分股超额收益率分析
作者: 基金经理
日期: 2025年
"""

from WindPy import w
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import warnings

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

def connect_wind():
    """连接Wind数据库"""
    try:
        w.start()
        print("Wind数据库连接成功")
        return True
    except Exception as e:
        print(f"Wind数据库连接失败: {e}")
        return False

def get_hs300_constituents():
    """获取沪深300成分股代码"""
    try:
        # 获取最新的沪深300成分股
        data = w.wset("sectorconstituent", "date=2025-07-31;sectorid=a001010100000000")
        if data.ErrorCode != 0:
            print(f"获取成分股失败: {data.Data}")
            return None
        
        constituents = data.Data[1]  # 股票代码列表
        print(f"成功获取{len(constituents)}只沪深300成分股")
        return constituents
    except Exception as e:
        print(f"获取成分股代码时出错: {e}")
        return None

def calculate_returns(stock_codes, start_date, end_date):
    """计算个股和指数收益率"""
    try:
        # 获取个股价格数据
        print("正在获取个股价格数据...")
        stock_data = w.wsd(stock_codes, "close", start_date, end_date, "")
        if stock_data.ErrorCode != 0:
            print(f"获取个股数据失败: {stock_data.Data}")
            return None, None
        
        # 获取沪深300指数数据
        print("正在获取沪深300指数数据...")
        index_data = w.wsd("000300.SH", "close", start_date, end_date, "")
        if index_data.ErrorCode != 0:
            print(f"获取指数数据失败: {index_data.Data}")
            return None, None
        
        # 构建数据框
        dates = stock_data.Times
        stock_prices = pd.DataFrame(stock_data.Data, 
                                  index=stock_codes, 
                                  columns=dates).T
        
        index_prices = pd.Series(index_data.Data[0], 
                               index=dates, 
                               name='HS300')
        
        # 计算收益率
        stock_returns = stock_prices.pct_change().dropna()
        index_returns = index_prices.pct_change().dropna()
        
        print(f"数据期间: {stock_returns.index[0].strftime('%Y-%m-%d')} 至 {stock_returns.index[-1].strftime('%Y-%m-%d')}")
        print(f"有效交易日数量: {len(stock_returns)}")
        
        return stock_returns, index_returns
        
    except Exception as e:
        print(f"计算收益率时出错: {e}")
        return None, None

def calculate_excess_returns(stock_returns, index_returns):
    """计算超额收益率"""
    try:
        # 确保日期对齐
        common_dates = stock_returns.index.intersection(index_returns.index)
        stock_returns_aligned = stock_returns.loc[common_dates]
        index_returns_aligned = index_returns.loc[common_dates]
        
        # 计算超额收益率
        excess_returns = stock_returns_aligned.sub(index_returns_aligned, axis=0)
        
        # 计算累计超额收益率
        cumulative_excess_returns = (1 + excess_returns).cumprod() - 1
        
        # 计算年初至今超额收益率
        ytd_excess_returns = cumulative_excess_returns.iloc[-1]
        
        print(f"计算完成，共{len(ytd_excess_returns)}只个股的超额收益率")
        
        return excess_returns, ytd_excess_returns
        
    except Exception as e:
        print(f"计算超额收益率时出错: {e}")
        return None, None

def create_visualizations(ytd_excess_returns, excess_returns):
    """创建可视化图表"""
    try:
        # 创建图表目录
        save_dir = os.path.expanduser("~/Documents/沪深300超额收益率分析")
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置图表大小
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 超额收益率分布直方图
        plt.subplot(2, 3, 1)
        plt.hist(ytd_excess_returns * 100, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('沪深300成分股年初至今超额收益率分布', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.ylabel('频数', fontsize=12)
        plt.axvline(ytd_excess_returns.mean() * 100, color='red', linestyle='--', 
                   label=f'均值: {ytd_excess_returns.mean()*100:.2f}%')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 箱线图
        plt.subplot(2, 3, 2)
        box_plot = plt.boxplot(ytd_excess_returns * 100, patch_artist=True)
        box_plot['boxes'][0].set_facecolor('lightblue')
        plt.title('超额收益率箱线图', fontsize=14, fontweight='bold')
        plt.ylabel('超额收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 3. 前20名和后20名
        plt.subplot(2, 3, 3)
        top_20 = ytd_excess_returns.nlargest(20)
        bottom_20 = ytd_excess_returns.nsmallest(20)
        
        y_pos = np.arange(len(top_20))
        plt.barh(y_pos, top_20 * 100, color='green', alpha=0.7)
        plt.title('超额收益率前20名', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.yticks(y_pos, [code[:6] for code in top_20.index], fontsize=8)
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 3, 4)
        y_pos = np.arange(len(bottom_20))
        plt.barh(y_pos, bottom_20 * 100, color='red', alpha=0.7)
        plt.title('超额收益率后20名', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.yticks(y_pos, [code[:6] for code in bottom_20.index], fontsize=8)
        plt.grid(True, alpha=0.3)
        
        # 4. 超额收益率分位数分析
        plt.subplot(2, 3, 5)
        quartiles = ytd_excess_returns.quantile([0.25, 0.5, 0.75])
        quartile_labels = ['25%分位数', '50%分位数(中位数)', '75%分位数']
        colors = ['lightcoral', 'gold', 'lightgreen']
        
        plt.bar(quartile_labels, quartiles * 100, color=colors, alpha=0.8)
        plt.title('超额收益率分位数分析', fontsize=14, fontweight='bold')
        plt.ylabel('超额收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, v in enumerate(quartiles * 100):
            plt.text(i, v + 0.5, f'{v:.2f}%', ha='center', fontweight='bold')
        
        # 5. 密度图
        plt.subplot(2, 3, 6)
        plt.hist(ytd_excess_returns * 100, bins=50, density=True, alpha=0.7, 
                color='skyblue', label='实际分布')
        
        # 添加正态分布对比
        mu, sigma = ytd_excess_returns.mean() * 100, ytd_excess_returns.std() * 100
        x = np.linspace(ytd_excess_returns.min() * 100, ytd_excess_returns.max() * 100, 100)
        normal_dist = (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu)/sigma)**2)
        plt.plot(x, normal_dist, 'r-', linewidth=2, label='正态分布对比')
        
        plt.title('超额收益率概率密度分布', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.ylabel('概率密度', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(save_dir, f'沪深300超额收益率分析_{datetime.now().strftime("%Y%m%d")}.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {chart_path}")
        
        # 显示图表
        plt.show()
        
        return save_dir
        
    except Exception as e:
        print(f"创建图表时出错: {e}")
        return None

def generate_summary_report(ytd_excess_returns, save_dir):
    """生成汇总报告"""
    try:
        # 统计分析
        stats = {
            '样本数量': len(ytd_excess_returns),
            '平均超额收益率': f"{ytd_excess_returns.mean()*100:.2f}%",
            '中位数超额收益率': f"{ytd_excess_returns.median()*100:.2f}%",
            '标准差': f"{ytd_excess_returns.std()*100:.2f}%",
            '最大超额收益率': f"{ytd_excess_returns.max()*100:.2f}%",
            '最小超额收益率': f"{ytd_excess_returns.min()*100:.2f}%",
            '正超额收益股票数量': len(ytd_excess_returns[ytd_excess_returns > 0]),
            '负超额收益股票数量': len(ytd_excess_returns[ytd_excess_returns < 0]),
            '跑赢指数股票比例': f"{len(ytd_excess_returns[ytd_excess_returns > 0])/len(ytd_excess_returns)*100:.1f}%"
        }
        
        # 保存统计报告
        report_path = os.path.join(save_dir, f'统计报告_{datetime.now().strftime("%Y%m%d")}.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("沪深300成分股超额收益率分析报告\n")
            f.write("="*50 + "\n")
            f.write(f"分析日期: {datetime.now().strftime('%Y年%m月%d日')}\n")
            f.write(f"数据期间: 2025年初至{datetime.now().strftime('%Y年%m月%d日')}\n\n")
            
            f.write("主要统计指标:\n")
            f.write("-"*30 + "\n")
            for key, value in stats.items():
                f.write(f"{key}: {value}\n")
            
            f.write(f"\n表现最佳前5只股票:\n")
            f.write("-"*30 + "\n")
            top_5 = ytd_excess_returns.nlargest(5)
            for i, (code, ret) in enumerate(top_5.items(), 1):
                f.write(f"{i}. {code}: {ret*100:.2f}%\n")
            
            f.write(f"\n表现最差后5只股票:\n")
            f.write("-"*30 + "\n")
            bottom_5 = ytd_excess_returns.nsmallest(5)
            for i, (code, ret) in enumerate(bottom_5.items(), 1):
                f.write(f"{i}. {code}: {ret*100:.2f}%\n")
        
        print(f"统计报告已保存至: {report_path}")
        
        # 打印控制台摘要
        print("\n" + "="*60)
        print("沪深300成分股超额收益率分析摘要")
        print("="*60)
        for key, value in stats.items():
            print(f"{key}: {value}")
        
        return report_path
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        return None

def main():
    """主函数"""
    print("开始沪深300成分股超额收益率分析...")
    print("="*60)
    
    # 1. 连接Wind数据库
    if not connect_wind():
        print("无法连接Wind数据库，程序退出")
        return
    
    # 2. 获取成分股代码
    constituents = get_hs300_constituents()
    if constituents is None:
        print("无法获取成分股代码，程序退出")
        return
    
    # 3. 设置时间范围（今年初至今）
    start_date = "2025-01-01"
    end_date = datetime.now().strftime("%Y-%m-%d")
    
    # 4. 计算收益率
    stock_returns, index_returns = calculate_returns(constituents, start_date, end_date)
    if stock_returns is None or index_returns is None:
        print("无法获取价格数据，程序退出")
        return
    
    # 5. 计算超额收益率
    excess_returns, ytd_excess_returns = calculate_excess_returns(stock_returns, index_returns)
    if excess_returns is None or ytd_excess_returns is None:
        print("计算超额收益率失败，程序退出")
        return
    
    # 6. 创建可视化图表
    save_dir = create_visualizations(ytd_excess_returns, excess_returns)
    if save_dir is None:
        print("创建图表失败")
        return
    
    # 7. 生成汇总报告
    report_path = generate_summary_report(ytd_excess_returns, save_dir)
    
    # 8. 关闭Wind连接
    w.stop()
    
    print(f"\n分析完成！所有文件已保存至: {save_dir}")
    print("="*60)

if __name__ == "__main__":
    main()
