#!/bin/bash

echo "========================================"
echo "WindPy重新安装脚本"
echo "========================================"

# 步骤1：卸载现有WindPy
echo "步骤1: 卸载现有WindPy..."
pip3 uninstall WindPy -y 2>/dev/null || echo "未找到已安装的WindPy"

# 步骤2：清理缓存
echo "步骤2: 清理pip缓存..."
pip3 cache purge 2>/dev/null || echo "缓存清理完成"

# 步骤3：检查Wind终端
echo "步骤3: 检查Wind终端状态..."
if pgrep -f "Wind" > /dev/null; then
    echo "✓ 检测到Wind终端正在运行"
else
    echo "⚠ 未检测到Wind终端，请确保已启动Wind数据终端"
    echo "请手动启动Wind终端并登录后继续"
    read -p "Wind终端已启动并登录？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "请先启动Wind终端，然后重新运行此脚本"
        exit 1
    fi
fi

# 步骤4：提示手动安装WindPy
echo "步骤4: WindPy手动安装指导..."
echo "请按以下步骤手动安装WindPy："
echo "1. 打开浏览器访问: https://www.wind.com.cn/"
echo "2. 登录您的Wind账户"
echo "3. 搜索'WindPy'或'Python接口'"
echo "4. 下载最新版本的WindPy安装包"
echo "5. 解压并运行安装程序"
echo ""
echo "安装完成后，按Enter键继续测试..."
read

# 步骤5：测试安装
echo "步骤5: 测试WindPy安装..."
python3 -c "
try:
    from WindPy import w
    print('✓ WindPy导入成功')
    result = w.start()
    if hasattr(result, 'ErrorCode'):
        if result.ErrorCode == 0:
            print('✓ Wind连接成功')
            # 测试简单查询
            test = w.wsd('000001.SZ', 'close', '2025-07-30', '2025-07-31', '')
            if hasattr(test, 'ErrorCode') and test.ErrorCode == 0:
                print('✓ 数据查询测试成功')
                print('WindPy安装和配置完成！')
            else:
                print('⚠ 数据查询测试失败，请检查数据权限')
        else:
            print(f'✗ Wind连接失败，错误代码: {result.ErrorCode}')
    w.stop()
except ImportError as e:
    print(f'✗ WindPy导入失败: {e}')
    print('请确保正确安装了WindPy')
except Exception as e:
    print(f'✗ WindPy测试失败: {e}')
    print('请检查Wind终端是否正常运行')
"

echo "========================================"
echo "如果测试成功，现在可以运行主分析程序："
echo "./run_hs300_analysis.sh"
echo "========================================"
