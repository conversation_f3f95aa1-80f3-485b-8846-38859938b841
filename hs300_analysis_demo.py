#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沪深300指数成分股超额收益率分析 - 演示版本
使用模拟数据演示完整分析流程
作者: 基金经理
日期: 2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import warnings

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'PingFang SC']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

def generate_mock_data():
    """生成模拟的沪深300成分股数据"""
    print("生成模拟数据（演示版本）...")
    
    # 模拟300只股票代码
    stock_codes = []
    for i in range(300):
        if i < 150:
            code = f"{str(i+1).zfill(6)}.SZ"  # 深交所
        else:
            code = f"{str(600000+i-149).zfill(6)}.SH"  # 上交所
        stock_codes.append(code)
    
    # 生成交易日期（2025年1月1日至今）
    start_date = datetime(2025, 1, 1)
    end_date = datetime.now()
    
    # 生成交易日（排除周末）
    trading_days = []
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # 周一到周五
            trading_days.append(current_date)
        current_date += timedelta(days=1)
    
    print(f"模拟数据期间: {trading_days[0].strftime('%Y-%m-%d')} 至 {trading_days[-1].strftime('%Y-%m-%d')}")
    print(f"交易日数量: {len(trading_days)}")
    print(f"股票数量: {len(stock_codes)}")
    
    return stock_codes, trading_days

def simulate_returns(stock_codes, trading_days):
    """模拟股票收益率数据"""
    print("模拟股票收益率数据...")
    
    # 设置随机种子以确保结果可重现
    np.random.seed(42)
    
    # 模拟每日收益率
    n_days = len(trading_days)
    n_stocks = len(stock_codes)
    
    # 创建不同风格的股票表现
    stock_returns = np.zeros((n_days, n_stocks))
    
    for i, stock in enumerate(stock_codes):
        # 根据股票类型设置不同的表现特征
        if i < 50:  # 前50只：表现较好的股票
            daily_mean = 0.0008  # 平均日收益率0.08%
            volatility = 0.018   # 波动率1.8%
        elif i < 100:  # 51-100只：中等表现股票
            daily_mean = 0.0003  # 平均日收益率0.03%
            volatility = 0.020   # 波动率2.0%
        elif i < 200:  # 101-200只：接近指数表现
            daily_mean = 0.0001  # 平均日收益率0.01%
            volatility = 0.019   # 波动率1.9%
        else:  # 201-300只：表现较差的股票
            daily_mean = -0.0002  # 平均日收益率-0.02%
            volatility = 0.022    # 波动率2.2%
        
        # 生成收益率序列
        stock_returns[:, i] = np.random.normal(daily_mean, volatility, n_days)
    
    # 模拟沪深300指数收益率（大致为所有股票的加权平均）
    index_returns = np.mean(stock_returns, axis=1) + np.random.normal(0, 0.005, n_days)
    
    # 转换为DataFrame
    stock_returns_df = pd.DataFrame(stock_returns, index=trading_days, columns=stock_codes)
    index_returns_series = pd.Series(index_returns, index=trading_days, name='HS300')
    
    return stock_returns_df, index_returns_series

def calculate_excess_returns(stock_returns, index_returns):
    """计算超额收益率"""
    print("计算超额收益率...")
    
    # 计算超额收益率
    excess_returns = stock_returns.sub(index_returns, axis=0)
    
    # 计算累计超额收益率
    cumulative_excess_returns = (1 + excess_returns).cumprod() - 1
    
    # 计算年初至今超额收益率
    ytd_excess_returns = cumulative_excess_returns.iloc[-1]
    
    print(f"计算完成，共{len(ytd_excess_returns)}只个股的超额收益率")
    
    return excess_returns, ytd_excess_returns

def create_visualizations(ytd_excess_returns, excess_returns):
    """创建可视化图表"""
    try:
        # 创建图表目录
        save_dir = os.path.expanduser("~/Documents/沪深300超额收益率分析_演示版")
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置图表大小
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 超额收益率分布直方图
        plt.subplot(2, 3, 1)
        plt.hist(ytd_excess_returns * 100, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('沪深300成分股年初至今超额收益率分布\n（演示版 - 模拟数据）', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.ylabel('频数', fontsize=12)
        plt.axvline(ytd_excess_returns.mean() * 100, color='red', linestyle='--', 
                   label=f'均值: {ytd_excess_returns.mean()*100:.2f}%')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 箱线图
        plt.subplot(2, 3, 2)
        box_plot = plt.boxplot(ytd_excess_returns * 100, patch_artist=True)
        box_plot['boxes'][0].set_facecolor('lightblue')
        plt.title('超额收益率箱线图', fontsize=14, fontweight='bold')
        plt.ylabel('超额收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 3. 前20名和后20名
        plt.subplot(2, 3, 3)
        top_20 = ytd_excess_returns.nlargest(20)
        
        y_pos = np.arange(len(top_20))
        plt.barh(y_pos, top_20 * 100, color='green', alpha=0.7)
        plt.title('超额收益率前20名', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.yticks(y_pos, [code[:6] for code in top_20.index], fontsize=8)
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 3, 4)
        bottom_20 = ytd_excess_returns.nsmallest(20)
        y_pos = np.arange(len(bottom_20))
        plt.barh(y_pos, bottom_20 * 100, color='red', alpha=0.7)
        plt.title('超额收益率后20名', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.yticks(y_pos, [code[:6] for code in bottom_20.index], fontsize=8)
        plt.grid(True, alpha=0.3)
        
        # 4. 超额收益率分位数分析
        plt.subplot(2, 3, 5)
        quartiles = ytd_excess_returns.quantile([0.25, 0.5, 0.75])
        quartile_labels = ['25%分位数', '50%分位数\n(中位数)', '75%分位数']
        colors = ['lightcoral', 'gold', 'lightgreen']
        
        bars = plt.bar(quartile_labels, quartiles * 100, color=colors, alpha=0.8)
        plt.title('超额收益率分位数分析', fontsize=14, fontweight='bold')
        plt.ylabel('超额收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, v) in enumerate(zip(bars, quartiles * 100)):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{v:.2f}%', ha='center', va='bottom', fontweight='bold')
        
        # 5. 密度图
        plt.subplot(2, 3, 6)
        plt.hist(ytd_excess_returns * 100, bins=30, density=True, alpha=0.7, 
                color='skyblue', label='实际分布')
        
        # 添加正态分布对比
        mu, sigma = ytd_excess_returns.mean() * 100, ytd_excess_returns.std() * 100
        x = np.linspace(ytd_excess_returns.min() * 100, ytd_excess_returns.max() * 100, 100)
        normal_dist = (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu)/sigma)**2)
        plt.plot(x, normal_dist, 'r-', linewidth=2, label='正态分布对比')
        
        plt.title('超额收益率概率密度分布', fontsize=14, fontweight='bold')
        plt.xlabel('超额收益率 (%)', fontsize=12)
        plt.ylabel('概率密度', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(save_dir, f'沪深300超额收益率分析_演示版_{datetime.now().strftime("%Y%m%d")}.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"图表已保存至: {chart_path}")
        
        # 显示图表
        plt.show()
        
        return save_dir
        
    except Exception as e:
        print(f"创建图表时出错: {e}")
        return None

def generate_summary_report(ytd_excess_returns, save_dir):
    """生成汇总报告"""
    try:
        # 统计分析
        stats = {
            '样本数量': len(ytd_excess_returns),
            '平均超额收益率': f"{ytd_excess_returns.mean()*100:.2f}%",
            '中位数超额收益率': f"{ytd_excess_returns.median()*100:.2f}%",
            '标准差': f"{ytd_excess_returns.std()*100:.2f}%",
            '最大超额收益率': f"{ytd_excess_returns.max()*100:.2f}%",
            '最小超额收益率': f"{ytd_excess_returns.min()*100:.2f}%",
            '正超额收益股票数量': len(ytd_excess_returns[ytd_excess_returns > 0]),
            '负超额收益股票数量': len(ytd_excess_returns[ytd_excess_returns < 0]),
            '跑赢指数股票比例': f"{len(ytd_excess_returns[ytd_excess_returns > 0])/len(ytd_excess_returns)*100:.1f}%"
        }
        
        # 保存统计报告
        report_path = os.path.join(save_dir, f'统计报告_演示版_{datetime.now().strftime("%Y%m%d")}.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("沪深300成分股超额收益率分析报告 - 演示版\n")
            f.write("="*60 + "\n")
            f.write("*** 注意：本报告基于模拟数据，仅用于演示分析功能 ***\n")
            f.write("*** 实际使用请安装WindPy并连接Wind数据库 ***\n")
            f.write("="*60 + "\n\n")
            f.write(f"分析日期: {datetime.now().strftime('%Y年%m月%d日')}\n")
            f.write(f"数据期间: 2025年初至{datetime.now().strftime('%Y年%m月%d日')}\n\n")
            
            f.write("主要统计指标:\n")
            f.write("-"*30 + "\n")
            for key, value in stats.items():
                f.write(f"{key}: {value}\n")
            
            f.write(f"\n表现最佳前5只股票（模拟）:\n")
            f.write("-"*30 + "\n")
            top_5 = ytd_excess_returns.nlargest(5)
            for i, (code, ret) in enumerate(top_5.items(), 1):
                f.write(f"{i}. {code}: {ret*100:.2f}%\n")
            
            f.write(f"\n表现最差后5只股票（模拟）:\n")
            f.write("-"*30 + "\n")
            bottom_5 = ytd_excess_returns.nsmallest(5)
            for i, (code, ret) in enumerate(bottom_5.items(), 1):
                f.write(f"{i}. {code}: {ret*100:.2f}%\n")
                
            f.write(f"\n" + "="*60 + "\n")
            f.write("如何使用真实数据:\n")
            f.write("1. 访问万得官网下载WindPy: https://www.wind.com.cn/\n")
            f.write("2. 安装WindPy到Python环境\n")
            f.write("3. 启动Wind终端并登录\n")
            f.write("4. 运行 hs300_analysis.py 获取真实数据分析\n")
        
        print(f"统计报告已保存至: {report_path}")
        
        # 打印控制台摘要
        print("\n" + "="*60)
        print("沪深300成分股超额收益率分析摘要 - 演示版")
        print("*** 基于模拟数据，仅供演示 ***")
        print("="*60)
        for key, value in stats.items():
            print(f"{key}: {value}")
        
        return report_path
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("沪深300成分股超额收益率分析 - 演示版")
    print("*** 使用模拟数据演示完整分析流程 ***")
    print("="*60)
    
    try:
        # 1. 生成模拟数据
        stock_codes, trading_days = generate_mock_data()
        
        # 2. 模拟收益率
        stock_returns, index_returns = simulate_returns(stock_codes, trading_days)
        
        # 3. 计算超额收益率
        excess_returns, ytd_excess_returns = calculate_excess_returns(stock_returns, index_returns)
        
        # 4. 创建可视化图表
        save_dir = create_visualizations(ytd_excess_returns, excess_returns)
        if save_dir is None:
            print("创建图表失败")
            return
        
        # 5. 生成汇总报告
        report_path = generate_summary_report(ytd_excess_returns, save_dir)
        
        print(f"\n" + "="*60)
        print("✅ 演示分析完成！")
        print(f"📁 所有文件已保存至: {save_dir}")
        print("="*60)
        
        print(f"\n🔍 如何使用真实数据:")
        print("1. 访问万得官网下载WindPy: https://www.wind.com.cn/")
        print("2. 安装WindPy到当前Python环境")
        print("3. 启动Wind终端并登录")
        print("4. 运行 hs300_analysis.py 获取真实数据分析")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
