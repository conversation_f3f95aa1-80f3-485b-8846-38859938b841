# -*- coding: utf-8 -*-
"""
字体测试脚本
检查系统中可用的中文字体
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

print("检查系统中的中文字体...")

# 获取所有可用字体
fonts = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = []

# 常见的中文字体名称
chinese_font_names = [
    'Microsoft YaHei', 'Microsoft YaHei UI', 'Microsoft YaHei Light',
    'SimHei', 'SimSun', 'KaiTi', 'FangSong',
    'Arial Unicode MS', 'PingFang SC', 'Heiti SC',
    'STXihei', 'STKaiti', 'STSong'
]

print("\n系统中可用的中文字体：")
for font_name in chinese_font_names:
    if font_name in fonts:
        chinese_fonts.append(font_name)
        print(f"✓ {font_name}")
    else:
        print(f"✗ {font_name}")

if chinese_fonts:
    print(f"\n推荐使用字体：{chinese_fonts[0]}")
    
    # 测试字体显示
    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建测试图
    plt.figure(figsize=(8, 6))
    plt.plot([1, 2, 3, 4], [1, 4, 2, 3], 'o-')
    plt.title('中文字体测试：沪深300超额收益率分析')
    plt.xlabel('时间序列')
    plt.ylabel('收益率(%)')
    plt.grid(True, alpha=0.3)
    
    # 保存测试图
    plt.savefig('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Python/font_test.png', 
                dpi=150, bbox_inches='tight')
    plt.close()
    
    print("字体测试图已保存为 font_test.png")
else:
    print("\n未找到合适的中文字体，请安装Microsoft YaHei或其他中文字体")

print("\n字体检查完成！")
